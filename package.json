{"name": "trust", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "clsx": "^2.1.1", "framer-motion": "^12.0.1", "lottie-react": "^2.4.1", "motion": "^12.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "tailwind-merge": "^3.0.1", "typescript": "^4.4.2", "web-vitals": "^2.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17"}}