import { useEffect } from "react";
import Layout from "../components/Layout";
import HomeDownload from "../containers/Home/Download";
import TechStack from "../containers/Home/TechStack";
import HomeHeader from "../containers/Home/Header";
import HomeWorks from "../containers/Home/Works";

const HomePage = () => {
  useEffect(() => {
    const targetId = localStorage.getItem("scrollTo");
    if (targetId) {
      localStorage.removeItem("scrollTo");
      setTimeout(() => {
        const element = document.getElementById(targetId);
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      }, 100);
    }
  }, []);

  return (
    <Layout>
      <HomeHeader />
      <TechStack />
      <HomeWorks />
      <HomeDownload />
    </Layout>
  );
};

export default HomePage;
