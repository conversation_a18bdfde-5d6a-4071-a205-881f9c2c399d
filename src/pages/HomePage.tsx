import { useEffect } from "react";
import Layout from "../components/Layout";
import HomeDownload from "../containers/Home/Download";
import TechStack from "../containers/Home/TechStack";
import HomeHeader from "../containers/Home/Header";

const HomePage = () => {
  useEffect(() => {
    const targetId = localStorage.getItem("scrollTo");
    if (targetId) {
      localStorage.removeItem("scrollTo");
      setTimeout(() => {
        const element = document.getElementById(targetId);
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      }, 100);
    }
  }, []);

  return (
    <Layout>
      <HomeHeader />
      <section id="about" className="min-h-screen flex items-center justify-center bg-black">
        <TechStack />
      </section>
      <section id="education" className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="max-w-4xl mx-auto text-center text-white px-4">
          <h2 className="text-4xl font-bold mb-8 white-text-shadow">Education</h2>
          <div className="space-y-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-2xl font-semibold mb-2">Bachelor of Computer Science</h3>
              <p className="text-gray-300 mb-2">University Name</p>
              <p className="text-gray-400">2018 - 2022</p>
            </div>
          </div>
        </div>
      </section>
      <section id="experience" className="min-h-screen flex items-center justify-center bg-black">
        <div className="max-w-4xl mx-auto text-center text-white px-4">
          <h2 className="text-4xl font-bold mb-8 white-text-shadow">Experience</h2>
          <div className="space-y-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-2xl font-semibold mb-2">Full Stack Developer</h3>
              <p className="text-gray-300 mb-2">Company Name</p>
              <p className="text-gray-400 mb-4">2022 - Present</p>
              <p className="text-gray-300">Developing modern web applications using React, Node.js, and cloud technologies.</p>
            </div>
          </div>
        </div>
      </section>
      <section id="skills" className="min-h-screen flex items-center justify-center bg-gray-900">
        <TechStack />
      </section>
      <section id="projects" className="min-h-screen flex items-center justify-center bg-black">
        <HomeDownload />
      </section>
      <section id="contact" className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="max-w-4xl mx-auto text-center text-white px-4">
          <h2 className="text-4xl font-bold mb-8 white-text-shadow">Let's Work Together</h2>
          <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto">
            Ready to bring your ideas to life? I'm always open to discussing new opportunities,
            creative projects, and innovative solutions.
          </p>
          <div className="flex flex-col md:flex-row gap-6 justify-center items-center">
            <a
              href="mailto:<EMAIL>"
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-semibold hover:scale-105 transition-transform"
            >
              Get In Touch
            </a>
            <a
              href="https://wa.me/84962264623"
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-4 border border-gray-400 rounded-lg text-white font-semibold hover:bg-gray-800 transition-colors"
            >
              WhatsApp
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;
