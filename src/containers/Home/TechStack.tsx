import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { InitialAppearance } from '../../components/Animation';
import { HeaderText, NormalText } from '../../components/common/Text';
import TechStackCard from '../../components/Cards/TechStackCard';
import { TECH_STACK_ITEMS, TECH_CATEGORIES, TechStackItem } from '../../constants/TechStack';
import { Code2 } from 'lucide-react';

const TechStack: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredItems = selectedCategory === 'all' 
    ? TECH_STACK_ITEMS 
    : TECH_STACK_ITEMS.filter(item => item.category === selectedCategory);

  const categories = [
    { key: 'all', name: 'All Technologies', color: '#6366f1' },
    ...Object.entries(TECH_CATEGORIES).map(([key, value]) => ({
      key,
      name: value.name,
      color: value.color
    }))
  ];

  return (
    <div className="w-full max-w-6xl mx-auto px-4 text-white">
      {/* About Me Section */}
      <InitialAppearance className="w-full text-center mb-12" time={1}>
        <div className="flex w-full justify-center mb-6">
          <div className="flex w-max bg-gray-800 border border-gray-700 gap-[3px] px-[12px] py-[10px] rounded-full items-center">
            <Code2 className="w-[20px] h-[20px] text-blue-500" />
            <p className="text-[16px] text-white">About Me</p>
          </div>
        </div>
        <HeaderText className="text-white mb-6">
          Full Stack Developer & Tech Enthusiast
        </HeaderText>
        <NormalText className="max-w-3xl mx-auto text-gray-300 mb-8 text-lg leading-relaxed">
          I'm a passionate full-stack developer with 7+ years of experience creating digital solutions
          that make a difference. I specialize in modern web technologies and love turning complex
          problems into simple, beautiful, and intuitive solutions.
        </NormalText>

        {/* Resume Button */}
        <div className="mb-12">
          <a
            href="https://drive.google.com/file/d/1emWNdf6PMWO4BxAW5OWmlVjKkWYaaJyh/view"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-semibold hover:scale-105 transition-transform shadow-lg"
          >
            📄 My Resume
          </a>
        </div>
      </InitialAppearance>

      {/* Tech Stack Header */}
      <InitialAppearance className="w-full text-center mb-8" time={1.2}>
        <HeaderText className="text-white mb-4">
          Technologies & Tools
        </HeaderText>
        <div className="flex flex-wrap justify-center gap-3 max-w-4xl">
          {categories.map((category) => (
            <motion.button
              key={category.key}
              onClick={() => setSelectedCategory(category.key)}
              className={`
                px-4 py-2 rounded-full text-sm font-medium transition-all duration-300
                ${selectedCategory === category.key
                  ? 'text-white shadow-lg'
                  : 'text-gray-400 bg-gray-800/50 hover:bg-gray-700/70'
                }
              `}
              style={{
                backgroundColor: selectedCategory === category.key ? category.color : undefined,
                border: `1px solid ${selectedCategory === category.key ? category.color : 'transparent'}`
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.name}
            </motion.button>
          ))}
        </div>
      </InitialAppearance>

      {/* Tech Stack Grid */}
      <section className="w-full mb-14">
        <motion.div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 items-stretch"
          layout
        >
          {filteredItems.map((item: TechStackItem, index: number) => (
            <motion.div
              key={`${item.name}-${selectedCategory}`}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{
                duration: 0.4,
                delay: index * 0.05,
                layout: { duration: 0.3 }
              }}
            >
              <TechStackCard item={item} index={index} />
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Stats Section */}
      <InitialAppearance className="w-full text-center" time={1.5}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {Object.entries(TECH_CATEGORIES).map(([key, category]) => {
            const count = TECH_STACK_ITEMS.filter(item => item.category === key).length;
            return (
              <motion.div
                key={key}
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div
                  className="text-3xl font-bold mb-2"
                  style={{ color: category.color }}
                >
                  {count}
                </div>
                <div className="text-sm text-gray-400 capitalize">
                  {category.name}
                </div>
              </motion.div>
            );
          })}
        </div>
      </InitialAppearance>
    </div>
  );
};

export default TechStack;
