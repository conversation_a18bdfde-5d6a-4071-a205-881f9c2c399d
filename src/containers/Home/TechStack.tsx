import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { InitialAppearance } from '../../components/Animation';
import { HeaderText, NormalText } from '../../components/common/Text';
import TechStackCard from '../../components/Cards/TechStackCard';
import { TECH_STACK_ITEMS, TECH_CATEGORIES, TechStackItem } from '../../constants/TechStack';
import { Code2 } from 'lucide-react';

const TechStack: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredItems = selectedCategory === 'all' 
    ? TECH_STACK_ITEMS 
    : TECH_STACK_ITEMS.filter(item => item.category === selectedCategory);

  const categories = [
    { key: 'all', name: 'All Technologies', color: '#6366f1' },
    ...Object.entries(TECH_CATEGORIES).map(([key, value]) => ({
      key,
      name: value.name,
      color: value.color
    }))
  ];

  return (
    <section
      id="techstack"
      className="w-full px-4 text-gray-800 dark:text-white mb-16 justify-items-center pt-10"
    >
      {/* Header */}
      <InitialAppearance className="w-full justify-items-center" time={1}>
        <div className="flex w-full justify-center">
          <div className="flex w-max bg-gray-100 dark:bg-[#131315] border border-gray-200 dark:border-[#1d1d20] gap-[3px] px-[12px] py-[10px] rounded-full items-center">
            <Code2 className="w-[20px] h-[20px] text-blue-500" />
            <p className="text-[16px] text-gray-900 dark:text-white">Tech Stack</p>
          </div>
        </div>
        <div className="text-center mb-8 py-4">
          <HeaderText className="text-gray-900 dark:text-white">
            Technologies & Tools
          </HeaderText>
          <NormalText className="mt-4 max-w-2xl mx-auto text-gray-600 dark:text-gray-400">
            A comprehensive overview of the technologies, frameworks, and tools I use to build modern, scalable applications
          </NormalText>
        </div>
      </InitialAppearance>

      {/* Category Filter */}
      <InitialAppearance className="w-full justify-items-center mb-8" time={1.2}>
        <div className="flex flex-wrap justify-center gap-3 max-w-4xl">
          {categories.map((category) => (
            <motion.button
              key={category.key}
              onClick={() => setSelectedCategory(category.key)}
              className={`
                px-4 py-2 rounded-full text-sm font-medium transition-all duration-300
                ${selectedCategory === category.key
                  ? 'text-white shadow-lg'
                  : 'text-gray-600 dark:text-gray-400 bg-white/50 dark:bg-gray-800/50 hover:bg-white/70 dark:hover:bg-gray-700/70'
                }
              `}
              style={{
                backgroundColor: selectedCategory === category.key ? category.color : undefined,
                border: `1px solid ${selectedCategory === category.key ? category.color : 'transparent'}`
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.name}
            </motion.button>
          ))}
        </div>
      </InitialAppearance>

      {/* Tech Stack Grid */}
      <section className="w-full mb-14 px-4 justify-items-center">
        <motion.div
          className="max-w-7xl grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 gap-8 items-stretch"
          layout
        >
          {filteredItems.map((item: TechStackItem, index: number) => (
            <motion.div
              key={`${item.name}-${selectedCategory}`}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ 
                duration: 0.4, 
                delay: index * 0.05,
                layout: { duration: 0.3 }
              }}
            >
              <TechStackCard item={item} index={index} />
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Stats Section */}
      <InitialAppearance className="w-full justify-items-center" time={1.5}>
        <div className="max-w-4xl mx-auto grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
          {Object.entries(TECH_CATEGORIES).map(([key, category]) => {
            const count = TECH_STACK_ITEMS.filter(item => item.category === key).length;
            return (
              <motion.div
                key={key}
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{ color: category.color }}
                >
                  {count}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                  {category.name}
                </div>
              </motion.div>
            );
          })}
        </div>
      </InitialAppearance>
    </section>
  );
};

export default TechStack;
