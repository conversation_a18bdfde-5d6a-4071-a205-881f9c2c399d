import { GradientBg, InitialAppearance } from "../../components/Animation";
import { NormalText, TitleText } from "../../components/common/Text";
import { ProfilePhoto } from "../../components/common/Image";

const HomeHeader = () => {
  return (
    <section id="home" className="relative w-full min-h-screen flex items-center justify-center font-bold text-white">
      <GradientBg />
      <InitialAppearance className="w-full max-w-7xl mx-auto px-8 z-10" time={2}>
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          {/* Left side - Content */}
          <div className="lg:w-1/2 text-left">
            <div className="mb-8">
              <span className="text-yellow-400 text-2xl">⭐</span>
              <TitleText className="leading-tight mt-4">
                <span className="white-text-shadow text-[48px] sm:text-[64px] lg:text-[80px] xl:text-[96px] font-bold block">
                  Full Stack
                </span>
                <span className="text-neutral-200 text-[24px] sm:text-[32px] lg:text-[40px] font-normal block mt-2">
                  Building digital experiences that blend design and technology
                </span>
              </TitleText>
            </div>

            <div className="mb-8">
              <a
                href="#projects"
                className="inline-flex items-center px-6 py-3 bg-transparent border border-gray-400 rounded-lg text-white font-medium hover:bg-gray-800 transition-colors"
              >
                View Projects
              </a>
            </div>
          </div>

          {/* Right side - Profile Photo */}
          <div className="lg:w-1/2 flex justify-center lg:justify-end">
            <div className="relative">
              <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full p-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500">
                <div className="w-full h-full rounded-full overflow-hidden bg-black">
                  <img
                    src="/assets/images/id-photo.jpg"
                    alt="Do Quoc Dat"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-400 rounded-full"></div>
            </div>
          </div>
        </div>
      </InitialAppearance>
    </section>
  );
};

export default HomeHeader;
