import {
  appStore,
  googleAppStore,
  homeDownload,
  privateIcon,
  secure,
  twoArrowws,
} from "../../assets/image";
import { NormalButton } from "../../components/common/Button";

const HomeDownload = () => {
  return (
    <>
      <section className="w-full py-16 px-4 relative bg-black z-[10]">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
            <div className="lg:w-1/2 bg-black z-10">
              <img
                src={homeDownload}
                alt="Portfolio Projects"
                className="w-full max-w-lg mx-auto"
              />
            </div>
            <div className="lg:w-1/2 text-left">
              <h2 className="text-4xl font-bold text-white mb-6 white-text-shadow">
                Featured Projects
              </h2>
              <p className="text-gray-300 mb-8">
                Explore my portfolio of web applications, mobile apps, and full-stack solutions.
                Each project showcases modern technologies, clean code, and user-centered design
                principles that deliver exceptional user experiences.
              </p>
            <div className="flex flex-col md:flex-row gap-[20px]">
              <a
                href="https://github.com/techEdge3030"
                target="_blank"
                rel="noopener noreferrer"
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-semibold hover:scale-105 transition-transform text-center"
              >
                View All Projects
              </a>
              <a
                href="https://www.linkedin.com/in/do-quoc-dat-b865a6b5/"
                target="_blank"
                rel="noopener noreferrer"
                className="px-6 py-3 border border-gray-400 rounded-lg text-white font-semibold hover:bg-gray-800 transition-colors text-center"
              >
                Connect on LinkedIn
              </a>
            </div>
            <div className="mt-8">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white">
                  <img src={secure} alt="secure" />
                </div>
                <span className="text-gray-300">Modern Tech Stack</span>
              </div>
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white">
                  <img src={privateIcon} alt="secure" />
                </div>
                <span className="text-gray-300">Responsive Design</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white">
                  <img src={twoArrowws} className="w-4 h-4" alt="secure" />
                </div>
                <span className="text-gray-300">Performance Optimized</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    </>
  );
};

export default HomeDownload;
