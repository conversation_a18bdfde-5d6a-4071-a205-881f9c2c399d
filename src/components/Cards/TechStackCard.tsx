import React from 'react';
import { motion } from 'framer-motion';
import { TechStackItem } from '../../constants/TechStack';

// Simple icon component mapping
const getIconComponent = (iconName: string) => {
  switch (iconName) {
    case 'SiReact':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#61DAFB' }}>⚛️</div>;
    case 'SiTypescript':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#3178C6' }}>📘</div>;
    case 'SiNextdotjs':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#000000' }}>▲</div>;
    case 'SiTailwindcss':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#06B6D4' }}>🎨</div>;
    case 'SiThreedotjs':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#000000' }}>🎮</div>;
    case 'SiFramer':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#0055FF' }}>🎬</div>;
    case 'SiNodedotjs':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#339933' }}>🟢</div>;
    case 'SiPython':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#3776AB' }}>🐍</div>;
    case 'SiExpress':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#000000' }}>🚀</div>;
    case 'SiGraphql':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#E10098' }}>📊</div>;
    case 'SiMongodb':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#47A248' }}>🍃</div>;
    case 'SiPostgresql':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#336791' }}>🐘</div>;
    case 'SiRedis':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#DC382D' }}>⚡</div>;
    case 'SiGit':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#F05032' }}>📝</div>;
    case 'SiDocker':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#2496ED' }}>🐳</div>;
    case 'SiWebpack':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#8DD6F9' }}>📦</div>;
    case 'SiAmazonaws':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#FF9900' }}>☁️</div>;
    case 'SiVercel':
      return () => <div className="text-4xl mb-3 transition-all duration-300" style={{ color: '#000000' }}>▲</div>;
    default:
      return () => <div className="text-4xl mb-3 transition-all duration-300">💻</div>;
  }
};

interface TechStackCardProps {
  item: TechStackItem;
  index: number;
}

const TechStackCard: React.FC<TechStackCardProps> = ({ item, index }) => {
  const IconComponent = getIconComponent(item.icon);

  return (
    <motion.div
      className="relative group"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ y: -5, scale: 1.02 }}
    >
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600">
        <div className="flex flex-col items-center text-center">
          <motion.div
            whileHover={{ scale: 1.2, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <IconComponent />
          </motion.div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {item.name}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
            {item.description}
          </p>
          <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 capitalize">
            {item.category}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default TechStackCard;

export default TechStackCard;