import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { TechStackItem } from '../../constants/TechStack';
import {
  SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer,
  SiNodedotjs, SiPython, SiExpress, SiGraphql,
  SiMongodb, SiPostgresql, SiRedis,
  SiGit, SiDocker, SiWebpack,
  SiAmazonwebservices, SiVercel
} from 'react-icons/si';
import { IconType } from 'react-icons';

// Icon mapping with proper react-icons
const iconMap: { [key: string]: IconType } = {
  'SiReact': SiReact,
  'SiTypescript': SiTypescript,
  'SiNextdotjs': SiNextdotjs,
  'SiTailwindcss': SiTailwindcss,
  'SiThreedotjs': SiThreedotjs,
  'SiFramer': <PERSON><PERSON><PERSON><PERSON>,
  'SiNodedotjs': SiNodedotjs,
  'SiPython': SiPython,
  'SiExpress': SiExpress,
  'SiGraphql': SiGraphql,
  'SiMongodb': SiMongodb,
  'SiPostgresql': SiPostgresql,
  'SiRedis': SiRedis,
  'SiGit': SiGit,
  'SiDocker': SiDocker,
  'SiWebpack': SiWebpack,
  'SiAmazonaws': SiAmazonwebservices,
  'SiVercel': SiVercel,
};

interface TechStackCardProps {
  item: TechStackItem;
  index: number;
}

const TechStackCard: React.FC<TechStackCardProps> = ({ item, index }) => {
  const [isHovered, setIsHovered] = useState(false);
  const IconComponent = iconMap[item.icon] as React.ComponentType<{ size?: number; style?: React.CSSProperties; className?: string }>;

  if (!IconComponent) {
    return null;
  }

  return (
    <motion.div
      className="group cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ y: -8, scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="relative bg-white/5 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/20 dark:border-gray-700/50 rounded-2xl p-8 h-full transition-all duration-500 hover:border-gray-300/30 dark:hover:border-gray-600/50 hover:bg-white/10 dark:hover:bg-gray-800/70 hover:shadow-2xl hover:shadow-purple-500/10">

        {/* Gradient overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Icon */}
        <motion.div
          className="relative flex justify-center mb-6"
          whileHover={{ scale: 1.15, rotate: 5 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <div className="relative">
            <IconComponent
              size={48}
              style={{ color: item.color }}
              className="drop-shadow-lg"
            />
            {/* Glow effect */}
            <div
              className="absolute inset-0 blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500"
              style={{ backgroundColor: item.color }}
            />
          </div>
        </motion.div>

        {/* Content */}
        <div className="relative text-center">
          <motion.h3
            className="text-xl font-bold text-gray-800 dark:text-white mb-3"
            style={{ color: isHovered ? item.color : undefined }}
            transition={{ duration: 0.3 }}
          >
            {item.name}
          </motion.h3>

          <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed line-clamp-3">
            {item.description}
          </p>

          {/* Category Badge */}
          <motion.span
            className="inline-block px-4 py-2 text-xs font-semibold rounded-full backdrop-blur-sm"
            style={{
              backgroundColor: `${item.color}15`,
              color: item.color,
              border: `1px solid ${item.color}30`
            }}
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400 }}
          >
            {item.category}
          </motion.span>
        </div>

        {/* Shine effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
      </div>
    </motion.div>
  );
};

export default TechStackCard;