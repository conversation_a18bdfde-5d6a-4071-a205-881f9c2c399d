{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { MapControls as MapControls$1 } from 'three-stdlib';\nconst MapControls = /* @__PURE__ */React.forwardRef((props = {\n  enableDamping: true\n}, ref) => {\n  const {\n    domElement,\n    camera,\n    makeDefault,\n    onChange,\n    onStart,\n    onEnd,\n    ...rest\n  } = props;\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const explCamera = camera || defaultCamera;\n  const controls = React.useMemo(() => new MapControls$1(explCamera), [explCamera]);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      controls.dispose();\n      controls.removeEventListener('change', callback);\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, explDomElement]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame(() => controls.update(), -1);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: true\n  }, rest));\n});\nexport { MapControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "MapControls", "MapControls$1", "forwardRef", "props", "enableDamping", "ref", "dom<PERSON>lement", "camera", "makeDefault", "onChange", "onStart", "onEnd", "rest", "invalidate", "state", "defaultCamera", "gl", "events", "set", "get", "explDomElement", "connected", "explCamera", "controls", "useMemo", "useEffect", "connect", "callback", "e", "addEventListener", "dispose", "removeEventListener", "old", "update", "createElement", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MapControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { MapControls as MapControls$1 } from 'three-stdlib';\n\nconst MapControls = /* @__PURE__ */React.forwardRef((props = {\n  enableDamping: true\n}, ref) => {\n  const {\n    domElement,\n    camera,\n    makeDefault,\n    onChange,\n    onStart,\n    onEnd,\n    ...rest\n  } = props;\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const explCamera = camera || defaultCamera;\n  const controls = React.useMemo(() => new MapControls$1(explCamera), [explCamera]);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      controls.dispose();\n      controls.removeEventListener('change', callback);\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, explDomElement]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame(() => controls.update(), -1);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: true\n  }, rest));\n});\n\nexport { MapControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,IAAIC,aAAa,QAAQ,cAAc;AAE3D,MAAMD,WAAW,GAAG,eAAeD,KAAK,CAACG,UAAU,CAAC,CAACC,KAAK,GAAG;EAC3DC,aAAa,EAAE;AACjB,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC,UAAU;IACVC,MAAM;IACNC,WAAW;IACXC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACL,GAAGC;EACL,CAAC,GAAGT,KAAK;EACT,MAAMU,UAAU,GAAGhB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,aAAa,GAAGlB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACP,MAAM,CAAC;EACrD,MAAMS,EAAE,GAAGnB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGpB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGrB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACI,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGtB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,cAAc,GAAGd,UAAU,IAAIW,MAAM,CAACI,SAAS,IAAIL,EAAE,CAACV,UAAU;EACtE,MAAMgB,UAAU,GAAGf,MAAM,IAAIQ,aAAa;EAC1C,MAAMQ,QAAQ,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM,IAAIvB,aAAa,CAACqB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACjFvB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,CAACN,cAAc,CAAC;IAChC,MAAMO,QAAQ,GAAGC,CAAC,IAAI;MACpBf,UAAU,CAAC,CAAC;MACZ,IAAIJ,QAAQ,EAAEA,QAAQ,CAACmB,CAAC,CAAC;IAC3B,CAAC;IACDL,QAAQ,CAACM,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC7C,IAAIjB,OAAO,EAAEa,QAAQ,CAACM,gBAAgB,CAAC,OAAO,EAAEnB,OAAO,CAAC;IACxD,IAAIC,KAAK,EAAEY,QAAQ,CAACM,gBAAgB,CAAC,KAAK,EAAElB,KAAK,CAAC;IAClD,OAAO,MAAM;MACXY,QAAQ,CAACO,OAAO,CAAC,CAAC;MAClBP,QAAQ,CAACQ,mBAAmB,CAAC,QAAQ,EAAEJ,QAAQ,CAAC;MAChD,IAAIjB,OAAO,EAAEa,QAAQ,CAACQ,mBAAmB,CAAC,OAAO,EAAErB,OAAO,CAAC;MAC3D,IAAIC,KAAK,EAAEY,QAAQ,CAACQ,mBAAmB,CAAC,KAAK,EAAEpB,KAAK,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEY,QAAQ,EAAEV,UAAU,EAAEO,cAAc,CAAC,CAAC;EACpErB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIjB,WAAW,EAAE;MACf,MAAMwB,GAAG,GAAGb,GAAG,CAAC,CAAC,CAACI,QAAQ;MAC1B;MACAL,GAAG,CAAC;QACFK;MACF,CAAC,CAAC;MACF,OAAO,MAAML,GAAG,CAAC;QACfK,QAAQ,EAAES;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACxB,WAAW,EAAEe,QAAQ,CAAC,CAAC;EAC3BzB,QAAQ,CAAC,MAAMyB,QAAQ,CAACU,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,OAAO,aAAalC,KAAK,CAACmC,aAAa,CAAC,WAAW,EAAEtC,QAAQ,CAAC;IAC5DS,GAAG,EAAEA,GAAG;IACR8B,MAAM,EAAEZ,QAAQ;IAChBnB,aAAa,EAAE;EACjB,CAAC,EAAEQ,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}