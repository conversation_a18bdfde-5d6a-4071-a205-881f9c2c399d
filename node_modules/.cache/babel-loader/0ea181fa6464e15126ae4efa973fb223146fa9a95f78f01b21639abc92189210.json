{"ast": null, "code": "import { Mesh, AnimationMixer, AnimationClip } from \"three\";\nclass MorphAnimMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material);\n    this.type = \"MorphAnimMesh\";\n    this.mixer = new AnimationMixer(this);\n    this.activeAction = null;\n  }\n  setDirectionForward() {\n    this.mixer.timeScale = 1;\n  }\n  setDirectionBackward() {\n    this.mixer.timeScale = -1;\n  }\n  playAnimation(label, fps) {\n    if (this.activeAction) {\n      this.activeAction.stop();\n      this.activeAction = null;\n    }\n    const clip = AnimationClip.findByName(this, label);\n    if (clip) {\n      const action = this.mixer.clipAction(clip);\n      action.timeScale = clip.tracks.length * fps / clip.duration;\n      this.activeAction = action.play();\n    } else {\n      throw new Error(\"THREE.MorphAnimMesh: animations[\" + label + \"] undefined in .playAnimation()\");\n    }\n  }\n  updateAnimation(delta) {\n    this.mixer.update(delta);\n  }\n  copy(source, recursive) {\n    super.copy(source, recursive);\n    this.mixer = new AnimationMixer(this);\n    return this;\n  }\n}\nexport { MorphAnimMesh };", "map": {"version": 3, "names": ["MorphAnimMesh", "<PERSON><PERSON>", "constructor", "geometry", "material", "type", "mixer", "AnimationMixer", "activeAction", "setDirectionForward", "timeScale", "setDirectionBackward", "playAnimation", "label", "fps", "stop", "clip", "AnimationClip", "findByName", "action", "clipAction", "tracks", "length", "duration", "play", "Error", "updateAnimation", "delta", "update", "copy", "source", "recursive"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/misc/MorphAnimMesh.js"], "sourcesContent": ["import { AnimationClip, AnimationMixer, <PERSON><PERSON> } from 'three'\n\nclass MorphAnimMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material)\n\n    this.type = 'MorphAnimMesh'\n\n    this.mixer = new AnimationMixer(this)\n    this.activeAction = null\n  }\n\n  setDirectionForward() {\n    this.mixer.timeScale = 1.0\n  }\n\n  setDirectionBackward() {\n    this.mixer.timeScale = -1.0\n  }\n\n  playAnimation(label, fps) {\n    if (this.activeAction) {\n      this.activeAction.stop()\n      this.activeAction = null\n    }\n\n    const clip = AnimationClip.findByName(this, label)\n\n    if (clip) {\n      const action = this.mixer.clipAction(clip)\n      action.timeScale = (clip.tracks.length * fps) / clip.duration\n      this.activeAction = action.play()\n    } else {\n      throw new Error('THREE.MorphAnimMesh: animations[' + label + '] undefined in .playAnimation()')\n    }\n  }\n\n  updateAnimation(delta) {\n    this.mixer.update(delta)\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.mixer = new AnimationMixer(this)\n\n    return this\n  }\n}\n\nexport { MorphAnimMesh }\n"], "mappings": ";AAEA,MAAMA,aAAA,SAAsBC,IAAA,CAAK;EAC/BC,YAAYC,QAAA,EAAUC,QAAA,EAAU;IAC9B,MAAMD,QAAA,EAAUC,QAAQ;IAExB,KAAKC,IAAA,GAAO;IAEZ,KAAKC,KAAA,GAAQ,IAAIC,cAAA,CAAe,IAAI;IACpC,KAAKC,YAAA,GAAe;EACrB;EAEDC,oBAAA,EAAsB;IACpB,KAAKH,KAAA,CAAMI,SAAA,GAAY;EACxB;EAEDC,qBAAA,EAAuB;IACrB,KAAKL,KAAA,CAAMI,SAAA,GAAY;EACxB;EAEDE,cAAcC,KAAA,EAAOC,GAAA,EAAK;IACxB,IAAI,KAAKN,YAAA,EAAc;MACrB,KAAKA,YAAA,CAAaO,IAAA,CAAM;MACxB,KAAKP,YAAA,GAAe;IACrB;IAED,MAAMQ,IAAA,GAAOC,aAAA,CAAcC,UAAA,CAAW,MAAML,KAAK;IAEjD,IAAIG,IAAA,EAAM;MACR,MAAMG,MAAA,GAAS,KAAKb,KAAA,CAAMc,UAAA,CAAWJ,IAAI;MACzCG,MAAA,CAAOT,SAAA,GAAaM,IAAA,CAAKK,MAAA,CAAOC,MAAA,GAASR,GAAA,GAAOE,IAAA,CAAKO,QAAA;MACrD,KAAKf,YAAA,GAAeW,MAAA,CAAOK,IAAA,CAAM;IACvC,OAAW;MACL,MAAM,IAAIC,KAAA,CAAM,qCAAqCZ,KAAA,GAAQ,iCAAiC;IAC/F;EACF;EAEDa,gBAAgBC,KAAA,EAAO;IACrB,KAAKrB,KAAA,CAAMsB,MAAA,CAAOD,KAAK;EACxB;EAEDE,KAAKC,MAAA,EAAQC,SAAA,EAAW;IACtB,MAAMF,IAAA,CAAKC,MAAA,EAAQC,SAAS;IAE5B,KAAKzB,KAAA,GAAQ,IAAIC,cAAA,CAAe,IAAI;IAEpC,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}