{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { version } from '../helpers/constants.js';\nconst GridMaterial = /* @__PURE__ */shaderMaterial({\n  cellSize: 0.5,\n  sectionSize: 1,\n  fadeDistance: 100,\n  fadeStrength: 1,\n  fadeFrom: 1,\n  cellThickness: 0.5,\n  sectionThickness: 1,\n  cellColor: /* @__PURE__ */new THREE.Color(),\n  sectionColor: /* @__PURE__ */new THREE.Color(),\n  infiniteGrid: false,\n  followCamera: false,\n  worldCamProjPosition: /* @__PURE__ */new THREE.Vector3(),\n  worldPlanePosition: /* @__PURE__ */new THREE.Vector3()\n}, /* glsl */`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform vec3 worldPlanePosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      localPosition = position.xzy;\n      if (infiniteGrid) localPosition *= 1.0 + fadeDistance;\n      \n      worldPosition = modelMatrix * vec4(localPosition, 1.0);\n      if (followCamera) {\n        worldPosition.xyz += (worldCamProjPosition - worldPlanePosition);\n        localPosition = (inverse(modelMatrix) * worldPosition).xyz;\n      }\n\n      gl_Position = projectionMatrix * viewMatrix * worldPosition;\n    }\n  `, /* glsl */`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float fadeFrom;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = localPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1.0 - thickness;\n      return 1.0 - min(line, 1.0);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      vec3 from = worldCamProjPosition*vec3(fadeFrom);\n      float dist = distance(from, worldPosition.xyz);\n      float d = 1.0 - min(dist / fadeDistance, 1.0);\n      vec3 color = mix(cellColor, sectionColor, min(1.0, sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d, fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n    }\n  `);\nconst Grid = /* @__PURE__ */React.forwardRef(({\n  args,\n  cellColor = '#000000',\n  sectionColor = '#2080ff',\n  cellSize = 0.5,\n  sectionSize = 1,\n  followCamera = false,\n  infiniteGrid = false,\n  fadeDistance = 100,\n  fadeStrength = 1,\n  fadeFrom = 1,\n  cellThickness = 0.5,\n  sectionThickness = 1,\n  side = THREE.BackSide,\n  ...props\n}, fRef) => {\n  extend({\n    GridMaterial\n  });\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  const plane = new THREE.Plane();\n  const upVector = new THREE.Vector3(0, 1, 0);\n  const zeroVector = new THREE.Vector3(0, 0, 0);\n  useFrame(state => {\n    plane.setFromNormalAndCoplanarPoint(upVector, zeroVector).applyMatrix4(ref.current.matrixWorld);\n    const gridMaterial = ref.current.material;\n    const worldCamProjPosition = gridMaterial.uniforms.worldCamProjPosition;\n    const worldPlanePosition = gridMaterial.uniforms.worldPlanePosition;\n    plane.projectPoint(state.camera.position, worldCamProjPosition.value);\n    worldPlanePosition.value.set(0, 0, 0).applyMatrix4(ref.current.matrixWorld);\n  });\n  const uniforms1 = {\n    cellSize,\n    sectionSize,\n    cellColor,\n    sectionColor,\n    cellThickness,\n    sectionThickness\n  };\n  const uniforms2 = {\n    fadeDistance,\n    fadeStrength,\n    fadeFrom,\n    infiniteGrid,\n    followCamera\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"gridMaterial\", _extends({\n    transparent: true,\n    \"extensions-derivatives\": true,\n    side: side\n  }, uniforms1, uniforms2)), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }));\n});\nexport { Grid };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "useFrame", "shaderMaterial", "version", "GridMaterial", "cellSize", "sectionSize", "fadeDistance", "fadeStrength", "fadeFrom", "cellThickness", "sectionThickness", "cellColor", "Color", "sectionColor", "infiniteGrid", "followCamera", "worldCamProjPosition", "Vector3", "worldPlanePosition", "Grid", "forwardRef", "args", "side", "BackSide", "props", "fRef", "ref", "useRef", "useImperativeHandle", "current", "plane", "Plane", "upVector", "zeroVector", "state", "setFromNormalAndCoplanarPoint", "applyMatrix4", "matrixWorld", "gridMaterial", "material", "uniforms", "projectPoint", "camera", "position", "value", "set", "uniforms1", "uniforms2", "createElement", "frustumCulled", "transparent"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Grid.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { version } from '../helpers/constants.js';\n\nconst GridMaterial = /* @__PURE__ */shaderMaterial({\n  cellSize: 0.5,\n  sectionSize: 1,\n  fadeDistance: 100,\n  fadeStrength: 1,\n  fadeFrom: 1,\n  cellThickness: 0.5,\n  sectionThickness: 1,\n  cellColor: /* @__PURE__ */new THREE.Color(),\n  sectionColor: /* @__PURE__ */new THREE.Color(),\n  infiniteGrid: false,\n  followCamera: false,\n  worldCamProjPosition: /* @__PURE__ */new THREE.Vector3(),\n  worldPlanePosition: /* @__PURE__ */new THREE.Vector3()\n}, /* glsl */`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform vec3 worldPlanePosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      localPosition = position.xzy;\n      if (infiniteGrid) localPosition *= 1.0 + fadeDistance;\n      \n      worldPosition = modelMatrix * vec4(localPosition, 1.0);\n      if (followCamera) {\n        worldPosition.xyz += (worldCamProjPosition - worldPlanePosition);\n        localPosition = (inverse(modelMatrix) * worldPosition).xyz;\n      }\n\n      gl_Position = projectionMatrix * viewMatrix * worldPosition;\n    }\n  `, /* glsl */`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float fadeFrom;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = localPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1.0 - thickness;\n      return 1.0 - min(line, 1.0);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      vec3 from = worldCamProjPosition*vec3(fadeFrom);\n      float dist = distance(from, worldPosition.xyz);\n      float d = 1.0 - min(dist / fadeDistance, 1.0);\n      vec3 color = mix(cellColor, sectionColor, min(1.0, sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d, fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n    }\n  `);\nconst Grid = /* @__PURE__ */React.forwardRef(({\n  args,\n  cellColor = '#000000',\n  sectionColor = '#2080ff',\n  cellSize = 0.5,\n  sectionSize = 1,\n  followCamera = false,\n  infiniteGrid = false,\n  fadeDistance = 100,\n  fadeStrength = 1,\n  fadeFrom = 1,\n  cellThickness = 0.5,\n  sectionThickness = 1,\n  side = THREE.BackSide,\n  ...props\n}, fRef) => {\n  extend({\n    GridMaterial\n  });\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  const plane = new THREE.Plane();\n  const upVector = new THREE.Vector3(0, 1, 0);\n  const zeroVector = new THREE.Vector3(0, 0, 0);\n  useFrame(state => {\n    plane.setFromNormalAndCoplanarPoint(upVector, zeroVector).applyMatrix4(ref.current.matrixWorld);\n    const gridMaterial = ref.current.material;\n    const worldCamProjPosition = gridMaterial.uniforms.worldCamProjPosition;\n    const worldPlanePosition = gridMaterial.uniforms.worldPlanePosition;\n    plane.projectPoint(state.camera.position, worldCamProjPosition.value);\n    worldPlanePosition.value.set(0, 0, 0).applyMatrix4(ref.current.matrixWorld);\n  });\n  const uniforms1 = {\n    cellSize,\n    sectionSize,\n    cellColor,\n    sectionColor,\n    cellThickness,\n    sectionThickness\n  };\n  const uniforms2 = {\n    fadeDistance,\n    fadeStrength,\n    fadeFrom,\n    infiniteGrid,\n    followCamera\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"gridMaterial\", _extends({\n    transparent: true,\n    \"extensions-derivatives\": true,\n    side: side\n  }, uniforms1, uniforms2)), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }));\n});\n\nexport { Grid };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,YAAY,GAAG,eAAeF,cAAc,CAAC;EACjDG,QAAQ,EAAE,GAAG;EACbC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,GAAG;EACjBC,YAAY,EAAE,CAAC;EACfC,QAAQ,EAAE,CAAC;EACXC,aAAa,EAAE,GAAG;EAClBC,gBAAgB,EAAE,CAAC;EACnBC,SAAS,EAAE,eAAe,IAAIb,KAAK,CAACc,KAAK,CAAC,CAAC;EAC3CC,YAAY,EAAE,eAAe,IAAIf,KAAK,CAACc,KAAK,CAAC,CAAC;EAC9CE,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,KAAK;EACnBC,oBAAoB,EAAE,eAAe,IAAIlB,KAAK,CAACmB,OAAO,CAAC,CAAC;EACxDC,kBAAkB,EAAE,eAAe,IAAIpB,KAAK,CAACmB,OAAO,CAAC;AACvD,CAAC,EAAE,UAAU;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE,UAAU;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBf,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC/E;AACA,GAAG,CAAC;AACJ,MAAMiB,IAAI,GAAG,eAAetB,KAAK,CAACuB,UAAU,CAAC,CAAC;EAC5CC,IAAI;EACJV,SAAS,GAAG,SAAS;EACrBE,YAAY,GAAG,SAAS;EACxBT,QAAQ,GAAG,GAAG;EACdC,WAAW,GAAG,CAAC;EACfU,YAAY,GAAG,KAAK;EACpBD,YAAY,GAAG,KAAK;EACpBR,YAAY,GAAG,GAAG;EAClBC,YAAY,GAAG,CAAC;EAChBC,QAAQ,GAAG,CAAC;EACZC,aAAa,GAAG,GAAG;EACnBC,gBAAgB,GAAG,CAAC;EACpBY,IAAI,GAAGxB,KAAK,CAACyB,QAAQ;EACrB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV1B,MAAM,CAAC;IACLI;EACF,CAAC,CAAC;EACF,MAAMuB,GAAG,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC;EAC9B9B,KAAK,CAAC+B,mBAAmB,CAACH,IAAI,EAAE,MAAMC,GAAG,CAACG,OAAO,EAAE,EAAE,CAAC;EACtD,MAAMC,KAAK,GAAG,IAAIhC,KAAK,CAACiC,KAAK,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAG,IAAIlC,KAAK,CAACmB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,MAAMgB,UAAU,GAAG,IAAInC,KAAK,CAACmB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7CjB,QAAQ,CAACkC,KAAK,IAAI;IAChBJ,KAAK,CAACK,6BAA6B,CAACH,QAAQ,EAAEC,UAAU,CAAC,CAACG,YAAY,CAACV,GAAG,CAACG,OAAO,CAACQ,WAAW,CAAC;IAC/F,MAAMC,YAAY,GAAGZ,GAAG,CAACG,OAAO,CAACU,QAAQ;IACzC,MAAMvB,oBAAoB,GAAGsB,YAAY,CAACE,QAAQ,CAACxB,oBAAoB;IACvE,MAAME,kBAAkB,GAAGoB,YAAY,CAACE,QAAQ,CAACtB,kBAAkB;IACnEY,KAAK,CAACW,YAAY,CAACP,KAAK,CAACQ,MAAM,CAACC,QAAQ,EAAE3B,oBAAoB,CAAC4B,KAAK,CAAC;IACrE1B,kBAAkB,CAAC0B,KAAK,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACT,YAAY,CAACV,GAAG,CAACG,OAAO,CAACQ,WAAW,CAAC;EAC7E,CAAC,CAAC;EACF,MAAMS,SAAS,GAAG;IAChB1C,QAAQ;IACRC,WAAW;IACXM,SAAS;IACTE,YAAY;IACZJ,aAAa;IACbC;EACF,CAAC;EACD,MAAMqC,SAAS,GAAG;IAChBzC,YAAY;IACZC,YAAY;IACZC,QAAQ;IACRM,YAAY;IACZC;EACF,CAAC;EACD,OAAO,aAAalB,KAAK,CAACmD,aAAa,CAAC,MAAM,EAAEpD,QAAQ,CAAC;IACvD8B,GAAG,EAAEA,GAAG;IACRuB,aAAa,EAAE;EACjB,CAAC,EAAEzB,KAAK,CAAC,EAAE,aAAa3B,KAAK,CAACmD,aAAa,CAAC,cAAc,EAAEpD,QAAQ,CAAC;IACnEsD,WAAW,EAAE,IAAI;IACjB,wBAAwB,EAAE,IAAI;IAC9B5B,IAAI,EAAEA;EACR,CAAC,EAAEwB,SAAS,EAAEC,SAAS,CAAC,CAAC,EAAE,aAAalD,KAAK,CAACmD,aAAa,CAAC,eAAe,EAAE;IAC3E3B,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}