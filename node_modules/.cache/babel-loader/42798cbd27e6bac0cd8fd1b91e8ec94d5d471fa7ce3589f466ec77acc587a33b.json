{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nfunction MultiMaterial(props) {\n  const group = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = group.current.parent;\n    const geometry = parent == null ? void 0 : parent.geometry;\n    if (geometry) {\n      const oldMaterial = parent.material;\n      parent.material = group.current.__r3f.children.map(instance => instance.object);\n      const oldGroups = [...geometry.groups];\n      geometry.clearGroups();\n      parent.material.forEach((material, index) => {\n        if (index < parent.material.length - 1) material.depthWrite = false;\n        geometry.addGroup(0, Infinity, index);\n      });\n      return () => {\n        parent.material = oldMaterial;\n        geometry.groups = oldGroups;\n      };\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props));\n}\nexport { MultiMaterial };", "map": {"version": 3, "names": ["_extends", "React", "MultiMaterial", "props", "group", "useRef", "useLayoutEffect", "parent", "current", "geometry", "oldMaterial", "material", "__r3f", "children", "map", "instance", "object", "oldGroups", "groups", "clearGroups", "for<PERSON>ach", "index", "length", "depthWrite", "addGroup", "Infinity", "createElement", "ref"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MultiMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\n\nfunction MultiMaterial(props) {\n  const group = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = group.current.parent;\n    const geometry = parent == null ? void 0 : parent.geometry;\n    if (geometry) {\n      const oldMaterial = parent.material;\n      parent.material = group.current.__r3f.children.map(instance => instance.object);\n      const oldGroups = [...geometry.groups];\n      geometry.clearGroups();\n      parent.material.forEach((material, index) => {\n        if (index < parent.material.length - 1) material.depthWrite = false;\n        geometry.addGroup(0, Infinity, index);\n      });\n      return () => {\n        parent.material = oldMaterial;\n        geometry.groups = oldGroups;\n      };\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props));\n}\n\nexport { MultiMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,MAAMC,KAAK,GAAGH,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EAChCJ,KAAK,CAACK,eAAe,CAAC,MAAM;IAC1B,MAAMC,MAAM,GAAGH,KAAK,CAACI,OAAO,CAACD,MAAM;IACnC,MAAME,QAAQ,GAAGF,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,QAAQ;IAC1D,IAAIA,QAAQ,EAAE;MACZ,MAAMC,WAAW,GAAGH,MAAM,CAACI,QAAQ;MACnCJ,MAAM,CAACI,QAAQ,GAAGP,KAAK,CAACI,OAAO,CAACI,KAAK,CAACC,QAAQ,CAACC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC;MAC/E,MAAMC,SAAS,GAAG,CAAC,GAAGR,QAAQ,CAACS,MAAM,CAAC;MACtCT,QAAQ,CAACU,WAAW,CAAC,CAAC;MACtBZ,MAAM,CAACI,QAAQ,CAACS,OAAO,CAAC,CAACT,QAAQ,EAAEU,KAAK,KAAK;QAC3C,IAAIA,KAAK,GAAGd,MAAM,CAACI,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAEX,QAAQ,CAACY,UAAU,GAAG,KAAK;QACnEd,QAAQ,CAACe,QAAQ,CAAC,CAAC,EAAEC,QAAQ,EAAEJ,KAAK,CAAC;MACvC,CAAC,CAAC;MACF,OAAO,MAAM;QACXd,MAAM,CAACI,QAAQ,GAAGD,WAAW;QAC7BD,QAAQ,CAACS,MAAM,GAAGD,SAAS;MAC7B,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO,aAAahB,KAAK,CAACyB,aAAa,CAAC,OAAO,EAAE1B,QAAQ,CAAC;IACxD2B,GAAG,EAAEvB;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ;AAEA,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}