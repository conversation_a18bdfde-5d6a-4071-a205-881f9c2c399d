{"ast": null, "code": "import { <PERSON><PERSON>, FileLoader, Group, Mesh, BufferGeometry, Vector3, Quaternion, BufferAttribute, MeshBasicMaterial, DoubleSide, RawShaderMaterial, TextureLoader } from \"three\";\nimport { unzipSync, strFromU8 } from \"fflate\";\nclass TiltLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(buffer) {\n    const group = new Group();\n    const zip = unzipSync(new Uint8Array(buffer.slice(16)));\n    const metadata = JSON.parse(strFromU8(zip[\"metadata.json\"]));\n    const data = new DataView(zip[\"data.sketch\"].buffer);\n    const num_strokes = data.getInt32(16, true);\n    const brushes = {};\n    let offset = 20;\n    for (let i = 0; i < num_strokes; i++) {\n      const brush_index = data.getInt32(offset, true);\n      const brush_color = [data.getFloat32(offset + 4, true), data.getFloat32(offset + 8, true), data.getFloat32(offset + 12, true), data.getFloat32(offset + 16, true)];\n      const brush_size = data.getFloat32(offset + 20, true);\n      const stroke_mask = data.getUint32(offset + 24, true);\n      const controlpoint_mask = data.getUint32(offset + 28, true);\n      let offset_stroke_mask = 0;\n      let offset_controlpoint_mask = 0;\n      for (let j = 0; j < 4; j++) {\n        const byte = 1 << j;\n        if ((stroke_mask & byte) > 0) offset_stroke_mask += 4;\n        if ((controlpoint_mask & byte) > 0) offset_controlpoint_mask += 4;\n      }\n      offset = offset + 28 + offset_stroke_mask + 4;\n      const num_control_points = data.getInt32(offset, true);\n      const positions = new Float32Array(num_control_points * 3);\n      const quaternions = new Float32Array(num_control_points * 4);\n      offset = offset + 4;\n      for (let j = 0, k = 0; j < positions.length; j += 3, k += 4) {\n        positions[j + 0] = data.getFloat32(offset + 0, true);\n        positions[j + 1] = data.getFloat32(offset + 4, true);\n        positions[j + 2] = data.getFloat32(offset + 8, true);\n        quaternions[k + 0] = data.getFloat32(offset + 12, true);\n        quaternions[k + 1] = data.getFloat32(offset + 16, true);\n        quaternions[k + 2] = data.getFloat32(offset + 20, true);\n        quaternions[k + 3] = data.getFloat32(offset + 24, true);\n        offset = offset + 28 + offset_controlpoint_mask;\n      }\n      if (brush_index in brushes === false) {\n        brushes[brush_index] = [];\n      }\n      brushes[brush_index].push([positions, quaternions, brush_size, brush_color]);\n    }\n    for (const brush_index in brushes) {\n      const geometry = new StrokeGeometry(brushes[brush_index]);\n      const material = getMaterial(metadata.BrushIndex[brush_index]);\n      group.add(new Mesh(geometry, material));\n    }\n    return group;\n  }\n}\nclass StrokeGeometry extends BufferGeometry {\n  constructor(strokes) {\n    super();\n    const vertices = [];\n    const colors = [];\n    const uvs = [];\n    const position = new Vector3();\n    const prevPosition = new Vector3();\n    const quaternion = new Quaternion();\n    const prevQuaternion = new Quaternion();\n    const vector1 = new Vector3();\n    const vector2 = new Vector3();\n    const vector3 = new Vector3();\n    const vector4 = new Vector3();\n    for (const k in strokes) {\n      const stroke = strokes[k];\n      const positions = stroke[0];\n      const quaternions = stroke[1];\n      const size = stroke[2];\n      const color = stroke[3];\n      prevPosition.fromArray(positions, 0);\n      prevQuaternion.fromArray(quaternions, 0);\n      for (let i = 3, j = 4, l = positions.length; i < l; i += 3, j += 4) {\n        position.fromArray(positions, i);\n        quaternion.fromArray(quaternions, j);\n        vector1.set(-size, 0, 0);\n        vector1.applyQuaternion(quaternion);\n        vector1.add(position);\n        vector2.set(size, 0, 0);\n        vector2.applyQuaternion(quaternion);\n        vector2.add(position);\n        vector3.set(size, 0, 0);\n        vector3.applyQuaternion(prevQuaternion);\n        vector3.add(prevPosition);\n        vector4.set(-size, 0, 0);\n        vector4.applyQuaternion(prevQuaternion);\n        vector4.add(prevPosition);\n        vertices.push(vector1.x, vector1.y, -vector1.z);\n        vertices.push(vector2.x, vector2.y, -vector2.z);\n        vertices.push(vector4.x, vector4.y, -vector4.z);\n        vertices.push(vector2.x, vector2.y, -vector2.z);\n        vertices.push(vector3.x, vector3.y, -vector3.z);\n        vertices.push(vector4.x, vector4.y, -vector4.z);\n        prevPosition.copy(position);\n        prevQuaternion.copy(quaternion);\n        colors.push(...color);\n        colors.push(...color);\n        colors.push(...color);\n        colors.push(...color);\n        colors.push(...color);\n        colors.push(...color);\n        const p1 = i / l;\n        const p2 = (i - 3) / l;\n        uvs.push(p1, 0);\n        uvs.push(p1, 1);\n        uvs.push(p2, 0);\n        uvs.push(p1, 1);\n        uvs.push(p2, 1);\n        uvs.push(p2, 0);\n      }\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n    this.setAttribute(\"color\", new BufferAttribute(new Float32Array(colors), 4));\n    this.setAttribute(\"uv\", new BufferAttribute(new Float32Array(uvs), 2));\n  }\n}\nconst BRUSH_LIST_ARRAY = {\n  \"89d104cd-d012-426b-b5b3-bbaee63ac43c\": \"Bubbles\",\n  \"700f3aa8-9a7c-2384-8b8a-ea028905dd8c\": \"CelVinyl\",\n  \"0f0ff7b2-a677-45eb-a7d6-0cd7206f4816\": \"ChromaticWave\",\n  \"1161af82-50cf-47db-9706-0c3576d43c43\": \"CoarseBristles\",\n  \"79168f10-6961-464a-8be1-57ed364c5600\": \"CoarseBristlesSingleSided\",\n  \"1caa6d7d-f015-3f54-3a4b-8b5354d39f81\": \"Comet\",\n  \"c8313697-2563-47fc-832e-290f4c04b901\": \"DiamondHull\",\n  \"4391aaaa-df73-4396-9e33-31e4e4930b27\": \"Disco\",\n  \"d1d991f2-e7a0-4cf1-b328-f57e915e6260\": \"DotMarker\",\n  \"6a1cf9f9-032c-45ec-9b1d-a6680bee30f7\": \"Dots\",\n  \"0d3889f3-3ede-470c-8af4-f44813306126\": \"DoubleTaperedFlat\",\n  \"0d3889f3-3ede-470c-8af4-de4813306126\": \"DoubleTaperedMarker\",\n  \"d0262945-853c-4481-9cbd-88586bed93cb\": \"DuctTape\",\n  \"3ca16e2f-bdcd-4da2-8631-dcef342f40f1\": \"DuctTapeSingleSided\",\n  \"f6e85de3-6dcc-4e7f-87fd-cee8c3d25d51\": \"Electricity\",\n  \"02ffb866-7fb2-4d15-b761-1012cefb1360\": \"Embers\",\n  \"cb92b597-94ca-4255-b017-0e3f42f12f9e\": \"Fire\",\n  \"2d35bcf0-e4d8-452c-97b1-3311be063130\": \"Flat\",\n  \"55303bc4-c749-4a72-98d9-d23e68e76e18\": \"FlatDeprecated\",\n  \"280c0a7a-aad8-416c-a7d2-df63d129ca70\": \"FlatSingleSided\",\n  \"cf019139-d41c-4eb0-a1d0-5cf54b0a42f3\": \"Highlighter\",\n  \"6a1cf9f9-032c-45ec-9b6e-a6680bee32e9\": \"HyperGrid\",\n  \"dce872c2-7b49-4684-b59b-c45387949c5c\": \"Hypercolor\",\n  \"e8ef32b1-baa8-460a-9c2c-9cf8506794f5\": \"HypercolorSingleSided\",\n  \"2f212815-f4d3-c1a4-681a-feeaf9c6dc37\": \"Icing\",\n  \"f5c336cf-5108-4b40-ade9-c687504385ab\": \"Ink\",\n  \"c0012095-3ffd-4040-8ee1-fc180d346eaa\": \"InkSingleSided\",\n  \"4a76a27a-44d8-4bfe-9a8c-713749a499b0\": \"Leaves\",\n  \"ea19de07-d0c0-4484-9198-18489a3c1487\": \"LeavesSingleSided\",\n  \"2241cd32-8ba2-48a5-9ee7-2caef7e9ed62\": \"Light\",\n  \"4391aaaa-df81-4396-9e33-31e4e4930b27\": \"LightWire\",\n  \"d381e0f5-3def-4a0d-8853-31e9200bcbda\": \"Lofted\",\n  \"429ed64a-4e97-4466-84d3-145a861ef684\": \"Marker\",\n  \"79348357-432d-4746-8e29-0e25c112e3aa\": \"MatteHull\",\n  \"b2ffef01-eaaa-4ab5-aa64-95a2c4f5dbc6\": \"NeonPulse\",\n  \"f72ec0e7-a844-4e38-82e3-140c44772699\": \"OilPaint\",\n  \"c515dad7-4393-4681-81ad-162ef052241b\": \"OilPaintSingleSided\",\n  \"f1114e2e-eb8d-4fde-915a-6e653b54e9f5\": \"Paper\",\n  \"759f1ebd-20cd-4720-8d41-234e0da63716\": \"PaperSingleSided\",\n  \"e0abbc80-0f80-e854-4970-8924a0863dcc\": \"Petal\",\n  \"c33714d1-b2f9-412e-bd50-1884c9d46336\": \"Plasma\",\n  \"ad1ad437-76e2-450d-a23a-e17f8310b960\": \"Rainbow\",\n  \"faaa4d44-fcfb-4177-96be-753ac0421ba3\": \"ShinyHull\",\n  \"70d79cca-b159-4f35-990c-f02193947fe8\": \"Smoke\",\n  \"d902ed8b-d0d1-476c-a8de-878a79e3a34c\": \"Snow\",\n  \"accb32f5-4509-454f-93f8-1df3fd31df1b\": \"SoftHighlighter\",\n  \"cf7f0059-7aeb-53a4-2b67-c83d863a9ffa\": \"Spikes\",\n  \"8dc4a70c-d558-4efd-a5ed-d4e860f40dc3\": \"Splatter\",\n  \"7a1c8107-50c5-4b70-9a39-421576d6617e\": \"SplatterSingleSided\",\n  \"0eb4db27-3f82-408d-b5a1-19ebd7d5b711\": \"Stars\",\n  \"44bb800a-fbc3-4592-8426-94ecb05ddec3\": \"Streamers\",\n  \"0077f88c-d93a-42f3-b59b-b31c50cdb414\": \"Taffy\",\n  \"b468c1fb-f254-41ed-8ec9-57030bc5660c\": \"TaperedFlat\",\n  \"c8ccb53d-ae13-45ef-8afb-b730d81394eb\": \"TaperedFlatSingleSided\",\n  \"d90c6ad8-af0f-4b54-b422-e0f92abe1b3c\": \"TaperedMarker\",\n  \"1a26b8c0-8a07-4f8a-9fac-d2ef36e0cad0\": \"TaperedMarker_Flat\",\n  \"75b32cf0-fdd6-4d89-a64b-e2a00b247b0f\": \"ThickPaint\",\n  \"fdf0326a-c0d1-4fed-b101-9db0ff6d071f\": \"ThickPaintSingleSided\",\n  \"4391385a-df73-4396-9e33-31e4e4930b27\": \"Toon\",\n  \"a8fea537-da7c-4d4b-817f-24f074725d6d\": \"UnlitHull\",\n  \"d229d335-c334-495a-a801-660ac8a87360\": \"VelvetInk\",\n  \"10201aa3-ebc2-42d8-84b7-2e63f6eeb8ab\": \"Waveform\",\n  \"b67c0e81-ce6d-40a8-aeb0-ef036b081aa3\": \"WetPaint\",\n  \"dea67637-cd1a-27e4-c9b1-52f4bbcb84e5\": \"WetPaintSingleSided\",\n  \"5347acf0-a8e2-47b6-8346-30c70719d763\": \"WigglyGraphite\",\n  \"e814fef1-97fd-7194-4a2f-50c2bb918be2\": \"WigglyGraphiteSingleSided\",\n  \"4391385a-cf83-4396-9e33-31e4e4930b27\": \"Wire\"\n};\nconst common = {\n  colors: {\n    BloomColor: `\n\t\t\tvec3 BloomColor(vec3 color, float gain) {\n\t\t\t\t// Guarantee that there's at least a little bit of all 3 channels.\n\t\t\t\t// This makes fully-saturated strokes (which only have 2 non-zero\n\t\t\t\t// color channels) eventually clip to white rather than to a secondary.\n\t\t\t\tfloat cmin = length(color.rgb) * .05;\n\t\t\t\tcolor.rgb = max(color.rgb, vec3(cmin, cmin, cmin));\n\t\t\t\t// If we try to remove this pow() from .a, it brightens up\n\t\t\t\t// pressure-sensitive strokes; looks better as-is.\n\t\t\t\tcolor = pow(color, vec3(2.2));\n\t\t\t\tcolor.rgb *= 2. * exp(gain * 10.);\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`,\n    LinearToSrgb: `\n\t\t\tvec3 LinearToSrgb(vec3 color) {\n\t\t\t\t// Approximation http://chilliant.blogspot.com/2012/08/srgb-approximations-for-hlsl.html\n\t\t\t\tvec3 linearColor = color.rgb;\n\t\t\t\tvec3 S1 = sqrt(linearColor);\n\t\t\t\tvec3 S2 = sqrt(S1);\n\t\t\t\tvec3 S3 = sqrt(S2);\n\t\t\t\tcolor.rgb = 0.662002687 * S1 + 0.684122060 * S2 - 0.323583601 * S3 - 0.0225411470 * linearColor;\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`,\n    hsv: `\n\t\t\t// uniform sampler2D lookupTex;\n\t\t\tvec4 lookup(vec4 textureColor) {\n\t\t\t\treturn textureColor;\n\t\t\t}\n\n\t\t\tvec3 lookup(vec3 textureColor) {\n\t\t\t\treturn textureColor;\n\t\t\t}\n\n\t\t\tvec3 hsv2rgb( vec3 hsv ) {\n\t\t\t\tvec3 rgb = clamp( abs(mod(hsv.x*6.0+vec3(0.0,4.0,2.0),6.0)-3.0)-1.0, 0.0, 1.0 );\n\t\t\t\treturn hsv.z * mix( vec3(1.0), rgb, hsv.y);\n\t\t\t}\n\n\t\t\tvec3 rgb2hsv( vec3 rgb ) {\n\t\t\t\tvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\n\t\t\t\tvec4 p = mix(vec4(rgb.bg, K.wz), vec4(rgb.gb, K.xy), step(rgb.b, rgb.g));\n\t\t\t\tvec4 q = mix(vec4(p.xyw, rgb.r), vec4(rgb.r, p.yzx), step(p.x, rgb.r));\n\n\t\t\t\tfloat d = q.x - min(q.w, q.y);\n\t\t\t\tfloat e = 1.0e-10;\n\n\t\t\t\treturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);\n\t\t\t}\n\t\t`,\n    SrgbToLinear: `\n\t\t\tvec3 SrgbToLinear(vec3 color) {\n\t\t\t\t// Approximation http://chilliant.blogspot.com/2012/08/srgb-approximations-for-hlsl.html\n\t\t\t\tvec3 sRGB = color.rgb;\n\t\t\t\tcolor.rgb = sRGB * (sRGB * (sRGB * 0.305306011 + 0.682171111) + 0.012522878);\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`\n  }\n};\nconst shaders = () => ({\n  Light: {\n    uniforms: {\n      mainTex: {\n        value: new TextureLoader().setPath(\"./textures/tiltbrush/\").loader.load(\"Light.webp\")\n      },\n      alphaTest: {\n        value: 0.067\n      },\n      emission_gain: {\n        value: 0.45\n      },\n      alpha: {\n        value: 1\n      }\n    },\n    vertexShader: `\n\t\t\tprecision highp float;\n\t\t\tprecision highp int;\n\n\t\t\tattribute vec2 uv;\n\t\t\tattribute vec4 color;\n\t\t\tattribute vec3 position;\n\n\t\t\tuniform mat4 modelMatrix;\n\t\t\tuniform mat4 modelViewMatrix;\n\t\t\tuniform mat4 projectionMatrix;\n\t\t\tuniform mat4 viewMatrix;\n\t\t\tuniform mat3 normalMatrix;\n\t\t\tuniform vec3 cameraPosition;\n\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 vColor;\n\n\t\t\t${common.colors.LinearToSrgb}\n\t\t\t${common.colors.hsv}\n\n\t\t\tvoid main() {\n\n\t\t\t\tvUv = uv;\n\n\t\t\t\tvColor = lookup(color.rgb);\n\n\t\t\t\tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t}\n\t\t`,\n    fragmentShader: `\n\t\t\tprecision highp float;\n\t\t\tprecision highp int;\n\n\t\t\tuniform float emission_gain;\n\n\t\t\tuniform sampler2D mainTex;\n\t\t\tuniform float alphaTest;\n\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 vColor;\n\n\t\t\t${common.colors.BloomColor}\n\t\t\t${common.colors.SrgbToLinear}\n\n\t\t\tvoid main(){\n\t\t\t\tvec4 col = texture2D(mainTex, vUv);\n\t\t\t\tvec3 color = vColor;\n\t\t\t\tcolor = BloomColor(color, emission_gain);\n\t\t\t\tcolor = color * col.rgb;\n\t\t\t\tcolor = color * col.a;\n\t\t\t\tcolor = SrgbToLinear(color);\n\t\t\t\tgl_FragColor = vec4(color, 1.0);\n\t\t\t}\n\t\t`,\n    side: 2,\n    transparent: true,\n    depthFunc: 2,\n    depthWrite: true,\n    depthTest: false,\n    blending: 5,\n    blendDst: 201,\n    blendDstAlpha: 201,\n    blendEquation: 100,\n    blendEquationAlpha: 100,\n    blendSrc: 201,\n    blendSrcAlpha: 201\n  }\n});\nfunction getMaterial(GUID) {\n  const name = BRUSH_LIST_ARRAY[GUID];\n  switch (name) {\n    case \"Light\":\n      return new RawShaderMaterial(shaders().Light);\n    default:\n      return new MeshBasicMaterial({\n        vertexColors: true,\n        side: DoubleSide\n      });\n  }\n}\nexport { TiltLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setResponseType", "setWithCredentials", "withCredentials", "buffer", "parse", "e", "console", "error", "itemError", "group", "Group", "zip", "unzipSync", "Uint8Array", "slice", "metadata", "JSON", "strFromU8", "data", "DataView", "num_strokes", "getInt32", "brushes", "offset", "i", "brush_index", "brush_color", "getFloat32", "brush_size", "stroke_mask", "getUint32", "controlpoint_mask", "offset_stroke_mask", "offset_controlpoint_mask", "j", "byte", "num_control_points", "positions", "Float32Array", "quaternions", "k", "length", "push", "geometry", "StrokeGeometry", "material", "getMaterial", "BrushIndex", "add", "<PERSON><PERSON>", "BufferGeometry", "constructor", "strokes", "vertices", "colors", "uvs", "position", "Vector3", "prevPosition", "quaternion", "Quaternion", "prevQuaternion", "vector1", "vector2", "vector3", "vector4", "stroke", "size", "color", "fromArray", "l", "set", "applyQuaternion", "x", "y", "z", "copy", "p1", "p2", "setAttribute", "BufferAttribute", "BRUSH_LIST_ARRAY", "common", "BloomColor", "LinearToSrgb", "hsv", "SrgbToLinear", "shaders", "Light", "uniforms", "mainTex", "value", "TextureLoader", "alphaTest", "emission_gain", "alpha", "vertexShader", "fragmentShader", "side", "transparent", "depthFunc", "depthWrite", "depthTest", "blending", "blendDst", "blendDstAlpha", "blendEquation", "blendEquationAlpha", "blendSrc", "blendSrcAlpha", "GUID", "name", "RawShaderMaterial", "MeshBasicMaterial", "vertexColors", "DoubleSide"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/TiltLoader.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  DoubleSide,\n  FileLoader,\n  Group,\n  Loader,\n  Mesh,\n  MeshBasicMaterial,\n  RawShaderMaterial,\n  TextureLoader,\n  Quaternion,\n  Vector3,\n} from 'three'\nimport { unzipSync, strFromU8 } from 'fflate'\n\nclass TiltLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer) {\n    const group = new Group()\n    // https://docs.google.com/document/d/11ZsHozYn9FnWG7y3s3WAyKIACfbfwb4PbaS8cZ_xjvo/edit#\n\n    const zip = unzipSync(new Uint8Array(buffer.slice(16)))\n\n    /*\n\t\tconst thumbnail = zip[ 'thumbnail.png' ].buffer;\n\t\tconst img = document.createElement( 'img' );\n\t\timg.src = URL.createObjectURL( new Blob( [ thumbnail ] ) );\n\t\tdocument.body.appendChild( img );\n\t\t*/\n\n    const metadata = JSON.parse(strFromU8(zip['metadata.json']))\n\n    /*\n\t\tconst blob = new Blob( [ zip[ 'data.sketch' ].buffer ], { type: 'application/octet-stream' } );\n\t\twindow.open( URL.createObjectURL( blob ) );\n\t\t*/\n\n    const data = new DataView(zip['data.sketch'].buffer)\n\n    const num_strokes = data.getInt32(16, true)\n\n    const brushes = {}\n\n    let offset = 20\n\n    for (let i = 0; i < num_strokes; i++) {\n      const brush_index = data.getInt32(offset, true)\n\n      const brush_color = [\n        data.getFloat32(offset + 4, true),\n        data.getFloat32(offset + 8, true),\n        data.getFloat32(offset + 12, true),\n        data.getFloat32(offset + 16, true),\n      ]\n      const brush_size = data.getFloat32(offset + 20, true)\n      const stroke_mask = data.getUint32(offset + 24, true)\n      const controlpoint_mask = data.getUint32(offset + 28, true)\n\n      let offset_stroke_mask = 0\n      let offset_controlpoint_mask = 0\n\n      for (let j = 0; j < 4; j++) {\n        // TOFIX: I don't understand these masks yet\n\n        const byte = 1 << j\n        if ((stroke_mask & byte) > 0) offset_stroke_mask += 4\n        if ((controlpoint_mask & byte) > 0) offset_controlpoint_mask += 4\n      }\n\n      // console.log( { brush_index, brush_color, brush_size, stroke_mask, controlpoint_mask } );\n      // console.log( offset_stroke_mask, offset_controlpoint_mask );\n\n      offset = offset + 28 + offset_stroke_mask + 4 // TOFIX: This is wrong\n\n      const num_control_points = data.getInt32(offset, true)\n\n      // console.log( { num_control_points } );\n\n      const positions = new Float32Array(num_control_points * 3)\n      const quaternions = new Float32Array(num_control_points * 4)\n\n      offset = offset + 4\n\n      for (let j = 0, k = 0; j < positions.length; j += 3, k += 4) {\n        positions[j + 0] = data.getFloat32(offset + 0, true)\n        positions[j + 1] = data.getFloat32(offset + 4, true)\n        positions[j + 2] = data.getFloat32(offset + 8, true)\n\n        quaternions[k + 0] = data.getFloat32(offset + 12, true)\n        quaternions[k + 1] = data.getFloat32(offset + 16, true)\n        quaternions[k + 2] = data.getFloat32(offset + 20, true)\n        quaternions[k + 3] = data.getFloat32(offset + 24, true)\n\n        offset = offset + 28 + offset_controlpoint_mask // TOFIX: This is wrong\n      }\n\n      if (brush_index in brushes === false) {\n        brushes[brush_index] = []\n      }\n\n      brushes[brush_index].push([positions, quaternions, brush_size, brush_color])\n    }\n\n    for (const brush_index in brushes) {\n      const geometry = new StrokeGeometry(brushes[brush_index])\n      const material = getMaterial(metadata.BrushIndex[brush_index])\n\n      group.add(new Mesh(geometry, material))\n    }\n\n    return group\n  }\n}\n\nclass StrokeGeometry extends BufferGeometry {\n  constructor(strokes) {\n    super()\n\n    const vertices = []\n    const colors = []\n    const uvs = []\n\n    const position = new Vector3()\n    const prevPosition = new Vector3()\n\n    const quaternion = new Quaternion()\n    const prevQuaternion = new Quaternion()\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    // size = size / 2;\n\n    for (const k in strokes) {\n      const stroke = strokes[k]\n      const positions = stroke[0]\n      const quaternions = stroke[1]\n      const size = stroke[2]\n      const color = stroke[3]\n\n      prevPosition.fromArray(positions, 0)\n      prevQuaternion.fromArray(quaternions, 0)\n\n      for (let i = 3, j = 4, l = positions.length; i < l; i += 3, j += 4) {\n        position.fromArray(positions, i)\n        quaternion.fromArray(quaternions, j)\n\n        vector1.set(-size, 0, 0)\n        vector1.applyQuaternion(quaternion)\n        vector1.add(position)\n\n        vector2.set(size, 0, 0)\n        vector2.applyQuaternion(quaternion)\n        vector2.add(position)\n\n        vector3.set(size, 0, 0)\n        vector3.applyQuaternion(prevQuaternion)\n        vector3.add(prevPosition)\n\n        vector4.set(-size, 0, 0)\n        vector4.applyQuaternion(prevQuaternion)\n        vector4.add(prevPosition)\n\n        vertices.push(vector1.x, vector1.y, -vector1.z)\n        vertices.push(vector2.x, vector2.y, -vector2.z)\n        vertices.push(vector4.x, vector4.y, -vector4.z)\n\n        vertices.push(vector2.x, vector2.y, -vector2.z)\n        vertices.push(vector3.x, vector3.y, -vector3.z)\n        vertices.push(vector4.x, vector4.y, -vector4.z)\n\n        prevPosition.copy(position)\n        prevQuaternion.copy(quaternion)\n\n        colors.push(...color)\n        colors.push(...color)\n        colors.push(...color)\n\n        colors.push(...color)\n        colors.push(...color)\n        colors.push(...color)\n\n        const p1 = i / l\n        const p2 = (i - 3) / l\n\n        uvs.push(p1, 0)\n        uvs.push(p1, 1)\n        uvs.push(p2, 0)\n\n        uvs.push(p1, 1)\n        uvs.push(p2, 1)\n        uvs.push(p2, 0)\n      }\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('color', new BufferAttribute(new Float32Array(colors), 4))\n    this.setAttribute('uv', new BufferAttribute(new Float32Array(uvs), 2))\n  }\n}\n\nconst BRUSH_LIST_ARRAY = {\n  '89d104cd-d012-426b-b5b3-bbaee63ac43c': 'Bubbles',\n  '700f3aa8-9a7c-2384-8b8a-ea028905dd8c': 'CelVinyl',\n  '0f0ff7b2-a677-45eb-a7d6-0cd7206f4816': 'ChromaticWave',\n  '1161af82-50cf-47db-9706-0c3576d43c43': 'CoarseBristles',\n  '79168f10-6961-464a-8be1-57ed364c5600': 'CoarseBristlesSingleSided',\n  '1caa6d7d-f015-3f54-3a4b-8b5354d39f81': 'Comet',\n  'c8313697-2563-47fc-832e-290f4c04b901': 'DiamondHull',\n  '4391aaaa-df73-4396-9e33-31e4e4930b27': 'Disco',\n  'd1d991f2-e7a0-4cf1-b328-f57e915e6260': 'DotMarker',\n  '6a1cf9f9-032c-45ec-9b1d-a6680bee30f7': 'Dots',\n  '0d3889f3-3ede-470c-8af4-f44813306126': 'DoubleTaperedFlat',\n  '0d3889f3-3ede-470c-8af4-de4813306126': 'DoubleTaperedMarker',\n  'd0262945-853c-4481-9cbd-88586bed93cb': 'DuctTape',\n  '3ca16e2f-bdcd-4da2-8631-dcef342f40f1': 'DuctTapeSingleSided',\n  'f6e85de3-6dcc-4e7f-87fd-cee8c3d25d51': 'Electricity',\n  '02ffb866-7fb2-4d15-b761-1012cefb1360': 'Embers',\n  'cb92b597-94ca-4255-b017-0e3f42f12f9e': 'Fire',\n  '2d35bcf0-e4d8-452c-97b1-3311be063130': 'Flat',\n  '55303bc4-c749-4a72-98d9-d23e68e76e18': 'FlatDeprecated',\n  '280c0a7a-aad8-416c-a7d2-df63d129ca70': 'FlatSingleSided',\n  'cf019139-d41c-4eb0-a1d0-5cf54b0a42f3': 'Highlighter',\n  '6a1cf9f9-032c-45ec-9b6e-a6680bee32e9': 'HyperGrid',\n  'dce872c2-7b49-4684-b59b-c45387949c5c': 'Hypercolor',\n  'e8ef32b1-baa8-460a-9c2c-9cf8506794f5': 'HypercolorSingleSided',\n  '2f212815-f4d3-c1a4-681a-feeaf9c6dc37': 'Icing',\n  'f5c336cf-5108-4b40-ade9-c687504385ab': 'Ink',\n  'c0012095-3ffd-4040-8ee1-fc180d346eaa': 'InkSingleSided',\n  '4a76a27a-44d8-4bfe-9a8c-713749a499b0': 'Leaves',\n  'ea19de07-d0c0-4484-9198-18489a3c1487': 'LeavesSingleSided',\n  '2241cd32-8ba2-48a5-9ee7-2caef7e9ed62': 'Light',\n  '4391aaaa-df81-4396-9e33-31e4e4930b27': 'LightWire',\n  'd381e0f5-3def-4a0d-8853-31e9200bcbda': 'Lofted',\n  '429ed64a-4e97-4466-84d3-145a861ef684': 'Marker',\n  '79348357-432d-4746-8e29-0e25c112e3aa': 'MatteHull',\n  'b2ffef01-eaaa-4ab5-aa64-95a2c4f5dbc6': 'NeonPulse',\n  'f72ec0e7-a844-4e38-82e3-140c44772699': 'OilPaint',\n  'c515dad7-4393-4681-81ad-162ef052241b': 'OilPaintSingleSided',\n  'f1114e2e-eb8d-4fde-915a-6e653b54e9f5': 'Paper',\n  '759f1ebd-20cd-4720-8d41-234e0da63716': 'PaperSingleSided',\n  'e0abbc80-0f80-e854-4970-8924a0863dcc': 'Petal',\n  'c33714d1-b2f9-412e-bd50-1884c9d46336': 'Plasma',\n  'ad1ad437-76e2-450d-a23a-e17f8310b960': 'Rainbow',\n  'faaa4d44-fcfb-4177-96be-753ac0421ba3': 'ShinyHull',\n  '70d79cca-b159-4f35-990c-f02193947fe8': 'Smoke',\n  'd902ed8b-d0d1-476c-a8de-878a79e3a34c': 'Snow',\n  'accb32f5-4509-454f-93f8-1df3fd31df1b': 'SoftHighlighter',\n  'cf7f0059-7aeb-53a4-2b67-c83d863a9ffa': 'Spikes',\n  '8dc4a70c-d558-4efd-a5ed-d4e860f40dc3': 'Splatter',\n  '7a1c8107-50c5-4b70-9a39-421576d6617e': 'SplatterSingleSided',\n  '0eb4db27-3f82-408d-b5a1-19ebd7d5b711': 'Stars',\n  '44bb800a-fbc3-4592-8426-94ecb05ddec3': 'Streamers',\n  '0077f88c-d93a-42f3-b59b-b31c50cdb414': 'Taffy',\n  'b468c1fb-f254-41ed-8ec9-57030bc5660c': 'TaperedFlat',\n  'c8ccb53d-ae13-45ef-8afb-b730d81394eb': 'TaperedFlatSingleSided',\n  'd90c6ad8-af0f-4b54-b422-e0f92abe1b3c': 'TaperedMarker',\n  '1a26b8c0-8a07-4f8a-9fac-d2ef36e0cad0': 'TaperedMarker_Flat',\n  '75b32cf0-fdd6-4d89-a64b-e2a00b247b0f': 'ThickPaint',\n  'fdf0326a-c0d1-4fed-b101-9db0ff6d071f': 'ThickPaintSingleSided',\n  '4391385a-df73-4396-9e33-31e4e4930b27': 'Toon',\n  'a8fea537-da7c-4d4b-817f-24f074725d6d': 'UnlitHull',\n  'd229d335-c334-495a-a801-660ac8a87360': 'VelvetInk',\n  '10201aa3-ebc2-42d8-84b7-2e63f6eeb8ab': 'Waveform',\n  'b67c0e81-ce6d-40a8-aeb0-ef036b081aa3': 'WetPaint',\n  'dea67637-cd1a-27e4-c9b1-52f4bbcb84e5': 'WetPaintSingleSided',\n  '5347acf0-a8e2-47b6-8346-30c70719d763': 'WigglyGraphite',\n  'e814fef1-97fd-7194-4a2f-50c2bb918be2': 'WigglyGraphiteSingleSided',\n  '4391385a-cf83-4396-9e33-31e4e4930b27': 'Wire',\n}\n\nconst common = {\n  colors: {\n    BloomColor: `\n\t\t\tvec3 BloomColor(vec3 color, float gain) {\n\t\t\t\t// Guarantee that there's at least a little bit of all 3 channels.\n\t\t\t\t// This makes fully-saturated strokes (which only have 2 non-zero\n\t\t\t\t// color channels) eventually clip to white rather than to a secondary.\n\t\t\t\tfloat cmin = length(color.rgb) * .05;\n\t\t\t\tcolor.rgb = max(color.rgb, vec3(cmin, cmin, cmin));\n\t\t\t\t// If we try to remove this pow() from .a, it brightens up\n\t\t\t\t// pressure-sensitive strokes; looks better as-is.\n\t\t\t\tcolor = pow(color, vec3(2.2));\n\t\t\t\tcolor.rgb *= 2. * exp(gain * 10.);\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`,\n\n    LinearToSrgb: `\n\t\t\tvec3 LinearToSrgb(vec3 color) {\n\t\t\t\t// Approximation http://chilliant.blogspot.com/2012/08/srgb-approximations-for-hlsl.html\n\t\t\t\tvec3 linearColor = color.rgb;\n\t\t\t\tvec3 S1 = sqrt(linearColor);\n\t\t\t\tvec3 S2 = sqrt(S1);\n\t\t\t\tvec3 S3 = sqrt(S2);\n\t\t\t\tcolor.rgb = 0.662002687 * S1 + 0.684122060 * S2 - 0.323583601 * S3 - 0.0225411470 * linearColor;\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`,\n\n    hsv: `\n\t\t\t// uniform sampler2D lookupTex;\n\t\t\tvec4 lookup(vec4 textureColor) {\n\t\t\t\treturn textureColor;\n\t\t\t}\n\n\t\t\tvec3 lookup(vec3 textureColor) {\n\t\t\t\treturn textureColor;\n\t\t\t}\n\n\t\t\tvec3 hsv2rgb( vec3 hsv ) {\n\t\t\t\tvec3 rgb = clamp( abs(mod(hsv.x*6.0+vec3(0.0,4.0,2.0),6.0)-3.0)-1.0, 0.0, 1.0 );\n\t\t\t\treturn hsv.z * mix( vec3(1.0), rgb, hsv.y);\n\t\t\t}\n\n\t\t\tvec3 rgb2hsv( vec3 rgb ) {\n\t\t\t\tvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\n\t\t\t\tvec4 p = mix(vec4(rgb.bg, K.wz), vec4(rgb.gb, K.xy), step(rgb.b, rgb.g));\n\t\t\t\tvec4 q = mix(vec4(p.xyw, rgb.r), vec4(rgb.r, p.yzx), step(p.x, rgb.r));\n\n\t\t\t\tfloat d = q.x - min(q.w, q.y);\n\t\t\t\tfloat e = 1.0e-10;\n\n\t\t\t\treturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);\n\t\t\t}\n\t\t`,\n\n    SrgbToLinear: `\n\t\t\tvec3 SrgbToLinear(vec3 color) {\n\t\t\t\t// Approximation http://chilliant.blogspot.com/2012/08/srgb-approximations-for-hlsl.html\n\t\t\t\tvec3 sRGB = color.rgb;\n\t\t\t\tcolor.rgb = sRGB * (sRGB * (sRGB * 0.305306011 + 0.682171111) + 0.012522878);\n\t\t\t\treturn color;\n\t\t\t}\n\t\t`,\n  },\n}\n\nconst shaders = () => ({\n  Light: {\n    uniforms: {\n      mainTex: {\n        value: new TextureLoader().setPath('./textures/tiltbrush/').loader.load('Light.webp'),\n      },\n      alphaTest: { value: 0.067 },\n      emission_gain: { value: 0.45 },\n      alpha: { value: 1 },\n    },\n    vertexShader: `\n\t\t\tprecision highp float;\n\t\t\tprecision highp int;\n\n\t\t\tattribute vec2 uv;\n\t\t\tattribute vec4 color;\n\t\t\tattribute vec3 position;\n\n\t\t\tuniform mat4 modelMatrix;\n\t\t\tuniform mat4 modelViewMatrix;\n\t\t\tuniform mat4 projectionMatrix;\n\t\t\tuniform mat4 viewMatrix;\n\t\t\tuniform mat3 normalMatrix;\n\t\t\tuniform vec3 cameraPosition;\n\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 vColor;\n\n\t\t\t${common.colors.LinearToSrgb}\n\t\t\t${common.colors.hsv}\n\n\t\t\tvoid main() {\n\n\t\t\t\tvUv = uv;\n\n\t\t\t\tvColor = lookup(color.rgb);\n\n\t\t\t\tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t}\n\t\t`,\n    fragmentShader: `\n\t\t\tprecision highp float;\n\t\t\tprecision highp int;\n\n\t\t\tuniform float emission_gain;\n\n\t\t\tuniform sampler2D mainTex;\n\t\t\tuniform float alphaTest;\n\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 vColor;\n\n\t\t\t${common.colors.BloomColor}\n\t\t\t${common.colors.SrgbToLinear}\n\n\t\t\tvoid main(){\n\t\t\t\tvec4 col = texture2D(mainTex, vUv);\n\t\t\t\tvec3 color = vColor;\n\t\t\t\tcolor = BloomColor(color, emission_gain);\n\t\t\t\tcolor = color * col.rgb;\n\t\t\t\tcolor = color * col.a;\n\t\t\t\tcolor = SrgbToLinear(color);\n\t\t\t\tgl_FragColor = vec4(color, 1.0);\n\t\t\t}\n\t\t`,\n    side: 2,\n    transparent: true,\n    depthFunc: 2,\n    depthWrite: true,\n    depthTest: false,\n    blending: 5,\n    blendDst: 201,\n    blendDstAlpha: 201,\n    blendEquation: 100,\n    blendEquationAlpha: 100,\n    blendSrc: 201,\n    blendSrcAlpha: 201,\n  },\n})\n\nfunction getMaterial(GUID) {\n  const name = BRUSH_LIST_ARRAY[GUID]\n\n  switch (name) {\n    case 'Light':\n      return new RawShaderMaterial(shaders().Light)\n\n    default:\n      return new MeshBasicMaterial({ vertexColors: true, side: DoubleSide })\n  }\n}\n\nexport { TiltLoader }\n"], "mappings": ";;AAgBA,MAAMA,UAAA,SAAmBC,MAAA,CAAO;EAC9BC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC1CF,MAAA,CAAOG,OAAA,CAAQ,KAAKC,IAAI;IACxBJ,MAAA,CAAOK,eAAA,CAAgB,aAAa;IACpCL,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAE9CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,MAAA,EAAQ;MAChB,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,MAAM,CAAC;MAC3B,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMG,OAAA,CAAQW,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDW,MAAMD,MAAA,EAAQ;IACZ,MAAMM,KAAA,GAAQ,IAAIC,KAAA,CAAO;IAGzB,MAAMC,GAAA,GAAMC,SAAA,CAAU,IAAIC,UAAA,CAAWV,MAAA,CAAOW,KAAA,CAAM,EAAE,CAAC,CAAC;IAStD,MAAMC,QAAA,GAAWC,IAAA,CAAKZ,KAAA,CAAMa,SAAA,CAAUN,GAAA,CAAI,eAAe,CAAC,CAAC;IAO3D,MAAMO,IAAA,GAAO,IAAIC,QAAA,CAASR,GAAA,CAAI,aAAa,EAAER,MAAM;IAEnD,MAAMiB,WAAA,GAAcF,IAAA,CAAKG,QAAA,CAAS,IAAI,IAAI;IAE1C,MAAMC,OAAA,GAAU,CAAE;IAElB,IAAIC,MAAA,GAAS;IAEb,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,WAAA,EAAaI,CAAA,IAAK;MACpC,MAAMC,WAAA,GAAcP,IAAA,CAAKG,QAAA,CAASE,MAAA,EAAQ,IAAI;MAE9C,MAAMG,WAAA,GAAc,CAClBR,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,GAAG,IAAI,GAChCL,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,GAAG,IAAI,GAChCL,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI,GACjCL,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI,EAClC;MACD,MAAMK,UAAA,GAAaV,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI;MACpD,MAAMM,WAAA,GAAcX,IAAA,CAAKY,SAAA,CAAUP,MAAA,GAAS,IAAI,IAAI;MACpD,MAAMQ,iBAAA,GAAoBb,IAAA,CAAKY,SAAA,CAAUP,MAAA,GAAS,IAAI,IAAI;MAE1D,IAAIS,kBAAA,GAAqB;MACzB,IAAIC,wBAAA,GAA2B;MAE/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAG1B,MAAMC,IAAA,GAAO,KAAKD,CAAA;QAClB,KAAKL,WAAA,GAAcM,IAAA,IAAQ,GAAGH,kBAAA,IAAsB;QACpD,KAAKD,iBAAA,GAAoBI,IAAA,IAAQ,GAAGF,wBAAA,IAA4B;MACjE;MAKDV,MAAA,GAASA,MAAA,GAAS,KAAKS,kBAAA,GAAqB;MAE5C,MAAMI,kBAAA,GAAqBlB,IAAA,CAAKG,QAAA,CAASE,MAAA,EAAQ,IAAI;MAIrD,MAAMc,SAAA,GAAY,IAAIC,YAAA,CAAaF,kBAAA,GAAqB,CAAC;MACzD,MAAMG,WAAA,GAAc,IAAID,YAAA,CAAaF,kBAAA,GAAqB,CAAC;MAE3Db,MAAA,GAASA,MAAA,GAAS;MAElB,SAASW,CAAA,GAAI,GAAGM,CAAA,GAAI,GAAGN,CAAA,GAAIG,SAAA,CAAUI,MAAA,EAAQP,CAAA,IAAK,GAAGM,CAAA,IAAK,GAAG;QAC3DH,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAIhB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,GAAG,IAAI;QACnDc,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAIhB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,GAAG,IAAI;QACnDc,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAIhB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,GAAG,IAAI;QAEnDgB,WAAA,CAAYC,CAAA,GAAI,CAAC,IAAItB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI;QACtDgB,WAAA,CAAYC,CAAA,GAAI,CAAC,IAAItB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI;QACtDgB,WAAA,CAAYC,CAAA,GAAI,CAAC,IAAItB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI;QACtDgB,WAAA,CAAYC,CAAA,GAAI,CAAC,IAAItB,IAAA,CAAKS,UAAA,CAAWJ,MAAA,GAAS,IAAI,IAAI;QAEtDA,MAAA,GAASA,MAAA,GAAS,KAAKU,wBAAA;MACxB;MAED,IAAIR,WAAA,IAAeH,OAAA,KAAY,OAAO;QACpCA,OAAA,CAAQG,WAAW,IAAI,EAAE;MAC1B;MAEDH,OAAA,CAAQG,WAAW,EAAEiB,IAAA,CAAK,CAACL,SAAA,EAAWE,WAAA,EAAaX,UAAA,EAAYF,WAAW,CAAC;IAC5E;IAED,WAAWD,WAAA,IAAeH,OAAA,EAAS;MACjC,MAAMqB,QAAA,GAAW,IAAIC,cAAA,CAAetB,OAAA,CAAQG,WAAW,CAAC;MACxD,MAAMoB,QAAA,GAAWC,WAAA,CAAY/B,QAAA,CAASgC,UAAA,CAAWtB,WAAW,CAAC;MAE7DhB,KAAA,CAAMuC,GAAA,CAAI,IAAIC,IAAA,CAAKN,QAAA,EAAUE,QAAQ,CAAC;IACvC;IAED,OAAOpC,KAAA;EACR;AACH;AAEA,MAAMmC,cAAA,SAAuBM,cAAA,CAAe;EAC1CC,YAAYC,OAAA,EAAS;IACnB,MAAO;IAEP,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,MAAA,GAAS,EAAE;IACjB,MAAMC,GAAA,GAAM,EAAE;IAEd,MAAMC,QAAA,GAAW,IAAIC,OAAA,CAAS;IAC9B,MAAMC,YAAA,GAAe,IAAID,OAAA,CAAS;IAElC,MAAME,UAAA,GAAa,IAAIC,UAAA,CAAY;IACnC,MAAMC,cAAA,GAAiB,IAAID,UAAA,CAAY;IAEvC,MAAME,OAAA,GAAU,IAAIL,OAAA,CAAS;IAC7B,MAAMM,OAAA,GAAU,IAAIN,OAAA,CAAS;IAC7B,MAAMO,OAAA,GAAU,IAAIP,OAAA,CAAS;IAC7B,MAAMQ,OAAA,GAAU,IAAIR,OAAA,CAAS;IAI7B,WAAWjB,CAAA,IAAKY,OAAA,EAAS;MACvB,MAAMc,MAAA,GAASd,OAAA,CAAQZ,CAAC;MACxB,MAAMH,SAAA,GAAY6B,MAAA,CAAO,CAAC;MAC1B,MAAM3B,WAAA,GAAc2B,MAAA,CAAO,CAAC;MAC5B,MAAMC,IAAA,GAAOD,MAAA,CAAO,CAAC;MACrB,MAAME,KAAA,GAAQF,MAAA,CAAO,CAAC;MAEtBR,YAAA,CAAaW,SAAA,CAAUhC,SAAA,EAAW,CAAC;MACnCwB,cAAA,CAAeQ,SAAA,CAAU9B,WAAA,EAAa,CAAC;MAEvC,SAASf,CAAA,GAAI,GAAGU,CAAA,GAAI,GAAGoC,CAAA,GAAIjC,SAAA,CAAUI,MAAA,EAAQjB,CAAA,GAAI8C,CAAA,EAAG9C,CAAA,IAAK,GAAGU,CAAA,IAAK,GAAG;QAClEsB,QAAA,CAASa,SAAA,CAAUhC,SAAA,EAAWb,CAAC;QAC/BmC,UAAA,CAAWU,SAAA,CAAU9B,WAAA,EAAaL,CAAC;QAEnC4B,OAAA,CAAQS,GAAA,CAAI,CAACJ,IAAA,EAAM,GAAG,CAAC;QACvBL,OAAA,CAAQU,eAAA,CAAgBb,UAAU;QAClCG,OAAA,CAAQd,GAAA,CAAIQ,QAAQ;QAEpBO,OAAA,CAAQQ,GAAA,CAAIJ,IAAA,EAAM,GAAG,CAAC;QACtBJ,OAAA,CAAQS,eAAA,CAAgBb,UAAU;QAClCI,OAAA,CAAQf,GAAA,CAAIQ,QAAQ;QAEpBQ,OAAA,CAAQO,GAAA,CAAIJ,IAAA,EAAM,GAAG,CAAC;QACtBH,OAAA,CAAQQ,eAAA,CAAgBX,cAAc;QACtCG,OAAA,CAAQhB,GAAA,CAAIU,YAAY;QAExBO,OAAA,CAAQM,GAAA,CAAI,CAACJ,IAAA,EAAM,GAAG,CAAC;QACvBF,OAAA,CAAQO,eAAA,CAAgBX,cAAc;QACtCI,OAAA,CAAQjB,GAAA,CAAIU,YAAY;QAExBL,QAAA,CAASX,IAAA,CAAKoB,OAAA,CAAQW,CAAA,EAAGX,OAAA,CAAQY,CAAA,EAAG,CAACZ,OAAA,CAAQa,CAAC;QAC9CtB,QAAA,CAASX,IAAA,CAAKqB,OAAA,CAAQU,CAAA,EAAGV,OAAA,CAAQW,CAAA,EAAG,CAACX,OAAA,CAAQY,CAAC;QAC9CtB,QAAA,CAASX,IAAA,CAAKuB,OAAA,CAAQQ,CAAA,EAAGR,OAAA,CAAQS,CAAA,EAAG,CAACT,OAAA,CAAQU,CAAC;QAE9CtB,QAAA,CAASX,IAAA,CAAKqB,OAAA,CAAQU,CAAA,EAAGV,OAAA,CAAQW,CAAA,EAAG,CAACX,OAAA,CAAQY,CAAC;QAC9CtB,QAAA,CAASX,IAAA,CAAKsB,OAAA,CAAQS,CAAA,EAAGT,OAAA,CAAQU,CAAA,EAAG,CAACV,OAAA,CAAQW,CAAC;QAC9CtB,QAAA,CAASX,IAAA,CAAKuB,OAAA,CAAQQ,CAAA,EAAGR,OAAA,CAAQS,CAAA,EAAG,CAACT,OAAA,CAAQU,CAAC;QAE9CjB,YAAA,CAAakB,IAAA,CAAKpB,QAAQ;QAC1BK,cAAA,CAAee,IAAA,CAAKjB,UAAU;QAE9BL,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QACpBd,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QACpBd,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QAEpBd,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QACpBd,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QACpBd,MAAA,CAAOZ,IAAA,CAAK,GAAG0B,KAAK;QAEpB,MAAMS,EAAA,GAAKrD,CAAA,GAAI8C,CAAA;QACf,MAAMQ,EAAA,IAAMtD,CAAA,GAAI,KAAK8C,CAAA;QAErBf,GAAA,CAAIb,IAAA,CAAKmC,EAAA,EAAI,CAAC;QACdtB,GAAA,CAAIb,IAAA,CAAKmC,EAAA,EAAI,CAAC;QACdtB,GAAA,CAAIb,IAAA,CAAKoC,EAAA,EAAI,CAAC;QAEdvB,GAAA,CAAIb,IAAA,CAAKmC,EAAA,EAAI,CAAC;QACdtB,GAAA,CAAIb,IAAA,CAAKoC,EAAA,EAAI,CAAC;QACdvB,GAAA,CAAIb,IAAA,CAAKoC,EAAA,EAAI,CAAC;MACf;IACF;IAED,KAAKC,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAI1C,YAAA,CAAae,QAAQ,GAAG,CAAC,CAAC;IAChF,KAAK0B,YAAA,CAAa,SAAS,IAAIC,eAAA,CAAgB,IAAI1C,YAAA,CAAagB,MAAM,GAAG,CAAC,CAAC;IAC3E,KAAKyB,YAAA,CAAa,MAAM,IAAIC,eAAA,CAAgB,IAAI1C,YAAA,CAAaiB,GAAG,GAAG,CAAC,CAAC;EACtE;AACH;AAEA,MAAM0B,gBAAA,GAAmB;EACvB,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;EACxC,wCAAwC;AAC1C;AAEA,MAAMC,MAAA,GAAS;EACb5B,MAAA,EAAQ;IACN6B,UAAA,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAeZC,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYdC,GAAA,EAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IA2BLC,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQf;AACH;AAEA,MAAMC,OAAA,GAAUA,CAAA,MAAO;EACrBC,KAAA,EAAO;IACLC,QAAA,EAAU;MACRC,OAAA,EAAS;QACPC,KAAA,EAAO,IAAIC,aAAA,CAAa,EAAG9F,OAAA,CAAQ,uBAAuB,EAAEH,MAAA,CAAON,IAAA,CAAK,YAAY;MACrF;MACDwG,SAAA,EAAW;QAAEF,KAAA,EAAO;MAAO;MAC3BG,aAAA,EAAe;QAAEH,KAAA,EAAO;MAAM;MAC9BI,KAAA,EAAO;QAAEJ,KAAA,EAAO;MAAG;IACpB;IACDK,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAkBbd,MAAA,CAAO5B,MAAA,CAAO8B,YAAA;AAAA,KACdF,MAAA,CAAO5B,MAAA,CAAO+B,GAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAcfY,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAYff,MAAA,CAAO5B,MAAA,CAAO6B,UAAA;AAAA,KACdD,MAAA,CAAO5B,MAAA,CAAOgC,YAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYfY,IAAA,EAAM;IACNC,WAAA,EAAa;IACbC,SAAA,EAAW;IACXC,UAAA,EAAY;IACZC,SAAA,EAAW;IACXC,QAAA,EAAU;IACVC,QAAA,EAAU;IACVC,aAAA,EAAe;IACfC,aAAA,EAAe;IACfC,kBAAA,EAAoB;IACpBC,QAAA,EAAU;IACVC,aAAA,EAAe;EAChB;AACH;AAEA,SAAS/D,YAAYgE,IAAA,EAAM;EACzB,MAAMC,IAAA,GAAO9B,gBAAA,CAAiB6B,IAAI;EAElC,QAAQC,IAAA;IACN,KAAK;MACH,OAAO,IAAIC,iBAAA,CAAkBzB,OAAA,CAAS,EAACC,KAAK;IAE9C;MACE,OAAO,IAAIyB,iBAAA,CAAkB;QAAEC,YAAA,EAAc;QAAMhB,IAAA,EAAMiB;MAAA,CAAY;EACxE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}