{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { UniformsUtils, ShaderMaterial } from \"three\";\nimport { DotScreenShader } from \"../shaders/DotScreenShader.js\";\nclass DotScreenPass extends Pass {\n  constructor(center, angle, scale) {\n    super();\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"uniforms\");\n    if (DotScreenShader === void 0) console.error(\"THREE.DotScreenPass relies on THREE.DotScreenShader\");\n    const shader = DotScreenShader;\n    this.uniforms = UniformsUtils.clone(shader.uniforms);\n    if (center !== void 0) this.uniforms[\"center\"].value.copy(center);\n    if (angle !== void 0) this.uniforms[\"angle\"].value = angle;\n    if (scale !== void 0) this.uniforms[\"scale\"].value = scale;\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader\n    });\n    this.fsQuad = new FullScreenQuad(this.material);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.uniforms[\"tDiffuse\"].value = readBuffer.texture;\n    this.uniforms[\"tSize\"].value.set(readBuffer.width, readBuffer.height);\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n}\nexport { DotScreenPass };", "map": {"version": 3, "names": ["DotScreenPass", "Pass", "constructor", "center", "angle", "scale", "__publicField", "DotScreenShader", "console", "error", "shader", "uniforms", "UniformsUtils", "clone", "value", "copy", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "texture", "set", "width", "height", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/DotScreenPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { IUniform, ShaderMaterial, UniformsUtils, Vector2, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { DotScreenShader } from '../shaders/DotScreenShader'\n\nclass DotScreenPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: Record<keyof typeof DotScreenShader['uniforms'], IUniform<any>>\n\n  constructor(center?: Vector2, angle?: number, scale?: number) {\n    super()\n    if (DotScreenShader === undefined) console.error('THREE.DotScreenPass relies on THREE.DotScreenShader')\n    const shader = DotScreenShader\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n    if (center !== undefined) this.uniforms['center'].value.copy(center)\n    if (angle !== undefined) this.uniforms['angle'].value = angle\n    if (scale !== undefined) this.uniforms['scale'].value = scale\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n    })\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['tSize'].value.set(readBuffer.width, readBuffer.height)\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { DotScreenPass }\n"], "mappings": ";;;;;;;;;;;;;;AAIA,MAAMA,aAAA,SAAsBC,IAAA,CAAK;EAM/BC,YAAYC,MAAA,EAAkBC,KAAA,EAAgBC,KAAA,EAAgB;IACtD;IANDC,aAAA;IACAA,aAAA;IAEAA,aAAA;IAIL,IAAIC,eAAA,KAAoB,QAAWC,OAAA,CAAQC,KAAA,CAAM,qDAAqD;IACtG,MAAMC,MAAA,GAASH,eAAA;IACf,KAAKI,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMH,MAAA,CAAOC,QAAQ;IACnD,IAAIR,MAAA,KAAW,QAAW,KAAKQ,QAAA,CAAS,QAAQ,EAAEG,KAAA,CAAMC,IAAA,CAAKZ,MAAM;IACnE,IAAIC,KAAA,KAAU,QAAgB,KAAAO,QAAA,CAAS,OAAO,EAAEG,KAAA,GAAQV,KAAA;IACxD,IAAIC,KAAA,KAAU,QAAgB,KAAAM,QAAA,CAAS,OAAO,EAAEG,KAAA,GAAQT,KAAA;IACnD,KAAAW,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCN,QAAA,EAAU,KAAKA,QAAA;MACfO,YAAA,EAAcR,MAAA,CAAOQ,YAAA;MACrBC,cAAA,EAAgBT,MAAA,CAAOS;IAAA,CACxB;IACD,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKL,QAAQ;EAChD;EAEOM,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EAEM;IACN,KAAKd,QAAA,CAAS,UAAU,EAAEG,KAAA,GAAQW,UAAA,CAAWC,OAAA;IACxC,KAAAf,QAAA,CAAS,OAAO,EAAEG,KAAA,CAAMa,GAAA,CAAIF,UAAA,CAAWG,KAAA,EAAOH,UAAA,CAAWI,MAAM;IAEpE,IAAI,KAAKC,cAAA,EAAgB;MACvBP,QAAA,CAASQ,eAAA,CAAgB,IAAI;MACxB,KAAAX,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASQ,eAAA,CAAgBP,WAAW;MACpC,IAAI,KAAKQ,KAAA,EAAOT,QAAA,CAASS,KAAA,CAAM;MAC1B,KAAAZ,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC7B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}