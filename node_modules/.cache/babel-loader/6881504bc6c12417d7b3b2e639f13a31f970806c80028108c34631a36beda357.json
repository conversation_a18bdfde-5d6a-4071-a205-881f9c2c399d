{"ast": null, "code": "import * as React from 'react';\nimport { forwardRef, useEffect, useImperativeHandle, useContext, createContext } from 'react';\nimport { suspend, clear } from 'suspend-react';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst FaceLandmarkerContext = /* @__PURE__ */createContext({});\nconst FaceLandmarkerDefaults = {\n  basePath: 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.17/wasm',\n  options: {\n    baseOptions: {\n      modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',\n      delegate: 'GPU'\n    },\n    runningMode: 'VIDEO',\n    outputFaceBlendshapes: true,\n    outputFacialTransformationMatrixes: true\n  }\n};\nconst FaceLandmarker = /*#__PURE__*/forwardRef(({\n  basePath = FaceLandmarkerDefaults.basePath,\n  options = FaceLandmarkerDefaults.options,\n  children\n}, fref) => {\n  const opts = JSON.stringify(options);\n  const faceLandmarker = suspend(async () => {\n    const {\n      FilesetResolver,\n      FaceLandmarker\n    } = await import('@mediapipe/tasks-vision');\n    const vision = await FilesetResolver.forVisionTasks(basePath);\n    return FaceLandmarker.createFromOptions(vision, options);\n  }, [basePath, opts]);\n  useEffect(() => {\n    return () => {\n      faceLandmarker == null || faceLandmarker.close();\n      clear([basePath, opts]);\n    };\n  }, [faceLandmarker, basePath, opts]);\n  useImperativeHandle(fref, () => faceLandmarker, [faceLandmarker]); // expose faceLandmarker through ref\n\n  return /*#__PURE__*/React.createElement(FaceLandmarkerContext.Provider, {\n    value: faceLandmarker\n  }, children);\n});\nfunction useFaceLandmarker() {\n  return useContext(FaceLandmarkerContext);\n}\nexport { FaceLandmarker, FaceLandmarkerDefaults, useFaceLandmarker };", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useContext", "createContext", "suspend", "clear", "FaceLandmarkerContext", "FaceLandmarkerDefaults", "basePath", "options", "baseOptions", "modelAssetPath", "delegate", "runningMode", "outputFaceBlendshapes", "outputFacialTransformationMatrixes", "FaceLandmarker", "children", "fref", "opts", "JSON", "stringify", "faceLandmarker", "FilesetResolver", "vision", "forVisionTasks", "createFromOptions", "close", "createElement", "Provider", "value", "useFaceLandmarker"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/FaceLandmarker.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useEffect, useImperativeHandle, useContext, createContext } from 'react';\nimport { suspend, clear } from 'suspend-react';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst FaceLandmarkerContext = /* @__PURE__ */createContext({});\nconst FaceLandmarkerDefaults = {\n  basePath: 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.17/wasm',\n  options: {\n    baseOptions: {\n      modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',\n      delegate: 'GPU'\n    },\n    runningMode: 'VIDEO',\n    outputFaceBlendshapes: true,\n    outputFacialTransformationMatrixes: true\n  }\n};\nconst FaceLandmarker = /*#__PURE__*/forwardRef(({\n  basePath = FaceLandmarkerDefaults.basePath,\n  options = FaceLandmarkerDefaults.options,\n  children\n}, fref) => {\n  const opts = JSON.stringify(options);\n  const faceLandmarker = suspend(async () => {\n    const {\n      FilesetResolver,\n      FaceLandmarker\n    } = await import('@mediapipe/tasks-vision');\n    const vision = await FilesetResolver.forVisionTasks(basePath);\n    return FaceLandmarker.createFromOptions(vision, options);\n  }, [basePath, opts]);\n  useEffect(() => {\n    return () => {\n      faceLandmarker == null || faceLandmarker.close();\n      clear([basePath, opts]);\n    };\n  }, [faceLandmarker, basePath, opts]);\n  useImperativeHandle(fref, () => faceLandmarker, [faceLandmarker]); // expose faceLandmarker through ref\n\n  return /*#__PURE__*/React.createElement(FaceLandmarkerContext.Provider, {\n    value: faceLandmarker\n  }, children);\n});\nfunction useFaceLandmarker() {\n  return useContext(FaceLandmarkerContext);\n}\n\nexport { FaceLandmarker, FaceLandmarkerDefaults, useFaceLandmarker };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AAC7F,SAASC,OAAO,EAAEC,KAAK,QAAQ,eAAe;;AAE9C;AACA,MAAMC,qBAAqB,GAAG,eAAeH,aAAa,CAAC,CAAC,CAAC,CAAC;AAC9D,MAAMI,sBAAsB,GAAG;EAC7BC,QAAQ,EAAE,mEAAmE;EAC7EC,OAAO,EAAE;IACPC,WAAW,EAAE;MACXC,cAAc,EAAE,gHAAgH;MAChIC,QAAQ,EAAE;IACZ,CAAC;IACDC,WAAW,EAAE,OAAO;IACpBC,qBAAqB,EAAE,IAAI;IAC3BC,kCAAkC,EAAE;EACtC;AACF,CAAC;AACD,MAAMC,cAAc,GAAG,aAAajB,UAAU,CAAC,CAAC;EAC9CS,QAAQ,GAAGD,sBAAsB,CAACC,QAAQ;EAC1CC,OAAO,GAAGF,sBAAsB,CAACE,OAAO;EACxCQ;AACF,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACZ,OAAO,CAAC;EACpC,MAAMa,cAAc,GAAGlB,OAAO,CAAC,YAAY;IACzC,MAAM;MACJmB,eAAe;MACfP;IACF,CAAC,GAAG,MAAM,MAAM,CAAC,yBAAyB,CAAC;IAC3C,MAAMQ,MAAM,GAAG,MAAMD,eAAe,CAACE,cAAc,CAACjB,QAAQ,CAAC;IAC7D,OAAOQ,cAAc,CAACU,iBAAiB,CAACF,MAAM,EAAEf,OAAO,CAAC;EAC1D,CAAC,EAAE,CAACD,QAAQ,EAAEW,IAAI,CAAC,CAAC;EACpBnB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXsB,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACK,KAAK,CAAC,CAAC;MAChDtB,KAAK,CAAC,CAACG,QAAQ,EAAEW,IAAI,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACG,cAAc,EAAEd,QAAQ,EAAEW,IAAI,CAAC,CAAC;EACpClB,mBAAmB,CAACiB,IAAI,EAAE,MAAMI,cAAc,EAAE,CAACA,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEnE,OAAO,aAAaxB,KAAK,CAAC8B,aAAa,CAACtB,qBAAqB,CAACuB,QAAQ,EAAE;IACtEC,KAAK,EAAER;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,SAASc,iBAAiBA,CAAA,EAAG;EAC3B,OAAO7B,UAAU,CAACI,qBAAqB,CAAC;AAC1C;AAEA,SAASU,cAAc,EAAET,sBAAsB,EAAEwB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}