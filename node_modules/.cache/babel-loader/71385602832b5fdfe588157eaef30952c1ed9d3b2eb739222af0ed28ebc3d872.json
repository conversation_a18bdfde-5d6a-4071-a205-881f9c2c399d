{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\nconst isFunction = node => typeof node === 'function';\nconst OrthographicCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  children,\n  makeDefault,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.updateProjectionMatrix();\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"orthographicCamera\", _extends({\n    left: size.width / -2,\n    right: size.width / 2,\n    top: size.height / 2,\n    bottom: size.height / -2,\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\nexport { OrthographicCamera };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "useFBO", "isFunction", "node", "OrthographicCamera", "forwardRef", "envMap", "resolution", "frames", "Infinity", "children", "makeDefault", "props", "ref", "set", "camera", "size", "cameraRef", "useRef", "useImperativeHandle", "current", "groupRef", "fbo", "useLayoutEffect", "manual", "updateProjectionMatrix", "oldCam", "count", "oldEnvMap", "functional", "state", "visible", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scene", "background", "render", "createElement", "Fragment", "left", "width", "right", "top", "height", "bottom", "texture"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/OrthographicCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\n\nconst isFunction = node => typeof node === 'function';\nconst OrthographicCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  children,\n  makeDefault,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.updateProjectionMatrix();\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"orthographicCamera\", _extends({\n    left: size.width / -2,\n    right: size.width / 2,\n    top: size.height / 2,\n    bottom: size.height / -2,\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { OrthographicCamera };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,MAAM,QAAQ,UAAU;AAEjC,MAAMC,UAAU,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,UAAU;AACrD,MAAMC,kBAAkB,GAAG,eAAeN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC1DC,MAAM;EACNC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAGC,QAAQ;EACjBC,QAAQ;EACRC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,GAAG,GAAGf,QAAQ,CAAC,CAAC;IACpBe;EACF,CAAC,KAAKA,GAAG,CAAC;EACV,MAAMC,MAAM,GAAGhB,QAAQ,CAAC,CAAC;IACvBgB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,IAAI,GAAGjB,QAAQ,CAAC,CAAC;IACrBiB;EACF,CAAC,KAAKA,IAAI,CAAC;EACX,MAAMC,SAAS,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACpCpB,KAAK,CAACqB,mBAAmB,CAACN,GAAG,EAAE,MAAMI,SAAS,CAACG,OAAO,EAAE,EAAE,CAAC;EAC3D,MAAMC,QAAQ,GAAGvB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMI,GAAG,GAAGrB,MAAM,CAACM,UAAU,CAAC;EAC9BT,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACX,KAAK,CAACY,MAAM,EAAE;MACjBP,SAAS,CAACG,OAAO,CAACK,sBAAsB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACT,IAAI,EAAEJ,KAAK,CAAC,CAAC;EACjBd,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1BN,SAAS,CAACG,OAAO,CAACK,sBAAsB,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF3B,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1B,IAAIZ,WAAW,EAAE;MACf,MAAMe,MAAM,GAAGX,MAAM;MACrBD,GAAG,CAAC,OAAO;QACTC,MAAM,EAAEE,SAAS,CAACG;MACpB,CAAC,CAAC,CAAC;MACH,OAAO,MAAMN,GAAG,CAAC,OAAO;QACtBC,MAAM,EAAEW;MACV,CAAC,CAAC,CAAC;IACL;IACA;IACA;EACF,CAAC,EAAE,CAACT,SAAS,EAAEN,WAAW,EAAEG,GAAG,CAAC,CAAC;EACjC,IAAIa,KAAK,GAAG,CAAC;EACb,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAG3B,UAAU,CAACQ,QAAQ,CAAC;EACvCV,QAAQ,CAAC8B,KAAK,IAAI;IAChB,IAAID,UAAU,KAAKrB,MAAM,KAAKC,QAAQ,IAAIkB,KAAK,GAAGnB,MAAM,CAAC,EAAE;MACzDa,QAAQ,CAACD,OAAO,CAACW,OAAO,GAAG,KAAK;MAChCD,KAAK,CAACE,EAAE,CAACC,eAAe,CAACX,GAAG,CAAC;MAC7BM,SAAS,GAAGE,KAAK,CAACI,KAAK,CAACC,UAAU;MAClC,IAAI7B,MAAM,EAAEwB,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG7B,MAAM;MAC3CwB,KAAK,CAACE,EAAE,CAACI,MAAM,CAACN,KAAK,CAACI,KAAK,EAAEjB,SAAS,CAACG,OAAO,CAAC;MAC/CU,KAAK,CAACI,KAAK,CAACC,UAAU,GAAGP,SAAS;MAClCE,KAAK,CAACE,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9BZ,QAAQ,CAACD,OAAO,CAACW,OAAO,GAAG,IAAI;MAC/BJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAa7B,KAAK,CAACuC,aAAa,CAACvC,KAAK,CAACwC,QAAQ,EAAE,IAAI,EAAE,aAAaxC,KAAK,CAACuC,aAAa,CAAC,oBAAoB,EAAExC,QAAQ,CAAC;IAC5H0C,IAAI,EAAEvB,IAAI,CAACwB,KAAK,GAAG,CAAC,CAAC;IACrBC,KAAK,EAAEzB,IAAI,CAACwB,KAAK,GAAG,CAAC;IACrBE,GAAG,EAAE1B,IAAI,CAAC2B,MAAM,GAAG,CAAC;IACpBC,MAAM,EAAE5B,IAAI,CAAC2B,MAAM,GAAG,CAAC,CAAC;IACxB9B,GAAG,EAAEI;EACP,CAAC,EAAEL,KAAK,CAAC,EAAE,CAACiB,UAAU,IAAInB,QAAQ,CAAC,EAAE,aAAaZ,KAAK,CAACuC,aAAa,CAAC,OAAO,EAAE;IAC7ExB,GAAG,EAAEQ;EACP,CAAC,EAAEQ,UAAU,IAAInB,QAAQ,CAACY,GAAG,CAACuB,OAAO,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,SAASzC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}