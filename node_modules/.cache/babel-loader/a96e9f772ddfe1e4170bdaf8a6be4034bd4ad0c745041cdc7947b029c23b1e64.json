{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { RenderCubeTexture } from './RenderCubeTexture.js';\nfunction Fisheye({\n  renderPriority = 1,\n  zoom = 0,\n  segments = 64,\n  children,\n  resolution = 896,\n  ...props\n}) {\n  const sphere = React.useRef(null);\n  const cubeApi = React.useRef(null);\n\n  // This isn't more than a simple sphere and a fixed orthographc camera\n  // pointing at it. A virtual scene is portalled into the environment map\n  // of its material. The cube-camera filming that scene is being synced to\n  // the portals default camera with the <UpdateCubeCamera> component.\n\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const [orthoC] = React.useState(() => new THREE.OrthographicCamera());\n  React.useLayoutEffect(() => {\n    orthoC.position.set(0, 0, 100);\n    orthoC.zoom = 100;\n    orthoC.left = width / -2;\n    orthoC.right = width / 2;\n    orthoC.top = height / 2;\n    orthoC.bottom = height / -2;\n    orthoC.updateProjectionMatrix();\n  }, [width, height]);\n  const radius = Math.sqrt(width * width + height * height) / 100 * (0.5 + zoom / 2);\n  const normal = new THREE.Vector3();\n  const sph = new THREE.Sphere(new THREE.Vector3(), radius);\n  const normalMatrix = new THREE.Matrix3();\n  const compute = React.useCallback((event, state, prev) => {\n    // Raycast from the render camera to the sphere and get the surface normal\n    // of the point hit in world space of the sphere scene\n    // We have to set the raycaster using the orthocam and pointer\n    // to perform sphere interscetions.\n    state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n    state.raycaster.setFromCamera(state.pointer, orthoC);\n    if (!state.raycaster.ray.intersectSphere(sph, normal)) return;else normal.normalize();\n    // Get the matrix for transforming normals into world space\n    normalMatrix.getNormalMatrix(cubeApi.current.camera.matrixWorld);\n    // Get the ray\n    cubeApi.current.camera.getWorldPosition(state.raycaster.ray.origin);\n    state.raycaster.ray.direction.set(0, 0, 1).reflect(normal);\n    state.raycaster.ray.direction.x *= -1; // flip across X to accommodate the \"flip\" of the env map\n    state.raycaster.ray.direction.applyNormalMatrix(normalMatrix).multiplyScalar(-1);\n    return undefined;\n  }, []);\n  useFrame(state => {\n    // Take over rendering\n    if (renderPriority) state.gl.render(sphere.current, orthoC);\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: sphere\n  }, props, {\n    scale: radius\n  }), /*#__PURE__*/React.createElement(\"sphereGeometry\", {\n    args: [1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", null, /*#__PURE__*/React.createElement(RenderCubeTexture, {\n    compute: compute,\n    attach: \"envMap\",\n    flip: true,\n    resolution: resolution,\n    ref: cubeApi\n  }, children, /*#__PURE__*/React.createElement(UpdateCubeCamera, {\n    api: cubeApi\n  })))));\n}\nfunction UpdateCubeCamera({\n  api\n}) {\n  const t = new THREE.Vector3();\n  const r = new THREE.Quaternion();\n  const s = new THREE.Vector3();\n  const e = new THREE.Euler(0, Math.PI, 0);\n  useFrame(state => {\n    // Read out the cameras whereabouts, state.camera is the one *within* the portal\n    state.camera.matrixWorld.decompose(t, r, s);\n    // Apply its position and rotation, flip the Y axis\n    api.current.camera.position.copy(t);\n    api.current.camera.quaternion.setFromEuler(e).premultiply(r);\n  });\n  return null;\n}\nexport { Fisheye };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "useFrame", "RenderCubeTexture", "Fisheye", "renderPriority", "zoom", "segments", "children", "resolution", "props", "sphere", "useRef", "cubeApi", "width", "height", "state", "size", "orthoC", "useState", "OrthographicCamera", "useLayoutEffect", "position", "set", "left", "right", "top", "bottom", "updateProjectionMatrix", "radius", "Math", "sqrt", "normal", "Vector3", "sph", "Sphere", "normalMatrix", "Matrix3", "compute", "useCallback", "event", "prev", "pointer", "offsetX", "offsetY", "raycaster", "setFromCamera", "ray", "intersectSphere", "normalize", "getNormalMatrix", "current", "camera", "matrixWorld", "getWorldPosition", "origin", "direction", "reflect", "x", "applyNormalMatrix", "multiplyScalar", "undefined", "gl", "render", "createElement", "Fragment", "ref", "scale", "args", "attach", "flip", "UpdateCubeCamera", "api", "t", "r", "Quaternion", "s", "e", "<PERSON>uler", "PI", "decompose", "copy", "quaternion", "setFromEuler", "premultiply"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Fisheye.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { RenderCubeTexture } from './RenderCubeTexture.js';\n\nfunction Fisheye({\n  renderPriority = 1,\n  zoom = 0,\n  segments = 64,\n  children,\n  resolution = 896,\n  ...props\n}) {\n  const sphere = React.useRef(null);\n  const cubeApi = React.useRef(null);\n\n  // This isn't more than a simple sphere and a fixed orthographc camera\n  // pointing at it. A virtual scene is portalled into the environment map\n  // of its material. The cube-camera filming that scene is being synced to\n  // the portals default camera with the <UpdateCubeCamera> component.\n\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const [orthoC] = React.useState(() => new THREE.OrthographicCamera());\n  React.useLayoutEffect(() => {\n    orthoC.position.set(0, 0, 100);\n    orthoC.zoom = 100;\n    orthoC.left = width / -2;\n    orthoC.right = width / 2;\n    orthoC.top = height / 2;\n    orthoC.bottom = height / -2;\n    orthoC.updateProjectionMatrix();\n  }, [width, height]);\n  const radius = Math.sqrt(width * width + height * height) / 100 * (0.5 + zoom / 2);\n  const normal = new THREE.Vector3();\n  const sph = new THREE.Sphere(new THREE.Vector3(), radius);\n  const normalMatrix = new THREE.Matrix3();\n  const compute = React.useCallback((event, state, prev) => {\n    // Raycast from the render camera to the sphere and get the surface normal\n    // of the point hit in world space of the sphere scene\n    // We have to set the raycaster using the orthocam and pointer\n    // to perform sphere interscetions.\n    state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n    state.raycaster.setFromCamera(state.pointer, orthoC);\n    if (!state.raycaster.ray.intersectSphere(sph, normal)) return;else normal.normalize();\n    // Get the matrix for transforming normals into world space\n    normalMatrix.getNormalMatrix(cubeApi.current.camera.matrixWorld);\n    // Get the ray\n    cubeApi.current.camera.getWorldPosition(state.raycaster.ray.origin);\n    state.raycaster.ray.direction.set(0, 0, 1).reflect(normal);\n    state.raycaster.ray.direction.x *= -1; // flip across X to accommodate the \"flip\" of the env map\n    state.raycaster.ray.direction.applyNormalMatrix(normalMatrix).multiplyScalar(-1);\n    return undefined;\n  }, []);\n  useFrame(state => {\n    // Take over rendering\n    if (renderPriority) state.gl.render(sphere.current, orthoC);\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: sphere\n  }, props, {\n    scale: radius\n  }), /*#__PURE__*/React.createElement(\"sphereGeometry\", {\n    args: [1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", null, /*#__PURE__*/React.createElement(RenderCubeTexture, {\n    compute: compute,\n    attach: \"envMap\",\n    flip: true,\n    resolution: resolution,\n    ref: cubeApi\n  }, children, /*#__PURE__*/React.createElement(UpdateCubeCamera, {\n    api: cubeApi\n  })))));\n}\nfunction UpdateCubeCamera({\n  api\n}) {\n  const t = new THREE.Vector3();\n  const r = new THREE.Quaternion();\n  const s = new THREE.Vector3();\n  const e = new THREE.Euler(0, Math.PI, 0);\n  useFrame(state => {\n    // Read out the cameras whereabouts, state.camera is the one *within* the portal\n    state.camera.matrixWorld.decompose(t, r, s);\n    // Apply its position and rotation, flip the Y axis\n    api.current.camera.position.copy(t);\n    api.current.camera.quaternion.setFromEuler(e).premultiply(r);\n  });\n  return null;\n}\n\nexport { Fisheye };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,iBAAiB,QAAQ,wBAAwB;AAE1D,SAASC,OAAOA,CAAC;EACfC,cAAc,GAAG,CAAC;EAClBC,IAAI,GAAG,CAAC;EACRC,QAAQ,GAAG,EAAE;EACbC,QAAQ;EACRC,UAAU,GAAG,GAAG;EAChB,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,OAAO,GAAGb,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA;EACA;EACA;;EAEA,MAAM;IACJE,KAAK;IACLC;EACF,CAAC,GAAGd,QAAQ,CAACe,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EACjC,MAAM,CAACC,MAAM,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,MAAM,IAAIpB,KAAK,CAACqB,kBAAkB,CAAC,CAAC,CAAC;EACrEpB,KAAK,CAACqB,eAAe,CAAC,MAAM;IAC1BH,MAAM,CAACI,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;IAC9BL,MAAM,CAACZ,IAAI,GAAG,GAAG;IACjBY,MAAM,CAACM,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC;IACxBI,MAAM,CAACO,KAAK,GAAGX,KAAK,GAAG,CAAC;IACxBI,MAAM,CAACQ,GAAG,GAAGX,MAAM,GAAG,CAAC;IACvBG,MAAM,CAACS,MAAM,GAAGZ,MAAM,GAAG,CAAC,CAAC;IAC3BG,MAAM,CAACU,sBAAsB,CAAC,CAAC;EACjC,CAAC,EAAE,CAACd,KAAK,EAAEC,MAAM,CAAC,CAAC;EACnB,MAAMc,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACjB,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGA,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,GAAGT,IAAI,GAAG,CAAC,CAAC;EAClF,MAAM0B,MAAM,GAAG,IAAIjC,KAAK,CAACkC,OAAO,CAAC,CAAC;EAClC,MAAMC,GAAG,GAAG,IAAInC,KAAK,CAACoC,MAAM,CAAC,IAAIpC,KAAK,CAACkC,OAAO,CAAC,CAAC,EAAEJ,MAAM,CAAC;EACzD,MAAMO,YAAY,GAAG,IAAIrC,KAAK,CAACsC,OAAO,CAAC,CAAC;EACxC,MAAMC,OAAO,GAAGtC,KAAK,CAACuC,WAAW,CAAC,CAACC,KAAK,EAAExB,KAAK,EAAEyB,IAAI,KAAK;IACxD;IACA;IACA;IACA;IACAzB,KAAK,CAAC0B,OAAO,CAACnB,GAAG,CAACiB,KAAK,CAACG,OAAO,GAAG3B,KAAK,CAACC,IAAI,CAACH,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE0B,KAAK,CAACI,OAAO,GAAG5B,KAAK,CAACC,IAAI,CAACF,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzGC,KAAK,CAAC6B,SAAS,CAACC,aAAa,CAAC9B,KAAK,CAAC0B,OAAO,EAAExB,MAAM,CAAC;IACpD,IAAI,CAACF,KAAK,CAAC6B,SAAS,CAACE,GAAG,CAACC,eAAe,CAACd,GAAG,EAAEF,MAAM,CAAC,EAAE,OAAO,KAAKA,MAAM,CAACiB,SAAS,CAAC,CAAC;IACrF;IACAb,YAAY,CAACc,eAAe,CAACrC,OAAO,CAACsC,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC;IAChE;IACAxC,OAAO,CAACsC,OAAO,CAACC,MAAM,CAACE,gBAAgB,CAACtC,KAAK,CAAC6B,SAAS,CAACE,GAAG,CAACQ,MAAM,CAAC;IACnEvC,KAAK,CAAC6B,SAAS,CAACE,GAAG,CAACS,SAAS,CAACjC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACkC,OAAO,CAACzB,MAAM,CAAC;IAC1DhB,KAAK,CAAC6B,SAAS,CAACE,GAAG,CAACS,SAAS,CAACE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvC1C,KAAK,CAAC6B,SAAS,CAACE,GAAG,CAACS,SAAS,CAACG,iBAAiB,CAACvB,YAAY,CAAC,CAACwB,cAAc,CAAC,CAAC,CAAC,CAAC;IAChF,OAAOC,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EACN3D,QAAQ,CAACc,KAAK,IAAI;IAChB;IACA,IAAIX,cAAc,EAAEW,KAAK,CAAC8C,EAAE,CAACC,MAAM,CAACpD,MAAM,CAACwC,OAAO,EAAEjC,MAAM,CAAC;EAC7D,CAAC,EAAEb,cAAc,CAAC;EAClB,OAAO,aAAaL,KAAK,CAACgE,aAAa,CAAChE,KAAK,CAACiE,QAAQ,EAAE,IAAI,EAAE,aAAajE,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAElE,QAAQ,CAAC;IAC9GoE,GAAG,EAAEvD;EACP,CAAC,EAAED,KAAK,EAAE;IACRyD,KAAK,EAAEtC;EACT,CAAC,CAAC,EAAE,aAAa7B,KAAK,CAACgE,aAAa,CAAC,gBAAgB,EAAE;IACrDI,IAAI,EAAE,CAAC,CAAC,EAAE7D,QAAQ,EAAEA,QAAQ;EAC9B,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACgE,aAAa,CAAC,mBAAmB,EAAE,IAAI,EAAE,aAAahE,KAAK,CAACgE,aAAa,CAAC7D,iBAAiB,EAAE;IAClHmC,OAAO,EAAEA,OAAO;IAChB+B,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,IAAI;IACV7D,UAAU,EAAEA,UAAU;IACtByD,GAAG,EAAErD;EACP,CAAC,EAAEL,QAAQ,EAAE,aAAaR,KAAK,CAACgE,aAAa,CAACO,gBAAgB,EAAE;IAC9DC,GAAG,EAAE3D;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR;AACA,SAAS0D,gBAAgBA,CAAC;EACxBC;AACF,CAAC,EAAE;EACD,MAAMC,CAAC,GAAG,IAAI1E,KAAK,CAACkC,OAAO,CAAC,CAAC;EAC7B,MAAMyC,CAAC,GAAG,IAAI3E,KAAK,CAAC4E,UAAU,CAAC,CAAC;EAChC,MAAMC,CAAC,GAAG,IAAI7E,KAAK,CAACkC,OAAO,CAAC,CAAC;EAC7B,MAAM4C,CAAC,GAAG,IAAI9E,KAAK,CAAC+E,KAAK,CAAC,CAAC,EAAEhD,IAAI,CAACiD,EAAE,EAAE,CAAC,CAAC;EACxC7E,QAAQ,CAACc,KAAK,IAAI;IAChB;IACAA,KAAK,CAACoC,MAAM,CAACC,WAAW,CAAC2B,SAAS,CAACP,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC;IAC3C;IACAJ,GAAG,CAACrB,OAAO,CAACC,MAAM,CAAC9B,QAAQ,CAAC2D,IAAI,CAACR,CAAC,CAAC;IACnCD,GAAG,CAACrB,OAAO,CAACC,MAAM,CAAC8B,UAAU,CAACC,YAAY,CAACN,CAAC,CAAC,CAACO,WAAW,CAACV,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AAEA,SAAStE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}