{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshStandardMaterial } from 'three';\nimport { useFrame } from '@react-three/fiber';\nclass WobbleMaterialImpl extends MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this.setValues(parameters);\n    this._time = {\n      value: 0\n    };\n    this._factor = {\n      value: 1\n    };\n  }\n\n  // FIXME Use `THREE.WebGLProgramParametersWithUniforms` type when able to target @types/three@0.160.0\n  onBeforeCompile(shader) {\n    shader.uniforms.time = this._time;\n    shader.uniforms.factor = this._factor;\n    shader.vertexShader = `\n      uniform float time;\n      uniform float factor;\n      ${shader.vertexShader}\n    `;\n    shader.vertexShader = shader.vertexShader.replace('#include <begin_vertex>', `float theta = sin( time + position.y ) / 2.0 * factor;\n        float c = cos( theta );\n        float s = sin( theta );\n        mat3 m = mat3( c, 0, s, 0, 1, 0, -s, 0, c );\n        vec3 transformed = vec3( position ) * m;\n        vNormal = vNormal * m;`);\n  }\n  get time() {\n    return this._time.value;\n  }\n  set time(v) {\n    this._time.value = v;\n  }\n  get factor() {\n    return this._factor.value;\n  }\n  set factor(v) {\n    this._factor.value = v;\n  }\n}\nconst MeshWobbleMaterial = /* @__PURE__ */React.forwardRef(({\n  speed = 1,\n  ...props\n}, ref) => {\n  const [material] = React.useState(() => new WobbleMaterialImpl());\n  useFrame(state => material && (material.time = state.clock.elapsedTime * speed));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }, props));\n});\nexport { MeshWobbleMaterial };", "map": {"version": 3, "names": ["_extends", "React", "MeshStandardMaterial", "useFrame", "WobbleMaterialImpl", "constructor", "parameters", "set<PERSON><PERSON><PERSON>", "_time", "value", "_factor", "onBeforeCompile", "shader", "uniforms", "time", "factor", "vertexShader", "replace", "v", "MeshWobbleMaterial", "forwardRef", "speed", "props", "ref", "material", "useState", "state", "clock", "elapsedTime", "createElement", "object", "attach"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MeshWobbleMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshStandardMaterial } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nclass WobbleMaterialImpl extends MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this.setValues(parameters);\n    this._time = {\n      value: 0\n    };\n    this._factor = {\n      value: 1\n    };\n  }\n\n  // FIXME Use `THREE.WebGLProgramParametersWithUniforms` type when able to target @types/three@0.160.0\n  onBeforeCompile(shader) {\n    shader.uniforms.time = this._time;\n    shader.uniforms.factor = this._factor;\n    shader.vertexShader = `\n      uniform float time;\n      uniform float factor;\n      ${shader.vertexShader}\n    `;\n    shader.vertexShader = shader.vertexShader.replace('#include <begin_vertex>', `float theta = sin( time + position.y ) / 2.0 * factor;\n        float c = cos( theta );\n        float s = sin( theta );\n        mat3 m = mat3( c, 0, s, 0, 1, 0, -s, 0, c );\n        vec3 transformed = vec3( position ) * m;\n        vNormal = vNormal * m;`);\n  }\n  get time() {\n    return this._time.value;\n  }\n  set time(v) {\n    this._time.value = v;\n  }\n  get factor() {\n    return this._factor.value;\n  }\n  set factor(v) {\n    this._factor.value = v;\n  }\n}\nconst MeshWobbleMaterial = /* @__PURE__ */React.forwardRef(({\n  speed = 1,\n  ...props\n}, ref) => {\n  const [material] = React.useState(() => new WobbleMaterialImpl());\n  useFrame(state => material && (material.time = state.clock.elapsedTime * speed));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }, props));\n});\n\nexport { MeshWobbleMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,OAAO;AAC5C,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,kBAAkB,SAASF,oBAAoB,CAAC;EACpDG,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC3B,KAAK,CAACA,UAAU,CAAC;IACjB,IAAI,CAACC,SAAS,CAACD,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,OAAO,GAAG;MACbD,KAAK,EAAE;IACT,CAAC;EACH;;EAEA;EACAE,eAAeA,CAACC,MAAM,EAAE;IACtBA,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACN,KAAK;IACjCI,MAAM,CAACC,QAAQ,CAACE,MAAM,GAAG,IAAI,CAACL,OAAO;IACrCE,MAAM,CAACI,YAAY,GAAG;AAC1B;AACA;AACA,QAAQJ,MAAM,CAACI,YAAY;AAC3B,KAAK;IACDJ,MAAM,CAACI,YAAY,GAAGJ,MAAM,CAACI,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAE;AACjF;AACA;AACA;AACA;AACA,+BAA+B,CAAC;EAC9B;EACA,IAAIH,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACN,KAAK,CAACC,KAAK;EACzB;EACA,IAAIK,IAAIA,CAACI,CAAC,EAAE;IACV,IAAI,CAACV,KAAK,CAACC,KAAK,GAAGS,CAAC;EACtB;EACA,IAAIH,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,OAAO,CAACD,KAAK;EAC3B;EACA,IAAIM,MAAMA,CAACG,CAAC,EAAE;IACZ,IAAI,CAACR,OAAO,CAACD,KAAK,GAAGS,CAAC;EACxB;AACF;AACA,MAAMC,kBAAkB,GAAG,eAAelB,KAAK,CAACmB,UAAU,CAAC,CAAC;EAC1DC,KAAK,GAAG,CAAC;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAACC,QAAQ,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,MAAM,IAAIrB,kBAAkB,CAAC,CAAC,CAAC;EACjED,QAAQ,CAACuB,KAAK,IAAIF,QAAQ,KAAKA,QAAQ,CAACV,IAAI,GAAGY,KAAK,CAACC,KAAK,CAACC,WAAW,GAAGP,KAAK,CAAC,CAAC;EAChF,OAAO,aAAapB,KAAK,CAAC4B,aAAa,CAAC,WAAW,EAAE7B,QAAQ,CAAC;IAC5D8B,MAAM,EAAEN,QAAQ;IAChBD,GAAG,EAAEA,GAAG;IACRQ,MAAM,EAAE;EACV,CAAC,EAAET,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}