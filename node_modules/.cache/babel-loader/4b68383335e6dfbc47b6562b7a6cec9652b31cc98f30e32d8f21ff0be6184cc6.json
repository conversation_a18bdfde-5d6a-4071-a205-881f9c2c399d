{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ector3, <PERSON>3, MeshPhongMaterial, UniformsUtils, ShaderLib, ShaderChunk } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nvar GeometryCompressionUtils = {\n  /**\n   * Make the input mesh.geometry's normal attribute encoded and compressed by 3 different methods.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the normal data.\n   *\n   * @param {THREE.Mesh} mesh\n   * @param {String} encodeMethod\t\t\"DEFAULT\" || \"OCT1Byte\" || \"OCT2Byte\" || \"ANGLES\"\n   *\n   */\n  compressNormals: function (mesh, encodeMethod) {\n    if (!mesh.geometry) {\n      console.error(\"Mesh must contain geometry. \");\n    }\n    const normal = mesh.geometry.attributes.normal;\n    if (!normal) {\n      console.error(\"Geometry must contain normal attribute. \");\n    }\n    if (normal.isPacked) return;\n    if (normal.itemSize != 3) {\n      console.error(\"normal.itemSize is not 3, which cannot be encoded. \");\n    }\n    const array = normal.array;\n    const count = normal.count;\n    let result;\n    if (encodeMethod == \"DEFAULT\") {\n      result = new Uint8Array(count * 3);\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[idx], array[idx + 1], array[idx + 2], 1);\n        result[idx + 0] = encoded[0];\n        result[idx + 1] = encoded[1];\n        result[idx + 2] = encoded[2];\n      }\n      mesh.geometry.setAttribute(\"normal\", new BufferAttribute(result, 3, true));\n      mesh.geometry.attributes.normal.bytes = result.length * 1;\n    } else if (encodeMethod == \"OCT1Byte\") {\n      result = new Int8Array(count * 2);\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 1);\n        result[idx / 3 * 2 + 0] = encoded[0];\n        result[idx / 3 * 2 + 1] = encoded[1];\n      }\n      mesh.geometry.setAttribute(\"normal\", new BufferAttribute(result, 2, true));\n      mesh.geometry.attributes.normal.bytes = result.length * 1;\n    } else if (encodeMethod == \"OCT2Byte\") {\n      result = new Int16Array(count * 2);\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 2);\n        result[idx / 3 * 2 + 0] = encoded[0];\n        result[idx / 3 * 2 + 1] = encoded[1];\n      }\n      mesh.geometry.setAttribute(\"normal\", new BufferAttribute(result, 2, true));\n      mesh.geometry.attributes.normal.bytes = result.length * 2;\n    } else if (encodeMethod == \"ANGLES\") {\n      result = new Uint16Array(count * 2);\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.anglesEncode(array[idx], array[idx + 1], array[idx + 2]);\n        result[idx / 3 * 2 + 0] = encoded[0];\n        result[idx / 3 * 2 + 1] = encoded[1];\n      }\n      mesh.geometry.setAttribute(\"normal\", new BufferAttribute(result, 2, true));\n      mesh.geometry.attributes.normal.bytes = result.length * 2;\n    } else {\n      console.error(\"Unrecognized encoding method, should be `DEFAULT` or `ANGLES` or `OCT`. \");\n    }\n    mesh.geometry.attributes.normal.needsUpdate = true;\n    mesh.geometry.attributes.normal.isPacked = true;\n    mesh.geometry.attributes.normal.packingMethod = encodeMethod;\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material);\n    }\n    if (encodeMethod == \"ANGLES\") {\n      mesh.material.defines.USE_PACKED_NORMAL = 0;\n    }\n    if (encodeMethod == \"OCT1Byte\") {\n      mesh.material.defines.USE_PACKED_NORMAL = 1;\n    }\n    if (encodeMethod == \"OCT2Byte\") {\n      mesh.material.defines.USE_PACKED_NORMAL = 1;\n    }\n    if (encodeMethod == \"DEFAULT\") {\n      mesh.material.defines.USE_PACKED_NORMAL = 2;\n    }\n  },\n  /**\n   * Make the input mesh.geometry's position attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the position data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressPositions: function (mesh) {\n    if (!mesh.geometry) {\n      console.error(\"Mesh must contain geometry. \");\n    }\n    const position = mesh.geometry.attributes.position;\n    if (!position) {\n      console.error(\"Geometry must contain position attribute. \");\n    }\n    if (position.isPacked) return;\n    if (position.itemSize != 3) {\n      console.error(\"position.itemSize is not 3, which cannot be packed. \");\n    }\n    const array = position.array;\n    const encodingBytes = 2;\n    const result = this.EncodingFuncs.quantizedEncode(array, encodingBytes);\n    const quantized = result.quantized;\n    const decodeMat = result.decodeMat;\n    if (mesh.geometry.boundingBox == null) mesh.geometry.computeBoundingBox();\n    if (mesh.geometry.boundingSphere == null) mesh.geometry.computeBoundingSphere();\n    mesh.geometry.setAttribute(\"position\", new BufferAttribute(quantized, 3));\n    mesh.geometry.attributes.position.isPacked = true;\n    mesh.geometry.attributes.position.needsUpdate = true;\n    mesh.geometry.attributes.position.bytes = quantized.length * encodingBytes;\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material);\n    }\n    mesh.material.defines.USE_PACKED_POSITION = 0;\n    mesh.material.uniforms.quantizeMatPos.value = decodeMat;\n    mesh.material.uniforms.quantizeMatPos.needsUpdate = true;\n  },\n  /**\n   * Make the input mesh.geometry's uv attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the uv data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressUvs: function (mesh) {\n    if (!mesh.geometry) {\n      console.error(\"Mesh must contain geometry property. \");\n    }\n    const uvs = mesh.geometry.attributes.uv;\n    if (!uvs) {\n      console.error(\"Geometry must contain uv attribute. \");\n    }\n    if (uvs.isPacked) return;\n    const range = {\n      min: Infinity,\n      max: -Infinity\n    };\n    const array = uvs.array;\n    for (let i = 0; i < array.length; i++) {\n      range.min = Math.min(range.min, array[i]);\n      range.max = Math.max(range.max, array[i]);\n    }\n    let result;\n    if (range.min >= -1 && range.max <= 1) {\n      result = new Uint16Array(array.length);\n      for (let i = 0; i < array.length; i += 2) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[i], array[i + 1], 0, 2);\n        result[i] = encoded[0];\n        result[i + 1] = encoded[1];\n      }\n      mesh.geometry.setAttribute(\"uv\", new BufferAttribute(result, 2, true));\n      mesh.geometry.attributes.uv.isPacked = true;\n      mesh.geometry.attributes.uv.needsUpdate = true;\n      mesh.geometry.attributes.uv.bytes = result.length * 2;\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material);\n      }\n      mesh.material.defines.USE_PACKED_UV = 0;\n    } else {\n      result = this.EncodingFuncs.quantizedEncodeUV(array, 2);\n      mesh.geometry.setAttribute(\"uv\", new BufferAttribute(result.quantized, 2));\n      mesh.geometry.attributes.uv.isPacked = true;\n      mesh.geometry.attributes.uv.needsUpdate = true;\n      mesh.geometry.attributes.uv.bytes = result.quantized.length * 2;\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material);\n      }\n      mesh.material.defines.USE_PACKED_UV = 1;\n      mesh.material.uniforms.quantizeMatUV.value = result.decodeMat;\n      mesh.material.uniforms.quantizeMatUV.needsUpdate = true;\n    }\n  },\n  EncodingFuncs: {\n    defaultEncode: function (x, y, z, bytes) {\n      if (bytes == 1) {\n        const tmpx = Math.round((x + 1) * 0.5 * 255);\n        const tmpy = Math.round((y + 1) * 0.5 * 255);\n        const tmpz = Math.round((z + 1) * 0.5 * 255);\n        return new Uint8Array([tmpx, tmpy, tmpz]);\n      } else if (bytes == 2) {\n        const tmpx = Math.round((x + 1) * 0.5 * 65535);\n        const tmpy = Math.round((y + 1) * 0.5 * 65535);\n        const tmpz = Math.round((z + 1) * 0.5 * 65535);\n        return new Uint16Array([tmpx, tmpy, tmpz]);\n      } else {\n        console.error(\"number of bytes must be 1 or 2\");\n      }\n    },\n    defaultDecode: function (array, bytes) {\n      if (bytes == 1) {\n        return [array[0] / 255 * 2 - 1, array[1] / 255 * 2 - 1, array[2] / 255 * 2 - 1];\n      } else if (bytes == 2) {\n        return [array[0] / 65535 * 2 - 1, array[1] / 65535 * 2 - 1, array[2] / 65535 * 2 - 1];\n      } else {\n        console.error(\"number of bytes must be 1 or 2\");\n      }\n    },\n    // for `Angles` encoding\n    anglesEncode: function (x, y, z) {\n      const normal0 = parseInt(0.5 * (1 + Math.atan2(y, x) / Math.PI) * 65535);\n      const normal1 = parseInt(0.5 * (1 + z) * 65535);\n      return new Uint16Array([normal0, normal1]);\n    },\n    // for `Octahedron` encoding\n    octEncodeBest: function (x, y, z, bytes) {\n      var oct, dec, best, currentCos, bestCos;\n      best = oct = octEncodeVec3(x, y, z, \"floor\", \"floor\");\n      dec = octDecodeVec2(oct);\n      bestCos = dot(x, y, z, dec);\n      oct = octEncodeVec3(x, y, z, \"ceil\", \"floor\");\n      dec = octDecodeVec2(oct);\n      currentCos = dot(x, y, z, dec);\n      if (currentCos > bestCos) {\n        best = oct;\n        bestCos = currentCos;\n      }\n      oct = octEncodeVec3(x, y, z, \"floor\", \"ceil\");\n      dec = octDecodeVec2(oct);\n      currentCos = dot(x, y, z, dec);\n      if (currentCos > bestCos) {\n        best = oct;\n        bestCos = currentCos;\n      }\n      oct = octEncodeVec3(x, y, z, \"ceil\", \"ceil\");\n      dec = octDecodeVec2(oct);\n      currentCos = dot(x, y, z, dec);\n      if (currentCos > bestCos) {\n        best = oct;\n      }\n      return best;\n      function octEncodeVec3(x0, y0, z0, xfunc, yfunc) {\n        var x2 = x0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0));\n        var y2 = y0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0));\n        if (z < 0) {\n          var tempx = (1 - Math.abs(y2)) * (x2 >= 0 ? 1 : -1);\n          var tempy = (1 - Math.abs(x2)) * (y2 >= 0 ? 1 : -1);\n          x2 = tempx;\n          y2 = tempy;\n          var diff = 1 - Math.abs(x2) - Math.abs(y2);\n          if (diff > 0) {\n            diff += 1e-3;\n            x2 += x2 > 0 ? diff / 2 : -diff / 2;\n            y2 += y2 > 0 ? diff / 2 : -diff / 2;\n          }\n        }\n        if (bytes == 1) {\n          return new Int8Array([Math[xfunc](x2 * 127.5 + (x2 < 0 ? 1 : 0)), Math[yfunc](y2 * 127.5 + (y2 < 0 ? 1 : 0))]);\n        }\n        if (bytes == 2) {\n          return new Int16Array([Math[xfunc](x2 * 32767.5 + (x2 < 0 ? 1 : 0)), Math[yfunc](y2 * 32767.5 + (y2 < 0 ? 1 : 0))]);\n        }\n      }\n      function octDecodeVec2(oct2) {\n        var x2 = oct2[0];\n        var y2 = oct2[1];\n        if (bytes == 1) {\n          x2 /= x2 < 0 ? 127 : 128;\n          y2 /= y2 < 0 ? 127 : 128;\n        } else if (bytes == 2) {\n          x2 /= x2 < 0 ? 32767 : 32768;\n          y2 /= y2 < 0 ? 32767 : 32768;\n        }\n        var z2 = 1 - Math.abs(x2) - Math.abs(y2);\n        if (z2 < 0) {\n          var tmpx = x2;\n          x2 = (1 - Math.abs(y2)) * (x2 >= 0 ? 1 : -1);\n          y2 = (1 - Math.abs(tmpx)) * (y2 >= 0 ? 1 : -1);\n        }\n        var length = Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2);\n        return [x2 / length, y2 / length, z2 / length];\n      }\n      function dot(x2, y2, z2, vec3) {\n        return x2 * vec3[0] + y2 * vec3[1] + z2 * vec3[2];\n      }\n    },\n    quantizedEncode: function (array, bytes) {\n      let quantized, segments;\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length);\n        segments = 255;\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length);\n        segments = 65535;\n      } else {\n        console.error(\"number of bytes error! \");\n      }\n      const decodeMat = new Matrix4();\n      const min = new Float32Array(3);\n      const max = new Float32Array(3);\n      min[0] = min[1] = min[2] = Number.MAX_VALUE;\n      max[0] = max[1] = max[2] = -Number.MAX_VALUE;\n      for (let i = 0; i < array.length; i += 3) {\n        min[0] = Math.min(min[0], array[i + 0]);\n        min[1] = Math.min(min[1], array[i + 1]);\n        min[2] = Math.min(min[2], array[i + 2]);\n        max[0] = Math.max(max[0], array[i + 0]);\n        max[1] = Math.max(max[1], array[i + 1]);\n        max[2] = Math.max(max[2], array[i + 2]);\n      }\n      decodeMat.scale(new Vector3((max[0] - min[0]) / segments, (max[1] - min[1]) / segments, (max[2] - min[2]) / segments));\n      decodeMat.elements[12] = min[0];\n      decodeMat.elements[13] = min[1];\n      decodeMat.elements[14] = min[2];\n      decodeMat.transpose();\n      const multiplier = new Float32Array([max[0] !== min[0] ? segments / (max[0] - min[0]) : 0, max[1] !== min[1] ? segments / (max[1] - min[1]) : 0, max[2] !== min[2] ? segments / (max[2] - min[2]) : 0]);\n      for (let i = 0; i < array.length; i += 3) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0]);\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1]);\n        quantized[i + 2] = Math.floor((array[i + 2] - min[2]) * multiplier[2]);\n      }\n      return {\n        quantized,\n        decodeMat\n      };\n    },\n    quantizedEncodeUV: function (array, bytes) {\n      let quantized, segments;\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length);\n        segments = 255;\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length);\n        segments = 65535;\n      } else {\n        console.error(\"number of bytes error! \");\n      }\n      const decodeMat = new Matrix3();\n      const min = new Float32Array(2);\n      const max = new Float32Array(2);\n      min[0] = min[1] = Number.MAX_VALUE;\n      max[0] = max[1] = -Number.MAX_VALUE;\n      for (let i = 0; i < array.length; i += 2) {\n        min[0] = Math.min(min[0], array[i + 0]);\n        min[1] = Math.min(min[1], array[i + 1]);\n        max[0] = Math.max(max[0], array[i + 0]);\n        max[1] = Math.max(max[1], array[i + 1]);\n      }\n      decodeMat.scale((max[0] - min[0]) / segments, (max[1] - min[1]) / segments);\n      decodeMat.elements[6] = min[0];\n      decodeMat.elements[7] = min[1];\n      decodeMat.transpose();\n      const multiplier = new Float32Array([max[0] !== min[0] ? segments / (max[0] - min[0]) : 0, max[1] !== min[1] ? segments / (max[1] - min[1]) : 0]);\n      for (let i = 0; i < array.length; i += 2) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0]);\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1]);\n      }\n      return {\n        quantized,\n        decodeMat\n      };\n    }\n  }\n};\nclass PackedPhongMaterial extends MeshPhongMaterial {\n  constructor(parameters) {\n    super();\n    this.defines = {};\n    this.type = \"PackedPhongMaterial\";\n    this.uniforms = UniformsUtils.merge([ShaderLib.phong.uniforms, {\n      quantizeMatPos: {\n        value: null\n      },\n      quantizeMatUV: {\n        value: null\n      }\n    }]);\n    this.vertexShader = [\"#define PHONG\", \"varying vec3 vViewPosition;\", \"#ifndef FLAT_SHADED\", \"varying vec3 vNormal;\", \"#endif\", ShaderChunk.common, ShaderChunk.uv_pars_vertex, ShaderChunk.uv2_pars_vertex, ShaderChunk.displacementmap_pars_vertex, ShaderChunk.envmap_pars_vertex, ShaderChunk.color_pars_vertex, ShaderChunk.fog_pars_vertex, ShaderChunk.morphtarget_pars_vertex, ShaderChunk.skinning_pars_vertex, ShaderChunk.shadowmap_pars_vertex, ShaderChunk.logdepthbuf_pars_vertex, ShaderChunk.clipping_planes_pars_vertex, `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\t#if USE_PACKED_NORMAL == 0\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfloat x = packedNormal.x * 2.0 - 1.0;\n\t\t\t\t\t\t\tfloat y = packedNormal.y * 2.0 - 1.0;\n\t\t\t\t\t\t\tvec2 scth = vec2(sin(x * PI), cos(x * PI));\n\t\t\t\t\t\t\tvec2 scphi = vec2(sqrt(1.0 - y * y), y);\n\t\t\t\t\t\t\treturn normalize( vec3(scth.y * scphi.x, scth.x * scphi.x, scphi.y) );\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 1\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = vec3(packedNormal.xy, 1.0 - abs(packedNormal.x) - abs(packedNormal.y));\n\t\t\t\t\t\t\tif (v.z < 0.0)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tv.xy = (1.0 - abs(v.yx)) * vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 2\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = (packedNormal * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\tuniform mat4 quantizeMatPos;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tuniform mat3 quantizeMatUV;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 0\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = (packedUV * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = ( vec3(packedUV, 1.0) * quantizeMatUV ).xy;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, \"void main() {\", ShaderChunk.uv_vertex, `#ifdef USE_UV\n\t\t\t\t\t#ifdef USE_PACKED_UV\n\t\t\t\t\t\tvUv = decodeUV(vUv);\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, ShaderChunk.uv2_vertex, ShaderChunk.color_vertex, ShaderChunk.beginnormal_vertex, `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\tobjectNormal = decodeNormal(objectNormal);\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_TANGENT\n\t\t\t\t\tvec3 objectTangent = vec3( tangent.xyz );\n\t\t\t\t#endif\n\t\t\t\t`, ShaderChunk.morphnormal_vertex, ShaderChunk.skinbase_vertex, ShaderChunk.skinnormal_vertex, ShaderChunk.defaultnormal_vertex, \"#ifndef FLAT_SHADED\", \"\tvNormal = normalize( transformedNormal );\", \"#endif\", ShaderChunk.begin_vertex, `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\ttransformed = ( vec4(transformed, 1.0) * quantizeMatPos ).xyz;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`, ShaderChunk.morphtarget_vertex, ShaderChunk.skinning_vertex, ShaderChunk.displacementmap_vertex, ShaderChunk.project_vertex, ShaderChunk.logdepthbuf_vertex, ShaderChunk.clipping_planes_vertex, \"vViewPosition = - mvPosition.xyz;\", ShaderChunk.worldpos_vertex, ShaderChunk.envmap_vertex, ShaderChunk.shadowmap_vertex, ShaderChunk.fog_vertex, \"}\"].join(\"\\n\");\n    this.fragmentShader = [\"#define PHONG\", \"uniform vec3 diffuse;\", \"uniform vec3 emissive;\", \"uniform vec3 specular;\", \"uniform float shininess;\", \"uniform float opacity;\", ShaderChunk.common, ShaderChunk.packing, ShaderChunk.dithering_pars_fragment, ShaderChunk.color_pars_fragment, ShaderChunk.uv_pars_fragment, ShaderChunk.uv2_pars_fragment, ShaderChunk.map_pars_fragment, ShaderChunk.alphamap_pars_fragment, ShaderChunk.aomap_pars_fragment, ShaderChunk.lightmap_pars_fragment, ShaderChunk.emissivemap_pars_fragment, ShaderChunk.envmap_common_pars_fragment, ShaderChunk.envmap_pars_fragment, ShaderChunk.cube_uv_reflection_fragment, ShaderChunk.fog_pars_fragment, ShaderChunk.bsdfs, ShaderChunk.lights_pars_begin, ShaderChunk.lights_phong_pars_fragment, ShaderChunk.shadowmap_pars_fragment, ShaderChunk.bumpmap_pars_fragment, ShaderChunk.normalmap_pars_fragment, ShaderChunk.specularmap_pars_fragment, ShaderChunk.logdepthbuf_pars_fragment, ShaderChunk.clipping_planes_pars_fragment, \"void main() {\", ShaderChunk.clipping_planes_fragment, \"vec4 diffuseColor = vec4( diffuse, opacity );\", \"ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\", \"vec3 totalEmissiveRadiance = emissive;\", ShaderChunk.logdepthbuf_fragment, ShaderChunk.map_fragment, ShaderChunk.color_fragment, ShaderChunk.alphamap_fragment, ShaderChunk.alphatest_fragment, ShaderChunk.specularmap_fragment, ShaderChunk.normal_fragment_begin, ShaderChunk.normal_fragment_maps, ShaderChunk.emissivemap_fragment,\n    // accumulation\n    ShaderChunk.lights_phong_fragment, ShaderChunk.lights_fragment_begin, ShaderChunk.lights_fragment_maps, ShaderChunk.lights_fragment_end,\n    // modulation\n    ShaderChunk.aomap_fragment, \"vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\", ShaderChunk.envmap_fragment, \"gl_FragColor = vec4( outgoingLight, diffuseColor.a );\", ShaderChunk.tonemapping_fragment, version >= 154 ? ShaderChunk.colorspace_fragment : ShaderChunk.encodings_fragment, ShaderChunk.fog_fragment, ShaderChunk.premultiplied_alpha_fragment, ShaderChunk.dithering_fragment, \"}\"].join(\"\\n\");\n    this.setValues(parameters);\n  }\n}\nexport { GeometryCompressionUtils, PackedPhongMaterial };", "map": {"version": 3, "names": ["GeometryCompressionUtils", "compressNormals", "mesh", "encodeMethod", "geometry", "console", "error", "normal", "attributes", "isPacked", "itemSize", "array", "count", "result", "Uint8Array", "idx", "length", "encoded", "EncodingFuncs", "defaultEncode", "setAttribute", "BufferAttribute", "bytes", "Int8Array", "octEncodeBest", "Int16Array", "Uint16Array", "anglesEncode", "needsUpdate", "packingMethod", "material", "PackedPhongMaterial", "copy", "defines", "USE_PACKED_NORMAL", "compressPositions", "position", "encodingBytes", "quantizedEncode", "quantized", "decodeMat", "boundingBox", "computeBoundingBox", "boundingSphere", "computeBoundingSphere", "USE_PACKED_POSITION", "uniforms", "quantizeMatPos", "value", "compressUvs", "uvs", "uv", "range", "min", "Infinity", "max", "i", "Math", "USE_PACKED_UV", "quantizedEncodeUV", "quantizeMatUV", "x", "y", "z", "tmpx", "round", "tmpy", "tmpz", "defaultDecode", "normal0", "parseInt", "atan2", "PI", "normal1", "oct", "dec", "best", "currentCos", "bestCos", "octEncodeVec3", "octDecodeVec2", "dot", "x0", "y0", "z0", "xfunc", "yfunc", "x2", "abs", "y2", "tempx", "tempy", "diff", "oct2", "z2", "sqrt", "vec3", "segments", "Matrix4", "Float32Array", "Number", "MAX_VALUE", "scale", "Vector3", "elements", "transpose", "multiplier", "floor", "Matrix3", "MeshPhongMaterial", "constructor", "parameters", "type", "UniformsUtils", "merge", "ShaderLib", "phong", "vertexShader", "ShaderChunk", "common", "uv_pars_vertex", "uv2_pars_vertex", "displacementmap_pars_vertex", "envmap_pars_vertex", "color_pars_vertex", "fog_pars_vertex", "morphtarget_pars_vertex", "skinning_pars_vertex", "shadowmap_pars_vertex", "logdepthbuf_pars_vertex", "clipping_planes_pars_vertex", "uv_vertex", "uv2_vertex", "color_vertex", "beginnormal_vertex", "morphnormal_vertex", "skinbase_vertex", "skinnormal_vertex", "defaultnormal_vertex", "begin_vertex", "morphtarget_vertex", "skinning_vertex", "displacementmap_vertex", "project_vertex", "logdepthbuf_vertex", "clipping_planes_vertex", "worldpos_vertex", "envmap_vertex", "shadowmap_vertex", "fog_vertex", "join", "fragmentShader", "packing", "dithering_pars_fragment", "color_pars_fragment", "uv_pars_fragment", "uv2_pars_fragment", "map_pars_fragment", "alphamap_pars_fragment", "aomap_pars_fragment", "lightmap_pars_fragment", "emissivemap_pars_fragment", "envmap_common_pars_fragment", "envmap_pars_fragment", "cube_uv_reflection_fragment", "fog_pars_fragment", "bsdfs", "lights_pars_begin", "lights_phong_pars_fragment", "shadowmap_pars_fragment", "bumpmap_pars_fragment", "normalmap_pars_fragment", "specularmap_pars_fragment", "logdepthbuf_pars_fragment", "clipping_planes_pars_fragment", "clipping_planes_fragment", "logdepthbuf_fragment", "map_fragment", "color_fragment", "alphamap_fragment", "alphatest_fragment", "specularmap_fragment", "normal_fragment_begin", "normal_fragment_maps", "emissivemap_fragment", "lights_phong_fragment", "lights_fragment_begin", "lights_fragment_maps", "lights_fragment_end", "aomap_fragment", "envmap_fragment", "tonemapping_fragment", "version", "colorspace_fragment", "encodings_fragment", "fog_fragment", "premultiplied_alpha_fragment", "dithering_fragment", "set<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/utils/GeometryCompressionUtils.js"], "sourcesContent": ["/**\n * Octahedron and Quantization encodings based on work by:\n *\n * @link https://github.com/tsherif/mesh-quantization-example\n *\n */\n\nimport {\n  BufferAttribute,\n  Matrix3,\n  Matrix4,\n  MeshPhongMaterial,\n  ShaderChunk,\n  ShaderLib,\n  UniformsUtils,\n  Vector3,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nvar GeometryCompressionUtils = {\n  /**\n   * Make the input mesh.geometry's normal attribute encoded and compressed by 3 different methods.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the normal data.\n   *\n   * @param {THREE.Mesh} mesh\n   * @param {String} encodeMethod\t\t\"DEFAULT\" || \"OCT1Byte\" || \"OCT2Byte\" || \"ANGLES\"\n   *\n   */\n  compressNormals: function (mesh, encodeMethod) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry. ')\n    }\n\n    const normal = mesh.geometry.attributes.normal\n\n    if (!normal) {\n      console.error('Geometry must contain normal attribute. ')\n    }\n\n    if (normal.isPacked) return\n\n    if (normal.itemSize != 3) {\n      console.error('normal.itemSize is not 3, which cannot be encoded. ')\n    }\n\n    const array = normal.array\n    const count = normal.count\n\n    let result\n    if (encodeMethod == 'DEFAULT') {\n      // TODO: Add 1 byte to the result, making the encoded length to be 4 bytes.\n      result = new Uint8Array(count * 3)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[idx], array[idx + 1], array[idx + 2], 1)\n\n        result[idx + 0] = encoded[0]\n        result[idx + 1] = encoded[1]\n        result[idx + 2] = encoded[2]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 3, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 1\n    } else if (encodeMethod == 'OCT1Byte') {\n      /**\n       * It is not recommended to use 1-byte octahedron normals encoding unless you want to extremely reduce the memory usage\n       * As it makes vertex data not aligned to a 4 byte boundary which may harm some WebGL implementations and sometimes the normal distortion is visible\n       * Please refer to @zeux 's comments in https://github.com/mrdoob/three.js/pull/18208\n       */\n\n      result = new Int8Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 1)\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 1\n    } else if (encodeMethod == 'OCT2Byte') {\n      result = new Int16Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 2)\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 2\n    } else if (encodeMethod == 'ANGLES') {\n      result = new Uint16Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.anglesEncode(array[idx], array[idx + 1], array[idx + 2])\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 2\n    } else {\n      console.error('Unrecognized encoding method, should be `DEFAULT` or `ANGLES` or `OCT`. ')\n    }\n\n    mesh.geometry.attributes.normal.needsUpdate = true\n    mesh.geometry.attributes.normal.isPacked = true\n    mesh.geometry.attributes.normal.packingMethod = encodeMethod\n\n    // modify material\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material)\n    }\n\n    if (encodeMethod == 'ANGLES') {\n      mesh.material.defines.USE_PACKED_NORMAL = 0\n    }\n\n    if (encodeMethod == 'OCT1Byte') {\n      mesh.material.defines.USE_PACKED_NORMAL = 1\n    }\n\n    if (encodeMethod == 'OCT2Byte') {\n      mesh.material.defines.USE_PACKED_NORMAL = 1\n    }\n\n    if (encodeMethod == 'DEFAULT') {\n      mesh.material.defines.USE_PACKED_NORMAL = 2\n    }\n  },\n\n  /**\n   * Make the input mesh.geometry's position attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the position data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressPositions: function (mesh) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry. ')\n    }\n\n    const position = mesh.geometry.attributes.position\n\n    if (!position) {\n      console.error('Geometry must contain position attribute. ')\n    }\n\n    if (position.isPacked) return\n\n    if (position.itemSize != 3) {\n      console.error('position.itemSize is not 3, which cannot be packed. ')\n    }\n\n    const array = position.array\n    const encodingBytes = 2\n\n    const result = this.EncodingFuncs.quantizedEncode(array, encodingBytes)\n\n    const quantized = result.quantized\n    const decodeMat = result.decodeMat\n\n    // IMPORTANT: calculate original geometry bounding info first, before updating packed positions\n    if (mesh.geometry.boundingBox == null) mesh.geometry.computeBoundingBox()\n    if (mesh.geometry.boundingSphere == null) mesh.geometry.computeBoundingSphere()\n\n    mesh.geometry.setAttribute('position', new BufferAttribute(quantized, 3))\n    mesh.geometry.attributes.position.isPacked = true\n    mesh.geometry.attributes.position.needsUpdate = true\n    mesh.geometry.attributes.position.bytes = quantized.length * encodingBytes\n\n    // modify material\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material)\n    }\n\n    mesh.material.defines.USE_PACKED_POSITION = 0\n\n    mesh.material.uniforms.quantizeMatPos.value = decodeMat\n    mesh.material.uniforms.quantizeMatPos.needsUpdate = true\n  },\n\n  /**\n   * Make the input mesh.geometry's uv attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the uv data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressUvs: function (mesh) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry property. ')\n    }\n\n    const uvs = mesh.geometry.attributes.uv\n\n    if (!uvs) {\n      console.error('Geometry must contain uv attribute. ')\n    }\n\n    if (uvs.isPacked) return\n\n    const range = { min: Infinity, max: -Infinity }\n\n    const array = uvs.array\n\n    for (let i = 0; i < array.length; i++) {\n      range.min = Math.min(range.min, array[i])\n      range.max = Math.max(range.max, array[i])\n    }\n\n    let result\n\n    if (range.min >= -1.0 && range.max <= 1.0) {\n      // use default encoding method\n      result = new Uint16Array(array.length)\n\n      for (let i = 0; i < array.length; i += 2) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[i], array[i + 1], 0, 2)\n\n        result[i] = encoded[0]\n        result[i + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('uv', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.uv.isPacked = true\n      mesh.geometry.attributes.uv.needsUpdate = true\n      mesh.geometry.attributes.uv.bytes = result.length * 2\n\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material)\n      }\n\n      mesh.material.defines.USE_PACKED_UV = 0\n    } else {\n      // use quantized encoding method\n      result = this.EncodingFuncs.quantizedEncodeUV(array, 2)\n\n      mesh.geometry.setAttribute('uv', new BufferAttribute(result.quantized, 2))\n      mesh.geometry.attributes.uv.isPacked = true\n      mesh.geometry.attributes.uv.needsUpdate = true\n      mesh.geometry.attributes.uv.bytes = result.quantized.length * 2\n\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material)\n      }\n\n      mesh.material.defines.USE_PACKED_UV = 1\n\n      mesh.material.uniforms.quantizeMatUV.value = result.decodeMat\n      mesh.material.uniforms.quantizeMatUV.needsUpdate = true\n    }\n  },\n\n  EncodingFuncs: {\n    defaultEncode: function (x, y, z, bytes) {\n      if (bytes == 1) {\n        const tmpx = Math.round((x + 1) * 0.5 * 255)\n        const tmpy = Math.round((y + 1) * 0.5 * 255)\n        const tmpz = Math.round((z + 1) * 0.5 * 255)\n        return new Uint8Array([tmpx, tmpy, tmpz])\n      } else if (bytes == 2) {\n        const tmpx = Math.round((x + 1) * 0.5 * 65535)\n        const tmpy = Math.round((y + 1) * 0.5 * 65535)\n        const tmpz = Math.round((z + 1) * 0.5 * 65535)\n        return new Uint16Array([tmpx, tmpy, tmpz])\n      } else {\n        console.error('number of bytes must be 1 or 2')\n      }\n    },\n\n    defaultDecode: function (array, bytes) {\n      if (bytes == 1) {\n        return [(array[0] / 255) * 2.0 - 1.0, (array[1] / 255) * 2.0 - 1.0, (array[2] / 255) * 2.0 - 1.0]\n      } else if (bytes == 2) {\n        return [(array[0] / 65535) * 2.0 - 1.0, (array[1] / 65535) * 2.0 - 1.0, (array[2] / 65535) * 2.0 - 1.0]\n      } else {\n        console.error('number of bytes must be 1 or 2')\n      }\n    },\n\n    // for `Angles` encoding\n    anglesEncode: function (x, y, z) {\n      const normal0 = parseInt(0.5 * (1.0 + Math.atan2(y, x) / Math.PI) * 65535)\n      const normal1 = parseInt(0.5 * (1.0 + z) * 65535)\n      return new Uint16Array([normal0, normal1])\n    },\n\n    // for `Octahedron` encoding\n    octEncodeBest: function (x, y, z, bytes) {\n      var oct, dec, best, currentCos, bestCos\n\n      // Test various combinations of ceil and floor\n      // to minimize rounding errors\n      best = oct = octEncodeVec3(x, y, z, 'floor', 'floor')\n      dec = octDecodeVec2(oct)\n      bestCos = dot(x, y, z, dec)\n\n      oct = octEncodeVec3(x, y, z, 'ceil', 'floor')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n        bestCos = currentCos\n      }\n\n      oct = octEncodeVec3(x, y, z, 'floor', 'ceil')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n        bestCos = currentCos\n      }\n\n      oct = octEncodeVec3(x, y, z, 'ceil', 'ceil')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n      }\n\n      return best\n\n      function octEncodeVec3(x0, y0, z0, xfunc, yfunc) {\n        var x = x0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0))\n        var y = y0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0))\n\n        if (z < 0) {\n          var tempx = (1 - Math.abs(y)) * (x >= 0 ? 1 : -1)\n          var tempy = (1 - Math.abs(x)) * (y >= 0 ? 1 : -1)\n\n          x = tempx\n          y = tempy\n\n          var diff = 1 - Math.abs(x) - Math.abs(y)\n          if (diff > 0) {\n            diff += 0.001\n            x += x > 0 ? diff / 2 : -diff / 2\n            y += y > 0 ? diff / 2 : -diff / 2\n          }\n        }\n\n        if (bytes == 1) {\n          return new Int8Array([Math[xfunc](x * 127.5 + (x < 0 ? 1 : 0)), Math[yfunc](y * 127.5 + (y < 0 ? 1 : 0))])\n        }\n\n        if (bytes == 2) {\n          return new Int16Array([\n            Math[xfunc](x * 32767.5 + (x < 0 ? 1 : 0)),\n            Math[yfunc](y * 32767.5 + (y < 0 ? 1 : 0)),\n          ])\n        }\n      }\n\n      function octDecodeVec2(oct) {\n        var x = oct[0]\n        var y = oct[1]\n\n        if (bytes == 1) {\n          x /= x < 0 ? 127 : 128\n          y /= y < 0 ? 127 : 128\n        } else if (bytes == 2) {\n          x /= x < 0 ? 32767 : 32768\n          y /= y < 0 ? 32767 : 32768\n        }\n\n        var z = 1 - Math.abs(x) - Math.abs(y)\n\n        if (z < 0) {\n          var tmpx = x\n          x = (1 - Math.abs(y)) * (x >= 0 ? 1 : -1)\n          y = (1 - Math.abs(tmpx)) * (y >= 0 ? 1 : -1)\n        }\n\n        var length = Math.sqrt(x * x + y * y + z * z)\n\n        return [x / length, y / length, z / length]\n      }\n\n      function dot(x, y, z, vec3) {\n        return x * vec3[0] + y * vec3[1] + z * vec3[2]\n      }\n    },\n\n    quantizedEncode: function (array, bytes) {\n      let quantized, segments\n\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length)\n        segments = 255\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length)\n        segments = 65535\n      } else {\n        console.error('number of bytes error! ')\n      }\n\n      const decodeMat = new Matrix4()\n\n      const min = new Float32Array(3)\n      const max = new Float32Array(3)\n\n      min[0] = min[1] = min[2] = Number.MAX_VALUE\n      max[0] = max[1] = max[2] = -Number.MAX_VALUE\n\n      for (let i = 0; i < array.length; i += 3) {\n        min[0] = Math.min(min[0], array[i + 0])\n        min[1] = Math.min(min[1], array[i + 1])\n        min[2] = Math.min(min[2], array[i + 2])\n        max[0] = Math.max(max[0], array[i + 0])\n        max[1] = Math.max(max[1], array[i + 1])\n        max[2] = Math.max(max[2], array[i + 2])\n      }\n\n      decodeMat.scale(\n        new Vector3((max[0] - min[0]) / segments, (max[1] - min[1]) / segments, (max[2] - min[2]) / segments),\n      )\n\n      decodeMat.elements[12] = min[0]\n      decodeMat.elements[13] = min[1]\n      decodeMat.elements[14] = min[2]\n\n      decodeMat.transpose()\n\n      const multiplier = new Float32Array([\n        max[0] !== min[0] ? segments / (max[0] - min[0]) : 0,\n        max[1] !== min[1] ? segments / (max[1] - min[1]) : 0,\n        max[2] !== min[2] ? segments / (max[2] - min[2]) : 0,\n      ])\n\n      for (let i = 0; i < array.length; i += 3) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0])\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1])\n        quantized[i + 2] = Math.floor((array[i + 2] - min[2]) * multiplier[2])\n      }\n\n      return {\n        quantized: quantized,\n        decodeMat: decodeMat,\n      }\n    },\n\n    quantizedEncodeUV: function (array, bytes) {\n      let quantized, segments\n\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length)\n        segments = 255\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length)\n        segments = 65535\n      } else {\n        console.error('number of bytes error! ')\n      }\n\n      const decodeMat = new Matrix3()\n\n      const min = new Float32Array(2)\n      const max = new Float32Array(2)\n\n      min[0] = min[1] = Number.MAX_VALUE\n      max[0] = max[1] = -Number.MAX_VALUE\n\n      for (let i = 0; i < array.length; i += 2) {\n        min[0] = Math.min(min[0], array[i + 0])\n        min[1] = Math.min(min[1], array[i + 1])\n        max[0] = Math.max(max[0], array[i + 0])\n        max[1] = Math.max(max[1], array[i + 1])\n      }\n\n      decodeMat.scale((max[0] - min[0]) / segments, (max[1] - min[1]) / segments)\n\n      decodeMat.elements[6] = min[0]\n      decodeMat.elements[7] = min[1]\n\n      decodeMat.transpose()\n\n      const multiplier = new Float32Array([\n        max[0] !== min[0] ? segments / (max[0] - min[0]) : 0,\n        max[1] !== min[1] ? segments / (max[1] - min[1]) : 0,\n      ])\n\n      for (let i = 0; i < array.length; i += 2) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0])\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1])\n      }\n\n      return {\n        quantized: quantized,\n        decodeMat: decodeMat,\n      }\n    },\n  },\n}\n\n/**\n * `PackedPhongMaterial` inherited from THREE.MeshPhongMaterial\n *\n * @param {Object} parameters\n */\nclass PackedPhongMaterial extends MeshPhongMaterial {\n  constructor(parameters) {\n    super()\n\n    this.defines = {}\n    this.type = 'PackedPhongMaterial'\n    this.uniforms = UniformsUtils.merge([\n      ShaderLib.phong.uniforms,\n\n      {\n        quantizeMatPos: { value: null },\n        quantizeMatUV: { value: null },\n      },\n    ])\n\n    this.vertexShader = [\n      '#define PHONG',\n\n      'varying vec3 vViewPosition;',\n\n      '#ifndef FLAT_SHADED',\n      'varying vec3 vNormal;',\n      '#endif',\n\n      ShaderChunk.common,\n      ShaderChunk.uv_pars_vertex,\n      ShaderChunk.uv2_pars_vertex,\n      ShaderChunk.displacementmap_pars_vertex,\n      ShaderChunk.envmap_pars_vertex,\n      ShaderChunk.color_pars_vertex,\n      ShaderChunk.fog_pars_vertex,\n      ShaderChunk.morphtarget_pars_vertex,\n      ShaderChunk.skinning_pars_vertex,\n      ShaderChunk.shadowmap_pars_vertex,\n      ShaderChunk.logdepthbuf_pars_vertex,\n      ShaderChunk.clipping_planes_pars_vertex,\n\n      `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\t#if USE_PACKED_NORMAL == 0\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfloat x = packedNormal.x * 2.0 - 1.0;\n\t\t\t\t\t\t\tfloat y = packedNormal.y * 2.0 - 1.0;\n\t\t\t\t\t\t\tvec2 scth = vec2(sin(x * PI), cos(x * PI));\n\t\t\t\t\t\t\tvec2 scphi = vec2(sqrt(1.0 - y * y), y);\n\t\t\t\t\t\t\treturn normalize( vec3(scth.y * scphi.x, scth.x * scphi.x, scphi.y) );\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 1\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = vec3(packedNormal.xy, 1.0 - abs(packedNormal.x) - abs(packedNormal.y));\n\t\t\t\t\t\t\tif (v.z < 0.0)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tv.xy = (1.0 - abs(v.yx)) * vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 2\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = (packedNormal * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\tuniform mat4 quantizeMatPos;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tuniform mat3 quantizeMatUV;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 0\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = (packedUV * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = ( vec3(packedUV, 1.0) * quantizeMatUV ).xy;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      'void main() {',\n\n      ShaderChunk.uv_vertex,\n\n      `#ifdef USE_UV\n\t\t\t\t\t#ifdef USE_PACKED_UV\n\t\t\t\t\t\tvUv = decodeUV(vUv);\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      ShaderChunk.uv2_vertex,\n      ShaderChunk.color_vertex,\n      ShaderChunk.beginnormal_vertex,\n\n      `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\tobjectNormal = decodeNormal(objectNormal);\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_TANGENT\n\t\t\t\t\tvec3 objectTangent = vec3( tangent.xyz );\n\t\t\t\t#endif\n\t\t\t\t`,\n\n      ShaderChunk.morphnormal_vertex,\n      ShaderChunk.skinbase_vertex,\n      ShaderChunk.skinnormal_vertex,\n      ShaderChunk.defaultnormal_vertex,\n\n      '#ifndef FLAT_SHADED',\n      '\tvNormal = normalize( transformedNormal );',\n      '#endif',\n\n      ShaderChunk.begin_vertex,\n\n      `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\ttransformed = ( vec4(transformed, 1.0) * quantizeMatPos ).xyz;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      ShaderChunk.morphtarget_vertex,\n      ShaderChunk.skinning_vertex,\n      ShaderChunk.displacementmap_vertex,\n      ShaderChunk.project_vertex,\n      ShaderChunk.logdepthbuf_vertex,\n      ShaderChunk.clipping_planes_vertex,\n\n      'vViewPosition = - mvPosition.xyz;',\n\n      ShaderChunk.worldpos_vertex,\n      ShaderChunk.envmap_vertex,\n      ShaderChunk.shadowmap_vertex,\n      ShaderChunk.fog_vertex,\n\n      '}',\n    ].join('\\n')\n\n    // Use the original MeshPhongMaterial's fragmentShader.\n    this.fragmentShader = [\n      '#define PHONG',\n\n      'uniform vec3 diffuse;',\n      'uniform vec3 emissive;',\n      'uniform vec3 specular;',\n      'uniform float shininess;',\n      'uniform float opacity;',\n\n      ShaderChunk.common,\n      ShaderChunk.packing,\n      ShaderChunk.dithering_pars_fragment,\n      ShaderChunk.color_pars_fragment,\n      ShaderChunk.uv_pars_fragment,\n      ShaderChunk.uv2_pars_fragment,\n      ShaderChunk.map_pars_fragment,\n      ShaderChunk.alphamap_pars_fragment,\n      ShaderChunk.aomap_pars_fragment,\n      ShaderChunk.lightmap_pars_fragment,\n      ShaderChunk.emissivemap_pars_fragment,\n      ShaderChunk.envmap_common_pars_fragment,\n      ShaderChunk.envmap_pars_fragment,\n      ShaderChunk.cube_uv_reflection_fragment,\n      ShaderChunk.fog_pars_fragment,\n      ShaderChunk.bsdfs,\n      ShaderChunk.lights_pars_begin,\n      ShaderChunk.lights_phong_pars_fragment,\n      ShaderChunk.shadowmap_pars_fragment,\n      ShaderChunk.bumpmap_pars_fragment,\n      ShaderChunk.normalmap_pars_fragment,\n      ShaderChunk.specularmap_pars_fragment,\n      ShaderChunk.logdepthbuf_pars_fragment,\n      ShaderChunk.clipping_planes_pars_fragment,\n\n      'void main() {',\n\n      ShaderChunk.clipping_planes_fragment,\n\n      'vec4 diffuseColor = vec4( diffuse, opacity );',\n      'ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );',\n      'vec3 totalEmissiveRadiance = emissive;',\n\n      ShaderChunk.logdepthbuf_fragment,\n      ShaderChunk.map_fragment,\n      ShaderChunk.color_fragment,\n      ShaderChunk.alphamap_fragment,\n      ShaderChunk.alphatest_fragment,\n      ShaderChunk.specularmap_fragment,\n      ShaderChunk.normal_fragment_begin,\n      ShaderChunk.normal_fragment_maps,\n      ShaderChunk.emissivemap_fragment,\n\n      // accumulation\n      ShaderChunk.lights_phong_fragment,\n      ShaderChunk.lights_fragment_begin,\n      ShaderChunk.lights_fragment_maps,\n      ShaderChunk.lights_fragment_end,\n\n      // modulation\n      ShaderChunk.aomap_fragment,\n\n      'vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;',\n\n      ShaderChunk.envmap_fragment,\n\n      'gl_FragColor = vec4( outgoingLight, diffuseColor.a );',\n\n      ShaderChunk.tonemapping_fragment,\n      version >= 154 ? ShaderChunk.colorspace_fragment : ShaderChunk.encodings_fragment,\n      ShaderChunk.fog_fragment,\n      ShaderChunk.premultiplied_alpha_fragment,\n      ShaderChunk.dithering_fragment,\n      '}',\n    ].join('\\n')\n\n    this.setValues(parameters)\n  }\n}\n\nexport { GeometryCompressionUtils, PackedPhongMaterial }\n"], "mappings": ";;AAmBG,IAACA,wBAAA,GAA2B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAS7BC,eAAA,EAAiB,SAAAA,CAAUC,IAAA,EAAMC,YAAA,EAAc;IAC7C,IAAI,CAACD,IAAA,CAAKE,QAAA,EAAU;MAClBC,OAAA,CAAQC,KAAA,CAAM,8BAA8B;IAC7C;IAED,MAAMC,MAAA,GAASL,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA;IAExC,IAAI,CAACA,MAAA,EAAQ;MACXF,OAAA,CAAQC,KAAA,CAAM,0CAA0C;IACzD;IAED,IAAIC,MAAA,CAAOE,QAAA,EAAU;IAErB,IAAIF,MAAA,CAAOG,QAAA,IAAY,GAAG;MACxBL,OAAA,CAAQC,KAAA,CAAM,qDAAqD;IACpE;IAED,MAAMK,KAAA,GAAQJ,MAAA,CAAOI,KAAA;IACrB,MAAMC,KAAA,GAAQL,MAAA,CAAOK,KAAA;IAErB,IAAIC,MAAA;IACJ,IAAIV,YAAA,IAAgB,WAAW;MAE7BU,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,GAAQ,CAAC;MAEjC,SAASG,GAAA,GAAM,GAAGA,GAAA,GAAMJ,KAAA,CAAMK,MAAA,EAAQD,GAAA,IAAO,GAAG;QAC9C,MAAME,OAAA,GAAU,KAAKC,aAAA,CAAcC,aAAA,CAAcR,KAAA,CAAMI,GAAG,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAG,CAAC;QAE9FF,MAAA,CAAOE,GAAA,GAAM,CAAC,IAAIE,OAAA,CAAQ,CAAC;QAC3BJ,MAAA,CAAOE,GAAA,GAAM,CAAC,IAAIE,OAAA,CAAQ,CAAC;QAC3BJ,MAAA,CAAOE,GAAA,GAAM,CAAC,IAAIE,OAAA,CAAQ,CAAC;MAC5B;MAEDf,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBR,MAAA,EAAQ,GAAG,IAAI,CAAC;MACzEX,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOe,KAAA,GAAQT,MAAA,CAAOG,MAAA,GAAS;IAC9D,WAAeb,YAAA,IAAgB,YAAY;MAOrCU,MAAA,GAAS,IAAIU,SAAA,CAAUX,KAAA,GAAQ,CAAC;MAEhC,SAASG,GAAA,GAAM,GAAGA,GAAA,GAAMJ,KAAA,CAAMK,MAAA,EAAQD,GAAA,IAAO,GAAG;QAC9C,MAAME,OAAA,GAAU,KAAKC,aAAA,CAAcM,aAAA,CAAcb,KAAA,CAAMI,GAAG,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAG,CAAC;QAE9FF,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;QACrCJ,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;MACtC;MAEDf,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBR,MAAA,EAAQ,GAAG,IAAI,CAAC;MACzEX,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOe,KAAA,GAAQT,MAAA,CAAOG,MAAA,GAAS;IAC9D,WAAeb,YAAA,IAAgB,YAAY;MACrCU,MAAA,GAAS,IAAIY,UAAA,CAAWb,KAAA,GAAQ,CAAC;MAEjC,SAASG,GAAA,GAAM,GAAGA,GAAA,GAAMJ,KAAA,CAAMK,MAAA,EAAQD,GAAA,IAAO,GAAG;QAC9C,MAAME,OAAA,GAAU,KAAKC,aAAA,CAAcM,aAAA,CAAcb,KAAA,CAAMI,GAAG,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAG,CAAC;QAE9FF,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;QACrCJ,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;MACtC;MAEDf,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBR,MAAA,EAAQ,GAAG,IAAI,CAAC;MACzEX,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOe,KAAA,GAAQT,MAAA,CAAOG,MAAA,GAAS;IAC9D,WAAeb,YAAA,IAAgB,UAAU;MACnCU,MAAA,GAAS,IAAIa,WAAA,CAAYd,KAAA,GAAQ,CAAC;MAElC,SAASG,GAAA,GAAM,GAAGA,GAAA,GAAMJ,KAAA,CAAMK,MAAA,EAAQD,GAAA,IAAO,GAAG;QAC9C,MAAME,OAAA,GAAU,KAAKC,aAAA,CAAcS,YAAA,CAAahB,KAAA,CAAMI,GAAG,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,GAAGJ,KAAA,CAAMI,GAAA,GAAM,CAAC,CAAC;QAE1FF,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;QACrCJ,MAAA,CAAQE,GAAA,GAAM,IAAK,IAAI,CAAC,IAAIE,OAAA,CAAQ,CAAC;MACtC;MAEDf,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBR,MAAA,EAAQ,GAAG,IAAI,CAAC;MACzEX,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOe,KAAA,GAAQT,MAAA,CAAOG,MAAA,GAAS;IAC9D,OAAW;MACLX,OAAA,CAAQC,KAAA,CAAM,0EAA0E;IACzF;IAEDJ,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOqB,WAAA,GAAc;IAC9C1B,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOE,QAAA,GAAW;IAC3CP,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAWD,MAAA,CAAOsB,aAAA,GAAgB1B,YAAA;IAGhD,IAAI,EAAED,IAAA,CAAK4B,QAAA,YAAoBC,mBAAA,GAAsB;MACnD7B,IAAA,CAAK4B,QAAA,GAAW,IAAIC,mBAAA,CAAqB,EAACC,IAAA,CAAK9B,IAAA,CAAK4B,QAAQ;IAC7D;IAED,IAAI3B,YAAA,IAAgB,UAAU;MAC5BD,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQC,iBAAA,GAAoB;IAC3C;IAED,IAAI/B,YAAA,IAAgB,YAAY;MAC9BD,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQC,iBAAA,GAAoB;IAC3C;IAED,IAAI/B,YAAA,IAAgB,YAAY;MAC9BD,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQC,iBAAA,GAAoB;IAC3C;IAED,IAAI/B,YAAA,IAAgB,WAAW;MAC7BD,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQC,iBAAA,GAAoB;IAC3C;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDC,iBAAA,EAAmB,SAAAA,CAAUjC,IAAA,EAAM;IACjC,IAAI,CAACA,IAAA,CAAKE,QAAA,EAAU;MAClBC,OAAA,CAAQC,KAAA,CAAM,8BAA8B;IAC7C;IAED,MAAM8B,QAAA,GAAWlC,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW4B,QAAA;IAE1C,IAAI,CAACA,QAAA,EAAU;MACb/B,OAAA,CAAQC,KAAA,CAAM,4CAA4C;IAC3D;IAED,IAAI8B,QAAA,CAAS3B,QAAA,EAAU;IAEvB,IAAI2B,QAAA,CAAS1B,QAAA,IAAY,GAAG;MAC1BL,OAAA,CAAQC,KAAA,CAAM,sDAAsD;IACrE;IAED,MAAMK,KAAA,GAAQyB,QAAA,CAASzB,KAAA;IACvB,MAAM0B,aAAA,GAAgB;IAEtB,MAAMxB,MAAA,GAAS,KAAKK,aAAA,CAAcoB,eAAA,CAAgB3B,KAAA,EAAO0B,aAAa;IAEtE,MAAME,SAAA,GAAY1B,MAAA,CAAO0B,SAAA;IACzB,MAAMC,SAAA,GAAY3B,MAAA,CAAO2B,SAAA;IAGzB,IAAItC,IAAA,CAAKE,QAAA,CAASqC,WAAA,IAAe,MAAMvC,IAAA,CAAKE,QAAA,CAASsC,kBAAA,CAAoB;IACzE,IAAIxC,IAAA,CAAKE,QAAA,CAASuC,cAAA,IAAkB,MAAMzC,IAAA,CAAKE,QAAA,CAASwC,qBAAA,CAAuB;IAE/E1C,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBkB,SAAA,EAAW,CAAC,CAAC;IACxErC,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW4B,QAAA,CAAS3B,QAAA,GAAW;IAC7CP,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW4B,QAAA,CAASR,WAAA,GAAc;IAChD1B,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW4B,QAAA,CAASd,KAAA,GAAQiB,SAAA,CAAUvB,MAAA,GAASqB,aAAA;IAG7D,IAAI,EAAEnC,IAAA,CAAK4B,QAAA,YAAoBC,mBAAA,GAAsB;MACnD7B,IAAA,CAAK4B,QAAA,GAAW,IAAIC,mBAAA,CAAqB,EAACC,IAAA,CAAK9B,IAAA,CAAK4B,QAAQ;IAC7D;IAED5B,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQY,mBAAA,GAAsB;IAE5C3C,IAAA,CAAK4B,QAAA,CAASgB,QAAA,CAASC,cAAA,CAAeC,KAAA,GAAQR,SAAA;IAC9CtC,IAAA,CAAK4B,QAAA,CAASgB,QAAA,CAASC,cAAA,CAAenB,WAAA,GAAc;EACrD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDqB,WAAA,EAAa,SAAAA,CAAU/C,IAAA,EAAM;IAC3B,IAAI,CAACA,IAAA,CAAKE,QAAA,EAAU;MAClBC,OAAA,CAAQC,KAAA,CAAM,uCAAuC;IACtD;IAED,MAAM4C,GAAA,GAAMhD,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA;IAErC,IAAI,CAACD,GAAA,EAAK;MACR7C,OAAA,CAAQC,KAAA,CAAM,sCAAsC;IACrD;IAED,IAAI4C,GAAA,CAAIzC,QAAA,EAAU;IAElB,MAAM2C,KAAA,GAAQ;MAAEC,GAAA,EAAKC,QAAA;MAAUC,GAAA,EAAK,CAAAD;IAAW;IAE/C,MAAM3C,KAAA,GAAQuC,GAAA,CAAIvC,KAAA;IAElB,SAAS6C,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK;MACrCJ,KAAA,CAAMC,GAAA,GAAMI,IAAA,CAAKJ,GAAA,CAAID,KAAA,CAAMC,GAAA,EAAK1C,KAAA,CAAM6C,CAAC,CAAC;MACxCJ,KAAA,CAAMG,GAAA,GAAME,IAAA,CAAKF,GAAA,CAAIH,KAAA,CAAMG,GAAA,EAAK5C,KAAA,CAAM6C,CAAC,CAAC;IACzC;IAED,IAAI3C,MAAA;IAEJ,IAAIuC,KAAA,CAAMC,GAAA,IAAO,MAAQD,KAAA,CAAMG,GAAA,IAAO,GAAK;MAEzC1C,MAAA,GAAS,IAAIa,WAAA,CAAYf,KAAA,CAAMK,MAAM;MAErC,SAASwC,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK,GAAG;QACxC,MAAMvC,OAAA,GAAU,KAAKC,aAAA,CAAcC,aAAA,CAAcR,KAAA,CAAM6C,CAAC,GAAG7C,KAAA,CAAM6C,CAAA,GAAI,CAAC,GAAG,GAAG,CAAC;QAE7E3C,MAAA,CAAO2C,CAAC,IAAIvC,OAAA,CAAQ,CAAC;QACrBJ,MAAA,CAAO2C,CAAA,GAAI,CAAC,IAAIvC,OAAA,CAAQ,CAAC;MAC1B;MAEDf,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,MAAM,IAAIC,eAAA,CAAgBR,MAAA,EAAQ,GAAG,IAAI,CAAC;MACrEX,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAG1C,QAAA,GAAW;MACvCP,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAGvB,WAAA,GAAc;MAC1C1B,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAG7B,KAAA,GAAQT,MAAA,CAAOG,MAAA,GAAS;MAEpD,IAAI,EAAEd,IAAA,CAAK4B,QAAA,YAAoBC,mBAAA,GAAsB;QACnD7B,IAAA,CAAK4B,QAAA,GAAW,IAAIC,mBAAA,CAAqB,EAACC,IAAA,CAAK9B,IAAA,CAAK4B,QAAQ;MAC7D;MAED5B,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQyB,aAAA,GAAgB;IAC5C,OAAW;MAEL7C,MAAA,GAAS,KAAKK,aAAA,CAAcyC,iBAAA,CAAkBhD,KAAA,EAAO,CAAC;MAEtDT,IAAA,CAAKE,QAAA,CAASgB,YAAA,CAAa,MAAM,IAAIC,eAAA,CAAgBR,MAAA,CAAO0B,SAAA,EAAW,CAAC,CAAC;MACzErC,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAG1C,QAAA,GAAW;MACvCP,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAGvB,WAAA,GAAc;MAC1C1B,IAAA,CAAKE,QAAA,CAASI,UAAA,CAAW2C,EAAA,CAAG7B,KAAA,GAAQT,MAAA,CAAO0B,SAAA,CAAUvB,MAAA,GAAS;MAE9D,IAAI,EAAEd,IAAA,CAAK4B,QAAA,YAAoBC,mBAAA,GAAsB;QACnD7B,IAAA,CAAK4B,QAAA,GAAW,IAAIC,mBAAA,CAAqB,EAACC,IAAA,CAAK9B,IAAA,CAAK4B,QAAQ;MAC7D;MAED5B,IAAA,CAAK4B,QAAA,CAASG,OAAA,CAAQyB,aAAA,GAAgB;MAEtCxD,IAAA,CAAK4B,QAAA,CAASgB,QAAA,CAASc,aAAA,CAAcZ,KAAA,GAAQnC,MAAA,CAAO2B,SAAA;MACpDtC,IAAA,CAAK4B,QAAA,CAASgB,QAAA,CAASc,aAAA,CAAchC,WAAA,GAAc;IACpD;EACF;EAEDV,aAAA,EAAe;IACbC,aAAA,EAAe,SAAAA,CAAU0C,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGzC,KAAA,EAAO;MACvC,IAAIA,KAAA,IAAS,GAAG;QACd,MAAM0C,IAAA,GAAOP,IAAA,CAAKQ,KAAA,EAAOJ,CAAA,GAAI,KAAK,MAAM,GAAG;QAC3C,MAAMK,IAAA,GAAOT,IAAA,CAAKQ,KAAA,EAAOH,CAAA,GAAI,KAAK,MAAM,GAAG;QAC3C,MAAMK,IAAA,GAAOV,IAAA,CAAKQ,KAAA,EAAOF,CAAA,GAAI,KAAK,MAAM,GAAG;QAC3C,OAAO,IAAIjD,UAAA,CAAW,CAACkD,IAAA,EAAME,IAAA,EAAMC,IAAI,CAAC;MAChD,WAAiB7C,KAAA,IAAS,GAAG;QACrB,MAAM0C,IAAA,GAAOP,IAAA,CAAKQ,KAAA,EAAOJ,CAAA,GAAI,KAAK,MAAM,KAAK;QAC7C,MAAMK,IAAA,GAAOT,IAAA,CAAKQ,KAAA,EAAOH,CAAA,GAAI,KAAK,MAAM,KAAK;QAC7C,MAAMK,IAAA,GAAOV,IAAA,CAAKQ,KAAA,EAAOF,CAAA,GAAI,KAAK,MAAM,KAAK;QAC7C,OAAO,IAAIrC,WAAA,CAAY,CAACsC,IAAA,EAAME,IAAA,EAAMC,IAAI,CAAC;MACjD,OAAa;QACL9D,OAAA,CAAQC,KAAA,CAAM,gCAAgC;MAC/C;IACF;IAED8D,aAAA,EAAe,SAAAA,CAAUzD,KAAA,EAAOW,KAAA,EAAO;MACrC,IAAIA,KAAA,IAAS,GAAG;QACd,OAAO,CAAEX,KAAA,CAAM,CAAC,IAAI,MAAO,IAAM,GAAMA,KAAA,CAAM,CAAC,IAAI,MAAO,IAAM,GAAMA,KAAA,CAAM,CAAC,IAAI,MAAO,IAAM,CAAG;MACxG,WAAiBW,KAAA,IAAS,GAAG;QACrB,OAAO,CAAEX,KAAA,CAAM,CAAC,IAAI,QAAS,IAAM,GAAMA,KAAA,CAAM,CAAC,IAAI,QAAS,IAAM,GAAMA,KAAA,CAAM,CAAC,IAAI,QAAS,IAAM,CAAG;MAC9G,OAAa;QACLN,OAAA,CAAQC,KAAA,CAAM,gCAAgC;MAC/C;IACF;IAAA;IAGDqB,YAAA,EAAc,SAAAA,CAAUkC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAC/B,MAAMM,OAAA,GAAUC,QAAA,CAAS,OAAO,IAAMb,IAAA,CAAKc,KAAA,CAAMT,CAAA,EAAGD,CAAC,IAAIJ,IAAA,CAAKe,EAAA,IAAM,KAAK;MACzE,MAAMC,OAAA,GAAUH,QAAA,CAAS,OAAO,IAAMP,CAAA,IAAK,KAAK;MAChD,OAAO,IAAIrC,WAAA,CAAY,CAAC2C,OAAA,EAASI,OAAO,CAAC;IAC1C;IAAA;IAGDjD,aAAA,EAAe,SAAAA,CAAUqC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGzC,KAAA,EAAO;MACvC,IAAIoD,GAAA,EAAKC,GAAA,EAAKC,IAAA,EAAMC,UAAA,EAAYC,OAAA;MAIhCF,IAAA,GAAOF,GAAA,GAAMK,aAAA,CAAclB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG,SAAS,OAAO;MACpDY,GAAA,GAAMK,aAAA,CAAcN,GAAG;MACvBI,OAAA,GAAUG,GAAA,CAAIpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGY,GAAG;MAE1BD,GAAA,GAAMK,aAAA,CAAclB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG,QAAQ,OAAO;MAC5CY,GAAA,GAAMK,aAAA,CAAcN,GAAG;MACvBG,UAAA,GAAaI,GAAA,CAAIpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGY,GAAG;MAE7B,IAAIE,UAAA,GAAaC,OAAA,EAAS;QACxBF,IAAA,GAAOF,GAAA;QACPI,OAAA,GAAUD,UAAA;MACX;MAEDH,GAAA,GAAMK,aAAA,CAAclB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG,SAAS,MAAM;MAC5CY,GAAA,GAAMK,aAAA,CAAcN,GAAG;MACvBG,UAAA,GAAaI,GAAA,CAAIpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGY,GAAG;MAE7B,IAAIE,UAAA,GAAaC,OAAA,EAAS;QACxBF,IAAA,GAAOF,GAAA;QACPI,OAAA,GAAUD,UAAA;MACX;MAEDH,GAAA,GAAMK,aAAA,CAAclB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG,QAAQ,MAAM;MAC3CY,GAAA,GAAMK,aAAA,CAAcN,GAAG;MACvBG,UAAA,GAAaI,GAAA,CAAIpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGY,GAAG;MAE7B,IAAIE,UAAA,GAAaC,OAAA,EAAS;QACxBF,IAAA,GAAOF,GAAA;MACR;MAED,OAAOE,IAAA;MAEP,SAASG,cAAcG,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,KAAA,EAAOC,KAAA,EAAO;QAC/C,IAAIC,EAAA,GAAIL,EAAA,IAAMzB,IAAA,CAAK+B,GAAA,CAAIN,EAAE,IAAIzB,IAAA,CAAK+B,GAAA,CAAIL,EAAE,IAAI1B,IAAA,CAAK+B,GAAA,CAAIJ,EAAE;QACvD,IAAIK,EAAA,GAAIN,EAAA,IAAM1B,IAAA,CAAK+B,GAAA,CAAIN,EAAE,IAAIzB,IAAA,CAAK+B,GAAA,CAAIL,EAAE,IAAI1B,IAAA,CAAK+B,GAAA,CAAIJ,EAAE;QAEvD,IAAIrB,CAAA,GAAI,GAAG;UACT,IAAI2B,KAAA,IAAS,IAAIjC,IAAA,CAAK+B,GAAA,CAAIC,EAAC,MAAMF,EAAA,IAAK,IAAI,IAAI;UAC9C,IAAII,KAAA,IAAS,IAAIlC,IAAA,CAAK+B,GAAA,CAAID,EAAC,MAAME,EAAA,IAAK,IAAI,IAAI;UAE9CF,EAAA,GAAIG,KAAA;UACJD,EAAA,GAAIE,KAAA;UAEJ,IAAIC,IAAA,GAAO,IAAInC,IAAA,CAAK+B,GAAA,CAAID,EAAC,IAAI9B,IAAA,CAAK+B,GAAA,CAAIC,EAAC;UACvC,IAAIG,IAAA,GAAO,GAAG;YACZA,IAAA,IAAQ;YACRL,EAAA,IAAKA,EAAA,GAAI,IAAIK,IAAA,GAAO,IAAI,CAACA,IAAA,GAAO;YAChCH,EAAA,IAAKA,EAAA,GAAI,IAAIG,IAAA,GAAO,IAAI,CAACA,IAAA,GAAO;UACjC;QACF;QAED,IAAItE,KAAA,IAAS,GAAG;UACd,OAAO,IAAIC,SAAA,CAAU,CAACkC,IAAA,CAAK4B,KAAK,EAAEE,EAAA,GAAI,SAASA,EAAA,GAAI,IAAI,IAAI,EAAE,GAAG9B,IAAA,CAAK6B,KAAK,EAAEG,EAAA,GAAI,SAASA,EAAA,GAAI,IAAI,IAAI,EAAE,CAAC,CAAC;QAC1G;QAED,IAAInE,KAAA,IAAS,GAAG;UACd,OAAO,IAAIG,UAAA,CAAW,CACpBgC,IAAA,CAAK4B,KAAK,EAAEE,EAAA,GAAI,WAAWA,EAAA,GAAI,IAAI,IAAI,EAAE,GACzC9B,IAAA,CAAK6B,KAAK,EAAEG,EAAA,GAAI,WAAWA,EAAA,GAAI,IAAI,IAAI,EAAE,EAC1C;QACF;MACF;MAED,SAAST,cAAca,IAAA,EAAK;QAC1B,IAAIN,EAAA,GAAIM,IAAA,CAAI,CAAC;QACb,IAAIJ,EAAA,GAAII,IAAA,CAAI,CAAC;QAEb,IAAIvE,KAAA,IAAS,GAAG;UACdiE,EAAA,IAAKA,EAAA,GAAI,IAAI,MAAM;UACnBE,EAAA,IAAKA,EAAA,GAAI,IAAI,MAAM;QAC7B,WAAmBnE,KAAA,IAAS,GAAG;UACrBiE,EAAA,IAAKA,EAAA,GAAI,IAAI,QAAQ;UACrBE,EAAA,IAAKA,EAAA,GAAI,IAAI,QAAQ;QACtB;QAED,IAAIK,EAAA,GAAI,IAAIrC,IAAA,CAAK+B,GAAA,CAAID,EAAC,IAAI9B,IAAA,CAAK+B,GAAA,CAAIC,EAAC;QAEpC,IAAIK,EAAA,GAAI,GAAG;UACT,IAAI9B,IAAA,GAAOuB,EAAA;UACXA,EAAA,IAAK,IAAI9B,IAAA,CAAK+B,GAAA,CAAIC,EAAC,MAAMF,EAAA,IAAK,IAAI,IAAI;UACtCE,EAAA,IAAK,IAAIhC,IAAA,CAAK+B,GAAA,CAAIxB,IAAI,MAAMyB,EAAA,IAAK,IAAI,IAAI;QAC1C;QAED,IAAIzE,MAAA,GAASyC,IAAA,CAAKsC,IAAA,CAAKR,EAAA,GAAIA,EAAA,GAAIE,EAAA,GAAIA,EAAA,GAAIK,EAAA,GAAIA,EAAC;QAE5C,OAAO,CAACP,EAAA,GAAIvE,MAAA,EAAQyE,EAAA,GAAIzE,MAAA,EAAQ8E,EAAA,GAAI9E,MAAM;MAC3C;MAED,SAASiE,IAAIM,EAAA,EAAGE,EAAA,EAAGK,EAAA,EAAGE,IAAA,EAAM;QAC1B,OAAOT,EAAA,GAAIS,IAAA,CAAK,CAAC,IAAIP,EAAA,GAAIO,IAAA,CAAK,CAAC,IAAIF,EAAA,GAAIE,IAAA,CAAK,CAAC;MAC9C;IACF;IAED1D,eAAA,EAAiB,SAAAA,CAAU3B,KAAA,EAAOW,KAAA,EAAO;MACvC,IAAIiB,SAAA,EAAW0D,QAAA;MAEf,IAAI3E,KAAA,IAAS,GAAG;QACdiB,SAAA,GAAY,IAAIzB,UAAA,CAAWH,KAAA,CAAMK,MAAM;QACvCiF,QAAA,GAAW;MACnB,WAAiB3E,KAAA,IAAS,GAAG;QACrBiB,SAAA,GAAY,IAAIb,WAAA,CAAYf,KAAA,CAAMK,MAAM;QACxCiF,QAAA,GAAW;MACnB,OAAa;QACL5F,OAAA,CAAQC,KAAA,CAAM,yBAAyB;MACxC;MAED,MAAMkC,SAAA,GAAY,IAAI0D,OAAA,CAAS;MAE/B,MAAM7C,GAAA,GAAM,IAAI8C,YAAA,CAAa,CAAC;MAC9B,MAAM5C,GAAA,GAAM,IAAI4C,YAAA,CAAa,CAAC;MAE9B9C,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAI+C,MAAA,CAAOC,SAAA;MAClC9C,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAI,CAAC6C,MAAA,CAAOC,SAAA;MAEnC,SAAS7C,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK,GAAG;QACxCH,GAAA,CAAI,CAAC,IAAII,IAAA,CAAKJ,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG1C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCH,GAAA,CAAI,CAAC,IAAII,IAAA,CAAKJ,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG1C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCH,GAAA,CAAI,CAAC,IAAII,IAAA,CAAKJ,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG1C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCD,GAAA,CAAI,CAAC,IAAIE,IAAA,CAAKF,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG5C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCD,GAAA,CAAI,CAAC,IAAIE,IAAA,CAAKF,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG5C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCD,GAAA,CAAI,CAAC,IAAIE,IAAA,CAAKF,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG5C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;MACvC;MAEDhB,SAAA,CAAU8D,KAAA,CACR,IAAIC,OAAA,EAAShD,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK4C,QAAA,GAAW1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK4C,QAAA,GAAW1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK4C,QAAQ,CACrG;MAEDzD,SAAA,CAAUgE,QAAA,CAAS,EAAE,IAAInD,GAAA,CAAI,CAAC;MAC9Bb,SAAA,CAAUgE,QAAA,CAAS,EAAE,IAAInD,GAAA,CAAI,CAAC;MAC9Bb,SAAA,CAAUgE,QAAA,CAAS,EAAE,IAAInD,GAAA,CAAI,CAAC;MAE9Bb,SAAA,CAAUiE,SAAA,CAAW;MAErB,MAAMC,UAAA,GAAa,IAAIP,YAAA,CAAa,CAClC5C,GAAA,CAAI,CAAC,MAAMF,GAAA,CAAI,CAAC,IAAI4C,QAAA,IAAY1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK,GACnDE,GAAA,CAAI,CAAC,MAAMF,GAAA,CAAI,CAAC,IAAI4C,QAAA,IAAY1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK,GACnDE,GAAA,CAAI,CAAC,MAAMF,GAAA,CAAI,CAAC,IAAI4C,QAAA,IAAY1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK,EACpD;MAED,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK,GAAG;QACxCjB,SAAA,CAAUiB,CAAA,GAAI,CAAC,IAAIC,IAAA,CAAKkD,KAAA,EAAOhG,KAAA,CAAM6C,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAI,CAAC,KAAKqD,UAAA,CAAW,CAAC,CAAC;QACrEnE,SAAA,CAAUiB,CAAA,GAAI,CAAC,IAAIC,IAAA,CAAKkD,KAAA,EAAOhG,KAAA,CAAM6C,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAI,CAAC,KAAKqD,UAAA,CAAW,CAAC,CAAC;QACrEnE,SAAA,CAAUiB,CAAA,GAAI,CAAC,IAAIC,IAAA,CAAKkD,KAAA,EAAOhG,KAAA,CAAM6C,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAI,CAAC,KAAKqD,UAAA,CAAW,CAAC,CAAC;MACtE;MAED,OAAO;QACLnE,SAAA;QACAC;MACD;IACF;IAEDmB,iBAAA,EAAmB,SAAAA,CAAUhD,KAAA,EAAOW,KAAA,EAAO;MACzC,IAAIiB,SAAA,EAAW0D,QAAA;MAEf,IAAI3E,KAAA,IAAS,GAAG;QACdiB,SAAA,GAAY,IAAIzB,UAAA,CAAWH,KAAA,CAAMK,MAAM;QACvCiF,QAAA,GAAW;MACnB,WAAiB3E,KAAA,IAAS,GAAG;QACrBiB,SAAA,GAAY,IAAIb,WAAA,CAAYf,KAAA,CAAMK,MAAM;QACxCiF,QAAA,GAAW;MACnB,OAAa;QACL5F,OAAA,CAAQC,KAAA,CAAM,yBAAyB;MACxC;MAED,MAAMkC,SAAA,GAAY,IAAIoE,OAAA,CAAS;MAE/B,MAAMvD,GAAA,GAAM,IAAI8C,YAAA,CAAa,CAAC;MAC9B,MAAM5C,GAAA,GAAM,IAAI4C,YAAA,CAAa,CAAC;MAE9B9C,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAI+C,MAAA,CAAOC,SAAA;MACzB9C,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAI,CAAC6C,MAAA,CAAOC,SAAA;MAE1B,SAAS7C,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK,GAAG;QACxCH,GAAA,CAAI,CAAC,IAAII,IAAA,CAAKJ,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG1C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCH,GAAA,CAAI,CAAC,IAAII,IAAA,CAAKJ,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG1C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCD,GAAA,CAAI,CAAC,IAAIE,IAAA,CAAKF,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG5C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;QACtCD,GAAA,CAAI,CAAC,IAAIE,IAAA,CAAKF,GAAA,CAAIA,GAAA,CAAI,CAAC,GAAG5C,KAAA,CAAM6C,CAAA,GAAI,CAAC,CAAC;MACvC;MAEDhB,SAAA,CAAU8D,KAAA,EAAO/C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK4C,QAAA,GAAW1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK4C,QAAQ;MAE1EzD,SAAA,CAAUgE,QAAA,CAAS,CAAC,IAAInD,GAAA,CAAI,CAAC;MAC7Bb,SAAA,CAAUgE,QAAA,CAAS,CAAC,IAAInD,GAAA,CAAI,CAAC;MAE7Bb,SAAA,CAAUiE,SAAA,CAAW;MAErB,MAAMC,UAAA,GAAa,IAAIP,YAAA,CAAa,CAClC5C,GAAA,CAAI,CAAC,MAAMF,GAAA,CAAI,CAAC,IAAI4C,QAAA,IAAY1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK,GACnDE,GAAA,CAAI,CAAC,MAAMF,GAAA,CAAI,CAAC,IAAI4C,QAAA,IAAY1C,GAAA,CAAI,CAAC,IAAIF,GAAA,CAAI,CAAC,KAAK,EACpD;MAED,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAI7C,KAAA,CAAMK,MAAA,EAAQwC,CAAA,IAAK,GAAG;QACxCjB,SAAA,CAAUiB,CAAA,GAAI,CAAC,IAAIC,IAAA,CAAKkD,KAAA,EAAOhG,KAAA,CAAM6C,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAI,CAAC,KAAKqD,UAAA,CAAW,CAAC,CAAC;QACrEnE,SAAA,CAAUiB,CAAA,GAAI,CAAC,IAAIC,IAAA,CAAKkD,KAAA,EAAOhG,KAAA,CAAM6C,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAI,CAAC,KAAKqD,UAAA,CAAW,CAAC,CAAC;MACtE;MAED,OAAO;QACLnE,SAAA;QACAC;MACD;IACF;EACF;AACH;AAOA,MAAMT,mBAAA,SAA4B8E,iBAAA,CAAkB;EAClDC,YAAYC,UAAA,EAAY;IACtB,MAAO;IAEP,KAAK9E,OAAA,GAAU,CAAE;IACjB,KAAK+E,IAAA,GAAO;IACZ,KAAKlE,QAAA,GAAWmE,aAAA,CAAcC,KAAA,CAAM,CAClCC,SAAA,CAAUC,KAAA,CAAMtE,QAAA,EAEhB;MACEC,cAAA,EAAgB;QAAEC,KAAA,EAAO;MAAM;MAC/BY,aAAA,EAAe;QAAEZ,KAAA,EAAO;MAAM;IAC/B,EACF;IAED,KAAKqE,YAAA,GAAe,CAClB,iBAEA,+BAEA,uBACA,yBACA,UAEAC,WAAA,CAAYC,MAAA,EACZD,WAAA,CAAYE,cAAA,EACZF,WAAA,CAAYG,eAAA,EACZH,WAAA,CAAYI,2BAAA,EACZJ,WAAA,CAAYK,kBAAA,EACZL,WAAA,CAAYM,iBAAA,EACZN,WAAA,CAAYO,eAAA,EACZP,WAAA,CAAYQ,uBAAA,EACZR,WAAA,CAAYS,oBAAA,EACZT,WAAA,CAAYU,qBAAA,EACZV,WAAA,CAAYW,uBAAA,EACZX,WAAA,CAAYY,2BAAA,EAEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAiCA;AAAA;AAAA;AAAA;AAAA,aAMA;AAAA;AAAA;AAAA;AAAA,aAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAkBA,iBAEAZ,WAAA,CAAYa,SAAA,EAEZ;AAAA;AAAA;AAAA;AAAA,aAMAb,WAAA,CAAYc,UAAA,EACZd,WAAA,CAAYe,YAAA,EACZf,WAAA,CAAYgB,kBAAA,EAEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OASAhB,WAAA,CAAYiB,kBAAA,EACZjB,WAAA,CAAYkB,eAAA,EACZlB,WAAA,CAAYmB,iBAAA,EACZnB,WAAA,CAAYoB,oBAAA,EAEZ,uBACA,8CACA,UAEApB,WAAA,CAAYqB,YAAA,EAEZ;AAAA;AAAA;AAAA;AAAA,aAMArB,WAAA,CAAYsB,kBAAA,EACZtB,WAAA,CAAYuB,eAAA,EACZvB,WAAA,CAAYwB,sBAAA,EACZxB,WAAA,CAAYyB,cAAA,EACZzB,WAAA,CAAY0B,kBAAA,EACZ1B,WAAA,CAAY2B,sBAAA,EAEZ,qCAEA3B,WAAA,CAAY4B,eAAA,EACZ5B,WAAA,CAAY6B,aAAA,EACZ7B,WAAA,CAAY8B,gBAAA,EACZ9B,WAAA,CAAY+B,UAAA,EAEZ,IACN,CAAMC,IAAA,CAAK,IAAI;IAGX,KAAKC,cAAA,GAAiB,CACpB,iBAEA,yBACA,0BACA,0BACA,4BACA,0BAEAjC,WAAA,CAAYC,MAAA,EACZD,WAAA,CAAYkC,OAAA,EACZlC,WAAA,CAAYmC,uBAAA,EACZnC,WAAA,CAAYoC,mBAAA,EACZpC,WAAA,CAAYqC,gBAAA,EACZrC,WAAA,CAAYsC,iBAAA,EACZtC,WAAA,CAAYuC,iBAAA,EACZvC,WAAA,CAAYwC,sBAAA,EACZxC,WAAA,CAAYyC,mBAAA,EACZzC,WAAA,CAAY0C,sBAAA,EACZ1C,WAAA,CAAY2C,yBAAA,EACZ3C,WAAA,CAAY4C,2BAAA,EACZ5C,WAAA,CAAY6C,oBAAA,EACZ7C,WAAA,CAAY8C,2BAAA,EACZ9C,WAAA,CAAY+C,iBAAA,EACZ/C,WAAA,CAAYgD,KAAA,EACZhD,WAAA,CAAYiD,iBAAA,EACZjD,WAAA,CAAYkD,0BAAA,EACZlD,WAAA,CAAYmD,uBAAA,EACZnD,WAAA,CAAYoD,qBAAA,EACZpD,WAAA,CAAYqD,uBAAA,EACZrD,WAAA,CAAYsD,yBAAA,EACZtD,WAAA,CAAYuD,yBAAA,EACZvD,WAAA,CAAYwD,6BAAA,EAEZ,iBAEAxD,WAAA,CAAYyD,wBAAA,EAEZ,iDACA,yGACA,0CAEAzD,WAAA,CAAY0D,oBAAA,EACZ1D,WAAA,CAAY2D,YAAA,EACZ3D,WAAA,CAAY4D,cAAA,EACZ5D,WAAA,CAAY6D,iBAAA,EACZ7D,WAAA,CAAY8D,kBAAA,EACZ9D,WAAA,CAAY+D,oBAAA,EACZ/D,WAAA,CAAYgE,qBAAA,EACZhE,WAAA,CAAYiE,oBAAA,EACZjE,WAAA,CAAYkE,oBAAA;IAAA;IAGZlE,WAAA,CAAYmE,qBAAA,EACZnE,WAAA,CAAYoE,qBAAA,EACZpE,WAAA,CAAYqE,oBAAA,EACZrE,WAAA,CAAYsE,mBAAA;IAAA;IAGZtE,WAAA,CAAYuE,cAAA,EAEZ,iLAEAvE,WAAA,CAAYwE,eAAA,EAEZ,yDAEAxE,WAAA,CAAYyE,oBAAA,EACZC,OAAA,IAAW,MAAM1E,WAAA,CAAY2E,mBAAA,GAAsB3E,WAAA,CAAY4E,kBAAA,EAC/D5E,WAAA,CAAY6E,YAAA,EACZ7E,WAAA,CAAY8E,4BAAA,EACZ9E,WAAA,CAAY+E,kBAAA,EACZ,IACN,CAAM/C,IAAA,CAAK,IAAI;IAEX,KAAKgD,SAAA,CAAUvF,UAAU;EAC1B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}