{"ast": null, "code": "import { Mesh, InstancedInterleavedBuffer, InterleavedBuffer<PERSON><PERSON>ri<PERSON>e, Vector4, Vector3, Box3, Sphere, MathUtils, Matrix4, Line3 } from \"three\";\nimport { LineSegmentsGeometry } from \"./LineSegmentsGeometry.js\";\nimport { LineMaterial } from \"./LineMaterial.js\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nconst _viewport = /* @__PURE__ */new Vector4();\nconst _start = /* @__PURE__ */new Vector3();\nconst _end = /* @__PURE__ */new Vector3();\nconst _start4 = /* @__PURE__ */new Vector4();\nconst _end4 = /* @__PURE__ */new Vector4();\nconst _ssOrigin = /* @__PURE__ */new Vector4();\nconst _ssOrigin3 = /* @__PURE__ */new Vector3();\nconst _mvMatrix = /* @__PURE__ */new Matrix4();\nconst _line = /* @__PURE__ */new Line3();\nconst _closestPoint = /* @__PURE__ */new Vector3();\nconst _box = /* @__PURE__ */new Box3();\nconst _sphere = /* @__PURE__ */new Sphere();\nconst _clipToWorldVector = /* @__PURE__ */new Vector4();\nlet _ray, _lineWidth;\nfunction getWorldSpaceHalfWidth(camera, distance, resolution) {\n  _clipToWorldVector.set(0, 0, -distance, 1).applyMatrix4(camera.projectionMatrix);\n  _clipToWorldVector.multiplyScalar(1 / _clipToWorldVector.w);\n  _clipToWorldVector.x = _lineWidth / resolution.width;\n  _clipToWorldVector.y = _lineWidth / resolution.height;\n  _clipToWorldVector.applyMatrix4(camera.projectionMatrixInverse);\n  _clipToWorldVector.multiplyScalar(1 / _clipToWorldVector.w);\n  return Math.abs(Math.max(_clipToWorldVector.x, _clipToWorldVector.y));\n}\nfunction raycastWorldUnits(lineSegments, intersects) {\n  const matrixWorld = lineSegments.matrixWorld;\n  const geometry = lineSegments.geometry;\n  const instanceStart = geometry.attributes.instanceStart;\n  const instanceEnd = geometry.attributes.instanceEnd;\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count);\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _line.start.fromBufferAttribute(instanceStart, i);\n    _line.end.fromBufferAttribute(instanceEnd, i);\n    _line.applyMatrix4(matrixWorld);\n    const pointOnLine = new Vector3();\n    const point = new Vector3();\n    _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine);\n    const isInside = point.distanceTo(pointOnLine) < _lineWidth * 0.5;\n    if (isInside) {\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null\n      });\n    }\n  }\n}\nfunction raycastScreenSpace(lineSegments, camera, intersects) {\n  const projectionMatrix = camera.projectionMatrix;\n  const material = lineSegments.material;\n  const resolution = material.resolution;\n  const matrixWorld = lineSegments.matrixWorld;\n  const geometry = lineSegments.geometry;\n  const instanceStart = geometry.attributes.instanceStart;\n  const instanceEnd = geometry.attributes.instanceEnd;\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count);\n  const near = -camera.near;\n  _ray.at(1, _ssOrigin);\n  _ssOrigin.w = 1;\n  _ssOrigin.applyMatrix4(camera.matrixWorldInverse);\n  _ssOrigin.applyMatrix4(projectionMatrix);\n  _ssOrigin.multiplyScalar(1 / _ssOrigin.w);\n  _ssOrigin.x *= resolution.x / 2;\n  _ssOrigin.y *= resolution.y / 2;\n  _ssOrigin.z = 0;\n  _ssOrigin3.copy(_ssOrigin);\n  _mvMatrix.multiplyMatrices(camera.matrixWorldInverse, matrixWorld);\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _start4.fromBufferAttribute(instanceStart, i);\n    _end4.fromBufferAttribute(instanceEnd, i);\n    _start4.w = 1;\n    _end4.w = 1;\n    _start4.applyMatrix4(_mvMatrix);\n    _end4.applyMatrix4(_mvMatrix);\n    const isBehindCameraNear = _start4.z > near && _end4.z > near;\n    if (isBehindCameraNear) {\n      continue;\n    }\n    if (_start4.z > near) {\n      const deltaDist = _start4.z - _end4.z;\n      const t = (_start4.z - near) / deltaDist;\n      _start4.lerp(_end4, t);\n    } else if (_end4.z > near) {\n      const deltaDist = _end4.z - _start4.z;\n      const t = (_end4.z - near) / deltaDist;\n      _end4.lerp(_start4, t);\n    }\n    _start4.applyMatrix4(projectionMatrix);\n    _end4.applyMatrix4(projectionMatrix);\n    _start4.multiplyScalar(1 / _start4.w);\n    _end4.multiplyScalar(1 / _end4.w);\n    _start4.x *= resolution.x / 2;\n    _start4.y *= resolution.y / 2;\n    _end4.x *= resolution.x / 2;\n    _end4.y *= resolution.y / 2;\n    _line.start.copy(_start4);\n    _line.start.z = 0;\n    _line.end.copy(_end4);\n    _line.end.z = 0;\n    const param = _line.closestPointToPointParameter(_ssOrigin3, true);\n    _line.at(param, _closestPoint);\n    const zPos = MathUtils.lerp(_start4.z, _end4.z, param);\n    const isInClipSpace = zPos >= -1 && zPos <= 1;\n    const isInside = _ssOrigin3.distanceTo(_closestPoint) < _lineWidth * 0.5;\n    if (isInClipSpace && isInside) {\n      _line.start.fromBufferAttribute(instanceStart, i);\n      _line.end.fromBufferAttribute(instanceEnd, i);\n      _line.start.applyMatrix4(matrixWorld);\n      _line.end.applyMatrix4(matrixWorld);\n      const pointOnLine = new Vector3();\n      const point = new Vector3();\n      _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine);\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null\n      });\n    }\n  }\n}\nclass LineSegments2 extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({\n    color: Math.random() * 16777215\n  })) {\n    super(geometry, material);\n    this.isLineSegments2 = true;\n    this.type = \"LineSegments2\";\n  }\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n  computeLineDistances() {\n    const geometry = this.geometry;\n    const instanceStart = geometry.attributes.instanceStart;\n    const instanceEnd = geometry.attributes.instanceEnd;\n    const lineDistances = new Float32Array(2 * instanceStart.count);\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i);\n      _end.fromBufferAttribute(instanceEnd, i);\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1];\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end);\n    }\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1);\n    geometry.setAttribute(\"instanceDistanceStart\", new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0));\n    geometry.setAttribute(\"instanceDistanceEnd\", new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1));\n    return this;\n  }\n  raycast(raycaster, intersects) {\n    const worldUnits = this.material.worldUnits;\n    const camera = raycaster.camera;\n    if (camera === null && !worldUnits) {\n      console.error('LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.');\n    }\n    const threshold = raycaster.params.Line2 !== void 0 ? raycaster.params.Line2.threshold || 0 : 0;\n    _ray = raycaster.ray;\n    const matrixWorld = this.matrixWorld;\n    const geometry = this.geometry;\n    const material = this.material;\n    _lineWidth = material.linewidth + threshold;\n    if (geometry.boundingSphere === null) {\n      geometry.computeBoundingSphere();\n    }\n    _sphere.copy(geometry.boundingSphere).applyMatrix4(matrixWorld);\n    let sphereMargin;\n    if (worldUnits) {\n      sphereMargin = _lineWidth * 0.5;\n    } else {\n      const distanceToSphere = Math.max(camera.near, _sphere.distanceToPoint(_ray.origin));\n      sphereMargin = getWorldSpaceHalfWidth(camera, distanceToSphere, material.resolution);\n    }\n    _sphere.radius += sphereMargin;\n    if (_ray.intersectsSphere(_sphere) === false) {\n      return;\n    }\n    if (geometry.boundingBox === null) {\n      geometry.computeBoundingBox();\n    }\n    _box.copy(geometry.boundingBox).applyMatrix4(matrixWorld);\n    let boxMargin;\n    if (worldUnits) {\n      boxMargin = _lineWidth * 0.5;\n    } else {\n      const distanceToBox = Math.max(camera.near, _box.distanceToPoint(_ray.origin));\n      boxMargin = getWorldSpaceHalfWidth(camera, distanceToBox, material.resolution);\n    }\n    _box.expandByScalar(boxMargin);\n    if (_ray.intersectsBox(_box) === false) {\n      return;\n    }\n    if (worldUnits) {\n      raycastWorldUnits(this, intersects);\n    } else {\n      raycastScreenSpace(this, camera, intersects);\n    }\n  }\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms;\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport);\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w);\n    }\n  }\n}\nexport { LineSegments2 };", "map": {"version": 3, "names": ["_viewport", "Vector4", "_start", "Vector3", "_end", "_start4", "_end4", "_ss<PERSON><PERSON><PERSON>", "_ssOrigin3", "_mvMatrix", "Matrix4", "_line", "Line3", "_closestPoint", "_box", "Box3", "_sphere", "Sphere", "_clipToWorldVector", "_ray", "_lineWidth", "getWorldSpaceHalfWidth", "camera", "distance", "resolution", "set", "applyMatrix4", "projectionMatrix", "multiplyScalar", "w", "x", "width", "y", "height", "projectionMatrixInverse", "Math", "abs", "max", "raycastWorldUnits", "lineSegments", "intersects", "matrixWorld", "geometry", "instanceStart", "attributes", "instanceEnd", "segmentCount", "min", "instanceCount", "count", "i", "l", "start", "fromBufferAttribute", "end", "pointOnLine", "point", "distanceSqToSegment", "isInside", "distanceTo", "push", "origin", "object", "face", "faceIndex", "uv", "UV1", "raycastScreenSpace", "material", "near", "at", "matrixWorldInverse", "z", "copy", "multiplyMatrices", "isBehindCameraNear", "deltaDist", "t", "lerp", "param", "closestPointToPointParameter", "zPos", "MathUtils", "isInClipSpace", "LineSegments2", "<PERSON><PERSON>", "constructor", "LineSegmentsGeometry", "LineMaterial", "color", "random", "isLineSegments2", "type", "computeLineDistances", "lineDistances", "Float32Array", "j", "instanceDistanceBuffer", "InstancedInterleavedBuffer", "setAttribute", "InterleavedBufferAttribute", "raycast", "raycaster", "worldUnits", "console", "error", "threshold", "params", "Line2", "ray", "linewidth", "boundingSphere", "computeBoundingSphere", "sphere<PERSON>argin", "distanceToSphere", "distanceToPoint", "radius", "intersectsSphere", "boundingBox", "computeBoundingBox", "boxMargin", "distanceToBox", "expandByScalar", "intersectsBox", "onBeforeRender", "renderer", "uniforms", "getViewport", "value"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/lines/LineSegments2.js"], "sourcesContent": ["import {\n  Box3,\n  InstancedInterleavedBuffer,\n  InterleavedBuffer<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Line3,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  Sphere,\n  Vector3,\n  Vector4,\n} from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\nimport { UV1 } from '../_polyfill/uv1'\n\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\n\nconst _start4 = /* @__PURE__ */ new Vector4()\nconst _end4 = /* @__PURE__ */ new Vector4()\n\nconst _ssOrigin = /* @__PURE__ */ new Vector4()\nconst _ssOrigin3 = /* @__PURE__ */ new Vector3()\nconst _mvMatrix = /* @__PURE__ */ new Matrix4()\nconst _line = /* @__PURE__ */ new Line3()\nconst _closestPoint = /* @__PURE__ */ new Vector3()\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _clipToWorldVector = /* @__PURE__ */ new Vector4()\n\nlet _ray, _lineWidth\n\n// Returns the margin required to expand by in world space given the distance from the camera,\n// line width, resolution, and camera projection\nfunction getWorldSpaceHalfWidth(camera, distance, resolution) {\n  // transform into clip space, adjust the x and y values by the pixel width offset, then\n  // transform back into world space to get world offset. Note clip space is [-1, 1] so full\n  // width does not need to be halved.\n  _clipToWorldVector.set(0, 0, -distance, 1.0).applyMatrix4(camera.projectionMatrix)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n  _clipToWorldVector.x = _lineWidth / resolution.width\n  _clipToWorldVector.y = _lineWidth / resolution.height\n  _clipToWorldVector.applyMatrix4(camera.projectionMatrixInverse)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n\n  return Math.abs(Math.max(_clipToWorldVector.x, _clipToWorldVector.y))\n}\n\nfunction raycastWorldUnits(lineSegments, intersects) {\n  const matrixWorld = lineSegments.matrixWorld\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _line.start.fromBufferAttribute(instanceStart, i)\n    _line.end.fromBufferAttribute(instanceEnd, i)\n\n    _line.applyMatrix4(matrixWorld)\n\n    const pointOnLine = new Vector3()\n    const point = new Vector3()\n\n    _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n    const isInside = point.distanceTo(pointOnLine) < _lineWidth * 0.5\n\n    if (isInside) {\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nfunction raycastScreenSpace(lineSegments, camera, intersects) {\n  const projectionMatrix = camera.projectionMatrix\n  const material = lineSegments.material\n  const resolution = material.resolution\n  const matrixWorld = lineSegments.matrixWorld\n\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  const near = -camera.near\n\n  //\n\n  // pick a point 1 unit out along the ray to avoid the ray origin\n  // sitting at the camera origin which will cause \"w\" to be 0 when\n  // applying the projection matrix.\n  _ray.at(1, _ssOrigin)\n\n  // ndc space [ - 1.0, 1.0 ]\n  _ssOrigin.w = 1\n  _ssOrigin.applyMatrix4(camera.matrixWorldInverse)\n  _ssOrigin.applyMatrix4(projectionMatrix)\n  _ssOrigin.multiplyScalar(1 / _ssOrigin.w)\n\n  // screen space\n  _ssOrigin.x *= resolution.x / 2\n  _ssOrigin.y *= resolution.y / 2\n  _ssOrigin.z = 0\n\n  _ssOrigin3.copy(_ssOrigin)\n\n  _mvMatrix.multiplyMatrices(camera.matrixWorldInverse, matrixWorld)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _start4.fromBufferAttribute(instanceStart, i)\n    _end4.fromBufferAttribute(instanceEnd, i)\n\n    _start4.w = 1\n    _end4.w = 1\n\n    // camera space\n    _start4.applyMatrix4(_mvMatrix)\n    _end4.applyMatrix4(_mvMatrix)\n\n    // skip the segment if it's entirely behind the camera\n    const isBehindCameraNear = _start4.z > near && _end4.z > near\n    if (isBehindCameraNear) {\n      continue\n    }\n\n    // trim the segment if it extends behind camera near\n    if (_start4.z > near) {\n      const deltaDist = _start4.z - _end4.z\n      const t = (_start4.z - near) / deltaDist\n      _start4.lerp(_end4, t)\n    } else if (_end4.z > near) {\n      const deltaDist = _end4.z - _start4.z\n      const t = (_end4.z - near) / deltaDist\n      _end4.lerp(_start4, t)\n    }\n\n    // clip space\n    _start4.applyMatrix4(projectionMatrix)\n    _end4.applyMatrix4(projectionMatrix)\n\n    // ndc space [ - 1.0, 1.0 ]\n    _start4.multiplyScalar(1 / _start4.w)\n    _end4.multiplyScalar(1 / _end4.w)\n\n    // screen space\n    _start4.x *= resolution.x / 2\n    _start4.y *= resolution.y / 2\n\n    _end4.x *= resolution.x / 2\n    _end4.y *= resolution.y / 2\n\n    // create 2d segment\n    _line.start.copy(_start4)\n    _line.start.z = 0\n\n    _line.end.copy(_end4)\n    _line.end.z = 0\n\n    // get closest point on ray to segment\n    const param = _line.closestPointToPointParameter(_ssOrigin3, true)\n    _line.at(param, _closestPoint)\n\n    // check if the intersection point is within clip space\n    const zPos = MathUtils.lerp(_start4.z, _end4.z, param)\n    const isInClipSpace = zPos >= -1 && zPos <= 1\n\n    const isInside = _ssOrigin3.distanceTo(_closestPoint) < _lineWidth * 0.5\n\n    if (isInClipSpace && isInside) {\n      _line.start.fromBufferAttribute(instanceStart, i)\n      _line.end.fromBufferAttribute(instanceEnd, i)\n\n      _line.start.applyMatrix4(matrixWorld)\n      _line.end.applyMatrix4(matrixWorld)\n\n      const pointOnLine = new Vector3()\n      const point = new Vector3()\n\n      _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n\n      intersects.push({\n        point: point,\n        pointOnLine: pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nclass LineSegments2 extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLineSegments2 = true\n\n    this.type = 'LineSegments2'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  raycast(raycaster, intersects) {\n    const worldUnits = this.material.worldUnits\n    const camera = raycaster.camera\n\n    if (camera === null && !worldUnits) {\n      console.error(\n        'LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.',\n      )\n    }\n\n    const threshold = raycaster.params.Line2 !== undefined ? raycaster.params.Line2.threshold || 0 : 0\n\n    _ray = raycaster.ray\n\n    const matrixWorld = this.matrixWorld\n    const geometry = this.geometry\n    const material = this.material\n\n    _lineWidth = material.linewidth + threshold\n\n    // check if we intersect the sphere bounds\n    if (geometry.boundingSphere === null) {\n      geometry.computeBoundingSphere()\n    }\n\n    _sphere.copy(geometry.boundingSphere).applyMatrix4(matrixWorld)\n\n    // increase the sphere bounds by the worst case line screen space width\n    let sphereMargin\n    if (worldUnits) {\n      sphereMargin = _lineWidth * 0.5\n    } else {\n      const distanceToSphere = Math.max(camera.near, _sphere.distanceToPoint(_ray.origin))\n      sphereMargin = getWorldSpaceHalfWidth(camera, distanceToSphere, material.resolution)\n    }\n\n    _sphere.radius += sphereMargin\n\n    if (_ray.intersectsSphere(_sphere) === false) {\n      return\n    }\n\n    // check if we intersect the box bounds\n    if (geometry.boundingBox === null) {\n      geometry.computeBoundingBox()\n    }\n\n    _box.copy(geometry.boundingBox).applyMatrix4(matrixWorld)\n\n    // increase the box bounds by the worst case line width\n    let boxMargin\n    if (worldUnits) {\n      boxMargin = _lineWidth * 0.5\n    } else {\n      const distanceToBox = Math.max(camera.near, _box.distanceToPoint(_ray.origin))\n      boxMargin = getWorldSpaceHalfWidth(camera, distanceToBox, material.resolution)\n    }\n\n    _box.expandByScalar(boxMargin)\n\n    if (_ray.intersectsBox(_box) === false) {\n      return\n    }\n\n    if (worldUnits) {\n      raycastWorldUnits(this, intersects)\n    } else {\n      raycastScreenSpace(this, camera, intersects)\n    }\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { LineSegments2 }\n"], "mappings": ";;;;AAgBA,MAAMA,SAAA,GAA4B,mBAAIC,OAAA,CAAS;AAE/C,MAAMC,MAAA,GAAyB,mBAAIC,OAAA,CAAS;AAC5C,MAAMC,IAAA,GAAuB,mBAAID,OAAA,CAAS;AAE1C,MAAME,OAAA,GAA0B,mBAAIJ,OAAA,CAAS;AAC7C,MAAMK,KAAA,GAAwB,mBAAIL,OAAA,CAAS;AAE3C,MAAMM,SAAA,GAA4B,mBAAIN,OAAA,CAAS;AAC/C,MAAMO,UAAA,GAA6B,mBAAIL,OAAA,CAAS;AAChD,MAAMM,SAAA,GAA4B,mBAAIC,OAAA,CAAS;AAC/C,MAAMC,KAAA,GAAwB,mBAAIC,KAAA,CAAO;AACzC,MAAMC,aAAA,GAAgC,mBAAIV,OAAA,CAAS;AAEnD,MAAMW,IAAA,GAAuB,mBAAIC,IAAA,CAAM;AACvC,MAAMC,OAAA,GAA0B,mBAAIC,MAAA,CAAQ;AAC5C,MAAMC,kBAAA,GAAqC,mBAAIjB,OAAA,CAAS;AAExD,IAAIkB,IAAA,EAAMC,UAAA;AAIV,SAASC,uBAAuBC,MAAA,EAAQC,QAAA,EAAUC,UAAA,EAAY;EAI5DN,kBAAA,CAAmBO,GAAA,CAAI,GAAG,GAAG,CAACF,QAAA,EAAU,CAAG,EAAEG,YAAA,CAAaJ,MAAA,CAAOK,gBAAgB;EACjFT,kBAAA,CAAmBU,cAAA,CAAe,IAAMV,kBAAA,CAAmBW,CAAC;EAC5DX,kBAAA,CAAmBY,CAAA,GAAIV,UAAA,GAAaI,UAAA,CAAWO,KAAA;EAC/Cb,kBAAA,CAAmBc,CAAA,GAAIZ,UAAA,GAAaI,UAAA,CAAWS,MAAA;EAC/Cf,kBAAA,CAAmBQ,YAAA,CAAaJ,MAAA,CAAOY,uBAAuB;EAC9DhB,kBAAA,CAAmBU,cAAA,CAAe,IAAMV,kBAAA,CAAmBW,CAAC;EAE5D,OAAOM,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKE,GAAA,CAAInB,kBAAA,CAAmBY,CAAA,EAAGZ,kBAAA,CAAmBc,CAAC,CAAC;AACtE;AAEA,SAASM,kBAAkBC,YAAA,EAAcC,UAAA,EAAY;EACnD,MAAMC,WAAA,GAAcF,YAAA,CAAaE,WAAA;EACjC,MAAMC,QAAA,GAAWH,YAAA,CAAaG,QAAA;EAC9B,MAAMC,aAAA,GAAgBD,QAAA,CAASE,UAAA,CAAWD,aAAA;EAC1C,MAAME,WAAA,GAAcH,QAAA,CAASE,UAAA,CAAWC,WAAA;EACxC,MAAMC,YAAA,GAAeX,IAAA,CAAKY,GAAA,CAAIL,QAAA,CAASM,aAAA,EAAeL,aAAA,CAAcM,KAAK;EAEzE,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIL,YAAA,EAAcI,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IAC5CvC,KAAA,CAAMyC,KAAA,CAAMC,mBAAA,CAAoBV,aAAA,EAAeO,CAAC;IAChDvC,KAAA,CAAM2C,GAAA,CAAID,mBAAA,CAAoBR,WAAA,EAAaK,CAAC;IAE5CvC,KAAA,CAAMe,YAAA,CAAae,WAAW;IAE9B,MAAMc,WAAA,GAAc,IAAIpD,OAAA,CAAS;IACjC,MAAMqD,KAAA,GAAQ,IAAIrD,OAAA,CAAS;IAE3BgB,IAAA,CAAKsC,mBAAA,CAAoB9C,KAAA,CAAMyC,KAAA,EAAOzC,KAAA,CAAM2C,GAAA,EAAKE,KAAA,EAAOD,WAAW;IACnE,MAAMG,QAAA,GAAWF,KAAA,CAAMG,UAAA,CAAWJ,WAAW,IAAInC,UAAA,GAAa;IAE9D,IAAIsC,QAAA,EAAU;MACZlB,UAAA,CAAWoB,IAAA,CAAK;QACdJ,KAAA;QACAD,WAAA;QACAhC,QAAA,EAAUJ,IAAA,CAAK0C,MAAA,CAAOF,UAAA,CAAWH,KAAK;QACtCM,MAAA,EAAQvB,YAAA;QACRwB,IAAA,EAAM;QACNC,SAAA,EAAWd,CAAA;QACXe,EAAA,EAAI;QACJ,CAACC,GAAG,GAAG;MACf,CAAO;IACF;EACF;AACH;AAEA,SAASC,mBAAmB5B,YAAA,EAAcjB,MAAA,EAAQkB,UAAA,EAAY;EAC5D,MAAMb,gBAAA,GAAmBL,MAAA,CAAOK,gBAAA;EAChC,MAAMyC,QAAA,GAAW7B,YAAA,CAAa6B,QAAA;EAC9B,MAAM5C,UAAA,GAAa4C,QAAA,CAAS5C,UAAA;EAC5B,MAAMiB,WAAA,GAAcF,YAAA,CAAaE,WAAA;EAEjC,MAAMC,QAAA,GAAWH,YAAA,CAAaG,QAAA;EAC9B,MAAMC,aAAA,GAAgBD,QAAA,CAASE,UAAA,CAAWD,aAAA;EAC1C,MAAME,WAAA,GAAcH,QAAA,CAASE,UAAA,CAAWC,WAAA;EACxC,MAAMC,YAAA,GAAeX,IAAA,CAAKY,GAAA,CAAIL,QAAA,CAASM,aAAA,EAAeL,aAAA,CAAcM,KAAK;EAEzE,MAAMoB,IAAA,GAAO,CAAC/C,MAAA,CAAO+C,IAAA;EAOrBlD,IAAA,CAAKmD,EAAA,CAAG,GAAG/D,SAAS;EAGpBA,SAAA,CAAUsB,CAAA,GAAI;EACdtB,SAAA,CAAUmB,YAAA,CAAaJ,MAAA,CAAOiD,kBAAkB;EAChDhE,SAAA,CAAUmB,YAAA,CAAaC,gBAAgB;EACvCpB,SAAA,CAAUqB,cAAA,CAAe,IAAIrB,SAAA,CAAUsB,CAAC;EAGxCtB,SAAA,CAAUuB,CAAA,IAAKN,UAAA,CAAWM,CAAA,GAAI;EAC9BvB,SAAA,CAAUyB,CAAA,IAAKR,UAAA,CAAWQ,CAAA,GAAI;EAC9BzB,SAAA,CAAUiE,CAAA,GAAI;EAEdhE,UAAA,CAAWiE,IAAA,CAAKlE,SAAS;EAEzBE,SAAA,CAAUiE,gBAAA,CAAiBpD,MAAA,CAAOiD,kBAAA,EAAoB9B,WAAW;EAEjE,SAASS,CAAA,GAAI,GAAGC,CAAA,GAAIL,YAAA,EAAcI,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IAC5C7C,OAAA,CAAQgD,mBAAA,CAAoBV,aAAA,EAAeO,CAAC;IAC5C5C,KAAA,CAAM+C,mBAAA,CAAoBR,WAAA,EAAaK,CAAC;IAExC7C,OAAA,CAAQwB,CAAA,GAAI;IACZvB,KAAA,CAAMuB,CAAA,GAAI;IAGVxB,OAAA,CAAQqB,YAAA,CAAajB,SAAS;IAC9BH,KAAA,CAAMoB,YAAA,CAAajB,SAAS;IAG5B,MAAMkE,kBAAA,GAAqBtE,OAAA,CAAQmE,CAAA,GAAIH,IAAA,IAAQ/D,KAAA,CAAMkE,CAAA,GAAIH,IAAA;IACzD,IAAIM,kBAAA,EAAoB;MACtB;IACD;IAGD,IAAItE,OAAA,CAAQmE,CAAA,GAAIH,IAAA,EAAM;MACpB,MAAMO,SAAA,GAAYvE,OAAA,CAAQmE,CAAA,GAAIlE,KAAA,CAAMkE,CAAA;MACpC,MAAMK,CAAA,IAAKxE,OAAA,CAAQmE,CAAA,GAAIH,IAAA,IAAQO,SAAA;MAC/BvE,OAAA,CAAQyE,IAAA,CAAKxE,KAAA,EAAOuE,CAAC;IAC3B,WAAevE,KAAA,CAAMkE,CAAA,GAAIH,IAAA,EAAM;MACzB,MAAMO,SAAA,GAAYtE,KAAA,CAAMkE,CAAA,GAAInE,OAAA,CAAQmE,CAAA;MACpC,MAAMK,CAAA,IAAKvE,KAAA,CAAMkE,CAAA,GAAIH,IAAA,IAAQO,SAAA;MAC7BtE,KAAA,CAAMwE,IAAA,CAAKzE,OAAA,EAASwE,CAAC;IACtB;IAGDxE,OAAA,CAAQqB,YAAA,CAAaC,gBAAgB;IACrCrB,KAAA,CAAMoB,YAAA,CAAaC,gBAAgB;IAGnCtB,OAAA,CAAQuB,cAAA,CAAe,IAAIvB,OAAA,CAAQwB,CAAC;IACpCvB,KAAA,CAAMsB,cAAA,CAAe,IAAItB,KAAA,CAAMuB,CAAC;IAGhCxB,OAAA,CAAQyB,CAAA,IAAKN,UAAA,CAAWM,CAAA,GAAI;IAC5BzB,OAAA,CAAQ2B,CAAA,IAAKR,UAAA,CAAWQ,CAAA,GAAI;IAE5B1B,KAAA,CAAMwB,CAAA,IAAKN,UAAA,CAAWM,CAAA,GAAI;IAC1BxB,KAAA,CAAM0B,CAAA,IAAKR,UAAA,CAAWQ,CAAA,GAAI;IAG1BrB,KAAA,CAAMyC,KAAA,CAAMqB,IAAA,CAAKpE,OAAO;IACxBM,KAAA,CAAMyC,KAAA,CAAMoB,CAAA,GAAI;IAEhB7D,KAAA,CAAM2C,GAAA,CAAImB,IAAA,CAAKnE,KAAK;IACpBK,KAAA,CAAM2C,GAAA,CAAIkB,CAAA,GAAI;IAGd,MAAMO,KAAA,GAAQpE,KAAA,CAAMqE,4BAAA,CAA6BxE,UAAA,EAAY,IAAI;IACjEG,KAAA,CAAM2D,EAAA,CAAGS,KAAA,EAAOlE,aAAa;IAG7B,MAAMoE,IAAA,GAAOC,SAAA,CAAUJ,IAAA,CAAKzE,OAAA,CAAQmE,CAAA,EAAGlE,KAAA,CAAMkE,CAAA,EAAGO,KAAK;IACrD,MAAMI,aAAA,GAAgBF,IAAA,IAAQ,MAAMA,IAAA,IAAQ;IAE5C,MAAMvB,QAAA,GAAWlD,UAAA,CAAWmD,UAAA,CAAW9C,aAAa,IAAIO,UAAA,GAAa;IAErE,IAAI+D,aAAA,IAAiBzB,QAAA,EAAU;MAC7B/C,KAAA,CAAMyC,KAAA,CAAMC,mBAAA,CAAoBV,aAAA,EAAeO,CAAC;MAChDvC,KAAA,CAAM2C,GAAA,CAAID,mBAAA,CAAoBR,WAAA,EAAaK,CAAC;MAE5CvC,KAAA,CAAMyC,KAAA,CAAM1B,YAAA,CAAae,WAAW;MACpC9B,KAAA,CAAM2C,GAAA,CAAI5B,YAAA,CAAae,WAAW;MAElC,MAAMc,WAAA,GAAc,IAAIpD,OAAA,CAAS;MACjC,MAAMqD,KAAA,GAAQ,IAAIrD,OAAA,CAAS;MAE3BgB,IAAA,CAAKsC,mBAAA,CAAoB9C,KAAA,CAAMyC,KAAA,EAAOzC,KAAA,CAAM2C,GAAA,EAAKE,KAAA,EAAOD,WAAW;MAEnEf,UAAA,CAAWoB,IAAA,CAAK;QACdJ,KAAA;QACAD,WAAA;QACAhC,QAAA,EAAUJ,IAAA,CAAK0C,MAAA,CAAOF,UAAA,CAAWH,KAAK;QACtCM,MAAA,EAAQvB,YAAA;QACRwB,IAAA,EAAM;QACNC,SAAA,EAAWd,CAAA;QACXe,EAAA,EAAI;QACJ,CAACC,GAAG,GAAG;MACf,CAAO;IACF;EACF;AACH;AAEA,MAAMkB,aAAA,SAAsBC,IAAA,CAAK;EAC/BC,YAAY5C,QAAA,GAAW,IAAI6C,oBAAA,CAAsB,GAAEnB,QAAA,GAAW,IAAIoB,YAAA,CAAa;IAAEC,KAAA,EAAOtD,IAAA,CAAKuD,MAAA,KAAW;EAAU,IAAG;IACnH,MAAMhD,QAAA,EAAU0B,QAAQ;IAExB,KAAKuB,eAAA,GAAkB;IAEvB,KAAKC,IAAA,GAAO;EACb;EAAA;EAIDC,qBAAA,EAAuB;IACrB,MAAMnD,QAAA,GAAW,KAAKA,QAAA;IAEtB,MAAMC,aAAA,GAAgBD,QAAA,CAASE,UAAA,CAAWD,aAAA;IAC1C,MAAME,WAAA,GAAcH,QAAA,CAASE,UAAA,CAAWC,WAAA;IACxC,MAAMiD,aAAA,GAAgB,IAAIC,YAAA,CAAa,IAAIpD,aAAA,CAAcM,KAAK;IAE9D,SAASC,CAAA,GAAI,GAAG8C,CAAA,GAAI,GAAG7C,CAAA,GAAIR,aAAA,CAAcM,KAAA,EAAOC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK8C,CAAA,IAAK,GAAG;MAClE9F,MAAA,CAAOmD,mBAAA,CAAoBV,aAAA,EAAeO,CAAC;MAC3C9C,IAAA,CAAKiD,mBAAA,CAAoBR,WAAA,EAAaK,CAAC;MAEvC4C,aAAA,CAAcE,CAAC,IAAIA,CAAA,KAAM,IAAI,IAAIF,aAAA,CAAcE,CAAA,GAAI,CAAC;MACpDF,aAAA,CAAcE,CAAA,GAAI,CAAC,IAAIF,aAAA,CAAcE,CAAC,IAAI9F,MAAA,CAAOyD,UAAA,CAAWvD,IAAI;IACjE;IAED,MAAM6F,sBAAA,GAAyB,IAAIC,0BAAA,CAA2BJ,aAAA,EAAe,GAAG,CAAC;IAEjFpD,QAAA,CAASyD,YAAA,CAAa,yBAAyB,IAAIC,0BAAA,CAA2BH,sBAAA,EAAwB,GAAG,CAAC,CAAC;IAC3GvD,QAAA,CAASyD,YAAA,CAAa,uBAAuB,IAAIC,0BAAA,CAA2BH,sBAAA,EAAwB,GAAG,CAAC,CAAC;IAEzG,OAAO;EACR;EAEDI,QAAQC,SAAA,EAAW9D,UAAA,EAAY;IAC7B,MAAM+D,UAAA,GAAa,KAAKnC,QAAA,CAASmC,UAAA;IACjC,MAAMjF,MAAA,GAASgF,SAAA,CAAUhF,MAAA;IAEzB,IAAIA,MAAA,KAAW,QAAQ,CAACiF,UAAA,EAAY;MAClCC,OAAA,CAAQC,KAAA,CACN,+HACD;IACF;IAED,MAAMC,SAAA,GAAYJ,SAAA,CAAUK,MAAA,CAAOC,KAAA,KAAU,SAAYN,SAAA,CAAUK,MAAA,CAAOC,KAAA,CAAMF,SAAA,IAAa,IAAI;IAEjGvF,IAAA,GAAOmF,SAAA,CAAUO,GAAA;IAEjB,MAAMpE,WAAA,GAAc,KAAKA,WAAA;IACzB,MAAMC,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAM0B,QAAA,GAAW,KAAKA,QAAA;IAEtBhD,UAAA,GAAagD,QAAA,CAAS0C,SAAA,GAAYJ,SAAA;IAGlC,IAAIhE,QAAA,CAASqE,cAAA,KAAmB,MAAM;MACpCrE,QAAA,CAASsE,qBAAA,CAAuB;IACjC;IAEDhG,OAAA,CAAQyD,IAAA,CAAK/B,QAAA,CAASqE,cAAc,EAAErF,YAAA,CAAae,WAAW;IAG9D,IAAIwE,YAAA;IACJ,IAAIV,UAAA,EAAY;MACdU,YAAA,GAAe7F,UAAA,GAAa;IAClC,OAAW;MACL,MAAM8F,gBAAA,GAAmB/E,IAAA,CAAKE,GAAA,CAAIf,MAAA,CAAO+C,IAAA,EAAMrD,OAAA,CAAQmG,eAAA,CAAgBhG,IAAA,CAAK0C,MAAM,CAAC;MACnFoD,YAAA,GAAe5F,sBAAA,CAAuBC,MAAA,EAAQ4F,gBAAA,EAAkB9C,QAAA,CAAS5C,UAAU;IACpF;IAEDR,OAAA,CAAQoG,MAAA,IAAUH,YAAA;IAElB,IAAI9F,IAAA,CAAKkG,gBAAA,CAAiBrG,OAAO,MAAM,OAAO;MAC5C;IACD;IAGD,IAAI0B,QAAA,CAAS4E,WAAA,KAAgB,MAAM;MACjC5E,QAAA,CAAS6E,kBAAA,CAAoB;IAC9B;IAEDzG,IAAA,CAAK2D,IAAA,CAAK/B,QAAA,CAAS4E,WAAW,EAAE5F,YAAA,CAAae,WAAW;IAGxD,IAAI+E,SAAA;IACJ,IAAIjB,UAAA,EAAY;MACdiB,SAAA,GAAYpG,UAAA,GAAa;IAC/B,OAAW;MACL,MAAMqG,aAAA,GAAgBtF,IAAA,CAAKE,GAAA,CAAIf,MAAA,CAAO+C,IAAA,EAAMvD,IAAA,CAAKqG,eAAA,CAAgBhG,IAAA,CAAK0C,MAAM,CAAC;MAC7E2D,SAAA,GAAYnG,sBAAA,CAAuBC,MAAA,EAAQmG,aAAA,EAAerD,QAAA,CAAS5C,UAAU;IAC9E;IAEDV,IAAA,CAAK4G,cAAA,CAAeF,SAAS;IAE7B,IAAIrG,IAAA,CAAKwG,aAAA,CAAc7G,IAAI,MAAM,OAAO;MACtC;IACD;IAED,IAAIyF,UAAA,EAAY;MACdjE,iBAAA,CAAkB,MAAME,UAAU;IACxC,OAAW;MACL2B,kBAAA,CAAmB,MAAM7C,MAAA,EAAQkB,UAAU;IAC5C;EACF;EAEDoF,eAAeC,QAAA,EAAU;IACvB,MAAMC,QAAA,GAAW,KAAK1D,QAAA,CAAS0D,QAAA;IAE/B,IAAIA,QAAA,IAAYA,QAAA,CAAStG,UAAA,EAAY;MACnCqG,QAAA,CAASE,WAAA,CAAY/H,SAAS;MAC9B,KAAKoE,QAAA,CAAS0D,QAAA,CAAStG,UAAA,CAAWwG,KAAA,CAAMvG,GAAA,CAAIzB,SAAA,CAAUwE,CAAA,EAAGxE,SAAA,CAAU6B,CAAC;IACrE;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}