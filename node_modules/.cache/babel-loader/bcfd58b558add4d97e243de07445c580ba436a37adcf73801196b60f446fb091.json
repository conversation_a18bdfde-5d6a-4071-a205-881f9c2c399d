{"ast": null, "code": "import { Group, LightProbe, DirectionalLight, WebGLCubeRenderTarget } from \"three\";\nclass SessionLightProbe {\n  constructor(xrLight, renderer, lightProbe, environmentEstimation, estimationStartCallback) {\n    this.xrLight = xrLight;\n    this.renderer = renderer;\n    this.lightProbe = lightProbe;\n    this.xrWebGLBinding = null;\n    this.estimationStartCallback = estimationStartCallback;\n    this.frameCallback = this.onXRFrame.bind(this);\n    const session = renderer.xr.getSession();\n    if (environmentEstimation && \"XRWebGLBinding\" in window) {\n      const cubeRenderTarget = new WebGLCubeRenderTarget(16);\n      xrLight.environment = cubeRenderTarget.texture;\n      const gl = renderer.getContext();\n      switch (session.preferredReflectionFormat) {\n        case \"srgba8\":\n          gl.getExtension(\"EXT_sRGB\");\n          break;\n        case \"rgba16f\":\n          gl.getExtension(\"OES_texture_half_float\");\n          break;\n      }\n      this.xrWebGLBinding = new XRWebGLBinding(session, gl);\n      this.lightProbe.addEventListener(\"reflectionchange\", () => {\n        this.updateReflection();\n      });\n    }\n    session.requestAnimationFrame(this.frameCallback);\n  }\n  updateReflection() {\n    const textureProperties = this.renderer.properties.get(this.xrLight.environment);\n    if (textureProperties) {\n      const cubeMap = this.xrWebGLBinding.getReflectionCubeMap(this.lightProbe);\n      if (cubeMap) {\n        textureProperties.__webglTexture = cubeMap;\n        this.xrLight.environment.needsPMREMUpdate = true;\n      }\n    }\n  }\n  onXRFrame(time, xrFrame) {\n    if (!this.xrLight) {\n      return;\n    }\n    const session = xrFrame.session;\n    session.requestAnimationFrame(this.frameCallback);\n    const lightEstimate = xrFrame.getLightEstimate(this.lightProbe);\n    if (lightEstimate) {\n      this.xrLight.lightProbe.sh.fromArray(lightEstimate.sphericalHarmonicsCoefficients);\n      this.xrLight.lightProbe.intensity = 1;\n      const intensityScalar = Math.max(1, Math.max(lightEstimate.primaryLightIntensity.x, Math.max(lightEstimate.primaryLightIntensity.y, lightEstimate.primaryLightIntensity.z)));\n      this.xrLight.directionalLight.color.setRGB(lightEstimate.primaryLightIntensity.x / intensityScalar, lightEstimate.primaryLightIntensity.y / intensityScalar, lightEstimate.primaryLightIntensity.z / intensityScalar);\n      this.xrLight.directionalLight.intensity = intensityScalar;\n      this.xrLight.directionalLight.position.copy(lightEstimate.primaryLightDirection);\n      if (this.estimationStartCallback) {\n        this.estimationStartCallback();\n        this.estimationStartCallback = null;\n      }\n    }\n  }\n  dispose() {\n    this.xrLight = null;\n    this.renderer = null;\n    this.lightProbe = null;\n    this.xrWebGLBinding = null;\n  }\n}\nclass XREstimatedLight extends Group {\n  constructor(renderer, environmentEstimation = true) {\n    super();\n    this.lightProbe = new LightProbe();\n    this.lightProbe.intensity = 0;\n    this.add(this.lightProbe);\n    this.directionalLight = new DirectionalLight();\n    this.directionalLight.intensity = 0;\n    this.add(this.directionalLight);\n    this.environment = null;\n    let sessionLightProbe = null;\n    let estimationStarted = false;\n    renderer.xr.addEventListener(\"sessionstart\", () => {\n      const session = renderer.xr.getSession();\n      if (\"requestLightProbe\" in session) {\n        session.requestLightProbe({\n          reflectionFormat: session.preferredReflectionFormat\n        }).then(probe => {\n          sessionLightProbe = new SessionLightProbe(this, renderer, probe, environmentEstimation, () => {\n            estimationStarted = true;\n            this.dispatchEvent({\n              type: \"estimationstart\"\n            });\n          });\n        });\n      }\n    });\n    renderer.xr.addEventListener(\"sessionend\", () => {\n      if (sessionLightProbe) {\n        sessionLightProbe.dispose();\n        sessionLightProbe = null;\n      }\n      if (estimationStarted) {\n        this.dispatchEvent({\n          type: \"estimationend\"\n        });\n      }\n    });\n    this.dispose = () => {\n      if (sessionLightProbe) {\n        sessionLightProbe.dispose();\n        sessionLightProbe = null;\n      }\n      this.remove(this.lightProbe);\n      this.lightProbe = null;\n      this.remove(this.directionalLight);\n      this.directionalLight = null;\n      this.environment = null;\n    };\n  }\n}\nexport { XREstimatedLight };", "map": {"version": 3, "names": ["SessionLightProbe", "constructor", "xrLight", "renderer", "lightProbe", "environmentEstimation", "estimationStartCallback", "xrWebGLBinding", "frameCallback", "onXRFrame", "bind", "session", "xr", "getSession", "window", "cubeRenderTarget", "WebGLCubeRenderTarget", "environment", "texture", "gl", "getContext", "preferredReflectionFormat", "getExtension", "XRWebGLBinding", "addEventListener", "updateReflection", "requestAnimationFrame", "textureProperties", "properties", "get", "cubeMap", "getReflectionCubeMap", "__webglTexture", "needsPMREMUpdate", "time", "xrFrame", "lightEstimate", "getLightEstimate", "sh", "fromArray", "sphericalHarmonicsCoefficients", "intensity", "intensityScalar", "Math", "max", "primaryLightIntensity", "x", "y", "z", "directionalLight", "color", "setRGB", "position", "copy", "primaryLightDirection", "dispose", "XREstimatedLight", "Group", "LightProbe", "add", "DirectionalLight", "sessionLightProbe", "estimationStarted", "requestLightProbe", "reflectionFormat", "then", "probe", "dispatchEvent", "type", "remove"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/webxr/XREstimatedLight.js"], "sourcesContent": ["import { DirectionalLight, Group, LightProbe, WebGLCubeRenderTarget } from 'three'\n\nclass SessionLightProbe {\n  constructor(xrLight, renderer, lightProbe, environmentEstimation, estimationStartCallback) {\n    this.xrLight = xrLight\n    this.renderer = renderer\n    this.lightProbe = lightProbe\n    this.xrWebGLBinding = null\n    this.estimationStartCallback = estimationStartCallback\n    this.frameCallback = this.onXRFrame.bind(this)\n\n    const session = renderer.xr.getSession()\n\n    // If the XRWebGLBinding class is available then we can also query an\n    // estimated reflection cube map.\n    if (environmentEstimation && 'XRWebGLBinding' in window) {\n      // This is the simplest way I know of to initialize a WebGL cubemap in Three.\n      const cubeRenderTarget = new WebGLCubeRenderTarget(16)\n      xrLight.environment = cubeRenderTarget.texture\n\n      const gl = renderer.getContext()\n\n      // Ensure that we have any extensions needed to use the preferred cube map format.\n      switch (session.preferredReflectionFormat) {\n        case 'srgba8':\n          gl.getExtension('EXT_sRGB')\n          break\n\n        case 'rgba16f':\n          gl.getExtension('OES_texture_half_float')\n          break\n      }\n\n      this.xrWebGLBinding = new XRWebGLBinding(session, gl)\n\n      this.lightProbe.addEventListener('reflectionchange', () => {\n        this.updateReflection()\n      })\n    }\n\n    // Start monitoring the XR animation frame loop to look for lighting\n    // estimation changes.\n    session.requestAnimationFrame(this.frameCallback)\n  }\n\n  updateReflection() {\n    const textureProperties = this.renderer.properties.get(this.xrLight.environment)\n\n    if (textureProperties) {\n      const cubeMap = this.xrWebGLBinding.getReflectionCubeMap(this.lightProbe)\n\n      if (cubeMap) {\n        textureProperties.__webglTexture = cubeMap\n\n        this.xrLight.environment.needsPMREMUpdate = true\n      }\n    }\n  }\n\n  onXRFrame(time, xrFrame) {\n    // If either this obejct or the XREstimatedLight has been destroyed, stop\n    // running the frame loop.\n    if (!this.xrLight) {\n      return\n    }\n\n    const session = xrFrame.session\n    session.requestAnimationFrame(this.frameCallback)\n\n    const lightEstimate = xrFrame.getLightEstimate(this.lightProbe)\n    if (lightEstimate) {\n      // We can copy the estimate's spherical harmonics array directly into the light probe.\n      this.xrLight.lightProbe.sh.fromArray(lightEstimate.sphericalHarmonicsCoefficients)\n      this.xrLight.lightProbe.intensity = 1.0\n\n      // For the directional light we have to normalize the color and set the scalar as the\n      // intensity, since WebXR can return color values that exceed 1.0.\n      const intensityScalar = Math.max(\n        1.0,\n        Math.max(\n          lightEstimate.primaryLightIntensity.x,\n          Math.max(lightEstimate.primaryLightIntensity.y, lightEstimate.primaryLightIntensity.z),\n        ),\n      )\n\n      this.xrLight.directionalLight.color.setRGB(\n        lightEstimate.primaryLightIntensity.x / intensityScalar,\n        lightEstimate.primaryLightIntensity.y / intensityScalar,\n        lightEstimate.primaryLightIntensity.z / intensityScalar,\n      )\n      this.xrLight.directionalLight.intensity = intensityScalar\n      this.xrLight.directionalLight.position.copy(lightEstimate.primaryLightDirection)\n\n      if (this.estimationStartCallback) {\n        this.estimationStartCallback()\n        this.estimationStartCallback = null\n      }\n    }\n  }\n\n  dispose() {\n    this.xrLight = null\n    this.renderer = null\n    this.lightProbe = null\n    this.xrWebGLBinding = null\n  }\n}\n\nexport class XREstimatedLight extends Group {\n  constructor(renderer, environmentEstimation = true) {\n    super()\n\n    this.lightProbe = new LightProbe()\n    this.lightProbe.intensity = 0\n    this.add(this.lightProbe)\n\n    this.directionalLight = new DirectionalLight()\n    this.directionalLight.intensity = 0\n    this.add(this.directionalLight)\n\n    // Will be set to a cube map in the SessionLightProbe is environment estimation is\n    // available and requested.\n    this.environment = null\n\n    let sessionLightProbe = null\n    let estimationStarted = false\n    renderer.xr.addEventListener('sessionstart', () => {\n      const session = renderer.xr.getSession()\n\n      if ('requestLightProbe' in session) {\n        session\n          .requestLightProbe({\n            reflectionFormat: session.preferredReflectionFormat,\n          })\n          .then((probe) => {\n            sessionLightProbe = new SessionLightProbe(this, renderer, probe, environmentEstimation, () => {\n              estimationStarted = true\n\n              // Fired to indicate that the estimated lighting values are now being updated.\n              this.dispatchEvent({ type: 'estimationstart' })\n            })\n          })\n      }\n    })\n\n    renderer.xr.addEventListener('sessionend', () => {\n      if (sessionLightProbe) {\n        sessionLightProbe.dispose()\n        sessionLightProbe = null\n      }\n\n      if (estimationStarted) {\n        // Fired to indicate that the estimated lighting values are no longer being updated.\n        this.dispatchEvent({ type: 'estimationend' })\n      }\n    })\n\n    // Done inline to provide access to sessionLightProbe.\n    this.dispose = () => {\n      if (sessionLightProbe) {\n        sessionLightProbe.dispose()\n        sessionLightProbe = null\n      }\n\n      this.remove(this.lightProbe)\n      this.lightProbe = null\n\n      this.remove(this.directionalLight)\n      this.directionalLight = null\n\n      this.environment = null\n    }\n  }\n}\n"], "mappings": ";AAEA,MAAMA,iBAAA,CAAkB;EACtBC,YAAYC,OAAA,EAASC,QAAA,EAAUC,UAAA,EAAYC,qBAAA,EAAuBC,uBAAA,EAAyB;IACzF,KAAKJ,OAAA,GAAUA,OAAA;IACf,KAAKC,QAAA,GAAWA,QAAA;IAChB,KAAKC,UAAA,GAAaA,UAAA;IAClB,KAAKG,cAAA,GAAiB;IACtB,KAAKD,uBAAA,GAA0BA,uBAAA;IAC/B,KAAKE,aAAA,GAAgB,KAAKC,SAAA,CAAUC,IAAA,CAAK,IAAI;IAE7C,MAAMC,OAAA,GAAUR,QAAA,CAASS,EAAA,CAAGC,UAAA,CAAY;IAIxC,IAAIR,qBAAA,IAAyB,oBAAoBS,MAAA,EAAQ;MAEvD,MAAMC,gBAAA,GAAmB,IAAIC,qBAAA,CAAsB,EAAE;MACrDd,OAAA,CAAQe,WAAA,GAAcF,gBAAA,CAAiBG,OAAA;MAEvC,MAAMC,EAAA,GAAKhB,QAAA,CAASiB,UAAA,CAAY;MAGhC,QAAQT,OAAA,CAAQU,yBAAA;QACd,KAAK;UACHF,EAAA,CAAGG,YAAA,CAAa,UAAU;UAC1B;QAEF,KAAK;UACHH,EAAA,CAAGG,YAAA,CAAa,wBAAwB;UACxC;MACH;MAED,KAAKf,cAAA,GAAiB,IAAIgB,cAAA,CAAeZ,OAAA,EAASQ,EAAE;MAEpD,KAAKf,UAAA,CAAWoB,gBAAA,CAAiB,oBAAoB,MAAM;QACzD,KAAKC,gBAAA,CAAkB;MAC/B,CAAO;IACF;IAIDd,OAAA,CAAQe,qBAAA,CAAsB,KAAKlB,aAAa;EACjD;EAEDiB,iBAAA,EAAmB;IACjB,MAAME,iBAAA,GAAoB,KAAKxB,QAAA,CAASyB,UAAA,CAAWC,GAAA,CAAI,KAAK3B,OAAA,CAAQe,WAAW;IAE/E,IAAIU,iBAAA,EAAmB;MACrB,MAAMG,OAAA,GAAU,KAAKvB,cAAA,CAAewB,oBAAA,CAAqB,KAAK3B,UAAU;MAExE,IAAI0B,OAAA,EAAS;QACXH,iBAAA,CAAkBK,cAAA,GAAiBF,OAAA;QAEnC,KAAK5B,OAAA,CAAQe,WAAA,CAAYgB,gBAAA,GAAmB;MAC7C;IACF;EACF;EAEDxB,UAAUyB,IAAA,EAAMC,OAAA,EAAS;IAGvB,IAAI,CAAC,KAAKjC,OAAA,EAAS;MACjB;IACD;IAED,MAAMS,OAAA,GAAUwB,OAAA,CAAQxB,OAAA;IACxBA,OAAA,CAAQe,qBAAA,CAAsB,KAAKlB,aAAa;IAEhD,MAAM4B,aAAA,GAAgBD,OAAA,CAAQE,gBAAA,CAAiB,KAAKjC,UAAU;IAC9D,IAAIgC,aAAA,EAAe;MAEjB,KAAKlC,OAAA,CAAQE,UAAA,CAAWkC,EAAA,CAAGC,SAAA,CAAUH,aAAA,CAAcI,8BAA8B;MACjF,KAAKtC,OAAA,CAAQE,UAAA,CAAWqC,SAAA,GAAY;MAIpC,MAAMC,eAAA,GAAkBC,IAAA,CAAKC,GAAA,CAC3B,GACAD,IAAA,CAAKC,GAAA,CACHR,aAAA,CAAcS,qBAAA,CAAsBC,CAAA,EACpCH,IAAA,CAAKC,GAAA,CAAIR,aAAA,CAAcS,qBAAA,CAAsBE,CAAA,EAAGX,aAAA,CAAcS,qBAAA,CAAsBG,CAAC,CACtF,CACF;MAED,KAAK9C,OAAA,CAAQ+C,gBAAA,CAAiBC,KAAA,CAAMC,MAAA,CAClCf,aAAA,CAAcS,qBAAA,CAAsBC,CAAA,GAAIJ,eAAA,EACxCN,aAAA,CAAcS,qBAAA,CAAsBE,CAAA,GAAIL,eAAA,EACxCN,aAAA,CAAcS,qBAAA,CAAsBG,CAAA,GAAIN,eACzC;MACD,KAAKxC,OAAA,CAAQ+C,gBAAA,CAAiBR,SAAA,GAAYC,eAAA;MAC1C,KAAKxC,OAAA,CAAQ+C,gBAAA,CAAiBG,QAAA,CAASC,IAAA,CAAKjB,aAAA,CAAckB,qBAAqB;MAE/E,IAAI,KAAKhD,uBAAA,EAAyB;QAChC,KAAKA,uBAAA,CAAyB;QAC9B,KAAKA,uBAAA,GAA0B;MAChC;IACF;EACF;EAEDiD,QAAA,EAAU;IACR,KAAKrD,OAAA,GAAU;IACf,KAAKC,QAAA,GAAW;IAChB,KAAKC,UAAA,GAAa;IAClB,KAAKG,cAAA,GAAiB;EACvB;AACH;AAEO,MAAMiD,gBAAA,SAAyBC,KAAA,CAAM;EAC1CxD,YAAYE,QAAA,EAAUE,qBAAA,GAAwB,MAAM;IAClD,MAAO;IAEP,KAAKD,UAAA,GAAa,IAAIsD,UAAA,CAAY;IAClC,KAAKtD,UAAA,CAAWqC,SAAA,GAAY;IAC5B,KAAKkB,GAAA,CAAI,KAAKvD,UAAU;IAExB,KAAK6C,gBAAA,GAAmB,IAAIW,gBAAA,CAAkB;IAC9C,KAAKX,gBAAA,CAAiBR,SAAA,GAAY;IAClC,KAAKkB,GAAA,CAAI,KAAKV,gBAAgB;IAI9B,KAAKhC,WAAA,GAAc;IAEnB,IAAI4C,iBAAA,GAAoB;IACxB,IAAIC,iBAAA,GAAoB;IACxB3D,QAAA,CAASS,EAAA,CAAGY,gBAAA,CAAiB,gBAAgB,MAAM;MACjD,MAAMb,OAAA,GAAUR,QAAA,CAASS,EAAA,CAAGC,UAAA,CAAY;MAExC,IAAI,uBAAuBF,OAAA,EAAS;QAClCA,OAAA,CACGoD,iBAAA,CAAkB;UACjBC,gBAAA,EAAkBrD,OAAA,CAAQU;QACtC,CAAW,EACA4C,IAAA,CAAMC,KAAA,IAAU;UACfL,iBAAA,GAAoB,IAAI7D,iBAAA,CAAkB,MAAMG,QAAA,EAAU+D,KAAA,EAAO7D,qBAAA,EAAuB,MAAM;YAC5FyD,iBAAA,GAAoB;YAGpB,KAAKK,aAAA,CAAc;cAAEC,IAAA,EAAM;YAAiB,CAAE;UAC5D,CAAa;QACb,CAAW;MACJ;IACP,CAAK;IAEDjE,QAAA,CAASS,EAAA,CAAGY,gBAAA,CAAiB,cAAc,MAAM;MAC/C,IAAIqC,iBAAA,EAAmB;QACrBA,iBAAA,CAAkBN,OAAA,CAAS;QAC3BM,iBAAA,GAAoB;MACrB;MAED,IAAIC,iBAAA,EAAmB;QAErB,KAAKK,aAAA,CAAc;UAAEC,IAAA,EAAM;QAAe,CAAE;MAC7C;IACP,CAAK;IAGD,KAAKb,OAAA,GAAU,MAAM;MACnB,IAAIM,iBAAA,EAAmB;QACrBA,iBAAA,CAAkBN,OAAA,CAAS;QAC3BM,iBAAA,GAAoB;MACrB;MAED,KAAKQ,MAAA,CAAO,KAAKjE,UAAU;MAC3B,KAAKA,UAAA,GAAa;MAElB,KAAKiE,MAAA,CAAO,KAAKpB,gBAAgB;MACjC,KAAKA,gBAAA,GAAmB;MAExB,KAAKhC,WAAA,GAAc;IACpB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}