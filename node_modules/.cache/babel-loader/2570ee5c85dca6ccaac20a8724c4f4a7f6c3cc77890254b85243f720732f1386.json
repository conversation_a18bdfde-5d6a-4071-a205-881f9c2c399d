{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CylinderGeometry, Matrix4, WebGLRenderTarget, RGBAFormat, ShaderMaterial, DoubleSide, RepeatWrapping } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { SpotLightMaterial } from '../materials/SpotLightMaterial.js';\nimport SpotlightShadowShader from '../helpers/glsl/DefaultSpotlightShadowShadows.glsl.js';\nconst isSpotLight = child => {\n  return child == null ? void 0 : child.isSpotLight;\n};\nfunction VolumetricMesh({\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5\n}) {\n  const mesh = React.useRef(null);\n  const size = useThree(state => state.size);\n  const camera = useThree(state => state.camera);\n  const dpr = useThree(state => state.viewport.dpr);\n  const [material] = React.useState(() => new SpotLightMaterial());\n  const [vec] = React.useState(() => new Vector3());\n  radiusTop = radiusTop === undefined ? 0.1 : radiusTop;\n  radiusBottom = radiusBottom === undefined ? angle * 7 : radiusBottom;\n  useFrame(() => {\n    material.uniforms.spotPosition.value.copy(mesh.current.getWorldPosition(vec));\n    mesh.current.lookAt(mesh.current.parent.target.getWorldPosition(vec));\n  });\n  const geom = React.useMemo(() => {\n    const geometry = new CylinderGeometry(radiusTop, radiusBottom, distance, 128, 64, true);\n    geometry.applyMatrix4(new Matrix4().makeTranslation(0, -distance / 2, 0));\n    geometry.applyMatrix4(new Matrix4().makeRotationX(-Math.PI / 2));\n    return geometry;\n  }, [distance, radiusTop, radiusBottom]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    geometry: geom,\n    raycast: () => null\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: material,\n    attach: \"material\",\n    \"uniforms-opacity-value\": opacity,\n    \"uniforms-lightColor-value\": color,\n    \"uniforms-attenuation-value\": attenuation,\n    \"uniforms-anglePower-value\": anglePower,\n    \"uniforms-depth-value\": depthBuffer,\n    \"uniforms-cameraNear-value\": camera.near,\n    \"uniforms-cameraFar-value\": camera.far,\n    \"uniforms-resolution-value\": depthBuffer ? [size.width * dpr, size.height * dpr] : [0, 0]\n  })));\n}\nfunction useCommon(spotlight, mesh, width, height, distance) {\n  const [[pos, dir]] = React.useState(() => [new Vector3(), new Vector3()]);\n  React.useLayoutEffect(() => {\n    if (isSpotLight(spotlight.current)) {\n      spotlight.current.shadow.mapSize.set(width, height);\n      spotlight.current.shadow.needsUpdate = true;\n    } else {\n      throw new Error('SpotlightShadow must be a child of a SpotLight');\n    }\n  }, [spotlight, width, height]);\n  useFrame(() => {\n    if (!spotlight.current) return;\n    const A = spotlight.current.position;\n    const B = spotlight.current.target.position;\n    dir.copy(B).sub(A);\n    var len = dir.length();\n    dir.normalize().multiplyScalar(len * distance);\n    pos.copy(A).add(dir);\n    mesh.current.position.copy(pos);\n    mesh.current.lookAt(spotlight.current.target.position);\n  });\n}\nfunction SpotlightShadowWithShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  shader = SpotlightShadowShader,\n  width = 512,\n  height = 512,\n  scale = 1,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  const renderTarget = React.useMemo(() => new WebGLRenderTarget(width, height, {\n    format: RGBAFormat,\n    stencilBuffer: false\n    // depthTexture: null!\n  }), [width, height]);\n  const uniforms = React.useRef({\n    uShadowMap: {\n      value: map\n    },\n    uTime: {\n      value: 0\n    }\n  });\n  React.useEffect(() => void (uniforms.current.uShadowMap.value = map), [map]);\n  const fsQuad = React.useMemo(() => new FullScreenQuad(new ShaderMaterial({\n    uniforms: uniforms.current,\n    vertexShader: /* glsl */`\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          `,\n    fragmentShader: shader\n  })), [shader]);\n  React.useEffect(() => () => {\n    fsQuad.material.dispose();\n    fsQuad.dispose();\n  }, [fsQuad]);\n  React.useEffect(() => () => renderTarget.dispose(), [renderTarget]);\n  useFrame(({\n    gl\n  }, dt) => {\n    uniforms.current.uTime.value += dt;\n    gl.setRenderTarget(renderTarget);\n    fsQuad.render(gl);\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: renderTarget.texture,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotlightShadowWithoutShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  width = 512,\n  height = 512,\n  scale,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: map,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotLightShadow(props) {\n  if (props.shader) return /*#__PURE__*/React.createElement(SpotlightShadowWithShader, props);\n  return /*#__PURE__*/React.createElement(SpotlightShadowWithoutShader, props);\n}\nconst SpotLight = /*#__PURE__*/React.forwardRef(({\n  // Volumetric\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5,\n  volumetric = true,\n  debug = false,\n  children,\n  ...props\n}, ref) => {\n  const spotlight = React.useRef(null);\n  React.useImperativeHandle(ref, () => spotlight.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", null, debug && spotlight.current && /*#__PURE__*/React.createElement(\"spotLightHelper\", {\n    args: [spotlight.current]\n  }), /*#__PURE__*/React.createElement(\"spotLight\", _extends({\n    ref: spotlight,\n    angle: angle,\n    color: color,\n    distance: distance,\n    castShadow: true\n  }, props), volumetric && /*#__PURE__*/React.createElement(VolumetricMesh, {\n    debug: debug,\n    opacity: opacity,\n    radiusTop: radiusTop,\n    radiusBottom: radiusBottom,\n    depthBuffer: depthBuffer,\n    color: color,\n    distance: distance,\n    angle: angle,\n    attenuation: attenuation,\n    anglePower: anglePower\n  })), children && /*#__PURE__*/React.cloneElement(children, {\n    spotlightRef: spotlight,\n    debug: debug\n  }));\n});\nexport { SpotLight, SpotLightShadow };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "CylinderGeometry", "Matrix4", "WebGLRenderTarget", "RGBAFormat", "ShaderMaterial", "DoubleSide", "RepeatWrapping", "useThree", "useFrame", "FullScreenQuad", "SpotLightMaterial", "SpotlightShadowShader", "isSpotLight", "child", "VolumetricMesh", "opacity", "radiusTop", "radiusBottom", "depthBuffer", "color", "distance", "angle", "attenuation", "anglePower", "mesh", "useRef", "size", "state", "camera", "dpr", "viewport", "material", "useState", "vec", "undefined", "uniforms", "spotPosition", "value", "copy", "current", "getWorldPosition", "lookAt", "parent", "target", "geom", "useMemo", "geometry", "applyMatrix4", "makeTranslation", "makeRotationX", "Math", "PI", "createElement", "Fragment", "ref", "raycast", "object", "attach", "near", "far", "width", "height", "useCommon", "spotlight", "pos", "dir", "useLayoutEffect", "shadow", "mapSize", "set", "needsUpdate", "Error", "A", "position", "B", "sub", "len", "length", "normalize", "multiplyScalar", "add", "SpotlightShadowWithShader", "alphaTest", "map", "shader", "scale", "children", "rest", "spotlightRef", "debug", "renderTarget", "format", "stencil<PERSON>uffer", "uShadowMap", "uTime", "useEffect", "fsQuad", "vertexShader", "fragmentShader", "dispose", "gl", "dt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "<PERSON><PERSON><PERSON><PERSON>", "transparent", "side", "alphaMap", "texture", "SpotlightShadowWithoutShader", "SpotLightShadow", "props", "SpotLight", "forwardRef", "volumetric", "useImperativeHandle", "args", "cloneElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/SpotLight.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CylinderGeometry, Matrix4, WebGLRenderTarget, RGBAFormat, ShaderMaterial, DoubleSide, RepeatWrapping } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { SpotLightMaterial } from '../materials/SpotLightMaterial.js';\nimport SpotlightShadowShader from '../helpers/glsl/DefaultSpotlightShadowShadows.glsl.js';\n\nconst isSpotLight = child => {\n  return child == null ? void 0 : child.isSpotLight;\n};\nfunction VolumetricMesh({\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5\n}) {\n  const mesh = React.useRef(null);\n  const size = useThree(state => state.size);\n  const camera = useThree(state => state.camera);\n  const dpr = useThree(state => state.viewport.dpr);\n  const [material] = React.useState(() => new SpotLightMaterial());\n  const [vec] = React.useState(() => new Vector3());\n  radiusTop = radiusTop === undefined ? 0.1 : radiusTop;\n  radiusBottom = radiusBottom === undefined ? angle * 7 : radiusBottom;\n  useFrame(() => {\n    material.uniforms.spotPosition.value.copy(mesh.current.getWorldPosition(vec));\n    mesh.current.lookAt(mesh.current.parent.target.getWorldPosition(vec));\n  });\n  const geom = React.useMemo(() => {\n    const geometry = new CylinderGeometry(radiusTop, radiusBottom, distance, 128, 64, true);\n    geometry.applyMatrix4(new Matrix4().makeTranslation(0, -distance / 2, 0));\n    geometry.applyMatrix4(new Matrix4().makeRotationX(-Math.PI / 2));\n    return geometry;\n  }, [distance, radiusTop, radiusBottom]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    geometry: geom,\n    raycast: () => null\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: material,\n    attach: \"material\",\n    \"uniforms-opacity-value\": opacity,\n    \"uniforms-lightColor-value\": color,\n    \"uniforms-attenuation-value\": attenuation,\n    \"uniforms-anglePower-value\": anglePower,\n    \"uniforms-depth-value\": depthBuffer,\n    \"uniforms-cameraNear-value\": camera.near,\n    \"uniforms-cameraFar-value\": camera.far,\n    \"uniforms-resolution-value\": depthBuffer ? [size.width * dpr, size.height * dpr] : [0, 0]\n  })));\n}\nfunction useCommon(spotlight, mesh, width, height, distance) {\n  const [[pos, dir]] = React.useState(() => [new Vector3(), new Vector3()]);\n  React.useLayoutEffect(() => {\n    if (isSpotLight(spotlight.current)) {\n      spotlight.current.shadow.mapSize.set(width, height);\n      spotlight.current.shadow.needsUpdate = true;\n    } else {\n      throw new Error('SpotlightShadow must be a child of a SpotLight');\n    }\n  }, [spotlight, width, height]);\n  useFrame(() => {\n    if (!spotlight.current) return;\n    const A = spotlight.current.position;\n    const B = spotlight.current.target.position;\n    dir.copy(B).sub(A);\n    var len = dir.length();\n    dir.normalize().multiplyScalar(len * distance);\n    pos.copy(A).add(dir);\n    mesh.current.position.copy(pos);\n    mesh.current.lookAt(spotlight.current.target.position);\n  });\n}\nfunction SpotlightShadowWithShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  shader = SpotlightShadowShader,\n  width = 512,\n  height = 512,\n  scale = 1,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  const renderTarget = React.useMemo(() => new WebGLRenderTarget(width, height, {\n    format: RGBAFormat,\n    stencilBuffer: false\n    // depthTexture: null!\n  }), [width, height]);\n  const uniforms = React.useRef({\n    uShadowMap: {\n      value: map\n    },\n    uTime: {\n      value: 0\n    }\n  });\n  React.useEffect(() => void (uniforms.current.uShadowMap.value = map), [map]);\n  const fsQuad = React.useMemo(() => new FullScreenQuad(new ShaderMaterial({\n    uniforms: uniforms.current,\n    vertexShader: /* glsl */`\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          `,\n    fragmentShader: shader\n  })), [shader]);\n  React.useEffect(() => () => {\n    fsQuad.material.dispose();\n    fsQuad.dispose();\n  }, [fsQuad]);\n  React.useEffect(() => () => renderTarget.dispose(), [renderTarget]);\n  useFrame(({\n    gl\n  }, dt) => {\n    uniforms.current.uTime.value += dt;\n    gl.setRenderTarget(renderTarget);\n    fsQuad.render(gl);\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: renderTarget.texture,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotlightShadowWithoutShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  width = 512,\n  height = 512,\n  scale,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: map,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotLightShadow(props) {\n  if (props.shader) return /*#__PURE__*/React.createElement(SpotlightShadowWithShader, props);\n  return /*#__PURE__*/React.createElement(SpotlightShadowWithoutShader, props);\n}\nconst SpotLight = /*#__PURE__*/React.forwardRef(({\n  // Volumetric\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5,\n  volumetric = true,\n  debug = false,\n  children,\n  ...props\n}, ref) => {\n  const spotlight = React.useRef(null);\n  React.useImperativeHandle(ref, () => spotlight.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", null, debug && spotlight.current && /*#__PURE__*/React.createElement(\"spotLightHelper\", {\n    args: [spotlight.current]\n  }), /*#__PURE__*/React.createElement(\"spotLight\", _extends({\n    ref: spotlight,\n    angle: angle,\n    color: color,\n    distance: distance,\n    castShadow: true\n  }, props), volumetric && /*#__PURE__*/React.createElement(VolumetricMesh, {\n    debug: debug,\n    opacity: opacity,\n    radiusTop: radiusTop,\n    radiusBottom: radiusBottom,\n    depthBuffer: depthBuffer,\n    color: color,\n    distance: distance,\n    angle: angle,\n    attenuation: attenuation,\n    anglePower: anglePower\n  })), children && /*#__PURE__*/React.cloneElement(children, {\n    spotlightRef: spotlight,\n    debug: debug\n  }));\n});\n\nexport { SpotLight, SpotLightShadow };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,UAAU,EAAEC,cAAc,QAAQ,OAAO;AACrI,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,qBAAqB,MAAM,uDAAuD;AAEzF,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,OAAOA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACD,WAAW;AACnD,CAAC;AACD,SAASE,cAAcA,CAAC;EACtBC,OAAO,GAAG,CAAC;EACXC,SAAS;EACTC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,OAAO;EACfC,QAAQ,GAAG,CAAC;EACZC,KAAK,GAAG,IAAI;EACZC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG;AACf,CAAC,EAAE;EACD,MAAMC,IAAI,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMC,IAAI,GAAGnB,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,MAAM,GAAGrB,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGtB,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACG,QAAQ,CAACD,GAAG,CAAC;EACjD,MAAM,CAACE,QAAQ,CAAC,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,MAAM,IAAItB,iBAAiB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACuB,GAAG,CAAC,GAAGnC,KAAK,CAACkC,QAAQ,CAAC,MAAM,IAAIjC,OAAO,CAAC,CAAC,CAAC;EACjDiB,SAAS,GAAGA,SAAS,KAAKkB,SAAS,GAAG,GAAG,GAAGlB,SAAS;EACrDC,YAAY,GAAGA,YAAY,KAAKiB,SAAS,GAAGb,KAAK,GAAG,CAAC,GAAGJ,YAAY;EACpET,QAAQ,CAAC,MAAM;IACbuB,QAAQ,CAACI,QAAQ,CAACC,YAAY,CAACC,KAAK,CAACC,IAAI,CAACd,IAAI,CAACe,OAAO,CAACC,gBAAgB,CAACP,GAAG,CAAC,CAAC;IAC7ET,IAAI,CAACe,OAAO,CAACE,MAAM,CAACjB,IAAI,CAACe,OAAO,CAACG,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAACP,GAAG,CAAC,CAAC;EACvE,CAAC,CAAC;EACF,MAAMW,IAAI,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,MAAM;IAC/B,MAAMC,QAAQ,GAAG,IAAI9C,gBAAgB,CAACgB,SAAS,EAAEC,YAAY,EAAEG,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC;IACvF0B,QAAQ,CAACC,YAAY,CAAC,IAAI9C,OAAO,CAAC,CAAC,CAAC+C,eAAe,CAAC,CAAC,EAAE,CAAC5B,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACzE0B,QAAQ,CAACC,YAAY,CAAC,IAAI9C,OAAO,CAAC,CAAC,CAACgD,aAAa,CAAC,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;IAChE,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAAC1B,QAAQ,EAAEJ,SAAS,EAAEC,YAAY,CAAC,CAAC;EACvC,OAAO,aAAanB,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACuD,QAAQ,EAAE,IAAI,EAAE,aAAavD,KAAK,CAACsD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACTsB,QAAQ,EAAEF,IAAI;IACdW,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAE,aAAazD,KAAK,CAACsD,aAAa,CAAC,WAAW,EAAE;IAC/CI,MAAM,EAAEzB,QAAQ;IAChB0B,MAAM,EAAE,UAAU;IAClB,wBAAwB,EAAE1C,OAAO;IACjC,2BAA2B,EAAEI,KAAK;IAClC,4BAA4B,EAAEG,WAAW;IACzC,2BAA2B,EAAEC,UAAU;IACvC,sBAAsB,EAAEL,WAAW;IACnC,2BAA2B,EAAEU,MAAM,CAAC8B,IAAI;IACxC,0BAA0B,EAAE9B,MAAM,CAAC+B,GAAG;IACtC,2BAA2B,EAAEzC,WAAW,GAAG,CAACQ,IAAI,CAACkC,KAAK,GAAG/B,GAAG,EAAEH,IAAI,CAACmC,MAAM,GAAGhC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EAC1F,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASiC,SAASA,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,EAAE;EAC3D,MAAM,CAAC,CAAC4C,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAGnE,KAAK,CAACkC,QAAQ,CAAC,MAAM,CAAC,IAAIjC,OAAO,CAAC,CAAC,EAAE,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAC;EACzED,KAAK,CAACoE,eAAe,CAAC,MAAM;IAC1B,IAAItD,WAAW,CAACmD,SAAS,CAACxB,OAAO,CAAC,EAAE;MAClCwB,SAAS,CAACxB,OAAO,CAAC4B,MAAM,CAACC,OAAO,CAACC,GAAG,CAACT,KAAK,EAAEC,MAAM,CAAC;MACnDE,SAAS,CAACxB,OAAO,CAAC4B,MAAM,CAACG,WAAW,GAAG,IAAI;IAC7C,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;EACF,CAAC,EAAE,CAACR,SAAS,EAAEH,KAAK,EAAEC,MAAM,CAAC,CAAC;EAC9BrD,QAAQ,CAAC,MAAM;IACb,IAAI,CAACuD,SAAS,CAACxB,OAAO,EAAE;IACxB,MAAMiC,CAAC,GAAGT,SAAS,CAACxB,OAAO,CAACkC,QAAQ;IACpC,MAAMC,CAAC,GAAGX,SAAS,CAACxB,OAAO,CAACI,MAAM,CAAC8B,QAAQ;IAC3CR,GAAG,CAAC3B,IAAI,CAACoC,CAAC,CAAC,CAACC,GAAG,CAACH,CAAC,CAAC;IAClB,IAAII,GAAG,GAAGX,GAAG,CAACY,MAAM,CAAC,CAAC;IACtBZ,GAAG,CAACa,SAAS,CAAC,CAAC,CAACC,cAAc,CAACH,GAAG,GAAGxD,QAAQ,CAAC;IAC9C4C,GAAG,CAAC1B,IAAI,CAACkC,CAAC,CAAC,CAACQ,GAAG,CAACf,GAAG,CAAC;IACpBzC,IAAI,CAACe,OAAO,CAACkC,QAAQ,CAACnC,IAAI,CAAC0B,GAAG,CAAC;IAC/BxC,IAAI,CAACe,OAAO,CAACE,MAAM,CAACsB,SAAS,CAACxB,OAAO,CAACI,MAAM,CAAC8B,QAAQ,CAAC;EACxD,CAAC,CAAC;AACJ;AACA,SAASQ,yBAAyBA,CAAC;EACjC7D,QAAQ,GAAG,GAAG;EACd8D,SAAS,GAAG,GAAG;EACfC,GAAG;EACHC,MAAM,GAAGzE,qBAAqB;EAC9BiD,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZwB,KAAK,GAAG,CAAC;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAE;EACD,MAAM/D,IAAI,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMsC,SAAS,GAAGwB,IAAI,CAACC,YAAY;EACnC,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACxB3B,SAAS,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,CAAC;EACnD,MAAMsE,YAAY,GAAG5F,KAAK,CAAC+C,OAAO,CAAC,MAAM,IAAI3C,iBAAiB,CAAC0D,KAAK,EAAEC,MAAM,EAAE;IAC5E8B,MAAM,EAAExF,UAAU;IAClByF,aAAa,EAAE;IACf;EACF,CAAC,CAAC,EAAE,CAAChC,KAAK,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAM1B,QAAQ,GAAGrC,KAAK,CAAC2B,MAAM,CAAC;IAC5BoE,UAAU,EAAE;MACVxD,KAAK,EAAE8C;IACT,CAAC;IACDW,KAAK,EAAE;MACLzD,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFvC,KAAK,CAACiG,SAAS,CAAC,MAAM,MAAM5D,QAAQ,CAACI,OAAO,CAACsD,UAAU,CAACxD,KAAK,GAAG8C,GAAG,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EAC5E,MAAMa,MAAM,GAAGlG,KAAK,CAAC+C,OAAO,CAAC,MAAM,IAAIpC,cAAc,CAAC,IAAIL,cAAc,CAAC;IACvE+B,QAAQ,EAAEA,QAAQ,CAACI,OAAO;IAC1B0D,YAAY,EAAE,UAAU;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;IACPC,cAAc,EAAEd;EAClB,CAAC,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACdtF,KAAK,CAACiG,SAAS,CAAC,MAAM,MAAM;IAC1BC,MAAM,CAACjE,QAAQ,CAACoE,OAAO,CAAC,CAAC;IACzBH,MAAM,CAACG,OAAO,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACZlG,KAAK,CAACiG,SAAS,CAAC,MAAM,MAAML,YAAY,CAACS,OAAO,CAAC,CAAC,EAAE,CAACT,YAAY,CAAC,CAAC;EACnElF,QAAQ,CAAC,CAAC;IACR4F;EACF,CAAC,EAAEC,EAAE,KAAK;IACRlE,QAAQ,CAACI,OAAO,CAACuD,KAAK,CAACzD,KAAK,IAAIgE,EAAE;IAClCD,EAAE,CAACE,eAAe,CAACZ,YAAY,CAAC;IAChCM,MAAM,CAACO,MAAM,CAACH,EAAE,CAAC;IACjBA,EAAE,CAACE,eAAe,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO,aAAaxG,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACuD,QAAQ,EAAE,IAAI,EAAE,aAAavD,KAAK,CAACsD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACT6D,KAAK,EAAEA,KAAK;IACZmB,UAAU,EAAE;EACd,CAAC,EAAE,aAAa1G,KAAK,CAACsD,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAatD,KAAK,CAACsD,aAAa,CAAC,mBAAmB,EAAE;IAChHqD,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAErG,UAAU;IAChB6E,SAAS,EAAEA,SAAS;IACpByB,QAAQ,EAAEjB,YAAY,CAACkB,OAAO;IAC9B,gBAAgB,EAAEtG,cAAc;IAChC,gBAAgB,EAAEA,cAAc;IAChCS,OAAO,EAAE0E,KAAK,GAAG,CAAC,GAAG;EACvB,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;AAChB;AACA,SAASuB,4BAA4BA,CAAC;EACpCzF,QAAQ,GAAG,GAAG;EACd8D,SAAS,GAAG,GAAG;EACfC,GAAG;EACHvB,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZwB,KAAK;EACLC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAE;EACD,MAAM/D,IAAI,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMsC,SAAS,GAAGwB,IAAI,CAACC,YAAY;EACnC,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACxB3B,SAAS,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,CAAC;EACnD,OAAO,aAAatB,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACuD,QAAQ,EAAE,IAAI,EAAE,aAAavD,KAAK,CAACsD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACT6D,KAAK,EAAEA,KAAK;IACZmB,UAAU,EAAE;EACd,CAAC,EAAE,aAAa1G,KAAK,CAACsD,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAatD,KAAK,CAACsD,aAAa,CAAC,mBAAmB,EAAE;IAChHqD,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAErG,UAAU;IAChB6E,SAAS,EAAEA,SAAS;IACpByB,QAAQ,EAAExB,GAAG;IACb,gBAAgB,EAAE7E,cAAc;IAChC,gBAAgB,EAAEA,cAAc;IAChCS,OAAO,EAAE0E,KAAK,GAAG,CAAC,GAAG;EACvB,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;AAChB;AACA,SAASwB,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAIA,KAAK,CAAC3B,MAAM,EAAE,OAAO,aAAatF,KAAK,CAACsD,aAAa,CAAC6B,yBAAyB,EAAE8B,KAAK,CAAC;EAC3F,OAAO,aAAajH,KAAK,CAACsD,aAAa,CAACyD,4BAA4B,EAAEE,KAAK,CAAC;AAC9E;AACA,MAAMC,SAAS,GAAG,aAAalH,KAAK,CAACmH,UAAU,CAAC,CAAC;EAC/C;EACAlG,OAAO,GAAG,CAAC;EACXC,SAAS;EACTC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,OAAO;EACfC,QAAQ,GAAG,CAAC;EACZC,KAAK,GAAG,IAAI;EACZC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,CAAC;EACd2F,UAAU,GAAG,IAAI;EACjBzB,KAAK,GAAG,KAAK;EACbH,QAAQ;EACR,GAAGyB;AACL,CAAC,EAAEzD,GAAG,KAAK;EACT,MAAMS,SAAS,GAAGjE,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EACpC3B,KAAK,CAACqH,mBAAmB,CAAC7D,GAAG,EAAE,MAAMS,SAAS,CAACxB,OAAO,EAAE,EAAE,CAAC;EAC3D,OAAO,aAAazC,KAAK,CAACsD,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEqC,KAAK,IAAI1B,SAAS,CAACxB,OAAO,IAAI,aAAazC,KAAK,CAACsD,aAAa,CAAC,iBAAiB,EAAE;IACvIgE,IAAI,EAAE,CAACrD,SAAS,CAACxB,OAAO;EAC1B,CAAC,CAAC,EAAE,aAAazC,KAAK,CAACsD,aAAa,CAAC,WAAW,EAAEvD,QAAQ,CAAC;IACzDyD,GAAG,EAAES,SAAS;IACd1C,KAAK,EAAEA,KAAK;IACZF,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBoF,UAAU,EAAE;EACd,CAAC,EAAEO,KAAK,CAAC,EAAEG,UAAU,IAAI,aAAapH,KAAK,CAACsD,aAAa,CAACtC,cAAc,EAAE;IACxE2E,KAAK,EAAEA,KAAK;IACZ1E,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBC,KAAK,EAAEA,KAAK;IACZC,WAAW,EAAEA,WAAW;IACxBC,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC,EAAE+D,QAAQ,IAAI,aAAaxF,KAAK,CAACuH,YAAY,CAAC/B,QAAQ,EAAE;IACzDE,YAAY,EAAEzB,SAAS;IACvB0B,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASuB,SAAS,EAAEF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}