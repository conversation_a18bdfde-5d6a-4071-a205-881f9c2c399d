{"ast": null, "code": "import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nimport StatsImpl from 'stats.js';\nimport { useEffectfulState } from '../helpers/useEffectfulState.js';\nfunction Stats({\n  showPanel = 0,\n  className,\n  parent\n}) {\n  const stats = useEffectfulState(() => new StatsImpl(), []);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      stats.showPanel(showPanel);\n      node == null || node.appendChild(stats.dom);\n      const classNames = (className !== null && className !== void 0 ? className : '').split(' ').filter(cls => cls);\n      if (classNames.length) stats.dom.classList.add(...classNames);\n      const begin = addEffect(() => stats.begin());\n      const end = addAfterEffect(() => stats.end());\n      return () => {\n        if (classNames.length) stats.dom.classList.remove(...classNames);\n        node == null || node.removeChild(stats.dom);\n        begin();\n        end();\n      };\n    }\n  }, [parent, stats, className, showPanel]);\n  return null;\n}\nexport { Stats };", "map": {"version": 3, "names": ["React", "addEffect", "addAfterEffect", "StatsImpl", "useEffectfulState", "Stats", "showPanel", "className", "parent", "stats", "useEffect", "node", "current", "document", "body", "append<PERSON><PERSON><PERSON>", "dom", "classNames", "split", "filter", "cls", "length", "classList", "add", "begin", "end", "remove", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Stats.js"], "sourcesContent": ["import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nimport StatsImpl from 'stats.js';\nimport { useEffectfulState } from '../helpers/useEffectfulState.js';\n\nfunction Stats({\n  showPanel = 0,\n  className,\n  parent\n}) {\n  const stats = useEffectfulState(() => new StatsImpl(), []);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      stats.showPanel(showPanel);\n      node == null || node.appendChild(stats.dom);\n      const classNames = (className !== null && className !== void 0 ? className : '').split(' ').filter(cls => cls);\n      if (classNames.length) stats.dom.classList.add(...classNames);\n      const begin = addEffect(() => stats.begin());\n      const end = addAfterEffect(() => stats.end());\n      return () => {\n        if (classNames.length) stats.dom.classList.remove(...classNames);\n        node == null || node.removeChild(stats.dom);\n        begin();\n        end();\n      };\n    }\n  }, [parent, stats, className, showPanel]);\n  return null;\n}\n\nexport { Stats };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,cAAc,QAAQ,oBAAoB;AAC9D,OAAOC,SAAS,MAAM,UAAU;AAChC,SAASC,iBAAiB,QAAQ,iCAAiC;AAEnE,SAASC,KAAKA,CAAC;EACbC,SAAS,GAAG,CAAC;EACbC,SAAS;EACTC;AACF,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGL,iBAAiB,CAAC,MAAM,IAAID,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAC1DH,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,IAAID,KAAK,EAAE;MACT,MAAME,IAAI,GAAGH,MAAM,IAAIA,MAAM,CAACI,OAAO,IAAIC,QAAQ,CAACC,IAAI;MACtDL,KAAK,CAACH,SAAS,CAACA,SAAS,CAAC;MAC1BK,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACI,WAAW,CAACN,KAAK,CAACO,GAAG,CAAC;MAC3C,MAAMC,UAAU,GAAG,CAACV,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE,EAAEW,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC;MAC9G,IAAIH,UAAU,CAACI,MAAM,EAAEZ,KAAK,CAACO,GAAG,CAACM,SAAS,CAACC,GAAG,CAAC,GAAGN,UAAU,CAAC;MAC7D,MAAMO,KAAK,GAAGvB,SAAS,CAAC,MAAMQ,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAGvB,cAAc,CAAC,MAAMO,KAAK,CAACgB,GAAG,CAAC,CAAC,CAAC;MAC7C,OAAO,MAAM;QACX,IAAIR,UAAU,CAACI,MAAM,EAAEZ,KAAK,CAACO,GAAG,CAACM,SAAS,CAACI,MAAM,CAAC,GAAGT,UAAU,CAAC;QAChEN,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACgB,WAAW,CAAClB,KAAK,CAACO,GAAG,CAAC;QAC3CQ,KAAK,CAAC,CAAC;QACPC,GAAG,CAAC,CAAC;MACP,CAAC;IACH;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEC,KAAK,EAAEF,SAAS,EAAED,SAAS,CAAC,CAAC;EACzC,OAAO,IAAI;AACb;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}