{"ast": null, "code": "/**\n *\n */\nfunction zero() {\n  return [0, 0];\n}\nfunction one() {\n  return [1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]));\n}\nvar vector2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, length as f, distance as g, lengthSqr as l, one as o, sub as s, vector2 as v, zero as z };", "map": {"version": 3, "names": ["zero", "one", "add", "a", "b", "addValue", "n", "sub", "subValue", "scale", "dot", "lengthSqr", "length", "Math", "sqrt", "distance", "vector2", "Object", "freeze", "__proto__", "c", "d", "e", "f", "g", "l", "o", "s", "v", "z"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/maath/dist/vector2-d2bf51f1.esm.js"], "sourcesContent": ["/**\n *\n */\nfunction zero() {\n  return [0, 0];\n}\nfunction one() {\n  return [1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]));\n}\n\nvar vector2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, length as f, distance as g, lengthSqr as l, one as o, sub as s, vector2 as v, zero as z };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,IAAIA,CAAA,EAAG;EACd,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACf;AACA,SAASC,GAAGA,CAAA,EAAG;EACb,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACf;AACA,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,SAASC,QAAQA,CAACF,CAAC,EAAEG,CAAC,EAAE;EACtB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AAC7B;AACA,SAASC,GAAGA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,SAASI,QAAQA,CAACL,CAAC,EAAEG,CAAC,EAAE;EACtB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AAC7B;AACA,SAASG,KAAKA,CAACN,CAAC,EAAEG,CAAC,EAAE;EACnB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AAC7B;AACA,SAASI,GAAGA,CAACP,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA;;AAEA,SAASO,SAASA,CAACR,CAAC,EAAE;EACpB,OAAOA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA;;AAEA,SAASS,MAAMA,CAACT,CAAC,EAAE;EACjB,OAAOU,IAAI,CAACC,IAAI,CAACX,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA,SAASY,QAAQA,CAACZ,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOS,IAAI,CAACC,IAAI,CAAC,CAACX,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF;AAEA,IAAIY,OAAO,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EACvCC,SAAS,EAAE,IAAI;EACfnB,IAAI,EAAEA,IAAI;EACVC,GAAG,EAAEA,GAAG;EACRC,GAAG,EAAEA,GAAG;EACRG,QAAQ,EAAEA,QAAQ;EAClBE,GAAG,EAAEA,GAAG;EACRC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZC,GAAG,EAAEA,GAAG;EACRC,SAAS,EAAEA,SAAS;EACpBC,MAAM,EAAEA,MAAM;EACdG,QAAQ,EAAEA;AACZ,CAAC,CAAC;AAEF,SAASb,GAAG,IAAIC,CAAC,EAAEE,QAAQ,IAAID,CAAC,EAAEI,QAAQ,IAAIY,CAAC,EAAEX,KAAK,IAAIY,CAAC,EAAEX,GAAG,IAAIY,CAAC,EAAEV,MAAM,IAAIW,CAAC,EAAER,QAAQ,IAAIS,CAAC,EAAEb,SAAS,IAAIc,CAAC,EAAExB,GAAG,IAAIyB,CAAC,EAAEnB,GAAG,IAAIoB,CAAC,EAAEX,OAAO,IAAIY,CAAC,EAAE5B,IAAI,IAAI6B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}