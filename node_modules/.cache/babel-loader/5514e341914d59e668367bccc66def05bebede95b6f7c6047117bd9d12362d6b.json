{"ast": null, "code": "import * as React from 'react';\nimport { useTexture } from './Texture.js';\nimport { RepeatWrapping, Vector2 } from 'three';\nimport { suspend } from 'suspend-react';\nconst NORMAL_ROOT = 'https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb';\nconst LIST_URL = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json';\nfunction useNormalTexture(id = 0, settings = {}, onLoad) {\n  const {\n    repeat = [1, 1],\n    anisotropy = 1,\n    offset = [0, 0]\n  } = settings;\n  const normalsList = suspend(() => fetch(LIST_URL).then(res => res.json()), ['normalsList']);\n  const numTot = React.useMemo(() => Object.keys(normalsList).length, []);\n  const DEFAULT_NORMAL = normalsList[0];\n  const imageName = normalsList[id] || DEFAULT_NORMAL;\n  const url = `${NORMAL_ROOT}/normals/${imageName}`;\n  const normalTexture = useTexture(url, onLoad);\n  React.useLayoutEffect(() => {\n    if (!normalTexture) return;\n    normalTexture.wrapS = normalTexture.wrapT = RepeatWrapping;\n    normalTexture.repeat = new Vector2(repeat[0], repeat[1]);\n    normalTexture.offset = new Vector2(offset[0], offset[1]);\n    normalTexture.anisotropy = anisotropy;\n  }, [normalTexture, anisotropy, repeat, offset]);\n  return [normalTexture, url, numTot];\n}\n\n//\n\nconst NormalTexture = ({\n  children,\n  id,\n  onLoad,\n  ...settings\n}) => {\n  const ret = useNormalTexture(id, settings, onLoad);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\nexport { NormalTexture, useNormalTexture };", "map": {"version": 3, "names": ["React", "useTexture", "RepeatWrapping", "Vector2", "suspend", "NORMAL_ROOT", "LIST_URL", "useNormalTexture", "id", "settings", "onLoad", "repeat", "anisotropy", "offset", "normalsList", "fetch", "then", "res", "json", "numTot", "useMemo", "Object", "keys", "length", "DEFAULT_NORMAL", "imageName", "url", "normalTexture", "useLayoutEffect", "wrapS", "wrapT", "NormalTexture", "children", "ret", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/NormalTexture.js"], "sourcesContent": ["import * as React from 'react';\nimport { useTexture } from './Texture.js';\nimport { RepeatWrapping, Vector2 } from 'three';\nimport { suspend } from 'suspend-react';\n\nconst NORMAL_ROOT = 'https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb';\nconst LIST_URL = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json';\nfunction useNormalTexture(id = 0, settings = {}, onLoad) {\n  const {\n    repeat = [1, 1],\n    anisotropy = 1,\n    offset = [0, 0]\n  } = settings;\n  const normalsList = suspend(() => fetch(LIST_URL).then(res => res.json()), ['normalsList']);\n  const numTot = React.useMemo(() => Object.keys(normalsList).length, []);\n  const DEFAULT_NORMAL = normalsList[0];\n  const imageName = normalsList[id] || DEFAULT_NORMAL;\n  const url = `${NORMAL_ROOT}/normals/${imageName}`;\n  const normalTexture = useTexture(url, onLoad);\n  React.useLayoutEffect(() => {\n    if (!normalTexture) return;\n    normalTexture.wrapS = normalTexture.wrapT = RepeatWrapping;\n    normalTexture.repeat = new Vector2(repeat[0], repeat[1]);\n    normalTexture.offset = new Vector2(offset[0], offset[1]);\n    normalTexture.anisotropy = anisotropy;\n  }, [normalTexture, anisotropy, repeat, offset]);\n  return [normalTexture, url, numTot];\n}\n\n//\n\nconst NormalTexture = ({\n  children,\n  id,\n  onLoad,\n  ...settings\n}) => {\n  const ret = useNormalTexture(id, settings, onLoad);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\n\nexport { NormalTexture, useNormalTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,EAAEC,OAAO,QAAQ,OAAO;AAC/C,SAASC,OAAO,QAAQ,eAAe;AAEvC,MAAMC,WAAW,GAAG,wFAAwF;AAC5G,MAAMC,QAAQ,GAAG,4EAA4E;AAC7F,SAASC,gBAAgBA,CAACC,EAAE,GAAG,CAAC,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAEC,MAAM,EAAE;EACvD,MAAM;IACJC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACfC,UAAU,GAAG,CAAC;IACdC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC,GAAGJ,QAAQ;EACZ,MAAMK,WAAW,GAAGV,OAAO,CAAC,MAAMW,KAAK,CAACT,QAAQ,CAAC,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;EAC3F,MAAMC,MAAM,GAAGnB,KAAK,CAACoB,OAAO,CAAC,MAAMC,MAAM,CAACC,IAAI,CAACR,WAAW,CAAC,CAACS,MAAM,EAAE,EAAE,CAAC;EACvE,MAAMC,cAAc,GAAGV,WAAW,CAAC,CAAC,CAAC;EACrC,MAAMW,SAAS,GAAGX,WAAW,CAACN,EAAE,CAAC,IAAIgB,cAAc;EACnD,MAAME,GAAG,GAAG,GAAGrB,WAAW,YAAYoB,SAAS,EAAE;EACjD,MAAME,aAAa,GAAG1B,UAAU,CAACyB,GAAG,EAAEhB,MAAM,CAAC;EAC7CV,KAAK,CAAC4B,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACD,aAAa,EAAE;IACpBA,aAAa,CAACE,KAAK,GAAGF,aAAa,CAACG,KAAK,GAAG5B,cAAc;IAC1DyB,aAAa,CAAChB,MAAM,GAAG,IAAIR,OAAO,CAACQ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxDgB,aAAa,CAACd,MAAM,GAAG,IAAIV,OAAO,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxDc,aAAa,CAACf,UAAU,GAAGA,UAAU;EACvC,CAAC,EAAE,CAACe,aAAa,EAAEf,UAAU,EAAED,MAAM,EAAEE,MAAM,CAAC,CAAC;EAC/C,OAAO,CAACc,aAAa,EAAED,GAAG,EAAEP,MAAM,CAAC;AACrC;;AAEA;;AAEA,MAAMY,aAAa,GAAGA,CAAC;EACrBC,QAAQ;EACRxB,EAAE;EACFE,MAAM;EACN,GAAGD;AACL,CAAC,KAAK;EACJ,MAAMwB,GAAG,GAAG1B,gBAAgB,CAACC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,CAAC;EAClD,OAAO,aAAaV,KAAK,CAACkC,aAAa,CAAClC,KAAK,CAACmC,QAAQ,EAAE,IAAI,EAAEH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,GAAG,CAAC,CAAC;AAC1G,CAAC;AAED,SAASF,aAAa,EAAExB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}