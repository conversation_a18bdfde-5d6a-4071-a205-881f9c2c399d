{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Download.tsx\";\nimport { homeDownload, privateIcon, secure, twoArrowws } from \"../../assets/image\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HomeDownload = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"w-full py-16 px-4 relative bg-black z-[10]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row items-center justify-between gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2 bg-black z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: homeDownload,\n              alt: \"Portfolio Projects\",\n              className: \"w-full max-w-lg mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-4xl font-bold text-white mb-6 white-text-shadow\",\n              children: \"Featured Projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 mb-8\",\n              children: \"Explore my portfolio of web applications, mobile apps, and full-stack solutions. Each project showcases modern technologies, clean code, and user-centered design principles that deliver exceptional user experiences.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row gap-[20px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://github.com/techEdge3030\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-semibold hover:scale-105 transition-transform text-center\",\n                children: \"View All Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://www.linkedin.com/in/do-quoc-dat-b865a6b5/\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"px-6 py-3 border border-gray-400 rounded-lg text-white font-semibold hover:bg-gray-800 transition-colors text-center\",\n                children: \"Connect on LinkedIn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 rounded-full flex items-center justify-center bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: secure,\n                    alt: \"secure\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Modern Tech Stack\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 rounded-full flex items-center justify-center bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: privateIcon,\n                    alt: \"secure\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Responsive Design\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 rounded-full flex items-center justify-center bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: twoArrowws,\n                    className: \"w-4 h-4\",\n                    alt: \"secure\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Performance Optimized\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_c = HomeDownload;\nexport default HomeDownload;\nvar _c;\n$RefreshReg$(_c, \"HomeDownload\");", "map": {"version": 3, "names": ["homeDownload", "privateIcon", "secure", "twoArrowws", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomeDownload", "children", "className", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Download.tsx"], "sourcesContent": ["import {\n  appStore,\n  googleAppStore,\n  homeDownload,\n  privateIcon,\n  secure,\n  twoArrowws,\n} from \"../../assets/image\";\nimport { NormalButton } from \"../../components/common/Button\";\n\nconst HomeDownload = () => {\n  return (\n    <>\n      <section className=\"w-full py-16 px-4 relative bg-black z-[10]\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n            <div className=\"lg:w-1/2 bg-black z-10\">\n              <img\n                src={homeDownload}\n                alt=\"Portfolio Projects\"\n                className=\"w-full max-w-lg mx-auto\"\n              />\n            </div>\n            <div className=\"lg:w-1/2 text-left\">\n              <h2 className=\"text-4xl font-bold text-white mb-6 white-text-shadow\">\n                Featured Projects\n              </h2>\n              <p className=\"text-gray-300 mb-8\">\n                Explore my portfolio of web applications, mobile apps, and full-stack solutions.\n                Each project showcases modern technologies, clean code, and user-centered design\n                principles that deliver exceptional user experiences.\n              </p>\n            <div className=\"flex flex-col md:flex-row gap-[20px]\">\n              <a\n                href=\"https://github.com/techEdge3030\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-semibold hover:scale-105 transition-transform text-center\"\n              >\n                View All Projects\n              </a>\n              <a\n                href=\"https://www.linkedin.com/in/do-quoc-dat-b865a6b5/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-6 py-3 border border-gray-400 rounded-lg text-white font-semibold hover:bg-gray-800 transition-colors text-center\"\n              >\n                Connect on LinkedIn\n              </a>\n            </div>\n            <div className=\"mt-8\">\n              <div className=\"flex items-center gap-2 mb-3\">\n                <div className=\"w-6 h-6 rounded-full flex items-center justify-center bg-white\">\n                  <img src={secure} alt=\"secure\" />\n                </div>\n                <span className=\"text-gray-300\">Modern Tech Stack</span>\n              </div>\n              <div className=\"flex items-center gap-2 mb-3\">\n                <div className=\"w-6 h-6 rounded-full flex items-center justify-center bg-white\">\n                  <img src={privateIcon} alt=\"secure\" />\n                </div>\n                <span className=\"text-gray-300\">Responsive Design</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-6 h-6 rounded-full flex items-center justify-center bg-white\">\n                  <img src={twoArrowws} className=\"w-4 h-4\" alt=\"secure\" />\n                </div>\n                <span className=\"text-gray-300\">Performance Optimized</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n\n    </>\n  );\n};\n\nexport default HomeDownload;\n"], "mappings": ";AAAA,SAGEA,YAAY,EACZC,WAAW,EACXC,MAAM,EACNC,UAAU,QACL,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,eACEJ,OAAA;MAASK,SAAS,EAAC,4CAA4C;MAAAD,QAAA,eAC7DJ,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAAD,QAAA,eAChCJ,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAD,QAAA,gBAC5EJ,OAAA;YAAKK,SAAS,EAAC,wBAAwB;YAAAD,QAAA,eACrCJ,OAAA;cACEM,GAAG,EAAEX,YAAa;cAClBY,GAAG,EAAC,oBAAoB;cACxBF,SAAS,EAAC;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKK,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACjCJ,OAAA;cAAIK,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAAC;YAErE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAGK,SAAS,EAAC,oBAAoB;cAAAD,QAAA,EAAC;YAIlC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACNX,OAAA;cAAKK,SAAS,EAAC,sCAAsC;cAAAD,QAAA,gBACnDJ,OAAA;gBACEY,IAAI,EAAC,iCAAiC;gBACtCC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzBT,SAAS,EAAC,6IAA6I;gBAAAD,QAAA,EACxJ;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJX,OAAA;gBACEY,IAAI,EAAC,mDAAmD;gBACxDC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzBT,SAAS,EAAC,sHAAsH;gBAAAD,QAAA,EACjI;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNX,OAAA;cAAKK,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBJ,OAAA;gBAAKK,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3CJ,OAAA;kBAAKK,SAAS,EAAC,gEAAgE;kBAAAD,QAAA,eAC7EJ,OAAA;oBAAKM,GAAG,EAAET,MAAO;oBAACU,GAAG,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNX,OAAA;kBAAMK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNX,OAAA;gBAAKK,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3CJ,OAAA;kBAAKK,SAAS,EAAC,gEAAgE;kBAAAD,QAAA,eAC7EJ,OAAA;oBAAKM,GAAG,EAAEV,WAAY;oBAACW,GAAG,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNX,OAAA;kBAAMK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNX,OAAA;gBAAKK,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtCJ,OAAA;kBAAKK,SAAS,EAAC,gEAAgE;kBAAAD,QAAA,eAC7EJ,OAAA;oBAAKM,GAAG,EAAER,UAAW;oBAACO,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNX,OAAA;kBAAMK,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC,gBAGR,CAAC;AAEP,CAAC;AAACI,EAAA,GApEIZ,YAAY;AAsElB,eAAeA,YAAY;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}