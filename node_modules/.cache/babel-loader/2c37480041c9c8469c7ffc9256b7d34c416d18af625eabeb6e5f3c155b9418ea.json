{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { applyProps } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nconst Lightformer = /* @__PURE__ */React.forwardRef(({\n  light,\n  args,\n  map,\n  toneMapped = false,\n  color = 'white',\n  form: Form = 'rect',\n  intensity = 1,\n  scale = 1,\n  target = [0, 0, 0],\n  children,\n  ...props\n}, forwardRef) => {\n  // Apply emissive power\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    if (!children && !props.material) {\n      applyProps(ref.current.material, {\n        color\n      });\n      ref.current.material.color.multiplyScalar(intensity);\n    }\n  }, [color, intensity, children, props.material]);\n\n  // Target light\n  React.useLayoutEffect(() => {\n    if (!props.rotation) ref.current.quaternion.identity();\n    if (target && !props.rotation) {\n      'boolean' === typeof target ? ref.current.lookAt(0, 0, 0) : ref.current.lookAt(Array.isArray(target) ? new THREE.Vector3(...target) : target);\n    }\n  }, [target, props.rotation]);\n\n  // Fix 2-dimensional scale\n  scale = Array.isArray(scale) && scale.length === 2 ? [scale[0], scale[1], 1] : scale;\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: scale\n  }, props), Form === 'circle' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: args ? args : [0, 0.5, 64]\n  }) : Form === 'ring' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: args ? args : [0.25, 0.5, 64]\n  }) : Form === 'rect' || Form === 'plane' ? /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args ? args : [1, 1]\n  }) : Form === 'box' ? /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: args ? args : [1, 1, 1]\n  }) : /*#__PURE__*/React.createElement(Form, {\n    args: args\n  }), children ? children : /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    toneMapped: toneMapped,\n    map: map,\n    side: THREE.DoubleSide\n  }), light && /*#__PURE__*/React.createElement(\"pointLight\", _extends({\n    castShadow: true\n  }, light)));\n});\nexport { Lightformer };", "map": {"version": 3, "names": ["_extends", "applyProps", "React", "THREE", "Lightformer", "forwardRef", "light", "args", "map", "toneMapped", "color", "form", "Form", "intensity", "scale", "target", "children", "props", "ref", "useRef", "useImperativeHandle", "current", "useLayoutEffect", "material", "multiplyScalar", "rotation", "quaternion", "identity", "lookAt", "Array", "isArray", "Vector3", "length", "createElement", "side", "DoubleSide", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Lightformer.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { applyProps } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nconst Lightformer = /* @__PURE__ */React.forwardRef(({\n  light,\n  args,\n  map,\n  toneMapped = false,\n  color = 'white',\n  form: Form = 'rect',\n  intensity = 1,\n  scale = 1,\n  target = [0, 0, 0],\n  children,\n  ...props\n}, forwardRef) => {\n  // Apply emissive power\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    if (!children && !props.material) {\n      applyProps(ref.current.material, {\n        color\n      });\n      ref.current.material.color.multiplyScalar(intensity);\n    }\n  }, [color, intensity, children, props.material]);\n\n  // Target light\n  React.useLayoutEffect(() => {\n    if (!props.rotation) ref.current.quaternion.identity();\n    if (target && !props.rotation) {\n      'boolean' === typeof target ? ref.current.lookAt(0, 0, 0) : ref.current.lookAt(Array.isArray(target) ? new THREE.Vector3(...target) : target);\n    }\n  }, [target, props.rotation]);\n\n  // Fix 2-dimensional scale\n  scale = Array.isArray(scale) && scale.length === 2 ? [scale[0], scale[1], 1] : scale;\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: scale\n  }, props), Form === 'circle' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: args ? args : [0, 0.5, 64]\n  }) : Form === 'ring' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: args ? args : [0.25, 0.5, 64]\n  }) : Form === 'rect' || Form === 'plane' ? /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args ? args : [1, 1]\n  }) : Form === 'box' ? /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: args ? args : [1, 1, 1]\n  }) : /*#__PURE__*/React.createElement(Form, {\n    args: args\n  }), children ? children : /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    toneMapped: toneMapped,\n    map: map,\n    side: THREE.DoubleSide\n  }), light && /*#__PURE__*/React.createElement(\"pointLight\", _extends({\n    castShadow: true\n  }, light)));\n});\n\nexport { Lightformer };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,WAAW,GAAG,eAAeF,KAAK,CAACG,UAAU,CAAC,CAAC;EACnDC,KAAK;EACLC,IAAI;EACJC,GAAG;EACHC,UAAU,GAAG,KAAK;EAClBC,KAAK,GAAG,OAAO;EACfC,IAAI,EAAEC,IAAI,GAAG,MAAM;EACnBC,SAAS,GAAG,CAAC;EACbC,KAAK,GAAG,CAAC;EACTC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEZ,UAAU,KAAK;EAChB;EACA,MAAMa,GAAG,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAC9BjB,KAAK,CAACkB,mBAAmB,CAACf,UAAU,EAAE,MAAMa,GAAG,CAACG,OAAO,EAAE,EAAE,CAAC;EAC5DnB,KAAK,CAACoB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACN,QAAQ,IAAI,CAACC,KAAK,CAACM,QAAQ,EAAE;MAChCtB,UAAU,CAACiB,GAAG,CAACG,OAAO,CAACE,QAAQ,EAAE;QAC/Bb;MACF,CAAC,CAAC;MACFQ,GAAG,CAACG,OAAO,CAACE,QAAQ,CAACb,KAAK,CAACc,cAAc,CAACX,SAAS,CAAC;IACtD;EACF,CAAC,EAAE,CAACH,KAAK,EAAEG,SAAS,EAAEG,QAAQ,EAAEC,KAAK,CAACM,QAAQ,CAAC,CAAC;;EAEhD;EACArB,KAAK,CAACoB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACL,KAAK,CAACQ,QAAQ,EAAEP,GAAG,CAACG,OAAO,CAACK,UAAU,CAACC,QAAQ,CAAC,CAAC;IACtD,IAAIZ,MAAM,IAAI,CAACE,KAAK,CAACQ,QAAQ,EAAE;MAC7B,SAAS,KAAK,OAAOV,MAAM,GAAGG,GAAG,CAACG,OAAO,CAACO,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAGV,GAAG,CAACG,OAAO,CAACO,MAAM,CAACC,KAAK,CAACC,OAAO,CAACf,MAAM,CAAC,GAAG,IAAIZ,KAAK,CAAC4B,OAAO,CAAC,GAAGhB,MAAM,CAAC,GAAGA,MAAM,CAAC;IAC/I;EACF,CAAC,EAAE,CAACA,MAAM,EAAEE,KAAK,CAACQ,QAAQ,CAAC,CAAC;;EAE5B;EACAX,KAAK,GAAGe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,IAAIA,KAAK,CAACkB,MAAM,KAAK,CAAC,GAAG,CAAClB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,KAAK;EACpF,OAAO,aAAaZ,KAAK,CAAC+B,aAAa,CAAC,MAAM,EAAEjC,QAAQ,CAAC;IACvDkB,GAAG,EAAEA,GAAG;IACRJ,KAAK,EAAEA;EACT,CAAC,EAAEG,KAAK,CAAC,EAAEL,IAAI,KAAK,QAAQ,GAAG,aAAaV,KAAK,CAAC+B,aAAa,CAAC,cAAc,EAAE;IAC9E1B,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;EACjC,CAAC,CAAC,GAAGK,IAAI,KAAK,MAAM,GAAG,aAAaV,KAAK,CAAC+B,aAAa,CAAC,cAAc,EAAE;IACtE1B,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;EACpC,CAAC,CAAC,GAAGK,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,GAAG,aAAaV,KAAK,CAAC+B,aAAa,CAAC,eAAe,EAAE;IAC3F1B,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;EAC3B,CAAC,CAAC,GAAGK,IAAI,KAAK,KAAK,GAAG,aAAaV,KAAK,CAAC+B,aAAa,CAAC,aAAa,EAAE;IACpE1B,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAC9B,CAAC,CAAC,GAAG,aAAaL,KAAK,CAAC+B,aAAa,CAACrB,IAAI,EAAE;IAC1CL,IAAI,EAAEA;EACR,CAAC,CAAC,EAAES,QAAQ,GAAGA,QAAQ,GAAG,aAAad,KAAK,CAAC+B,aAAa,CAAC,mBAAmB,EAAE;IAC9ExB,UAAU,EAAEA,UAAU;IACtBD,GAAG,EAAEA,GAAG;IACR0B,IAAI,EAAE/B,KAAK,CAACgC;EACd,CAAC,CAAC,EAAE7B,KAAK,IAAI,aAAaJ,KAAK,CAAC+B,aAAa,CAAC,YAAY,EAAEjC,QAAQ,CAAC;IACnEoC,UAAU,EAAE;EACd,CAAC,EAAE9B,KAAK,CAAC,CAAC,CAAC;AACb,CAAC,CAAC;AAEF,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}