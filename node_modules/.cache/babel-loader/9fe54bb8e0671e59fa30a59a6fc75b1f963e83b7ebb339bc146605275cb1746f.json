{"ast": null, "code": "import * as React from 'react';\nimport { useLayoutEffect, useEffect, useMemo } from 'react';\nimport { TextureLoader, Texture as Texture$1 } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nconst IsObject = url => url === Object(url) && !Array.isArray(url) && typeof url !== 'function';\nfunction useTexture(input, onLoad) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(TextureLoader, IsObject(input) ? Object.values(input) : input);\n  useLayoutEffect(() => {\n    onLoad == null || onLoad(textures);\n  }, [onLoad]);\n\n  // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n  // NOTE: only available for WebGLRenderer\n  useEffect(() => {\n    if ('initTexture' in gl) {\n      let textureArray = [];\n      if (Array.isArray(textures)) {\n        textureArray = textures;\n      } else if (textures instanceof Texture$1) {\n        textureArray = [textures];\n      } else if (IsObject(textures)) {\n        textureArray = Object.values(textures);\n      }\n      textureArray.forEach(texture => {\n        if (texture instanceof Texture$1) {\n          gl.initTexture(texture);\n        }\n      });\n    }\n  }, [gl, textures]);\n  const mappedTextures = useMemo(() => {\n    if (IsObject(input)) {\n      const keyed = {};\n      let i = 0;\n      for (const key in input) keyed[key] = textures[i++];\n      return keyed;\n    } else {\n      return textures;\n    }\n  }, [input, textures]);\n  return mappedTextures;\n}\nuseTexture.preload = url => useLoader.preload(TextureLoader, url);\nuseTexture.clear = input => useLoader.clear(TextureLoader, input);\n\n//\n\nconst Texture = ({\n  children,\n  input,\n  onLoad\n}) => {\n  const ret = useTexture(input, onLoad);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\nexport { IsObject, Texture, useTexture };", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useEffect", "useMemo", "TextureLoader", "Texture", "Texture$1", "useThree", "useLoader", "IsObject", "url", "Object", "Array", "isArray", "useTexture", "input", "onLoad", "gl", "state", "textures", "values", "textureArray", "for<PERSON>ach", "texture", "initTexture", "mappedTextures", "keyed", "i", "key", "preload", "clear", "children", "ret", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Texture.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect, useEffect, useMemo } from 'react';\nimport { TextureLoader, Texture as Texture$1 } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\n\nconst IsObject = url => url === Object(url) && !Array.isArray(url) && typeof url !== 'function';\nfunction useTexture(input, onLoad) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(TextureLoader, IsObject(input) ? Object.values(input) : input);\n  useLayoutEffect(() => {\n    onLoad == null || onLoad(textures);\n  }, [onLoad]);\n\n  // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n  // NOTE: only available for WebGLRenderer\n  useEffect(() => {\n    if ('initTexture' in gl) {\n      let textureArray = [];\n      if (Array.isArray(textures)) {\n        textureArray = textures;\n      } else if (textures instanceof Texture$1) {\n        textureArray = [textures];\n      } else if (IsObject(textures)) {\n        textureArray = Object.values(textures);\n      }\n      textureArray.forEach(texture => {\n        if (texture instanceof Texture$1) {\n          gl.initTexture(texture);\n        }\n      });\n    }\n  }, [gl, textures]);\n  const mappedTextures = useMemo(() => {\n    if (IsObject(input)) {\n      const keyed = {};\n      let i = 0;\n      for (const key in input) keyed[key] = textures[i++];\n      return keyed;\n    } else {\n      return textures;\n    }\n  }, [input, textures]);\n  return mappedTextures;\n}\nuseTexture.preload = url => useLoader.preload(TextureLoader, url);\nuseTexture.clear = input => useLoader.clear(TextureLoader, input);\n\n//\n\nconst Texture = ({\n  children,\n  input,\n  onLoad\n}) => {\n  const ret = useTexture(input, onLoad);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\n\nexport { IsObject, Texture, useTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SAASC,aAAa,EAAEC,OAAO,IAAIC,SAAS,QAAQ,OAAO;AAC3D,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAExD,MAAMC,QAAQ,GAAGC,GAAG,IAAIA,GAAG,KAAKC,MAAM,CAACD,GAAG,CAAC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,UAAU;AAC/F,SAASI,UAAUA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACjC,MAAMC,EAAE,GAAGV,QAAQ,CAACW,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,QAAQ,GAAGX,SAAS,CAACJ,aAAa,EAAEK,QAAQ,CAACM,KAAK,CAAC,GAAGJ,MAAM,CAACS,MAAM,CAACL,KAAK,CAAC,GAAGA,KAAK,CAAC;EACzFd,eAAe,CAAC,MAAM;IACpBe,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,QAAQ,CAAC;EACpC,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA;EACAd,SAAS,CAAC,MAAM;IACd,IAAI,aAAa,IAAIe,EAAE,EAAE;MACvB,IAAII,YAAY,GAAG,EAAE;MACrB,IAAIT,KAAK,CAACC,OAAO,CAACM,QAAQ,CAAC,EAAE;QAC3BE,YAAY,GAAGF,QAAQ;MACzB,CAAC,MAAM,IAAIA,QAAQ,YAAYb,SAAS,EAAE;QACxCe,YAAY,GAAG,CAACF,QAAQ,CAAC;MAC3B,CAAC,MAAM,IAAIV,QAAQ,CAACU,QAAQ,CAAC,EAAE;QAC7BE,YAAY,GAAGV,MAAM,CAACS,MAAM,CAACD,QAAQ,CAAC;MACxC;MACAE,YAAY,CAACC,OAAO,CAACC,OAAO,IAAI;QAC9B,IAAIA,OAAO,YAAYjB,SAAS,EAAE;UAChCW,EAAE,CAACO,WAAW,CAACD,OAAO,CAAC;QACzB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACN,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAClB,MAAMM,cAAc,GAAGtB,OAAO,CAAC,MAAM;IACnC,IAAIM,QAAQ,CAACM,KAAK,CAAC,EAAE;MACnB,MAAMW,KAAK,GAAG,CAAC,CAAC;MAChB,IAAIC,CAAC,GAAG,CAAC;MACT,KAAK,MAAMC,GAAG,IAAIb,KAAK,EAAEW,KAAK,CAACE,GAAG,CAAC,GAAGT,QAAQ,CAACQ,CAAC,EAAE,CAAC;MACnD,OAAOD,KAAK;IACd,CAAC,MAAM;MACL,OAAOP,QAAQ;IACjB;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEI,QAAQ,CAAC,CAAC;EACrB,OAAOM,cAAc;AACvB;AACAX,UAAU,CAACe,OAAO,GAAGnB,GAAG,IAAIF,SAAS,CAACqB,OAAO,CAACzB,aAAa,EAAEM,GAAG,CAAC;AACjEI,UAAU,CAACgB,KAAK,GAAGf,KAAK,IAAIP,SAAS,CAACsB,KAAK,CAAC1B,aAAa,EAAEW,KAAK,CAAC;;AAEjE;;AAEA,MAAMV,OAAO,GAAGA,CAAC;EACf0B,QAAQ;EACRhB,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMgB,GAAG,GAAGlB,UAAU,CAACC,KAAK,EAAEC,MAAM,CAAC;EACrC,OAAO,aAAahB,KAAK,CAACiC,aAAa,CAACjC,KAAK,CAACkC,QAAQ,EAAE,IAAI,EAAEH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,GAAG,CAAC,CAAC;AAC1G,CAAC;AAED,SAASvB,QAAQ,EAAEJ,OAAO,EAAES,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}