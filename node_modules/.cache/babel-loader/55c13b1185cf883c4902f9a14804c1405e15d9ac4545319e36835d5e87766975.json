{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { setUpdateRange } from '../helpers/deprecated.js';\nfunction isFunctionChild(value) {\n  return typeof value === 'function';\n}\nconst _instanceLocalMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _instanceWorldMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _instanceIntersects = [];\nconst _mesh = /* @__PURE__ */new THREE.Mesh();\nclass PositionMesh extends THREE.Group {\n  constructor() {\n    super();\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  }\n\n  // This will allow the virtual instance have bounds\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n\n  // And this will allow the virtual instance to receive events\n  raycast(raycaster, intersects) {\n    const parent = this.instance.current;\n    if (!parent) return;\n    if (!parent.geometry || !parent.material) return;\n    _mesh.geometry = parent.geometry;\n    const matrixWorld = parent.matrixWorld;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey);\n    // If the instance wasn't found or exceeds the parents draw range, bail out\n    if (instanceId === -1 || instanceId > parent.count) return;\n    // calculate the world matrix for each instance\n    parent.getMatrixAt(instanceId, _instanceLocalMatrix);\n    _instanceWorldMatrix.multiplyMatrices(matrixWorld, _instanceLocalMatrix);\n    // the mesh represents this single instance\n    _mesh.matrixWorld = _instanceWorldMatrix;\n    // raycast side according to instance material\n    if (parent.material instanceof THREE.Material) _mesh.material.side = parent.material.side;else _mesh.material.side = parent.material[0].side;\n    _mesh.raycast(raycaster, _instanceIntersects);\n    // process the result of raycast\n    for (let i = 0, l = _instanceIntersects.length; i < l; i++) {\n      const intersect = _instanceIntersects[i];\n      intersect.instanceId = instanceId;\n      intersect.object = this;\n      intersects.push(intersect);\n    }\n    _instanceIntersects.length = 0;\n  }\n}\nconst globalContext = /* @__PURE__ */React.createContext(null);\nconst parentMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst instanceMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst tempMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst translation = /* @__PURE__ */new THREE.Vector3();\nconst rotation = /* @__PURE__ */new THREE.Quaternion();\nconst scale = /* @__PURE__ */new THREE.Vector3();\nconst isInstancedBufferAttribute = attr => attr.isInstancedBufferAttribute;\nconst Instance = /* @__PURE__ */React.forwardRef(({\n  context,\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionMesh\n  }), []);\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context || globalContext);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionMesh\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: group\n  }, props), children);\n});\nconst Instances = /* @__PURE__ */React.forwardRef(({\n  context,\n  children,\n  range,\n  limit = 1000,\n  frames = Infinity,\n  ...props\n}, ref) => {\n  const [{\n    localContext,\n    instance\n  }] = React.useState(() => {\n    const localContext = /*#__PURE__*/React.createContext(null);\n    return {\n      localContext,\n      instance: /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(Instance, _extends({\n        context: localContext\n      }, props, {\n        ref: ref\n      })))\n    };\n  });\n  const parentRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => parentRef.current, []);\n  const [instances, setInstances] = React.useState([]);\n  const [[matrices, colors]] = React.useState(() => {\n    const mArray = new Float32Array(limit * 16);\n    for (let i = 0; i < limit; i++) tempMatrix.identity().toArray(mArray, i * 16);\n    return [mArray, new Float32Array([...new Array(limit * 3)].map(() => 1))];\n  });\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.instanceMatrix.needsUpdate = true;\n  });\n  let iterations = 0;\n  let count = 0;\n  const attributes = React.useRef([]);\n  React.useLayoutEffect(() => {\n    attributes.current = Object.entries(parentRef.current.geometry.attributes).filter(([_name, value]) => isInstancedBufferAttribute(value));\n  });\n  useFrame(() => {\n    if (frames === Infinity || iterations < frames) {\n      parentRef.current.updateMatrix();\n      parentRef.current.updateMatrixWorld();\n      parentMatrix.copy(parentRef.current.matrixWorld).invert();\n      count = Math.min(limit, range !== undefined ? range : limit, instances.length);\n      parentRef.current.count = count;\n      setUpdateRange(parentRef.current.instanceMatrix, {\n        start: 0,\n        count: count * 16\n      });\n      setUpdateRange(parentRef.current.instanceColor, {\n        start: 0,\n        count: count * 3\n      });\n      for (let i = 0; i < instances.length; i++) {\n        const instance = instances[i].current;\n        // Multiply the inverse of the InstancedMesh world matrix or else\n        // Instances will be double-transformed if <Instances> isn't at identity\n        instance.matrixWorld.decompose(translation, rotation, scale);\n        instanceMatrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n        instanceMatrix.toArray(matrices, i * 16);\n        parentRef.current.instanceMatrix.needsUpdate = true;\n        instance.color.toArray(colors, i * 3);\n        parentRef.current.instanceColor.needsUpdate = true;\n      }\n      iterations++;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setInstances(instances => [...instances, ref]);\n      return () => setInstances(instances => instances.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"instancedMesh\", _extends({\n    userData: {\n      instances,\n      limit,\n      frames\n    },\n    matrixAutoUpdate: false,\n    ref: parentRef,\n    args: [null, null, 0],\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceMatrix\",\n    args: [matrices, 16],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceColor\",\n    args: [colors, 3],\n    usage: THREE.DynamicDrawUsage\n  }), isFunctionChild(children) ? /*#__PURE__*/React.createElement(localContext.Provider, {\n    value: api\n  }, children(instance)) : context ? /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children) : /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children));\n});\n// TODO: make this non-recursive and type-safe\nconst Merged = /* @__PURE__ */React.forwardRef(function Merged({\n  meshes,\n  children,\n  ...props\n}, ref) {\n  const isArray = Array.isArray(meshes);\n  // Filter out meshes from collections, which may contain non-meshes\n  // @ts-expect-error\n  if (!isArray) for (const key of Object.keys(meshes)) if (!meshes[key].isMesh) delete meshes[key];\n  const render = args => isArray ?\n  // @ts-expect-error\n  children(...args) : children(\n  // @ts-expect-error\n  Object.keys(meshes)\n  // @ts-expect-error\n  .filter(key => meshes[key].isMesh).reduce((acc, key, i) => ({\n    ...acc,\n    [key]: args[i]\n  }), {}));\n\n  // @ts-expect-error\n  const components = (isArray ? meshes : Object.values(meshes)).map(({\n    geometry,\n    material\n  }) => /*#__PURE__*/React.createElement(Instances, _extends({\n    key: geometry.uuid,\n    geometry: geometry,\n    material: material\n  }, props)));\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, renderRecursive(render, components));\n});\n\n// https://github.com/jamesplease/react-composer\nfunction renderRecursive(render, components, results = []) {\n  // Once components is exhausted, we can render out the results array.\n  if (!components[0]) {\n    return render(results);\n  }\n\n  // Continue recursion for remaining items.\n  // results.concat([value]) ensures [...results, value] instead of [...results, ...value]\n  function nextRender(value) {\n    return renderRecursive(render, components.slice(1), results.concat([value]));\n  }\n\n  // Each props.components entry is either an element or function [element factory]\n  return typeof components[0] === 'function' ?\n  // When it is a function, produce an element by invoking it with \"render component values\".\n  components[0]({\n    results,\n    render: nextRender\n  }) : /*#__PURE__*/\n  // When it is an element, enhance the element's props with the render prop.\n  React.cloneElement(components[0], {\n    children: nextRender\n  });\n}\n\n/** Idea and implementation for global instances and instanced attributes by\n/*  Matias Gonzalez Fernandez https://x.com/matiNotFound\n/*  and Paul Henschel https://x.com/0xca0a\n*/\nfunction createInstances() {\n  const context = /*#__PURE__*/React.createContext(null);\n  return [/*#__PURE__*/React.forwardRef((props, fref) => /*#__PURE__*/React.createElement(Instances, _extends({\n    ref: fref,\n    context: context\n  }, props))), /*#__PURE__*/React.forwardRef((props, fref) => /*#__PURE__*/React.createElement(Instance, _extends({\n    ref: fref,\n    context: context\n  }, props)))];\n}\nconst InstancedAttribute = /*#__PURE__*/React.forwardRef(({\n  name,\n  defaultValue,\n  normalized,\n  usage = THREE.DynamicDrawUsage\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.__r3f.parent.object;\n    parent.geometry.attributes[name] = ref.current;\n    const value = Array.isArray(defaultValue) ? defaultValue : [defaultValue];\n    const array = Array.from({\n      length: parent.userData.limit\n    }, () => value).flat();\n    ref.current.array = new Float32Array(array);\n    ref.current.itemSize = value.length;\n    // @ts-expect-error\n    ref.current.count = array.length / ref.current.itemSize;\n    return () => {\n      delete parent.geometry.attributes[name];\n    };\n  }, [name]);\n  let iterations = 0;\n  useFrame(() => {\n    const parent = ref.current.__r3f.parent.object;\n    if (parent.userData.frames === Infinity || iterations < parent.userData.frames) {\n      for (let i = 0; i < parent.userData.instances.length; i++) {\n        const instance = parent.userData.instances[i].current;\n        const value = instance[name];\n        if (value !== undefined) {\n          ref.current.set(Array.isArray(value) ? value : typeof value.toArray === 'function' ? value.toArray() : [value], i * ref.current.itemSize);\n          ref.current.needsUpdate = true;\n        }\n      }\n      iterations++;\n    }\n  });\n  // @ts-expect-error we're abusing three API here by mutating immutable args\n  return /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    ref: ref,\n    usage: usage,\n    normalized: normalized\n  });\n});\nexport { Instance, InstancedAttribute, Instances, Merged, PositionMesh, createInstances };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useFrame", "setUpdateRange", "isFunctionChild", "value", "_instanceLocalMatrix", "Matrix4", "_instanceWorldMatrix", "_instanceIntersects", "_mesh", "<PERSON><PERSON>", "PositionMesh", "Group", "constructor", "color", "Color", "instance", "current", "undefined", "<PERSON><PERSON><PERSON>", "geometry", "_this$instance$curren", "raycast", "raycaster", "intersects", "parent", "material", "matrixWorld", "instanceId", "userData", "instances", "indexOf", "count", "getMatrixAt", "multiplyMatrices", "Material", "side", "i", "l", "length", "intersect", "object", "push", "globalContext", "createContext", "parentMatrix", "instanceMatrix", "tempMatrix", "translation", "Vector3", "rotation", "Quaternion", "scale", "isInstancedBufferAttribute", "attr", "Instance", "forwardRef", "context", "children", "props", "ref", "useMemo", "group", "useRef", "useImperativeHandle", "subscribe", "getParent", "useContext", "useLayoutEffect", "createElement", "Instances", "range", "limit", "frames", "Infinity", "localContext", "useState", "parentRef", "setInstances", "matrices", "colors", "m<PERSON>rray", "Float32Array", "identity", "toArray", "Array", "map", "useEffect", "needsUpdate", "iterations", "attributes", "Object", "entries", "filter", "_name", "updateMatrix", "updateMatrixWorld", "copy", "invert", "Math", "min", "start", "instanceColor", "decompose", "compose", "premultiply", "api", "item", "matrixAutoUpdate", "args", "attach", "usage", "DynamicDrawUsage", "Provider", "<PERSON>rged", "meshes", "isArray", "key", "keys", "<PERSON><PERSON><PERSON>", "render", "reduce", "acc", "components", "values", "uuid", "renderRecursive", "results", "nextR<PERSON>", "slice", "concat", "cloneElement", "createInstances", "fref", "InstancedAttribute", "name", "defaultValue", "normalized", "__r3f", "array", "from", "flat", "itemSize", "set"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Instances.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { setUpdateRange } from '../helpers/deprecated.js';\n\nfunction isFunctionChild(value) {\n  return typeof value === 'function';\n}\nconst _instanceLocalMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _instanceWorldMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _instanceIntersects = [];\nconst _mesh = /* @__PURE__ */new THREE.Mesh();\nclass PositionMesh extends THREE.Group {\n  constructor() {\n    super();\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  }\n\n  // This will allow the virtual instance have bounds\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n\n  // And this will allow the virtual instance to receive events\n  raycast(raycaster, intersects) {\n    const parent = this.instance.current;\n    if (!parent) return;\n    if (!parent.geometry || !parent.material) return;\n    _mesh.geometry = parent.geometry;\n    const matrixWorld = parent.matrixWorld;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey);\n    // If the instance wasn't found or exceeds the parents draw range, bail out\n    if (instanceId === -1 || instanceId > parent.count) return;\n    // calculate the world matrix for each instance\n    parent.getMatrixAt(instanceId, _instanceLocalMatrix);\n    _instanceWorldMatrix.multiplyMatrices(matrixWorld, _instanceLocalMatrix);\n    // the mesh represents this single instance\n    _mesh.matrixWorld = _instanceWorldMatrix;\n    // raycast side according to instance material\n    if (parent.material instanceof THREE.Material) _mesh.material.side = parent.material.side;else _mesh.material.side = parent.material[0].side;\n    _mesh.raycast(raycaster, _instanceIntersects);\n    // process the result of raycast\n    for (let i = 0, l = _instanceIntersects.length; i < l; i++) {\n      const intersect = _instanceIntersects[i];\n      intersect.instanceId = instanceId;\n      intersect.object = this;\n      intersects.push(intersect);\n    }\n    _instanceIntersects.length = 0;\n  }\n}\nconst globalContext = /* @__PURE__ */React.createContext(null);\nconst parentMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst instanceMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst tempMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst translation = /* @__PURE__ */new THREE.Vector3();\nconst rotation = /* @__PURE__ */new THREE.Quaternion();\nconst scale = /* @__PURE__ */new THREE.Vector3();\nconst isInstancedBufferAttribute = attr => attr.isInstancedBufferAttribute;\nconst Instance = /* @__PURE__ */React.forwardRef(({\n  context,\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionMesh\n  }), []);\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context || globalContext);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionMesh\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: group\n  }, props), children);\n});\nconst Instances = /* @__PURE__ */React.forwardRef(({\n  context,\n  children,\n  range,\n  limit = 1000,\n  frames = Infinity,\n  ...props\n}, ref) => {\n  const [{\n    localContext,\n    instance\n  }] = React.useState(() => {\n    const localContext = /*#__PURE__*/React.createContext(null);\n    return {\n      localContext,\n      instance: /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(Instance, _extends({\n        context: localContext\n      }, props, {\n        ref: ref\n      })))\n    };\n  });\n  const parentRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => parentRef.current, []);\n  const [instances, setInstances] = React.useState([]);\n  const [[matrices, colors]] = React.useState(() => {\n    const mArray = new Float32Array(limit * 16);\n    for (let i = 0; i < limit; i++) tempMatrix.identity().toArray(mArray, i * 16);\n    return [mArray, new Float32Array([...new Array(limit * 3)].map(() => 1))];\n  });\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.instanceMatrix.needsUpdate = true;\n  });\n  let iterations = 0;\n  let count = 0;\n  const attributes = React.useRef([]);\n  React.useLayoutEffect(() => {\n    attributes.current = Object.entries(parentRef.current.geometry.attributes).filter(([_name, value]) => isInstancedBufferAttribute(value));\n  });\n  useFrame(() => {\n    if (frames === Infinity || iterations < frames) {\n      parentRef.current.updateMatrix();\n      parentRef.current.updateMatrixWorld();\n      parentMatrix.copy(parentRef.current.matrixWorld).invert();\n      count = Math.min(limit, range !== undefined ? range : limit, instances.length);\n      parentRef.current.count = count;\n      setUpdateRange(parentRef.current.instanceMatrix, {\n        start: 0,\n        count: count * 16\n      });\n      setUpdateRange(parentRef.current.instanceColor, {\n        start: 0,\n        count: count * 3\n      });\n      for (let i = 0; i < instances.length; i++) {\n        const instance = instances[i].current;\n        // Multiply the inverse of the InstancedMesh world matrix or else\n        // Instances will be double-transformed if <Instances> isn't at identity\n        instance.matrixWorld.decompose(translation, rotation, scale);\n        instanceMatrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n        instanceMatrix.toArray(matrices, i * 16);\n        parentRef.current.instanceMatrix.needsUpdate = true;\n        instance.color.toArray(colors, i * 3);\n        parentRef.current.instanceColor.needsUpdate = true;\n      }\n      iterations++;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setInstances(instances => [...instances, ref]);\n      return () => setInstances(instances => instances.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"instancedMesh\", _extends({\n    userData: {\n      instances,\n      limit,\n      frames\n    },\n    matrixAutoUpdate: false,\n    ref: parentRef,\n    args: [null, null, 0],\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceMatrix\",\n    args: [matrices, 16],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceColor\",\n    args: [colors, 3],\n    usage: THREE.DynamicDrawUsage\n  }), isFunctionChild(children) ? /*#__PURE__*/React.createElement(localContext.Provider, {\n    value: api\n  }, children(instance)) : context ? /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children) : /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children));\n});\n// TODO: make this non-recursive and type-safe\nconst Merged = /* @__PURE__ */React.forwardRef(function Merged({\n  meshes,\n  children,\n  ...props\n}, ref) {\n  const isArray = Array.isArray(meshes);\n  // Filter out meshes from collections, which may contain non-meshes\n  // @ts-expect-error\n  if (!isArray) for (const key of Object.keys(meshes)) if (!meshes[key].isMesh) delete meshes[key];\n  const render = args => isArray ?\n  // @ts-expect-error\n  children(...args) : children(\n  // @ts-expect-error\n  Object.keys(meshes)\n  // @ts-expect-error\n  .filter(key => meshes[key].isMesh).reduce((acc, key, i) => ({\n    ...acc,\n    [key]: args[i]\n  }), {}));\n\n  // @ts-expect-error\n  const components = (isArray ? meshes : Object.values(meshes)).map(({\n    geometry,\n    material\n  }) => /*#__PURE__*/React.createElement(Instances, _extends({\n    key: geometry.uuid,\n    geometry: geometry,\n    material: material\n  }, props)));\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, renderRecursive(render, components));\n});\n\n// https://github.com/jamesplease/react-composer\nfunction renderRecursive(render, components, results = []) {\n  // Once components is exhausted, we can render out the results array.\n  if (!components[0]) {\n    return render(results);\n  }\n\n  // Continue recursion for remaining items.\n  // results.concat([value]) ensures [...results, value] instead of [...results, ...value]\n  function nextRender(value) {\n    return renderRecursive(render, components.slice(1), results.concat([value]));\n  }\n\n  // Each props.components entry is either an element or function [element factory]\n  return typeof components[0] === 'function' ?\n  // When it is a function, produce an element by invoking it with \"render component values\".\n  components[0]({\n    results,\n    render: nextRender\n  }) :\n  /*#__PURE__*/\n  // When it is an element, enhance the element's props with the render prop.\n  React.cloneElement(components[0], {\n    children: nextRender\n  });\n}\n\n/** Idea and implementation for global instances and instanced attributes by\n/*  Matias Gonzalez Fernandez https://x.com/matiNotFound\n/*  and Paul Henschel https://x.com/0xca0a\n*/\nfunction createInstances() {\n  const context = /*#__PURE__*/React.createContext(null);\n  return [/*#__PURE__*/React.forwardRef((props, fref) => /*#__PURE__*/React.createElement(Instances, _extends({\n    ref: fref,\n    context: context\n  }, props))), /*#__PURE__*/React.forwardRef((props, fref) => /*#__PURE__*/React.createElement(Instance, _extends({\n    ref: fref,\n    context: context\n  }, props)))];\n}\nconst InstancedAttribute = /*#__PURE__*/React.forwardRef(({\n  name,\n  defaultValue,\n  normalized,\n  usage = THREE.DynamicDrawUsage\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.__r3f.parent.object;\n    parent.geometry.attributes[name] = ref.current;\n    const value = Array.isArray(defaultValue) ? defaultValue : [defaultValue];\n    const array = Array.from({\n      length: parent.userData.limit\n    }, () => value).flat();\n    ref.current.array = new Float32Array(array);\n    ref.current.itemSize = value.length;\n    // @ts-expect-error\n    ref.current.count = array.length / ref.current.itemSize;\n    return () => {\n      delete parent.geometry.attributes[name];\n    };\n  }, [name]);\n  let iterations = 0;\n  useFrame(() => {\n    const parent = ref.current.__r3f.parent.object;\n    if (parent.userData.frames === Infinity || iterations < parent.userData.frames) {\n      for (let i = 0; i < parent.userData.instances.length; i++) {\n        const instance = parent.userData.instances[i].current;\n        const value = instance[name];\n        if (value !== undefined) {\n          ref.current.set(Array.isArray(value) ? value : typeof value.toArray === 'function' ? value.toArray() : [value], i * ref.current.itemSize);\n          ref.current.needsUpdate = true;\n        }\n      }\n      iterations++;\n    }\n  });\n  // @ts-expect-error we're abusing three API here by mutating immutable args\n  return /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    ref: ref,\n    usage: usage,\n    normalized: normalized\n  });\n});\n\nexport { Instance, InstancedAttribute, Instances, Merged, PositionMesh, createInstances };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,0BAA0B;AAEzD,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;AACA,MAAMC,oBAAoB,GAAG,eAAe,IAAIP,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC/D,MAAMC,oBAAoB,GAAG,eAAe,IAAIT,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC/D,MAAME,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,KAAK,GAAG,eAAe,IAAIX,KAAK,CAACY,IAAI,CAAC,CAAC;AAC7C,MAAMC,YAAY,SAASb,KAAK,CAACc,KAAK,CAAC;EACrCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,IAAIhB,KAAK,CAACiB,KAAK,CAAC,OAAO,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG;MACdC,OAAO,EAAEC;IACX,CAAC;IACD,IAAI,CAACC,WAAW,GAAG;MACjBF,OAAO,EAAEC;IACX,CAAC;EACH;;EAEA;EACA,IAAIE,QAAQA,CAAA,EAAG;IACb,IAAIC,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAG,IAAI,CAACL,QAAQ,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,qBAAqB,CAACD,QAAQ;EAC1G;;EAEA;EACAE,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;IAC7B,MAAMC,MAAM,GAAG,IAAI,CAACT,QAAQ,CAACC,OAAO;IACpC,IAAI,CAACQ,MAAM,EAAE;IACb,IAAI,CAACA,MAAM,CAACL,QAAQ,IAAI,CAACK,MAAM,CAACC,QAAQ,EAAE;IAC1CjB,KAAK,CAACW,QAAQ,GAAGK,MAAM,CAACL,QAAQ;IAChC,MAAMO,WAAW,GAAGF,MAAM,CAACE,WAAW;IACtC,MAAMC,UAAU,GAAGH,MAAM,CAACI,QAAQ,CAACC,SAAS,CAACC,OAAO,CAAC,IAAI,CAACZ,WAAW,CAAC;IACtE;IACA,IAAIS,UAAU,KAAK,CAAC,CAAC,IAAIA,UAAU,GAAGH,MAAM,CAACO,KAAK,EAAE;IACpD;IACAP,MAAM,CAACQ,WAAW,CAACL,UAAU,EAAEvB,oBAAoB,CAAC;IACpDE,oBAAoB,CAAC2B,gBAAgB,CAACP,WAAW,EAAEtB,oBAAoB,CAAC;IACxE;IACAI,KAAK,CAACkB,WAAW,GAAGpB,oBAAoB;IACxC;IACA,IAAIkB,MAAM,CAACC,QAAQ,YAAY5B,KAAK,CAACqC,QAAQ,EAAE1B,KAAK,CAACiB,QAAQ,CAACU,IAAI,GAAGX,MAAM,CAACC,QAAQ,CAACU,IAAI,CAAC,KAAK3B,KAAK,CAACiB,QAAQ,CAACU,IAAI,GAAGX,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACU,IAAI;IAC5I3B,KAAK,CAACa,OAAO,CAACC,SAAS,EAAEf,mBAAmB,CAAC;IAC7C;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG9B,mBAAmB,CAAC+B,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC1D,MAAMG,SAAS,GAAGhC,mBAAmB,CAAC6B,CAAC,CAAC;MACxCG,SAAS,CAACZ,UAAU,GAAGA,UAAU;MACjCY,SAAS,CAACC,MAAM,GAAG,IAAI;MACvBjB,UAAU,CAACkB,IAAI,CAACF,SAAS,CAAC;IAC5B;IACAhC,mBAAmB,CAAC+B,MAAM,GAAG,CAAC;EAChC;AACF;AACA,MAAMI,aAAa,GAAG,eAAe5C,KAAK,CAAC6C,aAAa,CAAC,IAAI,CAAC;AAC9D,MAAMC,YAAY,GAAG,eAAe,IAAI/C,KAAK,CAACQ,OAAO,CAAC,CAAC;AACvD,MAAMwC,cAAc,GAAG,eAAe,IAAIhD,KAAK,CAACQ,OAAO,CAAC,CAAC;AACzD,MAAMyC,UAAU,GAAG,eAAe,IAAIjD,KAAK,CAACQ,OAAO,CAAC,CAAC;AACrD,MAAM0C,WAAW,GAAG,eAAe,IAAIlD,KAAK,CAACmD,OAAO,CAAC,CAAC;AACtD,MAAMC,QAAQ,GAAG,eAAe,IAAIpD,KAAK,CAACqD,UAAU,CAAC,CAAC;AACtD,MAAMC,KAAK,GAAG,eAAe,IAAItD,KAAK,CAACmD,OAAO,CAAC,CAAC;AAChD,MAAMI,0BAA0B,GAAGC,IAAI,IAAIA,IAAI,CAACD,0BAA0B;AAC1E,MAAME,QAAQ,GAAG,eAAexD,KAAK,CAACyD,UAAU,CAAC,CAAC;EAChDC,OAAO;EACPC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT7D,KAAK,CAAC8D,OAAO,CAAC,MAAM7D,MAAM,CAAC;IACzBW;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMmD,KAAK,GAAG/D,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EAChChE,KAAK,CAACiE,mBAAmB,CAACJ,GAAG,EAAE,MAAME,KAAK,CAAC7C,OAAO,EAAE,EAAE,CAAC;EACvD,MAAM;IACJgD,SAAS;IACTC;EACF,CAAC,GAAGnE,KAAK,CAACoE,UAAU,CAACV,OAAO,IAAId,aAAa,CAAC;EAC9C5C,KAAK,CAACqE,eAAe,CAAC,MAAMH,SAAS,CAACH,KAAK,CAAC,EAAE,EAAE,CAAC;EACjD,OAAO,aAAa/D,KAAK,CAACsE,aAAa,CAAC,cAAc,EAAExE,QAAQ,CAAC;IAC/DmB,QAAQ,EAAEkD,SAAS,CAAC,CAAC;IACrB/C,WAAW,EAAE2C,KAAK;IAClBF,GAAG,EAAEE;EACP,CAAC,EAAEH,KAAK,CAAC,EAAED,QAAQ,CAAC;AACtB,CAAC,CAAC;AACF,MAAMY,SAAS,GAAG,eAAevE,KAAK,CAACyD,UAAU,CAAC,CAAC;EACjDC,OAAO;EACPC,QAAQ;EACRa,KAAK;EACLC,KAAK,GAAG,IAAI;EACZC,MAAM,GAAGC,QAAQ;EACjB,GAAGf;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAAC;IACLe,YAAY;IACZ3D;EACF,CAAC,CAAC,GAAGjB,KAAK,CAAC6E,QAAQ,CAAC,MAAM;IACxB,MAAMD,YAAY,GAAG,aAAa5E,KAAK,CAAC6C,aAAa,CAAC,IAAI,CAAC;IAC3D,OAAO;MACL+B,YAAY;MACZ3D,QAAQ,EAAE,aAAajB,KAAK,CAACyD,UAAU,CAAC,CAACG,KAAK,EAAEC,GAAG,KAAK,aAAa7D,KAAK,CAACsE,aAAa,CAACd,QAAQ,EAAE1D,QAAQ,CAAC;QAC1G4D,OAAO,EAAEkB;MACX,CAAC,EAAEhB,KAAK,EAAE;QACRC,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;IACL,CAAC;EACH,CAAC,CAAC;EACF,MAAMiB,SAAS,GAAG9E,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EACpChE,KAAK,CAACiE,mBAAmB,CAACJ,GAAG,EAAE,MAAMiB,SAAS,CAAC5D,OAAO,EAAE,EAAE,CAAC;EAC3D,MAAM,CAACa,SAAS,EAAEgD,YAAY,CAAC,GAAG/E,KAAK,CAAC6E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC,CAACG,QAAQ,EAAEC,MAAM,CAAC,CAAC,GAAGjF,KAAK,CAAC6E,QAAQ,CAAC,MAAM;IAChD,MAAMK,MAAM,GAAG,IAAIC,YAAY,CAACV,KAAK,GAAG,EAAE,CAAC;IAC3C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,KAAK,EAAEnC,CAAC,EAAE,EAAEU,UAAU,CAACoC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACH,MAAM,EAAE5C,CAAC,GAAG,EAAE,CAAC;IAC7E,OAAO,CAAC4C,MAAM,EAAE,IAAIC,YAAY,CAAC,CAAC,GAAG,IAAIG,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC,CAAC,CAACc,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3E,CAAC,CAAC;EACFvF,KAAK,CAACwF,SAAS,CAAC,MAAM;IACpB;IACAV,SAAS,CAAC5D,OAAO,CAAC6B,cAAc,CAAC0C,WAAW,GAAG,IAAI;EACrD,CAAC,CAAC;EACF,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIzD,KAAK,GAAG,CAAC;EACb,MAAM0D,UAAU,GAAG3F,KAAK,CAACgE,MAAM,CAAC,EAAE,CAAC;EACnChE,KAAK,CAACqE,eAAe,CAAC,MAAM;IAC1BsB,UAAU,CAACzE,OAAO,GAAG0E,MAAM,CAACC,OAAO,CAACf,SAAS,CAAC5D,OAAO,CAACG,QAAQ,CAACsE,UAAU,CAAC,CAACG,MAAM,CAAC,CAAC,CAACC,KAAK,EAAE1F,KAAK,CAAC,KAAKiD,0BAA0B,CAACjD,KAAK,CAAC,CAAC;EAC1I,CAAC,CAAC;EACFH,QAAQ,CAAC,MAAM;IACb,IAAIwE,MAAM,KAAKC,QAAQ,IAAIe,UAAU,GAAGhB,MAAM,EAAE;MAC9CI,SAAS,CAAC5D,OAAO,CAAC8E,YAAY,CAAC,CAAC;MAChClB,SAAS,CAAC5D,OAAO,CAAC+E,iBAAiB,CAAC,CAAC;MACrCnD,YAAY,CAACoD,IAAI,CAACpB,SAAS,CAAC5D,OAAO,CAACU,WAAW,CAAC,CAACuE,MAAM,CAAC,CAAC;MACzDlE,KAAK,GAAGmE,IAAI,CAACC,GAAG,CAAC5B,KAAK,EAAED,KAAK,KAAKrD,SAAS,GAAGqD,KAAK,GAAGC,KAAK,EAAE1C,SAAS,CAACS,MAAM,CAAC;MAC9EsC,SAAS,CAAC5D,OAAO,CAACe,KAAK,GAAGA,KAAK;MAC/B9B,cAAc,CAAC2E,SAAS,CAAC5D,OAAO,CAAC6B,cAAc,EAAE;QAC/CuD,KAAK,EAAE,CAAC;QACRrE,KAAK,EAAEA,KAAK,GAAG;MACjB,CAAC,CAAC;MACF9B,cAAc,CAAC2E,SAAS,CAAC5D,OAAO,CAACqF,aAAa,EAAE;QAC9CD,KAAK,EAAE,CAAC;QACRrE,KAAK,EAAEA,KAAK,GAAG;MACjB,CAAC,CAAC;MACF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,CAACS,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzC,MAAMrB,QAAQ,GAAGc,SAAS,CAACO,CAAC,CAAC,CAACpB,OAAO;QACrC;QACA;QACAD,QAAQ,CAACW,WAAW,CAAC4E,SAAS,CAACvD,WAAW,EAAEE,QAAQ,EAAEE,KAAK,CAAC;QAC5DN,cAAc,CAAC0D,OAAO,CAACxD,WAAW,EAAEE,QAAQ,EAAEE,KAAK,CAAC,CAACqD,WAAW,CAAC5D,YAAY,CAAC;QAC9EC,cAAc,CAACsC,OAAO,CAACL,QAAQ,EAAE1C,CAAC,GAAG,EAAE,CAAC;QACxCwC,SAAS,CAAC5D,OAAO,CAAC6B,cAAc,CAAC0C,WAAW,GAAG,IAAI;QACnDxE,QAAQ,CAACF,KAAK,CAACsE,OAAO,CAACJ,MAAM,EAAE3C,CAAC,GAAG,CAAC,CAAC;QACrCwC,SAAS,CAAC5D,OAAO,CAACqF,aAAa,CAACd,WAAW,GAAG,IAAI;MACpD;MACAC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EACF,MAAMiB,GAAG,GAAG3G,KAAK,CAAC8D,OAAO,CAAC,OAAO;IAC/BK,SAAS,EAAEA,CAAA,KAAMW,SAAS;IAC1BZ,SAAS,EAAEL,GAAG,IAAI;MAChBkB,YAAY,CAAChD,SAAS,IAAI,CAAC,GAAGA,SAAS,EAAE8B,GAAG,CAAC,CAAC;MAC9C,OAAO,MAAMkB,YAAY,CAAChD,SAAS,IAAIA,SAAS,CAAC+D,MAAM,CAACc,IAAI,IAAIA,IAAI,CAAC1F,OAAO,KAAK2C,GAAG,CAAC3C,OAAO,CAAC,CAAC;IAChG;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAalB,KAAK,CAACsE,aAAa,CAAC,eAAe,EAAExE,QAAQ,CAAC;IAChEgC,QAAQ,EAAE;MACRC,SAAS;MACT0C,KAAK;MACLC;IACF,CAAC;IACDmC,gBAAgB,EAAE,KAAK;IACvBhD,GAAG,EAAEiB,SAAS;IACdgC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IACrBvF,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEqC,KAAK,CAAC,EAAE,aAAa5D,KAAK,CAACsE,aAAa,CAAC,0BAA0B,EAAE;IACtEyC,MAAM,EAAE,gBAAgB;IACxBD,IAAI,EAAE,CAAC9B,QAAQ,EAAE,EAAE,CAAC;IACpBgC,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAE,aAAajH,KAAK,CAACsE,aAAa,CAAC,0BAA0B,EAAE;IAC/DyC,MAAM,EAAE,eAAe;IACvBD,IAAI,EAAE,CAAC7B,MAAM,EAAE,CAAC,CAAC;IACjB+B,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAE7G,eAAe,CAACuD,QAAQ,CAAC,GAAG,aAAa3D,KAAK,CAACsE,aAAa,CAACM,YAAY,CAACsC,QAAQ,EAAE;IACtF7G,KAAK,EAAEsG;EACT,CAAC,EAAEhD,QAAQ,CAAC1C,QAAQ,CAAC,CAAC,GAAGyC,OAAO,GAAG,aAAa1D,KAAK,CAACsE,aAAa,CAACZ,OAAO,CAACwD,QAAQ,EAAE;IACpF7G,KAAK,EAAEsG;EACT,CAAC,EAAEhD,QAAQ,CAAC,GAAG,aAAa3D,KAAK,CAACsE,aAAa,CAAC1B,aAAa,CAACsE,QAAQ,EAAE;IACtE7G,KAAK,EAAEsG;EACT,CAAC,EAAEhD,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF;AACA,MAAMwD,MAAM,GAAG,eAAenH,KAAK,CAACyD,UAAU,CAAC,SAAS0D,MAAMA,CAAC;EAC7DC,MAAM;EACNzD,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMwD,OAAO,GAAG/B,KAAK,CAAC+B,OAAO,CAACD,MAAM,CAAC;EACrC;EACA;EACA,IAAI,CAACC,OAAO,EAAE,KAAK,MAAMC,GAAG,IAAI1B,MAAM,CAAC2B,IAAI,CAACH,MAAM,CAAC,EAAE,IAAI,CAACA,MAAM,CAACE,GAAG,CAAC,CAACE,MAAM,EAAE,OAAOJ,MAAM,CAACE,GAAG,CAAC;EAChG,MAAMG,MAAM,GAAGX,IAAI,IAAIO,OAAO;EAC9B;EACA1D,QAAQ,CAAC,GAAGmD,IAAI,CAAC,GAAGnD,QAAQ;EAC5B;EACAiC,MAAM,CAAC2B,IAAI,CAACH,MAAM;EAClB;EAAA,CACCtB,MAAM,CAACwB,GAAG,IAAIF,MAAM,CAACE,GAAG,CAAC,CAACE,MAAM,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEL,GAAG,EAAEhF,CAAC,MAAM;IAC1D,GAAGqF,GAAG;IACN,CAACL,GAAG,GAAGR,IAAI,CAACxE,CAAC;EACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAER;EACA,MAAMsF,UAAU,GAAG,CAACP,OAAO,GAAGD,MAAM,GAAGxB,MAAM,CAACiC,MAAM,CAACT,MAAM,CAAC,EAAE7B,GAAG,CAAC,CAAC;IACjElE,QAAQ;IACRM;EACF,CAAC,KAAK,aAAa3B,KAAK,CAACsE,aAAa,CAACC,SAAS,EAAEzE,QAAQ,CAAC;IACzDwH,GAAG,EAAEjG,QAAQ,CAACyG,IAAI;IAClBzG,QAAQ,EAAEA,QAAQ;IAClBM,QAAQ,EAAEA;EACZ,CAAC,EAAEiC,KAAK,CAAC,CAAC,CAAC;EACX,OAAO,aAAa5D,KAAK,CAACsE,aAAa,CAAC,OAAO,EAAE;IAC/CT,GAAG,EAAEA;EACP,CAAC,EAAEkE,eAAe,CAACN,MAAM,EAAEG,UAAU,CAAC,CAAC;AACzC,CAAC,CAAC;;AAEF;AACA,SAASG,eAAeA,CAACN,MAAM,EAAEG,UAAU,EAAEI,OAAO,GAAG,EAAE,EAAE;EACzD;EACA,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,EAAE;IAClB,OAAOH,MAAM,CAACO,OAAO,CAAC;EACxB;;EAEA;EACA;EACA,SAASC,UAAUA,CAAC5H,KAAK,EAAE;IACzB,OAAO0H,eAAe,CAACN,MAAM,EAAEG,UAAU,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEF,OAAO,CAACG,MAAM,CAAC,CAAC9H,KAAK,CAAC,CAAC,CAAC;EAC9E;;EAEA;EACA,OAAO,OAAOuH,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU;EAC1C;EACAA,UAAU,CAAC,CAAC,CAAC,CAAC;IACZI,OAAO;IACPP,MAAM,EAAEQ;EACV,CAAC,CAAC,GACF;EACA;EACAjI,KAAK,CAACoI,YAAY,CAACR,UAAU,CAAC,CAAC,CAAC,EAAE;IAChCjE,QAAQ,EAAEsE;EACZ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAAA,EAAG;EACzB,MAAM3E,OAAO,GAAG,aAAa1D,KAAK,CAAC6C,aAAa,CAAC,IAAI,CAAC;EACtD,OAAO,CAAC,aAAa7C,KAAK,CAACyD,UAAU,CAAC,CAACG,KAAK,EAAE0E,IAAI,KAAK,aAAatI,KAAK,CAACsE,aAAa,CAACC,SAAS,EAAEzE,QAAQ,CAAC;IAC1G+D,GAAG,EAAEyE,IAAI;IACT5E,OAAO,EAAEA;EACX,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa5D,KAAK,CAACyD,UAAU,CAAC,CAACG,KAAK,EAAE0E,IAAI,KAAK,aAAatI,KAAK,CAACsE,aAAa,CAACd,QAAQ,EAAE1D,QAAQ,CAAC;IAC9G+D,GAAG,EAAEyE,IAAI;IACT5E,OAAO,EAAEA;EACX,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,CAAC;AACd;AACA,MAAM2E,kBAAkB,GAAG,aAAavI,KAAK,CAACyD,UAAU,CAAC,CAAC;EACxD+E,IAAI;EACJC,YAAY;EACZC,UAAU;EACV1B,KAAK,GAAGjH,KAAK,CAACkH;AAChB,CAAC,EAAEqB,IAAI,KAAK;EACV,MAAMzE,GAAG,GAAG7D,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EAC9BhE,KAAK,CAACiE,mBAAmB,CAACqE,IAAI,EAAE,MAAMzE,GAAG,CAAC3C,OAAO,EAAE,EAAE,CAAC;EACtDlB,KAAK,CAACqE,eAAe,CAAC,MAAM;IAC1B,MAAM3C,MAAM,GAAGmC,GAAG,CAAC3C,OAAO,CAACyH,KAAK,CAACjH,MAAM,CAACgB,MAAM;IAC9ChB,MAAM,CAACL,QAAQ,CAACsE,UAAU,CAAC6C,IAAI,CAAC,GAAG3E,GAAG,CAAC3C,OAAO;IAC9C,MAAMb,KAAK,GAAGiF,KAAK,CAAC+B,OAAO,CAACoB,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IACzE,MAAMG,KAAK,GAAGtD,KAAK,CAACuD,IAAI,CAAC;MACvBrG,MAAM,EAAEd,MAAM,CAACI,QAAQ,CAAC2C;IAC1B,CAAC,EAAE,MAAMpE,KAAK,CAAC,CAACyI,IAAI,CAAC,CAAC;IACtBjF,GAAG,CAAC3C,OAAO,CAAC0H,KAAK,GAAG,IAAIzD,YAAY,CAACyD,KAAK,CAAC;IAC3C/E,GAAG,CAAC3C,OAAO,CAAC6H,QAAQ,GAAG1I,KAAK,CAACmC,MAAM;IACnC;IACAqB,GAAG,CAAC3C,OAAO,CAACe,KAAK,GAAG2G,KAAK,CAACpG,MAAM,GAAGqB,GAAG,CAAC3C,OAAO,CAAC6H,QAAQ;IACvD,OAAO,MAAM;MACX,OAAOrH,MAAM,CAACL,QAAQ,CAACsE,UAAU,CAAC6C,IAAI,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,IAAI9C,UAAU,GAAG,CAAC;EAClBxF,QAAQ,CAAC,MAAM;IACb,MAAMwB,MAAM,GAAGmC,GAAG,CAAC3C,OAAO,CAACyH,KAAK,CAACjH,MAAM,CAACgB,MAAM;IAC9C,IAAIhB,MAAM,CAACI,QAAQ,CAAC4C,MAAM,KAAKC,QAAQ,IAAIe,UAAU,GAAGhE,MAAM,CAACI,QAAQ,CAAC4C,MAAM,EAAE;MAC9E,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACI,QAAQ,CAACC,SAAS,CAACS,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzD,MAAMrB,QAAQ,GAAGS,MAAM,CAACI,QAAQ,CAACC,SAAS,CAACO,CAAC,CAAC,CAACpB,OAAO;QACrD,MAAMb,KAAK,GAAGY,QAAQ,CAACuH,IAAI,CAAC;QAC5B,IAAInI,KAAK,KAAKc,SAAS,EAAE;UACvB0C,GAAG,CAAC3C,OAAO,CAAC8H,GAAG,CAAC1D,KAAK,CAAC+B,OAAO,CAAChH,KAAK,CAAC,GAAGA,KAAK,GAAG,OAAOA,KAAK,CAACgF,OAAO,KAAK,UAAU,GAAGhF,KAAK,CAACgF,OAAO,CAAC,CAAC,GAAG,CAAChF,KAAK,CAAC,EAAEiC,CAAC,GAAGuB,GAAG,CAAC3C,OAAO,CAAC6H,QAAQ,CAAC;UACzIlF,GAAG,CAAC3C,OAAO,CAACuE,WAAW,GAAG,IAAI;QAChC;MACF;MACAC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EACF;EACA,OAAO,aAAa1F,KAAK,CAACsE,aAAa,CAAC,0BAA0B,EAAE;IAClET,GAAG,EAAEA,GAAG;IACRmD,KAAK,EAAEA,KAAK;IACZ0B,UAAU,EAAEA;EACd,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAASlF,QAAQ,EAAE+E,kBAAkB,EAAEhE,SAAS,EAAE4C,MAAM,EAAEvG,YAAY,EAAEyH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}