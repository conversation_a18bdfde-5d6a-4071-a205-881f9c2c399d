{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useRef } from 'react';\nimport { Vector3 } from 'three';\nimport { calculateScaleFactor } from './calculateScaleFactor.js';\nconst worldPos = /* @__PURE__ */new Vector3();\n/**\n * Wraps children in an `Object3D` and attempts to scale from\n * world units to screen units * scale factor.\n *\n * For example, this will render a box of roughly 1x1 pixel size,\n * independently of how far the camera is.\n *\n * ```jsx\n * <ScreenSizer>\n *   <Box />\n * </ScreenSizer>\n * ```\n */\nconst ScreenSizer = /* @__PURE__ */forwardRef(({\n  scale = 1,\n  ...props\n}, ref) => {\n  const container = useRef(null);\n  React.useImperativeHandle(ref, () => container.current, []);\n  useFrame(state => {\n    const obj = container.current;\n    if (!obj) return;\n    const sf = calculateScaleFactor(obj.getWorldPosition(worldPos), scale, state.camera, state.size);\n    obj.scale.setScalar(sf * scale);\n  });\n  return /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: container\n  }, props));\n});\nexport { ScreenSizer };", "map": {"version": 3, "names": ["_extends", "useFrame", "React", "forwardRef", "useRef", "Vector3", "calculateScaleFactor", "worldPos", "ScreenSizer", "scale", "props", "ref", "container", "useImperativeHandle", "current", "state", "obj", "sf", "getWorldPosition", "camera", "size", "setScalar", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/ScreenSizer.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useRef } from 'react';\nimport { Vector3 } from 'three';\nimport { calculateScaleFactor } from './calculateScaleFactor.js';\n\nconst worldPos = /* @__PURE__ */new Vector3();\n/**\n * Wraps children in an `Object3D` and attempts to scale from\n * world units to screen units * scale factor.\n *\n * For example, this will render a box of roughly 1x1 pixel size,\n * independently of how far the camera is.\n *\n * ```jsx\n * <ScreenSizer>\n *   <Box />\n * </ScreenSizer>\n * ```\n */\nconst ScreenSizer = /* @__PURE__ */forwardRef(({\n  scale = 1,\n  ...props\n}, ref) => {\n  const container = useRef(null);\n  React.useImperativeHandle(ref, () => container.current, []);\n  useFrame(state => {\n    const obj = container.current;\n    if (!obj) return;\n    const sf = calculateScaleFactor(obj.getWorldPosition(worldPos), scale, state.camera, state.size);\n    obj.scale.setScalar(sf * scale);\n  });\n  return /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: container\n  }, props));\n});\n\nexport { ScreenSizer };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC1C,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,MAAMC,QAAQ,GAAG,eAAe,IAAIF,OAAO,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,GAAG,eAAeL,UAAU,CAAC,CAAC;EAC7CM,KAAK,GAAG,CAAC;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC9BF,KAAK,CAACW,mBAAmB,CAACF,GAAG,EAAE,MAAMC,SAAS,CAACE,OAAO,EAAE,EAAE,CAAC;EAC3Db,QAAQ,CAACc,KAAK,IAAI;IAChB,MAAMC,GAAG,GAAGJ,SAAS,CAACE,OAAO;IAC7B,IAAI,CAACE,GAAG,EAAE;IACV,MAAMC,EAAE,GAAGX,oBAAoB,CAACU,GAAG,CAACE,gBAAgB,CAACX,QAAQ,CAAC,EAAEE,KAAK,EAAEM,KAAK,CAACI,MAAM,EAAEJ,KAAK,CAACK,IAAI,CAAC;IAChGJ,GAAG,CAACP,KAAK,CAACY,SAAS,CAACJ,EAAE,GAAGR,KAAK,CAAC;EACjC,CAAC,CAAC;EACF,OAAO,aAAaP,KAAK,CAACoB,aAAa,CAAC,UAAU,EAAEtB,QAAQ,CAAC;IAC3DW,GAAG,EAAEC;EACP,CAAC,EAAEF,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}