{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useInstanceHandle, useFrame } from '@react-three/fiber';\nimport { misc, easing } from 'maath';\nconst isObject3DRef = ref => (ref == null ? void 0 : ref.current) instanceof THREE.Object3D;\nconst MotionContext = /* @__PURE__ */React.createContext(null);\nfunction useMotion() {\n  const context = React.useContext(MotionContext);\n  if (!context) throw new Error('useMotion hook must be used in a MotionPathControls component.');\n  return context;\n}\nfunction Debug({\n  points = 50,\n  color = 'black'\n}) {\n  const {\n    path\n  } = useMotion();\n  const [dots, setDots] = React.useState([]);\n  const material = React.useMemo(() => new THREE.MeshBasicMaterial({\n    color: color\n  }), [color]);\n  const geometry = React.useMemo(() => new THREE.SphereGeometry(0.025, 16, 16), []);\n  const last = React.useRef([]);\n  React.useEffect(() => {\n    if (path.curves !== last.current) {\n      setDots(path.getPoints(points));\n      last.current = path.curves;\n    }\n  });\n  return dots.map((item, index) => /*#__PURE__*/React.createElement(\"mesh\", {\n    key: index,\n    material: material,\n    geometry: geometry,\n    position: [item.x, item.y, item.z]\n  }));\n}\nconst MotionPathControls = /* @__PURE__ */React.forwardRef(({\n  children,\n  curves = [],\n  debug = false,\n  debugColor = 'black',\n  object,\n  focus,\n  loop = true,\n  offset = undefined,\n  smooth = false,\n  eps = 0.00001,\n  damping = 0.1,\n  focusDamping = 0.1,\n  maxSpeed = Infinity,\n  ...props\n}, fref) => {\n  const {\n    camera\n  } = useThree();\n  const ref = React.useRef(null);\n  const pos = React.useRef(offset !== null && offset !== void 0 ? offset : 0);\n  const path = React.useMemo(() => new THREE.CurvePath(), []);\n  const state = React.useMemo(() => ({\n    focus,\n    object: (object == null ? void 0 : object.current) instanceof THREE.Object3D ? object : {\n      current: camera\n    },\n    path,\n    current: pos.current,\n    offset: pos.current,\n    point: new THREE.Vector3(),\n    tangent: new THREE.Vector3(),\n    next: new THREE.Vector3()\n  }), [focus, object]);\n  const instanceRef = useInstanceHandle(ref);\n  React.useLayoutEffect(() => {\n    const instance = instanceRef.current;\n    path.curves = [];\n    const _curves = curves.length > 0 ? curves : instance.children.map(instance => instance.object);\n    for (let i = 0; i < _curves.length; i++) path.add(_curves[i]);\n\n    // Smoothen curve\n    if (smooth) {\n      const points = path.getPoints(typeof smooth === 'number' ? smooth : 1);\n      const catmull = new THREE.CatmullRomCurve3(points);\n      path.curves = [catmull];\n    }\n    path.updateArcLengths();\n  });\n  React.useImperativeHandle(fref, () => Object.assign(ref.current, {\n    motion: state\n  }), [state]);\n  React.useLayoutEffect(() => {\n    // When offset changes, normalise pos to avoid overshoot spinning\n    pos.current = misc.repeat(pos.current, 1);\n  }, [offset]);\n  const vec = React.useMemo(() => new THREE.Vector3(), []);\n  useFrame((_state, delta) => {\n    const lastOffset = state.offset;\n    easing.damp(pos, 'current', offset !== undefined ? offset : state.current, damping, delta, maxSpeed, undefined, eps);\n    state.offset = loop ? misc.repeat(pos.current, 1) : misc.clamp(pos.current, 0, 1);\n    if (path.getCurveLengths().length > 0) {\n      path.getPointAt(state.offset, state.point);\n      path.getTangentAt(state.offset, state.tangent).normalize();\n      path.getPointAt(misc.repeat(pos.current - (lastOffset - state.offset), 1), state.next);\n      const target = (object == null ? void 0 : object.current) instanceof THREE.Object3D ? object.current : camera;\n      target.position.copy(state.point);\n      if (focus) {\n        easing.dampLookAt(target, isObject3DRef(focus) ? focus.current.getWorldPosition(vec) : focus, focusDamping, delta, maxSpeed, undefined, eps);\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(MotionContext.Provider, {\n    value: state\n  }, children, debug && /*#__PURE__*/React.createElement(Debug, {\n    color: debugColor\n  })));\n});\nexport { MotionPathControls, useMotion };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "useInstanceHandle", "useFrame", "misc", "easing", "isObject3DRef", "ref", "current", "Object3D", "MotionContext", "createContext", "useMotion", "context", "useContext", "Error", "Debug", "points", "color", "path", "dots", "setDots", "useState", "material", "useMemo", "MeshBasicMaterial", "geometry", "SphereGeometry", "last", "useRef", "useEffect", "curves", "getPoints", "map", "item", "index", "createElement", "key", "position", "x", "y", "z", "MotionPathControls", "forwardRef", "children", "debug", "debugColor", "object", "focus", "loop", "offset", "undefined", "smooth", "eps", "damping", "focusDamping", "maxSpeed", "Infinity", "props", "fref", "camera", "pos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "point", "Vector3", "tangent", "next", "instanceRef", "useLayoutEffect", "instance", "_curves", "length", "i", "add", "catmull", "CatmullRomCurve3", "updateArcLengths", "useImperativeHandle", "Object", "assign", "motion", "repeat", "vec", "_state", "delta", "lastOffset", "damp", "clamp", "getCurveLengths", "getPointAt", "getTangentAt", "normalize", "target", "copy", "dampLookAt", "getWorldPosition", "Provider", "value"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MotionPathControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useInstanceHandle, useFrame } from '@react-three/fiber';\nimport { misc, easing } from 'maath';\n\nconst isObject3DRef = ref => (ref == null ? void 0 : ref.current) instanceof THREE.Object3D;\nconst MotionContext = /* @__PURE__ */React.createContext(null);\nfunction useMotion() {\n  const context = React.useContext(MotionContext);\n  if (!context) throw new Error('useMotion hook must be used in a MotionPathControls component.');\n  return context;\n}\nfunction Debug({\n  points = 50,\n  color = 'black'\n}) {\n  const {\n    path\n  } = useMotion();\n  const [dots, setDots] = React.useState([]);\n  const material = React.useMemo(() => new THREE.MeshBasicMaterial({\n    color: color\n  }), [color]);\n  const geometry = React.useMemo(() => new THREE.SphereGeometry(0.025, 16, 16), []);\n  const last = React.useRef([]);\n  React.useEffect(() => {\n    if (path.curves !== last.current) {\n      setDots(path.getPoints(points));\n      last.current = path.curves;\n    }\n  });\n  return dots.map((item, index) => /*#__PURE__*/React.createElement(\"mesh\", {\n    key: index,\n    material: material,\n    geometry: geometry,\n    position: [item.x, item.y, item.z]\n  }));\n}\nconst MotionPathControls = /* @__PURE__ */React.forwardRef(({\n  children,\n  curves = [],\n  debug = false,\n  debugColor = 'black',\n  object,\n  focus,\n  loop = true,\n  offset = undefined,\n  smooth = false,\n  eps = 0.00001,\n  damping = 0.1,\n  focusDamping = 0.1,\n  maxSpeed = Infinity,\n  ...props\n}, fref) => {\n  const {\n    camera\n  } = useThree();\n  const ref = React.useRef(null);\n  const pos = React.useRef(offset !== null && offset !== void 0 ? offset : 0);\n  const path = React.useMemo(() => new THREE.CurvePath(), []);\n  const state = React.useMemo(() => ({\n    focus,\n    object: (object == null ? void 0 : object.current) instanceof THREE.Object3D ? object : {\n      current: camera\n    },\n    path,\n    current: pos.current,\n    offset: pos.current,\n    point: new THREE.Vector3(),\n    tangent: new THREE.Vector3(),\n    next: new THREE.Vector3()\n  }), [focus, object]);\n  const instanceRef = useInstanceHandle(ref);\n  React.useLayoutEffect(() => {\n    const instance = instanceRef.current;\n    path.curves = [];\n    const _curves = curves.length > 0 ? curves : instance.children.map(instance => instance.object);\n    for (let i = 0; i < _curves.length; i++) path.add(_curves[i]);\n\n    // Smoothen curve\n    if (smooth) {\n      const points = path.getPoints(typeof smooth === 'number' ? smooth : 1);\n      const catmull = new THREE.CatmullRomCurve3(points);\n      path.curves = [catmull];\n    }\n    path.updateArcLengths();\n  });\n  React.useImperativeHandle(fref, () => Object.assign(ref.current, {\n    motion: state\n  }), [state]);\n  React.useLayoutEffect(() => {\n    // When offset changes, normalise pos to avoid overshoot spinning\n    pos.current = misc.repeat(pos.current, 1);\n  }, [offset]);\n  const vec = React.useMemo(() => new THREE.Vector3(), []);\n  useFrame((_state, delta) => {\n    const lastOffset = state.offset;\n    easing.damp(pos, 'current', offset !== undefined ? offset : state.current, damping, delta, maxSpeed, undefined, eps);\n    state.offset = loop ? misc.repeat(pos.current, 1) : misc.clamp(pos.current, 0, 1);\n    if (path.getCurveLengths().length > 0) {\n      path.getPointAt(state.offset, state.point);\n      path.getTangentAt(state.offset, state.tangent).normalize();\n      path.getPointAt(misc.repeat(pos.current - (lastOffset - state.offset), 1), state.next);\n      const target = (object == null ? void 0 : object.current) instanceof THREE.Object3D ? object.current : camera;\n      target.position.copy(state.point);\n      if (focus) {\n        easing.dampLookAt(target, isObject3DRef(focus) ? focus.current.getWorldPosition(vec) : focus, focusDamping, delta, maxSpeed, undefined, eps);\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(MotionContext.Provider, {\n    value: state\n  }, children, debug && /*#__PURE__*/React.createElement(Debug, {\n    color: debugColor\n  })));\n});\n\nexport { MotionPathControls, useMotion };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,oBAAoB;AAC1E,SAASC,IAAI,EAAEC,MAAM,QAAQ,OAAO;AAEpC,MAAMC,aAAa,GAAGC,GAAG,IAAI,CAACA,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,OAAO,aAAaT,KAAK,CAACU,QAAQ;AAC3F,MAAMC,aAAa,GAAG,eAAeV,KAAK,CAACW,aAAa,CAAC,IAAI,CAAC;AAC9D,SAASC,SAASA,CAAA,EAAG;EACnB,MAAMC,OAAO,GAAGb,KAAK,CAACc,UAAU,CAACJ,aAAa,CAAC;EAC/C,IAAI,CAACG,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,gEAAgE,CAAC;EAC/F,OAAOF,OAAO;AAChB;AACA,SAASG,KAAKA,CAAC;EACbC,MAAM,GAAG,EAAE;EACXC,KAAK,GAAG;AACV,CAAC,EAAE;EACD,MAAM;IACJC;EACF,CAAC,GAAGP,SAAS,CAAC,CAAC;EACf,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMC,QAAQ,GAAGvB,KAAK,CAACwB,OAAO,CAAC,MAAM,IAAIzB,KAAK,CAAC0B,iBAAiB,CAAC;IAC/DP,KAAK,EAAEA;EACT,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACZ,MAAMQ,QAAQ,GAAG1B,KAAK,CAACwB,OAAO,CAAC,MAAM,IAAIzB,KAAK,CAAC4B,cAAc,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;EACjF,MAAMC,IAAI,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,EAAE,CAAC;EAC7B7B,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB,IAAIX,IAAI,CAACY,MAAM,KAAKH,IAAI,CAACpB,OAAO,EAAE;MAChCa,OAAO,CAACF,IAAI,CAACa,SAAS,CAACf,MAAM,CAAC,CAAC;MAC/BW,IAAI,CAACpB,OAAO,GAAGW,IAAI,CAACY,MAAM;IAC5B;EACF,CAAC,CAAC;EACF,OAAOX,IAAI,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK,aAAanC,KAAK,CAACoC,aAAa,CAAC,MAAM,EAAE;IACxEC,GAAG,EAAEF,KAAK;IACVZ,QAAQ,EAAEA,QAAQ;IAClBG,QAAQ,EAAEA,QAAQ;IAClBY,QAAQ,EAAE,CAACJ,IAAI,CAACK,CAAC,EAAEL,IAAI,CAACM,CAAC,EAAEN,IAAI,CAACO,CAAC;EACnC,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,kBAAkB,GAAG,eAAe1C,KAAK,CAAC2C,UAAU,CAAC,CAAC;EAC1DC,QAAQ;EACRb,MAAM,GAAG,EAAE;EACXc,KAAK,GAAG,KAAK;EACbC,UAAU,GAAG,OAAO;EACpBC,MAAM;EACNC,KAAK;EACLC,IAAI,GAAG,IAAI;EACXC,MAAM,GAAGC,SAAS;EAClBC,MAAM,GAAG,KAAK;EACdC,GAAG,GAAG,OAAO;EACbC,OAAO,GAAG,GAAG;EACbC,YAAY,GAAG,GAAG;EAClBC,QAAQ,GAAGC,QAAQ;EACnB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAM;IACJC;EACF,CAAC,GAAG3D,QAAQ,CAAC,CAAC;EACd,MAAMM,GAAG,GAAGP,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgC,GAAG,GAAG7D,KAAK,CAAC6B,MAAM,CAACqB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC;EAC3E,MAAM/B,IAAI,GAAGnB,KAAK,CAACwB,OAAO,CAAC,MAAM,IAAIzB,KAAK,CAAC+D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3D,MAAMC,KAAK,GAAG/D,KAAK,CAACwB,OAAO,CAAC,OAAO;IACjCwB,KAAK;IACLD,MAAM,EAAE,CAACA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvC,OAAO,aAAaT,KAAK,CAACU,QAAQ,GAAGsC,MAAM,GAAG;MACtFvC,OAAO,EAAEoD;IACX,CAAC;IACDzC,IAAI;IACJX,OAAO,EAAEqD,GAAG,CAACrD,OAAO;IACpB0C,MAAM,EAAEW,GAAG,CAACrD,OAAO;IACnBwD,KAAK,EAAE,IAAIjE,KAAK,CAACkE,OAAO,CAAC,CAAC;IAC1BC,OAAO,EAAE,IAAInE,KAAK,CAACkE,OAAO,CAAC,CAAC;IAC5BE,IAAI,EAAE,IAAIpE,KAAK,CAACkE,OAAO,CAAC;EAC1B,CAAC,CAAC,EAAE,CAACjB,KAAK,EAAED,MAAM,CAAC,CAAC;EACpB,MAAMqB,WAAW,GAAGlE,iBAAiB,CAACK,GAAG,CAAC;EAC1CP,KAAK,CAACqE,eAAe,CAAC,MAAM;IAC1B,MAAMC,QAAQ,GAAGF,WAAW,CAAC5D,OAAO;IACpCW,IAAI,CAACY,MAAM,GAAG,EAAE;IAChB,MAAMwC,OAAO,GAAGxC,MAAM,CAACyC,MAAM,GAAG,CAAC,GAAGzC,MAAM,GAAGuC,QAAQ,CAAC1B,QAAQ,CAACX,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,CAACvB,MAAM,CAAC;IAC/F,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAEtD,IAAI,CAACuD,GAAG,CAACH,OAAO,CAACE,CAAC,CAAC,CAAC;;IAE7D;IACA,IAAIrB,MAAM,EAAE;MACV,MAAMnC,MAAM,GAAGE,IAAI,CAACa,SAAS,CAAC,OAAOoB,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMuB,OAAO,GAAG,IAAI5E,KAAK,CAAC6E,gBAAgB,CAAC3D,MAAM,CAAC;MAClDE,IAAI,CAACY,MAAM,GAAG,CAAC4C,OAAO,CAAC;IACzB;IACAxD,IAAI,CAAC0D,gBAAgB,CAAC,CAAC;EACzB,CAAC,CAAC;EACF7E,KAAK,CAAC8E,mBAAmB,CAACnB,IAAI,EAAE,MAAMoB,MAAM,CAACC,MAAM,CAACzE,GAAG,CAACC,OAAO,EAAE;IAC/DyE,MAAM,EAAElB;EACV,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACZ/D,KAAK,CAACqE,eAAe,CAAC,MAAM;IAC1B;IACAR,GAAG,CAACrD,OAAO,GAAGJ,IAAI,CAAC8E,MAAM,CAACrB,GAAG,CAACrD,OAAO,EAAE,CAAC,CAAC;EAC3C,CAAC,EAAE,CAAC0C,MAAM,CAAC,CAAC;EACZ,MAAMiC,GAAG,GAAGnF,KAAK,CAACwB,OAAO,CAAC,MAAM,IAAIzB,KAAK,CAACkE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;EACxD9D,QAAQ,CAAC,CAACiF,MAAM,EAAEC,KAAK,KAAK;IAC1B,MAAMC,UAAU,GAAGvB,KAAK,CAACb,MAAM;IAC/B7C,MAAM,CAACkF,IAAI,CAAC1B,GAAG,EAAE,SAAS,EAAEX,MAAM,KAAKC,SAAS,GAAGD,MAAM,GAAGa,KAAK,CAACvD,OAAO,EAAE8C,OAAO,EAAE+B,KAAK,EAAE7B,QAAQ,EAAEL,SAAS,EAAEE,GAAG,CAAC;IACpHU,KAAK,CAACb,MAAM,GAAGD,IAAI,GAAG7C,IAAI,CAAC8E,MAAM,CAACrB,GAAG,CAACrD,OAAO,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACoF,KAAK,CAAC3B,GAAG,CAACrD,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;IACjF,IAAIW,IAAI,CAACsE,eAAe,CAAC,CAAC,CAACjB,MAAM,GAAG,CAAC,EAAE;MACrCrD,IAAI,CAACuE,UAAU,CAAC3B,KAAK,CAACb,MAAM,EAAEa,KAAK,CAACC,KAAK,CAAC;MAC1C7C,IAAI,CAACwE,YAAY,CAAC5B,KAAK,CAACb,MAAM,EAAEa,KAAK,CAACG,OAAO,CAAC,CAAC0B,SAAS,CAAC,CAAC;MAC1DzE,IAAI,CAACuE,UAAU,CAACtF,IAAI,CAAC8E,MAAM,CAACrB,GAAG,CAACrD,OAAO,IAAI8E,UAAU,GAAGvB,KAAK,CAACb,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEa,KAAK,CAACI,IAAI,CAAC;MACtF,MAAM0B,MAAM,GAAG,CAAC9C,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvC,OAAO,aAAaT,KAAK,CAACU,QAAQ,GAAGsC,MAAM,CAACvC,OAAO,GAAGoD,MAAM;MAC7GiC,MAAM,CAACvD,QAAQ,CAACwD,IAAI,CAAC/B,KAAK,CAACC,KAAK,CAAC;MACjC,IAAIhB,KAAK,EAAE;QACT3C,MAAM,CAAC0F,UAAU,CAACF,MAAM,EAAEvF,aAAa,CAAC0C,KAAK,CAAC,GAAGA,KAAK,CAACxC,OAAO,CAACwF,gBAAgB,CAACb,GAAG,CAAC,GAAGnC,KAAK,EAAEO,YAAY,EAAE8B,KAAK,EAAE7B,QAAQ,EAAEL,SAAS,EAAEE,GAAG,CAAC;MAC9I;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAarD,KAAK,CAACoC,aAAa,CAAC,OAAO,EAAEtC,QAAQ,CAAC;IACxDS,GAAG,EAAEA;EACP,CAAC,EAAEmD,KAAK,CAAC,EAAE,aAAa1D,KAAK,CAACoC,aAAa,CAAC1B,aAAa,CAACuF,QAAQ,EAAE;IAClEC,KAAK,EAAEnC;EACT,CAAC,EAAEnB,QAAQ,EAAEC,KAAK,IAAI,aAAa7C,KAAK,CAACoC,aAAa,CAACpB,KAAK,EAAE;IAC5DE,KAAK,EAAE4B;EACT,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,SAASJ,kBAAkB,EAAE9B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}