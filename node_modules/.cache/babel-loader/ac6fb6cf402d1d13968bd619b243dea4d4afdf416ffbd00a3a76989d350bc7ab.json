{"ast": null, "code": "import * as React from 'react';\nfunction useCursor(hovered, onPointerOver = 'pointer', onPointerOut = 'auto', container = document.body) {\n  React.useEffect(() => {\n    if (hovered) {\n      container.style.cursor = onPointerOver;\n      return () => void (container.style.cursor = onPointerOut);\n    }\n  }, [hovered]);\n}\nexport { useCursor };", "map": {"version": 3, "names": ["React", "useCursor", "hovered", "onPointerOver", "onPointerOut", "container", "document", "body", "useEffect", "style", "cursor"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/useCursor.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction useCursor(hovered, onPointerOver = 'pointer', onPointerOut = 'auto', container = document.body) {\n  React.useEffect(() => {\n    if (hovered) {\n      container.style.cursor = onPointerOver;\n      return () => void (container.style.cursor = onPointerOut);\n    }\n  }, [hovered]);\n}\n\nexport { useCursor };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,OAAO,EAAEC,aAAa,GAAG,SAAS,EAAEC,YAAY,GAAG,MAAM,EAAEC,SAAS,GAAGC,QAAQ,CAACC,IAAI,EAAE;EACvGP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIN,OAAO,EAAE;MACXG,SAAS,CAACI,KAAK,CAACC,MAAM,GAAGP,aAAa;MACtC,OAAO,MAAM,MAAME,SAAS,CAACI,KAAK,CAACC,MAAM,GAAGN,YAAY,CAAC;IAC3D;EACF,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;AACf;AAEA,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}