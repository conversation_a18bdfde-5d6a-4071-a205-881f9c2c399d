{"ast": null, "code": "import * as React from 'react';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const depthBuffer = depth !== null && depth !== void 0 ? depth : _settings.depthBuffer; // backwards compatibility for deprecated `depth` prop\n\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n    if (depthBuffer) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\n\n//\n// Fbo component\n//\n\nconst Fbo = /* @__PURE__ */forwardRef(({\n  children,\n  width,\n  height,\n  ...settings\n}, fref) => {\n  const target = useFBO(width, height, settings);\n  useImperativeHandle(fref, () => target, [target]); // expose target through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(target));\n});\nexport { Fbo, useFBO };", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "THREE", "useThree", "useFBO", "width", "height", "settings", "size", "state", "viewport", "_width", "dpr", "_height", "_settings", "samples", "depth", "targetSettings", "depthBuffer", "target", "useMemo", "WebGLRenderTarget", "minFilter", "LinearFilter", "magFilter", "type", "HalfFloatType", "depthTexture", "DepthTexture", "FloatType", "useLayoutEffect", "setSize", "useEffect", "dispose", "Fbo", "children", "fref", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Fbo.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const depthBuffer = depth !== null && depth !== void 0 ? depth : _settings.depthBuffer; // backwards compatibility for deprecated `depth` prop\n\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n    if (depthBuffer) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\n\n//\n// Fbo component\n//\n\nconst Fbo = /* @__PURE__ */forwardRef(({\n  children,\n  width,\n  height,\n  ...settings\n}, fref) => {\n  const target = useFBO(width, height, settings);\n  useImperativeHandle(fref, () => target, [target]); // expose target through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(target));\n});\n\nexport { Fbo, useFBO };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;;AAE7C;AACA;AACA,SAASC,MAAMA,CAAC;AAChBC,KAAK,EAAE;AACPC,MAAM,EAAE;AACRC,QAAQ,EAAE;EACR,MAAMC,IAAI,GAAGL,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,QAAQ,GAAGP,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAClD,MAAMC,MAAM,GAAG,OAAON,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGG,IAAI,CAACH,KAAK,GAAGK,QAAQ,CAACE,GAAG;EAC5E,MAAMC,OAAO,GAAG,OAAOP,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGE,IAAI,CAACF,MAAM,GAAGI,QAAQ,CAACE,GAAG;EAChF,MAAME,SAAS,GAAG,CAAC,OAAOT,KAAK,KAAK,QAAQ,GAAGE,QAAQ,GAAGF,KAAK,KAAK,CAAC,CAAC;EACtE,MAAM;IACJU,OAAO,GAAG,CAAC;IACXC,KAAK;IACL,GAAGC;EACL,CAAC,GAAGH,SAAS;EACb,MAAMI,WAAW,GAAGF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGF,SAAS,CAACI,WAAW,CAAC,CAAC;;EAExF,MAAMC,MAAM,GAAGpB,KAAK,CAACqB,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAG,IAAIjB,KAAK,CAACmB,iBAAiB,CAACV,MAAM,EAAEE,OAAO,EAAE;MAC1DS,SAAS,EAAEpB,KAAK,CAACqB,YAAY;MAC7BC,SAAS,EAAEtB,KAAK,CAACqB,YAAY;MAC7BE,IAAI,EAAEvB,KAAK,CAACwB,aAAa;MACzB,GAAGT;IACL,CAAC,CAAC;IACF,IAAIC,WAAW,EAAE;MACfC,MAAM,CAACQ,YAAY,GAAG,IAAIzB,KAAK,CAAC0B,YAAY,CAACjB,MAAM,EAAEE,OAAO,EAAEX,KAAK,CAAC2B,SAAS,CAAC;IAChF;IACAV,MAAM,CAACJ,OAAO,GAAGA,OAAO;IACxB,OAAOI,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EACNpB,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1BX,MAAM,CAACY,OAAO,CAACpB,MAAM,EAAEE,OAAO,CAAC;IAC/B,IAAIE,OAAO,EAAEI,MAAM,CAACJ,OAAO,GAAGA,OAAO;EACvC,CAAC,EAAE,CAACA,OAAO,EAAEI,MAAM,EAAER,MAAM,EAAEE,OAAO,CAAC,CAAC;EACtCd,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMb,MAAM,CAACc,OAAO,CAAC,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,OAAOd,MAAM;AACf;;AAEA;AACA;AACA;;AAEA,MAAMe,GAAG,GAAG,eAAelC,UAAU,CAAC,CAAC;EACrCmC,QAAQ;EACR9B,KAAK;EACLC,MAAM;EACN,GAAGC;AACL,CAAC,EAAE6B,IAAI,KAAK;EACV,MAAMjB,MAAM,GAAGf,MAAM,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,CAAC;EAC9CN,mBAAmB,CAACmC,IAAI,EAAE,MAAMjB,MAAM,EAAE,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEnD,OAAO,aAAapB,KAAK,CAACsC,aAAa,CAACtC,KAAK,CAACuC,QAAQ,EAAE,IAAI,EAAEH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAChB,MAAM,CAAC,CAAC;AAC7G,CAAC,CAAC;AAEF,SAASe,GAAG,EAAE9B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}