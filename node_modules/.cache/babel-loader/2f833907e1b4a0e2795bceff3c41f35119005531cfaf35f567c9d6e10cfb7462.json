{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MathUtils } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\nimport { easing } from 'maath';\nfunction PresentationControls({\n  enabled = true,\n  snap,\n  global,\n  domElement,\n  cursor = true,\n  children,\n  speed = 1,\n  rotation = [0, 0, 0],\n  zoom = 1,\n  polar = [0, Math.PI / 2],\n  azimuth = [-Infinity, Infinity],\n  damping = 0.25\n}) {\n  const events = useThree(state => state.events);\n  const gl = useThree(state => state.gl);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const {\n    size\n  } = useThree();\n  const rPolar = React.useMemo(() => [rotation[0] + polar[0], rotation[0] + polar[1]], [rotation[0], polar[0], polar[1]]);\n  const rAzimuth = React.useMemo(() => [rotation[1] + azimuth[0], rotation[1] + azimuth[1]], [rotation[1], azimuth[0], azimuth[1]]);\n  const rInitial = React.useMemo(() => [MathUtils.clamp(rotation[0], ...rPolar), MathUtils.clamp(rotation[1], ...rAzimuth), rotation[2]], [rotation[0], rotation[1], rotation[2], rPolar, rAzimuth]);\n  React.useEffect(() => {\n    if (global && cursor && enabled) {\n      explDomElement.style.cursor = 'grab';\n      gl.domElement.style.cursor = '';\n      return () => {\n        explDomElement.style.cursor = 'default';\n        gl.domElement.style.cursor = 'default';\n      };\n    }\n  }, [global, cursor, explDomElement, enabled]);\n  const [animation] = React.useState({\n    scale: 1,\n    rotation: rInitial,\n    damping\n  });\n  const ref = React.useRef(null);\n  useFrame((state, delta) => {\n    easing.damp3(ref.current.scale, animation.scale, animation.damping, delta);\n    easing.dampE(ref.current.rotation, animation.rotation, animation.damping, delta);\n  });\n  const bind = useGesture({\n    onHover: ({\n      last\n    }) => {\n      if (cursor && !global && enabled) explDomElement.style.cursor = last ? 'auto' : 'grab';\n    },\n    onDrag: ({\n      down,\n      delta: [x, y],\n      memo: [oldY, oldX] = animation.rotation || rInitial\n    }) => {\n      if (!enabled) return [y, x];\n      if (cursor) explDomElement.style.cursor = down ? 'grabbing' : 'grab';\n      x = MathUtils.clamp(oldX + x / size.width * Math.PI * speed, ...rAzimuth);\n      y = MathUtils.clamp(oldY + y / size.height * Math.PI * speed, ...rPolar);\n      animation.scale = down && y > rPolar[1] / 2 ? zoom : 1;\n      animation.rotation = snap && !down ? rInitial : [y, x, 0];\n      animation.damping = snap && !down && typeof snap !== 'boolean' ? snap : damping;\n      return [y, x];\n    }\n  }, {\n    target: global ? explDomElement : undefined\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind == null ? void 0 : bind()), children);\n}\nexport { PresentationControls };", "map": {"version": 3, "names": ["_extends", "React", "MathUtils", "useThree", "useFrame", "useGesture", "easing", "PresentationControls", "enabled", "snap", "global", "dom<PERSON>lement", "cursor", "children", "speed", "rotation", "zoom", "polar", "Math", "PI", "azimuth", "Infinity", "damping", "events", "state", "gl", "explDomElement", "connected", "size", "rPolar", "useMemo", "rAzimuth", "rInitial", "clamp", "useEffect", "style", "animation", "useState", "scale", "ref", "useRef", "delta", "damp3", "current", "dampE", "bind", "onHover", "last", "onDrag", "down", "x", "y", "memo", "oldY", "oldX", "width", "height", "target", "undefined", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/PresentationControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MathUtils } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\nimport { easing } from 'maath';\n\nfunction PresentationControls({\n  enabled = true,\n  snap,\n  global,\n  domElement,\n  cursor = true,\n  children,\n  speed = 1,\n  rotation = [0, 0, 0],\n  zoom = 1,\n  polar = [0, Math.PI / 2],\n  azimuth = [-Infinity, Infinity],\n  damping = 0.25\n}) {\n  const events = useThree(state => state.events);\n  const gl = useThree(state => state.gl);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const {\n    size\n  } = useThree();\n  const rPolar = React.useMemo(() => [rotation[0] + polar[0], rotation[0] + polar[1]], [rotation[0], polar[0], polar[1]]);\n  const rAzimuth = React.useMemo(() => [rotation[1] + azimuth[0], rotation[1] + azimuth[1]], [rotation[1], azimuth[0], azimuth[1]]);\n  const rInitial = React.useMemo(() => [MathUtils.clamp(rotation[0], ...rPolar), MathUtils.clamp(rotation[1], ...rAzimuth), rotation[2]], [rotation[0], rotation[1], rotation[2], rPolar, rAzimuth]);\n  React.useEffect(() => {\n    if (global && cursor && enabled) {\n      explDomElement.style.cursor = 'grab';\n      gl.domElement.style.cursor = '';\n      return () => {\n        explDomElement.style.cursor = 'default';\n        gl.domElement.style.cursor = 'default';\n      };\n    }\n  }, [global, cursor, explDomElement, enabled]);\n  const [animation] = React.useState({\n    scale: 1,\n    rotation: rInitial,\n    damping\n  });\n  const ref = React.useRef(null);\n  useFrame((state, delta) => {\n    easing.damp3(ref.current.scale, animation.scale, animation.damping, delta);\n    easing.dampE(ref.current.rotation, animation.rotation, animation.damping, delta);\n  });\n  const bind = useGesture({\n    onHover: ({\n      last\n    }) => {\n      if (cursor && !global && enabled) explDomElement.style.cursor = last ? 'auto' : 'grab';\n    },\n    onDrag: ({\n      down,\n      delta: [x, y],\n      memo: [oldY, oldX] = animation.rotation || rInitial\n    }) => {\n      if (!enabled) return [y, x];\n      if (cursor) explDomElement.style.cursor = down ? 'grabbing' : 'grab';\n      x = MathUtils.clamp(oldX + x / size.width * Math.PI * speed, ...rAzimuth);\n      y = MathUtils.clamp(oldY + y / size.height * Math.PI * speed, ...rPolar);\n      animation.scale = down && y > rPolar[1] / 2 ? zoom : 1;\n      animation.rotation = snap && !down ? rInitial : [y, x, 0];\n      animation.damping = snap && !down && typeof snap !== 'boolean' ? snap : damping;\n      return [y, x];\n    }\n  }, {\n    target: global ? explDomElement : undefined\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind == null ? void 0 : bind()), children);\n}\n\nexport { PresentationControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,MAAM,QAAQ,OAAO;AAE9B,SAASC,oBAAoBA,CAAC;EAC5BC,OAAO,GAAG,IAAI;EACdC,IAAI;EACJC,MAAM;EACNC,UAAU;EACVC,MAAM,GAAG,IAAI;EACbC,QAAQ;EACRC,KAAK,GAAG,CAAC;EACTC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,IAAI,GAAG,CAAC;EACRC,KAAK,GAAG,CAAC,CAAC,EAAEC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;EACxBC,OAAO,GAAG,CAAC,CAACC,QAAQ,EAAEA,QAAQ,CAAC;EAC/BC,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGpB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC;EAC9C,MAAME,EAAE,GAAGtB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,cAAc,GAAGf,UAAU,IAAIY,MAAM,CAACI,SAAS,IAAIF,EAAE,CAACd,UAAU;EACtE,MAAM;IACJiB;EACF,CAAC,GAAGzB,QAAQ,CAAC,CAAC;EACd,MAAM0B,MAAM,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,MAAM,CAACf,QAAQ,CAAC,CAAC,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACvH,MAAMc,QAAQ,GAAG9B,KAAK,CAAC6B,OAAO,CAAC,MAAM,CAACf,QAAQ,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC,CAAC,CAAC,EAAEL,QAAQ,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjI,MAAMY,QAAQ,GAAG/B,KAAK,CAAC6B,OAAO,CAAC,MAAM,CAAC5B,SAAS,CAAC+B,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAGc,MAAM,CAAC,EAAE3B,SAAS,CAAC+B,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAGgB,QAAQ,CAAC,EAAEhB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEc,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAClM9B,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAIxB,MAAM,IAAIE,MAAM,IAAIJ,OAAO,EAAE;MAC/BkB,cAAc,CAACS,KAAK,CAACvB,MAAM,GAAG,MAAM;MACpCa,EAAE,CAACd,UAAU,CAACwB,KAAK,CAACvB,MAAM,GAAG,EAAE;MAC/B,OAAO,MAAM;QACXc,cAAc,CAACS,KAAK,CAACvB,MAAM,GAAG,SAAS;QACvCa,EAAE,CAACd,UAAU,CAACwB,KAAK,CAACvB,MAAM,GAAG,SAAS;MACxC,CAAC;IACH;EACF,CAAC,EAAE,CAACF,MAAM,EAAEE,MAAM,EAAEc,cAAc,EAAElB,OAAO,CAAC,CAAC;EAC7C,MAAM,CAAC4B,SAAS,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC;IACjCC,KAAK,EAAE,CAAC;IACRvB,QAAQ,EAAEiB,QAAQ;IAClBV;EACF,CAAC,CAAC;EACF,MAAMiB,GAAG,GAAGtC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAC9BpC,QAAQ,CAAC,CAACoB,KAAK,EAAEiB,KAAK,KAAK;IACzBnC,MAAM,CAACoC,KAAK,CAACH,GAAG,CAACI,OAAO,CAACL,KAAK,EAAEF,SAAS,CAACE,KAAK,EAAEF,SAAS,CAACd,OAAO,EAAEmB,KAAK,CAAC;IAC1EnC,MAAM,CAACsC,KAAK,CAACL,GAAG,CAACI,OAAO,CAAC5B,QAAQ,EAAEqB,SAAS,CAACrB,QAAQ,EAAEqB,SAAS,CAACd,OAAO,EAAEmB,KAAK,CAAC;EAClF,CAAC,CAAC;EACF,MAAMI,IAAI,GAAGxC,UAAU,CAAC;IACtByC,OAAO,EAAEA,CAAC;MACRC;IACF,CAAC,KAAK;MACJ,IAAInC,MAAM,IAAI,CAACF,MAAM,IAAIF,OAAO,EAAEkB,cAAc,CAACS,KAAK,CAACvB,MAAM,GAAGmC,IAAI,GAAG,MAAM,GAAG,MAAM;IACxF,CAAC;IACDC,MAAM,EAAEA,CAAC;MACPC,IAAI;MACJR,KAAK,EAAE,CAACS,CAAC,EAAEC,CAAC,CAAC;MACbC,IAAI,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,GAAGlB,SAAS,CAACrB,QAAQ,IAAIiB;IAC7C,CAAC,KAAK;MACJ,IAAI,CAACxB,OAAO,EAAE,OAAO,CAAC2C,CAAC,EAAED,CAAC,CAAC;MAC3B,IAAItC,MAAM,EAAEc,cAAc,CAACS,KAAK,CAACvB,MAAM,GAAGqC,IAAI,GAAG,UAAU,GAAG,MAAM;MACpEC,CAAC,GAAGhD,SAAS,CAAC+B,KAAK,CAACqB,IAAI,GAAGJ,CAAC,GAAGtB,IAAI,CAAC2B,KAAK,GAAGrC,IAAI,CAACC,EAAE,GAAGL,KAAK,EAAE,GAAGiB,QAAQ,CAAC;MACzEoB,CAAC,GAAGjD,SAAS,CAAC+B,KAAK,CAACoB,IAAI,GAAGF,CAAC,GAAGvB,IAAI,CAAC4B,MAAM,GAAGtC,IAAI,CAACC,EAAE,GAAGL,KAAK,EAAE,GAAGe,MAAM,CAAC;MACxEO,SAAS,CAACE,KAAK,GAAGW,IAAI,IAAIE,CAAC,GAAGtB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGb,IAAI,GAAG,CAAC;MACtDoB,SAAS,CAACrB,QAAQ,GAAGN,IAAI,IAAI,CAACwC,IAAI,GAAGjB,QAAQ,GAAG,CAACmB,CAAC,EAAED,CAAC,EAAE,CAAC,CAAC;MACzDd,SAAS,CAACd,OAAO,GAAGb,IAAI,IAAI,CAACwC,IAAI,IAAI,OAAOxC,IAAI,KAAK,SAAS,GAAGA,IAAI,GAAGa,OAAO;MAC/E,OAAO,CAAC6B,CAAC,EAAED,CAAC,CAAC;IACf;EACF,CAAC,EAAE;IACDO,MAAM,EAAE/C,MAAM,GAAGgB,cAAc,GAAGgC;EACpC,CAAC,CAAC;EACF,OAAO,aAAazD,KAAK,CAAC0D,aAAa,CAAC,OAAO,EAAE3D,QAAQ,CAAC;IACxDuC,GAAG,EAAEA;EACP,CAAC,EAAEM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,EAAEhC,QAAQ,CAAC;AAC/C;AAEA,SAASN,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}