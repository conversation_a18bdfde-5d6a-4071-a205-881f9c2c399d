{"ast": null, "code": "import { SphereGeometry, BoxGeometry, MeshStandardMaterial, InstancedMesh, DynamicDrawUsage, Matrix4, Vector3 } from \"three\";\nconst _matrix = /* @__PURE__ */new Matrix4();\nconst _vector = /* @__PURE__ */new Vector3();\nclass XRHandPrimitiveModel {\n  constructor(handModel, controller, path, handedness, options) {\n    this.controller = controller;\n    this.handModel = handModel;\n    this.envMap = null;\n    let geometry;\n    if (!options || !options.primitive || options.primitive === \"sphere\") {\n      geometry = new SphereGeometry(1, 10, 10);\n    } else if (options.primitive === \"box\") {\n      geometry = new BoxGeometry(1, 1, 1);\n    }\n    const material = new MeshStandardMaterial();\n    this.handMesh = new InstancedMesh(geometry, material, 30);\n    this.handMesh.instanceMatrix.setUsage(DynamicDrawUsage);\n    this.handMesh.castShadow = true;\n    this.handMesh.receiveShadow = true;\n    this.handModel.add(this.handMesh);\n    this.joints = [\"wrist\", \"thumb-metacarpal\", \"thumb-phalanx-proximal\", \"thumb-phalanx-distal\", \"thumb-tip\", \"index-finger-metacarpal\", \"index-finger-phalanx-proximal\", \"index-finger-phalanx-intermediate\", \"index-finger-phalanx-distal\", \"index-finger-tip\", \"middle-finger-metacarpal\", \"middle-finger-phalanx-proximal\", \"middle-finger-phalanx-intermediate\", \"middle-finger-phalanx-distal\", \"middle-finger-tip\", \"ring-finger-metacarpal\", \"ring-finger-phalanx-proximal\", \"ring-finger-phalanx-intermediate\", \"ring-finger-phalanx-distal\", \"ring-finger-tip\", \"pinky-finger-metacarpal\", \"pinky-finger-phalanx-proximal\", \"pinky-finger-phalanx-intermediate\", \"pinky-finger-phalanx-distal\", \"pinky-finger-tip\"];\n  }\n  updateMesh() {\n    const defaultRadius = 8e-3;\n    const joints = this.controller.joints;\n    let count = 0;\n    for (let i = 0; i < this.joints.length; i++) {\n      const joint = joints[this.joints[i]];\n      if (joint.visible) {\n        _vector.setScalar(joint.jointRadius || defaultRadius);\n        _matrix.compose(joint.position, joint.quaternion, _vector);\n        this.handMesh.setMatrixAt(i, _matrix);\n        count++;\n      }\n    }\n    this.handMesh.count = count;\n    this.handMesh.instanceMatrix.needsUpdate = true;\n  }\n}\nexport { XRHandPrimitiveModel };", "map": {"version": 3, "names": ["_matrix", "Matrix4", "_vector", "Vector3", "XRHandPrimitiveModel", "constructor", "handModel", "controller", "path", "handedness", "options", "envMap", "geometry", "primitive", "SphereGeometry", "BoxGeometry", "material", "MeshStandardMaterial", "hand<PERSON><PERSON>", "In<PERSON>d<PERSON>esh", "instanceMatrix", "setUsage", "DynamicDrawUsage", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "add", "joints", "updateMesh", "defaultRadius", "count", "i", "length", "joint", "visible", "setScalar", "jointRadius", "compose", "position", "quaternion", "setMatrixAt", "needsUpdate"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/webxr/XRHandPrimitiveModel.js"], "sourcesContent": ["import {\n  DynamicDrawUsage,\n  SphereGeometry,\n  BoxGeometry,\n  MeshStandardMaterial,\n  InstancedMesh,\n  Matrix4,\n  Vector3,\n} from 'three'\n\nconst _matrix = /* @__PURE__ */ new Matrix4()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass XRHandPrimitiveModel {\n  constructor(handModel, controller, path, handedness, options) {\n    this.controller = controller\n    this.handModel = handModel\n    this.envMap = null\n\n    let geometry\n\n    if (!options || !options.primitive || options.primitive === 'sphere') {\n      geometry = new SphereGeometry(1, 10, 10)\n    } else if (options.primitive === 'box') {\n      geometry = new BoxGeometry(1, 1, 1)\n    }\n\n    const material = new MeshStandardMaterial()\n\n    this.handMesh = new InstancedMesh(geometry, material, 30)\n    this.handMesh.instanceMatrix.setUsage(DynamicDrawUsage) // will be updated every frame\n    this.handMesh.castShadow = true\n    this.handMesh.receiveShadow = true\n    this.handModel.add(this.handMesh)\n\n    this.joints = [\n      'wrist',\n      'thumb-metacarpal',\n      'thumb-phalanx-proximal',\n      'thumb-phalanx-distal',\n      'thumb-tip',\n      'index-finger-metacarpal',\n      'index-finger-phalanx-proximal',\n      'index-finger-phalanx-intermediate',\n      'index-finger-phalanx-distal',\n      'index-finger-tip',\n      'middle-finger-metacarpal',\n      'middle-finger-phalanx-proximal',\n      'middle-finger-phalanx-intermediate',\n      'middle-finger-phalanx-distal',\n      'middle-finger-tip',\n      'ring-finger-metacarpal',\n      'ring-finger-phalanx-proximal',\n      'ring-finger-phalanx-intermediate',\n      'ring-finger-phalanx-distal',\n      'ring-finger-tip',\n      'pinky-finger-metacarpal',\n      'pinky-finger-phalanx-proximal',\n      'pinky-finger-phalanx-intermediate',\n      'pinky-finger-phalanx-distal',\n      'pinky-finger-tip',\n    ]\n  }\n\n  updateMesh() {\n    const defaultRadius = 0.008\n    const joints = this.controller.joints\n\n    let count = 0\n\n    for (let i = 0; i < this.joints.length; i++) {\n      const joint = joints[this.joints[i]]\n\n      if (joint.visible) {\n        _vector.setScalar(joint.jointRadius || defaultRadius)\n        _matrix.compose(joint.position, joint.quaternion, _vector)\n        this.handMesh.setMatrixAt(i, _matrix)\n\n        count++\n      }\n    }\n\n    this.handMesh.count = count\n    this.handMesh.instanceMatrix.needsUpdate = true\n  }\n}\n\nexport { XRHandPrimitiveModel }\n"], "mappings": ";AAUA,MAAMA,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAC7C,MAAMC,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAE7C,MAAMC,oBAAA,CAAqB;EACzBC,YAAYC,SAAA,EAAWC,UAAA,EAAYC,IAAA,EAAMC,UAAA,EAAYC,OAAA,EAAS;IAC5D,KAAKH,UAAA,GAAaA,UAAA;IAClB,KAAKD,SAAA,GAAYA,SAAA;IACjB,KAAKK,MAAA,GAAS;IAEd,IAAIC,QAAA;IAEJ,IAAI,CAACF,OAAA,IAAW,CAACA,OAAA,CAAQG,SAAA,IAAaH,OAAA,CAAQG,SAAA,KAAc,UAAU;MACpED,QAAA,GAAW,IAAIE,cAAA,CAAe,GAAG,IAAI,EAAE;IAC7C,WAAeJ,OAAA,CAAQG,SAAA,KAAc,OAAO;MACtCD,QAAA,GAAW,IAAIG,WAAA,CAAY,GAAG,GAAG,CAAC;IACnC;IAED,MAAMC,QAAA,GAAW,IAAIC,oBAAA,CAAsB;IAE3C,KAAKC,QAAA,GAAW,IAAIC,aAAA,CAAcP,QAAA,EAAUI,QAAA,EAAU,EAAE;IACxD,KAAKE,QAAA,CAASE,cAAA,CAAeC,QAAA,CAASC,gBAAgB;IACtD,KAAKJ,QAAA,CAASK,UAAA,GAAa;IAC3B,KAAKL,QAAA,CAASM,aAAA,GAAgB;IAC9B,KAAKlB,SAAA,CAAUmB,GAAA,CAAI,KAAKP,QAAQ;IAEhC,KAAKQ,MAAA,GAAS,CACZ,SACA,oBACA,0BACA,wBACA,aACA,2BACA,iCACA,qCACA,+BACA,oBACA,4BACA,kCACA,sCACA,gCACA,qBACA,0BACA,gCACA,oCACA,8BACA,mBACA,2BACA,iCACA,qCACA,+BACA,mBACD;EACF;EAEDC,WAAA,EAAa;IACX,MAAMC,aAAA,GAAgB;IACtB,MAAMF,MAAA,GAAS,KAAKnB,UAAA,CAAWmB,MAAA;IAE/B,IAAIG,KAAA,GAAQ;IAEZ,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKJ,MAAA,CAAOK,MAAA,EAAQD,CAAA,IAAK;MAC3C,MAAME,KAAA,GAAQN,MAAA,CAAO,KAAKA,MAAA,CAAOI,CAAC,CAAC;MAEnC,IAAIE,KAAA,CAAMC,OAAA,EAAS;QACjB/B,OAAA,CAAQgC,SAAA,CAAUF,KAAA,CAAMG,WAAA,IAAeP,aAAa;QACpD5B,OAAA,CAAQoC,OAAA,CAAQJ,KAAA,CAAMK,QAAA,EAAUL,KAAA,CAAMM,UAAA,EAAYpC,OAAO;QACzD,KAAKgB,QAAA,CAASqB,WAAA,CAAYT,CAAA,EAAG9B,OAAO;QAEpC6B,KAAA;MACD;IACF;IAED,KAAKX,QAAA,CAASW,KAAA,GAAQA,KAAA;IACtB,KAAKX,QAAA,CAASE,cAAA,CAAeoB,WAAA,GAAc;EAC5C;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}