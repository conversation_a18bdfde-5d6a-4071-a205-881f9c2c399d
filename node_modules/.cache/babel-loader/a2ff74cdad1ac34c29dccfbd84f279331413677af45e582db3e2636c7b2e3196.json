{"ast": null, "code": "import { Color, MathUtils } from \"three\";\nclass Lut {\n  constructor(colormap, count = 32) {\n    this.isLut = true;\n    this.lut = [];\n    this.map = [];\n    this.n = 0;\n    this.minV = 0;\n    this.maxV = 1;\n    this.setColorMap(colormap, count);\n  }\n  set(value) {\n    if (value.isLut === true) {\n      this.copy(value);\n    }\n    return this;\n  }\n  setMin(min) {\n    this.minV = min;\n    return this;\n  }\n  setMax(max) {\n    this.maxV = max;\n    return this;\n  }\n  setColorMap(colormap, count = 32) {\n    this.map = ColorMapKeywords[colormap] || ColorMapKeywords.rainbow;\n    this.n = count;\n    const step = 1 / this.n;\n    const minColor = new Color();\n    const maxColor = new Color();\n    this.lut.length = 0;\n    this.lut.push(new Color(this.map[0][1]));\n    for (let i = 1; i < count; i++) {\n      const alpha = i * step;\n      for (let j = 0; j < this.map.length - 1; j++) {\n        if (alpha > this.map[j][0] && alpha <= this.map[j + 1][0]) {\n          const min = this.map[j][0];\n          const max = this.map[j + 1][0];\n          minColor.setHex(this.map[j][1], \"srgb-linear\");\n          maxColor.setHex(this.map[j + 1][1], \"srgb-linear\");\n          const color = new Color().lerpColors(minColor, maxColor, (alpha - min) / (max - min));\n          this.lut.push(color);\n        }\n      }\n    }\n    this.lut.push(new Color(this.map[this.map.length - 1][1]));\n    return this;\n  }\n  copy(lut) {\n    this.lut = lut.lut;\n    this.map = lut.map;\n    this.n = lut.n;\n    this.minV = lut.minV;\n    this.maxV = lut.maxV;\n    return this;\n  }\n  getColor(alpha) {\n    alpha = MathUtils.clamp(alpha, this.minV, this.maxV);\n    alpha = (alpha - this.minV) / (this.maxV - this.minV);\n    const colorPosition = Math.round(alpha * this.n);\n    return this.lut[colorPosition];\n  }\n  addColorMap(name, arrayOfColors) {\n    ColorMapKeywords[name] = arrayOfColors;\n    return this;\n  }\n  createCanvas() {\n    const canvas = document.createElement(\"canvas\");\n    canvas.width = 1;\n    canvas.height = this.n;\n    this.updateCanvas(canvas);\n    return canvas;\n  }\n  updateCanvas(canvas) {\n    const ctx = canvas.getContext(\"2d\", {\n      alpha: false\n    });\n    const imageData = ctx.getImageData(0, 0, 1, this.n);\n    const data = imageData.data;\n    let k = 0;\n    const step = 1 / this.n;\n    const minColor = new Color();\n    const maxColor = new Color();\n    const finalColor = new Color();\n    for (let i = 1; i >= 0; i -= step) {\n      for (let j = this.map.length - 1; j >= 0; j--) {\n        if (i < this.map[j][0] && i >= this.map[j - 1][0]) {\n          const min = this.map[j - 1][0];\n          const max = this.map[j][0];\n          minColor.setHex(this.map[j - 1][1], \"srgb-linear\");\n          maxColor.setHex(this.map[j][1], \"srgb-linear\");\n          finalColor.lerpColors(minColor, maxColor, (i - min) / (max - min));\n          data[k * 4] = Math.round(finalColor.r * 255);\n          data[k * 4 + 1] = Math.round(finalColor.g * 255);\n          data[k * 4 + 2] = Math.round(finalColor.b * 255);\n          data[k * 4 + 3] = 255;\n          k += 1;\n        }\n      }\n    }\n    ctx.putImageData(imageData, 0, 0);\n    return canvas;\n  }\n}\nconst ColorMapKeywords = {\n  rainbow: [[0, 255], [0.2, 65535], [0.5, 65280], [0.8, 16776960], [1, 16711680]],\n  cooltowarm: [[0, 3952322], [0.2, 10206463], [0.5, 14474460], [0.8, 16163717], [1, 11797542]],\n  blackbody: [[0, 0], [0.2, 7864320], [0.5, 15086080], [0.8, 16776960], [1, 16777215]],\n  grayscale: [[0, 0], [0.2, 4210752], [0.5, 8355712], [0.8, 12566463], [1, 16777215]]\n};\nexport { ColorMapKeywords, Lut };", "map": {"version": 3, "names": ["Lut", "constructor", "colormap", "count", "isLut", "lut", "map", "n", "minV", "maxV", "setColorMap", "set", "value", "copy", "setMin", "min", "setMax", "max", "ColorMapKeywords", "rainbow", "step", "minColor", "Color", "maxColor", "length", "push", "i", "alpha", "j", "setHex", "color", "lerpColors", "getColor", "MathUtils", "clamp", "colorPosition", "Math", "round", "addColorMap", "name", "arrayOfColors", "createCanvas", "canvas", "document", "createElement", "width", "height", "updateCanvas", "ctx", "getContext", "imageData", "getImageData", "data", "k", "finalColor", "r", "g", "b", "putImageData", "cooltowarm", "blackbody", "grayscale"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/math/Lut.js"], "sourcesContent": ["import { Color, MathUtils } from 'three'\n\nclass Lut {\n  constructor(colormap, count = 32) {\n    this.isLut = true\n\n    this.lut = []\n    this.map = []\n    this.n = 0\n    this.minV = 0\n    this.maxV = 1\n\n    this.setColorMap(colormap, count)\n  }\n\n  set(value) {\n    if (value.isLut === true) {\n      this.copy(value)\n    }\n\n    return this\n  }\n\n  setMin(min) {\n    this.minV = min\n\n    return this\n  }\n\n  setMax(max) {\n    this.maxV = max\n\n    return this\n  }\n\n  setColorMap(colormap, count = 32) {\n    this.map = ColorMapKeywords[colormap] || ColorMapKeywords.rainbow\n    this.n = count\n\n    const step = 1.0 / this.n\n    const minColor = new Color()\n    const maxColor = new Color()\n\n    this.lut.length = 0\n\n    // sample at 0\n\n    this.lut.push(new Color(this.map[0][1]))\n\n    // sample at 1/n, ..., (n-1)/n\n\n    for (let i = 1; i < count; i++) {\n      const alpha = i * step\n\n      for (let j = 0; j < this.map.length - 1; j++) {\n        if (alpha > this.map[j][0] && alpha <= this.map[j + 1][0]) {\n          const min = this.map[j][0]\n          const max = this.map[j + 1][0]\n\n          minColor.setHex(this.map[j][1], 'srgb-linear')\n          maxColor.setHex(this.map[j + 1][1], 'srgb-linear')\n\n          const color = new Color().lerpColors(minColor, maxColor, (alpha - min) / (max - min))\n\n          this.lut.push(color)\n        }\n      }\n    }\n\n    // sample at 1\n\n    this.lut.push(new Color(this.map[this.map.length - 1][1]))\n\n    return this\n  }\n\n  copy(lut) {\n    this.lut = lut.lut\n    this.map = lut.map\n    this.n = lut.n\n    this.minV = lut.minV\n    this.maxV = lut.maxV\n\n    return this\n  }\n\n  getColor(alpha) {\n    alpha = MathUtils.clamp(alpha, this.minV, this.maxV)\n\n    alpha = (alpha - this.minV) / (this.maxV - this.minV)\n\n    const colorPosition = Math.round(alpha * this.n)\n\n    return this.lut[colorPosition]\n  }\n\n  addColorMap(name, arrayOfColors) {\n    ColorMapKeywords[name] = arrayOfColors\n\n    return this\n  }\n\n  createCanvas() {\n    const canvas = document.createElement('canvas')\n    canvas.width = 1\n    canvas.height = this.n\n\n    this.updateCanvas(canvas)\n\n    return canvas\n  }\n\n  updateCanvas(canvas) {\n    const ctx = canvas.getContext('2d', { alpha: false })\n\n    const imageData = ctx.getImageData(0, 0, 1, this.n)\n\n    const data = imageData.data\n\n    let k = 0\n\n    const step = 1.0 / this.n\n\n    const minColor = new Color()\n    const maxColor = new Color()\n    const finalColor = new Color()\n\n    for (let i = 1; i >= 0; i -= step) {\n      for (let j = this.map.length - 1; j >= 0; j--) {\n        if (i < this.map[j][0] && i >= this.map[j - 1][0]) {\n          const min = this.map[j - 1][0]\n          const max = this.map[j][0]\n\n          minColor.setHex(this.map[j - 1][1], 'srgb-linear')\n          maxColor.setHex(this.map[j][1], 'srgb-linear')\n\n          finalColor.lerpColors(minColor, maxColor, (i - min) / (max - min))\n\n          data[k * 4] = Math.round(finalColor.r * 255)\n          data[k * 4 + 1] = Math.round(finalColor.g * 255)\n          data[k * 4 + 2] = Math.round(finalColor.b * 255)\n          data[k * 4 + 3] = 255\n\n          k += 1\n        }\n      }\n    }\n\n    ctx.putImageData(imageData, 0, 0)\n\n    return canvas\n  }\n}\n\nconst ColorMapKeywords = {\n  rainbow: [\n    [0.0, 0x0000ff],\n    [0.2, 0x00ffff],\n    [0.5, 0x00ff00],\n    [0.8, 0xffff00],\n    [1.0, 0xff0000],\n  ],\n  cooltowarm: [\n    [0.0, 0x3c4ec2],\n    [0.2, 0x9bbcff],\n    [0.5, 0xdcdcdc],\n    [0.8, 0xf6a385],\n    [1.0, 0xb40426],\n  ],\n  blackbody: [\n    [0.0, 0x000000],\n    [0.2, 0x780000],\n    [0.5, 0xe63200],\n    [0.8, 0xffff00],\n    [1.0, 0xffffff],\n  ],\n  grayscale: [\n    [0.0, 0x000000],\n    [0.2, 0x404040],\n    [0.5, 0x7f7f80],\n    [0.8, 0xbfbfbf],\n    [1.0, 0xffffff],\n  ],\n}\n\nexport { Lut, ColorMapKeywords }\n"], "mappings": ";AAEA,MAAMA,GAAA,CAAI;EACRC,YAAYC,QAAA,EAAUC,KAAA,GAAQ,IAAI;IAChC,KAAKC,KAAA,GAAQ;IAEb,KAAKC,GAAA,GAAM,EAAE;IACb,KAAKC,GAAA,GAAM,EAAE;IACb,KAAKC,CAAA,GAAI;IACT,KAAKC,IAAA,GAAO;IACZ,KAAKC,IAAA,GAAO;IAEZ,KAAKC,WAAA,CAAYR,QAAA,EAAUC,KAAK;EACjC;EAEDQ,IAAIC,KAAA,EAAO;IACT,IAAIA,KAAA,CAAMR,KAAA,KAAU,MAAM;MACxB,KAAKS,IAAA,CAAKD,KAAK;IAChB;IAED,OAAO;EACR;EAEDE,OAAOC,GAAA,EAAK;IACV,KAAKP,IAAA,GAAOO,GAAA;IAEZ,OAAO;EACR;EAEDC,OAAOC,GAAA,EAAK;IACV,KAAKR,IAAA,GAAOQ,GAAA;IAEZ,OAAO;EACR;EAEDP,YAAYR,QAAA,EAAUC,KAAA,GAAQ,IAAI;IAChC,KAAKG,GAAA,GAAMY,gBAAA,CAAiBhB,QAAQ,KAAKgB,gBAAA,CAAiBC,OAAA;IAC1D,KAAKZ,CAAA,GAAIJ,KAAA;IAET,MAAMiB,IAAA,GAAO,IAAM,KAAKb,CAAA;IACxB,MAAMc,QAAA,GAAW,IAAIC,KAAA,CAAO;IAC5B,MAAMC,QAAA,GAAW,IAAID,KAAA,CAAO;IAE5B,KAAKjB,GAAA,CAAImB,MAAA,GAAS;IAIlB,KAAKnB,GAAA,CAAIoB,IAAA,CAAK,IAAIH,KAAA,CAAM,KAAKhB,GAAA,CAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAIvC,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAIvB,KAAA,EAAOuB,CAAA,IAAK;MAC9B,MAAMC,KAAA,GAAQD,CAAA,GAAIN,IAAA;MAElB,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKtB,GAAA,CAAIkB,MAAA,GAAS,GAAGI,CAAA,IAAK;QAC5C,IAAID,KAAA,GAAQ,KAAKrB,GAAA,CAAIsB,CAAC,EAAE,CAAC,KAAKD,KAAA,IAAS,KAAKrB,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC,GAAG;UACzD,MAAMb,GAAA,GAAM,KAAKT,GAAA,CAAIsB,CAAC,EAAE,CAAC;UACzB,MAAMX,GAAA,GAAM,KAAKX,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC;UAE7BP,QAAA,CAASQ,MAAA,CAAO,KAAKvB,GAAA,CAAIsB,CAAC,EAAE,CAAC,GAAG,aAAa;UAC7CL,QAAA,CAASM,MAAA,CAAO,KAAKvB,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC,GAAG,aAAa;UAEjD,MAAME,KAAA,GAAQ,IAAIR,KAAA,CAAO,EAACS,UAAA,CAAWV,QAAA,EAAUE,QAAA,GAAWI,KAAA,GAAQZ,GAAA,KAAQE,GAAA,GAAMF,GAAA,CAAI;UAEpF,KAAKV,GAAA,CAAIoB,IAAA,CAAKK,KAAK;QACpB;MACF;IACF;IAID,KAAKzB,GAAA,CAAIoB,IAAA,CAAK,IAAIH,KAAA,CAAM,KAAKhB,GAAA,CAAI,KAAKA,GAAA,CAAIkB,MAAA,GAAS,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO;EACR;EAEDX,KAAKR,GAAA,EAAK;IACR,KAAKA,GAAA,GAAMA,GAAA,CAAIA,GAAA;IACf,KAAKC,GAAA,GAAMD,GAAA,CAAIC,GAAA;IACf,KAAKC,CAAA,GAAIF,GAAA,CAAIE,CAAA;IACb,KAAKC,IAAA,GAAOH,GAAA,CAAIG,IAAA;IAChB,KAAKC,IAAA,GAAOJ,GAAA,CAAII,IAAA;IAEhB,OAAO;EACR;EAEDuB,SAASL,KAAA,EAAO;IACdA,KAAA,GAAQM,SAAA,CAAUC,KAAA,CAAMP,KAAA,EAAO,KAAKnB,IAAA,EAAM,KAAKC,IAAI;IAEnDkB,KAAA,IAASA,KAAA,GAAQ,KAAKnB,IAAA,KAAS,KAAKC,IAAA,GAAO,KAAKD,IAAA;IAEhD,MAAM2B,aAAA,GAAgBC,IAAA,CAAKC,KAAA,CAAMV,KAAA,GAAQ,KAAKpB,CAAC;IAE/C,OAAO,KAAKF,GAAA,CAAI8B,aAAa;EAC9B;EAEDG,YAAYC,IAAA,EAAMC,aAAA,EAAe;IAC/BtB,gBAAA,CAAiBqB,IAAI,IAAIC,aAAA;IAEzB,OAAO;EACR;EAEDC,aAAA,EAAe;IACb,MAAMC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC9CF,MAAA,CAAOG,KAAA,GAAQ;IACfH,MAAA,CAAOI,MAAA,GAAS,KAAKvC,CAAA;IAErB,KAAKwC,YAAA,CAAaL,MAAM;IAExB,OAAOA,MAAA;EACR;EAEDK,aAAaL,MAAA,EAAQ;IACnB,MAAMM,GAAA,GAAMN,MAAA,CAAOO,UAAA,CAAW,MAAM;MAAEtB,KAAA,EAAO;IAAA,CAAO;IAEpD,MAAMuB,SAAA,GAAYF,GAAA,CAAIG,YAAA,CAAa,GAAG,GAAG,GAAG,KAAK5C,CAAC;IAElD,MAAM6C,IAAA,GAAOF,SAAA,CAAUE,IAAA;IAEvB,IAAIC,CAAA,GAAI;IAER,MAAMjC,IAAA,GAAO,IAAM,KAAKb,CAAA;IAExB,MAAMc,QAAA,GAAW,IAAIC,KAAA,CAAO;IAC5B,MAAMC,QAAA,GAAW,IAAID,KAAA,CAAO;IAC5B,MAAMgC,UAAA,GAAa,IAAIhC,KAAA,CAAO;IAE9B,SAASI,CAAA,GAAI,GAAGA,CAAA,IAAK,GAAGA,CAAA,IAAKN,IAAA,EAAM;MACjC,SAASQ,CAAA,GAAI,KAAKtB,GAAA,CAAIkB,MAAA,GAAS,GAAGI,CAAA,IAAK,GAAGA,CAAA,IAAK;QAC7C,IAAIF,CAAA,GAAI,KAAKpB,GAAA,CAAIsB,CAAC,EAAE,CAAC,KAAKF,CAAA,IAAK,KAAKpB,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC,GAAG;UACjD,MAAMb,GAAA,GAAM,KAAKT,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC;UAC7B,MAAMX,GAAA,GAAM,KAAKX,GAAA,CAAIsB,CAAC,EAAE,CAAC;UAEzBP,QAAA,CAASQ,MAAA,CAAO,KAAKvB,GAAA,CAAIsB,CAAA,GAAI,CAAC,EAAE,CAAC,GAAG,aAAa;UACjDL,QAAA,CAASM,MAAA,CAAO,KAAKvB,GAAA,CAAIsB,CAAC,EAAE,CAAC,GAAG,aAAa;UAE7C0B,UAAA,CAAWvB,UAAA,CAAWV,QAAA,EAAUE,QAAA,GAAWG,CAAA,GAAIX,GAAA,KAAQE,GAAA,GAAMF,GAAA,CAAI;UAEjEqC,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAIjB,IAAA,CAAKC,KAAA,CAAMiB,UAAA,CAAWC,CAAA,GAAI,GAAG;UAC3CH,IAAA,CAAKC,CAAA,GAAI,IAAI,CAAC,IAAIjB,IAAA,CAAKC,KAAA,CAAMiB,UAAA,CAAWE,CAAA,GAAI,GAAG;UAC/CJ,IAAA,CAAKC,CAAA,GAAI,IAAI,CAAC,IAAIjB,IAAA,CAAKC,KAAA,CAAMiB,UAAA,CAAWG,CAAA,GAAI,GAAG;UAC/CL,IAAA,CAAKC,CAAA,GAAI,IAAI,CAAC,IAAI;UAElBA,CAAA,IAAK;QACN;MACF;IACF;IAEDL,GAAA,CAAIU,YAAA,CAAaR,SAAA,EAAW,GAAG,CAAC;IAEhC,OAAOR,MAAA;EACR;AACH;AAEK,MAACxB,gBAAA,GAAmB;EACvBC,OAAA,EAAS,CACP,CAAC,GAAK,GAAQ,GACd,CAAC,KAAK,KAAQ,GACd,CAAC,KAAK,KAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,GAAK,QAAQ,EACf;EACDwC,UAAA,EAAY,CACV,CAAC,GAAK,OAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,GAAK,QAAQ,EACf;EACDC,SAAA,EAAW,CACT,CAAC,GAAK,CAAQ,GACd,CAAC,KAAK,OAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,GAAK,QAAQ,EACf;EACDC,SAAA,EAAW,CACT,CAAC,GAAK,CAAQ,GACd,CAAC,KAAK,OAAQ,GACd,CAAC,KAAK,OAAQ,GACd,CAAC,KAAK,QAAQ,GACd,CAAC,GAAK,QAAQ;AAElB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}