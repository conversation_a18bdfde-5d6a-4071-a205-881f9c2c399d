{"ast": null, "code": "import { Loader, FileLoader, <PERSON>ufferGeometry, BufferAttribute } from \"three\";\nconst _taskCache = /* @__PURE__ */new WeakMap();\nclass DRACOLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.decoderPath = \"\";\n    this.decoderConfig = {};\n    this.decoderBinary = null;\n    this.decoderPending = null;\n    this.workerLimit = 4;\n    this.workerPool = [];\n    this.workerNextTaskID = 1;\n    this.workerSourceURL = \"\";\n    this.defaultAttributeIDs = {\n      position: \"POSITION\",\n      normal: \"NORMAL\",\n      color: \"COLOR\",\n      uv: \"TEX_COORD\"\n    };\n    this.defaultAttributeTypes = {\n      position: \"Float32Array\",\n      normal: \"Float32Array\",\n      color: \"Float32Array\",\n      uv: \"Float32Array\"\n    };\n  }\n  setDecoderPath(path) {\n    this.decoderPath = path;\n    return this;\n  }\n  setDecoderConfig(config) {\n    this.decoderConfig = config;\n    return this;\n  }\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, buffer => {\n      const taskConfig = {\n        attributeIDs: this.defaultAttributeIDs,\n        attributeTypes: this.defaultAttributeTypes,\n        useUniqueIDs: false\n      };\n      this.decodeGeometry(buffer, taskConfig).then(onLoad).catch(onError);\n    }, onProgress, onError);\n  }\n  /** @deprecated Kept for backward-compatibility with previous DRACOLoader versions. */\n  decodeDracoFile(buffer, callback, attributeIDs, attributeTypes) {\n    const taskConfig = {\n      attributeIDs: attributeIDs || this.defaultAttributeIDs,\n      attributeTypes: attributeTypes || this.defaultAttributeTypes,\n      useUniqueIDs: !!attributeIDs\n    };\n    this.decodeGeometry(buffer, taskConfig).then(callback);\n  }\n  decodeGeometry(buffer, taskConfig) {\n    for (const attribute in taskConfig.attributeTypes) {\n      const type = taskConfig.attributeTypes[attribute];\n      if (type.BYTES_PER_ELEMENT !== void 0) {\n        taskConfig.attributeTypes[attribute] = type.name;\n      }\n    }\n    const taskKey = JSON.stringify(taskConfig);\n    if (_taskCache.has(buffer)) {\n      const cachedTask = _taskCache.get(buffer);\n      if (cachedTask.key === taskKey) {\n        return cachedTask.promise;\n      } else if (buffer.byteLength === 0) {\n        throw new Error(\"THREE.DRACOLoader: Unable to re-decode a buffer with different settings. Buffer has already been transferred.\");\n      }\n    }\n    let worker;\n    const taskID = this.workerNextTaskID++;\n    const taskCost = buffer.byteLength;\n    const geometryPending = this._getWorker(taskID, taskCost).then(_worker => {\n      worker = _worker;\n      return new Promise((resolve, reject) => {\n        worker._callbacks[taskID] = {\n          resolve,\n          reject\n        };\n        worker.postMessage({\n          type: \"decode\",\n          id: taskID,\n          taskConfig,\n          buffer\n        }, [buffer]);\n      });\n    }).then(message => this._createGeometry(message.geometry));\n    geometryPending.catch(() => true).then(() => {\n      if (worker && taskID) {\n        this._releaseTask(worker, taskID);\n      }\n    });\n    _taskCache.set(buffer, {\n      key: taskKey,\n      promise: geometryPending\n    });\n    return geometryPending;\n  }\n  _createGeometry(geometryData) {\n    const geometry = new BufferGeometry();\n    if (geometryData.index) {\n      geometry.setIndex(new BufferAttribute(geometryData.index.array, 1));\n    }\n    for (let i = 0; i < geometryData.attributes.length; i++) {\n      const attribute = geometryData.attributes[i];\n      const name = attribute.name;\n      const array = attribute.array;\n      const itemSize = attribute.itemSize;\n      geometry.setAttribute(name, new BufferAttribute(array, itemSize));\n    }\n    return geometry;\n  }\n  _loadLibrary(url, responseType) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.decoderPath);\n    loader.setResponseType(responseType);\n    loader.setWithCredentials(this.withCredentials);\n    return new Promise((resolve, reject) => {\n      loader.load(url, resolve, void 0, reject);\n    });\n  }\n  preload() {\n    this._initDecoder();\n    return this;\n  }\n  _initDecoder() {\n    if (this.decoderPending) return this.decoderPending;\n    const useJS = typeof WebAssembly !== \"object\" || this.decoderConfig.type === \"js\";\n    const librariesPending = [];\n    if (useJS) {\n      librariesPending.push(this._loadLibrary(\"draco_decoder.js\", \"text\"));\n    } else {\n      librariesPending.push(this._loadLibrary(\"draco_wasm_wrapper.js\", \"text\"));\n      librariesPending.push(this._loadLibrary(\"draco_decoder.wasm\", \"arraybuffer\"));\n    }\n    this.decoderPending = Promise.all(librariesPending).then(libraries => {\n      const jsContent = libraries[0];\n      if (!useJS) {\n        this.decoderConfig.wasmBinary = libraries[1];\n      }\n      const fn = DRACOWorker.toString();\n      const body = [\"/* draco decoder */\", jsContent, \"\", \"/* worker */\", fn.substring(fn.indexOf(\"{\") + 1, fn.lastIndexOf(\"}\"))].join(\"\\n\");\n      this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n    });\n    return this.decoderPending;\n  }\n  _getWorker(taskID, taskCost) {\n    return this._initDecoder().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker2 = new Worker(this.workerSourceURL);\n        worker2._callbacks = {};\n        worker2._taskCosts = {};\n        worker2._taskLoad = 0;\n        worker2.postMessage({\n          type: \"init\",\n          decoderConfig: this.decoderConfig\n        });\n        worker2.onmessage = function (e) {\n          const message = e.data;\n          switch (message.type) {\n            case \"decode\":\n              worker2._callbacks[message.id].resolve(message);\n              break;\n            case \"error\":\n              worker2._callbacks[message.id].reject(message);\n              break;\n            default:\n              console.error('THREE.DRACOLoader: Unexpected message, \"' + message.type + '\"');\n          }\n        };\n        this.workerPool.push(worker2);\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1;\n        });\n      }\n      const worker = this.workerPool[this.workerPool.length - 1];\n      worker._taskCosts[taskID] = taskCost;\n      worker._taskLoad += taskCost;\n      return worker;\n    });\n  }\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID];\n    delete worker._callbacks[taskID];\n    delete worker._taskCosts[taskID];\n  }\n  debug() {\n    console.log(\"Task load: \", this.workerPool.map(worker => worker._taskLoad));\n  }\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate();\n    }\n    this.workerPool.length = 0;\n    return this;\n  }\n}\nfunction DRACOWorker() {\n  let decoderConfig;\n  let decoderPending;\n  onmessage = function (e) {\n    const message = e.data;\n    switch (message.type) {\n      case \"init\":\n        decoderConfig = message.decoderConfig;\n        decoderPending = new Promise(function (resolve) {\n          decoderConfig.onModuleLoaded = function (draco) {\n            resolve({\n              draco\n            });\n          };\n          DracoDecoderModule(decoderConfig);\n        });\n        break;\n      case \"decode\":\n        const buffer = message.buffer;\n        const taskConfig = message.taskConfig;\n        decoderPending.then(module => {\n          const draco = module.draco;\n          const decoder = new draco.Decoder();\n          const decoderBuffer = new draco.DecoderBuffer();\n          decoderBuffer.Init(new Int8Array(buffer), buffer.byteLength);\n          try {\n            const geometry = decodeGeometry(draco, decoder, decoderBuffer, taskConfig);\n            const buffers = geometry.attributes.map(attr => attr.array.buffer);\n            if (geometry.index) buffers.push(geometry.index.array.buffer);\n            self.postMessage({\n              type: \"decode\",\n              id: message.id,\n              geometry\n            }, buffers);\n          } catch (error) {\n            console.error(error);\n            self.postMessage({\n              type: \"error\",\n              id: message.id,\n              error: error.message\n            });\n          } finally {\n            draco.destroy(decoderBuffer);\n            draco.destroy(decoder);\n          }\n        });\n        break;\n    }\n  };\n  function decodeGeometry(draco, decoder, decoderBuffer, taskConfig) {\n    const attributeIDs = taskConfig.attributeIDs;\n    const attributeTypes = taskConfig.attributeTypes;\n    let dracoGeometry;\n    let decodingStatus;\n    const geometryType = decoder.GetEncodedGeometryType(decoderBuffer);\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      dracoGeometry = new draco.Mesh();\n      decodingStatus = decoder.DecodeBufferToMesh(decoderBuffer, dracoGeometry);\n    } else if (geometryType === draco.POINT_CLOUD) {\n      dracoGeometry = new draco.PointCloud();\n      decodingStatus = decoder.DecodeBufferToPointCloud(decoderBuffer, dracoGeometry);\n    } else {\n      throw new Error(\"THREE.DRACOLoader: Unexpected geometry type.\");\n    }\n    if (!decodingStatus.ok() || dracoGeometry.ptr === 0) {\n      throw new Error(\"THREE.DRACOLoader: Decoding failed: \" + decodingStatus.error_msg());\n    }\n    const geometry = {\n      index: null,\n      attributes: []\n    };\n    for (const attributeName in attributeIDs) {\n      const attributeType = self[attributeTypes[attributeName]];\n      let attribute;\n      let attributeID;\n      if (taskConfig.useUniqueIDs) {\n        attributeID = attributeIDs[attributeName];\n        attribute = decoder.GetAttributeByUniqueId(dracoGeometry, attributeID);\n      } else {\n        attributeID = decoder.GetAttributeId(dracoGeometry, draco[attributeIDs[attributeName]]);\n        if (attributeID === -1) continue;\n        attribute = decoder.GetAttribute(dracoGeometry, attributeID);\n      }\n      geometry.attributes.push(decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute));\n    }\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      geometry.index = decodeIndex(draco, decoder, dracoGeometry);\n    }\n    draco.destroy(dracoGeometry);\n    return geometry;\n  }\n  function decodeIndex(draco, decoder, dracoGeometry) {\n    const numFaces = dracoGeometry.num_faces();\n    const numIndices = numFaces * 3;\n    const byteLength = numIndices * 4;\n    const ptr = draco._malloc(byteLength);\n    decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr);\n    const index = new Uint32Array(draco.HEAPF32.buffer, ptr, numIndices).slice();\n    draco._free(ptr);\n    return {\n      array: index,\n      itemSize: 1\n    };\n  }\n  function decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute) {\n    const numComponents = attribute.num_components();\n    const numPoints = dracoGeometry.num_points();\n    const numValues = numPoints * numComponents;\n    const byteLength = numValues * attributeType.BYTES_PER_ELEMENT;\n    const dataType = getDracoDataType(draco, attributeType);\n    const ptr = draco._malloc(byteLength);\n    decoder.GetAttributeDataArrayForAllPoints(dracoGeometry, attribute, dataType, byteLength, ptr);\n    const array = new attributeType(draco.HEAPF32.buffer, ptr, numValues).slice();\n    draco._free(ptr);\n    return {\n      name: attributeName,\n      array,\n      itemSize: numComponents\n    };\n  }\n  function getDracoDataType(draco, attributeType) {\n    switch (attributeType) {\n      case Float32Array:\n        return draco.DT_FLOAT32;\n      case Int8Array:\n        return draco.DT_INT8;\n      case Int16Array:\n        return draco.DT_INT16;\n      case Int32Array:\n        return draco.DT_INT32;\n      case Uint8Array:\n        return draco.DT_UINT8;\n      case Uint16Array:\n        return draco.DT_UINT16;\n      case Uint32Array:\n        return draco.DT_UINT32;\n    }\n  }\n}\nexport { DRACOLoader };", "map": {"version": 3, "names": ["_taskCache", "WeakMap", "DRACOLoader", "Loader", "constructor", "manager", "decoder<PERSON><PERSON>", "decoderConfig", "decoderBinary", "decoderPending", "workerLimit", "workerPool", "workerNextTaskID", "workerSourceURL", "defaultAttributeIDs", "position", "normal", "color", "uv", "defaultAttributeTypes", "setDecoderPath", "path", "setDecoderConfig", "config", "setWorkerLimit", "load", "url", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "taskConfig", "attributeIDs", "attributeTypes", "useUniqueIDs", "decodeGeometry", "then", "catch", "decodeDracoFile", "callback", "attribute", "type", "BYTES_PER_ELEMENT", "name", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "has", "cachedTask", "get", "key", "promise", "byteLength", "Error", "worker", "taskID", "taskCost", "geometryPending", "_get<PERSON><PERSON><PERSON>", "_worker", "Promise", "resolve", "reject", "_callbacks", "postMessage", "id", "message", "_createGeometry", "geometry", "_releaseTask", "set", "geometryData", "BufferGeometry", "index", "setIndex", "BufferAttribute", "array", "i", "attributes", "length", "itemSize", "setAttribute", "_loadLibrary", "responseType", "preload", "_initDecoder", "useJS", "WebAssembly", "librariesPending", "push", "all", "libraries", "js<PERSON><PERSON><PERSON>", "wasmBinary", "fn", "DRACOWorker", "toString", "body", "substring", "indexOf", "lastIndexOf", "join", "URL", "createObjectURL", "Blob", "worker2", "Worker", "_taskCosts", "_taskLoad", "onmessage", "e", "data", "console", "error", "sort", "a", "b", "debug", "log", "map", "dispose", "terminate", "onModuleLoaded", "draco", "DracoDecoderModule", "module", "decoder", "Decoder", "decoder<PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Init", "Int8Array", "buffers", "attr", "self", "destroy", "dracoGeometry", "decodingStatus", "geometryType", "GetEncodedGeometryType", "TRIANGULAR_MESH", "<PERSON><PERSON>", "DecodeBufferToMesh", "POINT_CLOUD", "PointCloud", "DecodeBufferToPointCloud", "ok", "ptr", "error_msg", "attributeName", "attributeType", "attributeID", "GetAttributeByUniqueId", "GetAttributeId", "GetAttribute", "decodeAttribute", "decodeIndex", "numFaces", "num_faces", "numIndices", "_malloc", "GetTrianglesUInt32Array", "Uint32Array", "HEAPF32", "slice", "_free", "numComponents", "num_components", "numPoints", "num_points", "numValues", "dataType", "getDracoDataType", "GetAttributeDataArrayForAllPoints", "Float32Array", "DT_FLOAT32", "DT_INT8", "Int16Array", "DT_INT16", "Int32Array", "DT_INT32", "Uint8Array", "DT_UINT8", "Uint16Array", "DT_UINT16", "DT_UINT32"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/DRACOLoader.js"], "sourcesContent": ["import { BufferAttribute, <PERSON>ufferGeometry, FileLoader, Loader } from 'three'\n\nconst _taskCache = new WeakMap()\n\nclass DRACOLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.decoderPath = ''\n    this.decoderConfig = {}\n    this.decoderBinary = null\n    this.decoderPending = null\n\n    this.workerLimit = 4\n    this.workerPool = []\n    this.workerNextTaskID = 1\n    this.workerSourceURL = ''\n\n    this.defaultAttributeIDs = {\n      position: 'POSITION',\n      normal: 'NORMAL',\n      color: 'COLOR',\n      uv: 'TEX_COORD',\n    }\n    this.defaultAttributeTypes = {\n      position: 'Float32Array',\n      normal: 'Float32Array',\n      color: 'Float32Array',\n      uv: 'Float32Array',\n    }\n  }\n\n  setDecoderPath(path) {\n    this.decoderPath = path\n\n    return this\n  }\n\n  setDecoderConfig(config) {\n    this.decoderConfig = config\n\n    return this\n  }\n\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit\n\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      (buffer) => {\n        const taskConfig = {\n          attributeIDs: this.defaultAttributeIDs,\n          attributeTypes: this.defaultAttributeTypes,\n          useUniqueIDs: false,\n        }\n\n        this.decodeGeometry(buffer, taskConfig).then(onLoad).catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /** @deprecated Kept for backward-compatibility with previous DRACOLoader versions. */\n  decodeDracoFile(buffer, callback, attributeIDs, attributeTypes) {\n    const taskConfig = {\n      attributeIDs: attributeIDs || this.defaultAttributeIDs,\n      attributeTypes: attributeTypes || this.defaultAttributeTypes,\n      useUniqueIDs: !!attributeIDs,\n    }\n\n    this.decodeGeometry(buffer, taskConfig).then(callback)\n  }\n\n  decodeGeometry(buffer, taskConfig) {\n    // TODO: For backward-compatibility, support 'attributeTypes' objects containing\n    // references (rather than names) to typed array constructors. These must be\n    // serialized before sending them to the worker.\n    for (const attribute in taskConfig.attributeTypes) {\n      const type = taskConfig.attributeTypes[attribute]\n\n      if (type.BYTES_PER_ELEMENT !== undefined) {\n        taskConfig.attributeTypes[attribute] = type.name\n      }\n    }\n\n    //\n\n    const taskKey = JSON.stringify(taskConfig)\n\n    // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n    // again from this thread.\n    if (_taskCache.has(buffer)) {\n      const cachedTask = _taskCache.get(buffer)\n\n      if (cachedTask.key === taskKey) {\n        return cachedTask.promise\n      } else if (buffer.byteLength === 0) {\n        // Technically, it would be possible to wait for the previous task to complete,\n        // transfer the buffer back, and decode again with the second configuration. That\n        // is complex, and I don't know of any reason to decode a Draco buffer twice in\n        // different ways, so this is left unimplemented.\n        throw new Error(\n          'THREE.DRACOLoader: Unable to re-decode a buffer with different ' +\n            'settings. Buffer has already been transferred.',\n        )\n      }\n    }\n\n    //\n\n    let worker\n    const taskID = this.workerNextTaskID++\n    const taskCost = buffer.byteLength\n\n    // Obtain a worker and assign a task, and construct a geometry instance\n    // when the task completes.\n    const geometryPending = this._getWorker(taskID, taskCost)\n      .then((_worker) => {\n        worker = _worker\n\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = { resolve, reject }\n\n          worker.postMessage({ type: 'decode', id: taskID, taskConfig, buffer }, [buffer])\n\n          // this.debug();\n        })\n      })\n      .then((message) => this._createGeometry(message.geometry))\n\n    // Remove task from the task list.\n    // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n    geometryPending\n      .catch(() => true)\n      .then(() => {\n        if (worker && taskID) {\n          this._releaseTask(worker, taskID)\n\n          // this.debug();\n        }\n      })\n\n    // Cache the task result.\n    _taskCache.set(buffer, {\n      key: taskKey,\n      promise: geometryPending,\n    })\n\n    return geometryPending\n  }\n\n  _createGeometry(geometryData) {\n    const geometry = new BufferGeometry()\n\n    if (geometryData.index) {\n      geometry.setIndex(new BufferAttribute(geometryData.index.array, 1))\n    }\n\n    for (let i = 0; i < geometryData.attributes.length; i++) {\n      const attribute = geometryData.attributes[i]\n      const name = attribute.name\n      const array = attribute.array\n      const itemSize = attribute.itemSize\n\n      geometry.setAttribute(name, new BufferAttribute(array, itemSize))\n    }\n\n    return geometry\n  }\n\n  _loadLibrary(url, responseType) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.decoderPath)\n    loader.setResponseType(responseType)\n    loader.setWithCredentials(this.withCredentials)\n\n    return new Promise((resolve, reject) => {\n      loader.load(url, resolve, undefined, reject)\n    })\n  }\n\n  preload() {\n    this._initDecoder()\n\n    return this\n  }\n\n  _initDecoder() {\n    if (this.decoderPending) return this.decoderPending\n\n    const useJS = typeof WebAssembly !== 'object' || this.decoderConfig.type === 'js'\n    const librariesPending = []\n\n    if (useJS) {\n      librariesPending.push(this._loadLibrary('draco_decoder.js', 'text'))\n    } else {\n      librariesPending.push(this._loadLibrary('draco_wasm_wrapper.js', 'text'))\n      librariesPending.push(this._loadLibrary('draco_decoder.wasm', 'arraybuffer'))\n    }\n\n    this.decoderPending = Promise.all(librariesPending).then((libraries) => {\n      const jsContent = libraries[0]\n\n      if (!useJS) {\n        this.decoderConfig.wasmBinary = libraries[1]\n      }\n\n      const fn = DRACOWorker.toString()\n\n      const body = [\n        '/* draco decoder */',\n        jsContent,\n        '',\n        '/* worker */',\n        fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n      ].join('\\n')\n\n      this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n    })\n\n    return this.decoderPending\n  }\n\n  _getWorker(taskID, taskCost) {\n    return this._initDecoder().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker = new Worker(this.workerSourceURL)\n\n        worker._callbacks = {}\n        worker._taskCosts = {}\n        worker._taskLoad = 0\n\n        worker.postMessage({ type: 'init', decoderConfig: this.decoderConfig })\n\n        worker.onmessage = function (e) {\n          const message = e.data\n\n          switch (message.type) {\n            case 'decode':\n              worker._callbacks[message.id].resolve(message)\n              break\n\n            case 'error':\n              worker._callbacks[message.id].reject(message)\n              break\n\n            default:\n              console.error('THREE.DRACOLoader: Unexpected message, \"' + message.type + '\"')\n          }\n        }\n\n        this.workerPool.push(worker)\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1\n        })\n      }\n\n      const worker = this.workerPool[this.workerPool.length - 1]\n      worker._taskCosts[taskID] = taskCost\n      worker._taskLoad += taskCost\n      return worker\n    })\n  }\n\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID]\n    delete worker._callbacks[taskID]\n    delete worker._taskCosts[taskID]\n  }\n\n  debug() {\n    console.log(\n      'Task load: ',\n      this.workerPool.map((worker) => worker._taskLoad),\n    )\n  }\n\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate()\n    }\n\n    this.workerPool.length = 0\n\n    return this\n  }\n}\n\n/* WEB WORKER */\n\nfunction DRACOWorker() {\n  let decoderConfig\n  let decoderPending\n\n  onmessage = function (e) {\n    const message = e.data\n\n    switch (message.type) {\n      case 'init':\n        decoderConfig = message.decoderConfig\n        decoderPending = new Promise(function (resolve /*, reject*/) {\n          decoderConfig.onModuleLoaded = function (draco) {\n            // Module is Promise-like. Wrap before resolving to avoid loop.\n            resolve({ draco: draco })\n          }\n\n          DracoDecoderModule(decoderConfig)\n        })\n        break\n\n      case 'decode':\n        const buffer = message.buffer\n        const taskConfig = message.taskConfig\n        decoderPending.then((module) => {\n          const draco = module.draco\n          const decoder = new draco.Decoder()\n          const decoderBuffer = new draco.DecoderBuffer()\n          decoderBuffer.Init(new Int8Array(buffer), buffer.byteLength)\n\n          try {\n            const geometry = decodeGeometry(draco, decoder, decoderBuffer, taskConfig)\n\n            const buffers = geometry.attributes.map((attr) => attr.array.buffer)\n\n            if (geometry.index) buffers.push(geometry.index.array.buffer)\n\n            self.postMessage({ type: 'decode', id: message.id, geometry }, buffers)\n          } catch (error) {\n            console.error(error)\n\n            self.postMessage({ type: 'error', id: message.id, error: error.message })\n          } finally {\n            draco.destroy(decoderBuffer)\n            draco.destroy(decoder)\n          }\n        })\n        break\n    }\n  }\n\n  function decodeGeometry(draco, decoder, decoderBuffer, taskConfig) {\n    const attributeIDs = taskConfig.attributeIDs\n    const attributeTypes = taskConfig.attributeTypes\n\n    let dracoGeometry\n    let decodingStatus\n\n    const geometryType = decoder.GetEncodedGeometryType(decoderBuffer)\n\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      dracoGeometry = new draco.Mesh()\n      decodingStatus = decoder.DecodeBufferToMesh(decoderBuffer, dracoGeometry)\n    } else if (geometryType === draco.POINT_CLOUD) {\n      dracoGeometry = new draco.PointCloud()\n      decodingStatus = decoder.DecodeBufferToPointCloud(decoderBuffer, dracoGeometry)\n    } else {\n      throw new Error('THREE.DRACOLoader: Unexpected geometry type.')\n    }\n\n    if (!decodingStatus.ok() || dracoGeometry.ptr === 0) {\n      throw new Error('THREE.DRACOLoader: Decoding failed: ' + decodingStatus.error_msg())\n    }\n\n    const geometry = { index: null, attributes: [] }\n\n    // Gather all vertex attributes.\n    for (const attributeName in attributeIDs) {\n      const attributeType = self[attributeTypes[attributeName]]\n\n      let attribute\n      let attributeID\n\n      // A Draco file may be created with default vertex attributes, whose attribute IDs\n      // are mapped 1:1 from their semantic name (POSITION, NORMAL, ...). Alternatively,\n      // a Draco file may contain a custom set of attributes, identified by known unique\n      // IDs. glTF files always do the latter, and `.drc` files typically do the former.\n      if (taskConfig.useUniqueIDs) {\n        attributeID = attributeIDs[attributeName]\n        attribute = decoder.GetAttributeByUniqueId(dracoGeometry, attributeID)\n      } else {\n        attributeID = decoder.GetAttributeId(dracoGeometry, draco[attributeIDs[attributeName]])\n\n        if (attributeID === -1) continue\n\n        attribute = decoder.GetAttribute(dracoGeometry, attributeID)\n      }\n\n      geometry.attributes.push(decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute))\n    }\n\n    // Add index.\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      geometry.index = decodeIndex(draco, decoder, dracoGeometry)\n    }\n\n    draco.destroy(dracoGeometry)\n\n    return geometry\n  }\n\n  function decodeIndex(draco, decoder, dracoGeometry) {\n    const numFaces = dracoGeometry.num_faces()\n    const numIndices = numFaces * 3\n    const byteLength = numIndices * 4\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr)\n    const index = new Uint32Array(draco.HEAPF32.buffer, ptr, numIndices).slice()\n    draco._free(ptr)\n\n    return { array: index, itemSize: 1 }\n  }\n\n  function decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute) {\n    const numComponents = attribute.num_components()\n    const numPoints = dracoGeometry.num_points()\n    const numValues = numPoints * numComponents\n    const byteLength = numValues * attributeType.BYTES_PER_ELEMENT\n    const dataType = getDracoDataType(draco, attributeType)\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetAttributeDataArrayForAllPoints(dracoGeometry, attribute, dataType, byteLength, ptr)\n    const array = new attributeType(draco.HEAPF32.buffer, ptr, numValues).slice()\n    draco._free(ptr)\n\n    return {\n      name: attributeName,\n      array: array,\n      itemSize: numComponents,\n    }\n  }\n\n  function getDracoDataType(draco, attributeType) {\n    switch (attributeType) {\n      case Float32Array:\n        return draco.DT_FLOAT32\n      case Int8Array:\n        return draco.DT_INT8\n      case Int16Array:\n        return draco.DT_INT16\n      case Int32Array:\n        return draco.DT_INT32\n      case Uint8Array:\n        return draco.DT_UINT8\n      case Uint16Array:\n        return draco.DT_UINT16\n      case Uint32Array:\n        return draco.DT_UINT32\n    }\n  }\n}\n\nexport { DRACOLoader }\n"], "mappings": ";AAEA,MAAMA,UAAA,GAAa,mBAAIC,OAAA,CAAS;AAEhC,MAAMC,WAAA,SAAoBC,MAAA,CAAO;EAC/BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,WAAA,GAAc;IACnB,KAAKC,aAAA,GAAgB,CAAE;IACvB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,cAAA,GAAiB;IAEtB,KAAKC,WAAA,GAAc;IACnB,KAAKC,UAAA,GAAa,EAAE;IACpB,KAAKC,gBAAA,GAAmB;IACxB,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,mBAAA,GAAsB;MACzBC,QAAA,EAAU;MACVC,MAAA,EAAQ;MACRC,KAAA,EAAO;MACPC,EAAA,EAAI;IACL;IACD,KAAKC,qBAAA,GAAwB;MAC3BJ,QAAA,EAAU;MACVC,MAAA,EAAQ;MACRC,KAAA,EAAO;MACPC,EAAA,EAAI;IACL;EACF;EAEDE,eAAeC,IAAA,EAAM;IACnB,KAAKf,WAAA,GAAce,IAAA;IAEnB,OAAO;EACR;EAEDC,iBAAiBC,MAAA,EAAQ;IACvB,KAAKhB,aAAA,GAAgBgB,MAAA;IAErB,OAAO;EACR;EAEDC,eAAed,WAAA,EAAa;IAC1B,KAAKA,WAAA,GAAcA,WAAA;IAEnB,OAAO;EACR;EAEDe,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAK1B,OAAO;IAE1CyB,MAAA,CAAOE,OAAA,CAAQ,KAAKX,IAAI;IACxBS,MAAA,CAAOG,eAAA,CAAgB,aAAa;IACpCH,MAAA,CAAOI,gBAAA,CAAiB,KAAKC,aAAa;IAC1CL,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAE9CP,MAAA,CAAOL,IAAA,CACLC,GAAA,EACCY,MAAA,IAAW;MACV,MAAMC,UAAA,GAAa;QACjBC,YAAA,EAAc,KAAK1B,mBAAA;QACnB2B,cAAA,EAAgB,KAAKtB,qBAAA;QACrBuB,YAAA,EAAc;MACf;MAED,KAAKC,cAAA,CAAeL,MAAA,EAAQC,UAAU,EAAEK,IAAA,CAAKjB,MAAM,EAAEkB,KAAA,CAAMhB,OAAO;IACnE,GACDD,UAAA,EACAC,OACD;EACF;EAAA;EAGDiB,gBAAgBR,MAAA,EAAQS,QAAA,EAAUP,YAAA,EAAcC,cAAA,EAAgB;IAC9D,MAAMF,UAAA,GAAa;MACjBC,YAAA,EAAcA,YAAA,IAAgB,KAAK1B,mBAAA;MACnC2B,cAAA,EAAgBA,cAAA,IAAkB,KAAKtB,qBAAA;MACvCuB,YAAA,EAAc,CAAC,CAACF;IACjB;IAED,KAAKG,cAAA,CAAeL,MAAA,EAAQC,UAAU,EAAEK,IAAA,CAAKG,QAAQ;EACtD;EAEDJ,eAAeL,MAAA,EAAQC,UAAA,EAAY;IAIjC,WAAWS,SAAA,IAAaT,UAAA,CAAWE,cAAA,EAAgB;MACjD,MAAMQ,IAAA,GAAOV,UAAA,CAAWE,cAAA,CAAeO,SAAS;MAEhD,IAAIC,IAAA,CAAKC,iBAAA,KAAsB,QAAW;QACxCX,UAAA,CAAWE,cAAA,CAAeO,SAAS,IAAIC,IAAA,CAAKE,IAAA;MAC7C;IACF;IAID,MAAMC,OAAA,GAAUC,IAAA,CAAKC,SAAA,CAAUf,UAAU;IAIzC,IAAIvC,UAAA,CAAWuD,GAAA,CAAIjB,MAAM,GAAG;MAC1B,MAAMkB,UAAA,GAAaxD,UAAA,CAAWyD,GAAA,CAAInB,MAAM;MAExC,IAAIkB,UAAA,CAAWE,GAAA,KAAQN,OAAA,EAAS;QAC9B,OAAOI,UAAA,CAAWG,OAAA;MAC1B,WAAiBrB,MAAA,CAAOsB,UAAA,KAAe,GAAG;QAKlC,MAAM,IAAIC,KAAA,CACR,+GAED;MACF;IACF;IAID,IAAIC,MAAA;IACJ,MAAMC,MAAA,GAAS,KAAKnD,gBAAA;IACpB,MAAMoD,QAAA,GAAW1B,MAAA,CAAOsB,UAAA;IAIxB,MAAMK,eAAA,GAAkB,KAAKC,UAAA,CAAWH,MAAA,EAAQC,QAAQ,EACrDpB,IAAA,CAAMuB,OAAA,IAAY;MACjBL,MAAA,GAASK,OAAA;MAET,OAAO,IAAIC,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;QACtCR,MAAA,CAAOS,UAAA,CAAWR,MAAM,IAAI;UAAEM,OAAA;UAASC;QAAQ;QAE/CR,MAAA,CAAOU,WAAA,CAAY;UAAEvB,IAAA,EAAM;UAAUwB,EAAA,EAAIV,MAAA;UAAQxB,UAAA;UAAYD;QAAA,GAAU,CAACA,MAAM,CAAC;MAGzF,CAAS;IACT,CAAO,EACAM,IAAA,CAAM8B,OAAA,IAAY,KAAKC,eAAA,CAAgBD,OAAA,CAAQE,QAAQ,CAAC;IAI3DX,eAAA,CACGpB,KAAA,CAAM,MAAM,IAAI,EAChBD,IAAA,CAAK,MAAM;MACV,IAAIkB,MAAA,IAAUC,MAAA,EAAQ;QACpB,KAAKc,YAAA,CAAaf,MAAA,EAAQC,MAAM;MAGjC;IACT,CAAO;IAGH/D,UAAA,CAAW8E,GAAA,CAAIxC,MAAA,EAAQ;MACrBoB,GAAA,EAAKN,OAAA;MACLO,OAAA,EAASM;IACf,CAAK;IAED,OAAOA,eAAA;EACR;EAEDU,gBAAgBI,YAAA,EAAc;IAC5B,MAAMH,QAAA,GAAW,IAAII,cAAA,CAAgB;IAErC,IAAID,YAAA,CAAaE,KAAA,EAAO;MACtBL,QAAA,CAASM,QAAA,CAAS,IAAIC,eAAA,CAAgBJ,YAAA,CAAaE,KAAA,CAAMG,KAAA,EAAO,CAAC,CAAC;IACnE;IAED,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIN,YAAA,CAAaO,UAAA,CAAWC,MAAA,EAAQF,CAAA,IAAK;MACvD,MAAMrC,SAAA,GAAY+B,YAAA,CAAaO,UAAA,CAAWD,CAAC;MAC3C,MAAMlC,IAAA,GAAOH,SAAA,CAAUG,IAAA;MACvB,MAAMiC,KAAA,GAAQpC,SAAA,CAAUoC,KAAA;MACxB,MAAMI,QAAA,GAAWxC,SAAA,CAAUwC,QAAA;MAE3BZ,QAAA,CAASa,YAAA,CAAatC,IAAA,EAAM,IAAIgC,eAAA,CAAgBC,KAAA,EAAOI,QAAQ,CAAC;IACjE;IAED,OAAOZ,QAAA;EACR;EAEDc,aAAahE,GAAA,EAAKiE,YAAA,EAAc;IAC9B,MAAM7D,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAK1B,OAAO;IAC1CyB,MAAA,CAAOE,OAAA,CAAQ,KAAK1B,WAAW;IAC/BwB,MAAA,CAAOG,eAAA,CAAgB0D,YAAY;IACnC7D,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAE9C,OAAO,IAAI+B,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;MACtCxC,MAAA,CAAOL,IAAA,CAAKC,GAAA,EAAK2C,OAAA,EAAS,QAAWC,MAAM;IACjD,CAAK;EACF;EAEDsB,QAAA,EAAU;IACR,KAAKC,YAAA,CAAc;IAEnB,OAAO;EACR;EAEDA,aAAA,EAAe;IACb,IAAI,KAAKpF,cAAA,EAAgB,OAAO,KAAKA,cAAA;IAErC,MAAMqF,KAAA,GAAQ,OAAOC,WAAA,KAAgB,YAAY,KAAKxF,aAAA,CAAc0C,IAAA,KAAS;IAC7E,MAAM+C,gBAAA,GAAmB,EAAE;IAE3B,IAAIF,KAAA,EAAO;MACTE,gBAAA,CAAiBC,IAAA,CAAK,KAAKP,YAAA,CAAa,oBAAoB,MAAM,CAAC;IACzE,OAAW;MACLM,gBAAA,CAAiBC,IAAA,CAAK,KAAKP,YAAA,CAAa,yBAAyB,MAAM,CAAC;MACxEM,gBAAA,CAAiBC,IAAA,CAAK,KAAKP,YAAA,CAAa,sBAAsB,aAAa,CAAC;IAC7E;IAED,KAAKjF,cAAA,GAAiB2D,OAAA,CAAQ8B,GAAA,CAAIF,gBAAgB,EAAEpD,IAAA,CAAMuD,SAAA,IAAc;MACtE,MAAMC,SAAA,GAAYD,SAAA,CAAU,CAAC;MAE7B,IAAI,CAACL,KAAA,EAAO;QACV,KAAKvF,aAAA,CAAc8F,UAAA,GAAaF,SAAA,CAAU,CAAC;MAC5C;MAED,MAAMG,EAAA,GAAKC,WAAA,CAAYC,QAAA,CAAU;MAEjC,MAAMC,IAAA,GAAO,CACX,uBACAL,SAAA,EACA,IACA,gBACAE,EAAA,CAAGI,SAAA,CAAUJ,EAAA,CAAGK,OAAA,CAAQ,GAAG,IAAI,GAAGL,EAAA,CAAGM,WAAA,CAAY,GAAG,CAAC,EAC7D,CAAQC,IAAA,CAAK,IAAI;MAEX,KAAKhG,eAAA,GAAkBiG,GAAA,CAAIC,eAAA,CAAgB,IAAIC,IAAA,CAAK,CAACP,IAAI,CAAC,CAAC;IACjE,CAAK;IAED,OAAO,KAAKhG,cAAA;EACb;EAEDyD,WAAWH,MAAA,EAAQC,QAAA,EAAU;IAC3B,OAAO,KAAK6B,YAAA,GAAejD,IAAA,CAAK,MAAM;MACpC,IAAI,KAAKjC,UAAA,CAAW4E,MAAA,GAAS,KAAK7E,WAAA,EAAa;QAC7C,MAAMuG,OAAA,GAAS,IAAIC,MAAA,CAAO,KAAKrG,eAAe;QAE9CoG,OAAA,CAAO1C,UAAA,GAAa,CAAE;QACtB0C,OAAA,CAAOE,UAAA,GAAa,CAAE;QACtBF,OAAA,CAAOG,SAAA,GAAY;QAEnBH,OAAA,CAAOzC,WAAA,CAAY;UAAEvB,IAAA,EAAM;UAAQ1C,aAAA,EAAe,KAAKA;QAAA,CAAe;QAEtE0G,OAAA,CAAOI,SAAA,GAAY,UAAUC,CAAA,EAAG;UAC9B,MAAM5C,OAAA,GAAU4C,CAAA,CAAEC,IAAA;UAElB,QAAQ7C,OAAA,CAAQzB,IAAA;YACd,KAAK;cACHgE,OAAA,CAAO1C,UAAA,CAAWG,OAAA,CAAQD,EAAE,EAAEJ,OAAA,CAAQK,OAAO;cAC7C;YAEF,KAAK;cACHuC,OAAA,CAAO1C,UAAA,CAAWG,OAAA,CAAQD,EAAE,EAAEH,MAAA,CAAOI,OAAO;cAC5C;YAEF;cACE8C,OAAA,CAAQC,KAAA,CAAM,6CAA6C/C,OAAA,CAAQzB,IAAA,GAAO,GAAG;UAChF;QACF;QAED,KAAKtC,UAAA,CAAWsF,IAAA,CAAKgB,OAAM;MACnC,OAAa;QACL,KAAKtG,UAAA,CAAW+G,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;UACnC,OAAOD,CAAA,CAAEP,SAAA,GAAYQ,CAAA,CAAER,SAAA,GAAY,KAAK;QAClD,CAAS;MACF;MAED,MAAMtD,MAAA,GAAS,KAAKnD,UAAA,CAAW,KAAKA,UAAA,CAAW4E,MAAA,GAAS,CAAC;MACzDzB,MAAA,CAAOqD,UAAA,CAAWpD,MAAM,IAAIC,QAAA;MAC5BF,MAAA,CAAOsD,SAAA,IAAapD,QAAA;MACpB,OAAOF,MAAA;IACb,CAAK;EACF;EAEDe,aAAaf,MAAA,EAAQC,MAAA,EAAQ;IAC3BD,MAAA,CAAOsD,SAAA,IAAatD,MAAA,CAAOqD,UAAA,CAAWpD,MAAM;IAC5C,OAAOD,MAAA,CAAOS,UAAA,CAAWR,MAAM;IAC/B,OAAOD,MAAA,CAAOqD,UAAA,CAAWpD,MAAM;EAChC;EAED8D,MAAA,EAAQ;IACNL,OAAA,CAAQM,GAAA,CACN,eACA,KAAKnH,UAAA,CAAWoH,GAAA,CAAKjE,MAAA,IAAWA,MAAA,CAAOsD,SAAS,CACjD;EACF;EAEDY,QAAA,EAAU;IACR,SAAS3C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK1E,UAAA,CAAW4E,MAAA,EAAQ,EAAEF,CAAA,EAAG;MAC/C,KAAK1E,UAAA,CAAW0E,CAAC,EAAE4C,SAAA,CAAW;IAC/B;IAED,KAAKtH,UAAA,CAAW4E,MAAA,GAAS;IAEzB,OAAO;EACR;AACH;AAIA,SAASgB,YAAA,EAAc;EACrB,IAAIhG,aAAA;EACJ,IAAIE,cAAA;EAEJ4G,SAAA,GAAY,SAAAA,CAAUC,CAAA,EAAG;IACvB,MAAM5C,OAAA,GAAU4C,CAAA,CAAEC,IAAA;IAElB,QAAQ7C,OAAA,CAAQzB,IAAA;MACd,KAAK;QACH1C,aAAA,GAAgBmE,OAAA,CAAQnE,aAAA;QACxBE,cAAA,GAAiB,IAAI2D,OAAA,CAAQ,UAAUC,OAAA,EAAsB;UAC3D9D,aAAA,CAAc2H,cAAA,GAAiB,UAAUC,KAAA,EAAO;YAE9C9D,OAAA,CAAQ;cAAE8D;YAAA,CAAc;UACzB;UAEDC,kBAAA,CAAmB7H,aAAa;QAC1C,CAAS;QACD;MAEF,KAAK;QACH,MAAM+B,MAAA,GAASoC,OAAA,CAAQpC,MAAA;QACvB,MAAMC,UAAA,GAAamC,OAAA,CAAQnC,UAAA;QAC3B9B,cAAA,CAAemC,IAAA,CAAMyF,MAAA,IAAW;UAC9B,MAAMF,KAAA,GAAQE,MAAA,CAAOF,KAAA;UACrB,MAAMG,OAAA,GAAU,IAAIH,KAAA,CAAMI,OAAA,CAAS;UACnC,MAAMC,aAAA,GAAgB,IAAIL,KAAA,CAAMM,aAAA,CAAe;UAC/CD,aAAA,CAAcE,IAAA,CAAK,IAAIC,SAAA,CAAUrG,MAAM,GAAGA,MAAA,CAAOsB,UAAU;UAE3D,IAAI;YACF,MAAMgB,QAAA,GAAWjC,cAAA,CAAewF,KAAA,EAAOG,OAAA,EAASE,aAAA,EAAejG,UAAU;YAEzE,MAAMqG,OAAA,GAAUhE,QAAA,CAASU,UAAA,CAAWyC,GAAA,CAAKc,IAAA,IAASA,IAAA,CAAKzD,KAAA,CAAM9C,MAAM;YAEnE,IAAIsC,QAAA,CAASK,KAAA,EAAO2D,OAAA,CAAQ3C,IAAA,CAAKrB,QAAA,CAASK,KAAA,CAAMG,KAAA,CAAM9C,MAAM;YAE5DwG,IAAA,CAAKtE,WAAA,CAAY;cAAEvB,IAAA,EAAM;cAAUwB,EAAA,EAAIC,OAAA,CAAQD,EAAA;cAAIG;YAAU,GAAEgE,OAAO;UACvE,SAAQnB,KAAA,EAAP;YACAD,OAAA,CAAQC,KAAA,CAAMA,KAAK;YAEnBqB,IAAA,CAAKtE,WAAA,CAAY;cAAEvB,IAAA,EAAM;cAASwB,EAAA,EAAIC,OAAA,CAAQD,EAAA;cAAIgD,KAAA,EAAOA,KAAA,CAAM/C;YAAO,CAAE;UACpF,UAAoB;YACRyD,KAAA,CAAMY,OAAA,CAAQP,aAAa;YAC3BL,KAAA,CAAMY,OAAA,CAAQT,OAAO;UACtB;QACX,CAAS;QACD;IACH;EACF;EAED,SAAS3F,eAAewF,KAAA,EAAOG,OAAA,EAASE,aAAA,EAAejG,UAAA,EAAY;IACjE,MAAMC,YAAA,GAAeD,UAAA,CAAWC,YAAA;IAChC,MAAMC,cAAA,GAAiBF,UAAA,CAAWE,cAAA;IAElC,IAAIuG,aAAA;IACJ,IAAIC,cAAA;IAEJ,MAAMC,YAAA,GAAeZ,OAAA,CAAQa,sBAAA,CAAuBX,aAAa;IAEjE,IAAIU,YAAA,KAAiBf,KAAA,CAAMiB,eAAA,EAAiB;MAC1CJ,aAAA,GAAgB,IAAIb,KAAA,CAAMkB,IAAA,CAAM;MAChCJ,cAAA,GAAiBX,OAAA,CAAQgB,kBAAA,CAAmBd,aAAA,EAAeQ,aAAa;IAC9E,WAAeE,YAAA,KAAiBf,KAAA,CAAMoB,WAAA,EAAa;MAC7CP,aAAA,GAAgB,IAAIb,KAAA,CAAMqB,UAAA,CAAY;MACtCP,cAAA,GAAiBX,OAAA,CAAQmB,wBAAA,CAAyBjB,aAAA,EAAeQ,aAAa;IACpF,OAAW;MACL,MAAM,IAAInF,KAAA,CAAM,8CAA8C;IAC/D;IAED,IAAI,CAACoF,cAAA,CAAeS,EAAA,CAAE,KAAMV,aAAA,CAAcW,GAAA,KAAQ,GAAG;MACnD,MAAM,IAAI9F,KAAA,CAAM,yCAAyCoF,cAAA,CAAeW,SAAA,CAAS,CAAE;IACpF;IAED,MAAMhF,QAAA,GAAW;MAAEK,KAAA,EAAO;MAAMK,UAAA,EAAY;IAAI;IAGhD,WAAWuE,aAAA,IAAiBrH,YAAA,EAAc;MACxC,MAAMsH,aAAA,GAAgBhB,IAAA,CAAKrG,cAAA,CAAeoH,aAAa,CAAC;MAExD,IAAI7G,SAAA;MACJ,IAAI+G,WAAA;MAMJ,IAAIxH,UAAA,CAAWG,YAAA,EAAc;QAC3BqH,WAAA,GAAcvH,YAAA,CAAaqH,aAAa;QACxC7G,SAAA,GAAYsF,OAAA,CAAQ0B,sBAAA,CAAuBhB,aAAA,EAAee,WAAW;MAC7E,OAAa;QACLA,WAAA,GAAczB,OAAA,CAAQ2B,cAAA,CAAejB,aAAA,EAAeb,KAAA,CAAM3F,YAAA,CAAaqH,aAAa,CAAC,CAAC;QAEtF,IAAIE,WAAA,KAAgB,IAAI;QAExB/G,SAAA,GAAYsF,OAAA,CAAQ4B,YAAA,CAAalB,aAAA,EAAee,WAAW;MAC5D;MAEDnF,QAAA,CAASU,UAAA,CAAWW,IAAA,CAAKkE,eAAA,CAAgBhC,KAAA,EAAOG,OAAA,EAASU,aAAA,EAAea,aAAA,EAAeC,aAAA,EAAe9G,SAAS,CAAC;IACjH;IAGD,IAAIkG,YAAA,KAAiBf,KAAA,CAAMiB,eAAA,EAAiB;MAC1CxE,QAAA,CAASK,KAAA,GAAQmF,WAAA,CAAYjC,KAAA,EAAOG,OAAA,EAASU,aAAa;IAC3D;IAEDb,KAAA,CAAMY,OAAA,CAAQC,aAAa;IAE3B,OAAOpE,QAAA;EACR;EAED,SAASwF,YAAYjC,KAAA,EAAOG,OAAA,EAASU,aAAA,EAAe;IAClD,MAAMqB,QAAA,GAAWrB,aAAA,CAAcsB,SAAA,CAAW;IAC1C,MAAMC,UAAA,GAAaF,QAAA,GAAW;IAC9B,MAAMzG,UAAA,GAAa2G,UAAA,GAAa;IAEhC,MAAMZ,GAAA,GAAMxB,KAAA,CAAMqC,OAAA,CAAQ5G,UAAU;IACpC0E,OAAA,CAAQmC,uBAAA,CAAwBzB,aAAA,EAAepF,UAAA,EAAY+F,GAAG;IAC9D,MAAM1E,KAAA,GAAQ,IAAIyF,WAAA,CAAYvC,KAAA,CAAMwC,OAAA,CAAQrI,MAAA,EAAQqH,GAAA,EAAKY,UAAU,EAAEK,KAAA,CAAO;IAC5EzC,KAAA,CAAM0C,KAAA,CAAMlB,GAAG;IAEf,OAAO;MAAEvE,KAAA,EAAOH,KAAA;MAAOO,QAAA,EAAU;IAAG;EACrC;EAED,SAAS2E,gBAAgBhC,KAAA,EAAOG,OAAA,EAASU,aAAA,EAAea,aAAA,EAAeC,aAAA,EAAe9G,SAAA,EAAW;IAC/F,MAAM8H,aAAA,GAAgB9H,SAAA,CAAU+H,cAAA,CAAgB;IAChD,MAAMC,SAAA,GAAYhC,aAAA,CAAciC,UAAA,CAAY;IAC5C,MAAMC,SAAA,GAAYF,SAAA,GAAYF,aAAA;IAC9B,MAAMlH,UAAA,GAAasH,SAAA,GAAYpB,aAAA,CAAc5G,iBAAA;IAC7C,MAAMiI,QAAA,GAAWC,gBAAA,CAAiBjD,KAAA,EAAO2B,aAAa;IAEtD,MAAMH,GAAA,GAAMxB,KAAA,CAAMqC,OAAA,CAAQ5G,UAAU;IACpC0E,OAAA,CAAQ+C,iCAAA,CAAkCrC,aAAA,EAAehG,SAAA,EAAWmI,QAAA,EAAUvH,UAAA,EAAY+F,GAAG;IAC7F,MAAMvE,KAAA,GAAQ,IAAI0E,aAAA,CAAc3B,KAAA,CAAMwC,OAAA,CAAQrI,MAAA,EAAQqH,GAAA,EAAKuB,SAAS,EAAEN,KAAA,CAAO;IAC7EzC,KAAA,CAAM0C,KAAA,CAAMlB,GAAG;IAEf,OAAO;MACLxG,IAAA,EAAM0G,aAAA;MACNzE,KAAA;MACAI,QAAA,EAAUsF;IACX;EACF;EAED,SAASM,iBAAiBjD,KAAA,EAAO2B,aAAA,EAAe;IAC9C,QAAQA,aAAA;MACN,KAAKwB,YAAA;QACH,OAAOnD,KAAA,CAAMoD,UAAA;MACf,KAAK5C,SAAA;QACH,OAAOR,KAAA,CAAMqD,OAAA;MACf,KAAKC,UAAA;QACH,OAAOtD,KAAA,CAAMuD,QAAA;MACf,KAAKC,UAAA;QACH,OAAOxD,KAAA,CAAMyD,QAAA;MACf,KAAKC,UAAA;QACH,OAAO1D,KAAA,CAAM2D,QAAA;MACf,KAAKC,WAAA;QACH,OAAO5D,KAAA,CAAM6D,SAAA;MACf,KAAKtB,WAAA;QACH,OAAOvC,KAAA,CAAM8D,SAAA;IAChB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}