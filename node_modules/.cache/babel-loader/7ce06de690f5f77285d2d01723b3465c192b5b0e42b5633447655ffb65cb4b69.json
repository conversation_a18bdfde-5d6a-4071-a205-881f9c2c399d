{"ast": null, "code": "const CopyShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    opacity: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n    \tgl_FragColor = opacity * texel;\n\n    }\n  `)\n};\nexport { CopyShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "tDiffuse", "value", "opacity", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/CopyShader.ts"], "sourcesContent": ["/**\n * Full-screen textured quad shader\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type CopyShaderUniforms = {\n  opacity: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface ICopyShader extends IShader<CopyShaderUniforms> {}\n\nexport const CopyShader: ICopyShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n    \tgl_FragColor = opacity * texel;\n\n    }\n  `,\n}\n"], "mappings": "AAcO,MAAMA,UAAA,GAA0B;EACrCC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,OAAA,EAAS;MAAED,KAAA,EAAO;IAAI;EACxB;EAEAE,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAc7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}