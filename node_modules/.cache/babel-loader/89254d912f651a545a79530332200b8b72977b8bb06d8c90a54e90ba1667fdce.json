{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/index.tsx\";\nimport { PortfolioLogo } from \"../../common/Image\";\nimport NavMenuList from \"./NavMenuList\";\nimport ThemeToggle from \"../../ThemeToggle\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DesktopNav = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-screen px-[60px] py-[24px] hidden lg:block border-b border-solid border-[#1d1d20] justify-items-center\",\n    style: {\n      transform: \"translateZ(0)\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-[1320px] relative flex items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(PortfolioLogo, {\n        className: \"absolute left-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: /*#__PURE__*/_jsxDEV(NavMenuList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 flex items-center gap-4\",\n        children: /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = DesktopNav;\nexport default DesktopNav;\nvar _c;\n$RefreshReg$(_c, \"DesktopNav\");", "map": {"version": 3, "names": ["PortfolioLogo", "NavMenuList", "ThemeToggle", "jsxDEV", "_jsxDEV", "DesktopNav", "className", "style", "transform", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/index.tsx"], "sourcesContent": ["import { AnimationButton } from \"../../Animation\";\nimport { PortfolioLogo } from \"../../common/Image\";\nimport NavMenuList from \"./NavMenuList\";\nimport ThemeToggle from \"../../ThemeToggle\";\n\nconst DesktopNav = () => {\n  return (\n    <div\n      className=\"w-screen px-[60px] py-[24px] hidden lg:block border-b border-solid border-[#1d1d20] justify-items-center\"\n      style={{\n        transform: \"translateZ(0)\",\n      }}\n    >\n      <div className=\"w-full max-w-[1320px] relative flex items-center justify-center\">\n        <PortfolioLogo className=\"absolute left-0\" />\n        <div className=\"flex\">\n          <NavMenuList />\n        </div>\n        <div className=\"absolute right-0 flex items-center gap-4\">\n          <ThemeToggle />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DesktopNav;\n"], "mappings": ";AACA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IACEE,SAAS,EAAC,0GAA0G;IACpHC,KAAK,EAAE;MACLC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,eAEFL,OAAA;MAAKE,SAAS,EAAC,iEAAiE;MAAAG,QAAA,gBAC9EL,OAAA,CAACJ,aAAa;QAACM,SAAS,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CT,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAG,QAAA,eACnBL,OAAA,CAACH,WAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACNT,OAAA;QAAKE,SAAS,EAAC,0CAA0C;QAAAG,QAAA,eACvDL,OAAA,CAACF,WAAW;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAnBIT,UAAU;AAqBhB,eAAeA,UAAU;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}