{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer, SiNodedotjs, SiPython, SiExpress, SiGraphql, SiMongodb, SiPostgresql, SiRedis, SiGit, SiDocker, SiWebpack, SiAmazonaws, SiVercel } from 'react-icons/si';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Icon mapping with proper react-icons\nconst iconMap = {\n  'SiReact': SiReact,\n  'SiTypescript': SiTypescript,\n  'SiNextdotjs': SiNextdotjs,\n  'SiTailwindcss': SiT<PERSON>windcss,\n  'SiThreedotjs': SiThreedotjs,\n  'SiFramer': <PERSON><PERSON><PERSON><PERSON>,\n  'SiNodedotjs': SiNodedotjs,\n  'SiPython': SiPython,\n  'SiExpress': SiExpress,\n  'SiGraphql': SiGraphql,\n  'SiMongodb': SiMongodb,\n  'SiPostgresql': SiPostgresql,\n  'SiRedis': SiRedis,\n  'SiGit': SiGit,\n  'SiDocker': SiDocker,\n  'SiWebpack': SiWebpack,\n  'SiAmazonaws': SiAmazonaws,\n  'SiVercel': SiVercel\n};\nconst TechStackCard = ({\n  item,\n  index\n}) => {\n  _s();\n  const [isHovered, setIsHovered] = useState(false);\n  const IconComponent = iconMap[item.icon];\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"relative group\",\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      delay: index * 0.1\n    },\n    whileHover: {\n      y: -5,\n      scale: 1.02\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.2,\n            rotate: 5\n          },\n          transition: {\n            type: \"spring\",\n            stiffness: 300\n          },\n          children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 dark:text-gray-300 mb-3\",\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 capitalize\",\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(TechStackCard, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = TechStackCard;\nexport default TechStackCard;\nvar _c;\n$RefreshReg$(_c, \"TechStackCard\");", "map": {"version": 3, "names": ["React", "useState", "motion", "SiReact", "SiTypescript", "SiNextdotjs", "SiTailwindcss", "SiT<PERSON><PERSON>otjs", "<PERSON><PERSON><PERSON><PERSON>", "SiNodedotjs", "SiPython", "SiExpress", "SiGraphql", "SiMongodb", "SiPostgresql", "SiRedis", "SiGit", "<PERSON><PERSON><PERSON><PERSON>", "SiWebpack", "SiAmazonaws", "SiVercel", "jsxDEV", "_jsxDEV", "iconMap", "TechStackCard", "item", "index", "_s", "isHovered", "setIsHovered", "IconComponent", "icon", "div", "className", "initial", "opacity", "y", "animate", "transition", "delay", "whileHover", "scale", "children", "rotate", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "description", "category", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { TechStackItem } from '../../constants/TechStack';\nimport {\n  SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer,\n  SiNodedotjs, SiPython, SiExpress, SiGraphql,\n  SiMongodb, SiPostgresql, SiRedis,\n  SiGit, SiDocker, SiWebpack,\n  SiAmazonaws, SiVercel\n} from 'react-icons/si';\nimport { IconType } from 'react-icons';\n\n// Icon mapping with proper react-icons\nconst iconMap: { [key: string]: IconType } = {\n  'SiReact': SiReact,\n  'SiTypescript': SiTypescript,\n  'SiNextdotjs': SiNextdotjs,\n  'SiTailwindcss': SiTailwindcss,\n  'SiThreedotjs': SiThreedotjs,\n  'SiFramer': <PERSON><PERSON><PERSON><PERSON>,\n  'SiNodedotjs': SiNodedotjs,\n  'SiPython': SiPython,\n  'SiExpress': SiExpress,\n  'SiGraphql': SiGraphql,\n  'SiMongodb': SiMongodb,\n  'SiPostgresql': SiPostgresql,\n  'SiRedis': SiRedis,\n  'SiGit': SiGit,\n  'SiDocker': SiDocker,\n  'SiWebpack': SiWebpack,\n  'SiAmazonaws': SiAmazonaws,\n  'SiVercel': SiVercel,\n};\n\ninterface TechStackCardProps {\n  item: TechStackItem;\n  index: number;\n}\n\nconst TechStackCard: React.FC<TechStackCardProps> = ({ item, index }) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const IconComponent = iconMap[item.icon];\n\n  return (\n    <motion.div\n      className=\"relative group\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: index * 0.1 }}\n      whileHover={{ y: -5, scale: 1.02 }}\n    >\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\">\n        <div className=\"flex flex-col items-center text-center\">\n          <motion.div\n            whileHover={{ scale: 1.2, rotate: 5 }}\n            transition={{ type: \"spring\", stiffness: 300 }}\n          >\n            <IconComponent />\n          </motion.div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n            {item.name}\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-3\">\n            {item.description}\n          </p>\n          <span className=\"inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 capitalize\">\n            {item.category}\n          </span>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TechStackCard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AAEtC,SACEC,OAAO,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EACzEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAC3CC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAChCC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAC1BC,WAAW,EAAEC,QAAQ,QAChB,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxB;AACA,MAAMC,OAAoC,GAAG;EAC3C,SAAS,EAAEpB,OAAO;EAClB,cAAc,EAAEC,YAAY;EAC5B,aAAa,EAAEC,WAAW;EAC1B,eAAe,EAAEC,aAAa;EAC9B,cAAc,EAAEC,YAAY;EAC5B,UAAU,EAAEC,QAAQ;EACpB,aAAa,EAAEC,WAAW;EAC1B,UAAU,EAAEC,QAAQ;EACpB,WAAW,EAAEC,SAAS;EACtB,WAAW,EAAEC,SAAS;EACtB,WAAW,EAAEC,SAAS;EACtB,cAAc,EAAEC,YAAY;EAC5B,SAAS,EAAEC,OAAO;EAClB,OAAO,EAAEC,KAAK;EACd,UAAU,EAAEC,QAAQ;EACpB,WAAW,EAAEC,SAAS;EACtB,aAAa,EAAEC,WAAW;EAC1B,UAAU,EAAEC;AACd,CAAC;AAOD,MAAMI,aAA2C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM6B,aAAa,GAAGP,OAAO,CAACE,IAAI,CAACM,IAAI,CAAC;EAExC,oBACET,OAAA,CAACpB,MAAM,CAAC8B,GAAG;IACTC,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,KAAK,EAAEb,KAAK,GAAG;IAAI,CAAE;IACnCc,UAAU,EAAE;MAAEJ,CAAC,EAAE,CAAC,CAAC;MAAEK,KAAK,EAAE;IAAK,CAAE;IAAAC,QAAA,eAEnCpB,OAAA;MAAKW,SAAS,EAAC,6LAA6L;MAAAS,QAAA,eAC1MpB,OAAA;QAAKW,SAAS,EAAC,wCAAwC;QAAAS,QAAA,gBACrDpB,OAAA,CAACpB,MAAM,CAAC8B,GAAG;UACTQ,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEE,MAAM,EAAE;UAAE,CAAE;UACtCL,UAAU,EAAE;YAAEM,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAAAH,QAAA,eAE/CpB,OAAA,CAACQ,aAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb3B,OAAA;UAAIW,SAAS,EAAC,0DAA0D;UAAAS,QAAA,EACrEjB,IAAI,CAACyB;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACL3B,OAAA;UAAGW,SAAS,EAAC,+CAA+C;UAAAS,QAAA,EACzDjB,IAAI,CAAC0B;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACJ3B,OAAA;UAAMW,SAAS,EAAC,kIAAkI;UAAAS,QAAA,EAC/IjB,IAAI,CAAC2B;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACtB,EAAA,CAjCIH,aAA2C;AAAA6B,EAAA,GAA3C7B,aAA2C;AAmCjD,eAAeA,aAAa;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}