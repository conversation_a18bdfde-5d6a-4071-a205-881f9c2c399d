{"ast": null, "code": "import { DataTexture, FloatType, UnsignedIntType, RGBAFormat, RGIntegerFormat, NearestFilter, BufferAttribute } from 'three';\nimport { FloatVertexAttributeTexture, UIntVertexAttributeTexture } from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, RIGHT_NODE, OFFSET, SPLIT_AXIS } from '../core/utils/nodeBufferUtils.js';\nimport { getIndexArray, getVertexCount } from '../core/build/geometryUtils.js';\nexport class MeshBVHUniformStruct {\n  constructor() {\n    this.index = new UIntVertexAttributeTexture();\n    this.position = new FloatVertexAttributeTexture();\n    this.bvhBounds = new DataTexture();\n    this.bvhContents = new DataTexture();\n    this._cachedIndexAttr = null;\n    this.index.overrideItemSize = 3;\n  }\n  updateFrom(bvh) {\n    const {\n      geometry\n    } = bvh;\n    bvhToTextures(bvh, this.bvhBounds, this.bvhContents);\n    this.position.updateFrom(geometry.attributes.position);\n\n    // dereference a new index attribute if we're using indirect storage\n    if (bvh.indirect) {\n      const indirectBuffer = bvh._indirectBuffer;\n      if (this._cachedIndexAttr === null || this._cachedIndexAttr.count !== indirectBuffer.length) {\n        if (geometry.index) {\n          this._cachedIndexAttr = geometry.index.clone();\n        } else {\n          const array = getIndexArray(getVertexCount(geometry));\n          this._cachedIndexAttr = new BufferAttribute(array, 1, false);\n        }\n      }\n      dereferenceIndex(geometry, indirectBuffer, this._cachedIndexAttr);\n      this.index.updateFrom(this._cachedIndexAttr);\n    } else {\n      this.index.updateFrom(geometry.index);\n    }\n  }\n  dispose() {\n    const {\n      index,\n      position,\n      bvhBounds,\n      bvhContents\n    } = this;\n    if (index) index.dispose();\n    if (position) position.dispose();\n    if (bvhBounds) bvhBounds.dispose();\n    if (bvhContents) bvhContents.dispose();\n  }\n}\nfunction dereferenceIndex(geometry, indirectBuffer, target) {\n  const unpacked = target.array;\n  const indexArray = geometry.index ? geometry.index.array : null;\n  for (let i = 0, l = indirectBuffer.length; i < l; i++) {\n    const i3 = 3 * i;\n    const v3 = 3 * indirectBuffer[i];\n    for (let c = 0; c < 3; c++) {\n      unpacked[i3 + c] = indexArray ? indexArray[v3 + c] : v3 + c;\n    }\n  }\n}\nfunction bvhToTextures(bvh, boundsTexture, contentsTexture) {\n  const roots = bvh._roots;\n  if (roots.length !== 1) {\n    throw new Error('MeshBVHUniformStruct: Multi-root BVHs not supported.');\n  }\n  const root = roots[0];\n  const uint16Array = new Uint16Array(root);\n  const uint32Array = new Uint32Array(root);\n  const float32Array = new Float32Array(root);\n\n  // Both bounds need two elements per node so compute the height so it's twice as long as\n  // the width so we can expand the row by two and still have a square texture\n  const nodeCount = root.byteLength / BYTES_PER_NODE;\n  const boundsDimension = 2 * Math.ceil(Math.sqrt(nodeCount / 2));\n  const boundsArray = new Float32Array(4 * boundsDimension * boundsDimension);\n  const contentsDimension = Math.ceil(Math.sqrt(nodeCount));\n  const contentsArray = new Uint32Array(2 * contentsDimension * contentsDimension);\n  for (let i = 0; i < nodeCount; i++) {\n    const nodeIndex32 = i * BYTES_PER_NODE / 4;\n    const nodeIndex16 = nodeIndex32 * 2;\n    const boundsIndex = BOUNDING_DATA_INDEX(nodeIndex32);\n    for (let b = 0; b < 3; b++) {\n      boundsArray[8 * i + 0 + b] = float32Array[boundsIndex + 0 + b];\n      boundsArray[8 * i + 4 + b] = float32Array[boundsIndex + 3 + b];\n    }\n    if (IS_LEAF(nodeIndex16, uint16Array)) {\n      const count = COUNT(nodeIndex16, uint16Array);\n      const offset = OFFSET(nodeIndex32, uint32Array);\n      const mergedLeafCount = 0xffff0000 | count;\n      contentsArray[i * 2 + 0] = mergedLeafCount;\n      contentsArray[i * 2 + 1] = offset;\n    } else {\n      const rightIndex = 4 * RIGHT_NODE(nodeIndex32, uint32Array) / BYTES_PER_NODE;\n      const splitAxis = SPLIT_AXIS(nodeIndex32, uint32Array);\n      contentsArray[i * 2 + 0] = splitAxis;\n      contentsArray[i * 2 + 1] = rightIndex;\n    }\n  }\n  boundsTexture.image.data = boundsArray;\n  boundsTexture.image.width = boundsDimension;\n  boundsTexture.image.height = boundsDimension;\n  boundsTexture.format = RGBAFormat;\n  boundsTexture.type = FloatType;\n  boundsTexture.internalFormat = 'RGBA32F';\n  boundsTexture.minFilter = NearestFilter;\n  boundsTexture.magFilter = NearestFilter;\n  boundsTexture.generateMipmaps = false;\n  boundsTexture.needsUpdate = true;\n  boundsTexture.dispose();\n  contentsTexture.image.data = contentsArray;\n  contentsTexture.image.width = contentsDimension;\n  contentsTexture.image.height = contentsDimension;\n  contentsTexture.format = RGIntegerFormat;\n  contentsTexture.type = UnsignedIntType;\n  contentsTexture.internalFormat = 'RG32UI';\n  contentsTexture.minFilter = NearestFilter;\n  contentsTexture.magFilter = NearestFilter;\n  contentsTexture.generateMipmaps = false;\n  contentsTexture.needsUpdate = true;\n  contentsTexture.dispose();\n}", "map": {"version": 3, "names": ["DataTexture", "FloatType", "UnsignedIntType", "RGBAFormat", "RGIntegerFormat", "NearestFilter", "BufferAttribute", "FloatVertexAttributeTexture", "UIntVertexAttributeTexture", "BYTES_PER_NODE", "BOUNDING_DATA_INDEX", "COUNT", "IS_LEAF", "RIGHT_NODE", "OFFSET", "SPLIT_AXIS", "getIndexArray", "getVertexCount", "MeshBVHUniformStruct", "constructor", "index", "position", "bvhBounds", "bvhContents", "_cachedIndexAttr", "overrideItemSize", "updateFrom", "bvh", "geometry", "bvhToTextures", "attributes", "indirect", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON>er", "count", "length", "clone", "array", "dereferenceIndex", "dispose", "target", "unpacked", "indexArray", "i", "l", "i3", "v3", "c", "boundsTexture", "contentsTexture", "roots", "_roots", "Error", "root", "uint16Array", "Uint16Array", "uint32Array", "Uint32Array", "float32Array", "Float32Array", "nodeCount", "byteLength", "boundsDimension", "Math", "ceil", "sqrt", "boundsArray", "contentsDimension", "contentsArray", "nodeIndex32", "nodeIndex16", "boundsIndex", "b", "offset", "mergedLeafCount", "rightIndex", "splitAxis", "image", "data", "width", "height", "format", "type", "internalFormat", "minFilter", "magFilter", "generateMipmaps", "needsUpdate"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/three-mesh-bvh/src/gpu/MeshBVHUniformStruct.js"], "sourcesContent": ["import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n\tBufferAttribute,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/utils/nodeBufferUtils.js';\nimport { getIndexArray, getVertexCount } from '../core/build/geometryUtils.js';\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\t\tthis._cachedIndexAttr = null;\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t\t// dereference a new index attribute if we're using indirect storage\n\t\tif ( bvh.indirect ) {\n\n\t\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\t\tif (\n\t\t\t\tthis._cachedIndexAttr === null ||\n\t\t\t\tthis._cachedIndexAttr.count !== indirectBuffer.length\n\t\t\t) {\n\n\t\t\t\tif ( geometry.index ) {\n\n\t\t\t\t\tthis._cachedIndexAttr = geometry.index.clone();\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconst array = getIndexArray( getVertexCount( geometry ) );\n\t\t\t\t\tthis._cachedIndexAttr = new BufferAttribute( array, 1, false );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tdereferenceIndex( geometry, indirectBuffer, this._cachedIndexAttr );\n\t\t\tthis.index.updateFrom( this._cachedIndexAttr );\n\n\t\t} else {\n\n\t\t\tthis.index.updateFrom( geometry.index );\n\n\t\t}\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n\nfunction dereferenceIndex( geometry, indirectBuffer, target ) {\n\n\tconst unpacked = target.array;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst v3 = 3 * indirectBuffer[ i ];\n\t\tfor ( let c = 0; c < 3; c ++ ) {\n\n\t\t\tunpacked[ i3 + c ] = indexArray ? indexArray[ v3 + c ] : v3 + c;\n\n\t\t}\n\n\t}\n\n}\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n"], "mappings": "AAAA,SACCA,WAAW,EACXC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,eAAe,EACfC,aAAa,EACbC,eAAe,QACT,OAAO;AACd,SACCC,2BAA2B,EAC3BC,0BAA0B,QACpB,6BAA6B;AACpC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACCC,mBAAmB,EACnBC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,UAAU,QACJ,kCAAkC;AACzC,SAASC,aAAa,EAAEC,cAAc,QAAQ,gCAAgC;AAE9E,OAAO,MAAMC,oBAAoB,CAAC;EAEjCC,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACC,KAAK,GAAG,IAAIZ,0BAA0B,CAAC,CAAC;IAC7C,IAAI,CAACa,QAAQ,GAAG,IAAId,2BAA2B,CAAC,CAAC;IACjD,IAAI,CAACe,SAAS,GAAG,IAAItB,WAAW,CAAC,CAAC;IAClC,IAAI,CAACuB,WAAW,GAAG,IAAIvB,WAAW,CAAC,CAAC;IACpC,IAAI,CAACwB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACJ,KAAK,CAACK,gBAAgB,GAAG,CAAC;EAEhC;EAEAC,UAAUA,CAAEC,GAAG,EAAG;IAEjB,MAAM;MAAEC;IAAS,CAAC,GAAGD,GAAG;IACxBE,aAAa,CAAEF,GAAG,EAAE,IAAI,CAACL,SAAS,EAAE,IAAI,CAACC,WAAY,CAAC;IAEtD,IAAI,CAACF,QAAQ,CAACK,UAAU,CAAEE,QAAQ,CAACE,UAAU,CAACT,QAAS,CAAC;;IAExD;IACA,IAAKM,GAAG,CAACI,QAAQ,EAAG;MAEnB,MAAMC,cAAc,GAAGL,GAAG,CAACM,eAAe;MAC1C,IACC,IAAI,CAACT,gBAAgB,KAAK,IAAI,IAC9B,IAAI,CAACA,gBAAgB,CAACU,KAAK,KAAKF,cAAc,CAACG,MAAM,EACpD;QAED,IAAKP,QAAQ,CAACR,KAAK,EAAG;UAErB,IAAI,CAACI,gBAAgB,GAAGI,QAAQ,CAACR,KAAK,CAACgB,KAAK,CAAC,CAAC;QAE/C,CAAC,MAAM;UAEN,MAAMC,KAAK,GAAGrB,aAAa,CAAEC,cAAc,CAAEW,QAAS,CAAE,CAAC;UACzD,IAAI,CAACJ,gBAAgB,GAAG,IAAIlB,eAAe,CAAE+B,KAAK,EAAE,CAAC,EAAE,KAAM,CAAC;QAE/D;MAED;MAEAC,gBAAgB,CAAEV,QAAQ,EAAEI,cAAc,EAAE,IAAI,CAACR,gBAAiB,CAAC;MACnE,IAAI,CAACJ,KAAK,CAACM,UAAU,CAAE,IAAI,CAACF,gBAAiB,CAAC;IAE/C,CAAC,MAAM;MAEN,IAAI,CAACJ,KAAK,CAACM,UAAU,CAAEE,QAAQ,CAACR,KAAM,CAAC;IAExC;EAED;EAEAmB,OAAOA,CAAA,EAAG;IAET,MAAM;MAAEnB,KAAK;MAAEC,QAAQ;MAAEC,SAAS;MAAEC;IAAY,CAAC,GAAG,IAAI;IAExD,IAAKH,KAAK,EAAGA,KAAK,CAACmB,OAAO,CAAC,CAAC;IAC5B,IAAKlB,QAAQ,EAAGA,QAAQ,CAACkB,OAAO,CAAC,CAAC;IAClC,IAAKjB,SAAS,EAAGA,SAAS,CAACiB,OAAO,CAAC,CAAC;IACpC,IAAKhB,WAAW,EAAGA,WAAW,CAACgB,OAAO,CAAC,CAAC;EAEzC;AAED;AAEA,SAASD,gBAAgBA,CAAEV,QAAQ,EAAEI,cAAc,EAAEQ,MAAM,EAAG;EAE7D,MAAMC,QAAQ,GAAGD,MAAM,CAACH,KAAK;EAC7B,MAAMK,UAAU,GAAGd,QAAQ,CAACR,KAAK,GAAGQ,QAAQ,CAACR,KAAK,CAACiB,KAAK,GAAG,IAAI;EAC/D,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGZ,cAAc,CAACG,MAAM,EAAEQ,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;IAEzD,MAAME,EAAE,GAAG,CAAC,GAAGF,CAAC;IAChB,MAAMG,EAAE,GAAG,CAAC,GAAGd,cAAc,CAAEW,CAAC,CAAE;IAClC,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9BN,QAAQ,CAAEI,EAAE,GAAGE,CAAC,CAAE,GAAGL,UAAU,GAAGA,UAAU,CAAEI,EAAE,GAAGC,CAAC,CAAE,GAAGD,EAAE,GAAGC,CAAC;IAEhE;EAED;AAED;AAEA,SAASlB,aAAaA,CAAEF,GAAG,EAAEqB,aAAa,EAAEC,eAAe,EAAG;EAE7D,MAAMC,KAAK,GAAGvB,GAAG,CAACwB,MAAM;EAExB,IAAKD,KAAK,CAACf,MAAM,KAAK,CAAC,EAAG;IAEzB,MAAM,IAAIiB,KAAK,CAAE,sDAAuD,CAAC;EAE1E;EAEA,MAAMC,IAAI,GAAGH,KAAK,CAAE,CAAC,CAAE;EACvB,MAAMI,WAAW,GAAG,IAAIC,WAAW,CAAEF,IAAK,CAAC;EAC3C,MAAMG,WAAW,GAAG,IAAIC,WAAW,CAAEJ,IAAK,CAAC;EAC3C,MAAMK,YAAY,GAAG,IAAIC,YAAY,CAAEN,IAAK,CAAC;;EAE7C;EACA;EACA,MAAMO,SAAS,GAAGP,IAAI,CAACQ,UAAU,GAAGpD,cAAc;EAClD,MAAMqD,eAAe,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAED,IAAI,CAACE,IAAI,CAAEL,SAAS,GAAG,CAAE,CAAE,CAAC;EACnE,MAAMM,WAAW,GAAG,IAAIP,YAAY,CAAE,CAAC,GAAGG,eAAe,GAAGA,eAAgB,CAAC;EAE7E,MAAMK,iBAAiB,GAAGJ,IAAI,CAACC,IAAI,CAAED,IAAI,CAACE,IAAI,CAAEL,SAAU,CAAE,CAAC;EAC7D,MAAMQ,aAAa,GAAG,IAAIX,WAAW,CAAE,CAAC,GAAGU,iBAAiB,GAAGA,iBAAkB,CAAC;EAElF,KAAM,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,EAAEjB,CAAC,EAAG,EAAG;IAEtC,MAAM0B,WAAW,GAAG1B,CAAC,GAAGlC,cAAc,GAAG,CAAC;IAC1C,MAAM6D,WAAW,GAAGD,WAAW,GAAG,CAAC;IACnC,MAAME,WAAW,GAAG7D,mBAAmB,CAAE2D,WAAY,CAAC;IACtD,KAAM,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9BN,WAAW,CAAE,CAAC,GAAGvB,CAAC,GAAG,CAAC,GAAG6B,CAAC,CAAE,GAAGd,YAAY,CAAEa,WAAW,GAAG,CAAC,GAAGC,CAAC,CAAE;MAClEN,WAAW,CAAE,CAAC,GAAGvB,CAAC,GAAG,CAAC,GAAG6B,CAAC,CAAE,GAAGd,YAAY,CAAEa,WAAW,GAAG,CAAC,GAAGC,CAAC,CAAE;IAEnE;IAEA,IAAK5D,OAAO,CAAE0D,WAAW,EAAEhB,WAAY,CAAC,EAAG;MAE1C,MAAMpB,KAAK,GAAGvB,KAAK,CAAE2D,WAAW,EAAEhB,WAAY,CAAC;MAC/C,MAAMmB,MAAM,GAAG3D,MAAM,CAAEuD,WAAW,EAAEb,WAAY,CAAC;MAEjD,MAAMkB,eAAe,GAAG,UAAU,GAAGxC,KAAK;MAC1CkC,aAAa,CAAEzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAG+B,eAAe;MAC5CN,aAAa,CAAEzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAG8B,MAAM;IAEpC,CAAC,MAAM;MAEN,MAAME,UAAU,GAAG,CAAC,GAAG9D,UAAU,CAAEwD,WAAW,EAAEb,WAAY,CAAC,GAAG/C,cAAc;MAC9E,MAAMmE,SAAS,GAAG7D,UAAU,CAAEsD,WAAW,EAAEb,WAAY,CAAC;MAExDY,aAAa,CAAEzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGiC,SAAS;MACtCR,aAAa,CAAEzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGgC,UAAU;IAExC;EAED;EAEA3B,aAAa,CAAC6B,KAAK,CAACC,IAAI,GAAGZ,WAAW;EACtClB,aAAa,CAAC6B,KAAK,CAACE,KAAK,GAAGjB,eAAe;EAC3Cd,aAAa,CAAC6B,KAAK,CAACG,MAAM,GAAGlB,eAAe;EAC5Cd,aAAa,CAACiC,MAAM,GAAG9E,UAAU;EACjC6C,aAAa,CAACkC,IAAI,GAAGjF,SAAS;EAC9B+C,aAAa,CAACmC,cAAc,GAAG,SAAS;EACxCnC,aAAa,CAACoC,SAAS,GAAG/E,aAAa;EACvC2C,aAAa,CAACqC,SAAS,GAAGhF,aAAa;EACvC2C,aAAa,CAACsC,eAAe,GAAG,KAAK;EACrCtC,aAAa,CAACuC,WAAW,GAAG,IAAI;EAChCvC,aAAa,CAACT,OAAO,CAAC,CAAC;EAEvBU,eAAe,CAAC4B,KAAK,CAACC,IAAI,GAAGV,aAAa;EAC1CnB,eAAe,CAAC4B,KAAK,CAACE,KAAK,GAAGZ,iBAAiB;EAC/ClB,eAAe,CAAC4B,KAAK,CAACG,MAAM,GAAGb,iBAAiB;EAChDlB,eAAe,CAACgC,MAAM,GAAG7E,eAAe;EACxC6C,eAAe,CAACiC,IAAI,GAAGhF,eAAe;EACtC+C,eAAe,CAACkC,cAAc,GAAG,QAAQ;EACzClC,eAAe,CAACmC,SAAS,GAAG/E,aAAa;EACzC4C,eAAe,CAACoC,SAAS,GAAGhF,aAAa;EACzC4C,eAAe,CAACqC,eAAe,GAAG,KAAK;EACvCrC,eAAe,CAACsC,WAAW,GAAG,IAAI;EAClCtC,eAAe,CAACV,OAAO,CAAC,CAAC;AAE1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}