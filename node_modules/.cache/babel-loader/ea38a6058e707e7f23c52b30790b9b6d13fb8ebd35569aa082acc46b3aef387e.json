{"ast": null, "code": "import { useThree } from '@react-three/fiber';\nfunction useAspect(width, height, factor = 1) {\n  const v = useThree(state => state.viewport);\n  const adaptedHeight = height * (v.aspect > width / height ? v.width / width : v.height / height);\n  const adaptedWidth = width * (v.aspect > width / height ? v.width / width : v.height / height);\n  return [adaptedWidth * factor, adaptedHeight * factor, 1];\n}\nexport { useAspect };", "map": {"version": 3, "names": ["useThree", "useAspect", "width", "height", "factor", "v", "state", "viewport", "adaptedHeight", "aspect", "adapted<PERSON><PERSON>th"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/useAspect.js"], "sourcesContent": ["import { useThree } from '@react-three/fiber';\n\nfunction useAspect(width, height, factor = 1) {\n  const v = useThree(state => state.viewport);\n  const adaptedHeight = height * (v.aspect > width / height ? v.width / width : v.height / height);\n  const adaptedWidth = width * (v.aspect > width / height ? v.width / width : v.height / height);\n  return [adaptedWidth * factor, adaptedHeight * factor, 1];\n}\n\nexport { useAspect };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,EAAE;EAC5C,MAAMC,CAAC,GAAGL,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAC3C,MAAMC,aAAa,GAAGL,MAAM,IAAIE,CAAC,CAACI,MAAM,GAAGP,KAAK,GAAGC,MAAM,GAAGE,CAAC,CAACH,KAAK,GAAGA,KAAK,GAAGG,CAAC,CAACF,MAAM,GAAGA,MAAM,CAAC;EAChG,MAAMO,YAAY,GAAGR,KAAK,IAAIG,CAAC,CAACI,MAAM,GAAGP,KAAK,GAAGC,MAAM,GAAGE,CAAC,CAACH,KAAK,GAAGA,KAAK,GAAGG,CAAC,CAACF,MAAM,GAAGA,MAAM,CAAC;EAC9F,OAAO,CAACO,YAAY,GAAGN,MAAM,EAAEI,aAAa,GAAGJ,MAAM,EAAE,CAAC,CAAC;AAC3D;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}