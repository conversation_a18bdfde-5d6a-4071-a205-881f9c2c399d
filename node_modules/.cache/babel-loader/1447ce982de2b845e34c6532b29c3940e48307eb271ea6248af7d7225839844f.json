{"ast": null, "code": "import { REVISION } from \"three\";\nconst version = /* @__PURE__ */(() => parseInt(REVISION.replace(/\\D+/g, \"\")))();\nexport { version };", "map": {"version": 3, "names": ["version", "parseInt", "REVISION", "replace"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/_polyfill/constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "mappings": ";AAEa,MAAAA,OAAA,yBAAiCC,QAAA,CAASC,QAAA,CAASC,OAAA,CAAQ,QAAQ,EAAE,CAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}