{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector2, Vector3, Matrix4, Quaternion, Group, EllipseCurve, BufferGeometry, PerspectiveCamera, MathUtils, OrthographicCamera, Box3, Sphere, GridHelper, LineBasicMaterial, Line, Raycaster } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nconst STATE = {\n  IDLE: Symbol(),\n  ROTATE: Symbol(),\n  PAN: Symbol(),\n  SCALE: Symbol(),\n  FOV: Symbol(),\n  FOCUS: Symbol(),\n  ZROTATE: Symbol(),\n  TOUCH_MULTI: Symbol(),\n  ANIMATION_FOCUS: Symbol(),\n  ANIMATION_ROTATE: Symbol()\n};\nconst INPUT = {\n  NONE: Symbol(),\n  ONE_FINGER: Symbol(),\n  ONE_FINGER_SWITCHED: Symbol(),\n  TWO_FINGER: Symbol(),\n  MULT_FINGER: Symbol(),\n  CURSOR: Symbol()\n};\nconst _center = {\n  x: 0,\n  y: 0\n};\nconst _transformation = {\n  camera: /* @__PURE__ */new Matrix4(),\n  gizmos: /* @__PURE__ */new Matrix4()\n};\nconst _changeEvent = {\n  type: \"change\"\n};\nconst _startEvent = {\n  type: \"start\"\n};\nconst _endEvent = {\n  type: \"end\"\n};\nclass ArcballControls extends EventDispatcher {\n  constructor(camera, domElement = null, scene = null) {\n    super();\n    __publicField(this, \"camera\");\n    __publicField(this, \"domElement\");\n    __publicField(this, \"scene\");\n    __publicField(this, \"mouseActions\");\n    __publicField(this, \"_mouseOp\");\n    __publicField(this, \"_v2_1\");\n    __publicField(this, \"_v3_1\");\n    __publicField(this, \"_v3_2\");\n    __publicField(this, \"_m4_1\");\n    __publicField(this, \"_m4_2\");\n    __publicField(this, \"_quat\");\n    __publicField(this, \"_translationMatrix\");\n    __publicField(this, \"_rotationMatrix\");\n    __publicField(this, \"_scaleMatrix\");\n    __publicField(this, \"_rotationAxis\");\n    __publicField(this, \"_cameraMatrixState\");\n    __publicField(this, \"_cameraProjectionState\");\n    __publicField(this, \"_fovState\");\n    __publicField(this, \"_upState\");\n    __publicField(this, \"_zoomState\");\n    __publicField(this, \"_nearPos\");\n    __publicField(this, \"_farPos\");\n    __publicField(this, \"_gizmoMatrixState\");\n    __publicField(this, \"_up0\");\n    __publicField(this, \"_zoom0\");\n    __publicField(this, \"_fov0\");\n    __publicField(this, \"_initialNear\");\n    __publicField(this, \"_nearPos0\");\n    __publicField(this, \"_initialFar\");\n    __publicField(this, \"_farPos0\");\n    __publicField(this, \"_cameraMatrixState0\");\n    __publicField(this, \"_gizmoMatrixState0\");\n    __publicField(this, \"_button\");\n    __publicField(this, \"_touchStart\");\n    __publicField(this, \"_touchCurrent\");\n    __publicField(this, \"_input\");\n    __publicField(this, \"_switchSensibility\");\n    __publicField(this, \"_startFingerDistance\");\n    __publicField(this, \"_currentFingerDistance\");\n    __publicField(this, \"_startFingerRotation\");\n    __publicField(this, \"_currentFingerRotation\");\n    __publicField(this, \"_devPxRatio\");\n    __publicField(this, \"_downValid\");\n    __publicField(this, \"_nclicks\");\n    __publicField(this, \"_downEvents\");\n    __publicField(this, \"_clickStart\");\n    __publicField(this, \"_maxDownTime\");\n    __publicField(this, \"_maxInterval\");\n    __publicField(this, \"_posThreshold\");\n    __publicField(this, \"_movementThreshold\");\n    __publicField(this, \"_currentCursorPosition\");\n    __publicField(this, \"_startCursorPosition\");\n    __publicField(this, \"_grid\");\n    __publicField(this, \"_gridPosition\");\n    __publicField(this, \"_gizmos\");\n    __publicField(this, \"_curvePts\");\n    __publicField(this, \"_timeStart\");\n    __publicField(this, \"_animationId\");\n    __publicField(this, \"focusAnimationTime\");\n    __publicField(this, \"_timePrev\");\n    __publicField(this, \"_timeCurrent\");\n    __publicField(this, \"_anglePrev\");\n    __publicField(this, \"_angleCurrent\");\n    __publicField(this, \"_cursorPosPrev\");\n    __publicField(this, \"_cursorPosCurr\");\n    __publicField(this, \"_wPrev\");\n    __publicField(this, \"_wCurr\");\n    __publicField(this, \"adjustNearFar\");\n    __publicField(this, \"scaleFactor\");\n    __publicField(this, \"dampingFactor\");\n    __publicField(this, \"wMax\");\n    __publicField(this, \"enableAnimations\");\n    __publicField(this, \"enableGrid\");\n    __publicField(this, \"cursorZoom\");\n    __publicField(this, \"minFov\");\n    __publicField(this, \"maxFov\");\n    __publicField(this, \"enabled\");\n    __publicField(this, \"enablePan\");\n    __publicField(this, \"enableRotate\");\n    __publicField(this, \"enableZoom\");\n    __publicField(this, \"minDistance\");\n    __publicField(this, \"maxDistance\");\n    __publicField(this, \"minZoom\");\n    __publicField(this, \"maxZoom\");\n    __publicField(this, \"target\");\n    __publicField(this, \"_currentTarget\");\n    __publicField(this, \"_tbRadius\");\n    __publicField(this, \"_state\");\n    //listeners\n    __publicField(this, \"onWindowResize\", () => {\n      const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3;\n      if (this.camera) {\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n      }\n      const newRadius = this._tbRadius / scale;\n      const curve = new EllipseCurve(0, 0, newRadius, newRadius);\n      const points = curve.getPoints(this._curvePts);\n      const curveGeometry = new BufferGeometry().setFromPoints(points);\n      for (const gizmo in this._gizmos.children) {\n        const child = this._gizmos.children[gizmo];\n        child.geometry = curveGeometry;\n      }\n      this.dispatchEvent(_changeEvent);\n    });\n    __publicField(this, \"onContextMenu\", event => {\n      if (!this.enabled) {\n        return;\n      }\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        if (this.mouseActions[i].mouse == 2) {\n          event.preventDefault();\n          break;\n        }\n      }\n    });\n    __publicField(this, \"onPointerCancel\", () => {\n      this._touchStart.splice(0, this._touchStart.length);\n      this._touchCurrent.splice(0, this._touchCurrent.length);\n      this._input = INPUT.NONE;\n    });\n    __publicField(this, \"onPointerDown\", event => {\n      if (event.button == 0 && event.isPrimary) {\n        this._downValid = true;\n        this._downEvents.push(event);\n      } else {\n        this._downValid = false;\n      }\n      if (event.pointerType == \"touch\" && this._input != INPUT.CURSOR) {\n        this._touchStart.push(event);\n        this._touchCurrent.push(event);\n        switch (this._input) {\n          case INPUT.NONE:\n            this._input = INPUT.ONE_FINGER;\n            this.onSinglePanStart(event, \"ROTATE\");\n            window.addEventListener(\"pointermove\", this.onPointerMove);\n            window.addEventListener(\"pointerup\", this.onPointerUp);\n            break;\n          case INPUT.ONE_FINGER:\n          case INPUT.ONE_FINGER_SWITCHED:\n            this._input = INPUT.TWO_FINGER;\n            this.onRotateStart();\n            this.onPinchStart();\n            this.onDoublePanStart();\n            break;\n          case INPUT.TWO_FINGER:\n            this._input = INPUT.MULT_FINGER;\n            this.onTriplePanStart();\n            break;\n        }\n      } else if (event.pointerType != \"touch\" && this._input == INPUT.NONE) {\n        let modifier = null;\n        if (event.ctrlKey || event.metaKey) {\n          modifier = \"CTRL\";\n        } else if (event.shiftKey) {\n          modifier = \"SHIFT\";\n        }\n        this._mouseOp = this.getOpFromAction(event.button, modifier);\n        if (this._mouseOp) {\n          window.addEventListener(\"pointermove\", this.onPointerMove);\n          window.addEventListener(\"pointerup\", this.onPointerUp);\n          this._input = INPUT.CURSOR;\n          this._button = event.button;\n          this.onSinglePanStart(event, this._mouseOp);\n        }\n      }\n    });\n    __publicField(this, \"onPointerMove\", event => {\n      if (event.pointerType == \"touch\" && this._input != INPUT.CURSOR) {\n        switch (this._input) {\n          case INPUT.ONE_FINGER:\n            this.updateTouchEvent(event);\n            this.onSinglePanMove(event, STATE.ROTATE);\n            break;\n          case INPUT.ONE_FINGER_SWITCHED:\n            const movement = this.calculatePointersDistance(this._touchCurrent[0], event) * this._devPxRatio;\n            if (movement >= this._switchSensibility) {\n              this._input = INPUT.ONE_FINGER;\n              this.updateTouchEvent(event);\n              this.onSinglePanStart(event, \"ROTATE\");\n              break;\n            }\n            break;\n          case INPUT.TWO_FINGER:\n            this.updateTouchEvent(event);\n            this.onRotateMove();\n            this.onPinchMove();\n            this.onDoublePanMove();\n            break;\n          case INPUT.MULT_FINGER:\n            this.updateTouchEvent(event);\n            this.onTriplePanMove();\n            break;\n        }\n      } else if (event.pointerType != \"touch\" && this._input == INPUT.CURSOR) {\n        let modifier = null;\n        if (event.ctrlKey || event.metaKey) {\n          modifier = \"CTRL\";\n        } else if (event.shiftKey) {\n          modifier = \"SHIFT\";\n        }\n        const mouseOpState = this.getOpStateFromAction(this._button, modifier);\n        if (mouseOpState) {\n          this.onSinglePanMove(event, mouseOpState);\n        }\n      }\n      if (this._downValid) {\n        const movement = this.calculatePointersDistance(this._downEvents[this._downEvents.length - 1], event) * this._devPxRatio;\n        if (movement > this._movementThreshold) {\n          this._downValid = false;\n        }\n      }\n    });\n    __publicField(this, \"onPointerUp\", event => {\n      if (event.pointerType == \"touch\" && this._input != INPUT.CURSOR) {\n        const nTouch = this._touchCurrent.length;\n        for (let i = 0; i < nTouch; i++) {\n          if (this._touchCurrent[i].pointerId == event.pointerId) {\n            this._touchCurrent.splice(i, 1);\n            this._touchStart.splice(i, 1);\n            break;\n          }\n        }\n        switch (this._input) {\n          case INPUT.ONE_FINGER:\n          case INPUT.ONE_FINGER_SWITCHED:\n            window.removeEventListener(\"pointermove\", this.onPointerMove);\n            window.removeEventListener(\"pointerup\", this.onPointerUp);\n            this._input = INPUT.NONE;\n            this.onSinglePanEnd();\n            break;\n          case INPUT.TWO_FINGER:\n            this.onDoublePanEnd();\n            this.onPinchEnd();\n            this.onRotateEnd();\n            this._input = INPUT.ONE_FINGER_SWITCHED;\n            break;\n          case INPUT.MULT_FINGER:\n            if (this._touchCurrent.length == 0) {\n              window.removeEventListener(\"pointermove\", this.onPointerMove);\n              window.removeEventListener(\"pointerup\", this.onPointerUp);\n              this._input = INPUT.NONE;\n              this.onTriplePanEnd();\n            }\n            break;\n        }\n      } else if (event.pointerType != \"touch\" && this._input == INPUT.CURSOR) {\n        window.removeEventListener(\"pointermove\", this.onPointerMove);\n        window.removeEventListener(\"pointerup\", this.onPointerUp);\n        this._input = INPUT.NONE;\n        this.onSinglePanEnd();\n        this._button = -1;\n      }\n      if (event.isPrimary) {\n        if (this._downValid) {\n          const downTime = event.timeStamp - this._downEvents[this._downEvents.length - 1].timeStamp;\n          if (downTime <= this._maxDownTime) {\n            if (this._nclicks == 0) {\n              this._nclicks = 1;\n              this._clickStart = performance.now();\n            } else {\n              const clickInterval = event.timeStamp - this._clickStart;\n              const movement = this.calculatePointersDistance(this._downEvents[1], this._downEvents[0]) * this._devPxRatio;\n              if (clickInterval <= this._maxInterval && movement <= this._posThreshold) {\n                this._nclicks = 0;\n                this._downEvents.splice(0, this._downEvents.length);\n                this.onDoubleTap(event);\n              } else {\n                this._nclicks = 1;\n                this._downEvents.shift();\n                this._clickStart = performance.now();\n              }\n            }\n          } else {\n            this._downValid = false;\n            this._nclicks = 0;\n            this._downEvents.splice(0, this._downEvents.length);\n          }\n        } else {\n          this._nclicks = 0;\n          this._downEvents.splice(0, this._downEvents.length);\n        }\n      }\n    });\n    __publicField(this, \"onWheel\", event => {\n      var _a, _b;\n      if (this.enabled && this.enableZoom && this.domElement) {\n        let modifier = null;\n        if (event.ctrlKey || event.metaKey) {\n          modifier = \"CTRL\";\n        } else if (event.shiftKey) {\n          modifier = \"SHIFT\";\n        }\n        const mouseOp = this.getOpFromAction(\"WHEEL\", modifier);\n        if (mouseOp) {\n          event.preventDefault();\n          this.dispatchEvent(_startEvent);\n          const notchDeltaY = 125;\n          let sgn = event.deltaY / notchDeltaY;\n          let size = 1;\n          if (sgn > 0) {\n            size = 1 / this.scaleFactor;\n          } else if (sgn < 0) {\n            size = this.scaleFactor;\n          }\n          switch (mouseOp) {\n            case \"ZOOM\":\n              this.updateTbState(STATE.SCALE, true);\n              if (sgn > 0) {\n                size = 1 / Math.pow(this.scaleFactor, sgn);\n              } else if (sgn < 0) {\n                size = Math.pow(this.scaleFactor, -sgn);\n              }\n              if (this.cursorZoom && this.enablePan) {\n                let scalePoint;\n                if (this.camera instanceof OrthographicCamera) {\n                  scalePoint = (_a = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)) == null ? void 0 : _a.applyQuaternion(this.camera.quaternion).multiplyScalar(1 / this.camera.zoom).add(this._gizmos.position);\n                }\n                if (this.camera instanceof PerspectiveCamera) {\n                  scalePoint = (_b = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)) == null ? void 0 : _b.applyQuaternion(this.camera.quaternion).add(this._gizmos.position);\n                }\n                if (scalePoint !== void 0) this.applyTransformMatrix(this.applyScale(size, scalePoint));\n              } else {\n                this.applyTransformMatrix(this.applyScale(size, this._gizmos.position));\n              }\n              if (this._grid) {\n                this.disposeGrid();\n                this.drawGrid();\n              }\n              this.updateTbState(STATE.IDLE, false);\n              this.dispatchEvent(_changeEvent);\n              this.dispatchEvent(_endEvent);\n              break;\n            case \"FOV\":\n              if (this.camera instanceof PerspectiveCamera) {\n                this.updateTbState(STATE.FOV, true);\n                if (event.deltaX != 0) {\n                  sgn = event.deltaX / notchDeltaY;\n                  size = 1;\n                  if (sgn > 0) {\n                    size = 1 / Math.pow(this.scaleFactor, sgn);\n                  } else if (sgn < 0) {\n                    size = Math.pow(this.scaleFactor, -sgn);\n                  }\n                }\n                this._v3_1.setFromMatrixPosition(this._cameraMatrixState);\n                const x = this._v3_1.distanceTo(this._gizmos.position);\n                let xNew = x / size;\n                xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance);\n                const y = x * Math.tan(MathUtils.DEG2RAD * this.camera.fov * 0.5);\n                let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2);\n                if (newFov > this.maxFov) {\n                  newFov = this.maxFov;\n                } else if (newFov < this.minFov) {\n                  newFov = this.minFov;\n                }\n                const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2));\n                size = x / newDistance;\n                this.setFov(newFov);\n                this.applyTransformMatrix(this.applyScale(size, this._gizmos.position, false));\n              }\n              if (this._grid) {\n                this.disposeGrid();\n                this.drawGrid();\n              }\n              this.updateTbState(STATE.IDLE, false);\n              this.dispatchEvent(_changeEvent);\n              this.dispatchEvent(_endEvent);\n              break;\n          }\n        }\n      }\n    });\n    __publicField(this, \"onSinglePanStart\", (event, operation) => {\n      if (this.enabled && this.domElement) {\n        this.dispatchEvent(_startEvent);\n        this.setCenter(event.clientX, event.clientY);\n        switch (operation) {\n          case \"PAN\":\n            if (!this.enablePan) return;\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId);\n              this._animationId = -1;\n              this._timeStart = -1;\n              this.activateGizmos(false);\n              this.dispatchEvent(_changeEvent);\n            }\n            if (this.camera) {\n              this.updateTbState(STATE.PAN, true);\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement);\n              if (rayDir !== void 0) {\n                this._startCursorPosition.copy(rayDir);\n              }\n              if (this.enableGrid) {\n                this.drawGrid();\n                this.dispatchEvent(_changeEvent);\n              }\n            }\n            break;\n          case \"ROTATE\":\n            if (!this.enableRotate) return;\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId);\n              this._animationId = -1;\n              this._timeStart = -1;\n            }\n            if (this.camera) {\n              this.updateTbState(STATE.ROTATE, true);\n              const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius);\n              if (rayDir !== void 0) {\n                this._startCursorPosition.copy(rayDir);\n              }\n              this.activateGizmos(true);\n              if (this.enableAnimations) {\n                this._timePrev = this._timeCurrent = performance.now();\n                this._angleCurrent = this._anglePrev = 0;\n                this._cursorPosPrev.copy(this._startCursorPosition);\n                this._cursorPosCurr.copy(this._cursorPosPrev);\n                this._wCurr = 0;\n                this._wPrev = this._wCurr;\n              }\n            }\n            this.dispatchEvent(_changeEvent);\n            break;\n          case \"FOV\":\n            if (!this.enableZoom) return;\n            if (this.camera instanceof PerspectiveCamera) {\n              if (this._animationId != -1) {\n                cancelAnimationFrame(this._animationId);\n                this._animationId = -1;\n                this._timeStart = -1;\n                this.activateGizmos(false);\n                this.dispatchEvent(_changeEvent);\n              }\n              this.updateTbState(STATE.FOV, true);\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n              this._currentCursorPosition.copy(this._startCursorPosition);\n            }\n            break;\n          case \"ZOOM\":\n            if (!this.enableZoom) return;\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId);\n              this._animationId = -1;\n              this._timeStart = -1;\n              this.activateGizmos(false);\n              this.dispatchEvent(_changeEvent);\n            }\n            this.updateTbState(STATE.SCALE, true);\n            this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n            this._currentCursorPosition.copy(this._startCursorPosition);\n            break;\n        }\n      }\n    });\n    __publicField(this, \"onSinglePanMove\", (event, opState) => {\n      if (this.enabled && this.domElement) {\n        const restart = opState != this._state;\n        this.setCenter(event.clientX, event.clientY);\n        switch (opState) {\n          case STATE.PAN:\n            if (this.enablePan && this.camera) {\n              if (restart) {\n                this.dispatchEvent(_endEvent);\n                this.dispatchEvent(_startEvent);\n                this.updateTbState(opState, true);\n                const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement);\n                if (rayDir !== void 0) {\n                  this._startCursorPosition.copy(rayDir);\n                }\n                if (this.enableGrid) {\n                  this.drawGrid();\n                }\n                this.activateGizmos(false);\n              } else {\n                const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement);\n                if (rayDir !== void 0) {\n                  this._currentCursorPosition.copy(rayDir);\n                }\n                this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition));\n              }\n            }\n            break;\n          case STATE.ROTATE:\n            if (this.enableRotate && this.camera) {\n              if (restart) {\n                this.dispatchEvent(_endEvent);\n                this.dispatchEvent(_startEvent);\n                this.updateTbState(opState, true);\n                const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius);\n                if (rayDir !== void 0) {\n                  this._startCursorPosition.copy(rayDir);\n                }\n                if (this.enableGrid) {\n                  this.disposeGrid();\n                }\n                this.activateGizmos(true);\n              } else {\n                const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius);\n                if (rayDir !== void 0) {\n                  this._currentCursorPosition.copy(rayDir);\n                }\n                const distance = this._startCursorPosition.distanceTo(this._currentCursorPosition);\n                const angle = this._startCursorPosition.angleTo(this._currentCursorPosition);\n                const amount = Math.max(distance / this._tbRadius, angle);\n                this.applyTransformMatrix(this.rotate(this.calculateRotationAxis(this._startCursorPosition, this._currentCursorPosition), amount));\n                if (this.enableAnimations) {\n                  this._timePrev = this._timeCurrent;\n                  this._timeCurrent = performance.now();\n                  this._anglePrev = this._angleCurrent;\n                  this._angleCurrent = amount;\n                  this._cursorPosPrev.copy(this._cursorPosCurr);\n                  this._cursorPosCurr.copy(this._currentCursorPosition);\n                  this._wPrev = this._wCurr;\n                  this._wCurr = this.calculateAngularSpeed(this._anglePrev, this._angleCurrent, this._timePrev, this._timeCurrent);\n                }\n              }\n            }\n            break;\n          case STATE.SCALE:\n            if (this.enableZoom) {\n              if (restart) {\n                this.dispatchEvent(_endEvent);\n                this.dispatchEvent(_startEvent);\n                this.updateTbState(opState, true);\n                this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n                this._currentCursorPosition.copy(this._startCursorPosition);\n                if (this.enableGrid) {\n                  this.disposeGrid();\n                }\n                this.activateGizmos(false);\n              } else {\n                const screenNotches = 8;\n                this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n                const movement = this._currentCursorPosition.y - this._startCursorPosition.y;\n                let size = 1;\n                if (movement < 0) {\n                  size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches);\n                } else if (movement > 0) {\n                  size = Math.pow(this.scaleFactor, movement * screenNotches);\n                }\n                this.applyTransformMatrix(this.applyScale(size, this._gizmos.position));\n              }\n            }\n            break;\n          case STATE.FOV:\n            if (this.enableZoom && this.camera instanceof PerspectiveCamera) {\n              if (restart) {\n                this.dispatchEvent(_endEvent);\n                this.dispatchEvent(_startEvent);\n                this.updateTbState(opState, true);\n                this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n                this._currentCursorPosition.copy(this._startCursorPosition);\n                if (this.enableGrid) {\n                  this.disposeGrid();\n                }\n                this.activateGizmos(false);\n              } else {\n                const screenNotches = 8;\n                this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n                const movement = this._currentCursorPosition.y - this._startCursorPosition.y;\n                let size = 1;\n                if (movement < 0) {\n                  size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches);\n                } else if (movement > 0) {\n                  size = Math.pow(this.scaleFactor, movement * screenNotches);\n                }\n                this._v3_1.setFromMatrixPosition(this._cameraMatrixState);\n                const x = this._v3_1.distanceTo(this._gizmos.position);\n                let xNew = x / size;\n                xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance);\n                const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5);\n                let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2);\n                newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov);\n                const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2));\n                size = x / newDistance;\n                this._v3_2.setFromMatrixPosition(this._gizmoMatrixState);\n                this.setFov(newFov);\n                this.applyTransformMatrix(this.applyScale(size, this._v3_2, false));\n                const direction = this._gizmos.position.clone().sub(this.camera.position).normalize().multiplyScalar(newDistance / x);\n                this._m4_1.makeTranslation(direction.x, direction.y, direction.z);\n              }\n            }\n            break;\n        }\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    __publicField(this, \"onSinglePanEnd\", () => {\n      if (this._state == STATE.ROTATE) {\n        if (!this.enableRotate) {\n          return;\n        }\n        if (this.enableAnimations) {\n          const deltaTime = performance.now() - this._timeCurrent;\n          if (deltaTime < 120) {\n            const w = Math.abs((this._wPrev + this._wCurr) / 2);\n            const self = this;\n            this._animationId = window.requestAnimationFrame(function (t) {\n              self.updateTbState(STATE.ANIMATION_ROTATE, true);\n              const rotationAxis = self.calculateRotationAxis(self._cursorPosPrev, self._cursorPosCurr);\n              self.onRotationAnim(t, rotationAxis, Math.min(w, self.wMax));\n            });\n          } else {\n            this.updateTbState(STATE.IDLE, false);\n            this.activateGizmos(false);\n            this.dispatchEvent(_changeEvent);\n          }\n        } else {\n          this.updateTbState(STATE.IDLE, false);\n          this.activateGizmos(false);\n          this.dispatchEvent(_changeEvent);\n        }\n      } else if (this._state == STATE.PAN || this._state == STATE.IDLE) {\n        this.updateTbState(STATE.IDLE, false);\n        if (this.enableGrid) {\n          this.disposeGrid();\n        }\n        this.activateGizmos(false);\n        this.dispatchEvent(_changeEvent);\n      }\n      this.dispatchEvent(_endEvent);\n    });\n    __publicField(this, \"onDoubleTap\", event => {\n      if (this.enabled && this.enablePan && this.scene && this.camera && this.domElement) {\n        this.dispatchEvent(_startEvent);\n        this.setCenter(event.clientX, event.clientY);\n        const hitP = this.unprojectOnObj(this.getCursorNDC(_center.x, _center.y, this.domElement), this.camera);\n        if (hitP && this.enableAnimations) {\n          const self = this;\n          if (this._animationId != -1) {\n            window.cancelAnimationFrame(this._animationId);\n          }\n          this._timeStart = -1;\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.updateTbState(STATE.ANIMATION_FOCUS, true);\n            self.onFocusAnim(t, hitP, self._cameraMatrixState, self._gizmoMatrixState);\n          });\n        } else if (hitP && !this.enableAnimations) {\n          this.updateTbState(STATE.FOCUS, true);\n          this.focus(hitP, this.scaleFactor);\n          this.updateTbState(STATE.IDLE, false);\n          this.dispatchEvent(_changeEvent);\n        }\n      }\n      this.dispatchEvent(_endEvent);\n    });\n    __publicField(this, \"onDoublePanStart\", () => {\n      if (this.enabled && this.enablePan && this.camera && this.domElement) {\n        this.dispatchEvent(_startEvent);\n        this.updateTbState(STATE.PAN, true);\n        this.setCenter((this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2, (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2);\n        const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true);\n        if (rayDir !== void 0) {\n          this._startCursorPosition.copy(rayDir);\n        }\n        this._currentCursorPosition.copy(this._startCursorPosition);\n        this.activateGizmos(false);\n      }\n    });\n    __publicField(this, \"onDoublePanMove\", () => {\n      if (this.enabled && this.enablePan && this.camera && this.domElement) {\n        this.setCenter((this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2, (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2);\n        if (this._state != STATE.PAN) {\n          this.updateTbState(STATE.PAN, true);\n          this._startCursorPosition.copy(this._currentCursorPosition);\n        }\n        const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true);\n        if (rayDir !== void 0) this._currentCursorPosition.copy(rayDir);\n        this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition, true));\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    __publicField(this, \"onDoublePanEnd\", () => {\n      this.updateTbState(STATE.IDLE, false);\n      this.dispatchEvent(_endEvent);\n    });\n    __publicField(this, \"onRotateStart\", () => {\n      var _a;\n      if (this.enabled && this.enableRotate) {\n        this.dispatchEvent(_startEvent);\n        this.updateTbState(STATE.ZROTATE, true);\n        this._startFingerRotation = this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) + this.getAngle(this._touchStart[1], this._touchStart[0]);\n        this._currentFingerRotation = this._startFingerRotation;\n        (_a = this.camera) == null ? void 0 : _a.getWorldDirection(this._rotationAxis);\n        if (!this.enablePan && !this.enableZoom) {\n          this.activateGizmos(true);\n        }\n      }\n    });\n    __publicField(this, \"onRotateMove\", () => {\n      var _a;\n      if (this.enabled && this.enableRotate && this.camera && this.domElement) {\n        this.setCenter((this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2, (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2);\n        let rotationPoint;\n        if (this._state != STATE.ZROTATE) {\n          this.updateTbState(STATE.ZROTATE, true);\n          this._startFingerRotation = this._currentFingerRotation;\n        }\n        this._currentFingerRotation = this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) + this.getAngle(this._touchStart[1], this._touchStart[0]);\n        if (!this.enablePan) {\n          rotationPoint = new Vector3().setFromMatrixPosition(this._gizmoMatrixState);\n        } else if (this.camera) {\n          this._v3_2.setFromMatrixPosition(this._gizmoMatrixState);\n          rotationPoint = (_a = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)) == null ? void 0 : _a.applyQuaternion(this.camera.quaternion).multiplyScalar(1 / this.camera.zoom).add(this._v3_2);\n        }\n        const amount = MathUtils.DEG2RAD * (this._startFingerRotation - this._currentFingerRotation);\n        if (rotationPoint !== void 0) {\n          this.applyTransformMatrix(this.zRotate(rotationPoint, amount));\n        }\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    __publicField(this, \"onRotateEnd\", () => {\n      this.updateTbState(STATE.IDLE, false);\n      this.activateGizmos(false);\n      this.dispatchEvent(_endEvent);\n    });\n    __publicField(this, \"onPinchStart\", () => {\n      if (this.enabled && this.enableZoom) {\n        this.dispatchEvent(_startEvent);\n        this.updateTbState(STATE.SCALE, true);\n        this._startFingerDistance = this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1]);\n        this._currentFingerDistance = this._startFingerDistance;\n        this.activateGizmos(false);\n      }\n    });\n    __publicField(this, \"onPinchMove\", () => {\n      var _a, _b;\n      if (this.enabled && this.enableZoom && this.domElement) {\n        this.setCenter((this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2, (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2);\n        const minDistance = 12;\n        if (this._state != STATE.SCALE) {\n          this._startFingerDistance = this._currentFingerDistance;\n          this.updateTbState(STATE.SCALE, true);\n        }\n        this._currentFingerDistance = Math.max(this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1]), minDistance * this._devPxRatio);\n        const amount = this._currentFingerDistance / this._startFingerDistance;\n        let scalePoint;\n        if (!this.enablePan) {\n          scalePoint = this._gizmos.position;\n        } else {\n          if (this.camera instanceof OrthographicCamera) {\n            scalePoint = (_a = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)) == null ? void 0 : _a.applyQuaternion(this.camera.quaternion).multiplyScalar(1 / this.camera.zoom).add(this._gizmos.position);\n          } else if (this.camera instanceof PerspectiveCamera) {\n            scalePoint = (_b = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)) == null ? void 0 : _b.applyQuaternion(this.camera.quaternion).add(this._gizmos.position);\n          }\n        }\n        if (scalePoint !== void 0) {\n          this.applyTransformMatrix(this.applyScale(amount, scalePoint));\n        }\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    __publicField(this, \"onPinchEnd\", () => {\n      this.updateTbState(STATE.IDLE, false);\n      this.dispatchEvent(_endEvent);\n    });\n    __publicField(this, \"onTriplePanStart\", () => {\n      if (this.enabled && this.enableZoom && this.domElement) {\n        this.dispatchEvent(_startEvent);\n        this.updateTbState(STATE.SCALE, true);\n        let clientX = 0;\n        let clientY = 0;\n        const nFingers = this._touchCurrent.length;\n        for (let i = 0; i < nFingers; i++) {\n          clientX += this._touchCurrent[i].clientX;\n          clientY += this._touchCurrent[i].clientY;\n        }\n        this.setCenter(clientX / nFingers, clientY / nFingers);\n        this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n        this._currentCursorPosition.copy(this._startCursorPosition);\n      }\n    });\n    __publicField(this, \"onTriplePanMove\", () => {\n      if (this.enabled && this.enableZoom && this.camera && this.domElement) {\n        let clientX = 0;\n        let clientY = 0;\n        const nFingers = this._touchCurrent.length;\n        for (let i = 0; i < nFingers; i++) {\n          clientX += this._touchCurrent[i].clientX;\n          clientY += this._touchCurrent[i].clientY;\n        }\n        this.setCenter(clientX / nFingers, clientY / nFingers);\n        const screenNotches = 8;\n        this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5);\n        const movement = this._currentCursorPosition.y - this._startCursorPosition.y;\n        let size = 1;\n        if (movement < 0) {\n          size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches);\n        } else if (movement > 0) {\n          size = Math.pow(this.scaleFactor, movement * screenNotches);\n        }\n        this._v3_1.setFromMatrixPosition(this._cameraMatrixState);\n        const x = this._v3_1.distanceTo(this._gizmos.position);\n        let xNew = x / size;\n        xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance);\n        const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5);\n        let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2);\n        newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov);\n        const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2));\n        size = x / newDistance;\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState);\n        this.setFov(newFov);\n        this.applyTransformMatrix(this.applyScale(size, this._v3_2, false));\n        const direction = this._gizmos.position.clone().sub(this.camera.position).normalize().multiplyScalar(newDistance / x);\n        this._m4_1.makeTranslation(direction.x, direction.y, direction.z);\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    __publicField(this, \"onTriplePanEnd\", () => {\n      this.updateTbState(STATE.IDLE, false);\n      this.dispatchEvent(_endEvent);\n    });\n    /**\n     * Set _center's x/y coordinates\n     * @param {Number} clientX\n     * @param {Number} clientY\n     */\n    __publicField(this, \"setCenter\", (clientX, clientY) => {\n      _center.x = clientX;\n      _center.y = clientY;\n    });\n    /**\n     * Set default mouse actions\n     */\n    __publicField(this, \"initializeMouseActions\", () => {\n      this.setMouseAction(\"PAN\", 0, \"CTRL\");\n      this.setMouseAction(\"PAN\", 2);\n      this.setMouseAction(\"ROTATE\", 0);\n      this.setMouseAction(\"ZOOM\", \"WHEEL\");\n      this.setMouseAction(\"ZOOM\", 1);\n      this.setMouseAction(\"FOV\", \"WHEEL\", \"SHIFT\");\n      this.setMouseAction(\"FOV\", 1, \"SHIFT\");\n    });\n    /**\n     * Set a new mouse action by specifying the operation to be performed and a mouse/key combination. In case of conflict, replaces the existing one\n     * @param {String} operation The operation to be performed ('PAN', 'ROTATE', 'ZOOM', 'FOV)\n     * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n     * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n     * @returns {Boolean} True if the mouse action has been successfully added, false otherwise\n     */\n    __publicField(this, \"setMouseAction\", (operation, mouse, key = null) => {\n      const operationInput = [\"PAN\", \"ROTATE\", \"ZOOM\", \"FOV\"];\n      const mouseInput = [0, 1, 2, \"WHEEL\"];\n      const keyInput = [\"CTRL\", \"SHIFT\", null];\n      let state;\n      if (!operationInput.includes(operation) || !mouseInput.includes(mouse) || !keyInput.includes(key)) {\n        return false;\n      }\n      if (mouse == \"WHEEL\") {\n        if (operation != \"ZOOM\" && operation != \"FOV\") {\n          return false;\n        }\n      }\n      switch (operation) {\n        case \"PAN\":\n          state = STATE.PAN;\n          break;\n        case \"ROTATE\":\n          state = STATE.ROTATE;\n          break;\n        case \"ZOOM\":\n          state = STATE.SCALE;\n          break;\n        case \"FOV\":\n          state = STATE.FOV;\n          break;\n      }\n      const action = {\n        operation,\n        mouse,\n        key,\n        state\n      };\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        if (this.mouseActions[i].mouse == action.mouse && this.mouseActions[i].key == action.key) {\n          this.mouseActions.splice(i, 1, action);\n          return true;\n        }\n      }\n      this.mouseActions.push(action);\n      return true;\n    });\n    /**\n     * Return the operation associated to a mouse/keyboard combination\n     * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n     * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n     * @returns The operation if it has been found, null otherwise\n     */\n    __publicField(this, \"getOpFromAction\", (mouse, key) => {\n      let action;\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i];\n        if (action.mouse == mouse && action.key == key) {\n          return action.operation;\n        }\n      }\n      if (key) {\n        for (let i = 0; i < this.mouseActions.length; i++) {\n          action = this.mouseActions[i];\n          if (action.mouse == mouse && action.key == null) {\n            return action.operation;\n          }\n        }\n      }\n      return null;\n    });\n    /**\n     * Get the operation associated to mouse and key combination and returns the corresponding FSA state\n     * @param {Number} mouse Mouse button\n     * @param {String} key Keyboard modifier\n     * @returns The FSA state obtained from the operation associated to mouse/keyboard combination\n     */\n    __publicField(this, \"getOpStateFromAction\", (mouse, key) => {\n      let action;\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i];\n        if (action.mouse == mouse && action.key == key) {\n          return action.state;\n        }\n      }\n      if (key) {\n        for (let i = 0; i < this.mouseActions.length; i++) {\n          action = this.mouseActions[i];\n          if (action.mouse == mouse && action.key == null) {\n            return action.state;\n          }\n        }\n      }\n      return null;\n    });\n    /**\n     * Calculate the angle between two pointers\n     * @param {PointerEvent} p1\n     * @param {PointerEvent} p2\n     * @returns {Number} The angle between two pointers in degrees\n     */\n    __publicField(this, \"getAngle\", (p1, p2) => {\n      return Math.atan2(p2.clientY - p1.clientY, p2.clientX - p1.clientX) * 180 / Math.PI;\n    });\n    /**\n     * Update a PointerEvent inside current pointerevents array\n     * @param {PointerEvent} event\n     */\n    __publicField(this, \"updateTouchEvent\", event => {\n      for (let i = 0; i < this._touchCurrent.length; i++) {\n        if (this._touchCurrent[i].pointerId == event.pointerId) {\n          this._touchCurrent.splice(i, 1, event);\n          break;\n        }\n      }\n    });\n    /**\n     * Calculate the angular speed\n     * @param {Number} p0 Position at t0\n     * @param {Number} p1 Position at t1\n     * @param {Number} t0 Initial time in milliseconds\n     * @param {Number} t1 Ending time in milliseconds\n     */\n    __publicField(this, \"calculateAngularSpeed\", (p0, p1, t0, t1) => {\n      const s = p1 - p0;\n      const t = (t1 - t0) / 1e3;\n      if (t == 0) {\n        return 0;\n      }\n      return s / t;\n    });\n    /**\n     * Calculate the distance between two pointers\n     * @param {PointerEvent} p0 The first pointer\n     * @param {PointerEvent} p1 The second pointer\n     * @returns {number} The distance between the two pointers\n     */\n    __publicField(this, \"calculatePointersDistance\", (p0, p1) => {\n      return Math.sqrt(Math.pow(p1.clientX - p0.clientX, 2) + Math.pow(p1.clientY - p0.clientY, 2));\n    });\n    /**\n     * Calculate the rotation axis as the vector perpendicular between two vectors\n     * @param {Vector3} vec1 The first vector\n     * @param {Vector3} vec2 The second vector\n     * @returns {Vector3} The normalized rotation axis\n     */\n    __publicField(this, \"calculateRotationAxis\", (vec1, vec2) => {\n      this._rotationMatrix.extractRotation(this._cameraMatrixState);\n      this._quat.setFromRotationMatrix(this._rotationMatrix);\n      this._rotationAxis.crossVectors(vec1, vec2).applyQuaternion(this._quat);\n      return this._rotationAxis.normalize().clone();\n    });\n    /**\n     * Calculate the trackball radius so that gizmo's diamater will be 2/3 of the minimum side of the camera frustum\n     * @param {Camera} camera\n     * @returns {Number} The trackball radius\n     */\n    __publicField(this, \"calculateTbRadius\", camera => {\n      const factor = 0.67;\n      const distance = camera.position.distanceTo(this._gizmos.position);\n      if (camera instanceof PerspectiveCamera) {\n        const halfFovV = MathUtils.DEG2RAD * camera.fov * 0.5;\n        const halfFovH = Math.atan(camera.aspect * Math.tan(halfFovV));\n        return Math.tan(Math.min(halfFovV, halfFovH)) * distance * factor;\n      } else if (camera instanceof OrthographicCamera) {\n        return Math.min(camera.top, camera.right) * factor;\n      }\n    });\n    /**\n     * Focus operation consist of positioning the point of interest in front of the camera and a slightly zoom in\n     * @param {Vector3} point The point of interest\n     * @param {Number} size Scale factor\n     * @param {Number} amount Amount of operation to be completed (used for focus animations, default is complete full operation)\n     */\n    __publicField(this, \"focus\", (point, size, amount = 1) => {\n      if (this.camera) {\n        const focusPoint = point.clone();\n        focusPoint.sub(this._gizmos.position).multiplyScalar(amount);\n        this._translationMatrix.makeTranslation(focusPoint.x, focusPoint.y, focusPoint.z);\n        const gizmoStateTemp = this._gizmoMatrixState.clone();\n        this._gizmoMatrixState.premultiply(this._translationMatrix);\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n        const cameraStateTemp = this._cameraMatrixState.clone();\n        this._cameraMatrixState.premultiply(this._translationMatrix);\n        this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale);\n        if (this.enableZoom) {\n          this.applyTransformMatrix(this.applyScale(size, this._gizmos.position));\n        }\n        this._gizmoMatrixState.copy(gizmoStateTemp);\n        this._cameraMatrixState.copy(cameraStateTemp);\n      }\n    });\n    /**\n     * Draw a grid and add it to the scene\n     */\n    __publicField(this, \"drawGrid\", () => {\n      if (this.scene) {\n        const color = 8947848;\n        const multiplier = 3;\n        let size, divisions, maxLength, tick;\n        if (this.camera instanceof OrthographicCamera) {\n          const width = this.camera.right - this.camera.left;\n          const height = this.camera.bottom - this.camera.top;\n          maxLength = Math.max(width, height);\n          tick = maxLength / 20;\n          size = maxLength / this.camera.zoom * multiplier;\n          divisions = size / tick * this.camera.zoom;\n        } else if (this.camera instanceof PerspectiveCamera) {\n          const distance = this.camera.position.distanceTo(this._gizmos.position);\n          const halfFovV = MathUtils.DEG2RAD * this.camera.fov * 0.5;\n          const halfFovH = Math.atan(this.camera.aspect * Math.tan(halfFovV));\n          maxLength = Math.tan(Math.max(halfFovV, halfFovH)) * distance * 2;\n          tick = maxLength / 20;\n          size = maxLength * multiplier;\n          divisions = size / tick;\n        }\n        if (this._grid == null && this.camera) {\n          this._grid = new GridHelper(size, divisions, color, color);\n          this._grid.position.copy(this._gizmos.position);\n          this._gridPosition.copy(this._grid.position);\n          this._grid.quaternion.copy(this.camera.quaternion);\n          this._grid.rotateX(Math.PI * 0.5);\n          this.scene.add(this._grid);\n        }\n      }\n    });\n    __publicField(this, \"connect\", domElement => {\n      if (domElement === document) {\n        console.error('THREE.ArcballControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.');\n      }\n      this.domElement = domElement;\n      this.domElement.style.touchAction = \"none\";\n      this.domElement.addEventListener(\"contextmenu\", this.onContextMenu);\n      this.domElement.addEventListener(\"pointerdown\", this.onPointerDown);\n      this.domElement.addEventListener(\"pointercancel\", this.onPointerCancel);\n      this.domElement.addEventListener(\"wheel\", this.onWheel);\n    });\n    /**\n     * Remove all listeners, stop animations and clean scene\n     */\n    __publicField(this, \"dispose\", () => {\n      var _a, _b, _c, _d, _e;\n      if (this._animationId != -1) {\n        window.cancelAnimationFrame(this._animationId);\n      }\n      (_a = this.domElement) == null ? void 0 : _a.removeEventListener(\"pointerdown\", this.onPointerDown);\n      (_b = this.domElement) == null ? void 0 : _b.removeEventListener(\"pointercancel\", this.onPointerCancel);\n      (_c = this.domElement) == null ? void 0 : _c.removeEventListener(\"wheel\", this.onWheel);\n      (_d = this.domElement) == null ? void 0 : _d.removeEventListener(\"contextmenu\", this.onContextMenu);\n      window.removeEventListener(\"pointermove\", this.onPointerMove);\n      window.removeEventListener(\"pointerup\", this.onPointerUp);\n      window.removeEventListener(\"resize\", this.onWindowResize);\n      (_e = this.scene) == null ? void 0 : _e.remove(this._gizmos);\n      this.disposeGrid();\n    });\n    /**\n     * remove the grid from the scene\n     */\n    __publicField(this, \"disposeGrid\", () => {\n      if (this._grid && this.scene) {\n        this.scene.remove(this._grid);\n        this._grid = null;\n      }\n    });\n    /**\n     * Compute the easing out cubic function for ease out effect in animation\n     * @param {Number} t The absolute progress of the animation in the bound of 0 (beginning of the) and 1 (ending of animation)\n     * @returns {Number} Result of easing out cubic at time t\n     */\n    __publicField(this, \"easeOutCubic\", t => {\n      return 1 - Math.pow(1 - t, 3);\n    });\n    /**\n     * Make rotation gizmos more or less visible\n     * @param {Boolean} isActive If true, make gizmos more visible\n     */\n    __publicField(this, \"activateGizmos\", isActive => {\n      for (const gizmo of this._gizmos.children) {\n        gizmo.material.setValues({\n          opacity: isActive ? 1 : 0.6\n        });\n      }\n    });\n    /**\n     * Calculate the cursor position in NDC\n     * @param {number} x Cursor horizontal coordinate within the canvas\n     * @param {number} y Cursor vertical coordinate within the canvas\n     * @param {HTMLElement} canvas The canvas where the renderer draws its output\n     * @returns {Vector2} Cursor normalized position inside the canvas\n     */\n    __publicField(this, \"getCursorNDC\", (cursorX, cursorY, canvas) => {\n      const canvasRect = canvas.getBoundingClientRect();\n      this._v2_1.setX((cursorX - canvasRect.left) / canvasRect.width * 2 - 1);\n      this._v2_1.setY((canvasRect.bottom - cursorY) / canvasRect.height * 2 - 1);\n      return this._v2_1.clone();\n    });\n    /**\n     * Calculate the cursor position inside the canvas x/y coordinates with the origin being in the center of the canvas\n     * @param {Number} x Cursor horizontal coordinate within the canvas\n     * @param {Number} y Cursor vertical coordinate within the canvas\n     * @param {HTMLElement} canvas The canvas where the renderer draws its output\n     * @returns {Vector2} Cursor position inside the canvas\n     */\n    __publicField(this, \"getCursorPosition\", (cursorX, cursorY, canvas) => {\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas));\n      if (this.camera instanceof OrthographicCamera) {\n        this._v2_1.x *= (this.camera.right - this.camera.left) * 0.5;\n        this._v2_1.y *= (this.camera.top - this.camera.bottom) * 0.5;\n      }\n      return this._v2_1.clone();\n    });\n    /**\n     * Set the camera to be controlled\n     * @param {Camera} camera The virtual camera to be controlled\n     */\n    __publicField(this, \"setCamera\", camera => {\n      if (camera) {\n        camera.lookAt(this.target);\n        camera.updateMatrix();\n        if (camera instanceof PerspectiveCamera) {\n          this._fov0 = camera.fov;\n          this._fovState = camera.fov;\n        }\n        this._cameraMatrixState0.copy(camera.matrix);\n        this._cameraMatrixState.copy(this._cameraMatrixState0);\n        this._cameraProjectionState.copy(camera.projectionMatrix);\n        this._zoom0 = camera.zoom;\n        this._zoomState = this._zoom0;\n        this._initialNear = camera.near;\n        this._nearPos0 = camera.position.distanceTo(this.target) - camera.near;\n        this._nearPos = this._initialNear;\n        this._initialFar = camera.far;\n        this._farPos0 = camera.position.distanceTo(this.target) - camera.far;\n        this._farPos = this._initialFar;\n        this._up0.copy(camera.up);\n        this._upState.copy(camera.up);\n        this.camera = camera;\n        this.camera.updateProjectionMatrix();\n        const tbRadius = this.calculateTbRadius(camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        this.makeGizmos(this.target, this._tbRadius);\n      }\n    });\n    /**\n     * Creates the rotation gizmos matching trackball center and radius\n     * @param {Vector3} tbCenter The trackball center\n     * @param {number} tbRadius The trackball radius\n     */\n    __publicField(this, \"makeGizmos\", (tbCenter, tbRadius) => {\n      const curve = new EllipseCurve(0, 0, tbRadius, tbRadius);\n      const points = curve.getPoints(this._curvePts);\n      const curveGeometry = new BufferGeometry().setFromPoints(points);\n      const curveMaterialX = new LineBasicMaterial({\n        color: 16744576,\n        fog: false,\n        transparent: true,\n        opacity: 0.6\n      });\n      const curveMaterialY = new LineBasicMaterial({\n        color: 8454016,\n        fog: false,\n        transparent: true,\n        opacity: 0.6\n      });\n      const curveMaterialZ = new LineBasicMaterial({\n        color: 8421631,\n        fog: false,\n        transparent: true,\n        opacity: 0.6\n      });\n      const gizmoX = new Line(curveGeometry, curveMaterialX);\n      const gizmoY = new Line(curveGeometry, curveMaterialY);\n      const gizmoZ = new Line(curveGeometry, curveMaterialZ);\n      const rotation = Math.PI * 0.5;\n      gizmoX.rotation.x = rotation;\n      gizmoY.rotation.y = rotation;\n      this._gizmoMatrixState0.identity().setPosition(tbCenter);\n      this._gizmoMatrixState.copy(this._gizmoMatrixState0);\n      if (this.camera && this.camera.zoom != 1) {\n        const size = 1 / this.camera.zoom;\n        this._scaleMatrix.makeScale(size, size, size);\n        this._translationMatrix.makeTranslation(-tbCenter.x, -tbCenter.y, -tbCenter.z);\n        this._gizmoMatrixState.premultiply(this._translationMatrix).premultiply(this._scaleMatrix);\n        this._translationMatrix.makeTranslation(tbCenter.x, tbCenter.y, tbCenter.z);\n        this._gizmoMatrixState.premultiply(this._translationMatrix);\n      }\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n      this._gizmos.clear();\n      this._gizmos.add(gizmoX);\n      this._gizmos.add(gizmoY);\n      this._gizmos.add(gizmoZ);\n    });\n    /**\n     * Perform animation for focus operation\n     * @param {Number} time Instant in which this function is called as performance.now()\n     * @param {Vector3} point Point of interest for focus operation\n     * @param {Matrix4} cameraMatrix Camera matrix\n     * @param {Matrix4} gizmoMatrix Gizmos matrix\n     */\n    __publicField(this, \"onFocusAnim\", (time, point, cameraMatrix, gizmoMatrix) => {\n      if (this._timeStart == -1) {\n        this._timeStart = time;\n      }\n      if (this._state == STATE.ANIMATION_FOCUS) {\n        const deltaTime = time - this._timeStart;\n        const animTime = deltaTime / this.focusAnimationTime;\n        this._gizmoMatrixState.copy(gizmoMatrix);\n        if (animTime >= 1) {\n          this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n          this.focus(point, this.scaleFactor);\n          this._timeStart = -1;\n          this.updateTbState(STATE.IDLE, false);\n          this.activateGizmos(false);\n          this.dispatchEvent(_changeEvent);\n        } else {\n          const amount = this.easeOutCubic(animTime);\n          const size = 1 - amount + this.scaleFactor * amount;\n          this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n          this.focus(point, size, amount);\n          this.dispatchEvent(_changeEvent);\n          const self = this;\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.onFocusAnim(t, point, cameraMatrix, gizmoMatrix.clone());\n          });\n        }\n      } else {\n        this._animationId = -1;\n        this._timeStart = -1;\n      }\n    });\n    /**\n     * Perform animation for rotation operation\n     * @param {Number} time Instant in which this function is called as performance.now()\n     * @param {Vector3} rotationAxis Rotation axis\n     * @param {number} w0 Initial angular velocity\n     */\n    __publicField(this, \"onRotationAnim\", (time, rotationAxis, w0) => {\n      if (this._timeStart == -1) {\n        this._anglePrev = 0;\n        this._angleCurrent = 0;\n        this._timeStart = time;\n      }\n      if (this._state == STATE.ANIMATION_ROTATE) {\n        const deltaTime = (time - this._timeStart) / 1e3;\n        const w = w0 + -this.dampingFactor * deltaTime;\n        if (w > 0) {\n          this._angleCurrent = 0.5 * -this.dampingFactor * Math.pow(deltaTime, 2) + w0 * deltaTime + 0;\n          this.applyTransformMatrix(this.rotate(rotationAxis, this._angleCurrent));\n          this.dispatchEvent(_changeEvent);\n          const self = this;\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.onRotationAnim(t, rotationAxis, w0);\n          });\n        } else {\n          this._animationId = -1;\n          this._timeStart = -1;\n          this.updateTbState(STATE.IDLE, false);\n          this.activateGizmos(false);\n          this.dispatchEvent(_changeEvent);\n        }\n      } else {\n        this._animationId = -1;\n        this._timeStart = -1;\n        if (this._state != STATE.ROTATE) {\n          this.activateGizmos(false);\n          this.dispatchEvent(_changeEvent);\n        }\n      }\n    });\n    /**\n     * Perform pan operation moving camera between two points\n     * @param {Vector3} p0 Initial point\n     * @param {Vector3} p1 Ending point\n     * @param {Boolean} adjust If movement should be adjusted considering camera distance (Perspective only)\n     */\n    __publicField(this, \"pan\", (p0, p1, adjust = false) => {\n      if (this.camera) {\n        const movement = p0.clone().sub(p1);\n        if (this.camera instanceof OrthographicCamera) {\n          movement.multiplyScalar(1 / this.camera.zoom);\n        }\n        if (this.camera instanceof PerspectiveCamera && adjust) {\n          this._v3_1.setFromMatrixPosition(this._cameraMatrixState0);\n          this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0);\n          const distanceFactor = this._v3_1.distanceTo(this._v3_2) / this.camera.position.distanceTo(this._gizmos.position);\n          movement.multiplyScalar(1 / distanceFactor);\n        }\n        this._v3_1.set(movement.x, movement.y, 0).applyQuaternion(this.camera.quaternion);\n        this._m4_1.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z);\n        this.setTransformationMatrices(this._m4_1, this._m4_1);\n      }\n      return _transformation;\n    });\n    /**\n     * Reset trackball\n     */\n    __publicField(this, \"reset\", () => {\n      if (this.camera) {\n        this.camera.zoom = this._zoom0;\n        if (this.camera instanceof PerspectiveCamera) {\n          this.camera.fov = this._fov0;\n        }\n        this.camera.near = this._nearPos;\n        this.camera.far = this._farPos;\n        this._cameraMatrixState.copy(this._cameraMatrixState0);\n        this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale);\n        this.camera.up.copy(this._up0);\n        this.camera.updateMatrix();\n        this.camera.updateProjectionMatrix();\n        this._gizmoMatrixState.copy(this._gizmoMatrixState0);\n        this._gizmoMatrixState0.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n        this._gizmos.updateMatrix();\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        this.makeGizmos(this._gizmos.position, this._tbRadius);\n        this.camera.lookAt(this._gizmos.position);\n        this.updateTbState(STATE.IDLE, false);\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    /**\n     * Rotate the camera around an axis passing by trackball's center\n     * @param {Vector3} axis Rotation axis\n     * @param {number} angle Angle in radians\n     * @returns {Object} Object with 'camera' field containing transformation matrix resulting from the operation to be applied to the camera\n     */\n    __publicField(this, \"rotate\", (axis, angle) => {\n      const point = this._gizmos.position;\n      this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z);\n      this._rotationMatrix.makeRotationAxis(axis, -angle);\n      this._m4_1.makeTranslation(point.x, point.y, point.z);\n      this._m4_1.multiply(this._rotationMatrix);\n      this._m4_1.multiply(this._translationMatrix);\n      this.setTransformationMatrices(this._m4_1);\n      return _transformation;\n    });\n    __publicField(this, \"copyState\", () => {\n      if (this.camera) {\n        const state = JSON.stringify(this.camera instanceof OrthographicCamera ? {\n          arcballState: {\n            cameraFar: this.camera.far,\n            cameraMatrix: this.camera.matrix,\n            cameraNear: this.camera.near,\n            cameraUp: this.camera.up,\n            cameraZoom: this.camera.zoom,\n            gizmoMatrix: this._gizmos.matrix\n          }\n        } : {\n          arcballState: {\n            cameraFar: this.camera.far,\n            cameraFov: this.camera.fov,\n            cameraMatrix: this.camera.matrix,\n            cameraNear: this.camera.near,\n            cameraUp: this.camera.up,\n            cameraZoom: this.camera.zoom,\n            gizmoMatrix: this._gizmos.matrix\n          }\n        });\n        navigator.clipboard.writeText(state);\n      }\n    });\n    __publicField(this, \"pasteState\", () => {\n      const self = this;\n      navigator.clipboard.readText().then(function resolved(value) {\n        self.setStateFromJSON(value);\n      });\n    });\n    /**\n     * Save the current state of the control. This can later be recovered with .reset\n     */\n    __publicField(this, \"saveState\", () => {\n      if (!this.camera) return;\n      this._cameraMatrixState0.copy(this.camera.matrix);\n      this._gizmoMatrixState0.copy(this._gizmos.matrix);\n      this._nearPos = this.camera.near;\n      this._farPos = this.camera.far;\n      this._zoom0 = this.camera.zoom;\n      this._up0.copy(this.camera.up);\n      if (this.camera instanceof PerspectiveCamera) {\n        this._fov0 = this.camera.fov;\n      }\n    });\n    /**\n     * Perform uniform scale operation around a given point\n     * @param {Number} size Scale factor\n     * @param {Vector3} point Point around which scale\n     * @param {Boolean} scaleGizmos If gizmos should be scaled (Perspective only)\n     * @returns {Object} Object with 'camera' and 'gizmo' fields containing transformation matrices resulting from the operation to be applied to the camera and gizmos\n     */\n    __publicField(this, \"applyScale\", (size, point, scaleGizmos = true) => {\n      if (!this.camera) return;\n      const scalePoint = point.clone();\n      let sizeInverse = 1 / size;\n      if (this.camera instanceof OrthographicCamera) {\n        this.camera.zoom = this._zoomState;\n        this.camera.zoom *= size;\n        if (this.camera.zoom > this.maxZoom) {\n          this.camera.zoom = this.maxZoom;\n          sizeInverse = this._zoomState / this.maxZoom;\n        } else if (this.camera.zoom < this.minZoom) {\n          this.camera.zoom = this.minZoom;\n          sizeInverse = this._zoomState / this.minZoom;\n        }\n        this.camera.updateProjectionMatrix();\n        this._v3_1.setFromMatrixPosition(this._gizmoMatrixState);\n        this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse);\n        this._translationMatrix.makeTranslation(-this._v3_1.x, -this._v3_1.y, -this._v3_1.z);\n        this._m4_2.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z).multiply(this._scaleMatrix);\n        this._m4_2.multiply(this._translationMatrix);\n        scalePoint.sub(this._v3_1);\n        const amount = scalePoint.clone().multiplyScalar(sizeInverse);\n        scalePoint.sub(amount);\n        this._m4_1.makeTranslation(scalePoint.x, scalePoint.y, scalePoint.z);\n        this._m4_2.premultiply(this._m4_1);\n        this.setTransformationMatrices(this._m4_1, this._m4_2);\n        return _transformation;\n      }\n      if (this.camera instanceof PerspectiveCamera) {\n        this._v3_1.setFromMatrixPosition(this._cameraMatrixState);\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState);\n        let distance = this._v3_1.distanceTo(scalePoint);\n        let amount = distance - distance * sizeInverse;\n        const newDistance = distance - amount;\n        if (newDistance < this.minDistance) {\n          sizeInverse = this.minDistance / distance;\n          amount = distance - distance * sizeInverse;\n        } else if (newDistance > this.maxDistance) {\n          sizeInverse = this.maxDistance / distance;\n          amount = distance - distance * sizeInverse;\n        }\n        let direction = scalePoint.clone().sub(this._v3_1).normalize().multiplyScalar(amount);\n        this._m4_1.makeTranslation(direction.x, direction.y, direction.z);\n        if (scaleGizmos) {\n          const pos = this._v3_2;\n          distance = pos.distanceTo(scalePoint);\n          amount = distance - distance * sizeInverse;\n          direction = scalePoint.clone().sub(this._v3_2).normalize().multiplyScalar(amount);\n          this._translationMatrix.makeTranslation(pos.x, pos.y, pos.z);\n          this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse);\n          this._m4_2.makeTranslation(direction.x, direction.y, direction.z).multiply(this._translationMatrix);\n          this._m4_2.multiply(this._scaleMatrix);\n          this._translationMatrix.makeTranslation(-pos.x, -pos.y, -pos.z);\n          this._m4_2.multiply(this._translationMatrix);\n          this.setTransformationMatrices(this._m4_1, this._m4_2);\n        } else {\n          this.setTransformationMatrices(this._m4_1);\n        }\n        return _transformation;\n      }\n    });\n    /**\n     * Set camera fov\n     * @param {Number} value fov to be setted\n     */\n    __publicField(this, \"setFov\", value => {\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = MathUtils.clamp(value, this.minFov, this.maxFov);\n        this.camera.updateProjectionMatrix();\n      }\n    });\n    /**\n     * Set the trackball's center point\n     * @param {Number} x X coordinate\n     * @param {Number} y Y coordinate\n     * @param {Number} z Z coordinate\n     */\n    __publicField(this, \"setTarget\", (x, y, z) => {\n      if (this.camera) {\n        this.target.set(x, y, z);\n        this._gizmos.position.set(x, y, z);\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        this.makeGizmos(this.target, this._tbRadius);\n        this.camera.lookAt(this.target);\n      }\n    });\n    /**\n     * Rotate camera around its direction axis passing by a given point by a given angle\n     * @param {Vector3} point The point where the rotation axis is passing trough\n     * @param {Number} angle Angle in radians\n     * @returns The computed transormation matix\n     */\n    __publicField(this, \"zRotate\", (point, angle) => {\n      this._rotationMatrix.makeRotationAxis(this._rotationAxis, angle);\n      this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z);\n      this._m4_1.makeTranslation(point.x, point.y, point.z);\n      this._m4_1.multiply(this._rotationMatrix);\n      this._m4_1.multiply(this._translationMatrix);\n      this._v3_1.setFromMatrixPosition(this._gizmoMatrixState).sub(point);\n      this._v3_2.copy(this._v3_1).applyAxisAngle(this._rotationAxis, angle);\n      this._v3_2.sub(this._v3_1);\n      this._m4_2.makeTranslation(this._v3_2.x, this._v3_2.y, this._v3_2.z);\n      this.setTransformationMatrices(this._m4_1, this._m4_2);\n      return _transformation;\n    });\n    /**\n     * Unproject the cursor on the 3D object surface\n     * @param {Vector2} cursor Cursor coordinates in NDC\n     * @param {Camera} camera Virtual camera\n     * @returns {Vector3} The point of intersection with the model, if exist, null otherwise\n     */\n    __publicField(this, \"unprojectOnObj\", (cursor, camera) => {\n      if (!this.scene) return null;\n      const raycaster = new Raycaster();\n      raycaster.near = camera.near;\n      raycaster.far = camera.far;\n      raycaster.setFromCamera(cursor, camera);\n      const intersect = raycaster.intersectObjects(this.scene.children, true);\n      for (let i = 0; i < intersect.length; i++) {\n        if (intersect[i].object.uuid != this._gizmos.uuid && intersect[i].face) {\n          return intersect[i].point.clone();\n        }\n      }\n      return null;\n    });\n    /**\n     * Unproject the cursor on the trackball surface\n     * @param {Camera} camera The virtual camera\n     * @param {Number} cursorX Cursor horizontal coordinate on screen\n     * @param {Number} cursorY Cursor vertical coordinate on screen\n     * @param {HTMLElement} canvas The canvas where the renderer draws its output\n     * @param {number} tbRadius The trackball radius\n     * @returns {Vector3} The unprojected point on the trackball surface\n     */\n    __publicField(this, \"unprojectOnTbSurface\", (camera, cursorX, cursorY, canvas, tbRadius) => {\n      if (camera instanceof OrthographicCamera) {\n        this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas));\n        this._v3_1.set(this._v2_1.x, this._v2_1.y, 0);\n        const x2 = Math.pow(this._v2_1.x, 2);\n        const y2 = Math.pow(this._v2_1.y, 2);\n        const r2 = Math.pow(this._tbRadius, 2);\n        if (x2 + y2 <= r2 * 0.5) {\n          this._v3_1.setZ(Math.sqrt(r2 - (x2 + y2)));\n        } else {\n          this._v3_1.setZ(r2 * 0.5 / Math.sqrt(x2 + y2));\n        }\n        return this._v3_1;\n      }\n      if (camera instanceof PerspectiveCamera) {\n        this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas));\n        this._v3_1.set(this._v2_1.x, this._v2_1.y, -1);\n        this._v3_1.applyMatrix4(camera.projectionMatrixInverse);\n        const rayDir = this._v3_1.clone().normalize();\n        const cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position);\n        const radius2 = Math.pow(tbRadius, 2);\n        const h = this._v3_1.z;\n        const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2));\n        if (l == 0) {\n          rayDir.set(this._v3_1.x, this._v3_1.y, tbRadius);\n          return rayDir;\n        }\n        const m = h / l;\n        const q = cameraGizmoDistance;\n        let a = Math.pow(m, 2) + 1;\n        let b = 2 * m * q;\n        let c = Math.pow(q, 2) - radius2;\n        let delta = Math.pow(b, 2) - 4 * a * c;\n        if (delta >= 0) {\n          this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a));\n          this._v2_1.setY(m * this._v2_1.x + q);\n          const angle = MathUtils.RAD2DEG * this._v2_1.angle();\n          if (angle >= 45) {\n            const rayLength2 = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2));\n            rayDir.multiplyScalar(rayLength2);\n            rayDir.z += cameraGizmoDistance;\n            return rayDir;\n          }\n        }\n        a = m;\n        b = q;\n        c = -radius2 * 0.5;\n        delta = Math.pow(b, 2) - 4 * a * c;\n        this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a));\n        this._v2_1.setY(m * this._v2_1.x + q);\n        const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2));\n        rayDir.multiplyScalar(rayLength);\n        rayDir.z += cameraGizmoDistance;\n        return rayDir;\n      }\n    });\n    /**\n     * Unproject the cursor on the plane passing through the center of the trackball orthogonal to the camera\n     * @param {Camera} camera The virtual camera\n     * @param {Number} cursorX Cursor horizontal coordinate on screen\n     * @param {Number} cursorY Cursor vertical coordinate on screen\n     * @param {HTMLElement} canvas The canvas where the renderer draws its output\n     * @param {Boolean} initialDistance If initial distance between camera and gizmos should be used for calculations instead of current (Perspective only)\n     * @returns {Vector3} The unprojected point on the trackball plane\n     */\n    __publicField(this, \"unprojectOnTbPlane\", (camera, cursorX, cursorY, canvas, initialDistance = false) => {\n      if (camera instanceof OrthographicCamera) {\n        this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas));\n        this._v3_1.set(this._v2_1.x, this._v2_1.y, 0);\n        return this._v3_1.clone();\n      }\n      if (camera instanceof PerspectiveCamera) {\n        this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas));\n        this._v3_1.set(this._v2_1.x, this._v2_1.y, -1);\n        this._v3_1.applyMatrix4(camera.projectionMatrixInverse);\n        const rayDir = this._v3_1.clone().normalize();\n        const h = this._v3_1.z;\n        const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2));\n        let cameraGizmoDistance;\n        if (initialDistance) {\n          cameraGizmoDistance = this._v3_1.setFromMatrixPosition(this._cameraMatrixState0).distanceTo(this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0));\n        } else {\n          cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position);\n        }\n        if (l == 0) {\n          rayDir.set(0, 0, 0);\n          return rayDir;\n        }\n        const m = h / l;\n        const q = cameraGizmoDistance;\n        const x = -q / m;\n        const rayLength = Math.sqrt(Math.pow(q, 2) + Math.pow(x, 2));\n        rayDir.multiplyScalar(rayLength);\n        rayDir.z = 0;\n        return rayDir;\n      }\n    });\n    /**\n     * Update camera and gizmos state\n     */\n    __publicField(this, \"updateMatrixState\", () => {\n      if (!this.camera) return;\n      this._cameraMatrixState.copy(this.camera.matrix);\n      this._gizmoMatrixState.copy(this._gizmos.matrix);\n      if (this.camera instanceof OrthographicCamera) {\n        this._cameraProjectionState.copy(this.camera.projectionMatrix);\n        this.camera.updateProjectionMatrix();\n        this._zoomState = this.camera.zoom;\n      }\n      if (this.camera instanceof PerspectiveCamera) {\n        this._fovState = this.camera.fov;\n      }\n    });\n    /**\n     * Update the trackball FSA\n     * @param {STATE} newState New state of the FSA\n     * @param {Boolean} updateMatrices If matriices state should be updated\n     */\n    __publicField(this, \"updateTbState\", (newState, updateMatrices) => {\n      this._state = newState;\n      if (updateMatrices) {\n        this.updateMatrixState();\n      }\n    });\n    __publicField(this, \"update\", () => {\n      const EPS = 1e-6;\n      if (!this.target.equals(this._currentTarget) && this.camera) {\n        this._gizmos.position.set(this.target.x, this.target.y, this.target.z);\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        this.makeGizmos(this.target, this._tbRadius);\n        this._currentTarget.copy(this.target);\n      }\n      if (!this.camera) return;\n      if (this.camera instanceof OrthographicCamera) {\n        if (this.camera.zoom > this.maxZoom || this.camera.zoom < this.minZoom) {\n          const newZoom = MathUtils.clamp(this.camera.zoom, this.minZoom, this.maxZoom);\n          this.applyTransformMatrix(this.applyScale(newZoom / this.camera.zoom, this._gizmos.position, true));\n        }\n      }\n      if (this.camera instanceof PerspectiveCamera) {\n        const distance = this.camera.position.distanceTo(this._gizmos.position);\n        if (distance > this.maxDistance + EPS || distance < this.minDistance - EPS) {\n          const newDistance = MathUtils.clamp(distance, this.minDistance, this.maxDistance);\n          this.applyTransformMatrix(this.applyScale(newDistance / distance, this._gizmos.position));\n          this.updateMatrixState();\n        }\n        if (this.camera.fov < this.minFov || this.camera.fov > this.maxFov) {\n          this.camera.fov = MathUtils.clamp(this.camera.fov, this.minFov, this.maxFov);\n          this.camera.updateProjectionMatrix();\n        }\n        const oldRadius = this._tbRadius;\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        if (oldRadius < this._tbRadius - EPS || oldRadius > this._tbRadius + EPS) {\n          const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3;\n          const newRadius = this._tbRadius / scale;\n          const curve = new EllipseCurve(0, 0, newRadius, newRadius);\n          const points = curve.getPoints(this._curvePts);\n          const curveGeometry = new BufferGeometry().setFromPoints(points);\n          for (const gizmo in this._gizmos.children) {\n            const child = this._gizmos.children[gizmo];\n            child.geometry = curveGeometry;\n          }\n        }\n      }\n      this.camera.lookAt(this._gizmos.position);\n    });\n    __publicField(this, \"setStateFromJSON\", json => {\n      const state = JSON.parse(json);\n      if (state.arcballState && this.camera) {\n        this._cameraMatrixState.fromArray(state.arcballState.cameraMatrix.elements);\n        this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale);\n        this.camera.up.copy(state.arcballState.cameraUp);\n        this.camera.near = state.arcballState.cameraNear;\n        this.camera.far = state.arcballState.cameraFar;\n        this.camera.zoom = state.arcballState.cameraZoom;\n        if (this.camera instanceof PerspectiveCamera) {\n          this.camera.fov = state.arcballState.cameraFov;\n        }\n        this._gizmoMatrixState.fromArray(state.arcballState.gizmoMatrix.elements);\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n        this.camera.updateMatrix();\n        this.camera.updateProjectionMatrix();\n        this._gizmos.updateMatrix();\n        const tbRadius = this.calculateTbRadius(this.camera);\n        if (tbRadius !== void 0) {\n          this._tbRadius = tbRadius;\n        }\n        const gizmoTmp = new Matrix4().copy(this._gizmoMatrixState0);\n        this.makeGizmos(this._gizmos.position, this._tbRadius);\n        this._gizmoMatrixState0.copy(gizmoTmp);\n        this.camera.lookAt(this._gizmos.position);\n        this.updateTbState(STATE.IDLE, false);\n        this.dispatchEvent(_changeEvent);\n      }\n    });\n    this.camera = null;\n    this.domElement = domElement;\n    this.scene = scene;\n    this.mouseActions = [];\n    this._mouseOp = null;\n    this._v2_1 = new Vector2();\n    this._v3_1 = new Vector3();\n    this._v3_2 = new Vector3();\n    this._m4_1 = new Matrix4();\n    this._m4_2 = new Matrix4();\n    this._quat = new Quaternion();\n    this._translationMatrix = new Matrix4();\n    this._rotationMatrix = new Matrix4();\n    this._scaleMatrix = new Matrix4();\n    this._rotationAxis = new Vector3();\n    this._cameraMatrixState = new Matrix4();\n    this._cameraProjectionState = new Matrix4();\n    this._fovState = 1;\n    this._upState = new Vector3();\n    this._zoomState = 1;\n    this._nearPos = 0;\n    this._farPos = 0;\n    this._gizmoMatrixState = new Matrix4();\n    this._up0 = new Vector3();\n    this._zoom0 = 1;\n    this._fov0 = 0;\n    this._initialNear = 0;\n    this._nearPos0 = 0;\n    this._initialFar = 0;\n    this._farPos0 = 0;\n    this._cameraMatrixState0 = new Matrix4();\n    this._gizmoMatrixState0 = new Matrix4();\n    this._button = -1;\n    this._touchStart = [];\n    this._touchCurrent = [];\n    this._input = INPUT.NONE;\n    this._switchSensibility = 32;\n    this._startFingerDistance = 0;\n    this._currentFingerDistance = 0;\n    this._startFingerRotation = 0;\n    this._currentFingerRotation = 0;\n    this._devPxRatio = 0;\n    this._downValid = true;\n    this._nclicks = 0;\n    this._downEvents = [];\n    this._clickStart = 0;\n    this._maxDownTime = 250;\n    this._maxInterval = 300;\n    this._posThreshold = 24;\n    this._movementThreshold = 24;\n    this._currentCursorPosition = new Vector3();\n    this._startCursorPosition = new Vector3();\n    this._grid = null;\n    this._gridPosition = new Vector3();\n    this._gizmos = new Group();\n    this._curvePts = 128;\n    this._timeStart = -1;\n    this._animationId = -1;\n    this.focusAnimationTime = 500;\n    this._timePrev = 0;\n    this._timeCurrent = 0;\n    this._anglePrev = 0;\n    this._angleCurrent = 0;\n    this._cursorPosPrev = new Vector3();\n    this._cursorPosCurr = new Vector3();\n    this._wPrev = 0;\n    this._wCurr = 0;\n    this.adjustNearFar = false;\n    this.scaleFactor = 1.1;\n    this.dampingFactor = 25;\n    this.wMax = 20;\n    this.enableAnimations = true;\n    this.enableGrid = false;\n    this.cursorZoom = false;\n    this.minFov = 5;\n    this.maxFov = 90;\n    this.enabled = true;\n    this.enablePan = true;\n    this.enableRotate = true;\n    this.enableZoom = true;\n    this.minDistance = 0;\n    this.maxDistance = Infinity;\n    this.minZoom = 0;\n    this.maxZoom = Infinity;\n    this.target = new Vector3(0, 0, 0);\n    this._currentTarget = new Vector3(0, 0, 0);\n    this._tbRadius = 1;\n    this._state = STATE.IDLE;\n    this.setCamera(camera);\n    if (this.scene) {\n      this.scene.add(this._gizmos);\n    }\n    this._devPxRatio = window.devicePixelRatio;\n    this.initializeMouseActions();\n    if (this.domElement) this.connect(this.domElement);\n    window.addEventListener(\"resize\", this.onWindowResize);\n  }\n  /**\n   * Apply a transformation matrix, to the camera and gizmos\n   * @param {Object} transformation Object containing matrices to apply to camera and gizmos\n   */\n  applyTransformMatrix(transformation) {\n    if ((transformation == null ? void 0 : transformation.camera) && this.camera) {\n      this._m4_1.copy(this._cameraMatrixState).premultiply(transformation.camera);\n      this._m4_1.decompose(this.camera.position, this.camera.quaternion, this.camera.scale);\n      this.camera.updateMatrix();\n      if (this._state == STATE.ROTATE || this._state == STATE.ZROTATE || this._state == STATE.ANIMATION_ROTATE) {\n        this.camera.up.copy(this._upState).applyQuaternion(this.camera.quaternion);\n      }\n    }\n    if (transformation == null ? void 0 : transformation.gizmos) {\n      this._m4_1.copy(this._gizmoMatrixState).premultiply(transformation.gizmos);\n      this._m4_1.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale);\n      this._gizmos.updateMatrix();\n    }\n    if ((this._state == STATE.SCALE || this._state == STATE.FOCUS || this._state == STATE.ANIMATION_FOCUS) && this.camera) {\n      const tbRadius = this.calculateTbRadius(this.camera);\n      if (tbRadius !== void 0) {\n        this._tbRadius = tbRadius;\n      }\n      if (this.adjustNearFar) {\n        const cameraDistance = this.camera.position.distanceTo(this._gizmos.position);\n        const bb = new Box3();\n        bb.setFromObject(this._gizmos);\n        const sphere = new Sphere();\n        bb.getBoundingSphere(sphere);\n        const adjustedNearPosition = Math.max(this._nearPos0, sphere.radius + sphere.center.length());\n        const regularNearPosition = cameraDistance - this._initialNear;\n        const minNearPos = Math.min(adjustedNearPosition, regularNearPosition);\n        this.camera.near = cameraDistance - minNearPos;\n        const adjustedFarPosition = Math.min(this._farPos0, -sphere.radius + sphere.center.length());\n        const regularFarPosition = cameraDistance - this._initialFar;\n        const minFarPos = Math.min(adjustedFarPosition, regularFarPosition);\n        this.camera.far = cameraDistance - minFarPos;\n        this.camera.updateProjectionMatrix();\n      } else {\n        let update = false;\n        if (this.camera.near != this._initialNear) {\n          this.camera.near = this._initialNear;\n          update = true;\n        }\n        if (this.camera.far != this._initialFar) {\n          this.camera.far = this._initialFar;\n          update = true;\n        }\n        if (update) {\n          this.camera.updateProjectionMatrix();\n        }\n      }\n    }\n  }\n  /**\n   * Set gizmos visibility\n   * @param {Boolean} value Value of gizmos visibility\n   */\n  setGizmosVisible(value) {\n    this._gizmos.visible = value;\n    this.dispatchEvent(_changeEvent);\n  }\n  /**\n   * Set values in transformation object\n   * @param {Matrix4} camera Transformation to be applied to the camera\n   * @param {Matrix4} gizmos Transformation to be applied to gizmos\n   */\n  setTransformationMatrices(camera = null, gizmos = null) {\n    if (camera) {\n      if (_transformation.camera) {\n        _transformation.camera.copy(camera);\n      } else {\n        _transformation.camera = camera.clone();\n      }\n    } else {\n      _transformation.camera = null;\n    }\n    if (gizmos) {\n      if (_transformation.gizmos) {\n        _transformation.gizmos.copy(gizmos);\n      } else {\n        _transformation.gizmos = gizmos.clone();\n      }\n    } else {\n      _transformation.gizmos = null;\n    }\n  }\n}\nexport { ArcballControls };", "map": {"version": 3, "names": ["STATE", "IDLE", "Symbol", "ROTATE", "PAN", "SCALE", "FOV", "FOCUS", "ZROTATE", "TOUCH_MULTI", "ANIMATION_FOCUS", "ANIMATION_ROTATE", "INPUT", "NONE", "ONE_FINGER", "ONE_FINGER_SWITCHED", "TWO_FINGER", "MULT_FINGER", "CURSOR", "_center", "x", "y", "_transformation", "camera", "Matrix4", "gizmos", "_changeEvent", "type", "_startEvent", "_endEvent", "ArcballControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "dom<PERSON>lement", "scene", "__publicField", "scale", "_gizmos", "z", "tbRadius", "calculateTbRadius", "_tbRadius", "newRadius", "curve", "EllipseCurve", "points", "getPoints", "_curvePts", "curveGeometry", "BufferGeometry", "setFromPoints", "gizmo", "children", "child", "geometry", "dispatchEvent", "event", "enabled", "i", "mouseActions", "length", "mouse", "preventDefault", "_touchStart", "splice", "_touchCurrent", "_input", "button", "isPrimary", "_downValid", "_downEvents", "push", "pointerType", "onSinglePanStart", "window", "addEventListener", "onPointerMove", "onPointerUp", "onRotateStart", "onPinchStart", "onDoublePanStart", "onTriplePanStart", "modifier", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "_mouseOp", "getOpFromAction", "_button", "updateTouchEvent", "onSinglePanMove", "movement", "calculatePointersDistance", "_devPxRatio", "_switchSensibility", "onRotateMove", "onPinchMove", "onDoublePanMove", "onTriplePanMove", "mouseOpState", "getOpStateFromAction", "_movementThreshold", "nTouch", "pointerId", "removeEventListener", "onSinglePanEnd", "onDoublePanEnd", "onPinchEnd", "onRotateEnd", "onTriplePanEnd", "downTime", "timeStamp", "_maxDownTime", "_nclicks", "_clickStart", "performance", "now", "clickInterval", "_maxInterval", "_posThreshold", "onDoubleTap", "shift", "enableZoom", "mouseOp", "notchDeltaY", "sgn", "deltaY", "size", "scaleFactor", "updateTbState", "Math", "pow", "cursor<PERSON><PERSON>", "enablePan", "scalePoint", "OrthographicCamera", "_a", "unprojectOnTbPlane", "clientX", "clientY", "applyQuaternion", "quaternion", "multiplyScalar", "zoom", "add", "position", "PerspectiveCamera", "_b", "applyTransformMatrix", "applyScale", "_grid", "disposeGrid", "drawGrid", "deltaX", "_v3_1", "setFromMatrixPosition", "_cameraMatrixState", "distanceTo", "xNew", "MathUtils", "clamp", "minDistance", "maxDistance", "tan", "DEG2RAD", "fov", "newFov", "RAD2DEG", "atan", "max<PERSON>ov", "<PERSON><PERSON><PERSON>", "newDistance", "<PERSON><PERSON><PERSON>", "operation", "setCenter", "_animationId", "cancelAnimationFrame", "_timeStart", "activateGizmos", "rayDir", "_startCursorPosition", "copy", "enableGrid", "enableRotate", "unprojectOnTbSurface", "enableAnimations", "_timePrev", "_timeCurrent", "_angleCurrent", "_anglePrev", "_cursorPosPrev", "_cursorPosCurr", "_wCurr", "_wPrev", "setY", "getCursorNDC", "_currentCursorPosition", "opState", "restart", "_state", "pan", "distance", "angle", "angleTo", "amount", "max", "rotate", "calculateRotationAxis", "calculateAngularSpeed", "screenNotches", "_fovState", "_v3_2", "_gizmoMatrixState", "direction", "clone", "sub", "normalize", "_m4_1", "makeTranslation", "deltaTime", "w", "abs", "self", "requestAnimationFrame", "t", "rotationAxis", "onRotationAnim", "min", "wMax", "hitP", "unprojectOnObj", "onFocusAnim", "focus", "_startFingerRotation", "getAngle", "_currentFingerRotation", "getWorldDirection", "_rotationAxis", "rotationPoint", "Vector3", "zRotate", "_startFingerDistance", "_currentFingerDistance", "nFingers", "setMouseAction", "key", "operationInput", "mouseInput", "keyInput", "state", "includes", "action", "p1", "p2", "atan2", "PI", "p0", "t0", "t1", "s", "sqrt", "vec1", "vec2", "_rotationMatrix", "extractRotation", "_quat", "setFromRotationMatrix", "crossVectors", "factor", "halfFovV", "halfFovH", "aspect", "top", "right", "point", "focusPoint", "_translationMatrix", "gizmoStateTemp", "premultiply", "decompose", "cameraStateTemp", "color", "multiplier", "divisions", "max<PERSON><PERSON><PERSON>", "tick", "width", "left", "height", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_gridPosition", "rotateX", "document", "console", "error", "style", "touchAction", "onContextMenu", "onPointerDown", "onPointerCancel", "onWheel", "_c", "_d", "onWindowResize", "_e", "remove", "isActive", "material", "set<PERSON><PERSON><PERSON>", "opacity", "cursorX", "cursorY", "canvas", "canvasRect", "getBoundingClientRect", "_v2_1", "setX", "lookAt", "target", "updateMatrix", "_fov0", "_cameraMatrixState0", "matrix", "_cameraProjectionState", "projectionMatrix", "_zoom0", "_zoomState", "_initialNear", "near", "_nearPos0", "_nearPos", "_initialFar", "far", "_farPos0", "_farPos", "_up0", "up", "_upState", "updateProjectionMatrix", "makeGizmos", "tbCenter", "curveMaterialX", "LineBasicMaterial", "fog", "transparent", "curveMaterialY", "curveMaterialZ", "gizmoX", "Line", "gizmoY", "gizmoZ", "rotation", "_gizmoMatrixState0", "identity", "setPosition", "_scaleMatrix", "makeScale", "clear", "time", "cameraMatrix", "gizmoMatrix", "animTime", "focusAnimationTime", "easeOutCubic", "w0", "dampingFactor", "adjust", "distanceFactor", "set", "setTransformationMatrices", "axis", "makeRotationAxis", "multiply", "JSON", "stringify", "arcballState", "cameraFar", "cameraNear", "cameraUp", "cameraZoom", "cameraFov", "navigator", "clipboard", "writeText", "readText", "then", "resolved", "value", "setStateFromJSON", "scaleGizmos", "sizeInverse", "max<PERSON><PERSON>", "minZoom", "_m4_2", "pos", "applyAxisAngle", "cursor", "raycaster", "Raycaster", "setFromCamera", "intersect", "intersectObjects", "object", "uuid", "face", "getCursorPosition", "x2", "y2", "r2", "setZ", "applyMatrix4", "projectionMatrixInverse", "cameraGizmoDistance", "radius2", "h", "l", "m", "q", "a", "b", "c", "delta", "rayLength2", "<PERSON><PERSON><PERSON><PERSON>", "initialDistance", "newState", "updateMatrices", "updateMatrixState", "EPS", "equals", "_currentTarget", "newZoom", "oldRadius", "json", "parse", "fromArray", "elements", "gizmoTmp", "Vector2", "Quaternion", "Group", "adjustNearFar", "Infinity", "setCamera", "devicePixelRatio", "initializeMouseActions", "connect", "transformation", "cameraDistance", "bb", "Box3", "setFromObject", "sphere", "Sphere", "getBoundingSphere", "adjustedNearPosition", "radius", "center", "regularNearPosition", "minNearPos", "adjustedFarPosition", "regularFarPosition", "minFarPos", "update", "setGizmosVisible", "visible"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/controls/ArcballControls.ts"], "sourcesContent": ["import {\n  GridHelper,\n  EllipseCurve,\n  BufferGeometry,\n  Line,\n  LineBasicMaterial,\n  Raycaster,\n  Group,\n  Box3,\n  Sphere,\n  Quaternion,\n  Vector2,\n  Vector3,\n  Matrix4,\n  MathUtils,\n  Scene,\n  PerspectiveCamera,\n  OrthographicCamera,\n  Mesh,\n  Material,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\ntype Camera = OrthographicCamera | PerspectiveCamera\ntype Operation = 'PAN' | 'ROTATE' | 'ZOOM' | 'FOV'\ntype MouseButtonType = number | 'WHEEL'\ntype ModifierKey = 'CTRL' | 'SHIFT'\ntype MouseAction = {\n  operation: Operation\n  mouse: MouseButtonType\n  key: Modifier<PERSON>ey | null\n}\n\ntype Transformation = {\n  camera: Matrix4 | null\n  gizmos: Matrix4 | null\n}\n\n//trackball state\nconst STATE = {\n  IDLE: Symbol(),\n  ROTATE: Symbol(),\n  PAN: Symbol(),\n  SCALE: Symbol(),\n  FOV: Symbol(),\n  FOCUS: Symbol(),\n  ZROTATE: Symbol(),\n  TOUCH_MULTI: Symbol(),\n  ANIMATION_FOCUS: Symbol(),\n  ANIMATION_ROTATE: Symbol(),\n}\n\nconst INPUT = {\n  NONE: Symbol(),\n  ONE_FINGER: Symbol(),\n  ONE_FINGER_SWITCHED: Symbol(),\n  TWO_FINGER: Symbol(),\n  MULT_FINGER: Symbol(),\n  CURSOR: Symbol(),\n}\n\n//cursor center coordinates\nconst _center = {\n  x: 0,\n  y: 0,\n}\n\n//transformation matrices for gizmos and camera\nconst _transformation: Transformation = {\n  camera: /* @__PURE__ */ new Matrix4(),\n  gizmos: /* @__PURE__ */ new Matrix4(),\n}\n\n//events\nconst _changeEvent = { type: 'change' }\nconst _startEvent = { type: 'start' }\nconst _endEvent = { type: 'end' }\n\n/**\n *\n * @param {CamOrthographicCamera | PerspectiveCameraera} camera Virtual camera used in the scene\n * @param {HTMLElement=null} domElement Renderer's dom element\n * @param {Scene=null} scene The scene to be rendered\n */\nclass ArcballControls extends EventDispatcher<StandardControlsEventMap> {\n  private camera: OrthographicCamera | PerspectiveCamera | null\n  private domElement: HTMLElement | null | undefined\n  private scene: Scene | null | undefined\n\n  private mouseActions: (MouseAction & { state: Symbol })[]\n  private _mouseOp: Operation | null\n\n  private _v2_1: Vector2\n  private _v3_1: Vector3\n  private _v3_2: Vector3\n\n  private _m4_1: Matrix4\n  private _m4_2: Matrix4\n\n  private _quat: Quaternion\n\n  private _translationMatrix: Matrix4\n  private _rotationMatrix: Matrix4\n  private _scaleMatrix: Matrix4\n\n  private _rotationAxis: Vector3\n\n  private _cameraMatrixState: Matrix4\n  private _cameraProjectionState: Matrix4\n\n  private _fovState: number\n  private _upState: Vector3\n  private _zoomState: number\n  private _nearPos: number\n  private _farPos: number\n\n  private _gizmoMatrixState: Matrix4\n\n  private _up0: Vector3\n  private _zoom0: number\n  private _fov0: number\n  private _initialNear: number\n  private _nearPos0: number\n  private _initialFar: number\n  private _farPos0: number\n  private _cameraMatrixState0: Matrix4\n  private _gizmoMatrixState0: Matrix4\n\n  private _button: MouseButtonType\n  private _touchStart: PointerEvent[]\n  private _touchCurrent: PointerEvent[]\n  private _input: Symbol\n\n  private _switchSensibility: number\n  private _startFingerDistance: number\n  private _currentFingerDistance: number\n  private _startFingerRotation: number\n  private _currentFingerRotation: number\n\n  private _devPxRatio: number\n  private _downValid: boolean\n  private _nclicks: number\n  private _downEvents: PointerEvent[]\n  private _clickStart: number\n  private _maxDownTime: number\n  private _maxInterval: number\n  private _posThreshold: number\n  private _movementThreshold: number\n\n  private _currentCursorPosition: Vector3\n  private _startCursorPosition: Vector3\n\n  private _grid: GridHelper | null\n  private _gridPosition: Vector3\n\n  private _gizmos: Group\n  private _curvePts: number\n\n  private _timeStart: number\n  private _animationId: number\n\n  public focusAnimationTime: number\n\n  private _timePrev: number\n  private _timeCurrent: number\n  private _anglePrev: number\n  private _angleCurrent: number\n  private _cursorPosPrev: Vector3\n  private _cursorPosCurr: Vector3\n  private _wPrev: number\n  private _wCurr: number\n\n  public adjustNearFar: boolean\n  public scaleFactor: number\n  public dampingFactor: number\n  public wMax: number\n  public enableAnimations: boolean\n  public enableGrid: boolean\n  public cursorZoom: boolean\n  public minFov: number\n  public maxFov: number\n\n  public enabled: boolean\n  public enablePan: boolean\n  public enableRotate: boolean\n  public enableZoom: boolean\n\n  public minDistance: number\n  public maxDistance: number\n  public minZoom: number\n  public maxZoom: number\n\n  readonly target: Vector3\n  private _currentTarget: Vector3\n\n  private _tbRadius: number\n\n  private _state: Symbol\n\n  constructor(\n    camera: Camera | null,\n    domElement: HTMLElement | null | undefined = null,\n    scene: Scene | null | undefined = null,\n  ) {\n    super()\n    this.camera = null\n    this.domElement = domElement\n    this.scene = scene\n\n    this.mouseActions = []\n    this._mouseOp = null\n\n    //global vectors and matrices that are used in some operations to avoid creating new objects every time (e.g. every time cursor moves)\n    this._v2_1 = new Vector2()\n    this._v3_1 = new Vector3()\n    this._v3_2 = new Vector3()\n\n    this._m4_1 = new Matrix4()\n    this._m4_2 = new Matrix4()\n\n    this._quat = new Quaternion()\n\n    //transformation matrices\n    this._translationMatrix = new Matrix4() //matrix for translation operation\n    this._rotationMatrix = new Matrix4() //matrix for rotation operation\n    this._scaleMatrix = new Matrix4() //matrix for scaling operation\n\n    this._rotationAxis = new Vector3() //axis for rotate operation\n\n    //camera state\n    this._cameraMatrixState = new Matrix4()\n    this._cameraProjectionState = new Matrix4()\n\n    this._fovState = 1\n    this._upState = new Vector3()\n    this._zoomState = 1\n    this._nearPos = 0\n    this._farPos = 0\n\n    this._gizmoMatrixState = new Matrix4()\n\n    //initial values\n    this._up0 = new Vector3()\n    this._zoom0 = 1\n    this._fov0 = 0\n    this._initialNear = 0\n    this._nearPos0 = 0\n    this._initialFar = 0\n    this._farPos0 = 0\n    this._cameraMatrixState0 = new Matrix4()\n    this._gizmoMatrixState0 = new Matrix4()\n\n    //pointers array\n    this._button = -1\n    this._touchStart = []\n    this._touchCurrent = []\n    this._input = INPUT.NONE\n\n    //two fingers touch interaction\n    this._switchSensibility = 32 //minimum movement to be performed to fire single pan start after the second finger has been released\n    this._startFingerDistance = 0 //distance between two fingers\n    this._currentFingerDistance = 0\n    this._startFingerRotation = 0 //amount of rotation performed with two fingers\n    this._currentFingerRotation = 0\n\n    //double tap\n    this._devPxRatio = 0\n    this._downValid = true\n    this._nclicks = 0\n    this._downEvents = []\n    this._clickStart = 0 //first click time\n    this._maxDownTime = 250\n    this._maxInterval = 300\n    this._posThreshold = 24\n    this._movementThreshold = 24\n\n    //cursor positions\n    this._currentCursorPosition = new Vector3()\n    this._startCursorPosition = new Vector3()\n\n    //grid\n    this._grid = null //grid to be visualized during pan operation\n    this._gridPosition = new Vector3()\n\n    //gizmos\n    this._gizmos = new Group()\n    this._curvePts = 128\n\n    //animations\n    this._timeStart = -1 //initial time\n    this._animationId = -1\n\n    //focus animation\n    this.focusAnimationTime = 500 //duration of focus animation in ms\n\n    //rotate animation\n    this._timePrev = 0 //time at which previous rotate operation has been detected\n    this._timeCurrent = 0 //time at which current rotate operation has been detected\n    this._anglePrev = 0 //angle of previous rotation\n    this._angleCurrent = 0 //angle of current rotation\n    this._cursorPosPrev = new Vector3() //cursor position when previous rotate operation has been detected\n    this._cursorPosCurr = new Vector3() //cursor position when current rotate operation has been detected\n    this._wPrev = 0 //angular velocity of the previous rotate operation\n    this._wCurr = 0 //angular velocity of the current rotate operation\n\n    //parameters\n    this.adjustNearFar = false\n    this.scaleFactor = 1.1 //zoom/distance multiplier\n    this.dampingFactor = 25\n    this.wMax = 20 //maximum angular velocity allowed\n    this.enableAnimations = true //if animations should be performed\n    this.enableGrid = false //if grid should be showed during pan operation\n    this.cursorZoom = false //if wheel zoom should be cursor centered\n    this.minFov = 5\n    this.maxFov = 90\n\n    this.enabled = true\n    this.enablePan = true\n    this.enableRotate = true\n    this.enableZoom = true\n\n    this.minDistance = 0\n    this.maxDistance = Infinity\n    this.minZoom = 0\n    this.maxZoom = Infinity\n\n    //trackball parameters\n    this.target = new Vector3(0, 0, 0)\n    this._currentTarget = new Vector3(0, 0, 0)\n\n    this._tbRadius = 1\n\n    //FSA\n    this._state = STATE.IDLE\n\n    this.setCamera(camera)\n\n    if (this.scene) {\n      this.scene.add(this._gizmos)\n    }\n\n    this._devPxRatio = window.devicePixelRatio\n\n    this.initializeMouseActions()\n\n    if (this.domElement) this.connect(this.domElement)\n\n    window.addEventListener('resize', this.onWindowResize)\n  }\n\n  //listeners\n\n  private onWindowResize = (): void => {\n    const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n    if (this.camera) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n    }\n\n    const newRadius = this._tbRadius / scale\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n    const points = curve.getPoints(this._curvePts)\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    for (const gizmo in this._gizmos.children) {\n      const child = this._gizmos.children[gizmo] as Mesh\n      child.geometry = curveGeometry\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onContextMenu = (event: MouseEvent): void => {\n    if (!this.enabled) {\n      return\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == 2) {\n        //prevent only if button 2 is actually used\n        event.preventDefault()\n        break\n      }\n    }\n  }\n\n  private onPointerCancel = (): void => {\n    this._touchStart.splice(0, this._touchStart.length)\n    this._touchCurrent.splice(0, this._touchCurrent.length)\n    this._input = INPUT.NONE\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (event.button == 0 && event.isPrimary) {\n      this._downValid = true\n      this._downEvents.push(event)\n    } else {\n      this._downValid = false\n    }\n\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      this._touchStart.push(event)\n      this._touchCurrent.push(event)\n\n      switch (this._input) {\n        case INPUT.NONE:\n          //singleStart\n          this._input = INPUT.ONE_FINGER\n          this.onSinglePanStart(event, 'ROTATE')\n\n          window.addEventListener('pointermove', this.onPointerMove)\n          window.addEventListener('pointerup', this.onPointerUp)\n\n          break\n\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //doubleStart\n          this._input = INPUT.TWO_FINGER\n\n          this.onRotateStart()\n          this.onPinchStart()\n          this.onDoublePanStart()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //multipleStart\n          this._input = INPUT.MULT_FINGER\n          this.onTriplePanStart()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.NONE) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      this._mouseOp = this.getOpFromAction(event.button, modifier)\n      if (this._mouseOp) {\n        window.addEventListener('pointermove', this.onPointerMove)\n        window.addEventListener('pointerup', this.onPointerUp)\n\n        //singleStart\n        this._input = INPUT.CURSOR\n        this._button = event.button\n        this.onSinglePanStart(event, this._mouseOp)\n      }\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n          //singleMove\n          this.updateTouchEvent(event)\n\n          this.onSinglePanMove(event, STATE.ROTATE)\n          break\n\n        case INPUT.ONE_FINGER_SWITCHED:\n          const movement = this.calculatePointersDistance(this._touchCurrent[0], event) * this._devPxRatio\n\n          if (movement >= this._switchSensibility) {\n            //singleMove\n            this._input = INPUT.ONE_FINGER\n            this.updateTouchEvent(event)\n\n            this.onSinglePanStart(event, 'ROTATE')\n            break\n          }\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //rotate/pan/pinchMove\n          this.updateTouchEvent(event)\n\n          this.onRotateMove()\n          this.onPinchMove()\n          this.onDoublePanMove()\n\n          break\n\n        case INPUT.MULT_FINGER:\n          //multMove\n          this.updateTouchEvent(event)\n\n          this.onTriplePanMove()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOpState = this.getOpStateFromAction(this._button, modifier)\n\n      if (mouseOpState) {\n        this.onSinglePanMove(event, mouseOpState)\n      }\n    }\n\n    //checkDistance\n    if (this._downValid) {\n      const movement =\n        this.calculatePointersDistance(this._downEvents[this._downEvents.length - 1], event) * this._devPxRatio\n      if (movement > this._movementThreshold) {\n        this._downValid = false\n      }\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      const nTouch = this._touchCurrent.length\n\n      for (let i = 0; i < nTouch; i++) {\n        if (this._touchCurrent[i].pointerId == event.pointerId) {\n          this._touchCurrent.splice(i, 1)\n          this._touchStart.splice(i, 1)\n          break\n        }\n      }\n\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //singleEnd\n          window.removeEventListener('pointermove', this.onPointerMove)\n          window.removeEventListener('pointerup', this.onPointerUp)\n\n          this._input = INPUT.NONE\n          this.onSinglePanEnd()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //doubleEnd\n          this.onDoublePanEnd()\n          this.onPinchEnd()\n          this.onRotateEnd()\n\n          //switching to singleStart\n          this._input = INPUT.ONE_FINGER_SWITCHED\n\n          break\n\n        case INPUT.MULT_FINGER:\n          if (this._touchCurrent.length == 0) {\n            window.removeEventListener('pointermove', this.onPointerMove)\n            window.removeEventListener('pointerup', this.onPointerUp)\n\n            //multCancel\n            this._input = INPUT.NONE\n            this.onTriplePanEnd()\n          }\n\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      window.removeEventListener('pointermove', this.onPointerMove)\n      window.removeEventListener('pointerup', this.onPointerUp)\n\n      this._input = INPUT.NONE\n      this.onSinglePanEnd()\n      this._button = -1\n    }\n\n    if (event.isPrimary) {\n      if (this._downValid) {\n        const downTime = event.timeStamp - this._downEvents[this._downEvents.length - 1].timeStamp\n\n        if (downTime <= this._maxDownTime) {\n          if (this._nclicks == 0) {\n            //first valid click detected\n            this._nclicks = 1\n            this._clickStart = performance.now()\n          } else {\n            const clickInterval = event.timeStamp - this._clickStart\n            const movement = this.calculatePointersDistance(this._downEvents[1], this._downEvents[0]) * this._devPxRatio\n\n            if (clickInterval <= this._maxInterval && movement <= this._posThreshold) {\n              //second valid click detected\n              //fire double tap and reset values\n              this._nclicks = 0\n              this._downEvents.splice(0, this._downEvents.length)\n              this.onDoubleTap(event)\n            } else {\n              //new 'first click'\n              this._nclicks = 1\n              this._downEvents.shift()\n              this._clickStart = performance.now()\n            }\n          }\n        } else {\n          this._downValid = false\n          this._nclicks = 0\n          this._downEvents.splice(0, this._downEvents.length)\n        }\n      } else {\n        this._nclicks = 0\n        this._downEvents.splice(0, this._downEvents.length)\n      }\n    }\n  }\n\n  private onWheel = (event: WheelEvent): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOp = this.getOpFromAction('WHEEL', modifier)\n\n      if (mouseOp) {\n        event.preventDefault()\n        // @ts-ignore\n        this.dispatchEvent(_startEvent)\n\n        const notchDeltaY = 125 //distance of one notch of mouse wheel\n        let sgn = event.deltaY / notchDeltaY\n\n        let size = 1\n\n        if (sgn > 0) {\n          size = 1 / this.scaleFactor\n        } else if (sgn < 0) {\n          size = this.scaleFactor\n        }\n\n        switch (mouseOp) {\n          case 'ZOOM':\n            this.updateTbState(STATE.SCALE, true)\n\n            if (sgn > 0) {\n              size = 1 / Math.pow(this.scaleFactor, sgn)\n            } else if (sgn < 0) {\n              size = Math.pow(this.scaleFactor, -sgn)\n            }\n\n            if (this.cursorZoom && this.enablePan) {\n              let scalePoint\n\n              if (this.camera instanceof OrthographicCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .multiplyScalar(1 / this.camera.zoom)\n                  .add(this._gizmos.position)\n              }\n\n              if (this.camera instanceof PerspectiveCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .add(this._gizmos.position)\n              }\n\n              if (scalePoint !== undefined) this.applyTransformMatrix(this.applyScale(size, scalePoint))\n            } else {\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n\n          case 'FOV':\n            if (this.camera instanceof PerspectiveCamera) {\n              this.updateTbState(STATE.FOV, true)\n\n              //Vertigo effect\n\n              //\t  fov / 2\n              //\t\t|\\\n              //\t\t| \\\n              //\t\t|  \\\n              //\tx\t|\t\\\n              //\t\t| \t \\\n              //\t\t| \t  \\\n              //\t\t| _ _ _\\\n              //\t\t\ty\n\n              //check for iOs shift shortcut\n              if (event.deltaX != 0) {\n                sgn = event.deltaX / notchDeltaY\n\n                size = 1\n\n                if (sgn > 0) {\n                  size = 1 / Math.pow(this.scaleFactor, sgn)\n                } else if (sgn < 0) {\n                  size = Math.pow(this.scaleFactor, -sgn)\n                }\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this.camera.fov * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              if (newFov > this.maxFov) {\n                newFov = this.maxFov\n              } else if (newFov < this.minFov) {\n                newFov = this.minFov\n              }\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position, false))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n        }\n      }\n    }\n  }\n\n  private onSinglePanStart = (event: PointerEvent, operation: Operation): void => {\n    if (this.enabled && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (operation) {\n        case 'PAN':\n          if (!this.enablePan) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.PAN, true)\n            const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            if (this.enableGrid) {\n              this.drawGrid()\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n          }\n\n          break\n\n        case 'ROTATE':\n          if (!this.enableRotate) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.ROTATE, true)\n            const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            this.activateGizmos(true)\n            if (this.enableAnimations) {\n              this._timePrev = this._timeCurrent = performance.now()\n              this._angleCurrent = this._anglePrev = 0\n              this._cursorPosPrev.copy(this._startCursorPosition)\n              this._cursorPosCurr.copy(this._cursorPosPrev)\n              this._wCurr = 0\n              this._wPrev = this._wCurr\n            }\n          }\n\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n          break\n\n        case 'FOV':\n          if (!this.enableZoom) return\n\n          if (this.camera instanceof PerspectiveCamera) {\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId)\n              this._animationId = -1\n              this._timeStart = -1\n\n              this.activateGizmos(false)\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n\n            this.updateTbState(STATE.FOV, true)\n            this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n            this._currentCursorPosition.copy(this._startCursorPosition)\n          }\n          break\n\n        case 'ZOOM':\n          if (!this.enableZoom) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          this.updateTbState(STATE.SCALE, true)\n          this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n          this._currentCursorPosition.copy(this._startCursorPosition)\n          break\n      }\n    }\n  }\n\n  private onSinglePanMove = (event: PointerEvent, opState: Symbol): void => {\n    if (this.enabled && this.domElement) {\n      const restart = opState != this._state\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (opState) {\n        case STATE.PAN:\n          if (this.enablePan && this.camera) {\n            if (restart) {\n              //switch to pan operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n              if (this.enableGrid) {\n                this.drawGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with pan operation\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n              this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition))\n            }\n          }\n\n          break\n\n        case STATE.ROTATE:\n          if (this.enableRotate && this.camera) {\n            if (restart) {\n              //switch to rotate operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(true)\n            } else {\n              //continue with rotate operation\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n\n              const distance = this._startCursorPosition.distanceTo(this._currentCursorPosition)\n              const angle = this._startCursorPosition.angleTo(this._currentCursorPosition)\n              const amount = Math.max(distance / this._tbRadius, angle) //effective rotation angle\n\n              this.applyTransformMatrix(\n                this.rotate(this.calculateRotationAxis(this._startCursorPosition, this._currentCursorPosition), amount),\n              )\n\n              if (this.enableAnimations) {\n                this._timePrev = this._timeCurrent\n                this._timeCurrent = performance.now()\n                this._anglePrev = this._angleCurrent\n                this._angleCurrent = amount\n                this._cursorPosPrev.copy(this._cursorPosCurr)\n                this._cursorPosCurr.copy(this._currentCursorPosition)\n                this._wPrev = this._wCurr\n                this._wCurr = this.calculateAngularSpeed(\n                  this._anglePrev,\n                  this._angleCurrent,\n                  this._timePrev,\n                  this._timeCurrent,\n                )\n              }\n            }\n          }\n\n          break\n\n        case STATE.SCALE:\n          if (this.enableZoom) {\n            if (restart) {\n              //switch to zoom operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with zoom operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n          }\n\n          break\n\n        case STATE.FOV:\n          if (this.enableZoom && this.camera instanceof PerspectiveCamera) {\n            if (restart) {\n              //switch to fov operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with fov operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n              this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n              //adjusting distance\n              const direction = this._gizmos.position\n                .clone()\n                .sub(this.camera.position)\n                .normalize()\n                .multiplyScalar(newDistance / x)\n              this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n            }\n          }\n\n          break\n      }\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onSinglePanEnd = (): void => {\n    if (this._state == STATE.ROTATE) {\n      if (!this.enableRotate) {\n        return\n      }\n\n      if (this.enableAnimations) {\n        //perform rotation animation\n        const deltaTime = performance.now() - this._timeCurrent\n        if (deltaTime < 120) {\n          const w = Math.abs((this._wPrev + this._wCurr) / 2)\n\n          const self = this\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.updateTbState(STATE.ANIMATION_ROTATE, true)\n            const rotationAxis = self.calculateRotationAxis(self._cursorPosPrev, self._cursorPosCurr)\n\n            self.onRotationAnim(t, rotationAxis, Math.min(w, self.wMax))\n          })\n        } else {\n          //cursor has been standing still for over 120 ms since last movement\n          this.updateTbState(STATE.IDLE, false)\n          this.activateGizmos(false)\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n        }\n      } else {\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else if (this._state == STATE.PAN || this._state == STATE.IDLE) {\n      this.updateTbState(STATE.IDLE, false)\n\n      if (this.enableGrid) {\n        this.disposeGrid()\n      }\n\n      this.activateGizmos(false)\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoubleTap = (event: PointerEvent): void => {\n    if (this.enabled && this.enablePan && this.scene && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n      const hitP = this.unprojectOnObj(this.getCursorNDC(_center.x, _center.y, this.domElement), this.camera)\n\n      if (hitP && this.enableAnimations) {\n        const self = this\n        if (this._animationId != -1) {\n          window.cancelAnimationFrame(this._animationId)\n        }\n\n        this._timeStart = -1\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.updateTbState(STATE.ANIMATION_FOCUS, true)\n          self.onFocusAnim(t, hitP, self._cameraMatrixState, self._gizmoMatrixState)\n        })\n      } else if (hitP && !this.enableAnimations) {\n        this.updateTbState(STATE.FOCUS, true)\n        this.focus(hitP, this.scaleFactor)\n        this.updateTbState(STATE.IDLE, false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoublePanStart = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.PAN, true)\n\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) {\n        this._startCursorPosition.copy(rayDir)\n      }\n      this._currentCursorPosition.copy(this._startCursorPosition)\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onDoublePanMove = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      if (this._state != STATE.PAN) {\n        this.updateTbState(STATE.PAN, true)\n        this._startCursorPosition.copy(this._currentCursorPosition)\n      }\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) this._currentCursorPosition.copy(rayDir)\n      this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition, true))\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onDoublePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onRotateStart = (): void => {\n    if (this.enabled && this.enableRotate) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.ZROTATE, true)\n\n      //this._startFingerRotation = event.rotation;\n\n      this._startFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n      this._currentFingerRotation = this._startFingerRotation\n\n      this.camera?.getWorldDirection(this._rotationAxis) //rotation axis\n\n      if (!this.enablePan && !this.enableZoom) {\n        this.activateGizmos(true)\n      }\n    }\n  }\n\n  private onRotateMove = (): void => {\n    if (this.enabled && this.enableRotate && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      let rotationPoint\n\n      if (this._state != STATE.ZROTATE) {\n        this.updateTbState(STATE.ZROTATE, true)\n        this._startFingerRotation = this._currentFingerRotation\n      }\n\n      //this._currentFingerRotation = event.rotation;\n      this._currentFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n\n      if (!this.enablePan) {\n        rotationPoint = new Vector3().setFromMatrixPosition(this._gizmoMatrixState)\n      } else if (this.camera) {\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n        rotationPoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n          ?.applyQuaternion(this.camera.quaternion)\n          .multiplyScalar(1 / this.camera.zoom)\n          .add(this._v3_2)\n      }\n\n      const amount = MathUtils.DEG2RAD * (this._startFingerRotation - this._currentFingerRotation)\n\n      if (rotationPoint !== undefined) {\n        this.applyTransformMatrix(this.zRotate(rotationPoint, amount))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onRotateEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    this.activateGizmos(false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onPinchStart = (): void => {\n    if (this.enabled && this.enableZoom) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n      this.updateTbState(STATE.SCALE, true)\n\n      this._startFingerDistance = this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1])\n      this._currentFingerDistance = this._startFingerDistance\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onPinchMove = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      const minDistance = 12 //minimum distance between fingers (in css pixels)\n\n      if (this._state != STATE.SCALE) {\n        this._startFingerDistance = this._currentFingerDistance\n        this.updateTbState(STATE.SCALE, true)\n      }\n\n      this._currentFingerDistance = Math.max(\n        this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1]),\n        minDistance * this._devPxRatio,\n      )\n      const amount = this._currentFingerDistance / this._startFingerDistance\n\n      let scalePoint\n\n      if (!this.enablePan) {\n        scalePoint = this._gizmos.position\n      } else {\n        if (this.camera instanceof OrthographicCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .multiplyScalar(1 / this.camera.zoom)\n            .add(this._gizmos.position)\n        } else if (this.camera instanceof PerspectiveCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .add(this._gizmos.position)\n        }\n      }\n\n      if (scalePoint !== undefined) {\n        this.applyTransformMatrix(this.applyScale(amount, scalePoint))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onPinchEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onTriplePanStart = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.SCALE, true)\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n      this._currentCursorPosition.copy(this._startCursorPosition)\n    }\n  }\n\n  private onTriplePanMove = (): void => {\n    if (this.enabled && this.enableZoom && this.camera && this.domElement) {\n      //\t  fov / 2\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\tx\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t\t| _ _ _\\\n      //\t\t\ty\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n      this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n      const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n      let size = 1\n\n      if (movement < 0) {\n        size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n      } else if (movement > 0) {\n        size = Math.pow(this.scaleFactor, movement * screenNotches)\n      }\n\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      const x = this._v3_1.distanceTo(this._gizmos.position)\n      let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n      //check min and max distance\n      xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n      const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n      //calculate new fov\n      let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n      //check min and max fov\n      newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n      const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n      size = x / newDistance\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      this.setFov(newFov)\n      this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n      //adjusting distance\n      const direction = this._gizmos.position\n        .clone()\n        .sub(this.camera.position)\n        .normalize()\n        .multiplyScalar(newDistance / x)\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onTriplePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n    //this.dispatchEvent( _changeEvent );\n  }\n\n  /**\n   * Set _center's x/y coordinates\n   * @param {Number} clientX\n   * @param {Number} clientY\n   */\n  private setCenter = (clientX: number, clientY: number): void => {\n    _center.x = clientX\n    _center.y = clientY\n  }\n\n  /**\n   * Set default mouse actions\n   */\n  private initializeMouseActions = (): void => {\n    this.setMouseAction('PAN', 0, 'CTRL')\n    this.setMouseAction('PAN', 2)\n\n    this.setMouseAction('ROTATE', 0)\n\n    this.setMouseAction('ZOOM', 'WHEEL')\n    this.setMouseAction('ZOOM', 1)\n\n    this.setMouseAction('FOV', 'WHEEL', 'SHIFT')\n    this.setMouseAction('FOV', 1, 'SHIFT')\n  }\n\n  /**\n   * Set a new mouse action by specifying the operation to be performed and a mouse/key combination. In case of conflict, replaces the existing one\n   * @param {String} operation The operation to be performed ('PAN', 'ROTATE', 'ZOOM', 'FOV)\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns {Boolean} True if the mouse action has been successfully added, false otherwise\n   */\n  private setMouseAction = (operation: Operation, mouse: MouseButtonType, key: ModifierKey | null = null): boolean => {\n    const operationInput = ['PAN', 'ROTATE', 'ZOOM', 'FOV']\n    const mouseInput = [0, 1, 2, 'WHEEL']\n    const keyInput = ['CTRL', 'SHIFT', null]\n    let state\n\n    if (!operationInput.includes(operation) || !mouseInput.includes(mouse) || !keyInput.includes(key)) {\n      //invalid parameters\n      return false\n    }\n\n    if (mouse == 'WHEEL') {\n      if (operation != 'ZOOM' && operation != 'FOV') {\n        //cannot associate 2D operation to 1D input\n        return false\n      }\n    }\n\n    switch (operation) {\n      case 'PAN':\n        state = STATE.PAN\n        break\n\n      case 'ROTATE':\n        state = STATE.ROTATE\n        break\n\n      case 'ZOOM':\n        state = STATE.SCALE\n        break\n\n      case 'FOV':\n        state = STATE.FOV\n        break\n    }\n\n    const action = {\n      operation: operation,\n      mouse: mouse,\n      key: key,\n      state: state,\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == action.mouse && this.mouseActions[i].key == action.key) {\n        this.mouseActions.splice(i, 1, action)\n        return true\n      }\n    }\n\n    this.mouseActions.push(action)\n    return true\n  }\n\n  /**\n   * Return the operation associated to a mouse/keyboard combination\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns The operation if it has been found, null otherwise\n   */\n  private getOpFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Operation | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.operation\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.operation\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Get the operation associated to mouse and key combination and returns the corresponding FSA state\n   * @param {Number} mouse Mouse button\n   * @param {String} key Keyboard modifier\n   * @returns The FSA state obtained from the operation associated to mouse/keyboard combination\n   */\n  private getOpStateFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Symbol | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.state\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.state\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Calculate the angle between two pointers\n   * @param {PointerEvent} p1\n   * @param {PointerEvent} p2\n   * @returns {Number} The angle between two pointers in degrees\n   */\n  private getAngle = (p1: PointerEvent, p2: PointerEvent): number => {\n    return (Math.atan2(p2.clientY - p1.clientY, p2.clientX - p1.clientX) * 180) / Math.PI\n  }\n\n  /**\n   * Update a PointerEvent inside current pointerevents array\n   * @param {PointerEvent} event\n   */\n  private updateTouchEvent = (event: PointerEvent): void => {\n    for (let i = 0; i < this._touchCurrent.length; i++) {\n      if (this._touchCurrent[i].pointerId == event.pointerId) {\n        this._touchCurrent.splice(i, 1, event)\n        break\n      }\n    }\n  }\n\n  /**\n   * Apply a transformation matrix, to the camera and gizmos\n   * @param {Object} transformation Object containing matrices to apply to camera and gizmos\n   */\n  private applyTransformMatrix(transformation: Transformation | undefined): void {\n    if (transformation?.camera && this.camera) {\n      this._m4_1.copy(this._cameraMatrixState).premultiply(transformation.camera)\n      this._m4_1.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.updateMatrix()\n\n      //update camera up vector\n      if (this._state == STATE.ROTATE || this._state == STATE.ZROTATE || this._state == STATE.ANIMATION_ROTATE) {\n        this.camera.up.copy(this._upState).applyQuaternion(this.camera.quaternion)\n      }\n    }\n\n    if (transformation?.gizmos) {\n      this._m4_1.copy(this._gizmoMatrixState).premultiply(transformation.gizmos)\n      this._m4_1.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n    }\n\n    if (\n      (this._state == STATE.SCALE || this._state == STATE.FOCUS || this._state == STATE.ANIMATION_FOCUS) &&\n      this.camera\n    ) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (this.adjustNearFar) {\n        const cameraDistance = this.camera.position.distanceTo(this._gizmos.position)\n\n        const bb = new Box3()\n        bb.setFromObject(this._gizmos)\n        const sphere = new Sphere()\n        bb.getBoundingSphere(sphere)\n\n        const adjustedNearPosition = Math.max(this._nearPos0, sphere.radius + sphere.center.length())\n        const regularNearPosition = cameraDistance - this._initialNear\n\n        const minNearPos = Math.min(adjustedNearPosition, regularNearPosition)\n        this.camera.near = cameraDistance - minNearPos\n\n        const adjustedFarPosition = Math.min(this._farPos0, -sphere.radius + sphere.center.length())\n        const regularFarPosition = cameraDistance - this._initialFar\n\n        const minFarPos = Math.min(adjustedFarPosition, regularFarPosition)\n        this.camera.far = cameraDistance - minFarPos\n\n        this.camera.updateProjectionMatrix()\n      } else {\n        let update = false\n\n        if (this.camera.near != this._initialNear) {\n          this.camera.near = this._initialNear\n          update = true\n        }\n\n        if (this.camera.far != this._initialFar) {\n          this.camera.far = this._initialFar\n          update = true\n        }\n\n        if (update) {\n          this.camera.updateProjectionMatrix()\n        }\n      }\n    }\n  }\n\n  /**\n   * Calculate the angular speed\n   * @param {Number} p0 Position at t0\n   * @param {Number} p1 Position at t1\n   * @param {Number} t0 Initial time in milliseconds\n   * @param {Number} t1 Ending time in milliseconds\n   */\n  private calculateAngularSpeed = (p0: number, p1: number, t0: number, t1: number): number => {\n    const s = p1 - p0\n    const t = (t1 - t0) / 1000\n    if (t == 0) {\n      return 0\n    }\n\n    return s / t\n  }\n\n  /**\n   * Calculate the distance between two pointers\n   * @param {PointerEvent} p0 The first pointer\n   * @param {PointerEvent} p1 The second pointer\n   * @returns {number} The distance between the two pointers\n   */\n  private calculatePointersDistance = (p0: PointerEvent, p1: PointerEvent): number => {\n    return Math.sqrt(Math.pow(p1.clientX - p0.clientX, 2) + Math.pow(p1.clientY - p0.clientY, 2))\n  }\n\n  /**\n   * Calculate the rotation axis as the vector perpendicular between two vectors\n   * @param {Vector3} vec1 The first vector\n   * @param {Vector3} vec2 The second vector\n   * @returns {Vector3} The normalized rotation axis\n   */\n  private calculateRotationAxis = (vec1: Vector3, vec2: Vector3): Vector3 => {\n    this._rotationMatrix.extractRotation(this._cameraMatrixState)\n    this._quat.setFromRotationMatrix(this._rotationMatrix)\n\n    this._rotationAxis.crossVectors(vec1, vec2).applyQuaternion(this._quat)\n    return this._rotationAxis.normalize().clone()\n  }\n\n  /**\n   * Calculate the trackball radius so that gizmo's diamater will be 2/3 of the minimum side of the camera frustum\n   * @param {Camera} camera\n   * @returns {Number} The trackball radius\n   */\n  private calculateTbRadius = (camera: Camera): number | undefined => {\n    const factor = 0.67\n    const distance = camera.position.distanceTo(this._gizmos.position)\n\n    if (camera instanceof PerspectiveCamera) {\n      const halfFovV = MathUtils.DEG2RAD * camera.fov * 0.5 //vertical fov/2 in radians\n      const halfFovH = Math.atan(camera.aspect * Math.tan(halfFovV)) //horizontal fov/2 in radians\n      return Math.tan(Math.min(halfFovV, halfFovH)) * distance * factor\n    } else if (camera instanceof OrthographicCamera) {\n      return Math.min(camera.top, camera.right) * factor\n    }\n  }\n\n  /**\n   * Focus operation consist of positioning the point of interest in front of the camera and a slightly zoom in\n   * @param {Vector3} point The point of interest\n   * @param {Number} size Scale factor\n   * @param {Number} amount Amount of operation to be completed (used for focus animations, default is complete full operation)\n   */\n  private focus = (point: Vector3, size: number, amount = 1): void => {\n    if (this.camera) {\n      const focusPoint = point.clone()\n\n      //move center of camera (along with gizmos) towards point of interest\n      focusPoint.sub(this._gizmos.position).multiplyScalar(amount)\n      this._translationMatrix.makeTranslation(focusPoint.x, focusPoint.y, focusPoint.z)\n\n      const gizmoStateTemp = this._gizmoMatrixState.clone()\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      const cameraStateTemp = this._cameraMatrixState.clone()\n      this._cameraMatrixState.premultiply(this._translationMatrix)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      //apply zoom\n      if (this.enableZoom) {\n        this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n      }\n\n      this._gizmoMatrixState.copy(gizmoStateTemp)\n      this._cameraMatrixState.copy(cameraStateTemp)\n    }\n  }\n\n  /**\n   * Draw a grid and add it to the scene\n   */\n  private drawGrid = (): void => {\n    if (this.scene) {\n      const color = 0x888888\n      const multiplier = 3\n      let size, divisions, maxLength, tick\n\n      if (this.camera instanceof OrthographicCamera) {\n        const width = this.camera.right - this.camera.left\n        const height = this.camera.bottom - this.camera.top\n\n        maxLength = Math.max(width, height)\n        tick = maxLength / 20\n\n        size = (maxLength / this.camera.zoom) * multiplier\n        divisions = (size / tick) * this.camera.zoom\n      } else if (this.camera instanceof PerspectiveCamera) {\n        const distance = this.camera.position.distanceTo(this._gizmos.position)\n        const halfFovV = MathUtils.DEG2RAD * this.camera.fov * 0.5\n        const halfFovH = Math.atan(this.camera.aspect * Math.tan(halfFovV))\n\n        maxLength = Math.tan(Math.max(halfFovV, halfFovH)) * distance * 2\n        tick = maxLength / 20\n\n        size = maxLength * multiplier\n        divisions = size / tick\n      }\n\n      if (this._grid == null && this.camera) {\n        this._grid = new GridHelper(size, divisions, color, color)\n        this._grid.position.copy(this._gizmos.position)\n        this._gridPosition.copy(this._grid.position)\n        this._grid.quaternion.copy(this.camera.quaternion)\n        this._grid.rotateX(Math.PI * 0.5)\n\n        this.scene.add(this._grid)\n      }\n    }\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    // https://github.com/mrdoob/three.js/issues/20575\n\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.ArcballControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    // disables touch scroll\n    // touch-action needs to be defined for pointer events to work on mobile\n    // https://stackoverflow.com/a/48254578\n    this.domElement.style.touchAction = 'none'\n    this.domElement.addEventListener('contextmenu', this.onContextMenu)\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointercancel', this.onPointerCancel)\n    this.domElement.addEventListener('wheel', this.onWheel)\n  }\n\n  /**\n   * Remove all listeners, stop animations and clean scene\n   */\n  public dispose = (): void => {\n    if (this._animationId != -1) {\n      window.cancelAnimationFrame(this._animationId)\n    }\n\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointercancel', this.onPointerCancel)\n    this.domElement?.removeEventListener('wheel', this.onWheel)\n    this.domElement?.removeEventListener('contextmenu', this.onContextMenu)\n\n    window.removeEventListener('pointermove', this.onPointerMove)\n    window.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('resize', this.onWindowResize)\n\n    this.scene?.remove(this._gizmos)\n    this.disposeGrid()\n  }\n\n  /**\n   * remove the grid from the scene\n   */\n  private disposeGrid = (): void => {\n    if (this._grid && this.scene) {\n      this.scene.remove(this._grid)\n      this._grid = null\n    }\n  }\n\n  /**\n   * Compute the easing out cubic function for ease out effect in animation\n   * @param {Number} t The absolute progress of the animation in the bound of 0 (beginning of the) and 1 (ending of animation)\n   * @returns {Number} Result of easing out cubic at time t\n   */\n  private easeOutCubic = (t: number): number => {\n    return 1 - Math.pow(1 - t, 3)\n  }\n\n  /**\n   * Make rotation gizmos more or less visible\n   * @param {Boolean} isActive If true, make gizmos more visible\n   */\n  private activateGizmos = (isActive: boolean): void => {\n    for (const gizmo of this._gizmos.children) {\n      ;(gizmo as Mesh<BufferGeometry, Material>).material.setValues({ opacity: isActive ? 1 : 0.6 })\n    }\n  }\n\n  /**\n   * Calculate the cursor position in NDC\n   * @param {number} x Cursor horizontal coordinate within the canvas\n   * @param {number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor normalized position inside the canvas\n   */\n  private getCursorNDC = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    const canvasRect = canvas.getBoundingClientRect()\n    this._v2_1.setX(((cursorX - canvasRect.left) / canvasRect.width) * 2 - 1)\n    this._v2_1.setY(((canvasRect.bottom - cursorY) / canvasRect.height) * 2 - 1)\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Calculate the cursor position inside the canvas x/y coordinates with the origin being in the center of the canvas\n   * @param {Number} x Cursor horizontal coordinate within the canvas\n   * @param {Number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor position inside the canvas\n   */\n  private getCursorPosition = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n    if (this.camera instanceof OrthographicCamera) {\n      this._v2_1.x *= (this.camera.right - this.camera.left) * 0.5\n      this._v2_1.y *= (this.camera.top - this.camera.bottom) * 0.5\n    }\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Set the camera to be controlled\n   * @param {Camera} camera The virtual camera to be controlled\n   */\n  private setCamera = (camera: Camera | null): void => {\n    if (camera) {\n      camera.lookAt(this.target)\n      camera.updateMatrix()\n\n      //setting state\n      if (camera instanceof PerspectiveCamera) {\n        this._fov0 = camera.fov\n        this._fovState = camera.fov\n      }\n\n      this._cameraMatrixState0.copy(camera.matrix)\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraProjectionState.copy(camera.projectionMatrix)\n      this._zoom0 = camera.zoom\n      this._zoomState = this._zoom0\n\n      this._initialNear = camera.near\n      this._nearPos0 = camera.position.distanceTo(this.target) - camera.near\n      this._nearPos = this._initialNear\n\n      this._initialFar = camera.far\n      this._farPos0 = camera.position.distanceTo(this.target) - camera.far\n      this._farPos = this._initialFar\n\n      this._up0.copy(camera.up)\n      this._upState.copy(camera.up)\n\n      this.camera = camera\n\n      this.camera.updateProjectionMatrix()\n\n      //making gizmos\n      const tbRadius = this.calculateTbRadius(camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n    }\n  }\n\n  /**\n   * Set gizmos visibility\n   * @param {Boolean} value Value of gizmos visibility\n   */\n  public setGizmosVisible(value: boolean): void {\n    this._gizmos.visible = value\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  /**\n   * Creates the rotation gizmos matching trackball center and radius\n   * @param {Vector3} tbCenter The trackball center\n   * @param {number} tbRadius The trackball radius\n   */\n  private makeGizmos = (tbCenter: Vector3, tbRadius: number): void => {\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, tbRadius, tbRadius)\n    const points = curve.getPoints(this._curvePts)\n\n    //geometry\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    //material\n    const curveMaterialX = new LineBasicMaterial({ color: 0xff8080, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialY = new LineBasicMaterial({ color: 0x80ff80, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialZ = new LineBasicMaterial({ color: 0x8080ff, fog: false, transparent: true, opacity: 0.6 })\n\n    //line\n    const gizmoX = new Line(curveGeometry, curveMaterialX)\n    const gizmoY = new Line(curveGeometry, curveMaterialY)\n    const gizmoZ = new Line(curveGeometry, curveMaterialZ)\n\n    const rotation = Math.PI * 0.5\n    gizmoX.rotation.x = rotation\n    gizmoY.rotation.y = rotation\n\n    //setting state\n    this._gizmoMatrixState0.identity().setPosition(tbCenter)\n    this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n\n    if (this.camera && this.camera.zoom != 1) {\n      //adapt gizmos size to camera zoom\n      const size = 1 / this.camera.zoom\n      this._scaleMatrix.makeScale(size, size, size)\n      this._translationMatrix.makeTranslation(-tbCenter.x, -tbCenter.y, -tbCenter.z)\n\n      this._gizmoMatrixState.premultiply(this._translationMatrix).premultiply(this._scaleMatrix)\n      this._translationMatrix.makeTranslation(tbCenter.x, tbCenter.y, tbCenter.z)\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n    }\n\n    this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n    this._gizmos.clear()\n\n    this._gizmos.add(gizmoX)\n    this._gizmos.add(gizmoY)\n    this._gizmos.add(gizmoZ)\n  }\n\n  /**\n   * Perform animation for focus operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} point Point of interest for focus operation\n   * @param {Matrix4} cameraMatrix Camera matrix\n   * @param {Matrix4} gizmoMatrix Gizmos matrix\n   */\n  private onFocusAnim = (time: number, point: Vector3, cameraMatrix: Matrix4, gizmoMatrix: Matrix4): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_FOCUS) {\n      const deltaTime = time - this._timeStart\n      const animTime = deltaTime / this.focusAnimationTime\n\n      this._gizmoMatrixState.copy(gizmoMatrix)\n\n      if (animTime >= 1) {\n        //animation end\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n        this.focus(point, this.scaleFactor)\n\n        this._timeStart = -1\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      } else {\n        const amount = this.easeOutCubic(animTime)\n        const size = 1 - amount + this.scaleFactor * amount\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n        this.focus(point, size, amount)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onFocusAnim(t, point, cameraMatrix, gizmoMatrix.clone())\n        })\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n    }\n  }\n\n  /**\n   * Perform animation for rotation operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} rotationAxis Rotation axis\n   * @param {number} w0 Initial angular velocity\n   */\n  private onRotationAnim = (time: number, rotationAxis: Vector3, w0: number): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._anglePrev = 0\n      this._angleCurrent = 0\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_ROTATE) {\n      //w = w0 + alpha * t\n      const deltaTime = (time - this._timeStart) / 1000\n      const w = w0 + -this.dampingFactor * deltaTime\n\n      if (w > 0) {\n        //tetha = 0.5 * alpha * t^2 + w0 * t + tetha0\n        this._angleCurrent = 0.5 * -this.dampingFactor * Math.pow(deltaTime, 2) + w0 * deltaTime + 0\n        this.applyTransformMatrix(this.rotate(rotationAxis, this._angleCurrent))\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onRotationAnim(t, rotationAxis, w0)\n        })\n      } else {\n        this._animationId = -1\n        this._timeStart = -1\n\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n\n      if (this._state != STATE.ROTATE) {\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n  }\n\n  /**\n   * Perform pan operation moving camera between two points\n   * @param {Vector3} p0 Initial point\n   * @param {Vector3} p1 Ending point\n   * @param {Boolean} adjust If movement should be adjusted considering camera distance (Perspective only)\n   */\n  private pan = (p0: Vector3, p1: Vector3, adjust = false): Transformation => {\n    if (this.camera) {\n      const movement = p0.clone().sub(p1)\n\n      if (this.camera instanceof OrthographicCamera) {\n        //adjust movement amount\n        movement.multiplyScalar(1 / this.camera.zoom)\n      }\n\n      if (this.camera instanceof PerspectiveCamera && adjust) {\n        //adjust movement amount\n        this._v3_1.setFromMatrixPosition(this._cameraMatrixState0) //camera's initial position\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0) //gizmo's initial position\n        const distanceFactor =\n          this._v3_1.distanceTo(this._v3_2) / this.camera.position.distanceTo(this._gizmos.position)\n        movement.multiplyScalar(1 / distanceFactor)\n      }\n\n      this._v3_1.set(movement.x, movement.y, 0).applyQuaternion(this.camera.quaternion)\n\n      this._m4_1.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_1)\n    }\n    return _transformation\n  }\n\n  /**\n   * Reset trackball\n   */\n  public reset = (): void => {\n    if (this.camera) {\n      this.camera.zoom = this._zoom0\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = this._fov0\n      }\n\n      this.camera.near = this._nearPos\n      this.camera.far = this._farPos\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.up.copy(this._up0)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n      this._gizmoMatrixState0.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n\n      this.camera.lookAt(this._gizmos.position)\n\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  /**\n   * Rotate the camera around an axis passing by trackball's center\n   * @param {Vector3} axis Rotation axis\n   * @param {number} angle Angle in radians\n   * @returns {Object} Object with 'camera' field containing transformation matrix resulting from the operation to be applied to the camera\n   */\n  private rotate = (axis: Vector3, angle: number): Transformation => {\n    const point = this._gizmos.position //rotation center\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n    this._rotationMatrix.makeRotationAxis(axis, -angle)\n\n    //rotate camera\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this.setTransformationMatrices(this._m4_1)\n\n    return _transformation\n  }\n\n  public copyState = (): void => {\n    if (this.camera) {\n      const state = JSON.stringify(\n        this.camera instanceof OrthographicCamera\n          ? {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            }\n          : {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraFov: this.camera.fov,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            },\n      )\n\n      navigator.clipboard.writeText(state)\n    }\n  }\n\n  public pasteState = (): void => {\n    const self = this\n    navigator.clipboard.readText().then(function resolved(value) {\n      self.setStateFromJSON(value)\n    })\n  }\n\n  /**\n   * Save the current state of the control. This can later be recovered with .reset\n   */\n  public saveState = (): void => {\n    if (!this.camera) return\n\n    this._cameraMatrixState0.copy(this.camera.matrix)\n    this._gizmoMatrixState0.copy(this._gizmos.matrix)\n    this._nearPos = this.camera.near\n    this._farPos = this.camera.far\n    this._zoom0 = this.camera.zoom\n    this._up0.copy(this.camera.up)\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fov0 = this.camera.fov\n    }\n  }\n\n  /**\n   * Perform uniform scale operation around a given point\n   * @param {Number} size Scale factor\n   * @param {Vector3} point Point around which scale\n   * @param {Boolean} scaleGizmos If gizmos should be scaled (Perspective only)\n   * @returns {Object} Object with 'camera' and 'gizmo' fields containing transformation matrices resulting from the operation to be applied to the camera and gizmos\n   */\n  private applyScale = (size: number, point: Vector3, scaleGizmos = true): Transformation | undefined => {\n    if (!this.camera) return\n\n    const scalePoint = point.clone()\n    let sizeInverse = 1 / size\n\n    if (this.camera instanceof OrthographicCamera) {\n      //camera zoom\n      this.camera.zoom = this._zoomState\n      this.camera.zoom *= size\n\n      //check min and max zoom\n      if (this.camera.zoom > this.maxZoom) {\n        this.camera.zoom = this.maxZoom\n        sizeInverse = this._zoomState / this.maxZoom\n      } else if (this.camera.zoom < this.minZoom) {\n        this.camera.zoom = this.minZoom\n        sizeInverse = this._zoomState / this.minZoom\n      }\n\n      this.camera.updateProjectionMatrix()\n\n      this._v3_1.setFromMatrixPosition(this._gizmoMatrixState) //gizmos position\n\n      //scale gizmos so they appear in the same spot having the same dimension\n      this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n      this._translationMatrix.makeTranslation(-this._v3_1.x, -this._v3_1.y, -this._v3_1.z)\n\n      this._m4_2.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z).multiply(this._scaleMatrix)\n      this._m4_2.multiply(this._translationMatrix)\n\n      //move camera and gizmos to obtain pinch effect\n      scalePoint.sub(this._v3_1)\n\n      const amount = scalePoint.clone().multiplyScalar(sizeInverse)\n      scalePoint.sub(amount)\n\n      this._m4_1.makeTranslation(scalePoint.x, scalePoint.y, scalePoint.z)\n      this._m4_2.premultiply(this._m4_1)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_2)\n      return _transformation\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      //move camera\n      let distance = this._v3_1.distanceTo(scalePoint)\n      let amount = distance - distance * sizeInverse\n\n      //check min and max distance\n      const newDistance = distance - amount\n      if (newDistance < this.minDistance) {\n        sizeInverse = this.minDistance / distance\n        amount = distance - distance * sizeInverse\n      } else if (newDistance > this.maxDistance) {\n        sizeInverse = this.maxDistance / distance\n        amount = distance - distance * sizeInverse\n      }\n\n      let direction = scalePoint.clone().sub(this._v3_1).normalize().multiplyScalar(amount)\n\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      if (scaleGizmos) {\n        //scale gizmos so they appear in the same spot having the same dimension\n        const pos = this._v3_2\n\n        distance = pos.distanceTo(scalePoint)\n        amount = distance - distance * sizeInverse\n        direction = scalePoint.clone().sub(this._v3_2).normalize().multiplyScalar(amount)\n\n        this._translationMatrix.makeTranslation(pos.x, pos.y, pos.z)\n        this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n\n        this._m4_2.makeTranslation(direction.x, direction.y, direction.z).multiply(this._translationMatrix)\n        this._m4_2.multiply(this._scaleMatrix)\n\n        this._translationMatrix.makeTranslation(-pos.x, -pos.y, -pos.z)\n\n        this._m4_2.multiply(this._translationMatrix)\n        this.setTransformationMatrices(this._m4_1, this._m4_2)\n      } else {\n        this.setTransformationMatrices(this._m4_1)\n      }\n\n      return _transformation\n    }\n  }\n\n  /**\n   * Set camera fov\n   * @param {Number} value fov to be setted\n   */\n  private setFov = (value: number): void => {\n    if (this.camera instanceof PerspectiveCamera) {\n      this.camera.fov = MathUtils.clamp(value, this.minFov, this.maxFov)\n      this.camera.updateProjectionMatrix()\n    }\n  }\n\n  /**\n   * Set the trackball's center point\n   * @param {Number} x X coordinate\n   * @param {Number} y Y coordinate\n   * @param {Number} z Z coordinate\n   */\n  public setTarget = (x: number, y: number, z: number): void => {\n    if (this.camera) {\n      this.target.set(x, y, z)\n      this._gizmos.position.set(x, y, z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this.camera.lookAt(this.target)\n    }\n  }\n\n  /**\n   * Set values in transformation object\n   * @param {Matrix4} camera Transformation to be applied to the camera\n   * @param {Matrix4} gizmos Transformation to be applied to gizmos\n   */\n  private setTransformationMatrices(camera: Matrix4 | null = null, gizmos: Matrix4 | null = null): void {\n    if (camera) {\n      if (_transformation.camera) {\n        _transformation.camera.copy(camera)\n      } else {\n        _transformation.camera = camera.clone()\n      }\n    } else {\n      _transformation.camera = null\n    }\n\n    if (gizmos) {\n      if (_transformation.gizmos) {\n        _transformation.gizmos.copy(gizmos)\n      } else {\n        _transformation.gizmos = gizmos.clone()\n      }\n    } else {\n      _transformation.gizmos = null\n    }\n  }\n\n  /**\n   * Rotate camera around its direction axis passing by a given point by a given angle\n   * @param {Vector3} point The point where the rotation axis is passing trough\n   * @param {Number} angle Angle in radians\n   * @returns The computed transormation matix\n   */\n  private zRotate = (point: Vector3, angle: number): Transformation => {\n    this._rotationMatrix.makeRotationAxis(this._rotationAxis, angle)\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this._v3_1.setFromMatrixPosition(this._gizmoMatrixState).sub(point) //vector from rotation center to gizmos position\n    this._v3_2.copy(this._v3_1).applyAxisAngle(this._rotationAxis, angle) //apply rotation\n    this._v3_2.sub(this._v3_1)\n\n    this._m4_2.makeTranslation(this._v3_2.x, this._v3_2.y, this._v3_2.z)\n\n    this.setTransformationMatrices(this._m4_1, this._m4_2)\n    return _transformation\n  }\n\n  /**\n   * Unproject the cursor on the 3D object surface\n   * @param {Vector2} cursor Cursor coordinates in NDC\n   * @param {Camera} camera Virtual camera\n   * @returns {Vector3} The point of intersection with the model, if exist, null otherwise\n   */\n  private unprojectOnObj = (cursor: Vector2, camera: Camera): Vector3 | null => {\n    if (!this.scene) return null\n\n    const raycaster = new Raycaster()\n    raycaster.near = camera.near\n    raycaster.far = camera.far\n    raycaster.setFromCamera(cursor, camera)\n\n    const intersect = raycaster.intersectObjects(this.scene.children, true)\n    for (let i = 0; i < intersect.length; i++) {\n      if (intersect[i].object.uuid != this._gizmos.uuid && intersect[i].face) {\n        return intersect[i].point.clone()\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Unproject the cursor on the trackball surface\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {number} tbRadius The trackball radius\n   * @returns {Vector3} The unprojected point on the trackball surface\n   */\n  private unprojectOnTbSurface = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    tbRadius: number,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      const x2 = Math.pow(this._v2_1.x, 2)\n      const y2 = Math.pow(this._v2_1.y, 2)\n      const r2 = Math.pow(this._tbRadius, 2)\n\n      if (x2 + y2 <= r2 * 0.5) {\n        //intersection with sphere\n        this._v3_1.setZ(Math.sqrt(r2 - (x2 + y2)))\n      } else {\n        //intersection with hyperboloid\n        this._v3_1.setZ((r2 * 0.5) / Math.sqrt(x2 + y2))\n      }\n\n      return this._v3_1\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      //unproject cursor on the near plane\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n      const cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      const radius2 = Math.pow(tbRadius, 2)\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(this._v3_1.x, this._v3_1.y, tbRadius)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n\n      /*\n       * calculate intersection point between unprojected ray and trackball surface\n       *|y = m * x + q\n       *|x^2 + y^2 = r^2\n       *\n       * (m^2 + 1) * x^2 + (2 * m * q) * x + q^2 - r^2 = 0\n       */\n      let a = Math.pow(m, 2) + 1\n      let b = 2 * m * q\n      let c = Math.pow(q, 2) - radius2\n      let delta = Math.pow(b, 2) - 4 * a * c\n\n      if (delta >= 0) {\n        //intersection with sphere\n        this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n        this._v2_1.setY(m * this._v2_1.x + q)\n\n        const angle = MathUtils.RAD2DEG * this._v2_1.angle()\n\n        if (angle >= 45) {\n          //if angle between intersection point and X' axis is >= 45°, return that point\n          //otherwise, calculate intersection point with hyperboloid\n\n          const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n          rayDir.multiplyScalar(rayLength)\n          rayDir.z += cameraGizmoDistance\n          return rayDir\n        }\n      }\n\n      //intersection with hyperboloid\n      /*\n       *|y = m * x + q\n       *|y = (1 / x) * (r^2 / 2)\n       *\n       * m * x^2 + q * x - r^2 / 2 = 0\n       */\n\n      a = m\n      b = q\n      c = -radius2 * 0.5\n      delta = Math.pow(b, 2) - 4 * a * c\n      this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n      this._v2_1.setY(m * this._v2_1.x + q)\n\n      const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z += cameraGizmoDistance\n      return rayDir\n    }\n  }\n\n  /**\n   * Unproject the cursor on the plane passing through the center of the trackball orthogonal to the camera\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {Boolean} initialDistance If initial distance between camera and gizmos should be used for calculations instead of current (Perspective only)\n   * @returns {Vector3} The unprojected point on the trackball plane\n   */\n  private unprojectOnTbPlane = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    initialDistance = false,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      return this._v3_1.clone()\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      //unproject cursor on the near plane\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n      let cameraGizmoDistance\n\n      if (initialDistance) {\n        cameraGizmoDistance = this._v3_1\n          .setFromMatrixPosition(this._cameraMatrixState0)\n          .distanceTo(this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0))\n      } else {\n        cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      }\n\n      /*\n       * calculate intersection point between unprojected ray and the plane\n       *|y = mx + q\n       *|y = 0\n       *\n       * x = -q/m\n       */\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(0, 0, 0)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n      const x = -q / m\n\n      const rayLength = Math.sqrt(Math.pow(q, 2) + Math.pow(x, 2))\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z = 0\n      return rayDir\n    }\n  }\n\n  /**\n   * Update camera and gizmos state\n   */\n  private updateMatrixState = (): void => {\n    if (!this.camera) return\n\n    //update camera and gizmos state\n    this._cameraMatrixState.copy(this.camera.matrix)\n    this._gizmoMatrixState.copy(this._gizmos.matrix)\n\n    if (this.camera instanceof OrthographicCamera) {\n      this._cameraProjectionState.copy(this.camera.projectionMatrix)\n      this.camera.updateProjectionMatrix()\n      this._zoomState = this.camera.zoom\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fovState = this.camera.fov\n    }\n  }\n\n  /**\n   * Update the trackball FSA\n   * @param {STATE} newState New state of the FSA\n   * @param {Boolean} updateMatrices If matriices state should be updated\n   */\n  private updateTbState = (newState: Symbol, updateMatrices: boolean): void => {\n    this._state = newState\n    if (updateMatrices) {\n      this.updateMatrixState()\n    }\n  }\n\n  public update = (): void => {\n    const EPS = 0.000001\n\n    // Update target and gizmos state\n    if (!this.target.equals(this._currentTarget) && this.camera) {\n      this._gizmos.position.set(this.target.x, this.target.y, this.target.z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this._currentTarget.copy(this.target)\n    }\n\n    if (!this.camera) return\n\n    //check min/max parameters\n    if (this.camera instanceof OrthographicCamera) {\n      //check zoom\n      if (this.camera.zoom > this.maxZoom || this.camera.zoom < this.minZoom) {\n        const newZoom = MathUtils.clamp(this.camera.zoom, this.minZoom, this.maxZoom)\n        this.applyTransformMatrix(this.applyScale(newZoom / this.camera.zoom, this._gizmos.position, true))\n      }\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      //check distance\n      const distance = this.camera.position.distanceTo(this._gizmos.position)\n\n      if (distance > this.maxDistance + EPS || distance < this.minDistance - EPS) {\n        const newDistance = MathUtils.clamp(distance, this.minDistance, this.maxDistance)\n        this.applyTransformMatrix(this.applyScale(newDistance / distance, this._gizmos.position))\n        this.updateMatrixState()\n      }\n\n      //check fov\n      if (this.camera.fov < this.minFov || this.camera.fov > this.maxFov) {\n        this.camera.fov = MathUtils.clamp(this.camera.fov, this.minFov, this.maxFov)\n        this.camera.updateProjectionMatrix()\n      }\n\n      const oldRadius = this._tbRadius\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (oldRadius < this._tbRadius - EPS || oldRadius > this._tbRadius + EPS) {\n        const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n        const newRadius = this._tbRadius / scale\n        // @ts-ignore\n        const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n        const points = curve.getPoints(this._curvePts)\n        const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n        for (const gizmo in this._gizmos.children) {\n          const child = this._gizmos.children[gizmo] as Mesh\n          child.geometry = curveGeometry\n        }\n      }\n    }\n\n    this.camera.lookAt(this._gizmos.position)\n  }\n\n  private setStateFromJSON = (json: string): void => {\n    const state = JSON.parse(json)\n\n    if (state.arcballState && this.camera) {\n      this._cameraMatrixState.fromArray(state.arcballState.cameraMatrix.elements)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      this.camera.up.copy(state.arcballState.cameraUp)\n      this.camera.near = state.arcballState.cameraNear\n      this.camera.far = state.arcballState.cameraFar\n\n      this.camera.zoom = state.arcballState.cameraZoom\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = state.arcballState.cameraFov\n      }\n\n      this._gizmoMatrixState.fromArray(state.arcballState.gizmoMatrix.elements)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      const gizmoTmp = new Matrix4().copy(this._gizmoMatrixState0)\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n      this._gizmoMatrixState0.copy(gizmoTmp)\n\n      this.camera.lookAt(this._gizmos.position)\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n}\n\nexport { ArcballControls }\n"], "mappings": ";;;;;;;;;;;;;AAwCA,MAAMA,KAAA,GAAQ;EACZC,IAAA,EAAMC,MAAA,CAAO;EACbC,MAAA,EAAQD,MAAA,CAAO;EACfE,GAAA,EAAKF,MAAA,CAAO;EACZG,KAAA,EAAOH,MAAA,CAAO;EACdI,GAAA,EAAKJ,MAAA,CAAO;EACZK,KAAA,EAAOL,MAAA,CAAO;EACdM,OAAA,EAASN,MAAA,CAAO;EAChBO,WAAA,EAAaP,MAAA,CAAO;EACpBQ,eAAA,EAAiBR,MAAA,CAAO;EACxBS,gBAAA,EAAkBT,MAAA,CAAO;AAC3B;AAEA,MAAMU,KAAA,GAAQ;EACZC,IAAA,EAAMX,MAAA,CAAO;EACbY,UAAA,EAAYZ,MAAA,CAAO;EACnBa,mBAAA,EAAqBb,MAAA,CAAO;EAC5Bc,UAAA,EAAYd,MAAA,CAAO;EACnBe,WAAA,EAAaf,MAAA,CAAO;EACpBgB,MAAA,EAAQhB,MAAA,CAAO;AACjB;AAGA,MAAMiB,OAAA,GAAU;EACdC,CAAA,EAAG;EACHC,CAAA,EAAG;AACL;AAGA,MAAMC,eAAA,GAAkC;EACtCC,MAAA,qBAA4BC,OAAA,CAAQ;EACpCC,MAAA,qBAA4BD,OAAA,CAAQ;AACtC;AAGA,MAAME,YAAA,GAAe;EAAEC,IAAA,EAAM;AAAA;AAC7B,MAAMC,WAAA,GAAc;EAAED,IAAA,EAAM;AAAA;AAC5B,MAAME,SAAA,GAAY;EAAEF,IAAA,EAAM;AAAA;AAQ1B,MAAMG,eAAA,SAAwBC,eAAA,CAA0C;EAmHtEC,YACET,MAAA,EACAU,UAAA,GAA6C,MAC7CC,KAAA,GAAkC,MAClC;IACM;IAvHAC,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEDA,aAAA;IAECA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEDA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEEA,aAAA;IACDA,aAAA;IAEAA,aAAA;IAEAA,aAAA;IA2JA;IAAAA,aAAA,yBAAiB,MAAY;MACnC,MAAMC,KAAA,IAAS,KAAKC,OAAA,CAAQD,KAAA,CAAMhB,CAAA,GAAI,KAAKiB,OAAA,CAAQD,KAAA,CAAMf,CAAA,GAAI,KAAKgB,OAAA,CAAQD,KAAA,CAAME,CAAA,IAAK;MACrF,IAAI,KAAKf,MAAA,EAAQ;QACf,MAAMgB,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;MACF;MAEM,MAAAG,SAAA,GAAY,KAAKD,SAAA,GAAYL,KAAA;MAEnC,MAAMO,KAAA,GAAQ,IAAIC,YAAA,CAAa,GAAG,GAAGF,SAAA,EAAWA,SAAS;MACzD,MAAMG,MAAA,GAASF,KAAA,CAAMG,SAAA,CAAU,KAAKC,SAAS;MAC7C,MAAMC,aAAA,GAAgB,IAAIC,cAAA,CAAe,EAAEC,aAAA,CAAcL,MAAM;MAEpD,WAAAM,KAAA,IAAS,KAAKd,OAAA,CAAQe,QAAA,EAAU;QACzC,MAAMC,KAAA,GAAQ,KAAKhB,OAAA,CAAQe,QAAA,CAASD,KAAK;QACzCE,KAAA,CAAMC,QAAA,GAAWN,aAAA;MACnB;MAGA,KAAKO,aAAA,CAAc7B,YAAY;IAAA;IAGzBS,aAAA,wBAAiBqB,KAAA,IAA4B;MAC/C,KAAC,KAAKC,OAAA,EAAS;QACjB;MACF;MAEA,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;QACjD,IAAI,KAAKC,YAAA,CAAaD,CAAC,EAAEG,KAAA,IAAS,GAAG;UAEnCL,KAAA,CAAMM,cAAA,CAAe;UACrB;QACF;MACF;IAAA;IAGM3B,aAAA,0BAAkB,MAAY;MACpC,KAAK4B,WAAA,CAAYC,MAAA,CAAO,GAAG,KAAKD,WAAA,CAAYH,MAAM;MAClD,KAAKK,aAAA,CAAcD,MAAA,CAAO,GAAG,KAAKC,aAAA,CAAcL,MAAM;MACtD,KAAKM,MAAA,GAAStD,KAAA,CAAMC,IAAA;IAAA;IAGdsB,aAAA,wBAAiBqB,KAAA,IAA8B;MACrD,IAAIA,KAAA,CAAMW,MAAA,IAAU,KAAKX,KAAA,CAAMY,SAAA,EAAW;QACxC,KAAKC,UAAA,GAAa;QACb,KAAAC,WAAA,CAAYC,IAAA,CAAKf,KAAK;MAAA,OACtB;QACL,KAAKa,UAAA,GAAa;MACpB;MAEA,IAAIb,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMM,MAAA,EAAQ;QAC1D,KAAA6C,WAAA,CAAYQ,IAAA,CAAKf,KAAK;QACtB,KAAAS,aAAA,CAAcM,IAAA,CAAKf,KAAK;QAE7B,QAAQ,KAAKU,MAAA;UACX,KAAKtD,KAAA,CAAMC,IAAA;YAET,KAAKqD,MAAA,GAAStD,KAAA,CAAME,UAAA;YACf,KAAA2D,gBAAA,CAAiBjB,KAAA,EAAO,QAAQ;YAE9BkB,MAAA,CAAAC,gBAAA,CAAiB,eAAe,KAAKC,aAAa;YAClDF,MAAA,CAAAC,gBAAA,CAAiB,aAAa,KAAKE,WAAW;YAErD;UAEF,KAAKjE,KAAA,CAAME,UAAA;UACX,KAAKF,KAAA,CAAMG,mBAAA;YAET,KAAKmD,MAAA,GAAStD,KAAA,CAAMI,UAAA;YAEpB,KAAK8D,aAAA,CAAc;YACnB,KAAKC,YAAA,CAAa;YAClB,KAAKC,gBAAA,CAAiB;YAEtB;UAEF,KAAKpE,KAAA,CAAMI,UAAA;YAET,KAAKkD,MAAA,GAAStD,KAAA,CAAMK,WAAA;YACpB,KAAKgE,gBAAA,CAAiB;YACtB;QACJ;MAAA,WACSzB,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMC,IAAA,EAAM;QACpE,IAAIqE,QAAA,GAA+B;QAE/B,IAAA1B,KAAA,CAAM2B,OAAA,IAAW3B,KAAA,CAAM4B,OAAA,EAAS;UACvBF,QAAA;QAAA,WACF1B,KAAA,CAAM6B,QAAA,EAAU;UACdH,QAAA;QACb;QAEA,KAAKI,QAAA,GAAW,KAAKC,eAAA,CAAgB/B,KAAA,CAAMW,MAAA,EAAQe,QAAQ;QAC3D,IAAI,KAAKI,QAAA,EAAU;UACVZ,MAAA,CAAAC,gBAAA,CAAiB,eAAe,KAAKC,aAAa;UAClDF,MAAA,CAAAC,gBAAA,CAAiB,aAAa,KAAKE,WAAW;UAGrD,KAAKX,MAAA,GAAStD,KAAA,CAAMM,MAAA;UACpB,KAAKsE,OAAA,GAAUhC,KAAA,CAAMW,MAAA;UAChB,KAAAM,gBAAA,CAAiBjB,KAAA,EAAO,KAAK8B,QAAQ;QAC5C;MACF;IAAA;IAGMnD,aAAA,wBAAiBqB,KAAA,IAA8B;MACrD,IAAIA,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMM,MAAA,EAAQ;QAC/D,QAAQ,KAAKgD,MAAA;UACX,KAAKtD,KAAA,CAAME,UAAA;YAET,KAAK2E,gBAAA,CAAiBjC,KAAK;YAEtB,KAAAkC,eAAA,CAAgBlC,KAAA,EAAOxD,KAAA,CAAMG,MAAM;YACxC;UAEF,KAAKS,KAAA,CAAMG,mBAAA;YACH,MAAA4E,QAAA,GAAW,KAAKC,yBAAA,CAA0B,KAAK3B,aAAA,CAAc,CAAC,GAAGT,KAAK,IAAI,KAAKqC,WAAA;YAEjF,IAAAF,QAAA,IAAY,KAAKG,kBAAA,EAAoB;cAEvC,KAAK5B,MAAA,GAAStD,KAAA,CAAME,UAAA;cACpB,KAAK2E,gBAAA,CAAiBjC,KAAK;cAEtB,KAAAiB,gBAAA,CAAiBjB,KAAA,EAAO,QAAQ;cACrC;YACF;YAEA;UAEF,KAAK5C,KAAA,CAAMI,UAAA;YAET,KAAKyE,gBAAA,CAAiBjC,KAAK;YAE3B,KAAKuC,YAAA,CAAa;YAClB,KAAKC,WAAA,CAAY;YACjB,KAAKC,eAAA,CAAgB;YAErB;UAEF,KAAKrF,KAAA,CAAMK,WAAA;YAET,KAAKwE,gBAAA,CAAiBjC,KAAK;YAE3B,KAAK0C,eAAA,CAAgB;YACrB;QACJ;MAAA,WACS1C,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMM,MAAA,EAAQ;QACtE,IAAIgE,QAAA,GAA+B;QAE/B,IAAA1B,KAAA,CAAM2B,OAAA,IAAW3B,KAAA,CAAM4B,OAAA,EAAS;UACvBF,QAAA;QAAA,WACF1B,KAAA,CAAM6B,QAAA,EAAU;UACdH,QAAA;QACb;QAEA,MAAMiB,YAAA,GAAe,KAAKC,oBAAA,CAAqB,KAAKZ,OAAA,EAASN,QAAQ;QAErE,IAAIiB,YAAA,EAAc;UACX,KAAAT,eAAA,CAAgBlC,KAAA,EAAO2C,YAAY;QAC1C;MACF;MAGA,IAAI,KAAK9B,UAAA,EAAY;QACnB,MAAMsB,QAAA,GACJ,KAAKC,yBAAA,CAA0B,KAAKtB,WAAA,CAAY,KAAKA,WAAA,CAAYV,MAAA,GAAS,CAAC,GAAGJ,KAAK,IAAI,KAAKqC,WAAA;QAC1F,IAAAF,QAAA,GAAW,KAAKU,kBAAA,EAAoB;UACtC,KAAKhC,UAAA,GAAa;QACpB;MACF;IAAA;IAGMlC,aAAA,sBAAeqB,KAAA,IAA8B;MACnD,IAAIA,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMM,MAAA,EAAQ;QACzD,MAAAoF,MAAA,GAAS,KAAKrC,aAAA,CAAcL,MAAA;QAElC,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAI4C,MAAA,EAAQ5C,CAAA,IAAK;UAC/B,IAAI,KAAKO,aAAA,CAAcP,CAAC,EAAE6C,SAAA,IAAa/C,KAAA,CAAM+C,SAAA,EAAW;YACjD,KAAAtC,aAAA,CAAcD,MAAA,CAAON,CAAA,EAAG,CAAC;YACzB,KAAAK,WAAA,CAAYC,MAAA,CAAON,CAAA,EAAG,CAAC;YAC5B;UACF;QACF;QAEA,QAAQ,KAAKQ,MAAA;UACX,KAAKtD,KAAA,CAAME,UAAA;UACX,KAAKF,KAAA,CAAMG,mBAAA;YAEF2D,MAAA,CAAA8B,mBAAA,CAAoB,eAAe,KAAK5B,aAAa;YACrDF,MAAA,CAAA8B,mBAAA,CAAoB,aAAa,KAAK3B,WAAW;YAExD,KAAKX,MAAA,GAAStD,KAAA,CAAMC,IAAA;YACpB,KAAK4F,cAAA,CAAe;YAEpB;UAEF,KAAK7F,KAAA,CAAMI,UAAA;YAET,KAAK0F,cAAA,CAAe;YACpB,KAAKC,UAAA,CAAW;YAChB,KAAKC,WAAA,CAAY;YAGjB,KAAK1C,MAAA,GAAStD,KAAA,CAAMG,mBAAA;YAEpB;UAEF,KAAKH,KAAA,CAAMK,WAAA;YACL,SAAKgD,aAAA,CAAcL,MAAA,IAAU,GAAG;cAC3Bc,MAAA,CAAA8B,mBAAA,CAAoB,eAAe,KAAK5B,aAAa;cACrDF,MAAA,CAAA8B,mBAAA,CAAoB,aAAa,KAAK3B,WAAW;cAGxD,KAAKX,MAAA,GAAStD,KAAA,CAAMC,IAAA;cACpB,KAAKgG,cAAA,CAAe;YACtB;YAEA;QACJ;MAAA,WACSrD,KAAA,CAAMgB,WAAA,IAAe,WAAW,KAAKN,MAAA,IAAUtD,KAAA,CAAMM,MAAA,EAAQ;QAC/DwD,MAAA,CAAA8B,mBAAA,CAAoB,eAAe,KAAK5B,aAAa;QACrDF,MAAA,CAAA8B,mBAAA,CAAoB,aAAa,KAAK3B,WAAW;QAExD,KAAKX,MAAA,GAAStD,KAAA,CAAMC,IAAA;QACpB,KAAK4F,cAAA,CAAe;QACpB,KAAKjB,OAAA,GAAU;MACjB;MAEA,IAAIhC,KAAA,CAAMY,SAAA,EAAW;QACnB,IAAI,KAAKC,UAAA,EAAY;UACb,MAAAyC,QAAA,GAAWtD,KAAA,CAAMuD,SAAA,GAAY,KAAKzC,WAAA,CAAY,KAAKA,WAAA,CAAYV,MAAA,GAAS,CAAC,EAAEmD,SAAA;UAE7E,IAAAD,QAAA,IAAY,KAAKE,YAAA,EAAc;YAC7B,SAAKC,QAAA,IAAY,GAAG;cAEtB,KAAKA,QAAA,GAAW;cACX,KAAAC,WAAA,GAAcC,WAAA,CAAYC,GAAA;YAAI,OAC9B;cACC,MAAAC,aAAA,GAAgB7D,KAAA,CAAMuD,SAAA,GAAY,KAAKG,WAAA;cAC7C,MAAMvB,QAAA,GAAW,KAAKC,yBAAA,CAA0B,KAAKtB,WAAA,CAAY,CAAC,GAAG,KAAKA,WAAA,CAAY,CAAC,CAAC,IAAI,KAAKuB,WAAA;cAEjG,IAAIwB,aAAA,IAAiB,KAAKC,YAAA,IAAgB3B,QAAA,IAAY,KAAK4B,aAAA,EAAe;gBAGxE,KAAKN,QAAA,GAAW;gBAChB,KAAK3C,WAAA,CAAYN,MAAA,CAAO,GAAG,KAAKM,WAAA,CAAYV,MAAM;gBAClD,KAAK4D,WAAA,CAAYhE,KAAK;cAAA,OACjB;gBAEL,KAAKyD,QAAA,GAAW;gBAChB,KAAK3C,WAAA,CAAYmD,KAAA;gBACZ,KAAAP,WAAA,GAAcC,WAAA,CAAYC,GAAA;cACjC;YACF;UAAA,OACK;YACL,KAAK/C,UAAA,GAAa;YAClB,KAAK4C,QAAA,GAAW;YAChB,KAAK3C,WAAA,CAAYN,MAAA,CAAO,GAAG,KAAKM,WAAA,CAAYV,MAAM;UACpD;QAAA,OACK;UACL,KAAKqD,QAAA,GAAW;UAChB,KAAK3C,WAAA,CAAYN,MAAA,CAAO,GAAG,KAAKM,WAAA,CAAYV,MAAM;QACpD;MACF;IAAA;IAGMzB,aAAA,kBAAWqB,KAAA,IAA4B;;MAC7C,IAAI,KAAKC,OAAA,IAAW,KAAKiE,UAAA,IAAc,KAAKzF,UAAA,EAAY;QACtD,IAAIiD,QAAA,GAA+B;QAE/B,IAAA1B,KAAA,CAAM2B,OAAA,IAAW3B,KAAA,CAAM4B,OAAA,EAAS;UACvBF,QAAA;QAAA,WACF1B,KAAA,CAAM6B,QAAA,EAAU;UACdH,QAAA;QACb;QAEA,MAAMyC,OAAA,GAAU,KAAKpC,eAAA,CAAgB,SAASL,QAAQ;QAEtD,IAAIyC,OAAA,EAAS;UACXnE,KAAA,CAAMM,cAAA,CAAe;UAErB,KAAKP,aAAA,CAAc3B,WAAW;UAE9B,MAAMgG,WAAA,GAAc;UAChB,IAAAC,GAAA,GAAMrE,KAAA,CAAMsE,MAAA,GAASF,WAAA;UAEzB,IAAIG,IAAA,GAAO;UAEX,IAAIF,GAAA,GAAM,GAAG;YACXE,IAAA,GAAO,IAAI,KAAKC,WAAA;UAAA,WACPH,GAAA,GAAM,GAAG;YAClBE,IAAA,GAAO,KAAKC,WAAA;UACd;UAEA,QAAQL,OAAA;YACN,KAAK;cACE,KAAAM,aAAA,CAAcjI,KAAA,CAAMK,KAAA,EAAO,IAAI;cAEpC,IAAIwH,GAAA,GAAM,GAAG;gBACXE,IAAA,GAAO,IAAIG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAaH,GAAG;cAAA,WAChCA,GAAA,GAAM,GAAG;gBAClBE,IAAA,GAAOG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAa,CAACH,GAAG;cACxC;cAEI,SAAKO,UAAA,IAAc,KAAKC,SAAA,EAAW;gBACjC,IAAAC,UAAA;gBAEA,SAAK/G,MAAA,YAAkBgH,kBAAA,EAAoB;kBAChCD,UAAA,IAAAE,EAAA,QAAKC,kBAAA,CAAmB,KAAKlH,MAAA,EAAQiC,KAAA,CAAMkF,OAAA,EAASlF,KAAA,CAAMmF,OAAA,EAAS,KAAK1G,UAAU,MAAlF,gBAAAuG,EAAA,CACTI,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAA,EAC7BC,cAAA,CAAe,IAAI,KAAKvH,MAAA,CAAOwH,IAAA,EAC/BC,GAAA,CAAI,KAAK3G,OAAA,CAAQ4G,QAAA;gBACtB;gBAEI,SAAK1H,MAAA,YAAkB2H,iBAAA,EAAmB;kBAC5CZ,UAAA,IAAaa,EAAA,QAAKV,kBAAA,CAAmB,KAAKlH,MAAA,EAAQiC,KAAA,CAAMkF,OAAA,EAASlF,KAAA,CAAMmF,OAAA,EAAS,KAAK1G,UAAU,MAAlF,gBAAAkH,EAAA,CACTP,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAA,EAC7BG,GAAA,CAAI,KAAK3G,OAAA,CAAQ4G,QAAA;gBACtB;gBAEA,IAAIX,UAAA,KAAe,QAAW,KAAKc,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAMO,UAAU,CAAC;cAAA,OACpF;gBACL,KAAKc,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAK1F,OAAA,CAAQ4G,QAAQ,CAAC;cACxE;cAEA,IAAI,KAAKK,KAAA,EAAO;gBACd,KAAKC,WAAA,CAAY;gBACjB,KAAKC,QAAA,CAAS;cAChB;cAEK,KAAAvB,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;cAGpC,KAAKsD,aAAA,CAAc7B,YAAY;cAE/B,KAAK6B,aAAA,CAAc1B,SAAS;cAE5B;YAEF,KAAK;cACC,SAAKN,MAAA,YAAkB2H,iBAAA,EAAmB;gBACvC,KAAAjB,aAAA,CAAcjI,KAAA,CAAMM,GAAA,EAAK,IAAI;gBAe9B,IAAAkD,KAAA,CAAMiG,MAAA,IAAU,GAAG;kBACrB5B,GAAA,GAAMrE,KAAA,CAAMiG,MAAA,GAAS7B,WAAA;kBAEdG,IAAA;kBAEP,IAAIF,GAAA,GAAM,GAAG;oBACXE,IAAA,GAAO,IAAIG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAaH,GAAG;kBAAA,WAChCA,GAAA,GAAM,GAAG;oBAClBE,IAAA,GAAOG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAa,CAACH,GAAG;kBACxC;gBACF;gBAEK,KAAA6B,KAAA,CAAMC,qBAAA,CAAsB,KAAKC,kBAAkB;gBACxD,MAAMxI,CAAA,GAAI,KAAKsI,KAAA,CAAMG,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;gBACrD,IAAIa,IAAA,GAAO1I,CAAA,GAAI2G,IAAA;gBAGf+B,IAAA,GAAOC,SAAA,CAAUC,KAAA,CAAMF,IAAA,EAAM,KAAKG,WAAA,EAAa,KAAKC,WAAW;gBAEzD,MAAA7I,CAAA,GAAID,CAAA,GAAI8G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,GAAU,KAAK7I,MAAA,CAAO8I,GAAA,GAAM,GAAG;gBAGhE,IAAIC,MAAA,GAASP,SAAA,CAAUQ,OAAA,IAAWrC,IAAA,CAAKsC,IAAA,CAAKnJ,CAAA,GAAIyI,IAAI,IAAI;gBAGpD,IAAAQ,MAAA,GAAS,KAAKG,MAAA,EAAQ;kBACxBH,MAAA,GAAS,KAAKG,MAAA;gBAAA,WACLH,MAAA,GAAS,KAAKI,MAAA,EAAQ;kBAC/BJ,MAAA,GAAS,KAAKI,MAAA;gBAChB;gBAEA,MAAMC,WAAA,GAActJ,CAAA,GAAI6G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,IAAWE,MAAA,GAAS,EAAE;gBACjEvC,IAAA,GAAO3G,CAAA,GAAIuJ,WAAA;gBAEX,KAAKC,MAAA,CAAON,MAAM;gBACb,KAAAlB,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAK1F,OAAA,CAAQ4G,QAAA,EAAU,KAAK,CAAC;cAC/E;cAEA,IAAI,KAAKK,KAAA,EAAO;gBACd,KAAKC,WAAA,CAAY;gBACjB,KAAKC,QAAA,CAAS;cAChB;cAEK,KAAAvB,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;cAGpC,KAAKsD,aAAA,CAAc7B,YAAY;cAE/B,KAAK6B,aAAA,CAAc1B,SAAS;cAE5B;UACJ;QACF;MACF;IAAA;IAGMM,aAAA,2BAAmB,CAACqB,KAAA,EAAqBqH,SAAA,KAA+B;MAC1E,SAAKpH,OAAA,IAAW,KAAKxB,UAAA,EAAY;QAEnC,KAAKsB,aAAA,CAAc3B,WAAW;QAE9B,KAAKkJ,SAAA,CAAUtH,KAAA,CAAMkF,OAAA,EAASlF,KAAA,CAAMmF,OAAO;QAE3C,QAAQkC,SAAA;UACN,KAAK;YACH,IAAI,CAAC,KAAKxC,SAAA,EAAW;YAEjB,SAAK0C,YAAA,IAAgB,IAAI;cAC3BC,oBAAA,CAAqB,KAAKD,YAAY;cACtC,KAAKA,YAAA,GAAe;cACpB,KAAKE,UAAA,GAAa;cAElB,KAAKC,cAAA,CAAe,KAAK;cAEzB,KAAK3H,aAAA,CAAc7B,YAAY;YACjC;YAEA,IAAI,KAAKH,MAAA,EAAQ;cACV,KAAA0G,aAAA,CAAcjI,KAAA,CAAMI,GAAA,EAAK,IAAI;cAC5B,MAAA+K,MAAA,GAAS,KAAK1C,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU;cACzF,IAAIkJ,MAAA,KAAW,QAAW;gBACnB,KAAAC,oBAAA,CAAqBC,IAAA,CAAKF,MAAM;cACvC;cACA,IAAI,KAAKG,UAAA,EAAY;gBACnB,KAAK9B,QAAA,CAAS;gBAEd,KAAKjG,aAAA,CAAc7B,YAAY;cACjC;YACF;YAEA;UAEF,KAAK;YACH,IAAI,CAAC,KAAK6J,YAAA,EAAc;YAEpB,SAAKR,YAAA,IAAgB,IAAI;cAC3BC,oBAAA,CAAqB,KAAKD,YAAY;cACtC,KAAKA,YAAA,GAAe;cACpB,KAAKE,UAAA,GAAa;YACpB;YAEA,IAAI,KAAK1J,MAAA,EAAQ;cACV,KAAA0G,aAAA,CAAcjI,KAAA,CAAMG,MAAA,EAAQ,IAAI;cACrC,MAAMgL,MAAA,GAAS,KAAKK,oBAAA,CAAqB,KAAKjK,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAA,EAAY,KAAKQ,SAAS;cAC3G,IAAI0I,MAAA,KAAW,QAAW;gBACnB,KAAAC,oBAAA,CAAqBC,IAAA,CAAKF,MAAM;cACvC;cACA,KAAKD,cAAA,CAAe,IAAI;cACxB,IAAI,KAAKO,gBAAA,EAAkB;gBACzB,KAAKC,SAAA,GAAY,KAAKC,YAAA,GAAexE,WAAA,CAAYC,GAAA,CAAI;gBAChD,KAAAwE,aAAA,GAAgB,KAAKC,UAAA,GAAa;gBAClC,KAAAC,cAAA,CAAeT,IAAA,CAAK,KAAKD,oBAAoB;gBAC7C,KAAAW,cAAA,CAAeV,IAAA,CAAK,KAAKS,cAAc;gBAC5C,KAAKE,MAAA,GAAS;gBACd,KAAKC,MAAA,GAAS,KAAKD,MAAA;cACrB;YACF;YAGA,KAAKzI,aAAA,CAAc7B,YAAY;YAC/B;UAEF,KAAK;YACH,IAAI,CAAC,KAAKgG,UAAA,EAAY;YAElB,SAAKnG,MAAA,YAAkB2H,iBAAA,EAAmB;cACxC,SAAK6B,YAAA,IAAgB,IAAI;gBAC3BC,oBAAA,CAAqB,KAAKD,YAAY;gBACtC,KAAKA,YAAA,GAAe;gBACpB,KAAKE,UAAA,GAAa;gBAElB,KAAKC,cAAA,CAAe,KAAK;gBAEzB,KAAK3H,aAAA,CAAc7B,YAAY;cACjC;cAEK,KAAAuG,aAAA,CAAcjI,KAAA,CAAMM,GAAA,EAAK,IAAI;cAClC,KAAK8K,oBAAA,CAAqBc,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;cAC1F,KAAA+K,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;YAC5D;YACA;UAEF,KAAK;YACH,IAAI,CAAC,KAAK1D,UAAA,EAAY;YAElB,SAAKqD,YAAA,IAAgB,IAAI;cAC3BC,oBAAA,CAAqB,KAAKD,YAAY;cACtC,KAAKA,YAAA,GAAe;cACpB,KAAKE,UAAA,GAAa;cAElB,KAAKC,cAAA,CAAe,KAAK;cAEzB,KAAK3H,aAAA,CAAc7B,YAAY;YACjC;YAEK,KAAAuG,aAAA,CAAcjI,KAAA,CAAMK,KAAA,EAAO,IAAI;YACpC,KAAK+K,oBAAA,CAAqBc,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;YAC1F,KAAA+K,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;YAC1D;QACJ;MACF;IAAA;IAGMjJ,aAAA,0BAAkB,CAACqB,KAAA,EAAqB6I,OAAA,KAA0B;MACpE,SAAK5I,OAAA,IAAW,KAAKxB,UAAA,EAAY;QAC7B,MAAAqK,OAAA,GAAUD,OAAA,IAAW,KAAKE,MAAA;QAChC,KAAKzB,SAAA,CAAUtH,KAAA,CAAMkF,OAAA,EAASlF,KAAA,CAAMmF,OAAO;QAE3C,QAAQ0D,OAAA;UACN,KAAKrM,KAAA,CAAMI,GAAA;YACL,SAAKiI,SAAA,IAAa,KAAK9G,MAAA,EAAQ;cACjC,IAAI+K,OAAA,EAAS;gBAIX,KAAK/I,aAAA,CAAc1B,SAAS;gBAE5B,KAAK0B,aAAA,CAAc3B,WAAW;gBAEzB,KAAAqG,aAAA,CAAcoE,OAAA,EAAS,IAAI;gBAC1B,MAAAlB,MAAA,GAAS,KAAK1C,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU;gBACzF,IAAIkJ,MAAA,KAAW,QAAW;kBACnB,KAAAC,oBAAA,CAAqBC,IAAA,CAAKF,MAAM;gBACvC;gBACA,IAAI,KAAKG,UAAA,EAAY;kBACnB,KAAK9B,QAAA,CAAS;gBAChB;gBAEA,KAAK0B,cAAA,CAAe,KAAK;cAAA,OACpB;gBAEC,MAAAC,MAAA,GAAS,KAAK1C,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU;gBACzF,IAAIkJ,MAAA,KAAW,QAAW;kBACnB,KAAAiB,sBAAA,CAAuBf,IAAA,CAAKF,MAAM;gBACzC;gBACA,KAAK/B,oBAAA,CAAqB,KAAKoD,GAAA,CAAI,KAAKpB,oBAAA,EAAsB,KAAKgB,sBAAsB,CAAC;cAC5F;YACF;YAEA;UAEF,KAAKpM,KAAA,CAAMG,MAAA;YACL,SAAKoL,YAAA,IAAgB,KAAKhK,MAAA,EAAQ;cACpC,IAAI+K,OAAA,EAAS;gBAIX,KAAK/I,aAAA,CAAc1B,SAAS;gBAE5B,KAAK0B,aAAA,CAAc3B,WAAW;gBAEzB,KAAAqG,aAAA,CAAcoE,OAAA,EAAS,IAAI;gBAChC,MAAMlB,MAAA,GAAS,KAAKK,oBAAA,CAClB,KAAKjK,MAAA,EACLJ,OAAA,CAAQC,CAAA,EACRD,OAAA,CAAQE,CAAA,EACR,KAAKY,UAAA,EACL,KAAKQ,SAAA;gBAEP,IAAI0I,MAAA,KAAW,QAAW;kBACnB,KAAAC,oBAAA,CAAqBC,IAAA,CAAKF,MAAM;gBACvC;gBAEA,IAAI,KAAKG,UAAA,EAAY;kBACnB,KAAK/B,WAAA,CAAY;gBACnB;gBAEA,KAAK2B,cAAA,CAAe,IAAI;cAAA,OACnB;gBAEL,MAAMC,MAAA,GAAS,KAAKK,oBAAA,CAClB,KAAKjK,MAAA,EACLJ,OAAA,CAAQC,CAAA,EACRD,OAAA,CAAQE,CAAA,EACR,KAAKY,UAAA,EACL,KAAKQ,SAAA;gBAEP,IAAI0I,MAAA,KAAW,QAAW;kBACnB,KAAAiB,sBAAA,CAAuBf,IAAA,CAAKF,MAAM;gBACzC;gBAEA,MAAMsB,QAAA,GAAW,KAAKrB,oBAAA,CAAqBvB,UAAA,CAAW,KAAKuC,sBAAsB;gBACjF,MAAMM,KAAA,GAAQ,KAAKtB,oBAAA,CAAqBuB,OAAA,CAAQ,KAAKP,sBAAsB;gBAC3E,MAAMQ,MAAA,GAAS1E,IAAA,CAAK2E,GAAA,CAAIJ,QAAA,GAAW,KAAKhK,SAAA,EAAWiK,KAAK;gBAEnD,KAAAtD,oBAAA,CACH,KAAK0D,MAAA,CAAO,KAAKC,qBAAA,CAAsB,KAAK3B,oBAAA,EAAsB,KAAKgB,sBAAsB,GAAGQ,MAAM;gBAGxG,IAAI,KAAKnB,gBAAA,EAAkB;kBACzB,KAAKC,SAAA,GAAY,KAAKC,YAAA;kBACjB,KAAAA,YAAA,GAAexE,WAAA,CAAYC,GAAA;kBAChC,KAAKyE,UAAA,GAAa,KAAKD,aAAA;kBACvB,KAAKA,aAAA,GAAgBgB,MAAA;kBAChB,KAAAd,cAAA,CAAeT,IAAA,CAAK,KAAKU,cAAc;kBACvC,KAAAA,cAAA,CAAeV,IAAA,CAAK,KAAKe,sBAAsB;kBACpD,KAAKH,MAAA,GAAS,KAAKD,MAAA;kBACnB,KAAKA,MAAA,GAAS,KAAKgB,qBAAA,CACjB,KAAKnB,UAAA,EACL,KAAKD,aAAA,EACL,KAAKF,SAAA,EACL,KAAKC,YAAA;gBAET;cACF;YACF;YAEA;UAEF,KAAK3L,KAAA,CAAMK,KAAA;YACT,IAAI,KAAKqH,UAAA,EAAY;cACnB,IAAI4E,OAAA,EAAS;gBAIX,KAAK/I,aAAA,CAAc1B,SAAS;gBAE5B,KAAK0B,aAAA,CAAc3B,WAAW;gBAEzB,KAAAqG,aAAA,CAAcoE,OAAA,EAAS,IAAI;gBAChC,KAAKjB,oBAAA,CAAqBc,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;gBAC1F,KAAA+K,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;gBAE1D,IAAI,KAAKE,UAAA,EAAY;kBACnB,KAAK/B,WAAA,CAAY;gBACnB;gBAEA,KAAK2B,cAAA,CAAe,KAAK;cAAA,OACpB;gBAEL,MAAM+B,aAAA,GAAgB;gBACtB,KAAKb,sBAAA,CAAuBF,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;gBAEjG,MAAMsE,QAAA,GAAW,KAAKyG,sBAAA,CAAuB/K,CAAA,GAAI,KAAK+J,oBAAA,CAAqB/J,CAAA;gBAE3E,IAAI0G,IAAA,GAAO;gBAEX,IAAIpC,QAAA,GAAW,GAAG;kBAChBoC,IAAA,GAAO,IAAIG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAa,CAACrC,QAAA,GAAWsH,aAAa;gBAAA,WACtDtH,QAAA,GAAW,GAAG;kBACvBoC,IAAA,GAAOG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAarC,QAAA,GAAWsH,aAAa;gBAC5D;gBAEA,KAAK7D,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAK1F,OAAA,CAAQ4G,QAAQ,CAAC;cACxE;YACF;YAEA;UAEF,KAAKjJ,KAAA,CAAMM,GAAA;YACT,IAAI,KAAKoH,UAAA,IAAc,KAAKnG,MAAA,YAAkB2H,iBAAA,EAAmB;cAC/D,IAAIoD,OAAA,EAAS;gBAIX,KAAK/I,aAAA,CAAc1B,SAAS;gBAE5B,KAAK0B,aAAA,CAAc3B,WAAW;gBAEzB,KAAAqG,aAAA,CAAcoE,OAAA,EAAS,IAAI;gBAChC,KAAKjB,oBAAA,CAAqBc,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;gBAC1F,KAAA+K,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;gBAE1D,IAAI,KAAKE,UAAA,EAAY;kBACnB,KAAK/B,WAAA,CAAY;gBACnB;gBAEA,KAAK2B,cAAA,CAAe,KAAK;cAAA,OACpB;gBAEL,MAAM+B,aAAA,GAAgB;gBACtB,KAAKb,sBAAA,CAAuBF,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;gBAEjG,MAAMsE,QAAA,GAAW,KAAKyG,sBAAA,CAAuB/K,CAAA,GAAI,KAAK+J,oBAAA,CAAqB/J,CAAA;gBAE3E,IAAI0G,IAAA,GAAO;gBAEX,IAAIpC,QAAA,GAAW,GAAG;kBAChBoC,IAAA,GAAO,IAAIG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAa,CAACrC,QAAA,GAAWsH,aAAa;gBAAA,WACtDtH,QAAA,GAAW,GAAG;kBACvBoC,IAAA,GAAOG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAarC,QAAA,GAAWsH,aAAa;gBAC5D;gBAEK,KAAAvD,KAAA,CAAMC,qBAAA,CAAsB,KAAKC,kBAAkB;gBACxD,MAAMxI,CAAA,GAAI,KAAKsI,KAAA,CAAMG,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;gBACrD,IAAIa,IAAA,GAAO1I,CAAA,GAAI2G,IAAA;gBAGf+B,IAAA,GAAOC,SAAA,CAAUC,KAAA,CAAMF,IAAA,EAAM,KAAKG,WAAA,EAAa,KAAKC,WAAW;gBAEzD,MAAA7I,CAAA,GAAID,CAAA,GAAI8G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,GAAU,KAAK8C,SAAA,GAAY,GAAG;gBAG/D,IAAI5C,MAAA,GAASP,SAAA,CAAUQ,OAAA,IAAWrC,IAAA,CAAKsC,IAAA,CAAKnJ,CAAA,GAAIyI,IAAI,IAAI;gBAGxDQ,MAAA,GAASP,SAAA,CAAUC,KAAA,CAAMM,MAAA,EAAQ,KAAKI,MAAA,EAAQ,KAAKD,MAAM;gBAEzD,MAAME,WAAA,GAActJ,CAAA,GAAI6G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,IAAWE,MAAA,GAAS,EAAE;gBACjEvC,IAAA,GAAO3G,CAAA,GAAIuJ,WAAA;gBACN,KAAAwC,KAAA,CAAMxD,qBAAA,CAAsB,KAAKyD,iBAAiB;gBAEvD,KAAKxC,MAAA,CAAON,MAAM;gBAClB,KAAKlB,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAKoF,KAAA,EAAO,KAAK,CAAC;gBAGlE,MAAME,SAAA,GAAY,KAAKhL,OAAA,CAAQ4G,QAAA,CAC5BqE,KAAA,CACA,EAAAC,GAAA,CAAI,KAAKhM,MAAA,CAAO0H,QAAQ,EACxBuE,SAAA,GACA1E,cAAA,CAAe6B,WAAA,GAAcvJ,CAAC;gBACjC,KAAKqM,KAAA,CAAMC,eAAA,CAAgBL,SAAA,CAAUjM,CAAA,EAAGiM,SAAA,CAAUhM,CAAA,EAAGgM,SAAA,CAAU/K,CAAC;cAClE;YACF;YAEA;QACJ;QAGA,KAAKiB,aAAA,CAAc7B,YAAY;MACjC;IAAA;IAGMS,aAAA,yBAAiB,MAAY;MAC/B,SAAKoK,MAAA,IAAUvM,KAAA,CAAMG,MAAA,EAAQ;QAC3B,KAAC,KAAKoL,YAAA,EAAc;UACtB;QACF;QAEA,IAAI,KAAKE,gBAAA,EAAkB;UAEzB,MAAMkC,SAAA,GAAYxG,WAAA,CAAYC,GAAA,CAAI,IAAI,KAAKuE,YAAA;UAC3C,IAAIgC,SAAA,GAAY,KAAK;YACnB,MAAMC,CAAA,GAAI1F,IAAA,CAAK2F,GAAA,EAAK,KAAK5B,MAAA,GAAS,KAAKD,MAAA,IAAU,CAAC;YAElD,MAAM8B,IAAA,GAAO;YACb,KAAK/C,YAAA,GAAerG,MAAA,CAAOqJ,qBAAA,CAAsB,UAAUC,CAAA,EAAG;cACvDF,IAAA,CAAA7F,aAAA,CAAcjI,KAAA,CAAMW,gBAAA,EAAkB,IAAI;cAC/C,MAAMsN,YAAA,GAAeH,IAAA,CAAKf,qBAAA,CAAsBe,IAAA,CAAKhC,cAAA,EAAgBgC,IAAA,CAAK/B,cAAc;cAEnF+B,IAAA,CAAAI,cAAA,CAAeF,CAAA,EAAGC,YAAA,EAAc/F,IAAA,CAAKiG,GAAA,CAAIP,CAAA,EAAGE,IAAA,CAAKM,IAAI,CAAC;YAAA,CAC5D;UAAA,OACI;YAEA,KAAAnG,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;YACpC,KAAKiL,cAAA,CAAe,KAAK;YAEzB,KAAK3H,aAAA,CAAc7B,YAAY;UACjC;QAAA,OACK;UACA,KAAAuG,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;UACpC,KAAKiL,cAAA,CAAe,KAAK;UAEzB,KAAK3H,aAAA,CAAc7B,YAAY;QACjC;MAAA,WACS,KAAK6K,MAAA,IAAUvM,KAAA,CAAMI,GAAA,IAAO,KAAKmM,MAAA,IAAUvM,KAAA,CAAMC,IAAA,EAAM;QAC3D,KAAAgI,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;QAEpC,IAAI,KAAKqL,UAAA,EAAY;UACnB,KAAK/B,WAAA,CAAY;QACnB;QAEA,KAAK2B,cAAA,CAAe,KAAK;QAEzB,KAAK3H,aAAA,CAAc7B,YAAY;MACjC;MAGA,KAAK6B,aAAA,CAAc1B,SAAS;IAAA;IAGtBM,aAAA,sBAAeqB,KAAA,IAA8B;MAC/C,SAAKC,OAAA,IAAW,KAAK4E,SAAA,IAAa,KAAKnG,KAAA,IAAS,KAAKX,MAAA,IAAU,KAAKU,UAAA,EAAY;QAElF,KAAKsB,aAAA,CAAc3B,WAAW;QAE9B,KAAKkJ,SAAA,CAAUtH,KAAA,CAAMkF,OAAA,EAASlF,KAAA,CAAMmF,OAAO;QAC3C,MAAM0F,IAAA,GAAO,KAAKC,cAAA,CAAe,KAAKnC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,GAAG,KAAKV,MAAM;QAElG,IAAA8M,IAAA,IAAQ,KAAK5C,gBAAA,EAAkB;UACjC,MAAMqC,IAAA,GAAO;UACT,SAAK/C,YAAA,IAAgB,IAAI;YACpBrG,MAAA,CAAAsG,oBAAA,CAAqB,KAAKD,YAAY;UAC/C;UAEA,KAAKE,UAAA,GAAa;UAClB,KAAKF,YAAA,GAAerG,MAAA,CAAOqJ,qBAAA,CAAsB,UAAUC,CAAA,EAAG;YACvDF,IAAA,CAAA7F,aAAA,CAAcjI,KAAA,CAAMU,eAAA,EAAiB,IAAI;YAC9CoN,IAAA,CAAKS,WAAA,CAAYP,CAAA,EAAGK,IAAA,EAAMP,IAAA,CAAKlE,kBAAA,EAAoBkE,IAAA,CAAKV,iBAAiB;UAAA,CAC1E;QACQ,WAAAiB,IAAA,IAAQ,CAAC,KAAK5C,gBAAA,EAAkB;UACpC,KAAAxD,aAAA,CAAcjI,KAAA,CAAMO,KAAA,EAAO,IAAI;UAC/B,KAAAiO,KAAA,CAAMH,IAAA,EAAM,KAAKrG,WAAW;UAC5B,KAAAC,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;UAEpC,KAAKsD,aAAA,CAAc7B,YAAY;QACjC;MACF;MAGA,KAAK6B,aAAA,CAAc1B,SAAS;IAAA;IAGtBM,aAAA,2BAAmB,MAAY;MACrC,IAAI,KAAKsB,OAAA,IAAW,KAAK4E,SAAA,IAAa,KAAK9G,MAAA,IAAU,KAAKU,UAAA,EAAY;QAEpE,KAAKsB,aAAA,CAAc3B,WAAW;QAEzB,KAAAqG,aAAA,CAAcjI,KAAA,CAAMI,GAAA,EAAK,IAAI;QAE7B,KAAA0K,SAAA,EACF,KAAK7G,aAAA,CAAc,CAAC,EAAEyE,OAAA,GAAU,KAAKzE,aAAA,CAAc,CAAC,EAAEyE,OAAA,IAAW,IACjE,KAAKzE,aAAA,CAAc,CAAC,EAAE0E,OAAA,GAAU,KAAK1E,aAAA,CAAc,CAAC,EAAE0E,OAAA,IAAW;QAG9D,MAAAwC,MAAA,GAAS,KAAK1C,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAA,EAAY,IAAI;QAC/F,IAAIkJ,MAAA,KAAW,QAAW;UACnB,KAAAC,oBAAA,CAAqBC,IAAA,CAAKF,MAAM;QACvC;QACK,KAAAiB,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;QAE1D,KAAKF,cAAA,CAAe,KAAK;MAC3B;IAAA;IAGM/I,aAAA,0BAAkB,MAAY;MACpC,IAAI,KAAKsB,OAAA,IAAW,KAAK4E,SAAA,IAAa,KAAK9G,MAAA,IAAU,KAAKU,UAAA,EAAY;QAC/D,KAAA6I,SAAA,EACF,KAAK7G,aAAA,CAAc,CAAC,EAAEyE,OAAA,GAAU,KAAKzE,aAAA,CAAc,CAAC,EAAEyE,OAAA,IAAW,IACjE,KAAKzE,aAAA,CAAc,CAAC,EAAE0E,OAAA,GAAU,KAAK1E,aAAA,CAAc,CAAC,EAAE0E,OAAA,IAAW;QAGhE,SAAK4D,MAAA,IAAUvM,KAAA,CAAMI,GAAA,EAAK;UACvB,KAAA6H,aAAA,CAAcjI,KAAA,CAAMI,GAAA,EAAK,IAAI;UAC7B,KAAAgL,oBAAA,CAAqBC,IAAA,CAAK,KAAKe,sBAAsB;QAC5D;QAEM,MAAAjB,MAAA,GAAS,KAAK1C,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAA,EAAY,IAAI;QAC/F,IAAIkJ,MAAA,KAAW,QAAgB,KAAAiB,sBAAA,CAAuBf,IAAA,CAAKF,MAAM;QAC5D,KAAA/B,oBAAA,CAAqB,KAAKoD,GAAA,CAAI,KAAKpB,oBAAA,EAAsB,KAAKgB,sBAAA,EAAwB,IAAI,CAAC;QAEhG,KAAK7I,aAAA,CAAc7B,YAAY;MACjC;IAAA;IAGMS,aAAA,yBAAiB,MAAY;MAC9B,KAAA8F,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;MAEpC,KAAKsD,aAAA,CAAc1B,SAAS;IAAA;IAGtBM,aAAA,wBAAgB,MAAY;;MAC9B,SAAKsB,OAAA,IAAW,KAAK8H,YAAA,EAAc;QAErC,KAAKhI,aAAA,CAAc3B,WAAW;QAEzB,KAAAqG,aAAA,CAAcjI,KAAA,CAAMQ,OAAA,EAAS,IAAI;QAIjC,KAAAiO,oBAAA,GACH,KAAKC,QAAA,CAAS,KAAKzK,aAAA,CAAc,CAAC,GAAG,KAAKA,aAAA,CAAc,CAAC,CAAC,IAC1D,KAAKyK,QAAA,CAAS,KAAK3K,WAAA,CAAY,CAAC,GAAG,KAAKA,WAAA,CAAY,CAAC,CAAC;QACxD,KAAK4K,sBAAA,GAAyB,KAAKF,oBAAA;QAE9B,CAAAjG,EAAA,QAAAjH,MAAA,qBAAAiH,EAAA,CAAQoG,iBAAA,CAAkB,KAAKC,aAAA;QAEpC,IAAI,CAAC,KAAKxG,SAAA,IAAa,CAAC,KAAKX,UAAA,EAAY;UACvC,KAAKwD,cAAA,CAAe,IAAI;QAC1B;MACF;IAAA;IAGM/I,aAAA,uBAAe,MAAY;;MACjC,IAAI,KAAKsB,OAAA,IAAW,KAAK8H,YAAA,IAAgB,KAAKhK,MAAA,IAAU,KAAKU,UAAA,EAAY;QAClE,KAAA6I,SAAA,EACF,KAAK7G,aAAA,CAAc,CAAC,EAAEyE,OAAA,GAAU,KAAKzE,aAAA,CAAc,CAAC,EAAEyE,OAAA,IAAW,IACjE,KAAKzE,aAAA,CAAc,CAAC,EAAE0E,OAAA,GAAU,KAAK1E,aAAA,CAAc,CAAC,EAAE0E,OAAA,IAAW;QAEhE,IAAAmG,aAAA;QAEA,SAAKvC,MAAA,IAAUvM,KAAA,CAAMQ,OAAA,EAAS;UAC3B,KAAAyH,aAAA,CAAcjI,KAAA,CAAMQ,OAAA,EAAS,IAAI;UACtC,KAAKiO,oBAAA,GAAuB,KAAKE,sBAAA;QACnC;QAGK,KAAAA,sBAAA,GACH,KAAKD,QAAA,CAAS,KAAKzK,aAAA,CAAc,CAAC,GAAG,KAAKA,aAAA,CAAc,CAAC,CAAC,IAC1D,KAAKyK,QAAA,CAAS,KAAK3K,WAAA,CAAY,CAAC,GAAG,KAAKA,WAAA,CAAY,CAAC,CAAC;QAEpD,KAAC,KAAKsE,SAAA,EAAW;UACnByG,aAAA,GAAgB,IAAIC,OAAA,CAAU,EAAApF,qBAAA,CAAsB,KAAKyD,iBAAiB;QAAA,WACjE,KAAK7L,MAAA,EAAQ;UACjB,KAAA4L,KAAA,CAAMxD,qBAAA,CAAsB,KAAKyD,iBAAiB;UACvC0B,aAAA,IAAAtG,EAAA,QAAKC,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,MAA1E,gBAAAuG,EAAA,CACZI,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAA,EAC7BC,cAAA,CAAe,IAAI,KAAKvH,MAAA,CAAOwH,IAAA,EAC/BC,GAAA,CAAI,KAAKmE,KAAA;QACd;QAEA,MAAMP,MAAA,GAAS7C,SAAA,CAAUK,OAAA,IAAW,KAAKqE,oBAAA,GAAuB,KAAKE,sBAAA;QAErE,IAAIG,aAAA,KAAkB,QAAW;UAC/B,KAAK1F,oBAAA,CAAqB,KAAK4F,OAAA,CAAQF,aAAA,EAAelC,MAAM,CAAC;QAC/D;QAEA,KAAKrJ,aAAA,CAAc7B,YAAY;MACjC;IAAA;IAGMS,aAAA,sBAAc,MAAY;MAC3B,KAAA8F,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;MACpC,KAAKiL,cAAA,CAAe,KAAK;MAEzB,KAAK3H,aAAA,CAAc1B,SAAS;IAAA;IAGtBM,aAAA,uBAAe,MAAY;MAC7B,SAAKsB,OAAA,IAAW,KAAKiE,UAAA,EAAY;QAEnC,KAAKnE,aAAA,CAAc3B,WAAW;QACzB,KAAAqG,aAAA,CAAcjI,KAAA,CAAMK,KAAA,EAAO,IAAI;QAE/B,KAAA4O,oBAAA,GAAuB,KAAKrJ,yBAAA,CAA0B,KAAK3B,aAAA,CAAc,CAAC,GAAG,KAAKA,aAAA,CAAc,CAAC,CAAC;QACvG,KAAKiL,sBAAA,GAAyB,KAAKD,oBAAA;QAEnC,KAAK/D,cAAA,CAAe,KAAK;MAC3B;IAAA;IAGM/I,aAAA,sBAAc,MAAY;;MAChC,IAAI,KAAKsB,OAAA,IAAW,KAAKiE,UAAA,IAAc,KAAKzF,UAAA,EAAY;QACjD,KAAA6I,SAAA,EACF,KAAK7G,aAAA,CAAc,CAAC,EAAEyE,OAAA,GAAU,KAAKzE,aAAA,CAAc,CAAC,EAAEyE,OAAA,IAAW,IACjE,KAAKzE,aAAA,CAAc,CAAC,EAAE0E,OAAA,GAAU,KAAK1E,aAAA,CAAc,CAAC,EAAE0E,OAAA,IAAW;QAEpE,MAAMsB,WAAA,GAAc;QAEhB,SAAKsC,MAAA,IAAUvM,KAAA,CAAMK,KAAA,EAAO;UAC9B,KAAK4O,oBAAA,GAAuB,KAAKC,sBAAA;UAC5B,KAAAjH,aAAA,CAAcjI,KAAA,CAAMK,KAAA,EAAO,IAAI;QACtC;QAEA,KAAK6O,sBAAA,GAAyBhH,IAAA,CAAK2E,GAAA,CACjC,KAAKjH,yBAAA,CAA0B,KAAK3B,aAAA,CAAc,CAAC,GAAG,KAAKA,aAAA,CAAc,CAAC,CAAC,GAC3EgG,WAAA,GAAc,KAAKpE,WAAA;QAEf,MAAA+G,MAAA,GAAS,KAAKsC,sBAAA,GAAyB,KAAKD,oBAAA;QAE9C,IAAA3G,UAAA;QAEA,KAAC,KAAKD,SAAA,EAAW;UACnBC,UAAA,GAAa,KAAKjG,OAAA,CAAQ4G,QAAA;QAAA,OACrB;UACD,SAAK1H,MAAA,YAAkBgH,kBAAA,EAAoB;YAChCD,UAAA,IAAAE,EAAA,QAAKC,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,MAA1E,gBAAAuG,EAAA,CACTI,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAA,EAC7BC,cAAA,CAAe,IAAI,KAAKvH,MAAA,CAAOwH,IAAA,EAC/BC,GAAA,CAAI,KAAK3G,OAAA,CAAQ4G,QAAA;UAAQ,WACnB,KAAK1H,MAAA,YAAkB2H,iBAAA,EAAmB;YACnDZ,UAAA,IAAaa,EAAA,QAAKV,kBAAA,CAAmB,KAAKlH,MAAA,EAAQJ,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,MAA1E,gBAAAkH,EAAA,CACTP,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAA,EAC7BG,GAAA,CAAI,KAAK3G,OAAA,CAAQ4G,QAAA;UACtB;QACF;QAEA,IAAIX,UAAA,KAAe,QAAW;UAC5B,KAAKc,oBAAA,CAAqB,KAAKC,UAAA,CAAWuD,MAAA,EAAQtE,UAAU,CAAC;QAC/D;QAEA,KAAK/E,aAAA,CAAc7B,YAAY;MACjC;IAAA;IAGMS,aAAA,qBAAa,MAAY;MAC1B,KAAA8F,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;MAEpC,KAAKsD,aAAA,CAAc1B,SAAS;IAAA;IAGtBM,aAAA,2BAAmB,MAAY;MACrC,IAAI,KAAKsB,OAAA,IAAW,KAAKiE,UAAA,IAAc,KAAKzF,UAAA,EAAY;QAEtD,KAAKsB,aAAA,CAAc3B,WAAW;QAEzB,KAAAqG,aAAA,CAAcjI,KAAA,CAAMK,KAAA,EAAO,IAAI;QAGpC,IAAIqI,OAAA,GAAU;QACd,IAAIC,OAAA,GAAU;QACR,MAAAwG,QAAA,GAAW,KAAKlL,aAAA,CAAcL,MAAA;QAEpC,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAIyL,QAAA,EAAUzL,CAAA,IAAK;UACtBgF,OAAA,SAAKzE,aAAA,CAAcP,CAAC,EAAEgF,OAAA;UACtBC,OAAA,SAAK1E,aAAA,CAAcP,CAAC,EAAEiF,OAAA;QACnC;QAEA,KAAKmC,SAAA,CAAUpC,OAAA,GAAUyG,QAAA,EAAUxG,OAAA,GAAUwG,QAAQ;QAErD,KAAK/D,oBAAA,CAAqBc,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;QAC1F,KAAA+K,sBAAA,CAAuBf,IAAA,CAAK,KAAKD,oBAAoB;MAC5D;IAAA;IAGMjJ,aAAA,0BAAkB,MAAY;MACpC,IAAI,KAAKsB,OAAA,IAAW,KAAKiE,UAAA,IAAc,KAAKnG,MAAA,IAAU,KAAKU,UAAA,EAAY;QAYrE,IAAIyG,OAAA,GAAU;QACd,IAAIC,OAAA,GAAU;QACR,MAAAwG,QAAA,GAAW,KAAKlL,aAAA,CAAcL,MAAA;QAEpC,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAIyL,QAAA,EAAUzL,CAAA,IAAK;UACtBgF,OAAA,SAAKzE,aAAA,CAAcP,CAAC,EAAEgF,OAAA;UACtBC,OAAA,SAAK1E,aAAA,CAAcP,CAAC,EAAEiF,OAAA;QACnC;QAEA,KAAKmC,SAAA,CAAUpC,OAAA,GAAUyG,QAAA,EAAUxG,OAAA,GAAUwG,QAAQ;QAErD,MAAMlC,aAAA,GAAgB;QACtB,KAAKb,sBAAA,CAAuBF,IAAA,CAAK,KAAKC,YAAA,CAAahL,OAAA,CAAQC,CAAA,EAAGD,OAAA,CAAQE,CAAA,EAAG,KAAKY,UAAU,EAAEZ,CAAA,GAAI,GAAG;QAEjG,MAAMsE,QAAA,GAAW,KAAKyG,sBAAA,CAAuB/K,CAAA,GAAI,KAAK+J,oBAAA,CAAqB/J,CAAA;QAE3E,IAAI0G,IAAA,GAAO;QAEX,IAAIpC,QAAA,GAAW,GAAG;UAChBoC,IAAA,GAAO,IAAIG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAa,CAACrC,QAAA,GAAWsH,aAAa;QAAA,WACtDtH,QAAA,GAAW,GAAG;UACvBoC,IAAA,GAAOG,IAAA,CAAKC,GAAA,CAAI,KAAKH,WAAA,EAAarC,QAAA,GAAWsH,aAAa;QAC5D;QAEK,KAAAvD,KAAA,CAAMC,qBAAA,CAAsB,KAAKC,kBAAkB;QACxD,MAAMxI,CAAA,GAAI,KAAKsI,KAAA,CAAMG,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;QACrD,IAAIa,IAAA,GAAO1I,CAAA,GAAI2G,IAAA;QAGf+B,IAAA,GAAOC,SAAA,CAAUC,KAAA,CAAMF,IAAA,EAAM,KAAKG,WAAA,EAAa,KAAKC,WAAW;QAEzD,MAAA7I,CAAA,GAAID,CAAA,GAAI8G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,GAAU,KAAK8C,SAAA,GAAY,GAAG;QAG/D,IAAI5C,MAAA,GAASP,SAAA,CAAUQ,OAAA,IAAWrC,IAAA,CAAKsC,IAAA,CAAKnJ,CAAA,GAAIyI,IAAI,IAAI;QAGxDQ,MAAA,GAASP,SAAA,CAAUC,KAAA,CAAMM,MAAA,EAAQ,KAAKI,MAAA,EAAQ,KAAKD,MAAM;QAEzD,MAAME,WAAA,GAActJ,CAAA,GAAI6G,IAAA,CAAKiC,GAAA,CAAIJ,SAAA,CAAUK,OAAA,IAAWE,MAAA,GAAS,EAAE;QACjEvC,IAAA,GAAO3G,CAAA,GAAIuJ,WAAA;QACN,KAAAwC,KAAA,CAAMxD,qBAAA,CAAsB,KAAKyD,iBAAiB;QAEvD,KAAKxC,MAAA,CAAON,MAAM;QAClB,KAAKlB,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAKoF,KAAA,EAAO,KAAK,CAAC;QAGlE,MAAME,SAAA,GAAY,KAAKhL,OAAA,CAAQ4G,QAAA,CAC5BqE,KAAA,CACA,EAAAC,GAAA,CAAI,KAAKhM,MAAA,CAAO0H,QAAQ,EACxBuE,SAAA,GACA1E,cAAA,CAAe6B,WAAA,GAAcvJ,CAAC;QACjC,KAAKqM,KAAA,CAAMC,eAAA,CAAgBL,SAAA,CAAUjM,CAAA,EAAGiM,SAAA,CAAUhM,CAAA,EAAGgM,SAAA,CAAU/K,CAAC;QAGhE,KAAKiB,aAAA,CAAc7B,YAAY;MACjC;IAAA;IAGMS,aAAA,yBAAiB,MAAY;MAC9B,KAAA8F,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;MAEpC,KAAKsD,aAAA,CAAc1B,SAAS;IAAA;IAStB;AAAA;AAAA;AAAA;AAAA;IAAAM,aAAA,oBAAY,CAACuG,OAAA,EAAiBC,OAAA,KAA0B;MAC9DxH,OAAA,CAAQC,CAAA,GAAIsH,OAAA;MACZvH,OAAA,CAAQE,CAAA,GAAIsH,OAAA;IAAA;IAMN;AAAA;AAAA;IAAAxG,aAAA,iCAAyB,MAAY;MACtC,KAAAiN,cAAA,CAAe,OAAO,GAAG,MAAM;MAC/B,KAAAA,cAAA,CAAe,OAAO,CAAC;MAEvB,KAAAA,cAAA,CAAe,UAAU,CAAC;MAE1B,KAAAA,cAAA,CAAe,QAAQ,OAAO;MAC9B,KAAAA,cAAA,CAAe,QAAQ,CAAC;MAExB,KAAAA,cAAA,CAAe,OAAO,SAAS,OAAO;MACtC,KAAAA,cAAA,CAAe,OAAO,GAAG,OAAO;IAAA;IAU/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAjN,aAAA,yBAAiB,CAAC0I,SAAA,EAAsBhH,KAAA,EAAwBwL,GAAA,GAA0B,SAAkB;MAClH,MAAMC,cAAA,GAAiB,CAAC,OAAO,UAAU,QAAQ,KAAK;MACtD,MAAMC,UAAA,GAAa,CAAC,GAAG,GAAG,GAAG,OAAO;MACpC,MAAMC,QAAA,GAAW,CAAC,QAAQ,SAAS,IAAI;MACnC,IAAAC,KAAA;MAEJ,IAAI,CAACH,cAAA,CAAeI,QAAA,CAAS7E,SAAS,KAAK,CAAC0E,UAAA,CAAWG,QAAA,CAAS7L,KAAK,KAAK,CAAC2L,QAAA,CAASE,QAAA,CAASL,GAAG,GAAG;QAE1F;MACT;MAEA,IAAIxL,KAAA,IAAS,SAAS;QAChB,IAAAgH,SAAA,IAAa,UAAUA,SAAA,IAAa,OAAO;UAEtC;QACT;MACF;MAEA,QAAQA,SAAA;QACN,KAAK;UACH4E,KAAA,GAAQzP,KAAA,CAAMI,GAAA;UACd;QAEF,KAAK;UACHqP,KAAA,GAAQzP,KAAA,CAAMG,MAAA;UACd;QAEF,KAAK;UACHsP,KAAA,GAAQzP,KAAA,CAAMK,KAAA;UACd;QAEF,KAAK;UACHoP,KAAA,GAAQzP,KAAA,CAAMM,GAAA;UACd;MACJ;MAEA,MAAMqP,MAAA,GAAS;QACb9E,SAAA;QACAhH,KAAA;QACAwL,GAAA;QACAI;MAAA;MAGF,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;QACjD,IAAI,KAAKC,YAAA,CAAaD,CAAC,EAAEG,KAAA,IAAS8L,MAAA,CAAO9L,KAAA,IAAS,KAAKF,YAAA,CAAaD,CAAC,EAAE2L,GAAA,IAAOM,MAAA,CAAON,GAAA,EAAK;UACxF,KAAK1L,YAAA,CAAaK,MAAA,CAAON,CAAA,EAAG,GAAGiM,MAAM;UAC9B;QACT;MACF;MAEK,KAAAhM,YAAA,CAAaY,IAAA,CAAKoL,MAAM;MACtB;IAAA;IASD;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAxN,aAAA,0BAAkB,CAAC0B,KAAA,EAAwBwL,GAAA,KAA8C;MAC3F,IAAAM,MAAA;MAEJ,SAASjM,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;QACxCiM,MAAA,QAAKhM,YAAA,CAAaD,CAAC;QAC5B,IAAIiM,MAAA,CAAO9L,KAAA,IAASA,KAAA,IAAS8L,MAAA,CAAON,GAAA,IAAOA,GAAA,EAAK;UAC9C,OAAOM,MAAA,CAAO9E,SAAA;QAChB;MACF;MAEA,IAAIwE,GAAA,EAAK;QACP,SAAS3L,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;UACxCiM,MAAA,QAAKhM,YAAA,CAAaD,CAAC;UAC5B,IAAIiM,MAAA,CAAO9L,KAAA,IAASA,KAAA,IAAS8L,MAAA,CAAON,GAAA,IAAO,MAAM;YAC/C,OAAOM,MAAA,CAAO9E,SAAA;UAChB;QACF;MACF;MAEO;IAAA;IASD;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA1I,aAAA,+BAAuB,CAAC0B,KAAA,EAAwBwL,GAAA,KAA2C;MAC7F,IAAAM,MAAA;MAEJ,SAASjM,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;QACxCiM,MAAA,QAAKhM,YAAA,CAAaD,CAAC;QAC5B,IAAIiM,MAAA,CAAO9L,KAAA,IAASA,KAAA,IAAS8L,MAAA,CAAON,GAAA,IAAOA,GAAA,EAAK;UAC9C,OAAOM,MAAA,CAAOF,KAAA;QAChB;MACF;MAEA,IAAIJ,GAAA,EAAK;QACP,SAAS3L,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKC,YAAA,CAAaC,MAAA,EAAQF,CAAA,IAAK;UACxCiM,MAAA,QAAKhM,YAAA,CAAaD,CAAC;UAC5B,IAAIiM,MAAA,CAAO9L,KAAA,IAASA,KAAA,IAAS8L,MAAA,CAAON,GAAA,IAAO,MAAM;YAC/C,OAAOM,MAAA,CAAOF,KAAA;UAChB;QACF;MACF;MAEO;IAAA;IASD;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAtN,aAAA,mBAAW,CAACyN,EAAA,EAAkBC,EAAA,KAA6B;MACjE,OAAQ3H,IAAA,CAAK4H,KAAA,CAAMD,EAAA,CAAGlH,OAAA,GAAUiH,EAAA,CAAGjH,OAAA,EAASkH,EAAA,CAAGnH,OAAA,GAAUkH,EAAA,CAAGlH,OAAO,IAAI,MAAOR,IAAA,CAAK6H,EAAA;IAAA;IAO7E;AAAA;AAAA;AAAA;IAAA5N,aAAA,2BAAoBqB,KAAA,IAA8B;MACxD,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKO,aAAA,CAAcL,MAAA,EAAQF,CAAA,IAAK;QAClD,IAAI,KAAKO,aAAA,CAAcP,CAAC,EAAE6C,SAAA,IAAa/C,KAAA,CAAM+C,SAAA,EAAW;UACtD,KAAKtC,aAAA,CAAcD,MAAA,CAAON,CAAA,EAAG,GAAGF,KAAK;UACrC;QACF;MACF;IAAA;IAkFM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAArB,aAAA,gCAAwB,CAAC6N,EAAA,EAAYJ,EAAA,EAAYK,EAAA,EAAYC,EAAA,KAAuB;MAC1F,MAAMC,CAAA,GAAIP,EAAA,GAAKI,EAAA;MACT,MAAAhC,CAAA,IAAKkC,EAAA,GAAKD,EAAA,IAAM;MACtB,IAAIjC,CAAA,IAAK,GAAG;QACH;MACT;MAEA,OAAOmC,CAAA,GAAInC,CAAA;IAAA;IASL;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA7L,aAAA,oCAA4B,CAAC6N,EAAA,EAAkBJ,EAAA,KAA6B;MAClF,OAAO1H,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAIyH,EAAA,CAAGlH,OAAA,GAAUsH,EAAA,CAAGtH,OAAA,EAAS,CAAC,IAAIR,IAAA,CAAKC,GAAA,CAAIyH,EAAA,CAAGjH,OAAA,GAAUqH,EAAA,CAAGrH,OAAA,EAAS,CAAC,CAAC;IAAA;IAStF;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAxG,aAAA,gCAAwB,CAACkO,IAAA,EAAeC,IAAA,KAA2B;MACpE,KAAAC,eAAA,CAAgBC,eAAA,CAAgB,KAAK5G,kBAAkB;MACvD,KAAA6G,KAAA,CAAMC,qBAAA,CAAsB,KAAKH,eAAe;MAErD,KAAK1B,aAAA,CAAc8B,YAAA,CAAaN,IAAA,EAAMC,IAAI,EAAE1H,eAAA,CAAgB,KAAK6H,KAAK;MACtE,OAAO,KAAK5B,aAAA,CAAcrB,SAAA,CAAU,EAAEF,KAAA,CAAM;IAAA;IAQtC;AAAA;AAAA;AAAA;AAAA;IAAAnL,aAAA,4BAAqBZ,MAAA,IAAuC;MAClE,MAAMqP,MAAA,GAAS;MACf,MAAMnE,QAAA,GAAWlL,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;MAEjE,IAAI1H,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,MAAM2H,QAAA,GAAW9G,SAAA,CAAUK,OAAA,GAAU7I,MAAA,CAAO8I,GAAA,GAAM;QAC5C,MAAAyG,QAAA,GAAW5I,IAAA,CAAKsC,IAAA,CAAKjJ,MAAA,CAAOwP,MAAA,GAAS7I,IAAA,CAAKiC,GAAA,CAAI0G,QAAQ,CAAC;QACtD,OAAA3I,IAAA,CAAKiC,GAAA,CAAIjC,IAAA,CAAKiG,GAAA,CAAI0C,QAAA,EAAUC,QAAQ,CAAC,IAAIrE,QAAA,GAAWmE,MAAA;MAAA,WAClDrP,MAAA,YAAkBgH,kBAAA,EAAoB;QAC/C,OAAOL,IAAA,CAAKiG,GAAA,CAAI5M,MAAA,CAAOyP,GAAA,EAAKzP,MAAA,CAAO0P,KAAK,IAAIL,MAAA;MAC9C;IAAA;IASM;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAzO,aAAA,gBAAQ,CAAC+O,KAAA,EAAgBnJ,IAAA,EAAc6E,MAAA,GAAS,MAAY;MAClE,IAAI,KAAKrL,MAAA,EAAQ;QACT,MAAA4P,UAAA,GAAaD,KAAA,CAAM5D,KAAA;QAGzB6D,UAAA,CAAW5D,GAAA,CAAI,KAAKlL,OAAA,CAAQ4G,QAAQ,EAAEH,cAAA,CAAe8D,MAAM;QAC3D,KAAKwE,kBAAA,CAAmB1D,eAAA,CAAgByD,UAAA,CAAW/P,CAAA,EAAG+P,UAAA,CAAW9P,CAAA,EAAG8P,UAAA,CAAW7O,CAAC;QAE1E,MAAA+O,cAAA,GAAiB,KAAKjE,iBAAA,CAAkBE,KAAA,CAAM;QAC/C,KAAAF,iBAAA,CAAkBkE,WAAA,CAAY,KAAKF,kBAAkB;QACrD,KAAAhE,iBAAA,CAAkBmE,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;QAE7F,MAAAoP,eAAA,GAAkB,KAAK5H,kBAAA,CAAmB0D,KAAA,CAAM;QACjD,KAAA1D,kBAAA,CAAmB0H,WAAA,CAAY,KAAKF,kBAAkB;QACtD,KAAAxH,kBAAA,CAAmB2H,SAAA,CAAU,KAAKhQ,MAAA,CAAO0H,QAAA,EAAU,KAAK1H,MAAA,CAAOsH,UAAA,EAAY,KAAKtH,MAAA,CAAOa,KAAK;QAGjG,IAAI,KAAKsF,UAAA,EAAY;UACnB,KAAK0B,oBAAA,CAAqB,KAAKC,UAAA,CAAWtB,IAAA,EAAM,KAAK1F,OAAA,CAAQ4G,QAAQ,CAAC;QACxE;QAEK,KAAAmE,iBAAA,CAAkB/B,IAAA,CAAKgG,cAAc;QACrC,KAAAzH,kBAAA,CAAmByB,IAAA,CAAKmG,eAAe;MAC9C;IAAA;IAMM;AAAA;AAAA;IAAArP,aAAA,mBAAW,MAAY;MAC7B,IAAI,KAAKD,KAAA,EAAO;QACd,MAAMuP,KAAA,GAAQ;QACd,MAAMC,UAAA,GAAa;QACf,IAAA3J,IAAA,EAAM4J,SAAA,EAAWC,SAAA,EAAWC,IAAA;QAE5B,SAAKtQ,MAAA,YAAkBgH,kBAAA,EAAoB;UAC7C,MAAMuJ,KAAA,GAAQ,KAAKvQ,MAAA,CAAO0P,KAAA,GAAQ,KAAK1P,MAAA,CAAOwQ,IAAA;UAC9C,MAAMC,MAAA,GAAS,KAAKzQ,MAAA,CAAO0Q,MAAA,GAAS,KAAK1Q,MAAA,CAAOyP,GAAA;UAEpCY,SAAA,GAAA1J,IAAA,CAAK2E,GAAA,CAAIiF,KAAA,EAAOE,MAAM;UAClCH,IAAA,GAAOD,SAAA,GAAY;UAEX7J,IAAA,GAAA6J,SAAA,GAAY,KAAKrQ,MAAA,CAAOwH,IAAA,GAAQ2I,UAAA;UAC3BC,SAAA,GAAA5J,IAAA,GAAO8J,IAAA,GAAQ,KAAKtQ,MAAA,CAAOwH,IAAA;QAAA,WAC/B,KAAKxH,MAAA,YAAkB2H,iBAAA,EAAmB;UACnD,MAAMuD,QAAA,GAAW,KAAKlL,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;UACtE,MAAM4H,QAAA,GAAW9G,SAAA,CAAUK,OAAA,GAAU,KAAK7I,MAAA,CAAO8I,GAAA,GAAM;UACjD,MAAAyG,QAAA,GAAW5I,IAAA,CAAKsC,IAAA,CAAK,KAAKjJ,MAAA,CAAOwP,MAAA,GAAS7I,IAAA,CAAKiC,GAAA,CAAI0G,QAAQ,CAAC;UAEtDe,SAAA,GAAA1J,IAAA,CAAKiC,GAAA,CAAIjC,IAAA,CAAK2E,GAAA,CAAIgE,QAAA,EAAUC,QAAQ,CAAC,IAAIrE,QAAA,GAAW;UAChEoF,IAAA,GAAOD,SAAA,GAAY;UAEnB7J,IAAA,GAAO6J,SAAA,GAAYF,UAAA;UACnBC,SAAA,GAAY5J,IAAA,GAAO8J,IAAA;QACrB;QAEA,IAAI,KAAKvI,KAAA,IAAS,QAAQ,KAAK/H,MAAA,EAAQ;UACrC,KAAK+H,KAAA,GAAQ,IAAI4I,UAAA,CAAWnK,IAAA,EAAM4J,SAAA,EAAWF,KAAA,EAAOA,KAAK;UACzD,KAAKnI,KAAA,CAAML,QAAA,CAASoC,IAAA,CAAK,KAAKhJ,OAAA,CAAQ4G,QAAQ;UAC9C,KAAKkJ,aAAA,CAAc9G,IAAA,CAAK,KAAK/B,KAAA,CAAML,QAAQ;UAC3C,KAAKK,KAAA,CAAMT,UAAA,CAAWwC,IAAA,CAAK,KAAK9J,MAAA,CAAOsH,UAAU;UACjD,KAAKS,KAAA,CAAM8I,OAAA,CAAQlK,IAAA,CAAK6H,EAAA,GAAK,GAAG;UAE3B,KAAA7N,KAAA,CAAM8G,GAAA,CAAI,KAAKM,KAAK;QAC3B;MACF;IAAA;IAGKnH,aAAA,kBAAWF,UAAA,IAAkC;MAGlD,IAAKA,UAAA,KAAuBoQ,QAAA,EAAU;QAC5BC,OAAA,CAAAC,KAAA,CACN;MAEJ;MACA,KAAKtQ,UAAA,GAAaA,UAAA;MAIb,KAAAA,UAAA,CAAWuQ,KAAA,CAAMC,WAAA,GAAc;MACpC,KAAKxQ,UAAA,CAAW0C,gBAAA,CAAiB,eAAe,KAAK+N,aAAa;MAClE,KAAKzQ,UAAA,CAAW0C,gBAAA,CAAiB,eAAe,KAAKgO,aAAa;MAClE,KAAK1Q,UAAA,CAAW0C,gBAAA,CAAiB,iBAAiB,KAAKiO,eAAe;MACtE,KAAK3Q,UAAA,CAAW0C,gBAAA,CAAiB,SAAS,KAAKkO,OAAO;IAAA;IAMjD;AAAA;AAAA;IAAA1Q,aAAA,kBAAU,MAAY;;MACvB,SAAK4I,YAAA,IAAgB,IAAI;QACpBrG,MAAA,CAAAsG,oBAAA,CAAqB,KAAKD,YAAY;MAC/C;MAEA,CAAAvC,EAAA,QAAKvG,UAAA,KAAL,gBAAAuG,EAAA,CAAiBhC,mBAAA,CAAoB,eAAe,KAAKmM,aAAA;MACzD,CAAAxJ,EAAA,QAAKlH,UAAA,KAAL,gBAAAkH,EAAA,CAAiB3C,mBAAA,CAAoB,iBAAiB,KAAKoM,eAAA;MAC3D,CAAAE,EAAA,QAAK7Q,UAAA,KAAL,gBAAA6Q,EAAA,CAAiBtM,mBAAA,CAAoB,SAAS,KAAKqM,OAAA;MACnD,CAAAE,EAAA,QAAK9Q,UAAA,KAAL,gBAAA8Q,EAAA,CAAiBvM,mBAAA,CAAoB,eAAe,KAAKkM,aAAA;MAElDhO,MAAA,CAAA8B,mBAAA,CAAoB,eAAe,KAAK5B,aAAa;MACrDF,MAAA,CAAA8B,mBAAA,CAAoB,aAAa,KAAK3B,WAAW;MAEjDH,MAAA,CAAA8B,mBAAA,CAAoB,UAAU,KAAKwM,cAAc;MAEnD,CAAAC,EAAA,QAAA/Q,KAAA,qBAAA+Q,EAAA,CAAOC,MAAA,CAAO,KAAK7Q,OAAA;MACxB,KAAKkH,WAAA,CAAY;IAAA;IAMX;AAAA;AAAA;IAAApH,aAAA,sBAAc,MAAY;MAC5B,SAAKmH,KAAA,IAAS,KAAKpH,KAAA,EAAO;QACvB,KAAAA,KAAA,CAAMgR,MAAA,CAAO,KAAK5J,KAAK;QAC5B,KAAKA,KAAA,GAAQ;MACf;IAAA;IAQM;AAAA;AAAA;AAAA;AAAA;IAAAnH,aAAA,uBAAgB6L,CAAA,IAAsB;MAC5C,OAAO,IAAI9F,IAAA,CAAKC,GAAA,CAAI,IAAI6F,CAAA,EAAG,CAAC;IAAA;IAOtB;AAAA;AAAA;AAAA;IAAA7L,aAAA,yBAAkBgR,QAAA,IAA4B;MACzC,WAAAhQ,KAAA,IAAS,KAAKd,OAAA,CAAQe,QAAA,EAAU;QACvCD,KAAA,CAAyCiQ,QAAA,CAASC,SAAA,CAAU;UAAEC,OAAA,EAASH,QAAA,GAAW,IAAI;QAAA,CAAK;MAC/F;IAAA;IAUM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAhR,aAAA,uBAAe,CAACoR,OAAA,EAAiBC,OAAA,EAAiBC,MAAA,KAAiC;MACnF,MAAAC,UAAA,GAAaD,MAAA,CAAOE,qBAAA;MACrB,KAAAC,KAAA,CAAMC,IAAA,EAAON,OAAA,GAAUG,UAAA,CAAW3B,IAAA,IAAQ2B,UAAA,CAAW5B,KAAA,GAAS,IAAI,CAAC;MACnE,KAAA8B,KAAA,CAAM1H,IAAA,EAAOwH,UAAA,CAAWzB,MAAA,GAASuB,OAAA,IAAWE,UAAA,CAAW1B,MAAA,GAAU,IAAI,CAAC;MACpE,YAAK4B,KAAA,CAAMtG,KAAA;IAAM;IAUlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAnL,aAAA,4BAAoB,CAACoR,OAAA,EAAiBC,OAAA,EAAiBC,MAAA,KAAiC;MAC9F,KAAKG,KAAA,CAAMvI,IAAA,CAAK,KAAKc,YAAA,CAAaoH,OAAA,EAASC,OAAA,EAASC,MAAM,CAAC;MACvD,SAAKlS,MAAA,YAAkBgH,kBAAA,EAAoB;QAC7C,KAAKqL,KAAA,CAAMxS,CAAA,KAAM,KAAKG,MAAA,CAAO0P,KAAA,GAAQ,KAAK1P,MAAA,CAAOwQ,IAAA,IAAQ;QACzD,KAAK6B,KAAA,CAAMvS,CAAA,KAAM,KAAKE,MAAA,CAAOyP,GAAA,GAAM,KAAKzP,MAAA,CAAO0Q,MAAA,IAAU;MAC3D;MACO,YAAK2B,KAAA,CAAMtG,KAAA;IAAM;IAOlB;AAAA;AAAA;AAAA;IAAAnL,aAAA,oBAAaZ,MAAA,IAAgC;MACnD,IAAIA,MAAA,EAAQ;QACHA,MAAA,CAAAuS,MAAA,CAAO,KAAKC,MAAM;QACzBxS,MAAA,CAAOyS,YAAA,CAAa;QAGpB,IAAIzS,MAAA,YAAkB2H,iBAAA,EAAmB;UACvC,KAAK+K,KAAA,GAAQ1S,MAAA,CAAO8I,GAAA;UACpB,KAAK6C,SAAA,GAAY3L,MAAA,CAAO8I,GAAA;QAC1B;QAEK,KAAA6J,mBAAA,CAAoB7I,IAAA,CAAK9J,MAAA,CAAO4S,MAAM;QACtC,KAAAvK,kBAAA,CAAmByB,IAAA,CAAK,KAAK6I,mBAAmB;QAChD,KAAAE,sBAAA,CAAuB/I,IAAA,CAAK9J,MAAA,CAAO8S,gBAAgB;QACxD,KAAKC,MAAA,GAAS/S,MAAA,CAAOwH,IAAA;QACrB,KAAKwL,UAAA,GAAa,KAAKD,MAAA;QAEvB,KAAKE,YAAA,GAAejT,MAAA,CAAOkT,IAAA;QAC3B,KAAKC,SAAA,GAAYnT,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKkK,MAAM,IAAIxS,MAAA,CAAOkT,IAAA;QAClE,KAAKE,QAAA,GAAW,KAAKH,YAAA;QAErB,KAAKI,WAAA,GAAcrT,MAAA,CAAOsT,GAAA;QAC1B,KAAKC,QAAA,GAAWvT,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKkK,MAAM,IAAIxS,MAAA,CAAOsT,GAAA;QACjE,KAAKE,OAAA,GAAU,KAAKH,WAAA;QAEf,KAAAI,IAAA,CAAK3J,IAAA,CAAK9J,MAAA,CAAO0T,EAAE;QACnB,KAAAC,QAAA,CAAS7J,IAAA,CAAK9J,MAAA,CAAO0T,EAAE;QAE5B,KAAK1T,MAAA,GAASA,MAAA;QAEd,KAAKA,MAAA,CAAO4T,sBAAA;QAGN,MAAA5S,QAAA,GAAW,KAAKC,iBAAA,CAAkBjB,MAAM;QAC9C,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QACA,KAAK6S,UAAA,CAAW,KAAKrB,MAAA,EAAQ,KAAKtR,SAAS;MAC7C;IAAA;IAkBM;AAAA;AAAA;AAAA;AAAA;IAAAN,aAAA,qBAAa,CAACkT,QAAA,EAAmB9S,QAAA,KAA2B;MAElE,MAAMI,KAAA,GAAQ,IAAIC,YAAA,CAAa,GAAG,GAAGL,QAAA,EAAUA,QAAQ;MACvD,MAAMM,MAAA,GAASF,KAAA,CAAMG,SAAA,CAAU,KAAKC,SAAS;MAG7C,MAAMC,aAAA,GAAgB,IAAIC,cAAA,CAAe,EAAEC,aAAA,CAAcL,MAAM;MAG/D,MAAMyS,cAAA,GAAiB,IAAIC,iBAAA,CAAkB;QAAE9D,KAAA,EAAO;QAAU+D,GAAA,EAAK;QAAOC,WAAA,EAAa;QAAMnC,OAAA,EAAS;MAAK;MAC7G,MAAMoC,cAAA,GAAiB,IAAIH,iBAAA,CAAkB;QAAE9D,KAAA,EAAO;QAAU+D,GAAA,EAAK;QAAOC,WAAA,EAAa;QAAMnC,OAAA,EAAS;MAAK;MAC7G,MAAMqC,cAAA,GAAiB,IAAIJ,iBAAA,CAAkB;QAAE9D,KAAA,EAAO;QAAU+D,GAAA,EAAK;QAAOC,WAAA,EAAa;QAAMnC,OAAA,EAAS;MAAK;MAG7G,MAAMsC,MAAA,GAAS,IAAIC,IAAA,CAAK7S,aAAA,EAAesS,cAAc;MACrD,MAAMQ,MAAA,GAAS,IAAID,IAAA,CAAK7S,aAAA,EAAe0S,cAAc;MACrD,MAAMK,MAAA,GAAS,IAAIF,IAAA,CAAK7S,aAAA,EAAe2S,cAAc;MAE/C,MAAAK,QAAA,GAAW9N,IAAA,CAAK6H,EAAA,GAAK;MAC3B6F,MAAA,CAAOI,QAAA,CAAS5U,CAAA,GAAI4U,QAAA;MACpBF,MAAA,CAAOE,QAAA,CAAS3U,CAAA,GAAI2U,QAAA;MAGpB,KAAKC,kBAAA,CAAmBC,QAAA,CAAW,EAAAC,WAAA,CAAYd,QAAQ;MAClD,KAAAjI,iBAAA,CAAkB/B,IAAA,CAAK,KAAK4K,kBAAkB;MAEnD,IAAI,KAAK1U,MAAA,IAAU,KAAKA,MAAA,CAAOwH,IAAA,IAAQ,GAAG;QAElC,MAAAhB,IAAA,GAAO,IAAI,KAAKxG,MAAA,CAAOwH,IAAA;QAC7B,KAAKqN,YAAA,CAAaC,SAAA,CAAUtO,IAAA,EAAMA,IAAA,EAAMA,IAAI;QACvC,KAAAqJ,kBAAA,CAAmB1D,eAAA,CAAgB,CAAC2H,QAAA,CAASjU,CAAA,EAAG,CAACiU,QAAA,CAAShU,CAAA,EAAG,CAACgU,QAAA,CAAS/S,CAAC;QAE7E,KAAK8K,iBAAA,CAAkBkE,WAAA,CAAY,KAAKF,kBAAkB,EAAEE,WAAA,CAAY,KAAK8E,YAAY;QACzF,KAAKhF,kBAAA,CAAmB1D,eAAA,CAAgB2H,QAAA,CAASjU,CAAA,EAAGiU,QAAA,CAAShU,CAAA,EAAGgU,QAAA,CAAS/S,CAAC;QACrE,KAAA8K,iBAAA,CAAkBkE,WAAA,CAAY,KAAKF,kBAAkB;MAC5D;MAEK,KAAAhE,iBAAA,CAAkBmE,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;MAEnG,KAAKC,OAAA,CAAQiU,KAAA;MAER,KAAAjU,OAAA,CAAQ2G,GAAA,CAAI4M,MAAM;MAClB,KAAAvT,OAAA,CAAQ2G,GAAA,CAAI8M,MAAM;MAClB,KAAAzT,OAAA,CAAQ2G,GAAA,CAAI+M,MAAM;IAAA;IAUjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA5T,aAAA,sBAAc,CAACoU,IAAA,EAAcrF,KAAA,EAAgBsF,YAAA,EAAuBC,WAAA,KAA+B;MACrG,SAAKxL,UAAA,IAAc,IAAI;QAEzB,KAAKA,UAAA,GAAasL,IAAA;MACpB;MAEI,SAAKhK,MAAA,IAAUvM,KAAA,CAAMU,eAAA,EAAiB;QAClC,MAAAiN,SAAA,GAAY4I,IAAA,GAAO,KAAKtL,UAAA;QACxB,MAAAyL,QAAA,GAAW/I,SAAA,GAAY,KAAKgJ,kBAAA;QAE7B,KAAAvJ,iBAAA,CAAkB/B,IAAA,CAAKoL,WAAW;QAEvC,IAAIC,QAAA,IAAY,GAAG;UAGZ,KAAAtJ,iBAAA,CAAkBmE,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;UAE9F,KAAAoM,KAAA,CAAM0C,KAAA,EAAO,KAAKlJ,WAAW;UAElC,KAAKiD,UAAA,GAAa;UACb,KAAAhD,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;UACpC,KAAKiL,cAAA,CAAe,KAAK;UAGzB,KAAK3H,aAAA,CAAc7B,YAAY;QAAA,OAC1B;UACC,MAAAkL,MAAA,GAAS,KAAKgK,YAAA,CAAaF,QAAQ;UACzC,MAAM3O,IAAA,GAAO,IAAI6E,MAAA,GAAS,KAAK5E,WAAA,GAAc4E,MAAA;UAExC,KAAAQ,iBAAA,CAAkBmE,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;UAC9F,KAAAoM,KAAA,CAAM0C,KAAA,EAAOnJ,IAAA,EAAM6E,MAAM;UAG9B,KAAKrJ,aAAA,CAAc7B,YAAY;UAC/B,MAAMoM,IAAA,GAAO;UACb,KAAK/C,YAAA,GAAerG,MAAA,CAAOqJ,qBAAA,CAAsB,UAAUC,CAAA,EAAG;YAC5DF,IAAA,CAAKS,WAAA,CAAYP,CAAA,EAAGkD,KAAA,EAAOsF,YAAA,EAAcC,WAAA,CAAYnJ,KAAA,EAAO;UAAA,CAC7D;QACH;MAAA,OACK;QAGL,KAAKvC,YAAA,GAAe;QACpB,KAAKE,UAAA,GAAa;MACpB;IAAA;IASM;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA9I,aAAA,yBAAiB,CAACoU,IAAA,EAActI,YAAA,EAAuB4I,EAAA,KAAqB;MAC9E,SAAK5L,UAAA,IAAc,IAAI;QAEzB,KAAKY,UAAA,GAAa;QAClB,KAAKD,aAAA,GAAgB;QACrB,KAAKX,UAAA,GAAasL,IAAA;MACpB;MAEI,SAAKhK,MAAA,IAAUvM,KAAA,CAAMW,gBAAA,EAAkB;QAEnC,MAAAgN,SAAA,IAAa4I,IAAA,GAAO,KAAKtL,UAAA,IAAc;QAC7C,MAAM2C,CAAA,GAAIiJ,EAAA,GAAK,CAAC,KAAKC,aAAA,GAAgBnJ,SAAA;QAErC,IAAIC,CAAA,GAAI,GAAG;UAEJ,KAAAhC,aAAA,GAAgB,MAAM,CAAC,KAAKkL,aAAA,GAAgB5O,IAAA,CAAKC,GAAA,CAAIwF,SAAA,EAAW,CAAC,IAAIkJ,EAAA,GAAKlJ,SAAA,GAAY;UAC3F,KAAKvE,oBAAA,CAAqB,KAAK0D,MAAA,CAAOmB,YAAA,EAAc,KAAKrC,aAAa,CAAC;UAEvE,KAAKrI,aAAA,CAAc7B,YAAY;UAC/B,MAAMoM,IAAA,GAAO;UACb,KAAK/C,YAAA,GAAerG,MAAA,CAAOqJ,qBAAA,CAAsB,UAAUC,CAAA,EAAG;YACvDF,IAAA,CAAAI,cAAA,CAAeF,CAAA,EAAGC,YAAA,EAAc4I,EAAE;UAAA,CACxC;QAAA,OACI;UACL,KAAK9L,YAAA,GAAe;UACpB,KAAKE,UAAA,GAAa;UAEb,KAAAhD,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;UACpC,KAAKiL,cAAA,CAAe,KAAK;UAGzB,KAAK3H,aAAA,CAAc7B,YAAY;QACjC;MAAA,OACK;QAGL,KAAKqJ,YAAA,GAAe;QACpB,KAAKE,UAAA,GAAa;QAEd,SAAKsB,MAAA,IAAUvM,KAAA,CAAMG,MAAA,EAAQ;UAC/B,KAAK+K,cAAA,CAAe,KAAK;UAEzB,KAAK3H,aAAA,CAAc7B,YAAY;QACjC;MACF;IAAA;IASM;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAS,aAAA,cAAM,CAAC6N,EAAA,EAAaJ,EAAA,EAAamH,MAAA,GAAS,UAA0B;MAC1E,IAAI,KAAKxV,MAAA,EAAQ;QACf,MAAMoE,QAAA,GAAWqK,EAAA,CAAG1C,KAAA,CAAM,EAAEC,GAAA,CAAIqC,EAAE;QAE9B,SAAKrO,MAAA,YAAkBgH,kBAAA,EAAoB;UAE7C5C,QAAA,CAASmD,cAAA,CAAe,IAAI,KAAKvH,MAAA,CAAOwH,IAAI;QAC9C;QAEI,SAAKxH,MAAA,YAAkB2H,iBAAA,IAAqB6N,MAAA,EAAQ;UAEjD,KAAArN,KAAA,CAAMC,qBAAA,CAAsB,KAAKuK,mBAAmB;UACpD,KAAA/G,KAAA,CAAMxD,qBAAA,CAAsB,KAAKsM,kBAAkB;UACxD,MAAMe,cAAA,GACJ,KAAKtN,KAAA,CAAMG,UAAA,CAAW,KAAKsD,KAAK,IAAI,KAAK5L,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;UAClFtD,QAAA,CAAAmD,cAAA,CAAe,IAAIkO,cAAc;QAC5C;QAEK,KAAAtN,KAAA,CAAMuN,GAAA,CAAItR,QAAA,CAASvE,CAAA,EAAGuE,QAAA,CAAStE,CAAA,EAAG,CAAC,EAAEuH,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAU;QAE3E,KAAA4E,KAAA,CAAMC,eAAA,CAAgB,KAAKhE,KAAA,CAAMtI,CAAA,EAAG,KAAKsI,KAAA,CAAMrI,CAAA,EAAG,KAAKqI,KAAA,CAAMpH,CAAC;QAEnE,KAAK4U,yBAAA,CAA0B,KAAKzJ,KAAA,EAAO,KAAKA,KAAK;MACvD;MACO,OAAAnM,eAAA;IAAA;IAMF;AAAA;AAAA;IAAAa,aAAA,gBAAQ,MAAY;MACzB,IAAI,KAAKZ,MAAA,EAAQ;QACV,KAAAA,MAAA,CAAOwH,IAAA,GAAO,KAAKuL,MAAA;QAEpB,SAAK/S,MAAA,YAAkB2H,iBAAA,EAAmB;UACvC,KAAA3H,MAAA,CAAO8I,GAAA,GAAM,KAAK4J,KAAA;QACzB;QAEK,KAAA1S,MAAA,CAAOkT,IAAA,GAAO,KAAKE,QAAA;QACnB,KAAApT,MAAA,CAAOsT,GAAA,GAAM,KAAKE,OAAA;QAClB,KAAAnL,kBAAA,CAAmByB,IAAA,CAAK,KAAK6I,mBAAmB;QAChD,KAAAtK,kBAAA,CAAmB2H,SAAA,CAAU,KAAKhQ,MAAA,CAAO0H,QAAA,EAAU,KAAK1H,MAAA,CAAOsH,UAAA,EAAY,KAAKtH,MAAA,CAAOa,KAAK;QACjG,KAAKb,MAAA,CAAO0T,EAAA,CAAG5J,IAAA,CAAK,KAAK2J,IAAI;QAE7B,KAAKzT,MAAA,CAAOyS,YAAA;QACZ,KAAKzS,MAAA,CAAO4T,sBAAA;QAEP,KAAA/H,iBAAA,CAAkB/B,IAAA,CAAK,KAAK4K,kBAAkB;QAC9C,KAAAA,kBAAA,CAAmB1E,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;QACpG,KAAKC,OAAA,CAAQ2R,YAAA;QAEb,MAAMzR,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QACA,KAAK6S,UAAA,CAAW,KAAK/S,OAAA,CAAQ4G,QAAA,EAAU,KAAKxG,SAAS;QAErD,KAAKlB,MAAA,CAAOuS,MAAA,CAAO,KAAKzR,OAAA,CAAQ4G,QAAQ;QAEnC,KAAAhB,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;QAGpC,KAAKsD,aAAA,CAAc7B,YAAY;MACjC;IAAA;IASM;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAS,aAAA,iBAAS,CAACgV,IAAA,EAAezK,KAAA,KAAkC;MAC3D,MAAAwE,KAAA,GAAQ,KAAK7O,OAAA,CAAQ4G,QAAA;MACtB,KAAAmI,kBAAA,CAAmB1D,eAAA,CAAgB,CAACwD,KAAA,CAAM9P,CAAA,EAAG,CAAC8P,KAAA,CAAM7P,CAAA,EAAG,CAAC6P,KAAA,CAAM5O,CAAC;MACpE,KAAKiO,eAAA,CAAgB6G,gBAAA,CAAiBD,IAAA,EAAM,CAACzK,KAAK;MAGlD,KAAKe,KAAA,CAAMC,eAAA,CAAgBwD,KAAA,CAAM9P,CAAA,EAAG8P,KAAA,CAAM7P,CAAA,EAAG6P,KAAA,CAAM5O,CAAC;MAC/C,KAAAmL,KAAA,CAAM4J,QAAA,CAAS,KAAK9G,eAAe;MACnC,KAAA9C,KAAA,CAAM4J,QAAA,CAAS,KAAKjG,kBAAkB;MAEtC,KAAA8F,yBAAA,CAA0B,KAAKzJ,KAAK;MAElC,OAAAnM,eAAA;IAAA;IAGFa,aAAA,oBAAY,MAAY;MAC7B,IAAI,KAAKZ,MAAA,EAAQ;QACf,MAAMkO,KAAA,GAAQ6H,IAAA,CAAKC,SAAA,CACjB,KAAKhW,MAAA,YAAkBgH,kBAAA,GACnB;UACEiP,YAAA,EAAc;YACZC,SAAA,EAAW,KAAKlW,MAAA,CAAOsT,GAAA;YACvB2B,YAAA,EAAc,KAAKjV,MAAA,CAAO4S,MAAA;YAC1BuD,UAAA,EAAY,KAAKnW,MAAA,CAAOkT,IAAA;YACxBkD,QAAA,EAAU,KAAKpW,MAAA,CAAO0T,EAAA;YACtB2C,UAAA,EAAY,KAAKrW,MAAA,CAAOwH,IAAA;YACxB0N,WAAA,EAAa,KAAKpU,OAAA,CAAQ8R;UAC5B;QAAA,IAEF;UACEqD,YAAA,EAAc;YACZC,SAAA,EAAW,KAAKlW,MAAA,CAAOsT,GAAA;YACvBgD,SAAA,EAAW,KAAKtW,MAAA,CAAO8I,GAAA;YACvBmM,YAAA,EAAc,KAAKjV,MAAA,CAAO4S,MAAA;YAC1BuD,UAAA,EAAY,KAAKnW,MAAA,CAAOkT,IAAA;YACxBkD,QAAA,EAAU,KAAKpW,MAAA,CAAO0T,EAAA;YACtB2C,UAAA,EAAY,KAAKrW,MAAA,CAAOwH,IAAA;YACxB0N,WAAA,EAAa,KAAKpU,OAAA,CAAQ8R;UAC5B;QACF;QAGI2D,SAAA,CAAAC,SAAA,CAAUC,SAAA,CAAUvI,KAAK;MACrC;IAAA;IAGKtN,aAAA,qBAAa,MAAY;MAC9B,MAAM2L,IAAA,GAAO;MACbgK,SAAA,CAAUC,SAAA,CAAUE,QAAA,CAAS,EAAEC,IAAA,CAAK,SAASC,SAASC,KAAA,EAAO;QAC3DtK,IAAA,CAAKuK,gBAAA,CAAiBD,KAAK;MAAA,CAC5B;IAAA;IAMI;AAAA;AAAA;IAAAjW,aAAA,oBAAY,MAAY;MAC7B,IAAI,CAAC,KAAKZ,MAAA,EAAQ;MAElB,KAAK2S,mBAAA,CAAoB7I,IAAA,CAAK,KAAK9J,MAAA,CAAO4S,MAAM;MAChD,KAAK8B,kBAAA,CAAmB5K,IAAA,CAAK,KAAKhJ,OAAA,CAAQ8R,MAAM;MAC3C,KAAAQ,QAAA,GAAW,KAAKpT,MAAA,CAAOkT,IAAA;MACvB,KAAAM,OAAA,GAAU,KAAKxT,MAAA,CAAOsT,GAAA;MACtB,KAAAP,MAAA,GAAS,KAAK/S,MAAA,CAAOwH,IAAA;MAC1B,KAAKiM,IAAA,CAAK3J,IAAA,CAAK,KAAK9J,MAAA,CAAO0T,EAAE;MAEzB,SAAK1T,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,KAAA+K,KAAA,GAAQ,KAAK1S,MAAA,CAAO8I,GAAA;MAC3B;IAAA;IAUM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAlI,aAAA,qBAAa,CAAC4F,IAAA,EAAcmJ,KAAA,EAAgBoH,WAAA,GAAc,SAAqC;MACrG,IAAI,CAAC,KAAK/W,MAAA,EAAQ;MAEZ,MAAA+G,UAAA,GAAa4I,KAAA,CAAM5D,KAAA;MACzB,IAAIiL,WAAA,GAAc,IAAIxQ,IAAA;MAElB,SAAKxG,MAAA,YAAkBgH,kBAAA,EAAoB;QAExC,KAAAhH,MAAA,CAAOwH,IAAA,GAAO,KAAKwL,UAAA;QACxB,KAAKhT,MAAA,CAAOwH,IAAA,IAAQhB,IAAA;QAGpB,IAAI,KAAKxG,MAAA,CAAOwH,IAAA,GAAO,KAAKyP,OAAA,EAAS;UAC9B,KAAAjX,MAAA,CAAOwH,IAAA,GAAO,KAAKyP,OAAA;UACVD,WAAA,QAAKhE,UAAA,GAAa,KAAKiE,OAAA;QAC5B,gBAAKjX,MAAA,CAAOwH,IAAA,GAAO,KAAK0P,OAAA,EAAS;UACrC,KAAAlX,MAAA,CAAOwH,IAAA,GAAO,KAAK0P,OAAA;UACVF,WAAA,QAAKhE,UAAA,GAAa,KAAKkE,OAAA;QACvC;QAEA,KAAKlX,MAAA,CAAO4T,sBAAA;QAEP,KAAAzL,KAAA,CAAMC,qBAAA,CAAsB,KAAKyD,iBAAiB;QAGvD,KAAKgJ,YAAA,CAAaC,SAAA,CAAUkC,WAAA,EAAaA,WAAA,EAAaA,WAAW;QACjE,KAAKnH,kBAAA,CAAmB1D,eAAA,CAAgB,CAAC,KAAKhE,KAAA,CAAMtI,CAAA,EAAG,CAAC,KAAKsI,KAAA,CAAMrI,CAAA,EAAG,CAAC,KAAKqI,KAAA,CAAMpH,CAAC;QAEnF,KAAKoW,KAAA,CAAMhL,eAAA,CAAgB,KAAKhE,KAAA,CAAMtI,CAAA,EAAG,KAAKsI,KAAA,CAAMrI,CAAA,EAAG,KAAKqI,KAAA,CAAMpH,CAAC,EAAE+U,QAAA,CAAS,KAAKjB,YAAY;QAC1F,KAAAsC,KAAA,CAAMrB,QAAA,CAAS,KAAKjG,kBAAkB;QAGhC9I,UAAA,CAAAiF,GAAA,CAAI,KAAK7D,KAAK;QAEzB,MAAMkD,MAAA,GAAStE,UAAA,CAAWgF,KAAA,CAAM,EAAExE,cAAA,CAAeyP,WAAW;QAC5DjQ,UAAA,CAAWiF,GAAA,CAAIX,MAAM;QAErB,KAAKa,KAAA,CAAMC,eAAA,CAAgBpF,UAAA,CAAWlH,CAAA,EAAGkH,UAAA,CAAWjH,CAAA,EAAGiH,UAAA,CAAWhG,CAAC;QAC9D,KAAAoW,KAAA,CAAMpH,WAAA,CAAY,KAAK7D,KAAK;QAEjC,KAAKyJ,yBAAA,CAA0B,KAAKzJ,KAAA,EAAO,KAAKiL,KAAK;QAC9C,OAAApX,eAAA;MACT;MAEI,SAAKC,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,KAAAQ,KAAA,CAAMC,qBAAA,CAAsB,KAAKC,kBAAkB;QACnD,KAAAuD,KAAA,CAAMxD,qBAAA,CAAsB,KAAKyD,iBAAiB;QAGvD,IAAIX,QAAA,GAAW,KAAK/C,KAAA,CAAMG,UAAA,CAAWvB,UAAU;QAC3C,IAAAsE,MAAA,GAASH,QAAA,GAAWA,QAAA,GAAW8L,WAAA;QAGnC,MAAM5N,WAAA,GAAc8B,QAAA,GAAWG,MAAA;QAC3B,IAAAjC,WAAA,GAAc,KAAKV,WAAA,EAAa;UAClCsO,WAAA,GAAc,KAAKtO,WAAA,GAAcwC,QAAA;UACjCG,MAAA,GAASH,QAAA,GAAWA,QAAA,GAAW8L,WAAA;QAAA,WACtB5N,WAAA,GAAc,KAAKT,WAAA,EAAa;UACzCqO,WAAA,GAAc,KAAKrO,WAAA,GAAcuC,QAAA;UACjCG,MAAA,GAASH,QAAA,GAAWA,QAAA,GAAW8L,WAAA;QACjC;QAEI,IAAAlL,SAAA,GAAY/E,UAAA,CAAWgF,KAAA,GAAQC,GAAA,CAAI,KAAK7D,KAAK,EAAE8D,SAAA,GAAY1E,cAAA,CAAe8D,MAAM;QAEpF,KAAKa,KAAA,CAAMC,eAAA,CAAgBL,SAAA,CAAUjM,CAAA,EAAGiM,SAAA,CAAUhM,CAAA,EAAGgM,SAAA,CAAU/K,CAAC;QAEhE,IAAIgW,WAAA,EAAa;UAEf,MAAMK,GAAA,GAAM,KAAKxL,KAAA;UAENV,QAAA,GAAAkM,GAAA,CAAI9O,UAAA,CAAWvB,UAAU;UACpCsE,MAAA,GAASH,QAAA,GAAWA,QAAA,GAAW8L,WAAA;UACnBlL,SAAA,GAAA/E,UAAA,CAAWgF,KAAA,CAAM,EAAEC,GAAA,CAAI,KAAKJ,KAAK,EAAEK,SAAA,GAAY1E,cAAA,CAAe8D,MAAM;UAEhF,KAAKwE,kBAAA,CAAmB1D,eAAA,CAAgBiL,GAAA,CAAIvX,CAAA,EAAGuX,GAAA,CAAItX,CAAA,EAAGsX,GAAA,CAAIrW,CAAC;UAC3D,KAAK8T,YAAA,CAAaC,SAAA,CAAUkC,WAAA,EAAaA,WAAA,EAAaA,WAAW;UAE5D,KAAAG,KAAA,CAAMhL,eAAA,CAAgBL,SAAA,CAAUjM,CAAA,EAAGiM,SAAA,CAAUhM,CAAA,EAAGgM,SAAA,CAAU/K,CAAC,EAAE+U,QAAA,CAAS,KAAKjG,kBAAkB;UAC7F,KAAAsH,KAAA,CAAMrB,QAAA,CAAS,KAAKjB,YAAY;UAEhC,KAAAhF,kBAAA,CAAmB1D,eAAA,CAAgB,CAACiL,GAAA,CAAIvX,CAAA,EAAG,CAACuX,GAAA,CAAItX,CAAA,EAAG,CAACsX,GAAA,CAAIrW,CAAC;UAEzD,KAAAoW,KAAA,CAAMrB,QAAA,CAAS,KAAKjG,kBAAkB;UAC3C,KAAK8F,yBAAA,CAA0B,KAAKzJ,KAAA,EAAO,KAAKiL,KAAK;QAAA,OAChD;UACA,KAAAxB,yBAAA,CAA0B,KAAKzJ,KAAK;QAC3C;QAEO,OAAAnM,eAAA;MACT;IAAA;IAOM;AAAA;AAAA;AAAA;IAAAa,aAAA,iBAAUiW,KAAA,IAAwB;MACpC,SAAK7W,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,KAAA3H,MAAA,CAAO8I,GAAA,GAAMN,SAAA,CAAUC,KAAA,CAAMoO,KAAA,EAAO,KAAK1N,MAAA,EAAQ,KAAKD,MAAM;QACjE,KAAKlJ,MAAA,CAAO4T,sBAAA;MACd;IAAA;IASK;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAhT,aAAA,oBAAY,CAACf,CAAA,EAAWC,CAAA,EAAWiB,CAAA,KAAoB;MAC5D,IAAI,KAAKf,MAAA,EAAQ;QACf,KAAKwS,MAAA,CAAOkD,GAAA,CAAI7V,CAAA,EAAGC,CAAA,EAAGiB,CAAC;QACvB,KAAKD,OAAA,CAAQ4G,QAAA,CAASgO,GAAA,CAAI7V,CAAA,EAAGC,CAAA,EAAGiB,CAAC;QACjC,MAAMC,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QACA,KAAK6S,UAAA,CAAW,KAAKrB,MAAA,EAAQ,KAAKtR,SAAS;QACtC,KAAAlB,MAAA,CAAOuS,MAAA,CAAO,KAAKC,MAAM;MAChC;IAAA;IAoCM;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA5R,aAAA,kBAAU,CAAC+O,KAAA,EAAgBxE,KAAA,KAAkC;MACnE,KAAK6D,eAAA,CAAgB6G,gBAAA,CAAiB,KAAKvI,aAAA,EAAenC,KAAK;MAC1D,KAAA0E,kBAAA,CAAmB1D,eAAA,CAAgB,CAACwD,KAAA,CAAM9P,CAAA,EAAG,CAAC8P,KAAA,CAAM7P,CAAA,EAAG,CAAC6P,KAAA,CAAM5O,CAAC;MAEpE,KAAKmL,KAAA,CAAMC,eAAA,CAAgBwD,KAAA,CAAM9P,CAAA,EAAG8P,KAAA,CAAM7P,CAAA,EAAG6P,KAAA,CAAM5O,CAAC;MAC/C,KAAAmL,KAAA,CAAM4J,QAAA,CAAS,KAAK9G,eAAe;MACnC,KAAA9C,KAAA,CAAM4J,QAAA,CAAS,KAAKjG,kBAAkB;MAE3C,KAAK1H,KAAA,CAAMC,qBAAA,CAAsB,KAAKyD,iBAAiB,EAAEG,GAAA,CAAI2D,KAAK;MAC7D,KAAA/D,KAAA,CAAM9B,IAAA,CAAK,KAAK3B,KAAK,EAAEkP,cAAA,CAAe,KAAK/J,aAAA,EAAenC,KAAK;MAC/D,KAAAS,KAAA,CAAMI,GAAA,CAAI,KAAK7D,KAAK;MAEpB,KAAAgP,KAAA,CAAMhL,eAAA,CAAgB,KAAKP,KAAA,CAAM/L,CAAA,EAAG,KAAK+L,KAAA,CAAM9L,CAAA,EAAG,KAAK8L,KAAA,CAAM7K,CAAC;MAEnE,KAAK4U,yBAAA,CAA0B,KAAKzJ,KAAA,EAAO,KAAKiL,KAAK;MAC9C,OAAApX,eAAA;IAAA;IASD;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAa,aAAA,yBAAiB,CAAC0W,MAAA,EAAiBtX,MAAA,KAAmC;MAC5E,IAAI,CAAC,KAAKW,KAAA,EAAc;MAElB,MAAA4W,SAAA,GAAY,IAAIC,SAAA;MACtBD,SAAA,CAAUrE,IAAA,GAAOlT,MAAA,CAAOkT,IAAA;MACxBqE,SAAA,CAAUjE,GAAA,GAAMtT,MAAA,CAAOsT,GAAA;MACbiE,SAAA,CAAAE,aAAA,CAAcH,MAAA,EAAQtX,MAAM;MAEtC,MAAM0X,SAAA,GAAYH,SAAA,CAAUI,gBAAA,CAAiB,KAAKhX,KAAA,CAAMkB,QAAA,EAAU,IAAI;MACtE,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAIuV,SAAA,CAAUrV,MAAA,EAAQF,CAAA,IAAK;QACrC,IAAAuV,SAAA,CAAUvV,CAAC,EAAEyV,MAAA,CAAOC,IAAA,IAAQ,KAAK/W,OAAA,CAAQ+W,IAAA,IAAQH,SAAA,CAAUvV,CAAC,EAAE2V,IAAA,EAAM;UACtE,OAAOJ,SAAA,CAAUvV,CAAC,EAAEwN,KAAA,CAAM5D,KAAA,CAAM;QAClC;MACF;MAEO;IAAA;IAYD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAnL,aAAA,+BAAuB,CAC7BZ,MAAA,EACAgS,OAAA,EACAC,OAAA,EACAC,MAAA,EACAlR,QAAA,KACwB;MACxB,IAAIhB,MAAA,YAAkBgH,kBAAA,EAAoB;QACxC,KAAKqL,KAAA,CAAMvI,IAAA,CAAK,KAAKiO,iBAAA,CAAkB/F,OAAA,EAASC,OAAA,EAASC,MAAM,CAAC;QAC3D,KAAA/J,KAAA,CAAMuN,GAAA,CAAI,KAAKrD,KAAA,CAAMxS,CAAA,EAAG,KAAKwS,KAAA,CAAMvS,CAAA,EAAG,CAAC;QAE5C,MAAMkY,EAAA,GAAKrR,IAAA,CAAKC,GAAA,CAAI,KAAKyL,KAAA,CAAMxS,CAAA,EAAG,CAAC;QACnC,MAAMoY,EAAA,GAAKtR,IAAA,CAAKC,GAAA,CAAI,KAAKyL,KAAA,CAAMvS,CAAA,EAAG,CAAC;QACnC,MAAMoY,EAAA,GAAKvR,IAAA,CAAKC,GAAA,CAAI,KAAK1F,SAAA,EAAW,CAAC;QAEjC,IAAA8W,EAAA,GAAKC,EAAA,IAAMC,EAAA,GAAK,KAAK;UAEvB,KAAK/P,KAAA,CAAMgQ,IAAA,CAAKxR,IAAA,CAAKkI,IAAA,CAAKqJ,EAAA,IAAMF,EAAA,GAAKC,EAAA,CAAG,CAAC;QAAA,OACpC;UAEA,KAAA9P,KAAA,CAAMgQ,IAAA,CAAMD,EAAA,GAAK,MAAOvR,IAAA,CAAKkI,IAAA,CAAKmJ,EAAA,GAAKC,EAAE,CAAC;QACjD;QAEA,OAAO,KAAK9P,KAAA;MACd;MAEA,IAAInI,MAAA,YAAkB2H,iBAAA,EAAmB;QAEvC,KAAK0K,KAAA,CAAMvI,IAAA,CAAK,KAAKc,YAAA,CAAaoH,OAAA,EAASC,OAAA,EAASC,MAAM,CAAC;QAEtD,KAAA/J,KAAA,CAAMuN,GAAA,CAAI,KAAKrD,KAAA,CAAMxS,CAAA,EAAG,KAAKwS,KAAA,CAAMvS,CAAA,EAAG,EAAE;QACxC,KAAAqI,KAAA,CAAMiQ,YAAA,CAAapY,MAAA,CAAOqY,uBAAuB;QAEtD,MAAMzO,MAAA,GAAS,KAAKzB,KAAA,CAAM4D,KAAA,GAAQE,SAAA,CAAU;QAC5C,MAAMqM,mBAAA,GAAsBtY,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;QAC5E,MAAM6Q,OAAA,GAAU5R,IAAA,CAAKC,GAAA,CAAI5F,QAAA,EAAU,CAAC;QAY9B,MAAAwX,CAAA,GAAI,KAAKrQ,KAAA,CAAMpH,CAAA;QACrB,MAAM0X,CAAA,GAAI9R,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAI,KAAKuB,KAAA,CAAMtI,CAAA,EAAG,CAAC,IAAI8G,IAAA,CAAKC,GAAA,CAAI,KAAKuB,KAAA,CAAMrI,CAAA,EAAG,CAAC,CAAC;QAEzE,IAAI2Y,CAAA,IAAK,GAAG;UAEV7O,MAAA,CAAO8L,GAAA,CAAI,KAAKvN,KAAA,CAAMtI,CAAA,EAAG,KAAKsI,KAAA,CAAMrI,CAAA,EAAGkB,QAAQ;UACxC,OAAA4I,MAAA;QACT;QAEA,MAAM8O,CAAA,GAAIF,CAAA,GAAIC,CAAA;QACd,MAAME,CAAA,GAAIL,mBAAA;QASV,IAAIM,CAAA,GAAIjS,IAAA,CAAKC,GAAA,CAAI8R,CAAA,EAAG,CAAC,IAAI;QACrB,IAAAG,CAAA,GAAI,IAAIH,CAAA,GAAIC,CAAA;QAChB,IAAIG,CAAA,GAAInS,IAAA,CAAKC,GAAA,CAAI+R,CAAA,EAAG,CAAC,IAAIJ,OAAA;QACzB,IAAIQ,KAAA,GAAQpS,IAAA,CAAKC,GAAA,CAAIiS,CAAA,EAAG,CAAC,IAAI,IAAID,CAAA,GAAIE,CAAA;QAErC,IAAIC,KAAA,IAAS,GAAG;UAET,KAAA1G,KAAA,CAAMC,IAAA,EAAM,CAACuG,CAAA,GAAIlS,IAAA,CAAKkI,IAAA,CAAKkK,KAAK,MAAM,IAAIH,CAAA,CAAE;UACjD,KAAKvG,KAAA,CAAM1H,IAAA,CAAK+N,CAAA,GAAI,KAAKrG,KAAA,CAAMxS,CAAA,GAAI8Y,CAAC;UAEpC,MAAMxN,KAAA,GAAQ3C,SAAA,CAAUQ,OAAA,GAAU,KAAKqJ,KAAA,CAAMlH,KAAA;UAE7C,IAAIA,KAAA,IAAS,IAAI;YAIf,MAAM6N,UAAA,GAAYrS,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAI,KAAKyL,KAAA,CAAMxS,CAAA,EAAG,CAAC,IAAI8G,IAAA,CAAKC,GAAA,CAAI0R,mBAAA,GAAsB,KAAKjG,KAAA,CAAMvS,CAAA,EAAG,CAAC,CAAC;YACvG8J,MAAA,CAAOrC,cAAA,CAAeyR,UAAS;YAC/BpP,MAAA,CAAO7I,CAAA,IAAKuX,mBAAA;YACL,OAAA1O,MAAA;UACT;QACF;QAUIgP,CAAA,GAAAF,CAAA;QACAG,CAAA,GAAAF,CAAA;QACJG,CAAA,GAAI,CAACP,OAAA,GAAU;QACfQ,KAAA,GAAQpS,IAAA,CAAKC,GAAA,CAAIiS,CAAA,EAAG,CAAC,IAAI,IAAID,CAAA,GAAIE,CAAA;QAC5B,KAAAzG,KAAA,CAAMC,IAAA,EAAM,CAACuG,CAAA,GAAIlS,IAAA,CAAKkI,IAAA,CAAKkK,KAAK,MAAM,IAAIH,CAAA,CAAE;QACjD,KAAKvG,KAAA,CAAM1H,IAAA,CAAK+N,CAAA,GAAI,KAAKrG,KAAA,CAAMxS,CAAA,GAAI8Y,CAAC;QAEpC,MAAMM,SAAA,GAAYtS,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAI,KAAKyL,KAAA,CAAMxS,CAAA,EAAG,CAAC,IAAI8G,IAAA,CAAKC,GAAA,CAAI0R,mBAAA,GAAsB,KAAKjG,KAAA,CAAMvS,CAAA,EAAG,CAAC,CAAC;QAEvG8J,MAAA,CAAOrC,cAAA,CAAe0R,SAAS;QAC/BrP,MAAA,CAAO7I,CAAA,IAAKuX,mBAAA;QACL,OAAA1O,MAAA;MACT;IAAA;IAYM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAAhJ,aAAA,6BAAqB,CAC3BZ,MAAA,EACAgS,OAAA,EACAC,OAAA,EACAC,MAAA,EACAgH,eAAA,GAAkB,UACM;MACxB,IAAIlZ,MAAA,YAAkBgH,kBAAA,EAAoB;QACxC,KAAKqL,KAAA,CAAMvI,IAAA,CAAK,KAAKiO,iBAAA,CAAkB/F,OAAA,EAASC,OAAA,EAASC,MAAM,CAAC;QAC3D,KAAA/J,KAAA,CAAMuN,GAAA,CAAI,KAAKrD,KAAA,CAAMxS,CAAA,EAAG,KAAKwS,KAAA,CAAMvS,CAAA,EAAG,CAAC;QAErC,YAAKqI,KAAA,CAAM4D,KAAA;MACpB;MAEA,IAAI/L,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,KAAK0K,KAAA,CAAMvI,IAAA,CAAK,KAAKc,YAAA,CAAaoH,OAAA,EAASC,OAAA,EAASC,MAAM,CAAC;QAGtD,KAAA/J,KAAA,CAAMuN,GAAA,CAAI,KAAKrD,KAAA,CAAMxS,CAAA,EAAG,KAAKwS,KAAA,CAAMvS,CAAA,EAAG,EAAE;QACxC,KAAAqI,KAAA,CAAMiQ,YAAA,CAAapY,MAAA,CAAOqY,uBAAuB;QAEtD,MAAMzO,MAAA,GAAS,KAAKzB,KAAA,CAAM4D,KAAA,GAAQE,SAAA,CAAU;QAYtC,MAAAuM,CAAA,GAAI,KAAKrQ,KAAA,CAAMpH,CAAA;QACrB,MAAM0X,CAAA,GAAI9R,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAI,KAAKuB,KAAA,CAAMtI,CAAA,EAAG,CAAC,IAAI8G,IAAA,CAAKC,GAAA,CAAI,KAAKuB,KAAA,CAAMrI,CAAA,EAAG,CAAC,CAAC;QACrE,IAAAwY,mBAAA;QAEJ,IAAIY,eAAA,EAAiB;UACnBZ,mBAAA,GAAsB,KAAKnQ,KAAA,CACxBC,qBAAA,CAAsB,KAAKuK,mBAAmB,EAC9CrK,UAAA,CAAW,KAAKsD,KAAA,CAAMxD,qBAAA,CAAsB,KAAKsM,kBAAkB,CAAC;QAAA,OAClE;UACL4D,mBAAA,GAAsBtY,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;QACxE;QASA,IAAI+Q,CAAA,IAAK,GAAG;UAEH7O,MAAA,CAAA8L,GAAA,CAAI,GAAG,GAAG,CAAC;UACX,OAAA9L,MAAA;QACT;QAEA,MAAM8O,CAAA,GAAIF,CAAA,GAAIC,CAAA;QACd,MAAME,CAAA,GAAIL,mBAAA;QACJ,MAAAzY,CAAA,GAAI,CAAC8Y,CAAA,GAAID,CAAA;QAEf,MAAMO,SAAA,GAAYtS,IAAA,CAAKkI,IAAA,CAAKlI,IAAA,CAAKC,GAAA,CAAI+R,CAAA,EAAG,CAAC,IAAIhS,IAAA,CAAKC,GAAA,CAAI/G,CAAA,EAAG,CAAC,CAAC;QAC3D+J,MAAA,CAAOrC,cAAA,CAAe0R,SAAS;QAC/BrP,MAAA,CAAO7I,CAAA,GAAI;QACJ,OAAA6I,MAAA;MACT;IAAA;IAMM;AAAA;AAAA;IAAAhJ,aAAA,4BAAoB,MAAY;MACtC,IAAI,CAAC,KAAKZ,MAAA,EAAQ;MAGlB,KAAKqI,kBAAA,CAAmByB,IAAA,CAAK,KAAK9J,MAAA,CAAO4S,MAAM;MAC/C,KAAK/G,iBAAA,CAAkB/B,IAAA,CAAK,KAAKhJ,OAAA,CAAQ8R,MAAM;MAE3C,SAAK5S,MAAA,YAAkBgH,kBAAA,EAAoB;QAC7C,KAAK6L,sBAAA,CAAuB/I,IAAA,CAAK,KAAK9J,MAAA,CAAO8S,gBAAgB;QAC7D,KAAK9S,MAAA,CAAO4T,sBAAA;QACP,KAAAZ,UAAA,GAAa,KAAKhT,MAAA,CAAOwH,IAAA;MAChC;MAEI,SAAKxH,MAAA,YAAkB2H,iBAAA,EAAmB;QACvC,KAAAgE,SAAA,GAAY,KAAK3L,MAAA,CAAO8I,GAAA;MAC/B;IAAA;IAQM;AAAA;AAAA;AAAA;AAAA;IAAAlI,aAAA,wBAAgB,CAACuY,QAAA,EAAkBC,cAAA,KAAkC;MAC3E,KAAKpO,MAAA,GAASmO,QAAA;MACd,IAAIC,cAAA,EAAgB;QAClB,KAAKC,iBAAA,CAAkB;MACzB;IAAA;IAGKzY,aAAA,iBAAS,MAAY;MAC1B,MAAM0Y,GAAA,GAAM;MAGR,KAAC,KAAK9G,MAAA,CAAO+G,MAAA,CAAO,KAAKC,cAAc,KAAK,KAAKxZ,MAAA,EAAQ;QACtD,KAAAc,OAAA,CAAQ4G,QAAA,CAASgO,GAAA,CAAI,KAAKlD,MAAA,CAAO3S,CAAA,EAAG,KAAK2S,MAAA,CAAO1S,CAAA,EAAG,KAAK0S,MAAA,CAAOzR,CAAC;QACrE,MAAMC,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QACA,KAAK6S,UAAA,CAAW,KAAKrB,MAAA,EAAQ,KAAKtR,SAAS;QACtC,KAAAsY,cAAA,CAAe1P,IAAA,CAAK,KAAK0I,MAAM;MACtC;MAEA,IAAI,CAAC,KAAKxS,MAAA,EAAQ;MAGd,SAAKA,MAAA,YAAkBgH,kBAAA,EAAoB;QAEzC,SAAKhH,MAAA,CAAOwH,IAAA,GAAO,KAAKyP,OAAA,IAAW,KAAKjX,MAAA,CAAOwH,IAAA,GAAO,KAAK0P,OAAA,EAAS;UAChE,MAAAuC,OAAA,GAAUjR,SAAA,CAAUC,KAAA,CAAM,KAAKzI,MAAA,CAAOwH,IAAA,EAAM,KAAK0P,OAAA,EAAS,KAAKD,OAAO;UACvE,KAAApP,oBAAA,CAAqB,KAAKC,UAAA,CAAW2R,OAAA,GAAU,KAAKzZ,MAAA,CAAOwH,IAAA,EAAM,KAAK1G,OAAA,CAAQ4G,QAAA,EAAU,IAAI,CAAC;QACpG;MACF;MAEI,SAAK1H,MAAA,YAAkB2H,iBAAA,EAAmB;QAE5C,MAAMuD,QAAA,GAAW,KAAKlL,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;QAEtE,IAAIwD,QAAA,GAAW,KAAKvC,WAAA,GAAc2Q,GAAA,IAAOpO,QAAA,GAAW,KAAKxC,WAAA,GAAc4Q,GAAA,EAAK;UAC1E,MAAMlQ,WAAA,GAAcZ,SAAA,CAAUC,KAAA,CAAMyC,QAAA,EAAU,KAAKxC,WAAA,EAAa,KAAKC,WAAW;UAC3E,KAAAd,oBAAA,CAAqB,KAAKC,UAAA,CAAWsB,WAAA,GAAc8B,QAAA,EAAU,KAAKpK,OAAA,CAAQ4G,QAAQ,CAAC;UACxF,KAAK2R,iBAAA,CAAkB;QACzB;QAGI,SAAKrZ,MAAA,CAAO8I,GAAA,GAAM,KAAKK,MAAA,IAAU,KAAKnJ,MAAA,CAAO8I,GAAA,GAAM,KAAKI,MAAA,EAAQ;UAC7D,KAAAlJ,MAAA,CAAO8I,GAAA,GAAMN,SAAA,CAAUC,KAAA,CAAM,KAAKzI,MAAA,CAAO8I,GAAA,EAAK,KAAKK,MAAA,EAAQ,KAAKD,MAAM;UAC3E,KAAKlJ,MAAA,CAAO4T,sBAAA;QACd;QAEA,MAAM8F,SAAA,GAAY,KAAKxY,SAAA;QACvB,MAAMF,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QAEA,IAAI0Y,SAAA,GAAY,KAAKxY,SAAA,GAAYoY,GAAA,IAAOI,SAAA,GAAY,KAAKxY,SAAA,GAAYoY,GAAA,EAAK;UACxE,MAAMzY,KAAA,IAAS,KAAKC,OAAA,CAAQD,KAAA,CAAMhB,CAAA,GAAI,KAAKiB,OAAA,CAAQD,KAAA,CAAMf,CAAA,GAAI,KAAKgB,OAAA,CAAQD,KAAA,CAAME,CAAA,IAAK;UAC/E,MAAAI,SAAA,GAAY,KAAKD,SAAA,GAAYL,KAAA;UAEnC,MAAMO,KAAA,GAAQ,IAAIC,YAAA,CAAa,GAAG,GAAGF,SAAA,EAAWA,SAAS;UACzD,MAAMG,MAAA,GAASF,KAAA,CAAMG,SAAA,CAAU,KAAKC,SAAS;UAC7C,MAAMC,aAAA,GAAgB,IAAIC,cAAA,CAAe,EAAEC,aAAA,CAAcL,MAAM;UAEpD,WAAAM,KAAA,IAAS,KAAKd,OAAA,CAAQe,QAAA,EAAU;YACzC,MAAMC,KAAA,GAAQ,KAAKhB,OAAA,CAAQe,QAAA,CAASD,KAAK;YACzCE,KAAA,CAAMC,QAAA,GAAWN,aAAA;UACnB;QACF;MACF;MAEA,KAAKzB,MAAA,CAAOuS,MAAA,CAAO,KAAKzR,OAAA,CAAQ4G,QAAQ;IAAA;IAGlC9G,aAAA,2BAAoB+Y,IAAA,IAAuB;MAC3C,MAAAzL,KAAA,GAAQ6H,IAAA,CAAK6D,KAAA,CAAMD,IAAI;MAEzB,IAAAzL,KAAA,CAAM+H,YAAA,IAAgB,KAAKjW,MAAA,EAAQ;QACrC,KAAKqI,kBAAA,CAAmBwR,SAAA,CAAU3L,KAAA,CAAM+H,YAAA,CAAahB,YAAA,CAAa6E,QAAQ;QACrE,KAAAzR,kBAAA,CAAmB2H,SAAA,CAAU,KAAKhQ,MAAA,CAAO0H,QAAA,EAAU,KAAK1H,MAAA,CAAOsH,UAAA,EAAY,KAAKtH,MAAA,CAAOa,KAAK;QAEjG,KAAKb,MAAA,CAAO0T,EAAA,CAAG5J,IAAA,CAAKoE,KAAA,CAAM+H,YAAA,CAAaG,QAAQ;QAC1C,KAAApW,MAAA,CAAOkT,IAAA,GAAOhF,KAAA,CAAM+H,YAAA,CAAaE,UAAA;QACjC,KAAAnW,MAAA,CAAOsT,GAAA,GAAMpF,KAAA,CAAM+H,YAAA,CAAaC,SAAA;QAEhC,KAAAlW,MAAA,CAAOwH,IAAA,GAAO0G,KAAA,CAAM+H,YAAA,CAAaI,UAAA;QAElC,SAAKrW,MAAA,YAAkB2H,iBAAA,EAAmB;UACvC,KAAA3H,MAAA,CAAO8I,GAAA,GAAMoF,KAAA,CAAM+H,YAAA,CAAaK,SAAA;QACvC;QAEA,KAAKzK,iBAAA,CAAkBgO,SAAA,CAAU3L,KAAA,CAAM+H,YAAA,CAAaf,WAAA,CAAY4E,QAAQ;QACnE,KAAAjO,iBAAA,CAAkBmE,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;QAEnG,KAAKb,MAAA,CAAOyS,YAAA;QACZ,KAAKzS,MAAA,CAAO4T,sBAAA;QAEZ,KAAK9S,OAAA,CAAQ2R,YAAA;QAEb,MAAMzR,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;QACnD,IAAIgB,QAAA,KAAa,QAAW;UAC1B,KAAKE,SAAA,GAAYF,QAAA;QACnB;QACA,MAAM+Y,QAAA,GAAW,IAAI9Z,OAAA,GAAU6J,IAAA,CAAK,KAAK4K,kBAAkB;QAC3D,KAAKb,UAAA,CAAW,KAAK/S,OAAA,CAAQ4G,QAAA,EAAU,KAAKxG,SAAS;QAChD,KAAAwT,kBAAA,CAAmB5K,IAAA,CAAKiQ,QAAQ;QAErC,KAAK/Z,MAAA,CAAOuS,MAAA,CAAO,KAAKzR,OAAA,CAAQ4G,QAAQ;QACnC,KAAAhB,aAAA,CAAcjI,KAAA,CAAMC,IAAA,EAAM,KAAK;QAGpC,KAAKsD,aAAA,CAAc7B,YAAY;MACjC;IAAA;IA5jFA,KAAKH,MAAA,GAAS;IACd,KAAKU,UAAA,GAAaA,UAAA;IAClB,KAAKC,KAAA,GAAQA,KAAA;IAEb,KAAKyB,YAAA,GAAe;IACpB,KAAK2B,QAAA,GAAW;IAGX,KAAAsO,KAAA,GAAQ,IAAI2H,OAAA;IACZ,KAAA7R,KAAA,GAAQ,IAAIqF,OAAA;IACZ,KAAA5B,KAAA,GAAQ,IAAI4B,OAAA;IAEZ,KAAAtB,KAAA,GAAQ,IAAIjM,OAAA;IACZ,KAAAkX,KAAA,GAAQ,IAAIlX,OAAA;IAEZ,KAAAiP,KAAA,GAAQ,IAAI+K,UAAA;IAGZ,KAAApK,kBAAA,GAAqB,IAAI5P,OAAA;IACzB,KAAA+O,eAAA,GAAkB,IAAI/O,OAAA;IACtB,KAAA4U,YAAA,GAAe,IAAI5U,OAAA;IAEnB,KAAAqN,aAAA,GAAgB,IAAIE,OAAA;IAGpB,KAAAnF,kBAAA,GAAqB,IAAIpI,OAAA;IACzB,KAAA4S,sBAAA,GAAyB,IAAI5S,OAAA;IAElC,KAAK0L,SAAA,GAAY;IACZ,KAAAgI,QAAA,GAAW,IAAInG,OAAA;IACpB,KAAKwF,UAAA,GAAa;IAClB,KAAKI,QAAA,GAAW;IAChB,KAAKI,OAAA,GAAU;IAEV,KAAA3H,iBAAA,GAAoB,IAAI5L,OAAA;IAGxB,KAAAwT,IAAA,GAAO,IAAIjG,OAAA;IAChB,KAAKuF,MAAA,GAAS;IACd,KAAKL,KAAA,GAAQ;IACb,KAAKO,YAAA,GAAe;IACpB,KAAKE,SAAA,GAAY;IACjB,KAAKE,WAAA,GAAc;IACnB,KAAKE,QAAA,GAAW;IACX,KAAAZ,mBAAA,GAAsB,IAAI1S,OAAA;IAC1B,KAAAyU,kBAAA,GAAqB,IAAIzU,OAAA;IAG9B,KAAKgE,OAAA,GAAU;IACf,KAAKzB,WAAA,GAAc;IACnB,KAAKE,aAAA,GAAgB;IACrB,KAAKC,MAAA,GAAStD,KAAA,CAAMC,IAAA;IAGpB,KAAKiF,kBAAA,GAAqB;IAC1B,KAAKmJ,oBAAA,GAAuB;IAC5B,KAAKC,sBAAA,GAAyB;IAC9B,KAAKT,oBAAA,GAAuB;IAC5B,KAAKE,sBAAA,GAAyB;IAG9B,KAAK9I,WAAA,GAAc;IACnB,KAAKxB,UAAA,GAAa;IAClB,KAAK4C,QAAA,GAAW;IAChB,KAAK3C,WAAA,GAAc;IACnB,KAAK4C,WAAA,GAAc;IACnB,KAAKF,YAAA,GAAe;IACpB,KAAKM,YAAA,GAAe;IACpB,KAAKC,aAAA,GAAgB;IACrB,KAAKlB,kBAAA,GAAqB;IAGrB,KAAA+F,sBAAA,GAAyB,IAAI2C,OAAA;IAC7B,KAAA3D,oBAAA,GAAuB,IAAI2D,OAAA;IAGhC,KAAKzF,KAAA,GAAQ;IACR,KAAA6I,aAAA,GAAgB,IAAIpD,OAAA;IAGpB,KAAA1M,OAAA,GAAU,IAAIoZ,KAAA;IACnB,KAAK1Y,SAAA,GAAY;IAGjB,KAAKkI,UAAA,GAAa;IAClB,KAAKF,YAAA,GAAe;IAGpB,KAAK4L,kBAAA,GAAqB;IAG1B,KAAKjL,SAAA,GAAY;IACjB,KAAKC,YAAA,GAAe;IACpB,KAAKE,UAAA,GAAa;IAClB,KAAKD,aAAA,GAAgB;IAChB,KAAAE,cAAA,GAAiB,IAAIiD,OAAA;IACrB,KAAAhD,cAAA,GAAiB,IAAIgD,OAAA;IAC1B,KAAK9C,MAAA,GAAS;IACd,KAAKD,MAAA,GAAS;IAGd,KAAK0P,aAAA,GAAgB;IACrB,KAAK1T,WAAA,GAAc;IACnB,KAAK8O,aAAA,GAAgB;IACrB,KAAK1I,IAAA,GAAO;IACZ,KAAK3C,gBAAA,GAAmB;IACxB,KAAKH,UAAA,GAAa;IAClB,KAAKlD,UAAA,GAAa;IAClB,KAAKsC,MAAA,GAAS;IACd,KAAKD,MAAA,GAAS;IAEd,KAAKhH,OAAA,GAAU;IACf,KAAK4E,SAAA,GAAY;IACjB,KAAKkD,YAAA,GAAe;IACpB,KAAK7D,UAAA,GAAa;IAElB,KAAKuC,WAAA,GAAc;IACnB,KAAKC,WAAA,GAAcyR,QAAA;IACnB,KAAKlD,OAAA,GAAU;IACf,KAAKD,OAAA,GAAUmD,QAAA;IAGf,KAAK5H,MAAA,GAAS,IAAIhF,OAAA,CAAQ,GAAG,GAAG,CAAC;IACjC,KAAKgM,cAAA,GAAiB,IAAIhM,OAAA,CAAQ,GAAG,GAAG,CAAC;IAEzC,KAAKtM,SAAA,GAAY;IAGjB,KAAK8J,MAAA,GAASvM,KAAA,CAAMC,IAAA;IAEpB,KAAK2b,SAAA,CAAUra,MAAM;IAErB,IAAI,KAAKW,KAAA,EAAO;MACT,KAAAA,KAAA,CAAM8G,GAAA,CAAI,KAAK3G,OAAO;IAC7B;IAEA,KAAKwD,WAAA,GAAcnB,MAAA,CAAOmX,gBAAA;IAE1B,KAAKC,sBAAA,CAAuB;IAE5B,IAAI,KAAK7Z,UAAA,EAAiB,KAAA8Z,OAAA,CAAQ,KAAK9Z,UAAU;IAE1CyC,MAAA,CAAAC,gBAAA,CAAiB,UAAU,KAAKqO,cAAc;EACvD;EAAA;AAAA;AAAA;AAAA;EA2vCQ5J,qBAAqB4S,cAAA,EAAkD;IACzE,KAAAA,cAAA,oBAAAA,cAAA,CAAgBza,MAAA,KAAU,KAAKA,MAAA,EAAQ;MACzC,KAAKkM,KAAA,CAAMpC,IAAA,CAAK,KAAKzB,kBAAkB,EAAE0H,WAAA,CAAY0K,cAAA,CAAeza,MAAM;MACrE,KAAAkM,KAAA,CAAM8D,SAAA,CAAU,KAAKhQ,MAAA,CAAO0H,QAAA,EAAU,KAAK1H,MAAA,CAAOsH,UAAA,EAAY,KAAKtH,MAAA,CAAOa,KAAK;MACpF,KAAKb,MAAA,CAAOyS,YAAA;MAGR,SAAKzH,MAAA,IAAUvM,KAAA,CAAMG,MAAA,IAAU,KAAKoM,MAAA,IAAUvM,KAAA,CAAMQ,OAAA,IAAW,KAAK+L,MAAA,IAAUvM,KAAA,CAAMW,gBAAA,EAAkB;QACnG,KAAAY,MAAA,CAAO0T,EAAA,CAAG5J,IAAA,CAAK,KAAK6J,QAAQ,EAAEtM,eAAA,CAAgB,KAAKrH,MAAA,CAAOsH,UAAU;MAC3E;IACF;IAEA,IAAImT,cAAA,oBAAAA,cAAA,CAAgBva,MAAA,EAAQ;MAC1B,KAAKgM,KAAA,CAAMpC,IAAA,CAAK,KAAK+B,iBAAiB,EAAEkE,WAAA,CAAY0K,cAAA,CAAeva,MAAM;MACpE,KAAAgM,KAAA,CAAM8D,SAAA,CAAU,KAAKlP,OAAA,CAAQ4G,QAAA,EAAU,KAAK5G,OAAA,CAAQwG,UAAA,EAAY,KAAKxG,OAAA,CAAQD,KAAK;MACvF,KAAKC,OAAA,CAAQ2R,YAAA;IACf;IAEA,KACG,KAAKzH,MAAA,IAAUvM,KAAA,CAAMK,KAAA,IAAS,KAAKkM,MAAA,IAAUvM,KAAA,CAAMO,KAAA,IAAS,KAAKgM,MAAA,IAAUvM,KAAA,CAAMU,eAAA,KAClF,KAAKa,MAAA,EACL;MACA,MAAMgB,QAAA,GAAW,KAAKC,iBAAA,CAAkB,KAAKjB,MAAM;MACnD,IAAIgB,QAAA,KAAa,QAAW;QAC1B,KAAKE,SAAA,GAAYF,QAAA;MACnB;MAEA,IAAI,KAAKmZ,aAAA,EAAe;QACtB,MAAMO,cAAA,GAAiB,KAAK1a,MAAA,CAAO0H,QAAA,CAASY,UAAA,CAAW,KAAKxH,OAAA,CAAQ4G,QAAQ;QAEtE,MAAAiT,EAAA,GAAK,IAAIC,IAAA;QACZD,EAAA,CAAAE,aAAA,CAAc,KAAK/Z,OAAO;QACvB,MAAAga,MAAA,GAAS,IAAIC,MAAA;QACnBJ,EAAA,CAAGK,iBAAA,CAAkBF,MAAM;QAErB,MAAAG,oBAAA,GAAuBtU,IAAA,CAAK2E,GAAA,CAAI,KAAK6H,SAAA,EAAW2H,MAAA,CAAOI,MAAA,GAASJ,MAAA,CAAOK,MAAA,CAAO9Y,MAAA,CAAQ;QACtF,MAAA+Y,mBAAA,GAAsBV,cAAA,GAAiB,KAAKzH,YAAA;QAElD,MAAMoI,UAAA,GAAa1U,IAAA,CAAKiG,GAAA,CAAIqO,oBAAA,EAAsBG,mBAAmB;QAChE,KAAApb,MAAA,CAAOkT,IAAA,GAAOwH,cAAA,GAAiBW,UAAA;QAE9B,MAAAC,mBAAA,GAAsB3U,IAAA,CAAKiG,GAAA,CAAI,KAAK2G,QAAA,EAAU,CAACuH,MAAA,CAAOI,MAAA,GAASJ,MAAA,CAAOK,MAAA,CAAO9Y,MAAA,CAAQ;QACrF,MAAAkZ,kBAAA,GAAqBb,cAAA,GAAiB,KAAKrH,WAAA;QAEjD,MAAMmI,SAAA,GAAY7U,IAAA,CAAKiG,GAAA,CAAI0O,mBAAA,EAAqBC,kBAAkB;QAC7D,KAAAvb,MAAA,CAAOsT,GAAA,GAAMoH,cAAA,GAAiBc,SAAA;QAEnC,KAAKxb,MAAA,CAAO4T,sBAAA;MAAuB,OAC9B;QACL,IAAI6H,MAAA,GAAS;QAEb,IAAI,KAAKzb,MAAA,CAAOkT,IAAA,IAAQ,KAAKD,YAAA,EAAc;UACpC,KAAAjT,MAAA,CAAOkT,IAAA,GAAO,KAAKD,YAAA;UACfwI,MAAA;QACX;QAEA,IAAI,KAAKzb,MAAA,CAAOsT,GAAA,IAAO,KAAKD,WAAA,EAAa;UAClC,KAAArT,MAAA,CAAOsT,GAAA,GAAM,KAAKD,WAAA;UACdoI,MAAA;QACX;QAEA,IAAIA,MAAA,EAAQ;UACV,KAAKzb,MAAA,CAAO4T,sBAAA;QACd;MACF;IACF;EACF;EAAA;AAAA;AAAA;AAAA;EA4RO8H,iBAAiB7E,KAAA,EAAsB;IAC5C,KAAK/V,OAAA,CAAQ6a,OAAA,GAAU9E,KAAA;IAEvB,KAAK7U,aAAA,CAAc7B,YAAY;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;EA2bQwV,0BAA0B3V,MAAA,GAAyB,MAAME,MAAA,GAAyB,MAAY;IACpG,IAAIF,MAAA,EAAQ;MACV,IAAID,eAAA,CAAgBC,MAAA,EAAQ;QACVD,eAAA,CAAAC,MAAA,CAAO8J,IAAA,CAAK9J,MAAM;MAAA,OAC7B;QACWD,eAAA,CAAAC,MAAA,GAASA,MAAA,CAAO+L,KAAA;MAClC;IAAA,OACK;MACLhM,eAAA,CAAgBC,MAAA,GAAS;IAC3B;IAEA,IAAIE,MAAA,EAAQ;MACV,IAAIH,eAAA,CAAgBG,MAAA,EAAQ;QACVH,eAAA,CAAAG,MAAA,CAAO4J,IAAA,CAAK5J,MAAM;MAAA,OAC7B;QACWH,eAAA,CAAAG,MAAA,GAASA,MAAA,CAAO6L,KAAA;MAClC;IAAA,OACK;MACLhM,eAAA,CAAgBG,MAAA,GAAS;IAC3B;EACF;AAmYF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}