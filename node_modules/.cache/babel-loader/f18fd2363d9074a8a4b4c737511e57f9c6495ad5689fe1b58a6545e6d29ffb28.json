{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { MarchingCubes as MarchingCubes$1 } from 'three-stdlib';\nimport { useFrame } from '@react-three/fiber';\nconst globalContext = /* @__PURE__ */React.createContext(null);\nconst MarchingCubes = /* @__PURE__ */React.forwardRef(({\n  resolution = 28,\n  maxPolyCount = 10000,\n  enableUvs = false,\n  enableColors = false,\n  children,\n  ...props\n}, ref) => {\n  const marchingCubesRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => marchingCubesRef.current, []);\n  const marchingCubes = React.useMemo(() => new MarchingCubes$1(resolution, null, enableUvs, enableColors, maxPolyCount), [resolution, maxPolyCount, enableUvs, enableColors]);\n  const api = React.useMemo(() => ({\n    getParent: () => marchingCubesRef\n  }), []);\n  useFrame(() => {\n    marchingCubes.update();\n    marchingCubes.reset();\n  }, -1); // To make sure the reset runs before the balls or planes are added\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: marchingCubes,\n    ref: marchingCubesRef\n  }, props), /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children)));\n});\nconst MarchingCube = /* @__PURE__ */React.forwardRef(({\n  strength = 0.5,\n  subtract = 12,\n  color,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const cubeRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cubeRef.current, []);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (!parentRef.current || !cubeRef.current) return;\n    cubeRef.current.getWorldPosition(vec);\n    parentRef.current.addBall(0.5 + vec.x * 0.5, 0.5 + vec.y * 0.5, 0.5 + vec.z * 0.5, strength, subtract, color);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: cubeRef\n  }, props));\n});\nconst MarchingPlane = /* @__PURE__ */React.forwardRef(({\n  planeType: _planeType = 'x',\n  strength = 0.5,\n  subtract = 12,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const wallRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => wallRef.current, []);\n  const planeType = React.useMemo(() => _planeType === 'x' ? 'addPlaneX' : _planeType === 'y' ? 'addPlaneY' : 'addPlaneZ', [_planeType]);\n  useFrame(() => {\n    if (!parentRef.current || !wallRef.current) return;\n    parentRef.current[planeType](strength, subtract);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: wallRef\n  }, props));\n});\nexport { MarchingCube, MarchingCubes, MarchingPlane };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "MarchingCubes", "MarchingCubes$1", "useFrame", "globalContext", "createContext", "forwardRef", "resolution", "maxPolyCount", "enableUvs", "enableColors", "children", "props", "ref", "marchingCubesRef", "useRef", "useImperativeHandle", "current", "marchingCubes", "useMemo", "api", "getParent", "update", "reset", "createElement", "Fragment", "object", "Provider", "value", "MarchingCube", "strength", "subtract", "color", "useContext", "parentRef", "cubeRef", "vec", "Vector3", "state", "getWorldPosition", "addBall", "x", "y", "z", "MarchingPlane", "planeType", "_planeType", "wallRef"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MarchingCubes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { MarchingCubes as MarchingCubes$1 } from 'three-stdlib';\nimport { useFrame } from '@react-three/fiber';\n\nconst globalContext = /* @__PURE__ */React.createContext(null);\nconst MarchingCubes = /* @__PURE__ */React.forwardRef(({\n  resolution = 28,\n  maxPolyCount = 10000,\n  enableUvs = false,\n  enableColors = false,\n  children,\n  ...props\n}, ref) => {\n  const marchingCubesRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => marchingCubesRef.current, []);\n  const marchingCubes = React.useMemo(() => new MarchingCubes$1(resolution, null, enableUvs, enableColors, maxPolyCount), [resolution, maxPolyCount, enableUvs, enableColors]);\n  const api = React.useMemo(() => ({\n    getParent: () => marchingCubesRef\n  }), []);\n  useFrame(() => {\n    marchingCubes.update();\n    marchingCubes.reset();\n  }, -1); // To make sure the reset runs before the balls or planes are added\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: marchingCubes,\n    ref: marchingCubesRef\n  }, props), /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children)));\n});\nconst MarchingCube = /* @__PURE__ */React.forwardRef(({\n  strength = 0.5,\n  subtract = 12,\n  color,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const cubeRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cubeRef.current, []);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (!parentRef.current || !cubeRef.current) return;\n    cubeRef.current.getWorldPosition(vec);\n    parentRef.current.addBall(0.5 + vec.x * 0.5, 0.5 + vec.y * 0.5, 0.5 + vec.z * 0.5, strength, subtract, color);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: cubeRef\n  }, props));\n});\nconst MarchingPlane = /* @__PURE__ */React.forwardRef(({\n  planeType: _planeType = 'x',\n  strength = 0.5,\n  subtract = 12,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const wallRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => wallRef.current, []);\n  const planeType = React.useMemo(() => _planeType === 'x' ? 'addPlaneX' : _planeType === 'y' ? 'addPlaneY' : 'addPlaneZ', [_planeType]);\n  useFrame(() => {\n    if (!parentRef.current || !wallRef.current) return;\n    parentRef.current[planeType](strength, subtract);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: wallRef\n  }, props));\n});\n\nexport { MarchingCube, MarchingCubes, MarchingPlane };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,eAAe,QAAQ,cAAc;AAC/D,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,aAAa,GAAG,eAAeJ,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AAC9D,MAAMJ,aAAa,GAAG,eAAeD,KAAK,CAACM,UAAU,CAAC,CAAC;EACrDC,UAAU,GAAG,EAAE;EACfC,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,YAAY,GAAG,KAAK;EACpBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,gBAAgB,GAAGd,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAC3Cf,KAAK,CAACgB,mBAAmB,CAACH,GAAG,EAAE,MAAMC,gBAAgB,CAACG,OAAO,EAAE,EAAE,CAAC;EAClE,MAAMC,aAAa,GAAGlB,KAAK,CAACmB,OAAO,CAAC,MAAM,IAAIjB,eAAe,CAACK,UAAU,EAAE,IAAI,EAAEE,SAAS,EAAEC,YAAY,EAAEF,YAAY,CAAC,EAAE,CAACD,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,YAAY,CAAC,CAAC;EAC5K,MAAMU,GAAG,GAAGpB,KAAK,CAACmB,OAAO,CAAC,OAAO;IAC/BE,SAAS,EAAEA,CAAA,KAAMP;EACnB,CAAC,CAAC,EAAE,EAAE,CAAC;EACPX,QAAQ,CAAC,MAAM;IACbe,aAAa,CAACI,MAAM,CAAC,CAAC;IACtBJ,aAAa,CAACK,KAAK,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAER,OAAO,aAAavB,KAAK,CAACwB,aAAa,CAACxB,KAAK,CAACyB,QAAQ,EAAE,IAAI,EAAE,aAAazB,KAAK,CAACwB,aAAa,CAAC,WAAW,EAAE1B,QAAQ,CAAC;IACnH4B,MAAM,EAAER,aAAa;IACrBL,GAAG,EAAEC;EACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAaZ,KAAK,CAACwB,aAAa,CAACpB,aAAa,CAACuB,QAAQ,EAAE;IAClEC,KAAK,EAAER;EACT,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AACF,MAAMkB,YAAY,GAAG,eAAe7B,KAAK,CAACM,UAAU,CAAC,CAAC;EACpDwB,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG,EAAE;EACbC,KAAK;EACL,GAAGpB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJQ;EACF,CAAC,GAAGrB,KAAK,CAACiC,UAAU,CAAC7B,aAAa,CAAC;EACnC,MAAM8B,SAAS,GAAGlC,KAAK,CAACmB,OAAO,CAAC,MAAME,SAAS,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAC/D,MAAMc,OAAO,GAAGnC,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAClCf,KAAK,CAACgB,mBAAmB,CAACH,GAAG,EAAE,MAAMsB,OAAO,CAAClB,OAAO,EAAE,EAAE,CAAC;EACzD,MAAMmB,GAAG,GAAG,IAAIrC,KAAK,CAACsC,OAAO,CAAC,CAAC;EAC/BlC,QAAQ,CAACmC,KAAK,IAAI;IAChB,IAAI,CAACJ,SAAS,CAACjB,OAAO,IAAI,CAACkB,OAAO,CAAClB,OAAO,EAAE;IAC5CkB,OAAO,CAAClB,OAAO,CAACsB,gBAAgB,CAACH,GAAG,CAAC;IACrCF,SAAS,CAACjB,OAAO,CAACuB,OAAO,CAAC,GAAG,GAAGJ,GAAG,CAACK,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGL,GAAG,CAACM,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGN,GAAG,CAACO,CAAC,GAAG,GAAG,EAAEb,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,CAAC;EAC/G,CAAC,CAAC;EACF,OAAO,aAAahC,KAAK,CAACwB,aAAa,CAAC,OAAO,EAAE1B,QAAQ,CAAC;IACxDe,GAAG,EAAEsB;EACP,CAAC,EAAEvB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,MAAMgC,aAAa,GAAG,eAAe5C,KAAK,CAACM,UAAU,CAAC,CAAC;EACrDuC,SAAS,EAAEC,UAAU,GAAG,GAAG;EAC3BhB,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG,EAAE;EACb,GAAGnB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJQ;EACF,CAAC,GAAGrB,KAAK,CAACiC,UAAU,CAAC7B,aAAa,CAAC;EACnC,MAAM8B,SAAS,GAAGlC,KAAK,CAACmB,OAAO,CAAC,MAAME,SAAS,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAC/D,MAAM0B,OAAO,GAAG/C,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAClCf,KAAK,CAACgB,mBAAmB,CAACH,GAAG,EAAE,MAAMkC,OAAO,CAAC9B,OAAO,EAAE,EAAE,CAAC;EACzD,MAAM4B,SAAS,GAAG7C,KAAK,CAACmB,OAAO,CAAC,MAAM2B,UAAU,KAAK,GAAG,GAAG,WAAW,GAAGA,UAAU,KAAK,GAAG,GAAG,WAAW,GAAG,WAAW,EAAE,CAACA,UAAU,CAAC,CAAC;EACtI3C,QAAQ,CAAC,MAAM;IACb,IAAI,CAAC+B,SAAS,CAACjB,OAAO,IAAI,CAAC8B,OAAO,CAAC9B,OAAO,EAAE;IAC5CiB,SAAS,CAACjB,OAAO,CAAC4B,SAAS,CAAC,CAACf,QAAQ,EAAEC,QAAQ,CAAC;EAClD,CAAC,CAAC;EACF,OAAO,aAAa/B,KAAK,CAACwB,aAAa,CAAC,OAAO,EAAE1B,QAAQ,CAAC;IACxDe,GAAG,EAAEkC;EACP,CAAC,EAAEnC,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASiB,YAAY,EAAE5B,aAAa,EAAE2C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}