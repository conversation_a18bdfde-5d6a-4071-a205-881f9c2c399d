{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { useThree, useFrame, context as context$1 } from '@react-three/fiber';\nimport { easing } from 'maath';\nconst context = /* @__PURE__ */React.createContext(null);\nfunction useScroll() {\n  return React.useContext(context);\n}\nfunction ScrollControls({\n  eps = 0.00001,\n  enabled = true,\n  infinite,\n  horizontal,\n  pages = 1,\n  distance = 1,\n  damping = 0.25,\n  maxSpeed = Infinity,\n  prepend = false,\n  style = {},\n  children\n}) {\n  const {\n    get,\n    setEvents,\n    gl,\n    size,\n    invalidate,\n    events\n  } = useThree();\n  const [el] = React.useState(() => document.createElement('div'));\n  const [fill] = React.useState(() => document.createElement('div'));\n  const [fixed] = React.useState(() => document.createElement('div'));\n  const target = gl.domElement.parentNode;\n  const scroll = React.useRef(0);\n  const state = React.useMemo(() => {\n    const state = {\n      el,\n      eps,\n      fill,\n      fixed,\n      horizontal,\n      damping,\n      offset: 0,\n      delta: 0,\n      scroll,\n      pages,\n      // 0-1 for a range between from -> from + distance\n      range(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset < start ? 0 : this.offset > end ? 1 : (this.offset - start) / (end - start);\n      },\n      // 0-1-0 for a range between from -> from + distance\n      curve(from, distance, margin = 0) {\n        return Math.sin(this.range(from, distance, margin) * Math.PI);\n      },\n      // true/false for a range between from -> from + distance\n      visible(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset >= start && this.offset <= end;\n      }\n    };\n    return state;\n  }, [eps, damping, horizontal, pages]);\n  React.useEffect(() => {\n    el.style.position = 'absolute';\n    el.style.width = '100%';\n    el.style.height = '100%';\n    el.style[horizontal ? 'overflowX' : 'overflowY'] = 'auto';\n    el.style[horizontal ? 'overflowY' : 'overflowX'] = 'hidden';\n    el.style.top = '0px';\n    el.style.left = '0px';\n    for (const key in style) {\n      el.style[key] = style[key];\n    }\n    fixed.style.position = 'sticky';\n    fixed.style.top = '0px';\n    fixed.style.left = '0px';\n    fixed.style.width = '100%';\n    fixed.style.height = '100%';\n    fixed.style.overflow = 'hidden';\n    el.appendChild(fixed);\n    fill.style.height = horizontal ? '100%' : `${pages * distance * 100}%`;\n    fill.style.width = horizontal ? `${pages * distance * 100}%` : '100%';\n    fill.style.pointerEvents = 'none';\n    el.appendChild(fill);\n    if (prepend) target.prepend(el);else target.appendChild(el);\n\n    // Init scroll one pixel in to allow upward/leftward scroll\n    el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n    const oldTarget = events.connected || gl.domElement;\n    requestAnimationFrame(() => events.connect == null ? void 0 : events.connect(el));\n    const oldCompute = get().events.compute;\n    setEvents({\n      compute(event, state) {\n        // we are using boundingClientRect because we could not rely on target.offsetTop as canvas could be positioned anywhere in dom\n        const {\n          left,\n          top\n        } = target.getBoundingClientRect();\n        const offsetX = event.clientX - left;\n        const offsetY = event.clientY - top;\n        state.pointer.set(offsetX / state.size.width * 2 - 1, -(offsetY / state.size.height) * 2 + 1);\n        state.raycaster.setFromCamera(state.pointer, state.camera);\n      }\n    });\n    return () => {\n      target.removeChild(el);\n      setEvents({\n        compute: oldCompute\n      });\n      events.connect == null || events.connect(oldTarget);\n    };\n  }, [pages, distance, horizontal, el, fill, fixed, target]);\n  React.useEffect(() => {\n    if (events.connected === el) {\n      const containerLength = size[horizontal ? 'width' : 'height'];\n      const scrollLength = el[horizontal ? 'scrollWidth' : 'scrollHeight'];\n      const scrollThreshold = scrollLength - containerLength;\n      let current = 0;\n      let disableScroll = true;\n      let firstRun = true;\n      const onScroll = () => {\n        // Prevent first scroll because it is indirectly caused by the one pixel offset\n        if (!enabled || firstRun) return;\n        invalidate();\n        current = el[horizontal ? 'scrollLeft' : 'scrollTop'];\n        scroll.current = current / scrollThreshold;\n        if (infinite) {\n          if (!disableScroll) {\n            if (current >= scrollThreshold) {\n              const damp = 1 - state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n              scroll.current = state.offset = -damp;\n              disableScroll = true;\n            } else if (current <= 0) {\n              const damp = 1 + state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = scrollLength;\n              scroll.current = state.offset = damp;\n              disableScroll = true;\n            }\n          }\n          if (disableScroll) setTimeout(() => disableScroll = false, 40);\n        }\n      };\n      el.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n      requestAnimationFrame(() => firstRun = false);\n      const onWheel = e => el.scrollLeft += e.deltaY / 2;\n      if (horizontal) el.addEventListener('wheel', onWheel, {\n        passive: true\n      });\n      return () => {\n        el.removeEventListener('scroll', onScroll);\n        if (horizontal) el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [el, events, size, infinite, state, invalidate, horizontal, enabled]);\n  let last = 0;\n  useFrame((_, delta) => {\n    last = state.offset;\n    easing.damp(state, 'offset', scroll.current, damping, delta, maxSpeed, undefined, eps);\n    easing.damp(state, 'delta', Math.abs(last - state.offset), damping, delta, maxSpeed, undefined, eps);\n    if (state.delta > eps) invalidate();\n  });\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, children);\n}\nconst ScrollCanvas = /* @__PURE__ */React.forwardRef(({\n  children\n}, ref) => {\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const state = useScroll();\n  const {\n    width,\n    height\n  } = useThree(state => state.viewport);\n  useFrame(() => {\n    group.current.position.x = state.horizontal ? -width * (state.pages - 1) * state.offset : 0;\n    group.current.position.y = state.horizontal ? 0 : height * (state.pages - 1) * state.offset;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: group\n  }, children);\n});\nconst ScrollHtml = /*#__PURE__*/React.forwardRef(({\n  children,\n  style,\n  ...props\n}, ref) => {\n  const state = useScroll();\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const fiberState = React.useContext(context$1);\n  const root = React.useMemo(() => ReactDOM.createRoot(state.fixed), [state.fixed]);\n  useFrame(() => {\n    if (state.delta > state.eps) {\n      group.current.style.transform = `translate3d(${state.horizontal ? -width * (state.pages - 1) * state.offset : 0}px,${state.horizontal ? 0 : height * (state.pages - 1) * -state.offset}px,0)`;\n    }\n  });\n  root.render(/*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: group,\n    style: {\n      ...style,\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      willChange: 'transform'\n    }\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(context$1.Provider, {\n    value: fiberState\n  }, children))));\n  return null;\n});\nconst Scroll = /* @__PURE__ */React.forwardRef(({\n  html,\n  ...props\n}, ref) => {\n  const El = html ? ScrollHtml : ScrollCanvas;\n  return /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref\n  }, props));\n});\nexport { Scroll, ScrollControls, useScroll };", "map": {"version": 3, "names": ["_extends", "React", "ReactDOM", "useThree", "useFrame", "context", "context$1", "easing", "createContext", "useScroll", "useContext", "ScrollControls", "eps", "enabled", "infinite", "horizontal", "pages", "distance", "damping", "maxSpeed", "Infinity", "prepend", "style", "children", "get", "setEvents", "gl", "size", "invalidate", "events", "el", "useState", "document", "createElement", "fill", "fixed", "target", "dom<PERSON>lement", "parentNode", "scroll", "useRef", "state", "useMemo", "offset", "delta", "range", "from", "margin", "start", "end", "curve", "Math", "sin", "PI", "visible", "useEffect", "position", "width", "height", "top", "left", "key", "overflow", "append<PERSON><PERSON><PERSON>", "pointerEvents", "old<PERSON><PERSON>get", "connected", "requestAnimationFrame", "connect", "oldCompute", "compute", "event", "getBoundingClientRect", "offsetX", "clientX", "offsetY", "clientY", "pointer", "set", "raycaster", "setFromCamera", "camera", "<PERSON><PERSON><PERSON><PERSON>", "containerLength", "<PERSON><PERSON><PERSON><PERSON>", "scrollThreshold", "current", "disableScroll", "firstRun", "onScroll", "damp", "setTimeout", "addEventListener", "passive", "onWheel", "e", "scrollLeft", "deltaY", "removeEventListener", "last", "_", "undefined", "abs", "Provider", "value", "ScrollCanvas", "forwardRef", "ref", "group", "useImperativeHandle", "viewport", "x", "y", "ScrollHtml", "props", "fiberState", "root", "createRoot", "transform", "render", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "html", "El"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/ScrollControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { useThree, useFrame, context as context$1 } from '@react-three/fiber';\nimport { easing } from 'maath';\n\nconst context = /* @__PURE__ */React.createContext(null);\nfunction useScroll() {\n  return React.useContext(context);\n}\nfunction ScrollControls({\n  eps = 0.00001,\n  enabled = true,\n  infinite,\n  horizontal,\n  pages = 1,\n  distance = 1,\n  damping = 0.25,\n  maxSpeed = Infinity,\n  prepend = false,\n  style = {},\n  children\n}) {\n  const {\n    get,\n    setEvents,\n    gl,\n    size,\n    invalidate,\n    events\n  } = useThree();\n  const [el] = React.useState(() => document.createElement('div'));\n  const [fill] = React.useState(() => document.createElement('div'));\n  const [fixed] = React.useState(() => document.createElement('div'));\n  const target = gl.domElement.parentNode;\n  const scroll = React.useRef(0);\n  const state = React.useMemo(() => {\n    const state = {\n      el,\n      eps,\n      fill,\n      fixed,\n      horizontal,\n      damping,\n      offset: 0,\n      delta: 0,\n      scroll,\n      pages,\n      // 0-1 for a range between from -> from + distance\n      range(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset < start ? 0 : this.offset > end ? 1 : (this.offset - start) / (end - start);\n      },\n      // 0-1-0 for a range between from -> from + distance\n      curve(from, distance, margin = 0) {\n        return Math.sin(this.range(from, distance, margin) * Math.PI);\n      },\n      // true/false for a range between from -> from + distance\n      visible(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset >= start && this.offset <= end;\n      }\n    };\n    return state;\n  }, [eps, damping, horizontal, pages]);\n  React.useEffect(() => {\n    el.style.position = 'absolute';\n    el.style.width = '100%';\n    el.style.height = '100%';\n    el.style[horizontal ? 'overflowX' : 'overflowY'] = 'auto';\n    el.style[horizontal ? 'overflowY' : 'overflowX'] = 'hidden';\n    el.style.top = '0px';\n    el.style.left = '0px';\n    for (const key in style) {\n      el.style[key] = style[key];\n    }\n    fixed.style.position = 'sticky';\n    fixed.style.top = '0px';\n    fixed.style.left = '0px';\n    fixed.style.width = '100%';\n    fixed.style.height = '100%';\n    fixed.style.overflow = 'hidden';\n    el.appendChild(fixed);\n    fill.style.height = horizontal ? '100%' : `${pages * distance * 100}%`;\n    fill.style.width = horizontal ? `${pages * distance * 100}%` : '100%';\n    fill.style.pointerEvents = 'none';\n    el.appendChild(fill);\n    if (prepend) target.prepend(el);else target.appendChild(el);\n\n    // Init scroll one pixel in to allow upward/leftward scroll\n    el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n    const oldTarget = events.connected || gl.domElement;\n    requestAnimationFrame(() => events.connect == null ? void 0 : events.connect(el));\n    const oldCompute = get().events.compute;\n    setEvents({\n      compute(event, state) {\n        // we are using boundingClientRect because we could not rely on target.offsetTop as canvas could be positioned anywhere in dom\n        const {\n          left,\n          top\n        } = target.getBoundingClientRect();\n        const offsetX = event.clientX - left;\n        const offsetY = event.clientY - top;\n        state.pointer.set(offsetX / state.size.width * 2 - 1, -(offsetY / state.size.height) * 2 + 1);\n        state.raycaster.setFromCamera(state.pointer, state.camera);\n      }\n    });\n    return () => {\n      target.removeChild(el);\n      setEvents({\n        compute: oldCompute\n      });\n      events.connect == null || events.connect(oldTarget);\n    };\n  }, [pages, distance, horizontal, el, fill, fixed, target]);\n  React.useEffect(() => {\n    if (events.connected === el) {\n      const containerLength = size[horizontal ? 'width' : 'height'];\n      const scrollLength = el[horizontal ? 'scrollWidth' : 'scrollHeight'];\n      const scrollThreshold = scrollLength - containerLength;\n      let current = 0;\n      let disableScroll = true;\n      let firstRun = true;\n      const onScroll = () => {\n        // Prevent first scroll because it is indirectly caused by the one pixel offset\n        if (!enabled || firstRun) return;\n        invalidate();\n        current = el[horizontal ? 'scrollLeft' : 'scrollTop'];\n        scroll.current = current / scrollThreshold;\n        if (infinite) {\n          if (!disableScroll) {\n            if (current >= scrollThreshold) {\n              const damp = 1 - state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n              scroll.current = state.offset = -damp;\n              disableScroll = true;\n            } else if (current <= 0) {\n              const damp = 1 + state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = scrollLength;\n              scroll.current = state.offset = damp;\n              disableScroll = true;\n            }\n          }\n          if (disableScroll) setTimeout(() => disableScroll = false, 40);\n        }\n      };\n      el.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n      requestAnimationFrame(() => firstRun = false);\n      const onWheel = e => el.scrollLeft += e.deltaY / 2;\n      if (horizontal) el.addEventListener('wheel', onWheel, {\n        passive: true\n      });\n      return () => {\n        el.removeEventListener('scroll', onScroll);\n        if (horizontal) el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [el, events, size, infinite, state, invalidate, horizontal, enabled]);\n  let last = 0;\n  useFrame((_, delta) => {\n    last = state.offset;\n    easing.damp(state, 'offset', scroll.current, damping, delta, maxSpeed, undefined, eps);\n    easing.damp(state, 'delta', Math.abs(last - state.offset), damping, delta, maxSpeed, undefined, eps);\n    if (state.delta > eps) invalidate();\n  });\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, children);\n}\nconst ScrollCanvas = /* @__PURE__ */React.forwardRef(({\n  children\n}, ref) => {\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const state = useScroll();\n  const {\n    width,\n    height\n  } = useThree(state => state.viewport);\n  useFrame(() => {\n    group.current.position.x = state.horizontal ? -width * (state.pages - 1) * state.offset : 0;\n    group.current.position.y = state.horizontal ? 0 : height * (state.pages - 1) * state.offset;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: group\n  }, children);\n});\nconst ScrollHtml = /*#__PURE__*/React.forwardRef(({\n  children,\n  style,\n  ...props\n}, ref) => {\n  const state = useScroll();\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const fiberState = React.useContext(context$1);\n  const root = React.useMemo(() => ReactDOM.createRoot(state.fixed), [state.fixed]);\n  useFrame(() => {\n    if (state.delta > state.eps) {\n      group.current.style.transform = `translate3d(${state.horizontal ? -width * (state.pages - 1) * state.offset : 0}px,${state.horizontal ? 0 : height * (state.pages - 1) * -state.offset}px,0)`;\n    }\n  });\n  root.render(/*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: group,\n    style: {\n      ...style,\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      willChange: 'transform'\n    }\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(context$1.Provider, {\n    value: fiberState\n  }, children))));\n  return null;\n});\nconst Scroll = /* @__PURE__ */React.forwardRef(({\n  html,\n  ...props\n}, ref) => {\n  const El = html ? ScrollHtml : ScrollCanvas;\n  return /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref\n  }, props));\n});\n\nexport { Scroll, ScrollControls, useScroll };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,IAAIC,SAAS,QAAQ,oBAAoB;AAC7E,SAASC,MAAM,QAAQ,OAAO;AAE9B,MAAMF,OAAO,GAAG,eAAeJ,KAAK,CAACO,aAAa,CAAC,IAAI,CAAC;AACxD,SAASC,SAASA,CAAA,EAAG;EACnB,OAAOR,KAAK,CAACS,UAAU,CAACL,OAAO,CAAC;AAClC;AACA,SAASM,cAAcA,CAAC;EACtBC,GAAG,GAAG,OAAO;EACbC,OAAO,GAAG,IAAI;EACdC,QAAQ;EACRC,UAAU;EACVC,KAAK,GAAG,CAAC;EACTC,QAAQ,GAAG,CAAC;EACZC,OAAO,GAAG,IAAI;EACdC,QAAQ,GAAGC,QAAQ;EACnBC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG,CAAC,CAAC;EACVC;AACF,CAAC,EAAE;EACD,MAAM;IACJC,GAAG;IACHC,SAAS;IACTC,EAAE;IACFC,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,GAAG1B,QAAQ,CAAC,CAAC;EACd,MAAM,CAAC2B,EAAE,CAAC,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,MAAMC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChE,MAAM,CAACC,IAAI,CAAC,GAAGjC,KAAK,CAAC8B,QAAQ,CAAC,MAAMC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;EAClE,MAAM,CAACE,KAAK,CAAC,GAAGlC,KAAK,CAAC8B,QAAQ,CAAC,MAAMC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;EACnE,MAAMG,MAAM,GAAGV,EAAE,CAACW,UAAU,CAACC,UAAU;EACvC,MAAMC,MAAM,GAAGtC,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGxC,KAAK,CAACyC,OAAO,CAAC,MAAM;IAChC,MAAMD,KAAK,GAAG;MACZX,EAAE;MACFlB,GAAG;MACHsB,IAAI;MACJC,KAAK;MACLpB,UAAU;MACVG,OAAO;MACPyB,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRL,MAAM;MACNvB,KAAK;MACL;MACA6B,KAAKA,CAACC,IAAI,EAAE7B,QAAQ,EAAE8B,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMC,KAAK,GAAGF,IAAI,GAAGC,MAAM;QAC3B,MAAME,GAAG,GAAGD,KAAK,GAAG/B,QAAQ,GAAG8B,MAAM,GAAG,CAAC;QACzC,OAAO,IAAI,CAACJ,MAAM,GAAGK,KAAK,GAAG,CAAC,GAAG,IAAI,CAACL,MAAM,GAAGM,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAACN,MAAM,GAAGK,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;MAChG,CAAC;MACD;MACAE,KAAKA,CAACJ,IAAI,EAAE7B,QAAQ,EAAE8B,MAAM,GAAG,CAAC,EAAE;QAChC,OAAOI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACP,KAAK,CAACC,IAAI,EAAE7B,QAAQ,EAAE8B,MAAM,CAAC,GAAGI,IAAI,CAACE,EAAE,CAAC;MAC/D,CAAC;MACD;MACAC,OAAOA,CAACR,IAAI,EAAE7B,QAAQ,EAAE8B,MAAM,GAAG,CAAC,EAAE;QAClC,MAAMC,KAAK,GAAGF,IAAI,GAAGC,MAAM;QAC3B,MAAME,GAAG,GAAGD,KAAK,GAAG/B,QAAQ,GAAG8B,MAAM,GAAG,CAAC;QACzC,OAAO,IAAI,CAACJ,MAAM,IAAIK,KAAK,IAAI,IAAI,CAACL,MAAM,IAAIM,GAAG;MACnD;IACF,CAAC;IACD,OAAOR,KAAK;EACd,CAAC,EAAE,CAAC7B,GAAG,EAAEM,OAAO,EAAEH,UAAU,EAAEC,KAAK,CAAC,CAAC;EACrCf,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpBzB,EAAE,CAACR,KAAK,CAACkC,QAAQ,GAAG,UAAU;IAC9B1B,EAAE,CAACR,KAAK,CAACmC,KAAK,GAAG,MAAM;IACvB3B,EAAE,CAACR,KAAK,CAACoC,MAAM,GAAG,MAAM;IACxB5B,EAAE,CAACR,KAAK,CAACP,UAAU,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,MAAM;IACzDe,EAAE,CAACR,KAAK,CAACP,UAAU,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,QAAQ;IAC3De,EAAE,CAACR,KAAK,CAACqC,GAAG,GAAG,KAAK;IACpB7B,EAAE,CAACR,KAAK,CAACsC,IAAI,GAAG,KAAK;IACrB,KAAK,MAAMC,GAAG,IAAIvC,KAAK,EAAE;MACvBQ,EAAE,CAACR,KAAK,CAACuC,GAAG,CAAC,GAAGvC,KAAK,CAACuC,GAAG,CAAC;IAC5B;IACA1B,KAAK,CAACb,KAAK,CAACkC,QAAQ,GAAG,QAAQ;IAC/BrB,KAAK,CAACb,KAAK,CAACqC,GAAG,GAAG,KAAK;IACvBxB,KAAK,CAACb,KAAK,CAACsC,IAAI,GAAG,KAAK;IACxBzB,KAAK,CAACb,KAAK,CAACmC,KAAK,GAAG,MAAM;IAC1BtB,KAAK,CAACb,KAAK,CAACoC,MAAM,GAAG,MAAM;IAC3BvB,KAAK,CAACb,KAAK,CAACwC,QAAQ,GAAG,QAAQ;IAC/BhC,EAAE,CAACiC,WAAW,CAAC5B,KAAK,CAAC;IACrBD,IAAI,CAACZ,KAAK,CAACoC,MAAM,GAAG3C,UAAU,GAAG,MAAM,GAAG,GAAGC,KAAK,GAAGC,QAAQ,GAAG,GAAG,GAAG;IACtEiB,IAAI,CAACZ,KAAK,CAACmC,KAAK,GAAG1C,UAAU,GAAG,GAAGC,KAAK,GAAGC,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM;IACrEiB,IAAI,CAACZ,KAAK,CAAC0C,aAAa,GAAG,MAAM;IACjClC,EAAE,CAACiC,WAAW,CAAC7B,IAAI,CAAC;IACpB,IAAIb,OAAO,EAAEe,MAAM,CAACf,OAAO,CAACS,EAAE,CAAC,CAAC,KAAKM,MAAM,CAAC2B,WAAW,CAACjC,EAAE,CAAC;;IAE3D;IACAA,EAAE,CAACf,UAAU,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC;IAC/C,MAAMkD,SAAS,GAAGpC,MAAM,CAACqC,SAAS,IAAIxC,EAAE,CAACW,UAAU;IACnD8B,qBAAqB,CAAC,MAAMtC,MAAM,CAACuC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvC,MAAM,CAACuC,OAAO,CAACtC,EAAE,CAAC,CAAC;IACjF,MAAMuC,UAAU,GAAG7C,GAAG,CAAC,CAAC,CAACK,MAAM,CAACyC,OAAO;IACvC7C,SAAS,CAAC;MACR6C,OAAOA,CAACC,KAAK,EAAE9B,KAAK,EAAE;QACpB;QACA,MAAM;UACJmB,IAAI;UACJD;QACF,CAAC,GAAGvB,MAAM,CAACoC,qBAAqB,CAAC,CAAC;QAClC,MAAMC,OAAO,GAAGF,KAAK,CAACG,OAAO,GAAGd,IAAI;QACpC,MAAMe,OAAO,GAAGJ,KAAK,CAACK,OAAO,GAAGjB,GAAG;QACnClB,KAAK,CAACoC,OAAO,CAACC,GAAG,CAACL,OAAO,GAAGhC,KAAK,CAACd,IAAI,CAAC8B,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEkB,OAAO,GAAGlC,KAAK,CAACd,IAAI,CAAC+B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7FjB,KAAK,CAACsC,SAAS,CAACC,aAAa,CAACvC,KAAK,CAACoC,OAAO,EAAEpC,KAAK,CAACwC,MAAM,CAAC;MAC5D;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX7C,MAAM,CAAC8C,WAAW,CAACpD,EAAE,CAAC;MACtBL,SAAS,CAAC;QACR6C,OAAO,EAAED;MACX,CAAC,CAAC;MACFxC,MAAM,CAACuC,OAAO,IAAI,IAAI,IAAIvC,MAAM,CAACuC,OAAO,CAACH,SAAS,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CAACjD,KAAK,EAAEC,QAAQ,EAAEF,UAAU,EAAEe,EAAE,EAAEI,IAAI,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAC;EAC1DnC,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB,IAAI1B,MAAM,CAACqC,SAAS,KAAKpC,EAAE,EAAE;MAC3B,MAAMqD,eAAe,GAAGxD,IAAI,CAACZ,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;MAC7D,MAAMqE,YAAY,GAAGtD,EAAE,CAACf,UAAU,GAAG,aAAa,GAAG,cAAc,CAAC;MACpE,MAAMsE,eAAe,GAAGD,YAAY,GAAGD,eAAe;MACtD,IAAIG,OAAO,GAAG,CAAC;MACf,IAAIC,aAAa,GAAG,IAAI;MACxB,IAAIC,QAAQ,GAAG,IAAI;MACnB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;QACrB;QACA,IAAI,CAAC5E,OAAO,IAAI2E,QAAQ,EAAE;QAC1B5D,UAAU,CAAC,CAAC;QACZ0D,OAAO,GAAGxD,EAAE,CAACf,UAAU,GAAG,YAAY,GAAG,WAAW,CAAC;QACrDwB,MAAM,CAAC+C,OAAO,GAAGA,OAAO,GAAGD,eAAe;QAC1C,IAAIvE,QAAQ,EAAE;UACZ,IAAI,CAACyE,aAAa,EAAE;YAClB,IAAID,OAAO,IAAID,eAAe,EAAE;cAC9B,MAAMK,IAAI,GAAG,CAAC,GAAGjD,KAAK,CAACE,MAAM;cAC7Bb,EAAE,CAACf,UAAU,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC;cAC/CwB,MAAM,CAAC+C,OAAO,GAAG7C,KAAK,CAACE,MAAM,GAAG,CAAC+C,IAAI;cACrCH,aAAa,GAAG,IAAI;YACtB,CAAC,MAAM,IAAID,OAAO,IAAI,CAAC,EAAE;cACvB,MAAMI,IAAI,GAAG,CAAC,GAAGjD,KAAK,CAACE,MAAM;cAC7Bb,EAAE,CAACf,UAAU,GAAG,YAAY,GAAG,WAAW,CAAC,GAAGqE,YAAY;cAC1D7C,MAAM,CAAC+C,OAAO,GAAG7C,KAAK,CAACE,MAAM,GAAG+C,IAAI;cACpCH,aAAa,GAAG,IAAI;YACtB;UACF;UACA,IAAIA,aAAa,EAAEI,UAAU,CAAC,MAAMJ,aAAa,GAAG,KAAK,EAAE,EAAE,CAAC;QAChE;MACF,CAAC;MACDzD,EAAE,CAAC8D,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;QACtCI,OAAO,EAAE;MACX,CAAC,CAAC;MACF1B,qBAAqB,CAAC,MAAMqB,QAAQ,GAAG,KAAK,CAAC;MAC7C,MAAMM,OAAO,GAAGC,CAAC,IAAIjE,EAAE,CAACkE,UAAU,IAAID,CAAC,CAACE,MAAM,GAAG,CAAC;MAClD,IAAIlF,UAAU,EAAEe,EAAE,CAAC8D,gBAAgB,CAAC,OAAO,EAAEE,OAAO,EAAE;QACpDD,OAAO,EAAE;MACX,CAAC,CAAC;MACF,OAAO,MAAM;QACX/D,EAAE,CAACoE,mBAAmB,CAAC,QAAQ,EAAET,QAAQ,CAAC;QAC1C,IAAI1E,UAAU,EAAEe,EAAE,CAACoE,mBAAmB,CAAC,OAAO,EAAEJ,OAAO,CAAC;MAC1D,CAAC;IACH;EACF,CAAC,EAAE,CAAChE,EAAE,EAAED,MAAM,EAAEF,IAAI,EAAEb,QAAQ,EAAE2B,KAAK,EAAEb,UAAU,EAAEb,UAAU,EAAEF,OAAO,CAAC,CAAC;EACxE,IAAIsF,IAAI,GAAG,CAAC;EACZ/F,QAAQ,CAAC,CAACgG,CAAC,EAAExD,KAAK,KAAK;IACrBuD,IAAI,GAAG1D,KAAK,CAACE,MAAM;IACnBpC,MAAM,CAACmF,IAAI,CAACjD,KAAK,EAAE,QAAQ,EAAEF,MAAM,CAAC+C,OAAO,EAAEpE,OAAO,EAAE0B,KAAK,EAAEzB,QAAQ,EAAEkF,SAAS,EAAEzF,GAAG,CAAC;IACtFL,MAAM,CAACmF,IAAI,CAACjD,KAAK,EAAE,OAAO,EAAEU,IAAI,CAACmD,GAAG,CAACH,IAAI,GAAG1D,KAAK,CAACE,MAAM,CAAC,EAAEzB,OAAO,EAAE0B,KAAK,EAAEzB,QAAQ,EAAEkF,SAAS,EAAEzF,GAAG,CAAC;IACpG,IAAI6B,KAAK,CAACG,KAAK,GAAGhC,GAAG,EAAEgB,UAAU,CAAC,CAAC;EACrC,CAAC,CAAC;EACF,OAAO,aAAa3B,KAAK,CAACgC,aAAa,CAAC5B,OAAO,CAACkG,QAAQ,EAAE;IACxDC,KAAK,EAAE/D;EACT,CAAC,EAAElB,QAAQ,CAAC;AACd;AACA,MAAMkF,YAAY,GAAG,eAAexG,KAAK,CAACyG,UAAU,CAAC,CAAC;EACpDnF;AACF,CAAC,EAAEoF,GAAG,KAAK;EACT,MAAMC,KAAK,GAAG3G,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAChCvC,KAAK,CAAC4G,mBAAmB,CAACF,GAAG,EAAE,MAAMC,KAAK,CAACtB,OAAO,EAAE,EAAE,CAAC;EACvD,MAAM7C,KAAK,GAAGhC,SAAS,CAAC,CAAC;EACzB,MAAM;IACJgD,KAAK;IACLC;EACF,CAAC,GAAGvD,QAAQ,CAACsC,KAAK,IAAIA,KAAK,CAACqE,QAAQ,CAAC;EACrC1G,QAAQ,CAAC,MAAM;IACbwG,KAAK,CAACtB,OAAO,CAAC9B,QAAQ,CAACuD,CAAC,GAAGtE,KAAK,CAAC1B,UAAU,GAAG,CAAC0C,KAAK,IAAIhB,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,GAAGyB,KAAK,CAACE,MAAM,GAAG,CAAC;IAC3FiE,KAAK,CAACtB,OAAO,CAAC9B,QAAQ,CAACwD,CAAC,GAAGvE,KAAK,CAAC1B,UAAU,GAAG,CAAC,GAAG2C,MAAM,IAAIjB,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,GAAGyB,KAAK,CAACE,MAAM;EAC7F,CAAC,CAAC;EACF,OAAO,aAAa1C,KAAK,CAACgC,aAAa,CAAC,OAAO,EAAE;IAC/C0E,GAAG,EAAEC;EACP,CAAC,EAAErF,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,MAAM0F,UAAU,GAAG,aAAahH,KAAK,CAACyG,UAAU,CAAC,CAAC;EAChDnF,QAAQ;EACRD,KAAK;EACL,GAAG4F;AACL,CAAC,EAAEP,GAAG,KAAK;EACT,MAAMlE,KAAK,GAAGhC,SAAS,CAAC,CAAC;EACzB,MAAMmG,KAAK,GAAG3G,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAChCvC,KAAK,CAAC4G,mBAAmB,CAACF,GAAG,EAAE,MAAMC,KAAK,CAACtB,OAAO,EAAE,EAAE,CAAC;EACvD,MAAM;IACJ7B,KAAK;IACLC;EACF,CAAC,GAAGvD,QAAQ,CAACsC,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC;EACjC,MAAMwF,UAAU,GAAGlH,KAAK,CAACS,UAAU,CAACJ,SAAS,CAAC;EAC9C,MAAM8G,IAAI,GAAGnH,KAAK,CAACyC,OAAO,CAAC,MAAMxC,QAAQ,CAACmH,UAAU,CAAC5E,KAAK,CAACN,KAAK,CAAC,EAAE,CAACM,KAAK,CAACN,KAAK,CAAC,CAAC;EACjF/B,QAAQ,CAAC,MAAM;IACb,IAAIqC,KAAK,CAACG,KAAK,GAAGH,KAAK,CAAC7B,GAAG,EAAE;MAC3BgG,KAAK,CAACtB,OAAO,CAAChE,KAAK,CAACgG,SAAS,GAAG,eAAe7E,KAAK,CAAC1B,UAAU,GAAG,CAAC0C,KAAK,IAAIhB,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,GAAGyB,KAAK,CAACE,MAAM,GAAG,CAAC,MAAMF,KAAK,CAAC1B,UAAU,GAAG,CAAC,GAAG2C,MAAM,IAAIjB,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,GAAG,CAACyB,KAAK,CAACE,MAAM,OAAO;IAC/L;EACF,CAAC,CAAC;EACFyE,IAAI,CAACG,MAAM,CAAC,aAAatH,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAEjC,QAAQ,CAAC;IAC3D2G,GAAG,EAAEC,KAAK;IACVtF,KAAK,EAAE;MACL,GAAGA,KAAK;MACRkC,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACP4D,UAAU,EAAE;IACd;EACF,CAAC,EAAEN,KAAK,CAAC,EAAE,aAAajH,KAAK,CAACgC,aAAa,CAAC5B,OAAO,CAACkG,QAAQ,EAAE;IAC5DC,KAAK,EAAE/D;EACT,CAAC,EAAE,aAAaxC,KAAK,CAACgC,aAAa,CAAC3B,SAAS,CAACiG,QAAQ,EAAE;IACtDC,KAAK,EAAEW;EACT,CAAC,EAAE5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACf,OAAO,IAAI;AACb,CAAC,CAAC;AACF,MAAMkG,MAAM,GAAG,eAAexH,KAAK,CAACyG,UAAU,CAAC,CAAC;EAC9CgB,IAAI;EACJ,GAAGR;AACL,CAAC,EAAEP,GAAG,KAAK;EACT,MAAMgB,EAAE,GAAGD,IAAI,GAAGT,UAAU,GAAGR,YAAY;EAC3C,OAAO,aAAaxG,KAAK,CAACgC,aAAa,CAAC0F,EAAE,EAAE3H,QAAQ,CAAC;IACnD2G,GAAG,EAAEA;EACP,CAAC,EAAEO,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASO,MAAM,EAAE9G,cAAc,EAAEF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}