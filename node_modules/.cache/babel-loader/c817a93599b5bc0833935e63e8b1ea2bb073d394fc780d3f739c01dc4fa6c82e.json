{"ast": null, "code": "const BlendShader = {\n  uniforms: {\n    tDiffuse1: {\n      value: null\n    },\n    tDiffuse2: {\n      value: null\n    },\n    mixRatio: {\n      value: 0.5\n    },\n    opacity: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float opacity;\n    uniform float mixRatio;\n\n    uniform sampler2D tDiffuse1;\n    uniform sampler2D tDiffuse2;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel1 = texture2D( tDiffuse1, vUv );\n    \tvec4 texel2 = texture2D( tDiffuse2, vUv );\n    \tgl_FragColor = opacity * mix( texel1, texel2, mixRatio );\n\n    }\n  `)\n};\nexport { BlendShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "tDiffuse1", "value", "tDiffuse2", "mixRatio", "opacity", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/BlendShader.ts"], "sourcesContent": ["/**\n * Blend two textures\n */\n\nexport const BlendShader = {\n  uniforms: {\n    tDiffuse1: { value: null },\n    tDiffuse2: { value: null },\n    mixRatio: { value: 0.5 },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n    uniform float mixRatio;\n\n    uniform sampler2D tDiffuse1;\n    uniform sampler2D tDiffuse2;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel1 = texture2D( tDiffuse1, vUv );\n    \tvec4 texel2 = texture2D( tDiffuse2, vUv );\n    \tgl_FragColor = opacity * mix( texel1, texel2, mixRatio );\n\n    }\n  `,\n}\n"], "mappings": "AAIO,MAAMA,WAAA,GAAc;EACzBC,QAAA,EAAU;IACRC,SAAA,EAAW;MAAEC,KAAA,EAAO;IAAK;IACzBC,SAAA,EAAW;MAAED,KAAA,EAAO;IAAK;IACzBE,QAAA,EAAU;MAAEF,KAAA,EAAO;IAAI;IACvBG,OAAA,EAAS;MAAEH,KAAA,EAAO;IAAI;EACxB;EAEAI,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}