{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = /* @__PURE__ */create('box');\nconst Circle = /* @__PURE__ */create('circle');\nconst Cone = /* @__PURE__ */create('cone');\nconst Cylinder = /* @__PURE__ */create('cylinder');\nconst Sphere = /* @__PURE__ */create('sphere');\nconst Plane = /* @__PURE__ */create('plane');\nconst Tube = /* @__PURE__ */create('tube');\nconst Torus = /* @__PURE__ */create('torus');\nconst TorusKnot = /* @__PURE__ */create('torusKnot');\nconst Tetrahedron = /* @__PURE__ */create('tetrahedron');\nconst Ring = /* @__PURE__ */create('ring');\nconst Polyhedron = /* @__PURE__ */create('polyhedron');\nconst Icosahedron = /* @__PURE__ */create('icosahedron');\nconst Octahedron = /* @__PURE__ */create('octahedron');\nconst Dodecahedron = /* @__PURE__ */create('dodecahedron');\nconst Extrude = /* @__PURE__ */create('extrude');\nconst Lathe = /* @__PURE__ */create('lathe');\nconst Capsule = /* @__PURE__ */create('capsule');\nconst Shape = /* @__PURE__ */create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "create", "type", "effect", "El", "forwardRef", "args", "children", "props", "fref", "ref", "useRef", "useImperativeHandle", "current", "useLayoutEffect", "createElement", "attach", "Box", "Circle", "Cone", "<PERSON><PERSON><PERSON>", "Sphere", "Plane", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tetrahedron", "Ring", "Polyhedron", "Icosahedron", "Octahedron", "Dodecahedron", "Extrude", "Lathe", "Capsule", "<PERSON><PERSON><PERSON>", "geometry", "pos", "attributes", "position", "b3", "Box3", "setFromBufferAttribute", "b3size", "Vector3", "getSize", "uv", "x", "y", "u", "v", "i", "count", "getX", "getY", "min", "push", "setAttribute", "Float32BufferAttribute"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/shapes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = /* @__PURE__ */create('box');\nconst Circle = /* @__PURE__ */create('circle');\nconst Cone = /* @__PURE__ */create('cone');\nconst Cylinder = /* @__PURE__ */create('cylinder');\nconst Sphere = /* @__PURE__ */create('sphere');\nconst Plane = /* @__PURE__ */create('plane');\nconst Tube = /* @__PURE__ */create('tube');\nconst Torus = /* @__PURE__ */create('torus');\nconst TorusKnot = /* @__PURE__ */create('torusKnot');\nconst Tetrahedron = /* @__PURE__ */create('tetrahedron');\nconst Ring = /* @__PURE__ */create('ring');\nconst Polyhedron = /* @__PURE__ */create('polyhedron');\nconst Icosahedron = /* @__PURE__ */create('icosahedron');\nconst Octahedron = /* @__PURE__ */create('octahedron');\nconst Dodecahedron = /* @__PURE__ */create('dodecahedron');\nconst Extrude = /* @__PURE__ */create('extrude');\nconst Lathe = /* @__PURE__ */create('lathe');\nconst Capsule = /* @__PURE__ */create('capsule');\nconst Shape = /* @__PURE__ */create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\n\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,MAAMA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5B,MAAMC,EAAE,GAAGF,IAAI,GAAG,UAAU;EAC5B,OAAO,aAAaH,KAAK,CAACM,UAAU,CAAC,CAAC;IACpCC,IAAI;IACJC,QAAQ;IACR,GAAGC;EACL,CAAC,EAAEC,IAAI,KAAK;IACV,MAAMC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;IAC9BZ,KAAK,CAACa,mBAAmB,CAACH,IAAI,EAAE,MAAMC,GAAG,CAACG,OAAO,CAAC;IAClDd,KAAK,CAACe,eAAe,CAAC,MAAM,MAAMX,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC;IACjF,OAAO,aAAad,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAEjB,QAAQ,CAAC;MACvDY,GAAG,EAAEA;IACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAaT,KAAK,CAACgB,aAAa,CAACX,EAAE,EAAE;MAC9CY,MAAM,EAAE,UAAU;MAClBV,IAAI,EAAEA;IACR,CAAC,CAAC,EAAEC,QAAQ,CAAC;EACf,CAAC,CAAC;AACJ;AACA,MAAMU,GAAG,GAAG,eAAehB,MAAM,CAAC,KAAK,CAAC;AACxC,MAAMiB,MAAM,GAAG,eAAejB,MAAM,CAAC,QAAQ,CAAC;AAC9C,MAAMkB,IAAI,GAAG,eAAelB,MAAM,CAAC,MAAM,CAAC;AAC1C,MAAMmB,QAAQ,GAAG,eAAenB,MAAM,CAAC,UAAU,CAAC;AAClD,MAAMoB,MAAM,GAAG,eAAepB,MAAM,CAAC,QAAQ,CAAC;AAC9C,MAAMqB,KAAK,GAAG,eAAerB,MAAM,CAAC,OAAO,CAAC;AAC5C,MAAMsB,IAAI,GAAG,eAAetB,MAAM,CAAC,MAAM,CAAC;AAC1C,MAAMuB,KAAK,GAAG,eAAevB,MAAM,CAAC,OAAO,CAAC;AAC5C,MAAMwB,SAAS,GAAG,eAAexB,MAAM,CAAC,WAAW,CAAC;AACpD,MAAMyB,WAAW,GAAG,eAAezB,MAAM,CAAC,aAAa,CAAC;AACxD,MAAM0B,IAAI,GAAG,eAAe1B,MAAM,CAAC,MAAM,CAAC;AAC1C,MAAM2B,UAAU,GAAG,eAAe3B,MAAM,CAAC,YAAY,CAAC;AACtD,MAAM4B,WAAW,GAAG,eAAe5B,MAAM,CAAC,aAAa,CAAC;AACxD,MAAM6B,UAAU,GAAG,eAAe7B,MAAM,CAAC,YAAY,CAAC;AACtD,MAAM8B,YAAY,GAAG,eAAe9B,MAAM,CAAC,cAAc,CAAC;AAC1D,MAAM+B,OAAO,GAAG,eAAe/B,MAAM,CAAC,SAAS,CAAC;AAChD,MAAMgC,KAAK,GAAG,eAAehC,MAAM,CAAC,OAAO,CAAC;AAC5C,MAAMiC,OAAO,GAAG,eAAejC,MAAM,CAAC,SAAS,CAAC;AAChD,MAAMkC,KAAK,GAAG,eAAelC,MAAM,CAAC,OAAO,EAAE,CAAC;EAC5CmC;AACF,CAAC,KAAK;EACJ;EACA;EACA,MAAMC,GAAG,GAAGD,QAAQ,CAACE,UAAU,CAACC,QAAQ;EACxC,MAAMC,EAAE,GAAG,IAAIxC,KAAK,CAACyC,IAAI,CAAC,CAAC,CAACC,sBAAsB,CAACL,GAAG,CAAC;EACvD,MAAMM,MAAM,GAAG,IAAI3C,KAAK,CAAC4C,OAAO,CAAC,CAAC;EAClCJ,EAAE,CAACK,OAAO,CAACF,MAAM,CAAC;EAClB,MAAMG,EAAE,GAAG,EAAE;EACb,IAAIC,CAAC,GAAG,CAAC;IACPC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;EACP,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,GAAG,CAACe,KAAK,EAAED,CAAC,EAAE,EAAE;IAClCJ,CAAC,GAAGV,GAAG,CAACgB,IAAI,CAACF,CAAC,CAAC;IACfH,CAAC,GAAGX,GAAG,CAACiB,IAAI,CAACH,CAAC,CAAC;IACfF,CAAC,GAAG,CAACF,CAAC,GAAGP,EAAE,CAACe,GAAG,CAACR,CAAC,IAAIJ,MAAM,CAACI,CAAC;IAC7BG,CAAC,GAAG,CAACF,CAAC,GAAGR,EAAE,CAACe,GAAG,CAACP,CAAC,IAAIL,MAAM,CAACK,CAAC;IAC7BF,EAAE,CAACU,IAAI,CAACP,CAAC,EAAEC,CAAC,CAAC;EACf;EACAd,QAAQ,CAACqB,YAAY,CAAC,IAAI,EAAE,IAAIzD,KAAK,CAAC0D,sBAAsB,CAACZ,EAAE,EAAE,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,SAAS7B,GAAG,EAAEiB,OAAO,EAAEhB,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEW,YAAY,EAAEC,OAAO,EAAEH,WAAW,EAAEI,KAAK,EAAEH,UAAU,EAAER,KAAK,EAAEM,UAAU,EAAED,IAAI,EAAEQ,KAAK,EAAEd,MAAM,EAAEK,WAAW,EAAEF,KAAK,EAAEC,SAAS,EAAEF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}