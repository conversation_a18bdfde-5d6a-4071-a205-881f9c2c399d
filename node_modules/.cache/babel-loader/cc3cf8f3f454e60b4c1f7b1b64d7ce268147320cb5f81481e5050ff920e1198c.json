{"ast": null, "code": "import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\nconst Float = /* @__PURE__ */React.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  autoInvalidate = false,\n  ...props\n}, forwardRef) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  const offset = React.useRef(Math.random() * 10000);\n  useFrame(state => {\n    var _floatingRange$, _floatingRange$2;\n    if (!enabled || speed === 0) return;\n    if (autoInvalidate) state.invalidate();\n    const t = offset.current + state.clock.elapsedTime;\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = THREE.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref,\n    matrixAutoUpdate: false\n  }, children));\n});\nexport { Float };", "map": {"version": 3, "names": ["React", "useFrame", "THREE", "Float", "forwardRef", "children", "enabled", "speed", "rotationIntensity", "floatIntensity", "floatingRange", "autoInvalidate", "props", "ref", "useRef", "useImperativeHandle", "current", "offset", "Math", "random", "state", "_floatingRange$", "_floatingRange$2", "invalidate", "t", "clock", "elapsedTime", "rotation", "x", "cos", "y", "sin", "z", "yPosition", "MathUtils", "mapLinear", "position", "updateMatrix", "createElement", "matrixAutoUpdate"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Float.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nconst Float = /* @__PURE__ */React.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  autoInvalidate = false,\n  ...props\n}, forwardRef) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  const offset = React.useRef(Math.random() * 10000);\n  useFrame(state => {\n    var _floatingRange$, _floatingRange$2;\n    if (!enabled || speed === 0) return;\n    if (autoInvalidate) state.invalidate();\n    const t = offset.current + state.clock.elapsedTime;\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = THREE.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref,\n    matrixAutoUpdate: false\n  }, children));\n});\n\nexport { Float };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,KAAK,GAAG,eAAeH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC;EACTC,iBAAiB,GAAG,CAAC;EACrBC,cAAc,GAAG,CAAC;EAClBC,aAAa,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3BC,cAAc,GAAG,KAAK;EACtB,GAAGC;AACL,CAAC,EAAER,UAAU,KAAK;EAChB,MAAMS,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC,IAAI,CAAC;EAC9Bd,KAAK,CAACe,mBAAmB,CAACX,UAAU,EAAE,MAAMS,GAAG,CAACG,OAAO,EAAE,EAAE,CAAC;EAC5D,MAAMC,MAAM,GAAGjB,KAAK,CAACc,MAAM,CAACI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC;EAClDlB,QAAQ,CAACmB,KAAK,IAAI;IAChB,IAAIC,eAAe,EAAEC,gBAAgB;IACrC,IAAI,CAAChB,OAAO,IAAIC,KAAK,KAAK,CAAC,EAAE;IAC7B,IAAII,cAAc,EAAES,KAAK,CAACG,UAAU,CAAC,CAAC;IACtC,MAAMC,CAAC,GAAGP,MAAM,CAACD,OAAO,GAAGI,KAAK,CAACK,KAAK,CAACC,WAAW;IAClDb,GAAG,CAACG,OAAO,CAACW,QAAQ,CAACC,CAAC,GAAGV,IAAI,CAACW,GAAG,CAACL,CAAC,GAAG,CAAC,GAAGjB,KAAK,CAAC,GAAG,CAAC,GAAGC,iBAAiB;IACxEK,GAAG,CAACG,OAAO,CAACW,QAAQ,CAACG,CAAC,GAAGZ,IAAI,CAACa,GAAG,CAACP,CAAC,GAAG,CAAC,GAAGjB,KAAK,CAAC,GAAG,CAAC,GAAGC,iBAAiB;IACxEK,GAAG,CAACG,OAAO,CAACW,QAAQ,CAACK,CAAC,GAAGd,IAAI,CAACa,GAAG,CAACP,CAAC,GAAG,CAAC,GAAGjB,KAAK,CAAC,GAAG,EAAE,GAAGC,iBAAiB;IACzE,IAAIyB,SAAS,GAAGf,IAAI,CAACa,GAAG,CAACP,CAAC,GAAG,CAAC,GAAGjB,KAAK,CAAC,GAAG,EAAE;IAC5C0B,SAAS,GAAG/B,KAAK,CAACgC,SAAS,CAACC,SAAS,CAACF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAACZ,eAAe,GAAGX,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIW,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,GAAG,EAAE,CAACC,gBAAgB,GAAGZ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIY,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,GAAG,CAAC;IAC3UT,GAAG,CAACG,OAAO,CAACoB,QAAQ,CAACN,CAAC,GAAGG,SAAS,GAAGxB,cAAc;IACnDI,GAAG,CAACG,OAAO,CAACqB,YAAY,CAAC,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,aAAarC,KAAK,CAACsC,aAAa,CAAC,OAAO,EAAE1B,KAAK,EAAE,aAAaZ,KAAK,CAACsC,aAAa,CAAC,OAAO,EAAE;IAChGzB,GAAG,EAAEA,GAAG;IACR0B,gBAAgB,EAAE;EACpB,CAAC,EAAElC,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}