{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { DataTexture, RGBAFormat, FloatType, RepeatWrapping, NearestFilter, Mesh, InstancedMesh, DynamicDrawUsage, Matrix4 } from \"three\";\nconst CHANNELS = 4;\nconst TEXTURE_WIDTH = 1024;\nconst TEXTURE_HEIGHT = 4;\nconst initSplineTexture = (numberOfCurves = 1) => {\n  const dataArray = new Float32Array(TEXTURE_WIDTH * TEXTURE_HEIGHT * numberOfCurves * CHANNELS);\n  const dataTexture = new DataTexture(dataArray, TEXTURE_WIDTH, TEXTURE_HEIGHT * numberOfCurves, RGBAFormat, FloatType);\n  dataTexture.wrapS = RepeatWrapping;\n  dataTexture.wrapT = RepeatWrapping;\n  dataTexture.magFilter = NearestFilter;\n  dataTexture.needsUpdate = true;\n  return dataTexture;\n};\nconst updateSplineTexture = (texture, splineCurve, offset = 0) => {\n  const numberOfPoints = Math.floor(TEXTURE_WIDTH * (TEXTURE_HEIGHT / 4));\n  splineCurve.arcLengthDivisions = numberOfPoints / 2;\n  splineCurve.updateArcLengths();\n  const points = splineCurve.getSpacedPoints(numberOfPoints);\n  const frenetFrames = splineCurve.computeFrenetFrames(numberOfPoints, true);\n  for (let i = 0; i < numberOfPoints; i++) {\n    const rowOffset = Math.floor(i / TEXTURE_WIDTH);\n    const rowIndex = i % TEXTURE_WIDTH;\n    let pt = points[i];\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 0 + rowOffset + TEXTURE_HEIGHT * offset);\n    pt = frenetFrames.tangents[i];\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 1 + rowOffset + TEXTURE_HEIGHT * offset);\n    pt = frenetFrames.normals[i];\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 2 + rowOffset + TEXTURE_HEIGHT * offset);\n    pt = frenetFrames.binormals[i];\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 3 + rowOffset + TEXTURE_HEIGHT * offset);\n  }\n  texture.needsUpdate = true;\n};\nconst setTextureValue = (texture, index, x, y, z, o) => {\n  const image = texture.image;\n  const {\n    data\n  } = image;\n  const i = CHANNELS * TEXTURE_WIDTH * o;\n  data[index * CHANNELS + i + 0] = x;\n  data[index * CHANNELS + i + 1] = y;\n  data[index * CHANNELS + i + 2] = z;\n  data[index * CHANNELS + i + 3] = 1;\n};\nconst getUniforms = splineTexture => ({\n  spineTexture: {\n    value: splineTexture\n  },\n  pathOffset: {\n    type: \"f\",\n    value: 0\n  },\n  // time of path curve\n  pathSegment: {\n    type: \"f\",\n    value: 1\n  },\n  // fractional length of path\n  spineOffset: {\n    type: \"f\",\n    value: 161\n  },\n  spineLength: {\n    type: \"f\",\n    value: 400\n  },\n  flow: {\n    type: \"i\",\n    value: 1\n  }\n});\nfunction modifyShader(material, uniforms, numberOfCurves = 1) {\n  if (material.__ok) return;\n  material.__ok = true;\n  material.onBeforeCompile = shader => {\n    if (shader.__modified) return;\n    shader.__modified = true;\n    Object.assign(shader.uniforms, uniforms);\n    const vertexShader = /* glsl */\n    `\n\t\tuniform sampler2D spineTexture;\n\t\tuniform float pathOffset;\n\t\tuniform float pathSegment;\n\t\tuniform float spineOffset;\n\t\tuniform float spineLength;\n\t\tuniform int flow;\n\n\t\tfloat textureLayers = ${TEXTURE_HEIGHT * numberOfCurves}.;\n\t\tfloat textureStacks = ${TEXTURE_HEIGHT / 4}.;\n\n\t\t${shader.vertexShader}\n\t\t`.replace(\"#include <beginnormal_vertex>\", \"\").replace(\"#include <defaultnormal_vertex>\", \"\").replace(\"#include <begin_vertex>\", \"\").replace(/void\\s*main\\s*\\(\\)\\s*\\{/, /* glsl */\n    `\n        void main() {\n        #include <beginnormal_vertex>\n\n        vec4 worldPos = modelMatrix * vec4(position, 1.);\n\n        bool bend = flow > 0;\n        float xWeight = bend ? 0. : 1.;\n\n        #ifdef USE_INSTANCING\n        float pathOffsetFromInstanceMatrix = instanceMatrix[3][2];\n        float spineLengthFromInstanceMatrix = instanceMatrix[3][0];\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLengthFromInstanceMatrix : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset + pathOffsetFromInstanceMatrix)*textureStacks;\n        #else\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLength : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset)*textureStacks;\n        #endif\n\n        mt = mod(mt, textureStacks);\n        float rowOffset = floor(mt);\n\n        #ifdef USE_INSTANCING\n        rowOffset += instanceMatrix[3][1] * ${TEXTURE_HEIGHT}.;\n        #endif\n\n        vec3 spinePos = texture2D(spineTexture, vec2(mt, (0. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 a =        texture2D(spineTexture, vec2(mt, (1. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 b =        texture2D(spineTexture, vec2(mt, (2. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 c =        texture2D(spineTexture, vec2(mt, (3. + rowOffset + 0.5) / textureLayers)).xyz;\n        mat3 basis = mat3(a, b, c);\n\n        vec3 transformed = basis\n          * vec3(worldPos.x * xWeight, worldPos.y * 1., worldPos.z * 1.)\n          + spinePos;\n\n        vec3 transformedNormal = normalMatrix * (basis * objectNormal);\n\t\t\t`).replace(\"#include <project_vertex>\", /* glsl */\n    `vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );\n\t\t\t\tgl_Position = projectionMatrix * mvPosition;`);\n    shader.vertexShader = vertexShader;\n  };\n}\nclass Flow {\n  /**\n   * @param {Mesh} mesh The mesh to clone and modify to bend around the curve\n   * @param {number} numberOfCurves The amount of space that should preallocated for additional curves\n   */\n  constructor(mesh, numberOfCurves = 1) {\n    __publicField(this, \"curveArray\");\n    __publicField(this, \"curveLengthArray\");\n    __publicField(this, \"object3D\");\n    __publicField(this, \"splineTexure\");\n    __publicField(this, \"uniforms\");\n    const obj3D = mesh.clone();\n    const splineTexure = initSplineTexture(numberOfCurves);\n    const uniforms = getUniforms(splineTexure);\n    obj3D.traverse(child => {\n      if (child instanceof Mesh || child instanceof InstancedMesh) {\n        child.material = child.material.clone();\n        modifyShader(child.material, uniforms, numberOfCurves);\n      }\n    });\n    this.curveArray = new Array(numberOfCurves);\n    this.curveLengthArray = new Array(numberOfCurves);\n    this.object3D = obj3D;\n    this.splineTexure = splineTexure;\n    this.uniforms = uniforms;\n  }\n  updateCurve(index, curve) {\n    if (index >= this.curveArray.length) throw Error(\"Index out of range for Flow\");\n    const curveLength = curve.getLength();\n    this.uniforms.spineLength.value = curveLength;\n    this.curveLengthArray[index] = curveLength;\n    this.curveArray[index] = curve;\n    updateSplineTexture(this.splineTexure, curve, index);\n  }\n  moveAlongCurve(amount) {\n    this.uniforms.pathOffset.value += amount;\n  }\n}\nconst matrix = /* @__PURE__ */new Matrix4();\nclass InstancedFlow extends Flow {\n  /**\n   *\n   * @param {number} count The number of instanced elements\n   * @param {number} curveCount The number of curves to preallocate for\n   * @param {Geometry} geometry The geometry to use for the instanced mesh\n   * @param {Material} material The material to use for the instanced mesh\n   */\n  constructor(count, curveCount, geometry, material) {\n    const mesh = new InstancedMesh(geometry, material, count);\n    mesh.instanceMatrix.setUsage(DynamicDrawUsage);\n    mesh.frustumCulled = false;\n    super(mesh, curveCount);\n    __publicField(this, \"offsets\");\n    __publicField(this, \"whichCurve\");\n    this.offsets = new Array(count).fill(0);\n    this.whichCurve = new Array(count).fill(0);\n  }\n  /**\n   * The extra information about which curve and curve position is stored in the translation components of the matrix for the instanced objects\n   * This writes that information to the matrix and marks it as needing update.\n   *\n   * @param {number} index of the instanced element to update\n   */\n  writeChanges(index) {\n    matrix.makeTranslation(this.curveLengthArray[this.whichCurve[index]], this.whichCurve[index], this.offsets[index]);\n    this.object3D.setMatrixAt(index, matrix);\n    this.object3D.instanceMatrix.needsUpdate = true;\n  }\n  /**\n   * Move an individual element along the curve by a specific amount\n   *\n   * @param {number} index Which element to update\n   * @param {number} offset Move by how much\n   */\n  moveIndividualAlongCurve(index, offset) {\n    this.offsets[index] += offset;\n    this.writeChanges(index);\n  }\n  /**\n   * Select which curve to use for an element\n   *\n   * @param {number} index the index of the instanced element to update\n   * @param {number} curveNo the index of the curve it should use\n   */\n  setCurve(index, curveNo) {\n    if (isNaN(curveNo)) throw Error(\"curve index being set is Not a Number (NaN)\");\n    this.whichCurve[index] = curveNo;\n    this.writeChanges(index);\n  }\n}\nexport { Flow, InstancedFlow, getUniforms, initSplineTexture, modifyShader, updateSplineTexture };", "map": {"version": 3, "names": ["CHANNELS", "TEXTURE_WIDTH", "TEXTURE_HEIGHT", "initSplineTexture", "numberOfCurves", "dataArray", "Float32Array", "dataTexture", "DataTexture", "RGBAFormat", "FloatType", "wrapS", "RepeatWrapping", "wrapT", "magFilter", "NearestFilter", "needsUpdate", "updateSplineTexture", "texture", "splineCurve", "offset", "numberOfPoints", "Math", "floor", "arcLengthDivisions", "updateArcLengths", "points", "getSpacedPoints", "frenetFrames", "computeFrenetFrames", "i", "rowOffset", "rowIndex", "pt", "setTextureValue", "x", "y", "z", "tangents", "normals", "binormals", "index", "o", "image", "data", "getUniforms", "splineTexture", "spineTexture", "value", "pathOffset", "type", "pathSegment", "spineOffset", "<PERSON><PERSON><PERSON><PERSON>", "flow", "modifyShader", "material", "uniforms", "__ok", "onBeforeCompile", "shader", "__modified", "Object", "assign", "vertexShader", "replace", "Flow", "constructor", "mesh", "__publicField", "obj3D", "clone", "splineTexure", "traverse", "child", "<PERSON><PERSON>", "In<PERSON>d<PERSON>esh", "curveArray", "Array", "curveLengthArray", "object3D", "updateCurve", "curve", "length", "Error", "curveLength", "<PERSON><PERSON><PERSON><PERSON>", "moveAlongCurve", "amount", "matrix", "Matrix4", "InstancedFlow", "count", "curveCount", "geometry", "instanceMatrix", "setUsage", "DynamicDrawUsage", "frustumCulled", "offsets", "fill", "whichCurve", "writeChanges", "makeTranslation", "setMatrixAt", "moveIndividualAlongCurve", "setCurve", "curveNo", "isNaN"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/modifiers/CurveModifier.ts"], "sourcesContent": ["// Original src: https://github.com/zz85/threejs-path-flow\nconst CHANNELS = 4\nconst TEXTURE_WIDTH = 1024\nconst TEXTURE_HEIGHT = 4\n\nimport {\n  DataTexture,\n  RGBAFormat,\n  FloatType,\n  RepeatWrapping,\n  Mesh,\n  InstancedMesh,\n  NearestFilter,\n  DynamicDrawUsage,\n  Matrix4,\n  Material,\n  Curve,\n  BufferGeometry,\n} from 'three'\n\nimport type { IUniform } from 'three'\n\n/**\n * Make a new DataTexture to store the descriptions of the curves.\n *\n * @param { number } numberOfCurves the number of curves needed to be described by this texture.\n */\nexport const initSplineTexture = (numberOfCurves = 1): DataTexture => {\n  const dataArray = new Float32Array(TEXTURE_WIDTH * TEXTURE_HEIGHT * numberOfCurves * CHANNELS)\n  const dataTexture = new DataTexture(dataArray, TEXTURE_WIDTH, TEXTURE_HEIGHT * numberOfCurves, RGBAFormat, FloatType)\n\n  dataTexture.wrapS = RepeatWrapping\n  dataTexture.wrapT = RepeatWrapping\n  dataTexture.magFilter = NearestFilter\n  dataTexture.needsUpdate = true\n\n  return dataTexture\n}\n\n/**\n * Write the curve description to the data texture\n *\n * @param { DataTexture } texture The DataTexture to write to\n * @param { Curve } splineCurve The curve to describe\n * @param { number } offset Which curve slot to write to\n */\nexport const updateSplineTexture = <TCurve extends Curve<any>>(\n  texture: DataTexture,\n  splineCurve: TCurve,\n  offset = 0,\n): void => {\n  const numberOfPoints = Math.floor(TEXTURE_WIDTH * (TEXTURE_HEIGHT / 4))\n  splineCurve.arcLengthDivisions = numberOfPoints / 2\n  splineCurve.updateArcLengths()\n  const points = splineCurve.getSpacedPoints(numberOfPoints)\n  const frenetFrames = splineCurve.computeFrenetFrames(numberOfPoints, true)\n\n  for (let i = 0; i < numberOfPoints; i++) {\n    const rowOffset = Math.floor(i / TEXTURE_WIDTH)\n    const rowIndex = i % TEXTURE_WIDTH\n\n    let pt = points[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 0 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.tangents[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 1 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.normals[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 2 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.binormals[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 3 + rowOffset + TEXTURE_HEIGHT * offset)\n  }\n\n  texture.needsUpdate = true\n}\n\nconst setTextureValue = (texture: DataTexture, index: number, x: number, y: number, z: number, o: number): void => {\n  const image = texture.image\n  const { data } = image\n  const i = CHANNELS * TEXTURE_WIDTH * o // Row Offset\n  data[index * CHANNELS + i + 0] = x\n  data[index * CHANNELS + i + 1] = y\n  data[index * CHANNELS + i + 2] = z\n  data[index * CHANNELS + i + 3] = 1\n}\n\nexport interface INumericUniform extends IUniform {\n  type: 'f' | 'i'\n  value: number\n}\n\nexport type CurveModifierUniforms = {\n  spineTexture: IUniform<DataTexture>\n  pathOffset: INumericUniform\n  pathSegment: INumericUniform\n  spineOffset: INumericUniform\n  spineLength: INumericUniform\n  flow: INumericUniform\n}\n\n/**\n * Create a new set of uniforms for describing the curve modifier\n *\n * @param { DataTexture } Texture which holds the curve description\n */\nexport const getUniforms = (splineTexture: DataTexture): CurveModifierUniforms => ({\n  spineTexture: { value: splineTexture },\n  pathOffset: { type: 'f', value: 0 }, // time of path curve\n  pathSegment: { type: 'f', value: 1 }, // fractional length of path\n  spineOffset: { type: 'f', value: 161 },\n  spineLength: { type: 'f', value: 400 },\n  flow: { type: 'i', value: 1 },\n})\n\nexport type ModifiedMaterial<TMaterial extends Material> = TMaterial & {\n  __ok: boolean\n}\n\nexport function modifyShader<TMaterial extends Material = Material>(\n  material: ModifiedMaterial<TMaterial>,\n  uniforms: CurveModifierUniforms,\n  numberOfCurves = 1,\n): void {\n  if (material.__ok) return\n  material.__ok = true\n\n  material.onBeforeCompile = (shader: { vertexShader: string; uniforms: { [uniform: string]: IUniform } }): void => {\n    if ((shader as any).__modified) return\n    ;(shader as any).__modified = true\n\n    Object.assign(shader.uniforms, uniforms)\n\n    const vertexShader = /* glsl */ `\n\t\tuniform sampler2D spineTexture;\n\t\tuniform float pathOffset;\n\t\tuniform float pathSegment;\n\t\tuniform float spineOffset;\n\t\tuniform float spineLength;\n\t\tuniform int flow;\n\n\t\tfloat textureLayers = ${TEXTURE_HEIGHT * numberOfCurves}.;\n\t\tfloat textureStacks = ${TEXTURE_HEIGHT / 4}.;\n\n\t\t${shader.vertexShader}\n\t\t`\n      // chunk import moved in front of modified shader below\n      .replace('#include <beginnormal_vertex>', '')\n\n      // vec3 transformedNormal declaration overriden below\n      .replace('#include <defaultnormal_vertex>', '')\n\n      // vec3 transformed declaration overriden below\n      .replace('#include <begin_vertex>', '')\n\n      // shader override\n      .replace(\n        /void\\s*main\\s*\\(\\)\\s*\\{/,\n        /* glsl */ `\n        void main() {\n        #include <beginnormal_vertex>\n\n        vec4 worldPos = modelMatrix * vec4(position, 1.);\n\n        bool bend = flow > 0;\n        float xWeight = bend ? 0. : 1.;\n\n        #ifdef USE_INSTANCING\n        float pathOffsetFromInstanceMatrix = instanceMatrix[3][2];\n        float spineLengthFromInstanceMatrix = instanceMatrix[3][0];\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLengthFromInstanceMatrix : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset + pathOffsetFromInstanceMatrix)*textureStacks;\n        #else\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLength : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset)*textureStacks;\n        #endif\n\n        mt = mod(mt, textureStacks);\n        float rowOffset = floor(mt);\n\n        #ifdef USE_INSTANCING\n        rowOffset += instanceMatrix[3][1] * ${TEXTURE_HEIGHT}.;\n        #endif\n\n        vec3 spinePos = texture2D(spineTexture, vec2(mt, (0. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 a =        texture2D(spineTexture, vec2(mt, (1. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 b =        texture2D(spineTexture, vec2(mt, (2. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 c =        texture2D(spineTexture, vec2(mt, (3. + rowOffset + 0.5) / textureLayers)).xyz;\n        mat3 basis = mat3(a, b, c);\n\n        vec3 transformed = basis\n          * vec3(worldPos.x * xWeight, worldPos.y * 1., worldPos.z * 1.)\n          + spinePos;\n\n        vec3 transformedNormal = normalMatrix * (basis * objectNormal);\n\t\t\t`,\n      )\n      .replace(\n        '#include <project_vertex>',\n        /* glsl */ `vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );\n\t\t\t\tgl_Position = projectionMatrix * mvPosition;`,\n      )\n\n    shader.vertexShader = vertexShader\n  }\n}\n\n/**\n * A helper class for making meshes bend aroudn curves\n */\nexport class Flow<TMesh extends Mesh = Mesh> {\n  public curveArray: Curve<any>[]\n  public curveLengthArray: number[]\n\n  public object3D: TMesh\n  public splineTexure: DataTexture\n  public uniforms: CurveModifierUniforms\n\n  /**\n   * @param {Mesh} mesh The mesh to clone and modify to bend around the curve\n   * @param {number} numberOfCurves The amount of space that should preallocated for additional curves\n   */\n  constructor(mesh: TMesh, numberOfCurves = 1) {\n    const obj3D = mesh.clone() as TMesh\n    const splineTexure = initSplineTexture(numberOfCurves)\n    const uniforms = getUniforms(splineTexure)\n\n    obj3D.traverse((child) => {\n      if (child instanceof Mesh || child instanceof InstancedMesh) {\n        child.material = child.material.clone()\n        modifyShader(child.material, uniforms, numberOfCurves)\n      }\n    })\n\n    this.curveArray = new Array(numberOfCurves)\n    this.curveLengthArray = new Array(numberOfCurves)\n\n    this.object3D = obj3D\n    this.splineTexure = splineTexure\n    this.uniforms = uniforms\n  }\n\n  public updateCurve<TCurve extends Curve<any>>(index: number, curve: TCurve): void {\n    if (index >= this.curveArray.length) throw Error('Index out of range for Flow')\n    const curveLength = curve.getLength()\n    this.uniforms.spineLength.value = curveLength\n    this.curveLengthArray[index] = curveLength\n    this.curveArray[index] = curve\n    updateSplineTexture(this.splineTexure, curve, index)\n  }\n\n  public moveAlongCurve(amount: number): void {\n    this.uniforms.pathOffset.value += amount\n  }\n}\nconst matrix = /* @__PURE__ */ new Matrix4()\n\n/**\n * A helper class for creating instanced versions of flow, where the instances are placed on the curve.\n */\nexport class InstancedFlow<\n  TGeometry extends BufferGeometry = BufferGeometry,\n  TMaterial extends Material = Material\n> extends Flow<InstancedMesh<TGeometry, TMaterial>> {\n  public offsets: number[]\n  public whichCurve: number[]\n\n  /**\n   *\n   * @param {number} count The number of instanced elements\n   * @param {number} curveCount The number of curves to preallocate for\n   * @param {Geometry} geometry The geometry to use for the instanced mesh\n   * @param {Material} material The material to use for the instanced mesh\n   */\n  constructor(count: number, curveCount: number, geometry: TGeometry, material: TMaterial) {\n    const mesh = new InstancedMesh(geometry, material, count)\n    mesh.instanceMatrix.setUsage(DynamicDrawUsage)\n    mesh.frustumCulled = false\n    super(mesh, curveCount)\n\n    this.offsets = new Array(count).fill(0)\n    this.whichCurve = new Array(count).fill(0)\n  }\n\n  /**\n   * The extra information about which curve and curve position is stored in the translation components of the matrix for the instanced objects\n   * This writes that information to the matrix and marks it as needing update.\n   *\n   * @param {number} index of the instanced element to update\n   */\n  private writeChanges(index: number): void {\n    matrix.makeTranslation(this.curveLengthArray[this.whichCurve[index]], this.whichCurve[index], this.offsets[index])\n    this.object3D.setMatrixAt(index, matrix)\n    this.object3D.instanceMatrix.needsUpdate = true\n  }\n\n  /**\n   * Move an individual element along the curve by a specific amount\n   *\n   * @param {number} index Which element to update\n   * @param {number} offset Move by how much\n   */\n  public moveIndividualAlongCurve(index: number, offset: number): void {\n    this.offsets[index] += offset\n    this.writeChanges(index)\n  }\n\n  /**\n   * Select which curve to use for an element\n   *\n   * @param {number} index the index of the instanced element to update\n   * @param {number} curveNo the index of the curve it should use\n   */\n  public setCurve(index: number, curveNo: number): void {\n    if (isNaN(curveNo)) throw Error('curve index being set is Not a Number (NaN)')\n    this.whichCurve[index] = curveNo\n    this.writeChanges(index)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AACA,MAAMA,QAAA,GAAW;AACjB,MAAMC,aAAA,GAAgB;AACtB,MAAMC,cAAA,GAAiB;AAwBV,MAAAC,iBAAA,GAAoBA,CAACC,cAAA,GAAiB,MAAmB;EACpE,MAAMC,SAAA,GAAY,IAAIC,YAAA,CAAaL,aAAA,GAAgBC,cAAA,GAAiBE,cAAA,GAAiBJ,QAAQ;EACvF,MAAAO,WAAA,GAAc,IAAIC,WAAA,CAAYH,SAAA,EAAWJ,aAAA,EAAeC,cAAA,GAAiBE,cAAA,EAAgBK,UAAA,EAAYC,SAAS;EAEpHH,WAAA,CAAYI,KAAA,GAAQC,cAAA;EACpBL,WAAA,CAAYM,KAAA,GAAQD,cAAA;EACpBL,WAAA,CAAYO,SAAA,GAAYC,aAAA;EACxBR,WAAA,CAAYS,WAAA,GAAc;EAEnB,OAAAT,WAAA;AACT;AASO,MAAMU,mBAAA,GAAsBA,CACjCC,OAAA,EACAC,WAAA,EACAC,MAAA,GAAS,MACA;EACT,MAAMC,cAAA,GAAiBC,IAAA,CAAKC,KAAA,CAAMtB,aAAA,IAAiBC,cAAA,GAAiB,EAAE;EACtEiB,WAAA,CAAYK,kBAAA,GAAqBH,cAAA,GAAiB;EAClDF,WAAA,CAAYM,gBAAA,CAAiB;EACvB,MAAAC,MAAA,GAASP,WAAA,CAAYQ,eAAA,CAAgBN,cAAc;EACzD,MAAMO,YAAA,GAAeT,WAAA,CAAYU,mBAAA,CAAoBR,cAAA,EAAgB,IAAI;EAEzE,SAASS,CAAA,GAAI,GAAGA,CAAA,GAAIT,cAAA,EAAgBS,CAAA,IAAK;IACvC,MAAMC,SAAA,GAAYT,IAAA,CAAKC,KAAA,CAAMO,CAAA,GAAI7B,aAAa;IAC9C,MAAM+B,QAAA,GAAWF,CAAA,GAAI7B,aAAA;IAEjB,IAAAgC,EAAA,GAAKP,MAAA,CAAOI,CAAC;IACDI,eAAA,CAAAhB,OAAA,EAASc,QAAA,EAAUC,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAG,IAAIN,SAAA,GAAY7B,cAAA,GAAiBkB,MAAM;IACvFa,EAAA,GAAAL,YAAA,CAAaU,QAAA,CAASR,CAAC;IACZI,eAAA,CAAAhB,OAAA,EAASc,QAAA,EAAUC,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAG,IAAIN,SAAA,GAAY7B,cAAA,GAAiBkB,MAAM;IACvFa,EAAA,GAAAL,YAAA,CAAaW,OAAA,CAAQT,CAAC;IACXI,eAAA,CAAAhB,OAAA,EAASc,QAAA,EAAUC,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAG,IAAIN,SAAA,GAAY7B,cAAA,GAAiBkB,MAAM;IACvFa,EAAA,GAAAL,YAAA,CAAaY,SAAA,CAAUV,CAAC;IACbI,eAAA,CAAAhB,OAAA,EAASc,QAAA,EAAUC,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAG,IAAIN,SAAA,GAAY7B,cAAA,GAAiBkB,MAAM;EAC9F;EAEAF,OAAA,CAAQF,WAAA,GAAc;AACxB;AAEA,MAAMkB,eAAA,GAAkBA,CAAChB,OAAA,EAAsBuB,KAAA,EAAeN,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAWK,CAAA,KAAoB;EACjH,MAAMC,KAAA,GAAQzB,OAAA,CAAQyB,KAAA;EAChB;IAAEC;EAAS,IAAAD,KAAA;EACX,MAAAb,CAAA,GAAI9B,QAAA,GAAWC,aAAA,GAAgByC,CAAA;EACrCE,IAAA,CAAKH,KAAA,GAAQzC,QAAA,GAAW8B,CAAA,GAAI,CAAC,IAAIK,CAAA;EACjCS,IAAA,CAAKH,KAAA,GAAQzC,QAAA,GAAW8B,CAAA,GAAI,CAAC,IAAIM,CAAA;EACjCQ,IAAA,CAAKH,KAAA,GAAQzC,QAAA,GAAW8B,CAAA,GAAI,CAAC,IAAIO,CAAA;EACjCO,IAAA,CAAKH,KAAA,GAAQzC,QAAA,GAAW8B,CAAA,GAAI,CAAC,IAAI;AACnC;AAqBa,MAAAe,WAAA,GAAeC,aAAA,KAAuD;EACjFC,YAAA,EAAc;IAAEC,KAAA,EAAOF;EAAc;EACrCG,UAAA,EAAY;IAAEC,IAAA,EAAM;IAAKF,KAAA,EAAO;EAAE;EAAA;EAClCG,WAAA,EAAa;IAAED,IAAA,EAAM;IAAKF,KAAA,EAAO;EAAE;EAAA;EACnCI,WAAA,EAAa;IAAEF,IAAA,EAAM;IAAKF,KAAA,EAAO;EAAI;EACrCK,WAAA,EAAa;IAAEH,IAAA,EAAM;IAAKF,KAAA,EAAO;EAAI;EACrCM,IAAA,EAAM;IAAEJ,IAAA,EAAM;IAAKF,KAAA,EAAO;EAAE;AAC9B;AAMO,SAASO,aACdC,QAAA,EACAC,QAAA,EACArD,cAAA,GAAiB,GACX;EACN,IAAIoD,QAAA,CAASE,IAAA,EAAM;EACnBF,QAAA,CAASE,IAAA,GAAO;EAEPF,QAAA,CAAAG,eAAA,GAAmBC,MAAA,IAAsF;IAChH,IAAKA,MAAA,CAAeC,UAAA,EAAY;IAC9BD,MAAA,CAAeC,UAAA,GAAa;IAEvBC,MAAA,CAAAC,MAAA,CAAOH,MAAA,CAAOH,QAAA,EAAUA,QAAQ;IAEjC,MAAAO,YAAA;IAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAQV9D,cAAA,GAAiBE,cAAA;AAAA,0BACjBF,cAAA,GAAiB;AAAA;AAAA,IAEvC0D,MAAA,CAAOI,YAAA;AAAA,IAGJC,OAAA,CAAQ,iCAAiC,EAAE,EAG3CA,OAAA,CAAQ,mCAAmC,EAAE,EAG7CA,OAAA,CAAQ,2BAA2B,EAAE,EAGrCA,OAAA,CACC;IACW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8CAuB2B/D,cAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBvC+D,OAAA,CACC;IACW;AAAA,iDAEb;IAEFL,MAAA,CAAOI,YAAA,GAAeA,YAAA;EAAA;AAE1B;AAKO,MAAME,IAAA,CAAgC;EAAA;AAAA;AAAA;AAAA;EAY3CC,YAAYC,IAAA,EAAahE,cAAA,GAAiB,GAAG;IAXtCiE,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAOC,MAAAC,KAAA,GAAQF,IAAA,CAAKG,KAAA;IACb,MAAAC,YAAA,GAAerE,iBAAA,CAAkBC,cAAc;IAC/C,MAAAqD,QAAA,GAAWZ,WAAA,CAAY2B,YAAY;IAEnCF,KAAA,CAAAG,QAAA,CAAUC,KAAA,IAAU;MACpB,IAAAA,KAAA,YAAiBC,IAAA,IAAQD,KAAA,YAAiBE,aAAA,EAAe;QACrDF,KAAA,CAAAlB,QAAA,GAAWkB,KAAA,CAAMlB,QAAA,CAASe,KAAA,CAAM;QACzBhB,YAAA,CAAAmB,KAAA,CAAMlB,QAAA,EAAUC,QAAA,EAAUrD,cAAc;MACvD;IAAA,CACD;IAEI,KAAAyE,UAAA,GAAa,IAAIC,KAAA,CAAM1E,cAAc;IACrC,KAAA2E,gBAAA,GAAmB,IAAID,KAAA,CAAM1E,cAAc;IAEhD,KAAK4E,QAAA,GAAWV,KAAA;IAChB,KAAKE,YAAA,GAAeA,YAAA;IACpB,KAAKf,QAAA,GAAWA,QAAA;EAClB;EAEOwB,YAAuCxC,KAAA,EAAeyC,KAAA,EAAqB;IAC5E,IAAAzC,KAAA,IAAS,KAAKoC,UAAA,CAAWM,MAAA,EAAQ,MAAMC,KAAA,CAAM,6BAA6B;IACxE,MAAAC,WAAA,GAAcH,KAAA,CAAMI,SAAA;IACrB,KAAA7B,QAAA,CAASJ,WAAA,CAAYL,KAAA,GAAQqC,WAAA;IAC7B,KAAAN,gBAAA,CAAiBtC,KAAK,IAAI4C,WAAA;IAC1B,KAAAR,UAAA,CAAWpC,KAAK,IAAIyC,KAAA;IACLjE,mBAAA,MAAKuD,YAAA,EAAcU,KAAA,EAAOzC,KAAK;EACrD;EAEO8C,eAAeC,MAAA,EAAsB;IACrC,KAAA/B,QAAA,CAASR,UAAA,CAAWD,KAAA,IAASwC,MAAA;EACpC;AACF;AACA,MAAMC,MAAA,sBAA6BC,OAAA;AAK5B,MAAMC,aAAA,SAGHzB,IAAA,CAA0C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWlDC,YAAYyB,KAAA,EAAeC,UAAA,EAAoBC,QAAA,EAAqBtC,QAAA,EAAqB;IACvF,MAAMY,IAAA,GAAO,IAAIQ,aAAA,CAAckB,QAAA,EAAUtC,QAAA,EAAUoC,KAAK;IACnDxB,IAAA,CAAA2B,cAAA,CAAeC,QAAA,CAASC,gBAAgB;IAC7C7B,IAAA,CAAK8B,aAAA,GAAgB;IACrB,MAAM9B,IAAA,EAAMyB,UAAU;IAdjBxB,aAAA;IACAA,aAAA;IAeL,KAAK8B,OAAA,GAAU,IAAIrB,KAAA,CAAMc,KAAK,EAAEQ,IAAA,CAAK,CAAC;IACtC,KAAKC,UAAA,GAAa,IAAIvB,KAAA,CAAMc,KAAK,EAAEQ,IAAA,CAAK,CAAC;EAC3C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQE,aAAa7D,KAAA,EAAqB;IACxCgD,MAAA,CAAOc,eAAA,CAAgB,KAAKxB,gBAAA,CAAiB,KAAKsB,UAAA,CAAW5D,KAAK,CAAC,GAAG,KAAK4D,UAAA,CAAW5D,KAAK,GAAG,KAAK0D,OAAA,CAAQ1D,KAAK,CAAC;IAC5G,KAAAuC,QAAA,CAASwB,WAAA,CAAY/D,KAAA,EAAOgD,MAAM;IAClC,KAAAT,QAAA,CAASe,cAAA,CAAe/E,WAAA,GAAc;EAC7C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOyF,yBAAyBhE,KAAA,EAAerB,MAAA,EAAsB;IAC9D,KAAA+E,OAAA,CAAQ1D,KAAK,KAAKrB,MAAA;IACvB,KAAKkF,YAAA,CAAa7D,KAAK;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOiE,SAASjE,KAAA,EAAekE,OAAA,EAAuB;IACpD,IAAIC,KAAA,CAAMD,OAAO,GAAG,MAAMvB,KAAA,CAAM,6CAA6C;IACxE,KAAAiB,UAAA,CAAW5D,KAAK,IAAIkE,OAAA;IACzB,KAAKL,YAAA,CAAa7D,KAAK;EACzB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}