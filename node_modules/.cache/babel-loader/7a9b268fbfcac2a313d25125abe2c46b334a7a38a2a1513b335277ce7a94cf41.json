{"ast": null, "code": "import { Texture, LinearFilter, ClampToEdgeWrapping, MeshBasicMaterial, DoubleSide, Mesh, PlaneGeometry } from \"three\";\nclass VolumeSlice {\n  constructor(volume, index, axis) {\n    const slice = this;\n    this.volume = volume;\n    index = index || 0;\n    Object.defineProperty(this, \"index\", {\n      get: function () {\n        return index;\n      },\n      set: function (value) {\n        index = value;\n        slice.geometryNeedsUpdate = true;\n        return index;\n      }\n    });\n    this.axis = axis || \"z\";\n    this.canvas = document.createElement(\"canvas\");\n    this.canvasBuffer = document.createElement(\"canvas\");\n    this.updateGeometry();\n    const canvasMap = new Texture(this.canvas);\n    canvasMap.minFilter = LinearFilter;\n    canvasMap.wrapS = canvasMap.wrapT = ClampToEdgeWrapping;\n    if (\"colorSpace\" in canvasMap) canvasMap.colorSpace = \"srgb\";else canvasMap.encoding = 3001;\n    const material = new MeshBasicMaterial({\n      map: canvasMap,\n      side: DoubleSide,\n      transparent: true\n    });\n    this.mesh = new Mesh(this.geometry, material);\n    this.mesh.matrixAutoUpdate = false;\n    this.geometryNeedsUpdate = true;\n    this.repaint();\n  }\n  /**\n   * @member {Function} repaint Refresh the texture and the geometry if geometryNeedsUpdate is set to true\n   * @memberof VolumeSlice\n   */\n  repaint() {\n    if (this.geometryNeedsUpdate) {\n      this.updateGeometry();\n    }\n    const iLength = this.iLength,\n      jLength = this.jLength,\n      sliceAccess = this.sliceAccess,\n      volume = this.volume,\n      canvas = this.canvasBuffer,\n      ctx = this.ctxBuffer;\n    const imgData = ctx.getImageData(0, 0, iLength, jLength);\n    const data = imgData.data;\n    const volumeData = volume.data;\n    const upperThreshold = volume.upperThreshold;\n    const lowerThreshold = volume.lowerThreshold;\n    const windowLow = volume.windowLow;\n    const windowHigh = volume.windowHigh;\n    let pixelCount = 0;\n    if (volume.dataType === \"label\") {\n      for (let j = 0; j < jLength; j++) {\n        for (let i = 0; i < iLength; i++) {\n          let label = volumeData[sliceAccess(i, j)];\n          label = label >= this.colorMap.length ? label % this.colorMap.length + 1 : label;\n          const color = this.colorMap[label];\n          data[4 * pixelCount] = color >> 24 & 255;\n          data[4 * pixelCount + 1] = color >> 16 & 255;\n          data[4 * pixelCount + 2] = color >> 8 & 255;\n          data[4 * pixelCount + 3] = color & 255;\n          pixelCount++;\n        }\n      }\n    } else {\n      for (let j = 0; j < jLength; j++) {\n        for (let i = 0; i < iLength; i++) {\n          let value = volumeData[sliceAccess(i, j)];\n          let alpha = 255;\n          alpha = upperThreshold >= value ? lowerThreshold <= value ? alpha : 0 : 0;\n          value = Math.floor(255 * (value - windowLow) / (windowHigh - windowLow));\n          value = value > 255 ? 255 : value < 0 ? 0 : value | 0;\n          data[4 * pixelCount] = value;\n          data[4 * pixelCount + 1] = value;\n          data[4 * pixelCount + 2] = value;\n          data[4 * pixelCount + 3] = alpha;\n          pixelCount++;\n        }\n      }\n    }\n    ctx.putImageData(imgData, 0, 0);\n    this.ctx.drawImage(canvas, 0, 0, iLength, jLength, 0, 0, this.canvas.width, this.canvas.height);\n    this.mesh.material.map.needsUpdate = true;\n  }\n  /**\n   * @member {Function} Refresh the geometry according to axis and index\n   * @see Volume.extractPerpendicularPlane\n   * @memberof VolumeSlice\n   */\n  updateGeometry() {\n    const extracted = this.volume.extractPerpendicularPlane(this.axis, this.index);\n    this.sliceAccess = extracted.sliceAccess;\n    this.jLength = extracted.jLength;\n    this.iLength = extracted.iLength;\n    this.matrix = extracted.matrix;\n    this.canvas.width = extracted.planeWidth;\n    this.canvas.height = extracted.planeHeight;\n    this.canvasBuffer.width = this.iLength;\n    this.canvasBuffer.height = this.jLength;\n    this.ctx = this.canvas.getContext(\"2d\");\n    this.ctxBuffer = this.canvasBuffer.getContext(\"2d\");\n    if (this.geometry) this.geometry.dispose();\n    this.geometry = new PlaneGeometry(extracted.planeWidth, extracted.planeHeight);\n    if (this.mesh) {\n      this.mesh.geometry = this.geometry;\n      this.mesh.matrix.identity();\n      this.mesh.applyMatrix4(this.matrix);\n    }\n    this.geometryNeedsUpdate = false;\n  }\n}\nexport { VolumeSlice };", "map": {"version": 3, "names": ["VolumeSlice", "constructor", "volume", "index", "axis", "slice", "Object", "defineProperty", "get", "set", "value", "geometryNeedsUpdate", "canvas", "document", "createElement", "canvasBuffer", "updateGeometry", "canvasMap", "Texture", "minFilter", "LinearFilter", "wrapS", "wrapT", "ClampToEdgeWrapping", "colorSpace", "encoding", "material", "MeshBasicMaterial", "map", "side", "DoubleSide", "transparent", "mesh", "<PERSON><PERSON>", "geometry", "matrixAutoUpdate", "repaint", "i<PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "sliceAccess", "ctx", "ctxBuffer", "imgData", "getImageData", "data", "volumeData", "upperThreshold", "lowerThreshold", "windowLow", "windowHigh", "pixelCount", "dataType", "j", "i", "label", "colorMap", "length", "color", "alpha", "Math", "floor", "putImageData", "drawImage", "width", "height", "needsUpdate", "extracted", "extractPerpendicularPlane", "matrix", "planeWidth", "planeHeight", "getContext", "dispose", "PlaneGeometry", "identity", "applyMatrix4"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/misc/VolumeSlice.js"], "sourcesContent": ["import { ClampToEdgeWrapping, DoubleSide, LinearFilter, Mesh, MeshBasicMaterial, PlaneGeometry, Texture } from 'three'\n\n/**\n * This class has been made to hold a slice of a volume data\n * @class\n * @param   {Volume} volume    The associated volume\n * @param   {number}       [index=0] The index of the slice\n * @param   {string}       [axis='z']      For now only 'x', 'y' or 'z' but later it will change to a normal vector\n * @see Volume\n */\nclass VolumeSlice {\n  constructor(volume, index, axis) {\n    const slice = this\n    /**\n     * @member {Volume} volume The associated volume\n     */\n    this.volume = volume\n    /**\n     * @member {Number} index The index of the slice, if changed, will automatically call updateGeometry at the next repaint\n     */\n    index = index || 0\n    Object.defineProperty(this, 'index', {\n      get: function () {\n        return index\n      },\n      set: function (value) {\n        index = value\n        slice.geometryNeedsUpdate = true\n        return index\n      },\n    })\n    /**\n     * @member {String} axis The normal axis\n     */\n    this.axis = axis || 'z'\n\n    /**\n     * @member {HTMLCanvasElement} canvas The final canvas used for the texture\n     */\n    /**\n     * @member {CanvasRenderingContext2D} ctx Context of the canvas\n     */\n    this.canvas = document.createElement('canvas')\n    /**\n     * @member {HTMLCanvasElement} canvasBuffer The intermediary canvas used to paint the data\n     */\n    /**\n     * @member {CanvasRenderingContext2D} ctxBuffer Context of the canvas buffer\n     */\n    this.canvasBuffer = document.createElement('canvas')\n    this.updateGeometry()\n\n    const canvasMap = new Texture(this.canvas)\n    canvasMap.minFilter = LinearFilter\n    canvasMap.wrapS = canvasMap.wrapT = ClampToEdgeWrapping\n    if ('colorSpace' in canvasMap) canvasMap.colorSpace = 'srgb'\n    else canvasMap.encoding = 3001 // sRGBEncoding\n    const material = new MeshBasicMaterial({ map: canvasMap, side: DoubleSide, transparent: true })\n    /**\n     * @member {Mesh} mesh The mesh ready to get used in the scene\n     */\n    this.mesh = new Mesh(this.geometry, material)\n    this.mesh.matrixAutoUpdate = false\n    /**\n     * @member {Boolean} geometryNeedsUpdate If set to true, updateGeometry will be triggered at the next repaint\n     */\n    this.geometryNeedsUpdate = true\n    this.repaint()\n\n    /**\n     * @member {Number} iLength Width of slice in the original coordinate system, corresponds to the width of the buffer canvas\n     */\n\n    /**\n     * @member {Number} jLength Height of slice in the original coordinate system, corresponds to the height of the buffer canvas\n     */\n\n    /**\n     * @member {Function} sliceAccess Function that allow the slice to access right data\n     * @see Volume.extractPerpendicularPlane\n     * @param {Number} i The first coordinate\n     * @param {Number} j The second coordinate\n     * @returns {Number} the index corresponding to the voxel in volume.data of the given position in the slice\n     */\n  }\n\n  /**\n   * @member {Function} repaint Refresh the texture and the geometry if geometryNeedsUpdate is set to true\n   * @memberof VolumeSlice\n   */\n  repaint() {\n    if (this.geometryNeedsUpdate) {\n      this.updateGeometry()\n    }\n\n    const iLength = this.iLength,\n      jLength = this.jLength,\n      sliceAccess = this.sliceAccess,\n      volume = this.volume,\n      canvas = this.canvasBuffer,\n      ctx = this.ctxBuffer\n\n    // get the imageData and pixel array from the canvas\n    const imgData = ctx.getImageData(0, 0, iLength, jLength)\n    const data = imgData.data\n    const volumeData = volume.data\n    const upperThreshold = volume.upperThreshold\n    const lowerThreshold = volume.lowerThreshold\n    const windowLow = volume.windowLow\n    const windowHigh = volume.windowHigh\n\n    // manipulate some pixel elements\n    let pixelCount = 0\n\n    if (volume.dataType === 'label') {\n      //this part is currently useless but will be used when colortables will be handled\n      for (let j = 0; j < jLength; j++) {\n        for (let i = 0; i < iLength; i++) {\n          let label = volumeData[sliceAccess(i, j)]\n          label = label >= this.colorMap.length ? (label % this.colorMap.length) + 1 : label\n          const color = this.colorMap[label]\n          data[4 * pixelCount] = (color >> 24) & 0xff\n          data[4 * pixelCount + 1] = (color >> 16) & 0xff\n          data[4 * pixelCount + 2] = (color >> 8) & 0xff\n          data[4 * pixelCount + 3] = color & 0xff\n          pixelCount++\n        }\n      }\n    } else {\n      for (let j = 0; j < jLength; j++) {\n        for (let i = 0; i < iLength; i++) {\n          let value = volumeData[sliceAccess(i, j)]\n          let alpha = 0xff\n          //apply threshold\n          alpha = upperThreshold >= value ? (lowerThreshold <= value ? alpha : 0) : 0\n          //apply window level\n          value = Math.floor((255 * (value - windowLow)) / (windowHigh - windowLow))\n          value = value > 255 ? 255 : value < 0 ? 0 : value | 0\n\n          data[4 * pixelCount] = value\n          data[4 * pixelCount + 1] = value\n          data[4 * pixelCount + 2] = value\n          data[4 * pixelCount + 3] = alpha\n          pixelCount++\n        }\n      }\n    }\n\n    ctx.putImageData(imgData, 0, 0)\n    this.ctx.drawImage(canvas, 0, 0, iLength, jLength, 0, 0, this.canvas.width, this.canvas.height)\n\n    this.mesh.material.map.needsUpdate = true\n  }\n\n  /**\n   * @member {Function} Refresh the geometry according to axis and index\n   * @see Volume.extractPerpendicularPlane\n   * @memberof VolumeSlice\n   */\n  updateGeometry() {\n    const extracted = this.volume.extractPerpendicularPlane(this.axis, this.index)\n    this.sliceAccess = extracted.sliceAccess\n    this.jLength = extracted.jLength\n    this.iLength = extracted.iLength\n    this.matrix = extracted.matrix\n\n    this.canvas.width = extracted.planeWidth\n    this.canvas.height = extracted.planeHeight\n    this.canvasBuffer.width = this.iLength\n    this.canvasBuffer.height = this.jLength\n    this.ctx = this.canvas.getContext('2d')\n    this.ctxBuffer = this.canvasBuffer.getContext('2d')\n\n    if (this.geometry) this.geometry.dispose() // dispose existing geometry\n\n    this.geometry = new PlaneGeometry(extracted.planeWidth, extracted.planeHeight)\n\n    if (this.mesh) {\n      this.mesh.geometry = this.geometry\n      //reset mesh matrix\n      this.mesh.matrix.identity()\n      this.mesh.applyMatrix4(this.matrix)\n    }\n\n    this.geometryNeedsUpdate = false\n  }\n}\n\nexport { VolumeSlice }\n"], "mappings": ";AAUA,MAAMA,WAAA,CAAY;EAChBC,YAAYC,MAAA,EAAQC,KAAA,EAAOC,IAAA,EAAM;IAC/B,MAAMC,KAAA,GAAQ;IAId,KAAKH,MAAA,GAASA,MAAA;IAIdC,KAAA,GAAQA,KAAA,IAAS;IACjBG,MAAA,CAAOC,cAAA,CAAe,MAAM,SAAS;MACnCC,GAAA,EAAK,SAAAA,CAAA,EAAY;QACf,OAAOL,KAAA;MACR;MACDM,GAAA,EAAK,SAAAA,CAAUC,KAAA,EAAO;QACpBP,KAAA,GAAQO,KAAA;QACRL,KAAA,CAAMM,mBAAA,GAAsB;QAC5B,OAAOR,KAAA;MACR;IACP,CAAK;IAID,KAAKC,IAAA,GAAOA,IAAA,IAAQ;IAQpB,KAAKQ,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAO7C,KAAKC,YAAA,GAAeF,QAAA,CAASC,aAAA,CAAc,QAAQ;IACnD,KAAKE,cAAA,CAAgB;IAErB,MAAMC,SAAA,GAAY,IAAIC,OAAA,CAAQ,KAAKN,MAAM;IACzCK,SAAA,CAAUE,SAAA,GAAYC,YAAA;IACtBH,SAAA,CAAUI,KAAA,GAAQJ,SAAA,CAAUK,KAAA,GAAQC,mBAAA;IACpC,IAAI,gBAAgBN,SAAA,EAAWA,SAAA,CAAUO,UAAA,GAAa,YACjDP,SAAA,CAAUQ,QAAA,GAAW;IAC1B,MAAMC,QAAA,GAAW,IAAIC,iBAAA,CAAkB;MAAEC,GAAA,EAAKX,SAAA;MAAWY,IAAA,EAAMC,UAAA;MAAYC,WAAA,EAAa;IAAA,CAAM;IAI9F,KAAKC,IAAA,GAAO,IAAIC,IAAA,CAAK,KAAKC,QAAA,EAAUR,QAAQ;IAC5C,KAAKM,IAAA,CAAKG,gBAAA,GAAmB;IAI7B,KAAKxB,mBAAA,GAAsB;IAC3B,KAAKyB,OAAA,CAAS;EAiBf;EAAA;AAAA;AAAA;AAAA;EAMDA,QAAA,EAAU;IACR,IAAI,KAAKzB,mBAAA,EAAqB;MAC5B,KAAKK,cAAA,CAAgB;IACtB;IAED,MAAMqB,OAAA,GAAU,KAAKA,OAAA;MACnBC,OAAA,GAAU,KAAKA,OAAA;MACfC,WAAA,GAAc,KAAKA,WAAA;MACnBrC,MAAA,GAAS,KAAKA,MAAA;MACdU,MAAA,GAAS,KAAKG,YAAA;MACdyB,GAAA,GAAM,KAAKC,SAAA;IAGb,MAAMC,OAAA,GAAUF,GAAA,CAAIG,YAAA,CAAa,GAAG,GAAGN,OAAA,EAASC,OAAO;IACvD,MAAMM,IAAA,GAAOF,OAAA,CAAQE,IAAA;IACrB,MAAMC,UAAA,GAAa3C,MAAA,CAAO0C,IAAA;IAC1B,MAAME,cAAA,GAAiB5C,MAAA,CAAO4C,cAAA;IAC9B,MAAMC,cAAA,GAAiB7C,MAAA,CAAO6C,cAAA;IAC9B,MAAMC,SAAA,GAAY9C,MAAA,CAAO8C,SAAA;IACzB,MAAMC,UAAA,GAAa/C,MAAA,CAAO+C,UAAA;IAG1B,IAAIC,UAAA,GAAa;IAEjB,IAAIhD,MAAA,CAAOiD,QAAA,KAAa,SAAS;MAE/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAId,OAAA,EAASc,CAAA,IAAK;QAChC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIhB,OAAA,EAASgB,CAAA,IAAK;UAChC,IAAIC,KAAA,GAAQT,UAAA,CAAWN,WAAA,CAAYc,CAAA,EAAGD,CAAC,CAAC;UACxCE,KAAA,GAAQA,KAAA,IAAS,KAAKC,QAAA,CAASC,MAAA,GAAUF,KAAA,GAAQ,KAAKC,QAAA,CAASC,MAAA,GAAU,IAAIF,KAAA;UAC7E,MAAMG,KAAA,GAAQ,KAAKF,QAAA,CAASD,KAAK;UACjCV,IAAA,CAAK,IAAIM,UAAU,IAAKO,KAAA,IAAS,KAAM;UACvCb,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAKO,KAAA,IAAS,KAAM;UAC3Cb,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAKO,KAAA,IAAS,IAAK;UAC1Cb,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAIO,KAAA,GAAQ;UACnCP,UAAA;QACD;MACF;IACP,OAAW;MACL,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAId,OAAA,EAASc,CAAA,IAAK;QAChC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIhB,OAAA,EAASgB,CAAA,IAAK;UAChC,IAAI3C,KAAA,GAAQmC,UAAA,CAAWN,WAAA,CAAYc,CAAA,EAAGD,CAAC,CAAC;UACxC,IAAIM,KAAA,GAAQ;UAEZA,KAAA,GAAQZ,cAAA,IAAkBpC,KAAA,GAASqC,cAAA,IAAkBrC,KAAA,GAAQgD,KAAA,GAAQ,IAAK;UAE1EhD,KAAA,GAAQiD,IAAA,CAAKC,KAAA,CAAO,OAAOlD,KAAA,GAAQsC,SAAA,KAAeC,UAAA,GAAaD,SAAA,CAAU;UACzEtC,KAAA,GAAQA,KAAA,GAAQ,MAAM,MAAMA,KAAA,GAAQ,IAAI,IAAIA,KAAA,GAAQ;UAEpDkC,IAAA,CAAK,IAAIM,UAAU,IAAIxC,KAAA;UACvBkC,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAIxC,KAAA;UAC3BkC,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAIxC,KAAA;UAC3BkC,IAAA,CAAK,IAAIM,UAAA,GAAa,CAAC,IAAIQ,KAAA;UAC3BR,UAAA;QACD;MACF;IACF;IAEDV,GAAA,CAAIqB,YAAA,CAAanB,OAAA,EAAS,GAAG,CAAC;IAC9B,KAAKF,GAAA,CAAIsB,SAAA,CAAUlD,MAAA,EAAQ,GAAG,GAAGyB,OAAA,EAASC,OAAA,EAAS,GAAG,GAAG,KAAK1B,MAAA,CAAOmD,KAAA,EAAO,KAAKnD,MAAA,CAAOoD,MAAM;IAE9F,KAAKhC,IAAA,CAAKN,QAAA,CAASE,GAAA,CAAIqC,WAAA,GAAc;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;EAODjD,eAAA,EAAiB;IACf,MAAMkD,SAAA,GAAY,KAAKhE,MAAA,CAAOiE,yBAAA,CAA0B,KAAK/D,IAAA,EAAM,KAAKD,KAAK;IAC7E,KAAKoC,WAAA,GAAc2B,SAAA,CAAU3B,WAAA;IAC7B,KAAKD,OAAA,GAAU4B,SAAA,CAAU5B,OAAA;IACzB,KAAKD,OAAA,GAAU6B,SAAA,CAAU7B,OAAA;IACzB,KAAK+B,MAAA,GAASF,SAAA,CAAUE,MAAA;IAExB,KAAKxD,MAAA,CAAOmD,KAAA,GAAQG,SAAA,CAAUG,UAAA;IAC9B,KAAKzD,MAAA,CAAOoD,MAAA,GAASE,SAAA,CAAUI,WAAA;IAC/B,KAAKvD,YAAA,CAAagD,KAAA,GAAQ,KAAK1B,OAAA;IAC/B,KAAKtB,YAAA,CAAaiD,MAAA,GAAS,KAAK1B,OAAA;IAChC,KAAKE,GAAA,GAAM,KAAK5B,MAAA,CAAO2D,UAAA,CAAW,IAAI;IACtC,KAAK9B,SAAA,GAAY,KAAK1B,YAAA,CAAawD,UAAA,CAAW,IAAI;IAElD,IAAI,KAAKrC,QAAA,EAAU,KAAKA,QAAA,CAASsC,OAAA,CAAS;IAE1C,KAAKtC,QAAA,GAAW,IAAIuC,aAAA,CAAcP,SAAA,CAAUG,UAAA,EAAYH,SAAA,CAAUI,WAAW;IAE7E,IAAI,KAAKtC,IAAA,EAAM;MACb,KAAKA,IAAA,CAAKE,QAAA,GAAW,KAAKA,QAAA;MAE1B,KAAKF,IAAA,CAAKoC,MAAA,CAAOM,QAAA,CAAU;MAC3B,KAAK1C,IAAA,CAAK2C,YAAA,CAAa,KAAKP,MAAM;IACnC;IAED,KAAKzD,mBAAA,GAAsB;EAC5B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}