{"ast": null, "code": "/**\n * Sets `BufferAttribute.updateRange` since r159.\n */\nconst setUpdateRange = (attribute, updateRange) => {\n  attribute.updateRanges[0] = updateRange;\n};\nexport { setUpdateRange };", "map": {"version": 3, "names": ["setUpdateRange", "attribute", "updateRange", "updateRanges"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/helpers/deprecated.js"], "sourcesContent": ["/**\n * Sets `BufferAttribute.updateRange` since r159.\n */\nconst setUpdateRange = (attribute, updateRange) => {\n  attribute.updateRanges[0] = updateRange;\n};\n\nexport { setUpdateRange };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,cAAc,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;EACjDD,SAAS,CAACE,YAAY,CAAC,CAAC,CAAC,GAAGD,WAAW;AACzC,CAAC;AAED,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}