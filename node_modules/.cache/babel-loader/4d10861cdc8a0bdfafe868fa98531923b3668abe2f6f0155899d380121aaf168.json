{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector2, Color, WebGLRenderTarget, HalfFloatType, UniformsUtils, ShaderMaterial, Vector3, AdditiveBlending, MeshBasicMaterial } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nimport { LuminosityHighPassShader } from \"../shaders/LuminosityHighPassShader.js\";\nconst UnrealBloomPass = /* @__PURE__ */(() => {\n  const _UnrealBloomPass = class extends Pass {\n    constructor(resolution, strength, radius, threshold) {\n      super();\n      this.strength = strength !== void 0 ? strength : 1;\n      this.radius = radius;\n      this.threshold = threshold;\n      this.resolution = resolution !== void 0 ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256);\n      this.clearColor = new Color(0, 0, 0);\n      this.renderTargetsHorizontal = [];\n      this.renderTargetsVertical = [];\n      this.nMips = 5;\n      let resx = Math.round(this.resolution.x / 2);\n      let resy = Math.round(this.resolution.y / 2);\n      this.renderTargetBright = new WebGLRenderTarget(resx, resy, {\n        type: HalfFloatType\n      });\n      this.renderTargetBright.texture.name = \"UnrealBloomPass.bright\";\n      this.renderTargetBright.texture.generateMipmaps = false;\n      for (let i = 0; i < this.nMips; i++) {\n        const renderTargetHorizonal = new WebGLRenderTarget(resx, resy, {\n          type: HalfFloatType\n        });\n        renderTargetHorizonal.texture.name = \"UnrealBloomPass.h\" + i;\n        renderTargetHorizonal.texture.generateMipmaps = false;\n        this.renderTargetsHorizontal.push(renderTargetHorizonal);\n        const renderTargetVertical = new WebGLRenderTarget(resx, resy, {\n          type: HalfFloatType\n        });\n        renderTargetVertical.texture.name = \"UnrealBloomPass.v\" + i;\n        renderTargetVertical.texture.generateMipmaps = false;\n        this.renderTargetsVertical.push(renderTargetVertical);\n        resx = Math.round(resx / 2);\n        resy = Math.round(resy / 2);\n      }\n      const highPassShader = LuminosityHighPassShader;\n      this.highPassUniforms = UniformsUtils.clone(highPassShader.uniforms);\n      this.highPassUniforms[\"luminosityThreshold\"].value = threshold;\n      this.highPassUniforms[\"smoothWidth\"].value = 0.01;\n      this.materialHighPassFilter = new ShaderMaterial({\n        uniforms: this.highPassUniforms,\n        vertexShader: highPassShader.vertexShader,\n        fragmentShader: highPassShader.fragmentShader,\n        defines: {}\n      });\n      this.separableBlurMaterials = [];\n      const kernelSizeArray = [3, 5, 7, 9, 11];\n      resx = Math.round(this.resolution.x / 2);\n      resy = Math.round(this.resolution.y / 2);\n      for (let i = 0; i < this.nMips; i++) {\n        this.separableBlurMaterials.push(this.getSeperableBlurMaterial(kernelSizeArray[i]));\n        this.separableBlurMaterials[i].uniforms[\"texSize\"].value = new Vector2(resx, resy);\n        resx = Math.round(resx / 2);\n        resy = Math.round(resy / 2);\n      }\n      this.compositeMaterial = this.getCompositeMaterial(this.nMips);\n      this.compositeMaterial.uniforms[\"blurTexture1\"].value = this.renderTargetsVertical[0].texture;\n      this.compositeMaterial.uniforms[\"blurTexture2\"].value = this.renderTargetsVertical[1].texture;\n      this.compositeMaterial.uniforms[\"blurTexture3\"].value = this.renderTargetsVertical[2].texture;\n      this.compositeMaterial.uniforms[\"blurTexture4\"].value = this.renderTargetsVertical[3].texture;\n      this.compositeMaterial.uniforms[\"blurTexture5\"].value = this.renderTargetsVertical[4].texture;\n      this.compositeMaterial.uniforms[\"bloomStrength\"].value = strength;\n      this.compositeMaterial.uniforms[\"bloomRadius\"].value = 0.1;\n      this.compositeMaterial.needsUpdate = true;\n      const bloomFactors = [1, 0.8, 0.6, 0.4, 0.2];\n      this.compositeMaterial.uniforms[\"bloomFactors\"].value = bloomFactors;\n      this.bloomTintColors = [new Vector3(1, 1, 1), new Vector3(1, 1, 1), new Vector3(1, 1, 1), new Vector3(1, 1, 1), new Vector3(1, 1, 1)];\n      this.compositeMaterial.uniforms[\"bloomTintColors\"].value = this.bloomTintColors;\n      const copyShader = CopyShader;\n      this.copyUniforms = UniformsUtils.clone(copyShader.uniforms);\n      this.copyUniforms[\"opacity\"].value = 1;\n      this.materialCopy = new ShaderMaterial({\n        uniforms: this.copyUniforms,\n        vertexShader: copyShader.vertexShader,\n        fragmentShader: copyShader.fragmentShader,\n        blending: AdditiveBlending,\n        depthTest: false,\n        depthWrite: false,\n        transparent: true\n      });\n      this.enabled = true;\n      this.needsSwap = false;\n      this._oldClearColor = new Color();\n      this.oldClearAlpha = 1;\n      this.basic = new MeshBasicMaterial();\n      this.fsQuad = new FullScreenQuad(null);\n    }\n    dispose() {\n      for (let i = 0; i < this.renderTargetsHorizontal.length; i++) {\n        this.renderTargetsHorizontal[i].dispose();\n      }\n      for (let i = 0; i < this.renderTargetsVertical.length; i++) {\n        this.renderTargetsVertical[i].dispose();\n      }\n      this.renderTargetBright.dispose();\n      for (let i = 0; i < this.separableBlurMaterials.length; i++) {\n        this.separableBlurMaterials[i].dispose();\n      }\n      this.compositeMaterial.dispose();\n      this.materialCopy.dispose();\n      this.basic.dispose();\n      this.fsQuad.dispose();\n    }\n    setSize(width, height) {\n      let resx = Math.round(width / 2);\n      let resy = Math.round(height / 2);\n      this.renderTargetBright.setSize(resx, resy);\n      for (let i = 0; i < this.nMips; i++) {\n        this.renderTargetsHorizontal[i].setSize(resx, resy);\n        this.renderTargetsVertical[i].setSize(resx, resy);\n        this.separableBlurMaterials[i].uniforms[\"texSize\"].value = new Vector2(resx, resy);\n        resx = Math.round(resx / 2);\n        resy = Math.round(resy / 2);\n      }\n    }\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n      renderer.getClearColor(this._oldClearColor);\n      this.oldClearAlpha = renderer.getClearAlpha();\n      const oldAutoClear = renderer.autoClear;\n      renderer.autoClear = false;\n      renderer.setClearColor(this.clearColor, 0);\n      if (maskActive) renderer.state.buffers.stencil.setTest(false);\n      if (this.renderToScreen) {\n        this.fsQuad.material = this.basic;\n        this.basic.map = readBuffer.texture;\n        renderer.setRenderTarget(null);\n        renderer.clear();\n        this.fsQuad.render(renderer);\n      }\n      this.highPassUniforms[\"tDiffuse\"].value = readBuffer.texture;\n      this.highPassUniforms[\"luminosityThreshold\"].value = this.threshold;\n      this.fsQuad.material = this.materialHighPassFilter;\n      renderer.setRenderTarget(this.renderTargetBright);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      let inputRenderTarget = this.renderTargetBright;\n      for (let i = 0; i < this.nMips; i++) {\n        this.fsQuad.material = this.separableBlurMaterials[i];\n        this.separableBlurMaterials[i].uniforms[\"colorTexture\"].value = inputRenderTarget.texture;\n        this.separableBlurMaterials[i].uniforms[\"direction\"].value = _UnrealBloomPass.BlurDirectionX;\n        renderer.setRenderTarget(this.renderTargetsHorizontal[i]);\n        renderer.clear();\n        this.fsQuad.render(renderer);\n        this.separableBlurMaterials[i].uniforms[\"colorTexture\"].value = this.renderTargetsHorizontal[i].texture;\n        this.separableBlurMaterials[i].uniforms[\"direction\"].value = _UnrealBloomPass.BlurDirectionY;\n        renderer.setRenderTarget(this.renderTargetsVertical[i]);\n        renderer.clear();\n        this.fsQuad.render(renderer);\n        inputRenderTarget = this.renderTargetsVertical[i];\n      }\n      this.fsQuad.material = this.compositeMaterial;\n      this.compositeMaterial.uniforms[\"bloomStrength\"].value = this.strength;\n      this.compositeMaterial.uniforms[\"bloomRadius\"].value = this.radius;\n      this.compositeMaterial.uniforms[\"bloomTintColors\"].value = this.bloomTintColors;\n      renderer.setRenderTarget(this.renderTargetsHorizontal[0]);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.materialCopy;\n      this.copyUniforms[\"tDiffuse\"].value = this.renderTargetsHorizontal[0].texture;\n      if (maskActive) renderer.state.buffers.stencil.setTest(true);\n      if (this.renderToScreen) {\n        renderer.setRenderTarget(null);\n        this.fsQuad.render(renderer);\n      } else {\n        renderer.setRenderTarget(readBuffer);\n        this.fsQuad.render(renderer);\n      }\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha);\n      renderer.autoClear = oldAutoClear;\n    }\n    getSeperableBlurMaterial(kernelRadius) {\n      return new ShaderMaterial({\n        defines: {\n          KERNEL_RADIUS: kernelRadius,\n          SIGMA: kernelRadius\n        },\n        uniforms: {\n          colorTexture: {\n            value: null\n          },\n          texSize: {\n            value: new Vector2(0.5, 0.5)\n          },\n          direction: {\n            value: new Vector2(0.5, 0.5)\n          }\n        },\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n        fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat fSigma = float(SIGMA);\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, fSigma);\n\t\t\t\t\tvec3 diffuseSum = texture2D( colorTexture, vUv).rgb * weightSum;\n\t\t\t\t\tfor( int i = 1; i < KERNEL_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat x = float(i);\n\t\t\t\t\t\tfloat w = gaussianPdf(x, fSigma);\n\t\t\t\t\t\tvec2 uvOffset = direction * invSize * x;\n\t\t\t\t\t\tvec3 sample1 = texture2D( colorTexture, vUv + uvOffset).rgb;\n\t\t\t\t\t\tvec3 sample2 = texture2D( colorTexture, vUv - uvOffset).rgb;\n\t\t\t\t\t\tdiffuseSum += (sample1 + sample2) * w;\n\t\t\t\t\t\tweightSum += 2.0 * w;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = vec4(diffuseSum/weightSum, 1.0);\n\t\t\t\t}`\n      });\n    }\n    getCompositeMaterial(nMips) {\n      return new ShaderMaterial({\n        defines: {\n          NUM_MIPS: nMips\n        },\n        uniforms: {\n          blurTexture1: {\n            value: null\n          },\n          blurTexture2: {\n            value: null\n          },\n          blurTexture3: {\n            value: null\n          },\n          blurTexture4: {\n            value: null\n          },\n          blurTexture5: {\n            value: null\n          },\n          bloomStrength: {\n            value: 1\n          },\n          bloomFactors: {\n            value: null\n          },\n          bloomTintColors: {\n            value: null\n          },\n          bloomRadius: {\n            value: 0\n          }\n        },\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n        fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D blurTexture1;\n\t\t\t\tuniform sampler2D blurTexture2;\n\t\t\t\tuniform sampler2D blurTexture3;\n\t\t\t\tuniform sampler2D blurTexture4;\n\t\t\t\tuniform sampler2D blurTexture5;\n\t\t\t\tuniform float bloomStrength;\n\t\t\t\tuniform float bloomRadius;\n\t\t\t\tuniform float bloomFactors[NUM_MIPS];\n\t\t\t\tuniform vec3 bloomTintColors[NUM_MIPS];\n\n\t\t\t\tfloat lerpBloomFactor(const in float factor) {\n\t\t\t\t\tfloat mirrorFactor = 1.2 - factor;\n\t\t\t\t\treturn mix(factor, mirrorFactor, bloomRadius);\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tgl_FragColor = bloomStrength * ( lerpBloomFactor(bloomFactors[0]) * vec4(bloomTintColors[0], 1.0) * texture2D(blurTexture1, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[1]) * vec4(bloomTintColors[1], 1.0) * texture2D(blurTexture2, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[2]) * vec4(bloomTintColors[2], 1.0) * texture2D(blurTexture3, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[3]) * vec4(bloomTintColors[3], 1.0) * texture2D(blurTexture4, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[4]) * vec4(bloomTintColors[4], 1.0) * texture2D(blurTexture5, vUv) );\n\t\t\t\t}`\n      });\n    }\n  };\n  let UnrealBloomPass2 = _UnrealBloomPass;\n  __publicField(UnrealBloomPass2, \"BlurDirectionX\", new Vector2(1, 0));\n  __publicField(UnrealBloomPass2, \"BlurDirectionY\", new Vector2(0, 1));\n  return UnrealBloomPass2;\n})();\nexport { UnrealBloomPass };", "map": {"version": 3, "names": ["UnrealBloomPass", "_UnrealBloomPass", "Pass", "constructor", "resolution", "strength", "radius", "threshold", "Vector2", "x", "y", "clearColor", "Color", "renderTargetsHorizontal", "renderTargetsVertical", "nMips", "resx", "Math", "round", "resy", "renderTargetBright", "WebGLRenderTarget", "type", "HalfFloatType", "texture", "name", "generateMipmaps", "i", "renderTargetHorizonal", "push", "renderTargetVertical", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LuminosityHighPass<PERSON><PERSON>er", "highPassUniforms", "UniformsUtils", "clone", "uniforms", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ShaderMaterial", "vertexShader", "fragmentShader", "defines", "separableBlurMaterials", "kernelSizeArray", "getSeperableBlurMaterial", "compositeMaterial", "getCompositeMaterial", "needsUpdate", "bloomFactors", "bloomTintColors", "Vector3", "copyShader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyUniforms", "materialCopy", "blending", "AdditiveBlending", "depthTest", "depthWrite", "transparent", "enabled", "needsSwap", "_oldClearColor", "oldClearAlpha", "basic", "MeshBasicMaterial", "fsQuad", "FullScreenQuad", "dispose", "length", "setSize", "width", "height", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "maskActive", "getClearColor", "getClearAlpha", "oldAutoClear", "autoClear", "setClearColor", "state", "buffers", "stencil", "setTest", "renderToScreen", "material", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "inputRenderTarget", "BlurDirectionX", "BlurDirectionY", "kernelRadius", "KERNEL_RADIUS", "SIGMA", "colorTexture", "texSize", "direction", "NUM_MIPS", "blurTexture1", "blurTexture2", "blurTexture3", "blurTexture4", "blurTexture5", "bloomStrength", "bloomRadius", "UnrealBloomPass2", "__publicField"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/UnrealBloomPass.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  Color,\n  HalfFloatType,\n  MeshBasicMaterial,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector2,\n  Vector3,\n  WebGLRenderTarget,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { LuminosityHighPassShader } from '../shaders/LuminosityHighPassShader'\n\n/**\n * UnrealBloomPass is inspired by the bloom pass of Unreal Engine. It creates a\n * mip map chain of bloom textures and blurs them with different radii. Because\n * of the weighted combination of mips, and because larger blurs are done on\n * higher mips, this effect provides good quality and performance.\n *\n * Reference:\n * - https://docs.unrealengine.com/latest/INT/Engine/Rendering/PostProcessEffects/Bloom/\n */\nconst UnrealBloomPass = /* @__PURE__ */ (() => {\n  class UnrealBloomPass extends Pass {\n    static BlurDirectionX = new Vector2(1.0, 0.0)\n    static BlurDirectionY = new Vector2(0.0, 1.0)\n\n    constructor(resolution, strength, radius, threshold) {\n      super()\n\n      this.strength = strength !== undefined ? strength : 1\n      this.radius = radius\n      this.threshold = threshold\n      this.resolution = resolution !== undefined ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256)\n\n      // create color only once here, reuse it later inside the render function\n      this.clearColor = new Color(0, 0, 0)\n\n      // render targets\n      this.renderTargetsHorizontal = []\n      this.renderTargetsVertical = []\n      this.nMips = 5\n      let resx = Math.round(this.resolution.x / 2)\n      let resy = Math.round(this.resolution.y / 2)\n\n      this.renderTargetBright = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n      this.renderTargetBright.texture.name = 'UnrealBloomPass.bright'\n      this.renderTargetBright.texture.generateMipmaps = false\n\n      for (let i = 0; i < this.nMips; i++) {\n        const renderTargetHorizonal = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n\n        renderTargetHorizonal.texture.name = 'UnrealBloomPass.h' + i\n        renderTargetHorizonal.texture.generateMipmaps = false\n\n        this.renderTargetsHorizontal.push(renderTargetHorizonal)\n\n        const renderTargetVertical = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n\n        renderTargetVertical.texture.name = 'UnrealBloomPass.v' + i\n        renderTargetVertical.texture.generateMipmaps = false\n\n        this.renderTargetsVertical.push(renderTargetVertical)\n\n        resx = Math.round(resx / 2)\n\n        resy = Math.round(resy / 2)\n      }\n\n      // luminosity high pass material\n\n      const highPassShader = LuminosityHighPassShader\n      this.highPassUniforms = UniformsUtils.clone(highPassShader.uniforms)\n\n      this.highPassUniforms['luminosityThreshold'].value = threshold\n      this.highPassUniforms['smoothWidth'].value = 0.01\n\n      this.materialHighPassFilter = new ShaderMaterial({\n        uniforms: this.highPassUniforms,\n        vertexShader: highPassShader.vertexShader,\n        fragmentShader: highPassShader.fragmentShader,\n        defines: {},\n      })\n\n      // Gaussian Blur Materials\n      this.separableBlurMaterials = []\n      const kernelSizeArray = [3, 5, 7, 9, 11]\n      resx = Math.round(this.resolution.x / 2)\n      resy = Math.round(this.resolution.y / 2)\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.separableBlurMaterials.push(this.getSeperableBlurMaterial(kernelSizeArray[i]))\n\n        this.separableBlurMaterials[i].uniforms['texSize'].value = new Vector2(resx, resy)\n\n        resx = Math.round(resx / 2)\n\n        resy = Math.round(resy / 2)\n      }\n\n      // Composite material\n      this.compositeMaterial = this.getCompositeMaterial(this.nMips)\n      this.compositeMaterial.uniforms['blurTexture1'].value = this.renderTargetsVertical[0].texture\n      this.compositeMaterial.uniforms['blurTexture2'].value = this.renderTargetsVertical[1].texture\n      this.compositeMaterial.uniforms['blurTexture3'].value = this.renderTargetsVertical[2].texture\n      this.compositeMaterial.uniforms['blurTexture4'].value = this.renderTargetsVertical[3].texture\n      this.compositeMaterial.uniforms['blurTexture5'].value = this.renderTargetsVertical[4].texture\n      this.compositeMaterial.uniforms['bloomStrength'].value = strength\n      this.compositeMaterial.uniforms['bloomRadius'].value = 0.1\n      this.compositeMaterial.needsUpdate = true\n\n      const bloomFactors = [1.0, 0.8, 0.6, 0.4, 0.2]\n      this.compositeMaterial.uniforms['bloomFactors'].value = bloomFactors\n      this.bloomTintColors = [\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n      ]\n      this.compositeMaterial.uniforms['bloomTintColors'].value = this.bloomTintColors\n\n      // copy material\n\n      const copyShader = CopyShader\n\n      this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n      this.copyUniforms['opacity'].value = 1.0\n\n      this.materialCopy = new ShaderMaterial({\n        uniforms: this.copyUniforms,\n        vertexShader: copyShader.vertexShader,\n        fragmentShader: copyShader.fragmentShader,\n        blending: AdditiveBlending,\n        depthTest: false,\n        depthWrite: false,\n        transparent: true,\n      })\n\n      this.enabled = true\n      this.needsSwap = false\n\n      this._oldClearColor = new Color()\n      this.oldClearAlpha = 1\n\n      this.basic = new MeshBasicMaterial()\n\n      this.fsQuad = new FullScreenQuad(null)\n    }\n\n    dispose() {\n      for (let i = 0; i < this.renderTargetsHorizontal.length; i++) {\n        this.renderTargetsHorizontal[i].dispose()\n      }\n\n      for (let i = 0; i < this.renderTargetsVertical.length; i++) {\n        this.renderTargetsVertical[i].dispose()\n      }\n\n      this.renderTargetBright.dispose()\n\n      //\n\n      for (let i = 0; i < this.separableBlurMaterials.length; i++) {\n        this.separableBlurMaterials[i].dispose()\n      }\n\n      this.compositeMaterial.dispose()\n      this.materialCopy.dispose()\n      this.basic.dispose()\n\n      //\n\n      this.fsQuad.dispose()\n    }\n\n    setSize(width, height) {\n      let resx = Math.round(width / 2)\n      let resy = Math.round(height / 2)\n\n      this.renderTargetBright.setSize(resx, resy)\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.renderTargetsHorizontal[i].setSize(resx, resy)\n        this.renderTargetsVertical[i].setSize(resx, resy)\n\n        this.separableBlurMaterials[i].uniforms['texSize'].value = new Vector2(resx, resy)\n\n        resx = Math.round(resx / 2)\n        resy = Math.round(resy / 2)\n      }\n    }\n\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n      renderer.autoClear = false\n\n      renderer.setClearColor(this.clearColor, 0)\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(false)\n\n      // Render input to screen\n\n      if (this.renderToScreen) {\n        this.fsQuad.material = this.basic\n        this.basic.map = readBuffer.texture\n\n        renderer.setRenderTarget(null)\n        renderer.clear()\n        this.fsQuad.render(renderer)\n      }\n\n      // 1. Extract Bright Areas\n\n      this.highPassUniforms['tDiffuse'].value = readBuffer.texture\n      this.highPassUniforms['luminosityThreshold'].value = this.threshold\n      this.fsQuad.material = this.materialHighPassFilter\n\n      renderer.setRenderTarget(this.renderTargetBright)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // 2. Blur All the mips progressively\n\n      let inputRenderTarget = this.renderTargetBright\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.fsQuad.material = this.separableBlurMaterials[i]\n\n        this.separableBlurMaterials[i].uniforms['colorTexture'].value = inputRenderTarget.texture\n        this.separableBlurMaterials[i].uniforms['direction'].value = UnrealBloomPass.BlurDirectionX\n        renderer.setRenderTarget(this.renderTargetsHorizontal[i])\n        renderer.clear()\n        this.fsQuad.render(renderer)\n\n        this.separableBlurMaterials[i].uniforms['colorTexture'].value = this.renderTargetsHorizontal[i].texture\n        this.separableBlurMaterials[i].uniforms['direction'].value = UnrealBloomPass.BlurDirectionY\n        renderer.setRenderTarget(this.renderTargetsVertical[i])\n        renderer.clear()\n        this.fsQuad.render(renderer)\n\n        inputRenderTarget = this.renderTargetsVertical[i]\n      }\n\n      // Composite All the mips\n\n      this.fsQuad.material = this.compositeMaterial\n      this.compositeMaterial.uniforms['bloomStrength'].value = this.strength\n      this.compositeMaterial.uniforms['bloomRadius'].value = this.radius\n      this.compositeMaterial.uniforms['bloomTintColors'].value = this.bloomTintColors\n\n      renderer.setRenderTarget(this.renderTargetsHorizontal[0])\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Blend it additively over the input texture\n\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = this.renderTargetsHorizontal[0].texture\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(true)\n\n      if (this.renderToScreen) {\n        renderer.setRenderTarget(null)\n        this.fsQuad.render(renderer)\n      } else {\n        renderer.setRenderTarget(readBuffer)\n        this.fsQuad.render(renderer)\n      }\n\n      // Restore renderer settings\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    getSeperableBlurMaterial(kernelRadius) {\n      return new ShaderMaterial({\n        defines: {\n          KERNEL_RADIUS: kernelRadius,\n          SIGMA: kernelRadius,\n        },\n\n        uniforms: {\n          colorTexture: { value: null },\n          texSize: { value: new Vector2(0.5, 0.5) },\n          direction: { value: new Vector2(0.5, 0.5) },\n        },\n\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n        fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat fSigma = float(SIGMA);\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, fSigma);\n\t\t\t\t\tvec3 diffuseSum = texture2D( colorTexture, vUv).rgb * weightSum;\n\t\t\t\t\tfor( int i = 1; i < KERNEL_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat x = float(i);\n\t\t\t\t\t\tfloat w = gaussianPdf(x, fSigma);\n\t\t\t\t\t\tvec2 uvOffset = direction * invSize * x;\n\t\t\t\t\t\tvec3 sample1 = texture2D( colorTexture, vUv + uvOffset).rgb;\n\t\t\t\t\t\tvec3 sample2 = texture2D( colorTexture, vUv - uvOffset).rgb;\n\t\t\t\t\t\tdiffuseSum += (sample1 + sample2) * w;\n\t\t\t\t\t\tweightSum += 2.0 * w;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = vec4(diffuseSum/weightSum, 1.0);\n\t\t\t\t}`,\n      })\n    }\n\n    getCompositeMaterial(nMips) {\n      return new ShaderMaterial({\n        defines: {\n          NUM_MIPS: nMips,\n        },\n\n        uniforms: {\n          blurTexture1: { value: null },\n          blurTexture2: { value: null },\n          blurTexture3: { value: null },\n          blurTexture4: { value: null },\n          blurTexture5: { value: null },\n          bloomStrength: { value: 1.0 },\n          bloomFactors: { value: null },\n          bloomTintColors: { value: null },\n          bloomRadius: { value: 0.0 },\n        },\n\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n        fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D blurTexture1;\n\t\t\t\tuniform sampler2D blurTexture2;\n\t\t\t\tuniform sampler2D blurTexture3;\n\t\t\t\tuniform sampler2D blurTexture4;\n\t\t\t\tuniform sampler2D blurTexture5;\n\t\t\t\tuniform float bloomStrength;\n\t\t\t\tuniform float bloomRadius;\n\t\t\t\tuniform float bloomFactors[NUM_MIPS];\n\t\t\t\tuniform vec3 bloomTintColors[NUM_MIPS];\n\n\t\t\t\tfloat lerpBloomFactor(const in float factor) {\n\t\t\t\t\tfloat mirrorFactor = 1.2 - factor;\n\t\t\t\t\treturn mix(factor, mirrorFactor, bloomRadius);\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tgl_FragColor = bloomStrength * ( lerpBloomFactor(bloomFactors[0]) * vec4(bloomTintColors[0], 1.0) * texture2D(blurTexture1, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[1]) * vec4(bloomTintColors[1], 1.0) * texture2D(blurTexture2, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[2]) * vec4(bloomTintColors[2], 1.0) * texture2D(blurTexture3, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[3]) * vec4(bloomTintColors[3], 1.0) * texture2D(blurTexture4, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[4]) * vec4(bloomTintColors[4], 1.0) * texture2D(blurTexture5, vUv) );\n\t\t\t\t}`,\n      })\n    }\n  }\n\n  return UnrealBloomPass\n})()\n\nexport { UnrealBloomPass }\n"], "mappings": ";;;;;;;;;;;;;;;AAwBK,MAACA,eAAA,GAAmC,sBAAM;EAC7C,MAAMC,gBAAA,GAAN,cAA8BC,IAAA,CAAK;IAIjCC,YAAYC,UAAA,EAAYC,QAAA,EAAUC,MAAA,EAAQC,SAAA,EAAW;MACnD,MAAO;MAEP,KAAKF,QAAA,GAAWA,QAAA,KAAa,SAAYA,QAAA,GAAW;MACpD,KAAKC,MAAA,GAASA,MAAA;MACd,KAAKC,SAAA,GAAYA,SAAA;MACjB,KAAKH,UAAA,GAAaA,UAAA,KAAe,SAAY,IAAII,OAAA,CAAQJ,UAAA,CAAWK,CAAA,EAAGL,UAAA,CAAWM,CAAC,IAAI,IAAIF,OAAA,CAAQ,KAAK,GAAG;MAG3G,KAAKG,UAAA,GAAa,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC;MAGnC,KAAKC,uBAAA,GAA0B,EAAE;MACjC,KAAKC,qBAAA,GAAwB,EAAE;MAC/B,KAAKC,KAAA,GAAQ;MACb,IAAIC,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAM,KAAKd,UAAA,CAAWK,CAAA,GAAI,CAAC;MAC3C,IAAIU,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAM,KAAKd,UAAA,CAAWM,CAAA,GAAI,CAAC;MAE3C,KAAKU,kBAAA,GAAqB,IAAIC,iBAAA,CAAkBL,IAAA,EAAMG,IAAA,EAAM;QAAEG,IAAA,EAAMC;MAAA,CAAe;MACnF,KAAKH,kBAAA,CAAmBI,OAAA,CAAQC,IAAA,GAAO;MACvC,KAAKL,kBAAA,CAAmBI,OAAA,CAAQE,eAAA,GAAkB;MAElD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKZ,KAAA,EAAOY,CAAA,IAAK;QACnC,MAAMC,qBAAA,GAAwB,IAAIP,iBAAA,CAAkBL,IAAA,EAAMG,IAAA,EAAM;UAAEG,IAAA,EAAMC;QAAA,CAAe;QAEvFK,qBAAA,CAAsBJ,OAAA,CAAQC,IAAA,GAAO,sBAAsBE,CAAA;QAC3DC,qBAAA,CAAsBJ,OAAA,CAAQE,eAAA,GAAkB;QAEhD,KAAKb,uBAAA,CAAwBgB,IAAA,CAAKD,qBAAqB;QAEvD,MAAME,oBAAA,GAAuB,IAAIT,iBAAA,CAAkBL,IAAA,EAAMG,IAAA,EAAM;UAAEG,IAAA,EAAMC;QAAA,CAAe;QAEtFO,oBAAA,CAAqBN,OAAA,CAAQC,IAAA,GAAO,sBAAsBE,CAAA;QAC1DG,oBAAA,CAAqBN,OAAA,CAAQE,eAAA,GAAkB;QAE/C,KAAKZ,qBAAA,CAAsBe,IAAA,CAAKC,oBAAoB;QAEpDd,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC;QAE1BG,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC;MAC3B;MAID,MAAMY,cAAA,GAAiBC,wBAAA;MACvB,KAAKC,gBAAA,GAAmBC,aAAA,CAAcC,KAAA,CAAMJ,cAAA,CAAeK,QAAQ;MAEnE,KAAKH,gBAAA,CAAiB,qBAAqB,EAAEI,KAAA,GAAQ9B,SAAA;MACrD,KAAK0B,gBAAA,CAAiB,aAAa,EAAEI,KAAA,GAAQ;MAE7C,KAAKC,sBAAA,GAAyB,IAAIC,cAAA,CAAe;QAC/CH,QAAA,EAAU,KAAKH,gBAAA;QACfO,YAAA,EAAcT,cAAA,CAAeS,YAAA;QAC7BC,cAAA,EAAgBV,cAAA,CAAeU,cAAA;QAC/BC,OAAA,EAAS,CAAE;MACnB,CAAO;MAGD,KAAKC,sBAAA,GAAyB,EAAE;MAChC,MAAMC,eAAA,GAAkB,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE;MACvC5B,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAM,KAAKd,UAAA,CAAWK,CAAA,GAAI,CAAC;MACvCU,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAM,KAAKd,UAAA,CAAWM,CAAA,GAAI,CAAC;MAEvC,SAASiB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKZ,KAAA,EAAOY,CAAA,IAAK;QACnC,KAAKgB,sBAAA,CAAuBd,IAAA,CAAK,KAAKgB,wBAAA,CAAyBD,eAAA,CAAgBjB,CAAC,CAAC,CAAC;QAElF,KAAKgB,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQ,IAAI7B,OAAA,CAAQQ,IAAA,EAAMG,IAAI;QAEjFH,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC;QAE1BG,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC;MAC3B;MAGD,KAAK2B,iBAAA,GAAoB,KAAKC,oBAAA,CAAqB,KAAKhC,KAAK;MAC7D,KAAK+B,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKvB,qBAAA,CAAsB,CAAC,EAAEU,OAAA;MACtF,KAAKsB,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKvB,qBAAA,CAAsB,CAAC,EAAEU,OAAA;MACtF,KAAKsB,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKvB,qBAAA,CAAsB,CAAC,EAAEU,OAAA;MACtF,KAAKsB,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKvB,qBAAA,CAAsB,CAAC,EAAEU,OAAA;MACtF,KAAKsB,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKvB,qBAAA,CAAsB,CAAC,EAAEU,OAAA;MACtF,KAAKsB,iBAAA,CAAkBV,QAAA,CAAS,eAAe,EAAEC,KAAA,GAAQhC,QAAA;MACzD,KAAKyC,iBAAA,CAAkBV,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQ;MACvD,KAAKS,iBAAA,CAAkBE,WAAA,GAAc;MAErC,MAAMC,YAAA,GAAe,CAAC,GAAK,KAAK,KAAK,KAAK,GAAG;MAC7C,KAAKH,iBAAA,CAAkBV,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQY,YAAA;MACxD,KAAKC,eAAA,GAAkB,CACrB,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC,GACnB,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,GACnB,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,GACnB,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,GACnB,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,EACpB;MACD,KAAKL,iBAAA,CAAkBV,QAAA,CAAS,iBAAiB,EAAEC,KAAA,GAAQ,KAAKa,eAAA;MAIhE,MAAME,UAAA,GAAaC,UAAA;MAEnB,KAAKC,YAAA,GAAepB,aAAA,CAAcC,KAAA,CAAMiB,UAAA,CAAWhB,QAAQ;MAC3D,KAAKkB,YAAA,CAAa,SAAS,EAAEjB,KAAA,GAAQ;MAErC,KAAKkB,YAAA,GAAe,IAAIhB,cAAA,CAAe;QACrCH,QAAA,EAAU,KAAKkB,YAAA;QACfd,YAAA,EAAcY,UAAA,CAAWZ,YAAA;QACzBC,cAAA,EAAgBW,UAAA,CAAWX,cAAA;QAC3Be,QAAA,EAAUC,gBAAA;QACVC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZC,WAAA,EAAa;MACrB,CAAO;MAED,KAAKC,OAAA,GAAU;MACf,KAAKC,SAAA,GAAY;MAEjB,KAAKC,cAAA,GAAiB,IAAInD,KAAA,CAAO;MACjC,KAAKoD,aAAA,GAAgB;MAErB,KAAKC,KAAA,GAAQ,IAAIC,iBAAA,CAAmB;MAEpC,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;IACtC;IAEDC,QAAA,EAAU;MACR,SAAS1C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKd,uBAAA,CAAwByD,MAAA,EAAQ3C,CAAA,IAAK;QAC5D,KAAKd,uBAAA,CAAwBc,CAAC,EAAE0C,OAAA,CAAS;MAC1C;MAED,SAAS1C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKb,qBAAA,CAAsBwD,MAAA,EAAQ3C,CAAA,IAAK;QAC1D,KAAKb,qBAAA,CAAsBa,CAAC,EAAE0C,OAAA,CAAS;MACxC;MAED,KAAKjD,kBAAA,CAAmBiD,OAAA,CAAS;MAIjC,SAAS1C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKgB,sBAAA,CAAuB2B,MAAA,EAAQ3C,CAAA,IAAK;QAC3D,KAAKgB,sBAAA,CAAuBhB,CAAC,EAAE0C,OAAA,CAAS;MACzC;MAED,KAAKvB,iBAAA,CAAkBuB,OAAA,CAAS;MAChC,KAAKd,YAAA,CAAac,OAAA,CAAS;MAC3B,KAAKJ,KAAA,CAAMI,OAAA,CAAS;MAIpB,KAAKF,MAAA,CAAOE,OAAA,CAAS;IACtB;IAEDE,QAAQC,KAAA,EAAOC,MAAA,EAAQ;MACrB,IAAIzD,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMsD,KAAA,GAAQ,CAAC;MAC/B,IAAIrD,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAMuD,MAAA,GAAS,CAAC;MAEhC,KAAKrD,kBAAA,CAAmBmD,OAAA,CAAQvD,IAAA,EAAMG,IAAI;MAE1C,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKZ,KAAA,EAAOY,CAAA,IAAK;QACnC,KAAKd,uBAAA,CAAwBc,CAAC,EAAE4C,OAAA,CAAQvD,IAAA,EAAMG,IAAI;QAClD,KAAKL,qBAAA,CAAsBa,CAAC,EAAE4C,OAAA,CAAQvD,IAAA,EAAMG,IAAI;QAEhD,KAAKwB,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQ,IAAI7B,OAAA,CAAQQ,IAAA,EAAMG,IAAI;QAEjFH,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC;QAC1BG,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC;MAC3B;IACF;IAEDuD,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAYC,SAAA,EAAWC,UAAA,EAAY;MAC/DJ,QAAA,CAASK,aAAA,CAAc,KAAKjB,cAAc;MAC1C,KAAKC,aAAA,GAAgBW,QAAA,CAASM,aAAA,CAAe;MAC7C,MAAMC,YAAA,GAAeP,QAAA,CAASQ,SAAA;MAC9BR,QAAA,CAASQ,SAAA,GAAY;MAErBR,QAAA,CAASS,aAAA,CAAc,KAAKzE,UAAA,EAAY,CAAC;MAEzC,IAAIoE,UAAA,EAAYJ,QAAA,CAASU,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,KAAK;MAI5D,IAAI,KAAKC,cAAA,EAAgB;QACvB,KAAKtB,MAAA,CAAOuB,QAAA,GAAW,KAAKzB,KAAA;QAC5B,KAAKA,KAAA,CAAM0B,GAAA,GAAMd,UAAA,CAAWrD,OAAA;QAE5BmD,QAAA,CAASiB,eAAA,CAAgB,IAAI;QAC7BjB,QAAA,CAASkB,KAAA,CAAO;QAChB,KAAK1B,MAAA,CAAOO,MAAA,CAAOC,QAAQ;MAC5B;MAID,KAAK1C,gBAAA,CAAiB,UAAU,EAAEI,KAAA,GAAQwC,UAAA,CAAWrD,OAAA;MACrD,KAAKS,gBAAA,CAAiB,qBAAqB,EAAEI,KAAA,GAAQ,KAAK9B,SAAA;MAC1D,KAAK4D,MAAA,CAAOuB,QAAA,GAAW,KAAKpD,sBAAA;MAE5BqC,QAAA,CAASiB,eAAA,CAAgB,KAAKxE,kBAAkB;MAChDuD,QAAA,CAASkB,KAAA,CAAO;MAChB,KAAK1B,MAAA,CAAOO,MAAA,CAAOC,QAAQ;MAI3B,IAAImB,iBAAA,GAAoB,KAAK1E,kBAAA;MAE7B,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKZ,KAAA,EAAOY,CAAA,IAAK;QACnC,KAAKwC,MAAA,CAAOuB,QAAA,GAAW,KAAK/C,sBAAA,CAAuBhB,CAAC;QAEpD,KAAKgB,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQyD,iBAAA,CAAkBtE,OAAA;QAClF,KAAKmB,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQpC,gBAAA,CAAgB8F,cAAA;QAC7EpB,QAAA,CAASiB,eAAA,CAAgB,KAAK/E,uBAAA,CAAwBc,CAAC,CAAC;QACxDgD,QAAA,CAASkB,KAAA,CAAO;QAChB,KAAK1B,MAAA,CAAOO,MAAA,CAAOC,QAAQ;QAE3B,KAAKhC,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKxB,uBAAA,CAAwBc,CAAC,EAAEH,OAAA;QAChG,KAAKmB,sBAAA,CAAuBhB,CAAC,EAAES,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQpC,gBAAA,CAAgB+F,cAAA;QAC7ErB,QAAA,CAASiB,eAAA,CAAgB,KAAK9E,qBAAA,CAAsBa,CAAC,CAAC;QACtDgD,QAAA,CAASkB,KAAA,CAAO;QAChB,KAAK1B,MAAA,CAAOO,MAAA,CAAOC,QAAQ;QAE3BmB,iBAAA,GAAoB,KAAKhF,qBAAA,CAAsBa,CAAC;MACjD;MAID,KAAKwC,MAAA,CAAOuB,QAAA,GAAW,KAAK5C,iBAAA;MAC5B,KAAKA,iBAAA,CAAkBV,QAAA,CAAS,eAAe,EAAEC,KAAA,GAAQ,KAAKhC,QAAA;MAC9D,KAAKyC,iBAAA,CAAkBV,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQ,KAAK/B,MAAA;MAC5D,KAAKwC,iBAAA,CAAkBV,QAAA,CAAS,iBAAiB,EAAEC,KAAA,GAAQ,KAAKa,eAAA;MAEhEyB,QAAA,CAASiB,eAAA,CAAgB,KAAK/E,uBAAA,CAAwB,CAAC,CAAC;MACxD8D,QAAA,CAASkB,KAAA,CAAO;MAChB,KAAK1B,MAAA,CAAOO,MAAA,CAAOC,QAAQ;MAI3B,KAAKR,MAAA,CAAOuB,QAAA,GAAW,KAAKnC,YAAA;MAC5B,KAAKD,YAAA,CAAa,UAAU,EAAEjB,KAAA,GAAQ,KAAKxB,uBAAA,CAAwB,CAAC,EAAEW,OAAA;MAEtE,IAAIuD,UAAA,EAAYJ,QAAA,CAASU,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,IAAI;MAE3D,IAAI,KAAKC,cAAA,EAAgB;QACvBd,QAAA,CAASiB,eAAA,CAAgB,IAAI;QAC7B,KAAKzB,MAAA,CAAOO,MAAA,CAAOC,QAAQ;MACnC,OAAa;QACLA,QAAA,CAASiB,eAAA,CAAgBf,UAAU;QACnC,KAAKV,MAAA,CAAOO,MAAA,CAAOC,QAAQ;MAC5B;MAIDA,QAAA,CAASS,aAAA,CAAc,KAAKrB,cAAA,EAAgB,KAAKC,aAAa;MAC9DW,QAAA,CAASQ,SAAA,GAAYD,YAAA;IACtB;IAEDrC,yBAAyBoD,YAAA,EAAc;MACrC,OAAO,IAAI1D,cAAA,CAAe;QACxBG,OAAA,EAAS;UACPwD,aAAA,EAAeD,YAAA;UACfE,KAAA,EAAOF;QACR;QAED7D,QAAA,EAAU;UACRgE,YAAA,EAAc;YAAE/D,KAAA,EAAO;UAAM;UAC7BgE,OAAA,EAAS;YAAEhE,KAAA,EAAO,IAAI7B,OAAA,CAAQ,KAAK,GAAG;UAAG;UACzC8F,SAAA,EAAW;YAAEjE,KAAA,EAAO,IAAI7B,OAAA,CAAQ,KAAK,GAAG;UAAG;QAC5C;QAEDgC,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;QAMdC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAyBxB,CAAO;IACF;IAEDM,qBAAqBhC,KAAA,EAAO;MAC1B,OAAO,IAAIwB,cAAA,CAAe;QACxBG,OAAA,EAAS;UACP6D,QAAA,EAAUxF;QACX;QAEDqB,QAAA,EAAU;UACRoE,YAAA,EAAc;YAAEnE,KAAA,EAAO;UAAM;UAC7BoE,YAAA,EAAc;YAAEpE,KAAA,EAAO;UAAM;UAC7BqE,YAAA,EAAc;YAAErE,KAAA,EAAO;UAAM;UAC7BsE,YAAA,EAAc;YAAEtE,KAAA,EAAO;UAAM;UAC7BuE,YAAA,EAAc;YAAEvE,KAAA,EAAO;UAAM;UAC7BwE,aAAA,EAAe;YAAExE,KAAA,EAAO;UAAK;UAC7BY,YAAA,EAAc;YAAEZ,KAAA,EAAO;UAAM;UAC7Ba,eAAA,EAAiB;YAAEb,KAAA,EAAO;UAAM;UAChCyE,WAAA,EAAa;YAAEzE,KAAA,EAAO;UAAK;QAC5B;QAEDG,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;QAMdC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAuBxB,CAAO;IACF;EACF;EA/VD,IAAMsE,gBAAA,GAAN9G,gBAAA;EACE+G,aAAA,CADID,gBAAA,EACG,kBAAiB,IAAIvG,OAAA,CAAQ,GAAK,CAAG;EAC5CwG,aAAA,CAFID,gBAAA,EAEG,kBAAiB,IAAIvG,OAAA,CAAQ,GAAK,CAAG;EA+V9C,OAAOuG,gBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}