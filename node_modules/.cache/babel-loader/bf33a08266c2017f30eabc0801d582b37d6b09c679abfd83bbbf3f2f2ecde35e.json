{"ast": null, "code": "import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectClosestTri_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = ['x', 'y', 'z'];\nfunction raycastFirst_indirect(bvh, root, side, ray, near, far) {\n  BufferStack.setBuffer(bvh._roots[root]);\n  const result = _raycastFirst(0, bvh, side, ray, near, far);\n  BufferStack.clearBuffer();\n  return result;\n}\nfunction _raycastFirst(nodeIndex32, bvh, side, ray, near, far) {\n  const {\n    float32Array,\n    uint16Array,\n    uint32Array\n  } = BufferStack;\n  let nodeIndex16 = nodeIndex32 * 2;\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n    return intersectClosestTri_indirect(bvh, side, ray, offset, count, near, far);\n  } else {\n    // consider the position of the split plane with respect to the oncoming ray; whichever direction\n    // the ray is coming from, look for an intersection among that side of the tree first\n    const splitAxis = SPLIT_AXIS(nodeIndex32, uint32Array);\n    const xyzAxis = _xyzFields[splitAxis];\n    const rayDir = ray.direction[xyzAxis];\n    const leftToRight = rayDir >= 0;\n\n    // c1 is the child to check first\n    let c1, c2;\n    if (leftToRight) {\n      c1 = LEFT_NODE(nodeIndex32);\n      c2 = RIGHT_NODE(nodeIndex32, uint32Array);\n    } else {\n      c1 = RIGHT_NODE(nodeIndex32, uint32Array);\n      c2 = LEFT_NODE(nodeIndex32);\n    }\n    const c1Intersection = intersectRay(c1, float32Array, ray, near, far);\n    const c1Result = c1Intersection ? _raycastFirst(c1, bvh, side, ray, near, far) : null;\n\n    // if we got an intersection in the first node and it's closer than the second node's bounding\n    // box, we don't need to consider the second node because it couldn't possibly be a better result\n    if (c1Result) {\n      // check if the point is within the second bounds\n      // \"point\" is in the local frame of the bvh\n      const point = c1Result.point[xyzAxis];\n      const isOutside = leftToRight ? point <= float32Array[c2 + splitAxis] :\n      // min bounding data\n      point >= float32Array[c2 + splitAxis + 3]; // max bounding data\n\n      if (isOutside) {\n        return c1Result;\n      }\n    }\n\n    // either there was no intersection in the first node, or there could still be a closer\n    // intersection in the second, so check the second node and then take the better of the two\n    const c2Intersection = intersectRay(c2, float32Array, ray, near, far);\n    const c2Result = c2Intersection ? _raycastFirst(c2, bvh, side, ray, near, far) : null;\n    if (c1Result && c2Result) {\n      return c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n    } else {\n      return c1Result || c2Result || null;\n    }\n  }\n}\nexport { raycastFirst_indirect };", "map": {"version": 3, "names": ["IS_LEAF", "OFFSET", "COUNT", "SPLIT_AXIS", "LEFT_NODE", "RIGHT_NODE", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>", "intersectRay", "intersectClosestTri_indirect", "_xyzFields", "raycastFirst_indirect", "bvh", "root", "side", "ray", "near", "far", "<PERSON><PERSON><PERSON><PERSON>", "_roots", "result", "_raycastFirst", "<PERSON><PERSON><PERSON><PERSON>", "nodeIndex32", "float32Array", "uint16Array", "uint32Array", "nodeIndex16", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "splitAxis", "xyzAxis", "rayDir", "direction", "leftToRight", "c1", "c2", "c1Intersection", "c1Result", "point", "isOutside", "c2Intersection", "c2Result", "distance"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/three-mesh-bvh/src/core/cast/raycastFirst_indirect.generated.js"], "sourcesContent": ["import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectClosestTri_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = [ 'x', 'y', 'z' ];\n\nfunction raycastFirst_indirect( bvh, root, side, ray, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _raycastFirst( 0, bvh, side, ray, near, far );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _raycastFirst( nodeIndex32, bvh, side, ray, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\treturn intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far );\n\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = _xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, near, far );\n\t\tconst c1Result = c1Intersection ? _raycastFirst( c1, bvh, side, ray, near, far ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, near, far );\n\t\tconst c2Result = c2Intersection ? _raycastFirst( c2, bvh, side, ray, near, far ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycastFirst_indirect };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,UAAU,QAAQ,6BAA6B;AACvG,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAO,sCAAsC;AAC7C,SAASC,4BAA4B,QAAQ,+CAA+C;;AAE5F;AACA;AACA;;AAEA,MAAMC,UAAU,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;AAEpC,SAASC,qBAAqBA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEjEV,WAAW,CAACW,SAAS,CAAEN,GAAG,CAACO,MAAM,CAAEN,IAAI,CAAG,CAAC;EAC3C,MAAMO,MAAM,GAAGC,aAAa,CAAE,CAAC,EAAET,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAC5DV,WAAW,CAACe,WAAW,CAAC,CAAC;EAEzB,OAAOF,MAAM;AAEd;AAEA,SAASC,aAAaA,CAAEE,WAAW,EAAEX,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEhE,MAAM;IAAEO,YAAY;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGnB,WAAW;EAC9D,IAAIoB,WAAW,GAAGJ,WAAW,GAAG,CAAC;EAEjC,MAAMK,MAAM,GAAG3B,OAAO,CAAE0B,WAAW,EAAEF,WAAY,CAAC;EAClD,IAAKG,MAAM,EAAG;IAEb,MAAMC,MAAM,GAAG3B,MAAM,CAAEqB,WAAW,EAAEG,WAAY,CAAC;IACjD,MAAMI,KAAK,GAAG3B,KAAK,CAAEwB,WAAW,EAAEF,WAAY,CAAC;IAE/C,OAAOhB,4BAA4B,CAAEG,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEc,MAAM,EAAEC,KAAK,EAAEd,IAAI,EAAEC,GAAI,CAAC;EAGhF,CAAC,MAAM;IAEN;IACA;IACA,MAAMc,SAAS,GAAG3B,UAAU,CAAEmB,WAAW,EAAEG,WAAY,CAAC;IACxD,MAAMM,OAAO,GAAGtB,UAAU,CAAEqB,SAAS,CAAE;IACvC,MAAME,MAAM,GAAGlB,GAAG,CAACmB,SAAS,CAAEF,OAAO,CAAE;IACvC,MAAMG,WAAW,GAAGF,MAAM,IAAI,CAAC;;IAE/B;IACA,IAAIG,EAAE,EAAEC,EAAE;IACV,IAAKF,WAAW,EAAG;MAElBC,EAAE,GAAG/B,SAAS,CAAEkB,WAAY,CAAC;MAC7Bc,EAAE,GAAG/B,UAAU,CAAEiB,WAAW,EAAEG,WAAY,CAAC;IAE5C,CAAC,MAAM;MAENU,EAAE,GAAG9B,UAAU,CAAEiB,WAAW,EAAEG,WAAY,CAAC;MAC3CW,EAAE,GAAGhC,SAAS,CAAEkB,WAAY,CAAC;IAE9B;IAEA,MAAMe,cAAc,GAAG9B,YAAY,CAAE4B,EAAE,EAAEZ,YAAY,EAAET,GAAG,EAAEC,IAAI,EAAEC,GAAI,CAAC;IACvE,MAAMsB,QAAQ,GAAGD,cAAc,GAAGjB,aAAa,CAAEe,EAAE,EAAExB,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAI,CAAC,GAAG,IAAI;;IAEvF;IACA;IACA,IAAKsB,QAAQ,EAAG;MAEf;MACA;MACA,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAAER,OAAO,CAAE;MACvC,MAAMS,SAAS,GAAGN,WAAW,GAC5BK,KAAK,IAAIhB,YAAY,CAAEa,EAAE,GAAGN,SAAS,CAAE;MAAG;MAC1CS,KAAK,IAAIhB,YAAY,CAAEa,EAAE,GAAGN,SAAS,GAAG,CAAC,CAAE,CAAC,CAAC;;MAE9C,IAAKU,SAAS,EAAG;QAEhB,OAAOF,QAAQ;MAEhB;IAED;;IAEA;IACA;IACA,MAAMG,cAAc,GAAGlC,YAAY,CAAE6B,EAAE,EAAEb,YAAY,EAAET,GAAG,EAAEC,IAAI,EAAEC,GAAI,CAAC;IACvE,MAAM0B,QAAQ,GAAGD,cAAc,GAAGrB,aAAa,CAAEgB,EAAE,EAAEzB,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAI,CAAC,GAAG,IAAI;IAEvF,IAAKsB,QAAQ,IAAII,QAAQ,EAAG;MAE3B,OAAOJ,QAAQ,CAACK,QAAQ,IAAID,QAAQ,CAACC,QAAQ,GAAGL,QAAQ,GAAGI,QAAQ;IAEpE,CAAC,MAAM;MAEN,OAAOJ,QAAQ,IAAII,QAAQ,IAAI,IAAI;IAEpC;EAED;AAED;AAEA,SAAShC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}