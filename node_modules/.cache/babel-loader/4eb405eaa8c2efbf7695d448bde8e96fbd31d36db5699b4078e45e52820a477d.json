{"ast": null, "code": "import { CompressedTexture, ClampToEdgeWrapping } from \"three\";\nclass CompressedArrayTexture extends CompressedTexture {\n  constructor(mipmaps, width, height, depth, format, type) {\n    super(mipmaps, width, height, format, type);\n    this.isCompressedArrayTexture = true;\n    this.image.depth = depth;\n    this.wrapR = ClampToEdgeWrapping;\n  }\n}\nexport { CompressedArrayTexture };", "map": {"version": 3, "names": ["CompressedArrayTexture", "CompressedTexture", "constructor", "mipmaps", "width", "height", "depth", "format", "type", "isCompressedArrayTexture", "image", "wrapR", "ClampToEdgeWrapping"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/_polyfill/CompressedArrayTexture.js"], "sourcesContent": ["import { CompressedTexture, ClampToEdgeWrapping } from 'three'\n\nclass CompressedArrayTexture extends CompressedTexture {\n  constructor(mipmaps, width, height, depth, format, type) {\n    super(mipmaps, width, height, format, type)\n    this.isCompressedArrayTexture = true\n    this.image.depth = depth\n    this.wrapR = ClampToEdgeWrapping\n  }\n}\n\nexport { CompressedArrayTexture }\n"], "mappings": ";AAEA,MAAMA,sBAAA,SAA+BC,iBAAA,CAAkB;EACrDC,YAAYC,OAAA,EAASC,KAAA,EAAOC,MAAA,EAAQC,KAAA,EAAOC,MAAA,EAAQC,IAAA,EAAM;IACvD,MAAML,OAAA,EAASC,KAAA,EAAOC,MAAA,EAAQE,MAAA,EAAQC,IAAI;IAC1C,KAAKC,wBAAA,GAA2B;IAChC,KAAKC,KAAA,CAAMJ,KAAA,GAAQA,KAAA;IACnB,KAAKK,KAAA,GAAQC,mBAAA;EACd;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}