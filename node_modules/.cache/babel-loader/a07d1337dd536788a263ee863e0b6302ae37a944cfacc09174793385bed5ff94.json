{"ast": null, "code": "import { DataTextureLoader, HalfFloatType, CubeTexture, RGBAFormat, LinearFilter, DataUtils } from \"three\";\nlet UPNG;\nfunction init() {\n  if (UPNG) return UPNG;\n  UPNG = {};\n  UPNG.toRGBA8 = function (out) {\n    var w = out.width,\n      h = out.height;\n    if (out.tabs.acTL == null) return [UPNG.toRGBA8.decodeImage(out.data, w, h, out).buffer];\n    var frms = [];\n    if (out.frames[0].data == null) out.frames[0].data = out.data;\n    var len = w * h * 4,\n      img = new Uint8Array(len),\n      empty = new Uint8Array(len),\n      prev = new Uint8Array(len);\n    for (var i = 0; i < out.frames.length; i++) {\n      var frm = out.frames[i];\n      var fx = frm.rect.x,\n        fy = frm.rect.y,\n        fw = frm.rect.width,\n        fh = frm.rect.height;\n      var fdata = UPNG.toRGBA8.decodeImage(frm.data, fw, fh, out);\n      if (i != 0) for (var j = 0; j < len; j++) prev[j] = img[j];\n      if (frm.blend == 0) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 0);else if (frm.blend == 1) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 1);\n      frms.push(img.buffer.slice(0));\n      if (frm.dispose == 1) UPNG._copyTile(empty, fw, fh, img, w, h, fx, fy, 0);else if (frm.dispose == 2) for (var j = 0; j < len; j++) img[j] = prev[j];\n    }\n    return frms;\n  };\n  UPNG.toRGBA8.decodeImage = function (data, w, h, out) {\n    var area = w * h,\n      bpp = UPNG.decode._getBPP(out);\n    var bpl = Math.ceil(w * bpp / 8);\n    var bf = new Uint8Array(area * 4),\n      bf32 = new Uint32Array(bf.buffer);\n    var ctype = out.ctype,\n      depth = out.depth;\n    var rs = UPNG._bin.readUshort;\n    if (ctype == 6) {\n      var qarea = area << 2;\n      if (depth == 8) {\n        for (var i = 0; i < qarea; i += 4) {\n          bf[i] = data[i];\n          bf[i + 1] = data[i + 1];\n          bf[i + 2] = data[i + 2];\n          bf[i + 3] = data[i + 3];\n        }\n      }\n      if (depth == 16) {\n        for (var i = 0; i < qarea; i++) {\n          bf[i] = data[i << 1];\n        }\n      }\n    } else if (ctype == 2) {\n      var ts = out.tabs[\"tRNS\"];\n      if (ts == null) {\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 3;\n            bf32[i] = 255 << 24 | data[ti + 2] << 16 | data[ti + 1] << 8 | data[ti];\n          }\n        }\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 6;\n            bf32[i] = 255 << 24 | data[ti + 4] << 16 | data[ti + 2] << 8 | data[ti];\n          }\n        }\n      } else {\n        var tr = ts[0],\n          tg = ts[1],\n          tb = ts[2];\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 3;\n            bf32[i] = 255 << 24 | data[ti + 2] << 16 | data[ti + 1] << 8 | data[ti];\n            if (data[ti] == tr && data[ti + 1] == tg && data[ti + 2] == tb) bf[qi + 3] = 0;\n          }\n        }\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 6;\n            bf32[i] = 255 << 24 | data[ti + 4] << 16 | data[ti + 2] << 8 | data[ti];\n            if (rs(data, ti) == tr && rs(data, ti + 2) == tg && rs(data, ti + 4) == tb) bf[qi + 3] = 0;\n          }\n        }\n      }\n    } else if (ctype == 3) {\n      var p = out.tabs[\"PLTE\"],\n        ap = out.tabs[\"tRNS\"],\n        tl = ap ? ap.length : 0;\n      if (depth == 1) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = t0 + i << 2,\n              j = data[s0 + (i >> 3)] >> 7 - ((i & 7) << 0) & 1,\n              cj = 3 * j;\n            bf[qi] = p[cj];\n            bf[qi + 1] = p[cj + 1];\n            bf[qi + 2] = p[cj + 2];\n            bf[qi + 3] = j < tl ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 2) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = t0 + i << 2,\n              j = data[s0 + (i >> 2)] >> 6 - ((i & 3) << 1) & 3,\n              cj = 3 * j;\n            bf[qi] = p[cj];\n            bf[qi + 1] = p[cj + 1];\n            bf[qi + 2] = p[cj + 2];\n            bf[qi + 3] = j < tl ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 4) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w;\n          for (var i = 0; i < w; i++) {\n            var qi = t0 + i << 2,\n              j = data[s0 + (i >> 1)] >> 4 - ((i & 1) << 2) & 15,\n              cj = 3 * j;\n            bf[qi] = p[cj];\n            bf[qi + 1] = p[cj + 1];\n            bf[qi + 2] = p[cj + 2];\n            bf[qi + 3] = j < tl ? ap[j] : 255;\n          }\n        }\n      }\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            j = data[i],\n            cj = 3 * j;\n          bf[qi] = p[cj];\n          bf[qi + 1] = p[cj + 1];\n          bf[qi + 2] = p[cj + 2];\n          bf[qi + 3] = j < tl ? ap[j] : 255;\n        }\n      }\n    } else if (ctype == 4) {\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 1,\n            gr = data[di];\n          bf[qi] = gr;\n          bf[qi + 1] = gr;\n          bf[qi + 2] = gr;\n          bf[qi + 3] = data[di + 1];\n        }\n      }\n      if (depth == 16) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 2,\n            gr = data[di];\n          bf[qi] = gr;\n          bf[qi + 1] = gr;\n          bf[qi + 2] = gr;\n          bf[qi + 3] = data[di + 2];\n        }\n      }\n    } else if (ctype == 0) {\n      var tr = out.tabs[\"tRNS\"] ? out.tabs[\"tRNS\"] : -1;\n      for (var y = 0; y < h; y++) {\n        var off = y * bpl,\n          to = y * w;\n        if (depth == 1) {\n          for (var x = 0; x < w; x++) {\n            var gr = 255 * (data[off + (x >>> 3)] >>> 7 - (x & 7) & 1),\n              al = gr == tr * 255 ? 0 : 255;\n            bf32[to + x] = al << 24 | gr << 16 | gr << 8 | gr;\n          }\n        } else if (depth == 2) {\n          for (var x = 0; x < w; x++) {\n            var gr = 85 * (data[off + (x >>> 2)] >>> 6 - ((x & 3) << 1) & 3),\n              al = gr == tr * 85 ? 0 : 255;\n            bf32[to + x] = al << 24 | gr << 16 | gr << 8 | gr;\n          }\n        } else if (depth == 4) {\n          for (var x = 0; x < w; x++) {\n            var gr = 17 * (data[off + (x >>> 1)] >>> 4 - ((x & 1) << 2) & 15),\n              al = gr == tr * 17 ? 0 : 255;\n            bf32[to + x] = al << 24 | gr << 16 | gr << 8 | gr;\n          }\n        } else if (depth == 8) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + x],\n              al = gr == tr ? 0 : 255;\n            bf32[to + x] = al << 24 | gr << 16 | gr << 8 | gr;\n          }\n        } else if (depth == 16) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + (x << 1)],\n              al = rs(data, off + (x << 1)) == tr ? 0 : 255;\n            bf32[to + x] = al << 24 | gr << 16 | gr << 8 | gr;\n          }\n        }\n      }\n    }\n    return bf;\n  };\n  UPNG.decode = function (buff) {\n    var data = new Uint8Array(buff),\n      offset = 8,\n      bin = UPNG._bin,\n      rUs = bin.readUshort,\n      rUi = bin.readUint;\n    var out = {\n      tabs: {},\n      frames: []\n    };\n    var dd = new Uint8Array(data.length),\n      doff = 0;\n    var fd,\n      foff = 0;\n    var text, keyw, bfr;\n    var mgck = [137, 80, 78, 71, 13, 10, 26, 10];\n    for (var i = 0; i < 8; i++) if (data[i] != mgck[i]) throw new Error(\"The input is not a PNG file!\");\n    while (offset < data.length) {\n      var len = bin.readUint(data, offset);\n      offset += 4;\n      var type = bin.readASCII(data, offset, 4);\n      offset += 4;\n      if (type == \"IHDR\") {\n        UPNG.decode._IHDR(data, offset, out);\n      } else if (type == \"CgBI\") {\n        out.tabs[type] = data.slice(offset, offset + 4);\n      } else if (type == \"IDAT\") {\n        for (var i = 0; i < len; i++) dd[doff + i] = data[offset + i];\n        doff += len;\n      } else if (type == \"acTL\") {\n        out.tabs[type] = {\n          num_frames: rUi(data, offset),\n          num_plays: rUi(data, offset + 4)\n        };\n        fd = new Uint8Array(data.length);\n      } else if (type == \"fcTL\") {\n        if (foff != 0) {\n          var fr = out.frames[out.frames.length - 1];\n          fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height);\n          foff = 0;\n        }\n        var rct = {\n          x: rUi(data, offset + 12),\n          y: rUi(data, offset + 16),\n          width: rUi(data, offset + 4),\n          height: rUi(data, offset + 8)\n        };\n        var del = rUs(data, offset + 22);\n        del = rUs(data, offset + 20) / (del == 0 ? 100 : del);\n        var frm = {\n          rect: rct,\n          delay: Math.round(del * 1e3),\n          dispose: data[offset + 24],\n          blend: data[offset + 25]\n        };\n        out.frames.push(frm);\n      } else if (type == \"fdAT\") {\n        for (var i = 0; i < len - 4; i++) fd[foff + i] = data[offset + i + 4];\n        foff += len - 4;\n      } else if (type == \"pHYs\") {\n        out.tabs[type] = [bin.readUint(data, offset), bin.readUint(data, offset + 4), data[offset + 8]];\n      } else if (type == \"cHRM\") {\n        out.tabs[type] = [];\n        for (var i = 0; i < 8; i++) out.tabs[type].push(bin.readUint(data, offset + i * 4));\n      } else if (type == \"tEXt\" || type == \"zTXt\") {\n        if (out.tabs[type] == null) out.tabs[type] = {};\n        var nz = bin.nextZero(data, offset);\n        keyw = bin.readASCII(data, offset, nz - offset);\n        var tl = offset + len - nz - 1;\n        if (type == \"tEXt\") {\n          text = bin.readASCII(data, nz + 1, tl);\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(nz + 2, nz + 2 + tl));\n          text = bin.readUTF8(bfr, 0, bfr.length);\n        }\n        out.tabs[type][keyw] = text;\n      } else if (type == \"iTXt\") {\n        if (out.tabs[type] == null) out.tabs[type] = {};\n        var nz = 0,\n          off = offset;\n        nz = bin.nextZero(data, off);\n        keyw = bin.readASCII(data, off, nz - off);\n        off = nz + 1;\n        var cflag = data[off];\n        off += 2;\n        nz = bin.nextZero(data, off);\n        bin.readASCII(data, off, nz - off);\n        off = nz + 1;\n        nz = bin.nextZero(data, off);\n        bin.readUTF8(data, off, nz - off);\n        off = nz + 1;\n        var tl = len - (off - offset);\n        if (cflag == 0) {\n          text = bin.readUTF8(data, off, tl);\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(off, off + tl));\n          text = bin.readUTF8(bfr, 0, bfr.length);\n        }\n        out.tabs[type][keyw] = text;\n      } else if (type == \"PLTE\") {\n        out.tabs[type] = bin.readBytes(data, offset, len);\n      } else if (type == \"hIST\") {\n        var pl = out.tabs[\"PLTE\"].length / 3;\n        out.tabs[type] = [];\n        for (var i = 0; i < pl; i++) out.tabs[type].push(rUs(data, offset + i * 2));\n      } else if (type == \"tRNS\") {\n        if (out.ctype == 3) out.tabs[type] = bin.readBytes(data, offset, len);else if (out.ctype == 0) out.tabs[type] = rUs(data, offset);else if (out.ctype == 2) out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)];\n      } else if (type == \"gAMA\") {\n        out.tabs[type] = bin.readUint(data, offset) / 1e5;\n      } else if (type == \"sRGB\") {\n        out.tabs[type] = data[offset];\n      } else if (type == \"bKGD\") {\n        if (out.ctype == 0 || out.ctype == 4) {\n          out.tabs[type] = [rUs(data, offset)];\n        } else if (out.ctype == 2 || out.ctype == 6) {\n          out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)];\n        } else if (out.ctype == 3) {\n          out.tabs[type] = data[offset];\n        }\n      } else if (type == \"IEND\") {\n        break;\n      }\n      offset += len;\n      bin.readUint(data, offset);\n      offset += 4;\n    }\n    if (foff != 0) {\n      var fr = out.frames[out.frames.length - 1];\n      fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height);\n    }\n    out.data = UPNG.decode._decompress(out, dd, out.width, out.height);\n    delete out.compress;\n    delete out.interlace;\n    delete out.filter;\n    return out;\n  };\n  UPNG.decode._decompress = function (out, dd, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil(w * bpp / 8),\n      buff = new Uint8Array((bpl + 1 + out.interlace) * h);\n    if (out.tabs[\"CgBI\"]) dd = UPNG.inflateRaw(dd, buff);else dd = UPNG.decode._inflate(dd, buff);\n    if (out.interlace == 0) dd = UPNG.decode._filterZero(dd, out, 0, w, h);else if (out.interlace == 1) dd = UPNG.decode._readInterlace(dd, out);\n    return dd;\n  };\n  UPNG.decode._inflate = function (data, buff) {\n    var out = UPNG[\"inflateRaw\"](new Uint8Array(data.buffer, 2, data.length - 6), buff);\n    return out;\n  };\n  UPNG.inflateRaw = function () {\n    var H = {};\n    H.H = {};\n    H.H.N = function (N, W) {\n      var R = Uint8Array,\n        i = 0,\n        m = 0,\n        J = 0,\n        h = 0,\n        Q = 0,\n        X = 0,\n        u = 0,\n        w = 0,\n        d = 0,\n        v,\n        C;\n      if (N[0] == 3 && N[1] == 0) return W ? W : new R(0);\n      var V = H.H,\n        n = V.b,\n        A = V.e,\n        l = V.R,\n        M = V.n,\n        I = V.A,\n        e = V.Z,\n        b = V.m,\n        Z = W == null;\n      if (Z) W = new R(N.length >>> 2 << 5);\n      while (i == 0) {\n        i = n(N, d, 1);\n        m = n(N, d + 1, 2);\n        d += 3;\n        if (m == 0) {\n          if ((d & 7) != 0) d += 8 - (d & 7);\n          var D = (d >>> 3) + 4,\n            q = N[D - 4] | N[D - 3] << 8;\n          if (Z) W = H.H.W(W, w + q);\n          W.set(new R(N.buffer, N.byteOffset + D, q), w);\n          d = D + q << 3;\n          w += q;\n          continue;\n        }\n        if (Z) W = H.H.W(W, w + (1 << 17));\n        if (m == 1) {\n          v = b.J;\n          C = b.h;\n          X = (1 << 9) - 1;\n          u = (1 << 5) - 1;\n        }\n        if (m == 2) {\n          J = A(N, d, 5) + 257;\n          h = A(N, d + 5, 5) + 1;\n          Q = A(N, d + 10, 4) + 4;\n          d += 14;\n          var j = 1;\n          for (var c = 0; c < 38; c += 2) {\n            b.Q[c] = 0;\n            b.Q[c + 1] = 0;\n          }\n          for (var c = 0; c < Q; c++) {\n            var K = A(N, d + c * 3, 3);\n            b.Q[(b.X[c] << 1) + 1] = K;\n            if (K > j) j = K;\n          }\n          d += 3 * Q;\n          M(b.Q, j);\n          I(b.Q, j, b.u);\n          v = b.w;\n          C = b.d;\n          d = l(b.u, (1 << j) - 1, J + h, N, d, b.v);\n          var r = V.V(b.v, 0, J, b.C);\n          X = (1 << r) - 1;\n          var S = V.V(b.v, J, h, b.D);\n          u = (1 << S) - 1;\n          M(b.C, r);\n          I(b.C, r, v);\n          M(b.D, S);\n          I(b.D, S, C);\n        }\n        while (true) {\n          var T = v[e(N, d) & X];\n          d += T & 15;\n          var p = T >>> 4;\n          if (p >>> 8 == 0) {\n            W[w++] = p;\n          } else if (p == 256) {\n            break;\n          } else {\n            var z = w + p - 254;\n            if (p > 264) {\n              var _ = b.q[p - 257];\n              z = w + (_ >>> 3) + A(N, d, _ & 7);\n              d += _ & 7;\n            }\n            var $ = C[e(N, d) & u];\n            d += $ & 15;\n            var s = $ >>> 4,\n              Y = b.c[s],\n              a = (Y >>> 4) + n(N, d, Y & 15);\n            d += Y & 15;\n            while (w < z) {\n              W[w] = W[w++ - a];\n              W[w] = W[w++ - a];\n              W[w] = W[w++ - a];\n              W[w] = W[w++ - a];\n            }\n            w = z;\n          }\n        }\n      }\n      return W.length == w ? W : W.slice(0, w);\n    };\n    H.H.W = function (N, W) {\n      var R = N.length;\n      if (W <= R) return N;\n      var V = new Uint8Array(R << 1);\n      V.set(N, 0);\n      return V;\n    };\n    H.H.R = function (N, W, R, V, n, A) {\n      var l = H.H.e,\n        M = H.H.Z,\n        I = 0;\n      while (I < R) {\n        var e = N[M(V, n) & W];\n        n += e & 15;\n        var b = e >>> 4;\n        if (b <= 15) {\n          A[I] = b;\n          I++;\n        } else {\n          var Z = 0,\n            m = 0;\n          if (b == 16) {\n            m = 3 + l(V, n, 2);\n            n += 2;\n            Z = A[I - 1];\n          } else if (b == 17) {\n            m = 3 + l(V, n, 3);\n            n += 3;\n          } else if (b == 18) {\n            m = 11 + l(V, n, 7);\n            n += 7;\n          }\n          var J = I + m;\n          while (I < J) {\n            A[I] = Z;\n            I++;\n          }\n        }\n      }\n      return n;\n    };\n    H.H.V = function (N, W, R, V) {\n      var n = 0,\n        A = 0,\n        l = V.length >>> 1;\n      while (A < R) {\n        var M = N[A + W];\n        V[A << 1] = 0;\n        V[(A << 1) + 1] = M;\n        if (M > n) n = M;\n        A++;\n      }\n      while (A < l) {\n        V[A << 1] = 0;\n        V[(A << 1) + 1] = 0;\n        A++;\n      }\n      return n;\n    };\n    H.H.n = function (N, W) {\n      var R = H.H.m,\n        V = N.length,\n        n,\n        A,\n        l,\n        M,\n        I,\n        e = R.j;\n      for (var M = 0; M <= W; M++) e[M] = 0;\n      for (M = 1; M < V; M += 2) e[N[M]]++;\n      var b = R.K;\n      n = 0;\n      e[0] = 0;\n      for (A = 1; A <= W; A++) {\n        n = n + e[A - 1] << 1;\n        b[A] = n;\n      }\n      for (l = 0; l < V; l += 2) {\n        I = N[l + 1];\n        if (I != 0) {\n          N[l] = b[I];\n          b[I]++;\n        }\n      }\n    };\n    H.H.A = function (N, W, R) {\n      var V = N.length,\n        n = H.H.m,\n        A = n.r;\n      for (var l = 0; l < V; l += 2) {\n        if (N[l + 1] != 0) {\n          var M = l >> 1,\n            I = N[l + 1],\n            e = M << 4 | I,\n            b = W - I,\n            Z = N[l] << b,\n            m = Z + (1 << b);\n          while (Z != m) {\n            var J = A[Z] >>> 15 - W;\n            R[J] = e;\n            Z++;\n          }\n        }\n      }\n    };\n    H.H.l = function (N, W) {\n      var R = H.H.m.r,\n        V = 15 - W;\n      for (var n = 0; n < N.length; n += 2) {\n        var A = N[n] << W - N[n + 1];\n        N[n] = R[A] >>> V;\n      }\n    };\n    H.H.M = function (N, W, R) {\n      R = R << (W & 7);\n      var V = W >>> 3;\n      N[V] |= R;\n      N[V + 1] |= R >>> 8;\n    };\n    H.H.I = function (N, W, R) {\n      R = R << (W & 7);\n      var V = W >>> 3;\n      N[V] |= R;\n      N[V + 1] |= R >>> 8;\n      N[V + 2] |= R >>> 16;\n    };\n    H.H.e = function (N, W, R) {\n      return (N[W >>> 3] | N[(W >>> 3) + 1] << 8) >>> (W & 7) & (1 << R) - 1;\n    };\n    H.H.b = function (N, W, R) {\n      return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16) >>> (W & 7) & (1 << R) - 1;\n    };\n    H.H.Z = function (N, W) {\n      return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16) >>> (W & 7);\n    };\n    H.H.i = function (N, W) {\n      return (N[W >>> 3] | N[(W >>> 3) + 1] << 8 | N[(W >>> 3) + 2] << 16 | N[(W >>> 3) + 3] << 24) >>> (W & 7);\n    };\n    H.H.m = function () {\n      var N = Uint16Array,\n        W = Uint32Array;\n      return {\n        K: new N(16),\n        j: new N(16),\n        X: [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15],\n        S: [3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 999, 999, 999],\n        T: [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0],\n        q: new N(32),\n        p: [1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 65535, 65535],\n        z: [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0],\n        c: new W(32),\n        J: new N(512),\n        _: [],\n        h: new N(32),\n        $: [],\n        w: new N(32768),\n        C: [],\n        v: [],\n        d: new N(32768),\n        D: [],\n        u: new N(512),\n        Q: [],\n        r: new N(1 << 15),\n        s: new W(286),\n        Y: new W(30),\n        a: new W(19),\n        t: new W(15e3),\n        k: new N(1 << 16),\n        g: new N(1 << 15)\n      };\n    }();\n    (function () {\n      var N = H.H.m,\n        W = 1 << 15;\n      for (var R = 0; R < W; R++) {\n        var V = R;\n        V = (V & 2863311530) >>> 1 | (V & 1431655765) << 1;\n        V = (V & 3435973836) >>> 2 | (V & 858993459) << 2;\n        V = (V & 4042322160) >>> 4 | (V & 252645135) << 4;\n        V = (V & 4278255360) >>> 8 | (V & 16711935) << 8;\n        N.r[R] = (V >>> 16 | V << 16) >>> 17;\n      }\n      function n(A, l, M) {\n        while (l-- != 0) A.push(0, M);\n      }\n      for (var R = 0; R < 32; R++) {\n        N.q[R] = N.S[R] << 3 | N.T[R];\n        N.c[R] = N.p[R] << 4 | N.z[R];\n      }\n      n(N._, 144, 8);\n      n(N._, 255 - 143, 9);\n      n(N._, 279 - 255, 7);\n      n(N._, 287 - 279, 8);\n      H.H.n(N._, 9);\n      H.H.A(N._, 9, N.J);\n      H.H.l(N._, 9);\n      n(N.$, 32, 5);\n      H.H.n(N.$, 5);\n      H.H.A(N.$, 5, N.h);\n      H.H.l(N.$, 5);\n      n(N.Q, 19, 0);\n      n(N.C, 286, 0);\n      n(N.D, 30, 0);\n      n(N.v, 320, 0);\n    })();\n    return H.H.N;\n  }();\n  UPNG.decode._readInterlace = function (data, out) {\n    var w = out.width,\n      h = out.height;\n    var bpp = UPNG.decode._getBPP(out),\n      cbpp = bpp >> 3,\n      bpl = Math.ceil(w * bpp / 8);\n    var img = new Uint8Array(h * bpl);\n    var di = 0;\n    var starting_row = [0, 0, 4, 0, 2, 0, 1];\n    var starting_col = [0, 4, 0, 2, 0, 1, 0];\n    var row_increment = [8, 8, 8, 4, 4, 2, 2];\n    var col_increment = [8, 8, 4, 4, 2, 2, 1];\n    var pass = 0;\n    while (pass < 7) {\n      var ri = row_increment[pass],\n        ci = col_increment[pass];\n      var sw = 0,\n        sh = 0;\n      var cr = starting_row[pass];\n      while (cr < h) {\n        cr += ri;\n        sh++;\n      }\n      var cc = starting_col[pass];\n      while (cc < w) {\n        cc += ci;\n        sw++;\n      }\n      var bpll = Math.ceil(sw * bpp / 8);\n      UPNG.decode._filterZero(data, out, di, sw, sh);\n      var y = 0,\n        row = starting_row[pass];\n      var val;\n      while (row < h) {\n        var col = starting_col[pass];\n        var cdi = di + y * bpll << 3;\n        while (col < w) {\n          if (bpp == 1) {\n            val = data[cdi >> 3];\n            val = val >> 7 - (cdi & 7) & 1;\n            img[row * bpl + (col >> 3)] |= val << 7 - ((col & 7) << 0);\n          }\n          if (bpp == 2) {\n            val = data[cdi >> 3];\n            val = val >> 6 - (cdi & 7) & 3;\n            img[row * bpl + (col >> 2)] |= val << 6 - ((col & 3) << 1);\n          }\n          if (bpp == 4) {\n            val = data[cdi >> 3];\n            val = val >> 4 - (cdi & 7) & 15;\n            img[row * bpl + (col >> 1)] |= val << 4 - ((col & 1) << 2);\n          }\n          if (bpp >= 8) {\n            var ii = row * bpl + col * cbpp;\n            for (var j = 0; j < cbpp; j++) img[ii + j] = data[(cdi >> 3) + j];\n          }\n          cdi += bpp;\n          col += ci;\n        }\n        y++;\n        row += ri;\n      }\n      if (sw * sh != 0) di += sh * (1 + bpll);\n      pass = pass + 1;\n    }\n    return img;\n  };\n  UPNG.decode._getBPP = function (out) {\n    var noc = [1, null, 3, 1, 2, null, 4][out.ctype];\n    return noc * out.depth;\n  };\n  UPNG.decode._filterZero = function (data, out, off, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil(w * bpp / 8),\n      paeth = UPNG.decode._paeth;\n    bpp = Math.ceil(bpp / 8);\n    var i,\n      di,\n      type = data[off],\n      x = 0;\n    if (type > 1) data[off] = [0, 0, 1][type - 2];\n    if (type == 3) for (x = bpp; x < bpl; x++) data[x + 1] = data[x + 1] + (data[x + 1 - bpp] >>> 1) & 255;\n    for (var y = 0; y < h; y++) {\n      i = off + y * bpl;\n      di = i + y + 1;\n      type = data[di - 1];\n      x = 0;\n      if (type == 0) {\n        for (; x < bpl; x++) data[i + x] = data[di + x];\n      } else if (type == 1) {\n        for (; x < bpp; x++) data[i + x] = data[di + x];\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpp];\n      } else if (type == 2) {\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpl];\n      } else if (type == 3) {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + (data[i + x - bpl] >>> 1);\n        for (; x < bpl; x++) data[i + x] = data[di + x] + (data[i + x - bpl] + data[i + x - bpp] >>> 1);\n      } else {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + paeth(0, data[i + x - bpl], 0);\n        for (; x < bpl; x++) {\n          data[i + x] = data[di + x] + paeth(data[i + x - bpp], data[i + x - bpl], data[i + x - bpp - bpl]);\n        }\n      }\n    }\n    return data;\n  };\n  UPNG.decode._paeth = function (a, b, c) {\n    var p = a + b - c,\n      pa = p - a,\n      pb = p - b,\n      pc = p - c;\n    if (pa * pa <= pb * pb && pa * pa <= pc * pc) return a;else if (pb * pb <= pc * pc) return b;\n    return c;\n  };\n  UPNG.decode._IHDR = function (data, offset, out) {\n    var bin = UPNG._bin;\n    out.width = bin.readUint(data, offset);\n    offset += 4;\n    out.height = bin.readUint(data, offset);\n    offset += 4;\n    out.depth = data[offset];\n    offset++;\n    out.ctype = data[offset];\n    offset++;\n    out.compress = data[offset];\n    offset++;\n    out.filter = data[offset];\n    offset++;\n    out.interlace = data[offset];\n    offset++;\n  };\n  UPNG._bin = {\n    nextZero: function (data, p) {\n      while (data[p] != 0) p++;\n      return p;\n    },\n    readUshort: function (buff, p) {\n      return buff[p] << 8 | buff[p + 1];\n    },\n    writeUshort: function (buff, p, n) {\n      buff[p] = n >> 8 & 255;\n      buff[p + 1] = n & 255;\n    },\n    readUint: function (buff, p) {\n      return buff[p] * (256 * 256 * 256) + (buff[p + 1] << 16 | buff[p + 2] << 8 | buff[p + 3]);\n    },\n    writeUint: function (buff, p, n) {\n      buff[p] = n >> 24 & 255;\n      buff[p + 1] = n >> 16 & 255;\n      buff[p + 2] = n >> 8 & 255;\n      buff[p + 3] = n & 255;\n    },\n    readASCII: function (buff, p, l) {\n      var s = \"\";\n      for (var i = 0; i < l; i++) s += String.fromCharCode(buff[p + i]);\n      return s;\n    },\n    writeASCII: function (data, p, s) {\n      for (var i = 0; i < s.length; i++) data[p + i] = s.charCodeAt(i);\n    },\n    readBytes: function (buff, p, l) {\n      var arr = [];\n      for (var i = 0; i < l; i++) arr.push(buff[p + i]);\n      return arr;\n    },\n    pad: function (n) {\n      return n.length < 2 ? \"0\" + n : n;\n    },\n    readUTF8: function (buff, p, l) {\n      var s = \"\",\n        ns;\n      for (var i = 0; i < l; i++) s += \"%\" + UPNG._bin.pad(buff[p + i].toString(16));\n      try {\n        ns = decodeURIComponent(s);\n      } catch (e) {\n        return UPNG._bin.readASCII(buff, p, l);\n      }\n      return ns;\n    }\n  };\n  UPNG._copyTile = function (sb, sw, sh, tb, tw, th, xoff, yoff, mode) {\n    var w = Math.min(sw, tw),\n      h = Math.min(sh, th);\n    var si = 0,\n      ti = 0;\n    for (var y = 0; y < h; y++) {\n      for (var x = 0; x < w; x++) {\n        if (xoff >= 0 && yoff >= 0) {\n          si = y * sw + x << 2;\n          ti = (yoff + y) * tw + xoff + x << 2;\n        } else {\n          si = (-yoff + y) * sw - xoff + x << 2;\n          ti = y * tw + x << 2;\n        }\n        if (mode == 0) {\n          tb[ti] = sb[si];\n          tb[ti + 1] = sb[si + 1];\n          tb[ti + 2] = sb[si + 2];\n          tb[ti + 3] = sb[si + 3];\n        } else if (mode == 1) {\n          var fa = sb[si + 3] * (1 / 255),\n            fr = sb[si] * fa,\n            fg = sb[si + 1] * fa,\n            fb = sb[si + 2] * fa;\n          var ba = tb[ti + 3] * (1 / 255),\n            br = tb[ti] * ba,\n            bg = tb[ti + 1] * ba,\n            bb = tb[ti + 2] * ba;\n          var ifa = 1 - fa,\n            oa = fa + ba * ifa,\n            ioa = oa == 0 ? 0 : 1 / oa;\n          tb[ti + 3] = 255 * oa;\n          tb[ti + 0] = (fr + br * ifa) * ioa;\n          tb[ti + 1] = (fg + bg * ifa) * ioa;\n          tb[ti + 2] = (fb + bb * ifa) * ioa;\n        } else if (mode == 2) {\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2];\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2];\n          if (fa == ba && fr == br && fg == bg && fb == bb) {\n            tb[ti] = 0;\n            tb[ti + 1] = 0;\n            tb[ti + 2] = 0;\n            tb[ti + 3] = 0;\n          } else {\n            tb[ti] = fr;\n            tb[ti + 1] = fg;\n            tb[ti + 2] = fb;\n            tb[ti + 3] = fa;\n          }\n        } else if (mode == 3) {\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2];\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2];\n          if (fa == ba && fr == br && fg == bg && fb == bb) continue;\n          if (fa < 220 && ba > 20) return false;\n        }\n      }\n    }\n    return true;\n  };\n}\nclass RGBMLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n    this.type = HalfFloatType;\n    this.maxRange = 7;\n  }\n  setDataType(value) {\n    this.type = value;\n    return this;\n  }\n  setMaxRange(value) {\n    this.maxRange = value;\n    return this;\n  }\n  loadCubemap(urls, onLoad, onProgress, onError) {\n    const texture = new CubeTexture();\n    let loaded = 0;\n    const scope = this;\n    function loadTexture(i) {\n      scope.load(urls[i], function (image) {\n        texture.images[i] = image;\n        loaded++;\n        if (loaded === 6) {\n          texture.needsUpdate = true;\n          if (onLoad) onLoad(texture);\n        }\n      }, void 0, onError);\n    }\n    for (let i = 0; i < urls.length; ++i) {\n      loadTexture(i);\n    }\n    texture.type = this.type;\n    texture.format = RGBAFormat;\n    texture.minFilter = LinearFilter;\n    texture.generateMipmaps = false;\n    return texture;\n  }\n  parse(buffer) {\n    init();\n    const img = UPNG.decode(buffer);\n    const rgba = UPNG.toRGBA8(img)[0];\n    const data = new Uint8Array(rgba);\n    const size = img.width * img.height * 4;\n    const output = this.type === HalfFloatType ? new Uint16Array(size) : new Float32Array(size);\n    for (let i = 0; i < data.length; i += 4) {\n      const r = data[i + 0] / 255;\n      const g = data[i + 1] / 255;\n      const b = data[i + 2] / 255;\n      const a = data[i + 3] / 255;\n      if (this.type === HalfFloatType) {\n        output[i + 0] = DataUtils.toHalfFloat(Math.min(r * a * this.maxRange, 65504));\n        output[i + 1] = DataUtils.toHalfFloat(Math.min(g * a * this.maxRange, 65504));\n        output[i + 2] = DataUtils.toHalfFloat(Math.min(b * a * this.maxRange, 65504));\n        output[i + 3] = DataUtils.toHalfFloat(1);\n      } else {\n        output[i + 0] = r * a * this.maxRange;\n        output[i + 1] = g * a * this.maxRange;\n        output[i + 2] = b * a * this.maxRange;\n        output[i + 3] = 1;\n      }\n    }\n    return {\n      width: img.width,\n      height: img.height,\n      data: output,\n      format: RGBAFormat,\n      type: this.type,\n      flipY: true\n    };\n  }\n}\nexport { RGBMLoader };", "map": {"version": 3, "names": ["UPNG", "init", "toRGBA8", "out", "w", "width", "h", "height", "tabs", "acTL", "decodeImage", "data", "buffer", "frms", "frames", "len", "img", "Uint8Array", "empty", "prev", "i", "length", "frm", "fx", "rect", "x", "fy", "y", "fw", "fh", "fdata", "j", "blend", "_copyTile", "push", "slice", "dispose", "area", "bpp", "decode", "_getBPP", "bpl", "Math", "ceil", "bf", "bf32", "Uint32Array", "ctype", "depth", "rs", "_bin", "readUshort", "qarea", "ts", "ti", "tr", "tg", "tb", "qi", "p", "ap", "tl", "s0", "t0", "cj", "di", "gr", "off", "to", "al", "buff", "offset", "bin", "rUs", "rUi", "readUint", "dd", "doff", "fd", "foff", "text", "keyw", "bfr", "mgck", "Error", "type", "readASCII", "_IHDR", "num_frames", "num_plays", "fr", "_decompress", "rct", "del", "delay", "round", "nz", "nextZero", "_inflate", "readUTF8", "cflag", "readBytes", "pl", "compress", "interlace", "filter", "inflateRaw", "_filterZero", "_readInterlace", "H", "N", "W", "R", "m", "J", "Q", "X", "u", "d", "v", "C", "V", "n", "b", "A", "e", "l", "M", "I", "Z", "D", "q", "set", "byteOffset", "c", "K", "r", "S", "T", "z", "_", "$", "s", "Y", "a", "Uint16Array", "t", "k", "g", "cbpp", "starting_row", "starting_col", "row_increment", "col_increment", "pass", "ri", "ci", "sw", "sh", "cr", "cc", "bpll", "row", "val", "col", "cdi", "ii", "noc", "paeth", "_paeth", "pa", "pb", "pc", "writeUshort", "writeUint", "String", "fromCharCode", "writeASCII", "charCodeAt", "arr", "pad", "ns", "toString", "decodeURIComponent", "sb", "tw", "th", "xoff", "yoff", "mode", "min", "si", "fa", "fg", "fb", "ba", "br", "bg", "bb", "ifa", "oa", "ioa", "RGBMLoader", "DataTextureLoader", "constructor", "manager", "HalfFloatType", "max<PERSON><PERSON><PERSON>", "setDataType", "value", "setMaxRange", "loadCubemap", "urls", "onLoad", "onProgress", "onError", "texture", "CubeTexture", "loaded", "scope", "loadTexture", "load", "image", "images", "needsUpdate", "format", "RGBAFormat", "minFilter", "LinearFilter", "generateMipmaps", "parse", "rgba", "size", "output", "Float32Array", "DataUtils", "toHalfFloat", "flipY"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/RGBMLoader.js"], "sourcesContent": ["import { DataTextureLoader, RGBAFormat, LinearFilter, CubeTexture, HalfFloatType, DataUtils } from 'three'\n\nlet UPNG\n\nfunction init() {\n  if (UPNG) return UPNG\n  // from https://github.com/photopea/UPNG.js (MIT License)\n\n  UPNG = {}\n\n  UPNG.toRGBA8 = function (out) {\n    var w = out.width,\n      h = out.height\n    if (out.tabs.acTL == null) return [UPNG.toRGBA8.decodeImage(out.data, w, h, out).buffer]\n\n    var frms = []\n    if (out.frames[0].data == null) out.frames[0].data = out.data\n\n    var len = w * h * 4,\n      img = new Uint8Array(len),\n      empty = new Uint8Array(len),\n      prev = new Uint8Array(len)\n    for (var i = 0; i < out.frames.length; i++) {\n      var frm = out.frames[i]\n      var fx = frm.rect.x,\n        fy = frm.rect.y,\n        fw = frm.rect.width,\n        fh = frm.rect.height\n      var fdata = UPNG.toRGBA8.decodeImage(frm.data, fw, fh, out)\n\n      if (i != 0) for (var j = 0; j < len; j++) prev[j] = img[j]\n\n      if (frm.blend == 0) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 0)\n      else if (frm.blend == 1) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 1)\n\n      frms.push(img.buffer.slice(0))\n\n      if (frm.dispose == 1) UPNG._copyTile(empty, fw, fh, img, w, h, fx, fy, 0)\n      else if (frm.dispose == 2) for (var j = 0; j < len; j++) img[j] = prev[j]\n    }\n\n    return frms\n  }\n\n  UPNG.toRGBA8.decodeImage = function (data, w, h, out) {\n    var area = w * h,\n      bpp = UPNG.decode._getBPP(out)\n    var bpl = Math.ceil((w * bpp) / 8) // bytes per line\n\n    var bf = new Uint8Array(area * 4),\n      bf32 = new Uint32Array(bf.buffer)\n    var ctype = out.ctype,\n      depth = out.depth\n    var rs = UPNG._bin.readUshort\n\n    if (ctype == 6) {\n      // RGB + alpha\n\n      var qarea = area << 2\n      if (depth == 8) {\n        for (var i = 0; i < qarea; i += 4) {\n          bf[i] = data[i]\n          bf[i + 1] = data[i + 1]\n          bf[i + 2] = data[i + 2]\n          bf[i + 3] = data[i + 3]\n        }\n      }\n\n      if (depth == 16) {\n        for (var i = 0; i < qarea; i++) {\n          bf[i] = data[i << 1]\n        }\n      }\n    } else if (ctype == 2) {\n      // RGB\n\n      var ts = out.tabs['tRNS']\n      if (ts == null) {\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 3\n            bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti]\n          }\n        }\n\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 6\n            bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti]\n          }\n        }\n      } else {\n        var tr = ts[0],\n          tg = ts[1],\n          tb = ts[2]\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 3\n            bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti]\n            if (data[ti] == tr && data[ti + 1] == tg && data[ti + 2] == tb) bf[qi + 3] = 0\n          }\n        }\n\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 6\n            bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti]\n            if (rs(data, ti) == tr && rs(data, ti + 2) == tg && rs(data, ti + 4) == tb) bf[qi + 3] = 0\n          }\n        }\n      }\n    } else if (ctype == 3) {\n      // palette\n\n      var p = out.tabs['PLTE'],\n        ap = out.tabs['tRNS'],\n        tl = ap ? ap.length : 0\n      //console.log(p, ap);\n      if (depth == 1) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 3)] >> (7 - ((i & 7) << 0))) & 1,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 2) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 2)] >> (6 - ((i & 3) << 1))) & 3,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 4) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 1)] >> (4 - ((i & 1) << 2))) & 15,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            j = data[i],\n            cj = 3 * j\n          bf[qi] = p[cj]\n          bf[qi + 1] = p[cj + 1]\n          bf[qi + 2] = p[cj + 2]\n          bf[qi + 3] = j < tl ? ap[j] : 255\n        }\n      }\n    } else if (ctype == 4) {\n      // gray + alpha\n\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 1,\n            gr = data[di]\n          bf[qi] = gr\n          bf[qi + 1] = gr\n          bf[qi + 2] = gr\n          bf[qi + 3] = data[di + 1]\n        }\n      }\n\n      if (depth == 16) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 2,\n            gr = data[di]\n          bf[qi] = gr\n          bf[qi + 1] = gr\n          bf[qi + 2] = gr\n          bf[qi + 3] = data[di + 2]\n        }\n      }\n    } else if (ctype == 0) {\n      // gray\n\n      var tr = out.tabs['tRNS'] ? out.tabs['tRNS'] : -1\n      for (var y = 0; y < h; y++) {\n        var off = y * bpl,\n          to = y * w\n        if (depth == 1) {\n          for (var x = 0; x < w; x++) {\n            var gr = 255 * ((data[off + (x >>> 3)] >>> (7 - (x & 7))) & 1),\n              al = gr == tr * 255 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 2) {\n          for (var x = 0; x < w; x++) {\n            var gr = 85 * ((data[off + (x >>> 2)] >>> (6 - ((x & 3) << 1))) & 3),\n              al = gr == tr * 85 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 4) {\n          for (var x = 0; x < w; x++) {\n            var gr = 17 * ((data[off + (x >>> 1)] >>> (4 - ((x & 1) << 2))) & 15),\n              al = gr == tr * 17 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 8) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + x],\n              al = gr == tr ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 16) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + (x << 1)],\n              al = rs(data, off + (x << 1)) == tr ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        }\n      }\n    }\n\n    //console.log(Date.now()-time);\n    return bf\n  }\n\n  UPNG.decode = function (buff) {\n    var data = new Uint8Array(buff),\n      offset = 8,\n      bin = UPNG._bin,\n      rUs = bin.readUshort,\n      rUi = bin.readUint\n    var out = { tabs: {}, frames: [] }\n    var dd = new Uint8Array(data.length),\n      doff = 0 // put all IDAT data into it\n    var fd,\n      foff = 0 // frames\n    var text, keyw, bfr\n\n    var mgck = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]\n    for (var i = 0; i < 8; i++) if (data[i] != mgck[i]) throw new Error('The input is not a PNG file!')\n\n    while (offset < data.length) {\n      var len = bin.readUint(data, offset)\n      offset += 4\n      var type = bin.readASCII(data, offset, 4)\n      offset += 4\n      //console.log(type,len);\n\n      if (type == 'IHDR') {\n        UPNG.decode._IHDR(data, offset, out)\n      } else if (type == 'CgBI') {\n        out.tabs[type] = data.slice(offset, offset + 4)\n      } else if (type == 'IDAT') {\n        for (var i = 0; i < len; i++) dd[doff + i] = data[offset + i]\n        doff += len\n      } else if (type == 'acTL') {\n        out.tabs[type] = { num_frames: rUi(data, offset), num_plays: rUi(data, offset + 4) }\n        fd = new Uint8Array(data.length)\n      } else if (type == 'fcTL') {\n        if (foff != 0) {\n          var fr = out.frames[out.frames.length - 1]\n          fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height)\n          foff = 0\n        }\n\n        var rct = {\n          x: rUi(data, offset + 12),\n          y: rUi(data, offset + 16),\n          width: rUi(data, offset + 4),\n          height: rUi(data, offset + 8),\n        }\n        var del = rUs(data, offset + 22)\n        del = rUs(data, offset + 20) / (del == 0 ? 100 : del)\n        var frm = { rect: rct, delay: Math.round(del * 1000), dispose: data[offset + 24], blend: data[offset + 25] }\n        //console.log(frm);\n        out.frames.push(frm)\n      } else if (type == 'fdAT') {\n        for (var i = 0; i < len - 4; i++) fd[foff + i] = data[offset + i + 4]\n        foff += len - 4\n      } else if (type == 'pHYs') {\n        out.tabs[type] = [bin.readUint(data, offset), bin.readUint(data, offset + 4), data[offset + 8]]\n      } else if (type == 'cHRM') {\n        out.tabs[type] = []\n        for (var i = 0; i < 8; i++) out.tabs[type].push(bin.readUint(data, offset + i * 4))\n      } else if (type == 'tEXt' || type == 'zTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {}\n        var nz = bin.nextZero(data, offset)\n        keyw = bin.readASCII(data, offset, nz - offset)\n        var tl = offset + len - nz - 1\n        if (type == 'tEXt') {\n          text = bin.readASCII(data, nz + 1, tl)\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(nz + 2, nz + 2 + tl))\n          text = bin.readUTF8(bfr, 0, bfr.length)\n        }\n\n        out.tabs[type][keyw] = text\n      } else if (type == 'iTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {}\n        var nz = 0,\n          off = offset\n        nz = bin.nextZero(data, off)\n        keyw = bin.readASCII(data, off, nz - off)\n        off = nz + 1\n        var cflag = data[off]\n        off += 2\n        nz = bin.nextZero(data, off)\n        bin.readASCII(data, off, nz - off)\n        off = nz + 1\n        nz = bin.nextZero(data, off)\n        bin.readUTF8(data, off, nz - off)\n        off = nz + 1\n        var tl = len - (off - offset)\n        if (cflag == 0) {\n          text = bin.readUTF8(data, off, tl)\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(off, off + tl))\n          text = bin.readUTF8(bfr, 0, bfr.length)\n        }\n\n        out.tabs[type][keyw] = text\n      } else if (type == 'PLTE') {\n        out.tabs[type] = bin.readBytes(data, offset, len)\n      } else if (type == 'hIST') {\n        var pl = out.tabs['PLTE'].length / 3\n        out.tabs[type] = []\n        for (var i = 0; i < pl; i++) out.tabs[type].push(rUs(data, offset + i * 2))\n      } else if (type == 'tRNS') {\n        if (out.ctype == 3) out.tabs[type] = bin.readBytes(data, offset, len)\n        else if (out.ctype == 0) out.tabs[type] = rUs(data, offset)\n        else if (out.ctype == 2) out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)]\n        //else console.log(\"tRNS for unsupported color type\",out.ctype, len);\n      } else if (type == 'gAMA') {\n        out.tabs[type] = bin.readUint(data, offset) / 100000\n      } else if (type == 'sRGB') {\n        out.tabs[type] = data[offset]\n      } else if (type == 'bKGD') {\n        if (out.ctype == 0 || out.ctype == 4) {\n          out.tabs[type] = [rUs(data, offset)]\n        } else if (out.ctype == 2 || out.ctype == 6) {\n          out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)]\n        } else if (out.ctype == 3) {\n          out.tabs[type] = data[offset]\n        }\n      } else if (type == 'IEND') {\n        break\n      }\n\n      //else {  console.log(\"unknown chunk type\", type, len);  out.tabs[type]=data.slice(offset,offset+len);  }\n      offset += len\n      bin.readUint(data, offset)\n      offset += 4\n    }\n\n    if (foff != 0) {\n      var fr = out.frames[out.frames.length - 1]\n      fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height)\n    }\n\n    out.data = UPNG.decode._decompress(out, dd, out.width, out.height)\n\n    delete out.compress\n    delete out.interlace\n    delete out.filter\n    return out\n  }\n\n  UPNG.decode._decompress = function (out, dd, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil((w * bpp) / 8),\n      buff = new Uint8Array((bpl + 1 + out.interlace) * h)\n    if (out.tabs['CgBI']) dd = UPNG.inflateRaw(dd, buff)\n    else dd = UPNG.decode._inflate(dd, buff)\n\n    if (out.interlace == 0) dd = UPNG.decode._filterZero(dd, out, 0, w, h)\n    else if (out.interlace == 1) dd = UPNG.decode._readInterlace(dd, out)\n\n    return dd\n  }\n\n  UPNG.decode._inflate = function (data, buff) {\n    var out = UPNG['inflateRaw'](new Uint8Array(data.buffer, 2, data.length - 6), buff)\n    return out\n  }\n\n  UPNG.inflateRaw = (function () {\n    var H = {}\n    H.H = {}\n    H.H.N = function (N, W) {\n      var R = Uint8Array,\n        i = 0,\n        m = 0,\n        J = 0,\n        h = 0,\n        Q = 0,\n        X = 0,\n        u = 0,\n        w = 0,\n        d = 0,\n        v,\n        C\n      if (N[0] == 3 && N[1] == 0) return W ? W : new R(0)\n      var V = H.H,\n        n = V.b,\n        A = V.e,\n        l = V.R,\n        M = V.n,\n        I = V.A,\n        e = V.Z,\n        b = V.m,\n        Z = W == null\n      if (Z) W = new R((N.length >>> 2) << 5)\n      while (i == 0) {\n        i = n(N, d, 1)\n        m = n(N, d + 1, 2)\n        d += 3\n        if (m == 0) {\n          if ((d & 7) != 0) d += 8 - (d & 7)\n          var D = (d >>> 3) + 4,\n            q = N[D - 4] | (N[D - 3] << 8)\n          if (Z) W = H.H.W(W, w + q)\n          W.set(new R(N.buffer, N.byteOffset + D, q), w)\n          d = (D + q) << 3\n          w += q\n          continue\n        }\n\n        if (Z) W = H.H.W(W, w + (1 << 17))\n        if (m == 1) {\n          v = b.J\n          C = b.h\n          X = (1 << 9) - 1\n          u = (1 << 5) - 1\n        }\n\n        if (m == 2) {\n          J = A(N, d, 5) + 257\n          h = A(N, d + 5, 5) + 1\n          Q = A(N, d + 10, 4) + 4\n          d += 14\n          var j = 1\n          for (var c = 0; c < 38; c += 2) {\n            b.Q[c] = 0\n            b.Q[c + 1] = 0\n          }\n\n          for (var c = 0; c < Q; c++) {\n            var K = A(N, d + c * 3, 3)\n            b.Q[(b.X[c] << 1) + 1] = K\n            if (K > j) j = K\n          }\n\n          d += 3 * Q\n          M(b.Q, j)\n          I(b.Q, j, b.u)\n          v = b.w\n          C = b.d\n          d = l(b.u, (1 << j) - 1, J + h, N, d, b.v)\n          var r = V.V(b.v, 0, J, b.C)\n          X = (1 << r) - 1\n          var S = V.V(b.v, J, h, b.D)\n          u = (1 << S) - 1\n          M(b.C, r)\n          I(b.C, r, v)\n          M(b.D, S)\n          I(b.D, S, C)\n        }\n\n        while (!0) {\n          var T = v[e(N, d) & X]\n          d += T & 15\n          var p = T >>> 4\n          if (p >>> 8 == 0) {\n            W[w++] = p\n          } else if (p == 256) {\n            break\n          } else {\n            var z = w + p - 254\n            if (p > 264) {\n              var _ = b.q[p - 257]\n              z = w + (_ >>> 3) + A(N, d, _ & 7)\n              d += _ & 7\n            }\n\n            var $ = C[e(N, d) & u]\n            d += $ & 15\n            var s = $ >>> 4,\n              Y = b.c[s],\n              a = (Y >>> 4) + n(N, d, Y & 15)\n            d += Y & 15\n            while (w < z) {\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n            }\n\n            w = z\n          }\n        }\n      }\n\n      return W.length == w ? W : W.slice(0, w)\n    }\n\n    H.H.W = function (N, W) {\n      var R = N.length\n      if (W <= R) return N\n      var V = new Uint8Array(R << 1)\n      V.set(N, 0)\n      return V\n    }\n\n    H.H.R = function (N, W, R, V, n, A) {\n      var l = H.H.e,\n        M = H.H.Z,\n        I = 0\n      while (I < R) {\n        var e = N[M(V, n) & W]\n        n += e & 15\n        var b = e >>> 4\n        if (b <= 15) {\n          A[I] = b\n          I++\n        } else {\n          var Z = 0,\n            m = 0\n          if (b == 16) {\n            m = 3 + l(V, n, 2)\n            n += 2\n            Z = A[I - 1]\n          } else if (b == 17) {\n            m = 3 + l(V, n, 3)\n            n += 3\n          } else if (b == 18) {\n            m = 11 + l(V, n, 7)\n            n += 7\n          }\n\n          var J = I + m\n          while (I < J) {\n            A[I] = Z\n            I++\n          }\n        }\n      }\n\n      return n\n    }\n\n    H.H.V = function (N, W, R, V) {\n      var n = 0,\n        A = 0,\n        l = V.length >>> 1\n      while (A < R) {\n        var M = N[A + W]\n        V[A << 1] = 0\n        V[(A << 1) + 1] = M\n        if (M > n) n = M\n        A++\n      }\n\n      while (A < l) {\n        V[A << 1] = 0\n        V[(A << 1) + 1] = 0\n        A++\n      }\n\n      return n\n    }\n\n    H.H.n = function (N, W) {\n      var R = H.H.m,\n        V = N.length,\n        n,\n        A,\n        l,\n        M,\n        I,\n        e = R.j\n      for (var M = 0; M <= W; M++) e[M] = 0\n      for (M = 1; M < V; M += 2) e[N[M]]++\n      var b = R.K\n      n = 0\n      e[0] = 0\n      for (A = 1; A <= W; A++) {\n        n = (n + e[A - 1]) << 1\n        b[A] = n\n      }\n\n      for (l = 0; l < V; l += 2) {\n        I = N[l + 1]\n        if (I != 0) {\n          N[l] = b[I]\n          b[I]++\n        }\n      }\n    }\n\n    H.H.A = function (N, W, R) {\n      var V = N.length,\n        n = H.H.m,\n        A = n.r\n      for (var l = 0; l < V; l += 2) {\n        if (N[l + 1] != 0) {\n          var M = l >> 1,\n            I = N[l + 1],\n            e = (M << 4) | I,\n            b = W - I,\n            Z = N[l] << b,\n            m = Z + (1 << b)\n          while (Z != m) {\n            var J = A[Z] >>> (15 - W)\n            R[J] = e\n            Z++\n          }\n        }\n      }\n    }\n\n    H.H.l = function (N, W) {\n      var R = H.H.m.r,\n        V = 15 - W\n      for (var n = 0; n < N.length; n += 2) {\n        var A = N[n] << (W - N[n + 1])\n        N[n] = R[A] >>> V\n      }\n    }\n\n    H.H.M = function (N, W, R) {\n      R = R << (W & 7)\n      var V = W >>> 3\n      N[V] |= R\n      N[V + 1] |= R >>> 8\n    }\n\n    H.H.I = function (N, W, R) {\n      R = R << (W & 7)\n      var V = W >>> 3\n      N[V] |= R\n      N[V + 1] |= R >>> 8\n      N[V + 2] |= R >>> 16\n    }\n\n    H.H.e = function (N, W, R) {\n      return ((N[W >>> 3] | (N[(W >>> 3) + 1] << 8)) >>> (W & 7)) & ((1 << R) - 1)\n    }\n\n    H.H.b = function (N, W, R) {\n      return ((N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16)) >>> (W & 7)) & ((1 << R) - 1)\n    }\n\n    H.H.Z = function (N, W) {\n      return (N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16)) >>> (W & 7)\n    }\n\n    H.H.i = function (N, W) {\n      return (N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16) | (N[(W >>> 3) + 3] << 24)) >>> (W & 7)\n    }\n\n    H.H.m = (function () {\n      var N = Uint16Array,\n        W = Uint32Array\n      return {\n        K: new N(16),\n        j: new N(16),\n        X: [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15],\n        S: [\n          3,\n          4,\n          5,\n          6,\n          7,\n          8,\n          9,\n          10,\n          11,\n          13,\n          15,\n          17,\n          19,\n          23,\n          27,\n          31,\n          35,\n          43,\n          51,\n          59,\n          67,\n          83,\n          99,\n          115,\n          131,\n          163,\n          195,\n          227,\n          258,\n          999,\n          999,\n          999,\n        ],\n        T: [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0],\n        q: new N(32),\n        p: [\n          1,\n          2,\n          3,\n          4,\n          5,\n          7,\n          9,\n          13,\n          17,\n          25,\n          33,\n          49,\n          65,\n          97,\n          129,\n          193,\n          257,\n          385,\n          513,\n          769,\n          1025,\n          1537,\n          2049,\n          3073,\n          4097,\n          6145,\n          8193,\n          12289,\n          16385,\n          24577,\n          65535,\n          65535,\n        ],\n        z: [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0],\n        c: new W(32),\n        J: new N(512),\n        _: [],\n        h: new N(32),\n        $: [],\n        w: new N(32768),\n        C: [],\n        v: [],\n        d: new N(32768),\n        D: [],\n        u: new N(512),\n        Q: [],\n        r: new N(1 << 15),\n        s: new W(286),\n        Y: new W(30),\n        a: new W(19),\n        t: new W(15e3),\n        k: new N(1 << 16),\n        g: new N(1 << 15),\n      }\n    })()\n    ;(function () {\n      var N = H.H.m,\n        W = 1 << 15\n      for (var R = 0; R < W; R++) {\n        var V = R\n        V = ((V & 2863311530) >>> 1) | ((V & 1431655765) << 1)\n        V = ((V & 3435973836) >>> 2) | ((V & 858993459) << 2)\n        V = ((V & 4042322160) >>> 4) | ((V & 252645135) << 4)\n        V = ((V & 4278255360) >>> 8) | ((V & 16711935) << 8)\n        N.r[R] = ((V >>> 16) | (V << 16)) >>> 17\n      }\n\n      function n(A, l, M) {\n        while (l-- != 0) A.push(0, M)\n      }\n\n      for (var R = 0; R < 32; R++) {\n        N.q[R] = (N.S[R] << 3) | N.T[R]\n        N.c[R] = (N.p[R] << 4) | N.z[R]\n      }\n\n      n(N._, 144, 8)\n      n(N._, 255 - 143, 9)\n      n(N._, 279 - 255, 7)\n      n(N._, 287 - 279, 8)\n      H.H.n(N._, 9)\n      H.H.A(N._, 9, N.J)\n      H.H.l(N._, 9)\n      n(N.$, 32, 5)\n      H.H.n(N.$, 5)\n      H.H.A(N.$, 5, N.h)\n      H.H.l(N.$, 5)\n      n(N.Q, 19, 0)\n      n(N.C, 286, 0)\n      n(N.D, 30, 0)\n      n(N.v, 320, 0)\n    })()\n\n    return H.H.N\n  })()\n\n  UPNG.decode._readInterlace = function (data, out) {\n    var w = out.width,\n      h = out.height\n    var bpp = UPNG.decode._getBPP(out),\n      cbpp = bpp >> 3,\n      bpl = Math.ceil((w * bpp) / 8)\n    var img = new Uint8Array(h * bpl)\n    var di = 0\n\n    var starting_row = [0, 0, 4, 0, 2, 0, 1]\n    var starting_col = [0, 4, 0, 2, 0, 1, 0]\n    var row_increment = [8, 8, 8, 4, 4, 2, 2]\n    var col_increment = [8, 8, 4, 4, 2, 2, 1]\n\n    var pass = 0\n    while (pass < 7) {\n      var ri = row_increment[pass],\n        ci = col_increment[pass]\n      var sw = 0,\n        sh = 0\n      var cr = starting_row[pass]\n      while (cr < h) {\n        cr += ri\n        sh++\n      }\n\n      var cc = starting_col[pass]\n      while (cc < w) {\n        cc += ci\n        sw++\n      }\n\n      var bpll = Math.ceil((sw * bpp) / 8)\n      UPNG.decode._filterZero(data, out, di, sw, sh)\n\n      var y = 0,\n        row = starting_row[pass]\n      var val\n\n      while (row < h) {\n        var col = starting_col[pass]\n        var cdi = (di + y * bpll) << 3\n\n        while (col < w) {\n          if (bpp == 1) {\n            val = data[cdi >> 3]\n            val = (val >> (7 - (cdi & 7))) & 1\n            img[row * bpl + (col >> 3)] |= val << (7 - ((col & 7) << 0))\n          }\n\n          if (bpp == 2) {\n            val = data[cdi >> 3]\n            val = (val >> (6 - (cdi & 7))) & 3\n            img[row * bpl + (col >> 2)] |= val << (6 - ((col & 3) << 1))\n          }\n\n          if (bpp == 4) {\n            val = data[cdi >> 3]\n            val = (val >> (4 - (cdi & 7))) & 15\n            img[row * bpl + (col >> 1)] |= val << (4 - ((col & 1) << 2))\n          }\n\n          if (bpp >= 8) {\n            var ii = row * bpl + col * cbpp\n            for (var j = 0; j < cbpp; j++) img[ii + j] = data[(cdi >> 3) + j]\n          }\n\n          cdi += bpp\n          col += ci\n        }\n\n        y++\n        row += ri\n      }\n\n      if (sw * sh != 0) di += sh * (1 + bpll)\n      pass = pass + 1\n    }\n\n    return img\n  }\n\n  UPNG.decode._getBPP = function (out) {\n    var noc = [1, null, 3, 1, 2, null, 4][out.ctype]\n    return noc * out.depth\n  }\n\n  UPNG.decode._filterZero = function (data, out, off, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil((w * bpp) / 8),\n      paeth = UPNG.decode._paeth\n    bpp = Math.ceil(bpp / 8)\n\n    var i,\n      di,\n      type = data[off],\n      x = 0\n\n    if (type > 1) data[off] = [0, 0, 1][type - 2]\n    if (type == 3) for (x = bpp; x < bpl; x++) data[x + 1] = (data[x + 1] + (data[x + 1 - bpp] >>> 1)) & 255\n\n    for (var y = 0; y < h; y++) {\n      i = off + y * bpl\n      di = i + y + 1\n      type = data[di - 1]\n      x = 0\n\n      if (type == 0) {\n        for (; x < bpl; x++) data[i + x] = data[di + x]\n      } else if (type == 1) {\n        for (; x < bpp; x++) data[i + x] = data[di + x]\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpp]\n      } else if (type == 2) {\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpl]\n      } else if (type == 3) {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + (data[i + x - bpl] >>> 1)\n        for (; x < bpl; x++) data[i + x] = data[di + x] + ((data[i + x - bpl] + data[i + x - bpp]) >>> 1)\n      } else {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + paeth(0, data[i + x - bpl], 0)\n        for (; x < bpl; x++) {\n          data[i + x] = data[di + x] + paeth(data[i + x - bpp], data[i + x - bpl], data[i + x - bpp - bpl])\n        }\n      }\n    }\n\n    return data\n  }\n\n  UPNG.decode._paeth = function (a, b, c) {\n    var p = a + b - c,\n      pa = p - a,\n      pb = p - b,\n      pc = p - c\n    if (pa * pa <= pb * pb && pa * pa <= pc * pc) return a\n    else if (pb * pb <= pc * pc) return b\n    return c\n  }\n\n  UPNG.decode._IHDR = function (data, offset, out) {\n    var bin = UPNG._bin\n    out.width = bin.readUint(data, offset)\n    offset += 4\n    out.height = bin.readUint(data, offset)\n    offset += 4\n    out.depth = data[offset]\n    offset++\n    out.ctype = data[offset]\n    offset++\n    out.compress = data[offset]\n    offset++\n    out.filter = data[offset]\n    offset++\n    out.interlace = data[offset]\n    offset++\n  }\n\n  UPNG._bin = {\n    nextZero: function (data, p) {\n      while (data[p] != 0) p++\n      return p\n    },\n    readUshort: function (buff, p) {\n      return (buff[p] << 8) | buff[p + 1]\n    },\n    writeUshort: function (buff, p, n) {\n      buff[p] = (n >> 8) & 255\n      buff[p + 1] = n & 255\n    },\n    readUint: function (buff, p) {\n      return buff[p] * (256 * 256 * 256) + ((buff[p + 1] << 16) | (buff[p + 2] << 8) | buff[p + 3])\n    },\n    writeUint: function (buff, p, n) {\n      buff[p] = (n >> 24) & 255\n      buff[p + 1] = (n >> 16) & 255\n      buff[p + 2] = (n >> 8) & 255\n      buff[p + 3] = n & 255\n    },\n    readASCII: function (buff, p, l) {\n      var s = ''\n      for (var i = 0; i < l; i++) s += String.fromCharCode(buff[p + i])\n      return s\n    },\n    writeASCII: function (data, p, s) {\n      for (var i = 0; i < s.length; i++) data[p + i] = s.charCodeAt(i)\n    },\n    readBytes: function (buff, p, l) {\n      var arr = []\n      for (var i = 0; i < l; i++) arr.push(buff[p + i])\n      return arr\n    },\n    pad: function (n) {\n      return n.length < 2 ? '0' + n : n\n    },\n    readUTF8: function (buff, p, l) {\n      var s = '',\n        ns\n      for (var i = 0; i < l; i++) s += '%' + UPNG._bin.pad(buff[p + i].toString(16))\n      try {\n        ns = decodeURIComponent(s)\n      } catch (e) {\n        return UPNG._bin.readASCII(buff, p, l)\n      }\n\n      return ns\n    },\n  }\n  UPNG._copyTile = function (sb, sw, sh, tb, tw, th, xoff, yoff, mode) {\n    var w = Math.min(sw, tw),\n      h = Math.min(sh, th)\n    var si = 0,\n      ti = 0\n    for (var y = 0; y < h; y++) {\n      for (var x = 0; x < w; x++) {\n        if (xoff >= 0 && yoff >= 0) {\n          si = (y * sw + x) << 2\n          ti = ((yoff + y) * tw + xoff + x) << 2\n        } else {\n          si = ((-yoff + y) * sw - xoff + x) << 2\n          ti = (y * tw + x) << 2\n        }\n\n        if (mode == 0) {\n          tb[ti] = sb[si]\n          tb[ti + 1] = sb[si + 1]\n          tb[ti + 2] = sb[si + 2]\n          tb[ti + 3] = sb[si + 3]\n        } else if (mode == 1) {\n          var fa = sb[si + 3] * (1 / 255),\n            fr = sb[si] * fa,\n            fg = sb[si + 1] * fa,\n            fb = sb[si + 2] * fa\n          var ba = tb[ti + 3] * (1 / 255),\n            br = tb[ti] * ba,\n            bg = tb[ti + 1] * ba,\n            bb = tb[ti + 2] * ba\n\n          var ifa = 1 - fa,\n            oa = fa + ba * ifa,\n            ioa = oa == 0 ? 0 : 1 / oa\n          tb[ti + 3] = 255 * oa\n          tb[ti + 0] = (fr + br * ifa) * ioa\n          tb[ti + 1] = (fg + bg * ifa) * ioa\n          tb[ti + 2] = (fb + bb * ifa) * ioa\n        } else if (mode == 2) {\n          // copy only differences, otherwise zero\n\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2]\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2]\n          if (fa == ba && fr == br && fg == bg && fb == bb) {\n            tb[ti] = 0\n            tb[ti + 1] = 0\n            tb[ti + 2] = 0\n            tb[ti + 3] = 0\n          } else {\n            tb[ti] = fr\n            tb[ti + 1] = fg\n            tb[ti + 2] = fb\n            tb[ti + 3] = fa\n          }\n        } else if (mode == 3) {\n          // check if can be blended\n\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2]\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2]\n          if (fa == ba && fr == br && fg == bg && fb == bb) continue\n          //if(fa!=255 && ba!=0) return false;\n          if (fa < 220 && ba > 20) return false\n        }\n      }\n    }\n\n    return true\n  }\n}\n\nclass RGBMLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n    this.maxRange = 7 // more information about this property at https://iwasbeingirony.blogspot.com/2010/06/difference-between-rgbm-and-rgbd.html\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  setMaxRange(value) {\n    this.maxRange = value\n    return this\n  }\n\n  loadCubemap(urls, onLoad, onProgress, onError) {\n    const texture = new CubeTexture()\n\n    let loaded = 0\n\n    const scope = this\n\n    function loadTexture(i) {\n      scope.load(\n        urls[i],\n        function (image) {\n          texture.images[i] = image\n\n          loaded++\n\n          if (loaded === 6) {\n            texture.needsUpdate = true\n\n            if (onLoad) onLoad(texture)\n          }\n        },\n        undefined,\n        onError,\n      )\n    }\n\n    for (let i = 0; i < urls.length; ++i) {\n      loadTexture(i)\n    }\n\n    texture.type = this.type\n    texture.format = RGBAFormat\n    texture.minFilter = LinearFilter\n    texture.generateMipmaps = false\n\n    return texture\n  }\n\n  parse(buffer) {\n    init()\n    const img = UPNG.decode(buffer)\n    const rgba = UPNG.toRGBA8(img)[0]\n\n    const data = new Uint8Array(rgba)\n    const size = img.width * img.height * 4\n\n    const output = this.type === HalfFloatType ? new Uint16Array(size) : new Float32Array(size)\n\n    // decode RGBM\n\n    for (let i = 0; i < data.length; i += 4) {\n      const r = data[i + 0] / 255\n      const g = data[i + 1] / 255\n      const b = data[i + 2] / 255\n      const a = data[i + 3] / 255\n\n      if (this.type === HalfFloatType) {\n        output[i + 0] = DataUtils.toHalfFloat(Math.min(r * a * this.maxRange, 65504))\n        output[i + 1] = DataUtils.toHalfFloat(Math.min(g * a * this.maxRange, 65504))\n        output[i + 2] = DataUtils.toHalfFloat(Math.min(b * a * this.maxRange, 65504))\n        output[i + 3] = DataUtils.toHalfFloat(1)\n      } else {\n        output[i + 0] = r * a * this.maxRange\n        output[i + 1] = g * a * this.maxRange\n        output[i + 2] = b * a * this.maxRange\n        output[i + 3] = 1\n      }\n    }\n\n    return {\n      width: img.width,\n      height: img.height,\n      data: output,\n      format: RGBAFormat,\n      type: this.type,\n      flipY: true,\n    }\n  }\n}\n\nexport { RGBMLoader }\n"], "mappings": ";AAEA,IAAIA,IAAA;AAEJ,SAASC,KAAA,EAAO;EACd,IAAID,IAAA,EAAM,OAAOA,IAAA;EAGjBA,IAAA,GAAO,CAAE;EAETA,IAAA,CAAKE,OAAA,GAAU,UAAUC,GAAA,EAAK;IAC5B,IAAIC,CAAA,GAAID,GAAA,CAAIE,KAAA;MACVC,CAAA,GAAIH,GAAA,CAAII,MAAA;IACV,IAAIJ,GAAA,CAAIK,IAAA,CAAKC,IAAA,IAAQ,MAAM,OAAO,CAACT,IAAA,CAAKE,OAAA,CAAQQ,WAAA,CAAYP,GAAA,CAAIQ,IAAA,EAAMP,CAAA,EAAGE,CAAA,EAAGH,GAAG,EAAES,MAAM;IAEvF,IAAIC,IAAA,GAAO,EAAE;IACb,IAAIV,GAAA,CAAIW,MAAA,CAAO,CAAC,EAAEH,IAAA,IAAQ,MAAMR,GAAA,CAAIW,MAAA,CAAO,CAAC,EAAEH,IAAA,GAAOR,GAAA,CAAIQ,IAAA;IAEzD,IAAII,GAAA,GAAMX,CAAA,GAAIE,CAAA,GAAI;MAChBU,GAAA,GAAM,IAAIC,UAAA,CAAWF,GAAG;MACxBG,KAAA,GAAQ,IAAID,UAAA,CAAWF,GAAG;MAC1BI,IAAA,GAAO,IAAIF,UAAA,CAAWF,GAAG;IAC3B,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIjB,GAAA,CAAIW,MAAA,CAAOO,MAAA,EAAQD,CAAA,IAAK;MAC1C,IAAIE,GAAA,GAAMnB,GAAA,CAAIW,MAAA,CAAOM,CAAC;MACtB,IAAIG,EAAA,GAAKD,GAAA,CAAIE,IAAA,CAAKC,CAAA;QAChBC,EAAA,GAAKJ,GAAA,CAAIE,IAAA,CAAKG,CAAA;QACdC,EAAA,GAAKN,GAAA,CAAIE,IAAA,CAAKnB,KAAA;QACdwB,EAAA,GAAKP,GAAA,CAAIE,IAAA,CAAKjB,MAAA;MAChB,IAAIuB,KAAA,GAAQ9B,IAAA,CAAKE,OAAA,CAAQQ,WAAA,CAAYY,GAAA,CAAIX,IAAA,EAAMiB,EAAA,EAAIC,EAAA,EAAI1B,GAAG;MAE1D,IAAIiB,CAAA,IAAK,GAAG,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAIhB,GAAA,EAAKgB,CAAA,IAAKZ,IAAA,CAAKY,CAAC,IAAIf,GAAA,CAAIe,CAAC;MAEzD,IAAIT,GAAA,CAAIU,KAAA,IAAS,GAAGhC,IAAA,CAAKiC,SAAA,CAAUH,KAAA,EAAOF,EAAA,EAAIC,EAAA,EAAIb,GAAA,EAAKZ,CAAA,EAAGE,CAAA,EAAGiB,EAAA,EAAIG,EAAA,EAAI,CAAC,WAC7DJ,GAAA,CAAIU,KAAA,IAAS,GAAGhC,IAAA,CAAKiC,SAAA,CAAUH,KAAA,EAAOF,EAAA,EAAIC,EAAA,EAAIb,GAAA,EAAKZ,CAAA,EAAGE,CAAA,EAAGiB,EAAA,EAAIG,EAAA,EAAI,CAAC;MAE3Eb,IAAA,CAAKqB,IAAA,CAAKlB,GAAA,CAAIJ,MAAA,CAAOuB,KAAA,CAAM,CAAC,CAAC;MAE7B,IAAIb,GAAA,CAAIc,OAAA,IAAW,GAAGpC,IAAA,CAAKiC,SAAA,CAAUf,KAAA,EAAOU,EAAA,EAAIC,EAAA,EAAIb,GAAA,EAAKZ,CAAA,EAAGE,CAAA,EAAGiB,EAAA,EAAIG,EAAA,EAAI,CAAC,WAC/DJ,GAAA,CAAIc,OAAA,IAAW,GAAG,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIhB,GAAA,EAAKgB,CAAA,IAAKf,GAAA,CAAIe,CAAC,IAAIZ,IAAA,CAAKY,CAAC;IACzE;IAED,OAAOlB,IAAA;EACR;EAEDb,IAAA,CAAKE,OAAA,CAAQQ,WAAA,GAAc,UAAUC,IAAA,EAAMP,CAAA,EAAGE,CAAA,EAAGH,GAAA,EAAK;IACpD,IAAIkC,IAAA,GAAOjC,CAAA,GAAIE,CAAA;MACbgC,GAAA,GAAMtC,IAAA,CAAKuC,MAAA,CAAOC,OAAA,CAAQrC,GAAG;IAC/B,IAAIsC,GAAA,GAAMC,IAAA,CAAKC,IAAA,CAAMvC,CAAA,GAAIkC,GAAA,GAAO,CAAC;IAEjC,IAAIM,EAAA,GAAK,IAAI3B,UAAA,CAAWoB,IAAA,GAAO,CAAC;MAC9BQ,IAAA,GAAO,IAAIC,WAAA,CAAYF,EAAA,CAAGhC,MAAM;IAClC,IAAImC,KAAA,GAAQ5C,GAAA,CAAI4C,KAAA;MACdC,KAAA,GAAQ7C,GAAA,CAAI6C,KAAA;IACd,IAAIC,EAAA,GAAKjD,IAAA,CAAKkD,IAAA,CAAKC,UAAA;IAEnB,IAAIJ,KAAA,IAAS,GAAG;MAGd,IAAIK,KAAA,GAAQf,IAAA,IAAQ;MACpB,IAAIW,KAAA,IAAS,GAAG;QACd,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIgC,KAAA,EAAOhC,CAAA,IAAK,GAAG;UACjCwB,EAAA,CAAGxB,CAAC,IAAIT,IAAA,CAAKS,CAAC;UACdwB,EAAA,CAAGxB,CAAA,GAAI,CAAC,IAAIT,IAAA,CAAKS,CAAA,GAAI,CAAC;UACtBwB,EAAA,CAAGxB,CAAA,GAAI,CAAC,IAAIT,IAAA,CAAKS,CAAA,GAAI,CAAC;UACtBwB,EAAA,CAAGxB,CAAA,GAAI,CAAC,IAAIT,IAAA,CAAKS,CAAA,GAAI,CAAC;QACvB;MACF;MAED,IAAI4B,KAAA,IAAS,IAAI;QACf,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIgC,KAAA,EAAOhC,CAAA,IAAK;UAC9BwB,EAAA,CAAGxB,CAAC,IAAIT,IAAA,CAAKS,CAAA,IAAK,CAAC;QACpB;MACF;IACP,WAAe2B,KAAA,IAAS,GAAG;MAGrB,IAAIM,EAAA,GAAKlD,GAAA,CAAIK,IAAA,CAAK,MAAM;MACxB,IAAI6C,EAAA,IAAM,MAAM;QACd,IAAIL,KAAA,IAAS,GAAG;UACd,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;YAC7B,IAAIkC,EAAA,GAAKlC,CAAA,GAAI;YACbyB,IAAA,CAAKzB,CAAC,IAAK,OAAO,KAAOT,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,KAAO3C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,IAAK3C,IAAA,CAAK2C,EAAE;UAC7E;QACF;QAED,IAAIN,KAAA,IAAS,IAAI;UACf,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;YAC7B,IAAIkC,EAAA,GAAKlC,CAAA,GAAI;YACbyB,IAAA,CAAKzB,CAAC,IAAK,OAAO,KAAOT,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,KAAO3C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,IAAK3C,IAAA,CAAK2C,EAAE;UAC7E;QACF;MACT,OAAa;QACL,IAAIC,EAAA,GAAKF,EAAA,CAAG,CAAC;UACXG,EAAA,GAAKH,EAAA,CAAG,CAAC;UACTI,EAAA,GAAKJ,EAAA,CAAG,CAAC;QACX,IAAIL,KAAA,IAAS,GAAG;UACd,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;YAC7B,IAAIsC,EAAA,GAAKtC,CAAA,IAAK;cACZkC,EAAA,GAAKlC,CAAA,GAAI;YACXyB,IAAA,CAAKzB,CAAC,IAAK,OAAO,KAAOT,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,KAAO3C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,IAAK3C,IAAA,CAAK2C,EAAE;YAC5E,IAAI3C,IAAA,CAAK2C,EAAE,KAAKC,EAAA,IAAM5C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAKE,EAAA,IAAM7C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAKG,EAAA,EAAIb,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI;UAC9E;QACF;QAED,IAAIV,KAAA,IAAS,IAAI;UACf,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;YAC7B,IAAIsC,EAAA,GAAKtC,CAAA,IAAK;cACZkC,EAAA,GAAKlC,CAAA,GAAI;YACXyB,IAAA,CAAKzB,CAAC,IAAK,OAAO,KAAOT,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,KAAO3C,IAAA,CAAK2C,EAAA,GAAK,CAAC,KAAK,IAAK3C,IAAA,CAAK2C,EAAE;YAC5E,IAAIL,EAAA,CAAGtC,IAAA,EAAM2C,EAAE,KAAKC,EAAA,IAAMN,EAAA,CAAGtC,IAAA,EAAM2C,EAAA,GAAK,CAAC,KAAKE,EAAA,IAAMP,EAAA,CAAGtC,IAAA,EAAM2C,EAAA,GAAK,CAAC,KAAKG,EAAA,EAAIb,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI;UAC1F;QACF;MACF;IACP,WAAeX,KAAA,IAAS,GAAG;MAGrB,IAAIY,CAAA,GAAIxD,GAAA,CAAIK,IAAA,CAAK,MAAM;QACrBoD,EAAA,GAAKzD,GAAA,CAAIK,IAAA,CAAK,MAAM;QACpBqD,EAAA,GAAKD,EAAA,GAAKA,EAAA,CAAGvC,MAAA,GAAS;MAExB,IAAI2B,KAAA,IAAS,GAAG;QACd,SAASrB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;UAC1B,IAAImC,EAAA,GAAKnC,CAAA,GAAIc,GAAA;YACXsB,EAAA,GAAKpC,CAAA,GAAIvB,CAAA;UACX,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAIhB,CAAA,EAAGgB,CAAA,IAAK;YAC1B,IAAIsC,EAAA,GAAMK,EAAA,GAAK3C,CAAA,IAAM;cACnBW,CAAA,GAAKpB,IAAA,CAAKmD,EAAA,IAAM1C,CAAA,IAAK,EAAE,KAAM,MAAMA,CAAA,GAAI,MAAM,KAAO;cACpD4C,EAAA,GAAK,IAAIjC,CAAA;YACXa,EAAA,CAAGc,EAAE,IAAIC,CAAA,CAAEK,EAAE;YACbpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI3B,CAAA,GAAI8B,EAAA,GAAKD,EAAA,CAAG7B,CAAC,IAAI;UAC/B;QACF;MACF;MAED,IAAIiB,KAAA,IAAS,GAAG;QACd,SAASrB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;UAC1B,IAAImC,EAAA,GAAKnC,CAAA,GAAIc,GAAA;YACXsB,EAAA,GAAKpC,CAAA,GAAIvB,CAAA;UACX,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAIhB,CAAA,EAAGgB,CAAA,IAAK;YAC1B,IAAIsC,EAAA,GAAMK,EAAA,GAAK3C,CAAA,IAAM;cACnBW,CAAA,GAAKpB,IAAA,CAAKmD,EAAA,IAAM1C,CAAA,IAAK,EAAE,KAAM,MAAMA,CAAA,GAAI,MAAM,KAAO;cACpD4C,EAAA,GAAK,IAAIjC,CAAA;YACXa,EAAA,CAAGc,EAAE,IAAIC,CAAA,CAAEK,EAAE;YACbpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI3B,CAAA,GAAI8B,EAAA,GAAKD,EAAA,CAAG7B,CAAC,IAAI;UAC/B;QACF;MACF;MAED,IAAIiB,KAAA,IAAS,GAAG;QACd,SAASrB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;UAC1B,IAAImC,EAAA,GAAKnC,CAAA,GAAIc,GAAA;YACXsB,EAAA,GAAKpC,CAAA,GAAIvB,CAAA;UACX,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAIhB,CAAA,EAAGgB,CAAA,IAAK;YAC1B,IAAIsC,EAAA,GAAMK,EAAA,GAAK3C,CAAA,IAAM;cACnBW,CAAA,GAAKpB,IAAA,CAAKmD,EAAA,IAAM1C,CAAA,IAAK,EAAE,KAAM,MAAMA,CAAA,GAAI,MAAM,KAAO;cACpD4C,EAAA,GAAK,IAAIjC,CAAA;YACXa,EAAA,CAAGc,EAAE,IAAIC,CAAA,CAAEK,EAAE;YACbpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;YACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI3B,CAAA,GAAI8B,EAAA,GAAKD,EAAA,CAAG7B,CAAC,IAAI;UAC/B;QACF;MACF;MAED,IAAIiB,KAAA,IAAS,GAAG;QACd,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;UAC7B,IAAIsC,EAAA,GAAKtC,CAAA,IAAK;YACZW,CAAA,GAAIpB,IAAA,CAAKS,CAAC;YACV4C,EAAA,GAAK,IAAIjC,CAAA;UACXa,EAAA,CAAGc,EAAE,IAAIC,CAAA,CAAEK,EAAE;UACbpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;UACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIC,CAAA,CAAEK,EAAA,GAAK,CAAC;UACrBpB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI3B,CAAA,GAAI8B,EAAA,GAAKD,EAAA,CAAG7B,CAAC,IAAI;QAC/B;MACF;IACP,WAAegB,KAAA,IAAS,GAAG;MAGrB,IAAIC,KAAA,IAAS,GAAG;QACd,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;UAC7B,IAAIsC,EAAA,GAAKtC,CAAA,IAAK;YACZ6C,EAAA,GAAK7C,CAAA,IAAK;YACV8C,EAAA,GAAKvD,IAAA,CAAKsD,EAAE;UACdrB,EAAA,CAAGc,EAAE,IAAIQ,EAAA;UACTtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIQ,EAAA;UACbtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIQ,EAAA;UACbtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI/C,IAAA,CAAKsD,EAAA,GAAK,CAAC;QACzB;MACF;MAED,IAAIjB,KAAA,IAAS,IAAI;QACf,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIiB,IAAA,EAAMjB,CAAA,IAAK;UAC7B,IAAIsC,EAAA,GAAKtC,CAAA,IAAK;YACZ6C,EAAA,GAAK7C,CAAA,IAAK;YACV8C,EAAA,GAAKvD,IAAA,CAAKsD,EAAE;UACdrB,EAAA,CAAGc,EAAE,IAAIQ,EAAA;UACTtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIQ,EAAA;UACbtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAIQ,EAAA;UACbtB,EAAA,CAAGc,EAAA,GAAK,CAAC,IAAI/C,IAAA,CAAKsD,EAAA,GAAK,CAAC;QACzB;MACF;IACP,WAAelB,KAAA,IAAS,GAAG;MAGrB,IAAIQ,EAAA,GAAKpD,GAAA,CAAIK,IAAA,CAAK,MAAM,IAAIL,GAAA,CAAIK,IAAA,CAAK,MAAM,IAAI;MAC/C,SAASmB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;QAC1B,IAAIwC,GAAA,GAAMxC,CAAA,GAAIc,GAAA;UACZ2B,EAAA,GAAKzC,CAAA,GAAIvB,CAAA;QACX,IAAI4C,KAAA,IAAS,GAAG;UACd,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;YAC1B,IAAIyC,EAAA,GAAK,OAAQvD,IAAA,CAAKwD,GAAA,IAAO1C,CAAA,KAAM,EAAE,MAAO,KAAKA,CAAA,GAAI,KAAO;cAC1D4C,EAAA,GAAKH,EAAA,IAAMX,EAAA,GAAK,MAAM,IAAI;YAC5BV,IAAA,CAAKuB,EAAA,GAAK3C,CAAC,IAAK4C,EAAA,IAAM,KAAOH,EAAA,IAAM,KAAOA,EAAA,IAAM,IAAKA,EAAA;UACtD;QACX,WAAmBlB,KAAA,IAAS,GAAG;UACrB,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;YAC1B,IAAIyC,EAAA,GAAK,MAAOvD,IAAA,CAAKwD,GAAA,IAAO1C,CAAA,KAAM,EAAE,MAAO,MAAMA,CAAA,GAAI,MAAM,KAAO;cAChE4C,EAAA,GAAKH,EAAA,IAAMX,EAAA,GAAK,KAAK,IAAI;YAC3BV,IAAA,CAAKuB,EAAA,GAAK3C,CAAC,IAAK4C,EAAA,IAAM,KAAOH,EAAA,IAAM,KAAOA,EAAA,IAAM,IAAKA,EAAA;UACtD;QACX,WAAmBlB,KAAA,IAAS,GAAG;UACrB,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;YAC1B,IAAIyC,EAAA,GAAK,MAAOvD,IAAA,CAAKwD,GAAA,IAAO1C,CAAA,KAAM,EAAE,MAAO,MAAMA,CAAA,GAAI,MAAM,KAAO;cAChE4C,EAAA,GAAKH,EAAA,IAAMX,EAAA,GAAK,KAAK,IAAI;YAC3BV,IAAA,CAAKuB,EAAA,GAAK3C,CAAC,IAAK4C,EAAA,IAAM,KAAOH,EAAA,IAAM,KAAOA,EAAA,IAAM,IAAKA,EAAA;UACtD;QACX,WAAmBlB,KAAA,IAAS,GAAG;UACrB,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;YAC1B,IAAIyC,EAAA,GAAKvD,IAAA,CAAKwD,GAAA,GAAM1C,CAAC;cACnB4C,EAAA,GAAKH,EAAA,IAAMX,EAAA,GAAK,IAAI;YACtBV,IAAA,CAAKuB,EAAA,GAAK3C,CAAC,IAAK4C,EAAA,IAAM,KAAOH,EAAA,IAAM,KAAOA,EAAA,IAAM,IAAKA,EAAA;UACtD;QACX,WAAmBlB,KAAA,IAAS,IAAI;UACtB,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;YAC1B,IAAIyC,EAAA,GAAKvD,IAAA,CAAKwD,GAAA,IAAO1C,CAAA,IAAK,EAAE;cAC1B4C,EAAA,GAAKpB,EAAA,CAAGtC,IAAA,EAAMwD,GAAA,IAAO1C,CAAA,IAAK,EAAE,KAAK8B,EAAA,GAAK,IAAI;YAC5CV,IAAA,CAAKuB,EAAA,GAAK3C,CAAC,IAAK4C,EAAA,IAAM,KAAOH,EAAA,IAAM,KAAOA,EAAA,IAAM,IAAKA,EAAA;UACtD;QACF;MACF;IACF;IAGD,OAAOtB,EAAA;EACR;EAED5C,IAAA,CAAKuC,MAAA,GAAS,UAAU+B,IAAA,EAAM;IAC5B,IAAI3D,IAAA,GAAO,IAAIM,UAAA,CAAWqD,IAAI;MAC5BC,MAAA,GAAS;MACTC,GAAA,GAAMxE,IAAA,CAAKkD,IAAA;MACXuB,GAAA,GAAMD,GAAA,CAAIrB,UAAA;MACVuB,GAAA,GAAMF,GAAA,CAAIG,QAAA;IACZ,IAAIxE,GAAA,GAAM;MAAEK,IAAA,EAAM;MAAIM,MAAA,EAAQ;IAAI;IAClC,IAAI8D,EAAA,GAAK,IAAI3D,UAAA,CAAWN,IAAA,CAAKU,MAAM;MACjCwD,IAAA,GAAO;IACT,IAAIC,EAAA;MACFC,IAAA,GAAO;IACT,IAAIC,IAAA,EAAMC,IAAA,EAAMC,GAAA;IAEhB,IAAIC,IAAA,GAAO,CAAC,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI;IAC1D,SAAS/D,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK,IAAIT,IAAA,CAAKS,CAAC,KAAK+D,IAAA,CAAK/D,CAAC,GAAG,MAAM,IAAIgE,KAAA,CAAM,8BAA8B;IAElG,OAAOb,MAAA,GAAS5D,IAAA,CAAKU,MAAA,EAAQ;MAC3B,IAAIN,GAAA,GAAMyD,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM;MACnCA,MAAA,IAAU;MACV,IAAIc,IAAA,GAAOb,GAAA,CAAIc,SAAA,CAAU3E,IAAA,EAAM4D,MAAA,EAAQ,CAAC;MACxCA,MAAA,IAAU;MAGV,IAAIc,IAAA,IAAQ,QAAQ;QAClBrF,IAAA,CAAKuC,MAAA,CAAOgD,KAAA,CAAM5E,IAAA,EAAM4D,MAAA,EAAQpE,GAAG;MAC3C,WAAiBkF,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI1E,IAAA,CAAKwB,KAAA,CAAMoC,MAAA,EAAQA,MAAA,GAAS,CAAC;MACtD,WAAiBc,IAAA,IAAQ,QAAQ;QACzB,SAASjE,CAAA,GAAI,GAAGA,CAAA,GAAIL,GAAA,EAAKK,CAAA,IAAKwD,EAAA,CAAGC,IAAA,GAAOzD,CAAC,IAAIT,IAAA,CAAK4D,MAAA,GAASnD,CAAC;QAC5DyD,IAAA,IAAQ9D,GAAA;MAChB,WAAiBsE,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI;UAAEG,UAAA,EAAYd,GAAA,CAAI/D,IAAA,EAAM4D,MAAM;UAAGkB,SAAA,EAAWf,GAAA,CAAI/D,IAAA,EAAM4D,MAAA,GAAS,CAAC;QAAG;QACpFO,EAAA,GAAK,IAAI7D,UAAA,CAAWN,IAAA,CAAKU,MAAM;MACvC,WAAiBgE,IAAA,IAAQ,QAAQ;QACzB,IAAIN,IAAA,IAAQ,GAAG;UACb,IAAIW,EAAA,GAAKvF,GAAA,CAAIW,MAAA,CAAOX,GAAA,CAAIW,MAAA,CAAOO,MAAA,GAAS,CAAC;UACzCqE,EAAA,CAAG/E,IAAA,GAAOX,IAAA,CAAKuC,MAAA,CAAOoD,WAAA,CAAYxF,GAAA,EAAK2E,EAAA,CAAG3C,KAAA,CAAM,GAAG4C,IAAI,GAAGW,EAAA,CAAGlE,IAAA,CAAKnB,KAAA,EAAOqF,EAAA,CAAGlE,IAAA,CAAKjB,MAAM;UACvFwE,IAAA,GAAO;QACR;QAED,IAAIa,GAAA,GAAM;UACRnE,CAAA,EAAGiD,GAAA,CAAI/D,IAAA,EAAM4D,MAAA,GAAS,EAAE;UACxB5C,CAAA,EAAG+C,GAAA,CAAI/D,IAAA,EAAM4D,MAAA,GAAS,EAAE;UACxBlE,KAAA,EAAOqE,GAAA,CAAI/D,IAAA,EAAM4D,MAAA,GAAS,CAAC;UAC3BhE,MAAA,EAAQmE,GAAA,CAAI/D,IAAA,EAAM4D,MAAA,GAAS,CAAC;QAC7B;QACD,IAAIsB,GAAA,GAAMpB,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,EAAE;QAC/BsB,GAAA,GAAMpB,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,EAAE,KAAKsB,GAAA,IAAO,IAAI,MAAMA,GAAA;QACjD,IAAIvE,GAAA,GAAM;UAAEE,IAAA,EAAMoE,GAAA;UAAKE,KAAA,EAAOpD,IAAA,CAAKqD,KAAA,CAAMF,GAAA,GAAM,GAAI;UAAGzD,OAAA,EAASzB,IAAA,CAAK4D,MAAA,GAAS,EAAE;UAAGvC,KAAA,EAAOrB,IAAA,CAAK4D,MAAA,GAAS,EAAE;QAAG;QAE5GpE,GAAA,CAAIW,MAAA,CAAOoB,IAAA,CAAKZ,GAAG;MAC3B,WAAiB+D,IAAA,IAAQ,QAAQ;QACzB,SAASjE,CAAA,GAAI,GAAGA,CAAA,GAAIL,GAAA,GAAM,GAAGK,CAAA,IAAK0D,EAAA,CAAGC,IAAA,GAAO3D,CAAC,IAAIT,IAAA,CAAK4D,MAAA,GAASnD,CAAA,GAAI,CAAC;QACpE2D,IAAA,IAAQhE,GAAA,GAAM;MACtB,WAAiBsE,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAACb,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM,GAAGC,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAA,GAAS,CAAC,GAAG5D,IAAA,CAAK4D,MAAA,GAAS,CAAC,CAAC;MACtG,WAAiBc,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,EAAE;QACnB,SAASjE,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAKjB,GAAA,CAAIK,IAAA,CAAK6E,IAAI,EAAEnD,IAAA,CAAKsC,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAA,GAASnD,CAAA,GAAI,CAAC,CAAC;MACnF,WAAUiE,IAAA,IAAQ,UAAUA,IAAA,IAAQ,QAAQ;QAC3C,IAAIlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,KAAK,MAAMlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAAE;QAC/C,IAAIW,EAAA,GAAKxB,GAAA,CAAIyB,QAAA,CAAStF,IAAA,EAAM4D,MAAM;QAClCU,IAAA,GAAOT,GAAA,CAAIc,SAAA,CAAU3E,IAAA,EAAM4D,MAAA,EAAQyB,EAAA,GAAKzB,MAAM;QAC9C,IAAIV,EAAA,GAAKU,MAAA,GAASxD,GAAA,GAAMiF,EAAA,GAAK;QAC7B,IAAIX,IAAA,IAAQ,QAAQ;UAClBL,IAAA,GAAOR,GAAA,CAAIc,SAAA,CAAU3E,IAAA,EAAMqF,EAAA,GAAK,GAAGnC,EAAE;QAC/C,OAAe;UACLqB,GAAA,GAAMlF,IAAA,CAAKuC,MAAA,CAAO2D,QAAA,CAASvF,IAAA,CAAKwB,KAAA,CAAM6D,EAAA,GAAK,GAAGA,EAAA,GAAK,IAAInC,EAAE,CAAC;UAC1DmB,IAAA,GAAOR,GAAA,CAAI2B,QAAA,CAASjB,GAAA,EAAK,GAAGA,GAAA,CAAI7D,MAAM;QACvC;QAEDlB,GAAA,CAAIK,IAAA,CAAK6E,IAAI,EAAEJ,IAAI,IAAID,IAAA;MAC/B,WAAiBK,IAAA,IAAQ,QAAQ;QACzB,IAAIlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,KAAK,MAAMlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAAE;QAC/C,IAAIW,EAAA,GAAK;UACP7B,GAAA,GAAMI,MAAA;QACRyB,EAAA,GAAKxB,GAAA,CAAIyB,QAAA,CAAStF,IAAA,EAAMwD,GAAG;QAC3Bc,IAAA,GAAOT,GAAA,CAAIc,SAAA,CAAU3E,IAAA,EAAMwD,GAAA,EAAK6B,EAAA,GAAK7B,GAAG;QACxCA,GAAA,GAAM6B,EAAA,GAAK;QACX,IAAII,KAAA,GAAQzF,IAAA,CAAKwD,GAAG;QACpBA,GAAA,IAAO;QACP6B,EAAA,GAAKxB,GAAA,CAAIyB,QAAA,CAAStF,IAAA,EAAMwD,GAAG;QAC3BK,GAAA,CAAIc,SAAA,CAAU3E,IAAA,EAAMwD,GAAA,EAAK6B,EAAA,GAAK7B,GAAG;QACjCA,GAAA,GAAM6B,EAAA,GAAK;QACXA,EAAA,GAAKxB,GAAA,CAAIyB,QAAA,CAAStF,IAAA,EAAMwD,GAAG;QAC3BK,GAAA,CAAI2B,QAAA,CAASxF,IAAA,EAAMwD,GAAA,EAAK6B,EAAA,GAAK7B,GAAG;QAChCA,GAAA,GAAM6B,EAAA,GAAK;QACX,IAAInC,EAAA,GAAK9C,GAAA,IAAOoD,GAAA,GAAMI,MAAA;QACtB,IAAI6B,KAAA,IAAS,GAAG;UACdpB,IAAA,GAAOR,GAAA,CAAI2B,QAAA,CAASxF,IAAA,EAAMwD,GAAA,EAAKN,EAAE;QAC3C,OAAe;UACLqB,GAAA,GAAMlF,IAAA,CAAKuC,MAAA,CAAO2D,QAAA,CAASvF,IAAA,CAAKwB,KAAA,CAAMgC,GAAA,EAAKA,GAAA,GAAMN,EAAE,CAAC;UACpDmB,IAAA,GAAOR,GAAA,CAAI2B,QAAA,CAASjB,GAAA,EAAK,GAAGA,GAAA,CAAI7D,MAAM;QACvC;QAEDlB,GAAA,CAAIK,IAAA,CAAK6E,IAAI,EAAEJ,IAAI,IAAID,IAAA;MAC/B,WAAiBK,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAIb,GAAA,CAAI6B,SAAA,CAAU1F,IAAA,EAAM4D,MAAA,EAAQxD,GAAG;MACxD,WAAiBsE,IAAA,IAAQ,QAAQ;QACzB,IAAIiB,EAAA,GAAKnG,GAAA,CAAIK,IAAA,CAAK,MAAM,EAAEa,MAAA,GAAS;QACnClB,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,EAAE;QACnB,SAASjE,CAAA,GAAI,GAAGA,CAAA,GAAIkF,EAAA,EAAIlF,CAAA,IAAKjB,GAAA,CAAIK,IAAA,CAAK6E,IAAI,EAAEnD,IAAA,CAAKuC,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAASnD,CAAA,GAAI,CAAC,CAAC;MAClF,WAAiBiE,IAAA,IAAQ,QAAQ;QACzB,IAAIlF,GAAA,CAAI4C,KAAA,IAAS,GAAG5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAIb,GAAA,CAAI6B,SAAA,CAAU1F,IAAA,EAAM4D,MAAA,EAAQxD,GAAG,WAC3DZ,GAAA,CAAI4C,KAAA,IAAS,GAAG5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAIZ,GAAA,CAAI9D,IAAA,EAAM4D,MAAM,WACjDpE,GAAA,CAAI4C,KAAA,IAAS,GAAG5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAACZ,GAAA,CAAI9D,IAAA,EAAM4D,MAAM,GAAGE,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,CAAC,GAAGE,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,CAAC,CAAC;MAElH,WAAiBc,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAIb,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM,IAAI;MACtD,WAAiBc,IAAA,IAAQ,QAAQ;QACzBlF,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI1E,IAAA,CAAK4D,MAAM;MACpC,WAAiBc,IAAA,IAAQ,QAAQ;QACzB,IAAIlF,GAAA,CAAI4C,KAAA,IAAS,KAAK5C,GAAA,CAAI4C,KAAA,IAAS,GAAG;UACpC5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAACZ,GAAA,CAAI9D,IAAA,EAAM4D,MAAM,CAAC;QAC7C,WAAmBpE,GAAA,CAAI4C,KAAA,IAAS,KAAK5C,GAAA,CAAI4C,KAAA,IAAS,GAAG;UAC3C5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI,CAACZ,GAAA,CAAI9D,IAAA,EAAM4D,MAAM,GAAGE,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,CAAC,GAAGE,GAAA,CAAI9D,IAAA,EAAM4D,MAAA,GAAS,CAAC,CAAC;QAC3F,WAAmBpE,GAAA,CAAI4C,KAAA,IAAS,GAAG;UACzB5C,GAAA,CAAIK,IAAA,CAAK6E,IAAI,IAAI1E,IAAA,CAAK4D,MAAM;QAC7B;MACT,WAAiBc,IAAA,IAAQ,QAAQ;QACzB;MACD;MAGDd,MAAA,IAAUxD,GAAA;MACVyD,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM;MACzBA,MAAA,IAAU;IACX;IAED,IAAIQ,IAAA,IAAQ,GAAG;MACb,IAAIW,EAAA,GAAKvF,GAAA,CAAIW,MAAA,CAAOX,GAAA,CAAIW,MAAA,CAAOO,MAAA,GAAS,CAAC;MACzCqE,EAAA,CAAG/E,IAAA,GAAOX,IAAA,CAAKuC,MAAA,CAAOoD,WAAA,CAAYxF,GAAA,EAAK2E,EAAA,CAAG3C,KAAA,CAAM,GAAG4C,IAAI,GAAGW,EAAA,CAAGlE,IAAA,CAAKnB,KAAA,EAAOqF,EAAA,CAAGlE,IAAA,CAAKjB,MAAM;IACxF;IAEDJ,GAAA,CAAIQ,IAAA,GAAOX,IAAA,CAAKuC,MAAA,CAAOoD,WAAA,CAAYxF,GAAA,EAAKyE,EAAA,EAAIzE,GAAA,CAAIE,KAAA,EAAOF,GAAA,CAAII,MAAM;IAEjE,OAAOJ,GAAA,CAAIoG,QAAA;IACX,OAAOpG,GAAA,CAAIqG,SAAA;IACX,OAAOrG,GAAA,CAAIsG,MAAA;IACX,OAAOtG,GAAA;EACR;EAEDH,IAAA,CAAKuC,MAAA,CAAOoD,WAAA,GAAc,UAAUxF,GAAA,EAAKyE,EAAA,EAAIxE,CAAA,EAAGE,CAAA,EAAG;IACjD,IAAIgC,GAAA,GAAMtC,IAAA,CAAKuC,MAAA,CAAOC,OAAA,CAAQrC,GAAG;MAC/BsC,GAAA,GAAMC,IAAA,CAAKC,IAAA,CAAMvC,CAAA,GAAIkC,GAAA,GAAO,CAAC;MAC7BgC,IAAA,GAAO,IAAIrD,UAAA,EAAYwB,GAAA,GAAM,IAAItC,GAAA,CAAIqG,SAAA,IAAalG,CAAC;IACrD,IAAIH,GAAA,CAAIK,IAAA,CAAK,MAAM,GAAGoE,EAAA,GAAK5E,IAAA,CAAK0G,UAAA,CAAW9B,EAAA,EAAIN,IAAI,OAC9CM,EAAA,GAAK5E,IAAA,CAAKuC,MAAA,CAAO2D,QAAA,CAAStB,EAAA,EAAIN,IAAI;IAEvC,IAAInE,GAAA,CAAIqG,SAAA,IAAa,GAAG5B,EAAA,GAAK5E,IAAA,CAAKuC,MAAA,CAAOoE,WAAA,CAAY/B,EAAA,EAAIzE,GAAA,EAAK,GAAGC,CAAA,EAAGE,CAAC,WAC5DH,GAAA,CAAIqG,SAAA,IAAa,GAAG5B,EAAA,GAAK5E,IAAA,CAAKuC,MAAA,CAAOqE,cAAA,CAAehC,EAAA,EAAIzE,GAAG;IAEpE,OAAOyE,EAAA;EACR;EAED5E,IAAA,CAAKuC,MAAA,CAAO2D,QAAA,GAAW,UAAUvF,IAAA,EAAM2D,IAAA,EAAM;IAC3C,IAAInE,GAAA,GAAMH,IAAA,CAAK,YAAY,EAAE,IAAIiB,UAAA,CAAWN,IAAA,CAAKC,MAAA,EAAQ,GAAGD,IAAA,CAAKU,MAAA,GAAS,CAAC,GAAGiD,IAAI;IAClF,OAAOnE,GAAA;EACR;EAEDH,IAAA,CAAK0G,UAAA,GAAc,YAAY;IAC7B,IAAIG,CAAA,GAAI,CAAE;IACVA,CAAA,CAAEA,CAAA,GAAI,CAAE;IACRA,CAAA,CAAEA,CAAA,CAAEC,CAAA,GAAI,UAAUA,CAAA,EAAGC,CAAA,EAAG;MACtB,IAAIC,CAAA,GAAI/F,UAAA;QACNG,CAAA,GAAI;QACJ6F,CAAA,GAAI;QACJC,CAAA,GAAI;QACJ5G,CAAA,GAAI;QACJ6G,CAAA,GAAI;QACJC,CAAA,GAAI;QACJC,CAAA,GAAI;QACJjH,CAAA,GAAI;QACJkH,CAAA,GAAI;QACJC,CAAA;QACAC,CAAA;MACF,IAAIV,CAAA,CAAE,CAAC,KAAK,KAAKA,CAAA,CAAE,CAAC,KAAK,GAAG,OAAOC,CAAA,GAAIA,CAAA,GAAI,IAAIC,CAAA,CAAE,CAAC;MAClD,IAAIS,CAAA,GAAIZ,CAAA,CAAEA,CAAA;QACRa,CAAA,GAAID,CAAA,CAAEE,CAAA;QACNC,CAAA,GAAIH,CAAA,CAAEI,CAAA;QACNC,CAAA,GAAIL,CAAA,CAAET,CAAA;QACNe,CAAA,GAAIN,CAAA,CAAEC,CAAA;QACNM,CAAA,GAAIP,CAAA,CAAEG,CAAA;QACNC,CAAA,GAAIJ,CAAA,CAAEQ,CAAA;QACNN,CAAA,GAAIF,CAAA,CAAER,CAAA;QACNgB,CAAA,GAAIlB,CAAA,IAAK;MACX,IAAIkB,CAAA,EAAGlB,CAAA,GAAI,IAAIC,CAAA,CAAGF,CAAA,CAAEzF,MAAA,KAAW,KAAM,CAAC;MACtC,OAAOD,CAAA,IAAK,GAAG;QACbA,CAAA,GAAIsG,CAAA,CAAEZ,CAAA,EAAGQ,CAAA,EAAG,CAAC;QACbL,CAAA,GAAIS,CAAA,CAAEZ,CAAA,EAAGQ,CAAA,GAAI,GAAG,CAAC;QACjBA,CAAA,IAAK;QACL,IAAIL,CAAA,IAAK,GAAG;UACV,KAAKK,CAAA,GAAI,MAAM,GAAGA,CAAA,IAAK,KAAKA,CAAA,GAAI;UAChC,IAAIY,CAAA,IAAKZ,CAAA,KAAM,KAAK;YAClBa,CAAA,GAAIrB,CAAA,CAAEoB,CAAA,GAAI,CAAC,IAAKpB,CAAA,CAAEoB,CAAA,GAAI,CAAC,KAAK;UAC9B,IAAID,CAAA,EAAGlB,CAAA,GAAIF,CAAA,CAAEA,CAAA,CAAEE,CAAA,CAAEA,CAAA,EAAG3G,CAAA,GAAI+H,CAAC;UACzBpB,CAAA,CAAEqB,GAAA,CAAI,IAAIpB,CAAA,CAAEF,CAAA,CAAElG,MAAA,EAAQkG,CAAA,CAAEuB,UAAA,GAAaH,CAAA,EAAGC,CAAC,GAAG/H,CAAC;UAC7CkH,CAAA,GAAKY,CAAA,GAAIC,CAAA,IAAM;UACf/H,CAAA,IAAK+H,CAAA;UACL;QACD;QAED,IAAIF,CAAA,EAAGlB,CAAA,GAAIF,CAAA,CAAEA,CAAA,CAAEE,CAAA,CAAEA,CAAA,EAAG3G,CAAA,IAAK,KAAK,GAAG;QACjC,IAAI6G,CAAA,IAAK,GAAG;UACVM,CAAA,GAAII,CAAA,CAAET,CAAA;UACNM,CAAA,GAAIG,CAAA,CAAErH,CAAA;UACN8G,CAAA,IAAK,KAAK,KAAK;UACfC,CAAA,IAAK,KAAK,KAAK;QAChB;QAED,IAAIJ,CAAA,IAAK,GAAG;UACVC,CAAA,GAAIU,CAAA,CAAEd,CAAA,EAAGQ,CAAA,EAAG,CAAC,IAAI;UACjBhH,CAAA,GAAIsH,CAAA,CAAEd,CAAA,EAAGQ,CAAA,GAAI,GAAG,CAAC,IAAI;UACrBH,CAAA,GAAIS,CAAA,CAAEd,CAAA,EAAGQ,CAAA,GAAI,IAAI,CAAC,IAAI;UACtBA,CAAA,IAAK;UACL,IAAIvF,CAAA,GAAI;UACR,SAASuG,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK,GAAG;YAC9BX,CAAA,CAAER,CAAA,CAAEmB,CAAC,IAAI;YACTX,CAAA,CAAER,CAAA,CAAEmB,CAAA,GAAI,CAAC,IAAI;UACd;UAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAInB,CAAA,EAAGmB,CAAA,IAAK;YAC1B,IAAIC,CAAA,GAAIX,CAAA,CAAEd,CAAA,EAAGQ,CAAA,GAAIgB,CAAA,GAAI,GAAG,CAAC;YACzBX,CAAA,CAAER,CAAA,EAAGQ,CAAA,CAAEP,CAAA,CAAEkB,CAAC,KAAK,KAAK,CAAC,IAAIC,CAAA;YACzB,IAAIA,CAAA,GAAIxG,CAAA,EAAGA,CAAA,GAAIwG,CAAA;UAChB;UAEDjB,CAAA,IAAK,IAAIH,CAAA;UACTY,CAAA,CAAEJ,CAAA,CAAER,CAAA,EAAGpF,CAAC;UACRiG,CAAA,CAAEL,CAAA,CAAER,CAAA,EAAGpF,CAAA,EAAG4F,CAAA,CAAEN,CAAC;UACbE,CAAA,GAAII,CAAA,CAAEvH,CAAA;UACNoH,CAAA,GAAIG,CAAA,CAAEL,CAAA;UACNA,CAAA,GAAIQ,CAAA,CAAEH,CAAA,CAAEN,CAAA,GAAI,KAAKtF,CAAA,IAAK,GAAGmF,CAAA,GAAI5G,CAAA,EAAGwG,CAAA,EAAGQ,CAAA,EAAGK,CAAA,CAAEJ,CAAC;UACzC,IAAIiB,CAAA,GAAIf,CAAA,CAAEA,CAAA,CAAEE,CAAA,CAAEJ,CAAA,EAAG,GAAGL,CAAA,EAAGS,CAAA,CAAEH,CAAC;UAC1BJ,CAAA,IAAK,KAAKoB,CAAA,IAAK;UACf,IAAIC,CAAA,GAAIhB,CAAA,CAAEA,CAAA,CAAEE,CAAA,CAAEJ,CAAA,EAAGL,CAAA,EAAG5G,CAAA,EAAGqH,CAAA,CAAEO,CAAC;UAC1Bb,CAAA,IAAK,KAAKoB,CAAA,IAAK;UACfV,CAAA,CAAEJ,CAAA,CAAEH,CAAA,EAAGgB,CAAC;UACRR,CAAA,CAAEL,CAAA,CAAEH,CAAA,EAAGgB,CAAA,EAAGjB,CAAC;UACXQ,CAAA,CAAEJ,CAAA,CAAEO,CAAA,EAAGO,CAAC;UACRT,CAAA,CAAEL,CAAA,CAAEO,CAAA,EAAGO,CAAA,EAAGjB,CAAC;QACZ;QAED,OAAO,MAAI;UACT,IAAIkB,CAAA,GAAInB,CAAA,CAAEM,CAAA,CAAEf,CAAA,EAAGQ,CAAC,IAAIF,CAAC;UACrBE,CAAA,IAAKoB,CAAA,GAAI;UACT,IAAI/E,CAAA,GAAI+E,CAAA,KAAM;UACd,IAAI/E,CAAA,KAAM,KAAK,GAAG;YAChBoD,CAAA,CAAE3G,CAAA,EAAG,IAAIuD,CAAA;UACrB,WAAqBA,CAAA,IAAK,KAAK;YACnB;UACZ,OAAiB;YACL,IAAIgF,CAAA,GAAIvI,CAAA,GAAIuD,CAAA,GAAI;YAChB,IAAIA,CAAA,GAAI,KAAK;cACX,IAAIiF,CAAA,GAAIjB,CAAA,CAAEQ,CAAA,CAAExE,CAAA,GAAI,GAAG;cACnBgF,CAAA,GAAIvI,CAAA,IAAKwI,CAAA,KAAM,KAAKhB,CAAA,CAAEd,CAAA,EAAGQ,CAAA,EAAGsB,CAAA,GAAI,CAAC;cACjCtB,CAAA,IAAKsB,CAAA,GAAI;YACV;YAED,IAAIC,CAAA,GAAIrB,CAAA,CAAEK,CAAA,CAAEf,CAAA,EAAGQ,CAAC,IAAID,CAAC;YACrBC,CAAA,IAAKuB,CAAA,GAAI;YACT,IAAIC,CAAA,GAAID,CAAA,KAAM;cACZE,CAAA,GAAIpB,CAAA,CAAEW,CAAA,CAAEQ,CAAC;cACTE,CAAA,IAAKD,CAAA,KAAM,KAAKrB,CAAA,CAAEZ,CAAA,EAAGQ,CAAA,EAAGyB,CAAA,GAAI,EAAE;YAChCzB,CAAA,IAAKyB,CAAA,GAAI;YACT,OAAO3I,CAAA,GAAIuI,CAAA,EAAG;cACZ5B,CAAA,CAAE3G,CAAC,IAAI2G,CAAA,CAAE3G,CAAA,KAAM4I,CAAC;cAChBjC,CAAA,CAAE3G,CAAC,IAAI2G,CAAA,CAAE3G,CAAA,KAAM4I,CAAC;cAChBjC,CAAA,CAAE3G,CAAC,IAAI2G,CAAA,CAAE3G,CAAA,KAAM4I,CAAC;cAChBjC,CAAA,CAAE3G,CAAC,IAAI2G,CAAA,CAAE3G,CAAA,KAAM4I,CAAC;YACjB;YAED5I,CAAA,GAAIuI,CAAA;UACL;QACF;MACF;MAED,OAAO5B,CAAA,CAAE1F,MAAA,IAAUjB,CAAA,GAAI2G,CAAA,GAAIA,CAAA,CAAE5E,KAAA,CAAM,GAAG/B,CAAC;IACxC;IAEDyG,CAAA,CAAEA,CAAA,CAAEE,CAAA,GAAI,UAAUD,CAAA,EAAGC,CAAA,EAAG;MACtB,IAAIC,CAAA,GAAIF,CAAA,CAAEzF,MAAA;MACV,IAAI0F,CAAA,IAAKC,CAAA,EAAG,OAAOF,CAAA;MACnB,IAAIW,CAAA,GAAI,IAAIxG,UAAA,CAAW+F,CAAA,IAAK,CAAC;MAC7BS,CAAA,CAAEW,GAAA,CAAItB,CAAA,EAAG,CAAC;MACV,OAAOW,CAAA;IACR;IAEDZ,CAAA,CAAEA,CAAA,CAAEG,CAAA,GAAI,UAAUF,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGS,CAAA,EAAGC,CAAA,EAAGE,CAAA,EAAG;MAClC,IAAIE,CAAA,GAAIjB,CAAA,CAAEA,CAAA,CAAEgB,CAAA;QACVE,CAAA,GAAIlB,CAAA,CAAEA,CAAA,CAAEoB,CAAA;QACRD,CAAA,GAAI;MACN,OAAOA,CAAA,GAAIhB,CAAA,EAAG;QACZ,IAAIa,CAAA,GAAIf,CAAA,CAAEiB,CAAA,CAAEN,CAAA,EAAGC,CAAC,IAAIX,CAAC;QACrBW,CAAA,IAAKG,CAAA,GAAI;QACT,IAAIF,CAAA,GAAIE,CAAA,KAAM;QACd,IAAIF,CAAA,IAAK,IAAI;UACXC,CAAA,CAAEI,CAAC,IAAIL,CAAA;UACPK,CAAA;QACV,OAAe;UACL,IAAIC,CAAA,GAAI;YACNhB,CAAA,GAAI;UACN,IAAIU,CAAA,IAAK,IAAI;YACXV,CAAA,GAAI,IAAIa,CAAA,CAAEL,CAAA,EAAGC,CAAA,EAAG,CAAC;YACjBA,CAAA,IAAK;YACLO,CAAA,GAAIL,CAAA,CAAEI,CAAA,GAAI,CAAC;UACvB,WAAqBL,CAAA,IAAK,IAAI;YAClBV,CAAA,GAAI,IAAIa,CAAA,CAAEL,CAAA,EAAGC,CAAA,EAAG,CAAC;YACjBA,CAAA,IAAK;UACjB,WAAqBC,CAAA,IAAK,IAAI;YAClBV,CAAA,GAAI,KAAKa,CAAA,CAAEL,CAAA,EAAGC,CAAA,EAAG,CAAC;YAClBA,CAAA,IAAK;UACN;UAED,IAAIR,CAAA,GAAIc,CAAA,GAAIf,CAAA;UACZ,OAAOe,CAAA,GAAId,CAAA,EAAG;YACZU,CAAA,CAAEI,CAAC,IAAIC,CAAA;YACPD,CAAA;UACD;QACF;MACF;MAED,OAAON,CAAA;IACR;IAEDb,CAAA,CAAEA,CAAA,CAAEY,CAAA,GAAI,UAAUX,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGS,CAAA,EAAG;MAC5B,IAAIC,CAAA,GAAI;QACNE,CAAA,GAAI;QACJE,CAAA,GAAIL,CAAA,CAAEpG,MAAA,KAAW;MACnB,OAAOuG,CAAA,GAAIZ,CAAA,EAAG;QACZ,IAAIe,CAAA,GAAIjB,CAAA,CAAEc,CAAA,GAAIb,CAAC;QACfU,CAAA,CAAEG,CAAA,IAAK,CAAC,IAAI;QACZH,CAAA,EAAGG,CAAA,IAAK,KAAK,CAAC,IAAIG,CAAA;QAClB,IAAIA,CAAA,GAAIL,CAAA,EAAGA,CAAA,GAAIK,CAAA;QACfH,CAAA;MACD;MAED,OAAOA,CAAA,GAAIE,CAAA,EAAG;QACZL,CAAA,CAAEG,CAAA,IAAK,CAAC,IAAI;QACZH,CAAA,EAAGG,CAAA,IAAK,KAAK,CAAC,IAAI;QAClBA,CAAA;MACD;MAED,OAAOF,CAAA;IACR;IAEDb,CAAA,CAAEA,CAAA,CAAEa,CAAA,GAAI,UAAUZ,CAAA,EAAGC,CAAA,EAAG;MACtB,IAAIC,CAAA,GAAIH,CAAA,CAAEA,CAAA,CAAEI,CAAA;QACVQ,CAAA,GAAIX,CAAA,CAAEzF,MAAA;QACNqG,CAAA;QACAE,CAAA;QACAE,CAAA;QACAC,CAAA;QACAC,CAAA;QACAH,CAAA,GAAIb,CAAA,CAAEjF,CAAA;MACR,SAASgG,CAAA,GAAI,GAAGA,CAAA,IAAKhB,CAAA,EAAGgB,CAAA,IAAKF,CAAA,CAAEE,CAAC,IAAI;MACpC,KAAKA,CAAA,GAAI,GAAGA,CAAA,GAAIN,CAAA,EAAGM,CAAA,IAAK,GAAGF,CAAA,CAAEf,CAAA,CAAEiB,CAAC,CAAC;MACjC,IAAIJ,CAAA,GAAIX,CAAA,CAAEuB,CAAA;MACVb,CAAA,GAAI;MACJG,CAAA,CAAE,CAAC,IAAI;MACP,KAAKD,CAAA,GAAI,GAAGA,CAAA,IAAKb,CAAA,EAAGa,CAAA,IAAK;QACvBF,CAAA,GAAKA,CAAA,GAAIG,CAAA,CAAED,CAAA,GAAI,CAAC,KAAM;QACtBD,CAAA,CAAEC,CAAC,IAAIF,CAAA;MACR;MAED,KAAKI,CAAA,GAAI,GAAGA,CAAA,GAAIL,CAAA,EAAGK,CAAA,IAAK,GAAG;QACzBE,CAAA,GAAIlB,CAAA,CAAEgB,CAAA,GAAI,CAAC;QACX,IAAIE,CAAA,IAAK,GAAG;UACVlB,CAAA,CAAEgB,CAAC,IAAIH,CAAA,CAAEK,CAAC;UACVL,CAAA,CAAEK,CAAC;QACJ;MACF;IACF;IAEDnB,CAAA,CAAEA,CAAA,CAAEe,CAAA,GAAI,UAAUd,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACzB,IAAIS,CAAA,GAAIX,CAAA,CAAEzF,MAAA;QACRqG,CAAA,GAAIb,CAAA,CAAEA,CAAA,CAAEI,CAAA;QACRW,CAAA,GAAIF,CAAA,CAAEc,CAAA;MACR,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAIL,CAAA,EAAGK,CAAA,IAAK,GAAG;QAC7B,IAAIhB,CAAA,CAAEgB,CAAA,GAAI,CAAC,KAAK,GAAG;UACjB,IAAIC,CAAA,GAAID,CAAA,IAAK;YACXE,CAAA,GAAIlB,CAAA,CAAEgB,CAAA,GAAI,CAAC;YACXD,CAAA,GAAKE,CAAA,IAAK,IAAKC,CAAA;YACfL,CAAA,GAAIZ,CAAA,GAAIiB,CAAA;YACRC,CAAA,GAAInB,CAAA,CAAEgB,CAAC,KAAKH,CAAA;YACZV,CAAA,GAAIgB,CAAA,IAAK,KAAKN,CAAA;UAChB,OAAOM,CAAA,IAAKhB,CAAA,EAAG;YACb,IAAIC,CAAA,GAAIU,CAAA,CAAEK,CAAC,MAAO,KAAKlB,CAAA;YACvBC,CAAA,CAAEE,CAAC,IAAIW,CAAA;YACPI,CAAA;UACD;QACF;MACF;IACF;IAEDpB,CAAA,CAAEA,CAAA,CAAEiB,CAAA,GAAI,UAAUhB,CAAA,EAAGC,CAAA,EAAG;MACtB,IAAIC,CAAA,GAAIH,CAAA,CAAEA,CAAA,CAAEI,CAAA,CAAEuB,CAAA;QACZf,CAAA,GAAI,KAAKV,CAAA;MACX,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAIZ,CAAA,CAAEzF,MAAA,EAAQqG,CAAA,IAAK,GAAG;QACpC,IAAIE,CAAA,GAAId,CAAA,CAAEY,CAAC,KAAMX,CAAA,GAAID,CAAA,CAAEY,CAAA,GAAI,CAAC;QAC5BZ,CAAA,CAAEY,CAAC,IAAIV,CAAA,CAAEY,CAAC,MAAMH,CAAA;MACjB;IACF;IAEDZ,CAAA,CAAEA,CAAA,CAAEkB,CAAA,GAAI,UAAUjB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACzBA,CAAA,GAAIA,CAAA,KAAMD,CAAA,GAAI;MACd,IAAIU,CAAA,GAAIV,CAAA,KAAM;MACdD,CAAA,CAAEW,CAAC,KAAKT,CAAA;MACRF,CAAA,CAAEW,CAAA,GAAI,CAAC,KAAKT,CAAA,KAAM;IACnB;IAEDH,CAAA,CAAEA,CAAA,CAAEmB,CAAA,GAAI,UAAUlB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACzBA,CAAA,GAAIA,CAAA,KAAMD,CAAA,GAAI;MACd,IAAIU,CAAA,GAAIV,CAAA,KAAM;MACdD,CAAA,CAAEW,CAAC,KAAKT,CAAA;MACRF,CAAA,CAAEW,CAAA,GAAI,CAAC,KAAKT,CAAA,KAAM;MAClBF,CAAA,CAAEW,CAAA,GAAI,CAAC,KAAKT,CAAA,KAAM;IACnB;IAEDH,CAAA,CAAEA,CAAA,CAAEgB,CAAA,GAAI,UAAUf,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACzB,QAASF,CAAA,CAAEC,CAAA,KAAM,CAAC,IAAKD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,QAASA,CAAA,GAAI,MAAQ,KAAKC,CAAA,IAAK;IAC3E;IAEDH,CAAA,CAAEA,CAAA,CAAEc,CAAA,GAAI,UAAUb,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACzB,QAASF,CAAA,CAAEC,CAAA,KAAM,CAAC,IAAKD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,IAAMD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,SAAUA,CAAA,GAAI,MAAQ,KAAKC,CAAA,IAAK;IACtG;IAEDH,CAAA,CAAEA,CAAA,CAAEoB,CAAA,GAAI,UAAUnB,CAAA,EAAGC,CAAA,EAAG;MACtB,QAAQD,CAAA,CAAEC,CAAA,KAAM,CAAC,IAAKD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,IAAMD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,SAAUA,CAAA,GAAI;IACnF;IAEDF,CAAA,CAAEA,CAAA,CAAEzF,CAAA,GAAI,UAAU0F,CAAA,EAAGC,CAAA,EAAG;MACtB,QAAQD,CAAA,CAAEC,CAAA,KAAM,CAAC,IAAKD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,IAAMD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,KAAOD,CAAA,EAAGC,CAAA,KAAM,KAAK,CAAC,KAAK,SAAUA,CAAA,GAAI;IAC9G;IAEDF,CAAA,CAAEA,CAAA,CAAEI,CAAA,GAAK,YAAY;MACnB,IAAIH,CAAA,GAAImC,WAAA;QACNlC,CAAA,GAAIjE,WAAA;MACN,OAAO;QACLyF,CAAA,EAAG,IAAIzB,CAAA,CAAE,EAAE;QACX/E,CAAA,EAAG,IAAI+E,CAAA,CAAE,EAAE;QACXM,CAAA,EAAG,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;QACpEqB,CAAA,EAAG,CACD,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACD;QACDC,CAAA,EAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;QAClGP,CAAA,EAAG,IAAIrB,CAAA,CAAE,EAAE;QACXnD,CAAA,EAAG,CACD,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,OACA,OACA,MACD;QACDgF,CAAA,EAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;QAC1GL,CAAA,EAAG,IAAIvB,CAAA,CAAE,EAAE;QACXG,CAAA,EAAG,IAAIJ,CAAA,CAAE,GAAG;QACZ8B,CAAA,EAAG,EAAE;QACLtI,CAAA,EAAG,IAAIwG,CAAA,CAAE,EAAE;QACX+B,CAAA,EAAG,EAAE;QACLzI,CAAA,EAAG,IAAI0G,CAAA,CAAE,KAAK;QACdU,CAAA,EAAG,EAAE;QACLD,CAAA,EAAG,EAAE;QACLD,CAAA,EAAG,IAAIR,CAAA,CAAE,KAAK;QACdoB,CAAA,EAAG,EAAE;QACLb,CAAA,EAAG,IAAIP,CAAA,CAAE,GAAG;QACZK,CAAA,EAAG,EAAE;QACLqB,CAAA,EAAG,IAAI1B,CAAA,CAAE,KAAK,EAAE;QAChBgC,CAAA,EAAG,IAAI/B,CAAA,CAAE,GAAG;QACZgC,CAAA,EAAG,IAAIhC,CAAA,CAAE,EAAE;QACXiC,CAAA,EAAG,IAAIjC,CAAA,CAAE,EAAE;QACXmC,CAAA,EAAG,IAAInC,CAAA,CAAE,IAAI;QACboC,CAAA,EAAG,IAAIrC,CAAA,CAAE,KAAK,EAAE;QAChBsC,CAAA,EAAG,IAAItC,CAAA,CAAE,KAAK,EAAE;MACjB;IACP,EAAQ;IACH,CAAC,YAAY;MACZ,IAAIA,CAAA,GAAID,CAAA,CAAEA,CAAA,CAAEI,CAAA;QACVF,CAAA,GAAI,KAAK;MACX,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,EAAGC,CAAA,IAAK;QAC1B,IAAIS,CAAA,GAAIT,CAAA;QACRS,CAAA,IAAMA,CAAA,GAAI,gBAAgB,KAAOA,CAAA,GAAI,eAAe;QACpDA,CAAA,IAAMA,CAAA,GAAI,gBAAgB,KAAOA,CAAA,GAAI,cAAc;QACnDA,CAAA,IAAMA,CAAA,GAAI,gBAAgB,KAAOA,CAAA,GAAI,cAAc;QACnDA,CAAA,IAAMA,CAAA,GAAI,gBAAgB,KAAOA,CAAA,GAAI,aAAa;QAClDX,CAAA,CAAE0B,CAAA,CAAExB,CAAC,KAAMS,CAAA,KAAM,KAAOA,CAAA,IAAK,QAAS;MACvC;MAED,SAASC,EAAEE,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAG;QAClB,OAAOD,CAAA,MAAO,GAAGF,CAAA,CAAE1F,IAAA,CAAK,GAAG6F,CAAC;MAC7B;MAED,SAASf,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK;QAC3BF,CAAA,CAAEqB,CAAA,CAAEnB,CAAC,IAAKF,CAAA,CAAE2B,CAAA,CAAEzB,CAAC,KAAK,IAAKF,CAAA,CAAE4B,CAAA,CAAE1B,CAAC;QAC9BF,CAAA,CAAEwB,CAAA,CAAEtB,CAAC,IAAKF,CAAA,CAAEnD,CAAA,CAAEqD,CAAC,KAAK,IAAKF,CAAA,CAAE6B,CAAA,CAAE3B,CAAC;MAC/B;MAEDU,CAAA,CAAEZ,CAAA,CAAE8B,CAAA,EAAG,KAAK,CAAC;MACblB,CAAA,CAAEZ,CAAA,CAAE8B,CAAA,EAAG,MAAM,KAAK,CAAC;MACnBlB,CAAA,CAAEZ,CAAA,CAAE8B,CAAA,EAAG,MAAM,KAAK,CAAC;MACnBlB,CAAA,CAAEZ,CAAA,CAAE8B,CAAA,EAAG,MAAM,KAAK,CAAC;MACnB/B,CAAA,CAAEA,CAAA,CAAEa,CAAA,CAAEZ,CAAA,CAAE8B,CAAA,EAAG,CAAC;MACZ/B,CAAA,CAAEA,CAAA,CAAEe,CAAA,CAAEd,CAAA,CAAE8B,CAAA,EAAG,GAAG9B,CAAA,CAAEI,CAAC;MACjBL,CAAA,CAAEA,CAAA,CAAEiB,CAAA,CAAEhB,CAAA,CAAE8B,CAAA,EAAG,CAAC;MACZlB,CAAA,CAAEZ,CAAA,CAAE+B,CAAA,EAAG,IAAI,CAAC;MACZhC,CAAA,CAAEA,CAAA,CAAEa,CAAA,CAAEZ,CAAA,CAAE+B,CAAA,EAAG,CAAC;MACZhC,CAAA,CAAEA,CAAA,CAAEe,CAAA,CAAEd,CAAA,CAAE+B,CAAA,EAAG,GAAG/B,CAAA,CAAExG,CAAC;MACjBuG,CAAA,CAAEA,CAAA,CAAEiB,CAAA,CAAEhB,CAAA,CAAE+B,CAAA,EAAG,CAAC;MACZnB,CAAA,CAAEZ,CAAA,CAAEK,CAAA,EAAG,IAAI,CAAC;MACZO,CAAA,CAAEZ,CAAA,CAAEU,CAAA,EAAG,KAAK,CAAC;MACbE,CAAA,CAAEZ,CAAA,CAAEoB,CAAA,EAAG,IAAI,CAAC;MACZR,CAAA,CAAEZ,CAAA,CAAES,CAAA,EAAG,KAAK,CAAC;IACnB,GAAQ;IAEJ,OAAOV,CAAA,CAAEA,CAAA,CAAEC,CAAA;EACf,EAAM;EAEJ9G,IAAA,CAAKuC,MAAA,CAAOqE,cAAA,GAAiB,UAAUjG,IAAA,EAAMR,GAAA,EAAK;IAChD,IAAIC,CAAA,GAAID,GAAA,CAAIE,KAAA;MACVC,CAAA,GAAIH,GAAA,CAAII,MAAA;IACV,IAAI+B,GAAA,GAAMtC,IAAA,CAAKuC,MAAA,CAAOC,OAAA,CAAQrC,GAAG;MAC/BkJ,IAAA,GAAO/G,GAAA,IAAO;MACdG,GAAA,GAAMC,IAAA,CAAKC,IAAA,CAAMvC,CAAA,GAAIkC,GAAA,GAAO,CAAC;IAC/B,IAAItB,GAAA,GAAM,IAAIC,UAAA,CAAWX,CAAA,GAAImC,GAAG;IAChC,IAAIwB,EAAA,GAAK;IAET,IAAIqF,YAAA,GAAe,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACvC,IAAIC,YAAA,GAAe,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACvC,IAAIC,aAAA,GAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACxC,IAAIC,aAAA,GAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAExC,IAAIC,IAAA,GAAO;IACX,OAAOA,IAAA,GAAO,GAAG;MACf,IAAIC,EAAA,GAAKH,aAAA,CAAcE,IAAI;QACzBE,EAAA,GAAKH,aAAA,CAAcC,IAAI;MACzB,IAAIG,EAAA,GAAK;QACPC,EAAA,GAAK;MACP,IAAIC,EAAA,GAAKT,YAAA,CAAaI,IAAI;MAC1B,OAAOK,EAAA,GAAKzJ,CAAA,EAAG;QACbyJ,EAAA,IAAMJ,EAAA;QACNG,EAAA;MACD;MAED,IAAIE,EAAA,GAAKT,YAAA,CAAaG,IAAI;MAC1B,OAAOM,EAAA,GAAK5J,CAAA,EAAG;QACb4J,EAAA,IAAMJ,EAAA;QACNC,EAAA;MACD;MAED,IAAII,IAAA,GAAOvH,IAAA,CAAKC,IAAA,CAAMkH,EAAA,GAAKvH,GAAA,GAAO,CAAC;MACnCtC,IAAA,CAAKuC,MAAA,CAAOoE,WAAA,CAAYhG,IAAA,EAAMR,GAAA,EAAK8D,EAAA,EAAI4F,EAAA,EAAIC,EAAE;MAE7C,IAAInI,CAAA,GAAI;QACNuI,GAAA,GAAMZ,YAAA,CAAaI,IAAI;MACzB,IAAIS,GAAA;MAEJ,OAAOD,GAAA,GAAM5J,CAAA,EAAG;QACd,IAAI8J,GAAA,GAAMb,YAAA,CAAaG,IAAI;QAC3B,IAAIW,GAAA,GAAOpG,EAAA,GAAKtC,CAAA,GAAIsI,IAAA,IAAS;QAE7B,OAAOG,GAAA,GAAMhK,CAAA,EAAG;UACd,IAAIkC,GAAA,IAAO,GAAG;YACZ6H,GAAA,GAAMxJ,IAAA,CAAK0J,GAAA,IAAO,CAAC;YACnBF,GAAA,GAAOA,GAAA,IAAQ,KAAKE,GAAA,GAAM,KAAO;YACjCrJ,GAAA,CAAIkJ,GAAA,GAAMzH,GAAA,IAAO2H,GAAA,IAAO,EAAE,KAAKD,GAAA,IAAQ,MAAMC,GAAA,GAAM,MAAM;UAC1D;UAED,IAAI9H,GAAA,IAAO,GAAG;YACZ6H,GAAA,GAAMxJ,IAAA,CAAK0J,GAAA,IAAO,CAAC;YACnBF,GAAA,GAAOA,GAAA,IAAQ,KAAKE,GAAA,GAAM,KAAO;YACjCrJ,GAAA,CAAIkJ,GAAA,GAAMzH,GAAA,IAAO2H,GAAA,IAAO,EAAE,KAAKD,GAAA,IAAQ,MAAMC,GAAA,GAAM,MAAM;UAC1D;UAED,IAAI9H,GAAA,IAAO,GAAG;YACZ6H,GAAA,GAAMxJ,IAAA,CAAK0J,GAAA,IAAO,CAAC;YACnBF,GAAA,GAAOA,GAAA,IAAQ,KAAKE,GAAA,GAAM,KAAO;YACjCrJ,GAAA,CAAIkJ,GAAA,GAAMzH,GAAA,IAAO2H,GAAA,IAAO,EAAE,KAAKD,GAAA,IAAQ,MAAMC,GAAA,GAAM,MAAM;UAC1D;UAED,IAAI9H,GAAA,IAAO,GAAG;YACZ,IAAIgI,EAAA,GAAKJ,GAAA,GAAMzH,GAAA,GAAM2H,GAAA,GAAMf,IAAA;YAC3B,SAAStH,CAAA,GAAI,GAAGA,CAAA,GAAIsH,IAAA,EAAMtH,CAAA,IAAKf,GAAA,CAAIsJ,EAAA,GAAKvI,CAAC,IAAIpB,IAAA,EAAM0J,GAAA,IAAO,KAAKtI,CAAC;UACjE;UAEDsI,GAAA,IAAO/H,GAAA;UACP8H,GAAA,IAAOR,EAAA;QACR;QAEDjI,CAAA;QACAuI,GAAA,IAAOP,EAAA;MACR;MAED,IAAIE,EAAA,GAAKC,EAAA,IAAM,GAAG7F,EAAA,IAAM6F,EAAA,IAAM,IAAIG,IAAA;MAClCP,IAAA,GAAOA,IAAA,GAAO;IACf;IAED,OAAO1I,GAAA;EACR;EAEDhB,IAAA,CAAKuC,MAAA,CAAOC,OAAA,GAAU,UAAUrC,GAAA,EAAK;IACnC,IAAIoK,GAAA,GAAM,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,EAAEpK,GAAA,CAAI4C,KAAK;IAC/C,OAAOwH,GAAA,GAAMpK,GAAA,CAAI6C,KAAA;EAClB;EAEDhD,IAAA,CAAKuC,MAAA,CAAOoE,WAAA,GAAc,UAAUhG,IAAA,EAAMR,GAAA,EAAKgE,GAAA,EAAK/D,CAAA,EAAGE,CAAA,EAAG;IACxD,IAAIgC,GAAA,GAAMtC,IAAA,CAAKuC,MAAA,CAAOC,OAAA,CAAQrC,GAAG;MAC/BsC,GAAA,GAAMC,IAAA,CAAKC,IAAA,CAAMvC,CAAA,GAAIkC,GAAA,GAAO,CAAC;MAC7BkI,KAAA,GAAQxK,IAAA,CAAKuC,MAAA,CAAOkI,MAAA;IACtBnI,GAAA,GAAMI,IAAA,CAAKC,IAAA,CAAKL,GAAA,GAAM,CAAC;IAEvB,IAAIlB,CAAA;MACF6C,EAAA;MACAoB,IAAA,GAAO1E,IAAA,CAAKwD,GAAG;MACf1C,CAAA,GAAI;IAEN,IAAI4D,IAAA,GAAO,GAAG1E,IAAA,CAAKwD,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAEkB,IAAA,GAAO,CAAC;IAC5C,IAAIA,IAAA,IAAQ,GAAG,KAAK5D,CAAA,GAAIa,GAAA,EAAKb,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAKd,IAAA,CAAKc,CAAA,GAAI,CAAC,IAAKd,IAAA,CAAKc,CAAA,GAAI,CAAC,KAAKd,IAAA,CAAKc,CAAA,GAAI,IAAIa,GAAG,MAAM,KAAM;IAErG,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;MAC1BP,CAAA,GAAI+C,GAAA,GAAMxC,CAAA,GAAIc,GAAA;MACdwB,EAAA,GAAK7C,CAAA,GAAIO,CAAA,GAAI;MACb0D,IAAA,GAAO1E,IAAA,CAAKsD,EAAA,GAAK,CAAC;MAClBxC,CAAA,GAAI;MAEJ,IAAI4D,IAAA,IAAQ,GAAG;QACb,OAAO5D,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC;MACtD,WAAiB4D,IAAA,IAAQ,GAAG;QACpB,OAAO5D,CAAA,GAAIa,GAAA,EAAKb,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC;QAC9C,OAAOA,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,IAAId,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIa,GAAG;MAC1E,WAAiB+C,IAAA,IAAQ,GAAG;QACpB,OAAO5D,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,IAAId,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIgB,GAAG;MAC1E,WAAiB4C,IAAA,IAAQ,GAAG;QACpB,OAAO5D,CAAA,GAAIa,GAAA,EAAKb,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,KAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIgB,GAAG,MAAM;QACzE,OAAOhB,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,KAAMd,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIgB,GAAG,IAAI9B,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIa,GAAG,MAAO;MACvG,OAAa;QACL,OAAOb,CAAA,GAAIa,GAAA,EAAKb,CAAA,IAAKd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,IAAI+I,KAAA,CAAM,GAAG7J,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIgB,GAAG,GAAG,CAAC;QAC/E,OAAOhB,CAAA,GAAIgB,GAAA,EAAKhB,CAAA,IAAK;UACnBd,IAAA,CAAKS,CAAA,GAAIK,CAAC,IAAId,IAAA,CAAKsD,EAAA,GAAKxC,CAAC,IAAI+I,KAAA,CAAM7J,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIa,GAAG,GAAG3B,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIgB,GAAG,GAAG9B,IAAA,CAAKS,CAAA,GAAIK,CAAA,GAAIa,GAAA,GAAMG,GAAG,CAAC;QACjG;MACF;IACF;IAED,OAAO9B,IAAA;EACR;EAEDX,IAAA,CAAKuC,MAAA,CAAOkI,MAAA,GAAS,UAAUzB,CAAA,EAAGrB,CAAA,EAAGW,CAAA,EAAG;IACtC,IAAI3E,CAAA,GAAIqF,CAAA,GAAIrB,CAAA,GAAIW,CAAA;MACdoC,EAAA,GAAK/G,CAAA,GAAIqF,CAAA;MACT2B,EAAA,GAAKhH,CAAA,GAAIgE,CAAA;MACTiD,EAAA,GAAKjH,CAAA,GAAI2E,CAAA;IACX,IAAIoC,EAAA,GAAKA,EAAA,IAAMC,EAAA,GAAKA,EAAA,IAAMD,EAAA,GAAKA,EAAA,IAAME,EAAA,GAAKA,EAAA,EAAI,OAAO5B,CAAA,UAC5C2B,EAAA,GAAKA,EAAA,IAAMC,EAAA,GAAKA,EAAA,EAAI,OAAOjD,CAAA;IACpC,OAAOW,CAAA;EACR;EAEDtI,IAAA,CAAKuC,MAAA,CAAOgD,KAAA,GAAQ,UAAU5E,IAAA,EAAM4D,MAAA,EAAQpE,GAAA,EAAK;IAC/C,IAAIqE,GAAA,GAAMxE,IAAA,CAAKkD,IAAA;IACf/C,GAAA,CAAIE,KAAA,GAAQmE,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM;IACrCA,MAAA,IAAU;IACVpE,GAAA,CAAII,MAAA,GAASiE,GAAA,CAAIG,QAAA,CAAShE,IAAA,EAAM4D,MAAM;IACtCA,MAAA,IAAU;IACVpE,GAAA,CAAI6C,KAAA,GAAQrC,IAAA,CAAK4D,MAAM;IACvBA,MAAA;IACApE,GAAA,CAAI4C,KAAA,GAAQpC,IAAA,CAAK4D,MAAM;IACvBA,MAAA;IACApE,GAAA,CAAIoG,QAAA,GAAW5F,IAAA,CAAK4D,MAAM;IAC1BA,MAAA;IACApE,GAAA,CAAIsG,MAAA,GAAS9F,IAAA,CAAK4D,MAAM;IACxBA,MAAA;IACApE,GAAA,CAAIqG,SAAA,GAAY7F,IAAA,CAAK4D,MAAM;IAC3BA,MAAA;EACD;EAEDvE,IAAA,CAAKkD,IAAA,GAAO;IACV+C,QAAA,EAAU,SAAAA,CAAUtF,IAAA,EAAMgD,CAAA,EAAG;MAC3B,OAAOhD,IAAA,CAAKgD,CAAC,KAAK,GAAGA,CAAA;MACrB,OAAOA,CAAA;IACR;IACDR,UAAA,EAAY,SAAAA,CAAUmB,IAAA,EAAMX,CAAA,EAAG;MAC7B,OAAQW,IAAA,CAAKX,CAAC,KAAK,IAAKW,IAAA,CAAKX,CAAA,GAAI,CAAC;IACnC;IACDkH,WAAA,EAAa,SAAAA,CAAUvG,IAAA,EAAMX,CAAA,EAAG+D,CAAA,EAAG;MACjCpD,IAAA,CAAKX,CAAC,IAAK+D,CAAA,IAAK,IAAK;MACrBpD,IAAA,CAAKX,CAAA,GAAI,CAAC,IAAI+D,CAAA,GAAI;IACnB;IACD/C,QAAA,EAAU,SAAAA,CAAUL,IAAA,EAAMX,CAAA,EAAG;MAC3B,OAAOW,IAAA,CAAKX,CAAC,KAAK,MAAM,MAAM,QAASW,IAAA,CAAKX,CAAA,GAAI,CAAC,KAAK,KAAOW,IAAA,CAAKX,CAAA,GAAI,CAAC,KAAK,IAAKW,IAAA,CAAKX,CAAA,GAAI,CAAC;IAC5F;IACDmH,SAAA,EAAW,SAAAA,CAAUxG,IAAA,EAAMX,CAAA,EAAG+D,CAAA,EAAG;MAC/BpD,IAAA,CAAKX,CAAC,IAAK+D,CAAA,IAAK,KAAM;MACtBpD,IAAA,CAAKX,CAAA,GAAI,CAAC,IAAK+D,CAAA,IAAK,KAAM;MAC1BpD,IAAA,CAAKX,CAAA,GAAI,CAAC,IAAK+D,CAAA,IAAK,IAAK;MACzBpD,IAAA,CAAKX,CAAA,GAAI,CAAC,IAAI+D,CAAA,GAAI;IACnB;IACDpC,SAAA,EAAW,SAAAA,CAAUhB,IAAA,EAAMX,CAAA,EAAGmE,CAAA,EAAG;MAC/B,IAAIgB,CAAA,GAAI;MACR,SAAS1H,CAAA,GAAI,GAAGA,CAAA,GAAI0G,CAAA,EAAG1G,CAAA,IAAK0H,CAAA,IAAKiC,MAAA,CAAOC,YAAA,CAAa1G,IAAA,CAAKX,CAAA,GAAIvC,CAAC,CAAC;MAChE,OAAO0H,CAAA;IACR;IACDmC,UAAA,EAAY,SAAAA,CAAUtK,IAAA,EAAMgD,CAAA,EAAGmF,CAAA,EAAG;MAChC,SAAS1H,CAAA,GAAI,GAAGA,CAAA,GAAI0H,CAAA,CAAEzH,MAAA,EAAQD,CAAA,IAAKT,IAAA,CAAKgD,CAAA,GAAIvC,CAAC,IAAI0H,CAAA,CAAEoC,UAAA,CAAW9J,CAAC;IAChE;IACDiF,SAAA,EAAW,SAAAA,CAAU/B,IAAA,EAAMX,CAAA,EAAGmE,CAAA,EAAG;MAC/B,IAAIqD,GAAA,GAAM,EAAE;MACZ,SAAS/J,CAAA,GAAI,GAAGA,CAAA,GAAI0G,CAAA,EAAG1G,CAAA,IAAK+J,GAAA,CAAIjJ,IAAA,CAAKoC,IAAA,CAAKX,CAAA,GAAIvC,CAAC,CAAC;MAChD,OAAO+J,GAAA;IACR;IACDC,GAAA,EAAK,SAAAA,CAAU1D,CAAA,EAAG;MAChB,OAAOA,CAAA,CAAErG,MAAA,GAAS,IAAI,MAAMqG,CAAA,GAAIA,CAAA;IACjC;IACDvB,QAAA,EAAU,SAAAA,CAAU7B,IAAA,EAAMX,CAAA,EAAGmE,CAAA,EAAG;MAC9B,IAAIgB,CAAA,GAAI;QACNuC,EAAA;MACF,SAASjK,CAAA,GAAI,GAAGA,CAAA,GAAI0G,CAAA,EAAG1G,CAAA,IAAK0H,CAAA,IAAK,MAAM9I,IAAA,CAAKkD,IAAA,CAAKkI,GAAA,CAAI9G,IAAA,CAAKX,CAAA,GAAIvC,CAAC,EAAEkK,QAAA,CAAS,EAAE,CAAC;MAC7E,IAAI;QACFD,EAAA,GAAKE,kBAAA,CAAmBzC,CAAC;MAC1B,SAAQjB,CAAA,EAAP;QACA,OAAO7H,IAAA,CAAKkD,IAAA,CAAKoC,SAAA,CAAUhB,IAAA,EAAMX,CAAA,EAAGmE,CAAC;MACtC;MAED,OAAOuD,EAAA;IACR;EACF;EACDrL,IAAA,CAAKiC,SAAA,GAAY,UAAUuJ,EAAA,EAAI3B,EAAA,EAAIC,EAAA,EAAIrG,EAAA,EAAIgI,EAAA,EAAIC,EAAA,EAAIC,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAM;IACnE,IAAIzL,CAAA,GAAIsC,IAAA,CAAKoJ,GAAA,CAAIjC,EAAA,EAAI4B,EAAE;MACrBnL,CAAA,GAAIoC,IAAA,CAAKoJ,GAAA,CAAIhC,EAAA,EAAI4B,EAAE;IACrB,IAAIK,EAAA,GAAK;MACPzI,EAAA,GAAK;IACP,SAAS3B,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;MAC1B,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAIrB,CAAA,EAAGqB,CAAA,IAAK;QAC1B,IAAIkK,IAAA,IAAQ,KAAKC,IAAA,IAAQ,GAAG;UAC1BG,EAAA,GAAMpK,CAAA,GAAIkI,EAAA,GAAKpI,CAAA,IAAM;UACrB6B,EAAA,IAAOsI,IAAA,GAAOjK,CAAA,IAAK8J,EAAA,GAAKE,IAAA,GAAOlK,CAAA,IAAM;QAC/C,OAAe;UACLsK,EAAA,IAAO,CAACH,IAAA,GAAOjK,CAAA,IAAKkI,EAAA,GAAK8B,IAAA,GAAOlK,CAAA,IAAM;UACtC6B,EAAA,GAAM3B,CAAA,GAAI8J,EAAA,GAAKhK,CAAA,IAAM;QACtB;QAED,IAAIoK,IAAA,IAAQ,GAAG;UACbpI,EAAA,CAAGH,EAAE,IAAIkI,EAAA,CAAGO,EAAE;UACdtI,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAIkI,EAAA,CAAGO,EAAA,GAAK,CAAC;UACtBtI,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAIkI,EAAA,CAAGO,EAAA,GAAK,CAAC;UACtBtI,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAIkI,EAAA,CAAGO,EAAA,GAAK,CAAC;QAChC,WAAmBF,IAAA,IAAQ,GAAG;UACpB,IAAIG,EAAA,GAAKR,EAAA,CAAGO,EAAA,GAAK,CAAC,KAAK,IAAI;YACzBrG,EAAA,GAAK8F,EAAA,CAAGO,EAAE,IAAIC,EAAA;YACdC,EAAA,GAAKT,EAAA,CAAGO,EAAA,GAAK,CAAC,IAAIC,EAAA;YAClBE,EAAA,GAAKV,EAAA,CAAGO,EAAA,GAAK,CAAC,IAAIC,EAAA;UACpB,IAAIG,EAAA,GAAK1I,EAAA,CAAGH,EAAA,GAAK,CAAC,KAAK,IAAI;YACzB8I,EAAA,GAAK3I,EAAA,CAAGH,EAAE,IAAI6I,EAAA;YACdE,EAAA,GAAK5I,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI6I,EAAA;YAClBG,EAAA,GAAK7I,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI6I,EAAA;UAEpB,IAAII,GAAA,GAAM,IAAIP,EAAA;YACZQ,EAAA,GAAKR,EAAA,GAAKG,EAAA,GAAKI,GAAA;YACfE,GAAA,GAAMD,EAAA,IAAM,IAAI,IAAI,IAAIA,EAAA;UAC1B/I,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI,MAAMkJ,EAAA;UACnB/I,EAAA,CAAGH,EAAA,GAAK,CAAC,KAAKoC,EAAA,GAAK0G,EAAA,GAAKG,GAAA,IAAOE,GAAA;UAC/BhJ,EAAA,CAAGH,EAAA,GAAK,CAAC,KAAK2I,EAAA,GAAKI,EAAA,GAAKE,GAAA,IAAOE,GAAA;UAC/BhJ,EAAA,CAAGH,EAAA,GAAK,CAAC,KAAK4I,EAAA,GAAKI,EAAA,GAAKC,GAAA,IAAOE,GAAA;QACzC,WAAmBZ,IAAA,IAAQ,GAAG;UAGpB,IAAIG,EAAA,GAAKR,EAAA,CAAGO,EAAA,GAAK,CAAC;YAChBrG,EAAA,GAAK8F,EAAA,CAAGO,EAAE;YACVE,EAAA,GAAKT,EAAA,CAAGO,EAAA,GAAK,CAAC;YACdG,EAAA,GAAKV,EAAA,CAAGO,EAAA,GAAK,CAAC;UAChB,IAAII,EAAA,GAAK1I,EAAA,CAAGH,EAAA,GAAK,CAAC;YAChB8I,EAAA,GAAK3I,EAAA,CAAGH,EAAE;YACV+I,EAAA,GAAK5I,EAAA,CAAGH,EAAA,GAAK,CAAC;YACdgJ,EAAA,GAAK7I,EAAA,CAAGH,EAAA,GAAK,CAAC;UAChB,IAAI0I,EAAA,IAAMG,EAAA,IAAMzG,EAAA,IAAM0G,EAAA,IAAMH,EAAA,IAAMI,EAAA,IAAMH,EAAA,IAAMI,EAAA,EAAI;YAChD7I,EAAA,CAAGH,EAAE,IAAI;YACTG,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI;YACbG,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI;YACbG,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI;UACzB,OAAiB;YACLG,EAAA,CAAGH,EAAE,IAAIoC,EAAA;YACTjC,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI2I,EAAA;YACbxI,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI4I,EAAA;YACbzI,EAAA,CAAGH,EAAA,GAAK,CAAC,IAAI0I,EAAA;UACd;QACX,WAAmBH,IAAA,IAAQ,GAAG;UAGpB,IAAIG,EAAA,GAAKR,EAAA,CAAGO,EAAA,GAAK,CAAC;YAChBrG,EAAA,GAAK8F,EAAA,CAAGO,EAAE;YACVE,EAAA,GAAKT,EAAA,CAAGO,EAAA,GAAK,CAAC;YACdG,EAAA,GAAKV,EAAA,CAAGO,EAAA,GAAK,CAAC;UAChB,IAAII,EAAA,GAAK1I,EAAA,CAAGH,EAAA,GAAK,CAAC;YAChB8I,EAAA,GAAK3I,EAAA,CAAGH,EAAE;YACV+I,EAAA,GAAK5I,EAAA,CAAGH,EAAA,GAAK,CAAC;YACdgJ,EAAA,GAAK7I,EAAA,CAAGH,EAAA,GAAK,CAAC;UAChB,IAAI0I,EAAA,IAAMG,EAAA,IAAMzG,EAAA,IAAM0G,EAAA,IAAMH,EAAA,IAAMI,EAAA,IAAMH,EAAA,IAAMI,EAAA,EAAI;UAElD,IAAIN,EAAA,GAAK,OAAOG,EAAA,GAAK,IAAI,OAAO;QACjC;MACF;IACF;IAED,OAAO;EACR;AACH;AAEA,MAAMO,UAAA,SAAmBC,iBAAA,CAAkB;EACzCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKxH,IAAA,GAAOyH,aAAA;IACZ,KAAKC,QAAA,GAAW;EACjB;EAEDC,YAAYC,KAAA,EAAO;IACjB,KAAK5H,IAAA,GAAO4H,KAAA;IACZ,OAAO;EACR;EAEDC,YAAYD,KAAA,EAAO;IACjB,KAAKF,QAAA,GAAWE,KAAA;IAChB,OAAO;EACR;EAEDE,YAAYC,IAAA,EAAMC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IAC7C,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAa;IAEjC,IAAIC,MAAA,GAAS;IAEb,MAAMC,KAAA,GAAQ;IAEd,SAASC,YAAYxM,CAAA,EAAG;MACtBuM,KAAA,CAAME,IAAA,CACJT,IAAA,CAAKhM,CAAC,GACN,UAAU0M,KAAA,EAAO;QACfN,OAAA,CAAQO,MAAA,CAAO3M,CAAC,IAAI0M,KAAA;QAEpBJ,MAAA;QAEA,IAAIA,MAAA,KAAW,GAAG;UAChBF,OAAA,CAAQQ,WAAA,GAAc;UAEtB,IAAIX,MAAA,EAAQA,MAAA,CAAOG,OAAO;QAC3B;MACF,GACD,QACAD,OACD;IACF;IAED,SAASnM,CAAA,GAAI,GAAGA,CAAA,GAAIgM,IAAA,CAAK/L,MAAA,EAAQ,EAAED,CAAA,EAAG;MACpCwM,WAAA,CAAYxM,CAAC;IACd;IAEDoM,OAAA,CAAQnI,IAAA,GAAO,KAAKA,IAAA;IACpBmI,OAAA,CAAQS,MAAA,GAASC,UAAA;IACjBV,OAAA,CAAQW,SAAA,GAAYC,YAAA;IACpBZ,OAAA,CAAQa,eAAA,GAAkB;IAE1B,OAAOb,OAAA;EACR;EAEDc,MAAM1N,MAAA,EAAQ;IACZX,IAAA,CAAM;IACN,MAAMe,GAAA,GAAMhB,IAAA,CAAKuC,MAAA,CAAO3B,MAAM;IAC9B,MAAM2N,IAAA,GAAOvO,IAAA,CAAKE,OAAA,CAAQc,GAAG,EAAE,CAAC;IAEhC,MAAML,IAAA,GAAO,IAAIM,UAAA,CAAWsN,IAAI;IAChC,MAAMC,IAAA,GAAOxN,GAAA,CAAIX,KAAA,GAAQW,GAAA,CAAIT,MAAA,GAAS;IAEtC,MAAMkO,MAAA,GAAS,KAAKpJ,IAAA,KAASyH,aAAA,GAAgB,IAAI7D,WAAA,CAAYuF,IAAI,IAAI,IAAIE,YAAA,CAAaF,IAAI;IAI1F,SAASpN,CAAA,GAAI,GAAGA,CAAA,GAAIT,IAAA,CAAKU,MAAA,EAAQD,CAAA,IAAK,GAAG;MACvC,MAAMoH,CAAA,GAAI7H,IAAA,CAAKS,CAAA,GAAI,CAAC,IAAI;MACxB,MAAMgI,CAAA,GAAIzI,IAAA,CAAKS,CAAA,GAAI,CAAC,IAAI;MACxB,MAAMuG,CAAA,GAAIhH,IAAA,CAAKS,CAAA,GAAI,CAAC,IAAI;MACxB,MAAM4H,CAAA,GAAIrI,IAAA,CAAKS,CAAA,GAAI,CAAC,IAAI;MAExB,IAAI,KAAKiE,IAAA,KAASyH,aAAA,EAAe;QAC/B2B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIuN,SAAA,CAAUC,WAAA,CAAYlM,IAAA,CAAKoJ,GAAA,CAAItD,CAAA,GAAIQ,CAAA,GAAI,KAAK+D,QAAA,EAAU,KAAK,CAAC;QAC5E0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIuN,SAAA,CAAUC,WAAA,CAAYlM,IAAA,CAAKoJ,GAAA,CAAI1C,CAAA,GAAIJ,CAAA,GAAI,KAAK+D,QAAA,EAAU,KAAK,CAAC;QAC5E0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIuN,SAAA,CAAUC,WAAA,CAAYlM,IAAA,CAAKoJ,GAAA,CAAInE,CAAA,GAAIqB,CAAA,GAAI,KAAK+D,QAAA,EAAU,KAAK,CAAC;QAC5E0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIuN,SAAA,CAAUC,WAAA,CAAY,CAAC;MAC/C,OAAa;QACLH,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIoH,CAAA,GAAIQ,CAAA,GAAI,KAAK+D,QAAA;QAC7B0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIgI,CAAA,GAAIJ,CAAA,GAAI,KAAK+D,QAAA;QAC7B0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAIuG,CAAA,GAAIqB,CAAA,GAAI,KAAK+D,QAAA;QAC7B0B,MAAA,CAAOrN,CAAA,GAAI,CAAC,IAAI;MACjB;IACF;IAED,OAAO;MACLf,KAAA,EAAOW,GAAA,CAAIX,KAAA;MACXE,MAAA,EAAQS,GAAA,CAAIT,MAAA;MACZI,IAAA,EAAM8N,MAAA;MACNR,MAAA,EAAQC,UAAA;MACR7I,IAAA,EAAM,KAAKA,IAAA;MACXwJ,KAAA,EAAO;IACR;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}