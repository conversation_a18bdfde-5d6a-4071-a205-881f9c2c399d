{"ast": null, "code": "import { a as _isNativeReflectConstruct, _ as _setPrototypeOf } from './isNativeReflectConstruct-5594d075.esm.js';\nimport { Vector2, Matrix4 } from 'three';\nimport { d as determinant3, g as getMinor } from './matrix-baa530bf.esm.js';\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n    ax = _triangle$[0],\n    ay = _triangle$[1];\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n    bx = _triangle$2[0],\n    by = _triangle$2[1];\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n    cx = _triangle$3[0],\n    cy = _triangle$3[1];\n  var _point = _slicedToArray(point, 2),\n    px = _point[0],\n    py = _point[1]; // TODO Sub with static calc\n\n  var matrix = new Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n    x1 = _triangle$4[0],\n    y1 = _triangle$4[1];\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n    x2 = _triangle$5[0],\n    y2 = _triangle$5[1];\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n    x3 = _triangle$6[0],\n    y3 = _triangle$6[1]; // prettier-ignore\n\n  return determinant3(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n    ax = _triangle$7[0],\n    ay = _triangle$7[1]; // @ts-ignore\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n    bx = _triangle$8[0],\n    by = _triangle$8[1]; // @ts-ignore\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n    cx = _triangle$9[0],\n    cy = _triangle$9[1];\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = getMinor(m, 1, 1);\n  var m13 = getMinor(m, 1, 3);\n  var m12 = getMinor(m, 1, 2);\n  var m14 = getMinor(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n    _ref2 = _slicedToArray(_ref, 2),\n    ax = _ref2[0],\n    ay = _ref2[1];\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n    _ref4 = _slicedToArray(_ref3, 2),\n    bx = _ref4[0],\n    by = _ref4[1];\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n    _ref6 = _slicedToArray(_ref5, 2),\n    cx = _ref6[0],\n    cy = _ref6[1];\n  var _point2 = _slicedToArray(point, 2),\n    px = _point2[0],\n    py = _point2[1];\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = determinant3(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new Vector2();\nvar mv2 = new Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n      if (Array.isArray(p)) {\n        return _construct(Vector2, _toConsumableArray(p));\n      }\n      return p;\n    }),\n    _points$map2 = _slicedToArray(_points$map, 3),\n    p1 = _points$map2[0],\n    p2 = _points$map2[1],\n    p3 = _points$map2[2];\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\nexport { _slicedToArray as _, _toConsumableArray as a, triangleDeterminant as b, arePointsCollinear as c, doThreePointsMakeARight as d, isTriangleClockwise as e, isPointInCircumcircle as f, getCircumcircle as g, isPointInTriangle as i, triangle as t };", "map": {"version": 3, "names": ["a", "_isNativeReflectConstruct", "_", "_setPrototypeOf", "Vector2", "Matrix4", "d", "determinant3", "g", "getMinor", "_arrayWithHoles", "arr", "Array", "isArray", "_iterableToArrayLimit", "i", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "call", "next", "done", "push", "value", "length", "err", "_arrayLikeToArray", "len", "arr2", "_unsupportedIterableToArray", "o", "minLen", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "_nonIterableRest", "TypeError", "_slicedToArray", "_arrayWithoutHoles", "_iterableToArray", "iter", "_nonIterableSpread", "_toConsumableArray", "_construct", "Parent", "args", "Class", "Reflect", "construct", "apply", "<PERSON><PERSON><PERSON><PERSON>", "Function", "bind", "instance", "arguments", "isPointInTriangle", "point", "triangle", "_triangle$", "ax", "ay", "_triangle$2", "bx", "by", "_triangle$3", "cx", "cy", "_point", "px", "py", "matrix", "set", "determinant", "triangleDeterminant", "_triangle$4", "x1", "y1", "_triangle$5", "x2", "y2", "_triangle$6", "x3", "y3", "arePointsCollinear", "points", "isTriangleClockwise", "getCircumcircle", "_triangle$7", "_triangle$8", "_triangle$9", "m", "m11", "m13", "m12", "m14", "x0", "y0", "r2", "x", "Math", "abs", "y", "r", "sqrt", "isPointInCircumcircle", "_ref", "toArray", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "_point2", "Error", "x1mpx", "aympy", "bxmpx", "bympy", "cxmpx", "cympy", "mv1", "mv2", "doThreePointsMakeARight", "_points$map", "map", "p", "_points$map2", "p1", "p2", "p3", "p2p1", "subVectors", "p3p1", "cross", "freeze", "__proto__", "b", "c", "e", "f", "t"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/maath/dist/triangle-b62b9067.esm.js"], "sourcesContent": ["import { a as _isNativeReflectConstruct, _ as _setPrototypeOf } from './isNativeReflectConstruct-5594d075.esm.js';\nimport { Vector2, Matrix4 } from 'three';\nimport { d as determinant3, g as getMinor } from './matrix-baa530bf.esm.js';\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n      ax = _triangle$[0],\n      ay = _triangle$[1];\n\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$2[0],\n      by = _triangle$2[1];\n\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$3[0],\n      cy = _triangle$3[1];\n\n  var _point = _slicedToArray(point, 2),\n      px = _point[0],\n      py = _point[1]; // TODO Sub with static calc\n\n\n  var matrix = new Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n      x1 = _triangle$4[0],\n      y1 = _triangle$4[1];\n\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n      x2 = _triangle$5[0],\n      y2 = _triangle$5[1];\n\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n      x3 = _triangle$6[0],\n      y3 = _triangle$6[1]; // prettier-ignore\n\n\n  return determinant3(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n      ax = _triangle$7[0],\n      ay = _triangle$7[1]; // @ts-ignore\n\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$8[0],\n      by = _triangle$8[1]; // @ts-ignore\n\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$9[0],\n      cy = _triangle$9[1];\n\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = getMinor(m, 1, 1);\n  var m13 = getMinor(m, 1, 3);\n  var m12 = getMinor(m, 1, 2);\n  var m14 = getMinor(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n      _ref2 = _slicedToArray(_ref, 2),\n      ax = _ref2[0],\n      ay = _ref2[1];\n\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n      _ref4 = _slicedToArray(_ref3, 2),\n      bx = _ref4[0],\n      by = _ref4[1];\n\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n      _ref6 = _slicedToArray(_ref5, 2),\n      cx = _ref6[0],\n      cy = _ref6[1];\n\n  var _point2 = _slicedToArray(point, 2),\n      px = _point2[0],\n      py = _point2[1];\n\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = determinant3(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new Vector2();\nvar mv2 = new Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n    if (Array.isArray(p)) {\n      return _construct(Vector2, _toConsumableArray(p));\n    }\n\n    return p;\n  }),\n      _points$map2 = _slicedToArray(_points$map, 3),\n      p1 = _points$map2[0],\n      p2 = _points$map2[1],\n      p3 = _points$map2[2];\n\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\n\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\n\nexport { _slicedToArray as _, _toConsumableArray as a, triangleDeterminant as b, arePointsCollinear as c, doThreePointsMakeARight as d, isTriangleClockwise as e, isPointInCircumcircle as f, getCircumcircle as g, isPointInTriangle as i, triangle as t };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,eAAe,QAAQ,4CAA4C;AACjH,SAASC,OAAO,EAAEC,OAAO,QAAQ,OAAO;AACxC,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,0BAA0B;AAE3E,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC5B,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAOA,GAAG;AACpC;AAEA,SAASG,qBAAqBA,CAACH,GAAG,EAAEI,CAAC,EAAE;EACrC,IAAIC,EAAE,GAAGL,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOM,MAAM,KAAK,WAAW,IAAIN,GAAG,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,GAAG,CAAC,YAAY,CAAC;EAExG,IAAIK,EAAE,IAAI,IAAI,EAAE;EAChB,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,KAAK;EAEd,IAAIC,EAAE,EAAEC,EAAE;EAEV,IAAI;IACF,KAAKP,EAAE,GAAGA,EAAE,CAACQ,IAAI,CAACb,GAAG,CAAC,EAAE,EAAES,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACS,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEN,EAAE,GAAG,IAAI,EAAE;MAChED,IAAI,CAACQ,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAEnB,IAAIb,CAAC,IAAII,IAAI,CAACU,MAAM,KAAKd,CAAC,EAAE;IAC9B;EACF,CAAC,CAAC,OAAOe,GAAG,EAAE;IACZT,EAAE,GAAG,IAAI;IACTE,EAAE,GAAGO,GAAG;EACV,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACV,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjD,CAAC,SAAS;MACR,IAAIK,EAAE,EAAE,MAAME,EAAE;IAClB;EACF;EAEA,OAAOJ,IAAI;AACb;AAEA,SAASY,iBAAiBA,CAACpB,GAAG,EAAEqB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACkB,MAAM,EAAEG,GAAG,GAAGrB,GAAG,CAACkB,MAAM;EAErD,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEkB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEjB,CAAC,GAAGiB,GAAG,EAAEjB,CAAC,EAAE,EAAEkB,IAAI,CAAClB,CAAC,CAAC,GAAGJ,GAAG,CAACI,CAAC,CAAC;EAErE,OAAOkB,IAAI;AACb;AAEA,SAASC,2BAA2BA,CAACC,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOJ,iBAAiB,CAACI,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACW,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIJ,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACO,WAAW,EAAEL,CAAC,GAAGF,CAAC,CAACO,WAAW,CAACC,IAAI;EAC3D,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOzB,KAAK,CAACgC,IAAI,CAACT,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAON,iBAAiB,CAACI,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASU,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASC,cAAcA,CAACrC,GAAG,EAAEI,CAAC,EAAE;EAC9B,OAAOL,eAAe,CAACC,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEI,CAAC,CAAC,IAAImB,2BAA2B,CAACvB,GAAG,EAAEI,CAAC,CAAC,IAAI+B,gBAAgB,CAAC,CAAC;AAC3H;AAEA,SAASG,kBAAkBA,CAACtC,GAAG,EAAE;EAC/B,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAOoB,iBAAiB,CAACpB,GAAG,CAAC;AACvD;AAEA,SAASuC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAI,OAAOlC,MAAM,KAAK,WAAW,IAAIkC,IAAI,CAAClC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIiC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOvC,KAAK,CAACgC,IAAI,CAACO,IAAI,CAAC;AAC3H;AAEA,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIL,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASM,kBAAkBA,CAAC1C,GAAG,EAAE;EAC/B,OAAOsC,kBAAkB,CAACtC,GAAG,CAAC,IAAIuC,gBAAgB,CAACvC,GAAG,CAAC,IAAIuB,2BAA2B,CAACvB,GAAG,CAAC,IAAIyC,kBAAkB,CAAC,CAAC;AACrH;AAEA,SAASE,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACvC,IAAIxD,yBAAyB,CAAC,CAAC,EAAE;IAC/BqD,UAAU,GAAGI,OAAO,CAACC,SAAS;EAChC,CAAC,MAAM;IACLL,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACpD,IAAIzD,CAAC,GAAG,CAAC,IAAI,CAAC;MACdA,CAAC,CAAC2B,IAAI,CAACiC,KAAK,CAAC5D,CAAC,EAAEwD,IAAI,CAAC;MACrB,IAAIK,WAAW,GAAGC,QAAQ,CAACC,IAAI,CAACH,KAAK,CAACL,MAAM,EAAEvD,CAAC,CAAC;MAChD,IAAIgE,QAAQ,GAAG,IAAIH,WAAW,CAAC,CAAC;MAChC,IAAIJ,KAAK,EAAEtD,eAAe,CAAC6D,QAAQ,EAAEP,KAAK,CAAClB,SAAS,CAAC;MACrD,OAAOyB,QAAQ;IACjB,CAAC;EACH;EAEA,OAAOV,UAAU,CAACM,KAAK,CAAC,IAAI,EAAEK,SAAS,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC1C,IAAIC,UAAU,GAAGrB,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3CE,EAAE,GAAGD,UAAU,CAAC,CAAC,CAAC;IAClBE,EAAE,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEtB,IAAIG,WAAW,GAAGxB,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CK,EAAE,GAAGD,WAAW,CAAC,CAAC,CAAC;IACnBE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEvB,IAAIG,WAAW,GAAG3B,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CQ,EAAE,GAAGD,WAAW,CAAC,CAAC,CAAC;IACnBE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEvB,IAAIG,MAAM,GAAG9B,cAAc,CAACmB,KAAK,EAAE,CAAC,CAAC;IACjCY,EAAE,GAAGD,MAAM,CAAC,CAAC,CAAC;IACdE,EAAE,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGpB,IAAIG,MAAM,GAAG,IAAI5E,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE5B4E,MAAM,CAACC,GAAG,CAACZ,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAE,CAAC,CAAC;EAClI,OAAOC,MAAM,CAACE,WAAW,CAAC,CAAC,IAAI,CAAC;AAClC;AACA,SAASC,mBAAmBA,CAAChB,QAAQ,EAAE;EACrC,IAAIiB,WAAW,GAAGrC,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CkB,EAAE,GAAGD,WAAW,CAAC,CAAC,CAAC;IACnBE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEvB,IAAIG,WAAW,GAAGxC,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CqB,EAAE,GAAGD,WAAW,CAAC,CAAC,CAAC;IACnBE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEvB,IAAIG,WAAW,GAAG3C,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CwB,EAAE,GAAGD,WAAW,CAAC,CAAC,CAAC;IACnBE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzB,OAAOpF,YAAY,CAAC+E,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAClC,OAAOX,mBAAmB,CAACW,MAAM,CAAC,KAAK,CAAC;AAC1C,CAAC,CAAC;;AAEF,SAASC,mBAAmBA,CAAC5B,QAAQ,EAAE;EACrC,OAAOgB,mBAAmB,CAAChB,QAAQ,CAAC,GAAG,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6B,eAAeA,CAAC7B,QAAQ,EAAE;EACjC;EACA;EACA,IAAI8B,WAAW,GAAGlD,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CE,EAAE,GAAG4B,WAAW,CAAC,CAAC,CAAC;IACnB3B,EAAE,GAAG2B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzB,IAAIC,WAAW,GAAGnD,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CK,EAAE,GAAG0B,WAAW,CAAC,CAAC,CAAC;IACnBzB,EAAE,GAAGyB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzB,IAAIC,WAAW,GAAGpD,cAAc,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5CQ,EAAE,GAAGwB,WAAW,CAAC,CAAC,CAAC;IACnBvB,EAAE,GAAGuB,WAAW,CAAC,CAAC,CAAC;EAEvB,IAAIN,kBAAkB,CAAC1B,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAE/C,IAAIiC,CAAC,GAAG,IAAIhG,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEvBgG,CAAC,CAACnB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAEE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAEE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;EAC3G,IAAIyB,GAAG,GAAG7F,QAAQ,CAAC4F,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,IAAIE,GAAG,GAAG9F,QAAQ,CAAC4F,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,IAAIG,GAAG,GAAG/F,QAAQ,CAAC4F,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,IAAII,GAAG,GAAGhG,QAAQ,CAAC4F,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,IAAIK,EAAE,GAAG,GAAG,IAAIF,GAAG,GAAGF,GAAG,CAAC;EAC1B,IAAIK,EAAE,GAAG,GAAG,IAAIJ,GAAG,GAAGD,GAAG,CAAC;EAC1B,IAAIM,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGF,GAAG,GAAGH,GAAG;EACtC,OAAO;IACLO,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACL,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IAC9BM,CAAC,EAAEF,IAAI,CAACC,GAAG,CAACJ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAACA,EAAE;IAC/BM,CAAC,EAAEH,IAAI,CAACI,IAAI,CAACN,EAAE;EACjB,CAAC;AACH,CAAC,CAAC;;AAEF,SAASO,qBAAqBA,CAAChD,KAAK,EAAEC,QAAQ,EAAE;EAC9C,IAAIgD,IAAI,GAAGxG,KAAK,CAACC,OAAO,CAACuD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,CAAC;IACvEC,KAAK,GAAGtE,cAAc,CAACoE,IAAI,EAAE,CAAC,CAAC;IAC/B9C,EAAE,GAAGgD,KAAK,CAAC,CAAC,CAAC;IACb/C,EAAE,GAAG+C,KAAK,CAAC,CAAC,CAAC;EAEjB,IAAIC,KAAK,GAAG3G,KAAK,CAACC,OAAO,CAACuD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,CAAC;IACxEG,KAAK,GAAGxE,cAAc,CAACuE,KAAK,EAAE,CAAC,CAAC;IAChC9C,EAAE,GAAG+C,KAAK,CAAC,CAAC,CAAC;IACb9C,EAAE,GAAG8C,KAAK,CAAC,CAAC,CAAC;EAEjB,IAAIC,KAAK,GAAG7G,KAAK,CAACC,OAAO,CAACuD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,CAAC;IACxEK,KAAK,GAAG1E,cAAc,CAACyE,KAAK,EAAE,CAAC,CAAC;IAChC7C,EAAE,GAAG8C,KAAK,CAAC,CAAC,CAAC;IACb7C,EAAE,GAAG6C,KAAK,CAAC,CAAC,CAAC;EAEjB,IAAIC,OAAO,GAAG3E,cAAc,CAACmB,KAAK,EAAE,CAAC,CAAC;IAClCY,EAAE,GAAG4C,OAAO,CAAC,CAAC,CAAC;IACf3C,EAAE,GAAG2C,OAAO,CAAC,CAAC,CAAC;EAEnB,IAAI7B,kBAAkB,CAAC1B,QAAQ,CAAC,EAAE,MAAM,IAAIwD,KAAK,CAAC,wCAAwC,CAAC;EAC3F;AACF;AACA;AACA;AACA;;EAEE,IAAIC,KAAK,GAAGvD,EAAE,GAAGS,EAAE;EACnB,IAAI+C,KAAK,GAAGvD,EAAE,GAAGS,EAAE;EACnB,IAAI+C,KAAK,GAAGtD,EAAE,GAAGM,EAAE;EACnB,IAAIiD,KAAK,GAAGtD,EAAE,GAAGM,EAAE;EACnB,IAAIiD,KAAK,GAAGrD,EAAE,GAAGG,EAAE;EACnB,IAAImD,KAAK,GAAGrD,EAAE,GAAGG,EAAE,CAAC,CAAC;;EAErB,IAAI1E,CAAC,GAAGC,YAAY,CAACsH,KAAK,EAAEC,KAAK,EAAED,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAED,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAED,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;;EAE7J,IAAI5H,CAAC,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EAEA,OAAO,CAAC0F,mBAAmB,CAAC5B,QAAQ,CAAC,GAAG9D,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC;AACvD,CAAC,CAAC;;AAEF,IAAI6H,GAAG,GAAG,IAAI/H,OAAO,CAAC,CAAC;AACvB,IAAIgI,GAAG,GAAG,IAAIhI,OAAO,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASiI,uBAAuBA,CAACtC,MAAM,EAAE;EACvC,IAAIuC,WAAW,GAAGvC,MAAM,CAACwC,GAAG,CAAC,UAAUC,CAAC,EAAE;MACxC,IAAI5H,KAAK,CAACC,OAAO,CAAC2H,CAAC,CAAC,EAAE;QACpB,OAAOlF,UAAU,CAAClD,OAAO,EAAEiD,kBAAkB,CAACmF,CAAC,CAAC,CAAC;MACnD;MAEA,OAAOA,CAAC;IACV,CAAC,CAAC;IACEC,YAAY,GAAGzF,cAAc,CAACsF,WAAW,EAAE,CAAC,CAAC;IAC7CI,EAAE,GAAGD,YAAY,CAAC,CAAC,CAAC;IACpBE,EAAE,GAAGF,YAAY,CAAC,CAAC,CAAC;IACpBG,EAAE,GAAGH,YAAY,CAAC,CAAC,CAAC;EAExB,IAAI3C,kBAAkB,CAACC,MAAM,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;;EAE9C,IAAI8C,IAAI,GAAGV,GAAG,CAACW,UAAU,CAACH,EAAE,EAAED,EAAE,CAAC,CAAC,CAAC;;EAEnC,IAAIK,IAAI,GAAGX,GAAG,CAACU,UAAU,CAACF,EAAE,EAAEF,EAAE,CAAC;EACjC,IAAIM,KAAK,GAAGD,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EAC5B,OAAOG,KAAK,GAAG,CAAC;AAClB;AAEA,IAAI5E,QAAQ,GAAG,aAAa9B,MAAM,CAAC2G,MAAM,CAAC;EACxCC,SAAS,EAAE,IAAI;EACfhF,iBAAiB,EAAEA,iBAAiB;EACpCkB,mBAAmB,EAAEA,mBAAmB;EACxCU,kBAAkB,EAAEA,kBAAkB;EACtCE,mBAAmB,EAAEA,mBAAmB;EACxCC,eAAe,EAAEA,eAAe;EAChCkB,qBAAqB,EAAEA,qBAAqB;EAC5CkB,uBAAuB,EAAEA;AAC3B,CAAC,CAAC;AAEF,SAASrF,cAAc,IAAI9C,CAAC,EAAEmD,kBAAkB,IAAIrD,CAAC,EAAEoF,mBAAmB,IAAI+D,CAAC,EAAErD,kBAAkB,IAAIsD,CAAC,EAAEf,uBAAuB,IAAI/H,CAAC,EAAE0F,mBAAmB,IAAIqD,CAAC,EAAElC,qBAAqB,IAAImC,CAAC,EAAErD,eAAe,IAAIzF,CAAC,EAAE0D,iBAAiB,IAAInD,CAAC,EAAEqD,QAAQ,IAAImF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}