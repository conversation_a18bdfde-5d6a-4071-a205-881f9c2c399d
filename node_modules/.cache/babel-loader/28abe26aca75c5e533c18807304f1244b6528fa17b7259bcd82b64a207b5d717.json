{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { AxisArrow } from './AxisArrow.js';\nimport { AxisRotator } from './AxisRotator.js';\nimport { PlaneSlider } from './PlaneSlider.js';\nimport { ScalingSphere } from './ScalingSphere.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\nconst mL0 = /* @__PURE__ */new THREE.Matrix4();\nconst mW0 = /* @__PURE__ */new THREE.Matrix4();\nconst mP = /* @__PURE__ */new THREE.Matrix4();\nconst mPInv = /* @__PURE__ */new THREE.Matrix4();\nconst mW = /* @__PURE__ */new THREE.Matrix4();\nconst mL = /* @__PURE__ */new THREE.Matrix4();\nconst mL0Inv = /* @__PURE__ */new THREE.Matrix4();\nconst mdL = /* @__PURE__ */new THREE.Matrix4();\nconst mG = /* @__PURE__ */new THREE.Matrix4();\nconst bb = /* @__PURE__ */new THREE.Box3();\nconst bbObj = /* @__PURE__ */new THREE.Box3();\nconst vCenter = /* @__PURE__ */new THREE.Vector3();\nconst vSize = /* @__PURE__ */new THREE.Vector3();\nconst vAnchorOffset = /* @__PURE__ */new THREE.Vector3();\nconst vPosition = /* @__PURE__ */new THREE.Vector3();\nconst vScale = /* @__PURE__ */new THREE.Vector3();\nconst xDir = /* @__PURE__ */new THREE.Vector3(1, 0, 0);\nconst yDir = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst zDir = /* @__PURE__ */new THREE.Vector3(0, 0, 1);\nconst PivotControls = /* @__PURE__ */React.forwardRef(({\n  enabled = true,\n  matrix,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  autoTransform = true,\n  anchor,\n  disableAxes = false,\n  disableSliders = false,\n  disableRotations = false,\n  disableScaling = false,\n  activeAxes = [true, true, true],\n  offset = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  lineWidth = 4,\n  fixed = false,\n  translationLimits,\n  rotationLimits,\n  scaleLimits,\n  depthTest = true,\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  hoveredColor = '#ffff40',\n  annotations = false,\n  annotationsClass,\n  opacity = 1,\n  visible = true,\n  userData,\n  children,\n  ...props\n}, fRef) => {\n  const invalidate = useThree(state => state.invalidate);\n  const parentRef = React.useRef(null);\n  const ref = React.useRef(null);\n  const gizmoRef = React.useRef(null);\n  const childrenRef = React.useRef(null);\n  const translation = React.useRef([0, 0, 0]);\n  const cameraScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  const gizmoScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  React.useLayoutEffect(() => {\n    if (!anchor) return;\n    childrenRef.current.updateWorldMatrix(true, true);\n    mPInv.copy(childrenRef.current.matrixWorld).invert();\n    bb.makeEmpty();\n    childrenRef.current.traverse(obj => {\n      if (!obj.geometry) return;\n      if (!obj.geometry.boundingBox) obj.geometry.computeBoundingBox();\n      mL.copy(obj.matrixWorld).premultiply(mPInv);\n      bbObj.copy(obj.geometry.boundingBox);\n      bbObj.applyMatrix4(mL);\n      bb.union(bbObj);\n    });\n    vCenter.copy(bb.max).add(bb.min).multiplyScalar(0.5);\n    vSize.copy(bb.max).sub(bb.min).multiplyScalar(0.5);\n    vAnchorOffset.copy(vSize).multiply(new THREE.Vector3(...anchor)).add(vCenter);\n    vPosition.set(...offset).add(vAnchorOffset);\n    gizmoRef.current.position.copy(vPosition);\n    invalidate();\n  });\n  const config = React.useMemo(() => ({\n    onDragStart: props => {\n      mL0.copy(ref.current.matrix);\n      mW0.copy(ref.current.matrixWorld);\n      onDragStart && onDragStart(props);\n      invalidate();\n    },\n    onDrag: mdW => {\n      mP.copy(parentRef.current.matrixWorld);\n      mPInv.copy(mP).invert();\n      // After applying the delta\n      mW.copy(mW0).premultiply(mdW);\n      mL.copy(mW).premultiply(mPInv);\n      mL0Inv.copy(mL0).invert();\n      mdL.copy(mL).multiply(mL0Inv);\n      if (autoTransform) {\n        ref.current.matrix.copy(mL);\n      }\n      onDrag && onDrag(mL, mdL, mW, mdW);\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (onDragEnd) onDragEnd();\n      invalidate();\n    },\n    translation,\n    translationLimits,\n    rotationLimits,\n    axisColors,\n    hoveredColor,\n    opacity,\n    scale,\n    lineWidth,\n    fixed,\n    depthTest,\n    userData,\n    annotations,\n    annotationsClass\n  }), [onDragStart, onDrag, onDragEnd, translation, translationLimits, rotationLimits, scaleLimits, depthTest, scale, lineWidth, fixed, ...axisColors, hoveredColor, opacity, userData, autoTransform, annotations, annotationsClass]);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (fixed) {\n      const sf = calculateScaleFactor(gizmoRef.current.getWorldPosition(vec), scale, state.camera, state.size);\n      cameraScale.current.setScalar(sf);\n    }\n    if (matrix && matrix instanceof THREE.Matrix4) {\n      ref.current.matrix = matrix;\n    }\n    // Update gizmo scale in accordance with matrix changes\n    // Without this, there might be noticable turbulences if scaling happens fast enough\n    ref.current.updateWorldMatrix(true, true);\n    mG.makeRotationFromEuler(gizmoRef.current.rotation).setPosition(gizmoRef.current.position).premultiply(ref.current.matrixWorld);\n    gizmoScale.current.setFromMatrixScale(mG);\n    vScale.copy(cameraScale.current).divide(gizmoScale.current);\n    if (Math.abs(gizmoRef.current.scale.x - vScale.x) > 1e-4 || Math.abs(gizmoRef.current.scale.y - vScale.y) > 1e-4 || Math.abs(gizmoRef.current.scale.z - vScale.z) > 1e-4) {\n      gizmoRef.current.scale.copy(vScale);\n      state.invalidate();\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: config\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: parentRef\n  }, /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    visible: visible,\n    ref: gizmoRef,\n    position: offset,\n    rotation: rotation\n  }, enabled && /*#__PURE__*/React.createElement(React.Fragment, null, !disableAxes && activeAxes[0] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 0,\n    direction: xDir\n  }), !disableAxes && activeAxes[1] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 1,\n    direction: yDir\n  }), !disableAxes && activeAxes[2] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 2,\n    direction: zDir\n  }), !disableSliders && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableSliders && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableSliders && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableRotations && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableRotations && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableRotations && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableScaling && activeAxes[0] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 0,\n    direction: xDir\n  }), !disableScaling && activeAxes[1] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 1,\n    direction: yDir\n  }), !disableScaling && activeAxes[2] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 2,\n    direction: zDir\n  }))), /*#__PURE__*/React.createElement(\"group\", {\n    ref: childrenRef\n  }, children))));\n});\nexport { PivotControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "THREE", "AxisArrow", "AxisRotator", "PlaneSlider", "ScalingSphere", "context", "calculateScaleFactor", "mL0", "Matrix4", "mW0", "mP", "mPInv", "mW", "mL", "mL0Inv", "mdL", "mG", "bb", "Box3", "bbObj", "vCenter", "Vector3", "vSize", "vAnchorOffset", "vPosition", "vScale", "xDir", "yDir", "zDir", "PivotControls", "forwardRef", "enabled", "matrix", "onDragStart", "onDrag", "onDragEnd", "autoTransform", "anchor", "disableAxes", "disableSliders", "disableRotations", "disableScaling", "activeAxes", "offset", "rotation", "scale", "lineWidth", "fixed", "translationLimits", "rotationLimits", "scaleLimits", "depthTest", "axisColors", "hoveredColor", "annotations", "annotationsClass", "opacity", "visible", "userData", "children", "props", "fRef", "invalidate", "state", "parentRef", "useRef", "ref", "gizmoRef", "childrenRef", "translation", "cameraScale", "gizmoScale", "useLayoutEffect", "current", "updateWorldMatrix", "copy", "matrixWorld", "invert", "makeEmpty", "traverse", "obj", "geometry", "boundingBox", "computeBoundingBox", "premultiply", "applyMatrix4", "union", "max", "add", "min", "multiplyScalar", "sub", "multiply", "set", "position", "config", "useMemo", "mdW", "vec", "sf", "getWorldPosition", "camera", "size", "setScalar", "makeRotationFromEuler", "setPosition", "setFromMatrixScale", "divide", "Math", "abs", "x", "y", "z", "useImperativeHandle", "createElement", "Provider", "value", "matrixAutoUpdate", "Fragment", "axis", "direction", "dir1", "dir2"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/pivotControls/index.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { AxisArrow } from './AxisArrow.js';\nimport { AxisRotator } from './AxisRotator.js';\nimport { PlaneSlider } from './PlaneSlider.js';\nimport { ScalingSphere } from './ScalingSphere.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\n\nconst mL0 = /* @__PURE__ */new THREE.Matrix4();\nconst mW0 = /* @__PURE__ */new THREE.Matrix4();\nconst mP = /* @__PURE__ */new THREE.Matrix4();\nconst mPInv = /* @__PURE__ */new THREE.Matrix4();\nconst mW = /* @__PURE__ */new THREE.Matrix4();\nconst mL = /* @__PURE__ */new THREE.Matrix4();\nconst mL0Inv = /* @__PURE__ */new THREE.Matrix4();\nconst mdL = /* @__PURE__ */new THREE.Matrix4();\nconst mG = /* @__PURE__ */new THREE.Matrix4();\nconst bb = /* @__PURE__ */new THREE.Box3();\nconst bbObj = /* @__PURE__ */new THREE.Box3();\nconst vCenter = /* @__PURE__ */new THREE.Vector3();\nconst vSize = /* @__PURE__ */new THREE.Vector3();\nconst vAnchorOffset = /* @__PURE__ */new THREE.Vector3();\nconst vPosition = /* @__PURE__ */new THREE.Vector3();\nconst vScale = /* @__PURE__ */new THREE.Vector3();\nconst xDir = /* @__PURE__ */new THREE.Vector3(1, 0, 0);\nconst yDir = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst zDir = /* @__PURE__ */new THREE.Vector3(0, 0, 1);\nconst PivotControls = /* @__PURE__ */React.forwardRef(({\n  enabled = true,\n  matrix,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  autoTransform = true,\n  anchor,\n  disableAxes = false,\n  disableSliders = false,\n  disableRotations = false,\n  disableScaling = false,\n  activeAxes = [true, true, true],\n  offset = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  lineWidth = 4,\n  fixed = false,\n  translationLimits,\n  rotationLimits,\n  scaleLimits,\n  depthTest = true,\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  hoveredColor = '#ffff40',\n  annotations = false,\n  annotationsClass,\n  opacity = 1,\n  visible = true,\n  userData,\n  children,\n  ...props\n}, fRef) => {\n  const invalidate = useThree(state => state.invalidate);\n  const parentRef = React.useRef(null);\n  const ref = React.useRef(null);\n  const gizmoRef = React.useRef(null);\n  const childrenRef = React.useRef(null);\n  const translation = React.useRef([0, 0, 0]);\n  const cameraScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  const gizmoScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  React.useLayoutEffect(() => {\n    if (!anchor) return;\n    childrenRef.current.updateWorldMatrix(true, true);\n    mPInv.copy(childrenRef.current.matrixWorld).invert();\n    bb.makeEmpty();\n    childrenRef.current.traverse(obj => {\n      if (!obj.geometry) return;\n      if (!obj.geometry.boundingBox) obj.geometry.computeBoundingBox();\n      mL.copy(obj.matrixWorld).premultiply(mPInv);\n      bbObj.copy(obj.geometry.boundingBox);\n      bbObj.applyMatrix4(mL);\n      bb.union(bbObj);\n    });\n    vCenter.copy(bb.max).add(bb.min).multiplyScalar(0.5);\n    vSize.copy(bb.max).sub(bb.min).multiplyScalar(0.5);\n    vAnchorOffset.copy(vSize).multiply(new THREE.Vector3(...anchor)).add(vCenter);\n    vPosition.set(...offset).add(vAnchorOffset);\n    gizmoRef.current.position.copy(vPosition);\n    invalidate();\n  });\n  const config = React.useMemo(() => ({\n    onDragStart: props => {\n      mL0.copy(ref.current.matrix);\n      mW0.copy(ref.current.matrixWorld);\n      onDragStart && onDragStart(props);\n      invalidate();\n    },\n    onDrag: mdW => {\n      mP.copy(parentRef.current.matrixWorld);\n      mPInv.copy(mP).invert();\n      // After applying the delta\n      mW.copy(mW0).premultiply(mdW);\n      mL.copy(mW).premultiply(mPInv);\n      mL0Inv.copy(mL0).invert();\n      mdL.copy(mL).multiply(mL0Inv);\n      if (autoTransform) {\n        ref.current.matrix.copy(mL);\n      }\n      onDrag && onDrag(mL, mdL, mW, mdW);\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (onDragEnd) onDragEnd();\n      invalidate();\n    },\n    translation,\n    translationLimits,\n    rotationLimits,\n    axisColors,\n    hoveredColor,\n    opacity,\n    scale,\n    lineWidth,\n    fixed,\n    depthTest,\n    userData,\n    annotations,\n    annotationsClass\n  }), [onDragStart, onDrag, onDragEnd, translation, translationLimits, rotationLimits, scaleLimits, depthTest, scale, lineWidth, fixed, ...axisColors, hoveredColor, opacity, userData, autoTransform, annotations, annotationsClass]);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (fixed) {\n      const sf = calculateScaleFactor(gizmoRef.current.getWorldPosition(vec), scale, state.camera, state.size);\n      cameraScale.current.setScalar(sf);\n    }\n    if (matrix && matrix instanceof THREE.Matrix4) {\n      ref.current.matrix = matrix;\n    }\n    // Update gizmo scale in accordance with matrix changes\n    // Without this, there might be noticable turbulences if scaling happens fast enough\n    ref.current.updateWorldMatrix(true, true);\n    mG.makeRotationFromEuler(gizmoRef.current.rotation).setPosition(gizmoRef.current.position).premultiply(ref.current.matrixWorld);\n    gizmoScale.current.setFromMatrixScale(mG);\n    vScale.copy(cameraScale.current).divide(gizmoScale.current);\n    if (Math.abs(gizmoRef.current.scale.x - vScale.x) > 1e-4 || Math.abs(gizmoRef.current.scale.y - vScale.y) > 1e-4 || Math.abs(gizmoRef.current.scale.z - vScale.z) > 1e-4) {\n      gizmoRef.current.scale.copy(vScale);\n      state.invalidate();\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: config\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: parentRef\n  }, /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    visible: visible,\n    ref: gizmoRef,\n    position: offset,\n    rotation: rotation\n  }, enabled && /*#__PURE__*/React.createElement(React.Fragment, null, !disableAxes && activeAxes[0] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 0,\n    direction: xDir\n  }), !disableAxes && activeAxes[1] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 1,\n    direction: yDir\n  }), !disableAxes && activeAxes[2] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 2,\n    direction: zDir\n  }), !disableSliders && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableSliders && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableSliders && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableRotations && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableRotations && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableRotations && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableScaling && activeAxes[0] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 0,\n    direction: xDir\n  }), !disableScaling && activeAxes[1] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 1,\n    direction: yDir\n  }), !disableScaling && activeAxes[2] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 2,\n    direction: zDir\n  }))), /*#__PURE__*/React.createElement(\"group\", {\n    ref: childrenRef\n  }, children))));\n});\n\nexport { PivotControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,MAAMC,GAAG,GAAG,eAAe,IAAIP,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC9C,MAAMC,GAAG,GAAG,eAAe,IAAIT,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC9C,MAAME,EAAE,GAAG,eAAe,IAAIV,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC7C,MAAMG,KAAK,GAAG,eAAe,IAAIX,KAAK,CAACQ,OAAO,CAAC,CAAC;AAChD,MAAMI,EAAE,GAAG,eAAe,IAAIZ,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC7C,MAAMK,EAAE,GAAG,eAAe,IAAIb,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC7C,MAAMM,MAAM,GAAG,eAAe,IAAId,KAAK,CAACQ,OAAO,CAAC,CAAC;AACjD,MAAMO,GAAG,GAAG,eAAe,IAAIf,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC9C,MAAMQ,EAAE,GAAG,eAAe,IAAIhB,KAAK,CAACQ,OAAO,CAAC,CAAC;AAC7C,MAAMS,EAAE,GAAG,eAAe,IAAIjB,KAAK,CAACkB,IAAI,CAAC,CAAC;AAC1C,MAAMC,KAAK,GAAG,eAAe,IAAInB,KAAK,CAACkB,IAAI,CAAC,CAAC;AAC7C,MAAME,OAAO,GAAG,eAAe,IAAIpB,KAAK,CAACqB,OAAO,CAAC,CAAC;AAClD,MAAMC,KAAK,GAAG,eAAe,IAAItB,KAAK,CAACqB,OAAO,CAAC,CAAC;AAChD,MAAME,aAAa,GAAG,eAAe,IAAIvB,KAAK,CAACqB,OAAO,CAAC,CAAC;AACxD,MAAMG,SAAS,GAAG,eAAe,IAAIxB,KAAK,CAACqB,OAAO,CAAC,CAAC;AACpD,MAAMI,MAAM,GAAG,eAAe,IAAIzB,KAAK,CAACqB,OAAO,CAAC,CAAC;AACjD,MAAMK,IAAI,GAAG,eAAe,IAAI1B,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,MAAMM,IAAI,GAAG,eAAe,IAAI3B,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,MAAMO,IAAI,GAAG,eAAe,IAAI5B,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,MAAMQ,aAAa,GAAG,eAAe9B,KAAK,CAAC+B,UAAU,CAAC,CAAC;EACrDC,OAAO,GAAG,IAAI;EACdC,MAAM;EACNC,WAAW;EACXC,MAAM;EACNC,SAAS;EACTC,aAAa,GAAG,IAAI;EACpBC,MAAM;EACNC,WAAW,GAAG,KAAK;EACnBC,cAAc,GAAG,KAAK;EACtBC,gBAAgB,GAAG,KAAK;EACxBC,cAAc,GAAG,KAAK;EACtBC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC/BC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClBC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,KAAK,GAAG,CAAC;EACTC,SAAS,GAAG,CAAC;EACbC,KAAK,GAAG,KAAK;EACbC,iBAAiB;EACjBC,cAAc;EACdC,WAAW;EACXC,SAAS,GAAG,IAAI;EAChBC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC9CC,YAAY,GAAG,SAAS;EACxBC,WAAW,GAAG,KAAK;EACnBC,gBAAgB;EAChBC,OAAO,GAAG,CAAC;EACXC,OAAO,GAAG,IAAI;EACdC,QAAQ;EACRC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,UAAU,GAAGjE,QAAQ,CAACkE,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,SAAS,GAAGjE,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,GAAG,GAAGnE,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAME,QAAQ,GAAGpE,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMG,WAAW,GAAGrE,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMI,WAAW,GAAGtE,KAAK,CAACkE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3C,MAAMK,WAAW,GAAGvE,KAAK,CAACkE,MAAM,CAAC,IAAIjE,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAMkD,UAAU,GAAGxE,KAAK,CAACkE,MAAM,CAAC,IAAIjE,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3DtB,KAAK,CAACyE,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACnC,MAAM,EAAE;IACb+B,WAAW,CAACK,OAAO,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;IACjD/D,KAAK,CAACgE,IAAI,CAACP,WAAW,CAACK,OAAO,CAACG,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IACpD5D,EAAE,CAAC6D,SAAS,CAAC,CAAC;IACdV,WAAW,CAACK,OAAO,CAACM,QAAQ,CAACC,GAAG,IAAI;MAClC,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE;MACnB,IAAI,CAACD,GAAG,CAACC,QAAQ,CAACC,WAAW,EAAEF,GAAG,CAACC,QAAQ,CAACE,kBAAkB,CAAC,CAAC;MAChEtE,EAAE,CAAC8D,IAAI,CAACK,GAAG,CAACJ,WAAW,CAAC,CAACQ,WAAW,CAACzE,KAAK,CAAC;MAC3CQ,KAAK,CAACwD,IAAI,CAACK,GAAG,CAACC,QAAQ,CAACC,WAAW,CAAC;MACpC/D,KAAK,CAACkE,YAAY,CAACxE,EAAE,CAAC;MACtBI,EAAE,CAACqE,KAAK,CAACnE,KAAK,CAAC;IACjB,CAAC,CAAC;IACFC,OAAO,CAACuD,IAAI,CAAC1D,EAAE,CAACsE,GAAG,CAAC,CAACC,GAAG,CAACvE,EAAE,CAACwE,GAAG,CAAC,CAACC,cAAc,CAAC,GAAG,CAAC;IACpDpE,KAAK,CAACqD,IAAI,CAAC1D,EAAE,CAACsE,GAAG,CAAC,CAACI,GAAG,CAAC1E,EAAE,CAACwE,GAAG,CAAC,CAACC,cAAc,CAAC,GAAG,CAAC;IAClDnE,aAAa,CAACoD,IAAI,CAACrD,KAAK,CAAC,CAACsE,QAAQ,CAAC,IAAI5F,KAAK,CAACqB,OAAO,CAAC,GAAGgB,MAAM,CAAC,CAAC,CAACmD,GAAG,CAACpE,OAAO,CAAC;IAC7EI,SAAS,CAACqE,GAAG,CAAC,GAAGlD,MAAM,CAAC,CAAC6C,GAAG,CAACjE,aAAa,CAAC;IAC3C4C,QAAQ,CAACM,OAAO,CAACqB,QAAQ,CAACnB,IAAI,CAACnD,SAAS,CAAC;IACzCsC,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACF,MAAMiC,MAAM,GAAGhG,KAAK,CAACiG,OAAO,CAAC,OAAO;IAClC/D,WAAW,EAAE2B,KAAK,IAAI;MACpBrD,GAAG,CAACoE,IAAI,CAACT,GAAG,CAACO,OAAO,CAACzC,MAAM,CAAC;MAC5BvB,GAAG,CAACkE,IAAI,CAACT,GAAG,CAACO,OAAO,CAACG,WAAW,CAAC;MACjC3C,WAAW,IAAIA,WAAW,CAAC2B,KAAK,CAAC;MACjCE,UAAU,CAAC,CAAC;IACd,CAAC;IACD5B,MAAM,EAAE+D,GAAG,IAAI;MACbvF,EAAE,CAACiE,IAAI,CAACX,SAAS,CAACS,OAAO,CAACG,WAAW,CAAC;MACtCjE,KAAK,CAACgE,IAAI,CAACjE,EAAE,CAAC,CAACmE,MAAM,CAAC,CAAC;MACvB;MACAjE,EAAE,CAAC+D,IAAI,CAAClE,GAAG,CAAC,CAAC2E,WAAW,CAACa,GAAG,CAAC;MAC7BpF,EAAE,CAAC8D,IAAI,CAAC/D,EAAE,CAAC,CAACwE,WAAW,CAACzE,KAAK,CAAC;MAC9BG,MAAM,CAAC6D,IAAI,CAACpE,GAAG,CAAC,CAACsE,MAAM,CAAC,CAAC;MACzB9D,GAAG,CAAC4D,IAAI,CAAC9D,EAAE,CAAC,CAAC+E,QAAQ,CAAC9E,MAAM,CAAC;MAC7B,IAAIsB,aAAa,EAAE;QACjB8B,GAAG,CAACO,OAAO,CAACzC,MAAM,CAAC2C,IAAI,CAAC9D,EAAE,CAAC;MAC7B;MACAqB,MAAM,IAAIA,MAAM,CAACrB,EAAE,EAAEE,GAAG,EAAEH,EAAE,EAAEqF,GAAG,CAAC;MAClCnC,UAAU,CAAC,CAAC;IACd,CAAC;IACD3B,SAAS,EAAEA,CAAA,KAAM;MACf,IAAIA,SAAS,EAAEA,SAAS,CAAC,CAAC;MAC1B2B,UAAU,CAAC,CAAC;IACd,CAAC;IACDO,WAAW;IACXrB,iBAAiB;IACjBC,cAAc;IACdG,UAAU;IACVC,YAAY;IACZG,OAAO;IACPX,KAAK;IACLC,SAAS;IACTC,KAAK;IACLI,SAAS;IACTO,QAAQ;IACRJ,WAAW;IACXC;EACF,CAAC,CAAC,EAAE,CAACtB,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEkC,WAAW,EAAErB,iBAAiB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,SAAS,EAAEN,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAE,GAAGK,UAAU,EAAEC,YAAY,EAAEG,OAAO,EAAEE,QAAQ,EAAEtB,aAAa,EAAEkB,WAAW,EAAEC,gBAAgB,CAAC,CAAC;EACpO,MAAM2C,GAAG,GAAG,IAAIlG,KAAK,CAACqB,OAAO,CAAC,CAAC;EAC/BvB,QAAQ,CAACiE,KAAK,IAAI;IAChB,IAAIhB,KAAK,EAAE;MACT,MAAMoD,EAAE,GAAG7F,oBAAoB,CAAC6D,QAAQ,CAACM,OAAO,CAAC2B,gBAAgB,CAACF,GAAG,CAAC,EAAErD,KAAK,EAAEkB,KAAK,CAACsC,MAAM,EAAEtC,KAAK,CAACuC,IAAI,CAAC;MACxGhC,WAAW,CAACG,OAAO,CAAC8B,SAAS,CAACJ,EAAE,CAAC;IACnC;IACA,IAAInE,MAAM,IAAIA,MAAM,YAAYhC,KAAK,CAACQ,OAAO,EAAE;MAC7C0D,GAAG,CAACO,OAAO,CAACzC,MAAM,GAAGA,MAAM;IAC7B;IACA;IACA;IACAkC,GAAG,CAACO,OAAO,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;IACzC1D,EAAE,CAACwF,qBAAqB,CAACrC,QAAQ,CAACM,OAAO,CAAC7B,QAAQ,CAAC,CAAC6D,WAAW,CAACtC,QAAQ,CAACM,OAAO,CAACqB,QAAQ,CAAC,CAACV,WAAW,CAAClB,GAAG,CAACO,OAAO,CAACG,WAAW,CAAC;IAC/HL,UAAU,CAACE,OAAO,CAACiC,kBAAkB,CAAC1F,EAAE,CAAC;IACzCS,MAAM,CAACkD,IAAI,CAACL,WAAW,CAACG,OAAO,CAAC,CAACkC,MAAM,CAACpC,UAAU,CAACE,OAAO,CAAC;IAC3D,IAAImC,IAAI,CAACC,GAAG,CAAC1C,QAAQ,CAACM,OAAO,CAAC5B,KAAK,CAACiE,CAAC,GAAGrF,MAAM,CAACqF,CAAC,CAAC,GAAG,IAAI,IAAIF,IAAI,CAACC,GAAG,CAAC1C,QAAQ,CAACM,OAAO,CAAC5B,KAAK,CAACkE,CAAC,GAAGtF,MAAM,CAACsF,CAAC,CAAC,GAAG,IAAI,IAAIH,IAAI,CAACC,GAAG,CAAC1C,QAAQ,CAACM,OAAO,CAAC5B,KAAK,CAACmE,CAAC,GAAGvF,MAAM,CAACuF,CAAC,CAAC,GAAG,IAAI,EAAE;MACxK7C,QAAQ,CAACM,OAAO,CAAC5B,KAAK,CAAC8B,IAAI,CAAClD,MAAM,CAAC;MACnCsC,KAAK,CAACD,UAAU,CAAC,CAAC;IACpB;EACF,CAAC,CAAC;EACF/D,KAAK,CAACkH,mBAAmB,CAACpD,IAAI,EAAE,MAAMK,GAAG,CAACO,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAa1E,KAAK,CAACmH,aAAa,CAAC7G,OAAO,CAAC8G,QAAQ,EAAE;IACxDC,KAAK,EAAErB;EACT,CAAC,EAAE,aAAahG,KAAK,CAACmH,aAAa,CAAC,OAAO,EAAE;IAC3ChD,GAAG,EAAEF;EACP,CAAC,EAAE,aAAajE,KAAK,CAACmH,aAAa,CAAC,OAAO,EAAEtH,QAAQ,CAAC;IACpDsE,GAAG,EAAEA,GAAG;IACRlC,MAAM,EAAEA,MAAM;IACdqF,gBAAgB,EAAE;EACpB,CAAC,EAAEzD,KAAK,CAAC,EAAE,aAAa7D,KAAK,CAACmH,aAAa,CAAC,OAAO,EAAE;IACnDzD,OAAO,EAAEA,OAAO;IAChBS,GAAG,EAAEC,QAAQ;IACb2B,QAAQ,EAAEnD,MAAM;IAChBC,QAAQ,EAAEA;EACZ,CAAC,EAAEb,OAAO,IAAI,aAAahC,KAAK,CAACmH,aAAa,CAACnH,KAAK,CAACuH,QAAQ,EAAE,IAAI,EAAE,CAAChF,WAAW,IAAII,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAACjH,SAAS,EAAE;IAChJsH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE9F;EACb,CAAC,CAAC,EAAE,CAACY,WAAW,IAAII,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAACjH,SAAS,EAAE;IAC/EsH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE7F;EACb,CAAC,CAAC,EAAE,CAACW,WAAW,IAAII,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAACjH,SAAS,EAAE;IAC/EsH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE5F;EACb,CAAC,CAAC,EAAE,CAACW,cAAc,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC/G,WAAW,EAAE;IACrGoH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE/F,IAAI;IACVgG,IAAI,EAAE/F;EACR,CAAC,CAAC,EAAE,CAACY,cAAc,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC/G,WAAW,EAAE;IACrGoH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE7F,IAAI;IACV8F,IAAI,EAAEhG;EACR,CAAC,CAAC,EAAE,CAACa,cAAc,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC/G,WAAW,EAAE;IACrGoH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE9F,IAAI;IACV+F,IAAI,EAAE9F;EACR,CAAC,CAAC,EAAE,CAACY,gBAAgB,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAChH,WAAW,EAAE;IACvGqH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE/F,IAAI;IACVgG,IAAI,EAAE/F;EACR,CAAC,CAAC,EAAE,CAACa,gBAAgB,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAChH,WAAW,EAAE;IACvGqH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE7F,IAAI;IACV8F,IAAI,EAAEhG;EACR,CAAC,CAAC,EAAE,CAACc,gBAAgB,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAChH,WAAW,EAAE;IACvGqH,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE9F,IAAI;IACV+F,IAAI,EAAE9F;EACR,CAAC,CAAC,EAAE,CAACa,cAAc,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC9G,aAAa,EAAE;IACtFmH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE9F;EACb,CAAC,CAAC,EAAE,CAACe,cAAc,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC9G,aAAa,EAAE;IACtFmH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE7F;EACb,CAAC,CAAC,EAAE,CAACc,cAAc,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAI,aAAa3C,KAAK,CAACmH,aAAa,CAAC9G,aAAa,EAAE;IACtFmH,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE5F;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa7B,KAAK,CAACmH,aAAa,CAAC,OAAO,EAAE;IAC9ChD,GAAG,EAAEE;EACP,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC;AAEF,SAAS9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}