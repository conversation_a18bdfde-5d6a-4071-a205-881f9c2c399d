{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer, SiNodedotjs, SiPython, SiExpress, SiGraphql, SiMongodb, SiPostgresql, SiRedis, SiGit, SiDocker, SiWebpack, SiAmazonwebservices, SiVercel } from 'react-icons/si';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Icon mapping with proper react-icons\nconst iconMap = {\n  'SiReact': SiReact,\n  'SiTypescript': SiTypescript,\n  'SiNextdotjs': SiNextdotjs,\n  'SiTailwindcss': SiTailwindcss,\n  'SiThreedotjs': SiThreedotjs,\n  '<PERSON>Framer': <PERSON><PERSON><PERSON>er,\n  'SiNodedotjs': <PERSON>Nodedotjs,\n  'SiPython': SiPython,\n  'SiExpress': SiExpress,\n  'SiGraphql': SiGraphql,\n  'SiMongodb': SiMongodb,\n  'SiPostgresql': SiPostgresql,\n  'SiRedis': SiRedis,\n  'SiGit': SiGit,\n  'SiDocker': SiDocker,\n  'SiWebpack': SiWebpack,\n  'SiAmazonaws': SiAmazonwebservices,\n  'SiVercel': SiVercel\n};\nconst TechStackCard = ({\n  item,\n  index\n}) => {\n  _s();\n  const [isHovered, setIsHovered] = useState(false);\n  const IconComponent = iconMap[item.icon];\n  if (!IconComponent) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"group cursor-pointer\",\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      delay: index * 0.1\n    },\n    whileHover: {\n      y: -8,\n      scale: 1.02\n    },\n    onHoverStart: () => setIsHovered(true),\n    onHoverEnd: () => setIsHovered(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-white/5 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/20 dark:border-gray-700/50 rounded-2xl p-8 h-full transition-all duration-500 hover:border-gray-300/30 dark:hover:border-gray-600/50 hover:bg-white/10 dark:hover:bg-gray-800/70 hover:shadow-2xl hover:shadow-purple-500/10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative flex justify-center mb-6\",\n        whileHover: {\n          scale: 1.15,\n          rotate: 5\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 400,\n          damping: 10\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n            size: 48,\n            style: {\n              color: item.color\n            },\n            className: \"drop-shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500\",\n            style: {\n              backgroundColor: item.color\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n          className: \"text-xl font-bold text-gray-800 dark:text-white mb-3\",\n          style: {\n            color: isHovered ? item.color : undefined\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed line-clamp-3\",\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 text-xs font-semibold rounded-full backdrop-blur-sm\",\n          style: {\n            backgroundColor: `${item.color}15`,\n            color: item.color,\n            border: `1px solid ${item.color}30`\n          },\n          whileHover: {\n            scale: 1.05\n          },\n          transition: {\n            type: \"spring\",\n            stiffness: 400\n          },\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(TechStackCard, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = TechStackCard;\nexport default TechStackCard;\nvar _c;\n$RefreshReg$(_c, \"TechStackCard\");", "map": {"version": 3, "names": ["React", "useState", "motion", "SiReact", "SiTypescript", "SiNextdotjs", "SiTailwindcss", "SiT<PERSON><PERSON>otjs", "<PERSON><PERSON><PERSON><PERSON>", "SiNodedotjs", "SiPython", "SiExpress", "SiGraphql", "SiMongodb", "SiPostgresql", "SiRedis", "SiGit", "<PERSON><PERSON><PERSON><PERSON>", "SiWebpack", "SiAmazonwebservices", "SiVercel", "jsxDEV", "_jsxDEV", "iconMap", "TechStackCard", "item", "index", "_s", "isHovered", "setIsHovered", "IconComponent", "icon", "div", "className", "initial", "opacity", "y", "animate", "transition", "delay", "whileHover", "scale", "onHoverStart", "onHoverEnd", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rotate", "type", "stiffness", "damping", "size", "style", "color", "backgroundColor", "h3", "undefined", "duration", "name", "description", "span", "border", "category", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { TechStackItem } from '../../constants/TechStack';\nimport {\n  SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer,\n  SiNodedotjs, SiPython, SiExpress, SiGraphql,\n  SiMongodb, SiPostgresql, SiRedis,\n  SiGit, SiDocker, SiWebpack,\n  SiAmazonwebservices, SiVercel\n} from 'react-icons/si';\nimport { IconType } from 'react-icons';\n\n// Icon mapping with proper react-icons\nconst iconMap: { [key: string]: IconType } = {\n  'SiReact': SiReact,\n  'SiTypescript': SiTypescript,\n  'SiNextdotjs': SiNextdotjs,\n  'SiTailwindcss': SiTailwindcss,\n  'SiThreedotjs': SiThreedotjs,\n  'SiFramer': <PERSON><PERSON><PERSON><PERSON>,\n  'SiNodedotjs': SiNodedotjs,\n  'SiPython': SiPython,\n  'SiExpress': SiExpress,\n  'SiGraphql': SiGraphql,\n  'SiMongodb': SiMongodb,\n  'SiPostgresql': SiPostgresql,\n  'SiRedis': SiRedis,\n  'SiGit': SiGit,\n  'SiDocker': SiDocker,\n  'SiWebpack': SiWebpack,\n  'SiAmazonaws': SiAmazonwebservices,\n  'SiVercel': SiVercel,\n};\n\ninterface TechStackCardProps {\n  item: TechStackItem;\n  index: number;\n}\n\nconst TechStackCard: React.FC<TechStackCardProps> = ({ item, index }) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const IconComponent = iconMap[item.icon] as React.ComponentType<{ size?: number; style?: React.CSSProperties; className?: string }>;\n\n  if (!IconComponent) {\n    return null;\n  }\n\n  return (\n    <motion.div\n      className=\"group cursor-pointer\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: index * 0.1 }}\n      whileHover={{ y: -8, scale: 1.02 }}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n    >\n      <div className=\"relative bg-white/5 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/20 dark:border-gray-700/50 rounded-2xl p-8 h-full transition-all duration-500 hover:border-gray-300/30 dark:hover:border-gray-600/50 hover:bg-white/10 dark:hover:bg-gray-800/70 hover:shadow-2xl hover:shadow-purple-500/10\">\n\n        {/* Gradient overlay on hover */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n        {/* Icon */}\n        <motion.div\n          className=\"relative flex justify-center mb-6\"\n          whileHover={{ scale: 1.15, rotate: 5 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n        >\n          <div className=\"relative\">\n            <IconComponent\n              size={48}\n              style={{ color: item.color }}\n              className=\"drop-shadow-lg\"\n            />\n            {/* Glow effect */}\n            <div\n              className=\"absolute inset-0 blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500\"\n              style={{ backgroundColor: item.color }}\n            />\n          </div>\n        </motion.div>\n\n        {/* Content */}\n        <div className=\"relative text-center\">\n          <motion.h3\n            className=\"text-xl font-bold text-gray-800 dark:text-white mb-3\"\n            style={{ color: isHovered ? item.color : undefined }}\n            transition={{ duration: 0.3 }}\n          >\n            {item.name}\n          </motion.h3>\n\n          <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed line-clamp-3\">\n            {item.description}\n          </p>\n\n          {/* Category Badge */}\n          <motion.span\n            className=\"inline-block px-4 py-2 text-xs font-semibold rounded-full backdrop-blur-sm\"\n            style={{\n              backgroundColor: `${item.color}15`,\n              color: item.color,\n              border: `1px solid ${item.color}30`\n            }}\n            whileHover={{ scale: 1.05 }}\n            transition={{ type: \"spring\", stiffness: 400 }}\n          >\n            {item.category}\n          </motion.span>\n        </div>\n\n        {/* Shine effect */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out\" />\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TechStackCard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AAEtC,SACEC,OAAO,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EACzEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAC3CC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAChCC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAC1BC,mBAAmB,EAAEC,QAAQ,QACxB,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxB;AACA,MAAMC,OAAoC,GAAG;EAC3C,SAAS,EAAEpB,OAAO;EAClB,cAAc,EAAEC,YAAY;EAC5B,aAAa,EAAEC,WAAW;EAC1B,eAAe,EAAEC,aAAa;EAC9B,cAAc,EAAEC,YAAY;EAC5B,UAAU,EAAEC,QAAQ;EACpB,aAAa,EAAEC,WAAW;EAC1B,UAAU,EAAEC,QAAQ;EACpB,WAAW,EAAEC,SAAS;EACtB,WAAW,EAAEC,SAAS;EACtB,WAAW,EAAEC,SAAS;EACtB,cAAc,EAAEC,YAAY;EAC5B,SAAS,EAAEC,OAAO;EAClB,OAAO,EAAEC,KAAK;EACd,UAAU,EAAEC,QAAQ;EACpB,WAAW,EAAEC,SAAS;EACtB,aAAa,EAAEC,mBAAmB;EAClC,UAAU,EAAEC;AACd,CAAC;AAOD,MAAMI,aAA2C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM6B,aAAa,GAAGP,OAAO,CAACE,IAAI,CAACM,IAAI,CAA4F;EAEnI,IAAI,CAACD,aAAa,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,oBACER,OAAA,CAACpB,MAAM,CAAC8B,GAAG;IACTC,SAAS,EAAC,sBAAsB;IAChCC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,KAAK,EAAEb,KAAK,GAAG;IAAI,CAAE;IACnCc,UAAU,EAAE;MAAEJ,CAAC,EAAE,CAAC,CAAC;MAAEK,KAAK,EAAE;IAAK,CAAE;IACnCC,YAAY,EAAEA,CAAA,KAAMb,YAAY,CAAC,IAAI,CAAE;IACvCc,UAAU,EAAEA,CAAA,KAAMd,YAAY,CAAC,KAAK,CAAE;IAAAe,QAAA,eAEtCtB,OAAA;MAAKW,SAAS,EAAC,8SAA8S;MAAAW,QAAA,gBAG3TtB,OAAA;QAAKW,SAAS,EAAC;MAAkJ;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpK1B,OAAA,CAACpB,MAAM,CAAC8B,GAAG;QACTC,SAAS,EAAC,mCAAmC;QAC7CO,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEQ,MAAM,EAAE;QAAE,CAAE;QACvCX,UAAU,EAAE;UAAEY,IAAI,EAAE,QAAQ;UAAEC,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAR,QAAA,eAE5DtB,OAAA;UAAKW,SAAS,EAAC,UAAU;UAAAW,QAAA,gBACvBtB,OAAA,CAACQ,aAAa;YACZuB,IAAI,EAAE,EAAG;YACTC,KAAK,EAAE;cAAEC,KAAK,EAAE9B,IAAI,CAAC8B;YAAM,CAAE;YAC7BtB,SAAS,EAAC;UAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAEF1B,OAAA;YACEW,SAAS,EAAC,2FAA2F;YACrGqB,KAAK,EAAE;cAAEE,eAAe,EAAE/B,IAAI,CAAC8B;YAAM;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb1B,OAAA;QAAKW,SAAS,EAAC,sBAAsB;QAAAW,QAAA,gBACnCtB,OAAA,CAACpB,MAAM,CAACuD,EAAE;UACRxB,SAAS,EAAC,sDAAsD;UAChEqB,KAAK,EAAE;YAAEC,KAAK,EAAE3B,SAAS,GAAGH,IAAI,CAAC8B,KAAK,GAAGG;UAAU,CAAE;UACrDpB,UAAU,EAAE;YAAEqB,QAAQ,EAAE;UAAI,CAAE;UAAAf,QAAA,EAE7BnB,IAAI,CAACmC;QAAI;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEZ1B,OAAA;UAAGW,SAAS,EAAC,4EAA4E;UAAAW,QAAA,EACtFnB,IAAI,CAACoC;QAAW;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAGJ1B,OAAA,CAACpB,MAAM,CAAC4D,IAAI;UACV7B,SAAS,EAAC,4EAA4E;UACtFqB,KAAK,EAAE;YACLE,eAAe,EAAE,GAAG/B,IAAI,CAAC8B,KAAK,IAAI;YAClCA,KAAK,EAAE9B,IAAI,CAAC8B,KAAK;YACjBQ,MAAM,EAAE,aAAatC,IAAI,CAAC8B,KAAK;UACjC,CAAE;UACFf,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BH,UAAU,EAAE;YAAEY,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAAAP,QAAA,EAE9CnB,IAAI,CAACuC;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC;MAAqL;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACrB,EAAA,CA7EIH,aAA2C;AAAAyC,EAAA,GAA3CzC,aAA2C;AA+EjD,eAAeA,aAAa;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}