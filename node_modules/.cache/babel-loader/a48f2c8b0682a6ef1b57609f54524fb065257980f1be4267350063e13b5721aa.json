{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>kel<PERSON>, Vector3, Quaternion, Bone, VectorKeyframeTrack, QuaternionKeyframeTrack, AnimationClip } from \"three\";\nclass BVHLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.animateBonePositions = true;\n    this.animateBoneRotations = true;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(text) {\n    function readBvh(lines2) {\n      if (nextLine(lines2) !== \"HIERARCHY\") {\n        console.error(\"THREE.BVHLoader: HIERARCHY expected.\");\n      }\n      const list = [];\n      const root = readNode(lines2, nextLine(lines2), list);\n      if (nextLine(lines2) !== \"MOTION\") {\n        console.error(\"THREE.BVHLoader: MOTION expected.\");\n      }\n      let tokens = nextLine(lines2).split(/[\\s]+/);\n      const numFrames = parseInt(tokens[1]);\n      if (isNaN(numFrames)) {\n        console.error(\"THREE.BVHLoader: Failed to read number of frames.\");\n      }\n      tokens = nextLine(lines2).split(/[\\s]+/);\n      const frameTime = parseFloat(tokens[2]);\n      if (isNaN(frameTime)) {\n        console.error(\"THREE.BVHLoader: Failed to read frame time.\");\n      }\n      for (let i = 0; i < numFrames; i++) {\n        tokens = nextLine(lines2).split(/[\\s]+/);\n        readFrameData(tokens, i * frameTime, root);\n      }\n      return list;\n    }\n    function readFrameData(data, frameTime, bone) {\n      if (bone.type === \"ENDSITE\") return;\n      const keyframe = {\n        time: frameTime,\n        position: new Vector3(),\n        rotation: new Quaternion()\n      };\n      bone.frames.push(keyframe);\n      const quat = new Quaternion();\n      const vx = new Vector3(1, 0, 0);\n      const vy = new Vector3(0, 1, 0);\n      const vz = new Vector3(0, 0, 1);\n      for (let i = 0; i < bone.channels.length; i++) {\n        switch (bone.channels[i]) {\n          case \"Xposition\":\n            keyframe.position.x = parseFloat(data.shift().trim());\n            break;\n          case \"Yposition\":\n            keyframe.position.y = parseFloat(data.shift().trim());\n            break;\n          case \"Zposition\":\n            keyframe.position.z = parseFloat(data.shift().trim());\n            break;\n          case \"Xrotation\":\n            quat.setFromAxisAngle(vx, parseFloat(data.shift().trim()) * Math.PI / 180);\n            keyframe.rotation.multiply(quat);\n            break;\n          case \"Yrotation\":\n            quat.setFromAxisAngle(vy, parseFloat(data.shift().trim()) * Math.PI / 180);\n            keyframe.rotation.multiply(quat);\n            break;\n          case \"Zrotation\":\n            quat.setFromAxisAngle(vz, parseFloat(data.shift().trim()) * Math.PI / 180);\n            keyframe.rotation.multiply(quat);\n            break;\n          default:\n            console.warn(\"THREE.BVHLoader: Invalid channel type.\");\n        }\n      }\n      for (let i = 0; i < bone.children.length; i++) {\n        readFrameData(data, frameTime, bone.children[i]);\n      }\n    }\n    function readNode(lines2, firstline, list) {\n      const node = {\n        name: \"\",\n        type: \"\",\n        frames: []\n      };\n      list.push(node);\n      let tokens = firstline.split(/[\\s]+/);\n      if (tokens[0].toUpperCase() === \"END\" && tokens[1].toUpperCase() === \"SITE\") {\n        node.type = \"ENDSITE\";\n        node.name = \"ENDSITE\";\n      } else {\n        node.name = tokens[1];\n        node.type = tokens[0].toUpperCase();\n      }\n      if (nextLine(lines2) !== \"{\") {\n        console.error(\"THREE.BVHLoader: Expected opening { after type & name\");\n      }\n      tokens = nextLine(lines2).split(/[\\s]+/);\n      if (tokens[0] !== \"OFFSET\") {\n        console.error(\"THREE.BVHLoader: Expected OFFSET but got: \" + tokens[0]);\n      }\n      if (tokens.length !== 4) {\n        console.error(\"THREE.BVHLoader: Invalid number of values for OFFSET.\");\n      }\n      const offset = new Vector3(parseFloat(tokens[1]), parseFloat(tokens[2]), parseFloat(tokens[3]));\n      if (isNaN(offset.x) || isNaN(offset.y) || isNaN(offset.z)) {\n        console.error(\"THREE.BVHLoader: Invalid values of OFFSET.\");\n      }\n      node.offset = offset;\n      if (node.type !== \"ENDSITE\") {\n        tokens = nextLine(lines2).split(/[\\s]+/);\n        if (tokens[0] !== \"CHANNELS\") {\n          console.error(\"THREE.BVHLoader: Expected CHANNELS definition.\");\n        }\n        const numChannels = parseInt(tokens[1]);\n        node.channels = tokens.splice(2, numChannels);\n        node.children = [];\n      }\n      while (true) {\n        const line = nextLine(lines2);\n        if (line === \"}\") {\n          return node;\n        } else {\n          node.children.push(readNode(lines2, line, list));\n        }\n      }\n    }\n    function toTHREEBone(source, list) {\n      const bone = new Bone();\n      list.push(bone);\n      bone.position.add(source.offset);\n      bone.name = source.name;\n      if (source.type !== \"ENDSITE\") {\n        for (let i = 0; i < source.children.length; i++) {\n          bone.add(toTHREEBone(source.children[i], list));\n        }\n      }\n      return bone;\n    }\n    function toTHREEAnimation(bones2) {\n      const tracks = [];\n      for (let i = 0; i < bones2.length; i++) {\n        const bone = bones2[i];\n        if (bone.type === \"ENDSITE\") continue;\n        const times = [];\n        const positions = [];\n        const rotations = [];\n        for (let j = 0; j < bone.frames.length; j++) {\n          const frame = bone.frames[j];\n          times.push(frame.time);\n          positions.push(frame.position.x + bone.offset.x);\n          positions.push(frame.position.y + bone.offset.y);\n          positions.push(frame.position.z + bone.offset.z);\n          rotations.push(frame.rotation.x);\n          rotations.push(frame.rotation.y);\n          rotations.push(frame.rotation.z);\n          rotations.push(frame.rotation.w);\n        }\n        if (scope.animateBonePositions) {\n          tracks.push(new VectorKeyframeTrack(\".bones[\" + bone.name + \"].position\", times, positions));\n        }\n        if (scope.animateBoneRotations) {\n          tracks.push(new QuaternionKeyframeTrack(\".bones[\" + bone.name + \"].quaternion\", times, rotations));\n        }\n      }\n      return new AnimationClip(\"animation\", -1, tracks);\n    }\n    function nextLine(lines2) {\n      let line;\n      while ((line = lines2.shift().trim()).length === 0) {}\n      return line;\n    }\n    const scope = this;\n    const lines = text.split(/[\\r\\n]+/g);\n    const bones = readBvh(lines);\n    const threeBones = [];\n    toTHREEBone(bones[0], threeBones);\n    const threeClip = toTHREEAnimation(bones);\n    return {\n      skeleton: new Skeleton(threeBones),\n      clip: threeClip\n    };\n  }\n}\nexport { BVHLoader };", "map": {"version": 3, "names": ["BVHLoader", "Loader", "constructor", "manager", "animateBonePositions", "animateBoneRotations", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "readBvh", "lines2", "nextLine", "list", "root", "readNode", "tokens", "split", "numFrames", "parseInt", "isNaN", "frameTime", "parseFloat", "i", "readFrameData", "data", "bone", "type", "keyframe", "time", "position", "Vector3", "rotation", "Quaternion", "frames", "push", "quat", "vx", "vy", "vz", "channels", "length", "x", "shift", "trim", "y", "z", "setFromAxisAngle", "Math", "PI", "multiply", "warn", "children", "firstline", "node", "name", "toUpperCase", "offset", "numChannels", "splice", "line", "toTHREEBone", "source", "Bone", "add", "toTHREEAnimation", "bones2", "tracks", "times", "positions", "rotations", "j", "frame", "w", "VectorKeyframeTrack", "QuaternionKeyframeTrack", "AnimationClip", "lines", "bones", "threeBones", "threeClip", "skeleton", "Skeleton", "clip"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/BVHLoader.js"], "sourcesContent": ["import {\n  Animation<PERSON><PERSON>,\n  Bone,\n  FileLoader,\n  Loader,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  Skeleton,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\n/**\n * Description: reads BVH files and outputs a single Skeleton and an AnimationClip\n *\n * Currently only supports bvh files containing a single root.\n *\n */\n\nclass BVHLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.animateBonePositions = true\n    this.animateBoneRotations = true\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text) {\n    /*\n\t\t\treads a string array (lines) from a BVH file\n\t\t\tand outputs a skeleton structure including motion data\n\n\t\t\treturns thee root node:\n\t\t\t{ name: '', channels: [], children: [] }\n\t\t*/\n    function readBvh(lines) {\n      // read model structure\n\n      if (nextLine(lines) !== 'HIERARCHY') {\n        console.error('THREE.BVHLoader: HIERARCHY expected.')\n      }\n\n      const list = [] // collects flat array of all bones\n      const root = readNode(lines, nextLine(lines), list)\n\n      // read motion data\n\n      if (nextLine(lines) !== 'MOTION') {\n        console.error('THREE.BVHLoader: MOTION expected.')\n      }\n\n      // number of frames\n\n      let tokens = nextLine(lines).split(/[\\s]+/)\n      const numFrames = parseInt(tokens[1])\n\n      if (isNaN(numFrames)) {\n        console.error('THREE.BVHLoader: Failed to read number of frames.')\n      }\n\n      // frame time\n\n      tokens = nextLine(lines).split(/[\\s]+/)\n      const frameTime = parseFloat(tokens[2])\n\n      if (isNaN(frameTime)) {\n        console.error('THREE.BVHLoader: Failed to read frame time.')\n      }\n\n      // read frame data line by line\n\n      for (let i = 0; i < numFrames; i++) {\n        tokens = nextLine(lines).split(/[\\s]+/)\n        readFrameData(tokens, i * frameTime, root)\n      }\n\n      return list\n    }\n\n    /*\n\t\t\tRecursively reads data from a single frame into the bone hierarchy.\n\t\t\tThe passed bone hierarchy has to be structured in the same order as the BVH file.\n\t\t\tkeyframe data is stored in bone.frames.\n\n\t\t\t- data: splitted string array (frame values), values are shift()ed so\n\t\t\tthis should be empty after parsing the whole hierarchy.\n\t\t\t- frameTime: playback time for this keyframe.\n\t\t\t- bone: the bone to read frame data from.\n\t\t*/\n    function readFrameData(data, frameTime, bone) {\n      // end sites have no motion data\n\n      if (bone.type === 'ENDSITE') return\n\n      // add keyframe\n\n      const keyframe = {\n        time: frameTime,\n        position: new Vector3(),\n        rotation: new Quaternion(),\n      }\n\n      bone.frames.push(keyframe)\n\n      const quat = new Quaternion()\n\n      const vx = new Vector3(1, 0, 0)\n      const vy = new Vector3(0, 1, 0)\n      const vz = new Vector3(0, 0, 1)\n\n      // parse values for each channel in node\n\n      for (let i = 0; i < bone.channels.length; i++) {\n        switch (bone.channels[i]) {\n          case 'Xposition':\n            keyframe.position.x = parseFloat(data.shift().trim())\n            break\n          case 'Yposition':\n            keyframe.position.y = parseFloat(data.shift().trim())\n            break\n          case 'Zposition':\n            keyframe.position.z = parseFloat(data.shift().trim())\n            break\n          case 'Xrotation':\n            quat.setFromAxisAngle(vx, (parseFloat(data.shift().trim()) * Math.PI) / 180)\n            keyframe.rotation.multiply(quat)\n            break\n          case 'Yrotation':\n            quat.setFromAxisAngle(vy, (parseFloat(data.shift().trim()) * Math.PI) / 180)\n            keyframe.rotation.multiply(quat)\n            break\n          case 'Zrotation':\n            quat.setFromAxisAngle(vz, (parseFloat(data.shift().trim()) * Math.PI) / 180)\n            keyframe.rotation.multiply(quat)\n            break\n          default:\n            console.warn('THREE.BVHLoader: Invalid channel type.')\n        }\n      }\n\n      // parse child nodes\n\n      for (let i = 0; i < bone.children.length; i++) {\n        readFrameData(data, frameTime, bone.children[i])\n      }\n    }\n\n    /*\n\t\t Recursively parses the HIERACHY section of the BVH file\n\n\t\t - lines: all lines of the file. lines are consumed as we go along.\n\t\t - firstline: line containing the node type and name e.g. 'JOINT hip'\n\t\t - list: collects a flat list of nodes\n\n\t\t returns: a BVH node including children\n\t\t*/\n    function readNode(lines, firstline, list) {\n      const node = { name: '', type: '', frames: [] }\n      list.push(node)\n\n      // parse node type and name\n\n      let tokens = firstline.split(/[\\s]+/)\n\n      if (tokens[0].toUpperCase() === 'END' && tokens[1].toUpperCase() === 'SITE') {\n        node.type = 'ENDSITE'\n        node.name = 'ENDSITE' // bvh end sites have no name\n      } else {\n        node.name = tokens[1]\n        node.type = tokens[0].toUpperCase()\n      }\n\n      if (nextLine(lines) !== '{') {\n        console.error('THREE.BVHLoader: Expected opening { after type & name')\n      }\n\n      // parse OFFSET\n\n      tokens = nextLine(lines).split(/[\\s]+/)\n\n      if (tokens[0] !== 'OFFSET') {\n        console.error('THREE.BVHLoader: Expected OFFSET but got: ' + tokens[0])\n      }\n\n      if (tokens.length !== 4) {\n        console.error('THREE.BVHLoader: Invalid number of values for OFFSET.')\n      }\n\n      const offset = new Vector3(parseFloat(tokens[1]), parseFloat(tokens[2]), parseFloat(tokens[3]))\n\n      if (isNaN(offset.x) || isNaN(offset.y) || isNaN(offset.z)) {\n        console.error('THREE.BVHLoader: Invalid values of OFFSET.')\n      }\n\n      node.offset = offset\n\n      // parse CHANNELS definitions\n\n      if (node.type !== 'ENDSITE') {\n        tokens = nextLine(lines).split(/[\\s]+/)\n\n        if (tokens[0] !== 'CHANNELS') {\n          console.error('THREE.BVHLoader: Expected CHANNELS definition.')\n        }\n\n        const numChannels = parseInt(tokens[1])\n        node.channels = tokens.splice(2, numChannels)\n        node.children = []\n      }\n\n      // read children\n\n      while (true) {\n        const line = nextLine(lines)\n\n        if (line === '}') {\n          return node\n        } else {\n          node.children.push(readNode(lines, line, list))\n        }\n      }\n    }\n\n    /*\n\t\t\trecursively converts the internal bvh node structure to a Bone hierarchy\n\n\t\t\tsource: the bvh root node\n\t\t\tlist: pass an empty array, collects a flat list of all converted THREE.Bones\n\n\t\t\treturns the root Bone\n\t\t*/\n    function toTHREEBone(source, list) {\n      const bone = new Bone()\n      list.push(bone)\n\n      bone.position.add(source.offset)\n      bone.name = source.name\n\n      if (source.type !== 'ENDSITE') {\n        for (let i = 0; i < source.children.length; i++) {\n          bone.add(toTHREEBone(source.children[i], list))\n        }\n      }\n\n      return bone\n    }\n\n    /*\n\t\t\tbuilds a AnimationClip from the keyframe data saved in each bone.\n\n\t\t\tbone: bvh root node\n\n\t\t\treturns: a AnimationClip containing position and quaternion tracks\n\t\t*/\n    function toTHREEAnimation(bones) {\n      const tracks = []\n\n      // create a position and quaternion animation track for each node\n\n      for (let i = 0; i < bones.length; i++) {\n        const bone = bones[i]\n\n        if (bone.type === 'ENDSITE') continue\n\n        // track data\n\n        const times = []\n        const positions = []\n        const rotations = []\n\n        for (let j = 0; j < bone.frames.length; j++) {\n          const frame = bone.frames[j]\n\n          times.push(frame.time)\n\n          // the animation system animates the position property,\n          // so we have to add the joint offset to all values\n\n          positions.push(frame.position.x + bone.offset.x)\n          positions.push(frame.position.y + bone.offset.y)\n          positions.push(frame.position.z + bone.offset.z)\n\n          rotations.push(frame.rotation.x)\n          rotations.push(frame.rotation.y)\n          rotations.push(frame.rotation.z)\n          rotations.push(frame.rotation.w)\n        }\n\n        if (scope.animateBonePositions) {\n          tracks.push(new VectorKeyframeTrack('.bones[' + bone.name + '].position', times, positions))\n        }\n\n        if (scope.animateBoneRotations) {\n          tracks.push(new QuaternionKeyframeTrack('.bones[' + bone.name + '].quaternion', times, rotations))\n        }\n      }\n\n      return new AnimationClip('animation', -1, tracks)\n    }\n\n    /*\n\t\t\treturns the next non-empty line in lines\n\t\t*/\n    function nextLine(lines) {\n      let line\n      // skip empty lines\n      while ((line = lines.shift().trim()).length === 0) {}\n\n      return line\n    }\n\n    const scope = this\n\n    const lines = text.split(/[\\r\\n]+/g)\n\n    const bones = readBvh(lines)\n\n    const threeBones = []\n    toTHREEBone(bones[0], threeBones)\n\n    const threeClip = toTHREEAnimation(bones)\n\n    return {\n      skeleton: new Skeleton(threeBones),\n      clip: threeClip,\n    }\n  }\n}\n\nexport { BVHLoader }\n"], "mappings": ";AAmBA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,oBAAA,GAAuB;IAC5B,KAAKC,oBAAA,GAAuB;EAC7B;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMR,OAAO;IAC3CS,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,gBAAA,CAAiBL,KAAA,CAAMM,aAAa;IAC3CL,MAAA,CAAOM,kBAAA,CAAmBP,KAAA,CAAMQ,eAAe;IAC/CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,IAAA,EAAM;MACd,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMR,OAAA,CAAQsB,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDW,MAAMD,IAAA,EAAM;IAQV,SAASM,QAAQC,MAAA,EAAO;MAGtB,IAAIC,QAAA,CAASD,MAAK,MAAM,aAAa;QACnCJ,OAAA,CAAQC,KAAA,CAAM,sCAAsC;MACrD;MAED,MAAMK,IAAA,GAAO,EAAE;MACf,MAAMC,IAAA,GAAOC,QAAA,CAASJ,MAAA,EAAOC,QAAA,CAASD,MAAK,GAAGE,IAAI;MAIlD,IAAID,QAAA,CAASD,MAAK,MAAM,UAAU;QAChCJ,OAAA,CAAQC,KAAA,CAAM,mCAAmC;MAClD;MAID,IAAIQ,MAAA,GAASJ,QAAA,CAASD,MAAK,EAAEM,KAAA,CAAM,OAAO;MAC1C,MAAMC,SAAA,GAAYC,QAAA,CAASH,MAAA,CAAO,CAAC,CAAC;MAEpC,IAAII,KAAA,CAAMF,SAAS,GAAG;QACpBX,OAAA,CAAQC,KAAA,CAAM,mDAAmD;MAClE;MAIDQ,MAAA,GAASJ,QAAA,CAASD,MAAK,EAAEM,KAAA,CAAM,OAAO;MACtC,MAAMI,SAAA,GAAYC,UAAA,CAAWN,MAAA,CAAO,CAAC,CAAC;MAEtC,IAAII,KAAA,CAAMC,SAAS,GAAG;QACpBd,OAAA,CAAQC,KAAA,CAAM,6CAA6C;MAC5D;MAID,SAASe,CAAA,GAAI,GAAGA,CAAA,GAAIL,SAAA,EAAWK,CAAA,IAAK;QAClCP,MAAA,GAASJ,QAAA,CAASD,MAAK,EAAEM,KAAA,CAAM,OAAO;QACtCO,aAAA,CAAcR,MAAA,EAAQO,CAAA,GAAIF,SAAA,EAAWP,IAAI;MAC1C;MAED,OAAOD,IAAA;IACR;IAYD,SAASW,cAAcC,IAAA,EAAMJ,SAAA,EAAWK,IAAA,EAAM;MAG5C,IAAIA,IAAA,CAAKC,IAAA,KAAS,WAAW;MAI7B,MAAMC,QAAA,GAAW;QACfC,IAAA,EAAMR,SAAA;QACNS,QAAA,EAAU,IAAIC,OAAA,CAAS;QACvBC,QAAA,EAAU,IAAIC,UAAA,CAAY;MAC3B;MAEDP,IAAA,CAAKQ,MAAA,CAAOC,IAAA,CAAKP,QAAQ;MAEzB,MAAMQ,IAAA,GAAO,IAAIH,UAAA,CAAY;MAE7B,MAAMI,EAAA,GAAK,IAAIN,OAAA,CAAQ,GAAG,GAAG,CAAC;MAC9B,MAAMO,EAAA,GAAK,IAAIP,OAAA,CAAQ,GAAG,GAAG,CAAC;MAC9B,MAAMQ,EAAA,GAAK,IAAIR,OAAA,CAAQ,GAAG,GAAG,CAAC;MAI9B,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAIG,IAAA,CAAKc,QAAA,CAASC,MAAA,EAAQlB,CAAA,IAAK;QAC7C,QAAQG,IAAA,CAAKc,QAAA,CAASjB,CAAC;UACrB,KAAK;YACHK,QAAA,CAASE,QAAA,CAASY,CAAA,GAAIpB,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAK,EAAGC,IAAA,EAAM;YACpD;UACF,KAAK;YACHhB,QAAA,CAASE,QAAA,CAASe,CAAA,GAAIvB,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAK,EAAGC,IAAA,EAAM;YACpD;UACF,KAAK;YACHhB,QAAA,CAASE,QAAA,CAASgB,CAAA,GAAIxB,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAK,EAAGC,IAAA,EAAM;YACpD;UACF,KAAK;YACHR,IAAA,CAAKW,gBAAA,CAAiBV,EAAA,EAAKf,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAO,EAACC,IAAA,CAAI,CAAE,IAAII,IAAA,CAAKC,EAAA,GAAM,GAAG;YAC3ErB,QAAA,CAASI,QAAA,CAASkB,QAAA,CAASd,IAAI;YAC/B;UACF,KAAK;YACHA,IAAA,CAAKW,gBAAA,CAAiBT,EAAA,EAAKhB,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAO,EAACC,IAAA,CAAI,CAAE,IAAII,IAAA,CAAKC,EAAA,GAAM,GAAG;YAC3ErB,QAAA,CAASI,QAAA,CAASkB,QAAA,CAASd,IAAI;YAC/B;UACF,KAAK;YACHA,IAAA,CAAKW,gBAAA,CAAiBR,EAAA,EAAKjB,UAAA,CAAWG,IAAA,CAAKkB,KAAA,CAAO,EAACC,IAAA,CAAI,CAAE,IAAII,IAAA,CAAKC,EAAA,GAAM,GAAG;YAC3ErB,QAAA,CAASI,QAAA,CAASkB,QAAA,CAASd,IAAI;YAC/B;UACF;YACE7B,OAAA,CAAQ4C,IAAA,CAAK,wCAAwC;QACxD;MACF;MAID,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAIG,IAAA,CAAK0B,QAAA,CAASX,MAAA,EAAQlB,CAAA,IAAK;QAC7CC,aAAA,CAAcC,IAAA,EAAMJ,SAAA,EAAWK,IAAA,CAAK0B,QAAA,CAAS7B,CAAC,CAAC;MAChD;IACF;IAWD,SAASR,SAASJ,MAAA,EAAO0C,SAAA,EAAWxC,IAAA,EAAM;MACxC,MAAMyC,IAAA,GAAO;QAAEC,IAAA,EAAM;QAAI5B,IAAA,EAAM;QAAIO,MAAA,EAAQ;MAAI;MAC/CrB,IAAA,CAAKsB,IAAA,CAAKmB,IAAI;MAId,IAAItC,MAAA,GAASqC,SAAA,CAAUpC,KAAA,CAAM,OAAO;MAEpC,IAAID,MAAA,CAAO,CAAC,EAAEwC,WAAA,OAAkB,SAASxC,MAAA,CAAO,CAAC,EAAEwC,WAAA,CAAa,MAAK,QAAQ;QAC3EF,IAAA,CAAK3B,IAAA,GAAO;QACZ2B,IAAA,CAAKC,IAAA,GAAO;MACpB,OAAa;QACLD,IAAA,CAAKC,IAAA,GAAOvC,MAAA,CAAO,CAAC;QACpBsC,IAAA,CAAK3B,IAAA,GAAOX,MAAA,CAAO,CAAC,EAAEwC,WAAA,CAAa;MACpC;MAED,IAAI5C,QAAA,CAASD,MAAK,MAAM,KAAK;QAC3BJ,OAAA,CAAQC,KAAA,CAAM,uDAAuD;MACtE;MAIDQ,MAAA,GAASJ,QAAA,CAASD,MAAK,EAAEM,KAAA,CAAM,OAAO;MAEtC,IAAID,MAAA,CAAO,CAAC,MAAM,UAAU;QAC1BT,OAAA,CAAQC,KAAA,CAAM,+CAA+CQ,MAAA,CAAO,CAAC,CAAC;MACvE;MAED,IAAIA,MAAA,CAAOyB,MAAA,KAAW,GAAG;QACvBlC,OAAA,CAAQC,KAAA,CAAM,uDAAuD;MACtE;MAED,MAAMiD,MAAA,GAAS,IAAI1B,OAAA,CAAQT,UAAA,CAAWN,MAAA,CAAO,CAAC,CAAC,GAAGM,UAAA,CAAWN,MAAA,CAAO,CAAC,CAAC,GAAGM,UAAA,CAAWN,MAAA,CAAO,CAAC,CAAC,CAAC;MAE9F,IAAII,KAAA,CAAMqC,MAAA,CAAOf,CAAC,KAAKtB,KAAA,CAAMqC,MAAA,CAAOZ,CAAC,KAAKzB,KAAA,CAAMqC,MAAA,CAAOX,CAAC,GAAG;QACzDvC,OAAA,CAAQC,KAAA,CAAM,4CAA4C;MAC3D;MAED8C,IAAA,CAAKG,MAAA,GAASA,MAAA;MAId,IAAIH,IAAA,CAAK3B,IAAA,KAAS,WAAW;QAC3BX,MAAA,GAASJ,QAAA,CAASD,MAAK,EAAEM,KAAA,CAAM,OAAO;QAEtC,IAAID,MAAA,CAAO,CAAC,MAAM,YAAY;UAC5BT,OAAA,CAAQC,KAAA,CAAM,gDAAgD;QAC/D;QAED,MAAMkD,WAAA,GAAcvC,QAAA,CAASH,MAAA,CAAO,CAAC,CAAC;QACtCsC,IAAA,CAAKd,QAAA,GAAWxB,MAAA,CAAO2C,MAAA,CAAO,GAAGD,WAAW;QAC5CJ,IAAA,CAAKF,QAAA,GAAW,EAAE;MACnB;MAID,OAAO,MAAM;QACX,MAAMQ,IAAA,GAAOhD,QAAA,CAASD,MAAK;QAE3B,IAAIiD,IAAA,KAAS,KAAK;UAChB,OAAON,IAAA;QACjB,OAAe;UACLA,IAAA,CAAKF,QAAA,CAASjB,IAAA,CAAKpB,QAAA,CAASJ,MAAA,EAAOiD,IAAA,EAAM/C,IAAI,CAAC;QAC/C;MACF;IACF;IAUD,SAASgD,YAAYC,MAAA,EAAQjD,IAAA,EAAM;MACjC,MAAMa,IAAA,GAAO,IAAIqC,IAAA,CAAM;MACvBlD,IAAA,CAAKsB,IAAA,CAAKT,IAAI;MAEdA,IAAA,CAAKI,QAAA,CAASkC,GAAA,CAAIF,MAAA,CAAOL,MAAM;MAC/B/B,IAAA,CAAK6B,IAAA,GAAOO,MAAA,CAAOP,IAAA;MAEnB,IAAIO,MAAA,CAAOnC,IAAA,KAAS,WAAW;QAC7B,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAIuC,MAAA,CAAOV,QAAA,CAASX,MAAA,EAAQlB,CAAA,IAAK;UAC/CG,IAAA,CAAKsC,GAAA,CAAIH,WAAA,CAAYC,MAAA,CAAOV,QAAA,CAAS7B,CAAC,GAAGV,IAAI,CAAC;QAC/C;MACF;MAED,OAAOa,IAAA;IACR;IASD,SAASuC,iBAAiBC,MAAA,EAAO;MAC/B,MAAMC,MAAA,GAAS,EAAE;MAIjB,SAAS5C,CAAA,GAAI,GAAGA,CAAA,GAAI2C,MAAA,CAAMzB,MAAA,EAAQlB,CAAA,IAAK;QACrC,MAAMG,IAAA,GAAOwC,MAAA,CAAM3C,CAAC;QAEpB,IAAIG,IAAA,CAAKC,IAAA,KAAS,WAAW;QAI7B,MAAMyC,KAAA,GAAQ,EAAE;QAChB,MAAMC,SAAA,GAAY,EAAE;QACpB,MAAMC,SAAA,GAAY,EAAE;QAEpB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI7C,IAAA,CAAKQ,MAAA,CAAOO,MAAA,EAAQ8B,CAAA,IAAK;UAC3C,MAAMC,KAAA,GAAQ9C,IAAA,CAAKQ,MAAA,CAAOqC,CAAC;UAE3BH,KAAA,CAAMjC,IAAA,CAAKqC,KAAA,CAAM3C,IAAI;UAKrBwC,SAAA,CAAUlC,IAAA,CAAKqC,KAAA,CAAM1C,QAAA,CAASY,CAAA,GAAIhB,IAAA,CAAK+B,MAAA,CAAOf,CAAC;UAC/C2B,SAAA,CAAUlC,IAAA,CAAKqC,KAAA,CAAM1C,QAAA,CAASe,CAAA,GAAInB,IAAA,CAAK+B,MAAA,CAAOZ,CAAC;UAC/CwB,SAAA,CAAUlC,IAAA,CAAKqC,KAAA,CAAM1C,QAAA,CAASgB,CAAA,GAAIpB,IAAA,CAAK+B,MAAA,CAAOX,CAAC;UAE/CwB,SAAA,CAAUnC,IAAA,CAAKqC,KAAA,CAAMxC,QAAA,CAASU,CAAC;UAC/B4B,SAAA,CAAUnC,IAAA,CAAKqC,KAAA,CAAMxC,QAAA,CAASa,CAAC;UAC/ByB,SAAA,CAAUnC,IAAA,CAAKqC,KAAA,CAAMxC,QAAA,CAASc,CAAC;UAC/BwB,SAAA,CAAUnC,IAAA,CAAKqC,KAAA,CAAMxC,QAAA,CAASyC,CAAC;QAChC;QAED,IAAI9E,KAAA,CAAMP,oBAAA,EAAsB;UAC9B+E,MAAA,CAAOhC,IAAA,CAAK,IAAIuC,mBAAA,CAAoB,YAAYhD,IAAA,CAAK6B,IAAA,GAAO,cAAca,KAAA,EAAOC,SAAS,CAAC;QAC5F;QAED,IAAI1E,KAAA,CAAMN,oBAAA,EAAsB;UAC9B8E,MAAA,CAAOhC,IAAA,CAAK,IAAIwC,uBAAA,CAAwB,YAAYjD,IAAA,CAAK6B,IAAA,GAAO,gBAAgBa,KAAA,EAAOE,SAAS,CAAC;QAClG;MACF;MAED,OAAO,IAAIM,aAAA,CAAc,aAAa,IAAIT,MAAM;IACjD;IAKD,SAASvD,SAASD,MAAA,EAAO;MACvB,IAAIiD,IAAA;MAEJ,QAAQA,IAAA,GAAOjD,MAAA,CAAMgC,KAAA,CAAK,EAAGC,IAAA,IAAQH,MAAA,KAAW,GAAG,CAAE;MAErD,OAAOmB,IAAA;IACR;IAED,MAAMjE,KAAA,GAAQ;IAEd,MAAMkF,KAAA,GAAQzE,IAAA,CAAKa,KAAA,CAAM,UAAU;IAEnC,MAAM6D,KAAA,GAAQpE,OAAA,CAAQmE,KAAK;IAE3B,MAAME,UAAA,GAAa,EAAE;IACrBlB,WAAA,CAAYiB,KAAA,CAAM,CAAC,GAAGC,UAAU;IAEhC,MAAMC,SAAA,GAAYf,gBAAA,CAAiBa,KAAK;IAExC,OAAO;MACLG,QAAA,EAAU,IAAIC,QAAA,CAASH,UAAU;MACjCI,IAAA,EAAMH;IACP;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}