{"ast": null, "code": "import * as THREE from 'three';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\nimport { shaderStructs, shaderIntersectFunction, MeshBVHUniformStruct } from 'three-mesh-bvh';\nimport { version } from '../helpers/constants.js';\n\n// Author: N8Programs\nconst MeshRefractionMaterial = /* @__PURE__ */shaderMaterial({\n  envMap: null,\n  bounces: 3,\n  ior: 2.4,\n  correctMips: true,\n  aberrationStrength: 0.01,\n  fresnel: 0,\n  bvh: /* @__PURE__ */new MeshBVHUniformStruct(),\n  color: /* @__PURE__ */new THREE.Color('white'),\n  opacity: 1,\n  resolution: /* @__PURE__ */new THREE.Vector2(),\n  viewMatrixInverse: /* @__PURE__ */new THREE.Matrix4(),\n  projectionMatrixInverse: /* @__PURE__ */new THREE.Matrix4()\n}, /*glsl*/`\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_vertex>\n\n  void main() {\n    #include <color_vertex>\n\n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }`, /*glsl*/`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n\n  uniform float bounces;\n  ${shaderStructs}\n  ${shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  uniform float opacity;\n\n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);\n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n\n  #include <common>\n  #include <cube_uv_reflection_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n\n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n\n    vec4 diffuseColor = vec4(color, opacity);\n    #include <color_fragment>\n\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA\n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);\n\n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n  }`);\nexport { MeshRefractionMaterial };", "map": {"version": 3, "names": ["THREE", "shaderMaterial", "shaderStructs", "shaderIntersectFunction", "MeshBVHUniformStruct", "version", "MeshRefractionMaterial", "envMap", "bounces", "ior", "correctMips", "aberrationStrength", "fresnel", "bvh", "color", "Color", "opacity", "resolution", "Vector2", "viewMatrixInverse", "Matrix4", "projectionMatrixInverse"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/materials/MeshRefractionMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\nimport { shaderStructs, shaderIntersectFunction, MeshBVHUniformStruct } from 'three-mesh-bvh';\nimport { version } from '../helpers/constants.js';\n\n// Author: N8Programs\nconst MeshRefractionMaterial = /* @__PURE__ */shaderMaterial({\n  envMap: null,\n  bounces: 3,\n  ior: 2.4,\n  correctMips: true,\n  aberrationStrength: 0.01,\n  fresnel: 0,\n  bvh: /* @__PURE__ */new MeshBVHUniformStruct(),\n  color: /* @__PURE__ */new THREE.Color('white'),\n  opacity: 1,\n  resolution: /* @__PURE__ */new THREE.Vector2(),\n  viewMatrixInverse: /* @__PURE__ */new THREE.Matrix4(),\n  projectionMatrixInverse: /* @__PURE__ */new THREE.Matrix4()\n}, /*glsl*/`\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_vertex>\n\n  void main() {\n    #include <color_vertex>\n\n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }`, /*glsl*/`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n\n  uniform float bounces;\n  ${shaderStructs}\n  ${shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  uniform float opacity;\n\n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);\n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n\n  #include <common>\n  #include <cube_uv_reflection_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n\n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n\n    vec4 diffuseColor = vec4(color, opacity);\n    #include <color_fragment>\n\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA\n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);\n\n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n  }`);\n\nexport { MeshRefractionMaterial };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,aAAa,EAAEC,uBAAuB,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC7F,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AACA,MAAMC,sBAAsB,GAAG,eAAeL,cAAc,CAAC;EAC3DM,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,IAAI;EACjBC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE,eAAe,IAAIT,oBAAoB,CAAC,CAAC;EAC9CU,KAAK,EAAE,eAAe,IAAId,KAAK,CAACe,KAAK,CAAC,OAAO,CAAC;EAC9CC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,eAAe,IAAIjB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAC9CC,iBAAiB,EAAE,eAAe,IAAInB,KAAK,CAACoB,OAAO,CAAC,CAAC;EACrDC,uBAAuB,EAAE,eAAe,IAAIrB,KAAK,CAACoB,OAAO,CAAC;AAC5D,CAAC,EAAE,QAAQ;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,EAAE,QAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIlB,aAAa;AACjB,IAAIC,uBAAuB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBE,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC7E,IAAI,CAAC;AAEL,SAASC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}