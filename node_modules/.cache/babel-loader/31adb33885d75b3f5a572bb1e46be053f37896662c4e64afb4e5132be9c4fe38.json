{"ast": null, "code": "import { Mesh, InstancedInterleavedBuffer, InterleavedBufferAttribute, Vector3, Vector4 } from \"three\";\nimport { LineSegmentsGeometry } from \"./LineSegmentsGeometry.js\";\nimport { LineMaterial } from \"./LineMaterial.js\";\nconst _start = /* @__PURE__ */new Vector3();\nconst _end = /* @__PURE__ */new Vector3();\nconst _viewport = /* @__PURE__ */new Vector4();\nclass Wireframe extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({\n    color: Math.random() * 16777215\n  })) {\n    super(geometry, material);\n    this.isWireframe = true;\n    this.type = \"Wireframe\";\n  }\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n  computeLineDistances() {\n    const geometry = this.geometry;\n    const instanceStart = geometry.attributes.instanceStart;\n    const instanceEnd = geometry.attributes.instanceEnd;\n    const lineDistances = new Float32Array(2 * instanceStart.count);\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i);\n      _end.fromBufferAttribute(instanceEnd, i);\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1];\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end);\n    }\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1);\n    geometry.setAttribute(\"instanceDistanceStart\", new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0));\n    geometry.setAttribute(\"instanceDistanceEnd\", new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1));\n    return this;\n  }\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms;\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport);\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w);\n    }\n  }\n}\nexport { Wireframe };", "map": {"version": 3, "names": ["_start", "Vector3", "_end", "_viewport", "Vector4", "Wireframe", "<PERSON><PERSON>", "constructor", "geometry", "LineSegmentsGeometry", "material", "LineMaterial", "color", "Math", "random", "isWireframe", "type", "computeLineDistances", "instanceStart", "attributes", "instanceEnd", "lineDistances", "Float32Array", "count", "i", "j", "l", "fromBufferAttribute", "distanceTo", "instanceDistanceBuffer", "InstancedInterleavedBuffer", "setAttribute", "InterleavedBufferAttribute", "onBeforeRender", "renderer", "uniforms", "resolution", "getViewport", "value", "set", "z", "w"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/lines/Wireframe.js"], "sourcesContent": ["import { InstancedInterleavedBuffer, InterleavedBuffer<PERSON>ttribute, Mesh, Vector3, Vector4 } from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nclass Wireframe extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isWireframe = true\n\n    this.type = 'Wireframe'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { Wireframe }\n"], "mappings": ";;;AAIA,MAAMA,MAAA,GAAyB,mBAAIC,OAAA,CAAS;AAC5C,MAAMC,IAAA,GAAuB,mBAAID,OAAA,CAAS;AAC1C,MAAME,SAAA,GAA4B,mBAAIC,OAAA,CAAS;AAE/C,MAAMC,SAAA,SAAkBC,IAAA,CAAK;EAC3BC,YAAYC,QAAA,GAAW,IAAIC,oBAAA,CAAsB,GAAEC,QAAA,GAAW,IAAIC,YAAA,CAAa;IAAEC,KAAA,EAAOC,IAAA,CAAKC,MAAA,KAAW;EAAU,IAAG;IACnH,MAAMN,QAAA,EAAUE,QAAQ;IAExB,KAAKK,WAAA,GAAc;IAEnB,KAAKC,IAAA,GAAO;EACb;EAAA;EAIDC,qBAAA,EAAuB;IACrB,MAAMT,QAAA,GAAW,KAAKA,QAAA;IAEtB,MAAMU,aAAA,GAAgBV,QAAA,CAASW,UAAA,CAAWD,aAAA;IAC1C,MAAME,WAAA,GAAcZ,QAAA,CAASW,UAAA,CAAWC,WAAA;IACxC,MAAMC,aAAA,GAAgB,IAAIC,YAAA,CAAa,IAAIJ,aAAA,CAAcK,KAAK;IAE9D,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,CAAA,GAAIR,aAAA,CAAcK,KAAA,EAAOC,CAAA,GAAIE,CAAA,EAAGF,CAAA,IAAKC,CAAA,IAAK,GAAG;MAClEzB,MAAA,CAAO2B,mBAAA,CAAoBT,aAAA,EAAeM,CAAC;MAC3CtB,IAAA,CAAKyB,mBAAA,CAAoBP,WAAA,EAAaI,CAAC;MAEvCH,aAAA,CAAcI,CAAC,IAAIA,CAAA,KAAM,IAAI,IAAIJ,aAAA,CAAcI,CAAA,GAAI,CAAC;MACpDJ,aAAA,CAAcI,CAAA,GAAI,CAAC,IAAIJ,aAAA,CAAcI,CAAC,IAAIzB,MAAA,CAAO4B,UAAA,CAAW1B,IAAI;IACjE;IAED,MAAM2B,sBAAA,GAAyB,IAAIC,0BAAA,CAA2BT,aAAA,EAAe,GAAG,CAAC;IAEjFb,QAAA,CAASuB,YAAA,CAAa,yBAAyB,IAAIC,0BAAA,CAA2BH,sBAAA,EAAwB,GAAG,CAAC,CAAC;IAC3GrB,QAAA,CAASuB,YAAA,CAAa,uBAAuB,IAAIC,0BAAA,CAA2BH,sBAAA,EAAwB,GAAG,CAAC,CAAC;IAEzG,OAAO;EACR;EAEDI,eAAeC,QAAA,EAAU;IACvB,MAAMC,QAAA,GAAW,KAAKzB,QAAA,CAASyB,QAAA;IAE/B,IAAIA,QAAA,IAAYA,QAAA,CAASC,UAAA,EAAY;MACnCF,QAAA,CAASG,WAAA,CAAYlC,SAAS;MAC9B,KAAKO,QAAA,CAASyB,QAAA,CAASC,UAAA,CAAWE,KAAA,CAAMC,GAAA,CAAIpC,SAAA,CAAUqC,CAAA,EAAGrC,SAAA,CAAUsC,CAAC;IACrE;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}