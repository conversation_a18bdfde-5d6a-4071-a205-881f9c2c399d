{"ast": null, "code": "import { Color } from \"three\";\nconst LuminosityHighPassShader = {\n  shaderID: \"luminosityHighPass\",\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    luminosityThreshold: {\n      value: 1\n    },\n    smoothWidth: {\n      value: 1\n    },\n    defaultColor: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    defaultOpacity: {\n      value: 0\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform vec3 defaultColor;\n    uniform float defaultOpacity;\n    uniform float luminosityThreshold;\n    uniform float smoothWidth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tvec4 outputColor = vec4( defaultColor.rgb, defaultOpacity );\n\n    \tfloat alpha = smoothstep( luminosityThreshold, luminosityThreshold + smoothWidth, v );\n\n    \tgl_FragColor = mix( outputColor, texel, alpha );\n\n    }\n  `)\n};\nexport { LuminosityHighPassShader };", "map": {"version": 3, "names": ["LuminosityHighPass<PERSON><PERSON>er", "shaderID", "uniforms", "tDiffuse", "value", "luminosityThreshold", "smoothWidth", "defaultColor", "Color", "defaultOpacity", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/LuminosityHighPassShader.ts"], "sourcesContent": ["import { Color } from 'three'\n\n/**\n * Luminosity\n * http://en.wikipedia.org/wiki/Luminosity\n */\n\nexport const LuminosityHighPassShader = {\n  shaderID: 'luminosityHighPass',\n\n  uniforms: {\n    tDiffuse: { value: null },\n    luminosityThreshold: { value: 1.0 },\n    smoothWidth: { value: 1.0 },\n    defaultColor: { value: /* @__PURE__ */ new Color(0x000000) },\n    defaultOpacity: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform vec3 defaultColor;\n    uniform float defaultOpacity;\n    uniform float luminosityThreshold;\n    uniform float smoothWidth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tvec4 outputColor = vec4( defaultColor.rgb, defaultOpacity );\n\n    \tfloat alpha = smoothstep( luminosityThreshold, luminosityThreshold + smoothWidth, v );\n\n    \tgl_FragColor = mix( outputColor, texel, alpha );\n\n    }\n  `,\n}\n"], "mappings": ";AAOO,MAAMA,wBAAA,GAA2B;EACtCC,QAAA,EAAU;EAEVC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,mBAAA,EAAqB;MAAED,KAAA,EAAO;IAAI;IAClCE,WAAA,EAAa;MAAEF,KAAA,EAAO;IAAI;IAC1BG,YAAA,EAAc;MAAEH,KAAA,EAA2B,mBAAAI,KAAA,CAAM,CAAQ;IAAE;IAC3DC,cAAA,EAAgB;MAAEL,KAAA,EAAO;IAAI;EAC/B;EAEAM,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}