{"ast": null, "code": "import * as React from 'react';\nconst easeInExpo = x => x === 0 ? 0 : Math.pow(2, 10 * x - 10);\nfunction Backdrop({\n  children,\n  floor = 0.25,\n  segments = 20,\n  receiveShadow,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    let i = 0;\n    const offset = segments / segments / 2;\n    const position = ref.current.attributes.position;\n    for (let x = 0; x < segments + 1; x++) {\n      for (let y = 0; y < segments + 1; y++) {\n        position.setXYZ(i++, x / segments - offset + (x === 0 ? -floor : 0), y / segments - offset, easeInExpo(x / segments));\n      }\n    }\n    position.needsUpdate = true;\n    ref.current.computeVertexNormals();\n  }, [segments, floor]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: receiveShadow,\n    rotation: [-Math.PI / 2, 0, Math.PI / 2]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    ref: ref,\n    args: [1, 1, segments, segments]\n  }), children));\n}\nexport { Backdrop };", "map": {"version": 3, "names": ["React", "easeInExpo", "x", "Math", "pow", "Backdrop", "children", "floor", "segments", "receiveShadow", "props", "ref", "useRef", "useLayoutEffect", "i", "offset", "position", "current", "attributes", "y", "setXYZ", "needsUpdate", "computeVertexNormals", "createElement", "rotation", "PI", "args"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Backdrop.js"], "sourcesContent": ["import * as React from 'react';\n\nconst easeInExpo = x => x === 0 ? 0 : Math.pow(2, 10 * x - 10);\nfunction Backdrop({\n  children,\n  floor = 0.25,\n  segments = 20,\n  receiveShadow,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    let i = 0;\n    const offset = segments / segments / 2;\n    const position = ref.current.attributes.position;\n    for (let x = 0; x < segments + 1; x++) {\n      for (let y = 0; y < segments + 1; y++) {\n        position.setXYZ(i++, x / segments - offset + (x === 0 ? -floor : 0), y / segments - offset, easeInExpo(x / segments));\n      }\n    }\n    position.needsUpdate = true;\n    ref.current.computeVertexNormals();\n  }, [segments, floor]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: receiveShadow,\n    rotation: [-Math.PI / 2, 0, Math.PI / 2]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    ref: ref,\n    args: [1, 1, segments, segments]\n  }), children));\n}\n\nexport { Backdrop };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,UAAU,GAAGC,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGF,CAAC,GAAG,EAAE,CAAC;AAC9D,SAASG,QAAQA,CAAC;EAChBC,QAAQ;EACRC,KAAK,GAAG,IAAI;EACZC,QAAQ,GAAG,EAAE;EACbC,aAAa;EACb,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAC9BZ,KAAK,CAACa,eAAe,CAAC,MAAM;IAC1B,IAAIC,CAAC,GAAG,CAAC;IACT,MAAMC,MAAM,GAAGP,QAAQ,GAAGA,QAAQ,GAAG,CAAC;IACtC,MAAMQ,QAAQ,GAAGL,GAAG,CAACM,OAAO,CAACC,UAAU,CAACF,QAAQ;IAChD,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,QAAQ,GAAG,CAAC,EAAEN,CAAC,EAAE,EAAE;MACrC,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,QAAQ,GAAG,CAAC,EAAEW,CAAC,EAAE,EAAE;QACrCH,QAAQ,CAACI,MAAM,CAACN,CAAC,EAAE,EAAEZ,CAAC,GAAGM,QAAQ,GAAGO,MAAM,IAAIb,CAAC,KAAK,CAAC,GAAG,CAACK,KAAK,GAAG,CAAC,CAAC,EAAEY,CAAC,GAAGX,QAAQ,GAAGO,MAAM,EAAEd,UAAU,CAACC,CAAC,GAAGM,QAAQ,CAAC,CAAC;MACvH;IACF;IACAQ,QAAQ,CAACK,WAAW,GAAG,IAAI;IAC3BV,GAAG,CAACM,OAAO,CAACK,oBAAoB,CAAC,CAAC;EACpC,CAAC,EAAE,CAACd,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,aAAaP,KAAK,CAACuB,aAAa,CAAC,OAAO,EAAEb,KAAK,EAAE,aAAaV,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;IAC/Fd,aAAa,EAAEA,aAAa;IAC5Be,QAAQ,EAAE,CAAC,CAACrB,IAAI,CAACsB,EAAE,GAAG,CAAC,EAAE,CAAC,EAAEtB,IAAI,CAACsB,EAAE,GAAG,CAAC;EACzC,CAAC,EAAE,aAAazB,KAAK,CAACuB,aAAa,CAAC,eAAe,EAAE;IACnDZ,GAAG,EAAEA,GAAG;IACRe,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAElB,QAAQ,EAAEA,QAAQ;EACjC,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC;AAChB;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}