{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/contexts/ThemeContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [theme, setTheme] = useState(() => {\n    // Get theme from localStorage or default to 'system'\n    const savedTheme = localStorage.getItem('theme');\n    return savedTheme || 'system';\n  });\n  const [resolvedTheme, setResolvedTheme] = useState(() => {\n    // Initialize with system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  });\n\n  // Function to get system preference\n  const getSystemTheme = () => {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  };\n\n  // Update resolved theme based on current theme setting\n  useEffect(() => {\n    const updateResolvedTheme = () => {\n      if (theme === 'system') {\n        setResolvedTheme(getSystemTheme());\n      } else {\n        setResolvedTheme(theme);\n      }\n    };\n    updateResolvedTheme();\n\n    // Listen for system theme changes when theme is set to 'system'\n    if (theme === 'system') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      const handleChange = () => updateResolvedTheme();\n      mediaQuery.addEventListener('change', handleChange);\n      return () => mediaQuery.removeEventListener('change', handleChange);\n    }\n  }, [theme]);\n\n  // Apply theme to document\n  useEffect(() => {\n    const root = document.documentElement;\n\n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n\n    // Add current theme class\n    root.classList.add(resolvedTheme);\n\n    // Save theme to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme, resolvedTheme]);\n  const handleSetTheme = newTheme => {\n    setTheme(newTheme);\n  };\n  const value = {\n    theme,\n    setTheme: handleSetTheme,\n    resolvedTheme\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"C/gyPukSLiAN/RyyIgkjg31El1Q=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "theme", "setTheme", "savedTheme", "localStorage", "getItem", "resolvedTheme", "setResolvedTheme", "window", "matchMedia", "matches", "getSystemTheme", "updateResolvedTheme", "mediaQuery", "handleChange", "addEventListener", "removeEventListener", "root", "document", "documentElement", "classList", "remove", "add", "setItem", "handleSetTheme", "newTheme", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/contexts/ThemeContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\n\nexport type Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  resolvedTheme: 'light' | 'dark';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>(() => {\n    // Get theme from localStorage or default to 'system'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    return savedTheme || 'system';\n  });\n\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>(() => {\n    // Initialize with system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  });\n\n  // Function to get system preference\n  const getSystemTheme = (): 'light' | 'dark' => {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  };\n\n  // Update resolved theme based on current theme setting\n  useEffect(() => {\n    const updateResolvedTheme = () => {\n      if (theme === 'system') {\n        setResolvedTheme(getSystemTheme());\n      } else {\n        setResolvedTheme(theme);\n      }\n    };\n\n    updateResolvedTheme();\n\n    // Listen for system theme changes when theme is set to 'system'\n    if (theme === 'system') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      const handleChange = () => updateResolvedTheme();\n      \n      mediaQuery.addEventListener('change', handleChange);\n      return () => mediaQuery.removeEventListener('change', handleChange);\n    }\n  }, [theme]);\n\n  // Apply theme to document\n  useEffect(() => {\n    const root = document.documentElement;\n    \n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(resolvedTheme);\n    \n    // Save theme to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme, resolvedTheme]);\n\n  const handleSetTheme = (newTheme: Theme) => {\n    setTheme(newTheme);\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    setTheme: handleSetTheme,\n    resolvedTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU9E,MAAMC,YAAY,gBAAGN,aAAa,CAA+BO,SAAS,CAAC;AAE3E,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGT,UAAU,CAACK,YAAY,CAAC;EACxC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAYrB,OAAO,MAAMI,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC3E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAQ,MAAM;IAC9C;IACA,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAU;IACzD,OAAOF,UAAU,IAAI,QAAQ;EAC/B,CAAC,CAAC;EAEF,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAmB,MAAM;IACzE;IACA,OAAOmB,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,GAAG,MAAM,GAAG,OAAO;EACrF,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAwB;IAC7C,OAAOH,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,GAAG,MAAM,GAAG,OAAO;EACrF,CAAC;;EAED;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAIX,KAAK,KAAK,QAAQ,EAAE;QACtBM,gBAAgB,CAACI,cAAc,CAAC,CAAC,CAAC;MACpC,CAAC,MAAM;QACLJ,gBAAgB,CAACN,KAAK,CAAC;MACzB;IACF,CAAC;IAEDW,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIX,KAAK,KAAK,QAAQ,EAAE;MACtB,MAAMY,UAAU,GAAGL,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;MACpE,MAAMK,YAAY,GAAGA,CAAA,KAAMF,mBAAmB,CAAC,CAAC;MAEhDC,UAAU,CAACE,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;MACnD,OAAO,MAAMD,UAAU,CAACG,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACrE;EACF,CAAC,EAAE,CAACb,KAAK,CAAC,CAAC;;EAEX;EACAb,SAAS,CAAC,MAAM;IACd,MAAM6B,IAAI,GAAGC,QAAQ,CAACC,eAAe;;IAErC;IACAF,IAAI,CAACG,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC;;IAEtC;IACAJ,IAAI,CAACG,SAAS,CAACE,GAAG,CAAChB,aAAa,CAAC;;IAEjC;IACAF,YAAY,CAACmB,OAAO,CAAC,OAAO,EAAEtB,KAAK,CAAC;EACtC,CAAC,EAAE,CAACA,KAAK,EAAEK,aAAa,CAAC,CAAC;EAE1B,MAAMkB,cAAc,GAAIC,QAAe,IAAK;IAC1CvB,QAAQ,CAACuB,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMC,KAAuB,GAAG;IAC9BzB,KAAK;IACLC,QAAQ,EAAEsB,cAAc;IACxBlB;EACF,CAAC;EAED,oBACEf,OAAA,CAACC,YAAY,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EACjCA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAC/B,GAAA,CApEWF,aAA2C;AAAAkC,EAAA,GAA3ClC,aAA2C;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}