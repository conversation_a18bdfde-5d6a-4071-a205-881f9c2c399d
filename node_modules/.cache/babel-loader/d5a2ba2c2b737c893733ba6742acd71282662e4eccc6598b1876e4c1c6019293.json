{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-reconciler-constants.production.js');\n} else {\n  module.exports = require('./cjs/react-reconciler-constants.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/react-reconciler/constants.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-reconciler-constants.production.js');\n} else {\n  module.exports = require('./cjs/react-reconciler-constants.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gDAAgD,CAAC;AAC5E,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,iDAAiD,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}