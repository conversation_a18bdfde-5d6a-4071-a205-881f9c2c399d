{"ast": null, "code": "import { Vector3, <PERSON>, Vector2, Vector4, <PERSON>3, <PERSON>4, <PERSON><PERSON><PERSON>, <PERSON>3, <PERSON>Side } from \"three\";\nclass RenderableObject {\n  constructor() {\n    this.id = 0;\n    this.object = null;\n    this.z = 0;\n    this.renderOrder = 0;\n  }\n}\nclass RenderableFace {\n  constructor() {\n    this.id = 0;\n    this.v1 = new RenderableVertex();\n    this.v2 = new RenderableVertex();\n    this.v3 = new RenderableVertex();\n    this.normalModel = new Vector3();\n    this.vertexNormalsModel = [new Vector3(), new Vector3(), new Vector3()];\n    this.vertexNormalsLength = 0;\n    this.color = new Color();\n    this.material = null;\n    this.uvs = [new Vector2(), new Vector2(), new Vector2()];\n    this.z = 0;\n    this.renderOrder = 0;\n  }\n}\nclass RenderableVertex {\n  constructor() {\n    this.position = new Vector3();\n    this.positionWorld = new Vector3();\n    this.positionScreen = new Vector4();\n    this.visible = true;\n  }\n  copy(vertex) {\n    this.positionWorld.copy(vertex.positionWorld);\n    this.positionScreen.copy(vertex.positionScreen);\n  }\n}\nclass RenderableLine {\n  constructor() {\n    this.id = 0;\n    this.v1 = new RenderableVertex();\n    this.v2 = new RenderableVertex();\n    this.vertexColors = [new Color(), new Color()];\n    this.material = null;\n    this.z = 0;\n    this.renderOrder = 0;\n  }\n}\nclass RenderableSprite {\n  constructor() {\n    this.id = 0;\n    this.object = null;\n    this.x = 0;\n    this.y = 0;\n    this.z = 0;\n    this.rotation = 0;\n    this.scale = new Vector2();\n    this.material = null;\n    this.renderOrder = 0;\n  }\n}\nclass Projector {\n  constructor() {\n    let _object,\n      _objectCount,\n      _objectPoolLength = 0,\n      _vertex,\n      _vertexCount,\n      _vertexPoolLength = 0,\n      _face,\n      _faceCount,\n      _facePoolLength = 0,\n      _line,\n      _lineCount,\n      _linePoolLength = 0,\n      _sprite,\n      _spriteCount,\n      _spritePoolLength = 0,\n      _modelMatrix;\n    const _renderData = {\n        objects: [],\n        lights: [],\n        elements: []\n      },\n      _vector3 = new Vector3(),\n      _vector4 = new Vector4(),\n      _clipBox = new Box3(new Vector3(-1, -1, -1), new Vector3(1, 1, 1)),\n      _boundingBox = new Box3(),\n      _points3 = new Array(3),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _modelViewProjectionMatrix = new Matrix4(),\n      _frustum = new Frustum(),\n      _objectPool = [],\n      _vertexPool = [],\n      _facePool = [],\n      _linePool = [],\n      _spritePool = [];\n    function RenderList() {\n      const normals = [];\n      const colors = [];\n      const uvs = [];\n      let object = null;\n      const normalMatrix = new Matrix3();\n      function setObject(value) {\n        object = value;\n        normalMatrix.getNormalMatrix(object.matrixWorld);\n        normals.length = 0;\n        colors.length = 0;\n        uvs.length = 0;\n      }\n      function projectVertex(vertex) {\n        const position = vertex.position;\n        const positionWorld = vertex.positionWorld;\n        const positionScreen = vertex.positionScreen;\n        positionWorld.copy(position).applyMatrix4(_modelMatrix);\n        positionScreen.copy(positionWorld).applyMatrix4(_viewProjectionMatrix);\n        const invW = 1 / positionScreen.w;\n        positionScreen.x *= invW;\n        positionScreen.y *= invW;\n        positionScreen.z *= invW;\n        vertex.visible = positionScreen.x >= -1 && positionScreen.x <= 1 && positionScreen.y >= -1 && positionScreen.y <= 1 && positionScreen.z >= -1 && positionScreen.z <= 1;\n      }\n      function pushVertex(x, y, z) {\n        _vertex = getNextVertexInPool();\n        _vertex.position.set(x, y, z);\n        projectVertex(_vertex);\n      }\n      function pushNormal(x, y, z) {\n        normals.push(x, y, z);\n      }\n      function pushColor(r, g, b) {\n        colors.push(r, g, b);\n      }\n      function pushUv(x, y) {\n        uvs.push(x, y);\n      }\n      function checkTriangleVisibility(v1, v2, v3) {\n        if (v1.visible === true || v2.visible === true || v3.visible === true) return true;\n        _points3[0] = v1.positionScreen;\n        _points3[1] = v2.positionScreen;\n        _points3[2] = v3.positionScreen;\n        return _clipBox.intersectsBox(_boundingBox.setFromPoints(_points3));\n      }\n      function checkBackfaceCulling(v1, v2, v3) {\n        return (v3.positionScreen.x - v1.positionScreen.x) * (v2.positionScreen.y - v1.positionScreen.y) - (v3.positionScreen.y - v1.positionScreen.y) * (v2.positionScreen.x - v1.positionScreen.x) < 0;\n      }\n      function pushLine(a, b) {\n        const v1 = _vertexPool[a];\n        const v2 = _vertexPool[b];\n        v1.positionScreen.copy(v1.position).applyMatrix4(_modelViewProjectionMatrix);\n        v2.positionScreen.copy(v2.position).applyMatrix4(_modelViewProjectionMatrix);\n        if (clipLine(v1.positionScreen, v2.positionScreen) === true) {\n          v1.positionScreen.multiplyScalar(1 / v1.positionScreen.w);\n          v2.positionScreen.multiplyScalar(1 / v2.positionScreen.w);\n          _line = getNextLineInPool();\n          _line.id = object.id;\n          _line.v1.copy(v1);\n          _line.v2.copy(v2);\n          _line.z = Math.max(v1.positionScreen.z, v2.positionScreen.z);\n          _line.renderOrder = object.renderOrder;\n          _line.material = object.material;\n          if (object.material.vertexColors) {\n            _line.vertexColors[0].fromArray(colors, a * 3);\n            _line.vertexColors[1].fromArray(colors, b * 3);\n          }\n          _renderData.elements.push(_line);\n        }\n      }\n      function pushTriangle(a, b, c, material) {\n        const v1 = _vertexPool[a];\n        const v2 = _vertexPool[b];\n        const v3 = _vertexPool[c];\n        if (checkTriangleVisibility(v1, v2, v3) === false) return;\n        if (material.side === DoubleSide || checkBackfaceCulling(v1, v2, v3) === true) {\n          _face = getNextFaceInPool();\n          _face.id = object.id;\n          _face.v1.copy(v1);\n          _face.v2.copy(v2);\n          _face.v3.copy(v3);\n          _face.z = (v1.positionScreen.z + v2.positionScreen.z + v3.positionScreen.z) / 3;\n          _face.renderOrder = object.renderOrder;\n          _vector3.subVectors(v3.position, v2.position);\n          _vector4.subVectors(v1.position, v2.position);\n          _vector3.cross(_vector4);\n          _face.normalModel.copy(_vector3);\n          _face.normalModel.applyMatrix3(normalMatrix).normalize();\n          for (let i = 0; i < 3; i++) {\n            const normal = _face.vertexNormalsModel[i];\n            normal.fromArray(normals, arguments[i] * 3);\n            normal.applyMatrix3(normalMatrix).normalize();\n            const uv = _face.uvs[i];\n            uv.fromArray(uvs, arguments[i] * 2);\n          }\n          _face.vertexNormalsLength = 3;\n          _face.material = material;\n          if (material.vertexColors) {\n            _face.color.fromArray(colors, a * 3);\n          }\n          _renderData.elements.push(_face);\n        }\n      }\n      return {\n        setObject,\n        projectVertex,\n        checkTriangleVisibility,\n        checkBackfaceCulling,\n        pushVertex,\n        pushNormal,\n        pushColor,\n        pushUv,\n        pushLine,\n        pushTriangle\n      };\n    }\n    const renderList = new RenderList();\n    function projectObject(object) {\n      if (object.visible === false) return;\n      if (object.isLight) {\n        _renderData.lights.push(object);\n      } else if (object.isMesh || object.isLine || object.isPoints) {\n        if (object.material.visible === false) return;\n        if (object.frustumCulled === true && _frustum.intersectsObject(object) === false) return;\n        addObject(object);\n      } else if (object.isSprite) {\n        if (object.material.visible === false) return;\n        if (object.frustumCulled === true && _frustum.intersectsSprite(object) === false) return;\n        addObject(object);\n      }\n      const children = object.children;\n      for (let i = 0, l = children.length; i < l; i++) {\n        projectObject(children[i]);\n      }\n    }\n    function addObject(object) {\n      _object = getNextObjectInPool();\n      _object.id = object.id;\n      _object.object = object;\n      _vector3.setFromMatrixPosition(object.matrixWorld);\n      _vector3.applyMatrix4(_viewProjectionMatrix);\n      _object.z = _vector3.z;\n      _object.renderOrder = object.renderOrder;\n      _renderData.objects.push(_object);\n    }\n    this.projectScene = function (scene, camera, sortObjects, sortElements) {\n      _faceCount = 0;\n      _lineCount = 0;\n      _spriteCount = 0;\n      _renderData.elements.length = 0;\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      _viewMatrix.copy(camera.matrixWorldInverse);\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix);\n      _frustum.setFromProjectionMatrix(_viewProjectionMatrix);\n      _objectCount = 0;\n      _renderData.objects.length = 0;\n      _renderData.lights.length = 0;\n      projectObject(scene);\n      if (sortObjects === true) {\n        _renderData.objects.sort(painterSort);\n      }\n      const objects = _renderData.objects;\n      for (let o = 0, ol = objects.length; o < ol; o++) {\n        const object = objects[o].object;\n        const geometry = object.geometry;\n        renderList.setObject(object);\n        _modelMatrix = object.matrixWorld;\n        _vertexCount = 0;\n        if (object.isMesh) {\n          let material = object.material;\n          const isMultiMaterial = Array.isArray(material);\n          const attributes = geometry.attributes;\n          const groups = geometry.groups;\n          if (attributes.position === void 0) continue;\n          const positions = attributes.position.array;\n          for (let i = 0, l = positions.length; i < l; i += 3) {\n            let x = positions[i];\n            let y = positions[i + 1];\n            let z = positions[i + 2];\n            const morphTargets = geometry.morphAttributes.position;\n            if (morphTargets !== void 0) {\n              const morphTargetsRelative = geometry.morphTargetsRelative;\n              const morphInfluences = object.morphTargetInfluences;\n              for (let t = 0, tl = morphTargets.length; t < tl; t++) {\n                const influence = morphInfluences[t];\n                if (influence === 0) continue;\n                const target = morphTargets[t];\n                if (morphTargetsRelative) {\n                  x += target.getX(i / 3) * influence;\n                  y += target.getY(i / 3) * influence;\n                  z += target.getZ(i / 3) * influence;\n                } else {\n                  x += (target.getX(i / 3) - positions[i]) * influence;\n                  y += (target.getY(i / 3) - positions[i + 1]) * influence;\n                  z += (target.getZ(i / 3) - positions[i + 2]) * influence;\n                }\n              }\n            }\n            renderList.pushVertex(x, y, z);\n          }\n          if (attributes.normal !== void 0) {\n            const normals = attributes.normal.array;\n            for (let i = 0, l = normals.length; i < l; i += 3) {\n              renderList.pushNormal(normals[i], normals[i + 1], normals[i + 2]);\n            }\n          }\n          if (attributes.color !== void 0) {\n            const colors = attributes.color.array;\n            for (let i = 0, l = colors.length; i < l; i += 3) {\n              renderList.pushColor(colors[i], colors[i + 1], colors[i + 2]);\n            }\n          }\n          if (attributes.uv !== void 0) {\n            const uvs = attributes.uv.array;\n            for (let i = 0, l = uvs.length; i < l; i += 2) {\n              renderList.pushUv(uvs[i], uvs[i + 1]);\n            }\n          }\n          if (geometry.index !== null) {\n            const indices = geometry.index.array;\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g];\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material;\n                if (material === void 0) continue;\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material);\n                }\n              }\n            } else {\n              for (let i = 0, l = indices.length; i < l; i += 3) {\n                renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material);\n              }\n            }\n          } else {\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g];\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material;\n                if (material === void 0) continue;\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(i, i + 1, i + 2, material);\n                }\n              }\n            } else {\n              for (let i = 0, l = positions.length / 3; i < l; i += 3) {\n                renderList.pushTriangle(i, i + 1, i + 2, material);\n              }\n            }\n          }\n        } else if (object.isLine) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix);\n          const attributes = geometry.attributes;\n          if (attributes.position !== void 0) {\n            const positions = attributes.position.array;\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              renderList.pushVertex(positions[i], positions[i + 1], positions[i + 2]);\n            }\n            if (attributes.color !== void 0) {\n              const colors = attributes.color.array;\n              for (let i = 0, l = colors.length; i < l; i += 3) {\n                renderList.pushColor(colors[i], colors[i + 1], colors[i + 2]);\n              }\n            }\n            if (geometry.index !== null) {\n              const indices = geometry.index.array;\n              for (let i = 0, l = indices.length; i < l; i += 2) {\n                renderList.pushLine(indices[i], indices[i + 1]);\n              }\n            } else {\n              const step = object.isLineSegments ? 2 : 1;\n              for (let i = 0, l = positions.length / 3 - 1; i < l; i += step) {\n                renderList.pushLine(i, i + 1);\n              }\n            }\n          }\n        } else if (object.isPoints) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix);\n          const attributes = geometry.attributes;\n          if (attributes.position !== void 0) {\n            const positions = attributes.position.array;\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              _vector4.set(positions[i], positions[i + 1], positions[i + 2], 1);\n              _vector4.applyMatrix4(_modelViewProjectionMatrix);\n              pushPoint(_vector4, object, camera);\n            }\n          }\n        } else if (object.isSprite) {\n          object.modelViewMatrix.multiplyMatrices(camera.matrixWorldInverse, object.matrixWorld);\n          _vector4.set(_modelMatrix.elements[12], _modelMatrix.elements[13], _modelMatrix.elements[14], 1);\n          _vector4.applyMatrix4(_viewProjectionMatrix);\n          pushPoint(_vector4, object, camera);\n        }\n      }\n      if (sortElements === true) {\n        _renderData.elements.sort(painterSort);\n      }\n      return _renderData;\n    };\n    function pushPoint(_vector42, object, camera) {\n      const invW = 1 / _vector42.w;\n      _vector42.z *= invW;\n      if (_vector42.z >= -1 && _vector42.z <= 1) {\n        _sprite = getNextSpriteInPool();\n        _sprite.id = object.id;\n        _sprite.x = _vector42.x * invW;\n        _sprite.y = _vector42.y * invW;\n        _sprite.z = _vector42.z;\n        _sprite.renderOrder = object.renderOrder;\n        _sprite.object = object;\n        _sprite.rotation = object.rotation;\n        _sprite.scale.x = object.scale.x * Math.abs(_sprite.x - (_vector42.x + camera.projectionMatrix.elements[0]) / (_vector42.w + camera.projectionMatrix.elements[12]));\n        _sprite.scale.y = object.scale.y * Math.abs(_sprite.y - (_vector42.y + camera.projectionMatrix.elements[5]) / (_vector42.w + camera.projectionMatrix.elements[13]));\n        _sprite.material = object.material;\n        _renderData.elements.push(_sprite);\n      }\n    }\n    function getNextObjectInPool() {\n      if (_objectCount === _objectPoolLength) {\n        const object = new RenderableObject();\n        _objectPool.push(object);\n        _objectPoolLength++;\n        _objectCount++;\n        return object;\n      }\n      return _objectPool[_objectCount++];\n    }\n    function getNextVertexInPool() {\n      if (_vertexCount === _vertexPoolLength) {\n        const vertex = new RenderableVertex();\n        _vertexPool.push(vertex);\n        _vertexPoolLength++;\n        _vertexCount++;\n        return vertex;\n      }\n      return _vertexPool[_vertexCount++];\n    }\n    function getNextFaceInPool() {\n      if (_faceCount === _facePoolLength) {\n        const face = new RenderableFace();\n        _facePool.push(face);\n        _facePoolLength++;\n        _faceCount++;\n        return face;\n      }\n      return _facePool[_faceCount++];\n    }\n    function getNextLineInPool() {\n      if (_lineCount === _linePoolLength) {\n        const line = new RenderableLine();\n        _linePool.push(line);\n        _linePoolLength++;\n        _lineCount++;\n        return line;\n      }\n      return _linePool[_lineCount++];\n    }\n    function getNextSpriteInPool() {\n      if (_spriteCount === _spritePoolLength) {\n        const sprite = new RenderableSprite();\n        _spritePool.push(sprite);\n        _spritePoolLength++;\n        _spriteCount++;\n        return sprite;\n      }\n      return _spritePool[_spriteCount++];\n    }\n    function painterSort(a, b) {\n      if (a.renderOrder !== b.renderOrder) {\n        return a.renderOrder - b.renderOrder;\n      } else if (a.z !== b.z) {\n        return b.z - a.z;\n      } else if (a.id !== b.id) {\n        return a.id - b.id;\n      } else {\n        return 0;\n      }\n    }\n    function clipLine(s1, s2) {\n      let alpha1 = 0,\n        alpha2 = 1;\n      const bc1near = s1.z + s1.w,\n        bc2near = s2.z + s2.w,\n        bc1far = -s1.z + s1.w,\n        bc2far = -s2.z + s2.w;\n      if (bc1near >= 0 && bc2near >= 0 && bc1far >= 0 && bc2far >= 0) {\n        return true;\n      } else if (bc1near < 0 && bc2near < 0 || bc1far < 0 && bc2far < 0) {\n        return false;\n      } else {\n        if (bc1near < 0) {\n          alpha1 = Math.max(alpha1, bc1near / (bc1near - bc2near));\n        } else if (bc2near < 0) {\n          alpha2 = Math.min(alpha2, bc1near / (bc1near - bc2near));\n        }\n        if (bc1far < 0) {\n          alpha1 = Math.max(alpha1, bc1far / (bc1far - bc2far));\n        } else if (bc2far < 0) {\n          alpha2 = Math.min(alpha2, bc1far / (bc1far - bc2far));\n        }\n        if (alpha2 < alpha1) {\n          return false;\n        } else {\n          s1.lerp(s2, alpha1);\n          s2.lerp(s1, 1 - alpha2);\n          return true;\n        }\n      }\n    }\n  }\n}\nexport { Projector, RenderableFace, RenderableLine, RenderableObject, RenderableSprite, RenderableVertex };", "map": {"version": 3, "names": ["RenderableObject", "constructor", "id", "object", "z", "renderOrder", "RenderableFace", "v1", "RenderableVertex", "v2", "v3", "normalModel", "Vector3", "vertexNormalsModel", "vertexNormalsLength", "color", "Color", "material", "uvs", "Vector2", "position", "positionWorld", "positionScreen", "Vector4", "visible", "copy", "vertex", "RenderableLine", "vertexColors", "RenderableSprite", "x", "y", "rotation", "scale", "Projector", "_object", "_objectCount", "_objectPoolLength", "_vertex", "_vertexCount", "_vertexPoolLength", "_face", "_faceCount", "_facePoolLength", "_line", "_lineCount", "_linePoolLength", "_sprite", "_spriteCount", "_spritePoolLength", "_modelMatrix", "_renderData", "objects", "lights", "elements", "_vector3", "_vector4", "_clipBox", "Box3", "_boundingBox", "_points3", "Array", "_viewMatrix", "Matrix4", "_viewProjectionMatrix", "_modelViewProjectionMatrix", "_frustum", "Frustum", "_objectPool", "_vertexPool", "_facePool", "_linePool", "_spritePool", "RenderList", "normals", "colors", "normalMatrix", "Matrix3", "setObject", "value", "getNormalMatrix", "matrixWorld", "length", "projectVertex", "applyMatrix4", "invW", "w", "pushVertex", "getNextVertexInPool", "set", "pushNormal", "push", "pushColor", "r", "g", "b", "pushUv", "checkTriangleVisibility", "intersectsBox", "setFromPoints", "checkBackfaceCulling", "pushLine", "a", "clipLine", "multiplyScalar", "getNextLineInPool", "Math", "max", "fromArray", "pushTriangle", "c", "side", "DoubleSide", "getNextFaceInPool", "subVectors", "cross", "applyMatrix3", "normalize", "i", "normal", "arguments", "uv", "renderList", "projectObject", "isLight", "<PERSON><PERSON><PERSON>", "isLine", "isPoints", "frustumCulled", "intersectsObject", "addObject", "isSprite", "intersectsSprite", "children", "l", "getNextObjectInPool", "setFromMatrixPosition", "projectScene", "scene", "camera", "sortObjects", "sortElements", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "matrixWorldInverse", "multiplyMatrices", "projectionMatrix", "setFromProjectionMatrix", "sort", "<PERSON><PERSON><PERSON>", "o", "ol", "geometry", "isMultiMaterial", "isArray", "attributes", "groups", "positions", "array", "morphTargets", "morphAttributes", "morphTargetsRelative", "morphInfluences", "morphTargetInfluences", "t", "tl", "influence", "target", "getX", "getY", "getZ", "index", "indices", "group", "materialIndex", "start", "count", "step", "isLineSegments", "pushPoint", "modelViewMatrix", "_vector42", "getNextSpriteInPool", "abs", "face", "line", "sprite", "s1", "s2", "alpha1", "alpha2", "bc1near", "bc2near", "bc1far", "bc2far", "min", "lerp"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/renderers/Projector.js"], "sourcesContent": ["import { Box3, <PERSON>, <PERSON>S<PERSON>, <PERSON>ust<PERSON>, Matrix3, Matrix4, Vector2, Vector3, Vector4 } from 'three'\n\nclass RenderableObject {\n  constructor() {\n    this.id = 0\n\n    this.object = null\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableFace {\n  constructor() {\n    this.id = 0\n\n    this.v1 = new RenderableVertex()\n    this.v2 = new RenderableVertex()\n    this.v3 = new RenderableVertex()\n\n    this.normalModel = new Vector3()\n\n    this.vertexNormalsModel = [new Vector3(), new Vector3(), new Vector3()]\n    this.vertexNormalsLength = 0\n\n    this.color = new Color()\n    this.material = null\n    this.uvs = [new Vector2(), new Vector2(), new Vector2()]\n\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableVertex {\n  constructor() {\n    this.position = new Vector3()\n    this.positionWorld = new Vector3()\n    this.positionScreen = new Vector4()\n\n    this.visible = true\n  }\n\n  copy(vertex) {\n    this.positionWorld.copy(vertex.positionWorld)\n    this.positionScreen.copy(vertex.positionScreen)\n  }\n}\n\n//\n\nclass RenderableLine {\n  constructor() {\n    this.id = 0\n\n    this.v1 = new RenderableVertex()\n    this.v2 = new RenderableVertex()\n\n    this.vertexColors = [new Color(), new Color()]\n    this.material = null\n\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableSprite {\n  constructor() {\n    this.id = 0\n\n    this.object = null\n\n    this.x = 0\n    this.y = 0\n    this.z = 0\n\n    this.rotation = 0\n    this.scale = new Vector2()\n\n    this.material = null\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass Projector {\n  constructor() {\n    let _object,\n      _objectCount,\n      _objectPoolLength = 0,\n      _vertex,\n      _vertexCount,\n      _vertexPoolLength = 0,\n      _face,\n      _faceCount,\n      _facePoolLength = 0,\n      _line,\n      _lineCount,\n      _linePoolLength = 0,\n      _sprite,\n      _spriteCount,\n      _spritePoolLength = 0,\n      _modelMatrix\n\n    const _renderData = { objects: [], lights: [], elements: [] },\n      _vector3 = new Vector3(),\n      _vector4 = new Vector4(),\n      _clipBox = new Box3(new Vector3(-1, -1, -1), new Vector3(1, 1, 1)),\n      _boundingBox = new Box3(),\n      _points3 = new Array(3),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _modelViewProjectionMatrix = new Matrix4(),\n      _frustum = new Frustum(),\n      _objectPool = [],\n      _vertexPool = [],\n      _facePool = [],\n      _linePool = [],\n      _spritePool = []\n\n    //\n\n    function RenderList() {\n      const normals = []\n      const colors = []\n      const uvs = []\n\n      let object = null\n\n      const normalMatrix = new Matrix3()\n\n      function setObject(value) {\n        object = value\n\n        normalMatrix.getNormalMatrix(object.matrixWorld)\n\n        normals.length = 0\n        colors.length = 0\n        uvs.length = 0\n      }\n\n      function projectVertex(vertex) {\n        const position = vertex.position\n        const positionWorld = vertex.positionWorld\n        const positionScreen = vertex.positionScreen\n\n        positionWorld.copy(position).applyMatrix4(_modelMatrix)\n        positionScreen.copy(positionWorld).applyMatrix4(_viewProjectionMatrix)\n\n        const invW = 1 / positionScreen.w\n\n        positionScreen.x *= invW\n        positionScreen.y *= invW\n        positionScreen.z *= invW\n\n        vertex.visible =\n          positionScreen.x >= -1 &&\n          positionScreen.x <= 1 &&\n          positionScreen.y >= -1 &&\n          positionScreen.y <= 1 &&\n          positionScreen.z >= -1 &&\n          positionScreen.z <= 1\n      }\n\n      function pushVertex(x, y, z) {\n        _vertex = getNextVertexInPool()\n        _vertex.position.set(x, y, z)\n\n        projectVertex(_vertex)\n      }\n\n      function pushNormal(x, y, z) {\n        normals.push(x, y, z)\n      }\n\n      function pushColor(r, g, b) {\n        colors.push(r, g, b)\n      }\n\n      function pushUv(x, y) {\n        uvs.push(x, y)\n      }\n\n      function checkTriangleVisibility(v1, v2, v3) {\n        if (v1.visible === true || v2.visible === true || v3.visible === true) return true\n\n        _points3[0] = v1.positionScreen\n        _points3[1] = v2.positionScreen\n        _points3[2] = v3.positionScreen\n\n        return _clipBox.intersectsBox(_boundingBox.setFromPoints(_points3))\n      }\n\n      function checkBackfaceCulling(v1, v2, v3) {\n        return (\n          (v3.positionScreen.x - v1.positionScreen.x) * (v2.positionScreen.y - v1.positionScreen.y) -\n            (v3.positionScreen.y - v1.positionScreen.y) * (v2.positionScreen.x - v1.positionScreen.x) <\n          0\n        )\n      }\n\n      function pushLine(a, b) {\n        const v1 = _vertexPool[a]\n        const v2 = _vertexPool[b]\n\n        // Clip\n\n        v1.positionScreen.copy(v1.position).applyMatrix4(_modelViewProjectionMatrix)\n        v2.positionScreen.copy(v2.position).applyMatrix4(_modelViewProjectionMatrix)\n\n        if (clipLine(v1.positionScreen, v2.positionScreen) === true) {\n          // Perform the perspective divide\n          v1.positionScreen.multiplyScalar(1 / v1.positionScreen.w)\n          v2.positionScreen.multiplyScalar(1 / v2.positionScreen.w)\n\n          _line = getNextLineInPool()\n          _line.id = object.id\n          _line.v1.copy(v1)\n          _line.v2.copy(v2)\n          _line.z = Math.max(v1.positionScreen.z, v2.positionScreen.z)\n          _line.renderOrder = object.renderOrder\n\n          _line.material = object.material\n\n          if (object.material.vertexColors) {\n            _line.vertexColors[0].fromArray(colors, a * 3)\n            _line.vertexColors[1].fromArray(colors, b * 3)\n          }\n\n          _renderData.elements.push(_line)\n        }\n      }\n\n      function pushTriangle(a, b, c, material) {\n        const v1 = _vertexPool[a]\n        const v2 = _vertexPool[b]\n        const v3 = _vertexPool[c]\n\n        if (checkTriangleVisibility(v1, v2, v3) === false) return\n\n        if (material.side === DoubleSide || checkBackfaceCulling(v1, v2, v3) === true) {\n          _face = getNextFaceInPool()\n\n          _face.id = object.id\n          _face.v1.copy(v1)\n          _face.v2.copy(v2)\n          _face.v3.copy(v3)\n          _face.z = (v1.positionScreen.z + v2.positionScreen.z + v3.positionScreen.z) / 3\n          _face.renderOrder = object.renderOrder\n\n          // face normal\n          _vector3.subVectors(v3.position, v2.position)\n          _vector4.subVectors(v1.position, v2.position)\n          _vector3.cross(_vector4)\n          _face.normalModel.copy(_vector3)\n          _face.normalModel.applyMatrix3(normalMatrix).normalize()\n\n          for (let i = 0; i < 3; i++) {\n            const normal = _face.vertexNormalsModel[i]\n            normal.fromArray(normals, arguments[i] * 3)\n            normal.applyMatrix3(normalMatrix).normalize()\n\n            const uv = _face.uvs[i]\n            uv.fromArray(uvs, arguments[i] * 2)\n          }\n\n          _face.vertexNormalsLength = 3\n\n          _face.material = material\n\n          if (material.vertexColors) {\n            _face.color.fromArray(colors, a * 3)\n          }\n\n          _renderData.elements.push(_face)\n        }\n      }\n\n      return {\n        setObject: setObject,\n        projectVertex: projectVertex,\n        checkTriangleVisibility: checkTriangleVisibility,\n        checkBackfaceCulling: checkBackfaceCulling,\n        pushVertex: pushVertex,\n        pushNormal: pushNormal,\n        pushColor: pushColor,\n        pushUv: pushUv,\n        pushLine: pushLine,\n        pushTriangle: pushTriangle,\n      }\n    }\n\n    const renderList = new RenderList()\n\n    function projectObject(object) {\n      if (object.visible === false) return\n\n      if (object.isLight) {\n        _renderData.lights.push(object)\n      } else if (object.isMesh || object.isLine || object.isPoints) {\n        if (object.material.visible === false) return\n        if (object.frustumCulled === true && _frustum.intersectsObject(object) === false) return\n\n        addObject(object)\n      } else if (object.isSprite) {\n        if (object.material.visible === false) return\n        if (object.frustumCulled === true && _frustum.intersectsSprite(object) === false) return\n\n        addObject(object)\n      }\n\n      const children = object.children\n\n      for (let i = 0, l = children.length; i < l; i++) {\n        projectObject(children[i])\n      }\n    }\n\n    function addObject(object) {\n      _object = getNextObjectInPool()\n      _object.id = object.id\n      _object.object = object\n\n      _vector3.setFromMatrixPosition(object.matrixWorld)\n      _vector3.applyMatrix4(_viewProjectionMatrix)\n      _object.z = _vector3.z\n      _object.renderOrder = object.renderOrder\n\n      _renderData.objects.push(_object)\n    }\n\n    this.projectScene = function (scene, camera, sortObjects, sortElements) {\n      _faceCount = 0\n      _lineCount = 0\n      _spriteCount = 0\n\n      _renderData.elements.length = 0\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      _frustum.setFromProjectionMatrix(_viewProjectionMatrix)\n\n      //\n\n      _objectCount = 0\n\n      _renderData.objects.length = 0\n      _renderData.lights.length = 0\n\n      projectObject(scene)\n\n      if (sortObjects === true) {\n        _renderData.objects.sort(painterSort)\n      }\n\n      //\n\n      const objects = _renderData.objects\n\n      for (let o = 0, ol = objects.length; o < ol; o++) {\n        const object = objects[o].object\n        const geometry = object.geometry\n\n        renderList.setObject(object)\n\n        _modelMatrix = object.matrixWorld\n\n        _vertexCount = 0\n\n        if (object.isMesh) {\n          let material = object.material\n\n          const isMultiMaterial = Array.isArray(material)\n\n          const attributes = geometry.attributes\n          const groups = geometry.groups\n\n          if (attributes.position === undefined) continue\n\n          const positions = attributes.position.array\n\n          for (let i = 0, l = positions.length; i < l; i += 3) {\n            let x = positions[i]\n            let y = positions[i + 1]\n            let z = positions[i + 2]\n\n            const morphTargets = geometry.morphAttributes.position\n\n            if (morphTargets !== undefined) {\n              const morphTargetsRelative = geometry.morphTargetsRelative\n              const morphInfluences = object.morphTargetInfluences\n\n              for (let t = 0, tl = morphTargets.length; t < tl; t++) {\n                const influence = morphInfluences[t]\n\n                if (influence === 0) continue\n\n                const target = morphTargets[t]\n\n                if (morphTargetsRelative) {\n                  x += target.getX(i / 3) * influence\n                  y += target.getY(i / 3) * influence\n                  z += target.getZ(i / 3) * influence\n                } else {\n                  x += (target.getX(i / 3) - positions[i]) * influence\n                  y += (target.getY(i / 3) - positions[i + 1]) * influence\n                  z += (target.getZ(i / 3) - positions[i + 2]) * influence\n                }\n              }\n            }\n\n            renderList.pushVertex(x, y, z)\n          }\n\n          if (attributes.normal !== undefined) {\n            const normals = attributes.normal.array\n\n            for (let i = 0, l = normals.length; i < l; i += 3) {\n              renderList.pushNormal(normals[i], normals[i + 1], normals[i + 2])\n            }\n          }\n\n          if (attributes.color !== undefined) {\n            const colors = attributes.color.array\n\n            for (let i = 0, l = colors.length; i < l; i += 3) {\n              renderList.pushColor(colors[i], colors[i + 1], colors[i + 2])\n            }\n          }\n\n          if (attributes.uv !== undefined) {\n            const uvs = attributes.uv.array\n\n            for (let i = 0, l = uvs.length; i < l; i += 2) {\n              renderList.pushUv(uvs[i], uvs[i + 1])\n            }\n          }\n\n          if (geometry.index !== null) {\n            const indices = geometry.index.array\n\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g]\n\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material\n\n                if (material === undefined) continue\n\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material)\n                }\n              }\n            } else {\n              for (let i = 0, l = indices.length; i < l; i += 3) {\n                renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material)\n              }\n            }\n          } else {\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g]\n\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material\n\n                if (material === undefined) continue\n\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(i, i + 1, i + 2, material)\n                }\n              }\n            } else {\n              for (let i = 0, l = positions.length / 3; i < l; i += 3) {\n                renderList.pushTriangle(i, i + 1, i + 2, material)\n              }\n            }\n          }\n        } else if (object.isLine) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix)\n\n          const attributes = geometry.attributes\n\n          if (attributes.position !== undefined) {\n            const positions = attributes.position.array\n\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              renderList.pushVertex(positions[i], positions[i + 1], positions[i + 2])\n            }\n\n            if (attributes.color !== undefined) {\n              const colors = attributes.color.array\n\n              for (let i = 0, l = colors.length; i < l; i += 3) {\n                renderList.pushColor(colors[i], colors[i + 1], colors[i + 2])\n              }\n            }\n\n            if (geometry.index !== null) {\n              const indices = geometry.index.array\n\n              for (let i = 0, l = indices.length; i < l; i += 2) {\n                renderList.pushLine(indices[i], indices[i + 1])\n              }\n            } else {\n              const step = object.isLineSegments ? 2 : 1\n\n              for (let i = 0, l = positions.length / 3 - 1; i < l; i += step) {\n                renderList.pushLine(i, i + 1)\n              }\n            }\n          }\n        } else if (object.isPoints) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix)\n\n          const attributes = geometry.attributes\n\n          if (attributes.position !== undefined) {\n            const positions = attributes.position.array\n\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              _vector4.set(positions[i], positions[i + 1], positions[i + 2], 1)\n              _vector4.applyMatrix4(_modelViewProjectionMatrix)\n\n              pushPoint(_vector4, object, camera)\n            }\n          }\n        } else if (object.isSprite) {\n          object.modelViewMatrix.multiplyMatrices(camera.matrixWorldInverse, object.matrixWorld)\n          _vector4.set(_modelMatrix.elements[12], _modelMatrix.elements[13], _modelMatrix.elements[14], 1)\n          _vector4.applyMatrix4(_viewProjectionMatrix)\n\n          pushPoint(_vector4, object, camera)\n        }\n      }\n\n      if (sortElements === true) {\n        _renderData.elements.sort(painterSort)\n      }\n\n      return _renderData\n    }\n\n    function pushPoint(_vector4, object, camera) {\n      const invW = 1 / _vector4.w\n\n      _vector4.z *= invW\n\n      if (_vector4.z >= -1 && _vector4.z <= 1) {\n        _sprite = getNextSpriteInPool()\n        _sprite.id = object.id\n        _sprite.x = _vector4.x * invW\n        _sprite.y = _vector4.y * invW\n        _sprite.z = _vector4.z\n        _sprite.renderOrder = object.renderOrder\n        _sprite.object = object\n\n        _sprite.rotation = object.rotation\n\n        _sprite.scale.x =\n          object.scale.x *\n          Math.abs(\n            _sprite.x -\n              (_vector4.x + camera.projectionMatrix.elements[0]) / (_vector4.w + camera.projectionMatrix.elements[12]),\n          )\n        _sprite.scale.y =\n          object.scale.y *\n          Math.abs(\n            _sprite.y -\n              (_vector4.y + camera.projectionMatrix.elements[5]) / (_vector4.w + camera.projectionMatrix.elements[13]),\n          )\n\n        _sprite.material = object.material\n\n        _renderData.elements.push(_sprite)\n      }\n    }\n\n    // Pools\n\n    function getNextObjectInPool() {\n      if (_objectCount === _objectPoolLength) {\n        const object = new RenderableObject()\n        _objectPool.push(object)\n        _objectPoolLength++\n        _objectCount++\n        return object\n      }\n\n      return _objectPool[_objectCount++]\n    }\n\n    function getNextVertexInPool() {\n      if (_vertexCount === _vertexPoolLength) {\n        const vertex = new RenderableVertex()\n        _vertexPool.push(vertex)\n        _vertexPoolLength++\n        _vertexCount++\n        return vertex\n      }\n\n      return _vertexPool[_vertexCount++]\n    }\n\n    function getNextFaceInPool() {\n      if (_faceCount === _facePoolLength) {\n        const face = new RenderableFace()\n        _facePool.push(face)\n        _facePoolLength++\n        _faceCount++\n        return face\n      }\n\n      return _facePool[_faceCount++]\n    }\n\n    function getNextLineInPool() {\n      if (_lineCount === _linePoolLength) {\n        const line = new RenderableLine()\n        _linePool.push(line)\n        _linePoolLength++\n        _lineCount++\n        return line\n      }\n\n      return _linePool[_lineCount++]\n    }\n\n    function getNextSpriteInPool() {\n      if (_spriteCount === _spritePoolLength) {\n        const sprite = new RenderableSprite()\n        _spritePool.push(sprite)\n        _spritePoolLength++\n        _spriteCount++\n        return sprite\n      }\n\n      return _spritePool[_spriteCount++]\n    }\n\n    //\n\n    function painterSort(a, b) {\n      if (a.renderOrder !== b.renderOrder) {\n        return a.renderOrder - b.renderOrder\n      } else if (a.z !== b.z) {\n        return b.z - a.z\n      } else if (a.id !== b.id) {\n        return a.id - b.id\n      } else {\n        return 0\n      }\n    }\n\n    function clipLine(s1, s2) {\n      let alpha1 = 0,\n        alpha2 = 1\n\n      // Calculate the boundary coordinate of each vertex for the near and far clip planes,\n      // Z = -1 and Z = +1, respectively.\n\n      const bc1near = s1.z + s1.w,\n        bc2near = s2.z + s2.w,\n        bc1far = -s1.z + s1.w,\n        bc2far = -s2.z + s2.w\n\n      if (bc1near >= 0 && bc2near >= 0 && bc1far >= 0 && bc2far >= 0) {\n        // Both vertices lie entirely within all clip planes.\n        return true\n      } else if ((bc1near < 0 && bc2near < 0) || (bc1far < 0 && bc2far < 0)) {\n        // Both vertices lie entirely outside one of the clip planes.\n        return false\n      } else {\n        // The line segment spans at least one clip plane.\n\n        if (bc1near < 0) {\n          // v1 lies outside the near plane, v2 inside\n          alpha1 = Math.max(alpha1, bc1near / (bc1near - bc2near))\n        } else if (bc2near < 0) {\n          // v2 lies outside the near plane, v1 inside\n          alpha2 = Math.min(alpha2, bc1near / (bc1near - bc2near))\n        }\n\n        if (bc1far < 0) {\n          // v1 lies outside the far plane, v2 inside\n          alpha1 = Math.max(alpha1, bc1far / (bc1far - bc2far))\n        } else if (bc2far < 0) {\n          // v2 lies outside the far plane, v2 inside\n          alpha2 = Math.min(alpha2, bc1far / (bc1far - bc2far))\n        }\n\n        if (alpha2 < alpha1) {\n          // The line segment spans two boundaries, but is outside both of them.\n          // (This can't happen when we're only clipping against just near/far but good\n          //  to leave the check here for future usage if other clip planes are added.)\n          return false\n        } else {\n          // Update the s1 and s2 vertices to match the clipped line segment.\n          s1.lerp(s2, alpha1)\n          s2.lerp(s1, 1 - alpha2)\n\n          return true\n        }\n      }\n    }\n  }\n}\n\nexport { RenderableObject, RenderableFace, RenderableVertex, RenderableLine, RenderableSprite, Projector }\n"], "mappings": ";AAEA,MAAMA,gBAAA,CAAiB;EACrBC,YAAA,EAAc;IACZ,KAAKC,EAAA,GAAK;IAEV,KAAKC,MAAA,GAAS;IACd,KAAKC,CAAA,GAAI;IACT,KAAKC,WAAA,GAAc;EACpB;AACH;AAIA,MAAMC,cAAA,CAAe;EACnBL,YAAA,EAAc;IACZ,KAAKC,EAAA,GAAK;IAEV,KAAKK,EAAA,GAAK,IAAIC,gBAAA,CAAkB;IAChC,KAAKC,EAAA,GAAK,IAAID,gBAAA,CAAkB;IAChC,KAAKE,EAAA,GAAK,IAAIF,gBAAA,CAAkB;IAEhC,KAAKG,WAAA,GAAc,IAAIC,OAAA,CAAS;IAEhC,KAAKC,kBAAA,GAAqB,CAAC,IAAID,OAAA,CAAO,GAAI,IAAIA,OAAA,CAAS,GAAE,IAAIA,OAAA,EAAS;IACtE,KAAKE,mBAAA,GAAsB;IAE3B,KAAKC,KAAA,GAAQ,IAAIC,KAAA,CAAO;IACxB,KAAKC,QAAA,GAAW;IAChB,KAAKC,GAAA,GAAM,CAAC,IAAIC,OAAA,CAAO,GAAI,IAAIA,OAAA,CAAS,GAAE,IAAIA,OAAA,EAAS;IAEvD,KAAKf,CAAA,GAAI;IACT,KAAKC,WAAA,GAAc;EACpB;AACH;AAIA,MAAMG,gBAAA,CAAiB;EACrBP,YAAA,EAAc;IACZ,KAAKmB,QAAA,GAAW,IAAIR,OAAA,CAAS;IAC7B,KAAKS,aAAA,GAAgB,IAAIT,OAAA,CAAS;IAClC,KAAKU,cAAA,GAAiB,IAAIC,OAAA,CAAS;IAEnC,KAAKC,OAAA,GAAU;EAChB;EAEDC,KAAKC,MAAA,EAAQ;IACX,KAAKL,aAAA,CAAcI,IAAA,CAAKC,MAAA,CAAOL,aAAa;IAC5C,KAAKC,cAAA,CAAeG,IAAA,CAAKC,MAAA,CAAOJ,cAAc;EAC/C;AACH;AAIA,MAAMK,cAAA,CAAe;EACnB1B,YAAA,EAAc;IACZ,KAAKC,EAAA,GAAK;IAEV,KAAKK,EAAA,GAAK,IAAIC,gBAAA,CAAkB;IAChC,KAAKC,EAAA,GAAK,IAAID,gBAAA,CAAkB;IAEhC,KAAKoB,YAAA,GAAe,CAAC,IAAIZ,KAAA,CAAK,GAAI,IAAIA,KAAA,CAAK,CAAE;IAC7C,KAAKC,QAAA,GAAW;IAEhB,KAAKb,CAAA,GAAI;IACT,KAAKC,WAAA,GAAc;EACpB;AACH;AAIA,MAAMwB,gBAAA,CAAiB;EACrB5B,YAAA,EAAc;IACZ,KAAKC,EAAA,GAAK;IAEV,KAAKC,MAAA,GAAS;IAEd,KAAK2B,CAAA,GAAI;IACT,KAAKC,CAAA,GAAI;IACT,KAAK3B,CAAA,GAAI;IAET,KAAK4B,QAAA,GAAW;IAChB,KAAKC,KAAA,GAAQ,IAAId,OAAA,CAAS;IAE1B,KAAKF,QAAA,GAAW;IAChB,KAAKZ,WAAA,GAAc;EACpB;AACH;AAIA,MAAM6B,SAAA,CAAU;EACdjC,YAAA,EAAc;IACZ,IAAIkC,OAAA;MACFC,YAAA;MACAC,iBAAA,GAAoB;MACpBC,OAAA;MACAC,YAAA;MACAC,iBAAA,GAAoB;MACpBC,KAAA;MACAC,UAAA;MACAC,eAAA,GAAkB;MAClBC,KAAA;MACAC,UAAA;MACAC,eAAA,GAAkB;MAClBC,OAAA;MACAC,YAAA;MACAC,iBAAA,GAAoB;MACpBC,YAAA;IAEF,MAAMC,WAAA,GAAc;QAAEC,OAAA,EAAS;QAAIC,MAAA,EAAQ,EAAE;QAAEC,QAAA,EAAU;MAAI;MAC3DC,QAAA,GAAW,IAAI3C,OAAA,CAAS;MACxB4C,QAAA,GAAW,IAAIjC,OAAA,CAAS;MACxBkC,QAAA,GAAW,IAAIC,IAAA,CAAK,IAAI9C,OAAA,CAAQ,IAAI,IAAI,EAAE,GAAG,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,CAAC;MACjE+C,YAAA,GAAe,IAAID,IAAA,CAAM;MACzBE,QAAA,GAAW,IAAIC,KAAA,CAAM,CAAC;MACtBC,WAAA,GAAc,IAAIC,OAAA,CAAS;MAC3BC,qBAAA,GAAwB,IAAID,OAAA,CAAS;MACrCE,0BAAA,GAA6B,IAAIF,OAAA,CAAS;MAC1CG,QAAA,GAAW,IAAIC,OAAA,CAAS;MACxBC,WAAA,GAAc,EAAE;MAChBC,WAAA,GAAc,EAAE;MAChBC,SAAA,GAAY,EAAE;MACdC,SAAA,GAAY,EAAE;MACdC,WAAA,GAAc,EAAE;IAIlB,SAASC,WAAA,EAAa;MACpB,MAAMC,OAAA,GAAU,EAAE;MAClB,MAAMC,MAAA,GAAS,EAAE;MACjB,MAAMzD,GAAA,GAAM,EAAE;MAEd,IAAIf,MAAA,GAAS;MAEb,MAAMyE,YAAA,GAAe,IAAIC,OAAA,CAAS;MAElC,SAASC,UAAUC,KAAA,EAAO;QACxB5E,MAAA,GAAS4E,KAAA;QAETH,YAAA,CAAaI,eAAA,CAAgB7E,MAAA,CAAO8E,WAAW;QAE/CP,OAAA,CAAQQ,MAAA,GAAS;QACjBP,MAAA,CAAOO,MAAA,GAAS;QAChBhE,GAAA,CAAIgE,MAAA,GAAS;MACd;MAED,SAASC,cAAczD,MAAA,EAAQ;QAC7B,MAAMN,QAAA,GAAWM,MAAA,CAAON,QAAA;QACxB,MAAMC,aAAA,GAAgBK,MAAA,CAAOL,aAAA;QAC7B,MAAMC,cAAA,GAAiBI,MAAA,CAAOJ,cAAA;QAE9BD,aAAA,CAAcI,IAAA,CAAKL,QAAQ,EAAEgE,YAAA,CAAalC,YAAY;QACtD5B,cAAA,CAAeG,IAAA,CAAKJ,aAAa,EAAE+D,YAAA,CAAapB,qBAAqB;QAErE,MAAMqB,IAAA,GAAO,IAAI/D,cAAA,CAAegE,CAAA;QAEhChE,cAAA,CAAeQ,CAAA,IAAKuD,IAAA;QACpB/D,cAAA,CAAeS,CAAA,IAAKsD,IAAA;QACpB/D,cAAA,CAAelB,CAAA,IAAKiF,IAAA;QAEpB3D,MAAA,CAAOF,OAAA,GACLF,cAAA,CAAeQ,CAAA,IAAK,MACpBR,cAAA,CAAeQ,CAAA,IAAK,KACpBR,cAAA,CAAeS,CAAA,IAAK,MACpBT,cAAA,CAAeS,CAAA,IAAK,KACpBT,cAAA,CAAelB,CAAA,IAAK,MACpBkB,cAAA,CAAelB,CAAA,IAAK;MACvB;MAED,SAASmF,WAAWzD,CAAA,EAAGC,CAAA,EAAG3B,CAAA,EAAG;QAC3BkC,OAAA,GAAUkD,mBAAA,CAAqB;QAC/BlD,OAAA,CAAQlB,QAAA,CAASqE,GAAA,CAAI3D,CAAA,EAAGC,CAAA,EAAG3B,CAAC;QAE5B+E,aAAA,CAAc7C,OAAO;MACtB;MAED,SAASoD,WAAW5D,CAAA,EAAGC,CAAA,EAAG3B,CAAA,EAAG;QAC3BsE,OAAA,CAAQiB,IAAA,CAAK7D,CAAA,EAAGC,CAAA,EAAG3B,CAAC;MACrB;MAED,SAASwF,UAAUC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;QAC1BpB,MAAA,CAAOgB,IAAA,CAAKE,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACpB;MAED,SAASC,OAAOlE,CAAA,EAAGC,CAAA,EAAG;QACpBb,GAAA,CAAIyE,IAAA,CAAK7D,CAAA,EAAGC,CAAC;MACd;MAED,SAASkE,wBAAwB1F,EAAA,EAAIE,EAAA,EAAIC,EAAA,EAAI;QAC3C,IAAIH,EAAA,CAAGiB,OAAA,KAAY,QAAQf,EAAA,CAAGe,OAAA,KAAY,QAAQd,EAAA,CAAGc,OAAA,KAAY,MAAM,OAAO;QAE9EoC,QAAA,CAAS,CAAC,IAAIrD,EAAA,CAAGe,cAAA;QACjBsC,QAAA,CAAS,CAAC,IAAInD,EAAA,CAAGa,cAAA;QACjBsC,QAAA,CAAS,CAAC,IAAIlD,EAAA,CAAGY,cAAA;QAEjB,OAAOmC,QAAA,CAASyC,aAAA,CAAcvC,YAAA,CAAawC,aAAA,CAAcvC,QAAQ,CAAC;MACnE;MAED,SAASwC,qBAAqB7F,EAAA,EAAIE,EAAA,EAAIC,EAAA,EAAI;QACxC,QACGA,EAAA,CAAGY,cAAA,CAAeQ,CAAA,GAAIvB,EAAA,CAAGe,cAAA,CAAeQ,CAAA,KAAMrB,EAAA,CAAGa,cAAA,CAAeS,CAAA,GAAIxB,EAAA,CAAGe,cAAA,CAAeS,CAAA,KACpFrB,EAAA,CAAGY,cAAA,CAAeS,CAAA,GAAIxB,EAAA,CAAGe,cAAA,CAAeS,CAAA,KAAMtB,EAAA,CAAGa,cAAA,CAAeQ,CAAA,GAAIvB,EAAA,CAAGe,cAAA,CAAeQ,CAAA,IACzF;MAEH;MAED,SAASuE,SAASC,CAAA,EAAGP,CAAA,EAAG;QACtB,MAAMxF,EAAA,GAAK8D,WAAA,CAAYiC,CAAC;QACxB,MAAM7F,EAAA,GAAK4D,WAAA,CAAY0B,CAAC;QAIxBxF,EAAA,CAAGe,cAAA,CAAeG,IAAA,CAAKlB,EAAA,CAAGa,QAAQ,EAAEgE,YAAA,CAAanB,0BAA0B;QAC3ExD,EAAA,CAAGa,cAAA,CAAeG,IAAA,CAAKhB,EAAA,CAAGW,QAAQ,EAAEgE,YAAA,CAAanB,0BAA0B;QAE3E,IAAIsC,QAAA,CAAShG,EAAA,CAAGe,cAAA,EAAgBb,EAAA,CAAGa,cAAc,MAAM,MAAM;UAE3Df,EAAA,CAAGe,cAAA,CAAekF,cAAA,CAAe,IAAIjG,EAAA,CAAGe,cAAA,CAAegE,CAAC;UACxD7E,EAAA,CAAGa,cAAA,CAAekF,cAAA,CAAe,IAAI/F,EAAA,CAAGa,cAAA,CAAegE,CAAC;UAExD1C,KAAA,GAAQ6D,iBAAA,CAAmB;UAC3B7D,KAAA,CAAM1C,EAAA,GAAKC,MAAA,CAAOD,EAAA;UAClB0C,KAAA,CAAMrC,EAAA,CAAGkB,IAAA,CAAKlB,EAAE;UAChBqC,KAAA,CAAMnC,EAAA,CAAGgB,IAAA,CAAKhB,EAAE;UAChBmC,KAAA,CAAMxC,CAAA,GAAIsG,IAAA,CAAKC,GAAA,CAAIpG,EAAA,CAAGe,cAAA,CAAelB,CAAA,EAAGK,EAAA,CAAGa,cAAA,CAAelB,CAAC;UAC3DwC,KAAA,CAAMvC,WAAA,GAAcF,MAAA,CAAOE,WAAA;UAE3BuC,KAAA,CAAM3B,QAAA,GAAWd,MAAA,CAAOc,QAAA;UAExB,IAAId,MAAA,CAAOc,QAAA,CAASW,YAAA,EAAc;YAChCgB,KAAA,CAAMhB,YAAA,CAAa,CAAC,EAAEgF,SAAA,CAAUjC,MAAA,EAAQ2B,CAAA,GAAI,CAAC;YAC7C1D,KAAA,CAAMhB,YAAA,CAAa,CAAC,EAAEgF,SAAA,CAAUjC,MAAA,EAAQoB,CAAA,GAAI,CAAC;UAC9C;UAED5C,WAAA,CAAYG,QAAA,CAASqC,IAAA,CAAK/C,KAAK;QAChC;MACF;MAED,SAASiE,aAAaP,CAAA,EAAGP,CAAA,EAAGe,CAAA,EAAG7F,QAAA,EAAU;QACvC,MAAMV,EAAA,GAAK8D,WAAA,CAAYiC,CAAC;QACxB,MAAM7F,EAAA,GAAK4D,WAAA,CAAY0B,CAAC;QACxB,MAAMrF,EAAA,GAAK2D,WAAA,CAAYyC,CAAC;QAExB,IAAIb,uBAAA,CAAwB1F,EAAA,EAAIE,EAAA,EAAIC,EAAE,MAAM,OAAO;QAEnD,IAAIO,QAAA,CAAS8F,IAAA,KAASC,UAAA,IAAcZ,oBAAA,CAAqB7F,EAAA,EAAIE,EAAA,EAAIC,EAAE,MAAM,MAAM;UAC7E+B,KAAA,GAAQwE,iBAAA,CAAmB;UAE3BxE,KAAA,CAAMvC,EAAA,GAAKC,MAAA,CAAOD,EAAA;UAClBuC,KAAA,CAAMlC,EAAA,CAAGkB,IAAA,CAAKlB,EAAE;UAChBkC,KAAA,CAAMhC,EAAA,CAAGgB,IAAA,CAAKhB,EAAE;UAChBgC,KAAA,CAAM/B,EAAA,CAAGe,IAAA,CAAKf,EAAE;UAChB+B,KAAA,CAAMrC,CAAA,IAAKG,EAAA,CAAGe,cAAA,CAAelB,CAAA,GAAIK,EAAA,CAAGa,cAAA,CAAelB,CAAA,GAAIM,EAAA,CAAGY,cAAA,CAAelB,CAAA,IAAK;UAC9EqC,KAAA,CAAMpC,WAAA,GAAcF,MAAA,CAAOE,WAAA;UAG3BkD,QAAA,CAAS2D,UAAA,CAAWxG,EAAA,CAAGU,QAAA,EAAUX,EAAA,CAAGW,QAAQ;UAC5CoC,QAAA,CAAS0D,UAAA,CAAW3G,EAAA,CAAGa,QAAA,EAAUX,EAAA,CAAGW,QAAQ;UAC5CmC,QAAA,CAAS4D,KAAA,CAAM3D,QAAQ;UACvBf,KAAA,CAAM9B,WAAA,CAAYc,IAAA,CAAK8B,QAAQ;UAC/Bd,KAAA,CAAM9B,WAAA,CAAYyG,YAAA,CAAaxC,YAAY,EAAEyC,SAAA,CAAW;UAExD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;YAC1B,MAAMC,MAAA,GAAS9E,KAAA,CAAM5B,kBAAA,CAAmByG,CAAC;YACzCC,MAAA,CAAOX,SAAA,CAAUlC,OAAA,EAAS8C,SAAA,CAAUF,CAAC,IAAI,CAAC;YAC1CC,MAAA,CAAOH,YAAA,CAAaxC,YAAY,EAAEyC,SAAA,CAAW;YAE7C,MAAMI,EAAA,GAAKhF,KAAA,CAAMvB,GAAA,CAAIoG,CAAC;YACtBG,EAAA,CAAGb,SAAA,CAAU1F,GAAA,EAAKsG,SAAA,CAAUF,CAAC,IAAI,CAAC;UACnC;UAED7E,KAAA,CAAM3B,mBAAA,GAAsB;UAE5B2B,KAAA,CAAMxB,QAAA,GAAWA,QAAA;UAEjB,IAAIA,QAAA,CAASW,YAAA,EAAc;YACzBa,KAAA,CAAM1B,KAAA,CAAM6F,SAAA,CAAUjC,MAAA,EAAQ2B,CAAA,GAAI,CAAC;UACpC;UAEDnD,WAAA,CAAYG,QAAA,CAASqC,IAAA,CAAKlD,KAAK;QAChC;MACF;MAED,OAAO;QACLqC,SAAA;QACAK,aAAA;QACAc,uBAAA;QACAG,oBAAA;QACAb,UAAA;QACAG,UAAA;QACAE,SAAA;QACAI,MAAA;QACAK,QAAA;QACAQ;MACD;IACF;IAED,MAAMa,UAAA,GAAa,IAAIjD,UAAA,CAAY;IAEnC,SAASkD,cAAcxH,MAAA,EAAQ;MAC7B,IAAIA,MAAA,CAAOqB,OAAA,KAAY,OAAO;MAE9B,IAAIrB,MAAA,CAAOyH,OAAA,EAAS;QAClBzE,WAAA,CAAYE,MAAA,CAAOsC,IAAA,CAAKxF,MAAM;MACtC,WAAiBA,MAAA,CAAO0H,MAAA,IAAU1H,MAAA,CAAO2H,MAAA,IAAU3H,MAAA,CAAO4H,QAAA,EAAU;QAC5D,IAAI5H,MAAA,CAAOc,QAAA,CAASO,OAAA,KAAY,OAAO;QACvC,IAAIrB,MAAA,CAAO6H,aAAA,KAAkB,QAAQ9D,QAAA,CAAS+D,gBAAA,CAAiB9H,MAAM,MAAM,OAAO;QAElF+H,SAAA,CAAU/H,MAAM;MACxB,WAAiBA,MAAA,CAAOgI,QAAA,EAAU;QAC1B,IAAIhI,MAAA,CAAOc,QAAA,CAASO,OAAA,KAAY,OAAO;QACvC,IAAIrB,MAAA,CAAO6H,aAAA,KAAkB,QAAQ9D,QAAA,CAASkE,gBAAA,CAAiBjI,MAAM,MAAM,OAAO;QAElF+H,SAAA,CAAU/H,MAAM;MACjB;MAED,MAAMkI,QAAA,GAAWlI,MAAA,CAAOkI,QAAA;MAExB,SAASf,CAAA,GAAI,GAAGgB,CAAA,GAAID,QAAA,CAASnD,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK;QAC/CK,aAAA,CAAcU,QAAA,CAASf,CAAC,CAAC;MAC1B;IACF;IAED,SAASY,UAAU/H,MAAA,EAAQ;MACzBgC,OAAA,GAAUoG,mBAAA,CAAqB;MAC/BpG,OAAA,CAAQjC,EAAA,GAAKC,MAAA,CAAOD,EAAA;MACpBiC,OAAA,CAAQhC,MAAA,GAASA,MAAA;MAEjBoD,QAAA,CAASiF,qBAAA,CAAsBrI,MAAA,CAAO8E,WAAW;MACjD1B,QAAA,CAAS6B,YAAA,CAAapB,qBAAqB;MAC3C7B,OAAA,CAAQ/B,CAAA,GAAImD,QAAA,CAASnD,CAAA;MACrB+B,OAAA,CAAQ9B,WAAA,GAAcF,MAAA,CAAOE,WAAA;MAE7B8C,WAAA,CAAYC,OAAA,CAAQuC,IAAA,CAAKxD,OAAO;IACjC;IAED,KAAKsG,YAAA,GAAe,UAAUC,KAAA,EAAOC,MAAA,EAAQC,WAAA,EAAaC,YAAA,EAAc;MACtEnG,UAAA,GAAa;MACbG,UAAA,GAAa;MACbG,YAAA,GAAe;MAEfG,WAAA,CAAYG,QAAA,CAAS4B,MAAA,GAAS;MAE9B,IAAIwD,KAAA,CAAMI,qBAAA,KAA0B,MAAMJ,KAAA,CAAMK,iBAAA,CAAmB;MACnE,IAAIJ,MAAA,CAAOK,MAAA,KAAW,QAAQL,MAAA,CAAOG,qBAAA,KAA0B,MAAMH,MAAA,CAAOI,iBAAA,CAAmB;MAE/FjF,WAAA,CAAYrC,IAAA,CAAKkH,MAAA,CAAOM,kBAAkB;MAC1CjF,qBAAA,CAAsBkF,gBAAA,CAAiBP,MAAA,CAAOQ,gBAAA,EAAkBrF,WAAW;MAE3EI,QAAA,CAASkF,uBAAA,CAAwBpF,qBAAqB;MAItD5B,YAAA,GAAe;MAEfe,WAAA,CAAYC,OAAA,CAAQ8B,MAAA,GAAS;MAC7B/B,WAAA,CAAYE,MAAA,CAAO6B,MAAA,GAAS;MAE5ByC,aAAA,CAAce,KAAK;MAEnB,IAAIE,WAAA,KAAgB,MAAM;QACxBzF,WAAA,CAAYC,OAAA,CAAQiG,IAAA,CAAKC,WAAW;MACrC;MAID,MAAMlG,OAAA,GAAUD,WAAA,CAAYC,OAAA;MAE5B,SAASmG,CAAA,GAAI,GAAGC,EAAA,GAAKpG,OAAA,CAAQ8B,MAAA,EAAQqE,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAChD,MAAMpJ,MAAA,GAASiD,OAAA,CAAQmG,CAAC,EAAEpJ,MAAA;QAC1B,MAAMsJ,QAAA,GAAWtJ,MAAA,CAAOsJ,QAAA;QAExB/B,UAAA,CAAW5C,SAAA,CAAU3E,MAAM;QAE3B+C,YAAA,GAAe/C,MAAA,CAAO8E,WAAA;QAEtB1C,YAAA,GAAe;QAEf,IAAIpC,MAAA,CAAO0H,MAAA,EAAQ;UACjB,IAAI5G,QAAA,GAAWd,MAAA,CAAOc,QAAA;UAEtB,MAAMyI,eAAA,GAAkB7F,KAAA,CAAM8F,OAAA,CAAQ1I,QAAQ;UAE9C,MAAM2I,UAAA,GAAaH,QAAA,CAASG,UAAA;UAC5B,MAAMC,MAAA,GAASJ,QAAA,CAASI,MAAA;UAExB,IAAID,UAAA,CAAWxI,QAAA,KAAa,QAAW;UAEvC,MAAM0I,SAAA,GAAYF,UAAA,CAAWxI,QAAA,CAAS2I,KAAA;UAEtC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAIwB,SAAA,CAAU5E,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;YACnD,IAAIxF,CAAA,GAAIgI,SAAA,CAAUxC,CAAC;YACnB,IAAIvF,CAAA,GAAI+H,SAAA,CAAUxC,CAAA,GAAI,CAAC;YACvB,IAAIlH,CAAA,GAAI0J,SAAA,CAAUxC,CAAA,GAAI,CAAC;YAEvB,MAAM0C,YAAA,GAAeP,QAAA,CAASQ,eAAA,CAAgB7I,QAAA;YAE9C,IAAI4I,YAAA,KAAiB,QAAW;cAC9B,MAAME,oBAAA,GAAuBT,QAAA,CAASS,oBAAA;cACtC,MAAMC,eAAA,GAAkBhK,MAAA,CAAOiK,qBAAA;cAE/B,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKN,YAAA,CAAa9E,MAAA,EAAQmF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBACrD,MAAME,SAAA,GAAYJ,eAAA,CAAgBE,CAAC;gBAEnC,IAAIE,SAAA,KAAc,GAAG;gBAErB,MAAMC,MAAA,GAASR,YAAA,CAAaK,CAAC;gBAE7B,IAAIH,oBAAA,EAAsB;kBACxBpI,CAAA,IAAK0I,MAAA,CAAOC,IAAA,CAAKnD,CAAA,GAAI,CAAC,IAAIiD,SAAA;kBAC1BxI,CAAA,IAAKyI,MAAA,CAAOE,IAAA,CAAKpD,CAAA,GAAI,CAAC,IAAIiD,SAAA;kBAC1BnK,CAAA,IAAKoK,MAAA,CAAOG,IAAA,CAAKrD,CAAA,GAAI,CAAC,IAAIiD,SAAA;gBAC5C,OAAuB;kBACLzI,CAAA,KAAM0I,MAAA,CAAOC,IAAA,CAAKnD,CAAA,GAAI,CAAC,IAAIwC,SAAA,CAAUxC,CAAC,KAAKiD,SAAA;kBAC3CxI,CAAA,KAAMyI,MAAA,CAAOE,IAAA,CAAKpD,CAAA,GAAI,CAAC,IAAIwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,KAAKiD,SAAA;kBAC/CnK,CAAA,KAAMoK,MAAA,CAAOG,IAAA,CAAKrD,CAAA,GAAI,CAAC,IAAIwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,KAAKiD,SAAA;gBAChD;cACF;YACF;YAED7C,UAAA,CAAWnC,UAAA,CAAWzD,CAAA,EAAGC,CAAA,EAAG3B,CAAC;UAC9B;UAED,IAAIwJ,UAAA,CAAWrC,MAAA,KAAW,QAAW;YACnC,MAAM7C,OAAA,GAAUkF,UAAA,CAAWrC,MAAA,CAAOwC,KAAA;YAElC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAI5D,OAAA,CAAQQ,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;cACjDI,UAAA,CAAWhC,UAAA,CAAWhB,OAAA,CAAQ4C,CAAC,GAAG5C,OAAA,CAAQ4C,CAAA,GAAI,CAAC,GAAG5C,OAAA,CAAQ4C,CAAA,GAAI,CAAC,CAAC;YACjE;UACF;UAED,IAAIsC,UAAA,CAAW7I,KAAA,KAAU,QAAW;YAClC,MAAM4D,MAAA,GAASiF,UAAA,CAAW7I,KAAA,CAAMgJ,KAAA;YAEhC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAI3D,MAAA,CAAOO,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;cAChDI,UAAA,CAAW9B,SAAA,CAAUjB,MAAA,CAAO2C,CAAC,GAAG3C,MAAA,CAAO2C,CAAA,GAAI,CAAC,GAAG3C,MAAA,CAAO2C,CAAA,GAAI,CAAC,CAAC;YAC7D;UACF;UAED,IAAIsC,UAAA,CAAWnC,EAAA,KAAO,QAAW;YAC/B,MAAMvG,GAAA,GAAM0I,UAAA,CAAWnC,EAAA,CAAGsC,KAAA;YAE1B,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAIpH,GAAA,CAAIgE,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;cAC7CI,UAAA,CAAW1B,MAAA,CAAO9E,GAAA,CAAIoG,CAAC,GAAGpG,GAAA,CAAIoG,CAAA,GAAI,CAAC,CAAC;YACrC;UACF;UAED,IAAImC,QAAA,CAASmB,KAAA,KAAU,MAAM;YAC3B,MAAMC,OAAA,GAAUpB,QAAA,CAASmB,KAAA,CAAMb,KAAA;YAE/B,IAAIF,MAAA,CAAO3E,MAAA,GAAS,GAAG;cACrB,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAI+D,MAAA,CAAO3E,MAAA,EAAQY,CAAA,IAAK;gBACtC,MAAMgF,KAAA,GAAQjB,MAAA,CAAO/D,CAAC;gBAEtB7E,QAAA,GAAWyI,eAAA,KAAoB,OAAOvJ,MAAA,CAAOc,QAAA,CAAS6J,KAAA,CAAMC,aAAa,IAAI5K,MAAA,CAAOc,QAAA;gBAEpF,IAAIA,QAAA,KAAa,QAAW;gBAE5B,SAASqG,CAAA,GAAIwD,KAAA,CAAME,KAAA,EAAO1C,CAAA,GAAIwC,KAAA,CAAME,KAAA,GAAQF,KAAA,CAAMG,KAAA,EAAO3D,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;kBACtEI,UAAA,CAAWb,YAAA,CAAagE,OAAA,CAAQvD,CAAC,GAAGuD,OAAA,CAAQvD,CAAA,GAAI,CAAC,GAAGuD,OAAA,CAAQvD,CAAA,GAAI,CAAC,GAAGrG,QAAQ;gBAC7E;cACF;YACf,OAAmB;cACL,SAASqG,CAAA,GAAI,GAAGgB,CAAA,GAAIuC,OAAA,CAAQ3F,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;gBACjDI,UAAA,CAAWb,YAAA,CAAagE,OAAA,CAAQvD,CAAC,GAAGuD,OAAA,CAAQvD,CAAA,GAAI,CAAC,GAAGuD,OAAA,CAAQvD,CAAA,GAAI,CAAC,GAAGrG,QAAQ;cAC7E;YACF;UACb,OAAiB;YACL,IAAI4I,MAAA,CAAO3E,MAAA,GAAS,GAAG;cACrB,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAI+D,MAAA,CAAO3E,MAAA,EAAQY,CAAA,IAAK;gBACtC,MAAMgF,KAAA,GAAQjB,MAAA,CAAO/D,CAAC;gBAEtB7E,QAAA,GAAWyI,eAAA,KAAoB,OAAOvJ,MAAA,CAAOc,QAAA,CAAS6J,KAAA,CAAMC,aAAa,IAAI5K,MAAA,CAAOc,QAAA;gBAEpF,IAAIA,QAAA,KAAa,QAAW;gBAE5B,SAASqG,CAAA,GAAIwD,KAAA,CAAME,KAAA,EAAO1C,CAAA,GAAIwC,KAAA,CAAME,KAAA,GAAQF,KAAA,CAAMG,KAAA,EAAO3D,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;kBACtEI,UAAA,CAAWb,YAAA,CAAaS,CAAA,EAAGA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGrG,QAAQ;gBAClD;cACF;YACf,OAAmB;cACL,SAASqG,CAAA,GAAI,GAAGgB,CAAA,GAAIwB,SAAA,CAAU5E,MAAA,GAAS,GAAGoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;gBACvDI,UAAA,CAAWb,YAAA,CAAaS,CAAA,EAAGA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGrG,QAAQ;cAClD;YACF;UACF;QACX,WAAmBd,MAAA,CAAO2H,MAAA,EAAQ;UACxB7D,0BAAA,CAA2BiF,gBAAA,CAAiBlF,qBAAA,EAAuBd,YAAY;UAE/E,MAAM0G,UAAA,GAAaH,QAAA,CAASG,UAAA;UAE5B,IAAIA,UAAA,CAAWxI,QAAA,KAAa,QAAW;YACrC,MAAM0I,SAAA,GAAYF,UAAA,CAAWxI,QAAA,CAAS2I,KAAA;YAEtC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAIwB,SAAA,CAAU5E,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;cACnDI,UAAA,CAAWnC,UAAA,CAAWuE,SAAA,CAAUxC,CAAC,GAAGwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,GAAGwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,CAAC;YACvE;YAED,IAAIsC,UAAA,CAAW7I,KAAA,KAAU,QAAW;cAClC,MAAM4D,MAAA,GAASiF,UAAA,CAAW7I,KAAA,CAAMgJ,KAAA;cAEhC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAI3D,MAAA,CAAOO,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;gBAChDI,UAAA,CAAW9B,SAAA,CAAUjB,MAAA,CAAO2C,CAAC,GAAG3C,MAAA,CAAO2C,CAAA,GAAI,CAAC,GAAG3C,MAAA,CAAO2C,CAAA,GAAI,CAAC,CAAC;cAC7D;YACF;YAED,IAAImC,QAAA,CAASmB,KAAA,KAAU,MAAM;cAC3B,MAAMC,OAAA,GAAUpB,QAAA,CAASmB,KAAA,CAAMb,KAAA;cAE/B,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAIuC,OAAA,CAAQ3F,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;gBACjDI,UAAA,CAAWrB,QAAA,CAASwE,OAAA,CAAQvD,CAAC,GAAGuD,OAAA,CAAQvD,CAAA,GAAI,CAAC,CAAC;cAC/C;YACf,OAAmB;cACL,MAAM4D,IAAA,GAAO/K,MAAA,CAAOgL,cAAA,GAAiB,IAAI;cAEzC,SAAS7D,CAAA,GAAI,GAAGgB,CAAA,GAAIwB,SAAA,CAAU5E,MAAA,GAAS,IAAI,GAAGoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK4D,IAAA,EAAM;gBAC9DxD,UAAA,CAAWrB,QAAA,CAASiB,CAAA,EAAGA,CAAA,GAAI,CAAC;cAC7B;YACF;UACF;QACX,WAAmBnH,MAAA,CAAO4H,QAAA,EAAU;UAC1B9D,0BAAA,CAA2BiF,gBAAA,CAAiBlF,qBAAA,EAAuBd,YAAY;UAE/E,MAAM0G,UAAA,GAAaH,QAAA,CAASG,UAAA;UAE5B,IAAIA,UAAA,CAAWxI,QAAA,KAAa,QAAW;YACrC,MAAM0I,SAAA,GAAYF,UAAA,CAAWxI,QAAA,CAAS2I,KAAA;YAEtC,SAASzC,CAAA,GAAI,GAAGgB,CAAA,GAAIwB,SAAA,CAAU5E,MAAA,EAAQoC,CAAA,GAAIgB,CAAA,EAAGhB,CAAA,IAAK,GAAG;cACnD9D,QAAA,CAASiC,GAAA,CAAIqE,SAAA,CAAUxC,CAAC,GAAGwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,GAAGwC,SAAA,CAAUxC,CAAA,GAAI,CAAC,GAAG,CAAC;cAChE9D,QAAA,CAAS4B,YAAA,CAAanB,0BAA0B;cAEhDmH,SAAA,CAAU5H,QAAA,EAAUrD,MAAA,EAAQwI,MAAM;YACnC;UACF;QACX,WAAmBxI,MAAA,CAAOgI,QAAA,EAAU;UAC1BhI,MAAA,CAAOkL,eAAA,CAAgBnC,gBAAA,CAAiBP,MAAA,CAAOM,kBAAA,EAAoB9I,MAAA,CAAO8E,WAAW;UACrFzB,QAAA,CAASiC,GAAA,CAAIvC,YAAA,CAAaI,QAAA,CAAS,EAAE,GAAGJ,YAAA,CAAaI,QAAA,CAAS,EAAE,GAAGJ,YAAA,CAAaI,QAAA,CAAS,EAAE,GAAG,CAAC;UAC/FE,QAAA,CAAS4B,YAAA,CAAapB,qBAAqB;UAE3CoH,SAAA,CAAU5H,QAAA,EAAUrD,MAAA,EAAQwI,MAAM;QACnC;MACF;MAED,IAAIE,YAAA,KAAiB,MAAM;QACzB1F,WAAA,CAAYG,QAAA,CAAS+F,IAAA,CAAKC,WAAW;MACtC;MAED,OAAOnG,WAAA;IACR;IAED,SAASiI,UAAUE,SAAA,EAAUnL,MAAA,EAAQwI,MAAA,EAAQ;MAC3C,MAAMtD,IAAA,GAAO,IAAIiG,SAAA,CAAShG,CAAA;MAE1BgG,SAAA,CAASlL,CAAA,IAAKiF,IAAA;MAEd,IAAIiG,SAAA,CAASlL,CAAA,IAAK,MAAMkL,SAAA,CAASlL,CAAA,IAAK,GAAG;QACvC2C,OAAA,GAAUwI,mBAAA,CAAqB;QAC/BxI,OAAA,CAAQ7C,EAAA,GAAKC,MAAA,CAAOD,EAAA;QACpB6C,OAAA,CAAQjB,CAAA,GAAIwJ,SAAA,CAASxJ,CAAA,GAAIuD,IAAA;QACzBtC,OAAA,CAAQhB,CAAA,GAAIuJ,SAAA,CAASvJ,CAAA,GAAIsD,IAAA;QACzBtC,OAAA,CAAQ3C,CAAA,GAAIkL,SAAA,CAASlL,CAAA;QACrB2C,OAAA,CAAQ1C,WAAA,GAAcF,MAAA,CAAOE,WAAA;QAC7B0C,OAAA,CAAQ5C,MAAA,GAASA,MAAA;QAEjB4C,OAAA,CAAQf,QAAA,GAAW7B,MAAA,CAAO6B,QAAA;QAE1Be,OAAA,CAAQd,KAAA,CAAMH,CAAA,GACZ3B,MAAA,CAAO8B,KAAA,CAAMH,CAAA,GACb4E,IAAA,CAAK8E,GAAA,CACHzI,OAAA,CAAQjB,CAAA,IACLwJ,SAAA,CAASxJ,CAAA,GAAI6G,MAAA,CAAOQ,gBAAA,CAAiB7F,QAAA,CAAS,CAAC,MAAMgI,SAAA,CAAShG,CAAA,GAAIqD,MAAA,CAAOQ,gBAAA,CAAiB7F,QAAA,CAAS,EAAE,EACzG;QACHP,OAAA,CAAQd,KAAA,CAAMF,CAAA,GACZ5B,MAAA,CAAO8B,KAAA,CAAMF,CAAA,GACb2E,IAAA,CAAK8E,GAAA,CACHzI,OAAA,CAAQhB,CAAA,IACLuJ,SAAA,CAASvJ,CAAA,GAAI4G,MAAA,CAAOQ,gBAAA,CAAiB7F,QAAA,CAAS,CAAC,MAAMgI,SAAA,CAAShG,CAAA,GAAIqD,MAAA,CAAOQ,gBAAA,CAAiB7F,QAAA,CAAS,EAAE,EACzG;QAEHP,OAAA,CAAQ9B,QAAA,GAAWd,MAAA,CAAOc,QAAA;QAE1BkC,WAAA,CAAYG,QAAA,CAASqC,IAAA,CAAK5C,OAAO;MAClC;IACF;IAID,SAASwF,oBAAA,EAAsB;MAC7B,IAAInG,YAAA,KAAiBC,iBAAA,EAAmB;QACtC,MAAMlC,MAAA,GAAS,IAAIH,gBAAA,CAAkB;QACrCoE,WAAA,CAAYuB,IAAA,CAAKxF,MAAM;QACvBkC,iBAAA;QACAD,YAAA;QACA,OAAOjC,MAAA;MACR;MAED,OAAOiE,WAAA,CAAYhC,YAAA,EAAc;IAClC;IAED,SAASoD,oBAAA,EAAsB;MAC7B,IAAIjD,YAAA,KAAiBC,iBAAA,EAAmB;QACtC,MAAMd,MAAA,GAAS,IAAIlB,gBAAA,CAAkB;QACrC6D,WAAA,CAAYsB,IAAA,CAAKjE,MAAM;QACvBc,iBAAA;QACAD,YAAA;QACA,OAAOb,MAAA;MACR;MAED,OAAO2C,WAAA,CAAY9B,YAAA,EAAc;IAClC;IAED,SAAS0E,kBAAA,EAAoB;MAC3B,IAAIvE,UAAA,KAAeC,eAAA,EAAiB;QAClC,MAAM8I,IAAA,GAAO,IAAInL,cAAA,CAAgB;QACjCgE,SAAA,CAAUqB,IAAA,CAAK8F,IAAI;QACnB9I,eAAA;QACAD,UAAA;QACA,OAAO+I,IAAA;MACR;MAED,OAAOnH,SAAA,CAAU5B,UAAA,EAAY;IAC9B;IAED,SAAS+D,kBAAA,EAAoB;MAC3B,IAAI5D,UAAA,KAAeC,eAAA,EAAiB;QAClC,MAAM4I,IAAA,GAAO,IAAI/J,cAAA,CAAgB;QACjC4C,SAAA,CAAUoB,IAAA,CAAK+F,IAAI;QACnB5I,eAAA;QACAD,UAAA;QACA,OAAO6I,IAAA;MACR;MAED,OAAOnH,SAAA,CAAU1B,UAAA,EAAY;IAC9B;IAED,SAAS0I,oBAAA,EAAsB;MAC7B,IAAIvI,YAAA,KAAiBC,iBAAA,EAAmB;QACtC,MAAM0I,MAAA,GAAS,IAAI9J,gBAAA,CAAkB;QACrC2C,WAAA,CAAYmB,IAAA,CAAKgG,MAAM;QACvB1I,iBAAA;QACAD,YAAA;QACA,OAAO2I,MAAA;MACR;MAED,OAAOnH,WAAA,CAAYxB,YAAA,EAAc;IAClC;IAID,SAASsG,YAAYhD,CAAA,EAAGP,CAAA,EAAG;MACzB,IAAIO,CAAA,CAAEjG,WAAA,KAAgB0F,CAAA,CAAE1F,WAAA,EAAa;QACnC,OAAOiG,CAAA,CAAEjG,WAAA,GAAc0F,CAAA,CAAE1F,WAAA;MAC1B,WAAUiG,CAAA,CAAElG,CAAA,KAAM2F,CAAA,CAAE3F,CAAA,EAAG;QACtB,OAAO2F,CAAA,CAAE3F,CAAA,GAAIkG,CAAA,CAAElG,CAAA;MAChB,WAAUkG,CAAA,CAAEpG,EAAA,KAAO6F,CAAA,CAAE7F,EAAA,EAAI;QACxB,OAAOoG,CAAA,CAAEpG,EAAA,GAAK6F,CAAA,CAAE7F,EAAA;MACxB,OAAa;QACL,OAAO;MACR;IACF;IAED,SAASqG,SAASqF,EAAA,EAAIC,EAAA,EAAI;MACxB,IAAIC,MAAA,GAAS;QACXC,MAAA,GAAS;MAKX,MAAMC,OAAA,GAAUJ,EAAA,CAAGxL,CAAA,GAAIwL,EAAA,CAAGtG,CAAA;QACxB2G,OAAA,GAAUJ,EAAA,CAAGzL,CAAA,GAAIyL,EAAA,CAAGvG,CAAA;QACpB4G,MAAA,GAAS,CAACN,EAAA,CAAGxL,CAAA,GAAIwL,EAAA,CAAGtG,CAAA;QACpB6G,MAAA,GAAS,CAACN,EAAA,CAAGzL,CAAA,GAAIyL,EAAA,CAAGvG,CAAA;MAEtB,IAAI0G,OAAA,IAAW,KAAKC,OAAA,IAAW,KAAKC,MAAA,IAAU,KAAKC,MAAA,IAAU,GAAG;QAE9D,OAAO;MACf,WAAkBH,OAAA,GAAU,KAAKC,OAAA,GAAU,KAAOC,MAAA,GAAS,KAAKC,MAAA,GAAS,GAAI;QAErE,OAAO;MACf,OAAa;QAGL,IAAIH,OAAA,GAAU,GAAG;UAEfF,MAAA,GAASpF,IAAA,CAAKC,GAAA,CAAImF,MAAA,EAAQE,OAAA,IAAWA,OAAA,GAAUC,OAAA,CAAQ;QACjE,WAAmBA,OAAA,GAAU,GAAG;UAEtBF,MAAA,GAASrF,IAAA,CAAK0F,GAAA,CAAIL,MAAA,EAAQC,OAAA,IAAWA,OAAA,GAAUC,OAAA,CAAQ;QACxD;QAED,IAAIC,MAAA,GAAS,GAAG;UAEdJ,MAAA,GAASpF,IAAA,CAAKC,GAAA,CAAImF,MAAA,EAAQI,MAAA,IAAUA,MAAA,GAASC,MAAA,CAAO;QAC9D,WAAmBA,MAAA,GAAS,GAAG;UAErBJ,MAAA,GAASrF,IAAA,CAAK0F,GAAA,CAAIL,MAAA,EAAQG,MAAA,IAAUA,MAAA,GAASC,MAAA,CAAO;QACrD;QAED,IAAIJ,MAAA,GAASD,MAAA,EAAQ;UAInB,OAAO;QACjB,OAAe;UAELF,EAAA,CAAGS,IAAA,CAAKR,EAAA,EAAIC,MAAM;UAClBD,EAAA,CAAGQ,IAAA,CAAKT,EAAA,EAAI,IAAIG,MAAM;UAEtB,OAAO;QACR;MACF;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}