{"ast": null, "code": "const AfterimageShader = {\n  uniforms: {\n    damp: {\n      value: 0.96\n    },\n    tOld: {\n      value: null\n    },\n    tNew: {\n      value: null\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float damp;\n\n    uniform sampler2D tOld;\n    uniform sampler2D tNew;\n\n    varying vec2 vUv;\n\n    vec4 when_gt( vec4 x, float y ) {\n\n    \treturn max( sign( x - y ), 0.0 );\n\n    }\n\n    void main() {\n\n    \tvec4 texelOld = texture2D( tOld, vUv );\n    \tvec4 texelNew = texture2D( tNew, vUv );\n\n    \ttexelOld *= damp * when_gt( texelOld, 0.1 );\n\n    \tgl_FragColor = max(texelNew, texelOld);\n\n    }\n  `)\n};\nexport { AfterimageShader };", "map": {"version": 3, "names": ["AfterimageShader", "uniforms", "damp", "value", "tOld", "tNew", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/AfterimageShader.ts"], "sourcesContent": ["/**\n * Afterimage shader\n * I created this effect inspired by a demo on codepen:\n * https://codepen.io/brunoimbrizi/pen/MoRJaN?page=1&\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type AfterimageShaderUniforms = {\n  damp: IUniform<number>\n  tNew: IUniform<Texture | null>\n  tOld: IUniform<Texture | null>\n}\n\nexport interface IAfterimageShader extends IShader<AfterimageShaderUniforms> {}\n\nexport const AfterimageShader: IAfterimageShader = {\n  uniforms: {\n    damp: { value: 0.96 },\n    tOld: { value: null },\n    tNew: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float damp;\n\n    uniform sampler2D tOld;\n    uniform sampler2D tNew;\n\n    varying vec2 vUv;\n\n    vec4 when_gt( vec4 x, float y ) {\n\n    \treturn max( sign( x - y ), 0.0 );\n\n    }\n\n    void main() {\n\n    \tvec4 texelOld = texture2D( tOld, vUv );\n    \tvec4 texelNew = texture2D( tNew, vUv );\n\n    \ttexelOld *= damp * when_gt( texelOld, 0.1 );\n\n    \tgl_FragColor = max(texelNew, texelOld);\n\n    }\n  `,\n}\n"], "mappings": "AAiBO,MAAMA,gBAAA,GAAsC;EACjDC,QAAA,EAAU;IACRC,IAAA,EAAM;MAAEC,KAAA,EAAO;IAAK;IACpBC,IAAA,EAAM;MAAED,KAAA,EAAO;IAAK;IACpBE,IAAA,EAAM;MAAEF,KAAA,EAAO;IAAK;EACtB;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}