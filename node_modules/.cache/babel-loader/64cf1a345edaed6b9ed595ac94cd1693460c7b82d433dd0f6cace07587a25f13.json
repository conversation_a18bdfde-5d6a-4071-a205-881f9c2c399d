{"ast": null, "code": "import { Scene, WebGLRenderTarget, FloatType, MeshPhongMaterial, MeshBasicMaterial, DoubleSide, PlaneGeometry, Mesh } from \"three\";\nimport potpack from \"potpack\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nclass ProgressiveLightMap {\n  constructor(renderer, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.lightMapContainers = [];\n    this.compiled = false;\n    this.scene = new Scene();\n    this.scene.background = null;\n    this.tinyTarget = new WebGLRenderTarget(1, 1);\n    this.buffer1Active = false;\n    this.firstUpdate = true;\n    this.warned = false;\n    const format = /(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent) ? alfFloatType : FloatType;\n    this.progressiveLightMap1 = new WebGLRenderTarget(this.res, this.res, {\n      type: format\n    });\n    this.progressiveLightMap2 = new WebGLRenderTarget(this.res, this.res, {\n      type: format\n    });\n    this.uvMat = new MeshPhongMaterial();\n    this.uvMat.uniforms = {};\n    this.uvMat.onBeforeCompile = shader => {\n      shader.vertexShader = \"#define USE_LIGHTMAP\\n\" + shader.vertexShader.slice(0, -1) + `\tgl_Position = vec4((${UV1} - 0.5) * 2.0, 1.0, 1.0); }`;\n      const bodyStart = shader.fragmentShader.indexOf(\"void main() {\");\n      shader.fragmentShader = `varying vec2 v${UV1 === \"uv1\" ? UV1 : \"Uv2\"};\n` + shader.fragmentShader.slice(0, bodyStart) + \"\tuniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n\" + shader.fragmentShader.slice(bodyStart - 1, -1) + `\nvec3 texelOld = texture2D(previousShadowMap, v${UV1 === \"uv1\" ? UV1 : \"Uv2\"}).rgb;\n\t\t\t\tgl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/averagingWindow);\n\t\t\t}`;\n      shader.uniforms.previousShadowMap = {\n        value: this.progressiveLightMap1.texture\n      };\n      shader.uniforms.averagingWindow = {\n        value: 100\n      };\n      this.uvMat.uniforms = shader.uniforms;\n      this.uvMat.userData.shader = shader;\n      this.compiled = true;\n    };\n  }\n  /**\n   * Sets these objects' materials' lightmaps and modifies their uv1's.\n   * @param {Object3D} objects An array of objects and lights to set up your lightmap.\n   */\n  addObjectsToLightMap(objects) {\n    this.uv_boxes = [];\n    const padding = 3 / this.res;\n    for (let ob = 0; ob < objects.length; ob++) {\n      const object = objects[ob];\n      if (object.isLight) {\n        this.scene.attach(object);\n        continue;\n      }\n      if (!object.geometry.hasAttribute(\"uv\")) {\n        console.warn(\"All lightmap objects need UVs!\");\n        continue;\n      }\n      if (this.blurringPlane == null) {\n        this._initializeBlurPlane(this.res, this.progressiveLightMap1);\n      }\n      object.material.lightMap = this.progressiveLightMap2.texture;\n      object.material.dithering = true;\n      object.castShadow = true;\n      object.receiveShadow = true;\n      object.renderOrder = 1e3 + ob;\n      this.uv_boxes.push({\n        w: 1 + padding * 2,\n        h: 1 + padding * 2,\n        index: ob\n      });\n      this.lightMapContainers.push({\n        basicMat: object.material,\n        object\n      });\n      this.compiled = false;\n    }\n    const dimensions = potpack(this.uv_boxes);\n    this.uv_boxes.forEach(box => {\n      const uv1 = objects[box.index].geometry.getAttribute(\"uv\").clone();\n      for (let i = 0; i < uv1.array.length; i += uv1.itemSize) {\n        uv1.array[i] = (uv1.array[i] + box.x + padding) / dimensions.w;\n        uv1.array[i + 1] = (uv1.array[i + 1] + box.y + padding) / dimensions.h;\n      }\n      objects[box.index].geometry.setAttribute(UV1, uv1);\n      objects[box.index].geometry.getAttribute(UV1).needsUpdate = true;\n    });\n  }\n  /**\n   * This function renders each mesh one at a time into their respective surface maps\n   * @param {Camera} camera Standard Rendering Camera\n   * @param {number} blendWindow When >1, samples will accumulate over time.\n   * @param {boolean} blurEdges  Whether to fix UV Edges via blurring\n   */\n  update(camera, blendWindow = 100, blurEdges = true) {\n    if (this.blurringPlane == null) {\n      return;\n    }\n    const oldTarget = this.renderer.getRenderTarget();\n    this.blurringPlane.visible = blurEdges;\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.lightMapContainers[l].object.oldScene = this.lightMapContainers[l].object.parent;\n      this.scene.attach(this.lightMapContainers[l].object);\n    }\n    if (this.firstUpdate) {\n      this.renderer.setRenderTarget(this.tinyTarget);\n      this.renderer.render(this.scene, camera);\n      this.firstUpdate = false;\n    }\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.uvMat.uniforms.averagingWindow = {\n        value: blendWindow\n      };\n      this.lightMapContainers[l].object.material = this.uvMat;\n      this.lightMapContainers[l].object.oldFrustumCulled = this.lightMapContainers[l].object.frustumCulled;\n      this.lightMapContainers[l].object.frustumCulled = false;\n    }\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1;\n    this.renderer.setRenderTarget(activeMap);\n    this.uvMat.uniforms.previousShadowMap = {\n      value: inactiveMap.texture\n    };\n    this.blurringPlane.material.uniforms.previousShadowMap = {\n      value: inactiveMap.texture\n    };\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.lightMapContainers[l].object.frustumCulled = this.lightMapContainers[l].object.oldFrustumCulled;\n      this.lightMapContainers[l].object.material = this.lightMapContainers[l].basicMat;\n      this.lightMapContainers[l].object.oldScene.attach(this.lightMapContainers[l].object);\n    }\n    this.renderer.setRenderTarget(oldTarget);\n  }\n  /** DEBUG\n   * Draw the lightmap in the main scene.  Call this after adding the objects to it.\n   * @param {boolean} visible Whether the debug plane should be visible\n   * @param {Vector3} position Where the debug plane should be drawn\n   */\n  showDebugLightmap(visible, position = void 0) {\n    if (this.lightMapContainers.length == 0) {\n      if (!this.warned) {\n        console.warn(\"Call this after adding the objects!\");\n        this.warned = true;\n      }\n      return;\n    }\n    if (this.labelMesh == null) {\n      this.labelMaterial = new MeshBasicMaterial({\n        map: this.progressiveLightMap1.texture,\n        side: DoubleSide\n      });\n      this.labelPlane = new PlaneGeometry(100, 100);\n      this.labelMesh = new Mesh(this.labelPlane, this.labelMaterial);\n      this.labelMesh.position.y = 250;\n      this.lightMapContainers[0].object.parent.add(this.labelMesh);\n    }\n    if (position != void 0) {\n      this.labelMesh.position.copy(position);\n    }\n    this.labelMesh.visible = visible;\n  }\n  /**\n   * INTERNAL Creates the Blurring Plane\n   * @param {number} res The square resolution of this object's lightMap.\n   * @param {WebGLRenderTexture} lightMap The lightmap to initialize the plane with.\n   */\n  _initializeBlurPlane(res, lightMap = null) {\n    const blurMaterial = new MeshBasicMaterial();\n    blurMaterial.uniforms = {\n      previousShadowMap: {\n        value: null\n      },\n      pixelOffset: {\n        value: 1 / res\n      },\n      polygonOffset: true,\n      polygonOffsetFactor: -1,\n      polygonOffsetUnits: 3\n    };\n    blurMaterial.onBeforeCompile = shader => {\n      shader.vertexShader = \"#define USE_UV\\n\" + shader.vertexShader.slice(0, -1) + \"\tgl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }\";\n      const bodyStart = shader.fragmentShader.indexOf(\"void main() {\");\n      shader.fragmentShader = \"#define USE_UV\\n\" + shader.fragmentShader.slice(0, bodyStart) + \"\tuniform sampler2D previousShadowMap;\\n\tuniform float pixelOffset;\\n\" + shader.fragmentShader.slice(bodyStart - 1, -1) + `\tgl_FragColor.rgb = (\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset,  0.0        )).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( 0.0        ,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( 0.0        , -pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset,  0.0        )).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset, -pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset, -pixelOffset)).rgb)/8.0;\n\t\t}`;\n      shader.uniforms.previousShadowMap = {\n        value: lightMap.texture\n      };\n      shader.uniforms.pixelOffset = {\n        value: 0.5 / res\n      };\n      blurMaterial.uniforms = shader.uniforms;\n      blurMaterial.userData.shader = shader;\n      this.compiled = true;\n    };\n    this.blurringPlane = new Mesh(new PlaneGeometry(1, 1), blurMaterial);\n    this.blurringPlane.name = \"Blurring Plane\";\n    this.blurringPlane.frustumCulled = false;\n    this.blurringPlane.renderOrder = 0;\n    this.blurringPlane.material.depthWrite = false;\n    this.scene.add(this.blurringPlane);\n  }\n}\nexport { ProgressiveLightMap };", "map": {"version": 3, "names": ["ProgressiveLightMap", "constructor", "renderer", "res", "lightMapContainers", "compiled", "scene", "Scene", "background", "<PERSON><PERSON><PERSON><PERSON>", "WebGLRenderTarget", "buffer1Active", "firstUpdate", "warned", "format", "test", "navigator", "userAgent", "alfFloatType", "FloatType", "progressiveLightMap1", "type", "progressiveLightMap2", "uvMat", "MeshPhongMaterial", "uniforms", "onBeforeCompile", "shader", "vertexShader", "slice", "UV1", "bodyStart", "fragmentShader", "indexOf", "previousShadowMap", "value", "texture", "averagingWindow", "userData", "addObjectsToLightMap", "objects", "uv_boxes", "padding", "ob", "length", "object", "isLight", "attach", "geometry", "hasAttribute", "console", "warn", "blurringPlane", "_initializeBlurPlane", "material", "lightMap", "dithering", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "renderOrder", "push", "w", "h", "index", "basicMat", "dimensions", "potpack", "for<PERSON>ach", "box", "uv1", "getAttribute", "clone", "i", "array", "itemSize", "x", "y", "setAttribute", "needsUpdate", "update", "camera", "blendWindow", "blurEdges", "old<PERSON><PERSON>get", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible", "l", "oldScene", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "oldFrustumCulled", "frustumCulled", "activeMap", "inactiveMap", "showDebugLightmap", "position", "labelMesh", "labelMaterial", "MeshBasicMaterial", "map", "side", "DoubleSide", "labelPlane", "PlaneGeometry", "<PERSON><PERSON>", "add", "copy", "blurMaterial", "pixelOffset", "polygonOffset", "polygonOffsetFactor", "polygonOffsetUnits", "name", "depthWrite"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/misc/ProgressiveLightmap.js"], "sourcesContent": ["import {\n  Scene,\n  WebGLRenderTarget,\n  FloatType,\n  MeshBasicMaterial,\n  MeshPhongMaterial,\n  DoubleSide,\n  PlaneGeometry,\n  Mesh,\n} from 'three'\nimport potpack from 'potpack'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * Progressive Light Map Accumulator, by [zalo](https://github.com/zalo/)\n *\n * To use, simply construct a `ProgressiveLightMap` object,\n * `plmap.addObjectsToLightMap(object)` an array of semi-static\n * objects and lights to the class once, and then call\n * `plmap.update(camera)` every frame to begin accumulating\n * lighting samples.\n *\n * This should begin accumulating lightmaps which apply to\n * your objects, so you can start jittering lighting to achieve\n * the texture-space effect you're looking for.\n *\n * @param {WebGLRenderer} renderer A WebGL Rendering Context\n * @param {number} res The side-long dimension of you total lightmap\n */\nclass ProgressiveLightMap {\n  constructor(renderer, res = 1024) {\n    this.renderer = renderer\n    this.res = res\n    this.lightMapContainers = []\n    this.compiled = false\n    this.scene = new Scene()\n    this.scene.background = null\n    this.tinyTarget = new WebGLRenderTarget(1, 1)\n    this.buffer1Active = false\n    this.firstUpdate = true\n    this.warned = false\n\n    // Create the Progressive LightMap Texture\n    const format = /(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent) ? alfFloatType : FloatType\n    this.progressiveLightMap1 = new WebGLRenderTarget(this.res, this.res, { type: format })\n    this.progressiveLightMap2 = new WebGLRenderTarget(this.res, this.res, { type: format })\n\n    // Inject some spicy new logic into a standard phong material\n    this.uvMat = new MeshPhongMaterial()\n    this.uvMat.uniforms = {}\n    this.uvMat.onBeforeCompile = (shader) => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader =\n        '#define USE_LIGHTMAP\\n' +\n        shader.vertexShader.slice(0, -1) +\n        `\tgl_Position = vec4((${UV1} - 0.5) * 2.0, 1.0, 1.0); }`\n\n      // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n      const bodyStart = shader.fragmentShader.indexOf('void main() {')\n      shader.fragmentShader =\n        `varying vec2 v${UV1 === 'uv1' ? UV1 : 'Uv2'};\\n` +\n        shader.fragmentShader.slice(0, bodyStart) +\n        '\tuniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' +\n        shader.fragmentShader.slice(bodyStart - 1, -1) +\n        `\\nvec3 texelOld = texture2D(previousShadowMap, v${UV1 === 'uv1' ? UV1 : 'Uv2'}).rgb;\n\t\t\t\tgl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/averagingWindow);\n\t\t\t}`\n\n      // Set the Previous Frame's Texture Buffer and Averaging Window\n      shader.uniforms.previousShadowMap = { value: this.progressiveLightMap1.texture }\n      shader.uniforms.averagingWindow = { value: 100 }\n\n      this.uvMat.uniforms = shader.uniforms\n\n      // Set the new Shader to this\n      this.uvMat.userData.shader = shader\n\n      this.compiled = true\n    }\n  }\n\n  /**\n   * Sets these objects' materials' lightmaps and modifies their uv1's.\n   * @param {Object3D} objects An array of objects and lights to set up your lightmap.\n   */\n  addObjectsToLightMap(objects) {\n    // Prepare list of UV bounding boxes for packing later...\n    this.uv_boxes = []\n    const padding = 3 / this.res\n\n    for (let ob = 0; ob < objects.length; ob++) {\n      const object = objects[ob]\n\n      // If this object is a light, simply add it to the internal scene\n      if (object.isLight) {\n        this.scene.attach(object)\n        continue\n      }\n\n      if (!object.geometry.hasAttribute('uv')) {\n        console.warn('All lightmap objects need UVs!')\n        continue\n      }\n\n      if (this.blurringPlane == null) {\n        this._initializeBlurPlane(this.res, this.progressiveLightMap1)\n      }\n\n      // Apply the lightmap to the object\n      object.material.lightMap = this.progressiveLightMap2.texture\n      object.material.dithering = true\n      object.castShadow = true\n      object.receiveShadow = true\n      object.renderOrder = 1000 + ob\n\n      // Prepare UV boxes for potpack\n      // TODO: Size these by object surface area\n      this.uv_boxes.push({ w: 1 + padding * 2, h: 1 + padding * 2, index: ob })\n\n      this.lightMapContainers.push({ basicMat: object.material, object: object })\n\n      this.compiled = false\n    }\n\n    // Pack the objects' lightmap UVs into the same global space\n    const dimensions = potpack(this.uv_boxes)\n    this.uv_boxes.forEach((box) => {\n      const uv1 = objects[box.index].geometry.getAttribute('uv').clone()\n      for (let i = 0; i < uv1.array.length; i += uv1.itemSize) {\n        uv1.array[i] = (uv1.array[i] + box.x + padding) / dimensions.w\n        uv1.array[i + 1] = (uv1.array[i + 1] + box.y + padding) / dimensions.h\n      }\n\n      objects[box.index].geometry.setAttribute(UV1, uv1)\n      objects[box.index].geometry.getAttribute(UV1).needsUpdate = true\n    })\n  }\n\n  /**\n   * This function renders each mesh one at a time into their respective surface maps\n   * @param {Camera} camera Standard Rendering Camera\n   * @param {number} blendWindow When >1, samples will accumulate over time.\n   * @param {boolean} blurEdges  Whether to fix UV Edges via blurring\n   */\n  update(camera, blendWindow = 100, blurEdges = true) {\n    if (this.blurringPlane == null) {\n      return\n    }\n\n    // Store the original Render Target\n    const oldTarget = this.renderer.getRenderTarget()\n\n    // The blurring plane applies blur to the seams of the lightmap\n    this.blurringPlane.visible = blurEdges\n\n    // Steal the Object3D from the real world to our special dimension\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.lightMapContainers[l].object.oldScene = this.lightMapContainers[l].object.parent\n      this.scene.attach(this.lightMapContainers[l].object)\n    }\n\n    // Render once normally to initialize everything\n    if (this.firstUpdate) {\n      this.renderer.setRenderTarget(this.tinyTarget) // Tiny for Speed\n      this.renderer.render(this.scene, camera)\n      this.firstUpdate = false\n    }\n\n    // Set each object's material to the UV Unwrapped Surface Mapping Version\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.uvMat.uniforms.averagingWindow = { value: blendWindow }\n      this.lightMapContainers[l].object.material = this.uvMat\n      this.lightMapContainers[l].object.oldFrustumCulled = this.lightMapContainers[l].object.frustumCulled\n      this.lightMapContainers[l].object.frustumCulled = false\n    }\n\n    // Ping-pong two surface buffers for reading/writing\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1\n\n    // Render the object's surface maps\n    this.renderer.setRenderTarget(activeMap)\n    this.uvMat.uniforms.previousShadowMap = { value: inactiveMap.texture }\n    this.blurringPlane.material.uniforms.previousShadowMap = { value: inactiveMap.texture }\n    this.buffer1Active = !this.buffer1Active\n    this.renderer.render(this.scene, camera)\n\n    // Restore the object's Real-time Material and add it back to the original world\n    for (let l = 0; l < this.lightMapContainers.length; l++) {\n      this.lightMapContainers[l].object.frustumCulled = this.lightMapContainers[l].object.oldFrustumCulled\n      this.lightMapContainers[l].object.material = this.lightMapContainers[l].basicMat\n      this.lightMapContainers[l].object.oldScene.attach(this.lightMapContainers[l].object)\n    }\n\n    // Restore the original Render Target\n    this.renderer.setRenderTarget(oldTarget)\n  }\n\n  /** DEBUG\n   * Draw the lightmap in the main scene.  Call this after adding the objects to it.\n   * @param {boolean} visible Whether the debug plane should be visible\n   * @param {Vector3} position Where the debug plane should be drawn\n   */\n  showDebugLightmap(visible, position = undefined) {\n    if (this.lightMapContainers.length == 0) {\n      if (!this.warned) {\n        console.warn('Call this after adding the objects!')\n        this.warned = true\n      }\n\n      return\n    }\n\n    if (this.labelMesh == null) {\n      this.labelMaterial = new MeshBasicMaterial({\n        map: this.progressiveLightMap1.texture,\n        side: DoubleSide,\n      })\n      this.labelPlane = new PlaneGeometry(100, 100)\n      this.labelMesh = new Mesh(this.labelPlane, this.labelMaterial)\n      this.labelMesh.position.y = 250\n      this.lightMapContainers[0].object.parent.add(this.labelMesh)\n    }\n\n    if (position != undefined) {\n      this.labelMesh.position.copy(position)\n    }\n\n    this.labelMesh.visible = visible\n  }\n\n  /**\n   * INTERNAL Creates the Blurring Plane\n   * @param {number} res The square resolution of this object's lightMap.\n   * @param {WebGLRenderTexture} lightMap The lightmap to initialize the plane with.\n   */\n  _initializeBlurPlane(res, lightMap = null) {\n    const blurMaterial = new MeshBasicMaterial()\n    blurMaterial.uniforms = {\n      previousShadowMap: { value: null },\n      pixelOffset: { value: 1.0 / res },\n      polygonOffset: true,\n      polygonOffsetFactor: -1,\n      polygonOffsetUnits: 3.0,\n    }\n    blurMaterial.onBeforeCompile = (shader) => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader =\n        '#define USE_UV\\n' + shader.vertexShader.slice(0, -1) + '\tgl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }'\n\n      // Fragment Shader: Set Pixels to 9-tap box blur the current frame's Shadows\n      const bodyStart = shader.fragmentShader.indexOf('void main() {')\n      shader.fragmentShader =\n        '#define USE_UV\\n' +\n        shader.fragmentShader.slice(0, bodyStart) +\n        '\tuniform sampler2D previousShadowMap;\\n\tuniform float pixelOffset;\\n' +\n        shader.fragmentShader.slice(bodyStart - 1, -1) +\n        `\tgl_FragColor.rgb = (\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset,  0.0        )).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( 0.0        ,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( 0.0        , -pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset,  0.0        )).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset,  pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2( pixelOffset, -pixelOffset)).rgb +\n\t\t\t  texture2D(previousShadowMap, vUv + vec2(-pixelOffset, -pixelOffset)).rgb)/8.0;\n\t\t}`\n\n      // Set the LightMap Accumulation Buffer\n      shader.uniforms.previousShadowMap = { value: lightMap.texture }\n      shader.uniforms.pixelOffset = { value: 0.5 / res }\n      blurMaterial.uniforms = shader.uniforms\n\n      // Set the new Shader to this\n      blurMaterial.userData.shader = shader\n\n      this.compiled = true\n    }\n\n    this.blurringPlane = new Mesh(new PlaneGeometry(1, 1), blurMaterial)\n    this.blurringPlane.name = 'Blurring Plane'\n    this.blurringPlane.frustumCulled = false\n    this.blurringPlane.renderOrder = 0\n    this.blurringPlane.material.depthWrite = false\n    this.scene.add(this.blurringPlane)\n  }\n}\n\nexport { ProgressiveLightMap }\n"], "mappings": ";;;AA6BA,MAAMA,mBAAA,CAAoB;EACxBC,YAAYC,QAAA,EAAUC,GAAA,GAAM,MAAM;IAChC,KAAKD,QAAA,GAAWA,QAAA;IAChB,KAAKC,GAAA,GAAMA,GAAA;IACX,KAAKC,kBAAA,GAAqB,EAAE;IAC5B,KAAKC,QAAA,GAAW;IAChB,KAAKC,KAAA,GAAQ,IAAIC,KAAA,CAAO;IACxB,KAAKD,KAAA,CAAME,UAAA,GAAa;IACxB,KAAKC,UAAA,GAAa,IAAIC,iBAAA,CAAkB,GAAG,CAAC;IAC5C,KAAKC,aAAA,GAAgB;IACrB,KAAKC,WAAA,GAAc;IACnB,KAAKC,MAAA,GAAS;IAGd,MAAMC,MAAA,GAAS,8BAA8BC,IAAA,CAAKC,SAAA,CAAUC,SAAS,IAAIC,YAAA,GAAeC,SAAA;IACxF,KAAKC,oBAAA,GAAuB,IAAIV,iBAAA,CAAkB,KAAKP,GAAA,EAAK,KAAKA,GAAA,EAAK;MAAEkB,IAAA,EAAMP;IAAA,CAAQ;IACtF,KAAKQ,oBAAA,GAAuB,IAAIZ,iBAAA,CAAkB,KAAKP,GAAA,EAAK,KAAKA,GAAA,EAAK;MAAEkB,IAAA,EAAMP;IAAA,CAAQ;IAGtF,KAAKS,KAAA,GAAQ,IAAIC,iBAAA,CAAmB;IACpC,KAAKD,KAAA,CAAME,QAAA,GAAW,CAAE;IACxB,KAAKF,KAAA,CAAMG,eAAA,GAAmBC,MAAA,IAAW;MAEvCA,MAAA,CAAOC,YAAA,GACL,2BACAD,MAAA,CAAOC,YAAA,CAAaC,KAAA,CAAM,GAAG,EAAE,IAC/B,wBAAwBC,GAAA;MAG1B,MAAMC,SAAA,GAAYJ,MAAA,CAAOK,cAAA,CAAeC,OAAA,CAAQ,eAAe;MAC/DN,MAAA,CAAOK,cAAA,GACL,iBAAiBF,GAAA,KAAQ,QAAQA,GAAA,GAAM;AAAA,IACvCH,MAAA,CAAOK,cAAA,CAAeH,KAAA,CAAM,GAAGE,SAAS,IACxC,6EACAJ,MAAA,CAAOK,cAAA,CAAeH,KAAA,CAAME,SAAA,GAAY,GAAG,EAAE,IAC7C;AAAA,gDAAmDD,GAAA,KAAQ,QAAQA,GAAA,GAAM;AAAA;AAAA;MAK3EH,MAAA,CAAOF,QAAA,CAASS,iBAAA,GAAoB;QAAEC,KAAA,EAAO,KAAKf,oBAAA,CAAqBgB;MAAS;MAChFT,MAAA,CAAOF,QAAA,CAASY,eAAA,GAAkB;QAAEF,KAAA,EAAO;MAAK;MAEhD,KAAKZ,KAAA,CAAME,QAAA,GAAWE,MAAA,CAAOF,QAAA;MAG7B,KAAKF,KAAA,CAAMe,QAAA,CAASX,MAAA,GAASA,MAAA;MAE7B,KAAKtB,QAAA,GAAW;IACjB;EACF;EAAA;AAAA;AAAA;AAAA;EAMDkC,qBAAqBC,OAAA,EAAS;IAE5B,KAAKC,QAAA,GAAW,EAAE;IAClB,MAAMC,OAAA,GAAU,IAAI,KAAKvC,GAAA;IAEzB,SAASwC,EAAA,GAAK,GAAGA,EAAA,GAAKH,OAAA,CAAQI,MAAA,EAAQD,EAAA,IAAM;MAC1C,MAAME,MAAA,GAASL,OAAA,CAAQG,EAAE;MAGzB,IAAIE,MAAA,CAAOC,OAAA,EAAS;QAClB,KAAKxC,KAAA,CAAMyC,MAAA,CAAOF,MAAM;QACxB;MACD;MAED,IAAI,CAACA,MAAA,CAAOG,QAAA,CAASC,YAAA,CAAa,IAAI,GAAG;QACvCC,OAAA,CAAQC,IAAA,CAAK,gCAAgC;QAC7C;MACD;MAED,IAAI,KAAKC,aAAA,IAAiB,MAAM;QAC9B,KAAKC,oBAAA,CAAqB,KAAKlD,GAAA,EAAK,KAAKiB,oBAAoB;MAC9D;MAGDyB,MAAA,CAAOS,QAAA,CAASC,QAAA,GAAW,KAAKjC,oBAAA,CAAqBc,OAAA;MACrDS,MAAA,CAAOS,QAAA,CAASE,SAAA,GAAY;MAC5BX,MAAA,CAAOY,UAAA,GAAa;MACpBZ,MAAA,CAAOa,aAAA,GAAgB;MACvBb,MAAA,CAAOc,WAAA,GAAc,MAAOhB,EAAA;MAI5B,KAAKF,QAAA,CAASmB,IAAA,CAAK;QAAEC,CAAA,EAAG,IAAInB,OAAA,GAAU;QAAGoB,CAAA,EAAG,IAAIpB,OAAA,GAAU;QAAGqB,KAAA,EAAOpB;MAAE,CAAE;MAExE,KAAKvC,kBAAA,CAAmBwD,IAAA,CAAK;QAAEI,QAAA,EAAUnB,MAAA,CAAOS,QAAA;QAAUT;MAAA,CAAgB;MAE1E,KAAKxC,QAAA,GAAW;IACjB;IAGD,MAAM4D,UAAA,GAAaC,OAAA,CAAQ,KAAKzB,QAAQ;IACxC,KAAKA,QAAA,CAAS0B,OAAA,CAASC,GAAA,IAAQ;MAC7B,MAAMC,GAAA,GAAM7B,OAAA,CAAQ4B,GAAA,CAAIL,KAAK,EAAEf,QAAA,CAASsB,YAAA,CAAa,IAAI,EAAEC,KAAA,CAAO;MAClE,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,GAAA,CAAII,KAAA,CAAM7B,MAAA,EAAQ4B,CAAA,IAAKH,GAAA,CAAIK,QAAA,EAAU;QACvDL,GAAA,CAAII,KAAA,CAAMD,CAAC,KAAKH,GAAA,CAAII,KAAA,CAAMD,CAAC,IAAIJ,GAAA,CAAIO,CAAA,GAAIjC,OAAA,IAAWuB,UAAA,CAAWJ,CAAA;QAC7DQ,GAAA,CAAII,KAAA,CAAMD,CAAA,GAAI,CAAC,KAAKH,GAAA,CAAII,KAAA,CAAMD,CAAA,GAAI,CAAC,IAAIJ,GAAA,CAAIQ,CAAA,GAAIlC,OAAA,IAAWuB,UAAA,CAAWH,CAAA;MACtE;MAEDtB,OAAA,CAAQ4B,GAAA,CAAIL,KAAK,EAAEf,QAAA,CAAS6B,YAAA,CAAa/C,GAAA,EAAKuC,GAAG;MACjD7B,OAAA,CAAQ4B,GAAA,CAAIL,KAAK,EAAEf,QAAA,CAASsB,YAAA,CAAaxC,GAAG,EAAEgD,WAAA,GAAc;IAClE,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDC,OAAOC,MAAA,EAAQC,WAAA,GAAc,KAAKC,SAAA,GAAY,MAAM;IAClD,IAAI,KAAK9B,aAAA,IAAiB,MAAM;MAC9B;IACD;IAGD,MAAM+B,SAAA,GAAY,KAAKjF,QAAA,CAASkF,eAAA,CAAiB;IAGjD,KAAKhC,aAAA,CAAciC,OAAA,GAAUH,SAAA;IAG7B,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlF,kBAAA,CAAmBwC,MAAA,EAAQ0C,CAAA,IAAK;MACvD,KAAKlF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO0C,QAAA,GAAW,KAAKnF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO2C,MAAA;MAC/E,KAAKlF,KAAA,CAAMyC,MAAA,CAAO,KAAK3C,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAM;IACpD;IAGD,IAAI,KAAKjC,WAAA,EAAa;MACpB,KAAKV,QAAA,CAASuF,eAAA,CAAgB,KAAKhF,UAAU;MAC7C,KAAKP,QAAA,CAASwF,MAAA,CAAO,KAAKpF,KAAA,EAAO0E,MAAM;MACvC,KAAKpE,WAAA,GAAc;IACpB;IAGD,SAAS0E,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlF,kBAAA,CAAmBwC,MAAA,EAAQ0C,CAAA,IAAK;MACvD,KAAK/D,KAAA,CAAME,QAAA,CAASY,eAAA,GAAkB;QAAEF,KAAA,EAAO8C;MAAa;MAC5D,KAAK7E,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAOS,QAAA,GAAW,KAAK/B,KAAA;MAClD,KAAKnB,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO8C,gBAAA,GAAmB,KAAKvF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO+C,aAAA;MACvF,KAAKxF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO+C,aAAA,GAAgB;IACnD;IAGD,MAAMC,SAAA,GAAY,KAAKlF,aAAA,GAAgB,KAAKS,oBAAA,GAAuB,KAAKE,oBAAA;IACxE,MAAMwE,WAAA,GAAc,KAAKnF,aAAA,GAAgB,KAAKW,oBAAA,GAAuB,KAAKF,oBAAA;IAG1E,KAAKlB,QAAA,CAASuF,eAAA,CAAgBI,SAAS;IACvC,KAAKtE,KAAA,CAAME,QAAA,CAASS,iBAAA,GAAoB;MAAEC,KAAA,EAAO2D,WAAA,CAAY1D;IAAS;IACtE,KAAKgB,aAAA,CAAcE,QAAA,CAAS7B,QAAA,CAASS,iBAAA,GAAoB;MAAEC,KAAA,EAAO2D,WAAA,CAAY1D;IAAS;IACvF,KAAKzB,aAAA,GAAgB,CAAC,KAAKA,aAAA;IAC3B,KAAKT,QAAA,CAASwF,MAAA,CAAO,KAAKpF,KAAA,EAAO0E,MAAM;IAGvC,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlF,kBAAA,CAAmBwC,MAAA,EAAQ0C,CAAA,IAAK;MACvD,KAAKlF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO+C,aAAA,GAAgB,KAAKxF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO8C,gBAAA;MACpF,KAAKvF,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAOS,QAAA,GAAW,KAAKlD,kBAAA,CAAmBkF,CAAC,EAAEtB,QAAA;MACxE,KAAK5D,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAA,CAAO0C,QAAA,CAASxC,MAAA,CAAO,KAAK3C,kBAAA,CAAmBkF,CAAC,EAAEzC,MAAM;IACpF;IAGD,KAAK3C,QAAA,CAASuF,eAAA,CAAgBN,SAAS;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;EAODY,kBAAkBV,OAAA,EAASW,QAAA,GAAW,QAAW;IAC/C,IAAI,KAAK5F,kBAAA,CAAmBwC,MAAA,IAAU,GAAG;MACvC,IAAI,CAAC,KAAK/B,MAAA,EAAQ;QAChBqC,OAAA,CAAQC,IAAA,CAAK,qCAAqC;QAClD,KAAKtC,MAAA,GAAS;MACf;MAED;IACD;IAED,IAAI,KAAKoF,SAAA,IAAa,MAAM;MAC1B,KAAKC,aAAA,GAAgB,IAAIC,iBAAA,CAAkB;QACzCC,GAAA,EAAK,KAAKhF,oBAAA,CAAqBgB,OAAA;QAC/BiE,IAAA,EAAMC;MACd,CAAO;MACD,KAAKC,UAAA,GAAa,IAAIC,aAAA,CAAc,KAAK,GAAG;MAC5C,KAAKP,SAAA,GAAY,IAAIQ,IAAA,CAAK,KAAKF,UAAA,EAAY,KAAKL,aAAa;MAC7D,KAAKD,SAAA,CAAUD,QAAA,CAASpB,CAAA,GAAI;MAC5B,KAAKxE,kBAAA,CAAmB,CAAC,EAAEyC,MAAA,CAAO2C,MAAA,CAAOkB,GAAA,CAAI,KAAKT,SAAS;IAC5D;IAED,IAAID,QAAA,IAAY,QAAW;MACzB,KAAKC,SAAA,CAAUD,QAAA,CAASW,IAAA,CAAKX,QAAQ;IACtC;IAED,KAAKC,SAAA,CAAUZ,OAAA,GAAUA,OAAA;EAC1B;EAAA;AAAA;AAAA;AAAA;AAAA;EAODhC,qBAAqBlD,GAAA,EAAKoD,QAAA,GAAW,MAAM;IACzC,MAAMqD,YAAA,GAAe,IAAIT,iBAAA,CAAmB;IAC5CS,YAAA,CAAanF,QAAA,GAAW;MACtBS,iBAAA,EAAmB;QAAEC,KAAA,EAAO;MAAM;MAClC0E,WAAA,EAAa;QAAE1E,KAAA,EAAO,IAAMhC;MAAK;MACjC2G,aAAA,EAAe;MACfC,mBAAA,EAAqB;MACrBC,kBAAA,EAAoB;IACrB;IACDJ,YAAA,CAAalF,eAAA,GAAmBC,MAAA,IAAW;MAEzCA,MAAA,CAAOC,YAAA,GACL,qBAAqBD,MAAA,CAAOC,YAAA,CAAaC,KAAA,CAAM,GAAG,EAAE,IAAI;MAG1D,MAAME,SAAA,GAAYJ,MAAA,CAAOK,cAAA,CAAeC,OAAA,CAAQ,eAAe;MAC/DN,MAAA,CAAOK,cAAA,GACL,qBACAL,MAAA,CAAOK,cAAA,CAAeH,KAAA,CAAM,GAAGE,SAAS,IACxC,yEACAJ,MAAA,CAAOK,cAAA,CAAeH,KAAA,CAAME,SAAA,GAAY,GAAG,EAAE,IAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAYFJ,MAAA,CAAOF,QAAA,CAASS,iBAAA,GAAoB;QAAEC,KAAA,EAAOoB,QAAA,CAASnB;MAAS;MAC/DT,MAAA,CAAOF,QAAA,CAASoF,WAAA,GAAc;QAAE1E,KAAA,EAAO,MAAMhC;MAAK;MAClDyG,YAAA,CAAanF,QAAA,GAAWE,MAAA,CAAOF,QAAA;MAG/BmF,YAAA,CAAatE,QAAA,CAASX,MAAA,GAASA,MAAA;MAE/B,KAAKtB,QAAA,GAAW;IACjB;IAED,KAAK+C,aAAA,GAAgB,IAAIqD,IAAA,CAAK,IAAID,aAAA,CAAc,GAAG,CAAC,GAAGI,YAAY;IACnE,KAAKxD,aAAA,CAAc6D,IAAA,GAAO;IAC1B,KAAK7D,aAAA,CAAcwC,aAAA,GAAgB;IACnC,KAAKxC,aAAA,CAAcO,WAAA,GAAc;IACjC,KAAKP,aAAA,CAAcE,QAAA,CAAS4D,UAAA,GAAa;IACzC,KAAK5G,KAAA,CAAMoG,GAAA,CAAI,KAAKtD,aAAa;EAClC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}