{"ast": null, "code": "import { UniformsUtils, ShaderMaterial, NoBlending, WebGLRenderTarget } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nclass SavePass extends Pass {\n  constructor(renderTarget) {\n    super();\n    if (CopyShader === void 0) console.error(\"THREE.SavePass relies on CopyShader\");\n    const shader = CopyShader;\n    this.textureID = \"tDiffuse\";\n    this.uniforms = UniformsUtils.clone(shader.uniforms);\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      blending: NoBlending\n    });\n    this.renderTarget = renderTarget;\n    if (this.renderTarget === void 0) {\n      this.renderTarget = new WebGLRenderTarget(window.innerWidth, window.innerHeight);\n      this.renderTarget.texture.name = \"SavePass.rt\";\n    }\n    this.needsSwap = false;\n    this.fsQuad = new FullScreenQuad(this.material);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture;\n    }\n    renderer.setRenderTarget(this.renderTarget);\n    if (this.clear) renderer.clear();\n    this.fsQuad.render(renderer);\n  }\n}\nexport { SavePass };", "map": {"version": 3, "names": ["SavePass", "Pass", "constructor", "renderTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "error", "shader", "textureID", "uniforms", "UniformsUtils", "clone", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "blending", "NoBlending", "WebGLRenderTarget", "window", "innerWidth", "innerHeight", "texture", "name", "needsSwap", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/SavePass.js"], "sourcesContent": ["import { NoBlending, ShaderMaterial, UniformsUtils, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass SavePass extends Pass {\n  constructor(renderTarget) {\n    super()\n\n    if (CopyShader === undefined) console.error('THREE.SavePass relies on CopyShader')\n\n    const shader = CopyShader\n\n    this.textureID = 'tDiffuse'\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      blending: NoBlending,\n    })\n\n    this.renderTarget = renderTarget\n\n    if (this.renderTarget === undefined) {\n      this.renderTarget = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n      this.renderTarget.texture.name = 'SavePass.rt'\n    }\n\n    this.needsSwap = false\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */) {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture\n    }\n\n    renderer.setRenderTarget(this.renderTarget)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n  }\n}\n\nexport { SavePass }\n"], "mappings": ";;;AAIA,MAAMA,QAAA,SAAiBC,IAAA,CAAK;EAC1BC,YAAYC,YAAA,EAAc;IACxB,MAAO;IAEP,IAAIC,UAAA,KAAe,QAAWC,OAAA,CAAQC,KAAA,CAAM,qCAAqC;IAEjF,MAAMC,MAAA,GAASH,UAAA;IAEf,KAAKI,SAAA,GAAY;IAEjB,KAAKC,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMJ,MAAA,CAAOE,QAAQ;IAEnD,KAAKG,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCJ,QAAA,EAAU,KAAKA,QAAA;MACfK,YAAA,EAAcP,MAAA,CAAOO,YAAA;MACrBC,cAAA,EAAgBR,MAAA,CAAOQ,cAAA;MACvBC,QAAA,EAAUC;IAChB,CAAK;IAED,KAAKd,YAAA,GAAeA,YAAA;IAEpB,IAAI,KAAKA,YAAA,KAAiB,QAAW;MACnC,KAAKA,YAAA,GAAe,IAAIe,iBAAA,CAAkBC,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW;MAC/E,KAAKlB,YAAA,CAAamB,OAAA,CAAQC,IAAA,GAAO;IAClC;IAED,KAAKC,SAAA,GAAY;IAEjB,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKd,QAAQ;EAC/C;EAEDe,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAyC;IACrE,IAAI,KAAKrB,QAAA,CAAS,KAAKD,SAAS,GAAG;MACjC,KAAKC,QAAA,CAAS,KAAKD,SAAS,EAAEuB,KAAA,GAAQD,UAAA,CAAWR,OAAA;IAClD;IAEDM,QAAA,CAASI,eAAA,CAAgB,KAAK7B,YAAY;IAC1C,IAAI,KAAK8B,KAAA,EAAOL,QAAA,CAASK,KAAA,CAAO;IAChC,KAAKR,MAAA,CAAOE,MAAA,CAAOC,QAAQ;EAC5B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}