{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Quaternion } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\n/**\n * Wraps children in a billboarded group. Sample usage:\n *\n * ```js\n * <Billboard>\n *   <Text>hi</Text>\n * </Billboard>\n * ```\n */\nconst Billboard = /* @__PURE__ */React.forwardRef(function Billboard({\n  children,\n  follow = true,\n  lockX = false,\n  lockY = false,\n  lockZ = false,\n  ...props\n}, fref) {\n  const inner = React.useRef(null);\n  const localRef = React.useRef(null);\n  const q = new Quaternion();\n  useFrame(({\n    camera\n  }) => {\n    if (!follow || !localRef.current) return;\n\n    // save previous rotation in case we're locking an axis\n    const prevRotation = inner.current.rotation.clone();\n\n    // always face the camera\n    localRef.current.updateMatrix();\n    localRef.current.updateWorldMatrix(false, false);\n    localRef.current.getWorldQuaternion(q);\n    camera.getWorldQuaternion(inner.current.quaternion).premultiply(q.invert());\n\n    // readjust any axis that is locked\n    if (lockX) inner.current.rotation.x = prevRotation.x;\n    if (lockY) inner.current.rotation.y = prevRotation.y;\n    if (lockZ) inner.current.rotation.z = prevRotation.z;\n  });\n  React.useImperativeHandle(fref, () => localRef.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: localRef\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children));\n});\nexport { Billboard };", "map": {"version": 3, "names": ["_extends", "React", "Quaternion", "useFrame", "Billboard", "forwardRef", "children", "follow", "lockX", "lockY", "lockZ", "props", "fref", "inner", "useRef", "localRef", "q", "camera", "current", "prevRotation", "rotation", "clone", "updateMatrix", "updateWorldMatrix", "getWorldQuaternion", "quaternion", "premultiply", "invert", "x", "y", "z", "useImperativeHandle", "createElement", "ref"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Billboard.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Quaternion } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\n/**\n * Wraps children in a billboarded group. Sample usage:\n *\n * ```js\n * <Billboard>\n *   <Text>hi</Text>\n * </Billboard>\n * ```\n */\nconst Billboard = /* @__PURE__ */React.forwardRef(function Billboard({\n  children,\n  follow = true,\n  lockX = false,\n  lockY = false,\n  lockZ = false,\n  ...props\n}, fref) {\n  const inner = React.useRef(null);\n  const localRef = React.useRef(null);\n  const q = new Quaternion();\n  useFrame(({\n    camera\n  }) => {\n    if (!follow || !localRef.current) return;\n\n    // save previous rotation in case we're locking an axis\n    const prevRotation = inner.current.rotation.clone();\n\n    // always face the camera\n    localRef.current.updateMatrix();\n    localRef.current.updateWorldMatrix(false, false);\n    localRef.current.getWorldQuaternion(q);\n    camera.getWorldQuaternion(inner.current.quaternion).premultiply(q.invert());\n\n    // readjust any axis that is locked\n    if (lockX) inner.current.rotation.x = prevRotation.x;\n    if (lockY) inner.current.rotation.y = prevRotation.y;\n    if (lockZ) inner.current.rotation.z = prevRotation.z;\n  });\n  React.useImperativeHandle(fref, () => localRef.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: localRef\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children));\n});\n\nexport { Billboard };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,eAAeH,KAAK,CAACI,UAAU,CAAC,SAASD,SAASA,CAAC;EACnEE,QAAQ;EACRC,MAAM,GAAG,IAAI;EACbC,KAAK,GAAG,KAAK;EACbC,KAAK,GAAG,KAAK;EACbC,KAAK,GAAG,KAAK;EACb,GAAGC;AACL,CAAC,EAAEC,IAAI,EAAE;EACP,MAAMC,KAAK,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMC,QAAQ,GAAGd,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EACnC,MAAME,CAAC,GAAG,IAAId,UAAU,CAAC,CAAC;EAC1BC,QAAQ,CAAC,CAAC;IACRc;EACF,CAAC,KAAK;IACJ,IAAI,CAACV,MAAM,IAAI,CAACQ,QAAQ,CAACG,OAAO,EAAE;;IAElC;IACA,MAAMC,YAAY,GAAGN,KAAK,CAACK,OAAO,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC;;IAEnD;IACAN,QAAQ,CAACG,OAAO,CAACI,YAAY,CAAC,CAAC;IAC/BP,QAAQ,CAACG,OAAO,CAACK,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC;IAChDR,QAAQ,CAACG,OAAO,CAACM,kBAAkB,CAACR,CAAC,CAAC;IACtCC,MAAM,CAACO,kBAAkB,CAACX,KAAK,CAACK,OAAO,CAACO,UAAU,CAAC,CAACC,WAAW,CAACV,CAAC,CAACW,MAAM,CAAC,CAAC,CAAC;;IAE3E;IACA,IAAInB,KAAK,EAAEK,KAAK,CAACK,OAAO,CAACE,QAAQ,CAACQ,CAAC,GAAGT,YAAY,CAACS,CAAC;IACpD,IAAInB,KAAK,EAAEI,KAAK,CAACK,OAAO,CAACE,QAAQ,CAACS,CAAC,GAAGV,YAAY,CAACU,CAAC;IACpD,IAAInB,KAAK,EAAEG,KAAK,CAACK,OAAO,CAACE,QAAQ,CAACU,CAAC,GAAGX,YAAY,CAACW,CAAC;EACtD,CAAC,CAAC;EACF7B,KAAK,CAAC8B,mBAAmB,CAACnB,IAAI,EAAE,MAAMG,QAAQ,CAACG,OAAO,EAAE,EAAE,CAAC;EAC3D,OAAO,aAAajB,KAAK,CAAC+B,aAAa,CAAC,OAAO,EAAEhC,QAAQ,CAAC;IACxDiC,GAAG,EAAElB;EACP,CAAC,EAAEJ,KAAK,CAAC,EAAE,aAAaV,KAAK,CAAC+B,aAAa,CAAC,OAAO,EAAE;IACnDC,GAAG,EAAEpB;EACP,CAAC,EAAEP,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}