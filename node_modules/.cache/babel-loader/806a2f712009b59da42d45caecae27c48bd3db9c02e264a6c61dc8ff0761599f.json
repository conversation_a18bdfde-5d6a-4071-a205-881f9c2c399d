{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { context, useThree, createPortal, useFrame } from '@react-three/fiber';\nimport tunnel from 'tunnel-rat';\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst col = /* @__PURE__ */new THREE.Color();\nconst tracked = /* @__PURE__ */tunnel();\nfunction computeContainerPosition(canvasSize, trackRect) {\n  const {\n    right,\n    top,\n    left: trackLeft,\n    bottom: trackBottom,\n    width,\n    height\n  } = trackRect;\n  const isOffscreen = trackRect.bottom < 0 || top > canvasSize.height || right < 0 || trackRect.left > canvasSize.width;\n  const canvasBottom = canvasSize.top + canvasSize.height;\n  const bottom = canvasBottom - trackBottom;\n  const left = trackLeft - canvasSize.left;\n  return {\n    position: {\n      width,\n      height,\n      left,\n      top,\n      bottom,\n      right\n    },\n    isOffscreen\n  };\n}\nfunction prepareSkissor(state, {\n  left,\n  bottom,\n  width,\n  height\n}) {\n  let autoClear;\n  const aspect = width / height;\n  if (isOrthographicCamera(state.camera)) {\n    if (!state.camera.manual) {\n      if (state.camera.left !== width / -2 || state.camera.right !== width / 2 || state.camera.top !== height / 2 || state.camera.bottom !== height / -2) {\n        Object.assign(state.camera, {\n          left: width / -2,\n          right: width / 2,\n          top: height / 2,\n          bottom: height / -2\n        });\n        state.camera.updateProjectionMatrix();\n      }\n    } else {\n      state.camera.updateProjectionMatrix();\n    }\n  } else if (state.camera.aspect !== aspect) {\n    state.camera.aspect = aspect;\n    state.camera.updateProjectionMatrix();\n  }\n  autoClear = state.gl.autoClear;\n  state.gl.autoClear = false;\n  state.gl.setViewport(left, bottom, width, height);\n  state.gl.setScissor(left, bottom, width, height);\n  state.gl.setScissorTest(true);\n  return autoClear;\n}\nfunction finishSkissor(state, autoClear) {\n  // Restore the default state\n  state.gl.setScissorTest(false);\n  state.gl.autoClear = autoClear;\n}\nfunction clear(state) {\n  state.gl.getClearColor(col);\n  state.gl.setClearColor(col, state.gl.getClearAlpha());\n  state.gl.clear(true, true);\n}\nfunction Container({\n  visible = true,\n  canvasSize,\n  scene,\n  index,\n  children,\n  frames,\n  rect,\n  track\n}) {\n  const rootState = useThree();\n  const [isOffscreen, setOffscreen] = React.useState(false);\n  let frameCount = 0;\n  useFrame(state => {\n    if (frames === Infinity || frameCount <= frames) {\n      var _track$current;\n      if (track) rect.current = (_track$current = track.current) == null ? void 0 : _track$current.getBoundingClientRect();\n      frameCount++;\n    }\n    if (rect.current) {\n      const {\n        position,\n        isOffscreen: _isOffscreen\n      } = computeContainerPosition(canvasSize, rect.current);\n      if (isOffscreen !== _isOffscreen) setOffscreen(_isOffscreen);\n      if (visible && !isOffscreen && rect.current) {\n        const autoClear = prepareSkissor(state, position);\n        // When children are present render the portalled scene, otherwise the default scene\n        state.gl.render(children ? state.scene : scene, state.camera);\n        finishSkissor(state, autoClear);\n      }\n    }\n  }, index);\n  React.useLayoutEffect(() => {\n    const curRect = rect.current;\n    if (curRect && (!visible || !isOffscreen)) {\n      // If the view is not visible clear it once, but stop rendering afterwards!\n      const {\n        position\n      } = computeContainerPosition(canvasSize, curRect);\n      const autoClear = prepareSkissor(rootState, position);\n      clear(rootState);\n      finishSkissor(rootState, autoClear);\n    }\n  }, [visible, isOffscreen]);\n  React.useEffect(() => {\n    if (!track) return;\n    const curRect = rect.current;\n    // Connect the event layer to the tracking element\n    const old = rootState.get().events.connected;\n    rootState.setEvents({\n      connected: track.current\n    });\n    return () => {\n      if (curRect) {\n        const {\n          position\n        } = computeContainerPosition(canvasSize, curRect);\n        const autoClear = prepareSkissor(rootState, position);\n        clear(rootState);\n        finishSkissor(rootState, autoClear);\n      }\n      rootState.setEvents({\n        connected: old\n      });\n    };\n  }, [track]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  }));\n}\nconst CanvasView = /* @__PURE__ */React.forwardRef(({\n  track,\n  visible = true,\n  index = 1,\n  id,\n  style,\n  className,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  var _rect$current, _rect$current2, _rect$current3, _rect$current4;\n  const rect = React.useRef(null);\n  const {\n    size,\n    scene\n  } = useThree();\n  const [virtualScene] = React.useState(() => new THREE.Scene());\n  const [ready, toggle] = React.useReducer(() => true, false);\n  const compute = React.useCallback((event, state) => {\n    if (rect.current && track && track.current && event.target === track.current) {\n      const {\n        width,\n        height,\n        left,\n        top\n      } = rect.current;\n      const x = event.clientX - left;\n      const y = event.clientY - top;\n      state.pointer.set(x / width * 2 - 1, -(y / height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    }\n  }, [rect, track]);\n  React.useEffect(() => {\n    var _track$current2;\n    // We need the tracking elements bounds beforehand in order to inject it into the portal\n    if (track) rect.current = (_track$current2 = track.current) == null ? void 0 : _track$current2.getBoundingClientRect();\n    // And now we can proceed\n    toggle();\n  }, [track]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: fref\n  }, props), ready && createPortal(/*#__PURE__*/React.createElement(Container, {\n    visible: visible,\n    canvasSize: size,\n    frames: frames,\n    scene: scene,\n    track: track,\n    rect: rect,\n    index: index\n  }, children), virtualScene, {\n    events: {\n      compute,\n      priority: index\n    },\n    size: {\n      width: (_rect$current = rect.current) == null ? void 0 : _rect$current.width,\n      height: (_rect$current2 = rect.current) == null ? void 0 : _rect$current2.height,\n      // @ts-ignore\n      top: (_rect$current3 = rect.current) == null ? void 0 : _rect$current3.top,\n      // @ts-ignore\n      left: (_rect$current4 = rect.current) == null ? void 0 : _rect$current4.left\n    }\n  }));\n});\nconst HtmlView = /* @__PURE__ */React.forwardRef(({\n  as: El = 'div',\n  id,\n  visible,\n  className,\n  style,\n  index = 1,\n  track,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  const uuid = React.useId();\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref,\n    id: id,\n    className: className,\n    style: style\n  }, props)), /*#__PURE__*/React.createElement(tracked.In, null, /*#__PURE__*/React.createElement(CanvasView, {\n    visible: visible,\n    key: uuid,\n    track: ref,\n    frames: frames,\n    index: index\n  }, children)));\n});\nconst View = /* @__PURE__ */(() => {\n  const _View = /*#__PURE__*/React.forwardRef((props, fref) => {\n    // If we're inside a canvas we should be able to access the context store\n    const store = React.useContext(context);\n    // If that's not the case we render a tunnel\n    if (!store) return /*#__PURE__*/React.createElement(HtmlView, _extends({\n      ref: fref\n    }, props));\n    // Otherwise a plain canvas-view\n    else return /*#__PURE__*/React.createElement(CanvasView, _extends({\n      ref: fref\n    }, props));\n  });\n  _View.Port = () => /*#__PURE__*/React.createElement(tracked.Out, null);\n  return _View;\n})();\nexport { View };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "context", "useThree", "createPortal", "useFrame", "tunnel", "isOrthographicCamera", "def", "col", "Color", "tracked", "computeContainerPosition", "canvasSize", "trackRect", "right", "top", "left", "trackLeft", "bottom", "trackBottom", "width", "height", "isOffscreen", "canvasBottom", "position", "prepareSkissor", "state", "autoClear", "aspect", "camera", "manual", "Object", "assign", "updateProjectionMatrix", "gl", "setViewport", "set<PERSON><PERSON>sor", "setScissorTest", "finishSkissor", "clear", "getClearColor", "setClearColor", "getClearAlpha", "Container", "visible", "scene", "index", "children", "frames", "rect", "track", "rootState", "setOffscreen", "useState", "frameCount", "Infinity", "_track$current", "current", "getBoundingClientRect", "_isOffscreen", "render", "useLayoutEffect", "curR<PERSON>t", "useEffect", "old", "get", "events", "connected", "setEvents", "createElement", "Fragment", "onPointerOver", "CanvasView", "forwardRef", "id", "style", "className", "props", "fref", "_rect$current", "_rect$current2", "_rect$current3", "_rect$current4", "useRef", "size", "virtualScene", "Scene", "ready", "toggle", "useReducer", "compute", "useCallback", "event", "target", "x", "clientX", "y", "clientY", "pointer", "set", "raycaster", "setFromCamera", "_track$current2", "ref", "priority", "HtmlView", "as", "El", "uuid", "useId", "useImperativeHandle", "In", "key", "View", "_View", "store", "useContext", "Port", "Out"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/View.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { context, useThree, createPortal, useFrame } from '@react-three/fiber';\nimport tunnel from 'tunnel-rat';\n\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst col = /* @__PURE__ */new THREE.Color();\nconst tracked = /* @__PURE__ */tunnel();\nfunction computeContainerPosition(canvasSize, trackRect) {\n  const {\n    right,\n    top,\n    left: trackLeft,\n    bottom: trackBottom,\n    width,\n    height\n  } = trackRect;\n  const isOffscreen = trackRect.bottom < 0 || top > canvasSize.height || right < 0 || trackRect.left > canvasSize.width;\n  const canvasBottom = canvasSize.top + canvasSize.height;\n  const bottom = canvasBottom - trackBottom;\n  const left = trackLeft - canvasSize.left;\n  return {\n    position: {\n      width,\n      height,\n      left,\n      top,\n      bottom,\n      right\n    },\n    isOffscreen\n  };\n}\nfunction prepareSkissor(state, {\n  left,\n  bottom,\n  width,\n  height\n}) {\n  let autoClear;\n  const aspect = width / height;\n  if (isOrthographicCamera(state.camera)) {\n    if (!state.camera.manual) {\n      if (state.camera.left !== width / -2 || state.camera.right !== width / 2 || state.camera.top !== height / 2 || state.camera.bottom !== height / -2) {\n        Object.assign(state.camera, {\n          left: width / -2,\n          right: width / 2,\n          top: height / 2,\n          bottom: height / -2\n        });\n        state.camera.updateProjectionMatrix();\n      }\n    } else {\n      state.camera.updateProjectionMatrix();\n    }\n  } else if (state.camera.aspect !== aspect) {\n    state.camera.aspect = aspect;\n    state.camera.updateProjectionMatrix();\n  }\n  autoClear = state.gl.autoClear;\n  state.gl.autoClear = false;\n  state.gl.setViewport(left, bottom, width, height);\n  state.gl.setScissor(left, bottom, width, height);\n  state.gl.setScissorTest(true);\n  return autoClear;\n}\nfunction finishSkissor(state, autoClear) {\n  // Restore the default state\n  state.gl.setScissorTest(false);\n  state.gl.autoClear = autoClear;\n}\nfunction clear(state) {\n  state.gl.getClearColor(col);\n  state.gl.setClearColor(col, state.gl.getClearAlpha());\n  state.gl.clear(true, true);\n}\nfunction Container({\n  visible = true,\n  canvasSize,\n  scene,\n  index,\n  children,\n  frames,\n  rect,\n  track\n}) {\n  const rootState = useThree();\n  const [isOffscreen, setOffscreen] = React.useState(false);\n  let frameCount = 0;\n  useFrame(state => {\n    if (frames === Infinity || frameCount <= frames) {\n      var _track$current;\n      if (track) rect.current = (_track$current = track.current) == null ? void 0 : _track$current.getBoundingClientRect();\n      frameCount++;\n    }\n    if (rect.current) {\n      const {\n        position,\n        isOffscreen: _isOffscreen\n      } = computeContainerPosition(canvasSize, rect.current);\n      if (isOffscreen !== _isOffscreen) setOffscreen(_isOffscreen);\n      if (visible && !isOffscreen && rect.current) {\n        const autoClear = prepareSkissor(state, position);\n        // When children are present render the portalled scene, otherwise the default scene\n        state.gl.render(children ? state.scene : scene, state.camera);\n        finishSkissor(state, autoClear);\n      }\n    }\n  }, index);\n  React.useLayoutEffect(() => {\n    const curRect = rect.current;\n    if (curRect && (!visible || !isOffscreen)) {\n      // If the view is not visible clear it once, but stop rendering afterwards!\n      const {\n        position\n      } = computeContainerPosition(canvasSize, curRect);\n      const autoClear = prepareSkissor(rootState, position);\n      clear(rootState);\n      finishSkissor(rootState, autoClear);\n    }\n  }, [visible, isOffscreen]);\n  React.useEffect(() => {\n    if (!track) return;\n    const curRect = rect.current;\n    // Connect the event layer to the tracking element\n    const old = rootState.get().events.connected;\n    rootState.setEvents({\n      connected: track.current\n    });\n    return () => {\n      if (curRect) {\n        const {\n          position\n        } = computeContainerPosition(canvasSize, curRect);\n        const autoClear = prepareSkissor(rootState, position);\n        clear(rootState);\n        finishSkissor(rootState, autoClear);\n      }\n      rootState.setEvents({\n        connected: old\n      });\n    };\n  }, [track]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  }));\n}\nconst CanvasView = /* @__PURE__ */React.forwardRef(({\n  track,\n  visible = true,\n  index = 1,\n  id,\n  style,\n  className,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  var _rect$current, _rect$current2, _rect$current3, _rect$current4;\n  const rect = React.useRef(null);\n  const {\n    size,\n    scene\n  } = useThree();\n  const [virtualScene] = React.useState(() => new THREE.Scene());\n  const [ready, toggle] = React.useReducer(() => true, false);\n  const compute = React.useCallback((event, state) => {\n    if (rect.current && track && track.current && event.target === track.current) {\n      const {\n        width,\n        height,\n        left,\n        top\n      } = rect.current;\n      const x = event.clientX - left;\n      const y = event.clientY - top;\n      state.pointer.set(x / width * 2 - 1, -(y / height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    }\n  }, [rect, track]);\n  React.useEffect(() => {\n    var _track$current2;\n    // We need the tracking elements bounds beforehand in order to inject it into the portal\n    if (track) rect.current = (_track$current2 = track.current) == null ? void 0 : _track$current2.getBoundingClientRect();\n    // And now we can proceed\n    toggle();\n  }, [track]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: fref\n  }, props), ready && createPortal(/*#__PURE__*/React.createElement(Container, {\n    visible: visible,\n    canvasSize: size,\n    frames: frames,\n    scene: scene,\n    track: track,\n    rect: rect,\n    index: index\n  }, children), virtualScene, {\n    events: {\n      compute,\n      priority: index\n    },\n    size: {\n      width: (_rect$current = rect.current) == null ? void 0 : _rect$current.width,\n      height: (_rect$current2 = rect.current) == null ? void 0 : _rect$current2.height,\n      // @ts-ignore\n      top: (_rect$current3 = rect.current) == null ? void 0 : _rect$current3.top,\n      // @ts-ignore\n      left: (_rect$current4 = rect.current) == null ? void 0 : _rect$current4.left\n    }\n  }));\n});\nconst HtmlView = /* @__PURE__ */React.forwardRef(({\n  as: El = 'div',\n  id,\n  visible,\n  className,\n  style,\n  index = 1,\n  track,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  const uuid = React.useId();\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref,\n    id: id,\n    className: className,\n    style: style\n  }, props)), /*#__PURE__*/React.createElement(tracked.In, null, /*#__PURE__*/React.createElement(CanvasView, {\n    visible: visible,\n    key: uuid,\n    track: ref,\n    frames: frames,\n    index: index\n  }, children)));\n});\nconst View = /* @__PURE__ */(() => {\n  const _View = /*#__PURE__*/React.forwardRef((props, fref) => {\n    // If we're inside a canvas we should be able to access the context store\n    const store = React.useContext(context);\n    // If that's not the case we render a tunnel\n    if (!store) return /*#__PURE__*/React.createElement(HtmlView, _extends({\n      ref: fref\n    }, props));\n    // Otherwise a plain canvas-view\n    else return /*#__PURE__*/React.createElement(CanvasView, _extends({\n      ref: fref\n    }, props));\n  });\n  _View.Port = () => /*#__PURE__*/React.createElement(tracked.Out, null);\n  return _View;\n})();\n\nexport { View };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AAC9E,OAAOC,MAAM,MAAM,YAAY;AAE/B,MAAMC,oBAAoB,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,oBAAoB;AACnE,MAAME,GAAG,GAAG,eAAe,IAAIR,KAAK,CAACS,KAAK,CAAC,CAAC;AAC5C,MAAMC,OAAO,GAAG,eAAeL,MAAM,CAAC,CAAC;AACvC,SAASM,wBAAwBA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACvD,MAAM;IACJC,KAAK;IACLC,GAAG;IACHC,IAAI,EAAEC,SAAS;IACfC,MAAM,EAAEC,WAAW;IACnBC,KAAK;IACLC;EACF,CAAC,GAAGR,SAAS;EACb,MAAMS,WAAW,GAAGT,SAAS,CAACK,MAAM,GAAG,CAAC,IAAIH,GAAG,GAAGH,UAAU,CAACS,MAAM,IAAIP,KAAK,GAAG,CAAC,IAAID,SAAS,CAACG,IAAI,GAAGJ,UAAU,CAACQ,KAAK;EACrH,MAAMG,YAAY,GAAGX,UAAU,CAACG,GAAG,GAAGH,UAAU,CAACS,MAAM;EACvD,MAAMH,MAAM,GAAGK,YAAY,GAAGJ,WAAW;EACzC,MAAMH,IAAI,GAAGC,SAAS,GAAGL,UAAU,CAACI,IAAI;EACxC,OAAO;IACLQ,QAAQ,EAAE;MACRJ,KAAK;MACLC,MAAM;MACNL,IAAI;MACJD,GAAG;MACHG,MAAM;MACNJ;IACF,CAAC;IACDQ;EACF,CAAC;AACH;AACA,SAASG,cAAcA,CAACC,KAAK,EAAE;EAC7BV,IAAI;EACJE,MAAM;EACNE,KAAK;EACLC;AACF,CAAC,EAAE;EACD,IAAIM,SAAS;EACb,MAAMC,MAAM,GAAGR,KAAK,GAAGC,MAAM;EAC7B,IAAIf,oBAAoB,CAACoB,KAAK,CAACG,MAAM,CAAC,EAAE;IACtC,IAAI,CAACH,KAAK,CAACG,MAAM,CAACC,MAAM,EAAE;MACxB,IAAIJ,KAAK,CAACG,MAAM,CAACb,IAAI,KAAKI,KAAK,GAAG,CAAC,CAAC,IAAIM,KAAK,CAACG,MAAM,CAACf,KAAK,KAAKM,KAAK,GAAG,CAAC,IAAIM,KAAK,CAACG,MAAM,CAACd,GAAG,KAAKM,MAAM,GAAG,CAAC,IAAIK,KAAK,CAACG,MAAM,CAACX,MAAM,KAAKG,MAAM,GAAG,CAAC,CAAC,EAAE;QAClJU,MAAM,CAACC,MAAM,CAACN,KAAK,CAACG,MAAM,EAAE;UAC1Bb,IAAI,EAAEI,KAAK,GAAG,CAAC,CAAC;UAChBN,KAAK,EAAEM,KAAK,GAAG,CAAC;UAChBL,GAAG,EAAEM,MAAM,GAAG,CAAC;UACfH,MAAM,EAAEG,MAAM,GAAG,CAAC;QACpB,CAAC,CAAC;QACFK,KAAK,CAACG,MAAM,CAACI,sBAAsB,CAAC,CAAC;MACvC;IACF,CAAC,MAAM;MACLP,KAAK,CAACG,MAAM,CAACI,sBAAsB,CAAC,CAAC;IACvC;EACF,CAAC,MAAM,IAAIP,KAAK,CAACG,MAAM,CAACD,MAAM,KAAKA,MAAM,EAAE;IACzCF,KAAK,CAACG,MAAM,CAACD,MAAM,GAAGA,MAAM;IAC5BF,KAAK,CAACG,MAAM,CAACI,sBAAsB,CAAC,CAAC;EACvC;EACAN,SAAS,GAAGD,KAAK,CAACQ,EAAE,CAACP,SAAS;EAC9BD,KAAK,CAACQ,EAAE,CAACP,SAAS,GAAG,KAAK;EAC1BD,KAAK,CAACQ,EAAE,CAACC,WAAW,CAACnB,IAAI,EAAEE,MAAM,EAAEE,KAAK,EAAEC,MAAM,CAAC;EACjDK,KAAK,CAACQ,EAAE,CAACE,UAAU,CAACpB,IAAI,EAAEE,MAAM,EAAEE,KAAK,EAAEC,MAAM,CAAC;EAChDK,KAAK,CAACQ,EAAE,CAACG,cAAc,CAAC,IAAI,CAAC;EAC7B,OAAOV,SAAS;AAClB;AACA,SAASW,aAAaA,CAACZ,KAAK,EAAEC,SAAS,EAAE;EACvC;EACAD,KAAK,CAACQ,EAAE,CAACG,cAAc,CAAC,KAAK,CAAC;EAC9BX,KAAK,CAACQ,EAAE,CAACP,SAAS,GAAGA,SAAS;AAChC;AACA,SAASY,KAAKA,CAACb,KAAK,EAAE;EACpBA,KAAK,CAACQ,EAAE,CAACM,aAAa,CAAChC,GAAG,CAAC;EAC3BkB,KAAK,CAACQ,EAAE,CAACO,aAAa,CAACjC,GAAG,EAAEkB,KAAK,CAACQ,EAAE,CAACQ,aAAa,CAAC,CAAC,CAAC;EACrDhB,KAAK,CAACQ,EAAE,CAACK,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;AAC5B;AACA,SAASI,SAASA,CAAC;EACjBC,OAAO,GAAG,IAAI;EACdhC,UAAU;EACViC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,IAAI;EACJC;AACF,CAAC,EAAE;EACD,MAAMC,SAAS,GAAGjD,QAAQ,CAAC,CAAC;EAC5B,MAAM,CAACoB,WAAW,EAAE8B,YAAY,CAAC,GAAGrD,KAAK,CAACsD,QAAQ,CAAC,KAAK,CAAC;EACzD,IAAIC,UAAU,GAAG,CAAC;EAClBlD,QAAQ,CAACsB,KAAK,IAAI;IAChB,IAAIsB,MAAM,KAAKO,QAAQ,IAAID,UAAU,IAAIN,MAAM,EAAE;MAC/C,IAAIQ,cAAc;MAClB,IAAIN,KAAK,EAAED,IAAI,CAACQ,OAAO,GAAG,CAACD,cAAc,GAAGN,KAAK,CAACO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACE,qBAAqB,CAAC,CAAC;MACpHJ,UAAU,EAAE;IACd;IACA,IAAIL,IAAI,CAACQ,OAAO,EAAE;MAChB,MAAM;QACJjC,QAAQ;QACRF,WAAW,EAAEqC;MACf,CAAC,GAAGhD,wBAAwB,CAACC,UAAU,EAAEqC,IAAI,CAACQ,OAAO,CAAC;MACtD,IAAInC,WAAW,KAAKqC,YAAY,EAAEP,YAAY,CAACO,YAAY,CAAC;MAC5D,IAAIf,OAAO,IAAI,CAACtB,WAAW,IAAI2B,IAAI,CAACQ,OAAO,EAAE;QAC3C,MAAM9B,SAAS,GAAGF,cAAc,CAACC,KAAK,EAAEF,QAAQ,CAAC;QACjD;QACAE,KAAK,CAACQ,EAAE,CAAC0B,MAAM,CAACb,QAAQ,GAAGrB,KAAK,CAACmB,KAAK,GAAGA,KAAK,EAAEnB,KAAK,CAACG,MAAM,CAAC;QAC7DS,aAAa,CAACZ,KAAK,EAAEC,SAAS,CAAC;MACjC;IACF;EACF,CAAC,EAAEmB,KAAK,CAAC;EACT/C,KAAK,CAAC8D,eAAe,CAAC,MAAM;IAC1B,MAAMC,OAAO,GAAGb,IAAI,CAACQ,OAAO;IAC5B,IAAIK,OAAO,KAAK,CAAClB,OAAO,IAAI,CAACtB,WAAW,CAAC,EAAE;MACzC;MACA,MAAM;QACJE;MACF,CAAC,GAAGb,wBAAwB,CAACC,UAAU,EAAEkD,OAAO,CAAC;MACjD,MAAMnC,SAAS,GAAGF,cAAc,CAAC0B,SAAS,EAAE3B,QAAQ,CAAC;MACrDe,KAAK,CAACY,SAAS,CAAC;MAChBb,aAAa,CAACa,SAAS,EAAExB,SAAS,CAAC;IACrC;EACF,CAAC,EAAE,CAACiB,OAAO,EAAEtB,WAAW,CAAC,CAAC;EAC1BvB,KAAK,CAACgE,SAAS,CAAC,MAAM;IACpB,IAAI,CAACb,KAAK,EAAE;IACZ,MAAMY,OAAO,GAAGb,IAAI,CAACQ,OAAO;IAC5B;IACA,MAAMO,GAAG,GAAGb,SAAS,CAACc,GAAG,CAAC,CAAC,CAACC,MAAM,CAACC,SAAS;IAC5ChB,SAAS,CAACiB,SAAS,CAAC;MAClBD,SAAS,EAAEjB,KAAK,CAACO;IACnB,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIK,OAAO,EAAE;QACX,MAAM;UACJtC;QACF,CAAC,GAAGb,wBAAwB,CAACC,UAAU,EAAEkD,OAAO,CAAC;QACjD,MAAMnC,SAAS,GAAGF,cAAc,CAAC0B,SAAS,EAAE3B,QAAQ,CAAC;QACrDe,KAAK,CAACY,SAAS,CAAC;QAChBb,aAAa,CAACa,SAAS,EAAExB,SAAS,CAAC;MACrC;MACAwB,SAAS,CAACiB,SAAS,CAAC;QAClBD,SAAS,EAAEH;MACb,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACd,KAAK,CAAC,CAAC;EACX,OAAO,aAAanD,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAEvB,QAAQ,EAAE,aAAahD,KAAK,CAACsE,aAAa,CAAC,OAAO,EAAE;IAChHE,aAAa,EAAEA,CAAA,KAAM;EACvB,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,eAAezE,KAAK,CAAC0E,UAAU,CAAC,CAAC;EAClDvB,KAAK;EACLN,OAAO,GAAG,IAAI;EACdE,KAAK,GAAG,CAAC;EACT4B,EAAE;EACFC,KAAK;EACLC,SAAS;EACT5B,MAAM,GAAGO,QAAQ;EACjBR,QAAQ;EACR,GAAG8B;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,IAAIC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc;EACjE,MAAMjC,IAAI,GAAGlD,KAAK,CAACoF,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM;IACJC,IAAI;IACJvC;EACF,CAAC,GAAG3C,QAAQ,CAAC,CAAC;EACd,MAAM,CAACmF,YAAY,CAAC,GAAGtF,KAAK,CAACsD,QAAQ,CAAC,MAAM,IAAIrD,KAAK,CAACsF,KAAK,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGzF,KAAK,CAAC0F,UAAU,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC;EAC3D,MAAMC,OAAO,GAAG3F,KAAK,CAAC4F,WAAW,CAAC,CAACC,KAAK,EAAElE,KAAK,KAAK;IAClD,IAAIuB,IAAI,CAACQ,OAAO,IAAIP,KAAK,IAAIA,KAAK,CAACO,OAAO,IAAImC,KAAK,CAACC,MAAM,KAAK3C,KAAK,CAACO,OAAO,EAAE;MAC5E,MAAM;QACJrC,KAAK;QACLC,MAAM;QACNL,IAAI;QACJD;MACF,CAAC,GAAGkC,IAAI,CAACQ,OAAO;MAChB,MAAMqC,CAAC,GAAGF,KAAK,CAACG,OAAO,GAAG/E,IAAI;MAC9B,MAAMgF,CAAC,GAAGJ,KAAK,CAACK,OAAO,GAAGlF,GAAG;MAC7BW,KAAK,CAACwE,OAAO,CAACC,GAAG,CAACL,CAAC,GAAG1E,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE4E,CAAC,GAAG3E,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC3DK,KAAK,CAAC0E,SAAS,CAACC,aAAa,CAAC3E,KAAK,CAACwE,OAAO,EAAExE,KAAK,CAACG,MAAM,CAAC;IAC5D;EACF,CAAC,EAAE,CAACoB,IAAI,EAAEC,KAAK,CAAC,CAAC;EACjBnD,KAAK,CAACgE,SAAS,CAAC,MAAM;IACpB,IAAIuC,eAAe;IACnB;IACA,IAAIpD,KAAK,EAAED,IAAI,CAACQ,OAAO,GAAG,CAAC6C,eAAe,GAAGpD,KAAK,CAACO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,eAAe,CAAC5C,qBAAqB,CAAC,CAAC;IACtH;IACA8B,MAAM,CAAC,CAAC;EACV,CAAC,EAAE,CAACtC,KAAK,CAAC,CAAC;EACX,OAAO,aAAanD,KAAK,CAACsE,aAAa,CAAC,OAAO,EAAEvE,QAAQ,CAAC;IACxDyG,GAAG,EAAEzB;EACP,CAAC,EAAED,KAAK,CAAC,EAAEU,KAAK,IAAIpF,YAAY,CAAC,aAAaJ,KAAK,CAACsE,aAAa,CAAC1B,SAAS,EAAE;IAC3EC,OAAO,EAAEA,OAAO;IAChBhC,UAAU,EAAEwE,IAAI;IAChBpC,MAAM,EAAEA,MAAM;IACdH,KAAK,EAAEA,KAAK;IACZK,KAAK,EAAEA,KAAK;IACZD,IAAI,EAAEA,IAAI;IACVH,KAAK,EAAEA;EACT,CAAC,EAAEC,QAAQ,CAAC,EAAEsC,YAAY,EAAE;IAC1BnB,MAAM,EAAE;MACNwB,OAAO;MACPc,QAAQ,EAAE1D;IACZ,CAAC;IACDsC,IAAI,EAAE;MACJhE,KAAK,EAAE,CAAC2D,aAAa,GAAG9B,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,aAAa,CAAC3D,KAAK;MAC5EC,MAAM,EAAE,CAAC2D,cAAc,GAAG/B,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuB,cAAc,CAAC3D,MAAM;MAChF;MACAN,GAAG,EAAE,CAACkE,cAAc,GAAGhC,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwB,cAAc,CAAClE,GAAG;MAC1E;MACAC,IAAI,EAAE,CAACkE,cAAc,GAAGjC,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,cAAc,CAAClE;IAC1E;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMyF,QAAQ,GAAG,eAAe1G,KAAK,CAAC0E,UAAU,CAAC,CAAC;EAChDiC,EAAE,EAAEC,EAAE,GAAG,KAAK;EACdjC,EAAE;EACF9B,OAAO;EACPgC,SAAS;EACTD,KAAK;EACL7B,KAAK,GAAG,CAAC;EACTI,KAAK;EACLF,MAAM,GAAGO,QAAQ;EACjBR,QAAQ;EACR,GAAG8B;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAM8B,IAAI,GAAG7G,KAAK,CAAC8G,KAAK,CAAC,CAAC;EAC1B,MAAMN,GAAG,GAAGxG,KAAK,CAACoF,MAAM,CAAC,IAAI,CAAC;EAC9BpF,KAAK,CAAC+G,mBAAmB,CAAChC,IAAI,EAAE,MAAMyB,GAAG,CAAC9C,OAAO,CAAC;EAClD,OAAO,aAAa1D,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAE,aAAavE,KAAK,CAACsE,aAAa,CAACsC,EAAE,EAAE7G,QAAQ,CAAC;IAC1GyG,GAAG,EAAEA,GAAG;IACR7B,EAAE,EAAEA,EAAE;IACNE,SAAS,EAAEA,SAAS;IACpBD,KAAK,EAAEA;EACT,CAAC,EAAEE,KAAK,CAAC,CAAC,EAAE,aAAa9E,KAAK,CAACsE,aAAa,CAAC3D,OAAO,CAACqG,EAAE,EAAE,IAAI,EAAE,aAAahH,KAAK,CAACsE,aAAa,CAACG,UAAU,EAAE;IAC1G5B,OAAO,EAAEA,OAAO;IAChBoE,GAAG,EAAEJ,IAAI;IACT1D,KAAK,EAAEqD,GAAG;IACVvD,MAAM,EAAEA,MAAM;IACdF,KAAK,EAAEA;EACT,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AACF,MAAMkE,IAAI,GAAG,eAAe,CAAC,MAAM;EACjC,MAAMC,KAAK,GAAG,aAAanH,KAAK,CAAC0E,UAAU,CAAC,CAACI,KAAK,EAAEC,IAAI,KAAK;IAC3D;IACA,MAAMqC,KAAK,GAAGpH,KAAK,CAACqH,UAAU,CAACnH,OAAO,CAAC;IACvC;IACA,IAAI,CAACkH,KAAK,EAAE,OAAO,aAAapH,KAAK,CAACsE,aAAa,CAACoC,QAAQ,EAAE3G,QAAQ,CAAC;MACrEyG,GAAG,EAAEzB;IACP,CAAC,EAAED,KAAK,CAAC,CAAC;IACV;IAAA,KACK,OAAO,aAAa9E,KAAK,CAACsE,aAAa,CAACG,UAAU,EAAE1E,QAAQ,CAAC;MAChEyG,GAAG,EAAEzB;IACP,CAAC,EAAED,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;EACFqC,KAAK,CAACG,IAAI,GAAG,MAAM,aAAatH,KAAK,CAACsE,aAAa,CAAC3D,OAAO,CAAC4G,GAAG,EAAE,IAAI,CAAC;EACtE,OAAOJ,KAAK;AACd,CAAC,EAAE,CAAC;AAEJ,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}