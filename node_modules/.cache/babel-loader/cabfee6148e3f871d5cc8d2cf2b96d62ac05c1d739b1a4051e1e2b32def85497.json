{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventDispatcher {\n  constructor() {\n    // not defined in @types/three\n    __publicField(this, \"_listeners\");\n  }\n  /**\n   * Adds a listener to an event type.\n   * @param type The type of event to listen to.\n   * @param listener The function that gets called when the event is fired.\n   */\n  addEventListener(type, listener) {\n    if (this._listeners === void 0) this._listeners = {};\n    const listeners = this._listeners;\n    if (listeners[type] === void 0) {\n      listeners[type] = [];\n    }\n    if (listeners[type].indexOf(listener) === -1) {\n      listeners[type].push(listener);\n    }\n  }\n  /**\n      * Checks if listener is added to an event type.\n      * @param type The type of event to listen to.\n      * @param listener The function that gets called when the event is fired.\n      */\n  hasEventListener(type, listener) {\n    if (this._listeners === void 0) return false;\n    const listeners = this._listeners;\n    return listeners[type] !== void 0 && listeners[type].indexOf(listener) !== -1;\n  }\n  /**\n      * Removes a listener from an event type.\n      * @param type The type of the listener that gets removed.\n      * @param listener The listener function that gets removed.\n      */\n  removeEventListener(type, listener) {\n    if (this._listeners === void 0) return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[type];\n    if (listenerArray !== void 0) {\n      const index = listenerArray.indexOf(listener);\n      if (index !== -1) {\n        listenerArray.splice(index, 1);\n      }\n    }\n  }\n  /**\n      * Fire an event type.\n      * @param event The event that gets fired.\n      */\n  dispatchEvent(event) {\n    if (this._listeners === void 0) return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[event.type];\n    if (listenerArray !== void 0) {\n      event.target = this;\n      const array = listenerArray.slice(0);\n      for (let i = 0, l = array.length; i < l; i++) {\n        array[i].call(this, event);\n      }\n      event.target = null;\n    }\n  }\n}\nexport { EventDispatcher };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "__publicField", "addEventListener", "type", "listener", "_listeners", "listeners", "indexOf", "push", "hasEventListener", "removeEventListener", "listenerArray", "index", "splice", "dispatchEvent", "event", "target", "array", "slice", "i", "l", "length", "call"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "mappings": ";;;;;;;;;;;AA6BO,MAAMA,eAAA,CAA2C;EAAjDC,YAAA;IAEK;IAAAC,aAAA;EAAA;EAAA;AAAA;AAAA;AAAA;AAAA;EAOXC,iBACOC,IAAA,EACAC,QAAA,EACI;IAEV,IAAK,KAAKC,UAAA,KAAe,QAAY,KAAKA,UAAA,GAAa;IAEvD,MAAMC,SAAA,GAAY,KAAKD,UAAA;IAElB,IAAAC,SAAA,CAAWH,IAAK,MAAM,QAAY;MAE3BG,SAAA,CAAAH,IAAK,IAAI;IAErB;IAEA,IAAKG,SAAA,CAAWH,IAAK,EAAEI,OAAA,CAASH,QAAS,MAAM,IAAM;MAEzCE,SAAA,CAAAH,IAAK,EAAEK,IAAA,CAAMJ,QAAS;IAElC;EAED;EAAA;AAAA;AAAA;AAAA;AAAA;EAOGK,iBACIN,IAAA,EACAC,QAAA,EACO;IAEb,IAAK,KAAKC,UAAA,KAAe,QAAmB;IAE5C,MAAMC,SAAA,GAAY,KAAKD,UAAA;IAEhB,OAAAC,SAAA,CAAWH,IAAK,MAAM,UAAaG,SAAA,CAAWH,IAAK,EAAEI,OAAA,CAASH,QAAS,MAAM;EAErF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOGM,oBACIP,IAAA,EACAC,QAAA,EACI;IAEV,IAAK,KAAKC,UAAA,KAAe,QAAY;IAErC,MAAMC,SAAA,GAAY,KAAKD,UAAA;IACjB,MAAAM,aAAA,GAAgBL,SAAA,CAAWH,IAAK;IAEtC,IAAKQ,aAAA,KAAkB,QAAY;MAE5B,MAAAC,KAAA,GAAQD,aAAA,CAAcJ,OAAA,CAASH,QAAS;MAE9C,IAAKQ,KAAA,KAAU,IAAM;QAEND,aAAA,CAAAE,MAAA,CAAQD,KAAA,EAAO,CAAE;MAEhC;IAED;EAED;EAAA;AAAA;AAAA;AAAA;EAMGE,cAA0DC,KAAA,EAA0C;IAEtG,IAAK,KAAKV,UAAA,KAAe,QAAY;IAErC,MAAMC,SAAA,GAAY,KAAKD,UAAA;IACjB,MAAAM,aAAA,GAAgBL,SAAA,CAAWS,KAAA,CAAMZ,IAAK;IAE5C,IAAKQ,aAAA,KAAkB,QAAY;MAElCI,KAAA,CAAMC,MAAA,GAAS;MAGT,MAAAC,KAAA,GAAQN,aAAA,CAAcO,KAAA,CAAO,CAAE;MAErC,SAAUC,CAAA,GAAI,GAAGC,CAAA,GAAIH,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAO;QAEhDF,KAAA,CAAOE,CAAE,EAAEG,IAAA,CAAM,MAAMP,KAAM;MAE9B;MAEAA,KAAA,CAAMC,MAAA,GAAS;IAEhB;EAED;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}