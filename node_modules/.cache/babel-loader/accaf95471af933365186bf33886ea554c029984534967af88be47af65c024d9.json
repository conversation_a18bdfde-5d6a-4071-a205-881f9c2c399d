{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\nconst initialModelPosition = /* @__PURE__ */new THREE.Vector3();\nconst mousePosition2D = /* @__PURE__ */new THREE.Vector2();\nconst mousePosition3D = /* @__PURE__ */new THREE.Vector3();\nconst dragOffset = /* @__PURE__ */new THREE.Vector3();\nconst dragPlaneNormal = /* @__PURE__ */new THREE.Vector3();\nconst dragPlane = /* @__PURE__ */new THREE.Plane();\nconst DragControls = /*#__PURE__*/React.forwardRef(({\n  autoTransform = true,\n  matrix,\n  axisLock,\n  dragLimits,\n  onHover,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  children,\n  dragConfig,\n  ...props\n}, fRef) => {\n  const defaultControls = useThree(state => state.controls);\n  const {\n    camera,\n    size,\n    raycaster,\n    invalidate\n  } = useThree();\n  const ref = React.useRef(null);\n  const bind = useGesture({\n    onHover: ({\n      hovering\n    }) => onHover && onHover(hovering !== null && hovering !== void 0 ? hovering : false),\n    onDragStart: ({\n      event\n    }) => {\n      if (defaultControls) defaultControls.enabled = false;\n      const {\n        point\n      } = event;\n      ref.current.matrix.decompose(initialModelPosition, new THREE.Quaternion(), new THREE.Vector3());\n      mousePosition3D.copy(point);\n      dragOffset.copy(mousePosition3D).sub(initialModelPosition);\n      onDragStart && onDragStart(initialModelPosition);\n      invalidate();\n    },\n    onDrag: ({\n      xy: [dragX, dragY],\n      intentional\n    }) => {\n      if (!intentional) return;\n      const normalizedMouseX = (dragX - size.left) / size.width * 2 - 1;\n      const normalizedMouseY = -((dragY - size.top) / size.height) * 2 + 1;\n      mousePosition2D.set(normalizedMouseX, normalizedMouseY);\n      raycaster.setFromCamera(mousePosition2D, camera);\n      if (!axisLock) {\n        camera.getWorldDirection(dragPlaneNormal).negate();\n      } else {\n        switch (axisLock) {\n          case 'x':\n            dragPlaneNormal.set(1, 0, 0);\n            break;\n          case 'y':\n            dragPlaneNormal.set(0, 1, 0);\n            break;\n          case 'z':\n            dragPlaneNormal.set(0, 0, 1);\n            break;\n        }\n      }\n      dragPlane.setFromNormalAndCoplanarPoint(dragPlaneNormal, mousePosition3D);\n      raycaster.ray.intersectPlane(dragPlane, mousePosition3D);\n      const previousLocalMatrix = ref.current.matrix.clone();\n      const previousWorldMatrix = ref.current.matrixWorld.clone();\n      const intendedNewPosition = new THREE.Vector3(mousePosition3D.x - dragOffset.x, mousePosition3D.y - dragOffset.y, mousePosition3D.z - dragOffset.z);\n      if (dragLimits) {\n        intendedNewPosition.x = dragLimits[0] ? Math.max(Math.min(intendedNewPosition.x, dragLimits[0][1]), dragLimits[0][0]) : intendedNewPosition.x;\n        intendedNewPosition.y = dragLimits[1] ? Math.max(Math.min(intendedNewPosition.y, dragLimits[1][1]), dragLimits[1][0]) : intendedNewPosition.y;\n        intendedNewPosition.z = dragLimits[2] ? Math.max(Math.min(intendedNewPosition.z, dragLimits[2][1]), dragLimits[2][0]) : intendedNewPosition.z;\n      }\n      if (autoTransform) {\n        ref.current.matrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = ref.current.matrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = ref.current.matrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(ref.current.matrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      } else {\n        const tempMatrix = new THREE.Matrix4().copy(ref.current.matrix);\n        tempMatrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = tempMatrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = tempMatrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(tempMatrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      }\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (defaultControls) defaultControls.enabled = true;\n      onDragEnd && onDragEnd();\n      invalidate();\n    }\n  }, {\n    drag: {\n      filterTaps: true,\n      threshold: 1,\n      ...(typeof dragConfig === 'object' ? dragConfig : {})\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    if (!matrix) return;\n\n    // If the matrix is a real matrix4 it means that the user wants to control the gizmo\n    // In that case it should just be set, as a bare prop update would merely copy it\n    ref.current.matrix = matrix;\n  }, [matrix]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind(), {\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), children);\n});\nexport { DragControls };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "useThree", "useGesture", "initialModelPosition", "Vector3", "mousePosition2D", "Vector2", "mousePosition3D", "dragOffset", "dragPlaneNormal", "dragPlane", "Plane", "DragControls", "forwardRef", "autoTransform", "matrix", "axisLock", "dragLimits", "onHover", "onDragStart", "onDrag", "onDragEnd", "children", "dragConfig", "props", "fRef", "defaultControls", "state", "controls", "camera", "size", "raycaster", "invalidate", "ref", "useRef", "bind", "hovering", "event", "enabled", "point", "current", "decompose", "Quaternion", "copy", "sub", "xy", "dragX", "dragY", "intentional", "normalizedMouseX", "left", "width", "normalizedMouseY", "top", "height", "set", "setFromCamera", "getWorldDirection", "negate", "setFromNormalAndCoplanarPoint", "ray", "intersectPlane", "previousLocalMatrix", "clone", "previousWorldMatrix", "matrixWorld", "intendedNewPosition", "x", "y", "z", "Math", "max", "min", "setPosition", "deltaLocalMatrix", "multiply", "invert", "deltaWorldMatrix", "tempMatrix", "Matrix4", "drag", "filterTaps", "threshold", "useImperativeHandle", "useLayoutEffect", "createElement", "matrixAutoUpdate"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/DragControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\n\nconst initialModelPosition = /* @__PURE__ */new THREE.Vector3();\nconst mousePosition2D = /* @__PURE__ */new THREE.Vector2();\nconst mousePosition3D = /* @__PURE__ */new THREE.Vector3();\nconst dragOffset = /* @__PURE__ */new THREE.Vector3();\nconst dragPlaneNormal = /* @__PURE__ */new THREE.Vector3();\nconst dragPlane = /* @__PURE__ */new THREE.Plane();\nconst DragControls = /*#__PURE__*/React.forwardRef(({\n  autoTransform = true,\n  matrix,\n  axisLock,\n  dragLimits,\n  onHover,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  children,\n  dragConfig,\n  ...props\n}, fRef) => {\n  const defaultControls = useThree(state => state.controls);\n  const {\n    camera,\n    size,\n    raycaster,\n    invalidate\n  } = useThree();\n  const ref = React.useRef(null);\n  const bind = useGesture({\n    onHover: ({\n      hovering\n    }) => onHover && onHover(hovering !== null && hovering !== void 0 ? hovering : false),\n    onDragStart: ({\n      event\n    }) => {\n      if (defaultControls) defaultControls.enabled = false;\n      const {\n        point\n      } = event;\n      ref.current.matrix.decompose(initialModelPosition, new THREE.Quaternion(), new THREE.Vector3());\n      mousePosition3D.copy(point);\n      dragOffset.copy(mousePosition3D).sub(initialModelPosition);\n      onDragStart && onDragStart(initialModelPosition);\n      invalidate();\n    },\n    onDrag: ({\n      xy: [dragX, dragY],\n      intentional\n    }) => {\n      if (!intentional) return;\n      const normalizedMouseX = (dragX - size.left) / size.width * 2 - 1;\n      const normalizedMouseY = -((dragY - size.top) / size.height) * 2 + 1;\n      mousePosition2D.set(normalizedMouseX, normalizedMouseY);\n      raycaster.setFromCamera(mousePosition2D, camera);\n      if (!axisLock) {\n        camera.getWorldDirection(dragPlaneNormal).negate();\n      } else {\n        switch (axisLock) {\n          case 'x':\n            dragPlaneNormal.set(1, 0, 0);\n            break;\n          case 'y':\n            dragPlaneNormal.set(0, 1, 0);\n            break;\n          case 'z':\n            dragPlaneNormal.set(0, 0, 1);\n            break;\n        }\n      }\n      dragPlane.setFromNormalAndCoplanarPoint(dragPlaneNormal, mousePosition3D);\n      raycaster.ray.intersectPlane(dragPlane, mousePosition3D);\n      const previousLocalMatrix = ref.current.matrix.clone();\n      const previousWorldMatrix = ref.current.matrixWorld.clone();\n      const intendedNewPosition = new THREE.Vector3(mousePosition3D.x - dragOffset.x, mousePosition3D.y - dragOffset.y, mousePosition3D.z - dragOffset.z);\n      if (dragLimits) {\n        intendedNewPosition.x = dragLimits[0] ? Math.max(Math.min(intendedNewPosition.x, dragLimits[0][1]), dragLimits[0][0]) : intendedNewPosition.x;\n        intendedNewPosition.y = dragLimits[1] ? Math.max(Math.min(intendedNewPosition.y, dragLimits[1][1]), dragLimits[1][0]) : intendedNewPosition.y;\n        intendedNewPosition.z = dragLimits[2] ? Math.max(Math.min(intendedNewPosition.z, dragLimits[2][1]), dragLimits[2][0]) : intendedNewPosition.z;\n      }\n      if (autoTransform) {\n        ref.current.matrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = ref.current.matrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = ref.current.matrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(ref.current.matrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      } else {\n        const tempMatrix = new THREE.Matrix4().copy(ref.current.matrix);\n        tempMatrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = tempMatrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = tempMatrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(tempMatrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      }\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (defaultControls) defaultControls.enabled = true;\n      onDragEnd && onDragEnd();\n      invalidate();\n    }\n  }, {\n    drag: {\n      filterTaps: true,\n      threshold: 1,\n      ...(typeof dragConfig === 'object' ? dragConfig : {})\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    if (!matrix) return;\n\n    // If the matrix is a real matrix4 it means that the user wants to control the gizmo\n    // In that case it should just be set, as a bare prop update would merely copy it\n    ref.current.matrix = matrix;\n  }, [matrix]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind(), {\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), children);\n});\n\nexport { DragControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,UAAU,QAAQ,oBAAoB;AAE/C,MAAMC,oBAAoB,GAAG,eAAe,IAAIH,KAAK,CAACI,OAAO,CAAC,CAAC;AAC/D,MAAMC,eAAe,GAAG,eAAe,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC;AAC1D,MAAMC,eAAe,GAAG,eAAe,IAAIP,KAAK,CAACI,OAAO,CAAC,CAAC;AAC1D,MAAMI,UAAU,GAAG,eAAe,IAAIR,KAAK,CAACI,OAAO,CAAC,CAAC;AACrD,MAAMK,eAAe,GAAG,eAAe,IAAIT,KAAK,CAACI,OAAO,CAAC,CAAC;AAC1D,MAAMM,SAAS,GAAG,eAAe,IAAIV,KAAK,CAACW,KAAK,CAAC,CAAC;AAClD,MAAMC,YAAY,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAAC;EAClDC,aAAa,GAAG,IAAI;EACpBC,MAAM;EACNC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPC,WAAW;EACXC,MAAM;EACNC,SAAS;EACTC,QAAQ;EACRC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,eAAe,GAAGzB,QAAQ,CAAC0B,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACzD,MAAM;IACJC,MAAM;IACNC,IAAI;IACJC,SAAS;IACTC;EACF,CAAC,GAAG/B,QAAQ,CAAC,CAAC;EACd,MAAMgC,GAAG,GAAGlC,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,IAAI,GAAGjC,UAAU,CAAC;IACtBgB,OAAO,EAAEA,CAAC;MACRkB;IACF,CAAC,KAAKlB,OAAO,IAAIA,OAAO,CAACkB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,KAAK,CAAC;IACrFjB,WAAW,EAAEA,CAAC;MACZkB;IACF,CAAC,KAAK;MACJ,IAAIX,eAAe,EAAEA,eAAe,CAACY,OAAO,GAAG,KAAK;MACpD,MAAM;QACJC;MACF,CAAC,GAAGF,KAAK;MACTJ,GAAG,CAACO,OAAO,CAACzB,MAAM,CAAC0B,SAAS,CAACtC,oBAAoB,EAAE,IAAIH,KAAK,CAAC0C,UAAU,CAAC,CAAC,EAAE,IAAI1C,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;MAC/FG,eAAe,CAACoC,IAAI,CAACJ,KAAK,CAAC;MAC3B/B,UAAU,CAACmC,IAAI,CAACpC,eAAe,CAAC,CAACqC,GAAG,CAACzC,oBAAoB,CAAC;MAC1DgB,WAAW,IAAIA,WAAW,CAAChB,oBAAoB,CAAC;MAChD6B,UAAU,CAAC,CAAC;IACd,CAAC;IACDZ,MAAM,EAAEA,CAAC;MACPyB,EAAE,EAAE,CAACC,KAAK,EAAEC,KAAK,CAAC;MAClBC;IACF,CAAC,KAAK;MACJ,IAAI,CAACA,WAAW,EAAE;MAClB,MAAMC,gBAAgB,GAAG,CAACH,KAAK,GAAGhB,IAAI,CAACoB,IAAI,IAAIpB,IAAI,CAACqB,KAAK,GAAG,CAAC,GAAG,CAAC;MACjE,MAAMC,gBAAgB,GAAG,EAAE,CAACL,KAAK,GAAGjB,IAAI,CAACuB,GAAG,IAAIvB,IAAI,CAACwB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;MACpEjD,eAAe,CAACkD,GAAG,CAACN,gBAAgB,EAAEG,gBAAgB,CAAC;MACvDrB,SAAS,CAACyB,aAAa,CAACnD,eAAe,EAAEwB,MAAM,CAAC;MAChD,IAAI,CAACb,QAAQ,EAAE;QACba,MAAM,CAAC4B,iBAAiB,CAAChD,eAAe,CAAC,CAACiD,MAAM,CAAC,CAAC;MACpD,CAAC,MAAM;QACL,QAAQ1C,QAAQ;UACd,KAAK,GAAG;YACNP,eAAe,CAAC8C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B;UACF,KAAK,GAAG;YACN9C,eAAe,CAAC8C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B;UACF,KAAK,GAAG;YACN9C,eAAe,CAAC8C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B;QACJ;MACF;MACA7C,SAAS,CAACiD,6BAA6B,CAAClD,eAAe,EAAEF,eAAe,CAAC;MACzEwB,SAAS,CAAC6B,GAAG,CAACC,cAAc,CAACnD,SAAS,EAAEH,eAAe,CAAC;MACxD,MAAMuD,mBAAmB,GAAG7B,GAAG,CAACO,OAAO,CAACzB,MAAM,CAACgD,KAAK,CAAC,CAAC;MACtD,MAAMC,mBAAmB,GAAG/B,GAAG,CAACO,OAAO,CAACyB,WAAW,CAACF,KAAK,CAAC,CAAC;MAC3D,MAAMG,mBAAmB,GAAG,IAAIlE,KAAK,CAACI,OAAO,CAACG,eAAe,CAAC4D,CAAC,GAAG3D,UAAU,CAAC2D,CAAC,EAAE5D,eAAe,CAAC6D,CAAC,GAAG5D,UAAU,CAAC4D,CAAC,EAAE7D,eAAe,CAAC8D,CAAC,GAAG7D,UAAU,CAAC6D,CAAC,CAAC;MACnJ,IAAIpD,UAAU,EAAE;QACdiD,mBAAmB,CAACC,CAAC,GAAGlD,UAAU,CAAC,CAAC,CAAC,GAAGqD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,mBAAmB,CAACC,CAAC,EAAElD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiD,mBAAmB,CAACC,CAAC;QAC7ID,mBAAmB,CAACE,CAAC,GAAGnD,UAAU,CAAC,CAAC,CAAC,GAAGqD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,mBAAmB,CAACE,CAAC,EAAEnD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiD,mBAAmB,CAACE,CAAC;QAC7IF,mBAAmB,CAACG,CAAC,GAAGpD,UAAU,CAAC,CAAC,CAAC,GAAGqD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,mBAAmB,CAACG,CAAC,EAAEpD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiD,mBAAmB,CAACG,CAAC;MAC/I;MACA,IAAIvD,aAAa,EAAE;QACjBmB,GAAG,CAACO,OAAO,CAACzB,MAAM,CAAC0D,WAAW,CAACP,mBAAmB,CAAC;QACnD,MAAMQ,gBAAgB,GAAGzC,GAAG,CAACO,OAAO,CAACzB,MAAM,CAACgD,KAAK,CAAC,CAAC,CAACY,QAAQ,CAACb,mBAAmB,CAACc,MAAM,CAAC,CAAC,CAAC;QAC1F,MAAMC,gBAAgB,GAAG5C,GAAG,CAACO,OAAO,CAACzB,MAAM,CAACgD,KAAK,CAAC,CAAC,CAACY,QAAQ,CAACX,mBAAmB,CAACY,MAAM,CAAC,CAAC,CAAC;QAC1FxD,MAAM,IAAIA,MAAM,CAACa,GAAG,CAACO,OAAO,CAACzB,MAAM,EAAE2D,gBAAgB,EAAEzC,GAAG,CAACO,OAAO,CAACyB,WAAW,EAAEY,gBAAgB,CAAC;MACnG,CAAC,MAAM;QACL,MAAMC,UAAU,GAAG,IAAI9E,KAAK,CAAC+E,OAAO,CAAC,CAAC,CAACpC,IAAI,CAACV,GAAG,CAACO,OAAO,CAACzB,MAAM,CAAC;QAC/D+D,UAAU,CAACL,WAAW,CAACP,mBAAmB,CAAC;QAC3C,MAAMQ,gBAAgB,GAAGI,UAAU,CAACf,KAAK,CAAC,CAAC,CAACY,QAAQ,CAACb,mBAAmB,CAACc,MAAM,CAAC,CAAC,CAAC;QAClF,MAAMC,gBAAgB,GAAGC,UAAU,CAACf,KAAK,CAAC,CAAC,CAACY,QAAQ,CAACX,mBAAmB,CAACY,MAAM,CAAC,CAAC,CAAC;QAClFxD,MAAM,IAAIA,MAAM,CAAC0D,UAAU,EAAEJ,gBAAgB,EAAEzC,GAAG,CAACO,OAAO,CAACyB,WAAW,EAAEY,gBAAgB,CAAC;MAC3F;MACA7C,UAAU,CAAC,CAAC;IACd,CAAC;IACDX,SAAS,EAAEA,CAAA,KAAM;MACf,IAAIK,eAAe,EAAEA,eAAe,CAACY,OAAO,GAAG,IAAI;MACnDjB,SAAS,IAAIA,SAAS,CAAC,CAAC;MACxBW,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE;IACDgD,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,CAAC;MACZ,IAAI,OAAO3D,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;EACFxB,KAAK,CAACoF,mBAAmB,CAAC1D,IAAI,EAAE,MAAMQ,GAAG,CAACO,OAAO,EAAE,EAAE,CAAC;EACtDzC,KAAK,CAACqF,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACrE,MAAM,EAAE;;IAEb;IACA;IACAkB,GAAG,CAACO,OAAO,CAACzB,MAAM,GAAGA,MAAM;EAC7B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,OAAO,aAAahB,KAAK,CAACsF,aAAa,CAAC,OAAO,EAAEvF,QAAQ,CAAC;IACxDmC,GAAG,EAAEA;EACP,CAAC,EAAEE,IAAI,CAAC,CAAC,EAAE;IACTpB,MAAM,EAAEA,MAAM;IACduE,gBAAgB,EAAE;EACpB,CAAC,EAAE9D,KAAK,CAAC,EAAEF,QAAQ,CAAC;AACtB,CAAC,CAAC;AAEF,SAASV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}