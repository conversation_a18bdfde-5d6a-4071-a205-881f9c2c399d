{"ast": null, "code": "import { Loader, FileLoader, Group, BufferGeometry, Float32BufferAttribute, LineBasicMaterial, Material, PointsMaterial, MeshPhongMaterial, LineSegments, Points, Mesh, Vector3 } from \"three\";\nconst _object_pattern = /^[og]\\s*(.+)?/;\nconst _material_library_pattern = /^mtllib /;\nconst _material_use_pattern = /^usemtl /;\nconst _map_use_pattern = /^usemap /;\nconst _vA = /* @__PURE__ */new Vector3();\nconst _vB = /* @__PURE__ */new Vector3();\nconst _vC = /* @__PURE__ */new Vector3();\nconst _ab = /* @__PURE__ */new Vector3();\nconst _cb = /* @__PURE__ */new Vector3();\nfunction ParserState() {\n  const state = {\n    objects: [],\n    object: {},\n    vertices: [],\n    normals: [],\n    colors: [],\n    uvs: [],\n    materials: {},\n    materialLibraries: [],\n    startObject: function (name, fromDeclaration) {\n      if (this.object && this.object.fromDeclaration === false) {\n        this.object.name = name;\n        this.object.fromDeclaration = fromDeclaration !== false;\n        return;\n      }\n      const previousMaterial = this.object && typeof this.object.currentMaterial === \"function\" ? this.object.currentMaterial() : void 0;\n      if (this.object && typeof this.object._finalize === \"function\") {\n        this.object._finalize(true);\n      }\n      this.object = {\n        name: name || \"\",\n        fromDeclaration: fromDeclaration !== false,\n        geometry: {\n          vertices: [],\n          normals: [],\n          colors: [],\n          uvs: [],\n          hasUVIndices: false\n        },\n        materials: [],\n        smooth: true,\n        startMaterial: function (name2, libraries) {\n          const previous = this._finalize(false);\n          if (previous && (previous.inherited || previous.groupCount <= 0)) {\n            this.materials.splice(previous.index, 1);\n          }\n          const material = {\n            index: this.materials.length,\n            name: name2 || \"\",\n            mtllib: Array.isArray(libraries) && libraries.length > 0 ? libraries[libraries.length - 1] : \"\",\n            smooth: previous !== void 0 ? previous.smooth : this.smooth,\n            groupStart: previous !== void 0 ? previous.groupEnd : 0,\n            groupEnd: -1,\n            groupCount: -1,\n            inherited: false,\n            clone: function (index) {\n              const cloned = {\n                index: typeof index === \"number\" ? index : this.index,\n                name: this.name,\n                mtllib: this.mtllib,\n                smooth: this.smooth,\n                groupStart: 0,\n                groupEnd: -1,\n                groupCount: -1,\n                inherited: false\n              };\n              cloned.clone = this.clone.bind(cloned);\n              return cloned;\n            }\n          };\n          this.materials.push(material);\n          return material;\n        },\n        currentMaterial: function () {\n          if (this.materials.length > 0) {\n            return this.materials[this.materials.length - 1];\n          }\n          return void 0;\n        },\n        _finalize: function (end) {\n          const lastMultiMaterial = this.currentMaterial();\n          if (lastMultiMaterial && lastMultiMaterial.groupEnd === -1) {\n            lastMultiMaterial.groupEnd = this.geometry.vertices.length / 3;\n            lastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart;\n            lastMultiMaterial.inherited = false;\n          }\n          if (end && this.materials.length > 1) {\n            for (let mi = this.materials.length - 1; mi >= 0; mi--) {\n              if (this.materials[mi].groupCount <= 0) {\n                this.materials.splice(mi, 1);\n              }\n            }\n          }\n          if (end && this.materials.length === 0) {\n            this.materials.push({\n              name: \"\",\n              smooth: this.smooth\n            });\n          }\n          return lastMultiMaterial;\n        }\n      };\n      if (previousMaterial && previousMaterial.name && typeof previousMaterial.clone === \"function\") {\n        const declared = previousMaterial.clone(0);\n        declared.inherited = true;\n        this.object.materials.push(declared);\n      }\n      this.objects.push(this.object);\n    },\n    finalize: function () {\n      if (this.object && typeof this.object._finalize === \"function\") {\n        this.object._finalize(true);\n      }\n    },\n    parseVertexIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 3) * 3;\n    },\n    parseNormalIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 3) * 3;\n    },\n    parseUVIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 2) * 2;\n    },\n    addVertex: function (a, b, c) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addVertexPoint: function (a) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n    },\n    addVertexLine: function (a) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n    },\n    addNormal: function (a, b, c) {\n      const src = this.normals;\n      const dst = this.object.geometry.normals;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addFaceNormal: function (a, b, c) {\n      const src = this.vertices;\n      const dst = this.object.geometry.normals;\n      _vA.fromArray(src, a);\n      _vB.fromArray(src, b);\n      _vC.fromArray(src, c);\n      _cb.subVectors(_vC, _vB);\n      _ab.subVectors(_vA, _vB);\n      _cb.cross(_ab);\n      _cb.normalize();\n      dst.push(_cb.x, _cb.y, _cb.z);\n      dst.push(_cb.x, _cb.y, _cb.z);\n      dst.push(_cb.x, _cb.y, _cb.z);\n    },\n    addColor: function (a, b, c) {\n      const src = this.colors;\n      const dst = this.object.geometry.colors;\n      if (src[a] !== void 0) dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      if (src[b] !== void 0) dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      if (src[c] !== void 0) dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addUV: function (a, b, c) {\n      const src = this.uvs;\n      const dst = this.object.geometry.uvs;\n      dst.push(src[a + 0], src[a + 1]);\n      dst.push(src[b + 0], src[b + 1]);\n      dst.push(src[c + 0], src[c + 1]);\n    },\n    addDefaultUV: function () {\n      const dst = this.object.geometry.uvs;\n      dst.push(0, 0);\n      dst.push(0, 0);\n      dst.push(0, 0);\n    },\n    addUVLine: function (a) {\n      const src = this.uvs;\n      const dst = this.object.geometry.uvs;\n      dst.push(src[a + 0], src[a + 1]);\n    },\n    addFace: function (a, b, c, ua, ub, uc, na, nb, nc) {\n      const vLen = this.vertices.length;\n      let ia = this.parseVertexIndex(a, vLen);\n      let ib = this.parseVertexIndex(b, vLen);\n      let ic = this.parseVertexIndex(c, vLen);\n      this.addVertex(ia, ib, ic);\n      this.addColor(ia, ib, ic);\n      if (na !== void 0 && na !== \"\") {\n        const nLen = this.normals.length;\n        ia = this.parseNormalIndex(na, nLen);\n        ib = this.parseNormalIndex(nb, nLen);\n        ic = this.parseNormalIndex(nc, nLen);\n        this.addNormal(ia, ib, ic);\n      } else {\n        this.addFaceNormal(ia, ib, ic);\n      }\n      if (ua !== void 0 && ua !== \"\") {\n        const uvLen = this.uvs.length;\n        ia = this.parseUVIndex(ua, uvLen);\n        ib = this.parseUVIndex(ub, uvLen);\n        ic = this.parseUVIndex(uc, uvLen);\n        this.addUV(ia, ib, ic);\n        this.object.geometry.hasUVIndices = true;\n      } else {\n        this.addDefaultUV();\n      }\n    },\n    addPointGeometry: function (vertices) {\n      this.object.geometry.type = \"Points\";\n      const vLen = this.vertices.length;\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        const index = this.parseVertexIndex(vertices[vi], vLen);\n        this.addVertexPoint(index);\n        this.addColor(index);\n      }\n    },\n    addLineGeometry: function (vertices, uvs) {\n      this.object.geometry.type = \"Line\";\n      const vLen = this.vertices.length;\n      const uvLen = this.uvs.length;\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        this.addVertexLine(this.parseVertexIndex(vertices[vi], vLen));\n      }\n      for (let uvi = 0, l = uvs.length; uvi < l; uvi++) {\n        this.addUVLine(this.parseUVIndex(uvs[uvi], uvLen));\n      }\n    }\n  };\n  state.startObject(\"\", false);\n  return state;\n}\nclass OBJLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.materials = null;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  setMaterials(materials) {\n    this.materials = materials;\n    return this;\n  }\n  parse(text) {\n    const state = new ParserState();\n    if (text.indexOf(\"\\r\\n\") !== -1) {\n      text = text.replace(/\\r\\n/g, \"\\n\");\n    }\n    if (text.indexOf(\"\\\\\\n\") !== -1) {\n      text = text.replace(/\\\\\\n/g, \"\");\n    }\n    const lines = text.split(\"\\n\");\n    let line = \"\",\n      lineFirstChar = \"\";\n    let lineLength = 0;\n    let result = [];\n    const trimLeft = typeof \"\".trimLeft === \"function\";\n    for (let i = 0, l = lines.length; i < l; i++) {\n      line = lines[i];\n      line = trimLeft ? line.trimLeft() : line.trim();\n      lineLength = line.length;\n      if (lineLength === 0) continue;\n      lineFirstChar = line.charAt(0);\n      if (lineFirstChar === \"#\") continue;\n      if (lineFirstChar === \"v\") {\n        const data = line.split(/\\s+/);\n        switch (data[0]) {\n          case \"v\":\n            state.vertices.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]));\n            if (data.length >= 7) {\n              state.colors.push(parseFloat(data[4]), parseFloat(data[5]), parseFloat(data[6]));\n            } else {\n              state.colors.push(void 0, void 0, void 0);\n            }\n            break;\n          case \"vn\":\n            state.normals.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]));\n            break;\n          case \"vt\":\n            state.uvs.push(parseFloat(data[1]), parseFloat(data[2]));\n            break;\n        }\n      } else if (lineFirstChar === \"f\") {\n        const lineData = line.substr(1).trim();\n        const vertexData = lineData.split(/\\s+/);\n        const faceVertices = [];\n        for (let j = 0, jl = vertexData.length; j < jl; j++) {\n          const vertex = vertexData[j];\n          if (vertex.length > 0) {\n            const vertexParts = vertex.split(\"/\");\n            faceVertices.push(vertexParts);\n          }\n        }\n        const v1 = faceVertices[0];\n        for (let j = 1, jl = faceVertices.length - 1; j < jl; j++) {\n          const v2 = faceVertices[j];\n          const v3 = faceVertices[j + 1];\n          state.addFace(v1[0], v2[0], v3[0], v1[1], v2[1], v3[1], v1[2], v2[2], v3[2]);\n        }\n      } else if (lineFirstChar === \"l\") {\n        const lineParts = line.substring(1).trim().split(\" \");\n        let lineVertices = [];\n        const lineUVs = [];\n        if (line.indexOf(\"/\") === -1) {\n          lineVertices = lineParts;\n        } else {\n          for (let li = 0, llen = lineParts.length; li < llen; li++) {\n            const parts = lineParts[li].split(\"/\");\n            if (parts[0] !== \"\") lineVertices.push(parts[0]);\n            if (parts[1] !== \"\") lineUVs.push(parts[1]);\n          }\n        }\n        state.addLineGeometry(lineVertices, lineUVs);\n      } else if (lineFirstChar === \"p\") {\n        const lineData = line.substr(1).trim();\n        const pointData = lineData.split(\" \");\n        state.addPointGeometry(pointData);\n      } else if ((result = _object_pattern.exec(line)) !== null) {\n        const name = (\" \" + result[0].substr(1).trim()).substr(1);\n        state.startObject(name);\n      } else if (_material_use_pattern.test(line)) {\n        state.object.startMaterial(line.substring(7).trim(), state.materialLibraries);\n      } else if (_material_library_pattern.test(line)) {\n        state.materialLibraries.push(line.substring(7).trim());\n      } else if (_map_use_pattern.test(line)) {\n        console.warn('THREE.OBJLoader: Rendering identifier \"usemap\" not supported. Textures must be defined in MTL files.');\n      } else if (lineFirstChar === \"s\") {\n        result = line.split(\" \");\n        if (result.length > 1) {\n          const value = result[1].trim().toLowerCase();\n          state.object.smooth = value !== \"0\" && value !== \"off\";\n        } else {\n          state.object.smooth = true;\n        }\n        const material = state.object.currentMaterial();\n        if (material) material.smooth = state.object.smooth;\n      } else {\n        if (line === \"\\0\") continue;\n        console.warn('THREE.OBJLoader: Unexpected line: \"' + line + '\"');\n      }\n    }\n    state.finalize();\n    const container = new Group();\n    container.materialLibraries = [].concat(state.materialLibraries);\n    const hasPrimitives = !(state.objects.length === 1 && state.objects[0].geometry.vertices.length === 0);\n    if (hasPrimitives === true) {\n      for (let i = 0, l = state.objects.length; i < l; i++) {\n        const object = state.objects[i];\n        const geometry = object.geometry;\n        const materials = object.materials;\n        const isLine = geometry.type === \"Line\";\n        const isPoints = geometry.type === \"Points\";\n        let hasVertexColors = false;\n        if (geometry.vertices.length === 0) continue;\n        const buffergeometry = new BufferGeometry();\n        buffergeometry.setAttribute(\"position\", new Float32BufferAttribute(geometry.vertices, 3));\n        if (geometry.normals.length > 0) {\n          buffergeometry.setAttribute(\"normal\", new Float32BufferAttribute(geometry.normals, 3));\n        }\n        if (geometry.colors.length > 0) {\n          hasVertexColors = true;\n          buffergeometry.setAttribute(\"color\", new Float32BufferAttribute(geometry.colors, 3));\n        }\n        if (geometry.hasUVIndices === true) {\n          buffergeometry.setAttribute(\"uv\", new Float32BufferAttribute(geometry.uvs, 2));\n        }\n        const createdMaterials = [];\n        for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n          const sourceMaterial = materials[mi];\n          const materialHash = sourceMaterial.name + \"_\" + sourceMaterial.smooth + \"_\" + hasVertexColors;\n          let material = state.materials[materialHash];\n          if (this.materials !== null) {\n            material = this.materials.create(sourceMaterial.name);\n            if (isLine && material && !(material instanceof LineBasicMaterial)) {\n              const materialLine = new LineBasicMaterial();\n              Material.prototype.copy.call(materialLine, material);\n              materialLine.color.copy(material.color);\n              material = materialLine;\n            } else if (isPoints && material && !(material instanceof PointsMaterial)) {\n              const materialPoints = new PointsMaterial({\n                size: 10,\n                sizeAttenuation: false\n              });\n              Material.prototype.copy.call(materialPoints, material);\n              materialPoints.color.copy(material.color);\n              materialPoints.map = material.map;\n              material = materialPoints;\n            }\n          }\n          if (material === void 0) {\n            if (isLine) {\n              material = new LineBasicMaterial();\n            } else if (isPoints) {\n              material = new PointsMaterial({\n                size: 1,\n                sizeAttenuation: false\n              });\n            } else {\n              material = new MeshPhongMaterial();\n            }\n            material.name = sourceMaterial.name;\n            material.flatShading = sourceMaterial.smooth ? false : true;\n            material.vertexColors = hasVertexColors;\n            state.materials[materialHash] = material;\n          }\n          createdMaterials.push(material);\n        }\n        let mesh;\n        if (createdMaterials.length > 1) {\n          for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n            const sourceMaterial = materials[mi];\n            buffergeometry.addGroup(sourceMaterial.groupStart, sourceMaterial.groupCount, mi);\n          }\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials);\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials);\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials);\n          }\n        } else {\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials[0]);\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials[0]);\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials[0]);\n          }\n        }\n        mesh.name = object.name;\n        container.add(mesh);\n      }\n    } else {\n      if (state.vertices.length > 0) {\n        const material = new PointsMaterial({\n          size: 1,\n          sizeAttenuation: false\n        });\n        const buffergeometry = new BufferGeometry();\n        buffergeometry.setAttribute(\"position\", new Float32BufferAttribute(state.vertices, 3));\n        if (state.colors.length > 0 && state.colors[0] !== void 0) {\n          buffergeometry.setAttribute(\"color\", new Float32BufferAttribute(state.colors, 3));\n          material.vertexColors = true;\n        }\n        const points = new Points(buffergeometry, material);\n        container.add(points);\n      }\n    }\n    return container;\n  }\n}\nexport { OBJLoader };", "map": {"version": 3, "names": ["_object_pattern", "_material_library_pattern", "_material_use_pattern", "_map_use_pattern", "_vA", "Vector3", "_vB", "_vC", "_ab", "_cb", "ParserState", "state", "objects", "object", "vertices", "normals", "colors", "uvs", "materials", "materialLibraries", "startObject", "name", "fromDeclaration", "previousMaterial", "currentMaterial", "_finalize", "geometry", "hasUVIndices", "smooth", "startMaterial", "name2", "libraries", "previous", "inherited", "groupCount", "splice", "index", "material", "length", "mtllib", "Array", "isArray", "groupStart", "groupEnd", "clone", "cloned", "bind", "push", "end", "lastMultiMaterial", "mi", "declared", "finalize", "parseVertexIndex", "value", "len", "parseInt", "parseNormalIndex", "parseUVIndex", "addVertex", "a", "b", "c", "src", "dst", "addVertexPoint", "addVertexLine", "addNormal", "addFaceNormal", "fromArray", "subVectors", "cross", "normalize", "x", "y", "z", "addColor", "addUV", "addDefaultUV", "addUVLine", "addFace", "ua", "ub", "uc", "na", "nb", "nc", "vLen", "ia", "ib", "ic", "nLen", "uvLen", "addPointGeometry", "type", "vi", "l", "addLineGeometry", "uvi", "OBJLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "setMaterials", "indexOf", "replace", "lines", "split", "line", "lineFirstChar", "lineLength", "result", "trimLeft", "i", "trim", "char<PERSON>t", "data", "parseFloat", "lineData", "substr", "vertexData", "faceVertices", "j", "jl", "vertex", "vertexParts", "v1", "v2", "v3", "lineParts", "substring", "lineVertices", "lineUVs", "li", "llen", "parts", "pointData", "exec", "test", "warn", "toLowerCase", "container", "Group", "concat", "hasPrimitives", "isLine", "isPoints", "hasVertexColors", "buffergeometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "createdMaterials", "miLen", "sourceMaterial", "materialHash", "create", "LineBasicMaterial", "materialLine", "Material", "prototype", "copy", "call", "color", "PointsMaterial", "materialPoints", "size", "sizeAttenuation", "map", "MeshPhongMaterial", "flatShading", "vertexColors", "mesh", "addGroup", "LineSegments", "Points", "<PERSON><PERSON>", "add", "points"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/OBJLoader.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  Material,\n  Mesh,\n  MeshPhongMaterial,\n  Points,\n  PointsMaterial,\n  Vector3,\n} from 'three'\n\n// o object_name | g group_name\nconst _object_pattern = /^[og]\\s*(.+)?/\n// mtllib file_reference\nconst _material_library_pattern = /^mtllib /\n// usemtl material_name\nconst _material_use_pattern = /^usemtl /\n// usemap map_name\nconst _map_use_pattern = /^usemap /\n\nconst _vA = /* @__PURE__ */ new Vector3()\nconst _vB = /* @__PURE__ */ new Vector3()\nconst _vC = /* @__PURE__ */ new Vector3()\n\nconst _ab = /* @__PURE__ */ new Vector3()\nconst _cb = /* @__PURE__ */ new Vector3()\n\nfunction ParserState() {\n  const state = {\n    objects: [],\n    object: {},\n\n    vertices: [],\n    normals: [],\n    colors: [],\n    uvs: [],\n\n    materials: {},\n    materialLibraries: [],\n\n    startObject: function (name, fromDeclaration) {\n      // If the current object (initial from reset) is not from a g/o declaration in the parsed\n      // file. We need to use it for the first parsed g/o to keep things in sync.\n      if (this.object && this.object.fromDeclaration === false) {\n        this.object.name = name\n        this.object.fromDeclaration = fromDeclaration !== false\n        return\n      }\n\n      const previousMaterial =\n        this.object && typeof this.object.currentMaterial === 'function' ? this.object.currentMaterial() : undefined\n\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true)\n      }\n\n      this.object = {\n        name: name || '',\n        fromDeclaration: fromDeclaration !== false,\n\n        geometry: {\n          vertices: [],\n          normals: [],\n          colors: [],\n          uvs: [],\n          hasUVIndices: false,\n        },\n        materials: [],\n        smooth: true,\n\n        startMaterial: function (name, libraries) {\n          const previous = this._finalize(false)\n\n          // New usemtl declaration overwrites an inherited material, except if faces were declared\n          // after the material, then it must be preserved for proper MultiMaterial continuation.\n          if (previous && (previous.inherited || previous.groupCount <= 0)) {\n            this.materials.splice(previous.index, 1)\n          }\n\n          const material = {\n            index: this.materials.length,\n            name: name || '',\n            mtllib: Array.isArray(libraries) && libraries.length > 0 ? libraries[libraries.length - 1] : '',\n            smooth: previous !== undefined ? previous.smooth : this.smooth,\n            groupStart: previous !== undefined ? previous.groupEnd : 0,\n            groupEnd: -1,\n            groupCount: -1,\n            inherited: false,\n\n            clone: function (index) {\n              const cloned = {\n                index: typeof index === 'number' ? index : this.index,\n                name: this.name,\n                mtllib: this.mtllib,\n                smooth: this.smooth,\n                groupStart: 0,\n                groupEnd: -1,\n                groupCount: -1,\n                inherited: false,\n              }\n              cloned.clone = this.clone.bind(cloned)\n              return cloned\n            },\n          }\n\n          this.materials.push(material)\n\n          return material\n        },\n\n        currentMaterial: function () {\n          if (this.materials.length > 0) {\n            return this.materials[this.materials.length - 1]\n          }\n\n          return undefined\n        },\n\n        _finalize: function (end) {\n          const lastMultiMaterial = this.currentMaterial()\n          if (lastMultiMaterial && lastMultiMaterial.groupEnd === -1) {\n            lastMultiMaterial.groupEnd = this.geometry.vertices.length / 3\n            lastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart\n            lastMultiMaterial.inherited = false\n          }\n\n          // Ignore objects tail materials if no face declarations followed them before a new o/g started.\n          if (end && this.materials.length > 1) {\n            for (let mi = this.materials.length - 1; mi >= 0; mi--) {\n              if (this.materials[mi].groupCount <= 0) {\n                this.materials.splice(mi, 1)\n              }\n            }\n          }\n\n          // Guarantee at least one empty material, this makes the creation later more straight forward.\n          if (end && this.materials.length === 0) {\n            this.materials.push({\n              name: '',\n              smooth: this.smooth,\n            })\n          }\n\n          return lastMultiMaterial\n        },\n      }\n\n      // Inherit previous objects material.\n      // Spec tells us that a declared material must be set to all objects until a new material is declared.\n      // If a usemtl declaration is encountered while this new object is being parsed, it will\n      // overwrite the inherited material. Exception being that there was already face declarations\n      // to the inherited material, then it will be preserved for proper MultiMaterial continuation.\n\n      if (previousMaterial && previousMaterial.name && typeof previousMaterial.clone === 'function') {\n        const declared = previousMaterial.clone(0)\n        declared.inherited = true\n        this.object.materials.push(declared)\n      }\n\n      this.objects.push(this.object)\n    },\n\n    finalize: function () {\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true)\n      }\n    },\n\n    parseVertexIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 3) * 3\n    },\n\n    parseNormalIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 3) * 3\n    },\n\n    parseUVIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 2) * 2\n    },\n\n    addVertex: function (a, b, c) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n      dst.push(src[b + 0], src[b + 1], src[b + 2])\n      dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addVertexPoint: function (a) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n    },\n\n    addVertexLine: function (a) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n    },\n\n    addNormal: function (a, b, c) {\n      const src = this.normals\n      const dst = this.object.geometry.normals\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n      dst.push(src[b + 0], src[b + 1], src[b + 2])\n      dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addFaceNormal: function (a, b, c) {\n      const src = this.vertices\n      const dst = this.object.geometry.normals\n\n      _vA.fromArray(src, a)\n      _vB.fromArray(src, b)\n      _vC.fromArray(src, c)\n\n      _cb.subVectors(_vC, _vB)\n      _ab.subVectors(_vA, _vB)\n      _cb.cross(_ab)\n\n      _cb.normalize()\n\n      dst.push(_cb.x, _cb.y, _cb.z)\n      dst.push(_cb.x, _cb.y, _cb.z)\n      dst.push(_cb.x, _cb.y, _cb.z)\n    },\n\n    addColor: function (a, b, c) {\n      const src = this.colors\n      const dst = this.object.geometry.colors\n\n      if (src[a] !== undefined) dst.push(src[a + 0], src[a + 1], src[a + 2])\n      if (src[b] !== undefined) dst.push(src[b + 0], src[b + 1], src[b + 2])\n      if (src[c] !== undefined) dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addUV: function (a, b, c) {\n      const src = this.uvs\n      const dst = this.object.geometry.uvs\n\n      dst.push(src[a + 0], src[a + 1])\n      dst.push(src[b + 0], src[b + 1])\n      dst.push(src[c + 0], src[c + 1])\n    },\n\n    addDefaultUV: function () {\n      const dst = this.object.geometry.uvs\n\n      dst.push(0, 0)\n      dst.push(0, 0)\n      dst.push(0, 0)\n    },\n\n    addUVLine: function (a) {\n      const src = this.uvs\n      const dst = this.object.geometry.uvs\n\n      dst.push(src[a + 0], src[a + 1])\n    },\n\n    addFace: function (a, b, c, ua, ub, uc, na, nb, nc) {\n      const vLen = this.vertices.length\n\n      let ia = this.parseVertexIndex(a, vLen)\n      let ib = this.parseVertexIndex(b, vLen)\n      let ic = this.parseVertexIndex(c, vLen)\n\n      this.addVertex(ia, ib, ic)\n      this.addColor(ia, ib, ic)\n\n      // normals\n\n      if (na !== undefined && na !== '') {\n        const nLen = this.normals.length\n\n        ia = this.parseNormalIndex(na, nLen)\n        ib = this.parseNormalIndex(nb, nLen)\n        ic = this.parseNormalIndex(nc, nLen)\n\n        this.addNormal(ia, ib, ic)\n      } else {\n        this.addFaceNormal(ia, ib, ic)\n      }\n\n      // uvs\n\n      if (ua !== undefined && ua !== '') {\n        const uvLen = this.uvs.length\n\n        ia = this.parseUVIndex(ua, uvLen)\n        ib = this.parseUVIndex(ub, uvLen)\n        ic = this.parseUVIndex(uc, uvLen)\n\n        this.addUV(ia, ib, ic)\n\n        this.object.geometry.hasUVIndices = true\n      } else {\n        // add placeholder values (for inconsistent face definitions)\n\n        this.addDefaultUV()\n      }\n    },\n\n    addPointGeometry: function (vertices) {\n      this.object.geometry.type = 'Points'\n\n      const vLen = this.vertices.length\n\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        const index = this.parseVertexIndex(vertices[vi], vLen)\n\n        this.addVertexPoint(index)\n        this.addColor(index)\n      }\n    },\n\n    addLineGeometry: function (vertices, uvs) {\n      this.object.geometry.type = 'Line'\n\n      const vLen = this.vertices.length\n      const uvLen = this.uvs.length\n\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        this.addVertexLine(this.parseVertexIndex(vertices[vi], vLen))\n      }\n\n      for (let uvi = 0, l = uvs.length; uvi < l; uvi++) {\n        this.addUVLine(this.parseUVIndex(uvs[uvi], uvLen))\n      }\n    },\n  }\n\n  state.startObject('', false)\n\n  return state\n}\n\n//\n\nclass OBJLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.materials = null\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setMaterials(materials) {\n    this.materials = materials\n\n    return this\n  }\n\n  parse(text) {\n    const state = new ParserState()\n\n    if (text.indexOf('\\r\\n') !== -1) {\n      // This is faster than String.split with regex that splits on both\n      text = text.replace(/\\r\\n/g, '\\n')\n    }\n\n    if (text.indexOf('\\\\\\n') !== -1) {\n      // join lines separated by a line continuation character (\\)\n      text = text.replace(/\\\\\\n/g, '')\n    }\n\n    const lines = text.split('\\n')\n    let line = '',\n      lineFirstChar = ''\n    let lineLength = 0\n    let result = []\n\n    // Faster to just trim left side of the line. Use if available.\n    const trimLeft = typeof ''.trimLeft === 'function'\n\n    for (let i = 0, l = lines.length; i < l; i++) {\n      line = lines[i]\n\n      line = trimLeft ? line.trimLeft() : line.trim()\n\n      lineLength = line.length\n\n      if (lineLength === 0) continue\n\n      lineFirstChar = line.charAt(0)\n\n      // @todo invoke passed in handler if any\n      if (lineFirstChar === '#') continue\n\n      if (lineFirstChar === 'v') {\n        const data = line.split(/\\s+/)\n\n        switch (data[0]) {\n          case 'v':\n            state.vertices.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]))\n            if (data.length >= 7) {\n              state.colors.push(parseFloat(data[4]), parseFloat(data[5]), parseFloat(data[6]))\n            } else {\n              // if no colors are defined, add placeholders so color and vertex indices match\n\n              state.colors.push(undefined, undefined, undefined)\n            }\n\n            break\n          case 'vn':\n            state.normals.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]))\n            break\n          case 'vt':\n            state.uvs.push(parseFloat(data[1]), parseFloat(data[2]))\n            break\n        }\n      } else if (lineFirstChar === 'f') {\n        const lineData = line.substr(1).trim()\n        const vertexData = lineData.split(/\\s+/)\n        const faceVertices = []\n\n        // Parse the face vertex data into an easy to work with format\n\n        for (let j = 0, jl = vertexData.length; j < jl; j++) {\n          const vertex = vertexData[j]\n\n          if (vertex.length > 0) {\n            const vertexParts = vertex.split('/')\n            faceVertices.push(vertexParts)\n          }\n        }\n\n        // Draw an edge between the first vertex and all subsequent vertices to form an n-gon\n\n        const v1 = faceVertices[0]\n\n        for (let j = 1, jl = faceVertices.length - 1; j < jl; j++) {\n          const v2 = faceVertices[j]\n          const v3 = faceVertices[j + 1]\n\n          state.addFace(v1[0], v2[0], v3[0], v1[1], v2[1], v3[1], v1[2], v2[2], v3[2])\n        }\n      } else if (lineFirstChar === 'l') {\n        const lineParts = line.substring(1).trim().split(' ')\n        let lineVertices = []\n        const lineUVs = []\n\n        if (line.indexOf('/') === -1) {\n          lineVertices = lineParts\n        } else {\n          for (let li = 0, llen = lineParts.length; li < llen; li++) {\n            const parts = lineParts[li].split('/')\n\n            if (parts[0] !== '') lineVertices.push(parts[0])\n            if (parts[1] !== '') lineUVs.push(parts[1])\n          }\n        }\n\n        state.addLineGeometry(lineVertices, lineUVs)\n      } else if (lineFirstChar === 'p') {\n        const lineData = line.substr(1).trim()\n        const pointData = lineData.split(' ')\n\n        state.addPointGeometry(pointData)\n      } else if ((result = _object_pattern.exec(line)) !== null) {\n        // o object_name\n        // or\n        // g group_name\n\n        // WORKAROUND: https://bugs.chromium.org/p/v8/issues/detail?id=2869\n        // let name = result[ 0 ].substr( 1 ).trim();\n        const name = (' ' + result[0].substr(1).trim()).substr(1)\n\n        state.startObject(name)\n      } else if (_material_use_pattern.test(line)) {\n        // material\n\n        state.object.startMaterial(line.substring(7).trim(), state.materialLibraries)\n      } else if (_material_library_pattern.test(line)) {\n        // mtl file\n\n        state.materialLibraries.push(line.substring(7).trim())\n      } else if (_map_use_pattern.test(line)) {\n        // the line is parsed but ignored since the loader assumes textures are defined MTL files\n        // (according to https://www.okino.com/conv/imp_wave.htm, 'usemap' is the old-style Wavefront texture reference method)\n\n        console.warn(\n          'THREE.OBJLoader: Rendering identifier \"usemap\" not supported. Textures must be defined in MTL files.',\n        )\n      } else if (lineFirstChar === 's') {\n        result = line.split(' ')\n\n        // smooth shading\n\n        // @todo Handle files that have varying smooth values for a set of faces inside one geometry,\n        // but does not define a usemtl for each face set.\n        // This should be detected and a dummy material created (later MultiMaterial and geometry groups).\n        // This requires some care to not create extra material on each smooth value for \"normal\" obj files.\n        // where explicit usemtl defines geometry groups.\n        // Example asset: examples/models/obj/cerberus/Cerberus.obj\n\n        /*\n         * http://paulbourke.net/dataformats/obj/\n         * or\n         * http://www.cs.utah.edu/~boulos/cs3505/obj_spec.pdf\n         *\n         * From chapter \"Grouping\" Syntax explanation \"s group_number\":\n         * \"group_number is the smoothing group number. To turn off smoothing groups, use a value of 0 or off.\n         * Polygonal elements use group numbers to put elements in different smoothing groups. For free-form\n         * surfaces, smoothing groups are either turned on or off; there is no difference between values greater\n         * than 0.\"\n         */\n        if (result.length > 1) {\n          const value = result[1].trim().toLowerCase()\n          state.object.smooth = value !== '0' && value !== 'off'\n        } else {\n          // ZBrush can produce \"s\" lines #11707\n          state.object.smooth = true\n        }\n\n        const material = state.object.currentMaterial()\n        if (material) material.smooth = state.object.smooth\n      } else {\n        // Handle null terminated files without exception\n        if (line === '\\0') continue\n\n        console.warn('THREE.OBJLoader: Unexpected line: \"' + line + '\"')\n      }\n    }\n\n    state.finalize()\n\n    const container = new Group()\n    container.materialLibraries = [].concat(state.materialLibraries)\n\n    const hasPrimitives = !(state.objects.length === 1 && state.objects[0].geometry.vertices.length === 0)\n\n    if (hasPrimitives === true) {\n      for (let i = 0, l = state.objects.length; i < l; i++) {\n        const object = state.objects[i]\n        const geometry = object.geometry\n        const materials = object.materials\n        const isLine = geometry.type === 'Line'\n        const isPoints = geometry.type === 'Points'\n        let hasVertexColors = false\n\n        // Skip o/g line declarations that did not follow with any faces\n        if (geometry.vertices.length === 0) continue\n\n        const buffergeometry = new BufferGeometry()\n\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(geometry.vertices, 3))\n\n        if (geometry.normals.length > 0) {\n          buffergeometry.setAttribute('normal', new Float32BufferAttribute(geometry.normals, 3))\n        }\n\n        if (geometry.colors.length > 0) {\n          hasVertexColors = true\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(geometry.colors, 3))\n        }\n\n        if (geometry.hasUVIndices === true) {\n          buffergeometry.setAttribute('uv', new Float32BufferAttribute(geometry.uvs, 2))\n        }\n\n        // Create materials\n\n        const createdMaterials = []\n\n        for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n          const sourceMaterial = materials[mi]\n          const materialHash = sourceMaterial.name + '_' + sourceMaterial.smooth + '_' + hasVertexColors\n          let material = state.materials[materialHash]\n\n          if (this.materials !== null) {\n            material = this.materials.create(sourceMaterial.name)\n\n            // mtl etc. loaders probably can't create line materials correctly, copy properties to a line material.\n            if (isLine && material && !(material instanceof LineBasicMaterial)) {\n              const materialLine = new LineBasicMaterial()\n              Material.prototype.copy.call(materialLine, material)\n              materialLine.color.copy(material.color)\n              material = materialLine\n            } else if (isPoints && material && !(material instanceof PointsMaterial)) {\n              const materialPoints = new PointsMaterial({ size: 10, sizeAttenuation: false })\n              Material.prototype.copy.call(materialPoints, material)\n              materialPoints.color.copy(material.color)\n              materialPoints.map = material.map\n              material = materialPoints\n            }\n          }\n\n          if (material === undefined) {\n            if (isLine) {\n              material = new LineBasicMaterial()\n            } else if (isPoints) {\n              material = new PointsMaterial({ size: 1, sizeAttenuation: false })\n            } else {\n              material = new MeshPhongMaterial()\n            }\n\n            material.name = sourceMaterial.name\n            material.flatShading = sourceMaterial.smooth ? false : true\n            material.vertexColors = hasVertexColors\n\n            state.materials[materialHash] = material\n          }\n\n          createdMaterials.push(material)\n        }\n\n        // Create mesh\n\n        let mesh\n\n        if (createdMaterials.length > 1) {\n          for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n            const sourceMaterial = materials[mi]\n            buffergeometry.addGroup(sourceMaterial.groupStart, sourceMaterial.groupCount, mi)\n          }\n\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials)\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials)\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials)\n          }\n        } else {\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials[0])\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials[0])\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials[0])\n          }\n        }\n\n        mesh.name = object.name\n\n        container.add(mesh)\n      }\n    } else {\n      // if there is only the default parser state object with no geometry data, interpret data as point cloud\n\n      if (state.vertices.length > 0) {\n        const material = new PointsMaterial({ size: 1, sizeAttenuation: false })\n\n        const buffergeometry = new BufferGeometry()\n\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(state.vertices, 3))\n\n        if (state.colors.length > 0 && state.colors[0] !== undefined) {\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(state.colors, 3))\n          material.vertexColors = true\n        }\n\n        const points = new Points(buffergeometry, material)\n        container.add(points)\n      }\n    }\n\n    return container\n  }\n}\n\nexport { OBJLoader }\n"], "mappings": ";AAiBA,MAAMA,eAAA,GAAkB;AAExB,MAAMC,yBAAA,GAA4B;AAElC,MAAMC,qBAAA,GAAwB;AAE9B,MAAMC,gBAAA,GAAmB;AAEzB,MAAMC,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,GAAA,GAAsB,mBAAID,OAAA,CAAS;AACzC,MAAME,GAAA,GAAsB,mBAAIF,OAAA,CAAS;AAEzC,MAAMG,GAAA,GAAsB,mBAAIH,OAAA,CAAS;AACzC,MAAMI,GAAA,GAAsB,mBAAIJ,OAAA,CAAS;AAEzC,SAASK,YAAA,EAAc;EACrB,MAAMC,KAAA,GAAQ;IACZC,OAAA,EAAS,EAAE;IACXC,MAAA,EAAQ,CAAE;IAEVC,QAAA,EAAU,EAAE;IACZC,OAAA,EAAS,EAAE;IACXC,MAAA,EAAQ,EAAE;IACVC,GAAA,EAAK,EAAE;IAEPC,SAAA,EAAW,CAAE;IACbC,iBAAA,EAAmB,EAAE;IAErBC,WAAA,EAAa,SAAAA,CAAUC,IAAA,EAAMC,eAAA,EAAiB;MAG5C,IAAI,KAAKT,MAAA,IAAU,KAAKA,MAAA,CAAOS,eAAA,KAAoB,OAAO;QACxD,KAAKT,MAAA,CAAOQ,IAAA,GAAOA,IAAA;QACnB,KAAKR,MAAA,CAAOS,eAAA,GAAkBA,eAAA,KAAoB;QAClD;MACD;MAED,MAAMC,gBAAA,GACJ,KAAKV,MAAA,IAAU,OAAO,KAAKA,MAAA,CAAOW,eAAA,KAAoB,aAAa,KAAKX,MAAA,CAAOW,eAAA,CAAe,IAAK;MAErG,IAAI,KAAKX,MAAA,IAAU,OAAO,KAAKA,MAAA,CAAOY,SAAA,KAAc,YAAY;QAC9D,KAAKZ,MAAA,CAAOY,SAAA,CAAU,IAAI;MAC3B;MAED,KAAKZ,MAAA,GAAS;QACZQ,IAAA,EAAMA,IAAA,IAAQ;QACdC,eAAA,EAAiBA,eAAA,KAAoB;QAErCI,QAAA,EAAU;UACRZ,QAAA,EAAU,EAAE;UACZC,OAAA,EAAS,EAAE;UACXC,MAAA,EAAQ,EAAE;UACVC,GAAA,EAAK,EAAE;UACPU,YAAA,EAAc;QACf;QACDT,SAAA,EAAW,EAAE;QACbU,MAAA,EAAQ;QAERC,aAAA,EAAe,SAAAA,CAAUC,KAAA,EAAMC,SAAA,EAAW;UACxC,MAAMC,QAAA,GAAW,KAAKP,SAAA,CAAU,KAAK;UAIrC,IAAIO,QAAA,KAAaA,QAAA,CAASC,SAAA,IAAaD,QAAA,CAASE,UAAA,IAAc,IAAI;YAChE,KAAKhB,SAAA,CAAUiB,MAAA,CAAOH,QAAA,CAASI,KAAA,EAAO,CAAC;UACxC;UAED,MAAMC,QAAA,GAAW;YACfD,KAAA,EAAO,KAAKlB,SAAA,CAAUoB,MAAA;YACtBjB,IAAA,EAAMS,KAAA,IAAQ;YACdS,MAAA,EAAQC,KAAA,CAAMC,OAAA,CAAQV,SAAS,KAAKA,SAAA,CAAUO,MAAA,GAAS,IAAIP,SAAA,CAAUA,SAAA,CAAUO,MAAA,GAAS,CAAC,IAAI;YAC7FV,MAAA,EAAQI,QAAA,KAAa,SAAYA,QAAA,CAASJ,MAAA,GAAS,KAAKA,MAAA;YACxDc,UAAA,EAAYV,QAAA,KAAa,SAAYA,QAAA,CAASW,QAAA,GAAW;YACzDA,QAAA,EAAU;YACVT,UAAA,EAAY;YACZD,SAAA,EAAW;YAEXW,KAAA,EAAO,SAAAA,CAAUR,KAAA,EAAO;cACtB,MAAMS,MAAA,GAAS;gBACbT,KAAA,EAAO,OAAOA,KAAA,KAAU,WAAWA,KAAA,GAAQ,KAAKA,KAAA;gBAChDf,IAAA,EAAM,KAAKA,IAAA;gBACXkB,MAAA,EAAQ,KAAKA,MAAA;gBACbX,MAAA,EAAQ,KAAKA,MAAA;gBACbc,UAAA,EAAY;gBACZC,QAAA,EAAU;gBACVT,UAAA,EAAY;gBACZD,SAAA,EAAW;cACZ;cACDY,MAAA,CAAOD,KAAA,GAAQ,KAAKA,KAAA,CAAME,IAAA,CAAKD,MAAM;cACrC,OAAOA,MAAA;YACR;UACF;UAED,KAAK3B,SAAA,CAAU6B,IAAA,CAAKV,QAAQ;UAE5B,OAAOA,QAAA;QACR;QAEDb,eAAA,EAAiB,SAAAA,CAAA,EAAY;UAC3B,IAAI,KAAKN,SAAA,CAAUoB,MAAA,GAAS,GAAG;YAC7B,OAAO,KAAKpB,SAAA,CAAU,KAAKA,SAAA,CAAUoB,MAAA,GAAS,CAAC;UAChD;UAED,OAAO;QACR;QAEDb,SAAA,EAAW,SAAAA,CAAUuB,GAAA,EAAK;UACxB,MAAMC,iBAAA,GAAoB,KAAKzB,eAAA,CAAiB;UAChD,IAAIyB,iBAAA,IAAqBA,iBAAA,CAAkBN,QAAA,KAAa,IAAI;YAC1DM,iBAAA,CAAkBN,QAAA,GAAW,KAAKjB,QAAA,CAASZ,QAAA,CAASwB,MAAA,GAAS;YAC7DW,iBAAA,CAAkBf,UAAA,GAAae,iBAAA,CAAkBN,QAAA,GAAWM,iBAAA,CAAkBP,UAAA;YAC9EO,iBAAA,CAAkBhB,SAAA,GAAY;UAC/B;UAGD,IAAIe,GAAA,IAAO,KAAK9B,SAAA,CAAUoB,MAAA,GAAS,GAAG;YACpC,SAASY,EAAA,GAAK,KAAKhC,SAAA,CAAUoB,MAAA,GAAS,GAAGY,EAAA,IAAM,GAAGA,EAAA,IAAM;cACtD,IAAI,KAAKhC,SAAA,CAAUgC,EAAE,EAAEhB,UAAA,IAAc,GAAG;gBACtC,KAAKhB,SAAA,CAAUiB,MAAA,CAAOe,EAAA,EAAI,CAAC;cAC5B;YACF;UACF;UAGD,IAAIF,GAAA,IAAO,KAAK9B,SAAA,CAAUoB,MAAA,KAAW,GAAG;YACtC,KAAKpB,SAAA,CAAU6B,IAAA,CAAK;cAClB1B,IAAA,EAAM;cACNO,MAAA,EAAQ,KAAKA;YAC3B,CAAa;UACF;UAED,OAAOqB,iBAAA;QACR;MACF;MAQD,IAAI1B,gBAAA,IAAoBA,gBAAA,CAAiBF,IAAA,IAAQ,OAAOE,gBAAA,CAAiBqB,KAAA,KAAU,YAAY;QAC7F,MAAMO,QAAA,GAAW5B,gBAAA,CAAiBqB,KAAA,CAAM,CAAC;QACzCO,QAAA,CAASlB,SAAA,GAAY;QACrB,KAAKpB,MAAA,CAAOK,SAAA,CAAU6B,IAAA,CAAKI,QAAQ;MACpC;MAED,KAAKvC,OAAA,CAAQmC,IAAA,CAAK,KAAKlC,MAAM;IAC9B;IAEDuC,QAAA,EAAU,SAAAA,CAAA,EAAY;MACpB,IAAI,KAAKvC,MAAA,IAAU,OAAO,KAAKA,MAAA,CAAOY,SAAA,KAAc,YAAY;QAC9D,KAAKZ,MAAA,CAAOY,SAAA,CAAU,IAAI;MAC3B;IACF;IAED4B,gBAAA,EAAkB,SAAAA,CAAUC,KAAA,EAAOC,GAAA,EAAK;MACtC,MAAMnB,KAAA,GAAQoB,QAAA,CAASF,KAAA,EAAO,EAAE;MAChC,QAAQlB,KAAA,IAAS,IAAIA,KAAA,GAAQ,IAAIA,KAAA,GAAQmB,GAAA,GAAM,KAAK;IACrD;IAEDE,gBAAA,EAAkB,SAAAA,CAAUH,KAAA,EAAOC,GAAA,EAAK;MACtC,MAAMnB,KAAA,GAAQoB,QAAA,CAASF,KAAA,EAAO,EAAE;MAChC,QAAQlB,KAAA,IAAS,IAAIA,KAAA,GAAQ,IAAIA,KAAA,GAAQmB,GAAA,GAAM,KAAK;IACrD;IAEDG,YAAA,EAAc,SAAAA,CAAUJ,KAAA,EAAOC,GAAA,EAAK;MAClC,MAAMnB,KAAA,GAAQoB,QAAA,CAASF,KAAA,EAAO,EAAE;MAChC,QAAQlB,KAAA,IAAS,IAAIA,KAAA,GAAQ,IAAIA,KAAA,GAAQmB,GAAA,GAAM,KAAK;IACrD;IAEDI,SAAA,EAAW,SAAAA,CAAUC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAC5B,MAAMC,GAAA,GAAM,KAAKjD,QAAA;MACjB,MAAMkD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASZ,QAAA;MAEjCkD,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;MAC3CI,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,CAAC;MAC3CG,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,CAAC;IAC5C;IAEDG,cAAA,EAAgB,SAAAA,CAAUL,CAAA,EAAG;MAC3B,MAAMG,GAAA,GAAM,KAAKjD,QAAA;MACjB,MAAMkD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASZ,QAAA;MAEjCkD,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;IAC5C;IAEDM,aAAA,EAAe,SAAAA,CAAUN,CAAA,EAAG;MAC1B,MAAMG,GAAA,GAAM,KAAKjD,QAAA;MACjB,MAAMkD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASZ,QAAA;MAEjCkD,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;IAC5C;IAEDO,SAAA,EAAW,SAAAA,CAAUP,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAC5B,MAAMC,GAAA,GAAM,KAAKhD,OAAA;MACjB,MAAMiD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASX,OAAA;MAEjCiD,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;MAC3CI,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,CAAC;MAC3CG,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,CAAC;IAC5C;IAEDM,aAAA,EAAe,SAAAA,CAAUR,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAChC,MAAMC,GAAA,GAAM,KAAKjD,QAAA;MACjB,MAAMkD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASX,OAAA;MAEjCX,GAAA,CAAIiE,SAAA,CAAUN,GAAA,EAAKH,CAAC;MACpBtD,GAAA,CAAI+D,SAAA,CAAUN,GAAA,EAAKF,CAAC;MACpBtD,GAAA,CAAI8D,SAAA,CAAUN,GAAA,EAAKD,CAAC;MAEpBrD,GAAA,CAAI6D,UAAA,CAAW/D,GAAA,EAAKD,GAAG;MACvBE,GAAA,CAAI8D,UAAA,CAAWlE,GAAA,EAAKE,GAAG;MACvBG,GAAA,CAAI8D,KAAA,CAAM/D,GAAG;MAEbC,GAAA,CAAI+D,SAAA,CAAW;MAEfR,GAAA,CAAIjB,IAAA,CAAKtC,GAAA,CAAIgE,CAAA,EAAGhE,GAAA,CAAIiE,CAAA,EAAGjE,GAAA,CAAIkE,CAAC;MAC5BX,GAAA,CAAIjB,IAAA,CAAKtC,GAAA,CAAIgE,CAAA,EAAGhE,GAAA,CAAIiE,CAAA,EAAGjE,GAAA,CAAIkE,CAAC;MAC5BX,GAAA,CAAIjB,IAAA,CAAKtC,GAAA,CAAIgE,CAAA,EAAGhE,GAAA,CAAIiE,CAAA,EAAGjE,GAAA,CAAIkE,CAAC;IAC7B;IAEDC,QAAA,EAAU,SAAAA,CAAUhB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAC3B,MAAMC,GAAA,GAAM,KAAK/C,MAAA;MACjB,MAAMgD,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAASV,MAAA;MAEjC,IAAI+C,GAAA,CAAIH,CAAC,MAAM,QAAWI,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;MACrE,IAAIG,GAAA,CAAIF,CAAC,MAAM,QAAWG,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,CAAC;MACrE,IAAIE,GAAA,CAAID,CAAC,MAAM,QAAWE,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,CAAC;IACtE;IAEDe,KAAA,EAAO,SAAAA,CAAUjB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACxB,MAAMC,GAAA,GAAM,KAAK9C,GAAA;MACjB,MAAM+C,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAAST,GAAA;MAEjC+C,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;MAC/BI,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIF,CAAA,GAAI,CAAC,GAAGE,GAAA,CAAIF,CAAA,GAAI,CAAC,CAAC;MAC/BG,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAID,CAAA,GAAI,CAAC,GAAGC,GAAA,CAAID,CAAA,GAAI,CAAC,CAAC;IAChC;IAEDgB,YAAA,EAAc,SAAAA,CAAA,EAAY;MACxB,MAAMd,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAAST,GAAA;MAEjC+C,GAAA,CAAIjB,IAAA,CAAK,GAAG,CAAC;MACbiB,GAAA,CAAIjB,IAAA,CAAK,GAAG,CAAC;MACbiB,GAAA,CAAIjB,IAAA,CAAK,GAAG,CAAC;IACd;IAEDgC,SAAA,EAAW,SAAAA,CAAUnB,CAAA,EAAG;MACtB,MAAMG,GAAA,GAAM,KAAK9C,GAAA;MACjB,MAAM+C,GAAA,GAAM,KAAKnD,MAAA,CAAOa,QAAA,CAAST,GAAA;MAEjC+C,GAAA,CAAIjB,IAAA,CAAKgB,GAAA,CAAIH,CAAA,GAAI,CAAC,GAAGG,GAAA,CAAIH,CAAA,GAAI,CAAC,CAAC;IAChC;IAEDoB,OAAA,EAAS,SAAAA,CAAUpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGmB,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI;MAClD,MAAMC,IAAA,GAAO,KAAKzE,QAAA,CAASwB,MAAA;MAE3B,IAAIkD,EAAA,GAAK,KAAKnC,gBAAA,CAAiBO,CAAA,EAAG2B,IAAI;MACtC,IAAIE,EAAA,GAAK,KAAKpC,gBAAA,CAAiBQ,CAAA,EAAG0B,IAAI;MACtC,IAAIG,EAAA,GAAK,KAAKrC,gBAAA,CAAiBS,CAAA,EAAGyB,IAAI;MAEtC,KAAK5B,SAAA,CAAU6B,EAAA,EAAIC,EAAA,EAAIC,EAAE;MACzB,KAAKd,QAAA,CAASY,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAIxB,IAAIN,EAAA,KAAO,UAAaA,EAAA,KAAO,IAAI;QACjC,MAAMO,IAAA,GAAO,KAAK5E,OAAA,CAAQuB,MAAA;QAE1BkD,EAAA,GAAK,KAAK/B,gBAAA,CAAiB2B,EAAA,EAAIO,IAAI;QACnCF,EAAA,GAAK,KAAKhC,gBAAA,CAAiB4B,EAAA,EAAIM,IAAI;QACnCD,EAAA,GAAK,KAAKjC,gBAAA,CAAiB6B,EAAA,EAAIK,IAAI;QAEnC,KAAKxB,SAAA,CAAUqB,EAAA,EAAIC,EAAA,EAAIC,EAAE;MACjC,OAAa;QACL,KAAKtB,aAAA,CAAcoB,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAC9B;MAID,IAAIT,EAAA,KAAO,UAAaA,EAAA,KAAO,IAAI;QACjC,MAAMW,KAAA,GAAQ,KAAK3E,GAAA,CAAIqB,MAAA;QAEvBkD,EAAA,GAAK,KAAK9B,YAAA,CAAauB,EAAA,EAAIW,KAAK;QAChCH,EAAA,GAAK,KAAK/B,YAAA,CAAawB,EAAA,EAAIU,KAAK;QAChCF,EAAA,GAAK,KAAKhC,YAAA,CAAayB,EAAA,EAAIS,KAAK;QAEhC,KAAKf,KAAA,CAAMW,EAAA,EAAIC,EAAA,EAAIC,EAAE;QAErB,KAAK7E,MAAA,CAAOa,QAAA,CAASC,YAAA,GAAe;MAC5C,OAAa;QAGL,KAAKmD,YAAA,CAAc;MACpB;IACF;IAEDe,gBAAA,EAAkB,SAAAA,CAAU/E,QAAA,EAAU;MACpC,KAAKD,MAAA,CAAOa,QAAA,CAASoE,IAAA,GAAO;MAE5B,MAAMP,IAAA,GAAO,KAAKzE,QAAA,CAASwB,MAAA;MAE3B,SAASyD,EAAA,GAAK,GAAGC,CAAA,GAAIlF,QAAA,CAASwB,MAAA,EAAQyD,EAAA,GAAKC,CAAA,EAAGD,EAAA,IAAM;QAClD,MAAM3D,KAAA,GAAQ,KAAKiB,gBAAA,CAAiBvC,QAAA,CAASiF,EAAE,GAAGR,IAAI;QAEtD,KAAKtB,cAAA,CAAe7B,KAAK;QACzB,KAAKwC,QAAA,CAASxC,KAAK;MACpB;IACF;IAED6D,eAAA,EAAiB,SAAAA,CAAUnF,QAAA,EAAUG,GAAA,EAAK;MACxC,KAAKJ,MAAA,CAAOa,QAAA,CAASoE,IAAA,GAAO;MAE5B,MAAMP,IAAA,GAAO,KAAKzE,QAAA,CAASwB,MAAA;MAC3B,MAAMsD,KAAA,GAAQ,KAAK3E,GAAA,CAAIqB,MAAA;MAEvB,SAASyD,EAAA,GAAK,GAAGC,CAAA,GAAIlF,QAAA,CAASwB,MAAA,EAAQyD,EAAA,GAAKC,CAAA,EAAGD,EAAA,IAAM;QAClD,KAAK7B,aAAA,CAAc,KAAKb,gBAAA,CAAiBvC,QAAA,CAASiF,EAAE,GAAGR,IAAI,CAAC;MAC7D;MAED,SAASW,GAAA,GAAM,GAAGF,CAAA,GAAI/E,GAAA,CAAIqB,MAAA,EAAQ4D,GAAA,GAAMF,CAAA,EAAGE,GAAA,IAAO;QAChD,KAAKnB,SAAA,CAAU,KAAKrB,YAAA,CAAazC,GAAA,CAAIiF,GAAG,GAAGN,KAAK,CAAC;MAClD;IACF;EACF;EAEDjF,KAAA,CAAMS,WAAA,CAAY,IAAI,KAAK;EAE3B,OAAOT,KAAA;AACT;AAIA,MAAMwF,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKpF,SAAA,GAAY;EAClB;EAEDqF,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKR,OAAO;IAC1CO,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;IACxBH,MAAA,CAAOI,gBAAA,CAAiB,KAAKC,aAAa;IAC1CL,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAC9CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,IAAA,EAAM;MACd,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMN,OAAA,CAAQoB,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDgB,aAAazG,SAAA,EAAW;IACtB,KAAKA,SAAA,GAAYA,SAAA;IAEjB,OAAO;EACR;EAEDoG,MAAMD,IAAA,EAAM;IACV,MAAM1G,KAAA,GAAQ,IAAID,WAAA,CAAa;IAE/B,IAAI2G,IAAA,CAAKO,OAAA,CAAQ,MAAM,MAAM,IAAI;MAE/BP,IAAA,GAAOA,IAAA,CAAKQ,OAAA,CAAQ,SAAS,IAAI;IAClC;IAED,IAAIR,IAAA,CAAKO,OAAA,CAAQ,MAAM,MAAM,IAAI;MAE/BP,IAAA,GAAOA,IAAA,CAAKQ,OAAA,CAAQ,SAAS,EAAE;IAChC;IAED,MAAMC,KAAA,GAAQT,IAAA,CAAKU,KAAA,CAAM,IAAI;IAC7B,IAAIC,IAAA,GAAO;MACTC,aAAA,GAAgB;IAClB,IAAIC,UAAA,GAAa;IACjB,IAAIC,MAAA,GAAS,EAAE;IAGf,MAAMC,QAAA,GAAW,OAAO,GAAGA,QAAA,KAAa;IAExC,SAASC,CAAA,GAAI,GAAGrC,CAAA,GAAI8B,KAAA,CAAMxF,MAAA,EAAQ+F,CAAA,GAAIrC,CAAA,EAAGqC,CAAA,IAAK;MAC5CL,IAAA,GAAOF,KAAA,CAAMO,CAAC;MAEdL,IAAA,GAAOI,QAAA,GAAWJ,IAAA,CAAKI,QAAA,CAAQ,IAAKJ,IAAA,CAAKM,IAAA,CAAM;MAE/CJ,UAAA,GAAaF,IAAA,CAAK1F,MAAA;MAElB,IAAI4F,UAAA,KAAe,GAAG;MAEtBD,aAAA,GAAgBD,IAAA,CAAKO,MAAA,CAAO,CAAC;MAG7B,IAAIN,aAAA,KAAkB,KAAK;MAE3B,IAAIA,aAAA,KAAkB,KAAK;QACzB,MAAMO,IAAA,GAAOR,IAAA,CAAKD,KAAA,CAAM,KAAK;QAE7B,QAAQS,IAAA,CAAK,CAAC;UACZ,KAAK;YACH7H,KAAA,CAAMG,QAAA,CAASiC,IAAA,CAAK0F,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,CAAC;YACjF,IAAIA,IAAA,CAAKlG,MAAA,IAAU,GAAG;cACpB3B,KAAA,CAAMK,MAAA,CAAO+B,IAAA,CAAK0F,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,CAAC;YAC7F,OAAmB;cAGL7H,KAAA,CAAMK,MAAA,CAAO+B,IAAA,CAAK,QAAW,QAAW,MAAS;YAClD;YAED;UACF,KAAK;YACHpC,KAAA,CAAMI,OAAA,CAAQgC,IAAA,CAAK0F,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,CAAC;YAChF;UACF,KAAK;YACH7H,KAAA,CAAMM,GAAA,CAAI8B,IAAA,CAAK0F,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,GAAGC,UAAA,CAAWD,IAAA,CAAK,CAAC,CAAC,CAAC;YACvD;QACH;MACT,WAAiBP,aAAA,KAAkB,KAAK;QAChC,MAAMS,QAAA,GAAWV,IAAA,CAAKW,MAAA,CAAO,CAAC,EAAEL,IAAA,CAAM;QACtC,MAAMM,UAAA,GAAaF,QAAA,CAASX,KAAA,CAAM,KAAK;QACvC,MAAMc,YAAA,GAAe,EAAE;QAIvB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKH,UAAA,CAAWtG,MAAA,EAAQwG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnD,MAAME,MAAA,GAASJ,UAAA,CAAWE,CAAC;UAE3B,IAAIE,MAAA,CAAO1G,MAAA,GAAS,GAAG;YACrB,MAAM2G,WAAA,GAAcD,MAAA,CAAOjB,KAAA,CAAM,GAAG;YACpCc,YAAA,CAAa9F,IAAA,CAAKkG,WAAW;UAC9B;QACF;QAID,MAAMC,EAAA,GAAKL,YAAA,CAAa,CAAC;QAEzB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKF,YAAA,CAAavG,MAAA,GAAS,GAAGwG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACzD,MAAMK,EAAA,GAAKN,YAAA,CAAaC,CAAC;UACzB,MAAMM,EAAA,GAAKP,YAAA,CAAaC,CAAA,GAAI,CAAC;UAE7BnI,KAAA,CAAMqE,OAAA,CAAQkE,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,GAAGF,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,GAAGF,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,GAAGC,EAAA,CAAG,CAAC,CAAC;QAC5E;MACT,WAAiBnB,aAAA,KAAkB,KAAK;QAChC,MAAMoB,SAAA,GAAYrB,IAAA,CAAKsB,SAAA,CAAU,CAAC,EAAEhB,IAAA,CAAM,EAACP,KAAA,CAAM,GAAG;QACpD,IAAIwB,YAAA,GAAe,EAAE;QACrB,MAAMC,OAAA,GAAU,EAAE;QAElB,IAAIxB,IAAA,CAAKJ,OAAA,CAAQ,GAAG,MAAM,IAAI;UAC5B2B,YAAA,GAAeF,SAAA;QACzB,OAAe;UACL,SAASI,EAAA,GAAK,GAAGC,IAAA,GAAOL,SAAA,CAAU/G,MAAA,EAAQmH,EAAA,GAAKC,IAAA,EAAMD,EAAA,IAAM;YACzD,MAAME,KAAA,GAAQN,SAAA,CAAUI,EAAE,EAAE1B,KAAA,CAAM,GAAG;YAErC,IAAI4B,KAAA,CAAM,CAAC,MAAM,IAAIJ,YAAA,CAAaxG,IAAA,CAAK4G,KAAA,CAAM,CAAC,CAAC;YAC/C,IAAIA,KAAA,CAAM,CAAC,MAAM,IAAIH,OAAA,CAAQzG,IAAA,CAAK4G,KAAA,CAAM,CAAC,CAAC;UAC3C;QACF;QAEDhJ,KAAA,CAAMsF,eAAA,CAAgBsD,YAAA,EAAcC,OAAO;MACnD,WAAiBvB,aAAA,KAAkB,KAAK;QAChC,MAAMS,QAAA,GAAWV,IAAA,CAAKW,MAAA,CAAO,CAAC,EAAEL,IAAA,CAAM;QACtC,MAAMsB,SAAA,GAAYlB,QAAA,CAASX,KAAA,CAAM,GAAG;QAEpCpH,KAAA,CAAMkF,gBAAA,CAAiB+D,SAAS;MACxC,YAAkBzB,MAAA,GAASnI,eAAA,CAAgB6J,IAAA,CAAK7B,IAAI,OAAO,MAAM;QAOzD,MAAM3G,IAAA,IAAQ,MAAM8G,MAAA,CAAO,CAAC,EAAEQ,MAAA,CAAO,CAAC,EAAEL,IAAA,IAAQK,MAAA,CAAO,CAAC;QAExDhI,KAAA,CAAMS,WAAA,CAAYC,IAAI;MACvB,WAAUnB,qBAAA,CAAsB4J,IAAA,CAAK9B,IAAI,GAAG;QAG3CrH,KAAA,CAAME,MAAA,CAAOgB,aAAA,CAAcmG,IAAA,CAAKsB,SAAA,CAAU,CAAC,EAAEhB,IAAA,CAAI,GAAI3H,KAAA,CAAMQ,iBAAiB;MAC7E,WAAUlB,yBAAA,CAA0B6J,IAAA,CAAK9B,IAAI,GAAG;QAG/CrH,KAAA,CAAMQ,iBAAA,CAAkB4B,IAAA,CAAKiF,IAAA,CAAKsB,SAAA,CAAU,CAAC,EAAEhB,IAAA,EAAM;MACtD,WAAUnI,gBAAA,CAAiB2J,IAAA,CAAK9B,IAAI,GAAG;QAItCR,OAAA,CAAQuC,IAAA,CACN,sGACD;MACT,WAAiB9B,aAAA,KAAkB,KAAK;QAChCE,MAAA,GAASH,IAAA,CAAKD,KAAA,CAAM,GAAG;QAsBvB,IAAII,MAAA,CAAO7F,MAAA,GAAS,GAAG;UACrB,MAAMgB,KAAA,GAAQ6E,MAAA,CAAO,CAAC,EAAEG,IAAA,CAAI,EAAG0B,WAAA,CAAa;UAC5CrJ,KAAA,CAAME,MAAA,CAAOe,MAAA,GAAS0B,KAAA,KAAU,OAAOA,KAAA,KAAU;QAC3D,OAAe;UAEL3C,KAAA,CAAME,MAAA,CAAOe,MAAA,GAAS;QACvB;QAED,MAAMS,QAAA,GAAW1B,KAAA,CAAME,MAAA,CAAOW,eAAA,CAAiB;QAC/C,IAAIa,QAAA,EAAUA,QAAA,CAAST,MAAA,GAASjB,KAAA,CAAME,MAAA,CAAOe,MAAA;MACrD,OAAa;QAEL,IAAIoG,IAAA,KAAS,MAAM;QAEnBR,OAAA,CAAQuC,IAAA,CAAK,wCAAwC/B,IAAA,GAAO,GAAG;MAChE;IACF;IAEDrH,KAAA,CAAMyC,QAAA,CAAU;IAEhB,MAAM6G,SAAA,GAAY,IAAIC,KAAA,CAAO;IAC7BD,SAAA,CAAU9I,iBAAA,GAAoB,GAAGgJ,MAAA,CAAOxJ,KAAA,CAAMQ,iBAAiB;IAE/D,MAAMiJ,aAAA,GAAgB,EAAEzJ,KAAA,CAAMC,OAAA,CAAQ0B,MAAA,KAAW,KAAK3B,KAAA,CAAMC,OAAA,CAAQ,CAAC,EAAEc,QAAA,CAASZ,QAAA,CAASwB,MAAA,KAAW;IAEpG,IAAI8H,aAAA,KAAkB,MAAM;MAC1B,SAAS/B,CAAA,GAAI,GAAGrC,CAAA,GAAIrF,KAAA,CAAMC,OAAA,CAAQ0B,MAAA,EAAQ+F,CAAA,GAAIrC,CAAA,EAAGqC,CAAA,IAAK;QACpD,MAAMxH,MAAA,GAASF,KAAA,CAAMC,OAAA,CAAQyH,CAAC;QAC9B,MAAM3G,QAAA,GAAWb,MAAA,CAAOa,QAAA;QACxB,MAAMR,SAAA,GAAYL,MAAA,CAAOK,SAAA;QACzB,MAAMmJ,MAAA,GAAS3I,QAAA,CAASoE,IAAA,KAAS;QACjC,MAAMwE,QAAA,GAAW5I,QAAA,CAASoE,IAAA,KAAS;QACnC,IAAIyE,eAAA,GAAkB;QAGtB,IAAI7I,QAAA,CAASZ,QAAA,CAASwB,MAAA,KAAW,GAAG;QAEpC,MAAMkI,cAAA,GAAiB,IAAIC,cAAA,CAAgB;QAE3CD,cAAA,CAAeE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBjJ,QAAA,CAASZ,QAAA,EAAU,CAAC,CAAC;QAExF,IAAIY,QAAA,CAASX,OAAA,CAAQuB,MAAA,GAAS,GAAG;UAC/BkI,cAAA,CAAeE,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBjJ,QAAA,CAASX,OAAA,EAAS,CAAC,CAAC;QACtF;QAED,IAAIW,QAAA,CAASV,MAAA,CAAOsB,MAAA,GAAS,GAAG;UAC9BiI,eAAA,GAAkB;UAClBC,cAAA,CAAeE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBjJ,QAAA,CAASV,MAAA,EAAQ,CAAC,CAAC;QACpF;QAED,IAAIU,QAAA,CAASC,YAAA,KAAiB,MAAM;UAClC6I,cAAA,CAAeE,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBjJ,QAAA,CAAST,GAAA,EAAK,CAAC,CAAC;QAC9E;QAID,MAAM2J,gBAAA,GAAmB,EAAE;QAE3B,SAAS1H,EAAA,GAAK,GAAG2H,KAAA,GAAQ3J,SAAA,CAAUoB,MAAA,EAAQY,EAAA,GAAK2H,KAAA,EAAO3H,EAAA,IAAM;UAC3D,MAAM4H,cAAA,GAAiB5J,SAAA,CAAUgC,EAAE;UACnC,MAAM6H,YAAA,GAAeD,cAAA,CAAezJ,IAAA,GAAO,MAAMyJ,cAAA,CAAelJ,MAAA,GAAS,MAAM2I,eAAA;UAC/E,IAAIlI,QAAA,GAAW1B,KAAA,CAAMO,SAAA,CAAU6J,YAAY;UAE3C,IAAI,KAAK7J,SAAA,KAAc,MAAM;YAC3BmB,QAAA,GAAW,KAAKnB,SAAA,CAAU8J,MAAA,CAAOF,cAAA,CAAezJ,IAAI;YAGpD,IAAIgJ,MAAA,IAAUhI,QAAA,IAAY,EAAEA,QAAA,YAAoB4I,iBAAA,GAAoB;cAClE,MAAMC,YAAA,GAAe,IAAID,iBAAA,CAAmB;cAC5CE,QAAA,CAASC,SAAA,CAAUC,IAAA,CAAKC,IAAA,CAAKJ,YAAA,EAAc7I,QAAQ;cACnD6I,YAAA,CAAaK,KAAA,CAAMF,IAAA,CAAKhJ,QAAA,CAASkJ,KAAK;cACtClJ,QAAA,GAAW6I,YAAA;YACZ,WAAUZ,QAAA,IAAYjI,QAAA,IAAY,EAAEA,QAAA,YAAoBmJ,cAAA,GAAiB;cACxE,MAAMC,cAAA,GAAiB,IAAID,cAAA,CAAe;gBAAEE,IAAA,EAAM;gBAAIC,eAAA,EAAiB;cAAA,CAAO;cAC9ER,QAAA,CAASC,SAAA,CAAUC,IAAA,CAAKC,IAAA,CAAKG,cAAA,EAAgBpJ,QAAQ;cACrDoJ,cAAA,CAAeF,KAAA,CAAMF,IAAA,CAAKhJ,QAAA,CAASkJ,KAAK;cACxCE,cAAA,CAAeG,GAAA,GAAMvJ,QAAA,CAASuJ,GAAA;cAC9BvJ,QAAA,GAAWoJ,cAAA;YACZ;UACF;UAED,IAAIpJ,QAAA,KAAa,QAAW;YAC1B,IAAIgI,MAAA,EAAQ;cACVhI,QAAA,GAAW,IAAI4I,iBAAA,CAAmB;YACnC,WAAUX,QAAA,EAAU;cACnBjI,QAAA,GAAW,IAAImJ,cAAA,CAAe;gBAAEE,IAAA,EAAM;gBAAGC,eAAA,EAAiB;cAAA,CAAO;YAC/E,OAAmB;cACLtJ,QAAA,GAAW,IAAIwJ,iBAAA,CAAmB;YACnC;YAEDxJ,QAAA,CAAShB,IAAA,GAAOyJ,cAAA,CAAezJ,IAAA;YAC/BgB,QAAA,CAASyJ,WAAA,GAAchB,cAAA,CAAelJ,MAAA,GAAS,QAAQ;YACvDS,QAAA,CAAS0J,YAAA,GAAexB,eAAA;YAExB5J,KAAA,CAAMO,SAAA,CAAU6J,YAAY,IAAI1I,QAAA;UACjC;UAEDuI,gBAAA,CAAiB7H,IAAA,CAAKV,QAAQ;QAC/B;QAID,IAAI2J,IAAA;QAEJ,IAAIpB,gBAAA,CAAiBtI,MAAA,GAAS,GAAG;UAC/B,SAASY,EAAA,GAAK,GAAG2H,KAAA,GAAQ3J,SAAA,CAAUoB,MAAA,EAAQY,EAAA,GAAK2H,KAAA,EAAO3H,EAAA,IAAM;YAC3D,MAAM4H,cAAA,GAAiB5J,SAAA,CAAUgC,EAAE;YACnCsH,cAAA,CAAeyB,QAAA,CAASnB,cAAA,CAAepI,UAAA,EAAYoI,cAAA,CAAe5I,UAAA,EAAYgB,EAAE;UACjF;UAED,IAAImH,MAAA,EAAQ;YACV2B,IAAA,GAAO,IAAIE,YAAA,CAAa1B,cAAA,EAAgBI,gBAAgB;UACzD,WAAUN,QAAA,EAAU;YACnB0B,IAAA,GAAO,IAAIG,MAAA,CAAO3B,cAAA,EAAgBI,gBAAgB;UAC9D,OAAiB;YACLoB,IAAA,GAAO,IAAII,IAAA,CAAK5B,cAAA,EAAgBI,gBAAgB;UACjD;QACX,OAAe;UACL,IAAIP,MAAA,EAAQ;YACV2B,IAAA,GAAO,IAAIE,YAAA,CAAa1B,cAAA,EAAgBI,gBAAA,CAAiB,CAAC,CAAC;UAC5D,WAAUN,QAAA,EAAU;YACnB0B,IAAA,GAAO,IAAIG,MAAA,CAAO3B,cAAA,EAAgBI,gBAAA,CAAiB,CAAC,CAAC;UACjE,OAAiB;YACLoB,IAAA,GAAO,IAAII,IAAA,CAAK5B,cAAA,EAAgBI,gBAAA,CAAiB,CAAC,CAAC;UACpD;QACF;QAEDoB,IAAA,CAAK3K,IAAA,GAAOR,MAAA,CAAOQ,IAAA;QAEnB4I,SAAA,CAAUoC,GAAA,CAAIL,IAAI;MACnB;IACP,OAAW;MAGL,IAAIrL,KAAA,CAAMG,QAAA,CAASwB,MAAA,GAAS,GAAG;QAC7B,MAAMD,QAAA,GAAW,IAAImJ,cAAA,CAAe;UAAEE,IAAA,EAAM;UAAGC,eAAA,EAAiB;QAAA,CAAO;QAEvE,MAAMnB,cAAA,GAAiB,IAAIC,cAAA,CAAgB;QAE3CD,cAAA,CAAeE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBhK,KAAA,CAAMG,QAAA,EAAU,CAAC,CAAC;QAErF,IAAIH,KAAA,CAAMK,MAAA,CAAOsB,MAAA,GAAS,KAAK3B,KAAA,CAAMK,MAAA,CAAO,CAAC,MAAM,QAAW;UAC5DwJ,cAAA,CAAeE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBhK,KAAA,CAAMK,MAAA,EAAQ,CAAC,CAAC;UAChFqB,QAAA,CAAS0J,YAAA,GAAe;QACzB;QAED,MAAMO,MAAA,GAAS,IAAIH,MAAA,CAAO3B,cAAA,EAAgBnI,QAAQ;QAClD4H,SAAA,CAAUoC,GAAA,CAAIC,MAAM;MACrB;IACF;IAED,OAAOrC,SAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}