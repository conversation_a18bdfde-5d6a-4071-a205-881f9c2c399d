{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3 } from \"three\";\nconst isMesh = object => object.isMesh;\nclass STLExporter {\n  constructor() {\n    __publicField(this, \"binary\", false);\n    __publicField(this, \"output\", \"\");\n    __publicField(this, \"offset\", 80);\n    // skip header\n    __publicField(this, \"objects\", []);\n    __publicField(this, \"triangles\", 0);\n    __publicField(this, \"vA\", new Vector3());\n    __publicField(this, \"vB\", new Vector3());\n    __publicField(this, \"vC\", new Vector3());\n    __publicField(this, \"cb\", new Vector3());\n    __publicField(this, \"ab\", new Vector3());\n    __publicField(this, \"normal\", new Vector3());\n  }\n  parse(scene, options) {\n    this.binary = (options == null ? void 0 : options.binary) !== void 0 ? options == null ? void 0 : options.binary : false;\n    scene.traverse(object => {\n      if (isMesh(object)) {\n        const geometry = object.geometry;\n        if (!geometry.isBufferGeometry) {\n          throw new Error(\"THREE.STLExporter: Geometry is not of type THREE.BufferGeometry.\");\n        }\n        const index = geometry.index;\n        const positionAttribute = geometry.getAttribute(\"position\") || null;\n        if (!positionAttribute) return;\n        this.triangles += index !== null ? index.count / 3 : positionAttribute.count / 3;\n        this.objects.push({\n          object3d: object,\n          geometry\n        });\n      }\n    });\n    if (this.binary) {\n      const bufferLength = this.triangles * 2 + this.triangles * 3 * 4 * 4 + 80 + 4;\n      const arrayBuffer = new ArrayBuffer(bufferLength);\n      this.output = new DataView(arrayBuffer);\n      this.output.setUint32(this.offset, this.triangles, true);\n      this.offset += 4;\n    } else {\n      this.output = \"\";\n      this.output += \"solid exported\\n\";\n    }\n    for (let i = 0, il = this.objects.length; i < il; i++) {\n      const object = this.objects[i].object3d;\n      const geometry = this.objects[i].geometry;\n      const index = geometry.index;\n      const positionAttribute = geometry.getAttribute(\"position\");\n      if (index !== null) {\n        for (let j = 0; j < index.count; j += 3) {\n          const a = index.getX(j + 0);\n          const b = index.getX(j + 1);\n          const c = index.getX(j + 2);\n          this.writeFace(a, b, c, positionAttribute, object);\n        }\n      } else {\n        for (let j = 0; j < positionAttribute.count; j += 3) {\n          const a = j + 0;\n          const b = j + 1;\n          const c = j + 2;\n          this.writeFace(a, b, c, positionAttribute, object);\n        }\n      }\n    }\n    if (!this.binary) {\n      this.output += \"endsolid exported\\n\";\n    }\n    return this.output;\n  }\n  writeFace(a, b, c, positionAttribute, object) {\n    this.vA.fromBufferAttribute(positionAttribute, a);\n    this.vB.fromBufferAttribute(positionAttribute, b);\n    this.vC.fromBufferAttribute(positionAttribute, c);\n    if (object.isSkinnedMesh) {\n      const mesh = object;\n      if (\"applyBoneTransform\" in mesh) {\n        mesh.applyBoneTransform(a, this.vA);\n        mesh.applyBoneTransform(b, this.vB);\n        mesh.applyBoneTransform(c, this.vC);\n      } else {\n        mesh.boneTransform(a, this.vA);\n        mesh.boneTransform(b, this.vB);\n        mesh.boneTransform(c, this.vC);\n      }\n    }\n    this.vA.applyMatrix4(object.matrixWorld);\n    this.vB.applyMatrix4(object.matrixWorld);\n    this.vC.applyMatrix4(object.matrixWorld);\n    this.writeNormal(this.vA, this.vB, this.vC);\n    this.writeVertex(this.vA);\n    this.writeVertex(this.vB);\n    this.writeVertex(this.vC);\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setUint16(this.offset, 0, true);\n      this.offset += 2;\n    } else {\n      this.output += \"\t\tendloop\\n\";\n      this.output += \"\tendfacet\\n\";\n    }\n  }\n  writeNormal(vA, vB, vC) {\n    this.cb.subVectors(vC, vB);\n    this.ab.subVectors(vA, vB);\n    this.cb.cross(this.ab).normalize();\n    this.normal.copy(this.cb).normalize();\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, this.normal.x, true);\n      this.offset += 4;\n      this.output.setFloat32(this.offset, this.normal.y, true);\n      this.offset += 4;\n      this.output.setFloat32(this.offset, this.normal.z, true);\n      this.offset += 4;\n    } else {\n      this.output += `\tfacet normal ${this.normal.x} ${this.normal.y} ${this.normal.z}\n`;\n      this.output += \"\t\touter loop\\n\";\n    }\n  }\n  writeVertex(vertex) {\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, vertex.x, true);\n      this.offset += 4;\n      this.output.setFloat32(this.offset, vertex.y, true);\n      this.offset += 4;\n      this.output.setFloat32(this.offset, vertex.z, true);\n      this.offset += 4;\n    } else {\n      this.output += `\t\t\tvertex ${vertex.x} ${vertex.y} ${vertex.z}\n`;\n    }\n  }\n}\nexport { STLExporter };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "object", "STLExporter", "constructor", "__publicField", "Vector3", "parse", "scene", "options", "binary", "traverse", "geometry", "isBufferGeometry", "Error", "index", "positionAttribute", "getAttribute", "triangles", "count", "objects", "push", "object3d", "bufferLength", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "output", "DataView", "setUint32", "offset", "i", "il", "length", "j", "a", "getX", "b", "c", "writeFace", "vA", "fromBufferAttribute", "vB", "vC", "isSkinnedMesh", "mesh", "applyBoneTransform", "boneTransform", "applyMatrix4", "matrixWorld", "writeNormal", "writeVertex", "setUint16", "cb", "subVectors", "ab", "cross", "normalize", "normal", "copy", "setFloat32", "x", "y", "z", "vertex"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/exporters/STLExporter.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  InterleavedBufferAttribute,\n  Mesh,\n  Object3D,\n  SkinnedMesh,\n  Vector3,\n} from 'three'\n\nexport interface STLExporterOptionsBinary {\n  binary: true\n}\n\nexport interface STLExporterOptionsString {\n  binary?: false\n}\n\nexport interface STLExporterOptions {\n  binary?: boolean\n}\n\nconst isMesh = (object: unknown): object is Mesh => (object as any).isMesh\n\nexport class STLExporter {\n  private binary = false\n\n  private output: string | DataView = ''\n  private offset: number = 80 // skip header\n\n  private objects: { object3d: Object3D; geometry: BufferGeometry }[] = []\n  private triangles: number = 0\n\n  private vA = new Vector3()\n  private vB = new Vector3()\n  private vC = new Vector3()\n  private cb = new Vector3()\n  private ab = new Vector3()\n  private normal = new Vector3()\n\n  parse(scene: Object3D, options: STLExporterOptionsBinary): DataView\n  parse(scene: Object3D, options?: STLExporterOptionsString): string\n  parse(scene: Object3D, options?: STLExporterOptions): string | DataView {\n    this.binary = options?.binary !== undefined ? options?.binary : false\n\n    scene.traverse((object: Object3D) => {\n      if (isMesh(object)) {\n        const geometry = object.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.STLExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        const index = geometry.index\n        const positionAttribute = geometry.getAttribute('position') || null\n        if (!positionAttribute) return\n\n        this.triangles += index !== null ? index.count / 3 : positionAttribute.count / 3\n\n        this.objects.push({\n          object3d: object,\n          geometry: geometry,\n        })\n      }\n    })\n\n    if (this.binary) {\n      const bufferLength = this.triangles * 2 + this.triangles * 3 * 4 * 4 + 80 + 4\n      const arrayBuffer = new ArrayBuffer(bufferLength)\n      this.output = new DataView(arrayBuffer)\n      this.output.setUint32(this.offset, this.triangles, true)\n      this.offset += 4\n    } else {\n      this.output = ''\n      this.output += 'solid exported\\n'\n    }\n\n    for (let i = 0, il = this.objects.length; i < il; i++) {\n      const object = this.objects[i].object3d\n      const geometry = this.objects[i].geometry\n\n      const index = geometry.index\n      const positionAttribute = geometry.getAttribute('position')\n\n      if (index !== null) {\n        // indexed geometry\n        for (let j = 0; j < index.count; j += 3) {\n          const a = index.getX(j + 0)\n          const b = index.getX(j + 1)\n          const c = index.getX(j + 2)\n\n          this.writeFace(a, b, c, positionAttribute, object as SkinnedMesh)\n        }\n      } else {\n        // non-indexed geometry\n        for (let j = 0; j < positionAttribute.count; j += 3) {\n          const a = j + 0\n          const b = j + 1\n          const c = j + 2\n\n          this.writeFace(a, b, c, positionAttribute, object as SkinnedMesh)\n        }\n      }\n    }\n\n    if (!this.binary) {\n      this.output += 'endsolid exported\\n'\n    }\n\n    return this.output\n  }\n\n  private writeFace(\n    a: number,\n    b: number,\n    c: number,\n    positionAttribute: BufferAttribute | InterleavedBufferAttribute,\n    object: SkinnedMesh,\n  ): void {\n    this.vA.fromBufferAttribute(positionAttribute, a)\n    this.vB.fromBufferAttribute(positionAttribute, b)\n    this.vC.fromBufferAttribute(positionAttribute, c)\n\n    if (object.isSkinnedMesh) {\n      const mesh = object as Omit<SkinnedMesh, 'boneTransform' | 'applyBoneTransform'> &\n        (\n          | {\n              boneTransform(index: number, vector: Vector3): Vector3\n            }\n          | {\n              applyBoneTransform(index: number, vector: Vector3): Vector3\n            }\n        )\n\n      // r151 https://github.com/mrdoob/three.js/pull/25586\n      if ('applyBoneTransform' in mesh) {\n        mesh.applyBoneTransform(a, this.vA)\n        mesh.applyBoneTransform(b, this.vB)\n        mesh.applyBoneTransform(c, this.vC)\n      } else {\n        mesh.boneTransform(a, this.vA)\n        mesh.boneTransform(b, this.vB)\n        mesh.boneTransform(c, this.vC)\n      }\n    }\n\n    this.vA.applyMatrix4(object.matrixWorld)\n    this.vB.applyMatrix4(object.matrixWorld)\n    this.vC.applyMatrix4(object.matrixWorld)\n\n    this.writeNormal(this.vA, this.vB, this.vC)\n\n    this.writeVertex(this.vA)\n    this.writeVertex(this.vB)\n    this.writeVertex(this.vC)\n\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setUint16(this.offset, 0, true)\n      this.offset += 2\n    } else {\n      this.output += '\\t\\tendloop\\n'\n      this.output += '\\tendfacet\\n'\n    }\n  }\n\n  private writeNormal(vA: Vector3, vB: Vector3, vC: Vector3): void {\n    this.cb.subVectors(vC, vB)\n    this.ab.subVectors(vA, vB)\n    this.cb.cross(this.ab).normalize()\n\n    this.normal.copy(this.cb).normalize()\n\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, this.normal.x, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, this.normal.y, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, this.normal.z, true)\n      this.offset += 4\n    } else {\n      this.output += `\\tfacet normal ${this.normal.x} ${this.normal.y} ${this.normal.z}\\n`\n      this.output += '\\t\\touter loop\\n'\n    }\n  }\n\n  private writeVertex(vertex: Vector3): void {\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, vertex.x, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, vertex.y, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, vertex.z, true)\n      this.offset += 4\n    } else {\n      this.output += `\\t\\t\\tvertex ${vertex.x} ${vertex.y} ${vertex.z}\\n`\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAsBA,MAAMA,MAAA,GAAUC,MAAA,IAAqCA,MAAA,CAAeD,MAAA;AAE7D,MAAME,WAAA,CAAY;EAAlBC,YAAA;IACGC,aAAA,iBAAS;IAETA,aAAA,iBAA4B;IAC5BA,aAAA,iBAAiB;IAEjB;IAAAA,aAAA,kBAA8D;IAC9DA,aAAA,oBAAoB;IAEpBA,aAAA,aAAK,IAAIC,OAAA;IACTD,aAAA,aAAK,IAAIC,OAAA;IACTD,aAAA,aAAK,IAAIC,OAAA;IACTD,aAAA,aAAK,IAAIC,OAAA;IACTD,aAAA,aAAK,IAAIC,OAAA;IACTD,aAAA,iBAAS,IAAIC,OAAA;;EAIrBC,MAAMC,KAAA,EAAiBC,OAAA,EAAiD;IACtE,KAAKC,MAAA,IAASD,OAAA,oBAAAA,OAAA,CAASC,MAAA,MAAW,SAAYD,OAAA,oBAAAA,OAAA,CAASC,MAAA,GAAS;IAE1DF,KAAA,CAAAG,QAAA,CAAUT,MAAA,IAAqB;MAC/B,IAAAD,MAAA,CAAOC,MAAM,GAAG;QAClB,MAAMU,QAAA,GAAWV,MAAA,CAAOU,QAAA;QAEpB,KAACA,QAAA,CAASC,gBAAA,EAAkB;UACxB,UAAIC,KAAA,CAAM,kEAAkE;QACpF;QAEA,MAAMC,KAAA,GAAQH,QAAA,CAASG,KAAA;QACvB,MAAMC,iBAAA,GAAoBJ,QAAA,CAASK,YAAA,CAAa,UAAU,KAAK;QAC/D,IAAI,CAACD,iBAAA,EAAmB;QAExB,KAAKE,SAAA,IAAaH,KAAA,KAAU,OAAOA,KAAA,CAAMI,KAAA,GAAQ,IAAIH,iBAAA,CAAkBG,KAAA,GAAQ;QAE/E,KAAKC,OAAA,CAAQC,IAAA,CAAK;UAChBC,QAAA,EAAUpB,MAAA;UACVU;QAAA,CACD;MACH;IAAA,CACD;IAED,IAAI,KAAKF,MAAA,EAAQ;MACT,MAAAa,YAAA,GAAe,KAAKL,SAAA,GAAY,IAAI,KAAKA,SAAA,GAAY,IAAI,IAAI,IAAI,KAAK;MACtE,MAAAM,WAAA,GAAc,IAAIC,WAAA,CAAYF,YAAY;MAC3C,KAAAG,MAAA,GAAS,IAAIC,QAAA,CAASH,WAAW;MACtC,KAAKE,MAAA,CAAOE,SAAA,CAAU,KAAKC,MAAA,EAAQ,KAAKX,SAAA,EAAW,IAAI;MACvD,KAAKW,MAAA,IAAU;IAAA,OACV;MACL,KAAKH,MAAA,GAAS;MACd,KAAKA,MAAA,IAAU;IACjB;IAES,SAAAI,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKX,OAAA,CAAQY,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACrD,MAAM5B,MAAA,GAAS,KAAKkB,OAAA,CAAQU,CAAC,EAAER,QAAA;MAC/B,MAAMV,QAAA,GAAW,KAAKQ,OAAA,CAAQU,CAAC,EAAElB,QAAA;MAEjC,MAAMG,KAAA,GAAQH,QAAA,CAASG,KAAA;MACjB,MAAAC,iBAAA,GAAoBJ,QAAA,CAASK,YAAA,CAAa,UAAU;MAE1D,IAAIF,KAAA,KAAU,MAAM;QAElB,SAASkB,CAAA,GAAI,GAAGA,CAAA,GAAIlB,KAAA,CAAMI,KAAA,EAAOc,CAAA,IAAK,GAAG;UACvC,MAAMC,CAAA,GAAInB,KAAA,CAAMoB,IAAA,CAAKF,CAAA,GAAI,CAAC;UAC1B,MAAMG,CAAA,GAAIrB,KAAA,CAAMoB,IAAA,CAAKF,CAAA,GAAI,CAAC;UAC1B,MAAMI,CAAA,GAAItB,KAAA,CAAMoB,IAAA,CAAKF,CAAA,GAAI,CAAC;UAE1B,KAAKK,SAAA,CAAUJ,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAGrB,iBAAA,EAAmBd,MAAqB;QAClE;MAAA,OACK;QAEL,SAAS+B,CAAA,GAAI,GAAGA,CAAA,GAAIjB,iBAAA,CAAkBG,KAAA,EAAOc,CAAA,IAAK,GAAG;UACnD,MAAMC,CAAA,GAAID,CAAA,GAAI;UACd,MAAMG,CAAA,GAAIH,CAAA,GAAI;UACd,MAAMI,CAAA,GAAIJ,CAAA,GAAI;UAEd,KAAKK,SAAA,CAAUJ,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAGrB,iBAAA,EAAmBd,MAAqB;QAClE;MACF;IACF;IAEI,KAAC,KAAKQ,MAAA,EAAQ;MAChB,KAAKgB,MAAA,IAAU;IACjB;IAEA,OAAO,KAAKA,MAAA;EACd;EAEQY,UACNJ,CAAA,EACAE,CAAA,EACAC,CAAA,EACArB,iBAAA,EACAd,MAAA,EACM;IACD,KAAAqC,EAAA,CAAGC,mBAAA,CAAoBxB,iBAAA,EAAmBkB,CAAC;IAC3C,KAAAO,EAAA,CAAGD,mBAAA,CAAoBxB,iBAAA,EAAmBoB,CAAC;IAC3C,KAAAM,EAAA,CAAGF,mBAAA,CAAoBxB,iBAAA,EAAmBqB,CAAC;IAEhD,IAAInC,MAAA,CAAOyC,aAAA,EAAe;MACxB,MAAMC,IAAA,GAAO1C,MAAA;MAWb,IAAI,wBAAwB0C,IAAA,EAAM;QAC3BA,IAAA,CAAAC,kBAAA,CAAmBX,CAAA,EAAG,KAAKK,EAAE;QAC7BK,IAAA,CAAAC,kBAAA,CAAmBT,CAAA,EAAG,KAAKK,EAAE;QAC7BG,IAAA,CAAAC,kBAAA,CAAmBR,CAAA,EAAG,KAAKK,EAAE;MAAA,OAC7B;QACAE,IAAA,CAAAE,aAAA,CAAcZ,CAAA,EAAG,KAAKK,EAAE;QACxBK,IAAA,CAAAE,aAAA,CAAcV,CAAA,EAAG,KAAKK,EAAE;QACxBG,IAAA,CAAAE,aAAA,CAAcT,CAAA,EAAG,KAAKK,EAAE;MAC/B;IACF;IAEK,KAAAH,EAAA,CAAGQ,YAAA,CAAa7C,MAAA,CAAO8C,WAAW;IAClC,KAAAP,EAAA,CAAGM,YAAA,CAAa7C,MAAA,CAAO8C,WAAW;IAClC,KAAAN,EAAA,CAAGK,YAAA,CAAa7C,MAAA,CAAO8C,WAAW;IAEvC,KAAKC,WAAA,CAAY,KAAKV,EAAA,EAAI,KAAKE,EAAA,EAAI,KAAKC,EAAE;IAErC,KAAAQ,WAAA,CAAY,KAAKX,EAAE;IACnB,KAAAW,WAAA,CAAY,KAAKT,EAAE;IACnB,KAAAS,WAAA,CAAY,KAAKR,EAAE;IAExB,IAAI,KAAKhC,MAAA,IAAU,KAAKgB,MAAA,YAAkBC,QAAA,EAAU;MAClD,KAAKD,MAAA,CAAOyB,SAAA,CAAU,KAAKtB,MAAA,EAAQ,GAAG,IAAI;MAC1C,KAAKA,MAAA,IAAU;IAAA,OACV;MACL,KAAKH,MAAA,IAAU;MACf,KAAKA,MAAA,IAAU;IACjB;EACF;EAEQuB,YAAYV,EAAA,EAAaE,EAAA,EAAaC,EAAA,EAAmB;IAC1D,KAAAU,EAAA,CAAGC,UAAA,CAAWX,EAAA,EAAID,EAAE;IACpB,KAAAa,EAAA,CAAGD,UAAA,CAAWd,EAAA,EAAIE,EAAE;IACzB,KAAKW,EAAA,CAAGG,KAAA,CAAM,KAAKD,EAAE,EAAEE,SAAA;IAEvB,KAAKC,MAAA,CAAOC,IAAA,CAAK,KAAKN,EAAE,EAAEI,SAAA;IAE1B,IAAI,KAAK9C,MAAA,IAAU,KAAKgB,MAAA,YAAkBC,QAAA,EAAU;MAClD,KAAKD,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQ,KAAK4B,MAAA,CAAOG,CAAA,EAAG,IAAI;MACvD,KAAK/B,MAAA,IAAU;MACf,KAAKH,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQ,KAAK4B,MAAA,CAAOI,CAAA,EAAG,IAAI;MACvD,KAAKhC,MAAA,IAAU;MACf,KAAKH,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQ,KAAK4B,MAAA,CAAOK,CAAA,EAAG,IAAI;MACvD,KAAKjC,MAAA,IAAU;IAAA,OACV;MACA,KAAAH,MAAA,IAAU,iBAAkB,KAAK+B,MAAA,CAAOG,CAAA,IAAK,KAAKH,MAAA,CAAOI,CAAA,IAAK,KAAKJ,MAAA,CAAOK,CAAA;AAAA;MAC/E,KAAKpC,MAAA,IAAU;IACjB;EACF;EAEQwB,YAAYa,MAAA,EAAuB;IACzC,IAAI,KAAKrD,MAAA,IAAU,KAAKgB,MAAA,YAAkBC,QAAA,EAAU;MAClD,KAAKD,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQkC,MAAA,CAAOH,CAAA,EAAG,IAAI;MAClD,KAAK/B,MAAA,IAAU;MACf,KAAKH,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQkC,MAAA,CAAOF,CAAA,EAAG,IAAI;MAClD,KAAKhC,MAAA,IAAU;MACf,KAAKH,MAAA,CAAOiC,UAAA,CAAW,KAAK9B,MAAA,EAAQkC,MAAA,CAAOD,CAAA,EAAG,IAAI;MAClD,KAAKjC,MAAA,IAAU;IAAA,OACV;MACL,KAAKH,MAAA,IAAU,aAAgBqC,MAAA,CAAOH,CAAA,IAAKG,MAAA,CAAOF,CAAA,IAAKE,MAAA,CAAOD,CAAA;AAAA;IAChE;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}