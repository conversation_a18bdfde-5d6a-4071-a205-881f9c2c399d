{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\nexport { Html };", "map": {"version": 3, "names": ["_extends", "React", "ReactDOM", "Vector3", "DoubleSide", "OrthographicCamera", "PerspectiveCamera", "Vector2", "useThree", "useFrame", "v1", "v2", "v3", "v4", "defaultCalculatePosition", "el", "camera", "size", "objectPos", "setFromMatrixPosition", "matrixWorld", "project", "widthHalf", "width", "heightHalf", "height", "x", "y", "isObjectBehindCamera", "cameraPos", "deltaCamObj", "sub", "camDir", "getWorldDirection", "angleTo", "Math", "PI", "isObjectVisible", "raycaster", "occlude", "elPos", "screenPos", "clone", "set", "setFromCamera", "intersects", "intersectObjects", "length", "intersectionDistance", "distance", "pointDistance", "distanceTo", "ray", "origin", "objectScale", "zoom", "vFOV", "fov", "dist", "scaleFOV", "tan", "objectZIndex", "zIndexRange", "A", "far", "near", "B", "round", "undefined", "epsilon", "value", "abs", "getCSSMatrix", "matrix", "multipliers", "prepend", "matrix3d", "i", "elements", "getCameraCSSMatrix", "getObjectCSSMatrix", "scaleMultipliers", "factor", "f", "isRefObject", "ref", "Html", "forwardRef", "children", "eps", "style", "className", "center", "fullscreen", "portal", "distanceFactor", "sprite", "transform", "onOcclude", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "material", "geometry", "calculatePosition", "as", "wrapperClass", "pointerEvents", "props", "gl", "scene", "events", "viewport", "useState", "document", "createElement", "root", "useRef", "group", "oldZoom", "oldPosition", "transformOuterRef", "transformInnerRef", "target", "current", "connected", "dom<PERSON>lement", "parentNode", "occlusionMeshRef", "isMeshSizeSet", "isRayCastOcclusion", "useMemo", "Array", "isArray", "useLayoutEffect", "zIndex", "floor", "position", "currentRoot", "createRoot", "updateMatrixWorld", "cssText", "vec", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "styles", "top", "left", "transformStyle", "transformInnerStyles", "_root$current", "render", "_root$current2", "visible", "updateWorldMatrix", "isBehindCamera", "raytraceTarget", "map", "item", "previouslyVisible", "isvisible", "display", "<PERSON><PERSON><PERSON><PERSON>", "zRange", "projectionMatrix", "isOrthographicCamera", "bottom", "right", "cameraMatrix", "matrixWorldInverse", "cameraTransform", "transpose", "copyPosition", "scale", "perspective", "clientWidth", "clientHeight", "setScalar", "copy", "divideScalar", "ratio", "w", "h", "ele", "lookAt", "shaders", "vertexShader", "fragmentShader", "side"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,kBAAkB;AAC5C,SAASC,OAAO,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,OAAO;AAC3F,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,MAAMC,EAAE,GAAG,eAAe,IAAIP,OAAO,CAAC,CAAC;AACvC,MAAMQ,EAAE,GAAG,eAAe,IAAIR,OAAO,CAAC,CAAC;AACvC,MAAMS,EAAE,GAAG,eAAe,IAAIT,OAAO,CAAC,CAAC;AACvC,MAAMU,EAAE,GAAG,eAAe,IAAIN,OAAO,CAAC,CAAC;AACvC,SAASO,wBAAwBA,CAACC,EAAE,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAClD,MAAMC,SAAS,GAAGR,EAAE,CAACS,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EAC1DF,SAAS,CAACG,OAAO,CAACL,MAAM,CAAC;EACzB,MAAMM,SAAS,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC;EAChC,MAAMC,UAAU,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC;EAClC,OAAO,CAACP,SAAS,CAACQ,CAAC,GAAGJ,SAAS,GAAGA,SAAS,EAAE,EAAEJ,SAAS,CAACS,CAAC,GAAGH,UAAU,CAAC,GAAGA,UAAU,CAAC;AACxF;AACA,SAASI,oBAAoBA,CAACb,EAAE,EAAEC,MAAM,EAAE;EACxC,MAAME,SAAS,GAAGR,EAAE,CAACS,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EAC1D,MAAMS,SAAS,GAAGlB,EAAE,CAACQ,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;EAC9D,MAAMU,WAAW,GAAGZ,SAAS,CAACa,GAAG,CAACF,SAAS,CAAC;EAC5C,MAAMG,MAAM,GAAGhB,MAAM,CAACiB,iBAAiB,CAACrB,EAAE,CAAC;EAC3C,OAAOkB,WAAW,CAACI,OAAO,CAACF,MAAM,CAAC,GAAGG,IAAI,CAACC,EAAE,GAAG,CAAC;AAClD;AACA,SAASC,eAAeA,CAACtB,EAAE,EAAEC,MAAM,EAAEsB,SAAS,EAAEC,OAAO,EAAE;EACvD,MAAMC,KAAK,GAAG9B,EAAE,CAACS,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EACtD,MAAMqB,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;EAC/BD,SAAS,CAACpB,OAAO,CAACL,MAAM,CAAC;EACzBH,EAAE,CAAC8B,GAAG,CAACF,SAAS,CAACf,CAAC,EAAEe,SAAS,CAACd,CAAC,CAAC;EAChCW,SAAS,CAACM,aAAa,CAAC/B,EAAE,EAAEG,MAAM,CAAC;EACnC,MAAM6B,UAAU,GAAGP,SAAS,CAACQ,gBAAgB,CAACP,OAAO,EAAE,IAAI,CAAC;EAC5D,IAAIM,UAAU,CAACE,MAAM,EAAE;IACrB,MAAMC,oBAAoB,GAAGH,UAAU,CAAC,CAAC,CAAC,CAACI,QAAQ;IACnD,MAAMC,aAAa,GAAGV,KAAK,CAACW,UAAU,CAACb,SAAS,CAACc,GAAG,CAACC,MAAM,CAAC;IAC5D,OAAOH,aAAa,GAAGF,oBAAoB;EAC7C;EACA,OAAO,IAAI;AACb;AACA,SAASM,WAAWA,CAACvC,EAAE,EAAEC,MAAM,EAAE;EAC/B,IAAIA,MAAM,YAAYX,kBAAkB,EAAE;IACxC,OAAOW,MAAM,CAACuC,IAAI;EACpB,CAAC,MAAM,IAAIvC,MAAM,YAAYV,iBAAiB,EAAE;IAC9C,MAAMY,SAAS,GAAGR,EAAE,CAACS,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;IAC1D,MAAMS,SAAS,GAAGlB,EAAE,CAACQ,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAC9D,MAAMoC,IAAI,GAAGxC,MAAM,CAACyC,GAAG,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;IACvC,MAAMsB,IAAI,GAAGxC,SAAS,CAACiC,UAAU,CAACtB,SAAS,CAAC;IAC5C,MAAM8B,QAAQ,GAAG,CAAC,GAAGxB,IAAI,CAACyB,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC,GAAGE,IAAI;IAC9C,OAAO,CAAC,GAAGC,QAAQ;EACrB,CAAC,MAAM;IACL,OAAO,CAAC;EACV;AACF;AACA,SAASE,YAAYA,CAAC9C,EAAE,EAAEC,MAAM,EAAE8C,WAAW,EAAE;EAC7C,IAAI9C,MAAM,YAAYV,iBAAiB,IAAIU,MAAM,YAAYX,kBAAkB,EAAE;IAC/E,MAAMa,SAAS,GAAGR,EAAE,CAACS,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;IAC1D,MAAMS,SAAS,GAAGlB,EAAE,CAACQ,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAC9D,MAAMsC,IAAI,GAAGxC,SAAS,CAACiC,UAAU,CAACtB,SAAS,CAAC;IAC5C,MAAMkC,CAAC,GAAG,CAACD,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,KAAK9C,MAAM,CAACgD,GAAG,GAAGhD,MAAM,CAACiD,IAAI,CAAC;IACxE,MAAMC,CAAC,GAAGJ,WAAW,CAAC,CAAC,CAAC,GAAGC,CAAC,GAAG/C,MAAM,CAACgD,GAAG;IACzC,OAAO7B,IAAI,CAACgC,KAAK,CAACJ,CAAC,GAAGL,IAAI,GAAGQ,CAAC,CAAC;EACjC;EACA,OAAOE,SAAS;AAClB;AACA,MAAMC,OAAO,GAAGC,KAAK,IAAInC,IAAI,CAACoC,GAAG,CAACD,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,KAAK;AAC5D,SAASE,YAAYA,CAACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,GAAG,EAAE,EAAE;EACvD,IAAIC,QAAQ,GAAG,WAAW;EAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC7BD,QAAQ,IAAIP,OAAO,CAACK,WAAW,CAACG,CAAC,CAAC,GAAGJ,MAAM,CAACK,QAAQ,CAACD,CAAC,CAAC,CAAC,IAAIA,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;EACnF;EACA,OAAOF,OAAO,GAAGC,QAAQ;AAC3B;AACA,MAAMG,kBAAkB,GAAG,CAACL,WAAW,IAAI;EACzC,OAAOD,MAAM,IAAID,YAAY,CAACC,MAAM,EAAEC,WAAW,CAAC;AACpD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,MAAMM,kBAAkB,GAAG,CAACC,gBAAgB,IAAI;EAC9C,OAAO,CAACR,MAAM,EAAES,MAAM,KAAKV,YAAY,CAACC,MAAM,EAAEQ,gBAAgB,CAACC,MAAM,CAAC,EAAE,sBAAsB,CAAC;AACnG,CAAC,EAAEC,CAAC,IAAI,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjG,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,SAAS,IAAIA,GAAG;AAC3D;AACA,MAAMC,IAAI,GAAG,eAAerF,KAAK,CAACsF,UAAU,CAAC,CAAC;EAC5CC,QAAQ;EACRC,GAAG,GAAG,KAAK;EACXC,KAAK;EACLC,SAAS;EACThB,OAAO;EACPiB,MAAM;EACNC,UAAU;EACVC,MAAM;EACNC,cAAc;EACdC,MAAM,GAAG,KAAK;EACdC,SAAS,GAAG,KAAK;EACjB1D,OAAO;EACP2D,SAAS;EACTC,UAAU;EACVC,aAAa;EACbC,QAAQ;EACRC,QAAQ;EACRxC,WAAW,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;EAC3ByC,iBAAiB,GAAGzF,wBAAwB;EAC5C0F,EAAE,GAAG,KAAK;EACVC,YAAY;EACZC,aAAa,GAAG,MAAM;EACtB,GAAGC;AACL,CAAC,EAAEtB,GAAG,KAAK;EACT,MAAM;IACJuB,EAAE;IACF5F,MAAM;IACN6F,KAAK;IACL5F,IAAI;IACJqB,SAAS;IACTwE,MAAM;IACNC;EACF,CAAC,GAAGvG,QAAQ,CAAC,CAAC;EACd,MAAM,CAACO,EAAE,CAAC,GAAGd,KAAK,CAAC+G,QAAQ,CAAC,MAAMC,QAAQ,CAACC,aAAa,CAACV,EAAE,CAAC,CAAC;EAC7D,MAAMW,IAAI,GAAGlH,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMC,KAAK,GAAGpH,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAChC,MAAME,OAAO,GAAGrH,KAAK,CAACmH,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAMG,WAAW,GAAGtH,KAAK,CAACmH,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAMI,iBAAiB,GAAGvH,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMK,iBAAiB,GAAGxH,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAC5C;EACA,MAAMM,MAAM,GAAG,CAAC5B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6B,OAAO,KAAKb,MAAM,CAACc,SAAS,IAAIhB,EAAE,CAACiB,UAAU,CAACC,UAAU;EACzG,MAAMC,gBAAgB,GAAG9H,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMY,aAAa,GAAG/H,KAAK,CAACmH,MAAM,CAAC,KAAK,CAAC;EACzC,MAAMa,kBAAkB,GAAGhI,KAAK,CAACiI,OAAO,CAAC,MAAM;IAC7C,OAAO3F,OAAO,IAAIA,OAAO,KAAK,UAAU,IAAI4F,KAAK,CAACC,OAAO,CAAC7F,OAAO,CAAC,IAAIA,OAAO,CAACQ,MAAM,IAAIqC,WAAW,CAAC7C,OAAO,CAAC,CAAC,CAAC,CAAC;EACjH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACbtC,KAAK,CAACoI,eAAe,CAAC,MAAM;IAC1B,MAAMtH,EAAE,GAAG6F,EAAE,CAACiB,UAAU;IACxB,IAAItF,OAAO,IAAIA,OAAO,KAAK,UAAU,EAAE;MACrCxB,EAAE,CAAC2E,KAAK,CAAC4C,MAAM,GAAG,GAAGnG,IAAI,CAACoG,KAAK,CAACzE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MACrD/C,EAAE,CAAC2E,KAAK,CAAC8C,QAAQ,GAAG,UAAU;MAC9BzH,EAAE,CAAC2E,KAAK,CAACgB,aAAa,GAAG,MAAM;IACjC,CAAC,MAAM;MACL3F,EAAE,CAAC2E,KAAK,CAAC4C,MAAM,GAAG,IAAI;MACtBvH,EAAE,CAAC2E,KAAK,CAAC8C,QAAQ,GAAG,IAAI;MACxBzH,EAAE,CAAC2E,KAAK,CAACgB,aAAa,GAAG,IAAI;IAC/B;EACF,CAAC,EAAE,CAACnE,OAAO,CAAC,CAAC;EACbtC,KAAK,CAACoI,eAAe,CAAC,MAAM;IAC1B,IAAIhB,KAAK,CAACM,OAAO,EAAE;MACjB,MAAMc,WAAW,GAAGtB,IAAI,CAACQ,OAAO,GAAGzH,QAAQ,CAACwI,UAAU,CAAC3H,EAAE,CAAC;MAC1D8F,KAAK,CAAC8B,iBAAiB,CAAC,CAAC;MACzB,IAAI1C,SAAS,EAAE;QACblF,EAAE,CAAC2E,KAAK,CAACkD,OAAO,GAAG,qEAAqE;MAC1F,CAAC,MAAM;QACL,MAAMC,GAAG,GAAGtC,iBAAiB,CAACc,KAAK,CAACM,OAAO,EAAE3G,MAAM,EAAEC,IAAI,CAAC;QAC1DF,EAAE,CAAC2E,KAAK,CAACkD,OAAO,GAAG,wDAAwDC,GAAG,CAAC,CAAC,CAAC,MAAMA,GAAG,CAAC,CAAC,CAAC,6BAA6B;MAC5H;MACA,IAAInB,MAAM,EAAE;QACV,IAAI/C,OAAO,EAAE+C,MAAM,CAAC/C,OAAO,CAAC5D,EAAE,CAAC,CAAC,KAAK2G,MAAM,CAACoB,WAAW,CAAC/H,EAAE,CAAC;MAC7D;MACA,OAAO,MAAM;QACX,IAAI2G,MAAM,EAAEA,MAAM,CAACqB,WAAW,CAAChI,EAAE,CAAC;QAClC0H,WAAW,CAACO,OAAO,CAAC,CAAC;MACvB,CAAC;IACH;EACF,CAAC,EAAE,CAACtB,MAAM,EAAEzB,SAAS,CAAC,CAAC;EACvBhG,KAAK,CAACoI,eAAe,CAAC,MAAM;IAC1B,IAAI5B,YAAY,EAAE1F,EAAE,CAAC4E,SAAS,GAAGc,YAAY;EAC/C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB,MAAMwC,MAAM,GAAGhJ,KAAK,CAACiI,OAAO,CAAC,MAAM;IACjC,IAAIjC,SAAS,EAAE;MACb,OAAO;QACLuC,QAAQ,EAAE,UAAU;QACpBU,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACP5H,KAAK,EAAEN,IAAI,CAACM,KAAK;QACjBE,MAAM,EAAER,IAAI,CAACQ,MAAM;QACnB2H,cAAc,EAAE,aAAa;QAC7B1C,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL8B,QAAQ,EAAE,UAAU;QACpBvC,SAAS,EAAEL,MAAM,GAAG,0BAA0B,GAAG,MAAM;QACvD,IAAIC,UAAU,IAAI;UAChBqD,GAAG,EAAE,CAACjI,IAAI,CAACQ,MAAM,GAAG,CAAC;UACrB0H,IAAI,EAAE,CAAClI,IAAI,CAACM,KAAK,GAAG,CAAC;UACrBA,KAAK,EAAEN,IAAI,CAACM,KAAK;UACjBE,MAAM,EAAER,IAAI,CAACQ;QACf,CAAC,CAAC;QACF,GAAGiE;MACL,CAAC;IACH;EACF,CAAC,EAAE,CAACA,KAAK,EAAEE,MAAM,EAAEC,UAAU,EAAE5E,IAAI,EAAEgF,SAAS,CAAC,CAAC;EAChD,MAAMoD,oBAAoB,GAAGpJ,KAAK,CAACiI,OAAO,CAAC,OAAO;IAChDM,QAAQ,EAAE,UAAU;IACpB9B;EACF,CAAC,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACpBzG,KAAK,CAACoI,eAAe,CAAC,MAAM;IAC1BL,aAAa,CAACL,OAAO,GAAG,KAAK;IAC7B,IAAI1B,SAAS,EAAE;MACb,IAAIqD,aAAa;MACjB,CAACA,aAAa,GAAGnC,IAAI,CAACQ,OAAO,KAAK,IAAI,IAAI2B,aAAa,CAACC,MAAM,CAAC,aAAatJ,KAAK,CAACiH,aAAa,CAAC,KAAK,EAAE;QACrG7B,GAAG,EAAEmC,iBAAiB;QACtB9B,KAAK,EAAEuD;MACT,CAAC,EAAE,aAAahJ,KAAK,CAACiH,aAAa,CAAC,KAAK,EAAE;QACzC7B,GAAG,EAAEoC,iBAAiB;QACtB/B,KAAK,EAAE2D;MACT,CAAC,EAAE,aAAapJ,KAAK,CAACiH,aAAa,CAAC,KAAK,EAAE;QACzC7B,GAAG,EAAEA,GAAG;QACRM,SAAS,EAAEA,SAAS;QACpBD,KAAK,EAAEA,KAAK;QACZF,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAIgE,cAAc;MAClB,CAACA,cAAc,GAAGrC,IAAI,CAACQ,OAAO,KAAK,IAAI,IAAI6B,cAAc,CAACD,MAAM,CAAC,aAAatJ,KAAK,CAACiH,aAAa,CAAC,KAAK,EAAE;QACvG7B,GAAG,EAAEA,GAAG;QACRK,KAAK,EAAEuD,MAAM;QACbtD,SAAS,EAAEA,SAAS;QACpBH,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACF,MAAMiE,OAAO,GAAGxJ,KAAK,CAACmH,MAAM,CAAC,IAAI,CAAC;EAClC3G,QAAQ,CAACmG,EAAE,IAAI;IACb,IAAIS,KAAK,CAACM,OAAO,EAAE;MACjB3G,MAAM,CAAC2H,iBAAiB,CAAC,CAAC;MAC1BtB,KAAK,CAACM,OAAO,CAAC+B,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;MAC5C,MAAMb,GAAG,GAAG5C,SAAS,GAAGsB,WAAW,CAACI,OAAO,GAAGpB,iBAAiB,CAACc,KAAK,CAACM,OAAO,EAAE3G,MAAM,EAAEC,IAAI,CAAC;MAC5F,IAAIgF,SAAS,IAAI9D,IAAI,CAACoC,GAAG,CAAC+C,OAAO,CAACK,OAAO,GAAG3G,MAAM,CAACuC,IAAI,CAAC,GAAGkC,GAAG,IAAItD,IAAI,CAACoC,GAAG,CAACgD,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC,GAAGkB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,GAAG,IAAItD,IAAI,CAACoC,GAAG,CAACgD,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC,GAAGkB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,GAAG,EAAE;QACpK,MAAMkE,cAAc,GAAG/H,oBAAoB,CAACyF,KAAK,CAACM,OAAO,EAAE3G,MAAM,CAAC;QAClE,IAAI4I,cAAc,GAAG,KAAK;QAC1B,IAAI3B,kBAAkB,EAAE;UACtB,IAAIE,KAAK,CAACC,OAAO,CAAC7F,OAAO,CAAC,EAAE;YAC1BqH,cAAc,GAAGrH,OAAO,CAACsH,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnC,OAAO,CAAC;UACpD,CAAC,MAAM,IAAIpF,OAAO,KAAK,UAAU,EAAE;YACjCqH,cAAc,GAAG,CAAC/C,KAAK,CAAC;UAC1B;QACF;QACA,MAAMkD,iBAAiB,GAAGN,OAAO,CAAC9B,OAAO;QACzC,IAAIiC,cAAc,EAAE;UAClB,MAAMI,SAAS,GAAG3H,eAAe,CAACgF,KAAK,CAACM,OAAO,EAAE3G,MAAM,EAAEsB,SAAS,EAAEsH,cAAc,CAAC;UACnFH,OAAO,CAAC9B,OAAO,GAAGqC,SAAS,IAAI,CAACL,cAAc;QAChD,CAAC,MAAM;UACLF,OAAO,CAAC9B,OAAO,GAAG,CAACgC,cAAc;QACnC;QACA,IAAII,iBAAiB,KAAKN,OAAO,CAAC9B,OAAO,EAAE;UACzC,IAAIzB,SAAS,EAAEA,SAAS,CAAC,CAACuD,OAAO,CAAC9B,OAAO,CAAC,CAAC,KAAK5G,EAAE,CAAC2E,KAAK,CAACuE,OAAO,GAAGR,OAAO,CAAC9B,OAAO,GAAG,OAAO,GAAG,MAAM;QACvG;QACA,MAAMuC,SAAS,GAAG/H,IAAI,CAACoG,KAAK,CAACzE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChD,MAAMqG,MAAM,GAAG5H,OAAO,GAAG0F,kBAAkB,CAAC;QAAA,EAC1C,CAACnE,WAAW,CAAC,CAAC,CAAC,EAAEoG,SAAS,CAAC,GAAG,CAACA,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,GAAGpG,WAAW;QAChE/C,EAAE,CAAC2E,KAAK,CAAC4C,MAAM,GAAG,GAAGzE,YAAY,CAACwD,KAAK,CAACM,OAAO,EAAE3G,MAAM,EAAEmJ,MAAM,CAAC,EAAE;QAClE,IAAIlE,SAAS,EAAE;UACb,MAAM,CAAC3E,SAAS,EAAEE,UAAU,CAAC,GAAG,CAACP,IAAI,CAACM,KAAK,GAAG,CAAC,EAAEN,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC;UACjE,MAAMgC,GAAG,GAAGzC,MAAM,CAACoJ,gBAAgB,CAACtF,QAAQ,CAAC,CAAC,CAAC,GAAGtD,UAAU;UAC5D,MAAM;YACJ6I,oBAAoB;YACpBnB,GAAG;YACHC,IAAI;YACJmB,MAAM;YACNC;UACF,CAAC,GAAGvJ,MAAM;UACV,MAAMwJ,YAAY,GAAGzF,kBAAkB,CAAC/D,MAAM,CAACyJ,kBAAkB,CAAC;UAClE,MAAMC,eAAe,GAAGL,oBAAoB,GAAG,SAAS5G,GAAG,cAAcY,OAAO,CAAC,EAAEkG,KAAK,GAAGpB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM9E,OAAO,CAAC,CAAC6E,GAAG,GAAGoB,MAAM,IAAI,CAAC,CAAC,KAAK,GAAG,cAAc7G,GAAG,KAAK;UACpK,IAAIgB,MAAM,GAAG4C,KAAK,CAACM,OAAO,CAACvG,WAAW;UACtC,IAAI4E,MAAM,EAAE;YACVvB,MAAM,GAAGzD,MAAM,CAACyJ,kBAAkB,CAAC/H,KAAK,CAAC,CAAC,CAACiI,SAAS,CAAC,CAAC,CAACC,YAAY,CAACnG,MAAM,CAAC,CAACoG,KAAK,CAACxD,KAAK,CAACM,OAAO,CAACkD,KAAK,CAAC;YACtGpG,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YACjEL,MAAM,CAACK,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;UACzB;UACA/D,EAAE,CAAC2E,KAAK,CAACnE,KAAK,GAAGN,IAAI,CAACM,KAAK,GAAG,IAAI;UAClCR,EAAE,CAAC2E,KAAK,CAACjE,MAAM,GAAGR,IAAI,CAACQ,MAAM,GAAG,IAAI;UACpCV,EAAE,CAAC2E,KAAK,CAACoF,WAAW,GAAGT,oBAAoB,GAAG,EAAE,GAAG,GAAG5G,GAAG,IAAI;UAC7D,IAAI+D,iBAAiB,CAACG,OAAO,IAAIF,iBAAiB,CAACE,OAAO,EAAE;YAC1DH,iBAAiB,CAACG,OAAO,CAACjC,KAAK,CAACO,SAAS,GAAG,GAAGyE,eAAe,GAAGF,YAAY,aAAalJ,SAAS,MAAME,UAAU,KAAK;YACxHiG,iBAAiB,CAACE,OAAO,CAACjC,KAAK,CAACO,SAAS,GAAGjB,kBAAkB,CAACP,MAAM,EAAE,CAAC,IAAI,CAACsB,cAAc,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;UAC5G;QACF,CAAC,MAAM;UACL,MAAM8E,KAAK,GAAG9E,cAAc,KAAK3B,SAAS,GAAG,CAAC,GAAGd,WAAW,CAAC+D,KAAK,CAACM,OAAO,EAAE3G,MAAM,CAAC,GAAG+E,cAAc;UACpGhF,EAAE,CAAC2E,KAAK,CAACO,SAAS,GAAG,eAAe4C,GAAG,CAAC,CAAC,CAAC,MAAMA,GAAG,CAAC,CAAC,CAAC,eAAegC,KAAK,GAAG;QAC/E;QACAtD,WAAW,CAACI,OAAO,GAAGkB,GAAG;QACzBvB,OAAO,CAACK,OAAO,GAAG3G,MAAM,CAACuC,IAAI;MAC/B;IACF;IACA,IAAI,CAAC0E,kBAAkB,IAAIF,gBAAgB,CAACJ,OAAO,IAAI,CAACK,aAAa,CAACL,OAAO,EAAE;MAC7E,IAAI1B,SAAS,EAAE;QACb,IAAIuB,iBAAiB,CAACG,OAAO,EAAE;UAC7B,MAAM5G,EAAE,GAAGyG,iBAAiB,CAACG,OAAO,CAACnC,QAAQ,CAAC,CAAC,CAAC;UAChD,IAAIzE,EAAE,IAAI,IAAI,IAAIA,EAAE,CAACgK,WAAW,IAAIhK,EAAE,IAAI,IAAI,IAAIA,EAAE,CAACiK,YAAY,EAAE;YACjE,MAAM;cACJX;YACF,CAAC,GAAGrJ,MAAM;YACV,IAAIqJ,oBAAoB,IAAI/D,QAAQ,EAAE;cACpC,IAAIK,KAAK,CAACkE,KAAK,EAAE;gBACf,IAAI,CAAC1C,KAAK,CAACC,OAAO,CAACzB,KAAK,CAACkE,KAAK,CAAC,EAAE;kBAC/B9C,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACI,SAAS,CAAC,CAAC,GAAGtE,KAAK,CAACkE,KAAK,CAAC;gBAC3D,CAAC,MAAM,IAAIlE,KAAK,CAACkE,KAAK,YAAY1K,OAAO,EAAE;kBACzC4H,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACK,IAAI,CAACvE,KAAK,CAACkE,KAAK,CAACnI,KAAK,CAAC,CAAC,CAACyI,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC1E,CAAC,MAAM;kBACLpD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAAClI,GAAG,CAAC,CAAC,GAAGgE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGlE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGlE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChG;cACF;YACF,CAAC,MAAM;cACL,MAAMO,KAAK,GAAG,CAACrF,cAAc,IAAI,EAAE,IAAI,GAAG;cAC1C,MAAMsF,CAAC,GAAGtK,EAAE,CAACgK,WAAW,GAAGK,KAAK;cAChC,MAAME,CAAC,GAAGvK,EAAE,CAACiK,YAAY,GAAGI,KAAK;cACjCrD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAAClI,GAAG,CAAC0I,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;YAC7C;YACAtD,aAAa,CAACL,OAAO,GAAG,IAAI;UAC9B;QACF;MACF,CAAC,MAAM;QACL,MAAM4D,GAAG,GAAGxK,EAAE,CAACyE,QAAQ,CAAC,CAAC,CAAC;QAC1B,IAAI+F,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACR,WAAW,IAAIQ,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACP,YAAY,EAAE;UACrE,MAAMI,KAAK,GAAG,CAAC,GAAGrE,QAAQ,CAAC7B,MAAM;UACjC,MAAMmG,CAAC,GAAGE,GAAG,CAACR,WAAW,GAAGK,KAAK;UACjC,MAAME,CAAC,GAAGC,GAAG,CAACP,YAAY,GAAGI,KAAK;UAClCrD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAAClI,GAAG,CAAC0I,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;UAC3CtD,aAAa,CAACL,OAAO,GAAG,IAAI;QAC9B;QACAI,gBAAgB,CAACJ,OAAO,CAAC6D,MAAM,CAAC5E,EAAE,CAAC5F,MAAM,CAACwH,QAAQ,CAAC;MACrD;IACF;EACF,CAAC,CAAC;EACF,MAAMiD,OAAO,GAAGxL,KAAK,CAACiI,OAAO,CAAC,OAAO;IACnCwD,YAAY,EAAE,CAACzF,SAAS,GAAG,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAAG7B,SAAS;IACfuH,cAAc,EAAE,UAAU;AAC9B;AACA;AACA;AACA;EACE,CAAC,CAAC,EAAE,CAAC1F,SAAS,CAAC,CAAC;EAChB,OAAO,aAAahG,KAAK,CAACiH,aAAa,CAAC,OAAO,EAAElH,QAAQ,CAAC,CAAC,CAAC,EAAE2G,KAAK,EAAE;IACnEtB,GAAG,EAAEgC;EACP,CAAC,CAAC,EAAE9E,OAAO,IAAI,CAAC0F,kBAAkB,IAAI,aAAahI,KAAK,CAACiH,aAAa,CAAC,MAAM,EAAE;IAC7Ef,UAAU,EAAEA,UAAU;IACtBC,aAAa,EAAEA,aAAa;IAC5Bf,GAAG,EAAE0C;EACP,CAAC,EAAEzB,QAAQ,IAAI,aAAarG,KAAK,CAACiH,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAEb,QAAQ,IAAI,aAAapG,KAAK,CAACiH,aAAa,CAAC,gBAAgB,EAAE;IACrI0E,IAAI,EAAExL,UAAU;IAChBsL,YAAY,EAAED,OAAO,CAACC,YAAY;IAClCC,cAAc,EAAEF,OAAO,CAACE;EAC1B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,SAASrG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}