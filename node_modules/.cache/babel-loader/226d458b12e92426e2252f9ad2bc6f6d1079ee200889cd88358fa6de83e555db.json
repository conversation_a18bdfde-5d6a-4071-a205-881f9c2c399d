{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { AudioListener, AudioLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nconst PositionalAudio = /* @__PURE__ */React.forwardRef(({\n  url,\n  distance = 1,\n  loop = true,\n  autoplay,\n  ...props\n}, ref) => {\n  const sound = React.useRef(null);\n  React.useImperativeHandle(ref, () => sound.current, []);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const [listener] = React.useState(() => new AudioListener());\n  const buffer = useLoader(AudioLoader, url);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    if (_sound) {\n      _sound.setBuffer(buffer);\n      _sound.setRefDistance(distance);\n      _sound.setLoop(loop);\n      if (autoplay && !_sound.isPlaying) _sound.play();\n    }\n  }, [buffer, camera, distance, loop]);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    camera.add(listener);\n    return () => {\n      camera.remove(listener);\n      if (_sound) {\n        if (_sound.isPlaying) _sound.stop();\n        if (_sound.source && _sound.source._connected) _sound.disconnect();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"positionalAudio\", _extends({\n    ref: sound,\n    args: [listener]\n  }, props));\n});\nexport { PositionalAudio };", "map": {"version": 3, "names": ["_extends", "React", "AudioListener", "AudioLoader", "useThree", "useLoader", "PositionalAudio", "forwardRef", "url", "distance", "loop", "autoplay", "props", "ref", "sound", "useRef", "useImperativeHandle", "current", "camera", "listener", "useState", "buffer", "useEffect", "_sound", "<PERSON><PERSON><PERSON><PERSON>", "setRefDistance", "setLoop", "isPlaying", "play", "add", "remove", "stop", "source", "_connected", "disconnect", "createElement", "args"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/PositionalAudio.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { AudioListener, AudioLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\n\nconst PositionalAudio = /* @__PURE__ */React.forwardRef(({\n  url,\n  distance = 1,\n  loop = true,\n  autoplay,\n  ...props\n}, ref) => {\n  const sound = React.useRef(null);\n  React.useImperativeHandle(ref, () => sound.current, []);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const [listener] = React.useState(() => new AudioListener());\n  const buffer = useLoader(AudioLoader, url);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    if (_sound) {\n      _sound.setBuffer(buffer);\n      _sound.setRefDistance(distance);\n      _sound.setLoop(loop);\n      if (autoplay && !_sound.isPlaying) _sound.play();\n    }\n  }, [buffer, camera, distance, loop]);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    camera.add(listener);\n    return () => {\n      camera.remove(listener);\n      if (_sound) {\n        if (_sound.isPlaying) _sound.stop();\n        if (_sound.source && _sound.source._connected) _sound.disconnect();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"positionalAudio\", _extends({\n    ref: sound,\n    args: [listener]\n  }, props));\n});\n\nexport { PositionalAudio };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAExD,MAAMC,eAAe,GAAG,eAAeL,KAAK,CAACM,UAAU,CAAC,CAAC;EACvDC,GAAG;EACHC,QAAQ,GAAG,CAAC;EACZC,IAAI,GAAG,IAAI;EACXC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,KAAK,GAAGb,KAAK,CAACc,MAAM,CAAC,IAAI,CAAC;EAChCd,KAAK,CAACe,mBAAmB,CAACH,GAAG,EAAE,MAAMC,KAAK,CAACG,OAAO,EAAE,EAAE,CAAC;EACvD,MAAMC,MAAM,GAAGd,QAAQ,CAAC,CAAC;IACvBc;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAM,CAACC,QAAQ,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,MAAM,IAAIlB,aAAa,CAAC,CAAC,CAAC;EAC5D,MAAMmB,MAAM,GAAGhB,SAAS,CAACF,WAAW,EAAEK,GAAG,CAAC;EAC1CP,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAGT,KAAK,CAACG,OAAO;IAC5B,IAAIM,MAAM,EAAE;MACVA,MAAM,CAACC,SAAS,CAACH,MAAM,CAAC;MACxBE,MAAM,CAACE,cAAc,CAAChB,QAAQ,CAAC;MAC/Bc,MAAM,CAACG,OAAO,CAAChB,IAAI,CAAC;MACpB,IAAIC,QAAQ,IAAI,CAACY,MAAM,CAACI,SAAS,EAAEJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACP,MAAM,EAAEH,MAAM,EAAET,QAAQ,EAAEC,IAAI,CAAC,CAAC;EACpCT,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAGT,KAAK,CAACG,OAAO;IAC5BC,MAAM,CAACW,GAAG,CAACV,QAAQ,CAAC;IACpB,OAAO,MAAM;MACXD,MAAM,CAACY,MAAM,CAACX,QAAQ,CAAC;MACvB,IAAII,MAAM,EAAE;QACV,IAAIA,MAAM,CAACI,SAAS,EAAEJ,MAAM,CAACQ,IAAI,CAAC,CAAC;QACnC,IAAIR,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACC,UAAU,EAAEV,MAAM,CAACW,UAAU,CAAC,CAAC;MACpE;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAajC,KAAK,CAACkC,aAAa,CAAC,iBAAiB,EAAEnC,QAAQ,CAAC;IAClEa,GAAG,EAAEC,KAAK;IACVsB,IAAI,EAAE,CAACjB,QAAQ;EACjB,CAAC,EAAEP,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}