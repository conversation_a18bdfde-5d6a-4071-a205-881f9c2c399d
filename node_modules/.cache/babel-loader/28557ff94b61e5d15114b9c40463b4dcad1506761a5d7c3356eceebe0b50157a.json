{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/HomePage.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport Layout from \"../components/Layout\";\nimport HomeDownload from \"../containers/Home/Download\";\nimport TechStack from \"../containers/Home/TechStack\";\nimport HomeHeader from \"../containers/Home/Header\";\nimport HomeWorks from \"../containers/Home/Works\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  useEffect(() => {\n    const targetId = localStorage.getItem(\"scrollTo\");\n    if (targetId) {\n      localStorage.removeItem(\"scrollTo\");\n      setTimeout(() => {\n        const element = document.getElementById(targetId);\n        if (element) {\n          element.scrollIntoView({\n            behavior: \"smooth\",\n            block: \"start\"\n          });\n        }\n      }, 100);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(HomeHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TechStack, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HomeWorks, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HomeDownload, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["useEffect", "Layout", "HomeDownload", "TechStack", "HomeHeader", "HomeWorks", "jsxDEV", "_jsxDEV", "HomePage", "_s", "targetId", "localStorage", "getItem", "removeItem", "setTimeout", "element", "document", "getElementById", "scrollIntoView", "behavior", "block", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/HomePage.tsx"], "sourcesContent": ["import { useEffect } from \"react\";\nimport Layout from \"../components/Layout\";\nimport HomeDownload from \"../containers/Home/Download\";\nimport TechStack from \"../containers/Home/TechStack\";\nimport HomeHeader from \"../containers/Home/Header\";\nimport HomeWorks from \"../containers/Home/Works\";\n\nconst HomePage = () => {\n  useEffect(() => {\n    const targetId = localStorage.getItem(\"scrollTo\");\n    if (targetId) {\n      localStorage.removeItem(\"scrollTo\");\n      setTimeout(() => {\n        const element = document.getElementById(targetId);\n        if (element) {\n          element.scrollIntoView({ behavior: \"smooth\", block: \"start\" });\n        }\n      }, 100);\n    }\n  }, []);\n\n  return (\n    <Layout>\n      <HomeHeader />\n      <TechStack />\n      <HomeWorks />\n      <HomeDownload />\n    </Layout>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrBT,SAAS,CAAC,MAAM;IACd,MAAMU,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,IAAIF,QAAQ,EAAE;MACZC,YAAY,CAACE,UAAU,CAAC,UAAU,CAAC;MACnCC,UAAU,CAAC,MAAM;QACf,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACP,QAAQ,CAAC;QACjD,IAAIK,OAAO,EAAE;UACXA,OAAO,CAACG,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAQ,CAAC,CAAC;QAChE;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEb,OAAA,CAACN,MAAM;IAAAoB,QAAA,gBACLd,OAAA,CAACH,UAAU;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdlB,OAAA,CAACJ,SAAS;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACblB,OAAA,CAACF,SAAS;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACblB,OAAA,CAACL,YAAY;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAChB,EAAA,CAtBID,QAAQ;AAAAkB,EAAA,GAARlB,QAAQ;AAwBd,eAAeA,QAAQ;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}