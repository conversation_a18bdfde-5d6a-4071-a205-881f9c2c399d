{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { AsciiEffect } from 'three-stdlib';\nfunction AsciiRenderer({\n  renderIndex = 1,\n  bgColor = 'black',\n  fgColor = 'white',\n  characters = ' .:-+*=%@#',\n  invert = true,\n  color = false,\n  resolution = 0.15\n}) {\n  // Reactive state\n  const {\n    size,\n    gl,\n    scene,\n    camera\n  } = useThree();\n\n  // Create effect\n  const effect = React.useMemo(() => {\n    const effect = new AsciiEffect(gl, characters, {\n      invert,\n      color,\n      resolution\n    });\n    effect.domElement.style.position = 'absolute';\n    effect.domElement.style.top = '0px';\n    effect.domElement.style.left = '0px';\n    effect.domElement.style.pointerEvents = 'none';\n    return effect;\n  }, [characters, invert, color, resolution]);\n\n  // Styling\n  React.useLayoutEffect(() => {\n    effect.domElement.style.color = fgColor;\n    effect.domElement.style.backgroundColor = bgColor;\n  }, [fgColor, bgColor]);\n\n  // Append on mount, remove on unmount\n  React.useEffect(() => {\n    gl.domElement.style.opacity = '0';\n    gl.domElement.parentNode.appendChild(effect.domElement);\n    return () => {\n      gl.domElement.style.opacity = '1';\n      gl.domElement.parentNode.removeChild(effect.domElement);\n    };\n  }, [effect]);\n\n  // Set size\n  React.useEffect(() => {\n    effect.setSize(size.width, size.height);\n  }, [effect, size]);\n\n  // Take over render-loop (that is what the index is for)\n  useFrame(state => {\n    effect.render(scene, camera);\n  }, renderIndex);\n\n  // return something to not break type signatures\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\nexport { AsciiRenderer };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "AsciiEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIndex", "bgColor", "fgColor", "characters", "invert", "color", "resolution", "size", "gl", "scene", "camera", "effect", "useMemo", "dom<PERSON>lement", "style", "position", "top", "left", "pointerEvents", "useLayoutEffect", "backgroundColor", "useEffect", "opacity", "parentNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSize", "width", "height", "state", "render", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/AsciiRenderer.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { AsciiEffect } from 'three-stdlib';\n\nfunction AsciiRenderer({\n  renderIndex = 1,\n  bgColor = 'black',\n  fgColor = 'white',\n  characters = ' .:-+*=%@#',\n  invert = true,\n  color = false,\n  resolution = 0.15\n}) {\n  // Reactive state\n  const {\n    size,\n    gl,\n    scene,\n    camera\n  } = useThree();\n\n  // Create effect\n  const effect = React.useMemo(() => {\n    const effect = new AsciiEffect(gl, characters, {\n      invert,\n      color,\n      resolution\n    });\n    effect.domElement.style.position = 'absolute';\n    effect.domElement.style.top = '0px';\n    effect.domElement.style.left = '0px';\n    effect.domElement.style.pointerEvents = 'none';\n    return effect;\n  }, [characters, invert, color, resolution]);\n\n  // Styling\n  React.useLayoutEffect(() => {\n    effect.domElement.style.color = fgColor;\n    effect.domElement.style.backgroundColor = bgColor;\n  }, [fgColor, bgColor]);\n\n  // Append on mount, remove on unmount\n  React.useEffect(() => {\n    gl.domElement.style.opacity = '0';\n    gl.domElement.parentNode.appendChild(effect.domElement);\n    return () => {\n      gl.domElement.style.opacity = '1';\n      gl.domElement.parentNode.removeChild(effect.domElement);\n    };\n  }, [effect]);\n\n  // Set size\n  React.useEffect(() => {\n    effect.setSize(size.width, size.height);\n  }, [effect, size]);\n\n  // Take over render-loop (that is what the index is for)\n  useFrame(state => {\n    effect.render(scene, camera);\n  }, renderIndex);\n\n  // return something to not break type signatures\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\n\nexport { AsciiRenderer };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,WAAW,QAAQ,cAAc;AAE1C,SAASC,aAAaA,CAAC;EACrBC,WAAW,GAAG,CAAC;EACfC,OAAO,GAAG,OAAO;EACjBC,OAAO,GAAG,OAAO;EACjBC,UAAU,GAAG,YAAY;EACzBC,MAAM,GAAG,IAAI;EACbC,KAAK,GAAG,KAAK;EACbC,UAAU,GAAG;AACf,CAAC,EAAE;EACD;EACA,MAAM;IACJC,IAAI;IACJC,EAAE;IACFC,KAAK;IACLC;EACF,CAAC,GAAGd,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMe,MAAM,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAG,IAAIb,WAAW,CAACU,EAAE,EAAEL,UAAU,EAAE;MAC7CC,MAAM;MACNC,KAAK;MACLC;IACF,CAAC,CAAC;IACFK,MAAM,CAACE,UAAU,CAACC,KAAK,CAACC,QAAQ,GAAG,UAAU;IAC7CJ,MAAM,CAACE,UAAU,CAACC,KAAK,CAACE,GAAG,GAAG,KAAK;IACnCL,MAAM,CAACE,UAAU,CAACC,KAAK,CAACG,IAAI,GAAG,KAAK;IACpCN,MAAM,CAACE,UAAU,CAACC,KAAK,CAACI,aAAa,GAAG,MAAM;IAC9C,OAAOP,MAAM;EACf,CAAC,EAAE,CAACR,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC,CAAC;;EAE3C;EACAX,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1BR,MAAM,CAACE,UAAU,CAACC,KAAK,CAACT,KAAK,GAAGH,OAAO;IACvCS,MAAM,CAACE,UAAU,CAACC,KAAK,CAACM,eAAe,GAAGnB,OAAO;EACnD,CAAC,EAAE,CAACC,OAAO,EAAED,OAAO,CAAC,CAAC;;EAEtB;EACAN,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpBb,EAAE,CAACK,UAAU,CAACC,KAAK,CAACQ,OAAO,GAAG,GAAG;IACjCd,EAAE,CAACK,UAAU,CAACU,UAAU,CAACC,WAAW,CAACb,MAAM,CAACE,UAAU,CAAC;IACvD,OAAO,MAAM;MACXL,EAAE,CAACK,UAAU,CAACC,KAAK,CAACQ,OAAO,GAAG,GAAG;MACjCd,EAAE,CAACK,UAAU,CAACU,UAAU,CAACE,WAAW,CAACd,MAAM,CAACE,UAAU,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAACF,MAAM,CAAC,CAAC;;EAEZ;EACAhB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpBV,MAAM,CAACe,OAAO,CAACnB,IAAI,CAACoB,KAAK,EAAEpB,IAAI,CAACqB,MAAM,CAAC;EACzC,CAAC,EAAE,CAACjB,MAAM,EAAEJ,IAAI,CAAC,CAAC;;EAElB;EACAV,QAAQ,CAACgC,KAAK,IAAI;IAChBlB,MAAM,CAACmB,MAAM,CAACrB,KAAK,EAAEC,MAAM,CAAC;EAC9B,CAAC,EAAEV,WAAW,CAAC;;EAEf;EACA,OAAO,aAAaL,KAAK,CAACoC,aAAa,CAACpC,KAAK,CAACqC,QAAQ,EAAE,IAAI,CAAC;AAC/D;AAEA,SAASjC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}