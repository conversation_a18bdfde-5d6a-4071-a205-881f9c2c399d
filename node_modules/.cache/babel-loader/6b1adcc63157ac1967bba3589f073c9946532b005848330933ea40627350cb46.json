{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Color, Vector2, WebGLRenderTarget, HalfFloatType, NearestFilter, DepthTexture, UnsignedShortType, MeshDepthMaterial, RGBADepthPacking, NoBlending, MeshNormalMaterial, ShaderMaterial, UniformsUtils, CustomBlending, DstColorFactor, ZeroFactor, AddEquation, DstAlphaFactor } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { SAOShader } from \"../shaders/SAOShader.js\";\nimport { DepthLimitedBlurShader, BlurShaderUtils } from \"../shaders/DepthLimitedBlurShader.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nimport { UnpackDepthRGBAShader } from \"../shaders/UnpackDepthRGBAShader.js\";\nconst SAOPass = /* @__PURE__ */(() => {\n  class SAOPass2 extends Pass {\n    constructor(scene, camera, useDepthTexture = false, useNormals = false, resolution = new Vector2(256, 256)) {\n      super();\n      this.scene = scene;\n      this.camera = camera;\n      this.clear = true;\n      this.needsSwap = false;\n      this.supportsDepthTextureExtension = useDepthTexture;\n      this.supportsNormalTexture = useNormals;\n      this.originalClearColor = new Color();\n      this._oldClearColor = new Color();\n      this.oldClearAlpha = 1;\n      this.params = {\n        output: 0,\n        saoBias: 0.5,\n        saoIntensity: 0.18,\n        saoScale: 1,\n        saoKernelRadius: 100,\n        saoMinResolution: 0,\n        saoBlur: true,\n        saoBlurRadius: 8,\n        saoBlurStdDev: 4,\n        saoBlurDepthCutoff: 0.01\n      };\n      this.resolution = new Vector2(resolution.x, resolution.y);\n      this.saoRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, {\n        type: HalfFloatType\n      });\n      this.blurIntermediateRenderTarget = this.saoRenderTarget.clone();\n      this.beautyRenderTarget = this.saoRenderTarget.clone();\n      this.normalRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType\n      });\n      this.depthRenderTarget = this.normalRenderTarget.clone();\n      let depthTexture;\n      if (this.supportsDepthTextureExtension) {\n        depthTexture = new DepthTexture();\n        depthTexture.type = UnsignedShortType;\n        this.beautyRenderTarget.depthTexture = depthTexture;\n        this.beautyRenderTarget.depthBuffer = true;\n      }\n      this.depthMaterial = new MeshDepthMaterial();\n      this.depthMaterial.depthPacking = RGBADepthPacking;\n      this.depthMaterial.blending = NoBlending;\n      this.normalMaterial = new MeshNormalMaterial();\n      this.normalMaterial.blending = NoBlending;\n      this.saoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SAOShader.defines),\n        fragmentShader: SAOShader.fragmentShader,\n        vertexShader: SAOShader.vertexShader,\n        uniforms: UniformsUtils.clone(SAOShader.uniforms)\n      });\n      this.saoMaterial.extensions.derivatives = true;\n      this.saoMaterial.defines[\"DEPTH_PACKING\"] = this.supportsDepthTextureExtension ? 0 : 1;\n      this.saoMaterial.defines[\"NORMAL_TEXTURE\"] = this.supportsNormalTexture ? 1 : 0;\n      this.saoMaterial.defines[\"PERSPECTIVE_CAMERA\"] = this.camera.isPerspectiveCamera ? 1 : 0;\n      this.saoMaterial.uniforms[\"tDepth\"].value = this.supportsDepthTextureExtension ? depthTexture : this.depthRenderTarget.texture;\n      this.saoMaterial.uniforms[\"tNormal\"].value = this.normalRenderTarget.texture;\n      this.saoMaterial.uniforms[\"size\"].value.set(this.resolution.x, this.resolution.y);\n      this.saoMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.saoMaterial.uniforms[\"cameraProjectionMatrix\"].value = this.camera.projectionMatrix;\n      this.saoMaterial.blending = NoBlending;\n      this.vBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader\n      });\n      this.vBlurMaterial.defines[\"DEPTH_PACKING\"] = this.supportsDepthTextureExtension ? 0 : 1;\n      this.vBlurMaterial.defines[\"PERSPECTIVE_CAMERA\"] = this.camera.isPerspectiveCamera ? 1 : 0;\n      this.vBlurMaterial.uniforms[\"tDiffuse\"].value = this.saoRenderTarget.texture;\n      this.vBlurMaterial.uniforms[\"tDepth\"].value = this.supportsDepthTextureExtension ? depthTexture : this.depthRenderTarget.texture;\n      this.vBlurMaterial.uniforms[\"size\"].value.set(this.resolution.x, this.resolution.y);\n      this.vBlurMaterial.blending = NoBlending;\n      this.hBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader\n      });\n      this.hBlurMaterial.defines[\"DEPTH_PACKING\"] = this.supportsDepthTextureExtension ? 0 : 1;\n      this.hBlurMaterial.defines[\"PERSPECTIVE_CAMERA\"] = this.camera.isPerspectiveCamera ? 1 : 0;\n      this.hBlurMaterial.uniforms[\"tDiffuse\"].value = this.blurIntermediateRenderTarget.texture;\n      this.hBlurMaterial.uniforms[\"tDepth\"].value = this.supportsDepthTextureExtension ? depthTexture : this.depthRenderTarget.texture;\n      this.hBlurMaterial.uniforms[\"size\"].value.set(this.resolution.x, this.resolution.y);\n      this.hBlurMaterial.blending = NoBlending;\n      this.materialCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.materialCopy.transparent = true;\n      this.materialCopy.depthTest = false;\n      this.materialCopy.depthWrite = false;\n      this.materialCopy.blending = CustomBlending;\n      this.materialCopy.blendSrc = DstColorFactor;\n      this.materialCopy.blendDst = ZeroFactor;\n      this.materialCopy.blendEquation = AddEquation;\n      this.materialCopy.blendSrcAlpha = DstAlphaFactor;\n      this.materialCopy.blendDstAlpha = ZeroFactor;\n      this.materialCopy.blendEquationAlpha = AddEquation;\n      this.depthCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(UnpackDepthRGBAShader.uniforms),\n        vertexShader: UnpackDepthRGBAShader.vertexShader,\n        fragmentShader: UnpackDepthRGBAShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.fsQuad = new FullScreenQuad(null);\n    }\n    render(renderer, writeBuffer, readBuffer) {\n      if (this.renderToScreen) {\n        this.materialCopy.blending = NoBlending;\n        this.materialCopy.uniforms[\"tDiffuse\"].value = readBuffer.texture;\n        this.materialCopy.needsUpdate = true;\n        this.renderPass(renderer, this.materialCopy, null);\n      }\n      if (this.params.output === 1) {\n        return;\n      }\n      renderer.getClearColor(this._oldClearColor);\n      this.oldClearAlpha = renderer.getClearAlpha();\n      const oldAutoClear = renderer.autoClear;\n      renderer.autoClear = false;\n      renderer.setRenderTarget(this.depthRenderTarget);\n      renderer.clear();\n      this.saoMaterial.uniforms[\"bias\"].value = this.params.saoBias;\n      this.saoMaterial.uniforms[\"intensity\"].value = this.params.saoIntensity;\n      this.saoMaterial.uniforms[\"scale\"].value = this.params.saoScale;\n      this.saoMaterial.uniforms[\"kernelRadius\"].value = this.params.saoKernelRadius;\n      this.saoMaterial.uniforms[\"minResolution\"].value = this.params.saoMinResolution;\n      this.saoMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.saoMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      const depthCutoff = this.params.saoBlurDepthCutoff * (this.camera.far - this.camera.near);\n      this.vBlurMaterial.uniforms[\"depthCutoff\"].value = depthCutoff;\n      this.hBlurMaterial.uniforms[\"depthCutoff\"].value = depthCutoff;\n      this.vBlurMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.vBlurMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.hBlurMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.hBlurMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.params.saoBlurRadius = Math.floor(this.params.saoBlurRadius);\n      if (this.prevStdDev !== this.params.saoBlurStdDev || this.prevNumSamples !== this.params.saoBlurRadius) {\n        BlurShaderUtils.configure(this.vBlurMaterial, this.params.saoBlurRadius, this.params.saoBlurStdDev, new Vector2(0, 1));\n        BlurShaderUtils.configure(this.hBlurMaterial, this.params.saoBlurRadius, this.params.saoBlurStdDev, new Vector2(1, 0));\n        this.prevStdDev = this.params.saoBlurStdDev;\n        this.prevNumSamples = this.params.saoBlurRadius;\n      }\n      renderer.setClearColor(0);\n      renderer.setRenderTarget(this.beautyRenderTarget);\n      renderer.clear();\n      renderer.render(this.scene, this.camera);\n      if (!this.supportsDepthTextureExtension) {\n        this.renderOverride(renderer, this.depthMaterial, this.depthRenderTarget, 0, 1);\n      }\n      if (this.supportsNormalTexture) {\n        this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 7829503, 1);\n      }\n      this.renderPass(renderer, this.saoMaterial, this.saoRenderTarget, 16777215, 1);\n      if (this.params.saoBlur) {\n        this.renderPass(renderer, this.vBlurMaterial, this.blurIntermediateRenderTarget, 16777215, 1);\n        this.renderPass(renderer, this.hBlurMaterial, this.saoRenderTarget, 16777215, 1);\n      }\n      let outputMaterial = this.materialCopy;\n      if (this.params.output === 3) {\n        if (this.supportsDepthTextureExtension) {\n          this.materialCopy.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.depthTexture;\n          this.materialCopy.needsUpdate = true;\n        } else {\n          this.depthCopy.uniforms[\"tDiffuse\"].value = this.depthRenderTarget.texture;\n          this.depthCopy.needsUpdate = true;\n          outputMaterial = this.depthCopy;\n        }\n      } else if (this.params.output === 4) {\n        this.materialCopy.uniforms[\"tDiffuse\"].value = this.normalRenderTarget.texture;\n        this.materialCopy.needsUpdate = true;\n      } else {\n        this.materialCopy.uniforms[\"tDiffuse\"].value = this.saoRenderTarget.texture;\n        this.materialCopy.needsUpdate = true;\n      }\n      if (this.params.output === 0) {\n        outputMaterial.blending = CustomBlending;\n      } else {\n        outputMaterial.blending = NoBlending;\n      }\n      this.renderPass(renderer, outputMaterial, this.renderToScreen ? null : readBuffer);\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha);\n      renderer.autoClear = oldAutoClear;\n    }\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor);\n      const originalClearAlpha = renderer.getClearAlpha();\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.fsQuad.material = passMaterial;\n      this.fsQuad.render(renderer);\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor);\n      const originalClearAlpha = renderer.getClearAlpha();\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      clearColor = overrideMaterial.clearColor || clearColor;\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.scene.overrideMaterial = overrideMaterial;\n      renderer.render(this.scene, this.camera);\n      this.scene.overrideMaterial = null;\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    setSize(width, height) {\n      this.beautyRenderTarget.setSize(width, height);\n      this.saoRenderTarget.setSize(width, height);\n      this.blurIntermediateRenderTarget.setSize(width, height);\n      this.normalRenderTarget.setSize(width, height);\n      this.depthRenderTarget.setSize(width, height);\n      this.saoMaterial.uniforms[\"size\"].value.set(width, height);\n      this.saoMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.saoMaterial.uniforms[\"cameraProjectionMatrix\"].value = this.camera.projectionMatrix;\n      this.saoMaterial.needsUpdate = true;\n      this.vBlurMaterial.uniforms[\"size\"].value.set(width, height);\n      this.vBlurMaterial.needsUpdate = true;\n      this.hBlurMaterial.uniforms[\"size\"].value.set(width, height);\n      this.hBlurMaterial.needsUpdate = true;\n    }\n    dispose() {\n      this.saoRenderTarget.dispose();\n      this.blurIntermediateRenderTarget.dispose();\n      this.beautyRenderTarget.dispose();\n      this.normalRenderTarget.dispose();\n      this.depthRenderTarget.dispose();\n      this.depthMaterial.dispose();\n      this.normalMaterial.dispose();\n      this.saoMaterial.dispose();\n      this.vBlurMaterial.dispose();\n      this.hBlurMaterial.dispose();\n      this.materialCopy.dispose();\n      this.depthCopy.dispose();\n      this.fsQuad.dispose();\n    }\n  }\n  __publicField(SAOPass2, \"OUTPUT\", {\n    Beauty: 1,\n    Default: 0,\n    SAO: 2,\n    Depth: 3,\n    Normal: 4\n  });\n  return SAOPass2;\n})();\nexport { SAOPass };", "map": {"version": 3, "names": ["SAOPass", "SAOPass2", "Pass", "constructor", "scene", "camera", "useDepthTexture", "useNormals", "resolution", "Vector2", "clear", "needsSwap", "supportsDepthTextureExtension", "supportsNormalTexture", "originalClearColor", "Color", "_oldClearColor", "oldClearAlpha", "params", "output", "saoBias", "saoIntensity", "saoScale", "saoKernelRadius", "saoMinResolution", "saoBlur", "saoBlurRadius", "saoBlurStdDev", "saoBlur<PERSON><PERSON><PERSON><PERSON>", "x", "y", "saoRenderTarget", "WebGLRenderTarget", "type", "HalfFloatType", "blurIntermediateRenderTarget", "clone", "beautyR<PERSON><PERSON><PERSON><PERSON>", "normalRenderTarget", "minFilter", "NearestFilter", "magFilter", "depthRender<PERSON><PERSON>get", "depthTexture", "DepthTexture", "UnsignedShortType", "depthBuffer", "depthMaterial", "MeshDepthMaterial", "depthPacking", "RGBADepthPacking", "blending", "NoBlending", "normalMaterial", "MeshNormalMaterial", "saoMaterial", "ShaderMaterial", "defines", "Object", "assign", "SAOShader", "fragmentShader", "vertexShader", "uniforms", "UniformsUtils", "extensions", "derivatives", "isPerspectiveCamera", "value", "texture", "set", "copy", "projectionMatrixInverse", "projectionMatrix", "vBlurMaterial", "DepthLimited<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hBlurMaterial", "materialCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transparent", "depthTest", "depthWrite", "CustomBlending", "blendSrc", "DstColorFactor", "blendDst", "ZeroFactor", "blendEquation", "AddEquation", "blendSrcAlpha", "DstAlphaFactor", "blendDstAlpha", "blendEquationAlpha", "depthCopy", "UnpackDepthRGBAShader", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "renderToScreen", "needsUpdate", "renderPass", "getClearColor", "getClearAlpha", "oldAutoClear", "autoClear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "near", "far", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "prevStdDev", "prevNumSamples", "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "configure", "setClearColor", "renderOverride", "outputMaterial", "passMaterial", "renderTarget", "clearColor", "clearAlpha", "originalClearAlpha", "originalAutoClear", "setClearAlpha", "material", "overrideMaterial", "setSize", "width", "height", "dispose", "__publicField", "Beauty", "<PERSON><PERSON><PERSON>", "SAO", "De<PERSON><PERSON>", "Normal"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/SAOPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  CustomBlending,\n  DepthTexture,\n  DstAlphaFactor,\n  DstColorFactor,\n  HalfFloatType,\n  MeshDepthMaterial,\n  MeshNormalMaterial,\n  NearestFilter,\n  NoBlending,\n  RGBADepthPacking,\n  ShaderMaterial,\n  UniformsUtils,\n  UnsignedShortType,\n  Vector2,\n  WebGLR<PERSON>Target,\n  ZeroFactor,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SAOShader } from '../shaders/SAOShader'\nimport { DepthLimitedBlurShader } from '../shaders/DepthLimitedBlurShader'\nimport { BlurShaderUtils } from '../shaders/DepthLimitedBlurShader'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { UnpackDepthRGBAShader } from '../shaders/UnpackDepthRGBAShader'\n\n/**\n * SAO implementation inspired from bhouston previous SAO work\n */\nconst SAOPass = /* @__PURE__ */ (() => {\n  class SAOPass extends Pass {\n    static OUTPUT = {\n      Beauty: 1,\n      Default: 0,\n      SAO: 2,\n      Depth: 3,\n      Normal: 4,\n    }\n\n    constructor(scene, camera, useDepthTexture = false, useNormals = false, resolution = new Vector2(256, 256)) {\n      super()\n\n      this.scene = scene\n      this.camera = camera\n\n      this.clear = true\n      this.needsSwap = false\n\n      this.supportsDepthTextureExtension = useDepthTexture\n      this.supportsNormalTexture = useNormals\n\n      this.originalClearColor = new Color()\n      this._oldClearColor = new Color()\n      this.oldClearAlpha = 1\n\n      this.params = {\n        output: 0,\n        saoBias: 0.5,\n        saoIntensity: 0.18,\n        saoScale: 1,\n        saoKernelRadius: 100,\n        saoMinResolution: 0,\n        saoBlur: true,\n        saoBlurRadius: 8,\n        saoBlurStdDev: 4,\n        saoBlurDepthCutoff: 0.01,\n      }\n\n      this.resolution = new Vector2(resolution.x, resolution.y)\n\n      this.saoRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, { type: HalfFloatType })\n      this.blurIntermediateRenderTarget = this.saoRenderTarget.clone()\n      this.beautyRenderTarget = this.saoRenderTarget.clone()\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n      this.depthRenderTarget = this.normalRenderTarget.clone()\n\n      let depthTexture\n\n      if (this.supportsDepthTextureExtension) {\n        depthTexture = new DepthTexture()\n        depthTexture.type = UnsignedShortType\n\n        this.beautyRenderTarget.depthTexture = depthTexture\n        this.beautyRenderTarget.depthBuffer = true\n      }\n\n      this.depthMaterial = new MeshDepthMaterial()\n      this.depthMaterial.depthPacking = RGBADepthPacking\n      this.depthMaterial.blending = NoBlending\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      this.saoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SAOShader.defines),\n        fragmentShader: SAOShader.fragmentShader,\n        vertexShader: SAOShader.vertexShader,\n        uniforms: UniformsUtils.clone(SAOShader.uniforms),\n      })\n      this.saoMaterial.extensions.derivatives = true\n      this.saoMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.saoMaterial.defines['NORMAL_TEXTURE'] = this.supportsNormalTexture ? 1 : 0\n      this.saoMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.saoMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.saoMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.saoMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.saoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n      this.saoMaterial.uniforms['cameraProjectionMatrix'].value = this.camera.projectionMatrix\n      this.saoMaterial.blending = NoBlending\n\n      this.vBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader,\n      })\n      this.vBlurMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.vBlurMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.vBlurMaterial.uniforms['tDiffuse'].value = this.saoRenderTarget.texture\n      this.vBlurMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.vBlurMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.vBlurMaterial.blending = NoBlending\n\n      this.hBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader,\n      })\n      this.hBlurMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.hBlurMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.hBlurMaterial.uniforms['tDiffuse'].value = this.blurIntermediateRenderTarget.texture\n      this.hBlurMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.hBlurMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.hBlurMaterial.blending = NoBlending\n\n      this.materialCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.materialCopy.transparent = true\n      this.materialCopy.depthTest = false\n      this.materialCopy.depthWrite = false\n      this.materialCopy.blending = CustomBlending\n      this.materialCopy.blendSrc = DstColorFactor\n      this.materialCopy.blendDst = ZeroFactor\n      this.materialCopy.blendEquation = AddEquation\n      this.materialCopy.blendSrcAlpha = DstAlphaFactor\n      this.materialCopy.blendDstAlpha = ZeroFactor\n      this.materialCopy.blendEquationAlpha = AddEquation\n\n      this.depthCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(UnpackDepthRGBAShader.uniforms),\n        vertexShader: UnpackDepthRGBAShader.vertexShader,\n        fragmentShader: UnpackDepthRGBAShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n    }\n\n    render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive*/) {\n      // Rendering readBuffer first when rendering to screen\n      if (this.renderToScreen) {\n        this.materialCopy.blending = NoBlending\n        this.materialCopy.uniforms['tDiffuse'].value = readBuffer.texture\n        this.materialCopy.needsUpdate = true\n        this.renderPass(renderer, this.materialCopy, null)\n      }\n\n      if (this.params.output === 1) {\n        return\n      }\n\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n      renderer.autoClear = false\n\n      renderer.setRenderTarget(this.depthRenderTarget)\n      renderer.clear()\n\n      this.saoMaterial.uniforms['bias'].value = this.params.saoBias\n      this.saoMaterial.uniforms['intensity'].value = this.params.saoIntensity\n      this.saoMaterial.uniforms['scale'].value = this.params.saoScale\n      this.saoMaterial.uniforms['kernelRadius'].value = this.params.saoKernelRadius\n      this.saoMaterial.uniforms['minResolution'].value = this.params.saoMinResolution\n      this.saoMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.saoMaterial.uniforms['cameraFar'].value = this.camera.far\n      // this.saoMaterial.uniforms['randomSeed'].value = Math.random();\n\n      const depthCutoff = this.params.saoBlurDepthCutoff * (this.camera.far - this.camera.near)\n      this.vBlurMaterial.uniforms['depthCutoff'].value = depthCutoff\n      this.hBlurMaterial.uniforms['depthCutoff'].value = depthCutoff\n\n      this.vBlurMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.vBlurMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.hBlurMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.hBlurMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      this.params.saoBlurRadius = Math.floor(this.params.saoBlurRadius)\n      if (this.prevStdDev !== this.params.saoBlurStdDev || this.prevNumSamples !== this.params.saoBlurRadius) {\n        BlurShaderUtils.configure(\n          this.vBlurMaterial,\n          this.params.saoBlurRadius,\n          this.params.saoBlurStdDev,\n          new Vector2(0, 1),\n        )\n        BlurShaderUtils.configure(\n          this.hBlurMaterial,\n          this.params.saoBlurRadius,\n          this.params.saoBlurStdDev,\n          new Vector2(1, 0),\n        )\n        this.prevStdDev = this.params.saoBlurStdDev\n        this.prevNumSamples = this.params.saoBlurRadius\n      }\n\n      // Rendering scene to depth texture\n      renderer.setClearColor(0x000000)\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      // Re-render scene if depth texture extension is not supported\n      if (!this.supportsDepthTextureExtension) {\n        // Clear rule : far clipping plane in both RGBA and Basic encoding\n        this.renderOverride(renderer, this.depthMaterial, this.depthRenderTarget, 0x000000, 1.0)\n      }\n\n      if (this.supportsNormalTexture) {\n        // Clear rule : default normal is facing the camera\n        this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0x7777ff, 1.0)\n      }\n\n      // Rendering SAO texture\n      this.renderPass(renderer, this.saoMaterial, this.saoRenderTarget, 0xffffff, 1.0)\n\n      // Blurring SAO texture\n      if (this.params.saoBlur) {\n        this.renderPass(renderer, this.vBlurMaterial, this.blurIntermediateRenderTarget, 0xffffff, 1.0)\n        this.renderPass(renderer, this.hBlurMaterial, this.saoRenderTarget, 0xffffff, 1.0)\n      }\n\n      let outputMaterial = this.materialCopy\n      // Setting up SAO rendering\n      if (this.params.output === 3) {\n        if (this.supportsDepthTextureExtension) {\n          this.materialCopy.uniforms['tDiffuse'].value = this.beautyRenderTarget.depthTexture\n          this.materialCopy.needsUpdate = true\n        } else {\n          this.depthCopy.uniforms['tDiffuse'].value = this.depthRenderTarget.texture\n          this.depthCopy.needsUpdate = true\n          outputMaterial = this.depthCopy\n        }\n      } else if (this.params.output === 4) {\n        this.materialCopy.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n        this.materialCopy.needsUpdate = true\n      } else {\n        this.materialCopy.uniforms['tDiffuse'].value = this.saoRenderTarget.texture\n        this.materialCopy.needsUpdate = true\n      }\n\n      // Blending depends on output, only want a CustomBlending when showing SAO\n      if (this.params.output === 0) {\n        outputMaterial.blending = CustomBlending\n      } else {\n        outputMaterial.blending = NoBlending\n      }\n\n      // Rendering SAOPass result on top of previous pass\n      this.renderPass(renderer, outputMaterial, this.renderToScreen ? null : readBuffer)\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.beautyRenderTarget.setSize(width, height)\n      this.saoRenderTarget.setSize(width, height)\n      this.blurIntermediateRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.depthRenderTarget.setSize(width, height)\n\n      this.saoMaterial.uniforms['size'].value.set(width, height)\n      this.saoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n      this.saoMaterial.uniforms['cameraProjectionMatrix'].value = this.camera.projectionMatrix\n      this.saoMaterial.needsUpdate = true\n\n      this.vBlurMaterial.uniforms['size'].value.set(width, height)\n      this.vBlurMaterial.needsUpdate = true\n\n      this.hBlurMaterial.uniforms['size'].value.set(width, height)\n      this.hBlurMaterial.needsUpdate = true\n    }\n\n    dispose() {\n      this.saoRenderTarget.dispose()\n      this.blurIntermediateRenderTarget.dispose()\n      this.beautyRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.depthRenderTarget.dispose()\n\n      this.depthMaterial.dispose()\n      this.normalMaterial.dispose()\n      this.saoMaterial.dispose()\n      this.vBlurMaterial.dispose()\n      this.hBlurMaterial.dispose()\n      this.materialCopy.dispose()\n      this.depthCopy.dispose()\n\n      this.fsQuad.dispose()\n    }\n  }\n\n  return SAOPass\n})()\n\nexport { SAOPass }\n"], "mappings": ";;;;;;;;;;;;;;;;;AA8BK,MAACA,OAAA,GAA2B,sBAAM;EACrC,MAAMC,QAAA,SAAgBC,IAAA,CAAK;IASzBC,YAAYC,KAAA,EAAOC,MAAA,EAAQC,eAAA,GAAkB,OAAOC,UAAA,GAAa,OAAOC,UAAA,GAAa,IAAIC,OAAA,CAAQ,KAAK,GAAG,GAAG;MAC1G,MAAO;MAEP,KAAKL,KAAA,GAAQA,KAAA;MACb,KAAKC,MAAA,GAASA,MAAA;MAEd,KAAKK,KAAA,GAAQ;MACb,KAAKC,SAAA,GAAY;MAEjB,KAAKC,6BAAA,GAAgCN,eAAA;MACrC,KAAKO,qBAAA,GAAwBN,UAAA;MAE7B,KAAKO,kBAAA,GAAqB,IAAIC,KAAA,CAAO;MACrC,KAAKC,cAAA,GAAiB,IAAID,KAAA,CAAO;MACjC,KAAKE,aAAA,GAAgB;MAErB,KAAKC,MAAA,GAAS;QACZC,MAAA,EAAQ;QACRC,OAAA,EAAS;QACTC,YAAA,EAAc;QACdC,QAAA,EAAU;QACVC,eAAA,EAAiB;QACjBC,gBAAA,EAAkB;QAClBC,OAAA,EAAS;QACTC,aAAA,EAAe;QACfC,aAAA,EAAe;QACfC,kBAAA,EAAoB;MACrB;MAED,KAAKpB,UAAA,GAAa,IAAIC,OAAA,CAAQD,UAAA,CAAWqB,CAAA,EAAGrB,UAAA,CAAWsB,CAAC;MAExD,KAAKC,eAAA,GAAkB,IAAIC,iBAAA,CAAkB,KAAKxB,UAAA,CAAWqB,CAAA,EAAG,KAAKrB,UAAA,CAAWsB,CAAA,EAAG;QAAEG,IAAA,EAAMC;MAAa,CAAE;MAC1G,KAAKC,4BAAA,GAA+B,KAAKJ,eAAA,CAAgBK,KAAA,CAAO;MAChE,KAAKC,kBAAA,GAAqB,KAAKN,eAAA,CAAgBK,KAAA,CAAO;MAEtD,KAAKE,kBAAA,GAAqB,IAAIN,iBAAA,CAAkB,KAAKxB,UAAA,CAAWqB,CAAA,EAAG,KAAKrB,UAAA,CAAWsB,CAAA,EAAG;QACpFS,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD,aAAA;QACXP,IAAA,EAAMC;MACd,CAAO;MACD,KAAKQ,iBAAA,GAAoB,KAAKJ,kBAAA,CAAmBF,KAAA,CAAO;MAExD,IAAIO,YAAA;MAEJ,IAAI,KAAK/B,6BAAA,EAA+B;QACtC+B,YAAA,GAAe,IAAIC,YAAA,CAAc;QACjCD,YAAA,CAAaV,IAAA,GAAOY,iBAAA;QAEpB,KAAKR,kBAAA,CAAmBM,YAAA,GAAeA,YAAA;QACvC,KAAKN,kBAAA,CAAmBS,WAAA,GAAc;MACvC;MAED,KAAKC,aAAA,GAAgB,IAAIC,iBAAA,CAAmB;MAC5C,KAAKD,aAAA,CAAcE,YAAA,GAAeC,gBAAA;MAClC,KAAKH,aAAA,CAAcI,QAAA,GAAWC,UAAA;MAE9B,KAAKC,cAAA,GAAiB,IAAIC,kBAAA,CAAoB;MAC9C,KAAKD,cAAA,CAAeF,QAAA,GAAWC,UAAA;MAE/B,KAAKG,WAAA,GAAc,IAAIC,cAAA,CAAe;QACpCC,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIC,SAAA,CAAUH,OAAO;QAC5CI,cAAA,EAAgBD,SAAA,CAAUC,cAAA;QAC1BC,YAAA,EAAcF,SAAA,CAAUE,YAAA;QACxBC,QAAA,EAAUC,aAAA,CAAc5B,KAAA,CAAMwB,SAAA,CAAUG,QAAQ;MACxD,CAAO;MACD,KAAKR,WAAA,CAAYU,UAAA,CAAWC,WAAA,GAAc;MAC1C,KAAKX,WAAA,CAAYE,OAAA,CAAQ,eAAe,IAAI,KAAK7C,6BAAA,GAAgC,IAAI;MACrF,KAAK2C,WAAA,CAAYE,OAAA,CAAQ,gBAAgB,IAAI,KAAK5C,qBAAA,GAAwB,IAAI;MAC9E,KAAK0C,WAAA,CAAYE,OAAA,CAAQ,oBAAoB,IAAI,KAAKpD,MAAA,CAAO8D,mBAAA,GAAsB,IAAI;MACvF,KAAKZ,WAAA,CAAYQ,QAAA,CAAS,QAAQ,EAAEK,KAAA,GAAQ,KAAKxD,6BAAA,GAC7C+B,YAAA,GACA,KAAKD,iBAAA,CAAkB2B,OAAA;MAC3B,KAAKd,WAAA,CAAYQ,QAAA,CAAS,SAAS,EAAEK,KAAA,GAAQ,KAAK9B,kBAAA,CAAmB+B,OAAA;MACrE,KAAKd,WAAA,CAAYQ,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI,KAAK9D,UAAA,CAAWqB,CAAA,EAAG,KAAKrB,UAAA,CAAWsB,CAAC;MAChF,KAAKyB,WAAA,CAAYQ,QAAA,CAAS,+BAA+B,EAAEK,KAAA,CAAMG,IAAA,CAAK,KAAKlE,MAAA,CAAOmE,uBAAuB;MACzG,KAAKjB,WAAA,CAAYQ,QAAA,CAAS,wBAAwB,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOoE,gBAAA;MACxE,KAAKlB,WAAA,CAAYJ,QAAA,GAAWC,UAAA;MAE5B,KAAKsB,aAAA,GAAgB,IAAIlB,cAAA,CAAe;QACtCO,QAAA,EAAUC,aAAA,CAAc5B,KAAA,CAAMuC,sBAAA,CAAuBZ,QAAQ;QAC7DN,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIgB,sBAAA,CAAuBlB,OAAO;QACzDK,YAAA,EAAca,sBAAA,CAAuBb,YAAA;QACrCD,cAAA,EAAgBc,sBAAA,CAAuBd;MAC/C,CAAO;MACD,KAAKa,aAAA,CAAcjB,OAAA,CAAQ,eAAe,IAAI,KAAK7C,6BAAA,GAAgC,IAAI;MACvF,KAAK8D,aAAA,CAAcjB,OAAA,CAAQ,oBAAoB,IAAI,KAAKpD,MAAA,CAAO8D,mBAAA,GAAsB,IAAI;MACzF,KAAKO,aAAA,CAAcX,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAKrC,eAAA,CAAgBsC,OAAA;MACrE,KAAKK,aAAA,CAAcX,QAAA,CAAS,QAAQ,EAAEK,KAAA,GAAQ,KAAKxD,6BAAA,GAC/C+B,YAAA,GACA,KAAKD,iBAAA,CAAkB2B,OAAA;MAC3B,KAAKK,aAAA,CAAcX,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI,KAAK9D,UAAA,CAAWqB,CAAA,EAAG,KAAKrB,UAAA,CAAWsB,CAAC;MAClF,KAAK4C,aAAA,CAAcvB,QAAA,GAAWC,UAAA;MAE9B,KAAKwB,aAAA,GAAgB,IAAIpB,cAAA,CAAe;QACtCO,QAAA,EAAUC,aAAA,CAAc5B,KAAA,CAAMuC,sBAAA,CAAuBZ,QAAQ;QAC7DN,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIgB,sBAAA,CAAuBlB,OAAO;QACzDK,YAAA,EAAca,sBAAA,CAAuBb,YAAA;QACrCD,cAAA,EAAgBc,sBAAA,CAAuBd;MAC/C,CAAO;MACD,KAAKe,aAAA,CAAcnB,OAAA,CAAQ,eAAe,IAAI,KAAK7C,6BAAA,GAAgC,IAAI;MACvF,KAAKgE,aAAA,CAAcnB,OAAA,CAAQ,oBAAoB,IAAI,KAAKpD,MAAA,CAAO8D,mBAAA,GAAsB,IAAI;MACzF,KAAKS,aAAA,CAAcb,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAKjC,4BAAA,CAA6BkC,OAAA;MAClF,KAAKO,aAAA,CAAcb,QAAA,CAAS,QAAQ,EAAEK,KAAA,GAAQ,KAAKxD,6BAAA,GAC/C+B,YAAA,GACA,KAAKD,iBAAA,CAAkB2B,OAAA;MAC3B,KAAKO,aAAA,CAAcb,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI,KAAK9D,UAAA,CAAWqB,CAAA,EAAG,KAAKrB,UAAA,CAAWsB,CAAC;MAClF,KAAK8C,aAAA,CAAczB,QAAA,GAAWC,UAAA;MAE9B,KAAKyB,YAAA,GAAe,IAAIrB,cAAA,CAAe;QACrCO,QAAA,EAAUC,aAAA,CAAc5B,KAAA,CAAM0C,UAAA,CAAWf,QAAQ;QACjDD,YAAA,EAAcgB,UAAA,CAAWhB,YAAA;QACzBD,cAAA,EAAgBiB,UAAA,CAAWjB,cAAA;QAC3BV,QAAA,EAAUC;MAClB,CAAO;MACD,KAAKyB,YAAA,CAAaE,WAAA,GAAc;MAChC,KAAKF,YAAA,CAAaG,SAAA,GAAY;MAC9B,KAAKH,YAAA,CAAaI,UAAA,GAAa;MAC/B,KAAKJ,YAAA,CAAa1B,QAAA,GAAW+B,cAAA;MAC7B,KAAKL,YAAA,CAAaM,QAAA,GAAWC,cAAA;MAC7B,KAAKP,YAAA,CAAaQ,QAAA,GAAWC,UAAA;MAC7B,KAAKT,YAAA,CAAaU,aAAA,GAAgBC,WAAA;MAClC,KAAKX,YAAA,CAAaY,aAAA,GAAgBC,cAAA;MAClC,KAAKb,YAAA,CAAac,aAAA,GAAgBL,UAAA;MAClC,KAAKT,YAAA,CAAae,kBAAA,GAAqBJ,WAAA;MAEvC,KAAKK,SAAA,GAAY,IAAIrC,cAAA,CAAe;QAClCO,QAAA,EAAUC,aAAA,CAAc5B,KAAA,CAAM0D,qBAAA,CAAsB/B,QAAQ;QAC5DD,YAAA,EAAcgC,qBAAA,CAAsBhC,YAAA;QACpCD,cAAA,EAAgBiC,qBAAA,CAAsBjC,cAAA;QACtCV,QAAA,EAAUC;MAClB,CAAO;MAED,KAAK2C,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;IACtC;IAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAwC;MAEpE,IAAI,KAAKC,cAAA,EAAgB;QACvB,KAAKxB,YAAA,CAAa1B,QAAA,GAAWC,UAAA;QAC7B,KAAKyB,YAAA,CAAad,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQgC,UAAA,CAAW/B,OAAA;QAC1D,KAAKQ,YAAA,CAAayB,WAAA,GAAc;QAChC,KAAKC,UAAA,CAAWL,QAAA,EAAU,KAAKrB,YAAA,EAAc,IAAI;MAClD;MAED,IAAI,KAAK3D,MAAA,CAAOC,MAAA,KAAW,GAAG;QAC5B;MACD;MAED+E,QAAA,CAASM,aAAA,CAAc,KAAKxF,cAAc;MAC1C,KAAKC,aAAA,GAAgBiF,QAAA,CAASO,aAAA,CAAe;MAC7C,MAAMC,YAAA,GAAeR,QAAA,CAASS,SAAA;MAC9BT,QAAA,CAASS,SAAA,GAAY;MAErBT,QAAA,CAASU,eAAA,CAAgB,KAAKlE,iBAAiB;MAC/CwD,QAAA,CAASxF,KAAA,CAAO;MAEhB,KAAK6C,WAAA,CAAYQ,QAAA,CAAS,MAAM,EAAEK,KAAA,GAAQ,KAAKlD,MAAA,CAAOE,OAAA;MACtD,KAAKmC,WAAA,CAAYQ,QAAA,CAAS,WAAW,EAAEK,KAAA,GAAQ,KAAKlD,MAAA,CAAOG,YAAA;MAC3D,KAAKkC,WAAA,CAAYQ,QAAA,CAAS,OAAO,EAAEK,KAAA,GAAQ,KAAKlD,MAAA,CAAOI,QAAA;MACvD,KAAKiC,WAAA,CAAYQ,QAAA,CAAS,cAAc,EAAEK,KAAA,GAAQ,KAAKlD,MAAA,CAAOK,eAAA;MAC9D,KAAKgC,WAAA,CAAYQ,QAAA,CAAS,eAAe,EAAEK,KAAA,GAAQ,KAAKlD,MAAA,CAAOM,gBAAA;MAC/D,KAAK+B,WAAA,CAAYQ,QAAA,CAAS,YAAY,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOwG,IAAA;MAC5D,KAAKtD,WAAA,CAAYQ,QAAA,CAAS,WAAW,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOyG,GAAA;MAG3D,MAAMC,WAAA,GAAc,KAAK7F,MAAA,CAAOU,kBAAA,IAAsB,KAAKvB,MAAA,CAAOyG,GAAA,GAAM,KAAKzG,MAAA,CAAOwG,IAAA;MACpF,KAAKnC,aAAA,CAAcX,QAAA,CAAS,aAAa,EAAEK,KAAA,GAAQ2C,WAAA;MACnD,KAAKnC,aAAA,CAAcb,QAAA,CAAS,aAAa,EAAEK,KAAA,GAAQ2C,WAAA;MAEnD,KAAKrC,aAAA,CAAcX,QAAA,CAAS,YAAY,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOwG,IAAA;MAC9D,KAAKnC,aAAA,CAAcX,QAAA,CAAS,WAAW,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOyG,GAAA;MAC7D,KAAKlC,aAAA,CAAcb,QAAA,CAAS,YAAY,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOwG,IAAA;MAC9D,KAAKjC,aAAA,CAAcb,QAAA,CAAS,WAAW,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOyG,GAAA;MAE7D,KAAK5F,MAAA,CAAOQ,aAAA,GAAgBsF,IAAA,CAAKC,KAAA,CAAM,KAAK/F,MAAA,CAAOQ,aAAa;MAChE,IAAI,KAAKwF,UAAA,KAAe,KAAKhG,MAAA,CAAOS,aAAA,IAAiB,KAAKwF,cAAA,KAAmB,KAAKjG,MAAA,CAAOQ,aAAA,EAAe;QACtG0F,eAAA,CAAgBC,SAAA,CACd,KAAK3C,aAAA,EACL,KAAKxD,MAAA,CAAOQ,aAAA,EACZ,KAAKR,MAAA,CAAOS,aAAA,EACZ,IAAIlB,OAAA,CAAQ,GAAG,CAAC,CACjB;QACD2G,eAAA,CAAgBC,SAAA,CACd,KAAKzC,aAAA,EACL,KAAK1D,MAAA,CAAOQ,aAAA,EACZ,KAAKR,MAAA,CAAOS,aAAA,EACZ,IAAIlB,OAAA,CAAQ,GAAG,CAAC,CACjB;QACD,KAAKyG,UAAA,GAAa,KAAKhG,MAAA,CAAOS,aAAA;QAC9B,KAAKwF,cAAA,GAAiB,KAAKjG,MAAA,CAAOQ,aAAA;MACnC;MAGDwE,QAAA,CAASoB,aAAA,CAAc,CAAQ;MAC/BpB,QAAA,CAASU,eAAA,CAAgB,KAAKvE,kBAAkB;MAChD6D,QAAA,CAASxF,KAAA,CAAO;MAChBwF,QAAA,CAASD,MAAA,CAAO,KAAK7F,KAAA,EAAO,KAAKC,MAAM;MAGvC,IAAI,CAAC,KAAKO,6BAAA,EAA+B;QAEvC,KAAK2G,cAAA,CAAerB,QAAA,EAAU,KAAKnD,aAAA,EAAe,KAAKL,iBAAA,EAAmB,GAAU,CAAG;MACxF;MAED,IAAI,KAAK7B,qBAAA,EAAuB;QAE9B,KAAK0G,cAAA,CAAerB,QAAA,EAAU,KAAK7C,cAAA,EAAgB,KAAKf,kBAAA,EAAoB,SAAU,CAAG;MAC1F;MAGD,KAAKiE,UAAA,CAAWL,QAAA,EAAU,KAAK3C,WAAA,EAAa,KAAKxB,eAAA,EAAiB,UAAU,CAAG;MAG/E,IAAI,KAAKb,MAAA,CAAOO,OAAA,EAAS;QACvB,KAAK8E,UAAA,CAAWL,QAAA,EAAU,KAAKxB,aAAA,EAAe,KAAKvC,4BAAA,EAA8B,UAAU,CAAG;QAC9F,KAAKoE,UAAA,CAAWL,QAAA,EAAU,KAAKtB,aAAA,EAAe,KAAK7C,eAAA,EAAiB,UAAU,CAAG;MAClF;MAED,IAAIyF,cAAA,GAAiB,KAAK3C,YAAA;MAE1B,IAAI,KAAK3D,MAAA,CAAOC,MAAA,KAAW,GAAG;QAC5B,IAAI,KAAKP,6BAAA,EAA+B;UACtC,KAAKiE,YAAA,CAAad,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAK/B,kBAAA,CAAmBM,YAAA;UACvE,KAAKkC,YAAA,CAAayB,WAAA,GAAc;QAC1C,OAAe;UACL,KAAKT,SAAA,CAAU9B,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAK1B,iBAAA,CAAkB2B,OAAA;UACnE,KAAKwB,SAAA,CAAUS,WAAA,GAAc;UAC7BkB,cAAA,GAAiB,KAAK3B,SAAA;QACvB;MACF,WAAU,KAAK3E,MAAA,CAAOC,MAAA,KAAW,GAAG;QACnC,KAAK0D,YAAA,CAAad,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAK9B,kBAAA,CAAmB+B,OAAA;QACvE,KAAKQ,YAAA,CAAayB,WAAA,GAAc;MACxC,OAAa;QACL,KAAKzB,YAAA,CAAad,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQ,KAAKrC,eAAA,CAAgBsC,OAAA;QACpE,KAAKQ,YAAA,CAAayB,WAAA,GAAc;MACjC;MAGD,IAAI,KAAKpF,MAAA,CAAOC,MAAA,KAAW,GAAG;QAC5BqG,cAAA,CAAerE,QAAA,GAAW+B,cAAA;MAClC,OAAa;QACLsC,cAAA,CAAerE,QAAA,GAAWC,UAAA;MAC3B;MAGD,KAAKmD,UAAA,CAAWL,QAAA,EAAUsB,cAAA,EAAgB,KAAKnB,cAAA,GAAiB,OAAOD,UAAU;MAEjFF,QAAA,CAASoB,aAAA,CAAc,KAAKtG,cAAA,EAAgB,KAAKC,aAAa;MAC9DiF,QAAA,CAASS,SAAA,GAAYD,YAAA;IACtB;IAEDH,WAAWL,QAAA,EAAUuB,YAAA,EAAcC,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAEvE1B,QAAA,CAASM,aAAA,CAAc,KAAK1F,kBAAkB;MAC9C,MAAM+G,kBAAA,GAAqB3B,QAAA,CAASO,aAAA,CAAe;MACnD,MAAMqB,iBAAA,GAAoB5B,QAAA,CAASS,SAAA;MAEnCT,QAAA,CAASU,eAAA,CAAgBc,YAAY;MAGrCxB,QAAA,CAASS,SAAA,GAAY;MACrB,IAAIgB,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnDzB,QAAA,CAASoB,aAAA,CAAcK,UAAU;QACjCzB,QAAA,CAAS6B,aAAA,CAAcH,UAAA,IAAc,CAAG;QACxC1B,QAAA,CAASxF,KAAA,CAAO;MACjB;MAED,KAAKqF,MAAA,CAAOiC,QAAA,GAAWP,YAAA;MACvB,KAAK1B,MAAA,CAAOE,MAAA,CAAOC,QAAQ;MAG3BA,QAAA,CAASS,SAAA,GAAYmB,iBAAA;MACrB5B,QAAA,CAASoB,aAAA,CAAc,KAAKxG,kBAAkB;MAC9CoF,QAAA,CAAS6B,aAAA,CAAcF,kBAAkB;IAC1C;IAEDN,eAAerB,QAAA,EAAU+B,gBAAA,EAAkBP,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAC/E1B,QAAA,CAASM,aAAA,CAAc,KAAK1F,kBAAkB;MAC9C,MAAM+G,kBAAA,GAAqB3B,QAAA,CAASO,aAAA,CAAe;MACnD,MAAMqB,iBAAA,GAAoB5B,QAAA,CAASS,SAAA;MAEnCT,QAAA,CAASU,eAAA,CAAgBc,YAAY;MACrCxB,QAAA,CAASS,SAAA,GAAY;MAErBgB,UAAA,GAAaM,gBAAA,CAAiBN,UAAA,IAAcA,UAAA;MAC5CC,UAAA,GAAaK,gBAAA,CAAiBL,UAAA,IAAcA,UAAA;MAC5C,IAAID,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnDzB,QAAA,CAASoB,aAAA,CAAcK,UAAU;QACjCzB,QAAA,CAAS6B,aAAA,CAAcH,UAAA,IAAc,CAAG;QACxC1B,QAAA,CAASxF,KAAA,CAAO;MACjB;MAED,KAAKN,KAAA,CAAM6H,gBAAA,GAAmBA,gBAAA;MAC9B/B,QAAA,CAASD,MAAA,CAAO,KAAK7F,KAAA,EAAO,KAAKC,MAAM;MACvC,KAAKD,KAAA,CAAM6H,gBAAA,GAAmB;MAG9B/B,QAAA,CAASS,SAAA,GAAYmB,iBAAA;MACrB5B,QAAA,CAASoB,aAAA,CAAc,KAAKxG,kBAAkB;MAC9CoF,QAAA,CAAS6B,aAAA,CAAcF,kBAAkB;IAC1C;IAEDK,QAAQC,KAAA,EAAOC,MAAA,EAAQ;MACrB,KAAK/F,kBAAA,CAAmB6F,OAAA,CAAQC,KAAA,EAAOC,MAAM;MAC7C,KAAKrG,eAAA,CAAgBmG,OAAA,CAAQC,KAAA,EAAOC,MAAM;MAC1C,KAAKjG,4BAAA,CAA6B+F,OAAA,CAAQC,KAAA,EAAOC,MAAM;MACvD,KAAK9F,kBAAA,CAAmB4F,OAAA,CAAQC,KAAA,EAAOC,MAAM;MAC7C,KAAK1F,iBAAA,CAAkBwF,OAAA,CAAQC,KAAA,EAAOC,MAAM;MAE5C,KAAK7E,WAAA,CAAYQ,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI6D,KAAA,EAAOC,MAAM;MACzD,KAAK7E,WAAA,CAAYQ,QAAA,CAAS,+BAA+B,EAAEK,KAAA,CAAMG,IAAA,CAAK,KAAKlE,MAAA,CAAOmE,uBAAuB;MACzG,KAAKjB,WAAA,CAAYQ,QAAA,CAAS,wBAAwB,EAAEK,KAAA,GAAQ,KAAK/D,MAAA,CAAOoE,gBAAA;MACxE,KAAKlB,WAAA,CAAY+C,WAAA,GAAc;MAE/B,KAAK5B,aAAA,CAAcX,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI6D,KAAA,EAAOC,MAAM;MAC3D,KAAK1D,aAAA,CAAc4B,WAAA,GAAc;MAEjC,KAAK1B,aAAA,CAAcb,QAAA,CAAS,MAAM,EAAEK,KAAA,CAAME,GAAA,CAAI6D,KAAA,EAAOC,MAAM;MAC3D,KAAKxD,aAAA,CAAc0B,WAAA,GAAc;IAClC;IAED+B,QAAA,EAAU;MACR,KAAKtG,eAAA,CAAgBsG,OAAA,CAAS;MAC9B,KAAKlG,4BAAA,CAA6BkG,OAAA,CAAS;MAC3C,KAAKhG,kBAAA,CAAmBgG,OAAA,CAAS;MACjC,KAAK/F,kBAAA,CAAmB+F,OAAA,CAAS;MACjC,KAAK3F,iBAAA,CAAkB2F,OAAA,CAAS;MAEhC,KAAKtF,aAAA,CAAcsF,OAAA,CAAS;MAC5B,KAAKhF,cAAA,CAAegF,OAAA,CAAS;MAC7B,KAAK9E,WAAA,CAAY8E,OAAA,CAAS;MAC1B,KAAK3D,aAAA,CAAc2D,OAAA,CAAS;MAC5B,KAAKzD,aAAA,CAAcyD,OAAA,CAAS;MAC5B,KAAKxD,YAAA,CAAawD,OAAA,CAAS;MAC3B,KAAKxC,SAAA,CAAUwC,OAAA,CAAS;MAExB,KAAKtC,MAAA,CAAOsC,OAAA,CAAS;IACtB;EACF;EA1VCC,aAAA,CADIrI,QAAA,EACG,UAAS;IACdsI,MAAA,EAAQ;IACRC,OAAA,EAAS;IACTC,GAAA,EAAK;IACLC,KAAA,EAAO;IACPC,MAAA,EAAQ;EACT;EAsVH,OAAO1I,QAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}