{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { WebGLRenderTarget, HalfFloatType, RGBAFormat, UnsignedByteType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { EffectComposer, RenderPass, ShaderPass, GammaCorrectionShader } from 'three-stdlib';\nconst isWebGL2Available = () => {\n  try {\n    var canvas = document.createElement('canvas');\n    return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));\n  } catch (e) {\n    return false;\n  }\n};\nconst Effects = /* @__PURE__ */React.forwardRef(({\n  children,\n  multisamping = 8,\n  renderIndex = 1,\n  disableRender,\n  disableGamma,\n  disableRenderPass,\n  depthBuffer = true,\n  stencilBuffer = false,\n  anisotropy = 1,\n  colorSpace,\n  type,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    EffectComposer,\n    RenderPass,\n    ShaderPass\n  }), []);\n  const composer = React.useRef(null);\n  React.useImperativeHandle(ref, () => composer.current, []);\n  const {\n    scene,\n    camera,\n    gl,\n    size,\n    viewport\n  } = useThree();\n  const [target] = React.useState(() => {\n    const t = new WebGLRenderTarget(size.width, size.height, {\n      type: type || HalfFloatType,\n      format: RGBAFormat,\n      depthBuffer,\n      stencilBuffer,\n      anisotropy\n    });\n\n    // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n    if (type === UnsignedByteType && colorSpace != null) {\n      t.texture.colorSpace = colorSpace;\n    }\n    t.samples = multisamping;\n    return t;\n  });\n  React.useEffect(() => {\n    var _composer$current, _composer$current2;\n    (_composer$current = composer.current) == null || _composer$current.setSize(size.width, size.height);\n    (_composer$current2 = composer.current) == null || _composer$current2.setPixelRatio(viewport.dpr);\n  }, [gl, size, viewport.dpr]);\n  useFrame(() => {\n    var _composer$current3;\n    if (!disableRender) (_composer$current3 = composer.current) == null || _composer$current3.render();\n  }, renderIndex);\n  const passes = [];\n  if (!disableRenderPass) passes.push(/*#__PURE__*/React.createElement(\"renderPass\", {\n    key: \"renderpass\",\n    attach: `passes-${passes.length}`,\n    args: [scene, camera]\n  }));\n  if (!disableGamma) passes.push(/*#__PURE__*/React.createElement(\"shaderPass\", {\n    attach: `passes-${passes.length}`,\n    key: \"gammapass\",\n    args: [GammaCorrectionShader]\n  }));\n  React.Children.forEach(children, el => {\n    el && passes.push(/*#__PURE__*/React.cloneElement(el, {\n      key: passes.length,\n      attach: `passes-${passes.length}`\n    }));\n  });\n  return /*#__PURE__*/React.createElement(\"effectComposer\", _extends({\n    ref: composer,\n    args: [gl, target]\n  }, props), passes);\n});\nexport { Effects, isWebGL2Available };", "map": {"version": 3, "names": ["_extends", "React", "WebGLRenderTarget", "HalfFloatType", "RGBAFormat", "UnsignedByteType", "extend", "useThree", "useFrame", "EffectComposer", "RenderPass", "<PERSON><PERSON><PERSON><PERSON>", "GammaCorrectionShader", "isWebGL2Available", "canvas", "document", "createElement", "window", "WebGL2RenderingContext", "getContext", "e", "Effects", "forwardRef", "children", "multisamping", "renderIndex", "disableRender", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON>", "depthBuffer", "stencil<PERSON>uffer", "anisotropy", "colorSpace", "type", "props", "ref", "useMemo", "composer", "useRef", "useImperativeHandle", "current", "scene", "camera", "gl", "size", "viewport", "target", "useState", "t", "width", "height", "format", "texture", "samples", "useEffect", "_composer$current", "_composer$current2", "setSize", "setPixelRatio", "dpr", "_composer$current3", "render", "passes", "push", "key", "attach", "length", "args", "Children", "for<PERSON>ach", "el", "cloneElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Effects.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { WebGLRenderTarget, HalfFloatType, RGBAFormat, UnsignedByteType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { EffectComposer, RenderPass, ShaderPass, GammaCorrectionShader } from 'three-stdlib';\n\nconst isWebGL2Available = () => {\n  try {\n    var canvas = document.createElement('canvas');\n    return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));\n  } catch (e) {\n    return false;\n  }\n};\nconst Effects = /* @__PURE__ */React.forwardRef(({\n  children,\n  multisamping = 8,\n  renderIndex = 1,\n  disableRender,\n  disableGamma,\n  disableRenderPass,\n  depthBuffer = true,\n  stencilBuffer = false,\n  anisotropy = 1,\n  colorSpace,\n  type,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    EffectComposer,\n    RenderPass,\n    ShaderPass\n  }), []);\n  const composer = React.useRef(null);\n  React.useImperativeHandle(ref, () => composer.current, []);\n  const {\n    scene,\n    camera,\n    gl,\n    size,\n    viewport\n  } = useThree();\n  const [target] = React.useState(() => {\n    const t = new WebGLRenderTarget(size.width, size.height, {\n      type: type || HalfFloatType,\n      format: RGBAFormat,\n      depthBuffer,\n      stencilBuffer,\n      anisotropy\n    });\n\n    // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n    if (type === UnsignedByteType && colorSpace != null) {\n      t.texture.colorSpace = colorSpace;\n    }\n    t.samples = multisamping;\n    return t;\n  });\n  React.useEffect(() => {\n    var _composer$current, _composer$current2;\n    (_composer$current = composer.current) == null || _composer$current.setSize(size.width, size.height);\n    (_composer$current2 = composer.current) == null || _composer$current2.setPixelRatio(viewport.dpr);\n  }, [gl, size, viewport.dpr]);\n  useFrame(() => {\n    var _composer$current3;\n    if (!disableRender) (_composer$current3 = composer.current) == null || _composer$current3.render();\n  }, renderIndex);\n  const passes = [];\n  if (!disableRenderPass) passes.push(/*#__PURE__*/React.createElement(\"renderPass\", {\n    key: \"renderpass\",\n    attach: `passes-${passes.length}`,\n    args: [scene, camera]\n  }));\n  if (!disableGamma) passes.push(/*#__PURE__*/React.createElement(\"shaderPass\", {\n    attach: `passes-${passes.length}`,\n    key: \"gammapass\",\n    args: [GammaCorrectionShader]\n  }));\n  React.Children.forEach(children, el => {\n    el && passes.push(/*#__PURE__*/React.cloneElement(el, {\n      key: passes.length,\n      attach: `passes-${passes.length}`\n    }));\n  });\n  return /*#__PURE__*/React.createElement(\"effectComposer\", _extends({\n    ref: composer,\n    args: [gl, target]\n  }, props), passes);\n});\n\nexport { Effects, isWebGL2Available };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,OAAO;AACtF,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,qBAAqB,QAAQ,cAAc;AAE5F,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,IAAI;IACF,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,CAACC,sBAAsB,IAAIJ,MAAM,CAACK,UAAU,CAAC,QAAQ,CAAC,CAAC;EACzE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF,CAAC;AACD,MAAMC,OAAO,GAAG,eAAepB,KAAK,CAACqB,UAAU,CAAC,CAAC;EAC/CC,QAAQ;EACRC,YAAY,GAAG,CAAC;EAChBC,WAAW,GAAG,CAAC;EACfC,aAAa;EACbC,YAAY;EACZC,iBAAiB;EACjBC,WAAW,GAAG,IAAI;EAClBC,aAAa,GAAG,KAAK;EACrBC,UAAU,GAAG,CAAC;EACdC,UAAU;EACVC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTlC,KAAK,CAACmC,OAAO,CAAC,MAAM9B,MAAM,CAAC;IACzBG,cAAc;IACdC,UAAU;IACVC;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM0B,QAAQ,GAAGpC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EACnCrC,KAAK,CAACsC,mBAAmB,CAACJ,GAAG,EAAE,MAAME,QAAQ,CAACG,OAAO,EAAE,EAAE,CAAC;EAC1D,MAAM;IACJC,KAAK;IACLC,MAAM;IACNC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAGtC,QAAQ,CAAC,CAAC;EACd,MAAM,CAACuC,MAAM,CAAC,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,MAAM;IACpC,MAAMC,CAAC,GAAG,IAAI9C,iBAAiB,CAAC0C,IAAI,CAACK,KAAK,EAAEL,IAAI,CAACM,MAAM,EAAE;MACvDjB,IAAI,EAAEA,IAAI,IAAI9B,aAAa;MAC3BgD,MAAM,EAAE/C,UAAU;MAClByB,WAAW;MACXC,aAAa;MACbC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIE,IAAI,KAAK5B,gBAAgB,IAAI2B,UAAU,IAAI,IAAI,EAAE;MACnDgB,CAAC,CAACI,OAAO,CAACpB,UAAU,GAAGA,UAAU;IACnC;IACAgB,CAAC,CAACK,OAAO,GAAG7B,YAAY;IACxB,OAAOwB,CAAC;EACV,CAAC,CAAC;EACF/C,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,IAAIC,iBAAiB,EAAEC,kBAAkB;IACzC,CAACD,iBAAiB,GAAGlB,QAAQ,CAACG,OAAO,KAAK,IAAI,IAAIe,iBAAiB,CAACE,OAAO,CAACb,IAAI,CAACK,KAAK,EAAEL,IAAI,CAACM,MAAM,CAAC;IACpG,CAACM,kBAAkB,GAAGnB,QAAQ,CAACG,OAAO,KAAK,IAAI,IAAIgB,kBAAkB,CAACE,aAAa,CAACb,QAAQ,CAACc,GAAG,CAAC;EACnG,CAAC,EAAE,CAAChB,EAAE,EAAEC,IAAI,EAAEC,QAAQ,CAACc,GAAG,CAAC,CAAC;EAC5BnD,QAAQ,CAAC,MAAM;IACb,IAAIoD,kBAAkB;IACtB,IAAI,CAAClC,aAAa,EAAE,CAACkC,kBAAkB,GAAGvB,QAAQ,CAACG,OAAO,KAAK,IAAI,IAAIoB,kBAAkB,CAACC,MAAM,CAAC,CAAC;EACpG,CAAC,EAAEpC,WAAW,CAAC;EACf,MAAMqC,MAAM,GAAG,EAAE;EACjB,IAAI,CAAClC,iBAAiB,EAAEkC,MAAM,CAACC,IAAI,CAAC,aAAa9D,KAAK,CAACe,aAAa,CAAC,YAAY,EAAE;IACjFgD,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM,EAAE;IACjCC,IAAI,EAAE,CAAC1B,KAAK,EAAEC,MAAM;EACtB,CAAC,CAAC,CAAC;EACH,IAAI,CAACf,YAAY,EAAEmC,MAAM,CAACC,IAAI,CAAC,aAAa9D,KAAK,CAACe,aAAa,CAAC,YAAY,EAAE;IAC5EiD,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM,EAAE;IACjCF,GAAG,EAAE,WAAW;IAChBG,IAAI,EAAE,CAACvD,qBAAqB;EAC9B,CAAC,CAAC,CAAC;EACHX,KAAK,CAACmE,QAAQ,CAACC,OAAO,CAAC9C,QAAQ,EAAE+C,EAAE,IAAI;IACrCA,EAAE,IAAIR,MAAM,CAACC,IAAI,CAAC,aAAa9D,KAAK,CAACsE,YAAY,CAACD,EAAE,EAAE;MACpDN,GAAG,EAAEF,MAAM,CAACI,MAAM;MAClBD,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM;IACjC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO,aAAajE,KAAK,CAACe,aAAa,CAAC,gBAAgB,EAAEhB,QAAQ,CAAC;IACjEmC,GAAG,EAAEE,QAAQ;IACb8B,IAAI,EAAE,CAACxB,EAAE,EAAEG,MAAM;EACnB,CAAC,EAAEZ,KAAK,CAAC,EAAE4B,MAAM,CAAC;AACpB,CAAC,CAAC;AAEF,SAASzC,OAAO,EAAER,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}