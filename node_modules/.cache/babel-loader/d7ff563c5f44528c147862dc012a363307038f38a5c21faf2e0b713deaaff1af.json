{"ast": null, "code": "import { Mesh, Vector3, Color, FrontSide, Plane, Matrix4, Vector4, PerspectiveCamera, WebGLRenderTarget, UniformsUtils, UniformsLib, ShaderMaterial } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nclass Water extends Mesh {\n  constructor(geometry, options = {}) {\n    super(geometry);\n    this.isWater = true;\n    const scope = this;\n    const textureWidth = options.textureWidth !== void 0 ? options.textureWidth : 512;\n    const textureHeight = options.textureHeight !== void 0 ? options.textureHeight : 512;\n    const clipBias = options.clipBias !== void 0 ? options.clipBias : 0;\n    const alpha = options.alpha !== void 0 ? options.alpha : 1;\n    const time = options.time !== void 0 ? options.time : 0;\n    const normalSampler = options.waterNormals !== void 0 ? options.waterNormals : null;\n    const sunDirection = options.sunDirection !== void 0 ? options.sunDirection : new Vector3(0.70707, 0.70707, 0);\n    const sunColor = new Color(options.sunColor !== void 0 ? options.sunColor : 16777215);\n    const waterColor = new Color(options.waterColor !== void 0 ? options.waterColor : 8355711);\n    const eye = options.eye !== void 0 ? options.eye : new Vector3(0, 0, 0);\n    const distortionScale = options.distortionScale !== void 0 ? options.distortionScale : 20;\n    const side = options.side !== void 0 ? options.side : FrontSide;\n    const fog = options.fog !== void 0 ? options.fog : false;\n    const mirrorPlane = new Plane();\n    const normal = new Vector3();\n    const mirrorWorldPosition = new Vector3();\n    const cameraWorldPosition = new Vector3();\n    const rotationMatrix = new Matrix4();\n    const lookAtPosition = new Vector3(0, 0, -1);\n    const clipPlane = new Vector4();\n    const view = new Vector3();\n    const target = new Vector3();\n    const q = new Vector4();\n    const textureMatrix = new Matrix4();\n    const mirrorCamera = new PerspectiveCamera();\n    const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight);\n    const mirrorShader = {\n      uniforms: UniformsUtils.merge([UniformsLib[\"fog\"], UniformsLib[\"lights\"], {\n        normalSampler: {\n          value: null\n        },\n        mirrorSampler: {\n          value: null\n        },\n        alpha: {\n          value: 1\n        },\n        time: {\n          value: 0\n        },\n        size: {\n          value: 1\n        },\n        distortionScale: {\n          value: 20\n        },\n        textureMatrix: {\n          value: new Matrix4()\n        },\n        sunColor: {\n          value: new Color(8355711)\n        },\n        sunDirection: {\n          value: new Vector3(0.70707, 0.70707, 0)\n        },\n        eye: {\n          value: new Vector3()\n        },\n        waterColor: {\n          value: new Color(5592405)\n        }\n      }]),\n      vertexShader: (/* glsl */\n      `\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tuniform float time;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <shadowmap_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tmirrorCoord = modelMatrix * vec4( position, 1.0 );\n\t\t\t\t\tworldPosition = mirrorCoord.xyzw;\n\t\t\t\t\tmirrorCoord = textureMatrix * mirrorCoord;\n\t\t\t\t\tvec4 mvPosition =  modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t\t#include <beginnormal_vertex>\n\t\t\t\t#include <defaultnormal_vertex>\n\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t#include <fog_vertex>\n\t\t\t\t#include <shadowmap_vertex>\n\t\t\t}`),\n      fragmentShader: (/* glsl */\n      `\n\t\t\t\tuniform sampler2D mirrorSampler;\n\t\t\t\tuniform float alpha;\n\t\t\t\tuniform float time;\n\t\t\t\tuniform float size;\n\t\t\t\tuniform float distortionScale;\n\t\t\t\tuniform sampler2D normalSampler;\n\t\t\t\tuniform vec3 sunColor;\n\t\t\t\tuniform vec3 sunDirection;\n\t\t\t\tuniform vec3 eye;\n\t\t\t\tuniform vec3 waterColor;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\tvec4 getNoise( vec2 uv ) {\n\t\t\t\t\tvec2 uv0 = ( uv / 103.0 ) + vec2(time / 17.0, time / 29.0);\n\t\t\t\t\tvec2 uv1 = uv / 107.0-vec2( time / -19.0, time / 31.0 );\n\t\t\t\t\tvec2 uv2 = uv / vec2( 8907.0, 9803.0 ) + vec2( time / 101.0, time / 97.0 );\n\t\t\t\t\tvec2 uv3 = uv / vec2( 1091.0, 1027.0 ) - vec2( time / 109.0, time / -113.0 );\n\t\t\t\t\tvec4 noise = texture2D( normalSampler, uv0 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv1 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv2 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv3 );\n\t\t\t\t\treturn noise * 0.5 - 1.0;\n\t\t\t\t}\n\n\t\t\t\tvoid sunLight( const vec3 surfaceNormal, const vec3 eyeDirection, float shiny, float spec, float diffuse, inout vec3 diffuseColor, inout vec3 specularColor ) {\n\t\t\t\t\tvec3 reflection = normalize( reflect( -sunDirection, surfaceNormal ) );\n\t\t\t\t\tfloat direction = max( 0.0, dot( eyeDirection, reflection ) );\n\t\t\t\t\tspecularColor += pow( direction, shiny ) * sunColor * spec;\n\t\t\t\t\tdiffuseColor += max( dot( sunDirection, surfaceNormal ), 0.0 ) * sunColor * diffuse;\n\t\t\t\t}\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <packing>\n\t\t\t\t#include <bsdfs>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <lights_pars_begin>\n\t\t\t\t#include <shadowmap_pars_fragment>\n\t\t\t\t#include <shadowmask_pars_fragment>\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\t\t\t\t\tvec4 noise = getNoise( worldPosition.xz * size );\n\t\t\t\t\tvec3 surfaceNormal = normalize( noise.xzy * vec3( 1.5, 1.0, 1.5 ) );\n\n\t\t\t\t\tvec3 diffuseLight = vec3(0.0);\n\t\t\t\t\tvec3 specularLight = vec3(0.0);\n\n\t\t\t\t\tvec3 worldToEye = eye-worldPosition.xyz;\n\t\t\t\t\tvec3 eyeDirection = normalize( worldToEye );\n\t\t\t\t\tsunLight( surfaceNormal, eyeDirection, 100.0, 2.0, 0.5, diffuseLight, specularLight );\n\n\t\t\t\t\tfloat distance = length(worldToEye);\n\n\t\t\t\t\tvec2 distortion = surfaceNormal.xz * ( 0.001 + 1.0 / distance ) * distortionScale;\n\t\t\t\t\tvec3 reflectionSample = vec3( texture2D( mirrorSampler, mirrorCoord.xy / mirrorCoord.w + distortion ) );\n\n\t\t\t\t\tfloat theta = max( dot( eyeDirection, surfaceNormal ), 0.0 );\n\t\t\t\t\tfloat rf0 = 0.3;\n\t\t\t\t\tfloat reflectance = rf0 + ( 1.0 - rf0 ) * pow( ( 1.0 - theta ), 5.0 );\n\t\t\t\t\tvec3 scatter = max( 0.0, dot( surfaceNormal, eyeDirection ) ) * waterColor;\n\t\t\t\t\tvec3 albedo = mix( ( sunColor * diffuseLight * 0.3 + scatter ) * getShadowMask(), ( vec3( 0.1 ) + reflectionSample * 0.9 + reflectionSample * specularLight ), reflectance);\n\t\t\t\t\tvec3 outgoingLight = albedo;\n\t\t\t\t\tgl_FragColor = vec4( outgoingLight, alpha );\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n\t\t\t\t\t#include <fog_fragment>\t\n\t\t\t\t}`)\n    };\n    const material = new ShaderMaterial({\n      fragmentShader: mirrorShader.fragmentShader,\n      vertexShader: mirrorShader.vertexShader,\n      uniforms: UniformsUtils.clone(mirrorShader.uniforms),\n      lights: true,\n      side,\n      fog\n    });\n    material.uniforms[\"mirrorSampler\"].value = renderTarget.texture;\n    material.uniforms[\"textureMatrix\"].value = textureMatrix;\n    material.uniforms[\"alpha\"].value = alpha;\n    material.uniforms[\"time\"].value = time;\n    material.uniforms[\"normalSampler\"].value = normalSampler;\n    material.uniforms[\"sunColor\"].value = sunColor;\n    material.uniforms[\"waterColor\"].value = waterColor;\n    material.uniforms[\"sunDirection\"].value = sunDirection;\n    material.uniforms[\"distortionScale\"].value = distortionScale;\n    material.uniforms[\"eye\"].value = eye;\n    scope.material = material;\n    scope.onBeforeRender = function (renderer, scene, camera) {\n      mirrorWorldPosition.setFromMatrixPosition(scope.matrixWorld);\n      cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n      rotationMatrix.extractRotation(scope.matrixWorld);\n      normal.set(0, 0, 1);\n      normal.applyMatrix4(rotationMatrix);\n      view.subVectors(mirrorWorldPosition, cameraWorldPosition);\n      if (view.dot(normal) > 0) return;\n      view.reflect(normal).negate();\n      view.add(mirrorWorldPosition);\n      rotationMatrix.extractRotation(camera.matrixWorld);\n      lookAtPosition.set(0, 0, -1);\n      lookAtPosition.applyMatrix4(rotationMatrix);\n      lookAtPosition.add(cameraWorldPosition);\n      target.subVectors(mirrorWorldPosition, lookAtPosition);\n      target.reflect(normal).negate();\n      target.add(mirrorWorldPosition);\n      mirrorCamera.position.copy(view);\n      mirrorCamera.up.set(0, 1, 0);\n      mirrorCamera.up.applyMatrix4(rotationMatrix);\n      mirrorCamera.up.reflect(normal);\n      mirrorCamera.lookAt(target);\n      mirrorCamera.far = camera.far;\n      mirrorCamera.updateMatrixWorld();\n      mirrorCamera.projectionMatrix.copy(camera.projectionMatrix);\n      textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n      textureMatrix.multiply(mirrorCamera.projectionMatrix);\n      textureMatrix.multiply(mirrorCamera.matrixWorldInverse);\n      mirrorPlane.setFromNormalAndCoplanarPoint(normal, mirrorWorldPosition);\n      mirrorPlane.applyMatrix4(mirrorCamera.matrixWorldInverse);\n      clipPlane.set(mirrorPlane.normal.x, mirrorPlane.normal.y, mirrorPlane.normal.z, mirrorPlane.constant);\n      const projectionMatrix = mirrorCamera.projectionMatrix;\n      q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n      q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n      q.z = -1;\n      q.w = (1 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n      clipPlane.multiplyScalar(2 / clipPlane.dot(q));\n      projectionMatrix.elements[2] = clipPlane.x;\n      projectionMatrix.elements[6] = clipPlane.y;\n      projectionMatrix.elements[10] = clipPlane.z + 1 - clipBias;\n      projectionMatrix.elements[14] = clipPlane.w;\n      eye.setFromMatrixPosition(camera.matrixWorld);\n      const currentRenderTarget = renderer.getRenderTarget();\n      const currentXrEnabled = renderer.xr.enabled;\n      const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;\n      scope.visible = false;\n      renderer.xr.enabled = false;\n      renderer.shadowMap.autoUpdate = false;\n      renderer.setRenderTarget(renderTarget);\n      renderer.state.buffers.depth.setMask(true);\n      if (renderer.autoClear === false) renderer.clear();\n      renderer.render(scene, mirrorCamera);\n      scope.visible = true;\n      renderer.xr.enabled = currentXrEnabled;\n      renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;\n      renderer.setRenderTarget(currentRenderTarget);\n      const viewport = camera.viewport;\n      if (viewport !== void 0) {\n        renderer.state.viewport(viewport);\n      }\n    };\n  }\n}\nexport { Water };", "map": {"version": 3, "names": ["Water", "<PERSON><PERSON>", "constructor", "geometry", "options", "isWater", "scope", "textureWidth", "textureHeight", "clipBias", "alpha", "time", "normalSampler", "waterNormals", "sunDirection", "Vector3", "sunColor", "Color", "waterColor", "eye", "distortionScale", "side", "FrontSide", "fog", "mirrorPlane", "Plane", "normal", "mirrorWorldPosition", "cameraWorldPosition", "rotationMatrix", "Matrix4", "lookAtPosition", "clipPlane", "Vector4", "view", "target", "q", "textureMatrix", "mirrorCamera", "PerspectiveCamera", "renderTarget", "WebGLRenderTarget", "mirrorShader", "uniforms", "UniformsUtils", "merge", "UniformsLib", "value", "mirrorSampler", "size", "vertexShader", "fragmentShader", "version", "material", "ShaderMaterial", "clone", "lights", "texture", "onBeforeRender", "renderer", "scene", "camera", "setFromMatrixPosition", "matrixWorld", "extractRotation", "set", "applyMatrix4", "subVectors", "dot", "reflect", "negate", "add", "position", "copy", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "multiply", "matrixWorldInverse", "setFromNormalAndCoplanarPoint", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "viewport"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/objects/Water.js"], "sourcesContent": ["import {\n  Color,\n  FrontSide,\n  Matrix4,\n  <PERSON>sh,\n  PerspectiveCamera,\n  Plane,\n  ShaderMaterial,\n  UniformsLib,\n  UniformsUtils,\n  Vector3,\n  Vector4,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Work based on :\n * https://github.com/Slayvin: Flat mirror for three.js\n * https://home.adelphi.edu/~stemkoski/ : An implementation of water shader based on the flat mirror\n * http://29a.ch/ && http://29a.ch/slides/2012/webglwater/ : Water shader explanations in WebGL\n */\n\nclass Water extends Mesh {\n  constructor(geometry, options = {}) {\n    super(geometry)\n\n    this.isWater = true\n\n    const scope = this\n\n    const textureWidth = options.textureWidth !== undefined ? options.textureWidth : 512\n    const textureHeight = options.textureHeight !== undefined ? options.textureHeight : 512\n\n    const clipBias = options.clipBias !== undefined ? options.clipBias : 0.0\n    const alpha = options.alpha !== undefined ? options.alpha : 1.0\n    const time = options.time !== undefined ? options.time : 0.0\n    const normalSampler = options.waterNormals !== undefined ? options.waterNormals : null\n    const sunDirection = options.sunDirection !== undefined ? options.sunDirection : new Vector3(0.70707, 0.70707, 0.0)\n    const sunColor = new Color(options.sunColor !== undefined ? options.sunColor : 0xffffff)\n    const waterColor = new Color(options.waterColor !== undefined ? options.waterColor : 0x7f7f7f)\n    const eye = options.eye !== undefined ? options.eye : new Vector3(0, 0, 0)\n    const distortionScale = options.distortionScale !== undefined ? options.distortionScale : 20.0\n    const side = options.side !== undefined ? options.side : FrontSide\n    const fog = options.fog !== undefined ? options.fog : false\n\n    //\n\n    const mirrorPlane = new Plane()\n    const normal = new Vector3()\n    const mirrorWorldPosition = new Vector3()\n    const cameraWorldPosition = new Vector3()\n    const rotationMatrix = new Matrix4()\n    const lookAtPosition = new Vector3(0, 0, -1)\n    const clipPlane = new Vector4()\n\n    const view = new Vector3()\n    const target = new Vector3()\n    const q = new Vector4()\n\n    const textureMatrix = new Matrix4()\n\n    const mirrorCamera = new PerspectiveCamera()\n\n    const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight)\n\n    const mirrorShader = {\n      uniforms: UniformsUtils.merge([\n        UniformsLib['fog'],\n        UniformsLib['lights'],\n        {\n          normalSampler: { value: null },\n          mirrorSampler: { value: null },\n          alpha: { value: 1.0 },\n          time: { value: 0.0 },\n          size: { value: 1.0 },\n          distortionScale: { value: 20.0 },\n          textureMatrix: { value: new Matrix4() },\n          sunColor: { value: new Color(0x7f7f7f) },\n          sunDirection: { value: new Vector3(0.70707, 0.70707, 0) },\n          eye: { value: new Vector3() },\n          waterColor: { value: new Color(0x555555) },\n        },\n      ]),\n\n      vertexShader: /* glsl */ `\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tuniform float time;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <shadowmap_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tmirrorCoord = modelMatrix * vec4( position, 1.0 );\n\t\t\t\t\tworldPosition = mirrorCoord.xyzw;\n\t\t\t\t\tmirrorCoord = textureMatrix * mirrorCoord;\n\t\t\t\t\tvec4 mvPosition =  modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t\t#include <beginnormal_vertex>\n\t\t\t\t#include <defaultnormal_vertex>\n\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t#include <fog_vertex>\n\t\t\t\t#include <shadowmap_vertex>\n\t\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\t\t\t\tuniform sampler2D mirrorSampler;\n\t\t\t\tuniform float alpha;\n\t\t\t\tuniform float time;\n\t\t\t\tuniform float size;\n\t\t\t\tuniform float distortionScale;\n\t\t\t\tuniform sampler2D normalSampler;\n\t\t\t\tuniform vec3 sunColor;\n\t\t\t\tuniform vec3 sunDirection;\n\t\t\t\tuniform vec3 eye;\n\t\t\t\tuniform vec3 waterColor;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\tvec4 getNoise( vec2 uv ) {\n\t\t\t\t\tvec2 uv0 = ( uv / 103.0 ) + vec2(time / 17.0, time / 29.0);\n\t\t\t\t\tvec2 uv1 = uv / 107.0-vec2( time / -19.0, time / 31.0 );\n\t\t\t\t\tvec2 uv2 = uv / vec2( 8907.0, 9803.0 ) + vec2( time / 101.0, time / 97.0 );\n\t\t\t\t\tvec2 uv3 = uv / vec2( 1091.0, 1027.0 ) - vec2( time / 109.0, time / -113.0 );\n\t\t\t\t\tvec4 noise = texture2D( normalSampler, uv0 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv1 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv2 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv3 );\n\t\t\t\t\treturn noise * 0.5 - 1.0;\n\t\t\t\t}\n\n\t\t\t\tvoid sunLight( const vec3 surfaceNormal, const vec3 eyeDirection, float shiny, float spec, float diffuse, inout vec3 diffuseColor, inout vec3 specularColor ) {\n\t\t\t\t\tvec3 reflection = normalize( reflect( -sunDirection, surfaceNormal ) );\n\t\t\t\t\tfloat direction = max( 0.0, dot( eyeDirection, reflection ) );\n\t\t\t\t\tspecularColor += pow( direction, shiny ) * sunColor * spec;\n\t\t\t\t\tdiffuseColor += max( dot( sunDirection, surfaceNormal ), 0.0 ) * sunColor * diffuse;\n\t\t\t\t}\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <packing>\n\t\t\t\t#include <bsdfs>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <lights_pars_begin>\n\t\t\t\t#include <shadowmap_pars_fragment>\n\t\t\t\t#include <shadowmask_pars_fragment>\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\t\t\t\t\tvec4 noise = getNoise( worldPosition.xz * size );\n\t\t\t\t\tvec3 surfaceNormal = normalize( noise.xzy * vec3( 1.5, 1.0, 1.5 ) );\n\n\t\t\t\t\tvec3 diffuseLight = vec3(0.0);\n\t\t\t\t\tvec3 specularLight = vec3(0.0);\n\n\t\t\t\t\tvec3 worldToEye = eye-worldPosition.xyz;\n\t\t\t\t\tvec3 eyeDirection = normalize( worldToEye );\n\t\t\t\t\tsunLight( surfaceNormal, eyeDirection, 100.0, 2.0, 0.5, diffuseLight, specularLight );\n\n\t\t\t\t\tfloat distance = length(worldToEye);\n\n\t\t\t\t\tvec2 distortion = surfaceNormal.xz * ( 0.001 + 1.0 / distance ) * distortionScale;\n\t\t\t\t\tvec3 reflectionSample = vec3( texture2D( mirrorSampler, mirrorCoord.xy / mirrorCoord.w + distortion ) );\n\n\t\t\t\t\tfloat theta = max( dot( eyeDirection, surfaceNormal ), 0.0 );\n\t\t\t\t\tfloat rf0 = 0.3;\n\t\t\t\t\tfloat reflectance = rf0 + ( 1.0 - rf0 ) * pow( ( 1.0 - theta ), 5.0 );\n\t\t\t\t\tvec3 scatter = max( 0.0, dot( surfaceNormal, eyeDirection ) ) * waterColor;\n\t\t\t\t\tvec3 albedo = mix( ( sunColor * diffuseLight * 0.3 + scatter ) * getShadowMask(), ( vec3( 0.1 ) + reflectionSample * 0.9 + reflectionSample * specularLight ), reflectance);\n\t\t\t\t\tvec3 outgoingLight = albedo;\n\t\t\t\t\tgl_FragColor = vec4( outgoingLight, alpha );\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t\t\t#include <fog_fragment>\t\n\t\t\t\t}`,\n    }\n\n    const material = new ShaderMaterial({\n      fragmentShader: mirrorShader.fragmentShader,\n      vertexShader: mirrorShader.vertexShader,\n      uniforms: UniformsUtils.clone(mirrorShader.uniforms),\n      lights: true,\n      side: side,\n      fog: fog,\n    })\n\n    material.uniforms['mirrorSampler'].value = renderTarget.texture\n    material.uniforms['textureMatrix'].value = textureMatrix\n    material.uniforms['alpha'].value = alpha\n    material.uniforms['time'].value = time\n    material.uniforms['normalSampler'].value = normalSampler\n    material.uniforms['sunColor'].value = sunColor\n    material.uniforms['waterColor'].value = waterColor\n    material.uniforms['sunDirection'].value = sunDirection\n    material.uniforms['distortionScale'].value = distortionScale\n\n    material.uniforms['eye'].value = eye\n\n    scope.material = material\n\n    scope.onBeforeRender = function (renderer, scene, camera) {\n      mirrorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n      cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n      rotationMatrix.extractRotation(scope.matrixWorld)\n\n      normal.set(0, 0, 1)\n      normal.applyMatrix4(rotationMatrix)\n\n      view.subVectors(mirrorWorldPosition, cameraWorldPosition)\n\n      // Avoid rendering when mirror is facing away\n\n      if (view.dot(normal) > 0) return\n\n      view.reflect(normal).negate()\n      view.add(mirrorWorldPosition)\n\n      rotationMatrix.extractRotation(camera.matrixWorld)\n\n      lookAtPosition.set(0, 0, -1)\n      lookAtPosition.applyMatrix4(rotationMatrix)\n      lookAtPosition.add(cameraWorldPosition)\n\n      target.subVectors(mirrorWorldPosition, lookAtPosition)\n      target.reflect(normal).negate()\n      target.add(mirrorWorldPosition)\n\n      mirrorCamera.position.copy(view)\n      mirrorCamera.up.set(0, 1, 0)\n      mirrorCamera.up.applyMatrix4(rotationMatrix)\n      mirrorCamera.up.reflect(normal)\n      mirrorCamera.lookAt(target)\n\n      mirrorCamera.far = camera.far // Used in WebGLBackground\n\n      mirrorCamera.updateMatrixWorld()\n      mirrorCamera.projectionMatrix.copy(camera.projectionMatrix)\n\n      // Update the texture matrix\n      textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n      textureMatrix.multiply(mirrorCamera.projectionMatrix)\n      textureMatrix.multiply(mirrorCamera.matrixWorldInverse)\n\n      // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n      // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n      mirrorPlane.setFromNormalAndCoplanarPoint(normal, mirrorWorldPosition)\n      mirrorPlane.applyMatrix4(mirrorCamera.matrixWorldInverse)\n\n      clipPlane.set(mirrorPlane.normal.x, mirrorPlane.normal.y, mirrorPlane.normal.z, mirrorPlane.constant)\n\n      const projectionMatrix = mirrorCamera.projectionMatrix\n\n      q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0]\n      q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5]\n      q.z = -1.0\n      q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]\n\n      // Calculate the scaled plane vector\n      clipPlane.multiplyScalar(2.0 / clipPlane.dot(q))\n\n      // Replacing the third row of the projection matrix\n      projectionMatrix.elements[2] = clipPlane.x\n      projectionMatrix.elements[6] = clipPlane.y\n      projectionMatrix.elements[10] = clipPlane.z + 1.0 - clipBias\n      projectionMatrix.elements[14] = clipPlane.w\n\n      eye.setFromMatrixPosition(camera.matrixWorld)\n\n      // Render\n\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      const currentXrEnabled = renderer.xr.enabled\n      const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n\n      scope.visible = false\n\n      renderer.xr.enabled = false // Avoid camera modification and recursion\n      renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n\n      renderer.setRenderTarget(renderTarget)\n\n      renderer.state.buffers.depth.setMask(true) // make sure the depth buffer is writable so it can be properly cleared, see #18897\n\n      if (renderer.autoClear === false) renderer.clear()\n      renderer.render(scene, mirrorCamera)\n\n      scope.visible = true\n\n      renderer.xr.enabled = currentXrEnabled\n      renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n\n      renderer.setRenderTarget(currentRenderTarget)\n\n      // Restore viewport\n\n      const viewport = camera.viewport\n\n      if (viewport !== undefined) {\n        renderer.state.viewport(viewport)\n      }\n    }\n  }\n}\n\nexport { Water }\n"], "mappings": ";;AAuBA,MAAMA,KAAA,SAAcC,IAAA,CAAK;EACvBC,YAAYC,QAAA,EAAUC,OAAA,GAAU,IAAI;IAClC,MAAMD,QAAQ;IAEd,KAAKE,OAAA,GAAU;IAEf,MAAMC,KAAA,GAAQ;IAEd,MAAMC,YAAA,GAAeH,OAAA,CAAQG,YAAA,KAAiB,SAAYH,OAAA,CAAQG,YAAA,GAAe;IACjF,MAAMC,aAAA,GAAgBJ,OAAA,CAAQI,aAAA,KAAkB,SAAYJ,OAAA,CAAQI,aAAA,GAAgB;IAEpF,MAAMC,QAAA,GAAWL,OAAA,CAAQK,QAAA,KAAa,SAAYL,OAAA,CAAQK,QAAA,GAAW;IACrE,MAAMC,KAAA,GAAQN,OAAA,CAAQM,KAAA,KAAU,SAAYN,OAAA,CAAQM,KAAA,GAAQ;IAC5D,MAAMC,IAAA,GAAOP,OAAA,CAAQO,IAAA,KAAS,SAAYP,OAAA,CAAQO,IAAA,GAAO;IACzD,MAAMC,aAAA,GAAgBR,OAAA,CAAQS,YAAA,KAAiB,SAAYT,OAAA,CAAQS,YAAA,GAAe;IAClF,MAAMC,YAAA,GAAeV,OAAA,CAAQU,YAAA,KAAiB,SAAYV,OAAA,CAAQU,YAAA,GAAe,IAAIC,OAAA,CAAQ,SAAS,SAAS,CAAG;IAClH,MAAMC,QAAA,GAAW,IAAIC,KAAA,CAAMb,OAAA,CAAQY,QAAA,KAAa,SAAYZ,OAAA,CAAQY,QAAA,GAAW,QAAQ;IACvF,MAAME,UAAA,GAAa,IAAID,KAAA,CAAMb,OAAA,CAAQc,UAAA,KAAe,SAAYd,OAAA,CAAQc,UAAA,GAAa,OAAQ;IAC7F,MAAMC,GAAA,GAAMf,OAAA,CAAQe,GAAA,KAAQ,SAAYf,OAAA,CAAQe,GAAA,GAAM,IAAIJ,OAAA,CAAQ,GAAG,GAAG,CAAC;IACzE,MAAMK,eAAA,GAAkBhB,OAAA,CAAQgB,eAAA,KAAoB,SAAYhB,OAAA,CAAQgB,eAAA,GAAkB;IAC1F,MAAMC,IAAA,GAAOjB,OAAA,CAAQiB,IAAA,KAAS,SAAYjB,OAAA,CAAQiB,IAAA,GAAOC,SAAA;IACzD,MAAMC,GAAA,GAAMnB,OAAA,CAAQmB,GAAA,KAAQ,SAAYnB,OAAA,CAAQmB,GAAA,GAAM;IAItD,MAAMC,WAAA,GAAc,IAAIC,KAAA,CAAO;IAC/B,MAAMC,MAAA,GAAS,IAAIX,OAAA,CAAS;IAC5B,MAAMY,mBAAA,GAAsB,IAAIZ,OAAA,CAAS;IACzC,MAAMa,mBAAA,GAAsB,IAAIb,OAAA,CAAS;IACzC,MAAMc,cAAA,GAAiB,IAAIC,OAAA,CAAS;IACpC,MAAMC,cAAA,GAAiB,IAAIhB,OAAA,CAAQ,GAAG,GAAG,EAAE;IAC3C,MAAMiB,SAAA,GAAY,IAAIC,OAAA,CAAS;IAE/B,MAAMC,IAAA,GAAO,IAAInB,OAAA,CAAS;IAC1B,MAAMoB,MAAA,GAAS,IAAIpB,OAAA,CAAS;IAC5B,MAAMqB,CAAA,GAAI,IAAIH,OAAA,CAAS;IAEvB,MAAMI,aAAA,GAAgB,IAAIP,OAAA,CAAS;IAEnC,MAAMQ,YAAA,GAAe,IAAIC,iBAAA,CAAmB;IAE5C,MAAMC,YAAA,GAAe,IAAIC,iBAAA,CAAkBlC,YAAA,EAAcC,aAAa;IAEtE,MAAMkC,YAAA,GAAe;MACnBC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,CAC5BC,WAAA,CAAY,KAAK,GACjBA,WAAA,CAAY,QAAQ,GACpB;QACElC,aAAA,EAAe;UAAEmC,KAAA,EAAO;QAAM;QAC9BC,aAAA,EAAe;UAAED,KAAA,EAAO;QAAM;QAC9BrC,KAAA,EAAO;UAAEqC,KAAA,EAAO;QAAK;QACrBpC,IAAA,EAAM;UAAEoC,KAAA,EAAO;QAAK;QACpBE,IAAA,EAAM;UAAEF,KAAA,EAAO;QAAK;QACpB3B,eAAA,EAAiB;UAAE2B,KAAA,EAAO;QAAM;QAChCV,aAAA,EAAe;UAAEU,KAAA,EAAO,IAAIjB,OAAA;QAAW;QACvCd,QAAA,EAAU;UAAE+B,KAAA,EAAO,IAAI9B,KAAA,CAAM,OAAQ;QAAG;QACxCH,YAAA,EAAc;UAAEiC,KAAA,EAAO,IAAIhC,OAAA,CAAQ,SAAS,SAAS,CAAC;QAAG;QACzDI,GAAA,EAAK;UAAE4B,KAAA,EAAO,IAAIhC,OAAA;QAAW;QAC7BG,UAAA,EAAY;UAAE6B,KAAA,EAAO,IAAI9B,KAAA,CAAM,OAAQ;QAAG;MAC3C,EACF;MAEDiC,YAAA;MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MA0BzBC,cAAA;MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAsEhBC,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;IAGrD;IAED,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAe;MAClCH,cAAA,EAAgBT,YAAA,CAAaS,cAAA;MAC7BD,YAAA,EAAcR,YAAA,CAAaQ,YAAA;MAC3BP,QAAA,EAAUC,aAAA,CAAcW,KAAA,CAAMb,YAAA,CAAaC,QAAQ;MACnDa,MAAA,EAAQ;MACRnC,IAAA;MACAE;IACN,CAAK;IAED8B,QAAA,CAASV,QAAA,CAAS,eAAe,EAAEI,KAAA,GAAQP,YAAA,CAAaiB,OAAA;IACxDJ,QAAA,CAASV,QAAA,CAAS,eAAe,EAAEI,KAAA,GAAQV,aAAA;IAC3CgB,QAAA,CAASV,QAAA,CAAS,OAAO,EAAEI,KAAA,GAAQrC,KAAA;IACnC2C,QAAA,CAASV,QAAA,CAAS,MAAM,EAAEI,KAAA,GAAQpC,IAAA;IAClC0C,QAAA,CAASV,QAAA,CAAS,eAAe,EAAEI,KAAA,GAAQnC,aAAA;IAC3CyC,QAAA,CAASV,QAAA,CAAS,UAAU,EAAEI,KAAA,GAAQ/B,QAAA;IACtCqC,QAAA,CAASV,QAAA,CAAS,YAAY,EAAEI,KAAA,GAAQ7B,UAAA;IACxCmC,QAAA,CAASV,QAAA,CAAS,cAAc,EAAEI,KAAA,GAAQjC,YAAA;IAC1CuC,QAAA,CAASV,QAAA,CAAS,iBAAiB,EAAEI,KAAA,GAAQ3B,eAAA;IAE7CiC,QAAA,CAASV,QAAA,CAAS,KAAK,EAAEI,KAAA,GAAQ5B,GAAA;IAEjCb,KAAA,CAAM+C,QAAA,GAAWA,QAAA;IAEjB/C,KAAA,CAAMoD,cAAA,GAAiB,UAAUC,QAAA,EAAUC,KAAA,EAAOC,MAAA,EAAQ;MACxDlC,mBAAA,CAAoBmC,qBAAA,CAAsBxD,KAAA,CAAMyD,WAAW;MAC3DnC,mBAAA,CAAoBkC,qBAAA,CAAsBD,MAAA,CAAOE,WAAW;MAE5DlC,cAAA,CAAemC,eAAA,CAAgB1D,KAAA,CAAMyD,WAAW;MAEhDrC,MAAA,CAAOuC,GAAA,CAAI,GAAG,GAAG,CAAC;MAClBvC,MAAA,CAAOwC,YAAA,CAAarC,cAAc;MAElCK,IAAA,CAAKiC,UAAA,CAAWxC,mBAAA,EAAqBC,mBAAmB;MAIxD,IAAIM,IAAA,CAAKkC,GAAA,CAAI1C,MAAM,IAAI,GAAG;MAE1BQ,IAAA,CAAKmC,OAAA,CAAQ3C,MAAM,EAAE4C,MAAA,CAAQ;MAC7BpC,IAAA,CAAKqC,GAAA,CAAI5C,mBAAmB;MAE5BE,cAAA,CAAemC,eAAA,CAAgBH,MAAA,CAAOE,WAAW;MAEjDhC,cAAA,CAAekC,GAAA,CAAI,GAAG,GAAG,EAAE;MAC3BlC,cAAA,CAAemC,YAAA,CAAarC,cAAc;MAC1CE,cAAA,CAAewC,GAAA,CAAI3C,mBAAmB;MAEtCO,MAAA,CAAOgC,UAAA,CAAWxC,mBAAA,EAAqBI,cAAc;MACrDI,MAAA,CAAOkC,OAAA,CAAQ3C,MAAM,EAAE4C,MAAA,CAAQ;MAC/BnC,MAAA,CAAOoC,GAAA,CAAI5C,mBAAmB;MAE9BW,YAAA,CAAakC,QAAA,CAASC,IAAA,CAAKvC,IAAI;MAC/BI,YAAA,CAAaoC,EAAA,CAAGT,GAAA,CAAI,GAAG,GAAG,CAAC;MAC3B3B,YAAA,CAAaoC,EAAA,CAAGR,YAAA,CAAarC,cAAc;MAC3CS,YAAA,CAAaoC,EAAA,CAAGL,OAAA,CAAQ3C,MAAM;MAC9BY,YAAA,CAAaqC,MAAA,CAAOxC,MAAM;MAE1BG,YAAA,CAAasC,GAAA,GAAMf,MAAA,CAAOe,GAAA;MAE1BtC,YAAA,CAAauC,iBAAA,CAAmB;MAChCvC,YAAA,CAAawC,gBAAA,CAAiBL,IAAA,CAAKZ,MAAA,CAAOiB,gBAAgB;MAG1DzC,aAAA,CAAc4B,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;MAChG5B,aAAA,CAAc0C,QAAA,CAASzC,YAAA,CAAawC,gBAAgB;MACpDzC,aAAA,CAAc0C,QAAA,CAASzC,YAAA,CAAa0C,kBAAkB;MAItDxD,WAAA,CAAYyD,6BAAA,CAA8BvD,MAAA,EAAQC,mBAAmB;MACrEH,WAAA,CAAY0C,YAAA,CAAa5B,YAAA,CAAa0C,kBAAkB;MAExDhD,SAAA,CAAUiC,GAAA,CAAIzC,WAAA,CAAYE,MAAA,CAAOwD,CAAA,EAAG1D,WAAA,CAAYE,MAAA,CAAOyD,CAAA,EAAG3D,WAAA,CAAYE,MAAA,CAAO0D,CAAA,EAAG5D,WAAA,CAAY6D,QAAQ;MAEpG,MAAMP,gBAAA,GAAmBxC,YAAA,CAAawC,gBAAA;MAEtC1C,CAAA,CAAE8C,CAAA,IAAKI,IAAA,CAAKC,IAAA,CAAKvD,SAAA,CAAUkD,CAAC,IAAIJ,gBAAA,CAAiBU,QAAA,CAAS,CAAC,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,CAAC;MAC3FpD,CAAA,CAAE+C,CAAA,IAAKG,IAAA,CAAKC,IAAA,CAAKvD,SAAA,CAAUmD,CAAC,IAAIL,gBAAA,CAAiBU,QAAA,CAAS,CAAC,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,CAAC;MAC3FpD,CAAA,CAAEgD,CAAA,GAAI;MACNhD,CAAA,CAAEqD,CAAA,IAAK,IAAMX,gBAAA,CAAiBU,QAAA,CAAS,EAAE,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,EAAE;MAG1ExD,SAAA,CAAU0D,cAAA,CAAe,IAAM1D,SAAA,CAAUoC,GAAA,CAAIhC,CAAC,CAAC;MAG/C0C,gBAAA,CAAiBU,QAAA,CAAS,CAAC,IAAIxD,SAAA,CAAUkD,CAAA;MACzCJ,gBAAA,CAAiBU,QAAA,CAAS,CAAC,IAAIxD,SAAA,CAAUmD,CAAA;MACzCL,gBAAA,CAAiBU,QAAA,CAAS,EAAE,IAAIxD,SAAA,CAAUoD,CAAA,GAAI,IAAM3E,QAAA;MACpDqE,gBAAA,CAAiBU,QAAA,CAAS,EAAE,IAAIxD,SAAA,CAAUyD,CAAA;MAE1CtE,GAAA,CAAI2C,qBAAA,CAAsBD,MAAA,CAAOE,WAAW;MAI5C,MAAM4B,mBAAA,GAAsBhC,QAAA,CAASiC,eAAA,CAAiB;MAEtD,MAAMC,gBAAA,GAAmBlC,QAAA,CAASmC,EAAA,CAAGC,OAAA;MACrC,MAAMC,uBAAA,GAA0BrC,QAAA,CAASsC,SAAA,CAAUC,UAAA;MAEnD5F,KAAA,CAAM6F,OAAA,GAAU;MAEhBxC,QAAA,CAASmC,EAAA,CAAGC,OAAA,GAAU;MACtBpC,QAAA,CAASsC,SAAA,CAAUC,UAAA,GAAa;MAEhCvC,QAAA,CAASyC,eAAA,CAAgB5D,YAAY;MAErCmB,QAAA,CAAS0C,KAAA,CAAMC,OAAA,CAAQC,KAAA,CAAMC,OAAA,CAAQ,IAAI;MAEzC,IAAI7C,QAAA,CAAS8C,SAAA,KAAc,OAAO9C,QAAA,CAAS+C,KAAA,CAAO;MAClD/C,QAAA,CAASgD,MAAA,CAAO/C,KAAA,EAAOtB,YAAY;MAEnChC,KAAA,CAAM6F,OAAA,GAAU;MAEhBxC,QAAA,CAASmC,EAAA,CAAGC,OAAA,GAAUF,gBAAA;MACtBlC,QAAA,CAASsC,SAAA,CAAUC,UAAA,GAAaF,uBAAA;MAEhCrC,QAAA,CAASyC,eAAA,CAAgBT,mBAAmB;MAI5C,MAAMiB,QAAA,GAAW/C,MAAA,CAAO+C,QAAA;MAExB,IAAIA,QAAA,KAAa,QAAW;QAC1BjD,QAAA,CAAS0C,KAAA,CAAMO,QAAA,CAASA,QAAQ;MACjC;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}