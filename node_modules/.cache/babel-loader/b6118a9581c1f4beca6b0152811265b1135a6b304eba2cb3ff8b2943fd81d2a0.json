{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Object3D, Mesh, SphereGeometry, MeshBasicMaterial, BufferGeometry, Float32BufferAttribute, Line, LineBasicMaterial, InstancedMesh, Vector3 } from \"three\";\nconst _o = /* @__PURE__ */new Object3D();\nconst _v = /* @__PURE__ */new Vector3();\nclass RaycasterHelper extends Object3D {\n  constructor(raycaster, numberOfHitsToVisualize = 20) {\n    super();\n    __publicField(this, \"raycaster\");\n    __publicField(this, \"hits\");\n    __publicField(this, \"origin\");\n    __publicField(this, \"near\");\n    __publicField(this, \"far\");\n    __publicField(this, \"nearToFar\");\n    __publicField(this, \"originToNear\");\n    __publicField(this, \"hitPoints\");\n    __publicField(this, \"colors\", {\n      near: 16777215,\n      far: 16777215,\n      originToNear: 3355443,\n      nearToFar: 16777215,\n      origin: [978050, 16711771]\n    });\n    __publicField(this, \"setColors\", colors => {\n      const _colors = {\n        ...this.colors,\n        ...colors\n      };\n      this.near.material.color.set(_colors.near);\n      this.far.material.color.set(_colors.far);\n      this.nearToFar.material.color.set(_colors.nearToFar);\n      this.originToNear.material.color.set(_colors.originToNear);\n    });\n    __publicField(this, \"update\", () => {\n      var _a;\n      const origin = this.raycaster.ray.origin;\n      const direction = this.raycaster.ray.direction;\n      this.origin.position.copy(origin);\n      this.near.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.near));\n      this.far.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.far));\n      this.far.lookAt(origin);\n      this.near.lookAt(origin);\n      let pos = this.nearToFar.geometry.getAttribute(\"position\");\n      pos.set([...this.near.position.toArray(), ...this.far.position.toArray()]);\n      pos.needsUpdate = true;\n      pos = this.originToNear.geometry.getAttribute(\"position\");\n      pos.set([...origin.toArray(), ...this.near.position.toArray()]);\n      pos.needsUpdate = true;\n      for (let i = 0; i < this.numberOfHitsToVisualize; i++) {\n        const hit = (_a = this.hits) == null ? void 0 : _a[i];\n        if (hit) {\n          const {\n            point\n          } = hit;\n          _o.position.copy(point);\n          _o.scale.setScalar(1);\n        } else {\n          _o.scale.setScalar(0);\n        }\n        _o.updateMatrix();\n        this.hitPoints.setMatrixAt(i, _o.matrix);\n      }\n      this.hitPoints.instanceMatrix.needsUpdate = true;\n      this.origin.material.color.set(this.hits.length > 0 ? this.colors.origin[0] : this.colors.origin[1]);\n    });\n    __publicField(this, \"dispose\", () => {\n      this.origin.geometry.dispose();\n      this.origin.material.dispose();\n      this.near.geometry.dispose();\n      this.near.material.dispose();\n      this.far.geometry.dispose();\n      this.far.material.dispose();\n      this.nearToFar.geometry.dispose();\n      this.nearToFar.material.dispose();\n      this.originToNear.geometry.dispose();\n      this.originToNear.material.dispose();\n      this.hitPoints.dispose();\n    });\n    this.numberOfHitsToVisualize = numberOfHitsToVisualize;\n    this.raycaster = raycaster;\n    this.hits = [];\n    this.origin = new Mesh(new SphereGeometry(0.04, 32), new MeshBasicMaterial());\n    this.origin.name = \"RaycasterHelper_origin\";\n    this.origin.raycast = () => null;\n    const size = 0.1;\n    let geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute([-size, size, 0, size, size, 0, size, -size, 0, -size, -size, 0, -size, size, 0], 3));\n    this.near = new Line(geometry, new LineBasicMaterial());\n    this.near.name = \"RaycasterHelper_near\";\n    this.near.raycast = () => null;\n    this.far = new Line(geometry, new LineBasicMaterial());\n    this.far.name = \"RaycasterHelper_far\";\n    this.far.raycast = () => null;\n    this.nearToFar = new Line(new BufferGeometry(), new LineBasicMaterial());\n    this.nearToFar.name = \"RaycasterHelper_nearToFar\";\n    this.nearToFar.raycast = () => null;\n    this.nearToFar.geometry.setFromPoints([_v, _v]);\n    this.originToNear = new Line(this.nearToFar.geometry.clone(), new LineBasicMaterial());\n    this.originToNear.name = \"RaycasterHelper_originToNear\";\n    this.originToNear.raycast = () => null;\n    this.hitPoints = new InstancedMesh(new SphereGeometry(0.04), new MeshBasicMaterial(), this.numberOfHitsToVisualize);\n    this.hitPoints.name = \"RaycasterHelper_hits\";\n    this.hitPoints.raycast = () => null;\n    this.add(this.nearToFar);\n    this.add(this.originToNear);\n    this.add(this.near);\n    this.add(this.far);\n    this.add(this.origin);\n    this.add(this.hitPoints);\n    this.setColors();\n  }\n}\nexport { RaycasterHelper };", "map": {"version": 3, "names": ["_o", "Object3D", "_v", "Vector3", "RaycasterHelper", "constructor", "raycaster", "numberOfHitsToVisualize", "__publicField", "near", "far", "originToNear", "nearToFar", "origin", "colors", "_colors", "material", "color", "set", "ray", "direction", "position", "copy", "add", "clone", "multiplyScalar", "lookAt", "pos", "geometry", "getAttribute", "toArray", "needsUpdate", "i", "hit", "_a", "hits", "point", "scale", "setScalar", "updateMatrix", "hitPoints", "setMatrixAt", "matrix", "instanceMatrix", "length", "dispose", "<PERSON><PERSON>", "SphereGeometry", "MeshBasicMaterial", "name", "raycast", "size", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "Line", "LineBasicMaterial", "setFromPoints", "In<PERSON>d<PERSON>esh", "setColors"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/helpers/RaycasterHelper.ts"], "sourcesContent": ["/**\n * from https://github.com/gsimone/things/tree/main/packages/three-raycaster-helper\n */\n\nimport {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InstancedMesh,\n  Intersection,\n  Line,\n  LineBasicMaterial,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Raycaster,\n  SphereGeometry,\n  Vector3,\n} from 'three'\n\nconst _o = /* @__PURE__ */ new Object3D()\nconst _v = /* @__PURE__ */ new Vector3()\n\nclass RaycasterHelper extends Object3D {\n  raycaster: Raycaster\n  hits: Intersection[]\n\n  origin: Mesh<SphereGeometry, MeshBasicMaterial>\n  near: Line<BufferGeometry, LineBasicMaterial>\n  far: Line<BufferGeometry, LineBasicMaterial>\n\n  nearToFar: Line<BufferGeometry, LineBasicMaterial>\n  originToNear: Line<BufferGeometry, LineBasicMaterial>\n\n  hitPoints: InstancedMesh\n\n  colors = {\n    near: 0xffffff,\n    far: 0xffffff,\n    originToNear: 0x333333,\n    nearToFar: 0xffffff,\n    origin: [0x0eec82, 0xff005b],\n  }\n\n  constructor(raycaster: Raycaster, public numberOfHitsToVisualize = 20) {\n    super()\n    this.raycaster = raycaster\n\n    this.hits = []\n\n    this.origin = new Mesh(new SphereGeometry(0.04, 32), new MeshBasicMaterial())\n    this.origin.name = 'RaycasterHelper_origin'\n    this.origin.raycast = () => null\n\n    const size = 0.1\n    let geometry = new BufferGeometry()\n    // prettier-ignore\n    geometry.setAttribute( 'position', new Float32BufferAttribute( [\n              - size, size, 0,\n              size, size, 0,\n              size, - size, 0,\n              - size, - size, 0,\n              - size, size, 0\n          ], 3 ) );\n\n    this.near = new Line(geometry, new LineBasicMaterial())\n    this.near.name = 'RaycasterHelper_near'\n    this.near.raycast = () => null\n\n    this.far = new Line(geometry, new LineBasicMaterial())\n    this.far.name = 'RaycasterHelper_far'\n    this.far.raycast = () => null\n\n    this.nearToFar = new Line(new BufferGeometry(), new LineBasicMaterial())\n    this.nearToFar.name = 'RaycasterHelper_nearToFar'\n    this.nearToFar.raycast = () => null\n\n    this.nearToFar.geometry.setFromPoints([_v, _v])\n\n    this.originToNear = new Line(this.nearToFar.geometry.clone(), new LineBasicMaterial())\n    this.originToNear.name = 'RaycasterHelper_originToNear'\n    this.originToNear.raycast = () => null\n\n    this.hitPoints = new InstancedMesh(new SphereGeometry(0.04), new MeshBasicMaterial(), this.numberOfHitsToVisualize)\n    this.hitPoints.name = 'RaycasterHelper_hits'\n    this.hitPoints.raycast = () => null\n\n    this.add(this.nearToFar)\n    this.add(this.originToNear)\n\n    this.add(this.near)\n    this.add(this.far)\n\n    this.add(this.origin)\n    this.add(this.hitPoints)\n\n    this.setColors()\n  }\n\n  setColors = (colors?: Partial<typeof this.colors>) => {\n    const _colors = {\n      ...this.colors,\n      ...colors,\n    }\n\n    this.near.material.color.set(_colors.near)\n    this.far.material.color.set(_colors.far)\n    this.nearToFar.material.color.set(_colors.nearToFar)\n    this.originToNear.material.color.set(_colors.originToNear)\n  }\n\n  update = () => {\n    const origin = this.raycaster.ray.origin\n    const direction = this.raycaster.ray.direction\n\n    this.origin.position.copy(origin)\n\n    this.near.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.near))\n\n    this.far.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.far))\n\n    this.far.lookAt(origin)\n    this.near.lookAt(origin)\n\n    let pos = this.nearToFar.geometry.getAttribute('position') as BufferAttribute\n    pos.set([...this.near.position.toArray(), ...this.far.position.toArray()])\n    pos.needsUpdate = true\n\n    pos = this.originToNear.geometry.getAttribute('position') as BufferAttribute\n    pos.set([...origin.toArray(), ...this.near.position.toArray()])\n    pos.needsUpdate = true\n\n    /**\n     * Update hit points visualization\n     */\n    for (let i = 0; i < this.numberOfHitsToVisualize; i++) {\n      const hit = this.hits?.[i]\n\n      if (hit) {\n        const { point } = hit\n        _o.position.copy(point)\n        _o.scale.setScalar(1)\n      } else {\n        _o.scale.setScalar(0)\n      }\n\n      _o.updateMatrix()\n\n      this.hitPoints.setMatrixAt(i, _o.matrix)\n    }\n\n    this.hitPoints.instanceMatrix.needsUpdate = true\n\n    /**\n     * Update the color of the origin based on wether there are hits.\n     */\n    this.origin.material.color.set(this.hits.length > 0 ? this.colors.origin[0] : this.colors.origin[1])\n  }\n\n  dispose = () => {\n    this.origin.geometry.dispose()\n    this.origin.material.dispose()\n    this.near.geometry.dispose()\n    this.near.material.dispose()\n    this.far.geometry.dispose()\n    this.far.material.dispose()\n    this.nearToFar.geometry.dispose()\n    this.nearToFar.material.dispose()\n    this.originToNear.geometry.dispose()\n    this.originToNear.material.dispose()\n    this.hitPoints.dispose()\n  }\n}\n\nexport { RaycasterHelper }\n"], "mappings": ";;;;;;;;;;;;AAoBA,MAAMA,EAAA,sBAAyBC,QAAA;AAC/B,MAAMC,EAAA,sBAAyBC,OAAA;AAE/B,MAAMC,eAAA,SAAwBH,QAAA,CAAS;EAqBrCI,YAAYC,SAAA,EAA6BC,uBAAA,GAA0B,IAAI;IAC/D;IArBRC,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAEAA,aAAA,iBAAS;MACPC,IAAA,EAAM;MACNC,GAAA,EAAK;MACLC,YAAA,EAAc;MACdC,SAAA,EAAW;MACXC,MAAA,EAAQ,CAAC,QAAU,QAAQ;IAAA;IA0D7BL,aAAA,oBAAaM,MAAA,IAAyC;MACpD,MAAMC,OAAA,GAAU;QACd,GAAG,KAAKD,MAAA;QACR,GAAGA;MAAA;MAGL,KAAKL,IAAA,CAAKO,QAAA,CAASC,KAAA,CAAMC,GAAA,CAAIH,OAAA,CAAQN,IAAI;MACzC,KAAKC,GAAA,CAAIM,QAAA,CAASC,KAAA,CAAMC,GAAA,CAAIH,OAAA,CAAQL,GAAG;MACvC,KAAKE,SAAA,CAAUI,QAAA,CAASC,KAAA,CAAMC,GAAA,CAAIH,OAAA,CAAQH,SAAS;MACnD,KAAKD,YAAA,CAAaK,QAAA,CAASC,KAAA,CAAMC,GAAA,CAAIH,OAAA,CAAQJ,YAAY;IAAA;IAG3DH,aAAA,iBAAS,MAAM;;MACP,MAAAK,MAAA,GAAS,KAAKP,SAAA,CAAUa,GAAA,CAAIN,MAAA;MAC5B,MAAAO,SAAA,GAAY,KAAKd,SAAA,CAAUa,GAAA,CAAIC,SAAA;MAEhC,KAAAP,MAAA,CAAOQ,QAAA,CAASC,IAAA,CAAKT,MAAM;MAEhC,KAAKJ,IAAA,CAAKY,QAAA,CAASC,IAAA,CAAKT,MAAM,EAAEU,GAAA,CAAIH,SAAA,CAAUI,KAAA,GAAQC,cAAA,CAAe,KAAKnB,SAAA,CAAUG,IAAI,CAAC;MAEzF,KAAKC,GAAA,CAAIW,QAAA,CAASC,IAAA,CAAKT,MAAM,EAAEU,GAAA,CAAIH,SAAA,CAAUI,KAAA,GAAQC,cAAA,CAAe,KAAKnB,SAAA,CAAUI,GAAG,CAAC;MAElF,KAAAA,GAAA,CAAIgB,MAAA,CAAOb,MAAM;MACjB,KAAAJ,IAAA,CAAKiB,MAAA,CAAOb,MAAM;MAEvB,IAAIc,GAAA,GAAM,KAAKf,SAAA,CAAUgB,QAAA,CAASC,YAAA,CAAa,UAAU;MACzDF,GAAA,CAAIT,GAAA,CAAI,CAAC,GAAG,KAAKT,IAAA,CAAKY,QAAA,CAASS,OAAA,IAAW,GAAG,KAAKpB,GAAA,CAAIW,QAAA,CAASS,OAAA,EAAS,CAAC;MACzEH,GAAA,CAAII,WAAA,GAAc;MAElBJ,GAAA,GAAM,KAAKhB,YAAA,CAAaiB,QAAA,CAASC,YAAA,CAAa,UAAU;MACxDF,GAAA,CAAIT,GAAA,CAAI,CAAC,GAAGL,MAAA,CAAOiB,OAAA,CAAQ,GAAG,GAAG,KAAKrB,IAAA,CAAKY,QAAA,CAASS,OAAA,CAAQ,CAAC,CAAC;MAC9DH,GAAA,CAAII,WAAA,GAAc;MAKlB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKzB,uBAAA,EAAyByB,CAAA,IAAK;QAC/C,MAAAC,GAAA,IAAMC,EAAA,QAAKC,IAAA,KAAL,gBAAAD,EAAA,CAAYF,CAAA;QAExB,IAAIC,GAAA,EAAK;UACD;YAAEG;UAAU,IAAAH,GAAA;UACfjC,EAAA,CAAAqB,QAAA,CAASC,IAAA,CAAKc,KAAK;UACnBpC,EAAA,CAAAqC,KAAA,CAAMC,SAAA,CAAU,CAAC;QAAA,OACf;UACFtC,EAAA,CAAAqC,KAAA,CAAMC,SAAA,CAAU,CAAC;QACtB;QAEAtC,EAAA,CAAGuC,YAAA,CAAa;QAEhB,KAAKC,SAAA,CAAUC,WAAA,CAAYT,CAAA,EAAGhC,EAAA,CAAG0C,MAAM;MACzC;MAEK,KAAAF,SAAA,CAAUG,cAAA,CAAeZ,WAAA,GAAc;MAK5C,KAAKlB,MAAA,CAAOG,QAAA,CAASC,KAAA,CAAMC,GAAA,CAAI,KAAKiB,IAAA,CAAKS,MAAA,GAAS,IAAI,KAAK9B,MAAA,CAAOD,MAAA,CAAO,CAAC,IAAI,KAAKC,MAAA,CAAOD,MAAA,CAAO,CAAC,CAAC;IAAA;IAGrGL,aAAA,kBAAU,MAAM;MACT,KAAAK,MAAA,CAAOe,QAAA,CAASiB,OAAA;MAChB,KAAAhC,MAAA,CAAOG,QAAA,CAAS6B,OAAA;MAChB,KAAApC,IAAA,CAAKmB,QAAA,CAASiB,OAAA;MACd,KAAApC,IAAA,CAAKO,QAAA,CAAS6B,OAAA;MACd,KAAAnC,GAAA,CAAIkB,QAAA,CAASiB,OAAA;MACb,KAAAnC,GAAA,CAAIM,QAAA,CAAS6B,OAAA;MACb,KAAAjC,SAAA,CAAUgB,QAAA,CAASiB,OAAA;MACnB,KAAAjC,SAAA,CAAUI,QAAA,CAAS6B,OAAA;MACnB,KAAAlC,YAAA,CAAaiB,QAAA,CAASiB,OAAA;MACtB,KAAAlC,YAAA,CAAaK,QAAA,CAAS6B,OAAA;MAC3B,KAAKL,SAAA,CAAUK,OAAA;IAAQ;IA9HgB,KAAAtC,uBAAA,GAAAA,uBAAA;IAEvC,KAAKD,SAAA,GAAYA,SAAA;IAEjB,KAAK6B,IAAA,GAAO;IAEP,KAAAtB,MAAA,GAAS,IAAIiC,IAAA,CAAK,IAAIC,cAAA,CAAe,MAAM,EAAE,GAAG,IAAIC,iBAAA,EAAmB;IAC5E,KAAKnC,MAAA,CAAOoC,IAAA,GAAO;IACd,KAAApC,MAAA,CAAOqC,OAAA,GAAU,MAAM;IAE5B,MAAMC,IAAA,GAAO;IACT,IAAAvB,QAAA,GAAW,IAAIwB,cAAA;IAEVxB,QAAA,CAAAyB,YAAA,CAAc,YAAY,IAAIC,sBAAA,CAAwB,CACrD,CAAEH,IAAA,EAAMA,IAAA,EAAM,GACdA,IAAA,EAAMA,IAAA,EAAM,GACZA,IAAA,EAAM,CAAEA,IAAA,EAAM,GACd,CAAEA,IAAA,EAAM,CAAEA,IAAA,EAAM,GAChB,CAAEA,IAAA,EAAMA,IAAA,EAAM,IACf,CAAE,CAAE;IAEb,KAAK1C,IAAA,GAAO,IAAI8C,IAAA,CAAK3B,QAAA,EAAU,IAAI4B,iBAAA,EAAmB;IACtD,KAAK/C,IAAA,CAAKwC,IAAA,GAAO;IACZ,KAAAxC,IAAA,CAAKyC,OAAA,GAAU,MAAM;IAE1B,KAAKxC,GAAA,GAAM,IAAI6C,IAAA,CAAK3B,QAAA,EAAU,IAAI4B,iBAAA,EAAmB;IACrD,KAAK9C,GAAA,CAAIuC,IAAA,GAAO;IACX,KAAAvC,GAAA,CAAIwC,OAAA,GAAU,MAAM;IAEpB,KAAAtC,SAAA,GAAY,IAAI2C,IAAA,CAAK,IAAIH,cAAA,IAAkB,IAAII,iBAAA,EAAmB;IACvE,KAAK5C,SAAA,CAAUqC,IAAA,GAAO;IACjB,KAAArC,SAAA,CAAUsC,OAAA,GAAU,MAAM;IAE/B,KAAKtC,SAAA,CAAUgB,QAAA,CAAS6B,aAAA,CAAc,CAACvD,EAAA,EAAIA,EAAE,CAAC;IAEzC,KAAAS,YAAA,GAAe,IAAI4C,IAAA,CAAK,KAAK3C,SAAA,CAAUgB,QAAA,CAASJ,KAAA,CAAM,GAAG,IAAIgC,iBAAA,EAAmB;IACrF,KAAK7C,YAAA,CAAasC,IAAA,GAAO;IACpB,KAAAtC,YAAA,CAAauC,OAAA,GAAU,MAAM;IAE7B,KAAAV,SAAA,GAAY,IAAIkB,aAAA,CAAc,IAAIX,cAAA,CAAe,IAAI,GAAG,IAAIC,iBAAA,IAAqB,KAAKzC,uBAAuB;IAClH,KAAKiC,SAAA,CAAUS,IAAA,GAAO;IACjB,KAAAT,SAAA,CAAUU,OAAA,GAAU,MAAM;IAE1B,KAAA3B,GAAA,CAAI,KAAKX,SAAS;IAClB,KAAAW,GAAA,CAAI,KAAKZ,YAAY;IAErB,KAAAY,GAAA,CAAI,KAAKd,IAAI;IACb,KAAAc,GAAA,CAAI,KAAKb,GAAG;IAEZ,KAAAa,GAAA,CAAI,KAAKV,MAAM;IACf,KAAAU,GAAA,CAAI,KAAKiB,SAAS;IAEvB,KAAKmB,SAAA,CAAU;EACjB;AA2EF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}