{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nconst RenderCubeTexture = /* @__PURE__ */React.forwardRef(({\n  children,\n  compute,\n  renderPriority = -1,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  resolution = 896,\n  near = 0.1,\n  far = 1000,\n  flip = false,\n  position,\n  rotation,\n  scale,\n  quaternion,\n  matrix,\n  matrixAutoUpdate,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const camera = React.useRef(null);\n  const fbo = React.useMemo(() => {\n    const fbo = new THREE.WebGLCubeRenderTarget(Math.max((resolution || size.width) * viewport.dpr, (resolution || size.height) * viewport.dpr), {\n      stencilBuffer,\n      depthBuffer,\n      generateMipmaps\n    });\n    fbo.texture.isRenderTargetTexture = !flip;\n    fbo.texture.flipY = true;\n    fbo.texture.type = THREE.HalfFloatType;\n    return fbo;\n  }, [resolution, flip]);\n  React.useEffect(() => {\n    return () => fbo.dispose();\n  }, [fbo]);\n  const [vScene] = React.useState(() => new THREE.Scene());\n  React.useImperativeHandle(forwardRef, () => ({\n    scene: vScene,\n    fbo,\n    camera: camera.current\n  }), [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    camera: camera\n  }, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  })), vScene, {\n    events: {\n      compute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)), /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo],\n    position: position,\n    rotation: rotation,\n    scale: scale,\n    quaternion: quaternion,\n    matrix: matrix,\n    matrixAutoUpdate: matrixAutoUpdate\n  }));\n});\n\n// The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  camera\n}) {\n  let count = 0;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      camera.current.update(state.gl, state.scene);\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\nexport { RenderCubeTexture };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "createPortal", "useFrame", "RenderCubeTexture", "forwardRef", "children", "compute", "renderPriority", "eventPriority", "frames", "Infinity", "stencil<PERSON>uffer", "depthBuffer", "generateMipmaps", "resolution", "near", "far", "flip", "position", "rotation", "scale", "quaternion", "matrix", "matrixAutoUpdate", "props", "size", "viewport", "camera", "useRef", "fbo", "useMemo", "WebGLCubeRenderTarget", "Math", "max", "width", "dpr", "height", "texture", "isRenderTargetTexture", "flipY", "type", "HalfFloatType", "useEffect", "dispose", "vScene", "useState", "Scene", "useImperativeHandle", "scene", "current", "createElement", "Fragment", "Container", "onPointerOver", "events", "priority", "object", "ref", "args", "count", "state", "update", "gl"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/RenderCubeTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\n\nconst RenderCubeTexture = /* @__PURE__ */React.forwardRef(({\n  children,\n  compute,\n  renderPriority = -1,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  resolution = 896,\n  near = 0.1,\n  far = 1000,\n  flip = false,\n  position,\n  rotation,\n  scale,\n  quaternion,\n  matrix,\n  matrixAutoUpdate,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const camera = React.useRef(null);\n  const fbo = React.useMemo(() => {\n    const fbo = new THREE.WebGLCubeRenderTarget(Math.max((resolution || size.width) * viewport.dpr, (resolution || size.height) * viewport.dpr), {\n      stencilBuffer,\n      depthBuffer,\n      generateMipmaps\n    });\n    fbo.texture.isRenderTargetTexture = !flip;\n    fbo.texture.flipY = true;\n    fbo.texture.type = THREE.HalfFloatType;\n    return fbo;\n  }, [resolution, flip]);\n  React.useEffect(() => {\n    return () => fbo.dispose();\n  }, [fbo]);\n  const [vScene] = React.useState(() => new THREE.Scene());\n  React.useImperativeHandle(forwardRef, () => ({\n    scene: vScene,\n    fbo,\n    camera: camera.current\n  }), [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    camera: camera\n  }, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  })), vScene, {\n    events: {\n      compute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)), /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo],\n    position: position,\n    rotation: rotation,\n    scale: scale,\n    quaternion: quaternion,\n    matrix: matrix,\n    matrixAutoUpdate: matrixAutoUpdate\n  }));\n});\n\n// The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  camera\n}) {\n  let count = 0;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      camera.current.update(state.gl, state.scene);\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\n\nexport { RenderCubeTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AAErE,MAAMC,iBAAiB,GAAG,eAAeJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACzDC,QAAQ;EACRC,OAAO;EACPC,cAAc,GAAG,CAAC,CAAC;EACnBC,aAAa,GAAG,CAAC;EACjBC,MAAM,GAAGC,QAAQ;EACjBC,aAAa,GAAG,KAAK;EACrBC,WAAW,GAAG,IAAI;EAClBC,eAAe,GAAG,KAAK;EACvBC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,IAAI;EACVC,IAAI,GAAG,KAAK;EACZC,QAAQ;EACRC,QAAQ;EACRC,KAAK;EACLC,UAAU;EACVC,MAAM;EACNC,gBAAgB;EAChB,GAAGC;AACL,CAAC,EAAEpB,UAAU,KAAK;EAChB,MAAM;IACJqB,IAAI;IACJC;EACF,CAAC,GAAG1B,QAAQ,CAAC,CAAC;EACd,MAAM2B,MAAM,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,GAAG,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,MAAM;IAC9B,MAAMD,GAAG,GAAG,IAAI/B,KAAK,CAACiC,qBAAqB,CAACC,IAAI,CAACC,GAAG,CAAC,CAACnB,UAAU,IAAIW,IAAI,CAACS,KAAK,IAAIR,QAAQ,CAACS,GAAG,EAAE,CAACrB,UAAU,IAAIW,IAAI,CAACW,MAAM,IAAIV,QAAQ,CAACS,GAAG,CAAC,EAAE;MAC3IxB,aAAa;MACbC,WAAW;MACXC;IACF,CAAC,CAAC;IACFgB,GAAG,CAACQ,OAAO,CAACC,qBAAqB,GAAG,CAACrB,IAAI;IACzCY,GAAG,CAACQ,OAAO,CAACE,KAAK,GAAG,IAAI;IACxBV,GAAG,CAACQ,OAAO,CAACG,IAAI,GAAG1C,KAAK,CAAC2C,aAAa;IACtC,OAAOZ,GAAG;EACZ,CAAC,EAAE,CAACf,UAAU,EAAEG,IAAI,CAAC,CAAC;EACtBlB,KAAK,CAAC2C,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMb,GAAG,CAACc,OAAO,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACd,GAAG,CAAC,CAAC;EACT,MAAM,CAACe,MAAM,CAAC,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,MAAM,IAAI/C,KAAK,CAACgD,KAAK,CAAC,CAAC,CAAC;EACxD/C,KAAK,CAACgD,mBAAmB,CAAC3C,UAAU,EAAE,OAAO;IAC3C4C,KAAK,EAAEJ,MAAM;IACbf,GAAG;IACHF,MAAM,EAAEA,MAAM,CAACsB;EACjB,CAAC,CAAC,EAAE,CAACpB,GAAG,CAAC,CAAC;EACV,OAAO,aAAa9B,KAAK,CAACmD,aAAa,CAACnD,KAAK,CAACoD,QAAQ,EAAE,IAAI,EAAElD,YAAY,CAAC,aAAaF,KAAK,CAACmD,aAAa,CAACE,SAAS,EAAE;IACrH7C,cAAc,EAAEA,cAAc;IAC9BE,MAAM,EAAEA,MAAM;IACdkB,MAAM,EAAEA;EACV,CAAC,EAAEtB,QAAQ,EAAE,aAAaN,KAAK,CAACmD,aAAa,CAAC,OAAO,EAAE;IACrDG,aAAa,EAAEA,CAAA,KAAM;EACvB,CAAC,CAAC,CAAC,EAAET,MAAM,EAAE;IACXU,MAAM,EAAE;MACNhD,OAAO;MACPiD,QAAQ,EAAE/C;IACZ;EACF,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACmD,aAAa,CAAC,WAAW,EAAErD,QAAQ,CAAC;IACzD2D,MAAM,EAAE3B,GAAG,CAACQ;EACd,CAAC,EAAEb,KAAK,CAAC,CAAC,EAAE,aAAazB,KAAK,CAACmD,aAAa,CAAC,YAAY,EAAE;IACzDO,GAAG,EAAE9B,MAAM;IACX+B,IAAI,EAAE,CAAC3C,IAAI,EAAEC,GAAG,EAAEa,GAAG,CAAC;IACtBX,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA,QAAQ;IAClBC,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBC,MAAM,EAAEA,MAAM;IACdC,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAAS6B,SAASA,CAAC;EACjB3C,MAAM;EACNF,cAAc;EACdF,QAAQ;EACRsB;AACF,CAAC,EAAE;EACD,IAAIgC,KAAK,GAAG,CAAC;EACbzD,QAAQ,CAAC0D,KAAK,IAAI;IAChB,IAAInD,MAAM,KAAKC,QAAQ,IAAIiD,KAAK,GAAGlD,MAAM,EAAE;MACzCkB,MAAM,CAACsB,OAAO,CAACY,MAAM,CAACD,KAAK,CAACE,EAAE,EAAEF,KAAK,CAACZ,KAAK,CAAC;MAC5CW,KAAK,EAAE;IACT;EACF,CAAC,EAAEpD,cAAc,CAAC;EAClB,OAAO,aAAaR,KAAK,CAACmD,aAAa,CAACnD,KAAK,CAACoD,QAAQ,EAAE,IAAI,EAAE9C,QAAQ,CAAC;AACzE;AAEA,SAASF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}