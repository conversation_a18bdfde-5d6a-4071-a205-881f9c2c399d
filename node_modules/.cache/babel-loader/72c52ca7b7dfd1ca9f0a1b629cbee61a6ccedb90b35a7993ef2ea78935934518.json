{"ast": null, "code": "/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && (exports.ConcurrentRoot = 1, exports.ContinuousEventPriority = 8, exports.DefaultEventPriority = 32, exports.DiscreteEventPriority = 2, exports.IdleEventPriority = *********, exports.LegacyRoot = 0, exports.NoEventPriority = 0);", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "exports", "ConcurrentRoot", "ContinuousEventPriority", "DefaultEventPriority", "DiscreteEventPriority", "IdleEventPriority", "LegacyRoot", "NoEventPriority"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/react-reconciler/cjs/react-reconciler-constants.development.js"], "sourcesContent": ["/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  ((exports.ConcurrentRoot = 1),\n  (exports.ContinuousEventPriority = 8),\n  (exports.DefaultEventPriority = 32),\n  (exports.DiscreteEventPriority = 2),\n  (exports.IdleEventPriority = *********),\n  (exports.LegacyRoot = 0),\n  (exports.NoEventPriority = 0));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,YAAY,KAAKA,OAAO,CAACC,GAAG,CAACC,QAAQ,KACjCC,OAAO,CAACC,cAAc,GAAG,CAAC,EAC3BD,OAAO,CAACE,uBAAuB,GAAG,CAAC,EACnCF,OAAO,CAACG,oBAAoB,GAAG,EAAE,EACjCH,OAAO,CAACI,qBAAqB,GAAG,CAAC,EACjCJ,OAAO,CAACK,iBAAiB,GAAG,SAAS,EACrCL,OAAO,CAACM,UAAU,GAAG,CAAC,EACtBN,OAAO,CAACO,eAAe,GAAG,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}