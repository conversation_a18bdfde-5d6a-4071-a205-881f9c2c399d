{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { useGizmoContext } from './GizmoHelper.js';\nimport { Vector3, CanvasTexture } from 'three';\nconst colors = {\n  bg: '#f0f0f0',\n  hover: '#999',\n  text: 'black',\n  stroke: 'black'\n};\nconst defaultFaces = ['Right', 'Left', 'Top', 'Bottom', 'Front', 'Back'];\nconst makePositionVector = xyz => new Vector3(...xyz).multiplyScalar(0.38);\nconst corners = /* @__PURE__ */[[1, 1, 1], [1, 1, -1], [1, -1, 1], [1, -1, -1], [-1, 1, 1], [-1, 1, -1], [-1, -1, 1], [-1, -1, -1]].map(makePositionVector);\nconst cornerDimensions = [0.25, 0.25, 0.25];\nconst edges = /* @__PURE__ */[[1, 1, 0], [1, 0, 1], [1, 0, -1], [1, -1, 0], [0, 1, 1], [0, 1, -1], [0, -1, 1], [0, -1, -1], [-1, 1, 0], [-1, 0, 1], [-1, 0, -1], [-1, -1, 0]].map(makePositionVector);\nconst edgeDimensions = /* @__PURE__ */edges.map(edge => edge.toArray().map(axis => axis == 0 ? 0.5 : 0.25));\nconst FaceMaterial = ({\n  hover,\n  index,\n  font = '20px Inter var, Arial, sans-serif',\n  faces = defaultFaces,\n  color = colors.bg,\n  hoverColor = colors.hover,\n  textColor = colors.text,\n  strokeColor = colors.stroke,\n  opacity = 1\n}) => {\n  const gl = useThree(state => state.gl);\n  const texture = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 128;\n    canvas.height = 128;\n    const context = canvas.getContext('2d');\n    context.fillStyle = color;\n    context.fillRect(0, 0, canvas.width, canvas.height);\n    context.strokeStyle = strokeColor;\n    context.strokeRect(0, 0, canvas.width, canvas.height);\n    context.font = font;\n    context.textAlign = 'center';\n    context.fillStyle = textColor;\n    context.fillText(faces[index].toUpperCase(), 64, 76);\n    return new CanvasTexture(canvas);\n  }, [index, faces, font, color, textColor, strokeColor]);\n  return /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    map: texture,\n    \"map-anisotropy\": gl.capabilities.getMaxAnisotropy() || 1,\n    attach: `material-${index}`,\n    color: hover ? hoverColor : 'white',\n    transparent: true,\n    opacity: opacity\n  });\n};\nconst FaceCube = props => {\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const [hover, setHover] = React.useState(null);\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setHover(null);\n  };\n  const handleClick = e => {\n    e.stopPropagation();\n    tweenCamera(e.face.normal);\n  };\n  const handlePointerMove = e => {\n    e.stopPropagation();\n    setHover(Math.floor(e.faceIndex / 2));\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    onPointerOut: handlePointerOut,\n    onPointerMove: handlePointerMove,\n    onClick: props.onClick || handleClick\n  }, [...Array(6)].map((_, index) => /*#__PURE__*/React.createElement(FaceMaterial, _extends({\n    key: index,\n    index: index,\n    hover: hover === index\n  }, props))), /*#__PURE__*/React.createElement(\"boxGeometry\", null));\n};\nconst EdgeCube = ({\n  onClick,\n  dimensions,\n  position,\n  hoverColor = colors.hover\n}) => {\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const [hover, setHover] = React.useState(false);\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setHover(false);\n  };\n  const handlePointerOver = e => {\n    e.stopPropagation();\n    setHover(true);\n  };\n  const handleClick = e => {\n    e.stopPropagation();\n    tweenCamera(position);\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    scale: 1.01,\n    position: position,\n    onPointerOver: handlePointerOver,\n    onPointerOut: handlePointerOut,\n    onClick: onClick || handleClick\n  }, /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    color: hover ? hoverColor : 'white',\n    transparent: true,\n    opacity: 0.6,\n    visible: hover\n  }), /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: dimensions\n  }));\n};\nconst GizmoViewcube = props => {\n  return /*#__PURE__*/React.createElement(\"group\", {\n    scale: [60, 60, 60]\n  }, /*#__PURE__*/React.createElement(FaceCube, props), edges.map((edge, index) => /*#__PURE__*/React.createElement(EdgeCube, _extends({\n    key: index,\n    position: edge,\n    dimensions: edgeDimensions[index]\n  }, props))), corners.map((corner, index) => /*#__PURE__*/React.createElement(EdgeCube, _extends({\n    key: index,\n    position: corner,\n    dimensions: cornerDimensions\n  }, props))));\n};\nexport { GizmoViewcube };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useGizmoContext", "Vector3", "CanvasTexture", "colors", "bg", "hover", "text", "stroke", "defaultFaces", "makePositionVector", "xyz", "multiplyScalar", "corners", "map", "cornerDimensions", "edges", "edgeDimensions", "edge", "toArray", "axis", "FaceMaterial", "index", "font", "faces", "color", "hoverColor", "textColor", "strokeColor", "opacity", "gl", "state", "texture", "useMemo", "canvas", "document", "createElement", "width", "height", "context", "getContext", "fillStyle", "fillRect", "strokeStyle", "strokeRect", "textAlign", "fillText", "toUpperCase", "capabilities", "getMaxAnisotropy", "attach", "transparent", "FaceCube", "props", "tweenCamera", "setHover", "useState", "handlePointerOut", "e", "stopPropagation", "handleClick", "face", "normal", "handlePointerMove", "Math", "floor", "faceIndex", "onPointerOut", "onPointerMove", "onClick", "Array", "_", "key", "EdgeCube", "dimensions", "position", "handlePointerOver", "scale", "onPointerOver", "visible", "args", "GizmoViewcube", "corner"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/GizmoViewcube.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { useGizmoContext } from './GizmoHelper.js';\nimport { Vector3, CanvasTexture } from 'three';\n\nconst colors = {\n  bg: '#f0f0f0',\n  hover: '#999',\n  text: 'black',\n  stroke: 'black'\n};\nconst defaultFaces = ['Right', 'Left', 'Top', 'Bottom', 'Front', 'Back'];\nconst makePositionVector = xyz => new Vector3(...xyz).multiplyScalar(0.38);\nconst corners = /* @__PURE__ */[[1, 1, 1], [1, 1, -1], [1, -1, 1], [1, -1, -1], [-1, 1, 1], [-1, 1, -1], [-1, -1, 1], [-1, -1, -1]].map(makePositionVector);\nconst cornerDimensions = [0.25, 0.25, 0.25];\nconst edges = /* @__PURE__ */[[1, 1, 0], [1, 0, 1], [1, 0, -1], [1, -1, 0], [0, 1, 1], [0, 1, -1], [0, -1, 1], [0, -1, -1], [-1, 1, 0], [-1, 0, 1], [-1, 0, -1], [-1, -1, 0]].map(makePositionVector);\nconst edgeDimensions = /* @__PURE__ */edges.map(edge => edge.toArray().map(axis => axis == 0 ? 0.5 : 0.25));\nconst FaceMaterial = ({\n  hover,\n  index,\n  font = '20px Inter var, Arial, sans-serif',\n  faces = defaultFaces,\n  color = colors.bg,\n  hoverColor = colors.hover,\n  textColor = colors.text,\n  strokeColor = colors.stroke,\n  opacity = 1\n}) => {\n  const gl = useThree(state => state.gl);\n  const texture = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 128;\n    canvas.height = 128;\n    const context = canvas.getContext('2d');\n    context.fillStyle = color;\n    context.fillRect(0, 0, canvas.width, canvas.height);\n    context.strokeStyle = strokeColor;\n    context.strokeRect(0, 0, canvas.width, canvas.height);\n    context.font = font;\n    context.textAlign = 'center';\n    context.fillStyle = textColor;\n    context.fillText(faces[index].toUpperCase(), 64, 76);\n    return new CanvasTexture(canvas);\n  }, [index, faces, font, color, textColor, strokeColor]);\n  return /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    map: texture,\n    \"map-anisotropy\": gl.capabilities.getMaxAnisotropy() || 1,\n    attach: `material-${index}`,\n    color: hover ? hoverColor : 'white',\n    transparent: true,\n    opacity: opacity\n  });\n};\nconst FaceCube = props => {\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const [hover, setHover] = React.useState(null);\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setHover(null);\n  };\n  const handleClick = e => {\n    e.stopPropagation();\n    tweenCamera(e.face.normal);\n  };\n  const handlePointerMove = e => {\n    e.stopPropagation();\n    setHover(Math.floor(e.faceIndex / 2));\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    onPointerOut: handlePointerOut,\n    onPointerMove: handlePointerMove,\n    onClick: props.onClick || handleClick\n  }, [...Array(6)].map((_, index) => /*#__PURE__*/React.createElement(FaceMaterial, _extends({\n    key: index,\n    index: index,\n    hover: hover === index\n  }, props))), /*#__PURE__*/React.createElement(\"boxGeometry\", null));\n};\nconst EdgeCube = ({\n  onClick,\n  dimensions,\n  position,\n  hoverColor = colors.hover\n}) => {\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const [hover, setHover] = React.useState(false);\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setHover(false);\n  };\n  const handlePointerOver = e => {\n    e.stopPropagation();\n    setHover(true);\n  };\n  const handleClick = e => {\n    e.stopPropagation();\n    tweenCamera(position);\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    scale: 1.01,\n    position: position,\n    onPointerOver: handlePointerOver,\n    onPointerOut: handlePointerOut,\n    onClick: onClick || handleClick\n  }, /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    color: hover ? hoverColor : 'white',\n    transparent: true,\n    opacity: 0.6,\n    visible: hover\n  }), /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: dimensions\n  }));\n};\nconst GizmoViewcube = props => {\n  return /*#__PURE__*/React.createElement(\"group\", {\n    scale: [60, 60, 60]\n  }, /*#__PURE__*/React.createElement(FaceCube, props), edges.map((edge, index) => /*#__PURE__*/React.createElement(EdgeCube, _extends({\n    key: index,\n    position: edge,\n    dimensions: edgeDimensions[index]\n  }, props))), corners.map((corner, index) => /*#__PURE__*/React.createElement(EdgeCube, _extends({\n    key: index,\n    position: corner,\n    dimensions: cornerDimensions\n  }, props))));\n};\n\nexport { GizmoViewcube };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,OAAO,EAAEC,aAAa,QAAQ,OAAO;AAE9C,MAAMC,MAAM,GAAG;EACbC,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,YAAY,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;AACxE,MAAMC,kBAAkB,GAAGC,GAAG,IAAI,IAAIT,OAAO,CAAC,GAAGS,GAAG,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC;AAC1E,MAAMC,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAACJ,kBAAkB,CAAC;AAC3J,MAAMK,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAC3C,MAAMC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACF,GAAG,CAACJ,kBAAkB,CAAC;AACrM,MAAMO,cAAc,GAAG,eAAeD,KAAK,CAACF,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,CAACL,GAAG,CAACM,IAAI,IAAIA,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AAC3G,MAAMC,YAAY,GAAGA,CAAC;EACpBf,KAAK;EACLgB,KAAK;EACLC,IAAI,GAAG,mCAAmC;EAC1CC,KAAK,GAAGf,YAAY;EACpBgB,KAAK,GAAGrB,MAAM,CAACC,EAAE;EACjBqB,UAAU,GAAGtB,MAAM,CAACE,KAAK;EACzBqB,SAAS,GAAGvB,MAAM,CAACG,IAAI;EACvBqB,WAAW,GAAGxB,MAAM,CAACI,MAAM;EAC3BqB,OAAO,GAAG;AACZ,CAAC,KAAK;EACJ,MAAMC,EAAE,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,OAAO,GAAGjC,KAAK,CAACkC,OAAO,CAAC,MAAM;IAClC,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,KAAK,GAAG,GAAG;IAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;IACnB,MAAMC,OAAO,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;IACvCD,OAAO,CAACE,SAAS,GAAGhB,KAAK;IACzBc,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAER,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;IACnDC,OAAO,CAACI,WAAW,GAAGf,WAAW;IACjCW,OAAO,CAACK,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEV,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;IACrDC,OAAO,CAAChB,IAAI,GAAGA,IAAI;IACnBgB,OAAO,CAACM,SAAS,GAAG,QAAQ;IAC5BN,OAAO,CAACE,SAAS,GAAGd,SAAS;IAC7BY,OAAO,CAACO,QAAQ,CAACtB,KAAK,CAACF,KAAK,CAAC,CAACyB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IACpD,OAAO,IAAI5C,aAAa,CAAC+B,MAAM,CAAC;EAClC,CAAC,EAAE,CAACZ,KAAK,EAAEE,KAAK,EAAED,IAAI,EAAEE,KAAK,EAAEE,SAAS,EAAEC,WAAW,CAAC,CAAC;EACvD,OAAO,aAAa7B,KAAK,CAACqC,aAAa,CAAC,mBAAmB,EAAE;IAC3DtB,GAAG,EAAEkB,OAAO;IACZ,gBAAgB,EAAEF,EAAE,CAACkB,YAAY,CAACC,gBAAgB,CAAC,CAAC,IAAI,CAAC;IACzDC,MAAM,EAAE,YAAY5B,KAAK,EAAE;IAC3BG,KAAK,EAAEnB,KAAK,GAAGoB,UAAU,GAAG,OAAO;IACnCyB,WAAW,EAAE,IAAI;IACjBtB,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ,CAAC;AACD,MAAMuB,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC;EACF,CAAC,GAAGrD,eAAe,CAAC,CAAC;EACrB,MAAM,CAACK,KAAK,EAAEiD,QAAQ,CAAC,GAAGxD,KAAK,CAACyD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMC,gBAAgB,GAAGC,CAAC,IAAI;IAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EACD,MAAMK,WAAW,GAAGF,CAAC,IAAI;IACvBA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBL,WAAW,CAACI,CAAC,CAACG,IAAI,CAACC,MAAM,CAAC;EAC5B,CAAC;EACD,MAAMC,iBAAiB,GAAGL,CAAC,IAAI;IAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,QAAQ,CAACS,IAAI,CAACC,KAAK,CAACP,CAAC,CAACQ,SAAS,GAAG,CAAC,CAAC,CAAC;EACvC,CAAC;EACD,OAAO,aAAanE,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAE;IAC9C+B,YAAY,EAAEV,gBAAgB;IAC9BW,aAAa,EAAEL,iBAAiB;IAChCM,OAAO,EAAEhB,KAAK,CAACgB,OAAO,IAAIT;EAC5B,CAAC,EAAE,CAAC,GAAGU,KAAK,CAAC,CAAC,CAAC,CAAC,CAACxD,GAAG,CAAC,CAACyD,CAAC,EAAEjD,KAAK,KAAK,aAAavB,KAAK,CAACqC,aAAa,CAACf,YAAY,EAAEvB,QAAQ,CAAC;IACzF0E,GAAG,EAAElD,KAAK;IACVA,KAAK,EAAEA,KAAK;IACZhB,KAAK,EAAEA,KAAK,KAAKgB;EACnB,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,EAAE,aAAatD,KAAK,CAACqC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACrE,CAAC;AACD,MAAMqC,QAAQ,GAAGA,CAAC;EAChBJ,OAAO;EACPK,UAAU;EACVC,QAAQ;EACRjD,UAAU,GAAGtB,MAAM,CAACE;AACtB,CAAC,KAAK;EACJ,MAAM;IACJgD;EACF,CAAC,GAAGrD,eAAe,CAAC,CAAC;EACrB,MAAM,CAACK,KAAK,EAAEiD,QAAQ,CAAC,GAAGxD,KAAK,CAACyD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMC,gBAAgB,GAAGC,CAAC,IAAI;IAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,QAAQ,CAAC,KAAK,CAAC;EACjB,CAAC;EACD,MAAMqB,iBAAiB,GAAGlB,CAAC,IAAI;IAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EACD,MAAMK,WAAW,GAAGF,CAAC,IAAI;IACvBA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBL,WAAW,CAACqB,QAAQ,CAAC;EACvB,CAAC;EACD,OAAO,aAAa5E,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAE;IAC9CyC,KAAK,EAAE,IAAI;IACXF,QAAQ,EAAEA,QAAQ;IAClBG,aAAa,EAAEF,iBAAiB;IAChCT,YAAY,EAAEV,gBAAgB;IAC9BY,OAAO,EAAEA,OAAO,IAAIT;EACtB,CAAC,EAAE,aAAa7D,KAAK,CAACqC,aAAa,CAAC,mBAAmB,EAAE;IACvDX,KAAK,EAAEnB,KAAK,GAAGoB,UAAU,GAAG,OAAO;IACnCyB,WAAW,EAAE,IAAI;IACjBtB,OAAO,EAAE,GAAG;IACZkD,OAAO,EAAEzE;EACX,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACqC,aAAa,CAAC,aAAa,EAAE;IAClD4C,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACD,MAAMO,aAAa,GAAG5B,KAAK,IAAI;EAC7B,OAAO,aAAatD,KAAK,CAACqC,aAAa,CAAC,OAAO,EAAE;IAC/CyC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;EACpB,CAAC,EAAE,aAAa9E,KAAK,CAACqC,aAAa,CAACgB,QAAQ,EAAEC,KAAK,CAAC,EAAErC,KAAK,CAACF,GAAG,CAAC,CAACI,IAAI,EAAEI,KAAK,KAAK,aAAavB,KAAK,CAACqC,aAAa,CAACqC,QAAQ,EAAE3E,QAAQ,CAAC;IACnI0E,GAAG,EAAElD,KAAK;IACVqD,QAAQ,EAAEzD,IAAI;IACdwD,UAAU,EAAEzD,cAAc,CAACK,KAAK;EAClC,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,EAAExC,OAAO,CAACC,GAAG,CAAC,CAACoE,MAAM,EAAE5D,KAAK,KAAK,aAAavB,KAAK,CAACqC,aAAa,CAACqC,QAAQ,EAAE3E,QAAQ,CAAC;IAC9F0E,GAAG,EAAElD,KAAK;IACVqD,QAAQ,EAAEO,MAAM;IAChBR,UAAU,EAAE3D;EACd,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC,CAAC;AACd,CAAC;AAED,SAAS4B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}