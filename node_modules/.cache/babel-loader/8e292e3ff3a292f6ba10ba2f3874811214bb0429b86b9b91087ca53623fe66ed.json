{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { extend } from '@react-three/fiber';\nimport { TextGeometry, mergeVertices } from 'three-stdlib';\nimport { useFont } from './useFont.js';\nconst types = ['string', 'number'];\nconst getTextFromChildren = children => {\n  let label = '';\n  const rest = [];\n  React.Children.forEach(children, child => {\n    if (types.includes(typeof child)) label += child + '';else rest.push(child);\n  });\n  return [label, ...rest];\n};\nconst Text3D = /* @__PURE__ */React.forwardRef(({\n  font: _font,\n  letterSpacing = 0,\n  lineHeight = 1,\n  size = 1,\n  height = 0.2,\n  bevelThickness = 0.1,\n  bevelSize = 0.01,\n  bevelEnabled = false,\n  bevelOffset = 0,\n  bevelSegments = 4,\n  curveSegments = 8,\n  smooth,\n  children,\n  ...props\n}, fref) => {\n  React.useMemo(() => extend({\n    RenamedTextGeometry: TextGeometry\n  }), []);\n  const ref = React.useRef(null);\n  const font = useFont(_font);\n  const opts = useMemo(() => {\n    return {\n      font,\n      size,\n      height,\n      bevelThickness,\n      bevelSize,\n      bevelEnabled,\n      bevelSegments,\n      bevelOffset,\n      curveSegments,\n      letterSpacing,\n      lineHeight\n    };\n  }, [font, size, height, bevelThickness, bevelSize, bevelEnabled, bevelSegments, bevelOffset, curveSegments, letterSpacing, lineHeight]);\n\n  /**\n   * We need the `children` in the deps because we\n   * need to be able to do `<Text3d>{state}</Text3d>`.\n   */\n  const [label, ...rest] = useMemo(() => getTextFromChildren(children), [children]);\n  const args = React.useMemo(() => [label, opts], [label, opts]);\n  React.useLayoutEffect(() => {\n    if (smooth) {\n      ref.current.geometry = mergeVertices(ref.current.geometry, smooth);\n      ref.current.geometry.computeVertexNormals();\n    }\n  }, [args, smooth]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({}, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"renamedTextGeometry\", {\n    args: args\n  }), rest);\n});\nexport { Text3D };", "map": {"version": 3, "names": ["_extends", "React", "useMemo", "extend", "TextGeometry", "mergeVertices", "useFont", "types", "getTextFromChildren", "children", "label", "rest", "Children", "for<PERSON>ach", "child", "includes", "push", "Text3D", "forwardRef", "font", "_font", "letterSpacing", "lineHeight", "size", "height", "bevelThickness", "bevelSize", "bevelEnabled", "bevelOffset", "bevelSegments", "curveSegments", "smooth", "props", "fref", "RenamedTextGeometry", "ref", "useRef", "opts", "args", "useLayoutEffect", "current", "geometry", "computeVertexNormals", "useImperativeHandle", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Text3D.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { extend } from '@react-three/fiber';\nimport { TextGeometry, mergeVertices } from 'three-stdlib';\nimport { useFont } from './useFont.js';\n\nconst types = ['string', 'number'];\nconst getTextFromChildren = children => {\n  let label = '';\n  const rest = [];\n  React.Children.forEach(children, child => {\n    if (types.includes(typeof child)) label += child + '';else rest.push(child);\n  });\n  return [label, ...rest];\n};\nconst Text3D = /* @__PURE__ */React.forwardRef(({\n  font: _font,\n  letterSpacing = 0,\n  lineHeight = 1,\n  size = 1,\n  height = 0.2,\n  bevelThickness = 0.1,\n  bevelSize = 0.01,\n  bevelEnabled = false,\n  bevelOffset = 0,\n  bevelSegments = 4,\n  curveSegments = 8,\n  smooth,\n  children,\n  ...props\n}, fref) => {\n  React.useMemo(() => extend({\n    RenamedTextGeometry: TextGeometry\n  }), []);\n  const ref = React.useRef(null);\n  const font = useFont(_font);\n  const opts = useMemo(() => {\n    return {\n      font,\n      size,\n      height,\n      bevelThickness,\n      bevelSize,\n      bevelEnabled,\n      bevelSegments,\n      bevelOffset,\n      curveSegments,\n      letterSpacing,\n      lineHeight\n    };\n  }, [font, size, height, bevelThickness, bevelSize, bevelEnabled, bevelSegments, bevelOffset, curveSegments, letterSpacing, lineHeight]);\n\n  /**\n   * We need the `children` in the deps because we\n   * need to be able to do `<Text3d>{state}</Text3d>`.\n   */\n  const [label, ...rest] = useMemo(() => getTextFromChildren(children), [children]);\n  const args = React.useMemo(() => [label, opts], [label, opts]);\n  React.useLayoutEffect(() => {\n    if (smooth) {\n      ref.current.geometry = mergeVertices(ref.current.geometry, smooth);\n      ref.current.geometry.computeVertexNormals();\n    }\n  }, [args, smooth]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({}, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"renamedTextGeometry\", {\n    args: args\n  }), rest);\n});\n\nexport { Text3D };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,YAAY,EAAEC,aAAa,QAAQ,cAAc;AAC1D,SAASC,OAAO,QAAQ,cAAc;AAEtC,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAMC,mBAAmB,GAAGC,QAAQ,IAAI;EACtC,IAAIC,KAAK,GAAG,EAAE;EACd,MAAMC,IAAI,GAAG,EAAE;EACfV,KAAK,CAACW,QAAQ,CAACC,OAAO,CAACJ,QAAQ,EAAEK,KAAK,IAAI;IACxC,IAAIP,KAAK,CAACQ,QAAQ,CAAC,OAAOD,KAAK,CAAC,EAAEJ,KAAK,IAAII,KAAK,GAAG,EAAE,CAAC,KAAKH,IAAI,CAACK,IAAI,CAACF,KAAK,CAAC;EAC7E,CAAC,CAAC;EACF,OAAO,CAACJ,KAAK,EAAE,GAAGC,IAAI,CAAC;AACzB,CAAC;AACD,MAAMM,MAAM,GAAG,eAAehB,KAAK,CAACiB,UAAU,CAAC,CAAC;EAC9CC,IAAI,EAAEC,KAAK;EACXC,aAAa,GAAG,CAAC;EACjBC,UAAU,GAAG,CAAC;EACdC,IAAI,GAAG,CAAC;EACRC,MAAM,GAAG,GAAG;EACZC,cAAc,GAAG,GAAG;EACpBC,SAAS,GAAG,IAAI;EAChBC,YAAY,GAAG,KAAK;EACpBC,WAAW,GAAG,CAAC;EACfC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC;EACjBC,MAAM;EACNtB,QAAQ;EACR,GAAGuB;AACL,CAAC,EAAEC,IAAI,KAAK;EACVhC,KAAK,CAACC,OAAO,CAAC,MAAMC,MAAM,CAAC;IACzB+B,mBAAmB,EAAE9B;EACvB,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM+B,GAAG,GAAGlC,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMjB,IAAI,GAAGb,OAAO,CAACc,KAAK,CAAC;EAC3B,MAAMiB,IAAI,GAAGnC,OAAO,CAAC,MAAM;IACzB,OAAO;MACLiB,IAAI;MACJI,IAAI;MACJC,MAAM;MACNC,cAAc;MACdC,SAAS;MACTC,YAAY;MACZE,aAAa;MACbD,WAAW;MACXE,aAAa;MACbT,aAAa;MACbC;IACF,CAAC;EACH,CAAC,EAAE,CAACH,IAAI,EAAEI,IAAI,EAAEC,MAAM,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEE,aAAa,EAAED,WAAW,EAAEE,aAAa,EAAET,aAAa,EAAEC,UAAU,CAAC,CAAC;;EAEvI;AACF;AACA;AACA;EACE,MAAM,CAACZ,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGT,OAAO,CAAC,MAAMM,mBAAmB,CAACC,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACjF,MAAM6B,IAAI,GAAGrC,KAAK,CAACC,OAAO,CAAC,MAAM,CAACQ,KAAK,EAAE2B,IAAI,CAAC,EAAE,CAAC3B,KAAK,EAAE2B,IAAI,CAAC,CAAC;EAC9DpC,KAAK,CAACsC,eAAe,CAAC,MAAM;IAC1B,IAAIR,MAAM,EAAE;MACVI,GAAG,CAACK,OAAO,CAACC,QAAQ,GAAGpC,aAAa,CAAC8B,GAAG,CAACK,OAAO,CAACC,QAAQ,EAAEV,MAAM,CAAC;MAClEI,GAAG,CAACK,OAAO,CAACC,QAAQ,CAACC,oBAAoB,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEP,MAAM,CAAC,CAAC;EAClB9B,KAAK,CAAC0C,mBAAmB,CAACV,IAAI,EAAE,MAAME,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAavC,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IAClEG,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAalC,KAAK,CAAC2C,aAAa,CAAC,qBAAqB,EAAE;IAC1DN,IAAI,EAAEA;EACR,CAAC,CAAC,EAAE3B,IAAI,CAAC;AACX,CAAC,CAAC;AAEF,SAASM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}