{"ast": null, "code": "import * as THREE from 'three';\nfunction shaderMaterial(uniforms, vertexShader, fragmentShader, onInit) {\n  var _Class;\n  return _Class = class extends THREE.ShaderMaterial {\n    constructor(parameters) {\n      super({\n        vertexShader,\n        fragmentShader,\n        ...parameters\n      });\n      for (const key in uniforms) {\n        this.uniforms[key] = new THREE.Uniform(uniforms[key]);\n        Object.defineProperty(this, key, {\n          get() {\n            return this.uniforms[key].value;\n          },\n          set(value) {\n            this.uniforms[key].value = value;\n          }\n        });\n      }\n      this.uniforms = THREE.UniformsUtils.clone(this.uniforms);\n      onInit == null || onInit(this);\n    }\n  }, _Class.key = THREE.MathUtils.generateUUID(), _Class;\n}\nexport { shaderMaterial };", "map": {"version": 3, "names": ["THREE", "shaderMaterial", "uniforms", "vertexShader", "fragmentShader", "onInit", "_Class", "ShaderMaterial", "constructor", "parameters", "key", "Uniform", "Object", "defineProperty", "get", "value", "set", "UniformsUtils", "clone", "MathUtils", "generateUUID"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/shaderMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\n\nfunction shaderMaterial(uniforms, vertexShader, fragmentShader, onInit) {\n  var _Class;\n  return _Class = class extends THREE.ShaderMaterial {\n    constructor(parameters) {\n      super({\n        vertexShader,\n        fragmentShader,\n        ...parameters\n      });\n      for (const key in uniforms) {\n        this.uniforms[key] = new THREE.Uniform(uniforms[key]);\n        Object.defineProperty(this, key, {\n          get() {\n            return this.uniforms[key].value;\n          },\n          set(value) {\n            this.uniforms[key].value = value;\n          }\n        });\n      }\n      this.uniforms = THREE.UniformsUtils.clone(this.uniforms);\n      onInit == null || onInit(this);\n    }\n  }, _Class.key = THREE.MathUtils.generateUUID(), _Class;\n}\n\nexport { shaderMaterial };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAE;EACtE,IAAIC,MAAM;EACV,OAAOA,MAAM,GAAG,cAAcN,KAAK,CAACO,cAAc,CAAC;IACjDC,WAAWA,CAACC,UAAU,EAAE;MACtB,KAAK,CAAC;QACJN,YAAY;QACZC,cAAc;QACd,GAAGK;MACL,CAAC,CAAC;MACF,KAAK,MAAMC,GAAG,IAAIR,QAAQ,EAAE;QAC1B,IAAI,CAACA,QAAQ,CAACQ,GAAG,CAAC,GAAG,IAAIV,KAAK,CAACW,OAAO,CAACT,QAAQ,CAACQ,GAAG,CAAC,CAAC;QACrDE,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEH,GAAG,EAAE;UAC/BI,GAAGA,CAAA,EAAG;YACJ,OAAO,IAAI,CAACZ,QAAQ,CAACQ,GAAG,CAAC,CAACK,KAAK;UACjC,CAAC;UACDC,GAAGA,CAACD,KAAK,EAAE;YACT,IAAI,CAACb,QAAQ,CAACQ,GAAG,CAAC,CAACK,KAAK,GAAGA,KAAK;UAClC;QACF,CAAC,CAAC;MACJ;MACA,IAAI,CAACb,QAAQ,GAAGF,KAAK,CAACiB,aAAa,CAACC,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAAC;MACxDG,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,IAAI,CAAC;IAChC;EACF,CAAC,EAAEC,MAAM,CAACI,GAAG,GAAGV,KAAK,CAACmB,SAAS,CAACC,YAAY,CAAC,CAAC,EAAEd,MAAM;AACxD;AAEA,SAASL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}