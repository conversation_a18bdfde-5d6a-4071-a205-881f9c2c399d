{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshSurfaceSampler } from 'three-stdlib';\nimport { InstancedBufferAttribute, Vector3, Color, Object3D } from 'three';\nfunction useSurfaceSampler(mesh, count = 16, transform, weight, instanceMesh) {\n  const [buffer, setBuffer] = React.useState(() => {\n    const arr = Array.from({\n      length: count\n    }, () => [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]).flat();\n    return new InstancedBufferAttribute(Float32Array.from(arr), 16);\n  });\n  React.useLayoutEffect(() => {\n    if (typeof mesh.current === 'undefined') return;\n    const sampler = new MeshSurfaceSampler(mesh.current);\n    if (weight) {\n      sampler.setWeightAttribute(weight);\n    }\n    sampler.build();\n    const position = new Vector3();\n    const normal = new Vector3();\n    const color = new Color();\n    const dummy = new Object3D();\n    mesh.current.updateMatrixWorld(true);\n    for (let i = 0; i < count; i++) {\n      sampler.sample(position, normal, color);\n      if (typeof transform === 'function') {\n        transform({\n          dummy,\n          sampledMesh: mesh.current,\n          position,\n          normal,\n          color\n        }, i);\n      } else {\n        dummy.position.copy(position);\n      }\n      dummy.updateMatrix();\n      if (instanceMesh != null && instanceMesh.current) {\n        instanceMesh.current.setMatrixAt(i, dummy.matrix);\n      }\n      dummy.matrix.toArray(buffer.array, i * 16);\n    }\n    if (instanceMesh != null && instanceMesh.current) {\n      instanceMesh.current.instanceMatrix.needsUpdate = true;\n    }\n    buffer.needsUpdate = true;\n    setBuffer(new InstancedBufferAttribute(buffer.array, buffer.itemSize).copy(buffer));\n  }, [mesh, instanceMesh, weight, count, transform]);\n  return buffer;\n}\nfunction Sampler({\n  children,\n  weight,\n  transform,\n  instances,\n  mesh,\n  count = 16,\n  ...props\n}) {\n  const group = React.useRef(null);\n  const instancedRef = React.useRef(null);\n  const meshToSampleRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    var _instances$current, _mesh$current;\n    instancedRef.current = (_instances$current = instances == null ? void 0 : instances.current) !== null && _instances$current !== void 0 ? _instances$current : group.current.children.find(c => c.hasOwnProperty('instanceMatrix'));\n    meshToSampleRef.current = (_mesh$current = mesh == null ? void 0 : mesh.current) !== null && _mesh$current !== void 0 ? _mesh$current : group.current.children.find(c => c.type === 'Mesh');\n  }, [children, mesh == null ? void 0 : mesh.current, instances == null ? void 0 : instances.current]);\n  useSurfaceSampler(meshToSampleRef, count, transform, weight, instancedRef);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props), children);\n}\nexport { Sampler, useSurfaceSampler };", "map": {"version": 3, "names": ["_extends", "React", "MeshSurfaceSampler", "InstancedBufferAttribute", "Vector3", "Color", "Object3D", "useSurfaceSampler", "mesh", "count", "transform", "weight", "<PERSON><PERSON><PERSON>", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "useState", "arr", "Array", "from", "length", "flat", "Float32Array", "useLayoutEffect", "current", "sampler", "setWeightAttribute", "build", "position", "normal", "color", "dummy", "updateMatrixWorld", "i", "sample", "<PERSON><PERSON><PERSON>", "copy", "updateMatrix", "setMatrixAt", "matrix", "toArray", "array", "instanceMatrix", "needsUpdate", "itemSize", "<PERSON><PERSON>", "children", "instances", "props", "group", "useRef", "instancedRef", "meshToSampleRef", "_instances$current", "_mesh$current", "find", "c", "hasOwnProperty", "type", "createElement", "ref"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Sampler.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshSurfaceSampler } from 'three-stdlib';\nimport { InstancedBufferAttribute, Vector3, Color, Object3D } from 'three';\n\nfunction useSurfaceSampler(mesh, count = 16, transform, weight, instanceMesh) {\n  const [buffer, setBuffer] = React.useState(() => {\n    const arr = Array.from({\n      length: count\n    }, () => [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]).flat();\n    return new InstancedBufferAttribute(Float32Array.from(arr), 16);\n  });\n  React.useLayoutEffect(() => {\n    if (typeof mesh.current === 'undefined') return;\n    const sampler = new MeshSurfaceSampler(mesh.current);\n    if (weight) {\n      sampler.setWeightAttribute(weight);\n    }\n    sampler.build();\n    const position = new Vector3();\n    const normal = new Vector3();\n    const color = new Color();\n    const dummy = new Object3D();\n    mesh.current.updateMatrixWorld(true);\n    for (let i = 0; i < count; i++) {\n      sampler.sample(position, normal, color);\n      if (typeof transform === 'function') {\n        transform({\n          dummy,\n          sampledMesh: mesh.current,\n          position,\n          normal,\n          color\n        }, i);\n      } else {\n        dummy.position.copy(position);\n      }\n      dummy.updateMatrix();\n      if (instanceMesh != null && instanceMesh.current) {\n        instanceMesh.current.setMatrixAt(i, dummy.matrix);\n      }\n      dummy.matrix.toArray(buffer.array, i * 16);\n    }\n    if (instanceMesh != null && instanceMesh.current) {\n      instanceMesh.current.instanceMatrix.needsUpdate = true;\n    }\n    buffer.needsUpdate = true;\n    setBuffer(new InstancedBufferAttribute(buffer.array, buffer.itemSize).copy(buffer));\n  }, [mesh, instanceMesh, weight, count, transform]);\n  return buffer;\n}\nfunction Sampler({\n  children,\n  weight,\n  transform,\n  instances,\n  mesh,\n  count = 16,\n  ...props\n}) {\n  const group = React.useRef(null);\n  const instancedRef = React.useRef(null);\n  const meshToSampleRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    var _instances$current, _mesh$current;\n    instancedRef.current = (_instances$current = instances == null ? void 0 : instances.current) !== null && _instances$current !== void 0 ? _instances$current : group.current.children.find(c => c.hasOwnProperty('instanceMatrix'));\n    meshToSampleRef.current = (_mesh$current = mesh == null ? void 0 : mesh.current) !== null && _mesh$current !== void 0 ? _mesh$current : group.current.children.find(c => c.type === 'Mesh');\n  }, [children, mesh == null ? void 0 : mesh.current, instances == null ? void 0 : instances.current]);\n  useSurfaceSampler(meshToSampleRef, count, transform, weight, instancedRef);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props), children);\n}\n\nexport { Sampler, useSurfaceSampler };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,wBAAwB,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,OAAO;AAE1E,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAE;EAC5E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,MAAM;IAC/C,MAAMC,GAAG,GAAGC,KAAK,CAACC,IAAI,CAAC;MACrBC,MAAM,EAAEV;IACV,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACW,IAAI,CAAC,CAAC;IACjE,OAAO,IAAIjB,wBAAwB,CAACkB,YAAY,CAACH,IAAI,CAACF,GAAG,CAAC,EAAE,EAAE,CAAC;EACjE,CAAC,CAAC;EACFf,KAAK,CAACqB,eAAe,CAAC,MAAM;IAC1B,IAAI,OAAOd,IAAI,CAACe,OAAO,KAAK,WAAW,EAAE;IACzC,MAAMC,OAAO,GAAG,IAAItB,kBAAkB,CAACM,IAAI,CAACe,OAAO,CAAC;IACpD,IAAIZ,MAAM,EAAE;MACVa,OAAO,CAACC,kBAAkB,CAACd,MAAM,CAAC;IACpC;IACAa,OAAO,CAACE,KAAK,CAAC,CAAC;IACf,MAAMC,QAAQ,GAAG,IAAIvB,OAAO,CAAC,CAAC;IAC9B,MAAMwB,MAAM,GAAG,IAAIxB,OAAO,CAAC,CAAC;IAC5B,MAAMyB,KAAK,GAAG,IAAIxB,KAAK,CAAC,CAAC;IACzB,MAAMyB,KAAK,GAAG,IAAIxB,QAAQ,CAAC,CAAC;IAC5BE,IAAI,CAACe,OAAO,CAACQ,iBAAiB,CAAC,IAAI,CAAC;IACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,KAAK,EAAEuB,CAAC,EAAE,EAAE;MAC9BR,OAAO,CAACS,MAAM,CAACN,QAAQ,EAAEC,MAAM,EAAEC,KAAK,CAAC;MACvC,IAAI,OAAOnB,SAAS,KAAK,UAAU,EAAE;QACnCA,SAAS,CAAC;UACRoB,KAAK;UACLI,WAAW,EAAE1B,IAAI,CAACe,OAAO;UACzBI,QAAQ;UACRC,MAAM;UACNC;QACF,CAAC,EAAEG,CAAC,CAAC;MACP,CAAC,MAAM;QACLF,KAAK,CAACH,QAAQ,CAACQ,IAAI,CAACR,QAAQ,CAAC;MAC/B;MACAG,KAAK,CAACM,YAAY,CAAC,CAAC;MACpB,IAAIxB,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACW,OAAO,EAAE;QAChDX,YAAY,CAACW,OAAO,CAACc,WAAW,CAACL,CAAC,EAAEF,KAAK,CAACQ,MAAM,CAAC;MACnD;MACAR,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC1B,MAAM,CAAC2B,KAAK,EAAER,CAAC,GAAG,EAAE,CAAC;IAC5C;IACA,IAAIpB,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACW,OAAO,EAAE;MAChDX,YAAY,CAACW,OAAO,CAACkB,cAAc,CAACC,WAAW,GAAG,IAAI;IACxD;IACA7B,MAAM,CAAC6B,WAAW,GAAG,IAAI;IACzB5B,SAAS,CAAC,IAAIX,wBAAwB,CAACU,MAAM,CAAC2B,KAAK,EAAE3B,MAAM,CAAC8B,QAAQ,CAAC,CAACR,IAAI,CAACtB,MAAM,CAAC,CAAC;EACrF,CAAC,EAAE,CAACL,IAAI,EAAEI,YAAY,EAAED,MAAM,EAAEF,KAAK,EAAEC,SAAS,CAAC,CAAC;EAClD,OAAOG,MAAM;AACf;AACA,SAAS+B,OAAOA,CAAC;EACfC,QAAQ;EACRlC,MAAM;EACND,SAAS;EACToC,SAAS;EACTtC,IAAI;EACJC,KAAK,GAAG,EAAE;EACV,GAAGsC;AACL,CAAC,EAAE;EACD,MAAMC,KAAK,GAAG/C,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMC,YAAY,GAAGjD,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EACvC,MAAME,eAAe,GAAGlD,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EAC1ChD,KAAK,CAACqB,eAAe,CAAC,MAAM;IAC1B,IAAI8B,kBAAkB,EAAEC,aAAa;IACrCH,YAAY,CAAC3B,OAAO,GAAG,CAAC6B,kBAAkB,GAAGN,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvB,OAAO,MAAM,IAAI,IAAI6B,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGJ,KAAK,CAACzB,OAAO,CAACsB,QAAQ,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAClOL,eAAe,CAAC5B,OAAO,GAAG,CAAC8B,aAAa,GAAG7C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACe,OAAO,MAAM,IAAI,IAAI8B,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGL,KAAK,CAACzB,OAAO,CAACsB,QAAQ,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,IAAI,KAAK,MAAM,CAAC;EAC7L,CAAC,EAAE,CAACZ,QAAQ,EAAErC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACe,OAAO,EAAEuB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvB,OAAO,CAAC,CAAC;EACpGhB,iBAAiB,CAAC4C,eAAe,EAAE1C,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEuC,YAAY,CAAC;EAC1E,OAAO,aAAajD,KAAK,CAACyD,aAAa,CAAC,OAAO,EAAE1D,QAAQ,CAAC;IACxD2D,GAAG,EAAEX;EACP,CAAC,EAAED,KAAK,CAAC,EAAEF,QAAQ,CAAC;AACtB;AAEA,SAASD,OAAO,EAAErC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}