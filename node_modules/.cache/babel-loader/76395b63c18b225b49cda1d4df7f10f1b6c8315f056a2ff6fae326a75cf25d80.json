{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON>Loader, LineBasicMaterial, Group, Euler, BufferGeometry, Float32BufferAttribute, LineSegments } from \"three\";\nclass GCodeLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.splitLayer = false;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    let state = {\n      x: 0,\n      y: 0,\n      z: 0,\n      e: 0,\n      f: 0,\n      extruding: false,\n      relative: false\n    };\n    let layers = [];\n    let currentLayer = void 0;\n    const pathMaterial = new LineBasicMaterial({\n      color: 16711680\n    });\n    pathMaterial.name = \"path\";\n    const extrudingMaterial = new LineBasicMaterial({\n      color: 65280\n    });\n    extrudingMaterial.name = \"extruded\";\n    function newLayer(line) {\n      currentLayer = {\n        vertex: [],\n        pathVertex: [],\n        z: line.z\n      };\n      layers.push(currentLayer);\n    }\n    function addSegment(p1, p2) {\n      if (currentLayer === void 0) {\n        newLayer(p1);\n      }\n      if (state.extruding) {\n        currentLayer.vertex.push(p1.x, p1.y, p1.z);\n        currentLayer.vertex.push(p2.x, p2.y, p2.z);\n      } else {\n        currentLayer.pathVertex.push(p1.x, p1.y, p1.z);\n        currentLayer.pathVertex.push(p2.x, p2.y, p2.z);\n      }\n    }\n    function delta(v1, v2) {\n      return state.relative ? v2 : v2 - v1;\n    }\n    function absolute(v1, v2) {\n      return state.relative ? v1 + v2 : v2;\n    }\n    let lines = data.replace(/;.+/g, \"\").split(\"\\n\");\n    for (let i = 0; i < lines.length; i++) {\n      let tokens = lines[i].split(\" \");\n      let cmd = tokens[0].toUpperCase();\n      let args = {};\n      tokens.splice(1).forEach(function (token) {\n        if (token[0] !== void 0) {\n          let key = token[0].toLowerCase();\n          let value = parseFloat(token.substring(1));\n          args[key] = value;\n        }\n      });\n      if (cmd === \"G0\" || cmd === \"G1\") {\n        let line = {\n          x: args.x !== void 0 ? absolute(state.x, args.x) : state.x,\n          y: args.y !== void 0 ? absolute(state.y, args.y) : state.y,\n          z: args.z !== void 0 ? absolute(state.z, args.z) : state.z,\n          e: args.e !== void 0 ? absolute(state.e, args.e) : state.e,\n          f: args.f !== void 0 ? absolute(state.f, args.f) : state.f\n        };\n        if (delta(state.e, line.e) > 0) {\n          line.extruding = delta(state.e, line.e) > 0;\n          if (currentLayer == void 0 || line.z != currentLayer.z) {\n            newLayer(line);\n          }\n        }\n        addSegment(state, line);\n        state = line;\n      } else if (cmd === \"G2\" || cmd === \"G3\") ;else if (cmd === \"G90\") {\n        state.relative = false;\n      } else if (cmd === \"G91\") {\n        state.relative = true;\n      } else if (cmd === \"G92\") {\n        let line = state;\n        line.x = args.x !== void 0 ? args.x : line.x;\n        line.y = args.y !== void 0 ? args.y : line.y;\n        line.z = args.z !== void 0 ? args.z : line.z;\n        line.e = args.e !== void 0 ? args.e : line.e;\n        state = line;\n      } else ;\n    }\n    function addObject(vertex, extruding, i) {\n      let geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(vertex, 3));\n      let segments = new LineSegments(geometry, extruding ? extrudingMaterial : pathMaterial);\n      segments.name = \"layer\" + i;\n      object.add(segments);\n    }\n    const object = new Group();\n    object.name = \"gcode\";\n    if (this.splitLayer) {\n      for (let i = 0; i < layers.length; i++) {\n        let layer = layers[i];\n        addObject(layer.vertex, true, i);\n        addObject(layer.pathVertex, false, i);\n      }\n    } else {\n      const vertex = [],\n        pathVertex = [];\n      for (let i = 0; i < layers.length; i++) {\n        let layer = layers[i];\n        let layerVertex = layer.vertex;\n        let layerPathVertex = layer.pathVertex;\n        for (let j = 0; j < layerVertex.length; j++) {\n          vertex.push(layerVertex[j]);\n        }\n        for (let j = 0; j < layerPathVertex.length; j++) {\n          pathVertex.push(layerPathVertex[j]);\n        }\n      }\n      addObject(vertex, true, layers.length);\n      addObject(pathVertex, false, layers.length);\n    }\n    object.quaternion.setFromEuler(new Euler(-Math.PI / 2, 0, 0));\n    return object;\n  }\n}\nexport { GCodeLoader };", "map": {"version": 3, "names": ["GCode<PERSON><PERSON>der", "Loader", "constructor", "manager", "split<PERSON>ayer", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "state", "x", "y", "z", "f", "extruding", "relative", "layers", "<PERSON><PERSON><PERSON><PERSON>", "pathMaterial", "LineBasicMaterial", "color", "name", "extrudingMaterial", "<PERSON><PERSON><PERSON><PERSON>", "line", "vertex", "pathVertex", "push", "addSegment", "p1", "p2", "delta", "v1", "v2", "absolute", "lines", "replace", "split", "i", "length", "tokens", "cmd", "toUpperCase", "args", "splice", "for<PERSON>ach", "token", "key", "toLowerCase", "value", "parseFloat", "substring", "addObject", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "segments", "LineSegments", "object", "add", "Group", "layer", "layerVertex", "layerPathVertex", "j", "quaternion", "setFromEuler", "<PERSON>uler", "Math", "PI"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/GCodeLoader.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  Euler,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n} from 'three'\n\n/**\n * GCodeLoader is used to load gcode files usually used for 3D printing or CNC applications.\n *\n * Gcode files are composed by commands used by machines to create objects.\n *\n * @class GCodeLoader\n * @param {Manager} manager Loading manager.\n */\n\nclass GCodeLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.splitLayer = false\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    let state = { x: 0, y: 0, z: 0, e: 0, f: 0, extruding: false, relative: false }\n    let layers = []\n\n    let currentLayer = undefined\n\n    const pathMaterial = new LineBasicMaterial({ color: 0xff0000 })\n    pathMaterial.name = 'path'\n\n    const extrudingMaterial = new LineBasicMaterial({ color: 0x00ff00 })\n    extrudingMaterial.name = 'extruded'\n\n    function newLayer(line) {\n      currentLayer = { vertex: [], pathVertex: [], z: line.z }\n      layers.push(currentLayer)\n    }\n\n    //Create lie segment between p1 and p2\n    function addSegment(p1, p2) {\n      if (currentLayer === undefined) {\n        newLayer(p1)\n      }\n\n      if (state.extruding) {\n        currentLayer.vertex.push(p1.x, p1.y, p1.z)\n        currentLayer.vertex.push(p2.x, p2.y, p2.z)\n      } else {\n        currentLayer.pathVertex.push(p1.x, p1.y, p1.z)\n        currentLayer.pathVertex.push(p2.x, p2.y, p2.z)\n      }\n    }\n\n    function delta(v1, v2) {\n      return state.relative ? v2 : v2 - v1\n    }\n\n    function absolute(v1, v2) {\n      return state.relative ? v1 + v2 : v2\n    }\n\n    let lines = data.replace(/;.+/g, '').split('\\n')\n\n    for (let i = 0; i < lines.length; i++) {\n      let tokens = lines[i].split(' ')\n      let cmd = tokens[0].toUpperCase()\n\n      //Argumments\n      let args = {}\n      tokens.splice(1).forEach(function (token) {\n        if (token[0] !== undefined) {\n          let key = token[0].toLowerCase()\n          let value = parseFloat(token.substring(1))\n          args[key] = value\n        }\n      })\n\n      //Process commands\n      //G0/G1 – Linear Movement\n      if (cmd === 'G0' || cmd === 'G1') {\n        let line = {\n          x: args.x !== undefined ? absolute(state.x, args.x) : state.x,\n          y: args.y !== undefined ? absolute(state.y, args.y) : state.y,\n          z: args.z !== undefined ? absolute(state.z, args.z) : state.z,\n          e: args.e !== undefined ? absolute(state.e, args.e) : state.e,\n          f: args.f !== undefined ? absolute(state.f, args.f) : state.f,\n        }\n\n        //Layer change detection is or made by watching Z, it's made by watching when we extrude at a new Z position\n        if (delta(state.e, line.e) > 0) {\n          line.extruding = delta(state.e, line.e) > 0\n\n          if (currentLayer == undefined || line.z != currentLayer.z) {\n            newLayer(line)\n          }\n        }\n\n        addSegment(state, line)\n        state = line\n      } else if (cmd === 'G2' || cmd === 'G3') {\n        //G2/G3 - Arc Movement ( G2 clock wise and G3 counter clock wise )\n        //console.warn( 'THREE.GCodeLoader: Arc command not supported' );\n      } else if (cmd === 'G90') {\n        //G90: Set to Absolute Positioning\n        state.relative = false\n      } else if (cmd === 'G91') {\n        //G91: Set to state.relative Positioning\n        state.relative = true\n      } else if (cmd === 'G92') {\n        //G92: Set Position\n        let line = state\n        line.x = args.x !== undefined ? args.x : line.x\n        line.y = args.y !== undefined ? args.y : line.y\n        line.z = args.z !== undefined ? args.z : line.z\n        line.e = args.e !== undefined ? args.e : line.e\n        state = line\n      } else {\n        //console.warn( 'THREE.GCodeLoader: Command not supported:' + cmd );\n      }\n    }\n\n    function addObject(vertex, extruding, i) {\n      let geometry = new BufferGeometry()\n      geometry.setAttribute('position', new Float32BufferAttribute(vertex, 3))\n      let segments = new LineSegments(geometry, extruding ? extrudingMaterial : pathMaterial)\n      segments.name = 'layer' + i\n      object.add(segments)\n    }\n\n    const object = new Group()\n    object.name = 'gcode'\n\n    if (this.splitLayer) {\n      for (let i = 0; i < layers.length; i++) {\n        let layer = layers[i]\n        addObject(layer.vertex, true, i)\n        addObject(layer.pathVertex, false, i)\n      }\n    } else {\n      const vertex = [],\n        pathVertex = []\n\n      for (let i = 0; i < layers.length; i++) {\n        let layer = layers[i]\n        let layerVertex = layer.vertex\n        let layerPathVertex = layer.pathVertex\n\n        for (let j = 0; j < layerVertex.length; j++) {\n          vertex.push(layerVertex[j])\n        }\n\n        for (let j = 0; j < layerPathVertex.length; j++) {\n          pathVertex.push(layerPathVertex[j])\n        }\n      }\n\n      addObject(vertex, true, layers.length)\n      addObject(pathVertex, false, layers.length)\n    }\n\n    object.quaternion.setFromEuler(new Euler(-Math.PI / 2, 0, 0))\n\n    return object\n  }\n}\n\nexport { GCodeLoader }\n"], "mappings": ";AAoBA,MAAMA,WAAA,SAAoBC,MAAA,CAAO;EAC/BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,UAAA,GAAa;EACnB;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMP,OAAO;IAC3CQ,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,gBAAA,CAAiBL,KAAA,CAAMM,aAAa;IAC3CL,MAAA,CAAOM,kBAAA,CAAmBP,KAAA,CAAMQ,eAAe;IAC/CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,IAAA,EAAM;MACd,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMP,OAAA,CAAQqB,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDW,MAAMK,IAAA,EAAM;IACV,IAAIC,KAAA,GAAQ;MAAEC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGR,CAAA,EAAG;MAAGS,CAAA,EAAG;MAAGC,SAAA,EAAW;MAAOC,QAAA,EAAU;IAAO;IAC/E,IAAIC,MAAA,GAAS,EAAE;IAEf,IAAIC,YAAA,GAAe;IAEnB,MAAMC,YAAA,GAAe,IAAIC,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAQ,CAAE;IAC9DF,YAAA,CAAaG,IAAA,GAAO;IAEpB,MAAMC,iBAAA,GAAoB,IAAIH,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAQ,CAAE;IACnEE,iBAAA,CAAkBD,IAAA,GAAO;IAEzB,SAASE,SAASC,IAAA,EAAM;MACtBP,YAAA,GAAe;QAAEQ,MAAA,EAAQ;QAAIC,UAAA,EAAY;QAAId,CAAA,EAAGY,IAAA,CAAKZ;MAAG;MACxDI,MAAA,CAAOW,IAAA,CAAKV,YAAY;IACzB;IAGD,SAASW,WAAWC,EAAA,EAAIC,EAAA,EAAI;MAC1B,IAAIb,YAAA,KAAiB,QAAW;QAC9BM,QAAA,CAASM,EAAE;MACZ;MAED,IAAIpB,KAAA,CAAMK,SAAA,EAAW;QACnBG,YAAA,CAAaQ,MAAA,CAAOE,IAAA,CAAKE,EAAA,CAAGnB,CAAA,EAAGmB,EAAA,CAAGlB,CAAA,EAAGkB,EAAA,CAAGjB,CAAC;QACzCK,YAAA,CAAaQ,MAAA,CAAOE,IAAA,CAAKG,EAAA,CAAGpB,CAAA,EAAGoB,EAAA,CAAGnB,CAAA,EAAGmB,EAAA,CAAGlB,CAAC;MACjD,OAAa;QACLK,YAAA,CAAaS,UAAA,CAAWC,IAAA,CAAKE,EAAA,CAAGnB,CAAA,EAAGmB,EAAA,CAAGlB,CAAA,EAAGkB,EAAA,CAAGjB,CAAC;QAC7CK,YAAA,CAAaS,UAAA,CAAWC,IAAA,CAAKG,EAAA,CAAGpB,CAAA,EAAGoB,EAAA,CAAGnB,CAAA,EAAGmB,EAAA,CAAGlB,CAAC;MAC9C;IACF;IAED,SAASmB,MAAMC,EAAA,EAAIC,EAAA,EAAI;MACrB,OAAOxB,KAAA,CAAMM,QAAA,GAAWkB,EAAA,GAAKA,EAAA,GAAKD,EAAA;IACnC;IAED,SAASE,SAASF,EAAA,EAAIC,EAAA,EAAI;MACxB,OAAOxB,KAAA,CAAMM,QAAA,GAAWiB,EAAA,GAAKC,EAAA,GAAKA,EAAA;IACnC;IAED,IAAIE,KAAA,GAAQ3B,IAAA,CAAK4B,OAAA,CAAQ,QAAQ,EAAE,EAAEC,KAAA,CAAM,IAAI;IAE/C,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,KAAA,CAAMI,MAAA,EAAQD,CAAA,IAAK;MACrC,IAAIE,MAAA,GAASL,KAAA,CAAMG,CAAC,EAAED,KAAA,CAAM,GAAG;MAC/B,IAAII,GAAA,GAAMD,MAAA,CAAO,CAAC,EAAEE,WAAA,CAAa;MAGjC,IAAIC,IAAA,GAAO,CAAE;MACbH,MAAA,CAAOI,MAAA,CAAO,CAAC,EAAEC,OAAA,CAAQ,UAAUC,KAAA,EAAO;QACxC,IAAIA,KAAA,CAAM,CAAC,MAAM,QAAW;UAC1B,IAAIC,GAAA,GAAMD,KAAA,CAAM,CAAC,EAAEE,WAAA,CAAa;UAChC,IAAIC,KAAA,GAAQC,UAAA,CAAWJ,KAAA,CAAMK,SAAA,CAAU,CAAC,CAAC;UACzCR,IAAA,CAAKI,GAAG,IAAIE,KAAA;QACb;MACT,CAAO;MAID,IAAIR,GAAA,KAAQ,QAAQA,GAAA,KAAQ,MAAM;QAChC,IAAIjB,IAAA,GAAO;UACTd,CAAA,EAAGiC,IAAA,CAAKjC,CAAA,KAAM,SAAYwB,QAAA,CAASzB,KAAA,CAAMC,CAAA,EAAGiC,IAAA,CAAKjC,CAAC,IAAID,KAAA,CAAMC,CAAA;UAC5DC,CAAA,EAAGgC,IAAA,CAAKhC,CAAA,KAAM,SAAYuB,QAAA,CAASzB,KAAA,CAAME,CAAA,EAAGgC,IAAA,CAAKhC,CAAC,IAAIF,KAAA,CAAME,CAAA;UAC5DC,CAAA,EAAG+B,IAAA,CAAK/B,CAAA,KAAM,SAAYsB,QAAA,CAASzB,KAAA,CAAMG,CAAA,EAAG+B,IAAA,CAAK/B,CAAC,IAAIH,KAAA,CAAMG,CAAA;UAC5DR,CAAA,EAAGuC,IAAA,CAAKvC,CAAA,KAAM,SAAY8B,QAAA,CAASzB,KAAA,CAAML,CAAA,EAAGuC,IAAA,CAAKvC,CAAC,IAAIK,KAAA,CAAML,CAAA;UAC5DS,CAAA,EAAG8B,IAAA,CAAK9B,CAAA,KAAM,SAAYqB,QAAA,CAASzB,KAAA,CAAMI,CAAA,EAAG8B,IAAA,CAAK9B,CAAC,IAAIJ,KAAA,CAAMI;QAC7D;QAGD,IAAIkB,KAAA,CAAMtB,KAAA,CAAML,CAAA,EAAGoB,IAAA,CAAKpB,CAAC,IAAI,GAAG;UAC9BoB,IAAA,CAAKV,SAAA,GAAYiB,KAAA,CAAMtB,KAAA,CAAML,CAAA,EAAGoB,IAAA,CAAKpB,CAAC,IAAI;UAE1C,IAAIa,YAAA,IAAgB,UAAaO,IAAA,CAAKZ,CAAA,IAAKK,YAAA,CAAaL,CAAA,EAAG;YACzDW,QAAA,CAASC,IAAI;UACd;QACF;QAEDI,UAAA,CAAWnB,KAAA,EAAOe,IAAI;QACtBf,KAAA,GAAQe,IAAA;MAChB,WAAiBiB,GAAA,KAAQ,QAAQA,GAAA,KAAQ,MAAM,UAG9BA,GAAA,KAAQ,OAAO;QAExBhC,KAAA,CAAMM,QAAA,GAAW;MACzB,WAAiB0B,GAAA,KAAQ,OAAO;QAExBhC,KAAA,CAAMM,QAAA,GAAW;MACzB,WAAiB0B,GAAA,KAAQ,OAAO;QAExB,IAAIjB,IAAA,GAAOf,KAAA;QACXe,IAAA,CAAKd,CAAA,GAAIiC,IAAA,CAAKjC,CAAA,KAAM,SAAYiC,IAAA,CAAKjC,CAAA,GAAIc,IAAA,CAAKd,CAAA;QAC9Cc,IAAA,CAAKb,CAAA,GAAIgC,IAAA,CAAKhC,CAAA,KAAM,SAAYgC,IAAA,CAAKhC,CAAA,GAAIa,IAAA,CAAKb,CAAA;QAC9Ca,IAAA,CAAKZ,CAAA,GAAI+B,IAAA,CAAK/B,CAAA,KAAM,SAAY+B,IAAA,CAAK/B,CAAA,GAAIY,IAAA,CAAKZ,CAAA;QAC9CY,IAAA,CAAKpB,CAAA,GAAIuC,IAAA,CAAKvC,CAAA,KAAM,SAAYuC,IAAA,CAAKvC,CAAA,GAAIoB,IAAA,CAAKpB,CAAA;QAC9CK,KAAA,GAAQe,IAAA;MAChB,OAAa;IAGR;IAED,SAAS4B,UAAU3B,MAAA,EAAQX,SAAA,EAAWwB,CAAA,EAAG;MACvC,IAAIe,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACnCD,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB/B,MAAA,EAAQ,CAAC,CAAC;MACvE,IAAIgC,QAAA,GAAW,IAAIC,YAAA,CAAaL,QAAA,EAAUvC,SAAA,GAAYQ,iBAAA,GAAoBJ,YAAY;MACtFuC,QAAA,CAASpC,IAAA,GAAO,UAAUiB,CAAA;MAC1BqB,MAAA,CAAOC,GAAA,CAAIH,QAAQ;IACpB;IAED,MAAME,MAAA,GAAS,IAAIE,KAAA,CAAO;IAC1BF,MAAA,CAAOtC,IAAA,GAAO;IAEd,IAAI,KAAKlC,UAAA,EAAY;MACnB,SAASmD,CAAA,GAAI,GAAGA,CAAA,GAAItB,MAAA,CAAOuB,MAAA,EAAQD,CAAA,IAAK;QACtC,IAAIwB,KAAA,GAAQ9C,MAAA,CAAOsB,CAAC;QACpBc,SAAA,CAAUU,KAAA,CAAMrC,MAAA,EAAQ,MAAMa,CAAC;QAC/Bc,SAAA,CAAUU,KAAA,CAAMpC,UAAA,EAAY,OAAOY,CAAC;MACrC;IACP,OAAW;MACL,MAAMb,MAAA,GAAS,EAAE;QACfC,UAAA,GAAa,EAAE;MAEjB,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAItB,MAAA,CAAOuB,MAAA,EAAQD,CAAA,IAAK;QACtC,IAAIwB,KAAA,GAAQ9C,MAAA,CAAOsB,CAAC;QACpB,IAAIyB,WAAA,GAAcD,KAAA,CAAMrC,MAAA;QACxB,IAAIuC,eAAA,GAAkBF,KAAA,CAAMpC,UAAA;QAE5B,SAASuC,CAAA,GAAI,GAAGA,CAAA,GAAIF,WAAA,CAAYxB,MAAA,EAAQ0B,CAAA,IAAK;UAC3CxC,MAAA,CAAOE,IAAA,CAAKoC,WAAA,CAAYE,CAAC,CAAC;QAC3B;QAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAID,eAAA,CAAgBzB,MAAA,EAAQ0B,CAAA,IAAK;UAC/CvC,UAAA,CAAWC,IAAA,CAAKqC,eAAA,CAAgBC,CAAC,CAAC;QACnC;MACF;MAEDb,SAAA,CAAU3B,MAAA,EAAQ,MAAMT,MAAA,CAAOuB,MAAM;MACrCa,SAAA,CAAU1B,UAAA,EAAY,OAAOV,MAAA,CAAOuB,MAAM;IAC3C;IAEDoB,MAAA,CAAOO,UAAA,CAAWC,YAAA,CAAa,IAAIC,KAAA,CAAM,CAACC,IAAA,CAAKC,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC;IAE5D,OAAOX,MAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}