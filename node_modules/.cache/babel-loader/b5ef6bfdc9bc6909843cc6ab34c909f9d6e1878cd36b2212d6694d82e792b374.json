{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nfunction useHelper(object3D, helperConstructor, ...args) {\n  const helper = React.useRef(null);\n  const scene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    let currentHelper = undefined;\n    if (object3D && object3D != null && object3D.current && helperConstructor) {\n      helper.current = currentHelper = new helperConstructor(object3D.current, ...args);\n    }\n    if (currentHelper) {\n      // Prevent the helpers from blocking rays\n      currentHelper.traverse(child => child.raycast = () => null);\n      scene.add(currentHelper);\n      return () => {\n        helper.current = undefined;\n        scene.remove(currentHelper);\n        currentHelper.dispose == null || currentHelper.dispose();\n      };\n    }\n  }, [scene, helperConstructor, object3D, ...args]);\n  useFrame(() => {\n    var _helper$current;\n    return void ((_helper$current = helper.current) == null || _helper$current.update == null ? void 0 : _helper$current.update());\n  });\n  return helper;\n}\n\n//\n\nconst Helper = ({\n  type: helperConstructor,\n  args = []\n}) => {\n  const thisRef = React.useRef(null);\n  const parentRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    parentRef.current = thisRef.current.parent;\n  });\n  useHelper(parentRef, helperConstructor, ...args);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: thisRef\n  });\n};\nexport { Helper, useHelper };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "useHelper", "object3D", "helperConstructor", "args", "helper", "useRef", "scene", "state", "useLayoutEffect", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "current", "traverse", "child", "raycast", "add", "remove", "dispose", "_helper$current", "update", "Helper", "type", "thisRef", "parentRef", "parent", "createElement", "ref"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Helper.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nfunction useHelper(object3D, helperConstructor, ...args) {\n  const helper = React.useRef(null);\n  const scene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    let currentHelper = undefined;\n    if (object3D && object3D != null && object3D.current && helperConstructor) {\n      helper.current = currentHelper = new helperConstructor(object3D.current, ...args);\n    }\n    if (currentHelper) {\n      // Prevent the helpers from blocking rays\n      currentHelper.traverse(child => child.raycast = () => null);\n      scene.add(currentHelper);\n      return () => {\n        helper.current = undefined;\n        scene.remove(currentHelper);\n        currentHelper.dispose == null || currentHelper.dispose();\n      };\n    }\n  }, [scene, helperConstructor, object3D, ...args]);\n  useFrame(() => {\n    var _helper$current;\n    return void ((_helper$current = helper.current) == null || _helper$current.update == null ? void 0 : _helper$current.update());\n  });\n  return helper;\n}\n\n//\n\nconst Helper = ({\n  type: helperConstructor,\n  args = []\n}) => {\n  const thisRef = React.useRef(null);\n  const parentRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    parentRef.current = thisRef.current.parent;\n  });\n  useHelper(parentRef, helperConstructor, ...args);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: thisRef\n  });\n};\n\nexport { Helper, useHelper };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,SAASC,SAASA,CAACC,QAAQ,EAAEC,iBAAiB,EAAE,GAAGC,IAAI,EAAE;EACvD,MAAMC,MAAM,GAAGP,KAAK,CAACQ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,KAAK,GAAGR,QAAQ,CAACS,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAC5CT,KAAK,CAACW,eAAe,CAAC,MAAM;IAC1B,IAAIC,aAAa,GAAGC,SAAS;IAC7B,IAAIT,QAAQ,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACU,OAAO,IAAIT,iBAAiB,EAAE;MACzEE,MAAM,CAACO,OAAO,GAAGF,aAAa,GAAG,IAAIP,iBAAiB,CAACD,QAAQ,CAACU,OAAO,EAAE,GAAGR,IAAI,CAAC;IACnF;IACA,IAAIM,aAAa,EAAE;MACjB;MACAA,aAAa,CAACG,QAAQ,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,IAAI,CAAC;MAC3DR,KAAK,CAACS,GAAG,CAACN,aAAa,CAAC;MACxB,OAAO,MAAM;QACXL,MAAM,CAACO,OAAO,GAAGD,SAAS;QAC1BJ,KAAK,CAACU,MAAM,CAACP,aAAa,CAAC;QAC3BA,aAAa,CAACQ,OAAO,IAAI,IAAI,IAAIR,aAAa,CAACQ,OAAO,CAAC,CAAC;MAC1D,CAAC;IACH;EACF,CAAC,EAAE,CAACX,KAAK,EAAEJ,iBAAiB,EAAED,QAAQ,EAAE,GAAGE,IAAI,CAAC,CAAC;EACjDJ,QAAQ,CAAC,MAAM;IACb,IAAImB,eAAe;IACnB,OAAO,MAAM,CAACA,eAAe,GAAGd,MAAM,CAACO,OAAO,KAAK,IAAI,IAAIO,eAAe,CAACC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGD,eAAe,CAACC,MAAM,CAAC,CAAC,CAAC;EAChI,CAAC,CAAC;EACF,OAAOf,MAAM;AACf;;AAEA;;AAEA,MAAMgB,MAAM,GAAGA,CAAC;EACdC,IAAI,EAAEnB,iBAAiB;EACvBC,IAAI,GAAG;AACT,CAAC,KAAK;EACJ,MAAMmB,OAAO,GAAGzB,KAAK,CAACQ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkB,SAAS,GAAG1B,KAAK,CAACQ,MAAM,CAAC,IAAI,CAAC;EACpCR,KAAK,CAACW,eAAe,CAAC,MAAM;IAC1Be,SAAS,CAACZ,OAAO,GAAGW,OAAO,CAACX,OAAO,CAACa,MAAM;EAC5C,CAAC,CAAC;EACFxB,SAAS,CAACuB,SAAS,EAAErB,iBAAiB,EAAE,GAAGC,IAAI,CAAC;EAChD,OAAO,aAAaN,KAAK,CAAC4B,aAAa,CAAC,UAAU,EAAE;IAClDC,GAAG,EAAEJ;EACP,CAAC,CAAC;AACJ,CAAC;AAED,SAASF,MAAM,EAAEpB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}