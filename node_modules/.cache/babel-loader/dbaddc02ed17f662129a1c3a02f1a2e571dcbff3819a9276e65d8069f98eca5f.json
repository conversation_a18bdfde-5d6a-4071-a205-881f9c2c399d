{"ast": null, "code": "import * as React from 'react';\nimport { useThree, use<PERSON>rame } from '@react-three/fiber';\nimport { Vector3, Object3D, Matrix4, Quaternion } from 'three';\nimport { OrthographicCamera } from './OrthographicCamera.js';\nimport { Hud } from './Hud.js';\nconst Context = /* @__PURE__ */React.createContext({});\nconst useGizmoContext = () => {\n  return React.useContext(Context);\n};\nconst turnRate = 2 * Math.PI; // turn rate in angles per second\nconst dummy = /* @__PURE__ */new Object3D();\nconst matrix = /* @__PURE__ */new Matrix4();\nconst [q1, q2] = [/* @__PURE__ */new Quaternion(), /* @__PURE__ */new Quaternion()];\nconst target = /* @__PURE__ */new Vector3();\nconst targetPosition = /* @__PURE__ */new Vector3();\nconst isOrbitControls = controls => {\n  return 'minPolarAngle' in controls;\n};\nconst isCameraControls = controls => {\n  return 'getTarget' in controls;\n};\nconst GizmoHelper = ({\n  alignment = 'bottom-right',\n  margin = [80, 80],\n  renderPriority = 1,\n  onUpdate,\n  onTarget,\n  children\n}) => {\n  const size = useThree(state => state.size);\n  const mainCamera = useThree(state => state.camera);\n  // @ts-ignore\n  const defaultControls = useThree(state => state.controls);\n  const invalidate = useThree(state => state.invalidate);\n  const gizmoRef = React.useRef(null);\n  const virtualCam = React.useRef(null);\n  const animating = React.useRef(false);\n  const radius = React.useRef(0);\n  const focusPoint = React.useRef(new Vector3(0, 0, 0));\n  const defaultUp = React.useRef(new Vector3(0, 0, 0));\n  React.useEffect(() => {\n    defaultUp.current.copy(mainCamera.up);\n    dummy.up.copy(mainCamera.up);\n  }, [mainCamera]);\n  const tweenCamera = React.useCallback(direction => {\n    animating.current = true;\n    if (defaultControls || onTarget) {\n      focusPoint.current = (onTarget == null ? void 0 : onTarget()) || (isCameraControls(defaultControls) ? defaultControls.getTarget(focusPoint.current) : defaultControls == null ? void 0 : defaultControls.target);\n    }\n    radius.current = mainCamera.position.distanceTo(target);\n\n    // Rotate from current camera orientation\n    q1.copy(mainCamera.quaternion);\n\n    // To new current camera orientation\n    targetPosition.copy(direction).multiplyScalar(radius.current).add(target);\n    dummy.lookAt(targetPosition);\n    q2.copy(dummy.quaternion);\n    invalidate();\n  }, [defaultControls, mainCamera, onTarget, invalidate]);\n  useFrame((_, delta) => {\n    if (virtualCam.current && gizmoRef.current) {\n      var _gizmoRef$current;\n      // Animate step\n      if (animating.current) {\n        if (q1.angleTo(q2) < 0.01) {\n          animating.current = false;\n          // Orbit controls uses UP vector as the orbit axes,\n          // so we need to reset it after the animation is done\n          // moving it around for the controls to work correctly\n          if (isOrbitControls(defaultControls)) {\n            mainCamera.up.copy(defaultUp.current);\n          }\n        } else {\n          const step = delta * turnRate;\n          // animate position by doing a slerp and then scaling the position on the unit sphere\n          q1.rotateTowards(q2, step);\n          // animate orientation\n          mainCamera.position.set(0, 0, 1).applyQuaternion(q1).multiplyScalar(radius.current).add(focusPoint.current);\n          mainCamera.up.set(0, 1, 0).applyQuaternion(q1).normalize();\n          mainCamera.quaternion.copy(q1);\n          if (isCameraControls(defaultControls)) defaultControls.setPosition(mainCamera.position.x, mainCamera.position.y, mainCamera.position.z);\n          if (onUpdate) onUpdate();else if (defaultControls) defaultControls.update(delta);\n          invalidate();\n        }\n      }\n\n      // Sync Gizmo with main camera orientation\n      matrix.copy(mainCamera.matrix).invert();\n      (_gizmoRef$current = gizmoRef.current) == null || _gizmoRef$current.quaternion.setFromRotationMatrix(matrix);\n    }\n  });\n  const gizmoHelperContext = React.useMemo(() => ({\n    tweenCamera\n  }), [tweenCamera]);\n\n  // Position gizmo component within scene\n  const [marginX, marginY] = margin;\n  const x = alignment.endsWith('-center') ? 0 : alignment.endsWith('-left') ? -size.width / 2 + marginX : size.width / 2 - marginX;\n  const y = alignment.startsWith('center-') ? 0 : alignment.startsWith('top-') ? size.height / 2 - marginY : -size.height / 2 + marginY;\n  return /*#__PURE__*/React.createElement(Hud, {\n    renderPriority: renderPriority\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: gizmoHelperContext\n  }, /*#__PURE__*/React.createElement(OrthographicCamera, {\n    makeDefault: true,\n    ref: virtualCam,\n    position: [0, 0, 200]\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: gizmoRef,\n    position: [x, y, 0]\n  }, children)));\n};\nexport { GizmoHelper, useGizmoContext };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "Vector3", "Object3D", "Matrix4", "Quaternion", "OrthographicCamera", "<PERSON><PERSON>", "Context", "createContext", "useGizmoContext", "useContext", "turnRate", "Math", "PI", "dummy", "matrix", "q1", "q2", "target", "targetPosition", "isOrbitControls", "controls", "isCameraControls", "GizmoHelper", "alignment", "margin", "renderPriority", "onUpdate", "onTarget", "children", "size", "state", "mainCamera", "camera", "defaultControls", "invalidate", "gizmoRef", "useRef", "virtualCam", "animating", "radius", "focusPoint", "defaultUp", "useEffect", "current", "copy", "up", "tweenCamera", "useCallback", "direction", "get<PERSON><PERSON><PERSON>", "position", "distanceTo", "quaternion", "multiplyScalar", "add", "lookAt", "_", "delta", "_gizmoRef$current", "angleTo", "step", "rotateTowards", "set", "applyQuaternion", "normalize", "setPosition", "x", "y", "z", "update", "invert", "setFromRotationMatrix", "gizmoHelperContext", "useMemo", "marginX", "marginY", "endsWith", "width", "startsWith", "height", "createElement", "Provider", "value", "makeDefault", "ref"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/GizmoHelper.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, use<PERSON>rame } from '@react-three/fiber';\nimport { Vector3, Object3D, Matrix4, Quaternion } from 'three';\nimport { OrthographicCamera } from './OrthographicCamera.js';\nimport { Hud } from './Hud.js';\n\nconst Context = /* @__PURE__ */React.createContext({});\nconst useGizmoContext = () => {\n  return React.useContext(Context);\n};\nconst turnRate = 2 * Math.PI; // turn rate in angles per second\nconst dummy = /* @__PURE__ */new Object3D();\nconst matrix = /* @__PURE__ */new Matrix4();\nconst [q1, q2] = [/* @__PURE__ */new Quaternion(), /* @__PURE__ */new Quaternion()];\nconst target = /* @__PURE__ */new Vector3();\nconst targetPosition = /* @__PURE__ */new Vector3();\nconst isOrbitControls = controls => {\n  return 'minPolarAngle' in controls;\n};\nconst isCameraControls = controls => {\n  return 'getTarget' in controls;\n};\nconst GizmoHelper = ({\n  alignment = 'bottom-right',\n  margin = [80, 80],\n  renderPriority = 1,\n  onUpdate,\n  onTarget,\n  children\n}) => {\n  const size = useThree(state => state.size);\n  const mainCamera = useThree(state => state.camera);\n  // @ts-ignore\n  const defaultControls = useThree(state => state.controls);\n  const invalidate = useThree(state => state.invalidate);\n  const gizmoRef = React.useRef(null);\n  const virtualCam = React.useRef(null);\n  const animating = React.useRef(false);\n  const radius = React.useRef(0);\n  const focusPoint = React.useRef(new Vector3(0, 0, 0));\n  const defaultUp = React.useRef(new Vector3(0, 0, 0));\n  React.useEffect(() => {\n    defaultUp.current.copy(mainCamera.up);\n    dummy.up.copy(mainCamera.up);\n  }, [mainCamera]);\n  const tweenCamera = React.useCallback(direction => {\n    animating.current = true;\n    if (defaultControls || onTarget) {\n      focusPoint.current = (onTarget == null ? void 0 : onTarget()) || (isCameraControls(defaultControls) ? defaultControls.getTarget(focusPoint.current) : defaultControls == null ? void 0 : defaultControls.target);\n    }\n    radius.current = mainCamera.position.distanceTo(target);\n\n    // Rotate from current camera orientation\n    q1.copy(mainCamera.quaternion);\n\n    // To new current camera orientation\n    targetPosition.copy(direction).multiplyScalar(radius.current).add(target);\n    dummy.lookAt(targetPosition);\n    q2.copy(dummy.quaternion);\n    invalidate();\n  }, [defaultControls, mainCamera, onTarget, invalidate]);\n  useFrame((_, delta) => {\n    if (virtualCam.current && gizmoRef.current) {\n      var _gizmoRef$current;\n      // Animate step\n      if (animating.current) {\n        if (q1.angleTo(q2) < 0.01) {\n          animating.current = false;\n          // Orbit controls uses UP vector as the orbit axes,\n          // so we need to reset it after the animation is done\n          // moving it around for the controls to work correctly\n          if (isOrbitControls(defaultControls)) {\n            mainCamera.up.copy(defaultUp.current);\n          }\n        } else {\n          const step = delta * turnRate;\n          // animate position by doing a slerp and then scaling the position on the unit sphere\n          q1.rotateTowards(q2, step);\n          // animate orientation\n          mainCamera.position.set(0, 0, 1).applyQuaternion(q1).multiplyScalar(radius.current).add(focusPoint.current);\n          mainCamera.up.set(0, 1, 0).applyQuaternion(q1).normalize();\n          mainCamera.quaternion.copy(q1);\n          if (isCameraControls(defaultControls)) defaultControls.setPosition(mainCamera.position.x, mainCamera.position.y, mainCamera.position.z);\n          if (onUpdate) onUpdate();else if (defaultControls) defaultControls.update(delta);\n          invalidate();\n        }\n      }\n\n      // Sync Gizmo with main camera orientation\n      matrix.copy(mainCamera.matrix).invert();\n      (_gizmoRef$current = gizmoRef.current) == null || _gizmoRef$current.quaternion.setFromRotationMatrix(matrix);\n    }\n  });\n  const gizmoHelperContext = React.useMemo(() => ({\n    tweenCamera\n  }), [tweenCamera]);\n\n  // Position gizmo component within scene\n  const [marginX, marginY] = margin;\n  const x = alignment.endsWith('-center') ? 0 : alignment.endsWith('-left') ? -size.width / 2 + marginX : size.width / 2 - marginX;\n  const y = alignment.startsWith('center-') ? 0 : alignment.startsWith('top-') ? size.height / 2 - marginY : -size.height / 2 + marginY;\n  return /*#__PURE__*/React.createElement(Hud, {\n    renderPriority: renderPriority\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: gizmoHelperContext\n  }, /*#__PURE__*/React.createElement(OrthographicCamera, {\n    makeDefault: true,\n    ref: virtualCam,\n    position: [0, 0, 200]\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: gizmoRef,\n    position: [x, y, 0]\n  }, children)));\n};\n\nexport { GizmoHelper, useGizmoContext };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,GAAG,QAAQ,UAAU;AAE9B,MAAMC,OAAO,GAAG,eAAeT,KAAK,CAACU,aAAa,CAAC,CAAC,CAAC,CAAC;AACtD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,OAAOX,KAAK,CAACY,UAAU,CAACH,OAAO,CAAC;AAClC,CAAC;AACD,MAAMI,QAAQ,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;AAC9B,MAAMC,KAAK,GAAG,eAAe,IAAIZ,QAAQ,CAAC,CAAC;AAC3C,MAAMa,MAAM,GAAG,eAAe,IAAIZ,OAAO,CAAC,CAAC;AAC3C,MAAM,CAACa,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,eAAe,IAAIb,UAAU,CAAC,CAAC,EAAE,eAAe,IAAIA,UAAU,CAAC,CAAC,CAAC;AACnF,MAAMc,MAAM,GAAG,eAAe,IAAIjB,OAAO,CAAC,CAAC;AAC3C,MAAMkB,cAAc,GAAG,eAAe,IAAIlB,OAAO,CAAC,CAAC;AACnD,MAAMmB,eAAe,GAAGC,QAAQ,IAAI;EAClC,OAAO,eAAe,IAAIA,QAAQ;AACpC,CAAC;AACD,MAAMC,gBAAgB,GAAGD,QAAQ,IAAI;EACnC,OAAO,WAAW,IAAIA,QAAQ;AAChC,CAAC;AACD,MAAME,WAAW,GAAGA,CAAC;EACnBC,SAAS,GAAG,cAAc;EAC1BC,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EACjBC,cAAc,GAAG,CAAC;EAClBC,QAAQ;EACRC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,IAAI,GAAG/B,QAAQ,CAACgC,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,UAAU,GAAGjC,QAAQ,CAACgC,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC;EAClD;EACA,MAAMC,eAAe,GAAGnC,QAAQ,CAACgC,KAAK,IAAIA,KAAK,CAACV,QAAQ,CAAC;EACzD,MAAMc,UAAU,GAAGpC,QAAQ,CAACgC,KAAK,IAAIA,KAAK,CAACI,UAAU,CAAC;EACtD,MAAMC,QAAQ,GAAGtC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,UAAU,GAAGxC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,SAAS,GAAGzC,KAAK,CAACuC,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMG,MAAM,GAAG1C,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAMI,UAAU,GAAG3C,KAAK,CAACuC,MAAM,CAAC,IAAIpC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACrD,MAAMyC,SAAS,GAAG5C,KAAK,CAACuC,MAAM,CAAC,IAAIpC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpDH,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpBD,SAAS,CAACE,OAAO,CAACC,IAAI,CAACb,UAAU,CAACc,EAAE,CAAC;IACrChC,KAAK,CAACgC,EAAE,CAACD,IAAI,CAACb,UAAU,CAACc,EAAE,CAAC;EAC9B,CAAC,EAAE,CAACd,UAAU,CAAC,CAAC;EAChB,MAAMe,WAAW,GAAGjD,KAAK,CAACkD,WAAW,CAACC,SAAS,IAAI;IACjDV,SAAS,CAACK,OAAO,GAAG,IAAI;IACxB,IAAIV,eAAe,IAAIN,QAAQ,EAAE;MAC/Ba,UAAU,CAACG,OAAO,GAAG,CAAChB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC,MAAMN,gBAAgB,CAACY,eAAe,CAAC,GAAGA,eAAe,CAACgB,SAAS,CAACT,UAAU,CAACG,OAAO,CAAC,GAAGV,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAChB,MAAM,CAAC;IAClN;IACAsB,MAAM,CAACI,OAAO,GAAGZ,UAAU,CAACmB,QAAQ,CAACC,UAAU,CAAClC,MAAM,CAAC;;IAEvD;IACAF,EAAE,CAAC6B,IAAI,CAACb,UAAU,CAACqB,UAAU,CAAC;;IAE9B;IACAlC,cAAc,CAAC0B,IAAI,CAACI,SAAS,CAAC,CAACK,cAAc,CAACd,MAAM,CAACI,OAAO,CAAC,CAACW,GAAG,CAACrC,MAAM,CAAC;IACzEJ,KAAK,CAAC0C,MAAM,CAACrC,cAAc,CAAC;IAC5BF,EAAE,CAAC4B,IAAI,CAAC/B,KAAK,CAACuC,UAAU,CAAC;IACzBlB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACD,eAAe,EAAEF,UAAU,EAAEJ,QAAQ,EAAEO,UAAU,CAAC,CAAC;EACvDnC,QAAQ,CAAC,CAACyD,CAAC,EAAEC,KAAK,KAAK;IACrB,IAAIpB,UAAU,CAACM,OAAO,IAAIR,QAAQ,CAACQ,OAAO,EAAE;MAC1C,IAAIe,iBAAiB;MACrB;MACA,IAAIpB,SAAS,CAACK,OAAO,EAAE;QACrB,IAAI5B,EAAE,CAAC4C,OAAO,CAAC3C,EAAE,CAAC,GAAG,IAAI,EAAE;UACzBsB,SAAS,CAACK,OAAO,GAAG,KAAK;UACzB;UACA;UACA;UACA,IAAIxB,eAAe,CAACc,eAAe,CAAC,EAAE;YACpCF,UAAU,CAACc,EAAE,CAACD,IAAI,CAACH,SAAS,CAACE,OAAO,CAAC;UACvC;QACF,CAAC,MAAM;UACL,MAAMiB,IAAI,GAAGH,KAAK,GAAG/C,QAAQ;UAC7B;UACAK,EAAE,CAAC8C,aAAa,CAAC7C,EAAE,EAAE4C,IAAI,CAAC;UAC1B;UACA7B,UAAU,CAACmB,QAAQ,CAACY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,eAAe,CAAChD,EAAE,CAAC,CAACsC,cAAc,CAACd,MAAM,CAACI,OAAO,CAAC,CAACW,GAAG,CAACd,UAAU,CAACG,OAAO,CAAC;UAC3GZ,UAAU,CAACc,EAAE,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,eAAe,CAAChD,EAAE,CAAC,CAACiD,SAAS,CAAC,CAAC;UAC1DjC,UAAU,CAACqB,UAAU,CAACR,IAAI,CAAC7B,EAAE,CAAC;UAC9B,IAAIM,gBAAgB,CAACY,eAAe,CAAC,EAAEA,eAAe,CAACgC,WAAW,CAAClC,UAAU,CAACmB,QAAQ,CAACgB,CAAC,EAAEnC,UAAU,CAACmB,QAAQ,CAACiB,CAAC,EAAEpC,UAAU,CAACmB,QAAQ,CAACkB,CAAC,CAAC;UACvI,IAAI1C,QAAQ,EAAEA,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAIO,eAAe,EAAEA,eAAe,CAACoC,MAAM,CAACZ,KAAK,CAAC;UAChFvB,UAAU,CAAC,CAAC;QACd;MACF;;MAEA;MACApB,MAAM,CAAC8B,IAAI,CAACb,UAAU,CAACjB,MAAM,CAAC,CAACwD,MAAM,CAAC,CAAC;MACvC,CAACZ,iBAAiB,GAAGvB,QAAQ,CAACQ,OAAO,KAAK,IAAI,IAAIe,iBAAiB,CAACN,UAAU,CAACmB,qBAAqB,CAACzD,MAAM,CAAC;IAC9G;EACF,CAAC,CAAC;EACF,MAAM0D,kBAAkB,GAAG3E,KAAK,CAAC4E,OAAO,CAAC,OAAO;IAC9C3B;EACF,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAElB;EACA,MAAM,CAAC4B,OAAO,EAAEC,OAAO,CAAC,GAAGnD,MAAM;EACjC,MAAM0C,CAAC,GAAG3C,SAAS,CAACqD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAGrD,SAAS,CAACqD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC/C,IAAI,CAACgD,KAAK,GAAG,CAAC,GAAGH,OAAO,GAAG7C,IAAI,CAACgD,KAAK,GAAG,CAAC,GAAGH,OAAO;EAChI,MAAMP,CAAC,GAAG5C,SAAS,CAACuD,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAGvD,SAAS,CAACuD,UAAU,CAAC,MAAM,CAAC,GAAGjD,IAAI,CAACkD,MAAM,GAAG,CAAC,GAAGJ,OAAO,GAAG,CAAC9C,IAAI,CAACkD,MAAM,GAAG,CAAC,GAAGJ,OAAO;EACrI,OAAO,aAAa9E,KAAK,CAACmF,aAAa,CAAC3E,GAAG,EAAE;IAC3CoB,cAAc,EAAEA;EAClB,CAAC,EAAE,aAAa5B,KAAK,CAACmF,aAAa,CAAC1E,OAAO,CAAC2E,QAAQ,EAAE;IACpDC,KAAK,EAAEV;EACT,CAAC,EAAE,aAAa3E,KAAK,CAACmF,aAAa,CAAC5E,kBAAkB,EAAE;IACtD+E,WAAW,EAAE,IAAI;IACjBC,GAAG,EAAE/C,UAAU;IACfa,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG;EACtB,CAAC,CAAC,EAAE,aAAarD,KAAK,CAACmF,aAAa,CAAC,OAAO,EAAE;IAC5CI,GAAG,EAAEjD,QAAQ;IACbe,QAAQ,EAAE,CAACgB,CAAC,EAAEC,CAAC,EAAE,CAAC;EACpB,CAAC,EAAEvC,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,SAASN,WAAW,EAAEd,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}