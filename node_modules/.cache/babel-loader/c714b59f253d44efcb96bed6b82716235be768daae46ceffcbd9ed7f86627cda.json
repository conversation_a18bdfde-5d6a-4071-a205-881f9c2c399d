{"ast": null, "code": "import { Vector2 } from \"three\";\nconst ConvolutionShader = {\n  defines: {\n    KERNEL_SIZE_FLOAT: \"25.0\",\n    KERNEL_SIZE_INT: \"25\"\n  },\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    uImageIncrement: {\n      value: /* @__PURE__ */new Vector2(1953125e-9, 0)\n    },\n    cKernel: {\n      value: []\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv - ( ( KERNEL_SIZE_FLOAT - 1.0 ) / 2.0 ) * uImageIncrement;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float cKernel[ KERNEL_SIZE_INT ];\n\n    uniform sampler2D tDiffuse;\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 imageCoord = vUv;\n    \tvec4 sum = vec4( 0.0, 0.0, 0.0, 0.0 );\n\n    \tfor( int i = 0; i < KERNEL_SIZE_INT; i ++ ) {\n\n    \t\tsum += texture2D( tDiffuse, imageCoord ) * cKernel[ i ];\n    \t\timageCoord += uImageIncrement;\n\n    \t}\n\n    \tgl_FragColor = sum;\n\n    }\n  `),\n  buildKernel: function (sigma) {\n    function gauss(x, sigma2) {\n      return Math.exp(-(x * x) / (2 * sigma2 * sigma2));\n    }\n    const kMaxKernelSize = 25;\n    const kernelSize = Math.min(2 * Math.ceil(sigma * 3) + 1, kMaxKernelSize);\n    const halfWidth = (kernelSize - 1) * 0.5;\n    const values = new Array(kernelSize);\n    let sum = 0;\n    for (let i = 0; i < kernelSize; ++i) {\n      values[i] = gauss(i - halfWidth, sigma);\n      sum += values[i];\n    }\n    for (let i = 0; i < kernelSize; ++i) values[i] /= sum;\n    return values;\n  }\n};\nexport { ConvolutionShader };", "map": {"version": 3, "names": ["ConvolutionShader", "defines", "KERNEL_SIZE_FLOAT", "KERNEL_SIZE_INT", "uniforms", "tDiffuse", "value", "uImageIncrement", "Vector2", "cK<PERSON>l", "vertexShader", "fragmentShader", "buildKernel", "sigma", "gauss", "x", "sigma2", "Math", "exp", "kMaxKernelSize", "kernelSize", "min", "ceil", "halfWidth", "values", "Array", "sum", "i"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/ConvolutionShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Convolution shader\n * ported from o3d sample to WebGL / GLSL\n * http://o3d.googlecode.com/svn/trunk/samples/convolution.html\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type ConvolutionShaderDefines = {\n  KERNEL_SIZE_FLOAT: string\n  KERNEL_SIZE_INT: string\n}\n\nexport type ConvolutionShaderUniforms = {\n  cKernel: IUniform<number[]>\n  tDiffuse: IUniform<Texture | null>\n  uImageIncrement: IUniform<Vector2>\n}\n\nexport interface IConvolutionShader extends IShader<ConvolutionShaderUniforms, ConvolutionShaderDefines> {\n  buildKernel: (sigma: number) => number[]\n}\n\nexport const ConvolutionShader: IConvolutionShader = {\n  defines: {\n    KERNEL_SIZE_FLOAT: '25.0',\n    KERNEL_SIZE_INT: '25',\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    uImageIncrement: { value: /* @__PURE__ */ new Vector2(0.001953125, 0.0) },\n    cKernel: { value: [] },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv - ( ( KERNEL_SIZE_FLOAT - 1.0 ) / 2.0 ) * uImageIncrement;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float cKernel[ KERNEL_SIZE_INT ];\n\n    uniform sampler2D tDiffuse;\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 imageCoord = vUv;\n    \tvec4 sum = vec4( 0.0, 0.0, 0.0, 0.0 );\n\n    \tfor( int i = 0; i < KERNEL_SIZE_INT; i ++ ) {\n\n    \t\tsum += texture2D( tDiffuse, imageCoord ) * cKernel[ i ];\n    \t\timageCoord += uImageIncrement;\n\n    \t}\n\n    \tgl_FragColor = sum;\n\n    }\n  `,\n\n  buildKernel: function (sigma) {\n    // We lop off the sqrt(2 * pi) * sigma term, since we're going to normalize anyway.\n\n    function gauss(x: number, sigma: number): number {\n      return Math.exp(-(x * x) / (2.0 * sigma * sigma))\n    }\n\n    const kMaxKernelSize = 25\n\n    const kernelSize = Math.min(2 * Math.ceil(sigma * 3.0) + 1, kMaxKernelSize)\n\n    const halfWidth = (kernelSize - 1) * 0.5\n\n    const values: number[] = new Array(kernelSize)\n\n    let sum = 0.0\n\n    for (let i = 0; i < kernelSize; ++i) {\n      values[i] = gauss(i - halfWidth, sigma)\n      sum += values[i]\n    }\n\n    // normalize the kernel\n\n    for (let i = 0; i < kernelSize; ++i) values[i] /= sum\n\n    return values\n  },\n}\n"], "mappings": ";AA0BO,MAAMA,iBAAA,GAAwC;EACnDC,OAAA,EAAS;IACPC,iBAAA,EAAmB;IACnBC,eAAA,EAAiB;EACnB;EAEAC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,eAAA,EAAiB;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,YAAa,CAAG;IAAE;IACxEC,OAAA,EAAS;MAAEH,KAAA,EAAO;IAAG;EACvB;EAEAI,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAazBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAyB3BC,WAAA,EAAa,SAAAA,CAAUC,KAAA,EAAO;IAGnB,SAAAC,MAAMC,CAAA,EAAWC,MAAA,EAAuB;MAC/C,OAAOC,IAAA,CAAKC,GAAA,CAAI,EAAEH,CAAA,GAAIA,CAAA,KAAM,IAAMC,MAAA,GAAQA,MAAA,CAAM;IAClD;IAEA,MAAMG,cAAA,GAAiB;IAEjB,MAAAC,UAAA,GAAaH,IAAA,CAAKI,GAAA,CAAI,IAAIJ,IAAA,CAAKK,IAAA,CAAKT,KAAA,GAAQ,CAAG,IAAI,GAAGM,cAAc;IAEpE,MAAAI,SAAA,IAAaH,UAAA,GAAa,KAAK;IAE/B,MAAAI,MAAA,GAAmB,IAAIC,KAAA,CAAML,UAAU;IAE7C,IAAIM,GAAA,GAAM;IAEV,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIP,UAAA,EAAY,EAAEO,CAAA,EAAG;MACnCH,MAAA,CAAOG,CAAC,IAAIb,KAAA,CAAMa,CAAA,GAAIJ,SAAA,EAAWV,KAAK;MACtCa,GAAA,IAAOF,MAAA,CAAOG,CAAC;IACjB;IAIA,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIP,UAAA,EAAY,EAAEO,CAAA,EAAGH,MAAA,CAAOG,CAAC,KAAKD,GAAA;IAE3C,OAAAF,MAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}