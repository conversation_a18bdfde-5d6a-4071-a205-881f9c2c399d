{"ast": null, "code": "const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\nexport { presetsObj };", "map": {"version": 3, "names": ["presetsObj", "apartment", "city", "dawn", "forest", "lobby", "night", "park", "studio", "sunset", "warehouse"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\nexport { presetsObj };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACjBC,SAAS,EAAE,gBAAgB;EAC3BC,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAE,qBAAqB;EAC7BC,KAAK,EAAE,2BAA2B;EAClCC,KAAK,EAAE,wBAAwB;EAC/BC,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAE,wBAAwB;EAChCC,MAAM,EAAE,sBAAsB;EAC9BC,SAAS,EAAE;AACb,CAAC;AAED,SAASV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}