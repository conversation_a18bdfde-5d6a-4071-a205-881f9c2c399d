{"ast": null, "code": "/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { Q as QuadRenderer } from './QuadRenderer-DuOPRGA4.js';\nimport { ShaderMaterial, Vector3, NoBlending, SRGBColorSpace, LinearSRGBColorSpace, HalfFloatType, Loader, LoadingManager, Texture, UVMapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, FileLoader } from 'three';\nconst vertexShader = /* glsl */`\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */`\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends ShaderMaterial {\n  /**\n   *\n   * @param params\n   */\n  constructor({\n    gamma,\n    offsetHdr,\n    offsetSdr,\n    gainMapMin,\n    gainMapMax,\n    maxDisplayBoost,\n    hdrCapacityMin,\n    hdrCapacityMax,\n    sdr,\n    gainMap\n  }) {\n    super({\n      name: 'GainMapDecoderMaterial',\n      vertexShader,\n      fragmentShader,\n      uniforms: {\n        sdr: {\n          value: sdr\n        },\n        gainMap: {\n          value: gainMap\n        },\n        gamma: {\n          value: new Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2])\n        },\n        offsetHdr: {\n          value: new Vector3().fromArray(offsetHdr)\n        },\n        offsetSdr: {\n          value: new Vector3().fromArray(offsetSdr)\n        },\n        gainMapMin: {\n          value: new Vector3().fromArray(gainMapMin)\n        },\n        gainMapMax: {\n          value: new Vector3().fromArray(gainMapMax)\n        },\n        weightFactor: {\n          value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n        }\n      },\n      blending: NoBlending,\n      depthTest: false,\n      depthWrite: false\n    });\n    this._maxDisplayBoost = maxDisplayBoost;\n    this._hdrCapacityMin = hdrCapacityMin;\n    this._hdrCapacityMax = hdrCapacityMax;\n    this.needsUpdate = true;\n    this.uniformsNeedUpdate = true;\n  }\n  get sdr() {\n    return this.uniforms.sdr.value;\n  }\n  set sdr(value) {\n    this.uniforms.sdr.value = value;\n  }\n  get gainMap() {\n    return this.uniforms.gainMap.value;\n  }\n  set gainMap(value) {\n    this.uniforms.gainMap.value = value;\n  }\n  /**\n   * @see {@link GainMapMetadata.offsetHdr}\n   */\n  get offsetHdr() {\n    return this.uniforms.offsetHdr.value.toArray();\n  }\n  set offsetHdr(value) {\n    this.uniforms.offsetHdr.value.fromArray(value);\n  }\n  /**\n   * @see {@link GainMapMetadata.offsetSdr}\n   */\n  get offsetSdr() {\n    return this.uniforms.offsetSdr.value.toArray();\n  }\n  set offsetSdr(value) {\n    this.uniforms.offsetSdr.value.fromArray(value);\n  }\n  /**\n   * @see {@link GainMapMetadata.gainMapMin}\n   */\n  get gainMapMin() {\n    return this.uniforms.gainMapMin.value.toArray();\n  }\n  set gainMapMin(value) {\n    this.uniforms.gainMapMin.value.fromArray(value);\n  }\n  /**\n   * @see {@link GainMapMetadata.gainMapMax}\n   */\n  get gainMapMax() {\n    return this.uniforms.gainMapMax.value.toArray();\n  }\n  set gainMapMax(value) {\n    this.uniforms.gainMapMax.value.fromArray(value);\n  }\n  /**\n   * @see {@link GainMapMetadata.gamma}\n   */\n  get gamma() {\n    const g = this.uniforms.gamma.value;\n    return [1 / g.x, 1 / g.y, 1 / g.z];\n  }\n  set gamma(value) {\n    const g = this.uniforms.gamma.value;\n    g.x = 1.0 / value[0];\n    g.y = 1.0 / value[1];\n    g.z = 1.0 / value[2];\n  }\n  /**\n   * @see {@link GainMapMetadata.hdrCapacityMin}\n   * @remarks Logarithmic space\n   */\n  get hdrCapacityMin() {\n    return this._hdrCapacityMin;\n  }\n  set hdrCapacityMin(value) {\n    this._hdrCapacityMin = value;\n    this.calculateWeight();\n  }\n  /**\n   * @see {@link GainMapMetadata.hdrCapacityMin}\n   * @remarks Logarithmic space\n   */\n  get hdrCapacityMax() {\n    return this._hdrCapacityMax;\n  }\n  set hdrCapacityMax(value) {\n    this._hdrCapacityMax = value;\n    this.calculateWeight();\n  }\n  /**\n   * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n   * @remarks Non Logarithmic space\n   */\n  get maxDisplayBoost() {\n    return this._maxDisplayBoost;\n  }\n  set maxDisplayBoost(value) {\n    this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n    this.calculateWeight();\n  }\n  calculateWeight() {\n    const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n    this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n  }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = params => {\n  const {\n    sdr,\n    gainMap,\n    renderer\n  } = params;\n  if (sdr.colorSpace !== SRGBColorSpace) {\n    console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n    sdr.colorSpace = SRGBColorSpace;\n  }\n  sdr.needsUpdate = true;\n  if (gainMap.colorSpace !== LinearSRGBColorSpace) {\n    console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n    gainMap.colorSpace = LinearSRGBColorSpace;\n  }\n  gainMap.needsUpdate = true;\n  const material = new GainMapDecoderMaterial({\n    ...params,\n    sdr,\n    gainMap\n  });\n  const quadRenderer = new QuadRenderer({\n    // TODO: three types are generic, eslint complains here, see how we can solve\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n    width: sdr.image.width,\n    // TODO: three types are generic, eslint complains here, see how we can solve\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n    height: sdr.image.height,\n    type: HalfFloatType,\n    colorSpace: LinearSRGBColorSpace,\n    material,\n    renderer,\n    renderTargetOptions: params.renderTargetOptions\n  });\n  try {\n    quadRenderer.render();\n  } catch (e) {\n    quadRenderer.disposeOnDemandRenderer();\n    throw e;\n  }\n  return quadRenderer;\n};\nclass GainMapNotFoundError extends Error {}\nclass XMPMetadataNotFoundError extends Error {}\nconst getXMLValue = (xml, tag, defaultValue) => {\n  // Check for attribute format first: tag=\"value\"\n  const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n  if (attributeMatch) return attributeMatch[1];\n  // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n  const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n  if (tagMatch) {\n    // Check if it contains rdf:li elements\n    const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n    if (liValues && liValues.length === 3) {\n      return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n    }\n    return tagMatch[1].trim();\n  }\n  if (defaultValue !== undefined) return defaultValue;\n  throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = input => {\n  let str;\n  // support node test environment\n  if (typeof TextDecoder !== 'undefined') str = new TextDecoder().decode(input);else str = input.toString();\n  let start = str.indexOf('<x:xmpmeta');\n  while (start !== -1) {\n    const end = str.indexOf('x:xmpmeta>', start);\n    const xmpBlock = str.slice(start, end + 10);\n    try {\n      const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n      const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n      const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n      const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n      const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n      // These are always attributes, so we can use a simpler regex\n      const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n      const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n      const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n      if (!hdrCapacityMaxMatch) throw new Error('Incomplete gainmap metadata');\n      const hdrCapacityMax = hdrCapacityMaxMatch[1];\n      return {\n        gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n        gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n        gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n        offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n        offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n        hdrCapacityMin: parseFloat(hdrCapacityMin),\n        hdrCapacityMax: parseFloat(hdrCapacityMax)\n      };\n    } catch (e) {\n      // Continue searching for another xmpmeta block if this one fails\n    }\n    start = str.indexOf('<x:xmpmeta', end);\n  }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n  constructor(options) {\n    this.options = {\n      debug: options && options.debug !== undefined ? options.debug : false,\n      extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n      extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n    };\n  }\n  extract(imageArrayBuffer) {\n    return new Promise((resolve, reject) => {\n      const debug = this.options.debug;\n      const dataView = new DataView(imageArrayBuffer.buffer);\n      // If you're executing this line on a big endian machine, it'll be reversed.\n      // bigEnd further down though, refers to the endianness of the image itself.\n      if (dataView.getUint16(0) !== 0xffd8) {\n        reject(new Error('Not a valid jpeg'));\n        return;\n      }\n      const length = dataView.byteLength;\n      let offset = 2;\n      let loops = 0;\n      let marker; // APP# marker\n      while (offset < length) {\n        if (++loops > 250) {\n          reject(new Error(`Found no marker after ${loops} loops 😵`));\n          return;\n        }\n        if (dataView.getUint8(offset) !== 0xff) {\n          reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n          return;\n        }\n        marker = dataView.getUint8(offset + 1);\n        if (debug) console.log(`Marker: ${marker.toString(16)}`);\n        if (marker === 0xe2) {\n          if (debug) console.log('Found APP2 marker (0xffe2)');\n          // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n          // Great way to visualize image information in html is using Exiftool. E.g.:\n          // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n          const formatPt = offset + 4;\n          /*\n           *  Structure of the MP Format Identifier\n           *\n           *  Offset Addr.  | Code (Hex)  | Description\n           *  +00             ff            Marker Prefix      <-- offset\n           *  +01             e2            APP2\n           *  +02             #n            APP2 Field Length\n           *  +03             #n            APP2 Field Length\n           *  +04             4d            'M'                <-- formatPt\n           *  +05             50            'P'\n           *  +06             46            'F'\n           *  +07             00            NULL\n           *                                                   <-- tiffOffset\n           */\n          if (dataView.getUint32(formatPt) === 0x4d504600) {\n            // Found MPF tag, so we start dig out sub images\n            const tiffOffset = formatPt + 4;\n            let bigEnd; // Endianness from TIFF header\n            // Test for TIFF validity and endianness\n            // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n            if (dataView.getUint16(tiffOffset) === 0x4949) {\n              bigEnd = false;\n            } else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n              bigEnd = true;\n            } else {\n              reject(new Error('No valid endianness marker found in TIFF header'));\n              return;\n            }\n            if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n              reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n              return;\n            }\n            // 32 bit number stating the offset from the start of the 8 Byte MP Header\n            // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n            const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n            if (firstIFDOffset < 0x00000008) {\n              reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n              return;\n            }\n            // Move ahead to MP Index IFD\n            // Assume we're at the first IFD, so firstIFDOffset points to\n            // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n            // we fail silently here due to this assumption)\n            // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n            const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n            const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n            // Extract info from MPEntries (starting after Count)\n            const entriesStart = dirStart + 2;\n            let numberOfImages = 0;\n            for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n              // Each entry is 12 Bytes long\n              // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n              if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                // stored in Last 4 bytes of its 12 Byte entry.\n                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n              }\n            }\n            const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n            const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n            const images = [];\n            for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n              const image = {\n                MPType: dataView.getUint32(i, !bigEnd),\n                size: dataView.getUint32(i + 4, !bigEnd),\n                // This offset is specified relative to the address of the MP Endian\n                // field in the MP Header, unless the image is a First Individual Image,\n                // in which case the value of the offset shall be NULL (0x00000000).\n                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                start: -1,\n                end: -1,\n                isFII: false\n              };\n              if (!image.dataOffset) {\n                // dataOffset is 0x00000000 for First Individual Image\n                image.start = 0;\n                image.isFII = true;\n              } else {\n                image.start = tiffOffset + image.dataOffset;\n                image.isFII = false;\n              }\n              image.end = image.start + image.size;\n              images.push(image);\n            }\n            if (this.options.extractNonFII && images.length) {\n              const bufferBlob = new Blob([dataView]);\n              const imgs = [];\n              for (const image of images) {\n                if (image.isFII && !this.options.extractFII) {\n                  continue; // Skip FII\n                }\n                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                // we don't need this\n                // const imageUrl = URL.createObjectURL(imageBlob)\n                // image.img = document.createElement('img')\n                // image.img.src = imageUrl\n                imgs.push(imageBlob);\n              }\n              resolve(imgs);\n            }\n          }\n        }\n        offset += 2 + dataView.getUint16(offset + 2);\n      }\n    });\n  }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async jpegFile => {\n  const metadata = extractXMP(jpegFile);\n  if (!metadata) throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n  const mpfExtractor = new MPFExtractor({\n    extractFII: true,\n    extractNonFII: true\n  });\n  const images = await mpfExtractor.extract(jpegFile);\n  if (images.length !== 2) throw new GainMapNotFoundError('Gain map recovery image not found');\n  return {\n    sdr: new Uint8Array(await images[0].arrayBuffer()),\n    gainMap: new Uint8Array(await images[1].arrayBuffer()),\n    metadata\n  };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = blob => {\n  return new Promise((resolve, reject) => {\n    const img = document.createElement('img');\n    img.onload = () => {\n      resolve(img);\n    };\n    img.onerror = e => {\n      reject(e);\n    };\n    img.src = URL.createObjectURL(blob);\n  });\n};\nclass LoaderBase extends Loader {\n  /**\n   *\n   * @param renderer\n   * @param manager\n   */\n  constructor(renderer, manager) {\n    super(manager);\n    if (renderer) this._renderer = renderer;\n    this._internalLoadingManager = new LoadingManager();\n  }\n  /**\n   * Specify the renderer to use when rendering the gain map\n   *\n   * @param renderer\n   * @returns\n   */\n  setRenderer(renderer) {\n    this._renderer = renderer;\n    return this;\n  }\n  /**\n   * Specify the renderTarget options to use when rendering the gain map\n   *\n   * @param options\n   * @returns\n   */\n  setRenderTargetOptions(options) {\n    this._renderTargetOptions = options;\n    return this;\n  }\n  /**\n   * @private\n   * @returns\n   */\n  prepareQuadRenderer() {\n    if (!this._renderer) console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n    // temporary values\n    const material = new GainMapDecoderMaterial({\n      gainMapMax: [1, 1, 1],\n      gainMapMin: [0, 0, 0],\n      gamma: [1, 1, 1],\n      offsetHdr: [1, 1, 1],\n      offsetSdr: [1, 1, 1],\n      hdrCapacityMax: 1,\n      hdrCapacityMin: 0,\n      maxDisplayBoost: 1,\n      gainMap: new Texture(),\n      sdr: new Texture()\n    });\n    return new QuadRenderer({\n      width: 16,\n      height: 16,\n      type: HalfFloatType,\n      colorSpace: LinearSRGBColorSpace,\n      material,\n      renderer: this._renderer,\n      renderTargetOptions: this._renderTargetOptions\n    });\n  }\n  /**\n  * @private\n  * @param quadRenderer\n  * @param metadata\n  * @param sdrBuffer\n  * @param gainMapBuffer\n  */\n  async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n    // this is optional, will render a black gain-map if not present\n    const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], {\n      type: 'image/jpeg'\n    }) : undefined;\n    const sdrBlob = new Blob([sdrBuffer], {\n      type: 'image/jpeg'\n    });\n    let sdrImage;\n    let gainMapImage;\n    let needsFlip = false;\n    if (typeof createImageBitmap === 'undefined') {\n      const res = await Promise.all([gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined), getHTMLImageFromBlob(sdrBlob)]);\n      gainMapImage = res[0];\n      sdrImage = res[1];\n      needsFlip = true;\n    } else {\n      const res = await Promise.all([gainMapBlob ? createImageBitmap(gainMapBlob, {\n        imageOrientation: 'flipY'\n      }) : Promise.resolve(undefined), createImageBitmap(sdrBlob, {\n        imageOrientation: 'flipY'\n      })]);\n      gainMapImage = res[0];\n      sdrImage = res[1];\n    }\n    const gainMap = new Texture(gainMapImage || new ImageData(2, 2), UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, LinearSRGBColorSpace);\n    gainMap.flipY = needsFlip;\n    gainMap.needsUpdate = true;\n    const sdr = new Texture(sdrImage, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, SRGBColorSpace);\n    sdr.flipY = needsFlip;\n    sdr.needsUpdate = true;\n    quadRenderer.width = sdrImage.width;\n    quadRenderer.height = sdrImage.height;\n    quadRenderer.material.gainMap = gainMap;\n    quadRenderer.material.sdr = sdr;\n    quadRenderer.material.gainMapMin = metadata.gainMapMin;\n    quadRenderer.material.gainMapMax = metadata.gainMapMax;\n    quadRenderer.material.offsetHdr = metadata.offsetHdr;\n    quadRenderer.material.offsetSdr = metadata.offsetSdr;\n    quadRenderer.material.gamma = metadata.gamma;\n    quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n    quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n    quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n    quadRenderer.material.needsUpdate = true;\n    quadRenderer.render();\n  }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n  /**\n   * Loads a gainmap using separate data\n   * * sdr image\n   * * gain map image\n   * * metadata json\n   *\n   * useful for webp gain maps\n   *\n   * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n   * @param onLoad Load complete callback, will receive the result\n   * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n   * @param onError Error callback\n   * @returns\n   */\n  load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n    const quadRenderer = this.prepareQuadRenderer();\n    let sdr;\n    let gainMap;\n    let metadata;\n    const loadCheck = async () => {\n      if (sdr && gainMap && metadata) {\n        // solves #16\n        try {\n          await this.render(quadRenderer, metadata, sdr, gainMap);\n        } catch (error) {\n          this.manager.itemError(sdrUrl);\n          this.manager.itemError(gainMapUrl);\n          this.manager.itemError(metadataUrl);\n          if (typeof onError === 'function') onError(error);\n          quadRenderer.disposeOnDemandRenderer();\n          return;\n        }\n        if (typeof onLoad === 'function') onLoad(quadRenderer);\n        this.manager.itemEnd(sdrUrl);\n        this.manager.itemEnd(gainMapUrl);\n        this.manager.itemEnd(metadataUrl);\n        quadRenderer.disposeOnDemandRenderer();\n      }\n    };\n    let sdrLengthComputable = true;\n    let sdrTotal = 0;\n    let sdrLoaded = 0;\n    let gainMapLengthComputable = true;\n    let gainMapTotal = 0;\n    let gainMapLoaded = 0;\n    let metadataLengthComputable = true;\n    let metadataTotal = 0;\n    let metadataLoaded = 0;\n    const progressHandler = () => {\n      if (typeof onProgress === 'function') {\n        const total = sdrTotal + gainMapTotal + metadataTotal;\n        const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n        const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n        onProgress(new ProgressEvent('progress', {\n          lengthComputable,\n          loaded,\n          total\n        }));\n      }\n    };\n    this.manager.itemStart(sdrUrl);\n    this.manager.itemStart(gainMapUrl);\n    this.manager.itemStart(metadataUrl);\n    const sdrLoader = new FileLoader(this._internalLoadingManager);\n    sdrLoader.setResponseType('arraybuffer');\n    sdrLoader.setRequestHeader(this.requestHeader);\n    sdrLoader.setPath(this.path);\n    sdrLoader.setWithCredentials(this.withCredentials);\n    sdrLoader.load(sdrUrl, async buffer => {\n      /* istanbul ignore if\n       this condition exists only because of three.js types + strict mode\n      */\n      if (typeof buffer === 'string') throw new Error('Invalid sdr buffer');\n      sdr = buffer;\n      await loadCheck();\n    }, e => {\n      sdrLengthComputable = e.lengthComputable;\n      sdrLoaded = e.loaded;\n      sdrTotal = e.total;\n      progressHandler();\n    }, error => {\n      this.manager.itemError(sdrUrl);\n      if (typeof onError === 'function') onError(error);\n    });\n    const gainMapLoader = new FileLoader(this._internalLoadingManager);\n    gainMapLoader.setResponseType('arraybuffer');\n    gainMapLoader.setRequestHeader(this.requestHeader);\n    gainMapLoader.setPath(this.path);\n    gainMapLoader.setWithCredentials(this.withCredentials);\n    gainMapLoader.load(gainMapUrl, async buffer => {\n      /* istanbul ignore if\n       this condition exists only because of three.js types + strict mode\n      */\n      if (typeof buffer === 'string') throw new Error('Invalid gainmap buffer');\n      gainMap = buffer;\n      await loadCheck();\n    }, e => {\n      gainMapLengthComputable = e.lengthComputable;\n      gainMapLoaded = e.loaded;\n      gainMapTotal = e.total;\n      progressHandler();\n    }, error => {\n      this.manager.itemError(gainMapUrl);\n      if (typeof onError === 'function') onError(error);\n    });\n    const metadataLoader = new FileLoader(this._internalLoadingManager);\n    // metadataLoader.setResponseType('json')\n    metadataLoader.setRequestHeader(this.requestHeader);\n    metadataLoader.setPath(this.path);\n    metadataLoader.setWithCredentials(this.withCredentials);\n    metadataLoader.load(metadataUrl, async json => {\n      /* istanbul ignore if\n       this condition exists only because of three.js types + strict mode\n      */\n      if (typeof json !== 'string') throw new Error('Invalid metadata string');\n      // TODO: implement check on JSON file and remove this eslint disable\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      metadata = JSON.parse(json);\n      await loadCheck();\n    }, e => {\n      metadataLengthComputable = e.lengthComputable;\n      metadataLoaded = e.loaded;\n      metadataTotal = e.total;\n      progressHandler();\n    }, error => {\n      this.manager.itemError(metadataUrl);\n      if (typeof onError === 'function') onError(error);\n    });\n    return quadRenderer;\n  }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n  /**\n   * Loads a JPEG containing gain map metadata\n   * Renders a normal SDR image if gainmap data is not found\n   *\n   * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n   * @param onLoad Load complete callback, will receive the result\n   * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n   * @param onError Error callback\n   * @returns\n   */\n  load(url, onLoad, onProgress, onError) {\n    const quadRenderer = this.prepareQuadRenderer();\n    const loader = new FileLoader(this._internalLoadingManager);\n    loader.setResponseType('arraybuffer');\n    loader.setRequestHeader(this.requestHeader);\n    loader.setPath(this.path);\n    loader.setWithCredentials(this.withCredentials);\n    this.manager.itemStart(url);\n    loader.load(url, async jpeg => {\n      /* istanbul ignore if\n       this condition exists only because of three.js types + strict mode\n      */\n      if (typeof jpeg === 'string') throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n      const jpegBuffer = new Uint8Array(jpeg);\n      let sdrJPEG;\n      let gainMapJPEG;\n      let metadata;\n      try {\n        const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n        // gain map is successfully reconstructed\n        sdrJPEG = extractionResult.sdr;\n        gainMapJPEG = extractionResult.gainMap;\n        metadata = extractionResult.metadata;\n      } catch (e) {\n        // render the SDR version if this is not a gainmap\n        if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n          console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n          metadata = {\n            gainMapMin: [0, 0, 0],\n            gainMapMax: [1, 1, 1],\n            gamma: [1, 1, 1],\n            hdrCapacityMin: 0,\n            hdrCapacityMax: 1,\n            offsetHdr: [0, 0, 0],\n            offsetSdr: [0, 0, 0]\n          };\n          sdrJPEG = jpegBuffer;\n        } else {\n          throw e;\n        }\n      }\n      // solves #16\n      try {\n        await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n      } catch (error) {\n        this.manager.itemError(url);\n        if (typeof onError === 'function') onError(error);\n        quadRenderer.disposeOnDemandRenderer();\n        return;\n      }\n      if (typeof onLoad === 'function') onLoad(quadRenderer);\n      this.manager.itemEnd(url);\n      quadRenderer.disposeOnDemandRenderer();\n    }, onProgress, error => {\n      this.manager.itemError(url);\n      if (typeof onError === 'function') onError(error);\n    });\n    return quadRenderer;\n  }\n}\nexport { GainMapDecoderMaterial, GainMapLoader, HDRJPGLoader, HDRJPGLoader as JPEGRLoader, MPFExtractor, QuadRenderer, decode, extractGainmapFromJPEG, extractXMP };", "map": {"version": 3, "names": ["Q", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShaderMaterial", "Vector3", "NoBlending", "SRGBColorSpace", "LinearSRGBColorSpace", "HalfFloatType", "Loader", "LoadingManager", "Texture", "UVMapping", "ClampToEdgeWrapping", "LinearFilter", "LinearMipMapLinearFilter", "RGBAFormat", "UnsignedByteType", "<PERSON><PERSON><PERSON><PERSON>", "vertexShader", "fragmentShader", "GainMapDecoderMaterial", "constructor", "gamma", "offsetHdr", "offsetSdr", "gainMapMin", "gainMapMax", "maxDisplayBoost", "hdrCapacityMin", "hdrCapacityMax", "sdr", "gainMap", "name", "uniforms", "value", "fromArray", "weightFactor", "Math", "log2", "blending", "depthTest", "depthWrite", "_maxDisplayBoost", "_hdrCapacityMin", "_hdrCapacityMax", "needsUpdate", "uniformsNeedUpdate", "toArray", "g", "x", "y", "z", "calculateWeight", "max", "min", "val", "decode", "params", "renderer", "colorSpace", "console", "warn", "material", "quadRenderer", "width", "image", "height", "type", "renderTargetOptions", "render", "e", "dispose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GainMapNotFoundError", "Error", "XMPMetadataNotFoundError", "getXMLValue", "xml", "tag", "defaultValue", "attributeMatch", "RegExp", "exec", "tagMatch", "liV<PERSON><PERSON>", "match", "length", "map", "v", "replace", "trim", "undefined", "extractXMP", "input", "str", "TextDecoder", "toString", "start", "indexOf", "end", "xmpBlock", "slice", "offsetSDR", "offsetHDR", "hdrCapacityMinMatch", "hdrCapacityMaxMatch", "Array", "isArray", "parseFloat", "MPFExtractor", "options", "debug", "extractFII", "extractNonFII", "extract", "imageArrayBuffer", "Promise", "resolve", "reject", "dataView", "DataView", "buffer", "getUint16", "byteLength", "offset", "loops", "marker", "getUint8", "log", "formatPt", "getUint32", "tiffOffset", "bigEnd", "firstIFDOffset", "dirStart", "count", "entriesStart", "numberOfImages", "i", "nextIFDOffsetLen", "MPImageListValPt", "images", "MPType", "size", "dataOffset", "dependantImages", "isFII", "push", "bufferBlob", "Blob", "imgs", "imageBlob", "extractGainmapFromJPEG", "jpegFile", "metadata", "mpfExtractor", "Uint8Array", "arrayBuffer", "getHTMLImageFromBlob", "blob", "img", "document", "createElement", "onload", "onerror", "src", "URL", "createObjectURL", "LoaderBase", "manager", "_renderer", "_internalLoadingManager", "<PERSON><PERSON><PERSON><PERSON>", "setRenderTargetOptions", "_renderTargetOptions", "prepare<PERSON><PERSON><PERSON><PERSON><PERSON>", "sdr<PERSON><PERSON><PERSON>", "gainMapBuffer", "gainMapBlob", "sdrBlob", "sdrImage", "gainMapImage", "needsFlip", "createImageBitmap", "res", "all", "imageOrientation", "ImageData", "flipY", "pow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "load", "sdrUrl", "gainMapUrl", "metadataUrl", "onLoad", "onProgress", "onError", "loadCheck", "error", "itemError", "itemEnd", "sdrLengthComputable", "sdrTotal", "sdrLoaded", "gainMapLengthComputable", "gainMapTotal", "gainMapLoaded", "metadataLengthComputable", "metadataTotal", "metadataLoaded", "progressHandler", "total", "loaded", "lengthComputable", "ProgressEvent", "itemStart", "s<PERSON><PERSON><PERSON><PERSON>", "setResponseType", "setRequestHeader", "requestHeader", "set<PERSON>ath", "path", "setWithCredentials", "withCredentials", "gainMap<PERSON><PERSON>der", "metadataLoader", "json", "JSON", "parse", "HDRJPGLoader", "url", "loader", "jpeg", "jpegBuffer", "sdrJPEG", "gainMapJPEG", "extractionResult", "JPEGRLoader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@monogrid/gainmap-js/dist/decode.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { Q as QuadRenderer } from './QuadRenderer-DuOPRGA4.js';\nimport { ShaderMaterial, Vector3, NoBlending, SRGBColorSpace, LinearSRGBColorSpace, HalfFloatType, Loader, LoadingManager, Texture, UVMapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, FileLoader } from 'three';\n\nconst vertexShader = /* glsl */ `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */ `\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends ShaderMaterial {\n    /**\n     *\n     * @param params\n     */\n    constructor({ gamma, offsetHdr, offsetSdr, gainMapMin, gainMapMax, maxDisplayBoost, hdrCapacityMin, hdrCapacityMax, sdr, gainMap }) {\n        super({\n            name: 'GainMapDecoderMaterial',\n            vertexShader,\n            fragmentShader,\n            uniforms: {\n                sdr: { value: sdr },\n                gainMap: { value: gainMap },\n                gamma: { value: new Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2]) },\n                offsetHdr: { value: new Vector3().fromArray(offsetHdr) },\n                offsetSdr: { value: new Vector3().fromArray(offsetSdr) },\n                gainMapMin: { value: new Vector3().fromArray(gainMapMin) },\n                gainMapMax: { value: new Vector3().fromArray(gainMapMax) },\n                weightFactor: {\n                    value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n                }\n            },\n            blending: NoBlending,\n            depthTest: false,\n            depthWrite: false\n        });\n        this._maxDisplayBoost = maxDisplayBoost;\n        this._hdrCapacityMin = hdrCapacityMin;\n        this._hdrCapacityMax = hdrCapacityMax;\n        this.needsUpdate = true;\n        this.uniformsNeedUpdate = true;\n    }\n    get sdr() { return this.uniforms.sdr.value; }\n    set sdr(value) { this.uniforms.sdr.value = value; }\n    get gainMap() { return this.uniforms.gainMap.value; }\n    set gainMap(value) { this.uniforms.gainMap.value = value; }\n    /**\n     * @see {@link GainMapMetadata.offsetHdr}\n     */\n    get offsetHdr() { return this.uniforms.offsetHdr.value.toArray(); }\n    set offsetHdr(value) { this.uniforms.offsetHdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.offsetSdr}\n     */\n    get offsetSdr() { return this.uniforms.offsetSdr.value.toArray(); }\n    set offsetSdr(value) { this.uniforms.offsetSdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMin}\n     */\n    get gainMapMin() { return this.uniforms.gainMapMin.value.toArray(); }\n    set gainMapMin(value) { this.uniforms.gainMapMin.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMax}\n     */\n    get gainMapMax() { return this.uniforms.gainMapMax.value.toArray(); }\n    set gainMapMax(value) { this.uniforms.gainMapMax.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gamma}\n     */\n    get gamma() {\n        const g = this.uniforms.gamma.value;\n        return [1 / g.x, 1 / g.y, 1 / g.z];\n    }\n    set gamma(value) {\n        const g = this.uniforms.gamma.value;\n        g.x = 1.0 / value[0];\n        g.y = 1.0 / value[1];\n        g.z = 1.0 / value[2];\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMin() { return this._hdrCapacityMin; }\n    set hdrCapacityMin(value) {\n        this._hdrCapacityMin = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMax() { return this._hdrCapacityMax; }\n    set hdrCapacityMax(value) {\n        this._hdrCapacityMax = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n     * @remarks Non Logarithmic space\n     */\n    get maxDisplayBoost() { return this._maxDisplayBoost; }\n    set maxDisplayBoost(value) {\n        this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n        this.calculateWeight();\n    }\n    calculateWeight() {\n        const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n        this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n    }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = (params) => {\n    const { sdr, gainMap, renderer } = params;\n    if (sdr.colorSpace !== SRGBColorSpace) {\n        console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n        sdr.colorSpace = SRGBColorSpace;\n    }\n    sdr.needsUpdate = true;\n    if (gainMap.colorSpace !== LinearSRGBColorSpace) {\n        console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n        gainMap.colorSpace = LinearSRGBColorSpace;\n    }\n    gainMap.needsUpdate = true;\n    const material = new GainMapDecoderMaterial({\n        ...params,\n        sdr,\n        gainMap\n    });\n    const quadRenderer = new QuadRenderer({\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        width: sdr.image.width,\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        height: sdr.image.height,\n        type: HalfFloatType,\n        colorSpace: LinearSRGBColorSpace,\n        material,\n        renderer,\n        renderTargetOptions: params.renderTargetOptions\n    });\n    try {\n        quadRenderer.render();\n    }\n    catch (e) {\n        quadRenderer.disposeOnDemandRenderer();\n        throw e;\n    }\n    return quadRenderer;\n};\n\nclass GainMapNotFoundError extends Error {\n}\n\nclass XMPMetadataNotFoundError extends Error {\n}\n\nconst getXMLValue = (xml, tag, defaultValue) => {\n    // Check for attribute format first: tag=\"value\"\n    const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n    if (attributeMatch)\n        return attributeMatch[1];\n    // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n    const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n    if (tagMatch) {\n        // Check if it contains rdf:li elements\n        const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n        if (liValues && liValues.length === 3) {\n            return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n        }\n        return tagMatch[1].trim();\n    }\n    if (defaultValue !== undefined)\n        return defaultValue;\n    throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = (input) => {\n    let str;\n    // support node test environment\n    if (typeof TextDecoder !== 'undefined')\n        str = new TextDecoder().decode(input);\n    else\n        str = input.toString();\n    let start = str.indexOf('<x:xmpmeta');\n    while (start !== -1) {\n        const end = str.indexOf('x:xmpmeta>', start);\n        const xmpBlock = str.slice(start, end + 10);\n        try {\n            const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n            const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n            const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n            const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n            const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n            // These are always attributes, so we can use a simpler regex\n            const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n            const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n            const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n            if (!hdrCapacityMaxMatch)\n                throw new Error('Incomplete gainmap metadata');\n            const hdrCapacityMax = hdrCapacityMaxMatch[1];\n            return {\n                gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n                gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n                gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n                offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n                offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n                hdrCapacityMin: parseFloat(hdrCapacityMin),\n                hdrCapacityMax: parseFloat(hdrCapacityMax)\n            };\n        }\n        catch (e) {\n            // Continue searching for another xmpmeta block if this one fails\n        }\n        start = str.indexOf('<x:xmpmeta', end);\n    }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n    constructor(options) {\n        this.options = {\n            debug: options && options.debug !== undefined ? options.debug : false,\n            extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n            extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n        };\n    }\n    extract(imageArrayBuffer) {\n        return new Promise((resolve, reject) => {\n            const debug = this.options.debug;\n            const dataView = new DataView(imageArrayBuffer.buffer);\n            // If you're executing this line on a big endian machine, it'll be reversed.\n            // bigEnd further down though, refers to the endianness of the image itself.\n            if (dataView.getUint16(0) !== 0xffd8) {\n                reject(new Error('Not a valid jpeg'));\n                return;\n            }\n            const length = dataView.byteLength;\n            let offset = 2;\n            let loops = 0;\n            let marker; // APP# marker\n            while (offset < length) {\n                if (++loops > 250) {\n                    reject(new Error(`Found no marker after ${loops} loops 😵`));\n                    return;\n                }\n                if (dataView.getUint8(offset) !== 0xff) {\n                    reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n                    return;\n                }\n                marker = dataView.getUint8(offset + 1);\n                if (debug)\n                    console.log(`Marker: ${marker.toString(16)}`);\n                if (marker === 0xe2) {\n                    if (debug)\n                        console.log('Found APP2 marker (0xffe2)');\n                    // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n                    // Great way to visualize image information in html is using Exiftool. E.g.:\n                    // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n                    const formatPt = offset + 4;\n                    /*\n                     *  Structure of the MP Format Identifier\n                     *\n                     *  Offset Addr.  | Code (Hex)  | Description\n                     *  +00             ff            Marker Prefix      <-- offset\n                     *  +01             e2            APP2\n                     *  +02             #n            APP2 Field Length\n                     *  +03             #n            APP2 Field Length\n                     *  +04             4d            'M'                <-- formatPt\n                     *  +05             50            'P'\n                     *  +06             46            'F'\n                     *  +07             00            NULL\n                     *                                                   <-- tiffOffset\n                     */\n                    if (dataView.getUint32(formatPt) === 0x4d504600) {\n                        // Found MPF tag, so we start dig out sub images\n                        const tiffOffset = formatPt + 4;\n                        let bigEnd; // Endianness from TIFF header\n                        // Test for TIFF validity and endianness\n                        // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n                        if (dataView.getUint16(tiffOffset) === 0x4949) {\n                            bigEnd = false;\n                        }\n                        else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n                            bigEnd = true;\n                        }\n                        else {\n                            reject(new Error('No valid endianness marker found in TIFF header'));\n                            return;\n                        }\n                        if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n                            reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n                            return;\n                        }\n                        // 32 bit number stating the offset from the start of the 8 Byte MP Header\n                        // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n                        const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n                        if (firstIFDOffset < 0x00000008) {\n                            reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n                            return;\n                        }\n                        // Move ahead to MP Index IFD\n                        // Assume we're at the first IFD, so firstIFDOffset points to\n                        // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n                        // we fail silently here due to this assumption)\n                        // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n                        const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n                        const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n                        // Extract info from MPEntries (starting after Count)\n                        const entriesStart = dirStart + 2;\n                        let numberOfImages = 0;\n                        for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n                            // Each entry is 12 Bytes long\n                            // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n                            if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                                // stored in Last 4 bytes of its 12 Byte entry.\n                                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n                            }\n                        }\n                        const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n                        const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n                        const images = [];\n                        for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n                            const image = {\n                                MPType: dataView.getUint32(i, !bigEnd),\n                                size: dataView.getUint32(i + 4, !bigEnd),\n                                // This offset is specified relative to the address of the MP Endian\n                                // field in the MP Header, unless the image is a First Individual Image,\n                                // in which case the value of the offset shall be NULL (0x00000000).\n                                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                                start: -1,\n                                end: -1,\n                                isFII: false\n                            };\n                            if (!image.dataOffset) {\n                                // dataOffset is 0x00000000 for First Individual Image\n                                image.start = 0;\n                                image.isFII = true;\n                            }\n                            else {\n                                image.start = tiffOffset + image.dataOffset;\n                                image.isFII = false;\n                            }\n                            image.end = image.start + image.size;\n                            images.push(image);\n                        }\n                        if (this.options.extractNonFII && images.length) {\n                            const bufferBlob = new Blob([dataView]);\n                            const imgs = [];\n                            for (const image of images) {\n                                if (image.isFII && !this.options.extractFII) {\n                                    continue; // Skip FII\n                                }\n                                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                                // we don't need this\n                                // const imageUrl = URL.createObjectURL(imageBlob)\n                                // image.img = document.createElement('img')\n                                // image.img.src = imageUrl\n                                imgs.push(imageBlob);\n                            }\n                            resolve(imgs);\n                        }\n                    }\n                }\n                offset += 2 + dataView.getUint16(offset + 2);\n            }\n        });\n    }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async (jpegFile) => {\n    const metadata = extractXMP(jpegFile);\n    if (!metadata)\n        throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n    const mpfExtractor = new MPFExtractor({ extractFII: true, extractNonFII: true });\n    const images = await mpfExtractor.extract(jpegFile);\n    if (images.length !== 2)\n        throw new GainMapNotFoundError('Gain map recovery image not found');\n    return {\n        sdr: new Uint8Array(await images[0].arrayBuffer()),\n        gainMap: new Uint8Array(await images[1].arrayBuffer()),\n        metadata\n    };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = (blob) => {\n    return new Promise((resolve, reject) => {\n        const img = document.createElement('img');\n        img.onload = () => { resolve(img); };\n        img.onerror = (e) => { reject(e); };\n        img.src = URL.createObjectURL(blob);\n    });\n};\n\nclass LoaderBase extends Loader {\n    /**\n     *\n     * @param renderer\n     * @param manager\n     */\n    constructor(renderer, manager) {\n        super(manager);\n        if (renderer)\n            this._renderer = renderer;\n        this._internalLoadingManager = new LoadingManager();\n    }\n    /**\n     * Specify the renderer to use when rendering the gain map\n     *\n     * @param renderer\n     * @returns\n     */\n    setRenderer(renderer) {\n        this._renderer = renderer;\n        return this;\n    }\n    /**\n     * Specify the renderTarget options to use when rendering the gain map\n     *\n     * @param options\n     * @returns\n     */\n    setRenderTargetOptions(options) {\n        this._renderTargetOptions = options;\n        return this;\n    }\n    /**\n     * @private\n     * @returns\n     */\n    prepareQuadRenderer() {\n        if (!this._renderer)\n            console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n        // temporary values\n        const material = new GainMapDecoderMaterial({\n            gainMapMax: [1, 1, 1],\n            gainMapMin: [0, 0, 0],\n            gamma: [1, 1, 1],\n            offsetHdr: [1, 1, 1],\n            offsetSdr: [1, 1, 1],\n            hdrCapacityMax: 1,\n            hdrCapacityMin: 0,\n            maxDisplayBoost: 1,\n            gainMap: new Texture(),\n            sdr: new Texture()\n        });\n        return new QuadRenderer({\n            width: 16,\n            height: 16,\n            type: HalfFloatType,\n            colorSpace: LinearSRGBColorSpace,\n            material,\n            renderer: this._renderer,\n            renderTargetOptions: this._renderTargetOptions\n        });\n    }\n    /**\n   * @private\n   * @param quadRenderer\n   * @param metadata\n   * @param sdrBuffer\n   * @param gainMapBuffer\n   */\n    async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n        // this is optional, will render a black gain-map if not present\n        const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], { type: 'image/jpeg' }) : undefined;\n        const sdrBlob = new Blob([sdrBuffer], { type: 'image/jpeg' });\n        let sdrImage;\n        let gainMapImage;\n        let needsFlip = false;\n        if (typeof createImageBitmap === 'undefined') {\n            const res = await Promise.all([\n                gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined),\n                getHTMLImageFromBlob(sdrBlob)\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n            needsFlip = true;\n        }\n        else {\n            const res = await Promise.all([\n                gainMapBlob ? createImageBitmap(gainMapBlob, { imageOrientation: 'flipY' }) : Promise.resolve(undefined),\n                createImageBitmap(sdrBlob, { imageOrientation: 'flipY' })\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n        }\n        const gainMap = new Texture(gainMapImage || new ImageData(2, 2), UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, LinearSRGBColorSpace);\n        gainMap.flipY = needsFlip;\n        gainMap.needsUpdate = true;\n        const sdr = new Texture(sdrImage, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, SRGBColorSpace);\n        sdr.flipY = needsFlip;\n        sdr.needsUpdate = true;\n        quadRenderer.width = sdrImage.width;\n        quadRenderer.height = sdrImage.height;\n        quadRenderer.material.gainMap = gainMap;\n        quadRenderer.material.sdr = sdr;\n        quadRenderer.material.gainMapMin = metadata.gainMapMin;\n        quadRenderer.material.gainMapMax = metadata.gainMapMax;\n        quadRenderer.material.offsetHdr = metadata.offsetHdr;\n        quadRenderer.material.offsetSdr = metadata.offsetSdr;\n        quadRenderer.material.gamma = metadata.gamma;\n        quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n        quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n        quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n        quadRenderer.material.needsUpdate = true;\n        quadRenderer.render();\n    }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n    /**\n     * Loads a gainmap using separate data\n     * * sdr image\n     * * gain map image\n     * * metadata json\n     *\n     * useful for webp gain maps\n     *\n     * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        let sdr;\n        let gainMap;\n        let metadata;\n        const loadCheck = async () => {\n            if (sdr && gainMap && metadata) {\n                // solves #16\n                try {\n                    await this.render(quadRenderer, metadata, sdr, gainMap);\n                }\n                catch (error) {\n                    this.manager.itemError(sdrUrl);\n                    this.manager.itemError(gainMapUrl);\n                    this.manager.itemError(metadataUrl);\n                    if (typeof onError === 'function')\n                        onError(error);\n                    quadRenderer.disposeOnDemandRenderer();\n                    return;\n                }\n                if (typeof onLoad === 'function')\n                    onLoad(quadRenderer);\n                this.manager.itemEnd(sdrUrl);\n                this.manager.itemEnd(gainMapUrl);\n                this.manager.itemEnd(metadataUrl);\n                quadRenderer.disposeOnDemandRenderer();\n            }\n        };\n        let sdrLengthComputable = true;\n        let sdrTotal = 0;\n        let sdrLoaded = 0;\n        let gainMapLengthComputable = true;\n        let gainMapTotal = 0;\n        let gainMapLoaded = 0;\n        let metadataLengthComputable = true;\n        let metadataTotal = 0;\n        let metadataLoaded = 0;\n        const progressHandler = () => {\n            if (typeof onProgress === 'function') {\n                const total = sdrTotal + gainMapTotal + metadataTotal;\n                const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n                const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n                onProgress(new ProgressEvent('progress', { lengthComputable, loaded, total }));\n            }\n        };\n        this.manager.itemStart(sdrUrl);\n        this.manager.itemStart(gainMapUrl);\n        this.manager.itemStart(metadataUrl);\n        const sdrLoader = new FileLoader(this._internalLoadingManager);\n        sdrLoader.setResponseType('arraybuffer');\n        sdrLoader.setRequestHeader(this.requestHeader);\n        sdrLoader.setPath(this.path);\n        sdrLoader.setWithCredentials(this.withCredentials);\n        sdrLoader.load(sdrUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid sdr buffer');\n            sdr = buffer;\n            await loadCheck();\n        }, (e) => {\n            sdrLengthComputable = e.lengthComputable;\n            sdrLoaded = e.loaded;\n            sdrTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(sdrUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const gainMapLoader = new FileLoader(this._internalLoadingManager);\n        gainMapLoader.setResponseType('arraybuffer');\n        gainMapLoader.setRequestHeader(this.requestHeader);\n        gainMapLoader.setPath(this.path);\n        gainMapLoader.setWithCredentials(this.withCredentials);\n        gainMapLoader.load(gainMapUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid gainmap buffer');\n            gainMap = buffer;\n            await loadCheck();\n        }, (e) => {\n            gainMapLengthComputable = e.lengthComputable;\n            gainMapLoaded = e.loaded;\n            gainMapTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(gainMapUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const metadataLoader = new FileLoader(this._internalLoadingManager);\n        // metadataLoader.setResponseType('json')\n        metadataLoader.setRequestHeader(this.requestHeader);\n        metadataLoader.setPath(this.path);\n        metadataLoader.setWithCredentials(this.withCredentials);\n        metadataLoader.load(metadataUrl, async (json) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof json !== 'string')\n                throw new Error('Invalid metadata string');\n            // TODO: implement check on JSON file and remove this eslint disable\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            metadata = JSON.parse(json);\n            await loadCheck();\n        }, (e) => {\n            metadataLengthComputable = e.lengthComputable;\n            metadataLoaded = e.loaded;\n            metadataTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(metadataUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n    /**\n     * Loads a JPEG containing gain map metadata\n     * Renders a normal SDR image if gainmap data is not found\n     *\n     * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load(url, onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        const loader = new FileLoader(this._internalLoadingManager);\n        loader.setResponseType('arraybuffer');\n        loader.setRequestHeader(this.requestHeader);\n        loader.setPath(this.path);\n        loader.setWithCredentials(this.withCredentials);\n        this.manager.itemStart(url);\n        loader.load(url, async (jpeg) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof jpeg === 'string')\n                throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n            const jpegBuffer = new Uint8Array(jpeg);\n            let sdrJPEG;\n            let gainMapJPEG;\n            let metadata;\n            try {\n                const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n                // gain map is successfully reconstructed\n                sdrJPEG = extractionResult.sdr;\n                gainMapJPEG = extractionResult.gainMap;\n                metadata = extractionResult.metadata;\n            }\n            catch (e) {\n                // render the SDR version if this is not a gainmap\n                if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n                    console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n                    metadata = {\n                        gainMapMin: [0, 0, 0],\n                        gainMapMax: [1, 1, 1],\n                        gamma: [1, 1, 1],\n                        hdrCapacityMin: 0,\n                        hdrCapacityMax: 1,\n                        offsetHdr: [0, 0, 0],\n                        offsetSdr: [0, 0, 0]\n                    };\n                    sdrJPEG = jpegBuffer;\n                }\n                else {\n                    throw e;\n                }\n            }\n            // solves #16\n            try {\n                await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n            }\n            catch (error) {\n                this.manager.itemError(url);\n                if (typeof onError === 'function')\n                    onError(error);\n                quadRenderer.disposeOnDemandRenderer();\n                return;\n            }\n            if (typeof onLoad === 'function')\n                onLoad(quadRenderer);\n            this.manager.itemEnd(url);\n            quadRenderer.disposeOnDemandRenderer();\n        }, onProgress, (error) => {\n            this.manager.itemError(url);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\nexport { GainMapDecoderMaterial, GainMapLoader, HDRJPGLoader, HDRJPGLoader as JPEGRLoader, MPFExtractor, QuadRenderer, decode, extractGainmapFromJPEG, extractXMP };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,MAAM,EAAEC,cAAc,EAAEC,OAAO,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,OAAO;AAEnQ,MAAMC,YAAY,GAAG,UAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,cAAc,GAAG,UAAW;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,SAASlB,cAAc,CAAC;EAChD;AACJ;AACA;AACA;EACImB,WAAWA,CAAC;IAAEC,KAAK;IAAEC,SAAS;IAAEC,SAAS;IAAEC,UAAU;IAAEC,UAAU;IAAEC,eAAe;IAAEC,cAAc;IAAEC,cAAc;IAAEC,GAAG;IAAEC;EAAQ,CAAC,EAAE;IAChI,KAAK,CAAC;MACFC,IAAI,EAAE,wBAAwB;MAC9Bd,YAAY;MACZC,cAAc;MACdc,QAAQ,EAAE;QACNH,GAAG,EAAE;UAAEI,KAAK,EAAEJ;QAAI,CAAC;QACnBC,OAAO,EAAE;UAAEG,KAAK,EAAEH;QAAQ,CAAC;QAC3BT,KAAK,EAAE;UAAEY,KAAK,EAAE,IAAI/B,OAAO,CAAC,GAAG,GAAGmB,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC;QAAE,CAAC;QAC7EC,SAAS,EAAE;UAAEW,KAAK,EAAE,IAAI/B,OAAO,CAAC,CAAC,CAACgC,SAAS,CAACZ,SAAS;QAAE,CAAC;QACxDC,SAAS,EAAE;UAAEU,KAAK,EAAE,IAAI/B,OAAO,CAAC,CAAC,CAACgC,SAAS,CAACX,SAAS;QAAE,CAAC;QACxDC,UAAU,EAAE;UAAES,KAAK,EAAE,IAAI/B,OAAO,CAAC,CAAC,CAACgC,SAAS,CAACV,UAAU;QAAE,CAAC;QAC1DC,UAAU,EAAE;UAAEQ,KAAK,EAAE,IAAI/B,OAAO,CAAC,CAAC,CAACgC,SAAS,CAACT,UAAU;QAAE,CAAC;QAC1DU,YAAY,EAAE;UACVF,KAAK,EAAE,CAACG,IAAI,CAACC,IAAI,CAACX,eAAe,CAAC,GAAGC,cAAc,KAAKC,cAAc,GAAGD,cAAc;QAC3F;MACJ,CAAC;MACDW,QAAQ,EAAEnC,UAAU;MACpBoC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE;IAChB,CAAC,CAAC;IACF,IAAI,CAACC,gBAAgB,GAAGf,eAAe;IACvC,IAAI,CAACgB,eAAe,GAAGf,cAAc;IACrC,IAAI,CAACgB,eAAe,GAAGf,cAAc;IACrC,IAAI,CAACgB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,kBAAkB,GAAG,IAAI;EAClC;EACA,IAAIhB,GAAGA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACG,QAAQ,CAACH,GAAG,CAACI,KAAK;EAAE;EAC5C,IAAIJ,GAAGA,CAACI,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACH,GAAG,CAACI,KAAK,GAAGA,KAAK;EAAE;EAClD,IAAIH,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACE,QAAQ,CAACF,OAAO,CAACG,KAAK;EAAE;EACpD,IAAIH,OAAOA,CAACG,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACF,OAAO,CAACG,KAAK,GAAGA,KAAK;EAAE;EAC1D;AACJ;AACA;EACI,IAAIX,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACU,QAAQ,CAACV,SAAS,CAACW,KAAK,CAACa,OAAO,CAAC,CAAC;EAAE;EAClE,IAAIxB,SAASA,CAACW,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACV,SAAS,CAACW,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;EAAE;EACvE;AACJ;AACA;EACI,IAAIV,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACS,QAAQ,CAACT,SAAS,CAACU,KAAK,CAACa,OAAO,CAAC,CAAC;EAAE;EAClE,IAAIvB,SAASA,CAACU,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACT,SAAS,CAACU,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;EAAE;EACvE;AACJ;AACA;EACI,IAAIT,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACQ,QAAQ,CAACR,UAAU,CAACS,KAAK,CAACa,OAAO,CAAC,CAAC;EAAE;EACpE,IAAItB,UAAUA,CAACS,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACR,UAAU,CAACS,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;EAAE;EACzE;AACJ;AACA;EACI,IAAIR,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACO,QAAQ,CAACP,UAAU,CAACQ,KAAK,CAACa,OAAO,CAAC,CAAC;EAAE;EACpE,IAAIrB,UAAUA,CAACQ,KAAK,EAAE;IAAE,IAAI,CAACD,QAAQ,CAACP,UAAU,CAACQ,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;EAAE;EACzE;AACJ;AACA;EACI,IAAIZ,KAAKA,CAAA,EAAG;IACR,MAAM0B,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACX,KAAK,CAACY,KAAK;IACnC,OAAO,CAAC,CAAC,GAAGc,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGD,CAAC,CAACE,CAAC,EAAE,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC;EACtC;EACA,IAAI7B,KAAKA,CAACY,KAAK,EAAE;IACb,MAAMc,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACX,KAAK,CAACY,KAAK;IACnCc,CAAC,CAACC,CAAC,GAAG,GAAG,GAAGf,KAAK,CAAC,CAAC,CAAC;IACpBc,CAAC,CAACE,CAAC,GAAG,GAAG,GAAGhB,KAAK,CAAC,CAAC,CAAC;IACpBc,CAAC,CAACG,CAAC,GAAG,GAAG,GAAGjB,KAAK,CAAC,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIN,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACe,eAAe;EAAE;EACpD,IAAIf,cAAcA,CAACM,KAAK,EAAE;IACtB,IAAI,CAACS,eAAe,GAAGT,KAAK;IAC5B,IAAI,CAACkB,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIvB,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACe,eAAe;EAAE;EACpD,IAAIf,cAAcA,CAACK,KAAK,EAAE;IACtB,IAAI,CAACU,eAAe,GAAGV,KAAK;IAC5B,IAAI,CAACkB,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIzB,eAAeA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACe,gBAAgB;EAAE;EACtD,IAAIf,eAAeA,CAACO,KAAK,EAAE;IACvB,IAAI,CAACQ,gBAAgB,GAAGL,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,GAAG,CAAC,KAAK,EAAEpB,KAAK,CAAC,CAAC;IAC3D,IAAI,CAACkB,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,MAAMG,GAAG,GAAG,CAAClB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACI,gBAAgB,CAAC,GAAG,IAAI,CAACC,eAAe,KAAK,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,eAAe,CAAC;IACrH,IAAI,CAACV,QAAQ,CAACG,YAAY,CAACF,KAAK,GAAGG,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC;EACpE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAIC,MAAM,IAAK;EACvB,MAAM;IAAE3B,GAAG;IAAEC,OAAO;IAAE2B;EAAS,CAAC,GAAGD,MAAM;EACzC,IAAI3B,GAAG,CAAC6B,UAAU,KAAKtD,cAAc,EAAE;IACnCuD,OAAO,CAACC,IAAI,CAAC,uEAAuE,CAAC;IACrF/B,GAAG,CAAC6B,UAAU,GAAGtD,cAAc;EACnC;EACAyB,GAAG,CAACe,WAAW,GAAG,IAAI;EACtB,IAAId,OAAO,CAAC4B,UAAU,KAAKrD,oBAAoB,EAAE;IAC7CsD,OAAO,CAACC,IAAI,CAAC,iFAAiF,CAAC;IAC/F9B,OAAO,CAAC4B,UAAU,GAAGrD,oBAAoB;EAC7C;EACAyB,OAAO,CAACc,WAAW,GAAG,IAAI;EAC1B,MAAMiB,QAAQ,GAAG,IAAI1C,sBAAsB,CAAC;IACxC,GAAGqC,MAAM;IACT3B,GAAG;IACHC;EACJ,CAAC,CAAC;EACF,MAAMgC,YAAY,GAAG,IAAI9D,YAAY,CAAC;IAClC;IACA;IACA+D,KAAK,EAAElC,GAAG,CAACmC,KAAK,CAACD,KAAK;IACtB;IACA;IACAE,MAAM,EAAEpC,GAAG,CAACmC,KAAK,CAACC,MAAM;IACxBC,IAAI,EAAE5D,aAAa;IACnBoD,UAAU,EAAErD,oBAAoB;IAChCwD,QAAQ;IACRJ,QAAQ;IACRU,mBAAmB,EAAEX,MAAM,CAACW;EAChC,CAAC,CAAC;EACF,IAAI;IACAL,YAAY,CAACM,MAAM,CAAC,CAAC;EACzB,CAAC,CACD,OAAOC,CAAC,EAAE;IACNP,YAAY,CAACQ,uBAAuB,CAAC,CAAC;IACtC,MAAMD,CAAC;EACX;EACA,OAAOP,YAAY;AACvB,CAAC;AAED,MAAMS,oBAAoB,SAASC,KAAK,CAAC;AAGzC,MAAMC,wBAAwB,SAASD,KAAK,CAAC;AAG7C,MAAME,WAAW,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,YAAY,KAAK;EAC5C;EACA,MAAMC,cAAc,GAAG,IAAIC,MAAM,CAAC,GAAGH,GAAG,YAAY,EAAE,GAAG,CAAC,CAACI,IAAI,CAACL,GAAG,CAAC;EACpE,IAAIG,cAAc,EACd,OAAOA,cAAc,CAAC,CAAC,CAAC;EAC5B;EACA,MAAMG,QAAQ,GAAG,IAAIF,MAAM,CAAC,IAAIH,GAAG,uBAAuBA,GAAG,GAAG,EAAE,GAAG,CAAC,CAACI,IAAI,CAACL,GAAG,CAAC;EAChF,IAAIM,QAAQ,EAAE;IACV;IACA,MAAMC,QAAQ,GAAGD,QAAQ,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,4BAA4B,CAAC;IAChE,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;MACnC,OAAOF,QAAQ,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3D;IACA,OAAON,QAAQ,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;EAC7B;EACA,IAAIX,YAAY,KAAKY,SAAS,EAC1B,OAAOZ,YAAY;EACvB,MAAM,IAAIL,KAAK,CAAC,cAAcI,GAAG,sBAAsB,CAAC;AAC5D,CAAC;AACD,MAAMc,UAAU,GAAIC,KAAK,IAAK;EAC1B,IAAIC,GAAG;EACP;EACA,IAAI,OAAOC,WAAW,KAAK,WAAW,EAClCD,GAAG,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACtC,MAAM,CAACoC,KAAK,CAAC,CAAC,KAEtCC,GAAG,GAAGD,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC1B,IAAIC,KAAK,GAAGH,GAAG,CAACI,OAAO,CAAC,YAAY,CAAC;EACrC,OAAOD,KAAK,KAAK,CAAC,CAAC,EAAE;IACjB,MAAME,GAAG,GAAGL,GAAG,CAACI,OAAO,CAAC,YAAY,EAAED,KAAK,CAAC;IAC5C,MAAMG,QAAQ,GAAGN,GAAG,CAACO,KAAK,CAACJ,KAAK,EAAEE,GAAG,GAAG,EAAE,CAAC;IAC3C,IAAI;MACA,MAAMzE,UAAU,GAAGkD,WAAW,CAACwB,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAC;MACjE,MAAMzE,UAAU,GAAGiD,WAAW,CAACwB,QAAQ,EAAE,kBAAkB,CAAC;MAC5D,MAAM7E,KAAK,GAAGqD,WAAW,CAACwB,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAC;MACvD,MAAME,SAAS,GAAG1B,WAAW,CAACwB,QAAQ,EAAE,iBAAiB,EAAE,UAAU,CAAC;MACtE,MAAMG,SAAS,GAAG3B,WAAW,CAACwB,QAAQ,EAAE,iBAAiB,EAAE,UAAU,CAAC;MACtE;MACA,MAAMI,mBAAmB,GAAG,gCAAgC,CAACtB,IAAI,CAACkB,QAAQ,CAAC;MAC3E,MAAMvE,cAAc,GAAG2E,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC,CAAC,GAAG,GAAG;MACzE,MAAMC,mBAAmB,GAAG,gCAAgC,CAACvB,IAAI,CAACkB,QAAQ,CAAC;MAC3E,IAAI,CAACK,mBAAmB,EACpB,MAAM,IAAI/B,KAAK,CAAC,6BAA6B,CAAC;MAClD,MAAM5C,cAAc,GAAG2E,mBAAmB,CAAC,CAAC,CAAC;MAC7C,OAAO;QACH/E,UAAU,EAAEgF,KAAK,CAACC,OAAO,CAACjF,UAAU,CAAC,GAAGA,UAAU,CAAC6D,GAAG,CAACC,CAAC,IAAIoB,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,CAACoB,UAAU,CAAClF,UAAU,CAAC,EAAEkF,UAAU,CAAClF,UAAU,CAAC,EAAEkF,UAAU,CAAClF,UAAU,CAAC,CAAC;QACrJC,UAAU,EAAE+E,KAAK,CAACC,OAAO,CAAChF,UAAU,CAAC,GAAGA,UAAU,CAAC4D,GAAG,CAACC,CAAC,IAAIoB,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,CAACoB,UAAU,CAACjF,UAAU,CAAC,EAAEiF,UAAU,CAACjF,UAAU,CAAC,EAAEiF,UAAU,CAACjF,UAAU,CAAC,CAAC;QACrJJ,KAAK,EAAEmF,KAAK,CAACC,OAAO,CAACpF,KAAK,CAAC,GAAGA,KAAK,CAACgE,GAAG,CAACC,CAAC,IAAIoB,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,CAACoB,UAAU,CAACrF,KAAK,CAAC,EAAEqF,UAAU,CAACrF,KAAK,CAAC,EAAEqF,UAAU,CAACrF,KAAK,CAAC,CAAC;QACvHE,SAAS,EAAEiF,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,GAAGA,SAAS,CAACf,GAAG,CAACC,CAAC,IAAIoB,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,CAACoB,UAAU,CAACN,SAAS,CAAC,EAAEM,UAAU,CAACN,SAAS,CAAC,EAAEM,UAAU,CAACN,SAAS,CAAC,CAAC;QAC/I9E,SAAS,EAAEkF,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,GAAGA,SAAS,CAAChB,GAAG,CAACC,CAAC,IAAIoB,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,CAACoB,UAAU,CAACL,SAAS,CAAC,EAAEK,UAAU,CAACL,SAAS,CAAC,EAAEK,UAAU,CAACL,SAAS,CAAC,CAAC;QAC/I1E,cAAc,EAAE+E,UAAU,CAAC/E,cAAc,CAAC;QAC1CC,cAAc,EAAE8E,UAAU,CAAC9E,cAAc;MAC7C,CAAC;IACL,CAAC,CACD,OAAOyC,CAAC,EAAE;MACN;IAAA;IAEJ0B,KAAK,GAAGH,GAAG,CAACI,OAAO,CAAC,YAAY,EAAEC,GAAG,CAAC;EAC1C;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,YAAY,CAAC;EACfvF,WAAWA,CAACwF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAG;MACXC,KAAK,EAAED,OAAO,IAAIA,OAAO,CAACC,KAAK,KAAKpB,SAAS,GAAGmB,OAAO,CAACC,KAAK,GAAG,KAAK;MACrEC,UAAU,EAAEF,OAAO,IAAIA,OAAO,CAACE,UAAU,KAAKrB,SAAS,GAAGmB,OAAO,CAACE,UAAU,GAAG,IAAI;MACnFC,aAAa,EAAEH,OAAO,IAAIA,OAAO,CAACG,aAAa,KAAKtB,SAAS,GAAGmB,OAAO,CAACG,aAAa,GAAG;IAC5F,CAAC;EACL;EACAC,OAAOA,CAACC,gBAAgB,EAAE;IACtB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,MAAMP,KAAK,GAAG,IAAI,CAACD,OAAO,CAACC,KAAK;MAChC,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,CAACL,gBAAgB,CAACM,MAAM,CAAC;MACtD;MACA;MACA,IAAIF,QAAQ,CAACG,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QAClCJ,MAAM,CAAC,IAAI5C,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACrC;MACJ;MACA,MAAMY,MAAM,GAAGiC,QAAQ,CAACI,UAAU;MAClC,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIC,MAAM,CAAC,CAAC;MACZ,OAAOF,MAAM,GAAGtC,MAAM,EAAE;QACpB,IAAI,EAAEuC,KAAK,GAAG,GAAG,EAAE;UACfP,MAAM,CAAC,IAAI5C,KAAK,CAAC,yBAAyBmD,KAAK,WAAW,CAAC,CAAC;UAC5D;QACJ;QACA,IAAIN,QAAQ,CAACQ,QAAQ,CAACH,MAAM,CAAC,KAAK,IAAI,EAAE;UACpCN,MAAM,CAAC,IAAI5C,KAAK,CAAC,kCAAkCkD,MAAM,CAAC5B,QAAQ,CAAC,EAAE,CAAC,cAAcuB,QAAQ,CAACQ,QAAQ,CAACH,MAAM,CAAC,CAAC5B,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC9H;QACJ;QACA8B,MAAM,GAAGP,QAAQ,CAACQ,QAAQ,CAACH,MAAM,GAAG,CAAC,CAAC;QACtC,IAAIb,KAAK,EACLlD,OAAO,CAACmE,GAAG,CAAC,WAAWF,MAAM,CAAC9B,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QACjD,IAAI8B,MAAM,KAAK,IAAI,EAAE;UACjB,IAAIf,KAAK,EACLlD,OAAO,CAACmE,GAAG,CAAC,4BAA4B,CAAC;UAC7C;UACA;UACA;UACA,MAAMC,QAAQ,GAAGL,MAAM,GAAG,CAAC;UAC3B;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIL,QAAQ,CAACW,SAAS,CAACD,QAAQ,CAAC,KAAK,UAAU,EAAE;YAC7C;YACA,MAAME,UAAU,GAAGF,QAAQ,GAAG,CAAC;YAC/B,IAAIG,MAAM,CAAC,CAAC;YACZ;YACA;YACA,IAAIb,QAAQ,CAACG,SAAS,CAACS,UAAU,CAAC,KAAK,MAAM,EAAE;cAC3CC,MAAM,GAAG,KAAK;YAClB,CAAC,MACI,IAAIb,QAAQ,CAACG,SAAS,CAACS,UAAU,CAAC,KAAK,MAAM,EAAE;cAChDC,MAAM,GAAG,IAAI;YACjB,CAAC,MACI;cACDd,MAAM,CAAC,IAAI5C,KAAK,CAAC,iDAAiD,CAAC,CAAC;cACpE;YACJ;YACA,IAAI6C,QAAQ,CAACG,SAAS,CAACS,UAAU,GAAG,CAAC,EAAE,CAACC,MAAM,CAAC,KAAK,MAAM,EAAE;cACxDd,MAAM,CAAC,IAAI5C,KAAK,CAAC,yCAAyC,CAAC,CAAC;cAC5D;YACJ;YACA;YACA;YACA,MAAM2D,cAAc,GAAGd,QAAQ,CAACW,SAAS,CAACC,UAAU,GAAG,CAAC,EAAE,CAACC,MAAM,CAAC;YAClE,IAAIC,cAAc,GAAG,UAAU,EAAE;cAC7Bf,MAAM,CAAC,IAAI5C,KAAK,CAAC,iDAAiD,CAAC,CAAC;cACpE;YACJ;YACA;YACA;YACA;YACA;YACA;YACA,MAAM4D,QAAQ,GAAGH,UAAU,GAAGE,cAAc,CAAC,CAAC;YAC9C,MAAME,KAAK,GAAGhB,QAAQ,CAACG,SAAS,CAACY,QAAQ,EAAE,CAACF,MAAM,CAAC,CAAC,CAAC;YACrD;YACA,MAAMI,YAAY,GAAGF,QAAQ,GAAG,CAAC;YACjC,IAAIG,cAAc,GAAG,CAAC;YACtB,KAAK,IAAIC,CAAC,GAAGF,YAAY,EAAEE,CAAC,GAAGF,YAAY,GAAG,EAAE,GAAGD,KAAK,EAAEG,CAAC,IAAI,EAAE,EAAE;cAC/D;cACA;cACA,IAAInB,QAAQ,CAACG,SAAS,CAACgB,CAAC,EAAE,CAACN,MAAM,CAAC,KAAK,MAAM,EAAE;gBAC3C;gBACAK,cAAc,GAAGlB,QAAQ,CAACW,SAAS,CAACQ,CAAC,GAAG,CAAC,EAAE,CAACN,MAAM,CAAC;cACvD;YACJ;YACA,MAAMO,gBAAgB,GAAG,CAAC,CAAC,CAAC;YAC5B,MAAMC,gBAAgB,GAAGN,QAAQ,GAAG,CAAC,GAAGC,KAAK,GAAG,EAAE,GAAGI,gBAAgB;YACrE,MAAME,MAAM,GAAG,EAAE;YACjB,KAAK,IAAIH,CAAC,GAAGE,gBAAgB,EAAEF,CAAC,GAAGE,gBAAgB,GAAGH,cAAc,GAAG,EAAE,EAAEC,CAAC,IAAI,EAAE,EAAE;cAChF,MAAMxE,KAAK,GAAG;gBACV4E,MAAM,EAAEvB,QAAQ,CAACW,SAAS,CAACQ,CAAC,EAAE,CAACN,MAAM,CAAC;gBACtCW,IAAI,EAAExB,QAAQ,CAACW,SAAS,CAACQ,CAAC,GAAG,CAAC,EAAE,CAACN,MAAM,CAAC;gBACxC;gBACA;gBACA;gBACAY,UAAU,EAAEzB,QAAQ,CAACW,SAAS,CAACQ,CAAC,GAAG,CAAC,EAAE,CAACN,MAAM,CAAC;gBAC9Ca,eAAe,EAAE1B,QAAQ,CAACW,SAAS,CAACQ,CAAC,GAAG,EAAE,EAAE,CAACN,MAAM,CAAC;gBACpDnC,KAAK,EAAE,CAAC,CAAC;gBACTE,GAAG,EAAE,CAAC,CAAC;gBACP+C,KAAK,EAAE;cACX,CAAC;cACD,IAAI,CAAChF,KAAK,CAAC8E,UAAU,EAAE;gBACnB;gBACA9E,KAAK,CAAC+B,KAAK,GAAG,CAAC;gBACf/B,KAAK,CAACgF,KAAK,GAAG,IAAI;cACtB,CAAC,MACI;gBACDhF,KAAK,CAAC+B,KAAK,GAAGkC,UAAU,GAAGjE,KAAK,CAAC8E,UAAU;gBAC3C9E,KAAK,CAACgF,KAAK,GAAG,KAAK;cACvB;cACAhF,KAAK,CAACiC,GAAG,GAAGjC,KAAK,CAAC+B,KAAK,GAAG/B,KAAK,CAAC6E,IAAI;cACpCF,MAAM,CAACM,IAAI,CAACjF,KAAK,CAAC;YACtB;YACA,IAAI,IAAI,CAAC4C,OAAO,CAACG,aAAa,IAAI4B,MAAM,CAACvD,MAAM,EAAE;cAC7C,MAAM8D,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC9B,QAAQ,CAAC,CAAC;cACvC,MAAM+B,IAAI,GAAG,EAAE;cACf,KAAK,MAAMpF,KAAK,IAAI2E,MAAM,EAAE;gBACxB,IAAI3E,KAAK,CAACgF,KAAK,IAAI,CAAC,IAAI,CAACpC,OAAO,CAACE,UAAU,EAAE;kBACzC,SAAS,CAAC;gBACd;gBACA,MAAMuC,SAAS,GAAGH,UAAU,CAAC/C,KAAK,CAACnC,KAAK,CAAC+B,KAAK,EAAE/B,KAAK,CAACiC,GAAG,GAAG,CAAC,EAAE,YAAY,CAAC;gBAC5E;gBACA;gBACA;gBACA;gBACAmD,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC;cACxB;cACAlC,OAAO,CAACiC,IAAI,CAAC;YACjB;UACJ;QACJ;QACA1B,MAAM,IAAI,CAAC,GAAGL,QAAQ,CAACG,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;MAChD;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,sBAAsB,GAAG,MAAOC,QAAQ,IAAK;EAC/C,MAAMC,QAAQ,GAAG9D,UAAU,CAAC6D,QAAQ,CAAC;EACrC,IAAI,CAACC,QAAQ,EACT,MAAM,IAAI/E,wBAAwB,CAAC,iCAAiC,CAAC;EACzE,MAAMgF,YAAY,GAAG,IAAI9C,YAAY,CAAC;IAAEG,UAAU,EAAE,IAAI;IAAEC,aAAa,EAAE;EAAK,CAAC,CAAC;EAChF,MAAM4B,MAAM,GAAG,MAAMc,YAAY,CAACzC,OAAO,CAACuC,QAAQ,CAAC;EACnD,IAAIZ,MAAM,CAACvD,MAAM,KAAK,CAAC,EACnB,MAAM,IAAIb,oBAAoB,CAAC,mCAAmC,CAAC;EACvE,OAAO;IACH1C,GAAG,EAAE,IAAI6H,UAAU,CAAC,MAAMf,MAAM,CAAC,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC,CAAC;IAClD7H,OAAO,EAAE,IAAI4H,UAAU,CAAC,MAAMf,MAAM,CAAC,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC,CAAC;IACtDH;EACJ,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAIC,IAAI,IAAK;EACnC,OAAO,IAAI3C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAM0C,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzCF,GAAG,CAACG,MAAM,GAAG,MAAM;MAAE9C,OAAO,CAAC2C,GAAG,CAAC;IAAE,CAAC;IACpCA,GAAG,CAACI,OAAO,GAAI7F,CAAC,IAAK;MAAE+C,MAAM,CAAC/C,CAAC,CAAC;IAAE,CAAC;IACnCyF,GAAG,CAACK,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;EACvC,CAAC,CAAC;AACN,CAAC;AAED,MAAMS,UAAU,SAAS/J,MAAM,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACIa,WAAWA,CAACqC,QAAQ,EAAE8G,OAAO,EAAE;IAC3B,KAAK,CAACA,OAAO,CAAC;IACd,IAAI9G,QAAQ,EACR,IAAI,CAAC+G,SAAS,GAAG/G,QAAQ;IAC7B,IAAI,CAACgH,uBAAuB,GAAG,IAAIjK,cAAc,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkK,WAAWA,CAACjH,QAAQ,EAAE;IAClB,IAAI,CAAC+G,SAAS,GAAG/G,QAAQ;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkH,sBAAsBA,CAAC/D,OAAO,EAAE;IAC5B,IAAI,CAACgE,oBAAoB,GAAGhE,OAAO;IACnC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIiE,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACL,SAAS,EACf7G,OAAO,CAACC,IAAI,CAAC,qOAAqO,CAAC;IACvP;IACA,MAAMC,QAAQ,GAAG,IAAI1C,sBAAsB,CAAC;MACxCM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrBD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrBH,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAChBC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACpBC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACpBK,cAAc,EAAE,CAAC;MACjBD,cAAc,EAAE,CAAC;MACjBD,eAAe,EAAE,CAAC;MAClBI,OAAO,EAAE,IAAIrB,OAAO,CAAC,CAAC;MACtBoB,GAAG,EAAE,IAAIpB,OAAO,CAAC;IACrB,CAAC,CAAC;IACF,OAAO,IAAIT,YAAY,CAAC;MACpB+D,KAAK,EAAE,EAAE;MACTE,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE5D,aAAa;MACnBoD,UAAU,EAAErD,oBAAoB;MAChCwD,QAAQ;MACRJ,QAAQ,EAAE,IAAI,CAAC+G,SAAS;MACxBrG,mBAAmB,EAAE,IAAI,CAACyG;IAC9B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMxG,MAAMA,CAACN,YAAY,EAAE0F,QAAQ,EAAEsB,SAAS,EAAEC,aAAa,EAAE;IAC3D;IACA,MAAMC,WAAW,GAAGD,aAAa,GAAG,IAAI5B,IAAI,CAAC,CAAC4B,aAAa,CAAC,EAAE;MAAE7G,IAAI,EAAE;IAAa,CAAC,CAAC,GAAGuB,SAAS;IACjG,MAAMwF,OAAO,GAAG,IAAI9B,IAAI,CAAC,CAAC2B,SAAS,CAAC,EAAE;MAAE5G,IAAI,EAAE;IAAa,CAAC,CAAC;IAC7D,IAAIgH,QAAQ;IACZ,IAAIC,YAAY;IAChB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAI,OAAOC,iBAAiB,KAAK,WAAW,EAAE;MAC1C,MAAMC,GAAG,GAAG,MAAMpE,OAAO,CAACqE,GAAG,CAAC,CAC1BP,WAAW,GAAGpB,oBAAoB,CAACoB,WAAW,CAAC,GAAG9D,OAAO,CAACC,OAAO,CAAC1B,SAAS,CAAC,EAC5EmE,oBAAoB,CAACqB,OAAO,CAAC,CAChC,CAAC;MACFE,YAAY,GAAGG,GAAG,CAAC,CAAC,CAAC;MACrBJ,QAAQ,GAAGI,GAAG,CAAC,CAAC,CAAC;MACjBF,SAAS,GAAG,IAAI;IACpB,CAAC,MACI;MACD,MAAME,GAAG,GAAG,MAAMpE,OAAO,CAACqE,GAAG,CAAC,CAC1BP,WAAW,GAAGK,iBAAiB,CAACL,WAAW,EAAE;QAAEQ,gBAAgB,EAAE;MAAQ,CAAC,CAAC,GAAGtE,OAAO,CAACC,OAAO,CAAC1B,SAAS,CAAC,EACxG4F,iBAAiB,CAACJ,OAAO,EAAE;QAAEO,gBAAgB,EAAE;MAAQ,CAAC,CAAC,CAC5D,CAAC;MACFL,YAAY,GAAGG,GAAG,CAAC,CAAC,CAAC;MACrBJ,QAAQ,GAAGI,GAAG,CAAC,CAAC,CAAC;IACrB;IACA,MAAMxJ,OAAO,GAAG,IAAIrB,OAAO,CAAC0K,YAAY,IAAI,IAAIM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE/K,SAAS,EAAEC,mBAAmB,EAAEA,mBAAmB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,UAAU,EAAEC,gBAAgB,EAAE,CAAC,EAAEV,oBAAoB,CAAC;IACpNyB,OAAO,CAAC4J,KAAK,GAAGN,SAAS;IACzBtJ,OAAO,CAACc,WAAW,GAAG,IAAI;IAC1B,MAAMf,GAAG,GAAG,IAAIpB,OAAO,CAACyK,QAAQ,EAAExK,SAAS,EAAEC,mBAAmB,EAAEA,mBAAmB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,UAAU,EAAEC,gBAAgB,EAAE,CAAC,EAAEX,cAAc,CAAC;IAC/KyB,GAAG,CAAC6J,KAAK,GAAGN,SAAS;IACrBvJ,GAAG,CAACe,WAAW,GAAG,IAAI;IACtBkB,YAAY,CAACC,KAAK,GAAGmH,QAAQ,CAACnH,KAAK;IACnCD,YAAY,CAACG,MAAM,GAAGiH,QAAQ,CAACjH,MAAM;IACrCH,YAAY,CAACD,QAAQ,CAAC/B,OAAO,GAAGA,OAAO;IACvCgC,YAAY,CAACD,QAAQ,CAAChC,GAAG,GAAGA,GAAG;IAC/BiC,YAAY,CAACD,QAAQ,CAACrC,UAAU,GAAGgI,QAAQ,CAAChI,UAAU;IACtDsC,YAAY,CAACD,QAAQ,CAACpC,UAAU,GAAG+H,QAAQ,CAAC/H,UAAU;IACtDqC,YAAY,CAACD,QAAQ,CAACvC,SAAS,GAAGkI,QAAQ,CAAClI,SAAS;IACpDwC,YAAY,CAACD,QAAQ,CAACtC,SAAS,GAAGiI,QAAQ,CAACjI,SAAS;IACpDuC,YAAY,CAACD,QAAQ,CAACxC,KAAK,GAAGmI,QAAQ,CAACnI,KAAK;IAC5CyC,YAAY,CAACD,QAAQ,CAAClC,cAAc,GAAG6H,QAAQ,CAAC7H,cAAc;IAC9DmC,YAAY,CAACD,QAAQ,CAACjC,cAAc,GAAG4H,QAAQ,CAAC5H,cAAc;IAC9DkC,YAAY,CAACD,QAAQ,CAACnC,eAAe,GAAGU,IAAI,CAACuJ,GAAG,CAAC,CAAC,EAAEnC,QAAQ,CAAC5H,cAAc,CAAC;IAC5EkC,YAAY,CAACD,QAAQ,CAACjB,WAAW,GAAG,IAAI;IACxCkB,YAAY,CAACM,MAAM,CAAC,CAAC;EACzB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwH,aAAa,SAAStB,UAAU,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuB,IAAIA,CAAC,CAACC,MAAM,EAAEC,UAAU,EAAEC,WAAW,CAAC,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACjE,MAAMrI,YAAY,GAAG,IAAI,CAAC+G,mBAAmB,CAAC,CAAC;IAC/C,IAAIhJ,GAAG;IACP,IAAIC,OAAO;IACX,IAAI0H,QAAQ;IACZ,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAIvK,GAAG,IAAIC,OAAO,IAAI0H,QAAQ,EAAE;QAC5B;QACA,IAAI;UACA,MAAM,IAAI,CAACpF,MAAM,CAACN,YAAY,EAAE0F,QAAQ,EAAE3H,GAAG,EAAEC,OAAO,CAAC;QAC3D,CAAC,CACD,OAAOuK,KAAK,EAAE;UACV,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAACR,MAAM,CAAC;UAC9B,IAAI,CAACvB,OAAO,CAAC+B,SAAS,CAACP,UAAU,CAAC;UAClC,IAAI,CAACxB,OAAO,CAAC+B,SAAS,CAACN,WAAW,CAAC;UACnC,IAAI,OAAOG,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;UAClBvI,YAAY,CAACQ,uBAAuB,CAAC,CAAC;UACtC;QACJ;QACA,IAAI,OAAO2H,MAAM,KAAK,UAAU,EAC5BA,MAAM,CAACnI,YAAY,CAAC;QACxB,IAAI,CAACyG,OAAO,CAACgC,OAAO,CAACT,MAAM,CAAC;QAC5B,IAAI,CAACvB,OAAO,CAACgC,OAAO,CAACR,UAAU,CAAC;QAChC,IAAI,CAACxB,OAAO,CAACgC,OAAO,CAACP,WAAW,CAAC;QACjClI,YAAY,CAACQ,uBAAuB,CAAC,CAAC;MAC1C;IACJ,CAAC;IACD,IAAIkI,mBAAmB,GAAG,IAAI;IAC9B,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,uBAAuB,GAAG,IAAI;IAClC,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,wBAAwB,GAAG,IAAI;IACnC,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,cAAc,GAAG,CAAC;IACtB,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAI,OAAOf,UAAU,KAAK,UAAU,EAAE;QAClC,MAAMgB,KAAK,GAAGT,QAAQ,GAAGG,YAAY,GAAGG,aAAa;QACrD,MAAMI,MAAM,GAAGT,SAAS,GAAGG,aAAa,GAAGG,cAAc;QACzD,MAAMI,gBAAgB,GAAGZ,mBAAmB,IAAIG,uBAAuB,IAAIG,wBAAwB;QACnGZ,UAAU,CAAC,IAAImB,aAAa,CAAC,UAAU,EAAE;UAAED,gBAAgB;UAAED,MAAM;UAAED;QAAM,CAAC,CAAC,CAAC;MAClF;IACJ,CAAC;IACD,IAAI,CAAC3C,OAAO,CAAC+C,SAAS,CAACxB,MAAM,CAAC;IAC9B,IAAI,CAACvB,OAAO,CAAC+C,SAAS,CAACvB,UAAU,CAAC;IAClC,IAAI,CAACxB,OAAO,CAAC+C,SAAS,CAACtB,WAAW,CAAC;IACnC,MAAMuB,SAAS,GAAG,IAAIvM,UAAU,CAAC,IAAI,CAACyJ,uBAAuB,CAAC;IAC9D8C,SAAS,CAACC,eAAe,CAAC,aAAa,CAAC;IACxCD,SAAS,CAACE,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAAC;IAC9CH,SAAS,CAACI,OAAO,CAAC,IAAI,CAACC,IAAI,CAAC;IAC5BL,SAAS,CAACM,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAAC;IAClDP,SAAS,CAAC1B,IAAI,CAACC,MAAM,EAAE,MAAOvE,MAAM,IAAK;MACrC;AACZ;AACA;MACY,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAC1B,MAAM,IAAI/C,KAAK,CAAC,oBAAoB,CAAC;MACzC3C,GAAG,GAAG0F,MAAM;MACZ,MAAM6E,SAAS,CAAC,CAAC;IACrB,CAAC,EAAG/H,CAAC,IAAK;MACNmI,mBAAmB,GAAGnI,CAAC,CAAC+I,gBAAgB;MACxCV,SAAS,GAAGrI,CAAC,CAAC8I,MAAM;MACpBV,QAAQ,GAAGpI,CAAC,CAAC6I,KAAK;MAClBD,eAAe,CAAC,CAAC;IACrB,CAAC,EAAGZ,KAAK,IAAK;MACV,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAACR,MAAM,CAAC;MAC9B,IAAI,OAAOK,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;IACtB,CAAC,CAAC;IACF,MAAM0B,aAAa,GAAG,IAAI/M,UAAU,CAAC,IAAI,CAACyJ,uBAAuB,CAAC;IAClEsD,aAAa,CAACP,eAAe,CAAC,aAAa,CAAC;IAC5CO,aAAa,CAACN,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAAC;IAClDK,aAAa,CAACJ,OAAO,CAAC,IAAI,CAACC,IAAI,CAAC;IAChCG,aAAa,CAACF,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAAC;IACtDC,aAAa,CAAClC,IAAI,CAACE,UAAU,EAAE,MAAOxE,MAAM,IAAK;MAC7C;AACZ;AACA;MACY,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAC1B,MAAM,IAAI/C,KAAK,CAAC,wBAAwB,CAAC;MAC7C1C,OAAO,GAAGyF,MAAM;MAChB,MAAM6E,SAAS,CAAC,CAAC;IACrB,CAAC,EAAG/H,CAAC,IAAK;MACNsI,uBAAuB,GAAGtI,CAAC,CAAC+I,gBAAgB;MAC5CP,aAAa,GAAGxI,CAAC,CAAC8I,MAAM;MACxBP,YAAY,GAAGvI,CAAC,CAAC6I,KAAK;MACtBD,eAAe,CAAC,CAAC;IACrB,CAAC,EAAGZ,KAAK,IAAK;MACV,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAACP,UAAU,CAAC;MAClC,IAAI,OAAOI,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;IACtB,CAAC,CAAC;IACF,MAAM2B,cAAc,GAAG,IAAIhN,UAAU,CAAC,IAAI,CAACyJ,uBAAuB,CAAC;IACnE;IACAuD,cAAc,CAACP,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAAC;IACnDM,cAAc,CAACL,OAAO,CAAC,IAAI,CAACC,IAAI,CAAC;IACjCI,cAAc,CAACH,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAAC;IACvDE,cAAc,CAACnC,IAAI,CAACG,WAAW,EAAE,MAAOiC,IAAI,IAAK;MAC7C;AACZ;AACA;MACY,IAAI,OAAOA,IAAI,KAAK,QAAQ,EACxB,MAAM,IAAIzJ,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA;MACAgF,QAAQ,GAAG0E,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;MAC3B,MAAM7B,SAAS,CAAC,CAAC;IACrB,CAAC,EAAG/H,CAAC,IAAK;MACNyI,wBAAwB,GAAGzI,CAAC,CAAC+I,gBAAgB;MAC7CJ,cAAc,GAAG3I,CAAC,CAAC8I,MAAM;MACzBJ,aAAa,GAAG1I,CAAC,CAAC6I,KAAK;MACvBD,eAAe,CAAC,CAAC;IACrB,CAAC,EAAGZ,KAAK,IAAK;MACV,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAACN,WAAW,CAAC;MACnC,IAAI,OAAOG,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;IACtB,CAAC,CAAC;IACF,OAAOvI,YAAY;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsK,YAAY,SAAS9D,UAAU,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuB,IAAIA,CAACwC,GAAG,EAAEpC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACnC,MAAMrI,YAAY,GAAG,IAAI,CAAC+G,mBAAmB,CAAC,CAAC;IAC/C,MAAMyD,MAAM,GAAG,IAAItN,UAAU,CAAC,IAAI,CAACyJ,uBAAuB,CAAC;IAC3D6D,MAAM,CAACd,eAAe,CAAC,aAAa,CAAC;IACrCc,MAAM,CAACb,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAAC;IAC3CY,MAAM,CAACX,OAAO,CAAC,IAAI,CAACC,IAAI,CAAC;IACzBU,MAAM,CAACT,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAAC;IAC/C,IAAI,CAACvD,OAAO,CAAC+C,SAAS,CAACe,GAAG,CAAC;IAC3BC,MAAM,CAACzC,IAAI,CAACwC,GAAG,EAAE,MAAOE,IAAI,IAAK;MAC7B;AACZ;AACA;MACY,IAAI,OAAOA,IAAI,KAAK,QAAQ,EACxB,MAAM,IAAI/J,KAAK,CAAC,gEAAgE,CAAC;MACrF,MAAMgK,UAAU,GAAG,IAAI9E,UAAU,CAAC6E,IAAI,CAAC;MACvC,IAAIE,OAAO;MACX,IAAIC,WAAW;MACf,IAAIlF,QAAQ;MACZ,IAAI;QACA,MAAMmF,gBAAgB,GAAG,MAAMrF,sBAAsB,CAACkF,UAAU,CAAC;QACjE;QACAC,OAAO,GAAGE,gBAAgB,CAAC9M,GAAG;QAC9B6M,WAAW,GAAGC,gBAAgB,CAAC7M,OAAO;QACtC0H,QAAQ,GAAGmF,gBAAgB,CAACnF,QAAQ;MACxC,CAAC,CACD,OAAOnF,CAAC,EAAE;QACN;QACA,IAAIA,CAAC,YAAYI,wBAAwB,IAAIJ,CAAC,YAAYE,oBAAoB,EAAE;UAC5EZ,OAAO,CAACC,IAAI,CAAC,4CAA4CyK,GAAG,kFAAkF,CAAC;UAC/I7E,QAAQ,GAAG;YACPhI,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACrBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACrBJ,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAChBM,cAAc,EAAE,CAAC;YACjBC,cAAc,EAAE,CAAC;YACjBN,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACpBC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UACvB,CAAC;UACDkN,OAAO,GAAGD,UAAU;QACxB,CAAC,MACI;UACD,MAAMnK,CAAC;QACX;MACJ;MACA;MACA,IAAI;QACA,MAAM,IAAI,CAACD,MAAM,CAACN,YAAY,EAAE0F,QAAQ,EAAEiF,OAAO,EAAEC,WAAW,CAAC;MACnE,CAAC,CACD,OAAOrC,KAAK,EAAE;QACV,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAAC+B,GAAG,CAAC;QAC3B,IAAI,OAAOlC,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;QAClBvI,YAAY,CAACQ,uBAAuB,CAAC,CAAC;QACtC;MACJ;MACA,IAAI,OAAO2H,MAAM,KAAK,UAAU,EAC5BA,MAAM,CAACnI,YAAY,CAAC;MACxB,IAAI,CAACyG,OAAO,CAACgC,OAAO,CAAC8B,GAAG,CAAC;MACzBvK,YAAY,CAACQ,uBAAuB,CAAC,CAAC;IAC1C,CAAC,EAAE4H,UAAU,EAAGG,KAAK,IAAK;MACtB,IAAI,CAAC9B,OAAO,CAAC+B,SAAS,CAAC+B,GAAG,CAAC;MAC3B,IAAI,OAAOlC,OAAO,KAAK,UAAU,EAC7BA,OAAO,CAACE,KAAK,CAAC;IACtB,CAAC,CAAC;IACF,OAAOvI,YAAY;EACvB;AACJ;AAEA,SAAS3C,sBAAsB,EAAEyK,aAAa,EAAEwC,YAAY,EAAEA,YAAY,IAAIQ,WAAW,EAAEjI,YAAY,EAAE3G,YAAY,EAAEuD,MAAM,EAAE+F,sBAAsB,EAAE5D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}