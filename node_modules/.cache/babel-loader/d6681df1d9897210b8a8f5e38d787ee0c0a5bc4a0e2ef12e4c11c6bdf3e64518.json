{"ast": null, "code": "import * as React from 'react';\nimport { useEffect } from 'react';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport { KTX2Loader } from 'three-stdlib';\nimport { IsObject } from './Texture.js';\nconst cdn = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master';\nfunction useKTX2(input, basisPath = `${cdn}/basis/`) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(KTX2Loader, IsObject(input) ? Object.values(input) : input, loader => {\n    loader.detectSupport(gl);\n    loader.setTranscoderPath(basisPath);\n  });\n\n  // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\nuseKTX2.preload = (url, basisPath = `${cdn}/basis/`) => useLoader.preload(KTX2Loader, url, loader => {\n  loader.setTranscoderPath(basisPath);\n});\nuseKTX2.clear = input => useLoader.clear(KTX2Loader, input);\n\n//\n\nconst Ktx2 = ({\n  children,\n  input,\n  basisPath\n}) => {\n  const texture = useKTX2(input, basisPath);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n};\nexport { Ktx2, useKTX2 };", "map": {"version": 3, "names": ["React", "useEffect", "useThree", "useLoader", "KTX2Loader", "IsObject", "cdn", "useKTX2", "input", "basisPath", "gl", "state", "textures", "Object", "values", "loader", "detectSupport", "setTranscoderPath", "array", "Array", "isArray", "for<PERSON>ach", "initTexture", "keys", "keyed", "key", "assign", "indexOf", "preload", "url", "clear", "Ktx2", "children", "texture", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Ktx2.js"], "sourcesContent": ["import * as React from 'react';\nimport { useEffect } from 'react';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport { KTX2Loader } from 'three-stdlib';\nimport { IsObject } from './Texture.js';\n\nconst cdn = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master';\nfunction useKTX2(input, basisPath = `${cdn}/basis/`) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(KTX2Loader, IsObject(input) ? Object.values(input) : input, loader => {\n    loader.detectSupport(gl);\n    loader.setTranscoderPath(basisPath);\n  });\n\n  // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\nuseKTX2.preload = (url, basisPath = `${cdn}/basis/`) => useLoader.preload(KTX2Loader, url, loader => {\n  loader.setTranscoderPath(basisPath);\n});\nuseKTX2.clear = input => useLoader.clear(KTX2Loader, input);\n\n//\n\nconst Ktx2 = ({\n  children,\n  input,\n  basisPath\n}) => {\n  const texture = useKTX2(input, basisPath);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n};\n\nexport { Ktx2, useKTX2 };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,cAAc;AAEvC,MAAMC,GAAG,GAAG,uDAAuD;AACnE,SAASC,OAAOA,CAACC,KAAK,EAAEC,SAAS,GAAG,GAAGH,GAAG,SAAS,EAAE;EACnD,MAAMI,EAAE,GAAGR,QAAQ,CAACS,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,QAAQ,GAAGT,SAAS,CAACC,UAAU,EAAEC,QAAQ,CAACG,KAAK,CAAC,GAAGK,MAAM,CAACC,MAAM,CAACN,KAAK,CAAC,GAAGA,KAAK,EAAEO,MAAM,IAAI;IAC/FA,MAAM,CAACC,aAAa,CAACN,EAAE,CAAC;IACxBK,MAAM,CAACE,iBAAiB,CAACR,SAAS,CAAC;EACrC,CAAC,CAAC;;EAEF;EACA;EACAR,SAAS,CAAC,MAAM;IACd,MAAMiB,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DM,KAAK,CAACG,OAAO,CAACX,EAAE,CAACY,WAAW,CAAC;EAC/B,CAAC,EAAE,CAACZ,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAClB,IAAIP,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,MAAMe,IAAI,GAAGV,MAAM,CAACU,IAAI,CAACf,KAAK,CAAC;IAC/B,MAAMgB,KAAK,GAAG,CAAC,CAAC;IAChBD,IAAI,CAACF,OAAO,CAACI,GAAG,IAAIZ,MAAM,CAACa,MAAM,CAACF,KAAK,EAAE;MACvC,CAACC,GAAG,GAAGb,QAAQ,CAACW,IAAI,CAACI,OAAO,CAACF,GAAG,CAAC;IACnC,CAAC,CAAC,CAAC;IACH,OAAOD,KAAK;EACd,CAAC,MAAM;IACL,OAAOZ,QAAQ;EACjB;AACF;AACAL,OAAO,CAACqB,OAAO,GAAG,CAACC,GAAG,EAAEpB,SAAS,GAAG,GAAGH,GAAG,SAAS,KAAKH,SAAS,CAACyB,OAAO,CAACxB,UAAU,EAAEyB,GAAG,EAAEd,MAAM,IAAI;EACnGA,MAAM,CAACE,iBAAiB,CAACR,SAAS,CAAC;AACrC,CAAC,CAAC;AACFF,OAAO,CAACuB,KAAK,GAAGtB,KAAK,IAAIL,SAAS,CAAC2B,KAAK,CAAC1B,UAAU,EAAEI,KAAK,CAAC;;AAE3D;;AAEA,MAAMuB,IAAI,GAAGA,CAAC;EACZC,QAAQ;EACRxB,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMwB,OAAO,GAAG1B,OAAO,CAACC,KAAK,EAAEC,SAAS,CAAC;EACzC,OAAO,aAAaT,KAAK,CAACkC,aAAa,CAAClC,KAAK,CAACmC,QAAQ,EAAE,IAAI,EAAEH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,OAAO,CAAC,CAAC;AAC9G,CAAC;AAED,SAASF,IAAI,EAAExB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}