{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { PropertyBinding, InterpolateLinear, Color, Vector3, CompressedTexture, Texture, MathUtils, RGBAFormat, DoubleSide, BufferAttribute, InterpolateDiscrete, Matrix4, Scene, PlaneGeometry, ShaderMaterial, Uniform, Mesh, PerspectiveCamera, WebGLRenderer, NearestFilter, NearestMipmapNearestFilter, NearestMipmapLinearFilter, LinearFilter, LinearMipmapNearestFilter, LinearMipmapLinearFilter, ClampToEdgeWrapping, RepeatWrapping, MirroredRepeatWrapping } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nasync function readAsDataURL(blob) {\n  const buffer = await blob.arrayBuffer();\n  const data = btoa(String.fromCharCode(...new Uint8Array(buffer)));\n  return `data:${blob.type || \"\"};base64,${data}`;\n}\nlet _renderer;\nlet fullscreenQuadGeometry;\nlet fullscreenQuadMaterial;\nlet fullscreenQuad;\nfunction decompress(texture, maxTextureSize = Infinity, renderer = null) {\n  if (!fullscreenQuadGeometry) fullscreenQuadGeometry = new PlaneGeometry(2, 2, 1, 1);\n  if (!fullscreenQuadMaterial) fullscreenQuadMaterial = new ShaderMaterial({\n    uniforms: {\n      blitTexture: new Uniform(texture)\n    },\n    vertexShader: (/* glsl */\n    `\n        varying vec2 vUv;\n        void main(){\n            vUv = uv;\n            gl_Position = vec4(position.xy * 1.0,0.,.999999);\n        }\n      `),\n    fragmentShader: (/* glsl */\n    `\n          uniform sampler2D blitTexture; \n          varying vec2 vUv;\n\n          void main(){ \n              gl_FragColor = vec4(vUv.xy, 0, 1);\n              \n              #ifdef IS_SRGB\n              gl_FragColor = LinearTosRGB( texture2D( blitTexture, vUv) );\n              #else\n              gl_FragColor = texture2D( blitTexture, vUv);\n              #endif\n          }\n      `)\n  });\n  fullscreenQuadMaterial.uniforms.blitTexture.value = texture;\n  fullscreenQuadMaterial.defines.IS_SRGB = \"colorSpace\" in texture ? texture.colorSpace === \"srgb\" : texture.encoding === 3001;\n  fullscreenQuadMaterial.needsUpdate = true;\n  if (!fullscreenQuad) {\n    fullscreenQuad = new Mesh(fullscreenQuadGeometry, fullscreenQuadMaterial);\n    fullscreenQuad.frustrumCulled = false;\n  }\n  const _camera = new PerspectiveCamera();\n  const _scene = new Scene();\n  _scene.add(fullscreenQuad);\n  if (!renderer) {\n    renderer = _renderer = new WebGLRenderer({\n      antialias: false\n    });\n  }\n  renderer.setSize(Math.min(texture.image.width, maxTextureSize), Math.min(texture.image.height, maxTextureSize));\n  renderer.clear();\n  renderer.render(_scene, _camera);\n  const readableTexture = new Texture(renderer.domElement);\n  readableTexture.minFilter = texture.minFilter;\n  readableTexture.magFilter = texture.magFilter;\n  readableTexture.wrapS = texture.wrapS;\n  readableTexture.wrapT = texture.wrapT;\n  readableTexture.name = texture.name;\n  if (_renderer) {\n    _renderer.dispose();\n    _renderer = null;\n  }\n  return readableTexture;\n}\nconst KHR_mesh_quantization_ExtraAttrTypes = {\n  POSITION: [\"byte\", \"byte normalized\", \"unsigned byte\", \"unsigned byte normalized\", \"short\", \"short normalized\", \"unsigned short\", \"unsigned short normalized\"],\n  NORMAL: [\"byte normalized\", \"short normalized\"],\n  TANGENT: [\"byte normalized\", \"short normalized\"],\n  TEXCOORD: [\"byte\", \"byte normalized\", \"unsigned byte\", \"short\", \"short normalized\", \"unsigned short\"]\n};\nconst GLTFExporter = /* @__PURE__ */(() => {\n  class GLTFExporter2 {\n    constructor() {\n      this.pluginCallbacks = [];\n      this.register(function (writer) {\n        return new GLTFLightExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsUnlitExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsTransmissionExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsVolumeExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsIorExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsSpecularExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsClearcoatExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsIridescenceExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsSheenExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsAnisotropyExtension(writer);\n      });\n      this.register(function (writer) {\n        return new GLTFMaterialsEmissiveStrengthExtension(writer);\n      });\n    }\n    register(callback) {\n      if (this.pluginCallbacks.indexOf(callback) === -1) {\n        this.pluginCallbacks.push(callback);\n      }\n      return this;\n    }\n    unregister(callback) {\n      if (this.pluginCallbacks.indexOf(callback) !== -1) {\n        this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1);\n      }\n      return this;\n    }\n    /**\n     * Parse scenes and generate GLTF output\n     * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n     * @param  {Function} onDone  Callback on completed\n     * @param  {Function} onError  Callback on errors\n     * @param  {Object} options options\n     */\n    parse(input, onDone, onError, options) {\n      const writer = new GLTFWriter();\n      const plugins = [];\n      for (let i = 0, il = this.pluginCallbacks.length; i < il; i++) {\n        plugins.push(this.pluginCallbacks[i](writer));\n      }\n      writer.setPlugins(plugins);\n      writer.write(input, onDone, options).catch(onError);\n    }\n    parseAsync(input, options) {\n      const scope = this;\n      return new Promise(function (resolve, reject) {\n        scope.parse(input, resolve, reject, options);\n      });\n    }\n  }\n  /**\n   * Static utility functions\n   */\n  __publicField(GLTFExporter2, \"Utils\", {\n    insertKeyframe: function (track, time) {\n      const tolerance = 1e-3;\n      const valueSize = track.getValueSize();\n      const times = new track.TimeBufferType(track.times.length + 1);\n      const values = new track.ValueBufferType(track.values.length + valueSize);\n      const interpolant = track.createInterpolant(new track.ValueBufferType(valueSize));\n      let index;\n      if (track.times.length === 0) {\n        times[0] = time;\n        for (let i = 0; i < valueSize; i++) {\n          values[i] = 0;\n        }\n        index = 0;\n      } else if (time < track.times[0]) {\n        if (Math.abs(track.times[0] - time) < tolerance) return 0;\n        times[0] = time;\n        times.set(track.times, 1);\n        values.set(interpolant.evaluate(time), 0);\n        values.set(track.values, valueSize);\n        index = 0;\n      } else if (time > track.times[track.times.length - 1]) {\n        if (Math.abs(track.times[track.times.length - 1] - time) < tolerance) {\n          return track.times.length - 1;\n        }\n        times[times.length - 1] = time;\n        times.set(track.times, 0);\n        values.set(track.values, 0);\n        values.set(interpolant.evaluate(time), track.values.length);\n        index = times.length - 1;\n      } else {\n        for (let i = 0; i < track.times.length; i++) {\n          if (Math.abs(track.times[i] - time) < tolerance) return i;\n          if (track.times[i] < time && track.times[i + 1] > time) {\n            times.set(track.times.slice(0, i + 1), 0);\n            times[i + 1] = time;\n            times.set(track.times.slice(i + 1), i + 2);\n            values.set(track.values.slice(0, (i + 1) * valueSize), 0);\n            values.set(interpolant.evaluate(time), (i + 1) * valueSize);\n            values.set(track.values.slice((i + 1) * valueSize), (i + 2) * valueSize);\n            index = i + 1;\n            break;\n          }\n        }\n      }\n      track.times = times;\n      track.values = values;\n      return index;\n    },\n    mergeMorphTargetTracks: function (clip, root) {\n      const tracks = [];\n      const mergedTracks = {};\n      const sourceTracks = clip.tracks;\n      for (let i = 0; i < sourceTracks.length; ++i) {\n        let sourceTrack = sourceTracks[i];\n        const sourceTrackBinding = PropertyBinding.parseTrackName(sourceTrack.name);\n        const sourceTrackNode = PropertyBinding.findNode(root, sourceTrackBinding.nodeName);\n        if (sourceTrackBinding.propertyName !== \"morphTargetInfluences\" || sourceTrackBinding.propertyIndex === void 0) {\n          tracks.push(sourceTrack);\n          continue;\n        }\n        if (sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodDiscrete && sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodLinear) {\n          if (sourceTrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline) {\n            throw new Error(\"THREE.GLTFExporter: Cannot merge tracks with glTF CUBICSPLINE interpolation.\");\n          }\n          console.warn(\"THREE.GLTFExporter: Morph target interpolation mode not yet supported. Using LINEAR instead.\");\n          sourceTrack = sourceTrack.clone();\n          sourceTrack.setInterpolation(InterpolateLinear);\n        }\n        const targetCount = sourceTrackNode.morphTargetInfluences.length;\n        const targetIndex = sourceTrackNode.morphTargetDictionary[sourceTrackBinding.propertyIndex];\n        if (targetIndex === void 0) {\n          throw new Error(\"THREE.GLTFExporter: Morph target name not found: \" + sourceTrackBinding.propertyIndex);\n        }\n        let mergedTrack;\n        if (mergedTracks[sourceTrackNode.uuid] === void 0) {\n          mergedTrack = sourceTrack.clone();\n          const values = new mergedTrack.ValueBufferType(targetCount * mergedTrack.times.length);\n          for (let j = 0; j < mergedTrack.times.length; j++) {\n            values[j * targetCount + targetIndex] = mergedTrack.values[j];\n          }\n          mergedTrack.name = (sourceTrackBinding.nodeName || \"\") + \".morphTargetInfluences\";\n          mergedTrack.values = values;\n          mergedTracks[sourceTrackNode.uuid] = mergedTrack;\n          tracks.push(mergedTrack);\n          continue;\n        }\n        const sourceInterpolant = sourceTrack.createInterpolant(new sourceTrack.ValueBufferType(1));\n        mergedTrack = mergedTracks[sourceTrackNode.uuid];\n        for (let j = 0; j < mergedTrack.times.length; j++) {\n          mergedTrack.values[j * targetCount + targetIndex] = sourceInterpolant.evaluate(mergedTrack.times[j]);\n        }\n        for (let j = 0; j < sourceTrack.times.length; j++) {\n          const keyframeIndex = this.insertKeyframe(mergedTrack, sourceTrack.times[j]);\n          mergedTrack.values[keyframeIndex * targetCount + targetIndex] = sourceTrack.values[j];\n        }\n      }\n      clip.tracks = tracks;\n      return clip;\n    }\n  });\n  return GLTFExporter2;\n})();\nconst WEBGL_CONSTANTS = {\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  BYTE: 5120,\n  UNSIGNED_BYTE: 5121,\n  SHORT: 5122,\n  UNSIGNED_SHORT: 5123,\n  INT: 5124,\n  UNSIGNED_INT: 5125,\n  FLOAT: 5126,\n  ARRAY_BUFFER: 34962,\n  ELEMENT_ARRAY_BUFFER: 34963,\n  NEAREST: 9728,\n  LINEAR: 9729,\n  NEAREST_MIPMAP_NEAREST: 9984,\n  LINEAR_MIPMAP_NEAREST: 9985,\n  NEAREST_MIPMAP_LINEAR: 9986,\n  LINEAR_MIPMAP_LINEAR: 9987,\n  CLAMP_TO_EDGE: 33071,\n  MIRRORED_REPEAT: 33648,\n  REPEAT: 10497\n};\nconst KHR_MESH_QUANTIZATION = \"KHR_mesh_quantization\";\nconst THREE_TO_WEBGL = {};\nTHREE_TO_WEBGL[NearestFilter] = WEBGL_CONSTANTS.NEAREST;\nTHREE_TO_WEBGL[NearestMipmapNearestFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_NEAREST;\nTHREE_TO_WEBGL[NearestMipmapLinearFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_LINEAR;\nTHREE_TO_WEBGL[LinearFilter] = WEBGL_CONSTANTS.LINEAR;\nTHREE_TO_WEBGL[LinearMipmapNearestFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_NEAREST;\nTHREE_TO_WEBGL[LinearMipmapLinearFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_LINEAR;\nTHREE_TO_WEBGL[ClampToEdgeWrapping] = WEBGL_CONSTANTS.CLAMP_TO_EDGE;\nTHREE_TO_WEBGL[RepeatWrapping] = WEBGL_CONSTANTS.REPEAT;\nTHREE_TO_WEBGL[MirroredRepeatWrapping] = WEBGL_CONSTANTS.MIRRORED_REPEAT;\nconst PATH_PROPERTIES = {\n  scale: \"scale\",\n  position: \"translation\",\n  quaternion: \"rotation\",\n  morphTargetInfluences: \"weights\"\n};\nconst DEFAULT_SPECULAR_COLOR = /* @__PURE__ */new Color();\nconst GLB_HEADER_BYTES = 12;\nconst GLB_HEADER_MAGIC = 1179937895;\nconst GLB_VERSION = 2;\nconst GLB_CHUNK_PREFIX_BYTES = 8;\nconst GLB_CHUNK_TYPE_JSON = 1313821514;\nconst GLB_CHUNK_TYPE_BIN = 5130562;\nfunction equalArray(array1, array2) {\n  return array1.length === array2.length && array1.every(function (element, index) {\n    return element === array2[index];\n  });\n}\nfunction stringToArrayBuffer(text) {\n  return new TextEncoder().encode(text).buffer;\n}\nfunction isIdentityMatrix(matrix) {\n  return equalArray(matrix.elements, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);\n}\nfunction getMinMax(attribute, start, count) {\n  const output = {\n    min: new Array(attribute.itemSize).fill(Number.POSITIVE_INFINITY),\n    max: new Array(attribute.itemSize).fill(Number.NEGATIVE_INFINITY)\n  };\n  for (let i = start; i < start + count; i++) {\n    for (let a = 0; a < attribute.itemSize; a++) {\n      let value;\n      if (attribute.itemSize > 4) {\n        value = attribute.array[i * attribute.itemSize + a];\n      } else {\n        if (a === 0) value = attribute.getX(i);else if (a === 1) value = attribute.getY(i);else if (a === 2) value = attribute.getZ(i);else if (a === 3) value = attribute.getW(i);\n        if (attribute.normalized === true) {\n          value = MathUtils.normalize(value, attribute.array);\n        }\n      }\n      output.min[a] = Math.min(output.min[a], value);\n      output.max[a] = Math.max(output.max[a], value);\n    }\n  }\n  return output;\n}\nfunction getPaddedBufferSize(bufferSize) {\n  return Math.ceil(bufferSize / 4) * 4;\n}\nfunction getPaddedArrayBuffer(arrayBuffer, paddingByte = 0) {\n  const paddedLength = getPaddedBufferSize(arrayBuffer.byteLength);\n  if (paddedLength !== arrayBuffer.byteLength) {\n    const array = new Uint8Array(paddedLength);\n    array.set(new Uint8Array(arrayBuffer));\n    if (paddingByte !== 0) {\n      for (let i = arrayBuffer.byteLength; i < paddedLength; i++) {\n        array[i] = paddingByte;\n      }\n    }\n    return array.buffer;\n  }\n  return arrayBuffer;\n}\nfunction getCanvas() {\n  if (typeof document === \"undefined\" && typeof OffscreenCanvas !== \"undefined\") {\n    return new OffscreenCanvas(1, 1);\n  }\n  return document.createElement(\"canvas\");\n}\nfunction getToBlobPromise(canvas, mimeType) {\n  if (canvas.toBlob !== void 0) {\n    return new Promise(resolve => canvas.toBlob(resolve, mimeType));\n  }\n  let quality;\n  if (mimeType === \"image/jpeg\") {\n    quality = 0.92;\n  } else if (mimeType === \"image/webp\") {\n    quality = 0.8;\n  }\n  return canvas.convertToBlob({\n    type: mimeType,\n    quality\n  });\n}\nclass GLTFWriter {\n  constructor() {\n    this.plugins = [];\n    this.options = {};\n    this.pending = [];\n    this.buffers = [];\n    this.byteOffset = 0;\n    this.buffers = [];\n    this.nodeMap = /* @__PURE__ */new Map();\n    this.skins = [];\n    this.extensionsUsed = {};\n    this.extensionsRequired = {};\n    this.uids = /* @__PURE__ */new Map();\n    this.uid = 0;\n    this.json = {\n      asset: {\n        version: \"2.0\",\n        generator: \"THREE.GLTFExporter\"\n      }\n    };\n    this.cache = {\n      meshes: /* @__PURE__ */new Map(),\n      attributes: /* @__PURE__ */new Map(),\n      attributesNormalized: /* @__PURE__ */new Map(),\n      materials: /* @__PURE__ */new Map(),\n      textures: /* @__PURE__ */new Map(),\n      images: /* @__PURE__ */new Map()\n    };\n  }\n  setPlugins(plugins) {\n    this.plugins = plugins;\n  }\n  /**\n   * Parse scenes and generate GLTF output\n   * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n   * @param  {Function} onDone  Callback on completed\n   * @param  {Object} options options\n   */\n  async write(input, onDone, options = {}) {\n    this.options = Object.assign({\n      // default options\n      binary: false,\n      trs: false,\n      onlyVisible: true,\n      maxTextureSize: Infinity,\n      animations: [],\n      includeCustomExtensions: false\n    }, options);\n    if (this.options.animations.length > 0) {\n      this.options.trs = true;\n    }\n    this.processInput(input);\n    await Promise.all(this.pending);\n    const writer = this;\n    const buffers = writer.buffers;\n    const json = writer.json;\n    options = writer.options;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionsRequired = writer.extensionsRequired;\n    const blob = new Blob(buffers, {\n      type: \"application/octet-stream\"\n    });\n    const extensionsUsedList = Object.keys(extensionsUsed);\n    const extensionsRequiredList = Object.keys(extensionsRequired);\n    if (extensionsUsedList.length > 0) json.extensionsUsed = extensionsUsedList;\n    if (extensionsRequiredList.length > 0) json.extensionsRequired = extensionsRequiredList;\n    if (json.buffers && json.buffers.length > 0) json.buffers[0].byteLength = blob.size;\n    if (options.binary === true) {\n      blob.arrayBuffer().then(result => {\n        const binaryChunk = getPaddedArrayBuffer(result);\n        const binaryChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES));\n        binaryChunkPrefix.setUint32(0, binaryChunk.byteLength, true);\n        binaryChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_BIN, true);\n        const jsonChunk = getPaddedArrayBuffer(stringToArrayBuffer(JSON.stringify(json)), 32);\n        const jsonChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES));\n        jsonChunkPrefix.setUint32(0, jsonChunk.byteLength, true);\n        jsonChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_JSON, true);\n        const header = new ArrayBuffer(GLB_HEADER_BYTES);\n        const headerView = new DataView(header);\n        headerView.setUint32(0, GLB_HEADER_MAGIC, true);\n        headerView.setUint32(4, GLB_VERSION, true);\n        const totalByteLength = GLB_HEADER_BYTES + jsonChunkPrefix.byteLength + jsonChunk.byteLength + binaryChunkPrefix.byteLength + binaryChunk.byteLength;\n        headerView.setUint32(8, totalByteLength, true);\n        const glbBlob = new Blob([header, jsonChunkPrefix, jsonChunk, binaryChunkPrefix, binaryChunk], {\n          type: \"application/octet-stream\"\n        });\n        glbBlob.arrayBuffer().then(onDone);\n      });\n    } else {\n      if (json.buffers && json.buffers.length > 0) {\n        readAsDataURL(blob).then(uri => {\n          json.buffers[0].uri = uri;\n          onDone(json);\n        });\n      } else {\n        onDone(json);\n      }\n    }\n  }\n  /**\n   * Serializes a userData.\n   *\n   * @param {THREE.Object3D|THREE.Material} object\n   * @param {Object} objectDef\n   */\n  serializeUserData(object, objectDef) {\n    if (Object.keys(object.userData).length === 0) return;\n    const options = this.options;\n    const extensionsUsed = this.extensionsUsed;\n    try {\n      const json = JSON.parse(JSON.stringify(object.userData));\n      if (options.includeCustomExtensions && json.gltfExtensions) {\n        if (objectDef.extensions === void 0) objectDef.extensions = {};\n        for (const extensionName in json.gltfExtensions) {\n          objectDef.extensions[extensionName] = json.gltfExtensions[extensionName];\n          extensionsUsed[extensionName] = true;\n        }\n        delete json.gltfExtensions;\n      }\n      if (Object.keys(json).length > 0) objectDef.extras = json;\n    } catch (error) {\n      console.warn(\"THREE.GLTFExporter: userData of '\" + object.name + \"' won't be serialized because of JSON.stringify error - \" + error.message);\n    }\n  }\n  /**\n   * Returns ids for buffer attributes.\n   * @param  {Object} object\n   * @return {Integer}\n   */\n  getUID(attribute, isRelativeCopy = false) {\n    if (this.uids.has(attribute) === false) {\n      const uids2 = /* @__PURE__ */new Map();\n      uids2.set(true, this.uid++);\n      uids2.set(false, this.uid++);\n      this.uids.set(attribute, uids2);\n    }\n    const uids = this.uids.get(attribute);\n    return uids.get(isRelativeCopy);\n  }\n  /**\n   * Checks if normal attribute values are normalized.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {Boolean}\n   */\n  isNormalizedNormalAttribute(normal) {\n    const cache = this.cache;\n    if (cache.attributesNormalized.has(normal)) return false;\n    const v = new Vector3();\n    for (let i = 0, il = normal.count; i < il; i++) {\n      if (Math.abs(v.fromBufferAttribute(normal, i).length() - 1) > 5e-4) return false;\n    }\n    return true;\n  }\n  /**\n   * Creates normalized normal buffer attribute.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {BufferAttribute}\n   *\n   */\n  createNormalizedNormalAttribute(normal) {\n    const cache = this.cache;\n    if (cache.attributesNormalized.has(normal)) return cache.attributesNormalized.get(normal);\n    const attribute = normal.clone();\n    const v = new Vector3();\n    for (let i = 0, il = attribute.count; i < il; i++) {\n      v.fromBufferAttribute(attribute, i);\n      if (v.x === 0 && v.y === 0 && v.z === 0) {\n        v.setX(1);\n      } else {\n        v.normalize();\n      }\n      attribute.setXYZ(i, v.x, v.y, v.z);\n    }\n    cache.attributesNormalized.set(normal, attribute);\n    return attribute;\n  }\n  /**\n   * Applies a texture transform, if present, to the map definition. Requires\n   * the KHR_texture_transform extension.\n   *\n   * @param {Object} mapDef\n   * @param {THREE.Texture} texture\n   */\n  applyTextureTransform(mapDef, texture) {\n    let didTransform = false;\n    const transformDef = {};\n    if (texture.offset.x !== 0 || texture.offset.y !== 0) {\n      transformDef.offset = texture.offset.toArray();\n      didTransform = true;\n    }\n    if (texture.rotation !== 0) {\n      transformDef.rotation = texture.rotation;\n      didTransform = true;\n    }\n    if (texture.repeat.x !== 1 || texture.repeat.y !== 1) {\n      transformDef.scale = texture.repeat.toArray();\n      didTransform = true;\n    }\n    if (didTransform) {\n      mapDef.extensions = mapDef.extensions || {};\n      mapDef.extensions[\"KHR_texture_transform\"] = transformDef;\n      this.extensionsUsed[\"KHR_texture_transform\"] = true;\n    }\n  }\n  buildMetalRoughTexture(metalnessMap, roughnessMap) {\n    if (metalnessMap === roughnessMap) return metalnessMap;\n    function getEncodingConversion(map) {\n      if (\"colorSpace\" in map ? map.colorSpace === \"srgb\" : map.encoding === 3001) {\n        return function SRGBToLinear(c) {\n          return c < 0.04045 ? c * 0.0773993808 : Math.pow(c * 0.9478672986 + 0.0521327014, 2.4);\n        };\n      }\n      return function LinearToLinear(c) {\n        return c;\n      };\n    }\n    console.warn(\"THREE.GLTFExporter: Merged metalnessMap and roughnessMap textures.\");\n    if (metalnessMap instanceof CompressedTexture) {\n      metalnessMap = decompress(metalnessMap);\n    }\n    if (roughnessMap instanceof CompressedTexture) {\n      roughnessMap = decompress(roughnessMap);\n    }\n    const metalness = metalnessMap ? metalnessMap.image : null;\n    const roughness = roughnessMap ? roughnessMap.image : null;\n    const width = Math.max(metalness ? metalness.width : 0, roughness ? roughness.width : 0);\n    const height = Math.max(metalness ? metalness.height : 0, roughness ? roughness.height : 0);\n    const canvas = getCanvas();\n    canvas.width = width;\n    canvas.height = height;\n    const context = canvas.getContext(\"2d\");\n    context.fillStyle = \"#00ffff\";\n    context.fillRect(0, 0, width, height);\n    const composite = context.getImageData(0, 0, width, height);\n    if (metalness) {\n      context.drawImage(metalness, 0, 0, width, height);\n      const convert = getEncodingConversion(metalnessMap);\n      const data = context.getImageData(0, 0, width, height).data;\n      for (let i = 2; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256;\n      }\n    }\n    if (roughness) {\n      context.drawImage(roughness, 0, 0, width, height);\n      const convert = getEncodingConversion(roughnessMap);\n      const data = context.getImageData(0, 0, width, height).data;\n      for (let i = 1; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256;\n      }\n    }\n    context.putImageData(composite, 0, 0);\n    const reference = metalnessMap || roughnessMap;\n    const texture = reference.clone();\n    texture.source = new Texture(canvas).source;\n    if (\"colorSpace\" in texture) texture.colorSpace = \"\";else texture.encoding = 3e3;\n    texture.channel = (metalnessMap || roughnessMap).channel;\n    if (metalnessMap && roughnessMap && metalnessMap.channel !== roughnessMap.channel) {\n      console.warn(\"THREE.GLTFExporter: UV channels for metalnessMap and roughnessMap textures must match.\");\n    }\n    return texture;\n  }\n  /**\n   * Process a buffer to append to the default one.\n   * @param  {ArrayBuffer} buffer\n   * @return {Integer}\n   */\n  processBuffer(buffer) {\n    const json = this.json;\n    const buffers = this.buffers;\n    if (!json.buffers) json.buffers = [{\n      byteLength: 0\n    }];\n    buffers.push(buffer);\n    return 0;\n  }\n  /**\n   * Process and generate a BufferView\n   * @param  {BufferAttribute} attribute\n   * @param  {number} componentType\n   * @param  {number} start\n   * @param  {number} count\n   * @param  {number} target (Optional) Target usage of the BufferView\n   * @return {Object}\n   */\n  processBufferView(attribute, componentType, start, count, target) {\n    const json = this.json;\n    if (!json.bufferViews) json.bufferViews = [];\n    let componentSize;\n    switch (componentType) {\n      case WEBGL_CONSTANTS.BYTE:\n      case WEBGL_CONSTANTS.UNSIGNED_BYTE:\n        componentSize = 1;\n        break;\n      case WEBGL_CONSTANTS.SHORT:\n      case WEBGL_CONSTANTS.UNSIGNED_SHORT:\n        componentSize = 2;\n        break;\n      default:\n        componentSize = 4;\n    }\n    let byteStride = attribute.itemSize * componentSize;\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      byteStride = Math.ceil(byteStride / 4) * 4;\n    }\n    const byteLength = getPaddedBufferSize(count * byteStride);\n    const dataView = new DataView(new ArrayBuffer(byteLength));\n    let offset = 0;\n    for (let i = start; i < start + count; i++) {\n      for (let a = 0; a < attribute.itemSize; a++) {\n        let value;\n        if (attribute.itemSize > 4) {\n          value = attribute.array[i * attribute.itemSize + a];\n        } else {\n          if (a === 0) value = attribute.getX(i);else if (a === 1) value = attribute.getY(i);else if (a === 2) value = attribute.getZ(i);else if (a === 3) value = attribute.getW(i);\n          if (attribute.normalized === true) {\n            value = MathUtils.normalize(value, attribute.array);\n          }\n        }\n        if (componentType === WEBGL_CONSTANTS.FLOAT) {\n          dataView.setFloat32(offset, value, true);\n        } else if (componentType === WEBGL_CONSTANTS.INT) {\n          dataView.setInt32(offset, value, true);\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_INT) {\n          dataView.setUint32(offset, value, true);\n        } else if (componentType === WEBGL_CONSTANTS.SHORT) {\n          dataView.setInt16(offset, value, true);\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_SHORT) {\n          dataView.setUint16(offset, value, true);\n        } else if (componentType === WEBGL_CONSTANTS.BYTE) {\n          dataView.setInt8(offset, value);\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_BYTE) {\n          dataView.setUint8(offset, value);\n        }\n        offset += componentSize;\n      }\n      if (offset % byteStride !== 0) {\n        offset += byteStride - offset % byteStride;\n      }\n    }\n    const bufferViewDef = {\n      buffer: this.processBuffer(dataView.buffer),\n      byteOffset: this.byteOffset,\n      byteLength\n    };\n    if (target !== void 0) bufferViewDef.target = target;\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      bufferViewDef.byteStride = byteStride;\n    }\n    this.byteOffset += byteLength;\n    json.bufferViews.push(bufferViewDef);\n    const output = {\n      id: json.bufferViews.length - 1,\n      byteLength: 0\n    };\n    return output;\n  }\n  /**\n   * Process and generate a BufferView from an image Blob.\n   * @param {Blob} blob\n   * @return {Promise<Integer>}\n   */\n  processBufferViewImage(blob) {\n    const writer = this;\n    const json = writer.json;\n    if (!json.bufferViews) json.bufferViews = [];\n    return blob.arrayBuffer().then(result => {\n      const buffer = getPaddedArrayBuffer(result);\n      const bufferViewDef = {\n        buffer: writer.processBuffer(buffer),\n        byteOffset: writer.byteOffset,\n        byteLength: buffer.byteLength\n      };\n      writer.byteOffset += buffer.byteLength;\n      return json.bufferViews.push(bufferViewDef) - 1;\n    });\n  }\n  /**\n   * Process attribute to generate an accessor\n   * @param  {BufferAttribute} attribute Attribute to process\n   * @param  {THREE.BufferGeometry} geometry (Optional) Geometry used for truncated draw range\n   * @param  {Integer} start (Optional)\n   * @param  {Integer} count (Optional)\n   * @return {Integer|null} Index of the processed accessor on the \"accessors\" array\n   */\n  processAccessor(attribute, geometry, start, count) {\n    const json = this.json;\n    const types = {\n      1: \"SCALAR\",\n      2: \"VEC2\",\n      3: \"VEC3\",\n      4: \"VEC4\",\n      9: \"MAT3\",\n      16: \"MAT4\"\n    };\n    let componentType;\n    if (attribute.array.constructor === Float32Array) {\n      componentType = WEBGL_CONSTANTS.FLOAT;\n    } else if (attribute.array.constructor === Int32Array) {\n      componentType = WEBGL_CONSTANTS.INT;\n    } else if (attribute.array.constructor === Uint32Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_INT;\n    } else if (attribute.array.constructor === Int16Array) {\n      componentType = WEBGL_CONSTANTS.SHORT;\n    } else if (attribute.array.constructor === Uint16Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_SHORT;\n    } else if (attribute.array.constructor === Int8Array) {\n      componentType = WEBGL_CONSTANTS.BYTE;\n    } else if (attribute.array.constructor === Uint8Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_BYTE;\n    } else {\n      throw new Error(\"THREE.GLTFExporter: Unsupported bufferAttribute component type: \" + attribute.array.constructor.name);\n    }\n    if (start === void 0) start = 0;\n    if (count === void 0) count = attribute.count;\n    if (count === 0) return null;\n    const minMax = getMinMax(attribute, start, count);\n    let bufferViewTarget;\n    if (geometry !== void 0) {\n      bufferViewTarget = attribute === geometry.index ? WEBGL_CONSTANTS.ELEMENT_ARRAY_BUFFER : WEBGL_CONSTANTS.ARRAY_BUFFER;\n    }\n    const bufferView = this.processBufferView(attribute, componentType, start, count, bufferViewTarget);\n    const accessorDef = {\n      bufferView: bufferView.id,\n      byteOffset: bufferView.byteOffset,\n      componentType,\n      count,\n      max: minMax.max,\n      min: minMax.min,\n      type: types[attribute.itemSize]\n    };\n    if (attribute.normalized === true) accessorDef.normalized = true;\n    if (!json.accessors) json.accessors = [];\n    return json.accessors.push(accessorDef) - 1;\n  }\n  /**\n   * Process image\n   * @param  {Image} image to process\n   * @param  {Integer} format of the image (RGBAFormat)\n   * @param  {Boolean} flipY before writing out the image\n   * @param  {String} mimeType export format\n   * @return {Integer}     Index of the processed texture in the \"images\" array\n   */\n  processImage(image, format, flipY, mimeType = \"image/png\") {\n    if (image !== null) {\n      const writer = this;\n      const cache = writer.cache;\n      const json = writer.json;\n      const options = writer.options;\n      const pending = writer.pending;\n      if (!cache.images.has(image)) cache.images.set(image, {});\n      const cachedImages = cache.images.get(image);\n      const key = mimeType + \":flipY/\" + flipY.toString();\n      if (cachedImages[key] !== void 0) return cachedImages[key];\n      if (!json.images) json.images = [];\n      const imageDef = {\n        mimeType\n      };\n      const canvas = getCanvas();\n      canvas.width = Math.min(image.width, options.maxTextureSize);\n      canvas.height = Math.min(image.height, options.maxTextureSize);\n      const ctx = canvas.getContext(\"2d\");\n      if (flipY === true) {\n        ctx.translate(0, canvas.height);\n        ctx.scale(1, -1);\n      }\n      if (image.data !== void 0) {\n        if (format !== RGBAFormat) {\n          console.error(\"GLTFExporter: Only RGBAFormat is supported.\", format);\n        }\n        if (image.width > options.maxTextureSize || image.height > options.maxTextureSize) {\n          console.warn(\"GLTFExporter: Image size is bigger than maxTextureSize\", image);\n        }\n        const data = new Uint8ClampedArray(image.height * image.width * 4);\n        for (let i = 0; i < data.length; i += 4) {\n          data[i + 0] = image.data[i + 0];\n          data[i + 1] = image.data[i + 1];\n          data[i + 2] = image.data[i + 2];\n          data[i + 3] = image.data[i + 3];\n        }\n        ctx.putImageData(new ImageData(data, image.width, image.height), 0, 0);\n      } else {\n        ctx.drawImage(image, 0, 0, canvas.width, canvas.height);\n      }\n      if (options.binary === true) {\n        pending.push(getToBlobPromise(canvas, mimeType).then(blob => writer.processBufferViewImage(blob)).then(bufferViewIndex => {\n          imageDef.bufferView = bufferViewIndex;\n        }));\n      } else {\n        if (canvas.toDataURL !== void 0) {\n          imageDef.uri = canvas.toDataURL(mimeType);\n        } else {\n          pending.push(getToBlobPromise(canvas, mimeType).then(readAsDataURL).then(uri => {\n            imageDef.uri = uri;\n          }));\n        }\n      }\n      const index = json.images.push(imageDef) - 1;\n      cachedImages[key] = index;\n      return index;\n    } else {\n      throw new Error(\"THREE.GLTFExporter: No valid image data found. Unable to process texture.\");\n    }\n  }\n  /**\n   * Process sampler\n   * @param  {Texture} map Texture to process\n   * @return {Integer}     Index of the processed texture in the \"samplers\" array\n   */\n  processSampler(map) {\n    const json = this.json;\n    if (!json.samplers) json.samplers = [];\n    const samplerDef = {\n      magFilter: THREE_TO_WEBGL[map.magFilter],\n      minFilter: THREE_TO_WEBGL[map.minFilter],\n      wrapS: THREE_TO_WEBGL[map.wrapS],\n      wrapT: THREE_TO_WEBGL[map.wrapT]\n    };\n    return json.samplers.push(samplerDef) - 1;\n  }\n  /**\n   * Process texture\n   * @param  {Texture} map Map to process\n   * @return {Integer} Index of the processed texture in the \"textures\" array\n   */\n  processTexture(map) {\n    const writer = this;\n    const options = writer.options;\n    const cache = this.cache;\n    const json = this.json;\n    if (cache.textures.has(map)) return cache.textures.get(map);\n    if (!json.textures) json.textures = [];\n    if (map instanceof CompressedTexture) {\n      map = decompress(map, options.maxTextureSize);\n    }\n    let mimeType = map.userData.mimeType;\n    if (mimeType === \"image/webp\") mimeType = \"image/png\";\n    const textureDef = {\n      sampler: this.processSampler(map),\n      source: this.processImage(map.image, map.format, map.flipY, mimeType)\n    };\n    if (map.name) textureDef.name = map.name;\n    this._invokeAll(function (ext) {\n      ext.writeTexture && ext.writeTexture(map, textureDef);\n    });\n    const index = json.textures.push(textureDef) - 1;\n    cache.textures.set(map, index);\n    return index;\n  }\n  /**\n   * Process material\n   * @param  {THREE.Material} material Material to process\n   * @return {Integer|null} Index of the processed material in the \"materials\" array\n   */\n  processMaterial(material) {\n    const cache = this.cache;\n    const json = this.json;\n    if (cache.materials.has(material)) return cache.materials.get(material);\n    if (material.isShaderMaterial) {\n      console.warn(\"GLTFExporter: THREE.ShaderMaterial not supported.\");\n      return null;\n    }\n    if (!json.materials) json.materials = [];\n    const materialDef = {\n      pbrMetallicRoughness: {}\n    };\n    if (material.isMeshStandardMaterial !== true && material.isMeshBasicMaterial !== true) {\n      console.warn(\"GLTFExporter: Use MeshStandardMaterial or MeshBasicMaterial for best results.\");\n    }\n    const color = material.color.toArray().concat([material.opacity]);\n    if (!equalArray(color, [1, 1, 1, 1])) {\n      materialDef.pbrMetallicRoughness.baseColorFactor = color;\n    }\n    if (material.isMeshStandardMaterial) {\n      materialDef.pbrMetallicRoughness.metallicFactor = material.metalness;\n      materialDef.pbrMetallicRoughness.roughnessFactor = material.roughness;\n    } else {\n      materialDef.pbrMetallicRoughness.metallicFactor = 0.5;\n      materialDef.pbrMetallicRoughness.roughnessFactor = 0.5;\n    }\n    if (material.metalnessMap || material.roughnessMap) {\n      const metalRoughTexture = this.buildMetalRoughTexture(material.metalnessMap, material.roughnessMap);\n      const metalRoughMapDef = {\n        index: this.processTexture(metalRoughTexture),\n        channel: metalRoughTexture.channel\n      };\n      this.applyTextureTransform(metalRoughMapDef, metalRoughTexture);\n      materialDef.pbrMetallicRoughness.metallicRoughnessTexture = metalRoughMapDef;\n    }\n    if (material.map) {\n      const baseColorMapDef = {\n        index: this.processTexture(material.map),\n        texCoord: material.map.channel\n      };\n      this.applyTextureTransform(baseColorMapDef, material.map);\n      materialDef.pbrMetallicRoughness.baseColorTexture = baseColorMapDef;\n    }\n    if (material.emissive) {\n      const emissive = material.emissive;\n      const maxEmissiveComponent = Math.max(emissive.r, emissive.g, emissive.b);\n      if (maxEmissiveComponent > 0) {\n        materialDef.emissiveFactor = material.emissive.toArray();\n      }\n      if (material.emissiveMap) {\n        const emissiveMapDef = {\n          index: this.processTexture(material.emissiveMap),\n          texCoord: material.emissiveMap.channel\n        };\n        this.applyTextureTransform(emissiveMapDef, material.emissiveMap);\n        materialDef.emissiveTexture = emissiveMapDef;\n      }\n    }\n    if (material.normalMap) {\n      const normalMapDef = {\n        index: this.processTexture(material.normalMap),\n        texCoord: material.normalMap.channel\n      };\n      if (material.normalScale && material.normalScale.x !== 1) {\n        normalMapDef.scale = material.normalScale.x;\n      }\n      this.applyTextureTransform(normalMapDef, material.normalMap);\n      materialDef.normalTexture = normalMapDef;\n    }\n    if (material.aoMap) {\n      const occlusionMapDef = {\n        index: this.processTexture(material.aoMap),\n        texCoord: material.aoMap.channel\n      };\n      if (material.aoMapIntensity !== 1) {\n        occlusionMapDef.strength = material.aoMapIntensity;\n      }\n      this.applyTextureTransform(occlusionMapDef, material.aoMap);\n      materialDef.occlusionTexture = occlusionMapDef;\n    }\n    if (material.transparent) {\n      materialDef.alphaMode = \"BLEND\";\n    } else {\n      if (material.alphaTest > 0) {\n        materialDef.alphaMode = \"MASK\";\n        materialDef.alphaCutoff = material.alphaTest;\n      }\n    }\n    if (material.side === DoubleSide) materialDef.doubleSided = true;\n    if (material.name !== \"\") materialDef.name = material.name;\n    this.serializeUserData(material, materialDef);\n    this._invokeAll(function (ext) {\n      ext.writeMaterial && ext.writeMaterial(material, materialDef);\n    });\n    const index = json.materials.push(materialDef) - 1;\n    cache.materials.set(material, index);\n    return index;\n  }\n  /**\n   * Process mesh\n   * @param  {THREE.Mesh} mesh Mesh to process\n   * @return {Integer|null} Index of the processed mesh in the \"meshes\" array\n   */\n  processMesh(mesh) {\n    const cache = this.cache;\n    const json = this.json;\n    const meshCacheKeyParts = [mesh.geometry.uuid];\n    if (Array.isArray(mesh.material)) {\n      for (let i = 0, l = mesh.material.length; i < l; i++) {\n        meshCacheKeyParts.push(mesh.material[i].uuid);\n      }\n    } else {\n      meshCacheKeyParts.push(mesh.material.uuid);\n    }\n    const meshCacheKey = meshCacheKeyParts.join(\":\");\n    if (cache.meshes.has(meshCacheKey)) return cache.meshes.get(meshCacheKey);\n    const geometry = mesh.geometry;\n    let mode;\n    if (mesh.isLineSegments) {\n      mode = WEBGL_CONSTANTS.LINES;\n    } else if (mesh.isLineLoop) {\n      mode = WEBGL_CONSTANTS.LINE_LOOP;\n    } else if (mesh.isLine) {\n      mode = WEBGL_CONSTANTS.LINE_STRIP;\n    } else if (mesh.isPoints) {\n      mode = WEBGL_CONSTANTS.POINTS;\n    } else {\n      mode = mesh.material.wireframe ? WEBGL_CONSTANTS.LINES : WEBGL_CONSTANTS.TRIANGLES;\n    }\n    const meshDef = {};\n    const attributes = {};\n    const primitives = [];\n    const targets = [];\n    const nameConversion = {\n      ...(version >= 152 ? {\n        uv: \"TEXCOORD_0\",\n        uv1: \"TEXCOORD_1\",\n        uv2: \"TEXCOORD_2\",\n        uv3: \"TEXCOORD_3\"\n      } : {\n        uv: \"TEXCOORD_0\",\n        uv2: \"TEXCOORD_1\"\n      }),\n      color: \"COLOR_0\",\n      skinWeight: \"WEIGHTS_0\",\n      skinIndex: \"JOINTS_0\"\n    };\n    const originalNormal = geometry.getAttribute(\"normal\");\n    if (originalNormal !== void 0 && !this.isNormalizedNormalAttribute(originalNormal)) {\n      console.warn(\"THREE.GLTFExporter: Creating normalized normal attribute from the non-normalized one.\");\n      geometry.setAttribute(\"normal\", this.createNormalizedNormalAttribute(originalNormal));\n    }\n    let modifiedAttribute = null;\n    for (let attributeName in geometry.attributes) {\n      if (attributeName.slice(0, 5) === \"morph\") continue;\n      const attribute = geometry.attributes[attributeName];\n      attributeName = nameConversion[attributeName] || attributeName.toUpperCase();\n      const validVertexAttributes = /^(POSITION|NORMAL|TANGENT|TEXCOORD_\\d+|COLOR_\\d+|JOINTS_\\d+|WEIGHTS_\\d+)$/;\n      if (!validVertexAttributes.test(attributeName)) attributeName = \"_\" + attributeName;\n      if (cache.attributes.has(this.getUID(attribute))) {\n        attributes[attributeName] = cache.attributes.get(this.getUID(attribute));\n        continue;\n      }\n      modifiedAttribute = null;\n      const array = attribute.array;\n      if (attributeName === \"JOINTS_0\" && !(array instanceof Uint16Array) && !(array instanceof Uint8Array)) {\n        console.warn('GLTFExporter: Attribute \"skinIndex\" converted to type UNSIGNED_SHORT.');\n        modifiedAttribute = new BufferAttribute(new Uint16Array(array), attribute.itemSize, attribute.normalized);\n      }\n      const accessor = this.processAccessor(modifiedAttribute || attribute, geometry);\n      if (accessor !== null) {\n        if (!attributeName.startsWith(\"_\")) {\n          this.detectMeshQuantization(attributeName, attribute);\n        }\n        attributes[attributeName] = accessor;\n        cache.attributes.set(this.getUID(attribute), accessor);\n      }\n    }\n    if (originalNormal !== void 0) geometry.setAttribute(\"normal\", originalNormal);\n    if (Object.keys(attributes).length === 0) return null;\n    if (mesh.morphTargetInfluences !== void 0 && mesh.morphTargetInfluences.length > 0) {\n      const weights = [];\n      const targetNames = [];\n      const reverseDictionary = {};\n      if (mesh.morphTargetDictionary !== void 0) {\n        for (const key in mesh.morphTargetDictionary) {\n          reverseDictionary[mesh.morphTargetDictionary[key]] = key;\n        }\n      }\n      for (let i = 0; i < mesh.morphTargetInfluences.length; ++i) {\n        const target = {};\n        let warned = false;\n        for (const attributeName in geometry.morphAttributes) {\n          if (attributeName !== \"position\" && attributeName !== \"normal\") {\n            if (!warned) {\n              console.warn(\"GLTFExporter: Only POSITION and NORMAL morph are supported.\");\n              warned = true;\n            }\n            continue;\n          }\n          const attribute = geometry.morphAttributes[attributeName][i];\n          const gltfAttributeName = attributeName.toUpperCase();\n          const baseAttribute = geometry.attributes[attributeName];\n          if (cache.attributes.has(this.getUID(attribute, true))) {\n            target[gltfAttributeName] = cache.attributes.get(this.getUID(attribute, true));\n            continue;\n          }\n          const relativeAttribute = attribute.clone();\n          if (!geometry.morphTargetsRelative) {\n            for (let j = 0, jl = attribute.count; j < jl; j++) {\n              for (let a = 0; a < attribute.itemSize; a++) {\n                if (a === 0) relativeAttribute.setX(j, attribute.getX(j) - baseAttribute.getX(j));\n                if (a === 1) relativeAttribute.setY(j, attribute.getY(j) - baseAttribute.getY(j));\n                if (a === 2) relativeAttribute.setZ(j, attribute.getZ(j) - baseAttribute.getZ(j));\n                if (a === 3) relativeAttribute.setW(j, attribute.getW(j) - baseAttribute.getW(j));\n              }\n            }\n          }\n          target[gltfAttributeName] = this.processAccessor(relativeAttribute, geometry);\n          cache.attributes.set(this.getUID(baseAttribute, true), target[gltfAttributeName]);\n        }\n        targets.push(target);\n        weights.push(mesh.morphTargetInfluences[i]);\n        if (mesh.morphTargetDictionary !== void 0) targetNames.push(reverseDictionary[i]);\n      }\n      meshDef.weights = weights;\n      if (targetNames.length > 0) {\n        meshDef.extras = {};\n        meshDef.extras.targetNames = targetNames;\n      }\n    }\n    const isMultiMaterial = Array.isArray(mesh.material);\n    if (isMultiMaterial && geometry.groups.length === 0) return null;\n    const materials = isMultiMaterial ? mesh.material : [mesh.material];\n    const groups = isMultiMaterial ? geometry.groups : [{\n      materialIndex: 0,\n      start: void 0,\n      count: void 0\n    }];\n    for (let i = 0, il = groups.length; i < il; i++) {\n      const primitive = {\n        mode,\n        attributes\n      };\n      this.serializeUserData(geometry, primitive);\n      if (targets.length > 0) primitive.targets = targets;\n      if (geometry.index !== null) {\n        let cacheKey = this.getUID(geometry.index);\n        if (groups[i].start !== void 0 || groups[i].count !== void 0) {\n          cacheKey += \":\" + groups[i].start + \":\" + groups[i].count;\n        }\n        if (cache.attributes.has(cacheKey)) {\n          primitive.indices = cache.attributes.get(cacheKey);\n        } else {\n          primitive.indices = this.processAccessor(geometry.index, geometry, groups[i].start, groups[i].count);\n          cache.attributes.set(cacheKey, primitive.indices);\n        }\n        if (primitive.indices === null) delete primitive.indices;\n      }\n      const material = this.processMaterial(materials[groups[i].materialIndex]);\n      if (material !== null) primitive.material = material;\n      primitives.push(primitive);\n    }\n    meshDef.primitives = primitives;\n    if (!json.meshes) json.meshes = [];\n    this._invokeAll(function (ext) {\n      ext.writeMesh && ext.writeMesh(mesh, meshDef);\n    });\n    const index = json.meshes.push(meshDef) - 1;\n    cache.meshes.set(meshCacheKey, index);\n    return index;\n  }\n  /**\n   * If a vertex attribute with a\n   * [non-standard data type](https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html#meshes-overview)\n   * is used, it is checked whether it is a valid data type according to the\n   * [KHR_mesh_quantization](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md)\n   * extension.\n   * In this case the extension is automatically added to the list of used extensions.\n   *\n   * @param {string} attributeName\n   * @param {THREE.BufferAttribute} attribute\n   */\n  detectMeshQuantization(attributeName, attribute) {\n    if (this.extensionsUsed[KHR_MESH_QUANTIZATION]) return;\n    let attrType = void 0;\n    switch (attribute.array.constructor) {\n      case Int8Array:\n        attrType = \"byte\";\n        break;\n      case Uint8Array:\n        attrType = \"unsigned byte\";\n        break;\n      case Int16Array:\n        attrType = \"short\";\n        break;\n      case Uint16Array:\n        attrType = \"unsigned short\";\n        break;\n      default:\n        return;\n    }\n    if (attribute.normalized) attrType += \" normalized\";\n    const attrNamePrefix = attributeName.split(\"_\", 1)[0];\n    if (KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix] && KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix].includes(attrType)) {\n      this.extensionsUsed[KHR_MESH_QUANTIZATION] = true;\n      this.extensionsRequired[KHR_MESH_QUANTIZATION] = true;\n    }\n  }\n  /**\n   * Process camera\n   * @param  {THREE.Camera} camera Camera to process\n   * @return {Integer}      Index of the processed mesh in the \"camera\" array\n   */\n  processCamera(camera) {\n    const json = this.json;\n    if (!json.cameras) json.cameras = [];\n    const isOrtho = camera.isOrthographicCamera;\n    const cameraDef = {\n      type: isOrtho ? \"orthographic\" : \"perspective\"\n    };\n    if (isOrtho) {\n      cameraDef.orthographic = {\n        xmag: camera.right * 2,\n        ymag: camera.top * 2,\n        zfar: camera.far <= 0 ? 1e-3 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near\n      };\n    } else {\n      cameraDef.perspective = {\n        aspectRatio: camera.aspect,\n        yfov: MathUtils.degToRad(camera.fov),\n        zfar: camera.far <= 0 ? 1e-3 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near\n      };\n    }\n    if (camera.name !== \"\") cameraDef.name = camera.type;\n    return json.cameras.push(cameraDef) - 1;\n  }\n  /**\n   * Creates glTF animation entry from AnimationClip object.\n   *\n   * Status:\n   * - Only properties listed in PATH_PROPERTIES may be animated.\n   *\n   * @param {THREE.AnimationClip} clip\n   * @param {THREE.Object3D} root\n   * @return {number|null}\n   */\n  processAnimation(clip, root) {\n    const json = this.json;\n    const nodeMap = this.nodeMap;\n    if (!json.animations) json.animations = [];\n    clip = GLTFExporter.Utils.mergeMorphTargetTracks(clip.clone(), root);\n    const tracks = clip.tracks;\n    const channels = [];\n    const samplers = [];\n    for (let i = 0; i < tracks.length; ++i) {\n      const track = tracks[i];\n      const trackBinding = PropertyBinding.parseTrackName(track.name);\n      let trackNode = PropertyBinding.findNode(root, trackBinding.nodeName);\n      const trackProperty = PATH_PROPERTIES[trackBinding.propertyName];\n      if (trackBinding.objectName === \"bones\") {\n        if (trackNode.isSkinnedMesh === true) {\n          trackNode = trackNode.skeleton.getBoneByName(trackBinding.objectIndex);\n        } else {\n          trackNode = void 0;\n        }\n      }\n      if (!trackNode || !trackProperty) {\n        console.warn('THREE.GLTFExporter: Could not export animation track \"%s\".', track.name);\n        return null;\n      }\n      const inputItemSize = 1;\n      let outputItemSize = track.values.length / track.times.length;\n      if (trackProperty === PATH_PROPERTIES.morphTargetInfluences) {\n        outputItemSize /= trackNode.morphTargetInfluences.length;\n      }\n      let interpolation;\n      if (track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline === true) {\n        interpolation = \"CUBICSPLINE\";\n        outputItemSize /= 3;\n      } else if (track.getInterpolation() === InterpolateDiscrete) {\n        interpolation = \"STEP\";\n      } else {\n        interpolation = \"LINEAR\";\n      }\n      samplers.push({\n        input: this.processAccessor(new BufferAttribute(track.times, inputItemSize)),\n        output: this.processAccessor(new BufferAttribute(track.values, outputItemSize)),\n        interpolation\n      });\n      channels.push({\n        sampler: samplers.length - 1,\n        target: {\n          node: nodeMap.get(trackNode),\n          path: trackProperty\n        }\n      });\n    }\n    json.animations.push({\n      name: clip.name || \"clip_\" + json.animations.length,\n      samplers,\n      channels\n    });\n    return json.animations.length - 1;\n  }\n  /**\n   * @param {THREE.Object3D} object\n   * @return {number|null}\n   */\n  processSkin(object) {\n    const json = this.json;\n    const nodeMap = this.nodeMap;\n    const node = json.nodes[nodeMap.get(object)];\n    const skeleton = object.skeleton;\n    if (skeleton === void 0) return null;\n    const rootJoint = object.skeleton.bones[0];\n    if (rootJoint === void 0) return null;\n    const joints = [];\n    const inverseBindMatrices = new Float32Array(skeleton.bones.length * 16);\n    const temporaryBoneInverse = new Matrix4();\n    for (let i = 0; i < skeleton.bones.length; ++i) {\n      joints.push(nodeMap.get(skeleton.bones[i]));\n      temporaryBoneInverse.copy(skeleton.boneInverses[i]);\n      temporaryBoneInverse.multiply(object.bindMatrix).toArray(inverseBindMatrices, i * 16);\n    }\n    if (json.skins === void 0) json.skins = [];\n    json.skins.push({\n      inverseBindMatrices: this.processAccessor(new BufferAttribute(inverseBindMatrices, 16)),\n      joints,\n      skeleton: nodeMap.get(rootJoint)\n    });\n    const skinIndex = node.skin = json.skins.length - 1;\n    return skinIndex;\n  }\n  /**\n   * Process Object3D node\n   * @param  {THREE.Object3D} node Object3D to processNode\n   * @return {Integer} Index of the node in the nodes list\n   */\n  processNode(object) {\n    const json = this.json;\n    const options = this.options;\n    const nodeMap = this.nodeMap;\n    if (!json.nodes) json.nodes = [];\n    const nodeDef = {};\n    if (options.trs) {\n      const rotation = object.quaternion.toArray();\n      const position = object.position.toArray();\n      const scale = object.scale.toArray();\n      if (!equalArray(rotation, [0, 0, 0, 1])) {\n        nodeDef.rotation = rotation;\n      }\n      if (!equalArray(position, [0, 0, 0])) {\n        nodeDef.translation = position;\n      }\n      if (!equalArray(scale, [1, 1, 1])) {\n        nodeDef.scale = scale;\n      }\n    } else {\n      if (object.matrixAutoUpdate) {\n        object.updateMatrix();\n      }\n      if (isIdentityMatrix(object.matrix) === false) {\n        nodeDef.matrix = object.matrix.elements;\n      }\n    }\n    if (object.name !== \"\") nodeDef.name = String(object.name);\n    this.serializeUserData(object, nodeDef);\n    if (object.isMesh || object.isLine || object.isPoints) {\n      const meshIndex = this.processMesh(object);\n      if (meshIndex !== null) nodeDef.mesh = meshIndex;\n    } else if (object.isCamera) {\n      nodeDef.camera = this.processCamera(object);\n    }\n    if (object.isSkinnedMesh) this.skins.push(object);\n    if (object.children.length > 0) {\n      const children = [];\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        const child = object.children[i];\n        if (child.visible || options.onlyVisible === false) {\n          const nodeIndex2 = this.processNode(child);\n          if (nodeIndex2 !== null) children.push(nodeIndex2);\n        }\n      }\n      if (children.length > 0) nodeDef.children = children;\n    }\n    this._invokeAll(function (ext) {\n      ext.writeNode && ext.writeNode(object, nodeDef);\n    });\n    const nodeIndex = json.nodes.push(nodeDef) - 1;\n    nodeMap.set(object, nodeIndex);\n    return nodeIndex;\n  }\n  /**\n   * Process Scene\n   * @param  {Scene} node Scene to process\n   */\n  processScene(scene) {\n    const json = this.json;\n    const options = this.options;\n    if (!json.scenes) {\n      json.scenes = [];\n      json.scene = 0;\n    }\n    const sceneDef = {};\n    if (scene.name !== \"\") sceneDef.name = scene.name;\n    json.scenes.push(sceneDef);\n    const nodes = [];\n    for (let i = 0, l = scene.children.length; i < l; i++) {\n      const child = scene.children[i];\n      if (child.visible || options.onlyVisible === false) {\n        const nodeIndex = this.processNode(child);\n        if (nodeIndex !== null) nodes.push(nodeIndex);\n      }\n    }\n    if (nodes.length > 0) sceneDef.nodes = nodes;\n    this.serializeUserData(scene, sceneDef);\n  }\n  /**\n   * Creates a Scene to hold a list of objects and parse it\n   * @param  {Array} objects List of objects to process\n   */\n  processObjects(objects) {\n    const scene = new Scene();\n    scene.name = \"AuxScene\";\n    for (let i = 0; i < objects.length; i++) {\n      scene.children.push(objects[i]);\n    }\n    this.processScene(scene);\n  }\n  /**\n   * @param {THREE.Object3D|Array<THREE.Object3D>} input\n   */\n  processInput(input) {\n    const options = this.options;\n    input = input instanceof Array ? input : [input];\n    this._invokeAll(function (ext) {\n      ext.beforeParse && ext.beforeParse(input);\n    });\n    const objectsWithoutScene = [];\n    for (let i = 0; i < input.length; i++) {\n      if (input[i] instanceof Scene) {\n        this.processScene(input[i]);\n      } else {\n        objectsWithoutScene.push(input[i]);\n      }\n    }\n    if (objectsWithoutScene.length > 0) this.processObjects(objectsWithoutScene);\n    for (let i = 0; i < this.skins.length; ++i) {\n      this.processSkin(this.skins[i]);\n    }\n    for (let i = 0; i < options.animations.length; ++i) {\n      this.processAnimation(options.animations[i], input[0]);\n    }\n    this._invokeAll(function (ext) {\n      ext.afterParse && ext.afterParse(input);\n    });\n  }\n  _invokeAll(func) {\n    for (let i = 0, il = this.plugins.length; i < il; i++) {\n      func(this.plugins[i]);\n    }\n  }\n}\nclass GLTFLightExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_lights_punctual\";\n  }\n  writeNode(light, nodeDef) {\n    if (!light.isLight) return;\n    if (!light.isDirectionalLight && !light.isPointLight && !light.isSpotLight) {\n      console.warn(\"THREE.GLTFExporter: Only directional, point, and spot lights are supported.\", light);\n      return;\n    }\n    const writer = this.writer;\n    const json = writer.json;\n    const extensionsUsed = writer.extensionsUsed;\n    const lightDef = {};\n    if (light.name) lightDef.name = light.name;\n    lightDef.color = light.color.toArray();\n    lightDef.intensity = light.intensity;\n    if (light.isDirectionalLight) {\n      lightDef.type = \"directional\";\n    } else if (light.isPointLight) {\n      lightDef.type = \"point\";\n      if (light.distance > 0) lightDef.range = light.distance;\n    } else if (light.isSpotLight) {\n      lightDef.type = \"spot\";\n      if (light.distance > 0) lightDef.range = light.distance;\n      lightDef.spot = {};\n      lightDef.spot.innerConeAngle = (light.penumbra - 1) * light.angle * -1;\n      lightDef.spot.outerConeAngle = light.angle;\n    }\n    if (light.decay !== void 0 && light.decay !== 2) {\n      console.warn(\"THREE.GLTFExporter: Light decay may be lost. glTF is physically-based, and expects light.decay=2.\");\n    }\n    if (light.target && (light.target.parent !== light || light.target.position.x !== 0 || light.target.position.y !== 0 || light.target.position.z !== -1)) {\n      console.warn(\"THREE.GLTFExporter: Light direction may be lost. For best results, make light.target a child of the light with position 0,0,-1.\");\n    }\n    if (!extensionsUsed[this.name]) {\n      json.extensions = json.extensions || {};\n      json.extensions[this.name] = {\n        lights: []\n      };\n      extensionsUsed[this.name] = true;\n    }\n    const lights = json.extensions[this.name].lights;\n    lights.push(lightDef);\n    nodeDef.extensions = nodeDef.extensions || {};\n    nodeDef.extensions[this.name] = {\n      light: lights.length - 1\n    };\n  }\n}\nclass GLTFMaterialsUnlitExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_unlit\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshBasicMaterial) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = {};\n    extensionsUsed[this.name] = true;\n    materialDef.pbrMetallicRoughness.metallicFactor = 0;\n    materialDef.pbrMetallicRoughness.roughnessFactor = 0.9;\n  }\n}\nclass GLTFMaterialsClearcoatExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_clearcoat\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.clearcoat === 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.clearcoatFactor = material.clearcoat;\n    if (material.clearcoatMap) {\n      const clearcoatMapDef = {\n        index: writer.processTexture(material.clearcoatMap),\n        texCoord: material.clearcoatMap.channel\n      };\n      writer.applyTextureTransform(clearcoatMapDef, material.clearcoatMap);\n      extensionDef.clearcoatTexture = clearcoatMapDef;\n    }\n    extensionDef.clearcoatRoughnessFactor = material.clearcoatRoughness;\n    if (material.clearcoatRoughnessMap) {\n      const clearcoatRoughnessMapDef = {\n        index: writer.processTexture(material.clearcoatRoughnessMap),\n        texCoord: material.clearcoatRoughnessMap.channel\n      };\n      writer.applyTextureTransform(clearcoatRoughnessMapDef, material.clearcoatRoughnessMap);\n      extensionDef.clearcoatRoughnessTexture = clearcoatRoughnessMapDef;\n    }\n    if (material.clearcoatNormalMap) {\n      const clearcoatNormalMapDef = {\n        index: writer.processTexture(material.clearcoatNormalMap),\n        texCoord: material.clearcoatNormalMap.channel\n      };\n      writer.applyTextureTransform(clearcoatNormalMapDef, material.clearcoatNormalMap);\n      extensionDef.clearcoatNormalTexture = clearcoatNormalMapDef;\n    }\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsIridescenceExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_iridescence\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.iridescence === 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.iridescenceFactor = material.iridescence;\n    if (material.iridescenceMap) {\n      const iridescenceMapDef = {\n        index: writer.processTexture(material.iridescenceMap),\n        texCoord: material.iridescenceMap.channel\n      };\n      writer.applyTextureTransform(iridescenceMapDef, material.iridescenceMap);\n      extensionDef.iridescenceTexture = iridescenceMapDef;\n    }\n    extensionDef.iridescenceIor = material.iridescenceIOR;\n    extensionDef.iridescenceThicknessMinimum = material.iridescenceThicknessRange[0];\n    extensionDef.iridescenceThicknessMaximum = material.iridescenceThicknessRange[1];\n    if (material.iridescenceThicknessMap) {\n      const iridescenceThicknessMapDef = {\n        index: writer.processTexture(material.iridescenceThicknessMap),\n        texCoord: material.iridescenceThicknessMap.channel\n      };\n      writer.applyTextureTransform(iridescenceThicknessMapDef, material.iridescenceThicknessMap);\n      extensionDef.iridescenceThicknessTexture = iridescenceThicknessMapDef;\n    }\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsTransmissionExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_transmission\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.transmissionFactor = material.transmission;\n    if (material.transmissionMap) {\n      const transmissionMapDef = {\n        index: writer.processTexture(material.transmissionMap),\n        texCoord: material.transmissionMap.channel\n      };\n      writer.applyTextureTransform(transmissionMapDef, material.transmissionMap);\n      extensionDef.transmissionTexture = transmissionMapDef;\n    }\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsVolumeExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_volume\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.thicknessFactor = material.thickness;\n    if (material.thicknessMap) {\n      const thicknessMapDef = {\n        index: writer.processTexture(material.thicknessMap),\n        texCoord: material.thicknessMap.channel\n      };\n      writer.applyTextureTransform(thicknessMapDef, material.thicknessMap);\n      extensionDef.thicknessTexture = thicknessMapDef;\n    }\n    extensionDef.attenuationDistance = material.attenuationDistance;\n    extensionDef.attenuationColor = material.attenuationColor.toArray();\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsIorExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_ior\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.ior === 1.5) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.ior = material.ior;\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsSpecularExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_specular\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.specularIntensity === 1 && material.specularColor.equals(DEFAULT_SPECULAR_COLOR) && !material.specularIntensityMap && !material.specularColorTexture) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    if (material.specularIntensityMap) {\n      const specularIntensityMapDef = {\n        index: writer.processTexture(material.specularIntensityMap),\n        texCoord: material.specularIntensityMap.channel\n      };\n      writer.applyTextureTransform(specularIntensityMapDef, material.specularIntensityMap);\n      extensionDef.specularTexture = specularIntensityMapDef;\n    }\n    if (material.specularColorMap) {\n      const specularColorMapDef = {\n        index: writer.processTexture(material.specularColorMap),\n        texCoord: material.specularColorMap.channel\n      };\n      writer.applyTextureTransform(specularColorMapDef, material.specularColorMap);\n      extensionDef.specularColorTexture = specularColorMapDef;\n    }\n    extensionDef.specularFactor = material.specularIntensity;\n    extensionDef.specularColorFactor = material.specularColor.toArray();\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsSheenExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_sheen\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.sheen == 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    if (material.sheenRoughnessMap) {\n      const sheenRoughnessMapDef = {\n        index: writer.processTexture(material.sheenRoughnessMap),\n        texCoord: material.sheenRoughnessMap.channel\n      };\n      writer.applyTextureTransform(sheenRoughnessMapDef, material.sheenRoughnessMap);\n      extensionDef.sheenRoughnessTexture = sheenRoughnessMapDef;\n    }\n    if (material.sheenColorMap) {\n      const sheenColorMapDef = {\n        index: writer.processTexture(material.sheenColorMap),\n        texCoord: material.sheenColorMap.channel\n      };\n      writer.applyTextureTransform(sheenColorMapDef, material.sheenColorMap);\n      extensionDef.sheenColorTexture = sheenColorMapDef;\n    }\n    extensionDef.sheenRoughnessFactor = material.sheenRoughness;\n    extensionDef.sheenColorFactor = material.sheenColor.toArray();\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_anisotropy\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.anisotropy == 0) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    if (material.anisotropyMap) {\n      const anisotropyMapDef = {\n        index: writer.processTexture(material.anisotropyMap)\n      };\n      writer.applyTextureTransform(anisotropyMapDef, material.anisotropyMap);\n      extensionDef.anisotropyTexture = anisotropyMapDef;\n    }\n    extensionDef.anisotropyStrength = material.anisotropy;\n    extensionDef.anisotropyRotation = material.anisotropyRotation;\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(writer) {\n    this.writer = writer;\n    this.name = \"KHR_materials_emissive_strength\";\n  }\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshStandardMaterial || material.emissiveIntensity === 1) return;\n    const writer = this.writer;\n    const extensionsUsed = writer.extensionsUsed;\n    const extensionDef = {};\n    extensionDef.emissiveStrength = material.emissiveIntensity;\n    materialDef.extensions = materialDef.extensions || {};\n    materialDef.extensions[this.name] = extensionDef;\n    extensionsUsed[this.name] = true;\n  }\n}\nexport { GLTFExporter };", "map": {"version": 3, "names": ["readAsDataURL", "blob", "buffer", "arrayBuffer", "data", "btoa", "String", "fromCharCode", "Uint8Array", "type", "_renderer", "fullscreenQuadGeometry", "fullscreenQuadMaterial", "fullscreenQuad", "decompress", "texture", "maxTextureSize", "Infinity", "renderer", "PlaneGeometry", "ShaderMaterial", "uniforms", "blitTexture", "Uniform", "vertexShader", "fragmentShader", "value", "defines", "IS_SRGB", "colorSpace", "encoding", "needsUpdate", "<PERSON><PERSON>", "frustrumCulled", "_camera", "PerspectiveCamera", "_scene", "Scene", "add", "WebGLRenderer", "antialias", "setSize", "Math", "min", "image", "width", "height", "clear", "render", "readableTexture", "Texture", "dom<PERSON>lement", "minFilter", "magFilter", "wrapS", "wrapT", "name", "dispose", "KHR_mesh_quantization_ExtraAttrTypes", "POSITION", "NORMAL", "TANGENT", "TEXCOORD", "GLTFExporter", "GLTFExporter2", "constructor", "pluginCallbacks", "register", "writer", "GLTFLightExtension", "GLTFMaterialsUnlitExtension", "GLTFMaterialsTransmissionExtension", "GLTFMaterialsVolumeExtension", "GLTFMaterialsIorExtension", "GLTFMaterialsSpecularExtension", "GLTFMaterialsClearcoatExtension", "GLTFMaterialsIridescenceExtension", "GLTFMaterialsSheenExtension", "GLTFMaterialsAnisotropyExtension", "GLTFMaterialsEmissiveStrengthExtension", "callback", "indexOf", "push", "unregister", "splice", "parse", "input", "onDone", "onError", "options", "GLTFWriter", "plugins", "i", "il", "length", "setPlugins", "write", "catch", "parseAsync", "scope", "Promise", "resolve", "reject", "__publicField", "insertKeyframe", "track", "time", "tolerance", "valueSize", "getValueSize", "times", "TimeBufferType", "values", "ValueBufferType", "interpolant", "createInterpolant", "index", "abs", "set", "evaluate", "slice", "mergeMorphTargetTracks", "clip", "root", "tracks", "mergedTracks", "sourceTracks", "sourceTrack", "sourceTrackBinding", "PropertyBinding", "parseTrackName", "sourceTrackNode", "findNode", "nodeName", "propertyName", "propertyIndex", "InterpolantFactoryMethodDiscrete", "InterpolantFactoryMethodLinear", "isInterpolantFactoryMethodGLTFCubicSpline", "Error", "console", "warn", "clone", "setInterpolation", "InterpolateLinear", "targetCount", "morphTargetInfluences", "targetIndex", "morphTargetDictionary", "mergedTrack", "uuid", "j", "sourceInterpolant", "keyframeIndex", "WEBGL_CONSTANTS", "POINTS", "LINES", "LINE_LOOP", "LINE_STRIP", "TRIANGLES", "TRIANGLE_STRIP", "TRIANGLE_FAN", "BYTE", "UNSIGNED_BYTE", "SHORT", "UNSIGNED_SHORT", "INT", "UNSIGNED_INT", "FLOAT", "ARRAY_BUFFER", "ELEMENT_ARRAY_BUFFER", "NEAREST", "LINEAR", "NEAREST_MIPMAP_NEAREST", "LINEAR_MIPMAP_NEAREST", "NEAREST_MIPMAP_LINEAR", "LINEAR_MIPMAP_LINEAR", "CLAMP_TO_EDGE", "MIRRORED_REPEAT", "REPEAT", "KHR_MESH_QUANTIZATION", "THREE_TO_WEBGL", "NearestFilter", "NearestMipmapNearestFilter", "NearestMipmapLinearFilter", "LinearFilter", "LinearMipmapNearestFilter", "LinearMipmapLinearFilter", "ClampToEdgeWrapping", "RepeatWrapping", "MirroredRepeatWrapping", "PATH_PROPERTIES", "scale", "position", "quaternion", "DEFAULT_SPECULAR_COLOR", "Color", "GLB_HEADER_BYTES", "GLB_HEADER_MAGIC", "GLB_VERSION", "GLB_CHUNK_PREFIX_BYTES", "GLB_CHUNK_TYPE_JSON", "GLB_CHUNK_TYPE_BIN", "equalArray", "array1", "array2", "every", "element", "stringToArrayBuffer", "text", "TextEncoder", "encode", "isIdentityMatrix", "matrix", "elements", "getMinMax", "attribute", "start", "count", "output", "Array", "itemSize", "fill", "Number", "POSITIVE_INFINITY", "max", "NEGATIVE_INFINITY", "a", "array", "getX", "getY", "getZ", "getW", "normalized", "MathUtils", "normalize", "getPaddedBufferSize", "bufferSize", "ceil", "getPaddedArrayBuffer", "paddingByte", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "get<PERSON>anvas", "document", "OffscreenCanvas", "createElement", "getToBlobPromise", "canvas", "mimeType", "toBlob", "quality", "convertToBlob", "pending", "buffers", "byteOffset", "nodeMap", "Map", "skins", "extensionsUsed", "extensionsRequired", "uids", "uid", "json", "asset", "version", "generator", "cache", "meshes", "attributes", "attributesNormalized", "materials", "textures", "images", "Object", "assign", "binary", "trs", "onlyVisible", "animations", "includeCustomExtensions", "processInput", "all", "Blob", "extensionsUsedList", "keys", "extensionsRequiredList", "size", "then", "result", "binaryChunk", "binaryChunkPrefix", "DataView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setUint32", "jsonChunk", "JSON", "stringify", "jsonChunkPrefix", "header", "headerView", "totalByteLength", "glbBlob", "uri", "serializeUserData", "object", "objectDef", "userData", "gltfExtensions", "extensions", "extensionName", "extras", "error", "message", "getUID", "isRelativeCopy", "has", "uids2", "get", "isNormalizedNormalAttribute", "normal", "v", "Vector3", "fromBufferAttribute", "createNormalizedNormalAttribute", "x", "y", "z", "setX", "setXYZ", "applyTextureTransform", "mapDef", "didTransform", "transformDef", "offset", "toArray", "rotation", "repeat", "buildMetalRoughTexture", "metalnessMap", "roughnessMap", "getEncodingConversion", "map", "SRGBToLinear", "c", "pow", "LinearToLinear", "CompressedTexture", "metalness", "roughness", "context", "getContext", "fillStyle", "fillRect", "composite", "getImageData", "drawImage", "convert", "putImageData", "reference", "source", "channel", "processBuffer", "processBufferView", "componentType", "target", "bufferViews", "componentSize", "byteStride", "dataView", "setFloat32", "setInt32", "setInt16", "setUint16", "setInt8", "setUint8", "bufferViewDef", "id", "processBufferViewImage", "processAccessor", "geometry", "types", "Float32Array", "Int32Array", "Uint32Array", "Int16Array", "Uint16Array", "Int8Array", "minMax", "bufferViewTarget", "bufferView", "accessorDef", "accessors", "processImage", "format", "flipY", "cachedImages", "key", "toString", "imageDef", "ctx", "translate", "RGBAFormat", "Uint8ClampedArray", "ImageData", "bufferViewIndex", "toDataURL", "processSampler", "samplers", "samplerDef", "processTexture", "textureDef", "sampler", "_invokeAll", "ext", "writeTexture", "processMaterial", "material", "isShaderMaterial", "materialDef", "pbrMetallicRoughness", "isMeshStandardMaterial", "isMeshBasicMaterial", "color", "concat", "opacity", "baseColorFactor", "metallicFactor", "roughnessFactor", "metalRoughTexture", "metalRoughMapDef", "metallicRoughnessTexture", "baseColorMapDef", "texCoord", "baseColorTexture", "emissive", "maxEmissiveComponent", "r", "g", "b", "emissiveFactor", "emissiveMap", "emissiveMapDef", "emissiveTexture", "normalMap", "normalMapDef", "normalScale", "normalTexture", "aoMap", "occlusionMapDef", "aoMapIntensity", "strength", "occlusionTexture", "transparent", "alphaMode", "alphaTest", "<PERSON><PERSON><PERSON><PERSON>", "side", "DoubleSide", "doubleSided", "writeMaterial", "<PERSON><PERSON>esh", "mesh", "meshCacheKeyParts", "isArray", "l", "meshCache<PERSON>ey", "join", "mode", "isLineSegments", "isLineLoop", "isLine", "isPoints", "wireframe", "meshDef", "primitives", "targets", "nameConversion", "uv", "uv1", "uv2", "uv3", "skinWeight", "skinIndex", "originalNormal", "getAttribute", "setAttribute", "modifiedAttribute", "attributeName", "toUpperCase", "validVertexAttributes", "test", "BufferAttribute", "accessor", "startsWith", "detectMeshQuantization", "weights", "targetNames", "reverseDictionary", "warned", "morphAttributes", "gltfAttributeName", "baseAttribute", "relativeAttribute", "morphTargetsRelative", "jl", "setY", "setZ", "setW", "isMultiMaterial", "groups", "materialIndex", "primitive", "cache<PERSON>ey", "indices", "<PERSON><PERSON><PERSON>", "attrType", "attrNamePrefix", "split", "includes", "processCamera", "camera", "cameras", "<PERSON><PERSON><PERSON><PERSON>", "isOrthographicCamera", "cameraDef", "orthographic", "xmag", "right", "ymag", "top", "zfar", "far", "znear", "near", "perspective", "aspectRatio", "aspect", "yfov", "degToRad", "fov", "processAnimation", "Utils", "channels", "trackBinding", "trackNode", "trackProperty", "objectName", "isSkinnedMesh", "skeleton", "getBoneByName", "objectIndex", "inputItemSize", "outputItemSize", "interpolation", "getInterpolation", "InterpolateDiscrete", "node", "path", "processSkin", "nodes", "rootJoint", "bones", "joints", "inverseBindMatrices", "temporaryBoneInverse", "Matrix4", "copy", "boneInverses", "multiply", "bindMatrix", "skin", "processNode", "nodeDef", "translation", "matrixAutoUpdate", "updateMatrix", "<PERSON><PERSON><PERSON>", "meshIndex", "isCamera", "children", "child", "visible", "nodeIndex2", "writeNode", "nodeIndex", "processScene", "scene", "scenes", "sceneDef", "processObjects", "objects", "beforeParse", "objectsWithoutScene", "after<PERSON><PERSON>e", "func", "light", "isLight", "isDirectionalLight", "isPointLight", "isSpotLight", "lightDef", "intensity", "distance", "range", "spot", "innerConeAngle", "penumbra", "angle", "outerConeAngle", "decay", "parent", "lights", "isMeshPhysicalMaterial", "clearcoat", "extensionDef", "clearcoatFactor", "clearcoatMap", "clearcoatMapDef", "clearcoatTexture", "clearcoatRoughnessFactor", "clearcoatRoughness", "clearcoatRoughnessMap", "clearcoatRoughnessMapDef", "clearcoatRoughnessTexture", "clearcoatNormalMap", "clearcoatNormalMapDef", "clearcoatNormalTexture", "iridescence", "iridescenceFactor", "iridescenceMap", "iridescenceMapDef", "iridescenceTexture", "iridescenceIor", "iridescenceIOR", "iridescenceThicknessMinimum", "iridescenceThicknessRange", "iridescenceThicknessMaximum", "iridescenceThicknessMap", "iridescenceThicknessMapDef", "iridescenceThicknessTexture", "transmission", "transmissionFactor", "transmissionMap", "transmissionMapDef", "transmissionTexture", "thicknessFactor", "thickness", "thicknessMap", "thicknessMapDef", "thicknessTexture", "attenuationDistance", "attenuationColor", "ior", "specularIntensity", "specularColor", "equals", "specularIntensityMap", "specularColorTexture", "specularIntensityMapDef", "specularTexture", "specularColorMap", "specularColorMapDef", "specularFactor", "specularColorFactor", "sheen", "sheenRoughnessMap", "sheenRoughnessMapDef", "sheenRoughnessTexture", "sheenColorMap", "sheenColorMapDef", "sheenColorTexture", "sheenRoughnessFactor", "sheenRoughness", "sheenColorFactor", "sheenColor", "anisotropy", "anisotropyMap", "anisotropyMapDef", "anisotropyTexture", "anisotropyStrength", "anisotropyRotation", "emissiveIntensity", "emissiveStrength"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/exporters/GLTFExporter.js"], "sourcesContent": ["import {\n  RE<PERSON><PERSON><PERSON>,\n  <PERSON>ufferAttri<PERSON>e,\n  ClampToEdgeWrapping,\n  Color,\n  DoubleSide,\n  InterpolateDiscrete,\n  InterpolateLinear,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  LinearMipmapNearestFilter,\n  MathUtils,\n  Matrix4,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  NearestMipmapLinearFilter,\n  NearestMipmapNearestFilter,\n  PropertyBinding,\n  RGBAFormat,\n  RepeatWrapping,\n  Scene,\n  Texture,\n  CompressedTexture,\n  Vector3,\n  PlaneGeometry,\n  ShaderMaterial,\n  Uniform,\n  Mesh,\n  PerspectiveCamera,\n  WebGLRenderer,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nasync function readAsDataURL(blob) {\n  const buffer = await blob.arrayBuffer()\n  const data = btoa(String.fromCharCode(...new Uint8Array(buffer)))\n  return `data:${blob.type || ''};base64,${data}`\n}\n\nlet _renderer\nlet fullscreenQuadGeometry\nlet fullscreenQuadMaterial\nlet fullscreenQuad\n\nfunction decompress(texture, maxTextureSize = Infinity, renderer = null) {\n  if (!fullscreenQuadGeometry) fullscreenQuadGeometry = new PlaneGeometry(2, 2, 1, 1)\n  if (!fullscreenQuadMaterial)\n    fullscreenQuadMaterial = new ShaderMaterial({\n      uniforms: { blitTexture: new Uniform(texture) },\n      vertexShader: /* glsl */ `\n        varying vec2 vUv;\n        void main(){\n            vUv = uv;\n            gl_Position = vec4(position.xy * 1.0,0.,.999999);\n        }\n      `,\n      fragmentShader: /* glsl */ `\n          uniform sampler2D blitTexture; \n          varying vec2 vUv;\n\n          void main(){ \n              gl_FragColor = vec4(vUv.xy, 0, 1);\n              \n              #ifdef IS_SRGB\n              gl_FragColor = LinearTosRGB( texture2D( blitTexture, vUv) );\n              #else\n              gl_FragColor = texture2D( blitTexture, vUv);\n              #endif\n          }\n      `,\n    })\n\n  fullscreenQuadMaterial.uniforms.blitTexture.value = texture\n  fullscreenQuadMaterial.defines.IS_SRGB =\n    'colorSpace' in texture ? texture.colorSpace === 'srgb' : texture.encoding === 3001\n  fullscreenQuadMaterial.needsUpdate = true\n\n  if (!fullscreenQuad) {\n    fullscreenQuad = new Mesh(fullscreenQuadGeometry, fullscreenQuadMaterial)\n    fullscreenQuad.frustrumCulled = false\n  }\n\n  const _camera = new PerspectiveCamera()\n  const _scene = new Scene()\n  _scene.add(fullscreenQuad)\n\n  if (!renderer) {\n    renderer = _renderer = new WebGLRenderer({ antialias: false })\n  }\n\n  renderer.setSize(Math.min(texture.image.width, maxTextureSize), Math.min(texture.image.height, maxTextureSize))\n  renderer.clear()\n  renderer.render(_scene, _camera)\n\n  const readableTexture = new Texture(renderer.domElement)\n\n  readableTexture.minFilter = texture.minFilter\n  readableTexture.magFilter = texture.magFilter\n  readableTexture.wrapS = texture.wrapS\n  readableTexture.wrapT = texture.wrapT\n  readableTexture.name = texture.name\n\n  if (_renderer) {\n    _renderer.dispose()\n    _renderer = null\n  }\n\n  return readableTexture\n}\n\n/**\n * The KHR_mesh_quantization extension allows these extra attribute component types\n *\n * @see https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md#extending-mesh-attributes\n */\nconst KHR_mesh_quantization_ExtraAttrTypes = {\n  POSITION: [\n    'byte',\n    'byte normalized',\n    'unsigned byte',\n    'unsigned byte normalized',\n    'short',\n    'short normalized',\n    'unsigned short',\n    'unsigned short normalized',\n  ],\n  NORMAL: ['byte normalized', 'short normalized'],\n  TANGENT: ['byte normalized', 'short normalized'],\n  TEXCOORD: ['byte', 'byte normalized', 'unsigned byte', 'short', 'short normalized', 'unsigned short'],\n}\n\nconst GLTFExporter = /* @__PURE__ */ (() => {\n  class GLTFExporter {\n    /**\n     * Static utility functions\n     */\n    static Utils = {\n      insertKeyframe: function (track, time) {\n        const tolerance = 0.001 // 1ms\n        const valueSize = track.getValueSize()\n\n        const times = new track.TimeBufferType(track.times.length + 1)\n        const values = new track.ValueBufferType(track.values.length + valueSize)\n        const interpolant = track.createInterpolant(new track.ValueBufferType(valueSize))\n\n        let index\n\n        if (track.times.length === 0) {\n          times[0] = time\n\n          for (let i = 0; i < valueSize; i++) {\n            values[i] = 0\n          }\n\n          index = 0\n        } else if (time < track.times[0]) {\n          if (Math.abs(track.times[0] - time) < tolerance) return 0\n\n          times[0] = time\n          times.set(track.times, 1)\n\n          values.set(interpolant.evaluate(time), 0)\n          values.set(track.values, valueSize)\n\n          index = 0\n        } else if (time > track.times[track.times.length - 1]) {\n          if (Math.abs(track.times[track.times.length - 1] - time) < tolerance) {\n            return track.times.length - 1\n          }\n\n          times[times.length - 1] = time\n          times.set(track.times, 0)\n\n          values.set(track.values, 0)\n          values.set(interpolant.evaluate(time), track.values.length)\n\n          index = times.length - 1\n        } else {\n          for (let i = 0; i < track.times.length; i++) {\n            if (Math.abs(track.times[i] - time) < tolerance) return i\n\n            if (track.times[i] < time && track.times[i + 1] > time) {\n              times.set(track.times.slice(0, i + 1), 0)\n              times[i + 1] = time\n              times.set(track.times.slice(i + 1), i + 2)\n\n              values.set(track.values.slice(0, (i + 1) * valueSize), 0)\n              values.set(interpolant.evaluate(time), (i + 1) * valueSize)\n              values.set(track.values.slice((i + 1) * valueSize), (i + 2) * valueSize)\n\n              index = i + 1\n\n              break\n            }\n          }\n        }\n\n        track.times = times\n        track.values = values\n\n        return index\n      },\n\n      mergeMorphTargetTracks: function (clip, root) {\n        const tracks = []\n        const mergedTracks = {}\n        const sourceTracks = clip.tracks\n\n        for (let i = 0; i < sourceTracks.length; ++i) {\n          let sourceTrack = sourceTracks[i]\n          const sourceTrackBinding = PropertyBinding.parseTrackName(sourceTrack.name)\n          const sourceTrackNode = PropertyBinding.findNode(root, sourceTrackBinding.nodeName)\n\n          if (\n            sourceTrackBinding.propertyName !== 'morphTargetInfluences' ||\n            sourceTrackBinding.propertyIndex === undefined\n          ) {\n            // Tracks that don't affect morph targets, or that affect all morph targets together, can be left as-is.\n            tracks.push(sourceTrack)\n            continue\n          }\n\n          if (\n            sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodDiscrete &&\n            sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodLinear\n          ) {\n            if (sourceTrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline) {\n              // This should never happen, because glTF morph target animations\n              // affect all targets already.\n              throw new Error('THREE.GLTFExporter: Cannot merge tracks with glTF CUBICSPLINE interpolation.')\n            }\n\n            console.warn('THREE.GLTFExporter: Morph target interpolation mode not yet supported. Using LINEAR instead.')\n\n            sourceTrack = sourceTrack.clone()\n            sourceTrack.setInterpolation(InterpolateLinear)\n          }\n\n          const targetCount = sourceTrackNode.morphTargetInfluences.length\n          const targetIndex = sourceTrackNode.morphTargetDictionary[sourceTrackBinding.propertyIndex]\n\n          if (targetIndex === undefined) {\n            throw new Error('THREE.GLTFExporter: Morph target name not found: ' + sourceTrackBinding.propertyIndex)\n          }\n\n          let mergedTrack\n\n          // If this is the first time we've seen this object, create a new\n          // track to store merged keyframe data for each morph target.\n          if (mergedTracks[sourceTrackNode.uuid] === undefined) {\n            mergedTrack = sourceTrack.clone()\n\n            const values = new mergedTrack.ValueBufferType(targetCount * mergedTrack.times.length)\n\n            for (let j = 0; j < mergedTrack.times.length; j++) {\n              values[j * targetCount + targetIndex] = mergedTrack.values[j]\n            }\n\n            // We need to take into consideration the intended target node\n            // of our original un-merged morphTarget animation.\n            mergedTrack.name = (sourceTrackBinding.nodeName || '') + '.morphTargetInfluences'\n            mergedTrack.values = values\n\n            mergedTracks[sourceTrackNode.uuid] = mergedTrack\n            tracks.push(mergedTrack)\n\n            continue\n          }\n\n          const sourceInterpolant = sourceTrack.createInterpolant(new sourceTrack.ValueBufferType(1))\n\n          mergedTrack = mergedTracks[sourceTrackNode.uuid]\n\n          // For every existing keyframe of the merged track, write a (possibly\n          // interpolated) value from the source track.\n          for (let j = 0; j < mergedTrack.times.length; j++) {\n            mergedTrack.values[j * targetCount + targetIndex] = sourceInterpolant.evaluate(mergedTrack.times[j])\n          }\n\n          // For every existing keyframe of the source track, write a (possibly\n          // new) keyframe to the merged track. Values from the previous loop may\n          // be written again, but keyframes are de-duplicated.\n          for (let j = 0; j < sourceTrack.times.length; j++) {\n            const keyframeIndex = this.insertKeyframe(mergedTrack, sourceTrack.times[j])\n            mergedTrack.values[keyframeIndex * targetCount + targetIndex] = sourceTrack.values[j]\n          }\n        }\n\n        clip.tracks = tracks\n\n        return clip\n      },\n    }\n\n    constructor() {\n      this.pluginCallbacks = []\n\n      this.register(function (writer) {\n        return new GLTFLightExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsUnlitExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsTransmissionExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsVolumeExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsIorExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsSpecularExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsClearcoatExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsIridescenceExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsSheenExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsAnisotropyExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsEmissiveStrengthExtension(writer)\n      })\n    }\n\n    register(callback) {\n      if (this.pluginCallbacks.indexOf(callback) === -1) {\n        this.pluginCallbacks.push(callback)\n      }\n\n      return this\n    }\n\n    unregister(callback) {\n      if (this.pluginCallbacks.indexOf(callback) !== -1) {\n        this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1)\n      }\n\n      return this\n    }\n\n    /**\n     * Parse scenes and generate GLTF output\n     * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n     * @param  {Function} onDone  Callback on completed\n     * @param  {Function} onError  Callback on errors\n     * @param  {Object} options options\n     */\n    parse(input, onDone, onError, options) {\n      const writer = new GLTFWriter()\n      const plugins = []\n\n      for (let i = 0, il = this.pluginCallbacks.length; i < il; i++) {\n        plugins.push(this.pluginCallbacks[i](writer))\n      }\n\n      writer.setPlugins(plugins)\n      writer.write(input, onDone, options).catch(onError)\n    }\n\n    parseAsync(input, options) {\n      const scope = this\n\n      return new Promise(function (resolve, reject) {\n        scope.parse(input, resolve, reject, options)\n      })\n    }\n  }\n\n  return GLTFExporter\n})()\n\n//------------------------------------------------------------------------------\n// Constants\n//------------------------------------------------------------------------------\n\nconst WEBGL_CONSTANTS = {\n  POINTS: 0x0000,\n  LINES: 0x0001,\n  LINE_LOOP: 0x0002,\n  LINE_STRIP: 0x0003,\n  TRIANGLES: 0x0004,\n  TRIANGLE_STRIP: 0x0005,\n  TRIANGLE_FAN: 0x0006,\n\n  BYTE: 0x1400,\n  UNSIGNED_BYTE: 0x1401,\n  SHORT: 0x1402,\n  UNSIGNED_SHORT: 0x1403,\n  INT: 0x1404,\n  UNSIGNED_INT: 0x1405,\n  FLOAT: 0x1406,\n\n  ARRAY_BUFFER: 0x8892,\n  ELEMENT_ARRAY_BUFFER: 0x8893,\n\n  NEAREST: 0x2600,\n  LINEAR: 0x2601,\n  NEAREST_MIPMAP_NEAREST: 0x2700,\n  LINEAR_MIPMAP_NEAREST: 0x2701,\n  NEAREST_MIPMAP_LINEAR: 0x2702,\n  LINEAR_MIPMAP_LINEAR: 0x2703,\n\n  CLAMP_TO_EDGE: 33071,\n  MIRRORED_REPEAT: 33648,\n  REPEAT: 10497,\n}\n\nconst KHR_MESH_QUANTIZATION = 'KHR_mesh_quantization'\n\nconst THREE_TO_WEBGL = {}\n\nTHREE_TO_WEBGL[NearestFilter] = WEBGL_CONSTANTS.NEAREST\nTHREE_TO_WEBGL[NearestMipmapNearestFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_NEAREST\nTHREE_TO_WEBGL[NearestMipmapLinearFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_LINEAR\nTHREE_TO_WEBGL[LinearFilter] = WEBGL_CONSTANTS.LINEAR\nTHREE_TO_WEBGL[LinearMipmapNearestFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_NEAREST\nTHREE_TO_WEBGL[LinearMipmapLinearFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_LINEAR\n\nTHREE_TO_WEBGL[ClampToEdgeWrapping] = WEBGL_CONSTANTS.CLAMP_TO_EDGE\nTHREE_TO_WEBGL[RepeatWrapping] = WEBGL_CONSTANTS.REPEAT\nTHREE_TO_WEBGL[MirroredRepeatWrapping] = WEBGL_CONSTANTS.MIRRORED_REPEAT\n\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  position: 'translation',\n  quaternion: 'rotation',\n  morphTargetInfluences: 'weights',\n}\n\nconst DEFAULT_SPECULAR_COLOR = /* @__PURE__ */ new Color()\n\n// GLB constants\n// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#glb-file-format-specification\n\nconst GLB_HEADER_BYTES = 12\nconst GLB_HEADER_MAGIC = 0x46546c67\nconst GLB_VERSION = 2\n\nconst GLB_CHUNK_PREFIX_BYTES = 8\nconst GLB_CHUNK_TYPE_JSON = 0x4e4f534a\nconst GLB_CHUNK_TYPE_BIN = 0x004e4942\n\n//------------------------------------------------------------------------------\n// Utility functions\n//------------------------------------------------------------------------------\n\n/**\n * Compare two arrays\n * @param  {Array} array1 Array 1 to compare\n * @param  {Array} array2 Array 2 to compare\n * @return {Boolean}        Returns true if both arrays are equal\n */\nfunction equalArray(array1, array2) {\n  return (\n    array1.length === array2.length &&\n    array1.every(function (element, index) {\n      return element === array2[index]\n    })\n  )\n}\n\n/**\n * Converts a string to an ArrayBuffer.\n * @param  {string} text\n * @return {ArrayBuffer}\n */\nfunction stringToArrayBuffer(text) {\n  return new TextEncoder().encode(text).buffer\n}\n\n/**\n * Is identity matrix\n *\n * @param {Matrix4} matrix\n * @returns {Boolean} Returns true, if parameter is identity matrix\n */\nfunction isIdentityMatrix(matrix) {\n  return equalArray(matrix.elements, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])\n}\n\n/**\n * Get the min and max vectors from the given attribute\n * @param  {BufferAttribute} attribute Attribute to find the min/max in range from start to start + count\n * @param  {Integer} start\n * @param  {Integer} count\n * @return {Object} Object containing the `min` and `max` values (As an array of attribute.itemSize components)\n */\nfunction getMinMax(attribute, start, count) {\n  const output = {\n    min: new Array(attribute.itemSize).fill(Number.POSITIVE_INFINITY),\n    max: new Array(attribute.itemSize).fill(Number.NEGATIVE_INFINITY),\n  }\n\n  for (let i = start; i < start + count; i++) {\n    for (let a = 0; a < attribute.itemSize; a++) {\n      let value\n\n      if (attribute.itemSize > 4) {\n        // no support for interleaved data for itemSize > 4\n\n        value = attribute.array[i * attribute.itemSize + a]\n      } else {\n        if (a === 0) value = attribute.getX(i)\n        else if (a === 1) value = attribute.getY(i)\n        else if (a === 2) value = attribute.getZ(i)\n        else if (a === 3) value = attribute.getW(i)\n\n        if (attribute.normalized === true) {\n          value = MathUtils.normalize(value, attribute.array)\n        }\n      }\n\n      output.min[a] = Math.min(output.min[a], value)\n      output.max[a] = Math.max(output.max[a], value)\n    }\n  }\n\n  return output\n}\n\n/**\n * Get the required size + padding for a buffer, rounded to the next 4-byte boundary.\n * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#data-alignment\n *\n * @param {Integer} bufferSize The size the original buffer.\n * @returns {Integer} new buffer size with required padding.\n *\n */\nfunction getPaddedBufferSize(bufferSize) {\n  return Math.ceil(bufferSize / 4) * 4\n}\n\n/**\n * Returns a buffer aligned to 4-byte boundary.\n *\n * @param {ArrayBuffer} arrayBuffer Buffer to pad\n * @param {Integer} paddingByte (Optional)\n * @returns {ArrayBuffer} The same buffer if it's already aligned to 4-byte boundary or a new buffer\n */\nfunction getPaddedArrayBuffer(arrayBuffer, paddingByte = 0) {\n  const paddedLength = getPaddedBufferSize(arrayBuffer.byteLength)\n\n  if (paddedLength !== arrayBuffer.byteLength) {\n    const array = new Uint8Array(paddedLength)\n    array.set(new Uint8Array(arrayBuffer))\n\n    if (paddingByte !== 0) {\n      for (let i = arrayBuffer.byteLength; i < paddedLength; i++) {\n        array[i] = paddingByte\n      }\n    }\n\n    return array.buffer\n  }\n\n  return arrayBuffer\n}\n\nfunction getCanvas() {\n  if (typeof document === 'undefined' && typeof OffscreenCanvas !== 'undefined') {\n    return new OffscreenCanvas(1, 1)\n  }\n\n  return document.createElement('canvas')\n}\n\nfunction getToBlobPromise(canvas, mimeType) {\n  if (canvas.toBlob !== undefined) {\n    return new Promise((resolve) => canvas.toBlob(resolve, mimeType))\n  }\n\n  let quality\n\n  // Blink's implementation of convertToBlob seems to default to a quality level of 100%\n  // Use the Blink default quality levels of toBlob instead so that file sizes are comparable.\n  if (mimeType === 'image/jpeg') {\n    quality = 0.92\n  } else if (mimeType === 'image/webp') {\n    quality = 0.8\n  }\n\n  return canvas.convertToBlob({\n    type: mimeType,\n    quality: quality,\n  })\n}\n\n/**\n * Writer\n */\nclass GLTFWriter {\n  constructor() {\n    this.plugins = []\n\n    this.options = {}\n    this.pending = []\n    this.buffers = []\n\n    this.byteOffset = 0\n    this.buffers = []\n    this.nodeMap = new Map()\n    this.skins = []\n\n    this.extensionsUsed = {}\n    this.extensionsRequired = {}\n\n    this.uids = new Map()\n    this.uid = 0\n\n    this.json = {\n      asset: {\n        version: '2.0',\n        generator: 'THREE.GLTFExporter',\n      },\n    }\n\n    this.cache = {\n      meshes: new Map(),\n      attributes: new Map(),\n      attributesNormalized: new Map(),\n      materials: new Map(),\n      textures: new Map(),\n      images: new Map(),\n    }\n  }\n\n  setPlugins(plugins) {\n    this.plugins = plugins\n  }\n\n  /**\n   * Parse scenes and generate GLTF output\n   * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n   * @param  {Function} onDone  Callback on completed\n   * @param  {Object} options options\n   */\n  async write(input, onDone, options = {}) {\n    this.options = Object.assign(\n      {\n        // default options\n        binary: false,\n        trs: false,\n        onlyVisible: true,\n        maxTextureSize: Infinity,\n        animations: [],\n        includeCustomExtensions: false,\n      },\n      options,\n    )\n\n    if (this.options.animations.length > 0) {\n      // Only TRS properties, and not matrices, may be targeted by animation.\n      this.options.trs = true\n    }\n\n    this.processInput(input)\n\n    await Promise.all(this.pending)\n\n    const writer = this\n    const buffers = writer.buffers\n    const json = writer.json\n    options = writer.options\n\n    const extensionsUsed = writer.extensionsUsed\n    const extensionsRequired = writer.extensionsRequired\n\n    // Merge buffers.\n    const blob = new Blob(buffers, { type: 'application/octet-stream' })\n\n    // Declare extensions.\n    const extensionsUsedList = Object.keys(extensionsUsed)\n    const extensionsRequiredList = Object.keys(extensionsRequired)\n\n    if (extensionsUsedList.length > 0) json.extensionsUsed = extensionsUsedList\n    if (extensionsRequiredList.length > 0) json.extensionsRequired = extensionsRequiredList\n\n    // Update bytelength of the single buffer.\n    if (json.buffers && json.buffers.length > 0) json.buffers[0].byteLength = blob.size\n\n    if (options.binary === true) {\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#glb-file-format-specification\n\n      blob.arrayBuffer().then((result) => {\n        // Binary chunk.\n        const binaryChunk = getPaddedArrayBuffer(result)\n        const binaryChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES))\n        binaryChunkPrefix.setUint32(0, binaryChunk.byteLength, true)\n        binaryChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_BIN, true)\n\n        // JSON chunk.\n        const jsonChunk = getPaddedArrayBuffer(stringToArrayBuffer(JSON.stringify(json)), 0x20)\n        const jsonChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES))\n        jsonChunkPrefix.setUint32(0, jsonChunk.byteLength, true)\n        jsonChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_JSON, true)\n\n        // GLB header.\n        const header = new ArrayBuffer(GLB_HEADER_BYTES)\n        const headerView = new DataView(header)\n        headerView.setUint32(0, GLB_HEADER_MAGIC, true)\n        headerView.setUint32(4, GLB_VERSION, true)\n        const totalByteLength =\n          GLB_HEADER_BYTES +\n          jsonChunkPrefix.byteLength +\n          jsonChunk.byteLength +\n          binaryChunkPrefix.byteLength +\n          binaryChunk.byteLength\n        headerView.setUint32(8, totalByteLength, true)\n\n        const glbBlob = new Blob([header, jsonChunkPrefix, jsonChunk, binaryChunkPrefix, binaryChunk], {\n          type: 'application/octet-stream',\n        })\n\n        glbBlob.arrayBuffer().then(onDone)\n      })\n    } else {\n      if (json.buffers && json.buffers.length > 0) {\n        readAsDataURL(blob).then((uri) => {\n          json.buffers[0].uri = uri\n          onDone(json)\n        })\n      } else {\n        onDone(json)\n      }\n    }\n  }\n\n  /**\n   * Serializes a userData.\n   *\n   * @param {THREE.Object3D|THREE.Material} object\n   * @param {Object} objectDef\n   */\n  serializeUserData(object, objectDef) {\n    if (Object.keys(object.userData).length === 0) return\n\n    const options = this.options\n    const extensionsUsed = this.extensionsUsed\n\n    try {\n      const json = JSON.parse(JSON.stringify(object.userData))\n\n      if (options.includeCustomExtensions && json.gltfExtensions) {\n        if (objectDef.extensions === undefined) objectDef.extensions = {}\n\n        for (const extensionName in json.gltfExtensions) {\n          objectDef.extensions[extensionName] = json.gltfExtensions[extensionName]\n          extensionsUsed[extensionName] = true\n        }\n\n        delete json.gltfExtensions\n      }\n\n      if (Object.keys(json).length > 0) objectDef.extras = json\n    } catch (error) {\n      console.warn(\n        \"THREE.GLTFExporter: userData of '\" +\n          object.name +\n          \"' \" +\n          \"won't be serialized because of JSON.stringify error - \" +\n          error.message,\n      )\n    }\n  }\n\n  /**\n   * Returns ids for buffer attributes.\n   * @param  {Object} object\n   * @return {Integer}\n   */\n  getUID(attribute, isRelativeCopy = false) {\n    if (this.uids.has(attribute) === false) {\n      const uids = new Map()\n\n      uids.set(true, this.uid++)\n      uids.set(false, this.uid++)\n\n      this.uids.set(attribute, uids)\n    }\n\n    const uids = this.uids.get(attribute)\n\n    return uids.get(isRelativeCopy)\n  }\n\n  /**\n   * Checks if normal attribute values are normalized.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {Boolean}\n   */\n  isNormalizedNormalAttribute(normal) {\n    const cache = this.cache\n\n    if (cache.attributesNormalized.has(normal)) return false\n\n    const v = new Vector3()\n\n    for (let i = 0, il = normal.count; i < il; i++) {\n      // 0.0005 is from glTF-validator\n      if (Math.abs(v.fromBufferAttribute(normal, i).length() - 1.0) > 0.0005) return false\n    }\n\n    return true\n  }\n\n  /**\n   * Creates normalized normal buffer attribute.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {BufferAttribute}\n   *\n   */\n  createNormalizedNormalAttribute(normal) {\n    const cache = this.cache\n\n    if (cache.attributesNormalized.has(normal)) return cache.attributesNormalized.get(normal)\n\n    const attribute = normal.clone()\n    const v = new Vector3()\n\n    for (let i = 0, il = attribute.count; i < il; i++) {\n      v.fromBufferAttribute(attribute, i)\n\n      if (v.x === 0 && v.y === 0 && v.z === 0) {\n        // if values can't be normalized set (1, 0, 0)\n        v.setX(1.0)\n      } else {\n        v.normalize()\n      }\n\n      attribute.setXYZ(i, v.x, v.y, v.z)\n    }\n\n    cache.attributesNormalized.set(normal, attribute)\n\n    return attribute\n  }\n\n  /**\n   * Applies a texture transform, if present, to the map definition. Requires\n   * the KHR_texture_transform extension.\n   *\n   * @param {Object} mapDef\n   * @param {THREE.Texture} texture\n   */\n  applyTextureTransform(mapDef, texture) {\n    let didTransform = false\n    const transformDef = {}\n\n    if (texture.offset.x !== 0 || texture.offset.y !== 0) {\n      transformDef.offset = texture.offset.toArray()\n      didTransform = true\n    }\n\n    if (texture.rotation !== 0) {\n      transformDef.rotation = texture.rotation\n      didTransform = true\n    }\n\n    if (texture.repeat.x !== 1 || texture.repeat.y !== 1) {\n      transformDef.scale = texture.repeat.toArray()\n      didTransform = true\n    }\n\n    if (didTransform) {\n      mapDef.extensions = mapDef.extensions || {}\n      mapDef.extensions['KHR_texture_transform'] = transformDef\n      this.extensionsUsed['KHR_texture_transform'] = true\n    }\n  }\n\n  buildMetalRoughTexture(metalnessMap, roughnessMap) {\n    if (metalnessMap === roughnessMap) return metalnessMap\n\n    function getEncodingConversion(map) {\n      if ('colorSpace' in map ? map.colorSpace === 'srgb' : map.encoding === 3001) {\n        return function SRGBToLinear(c) {\n          return c < 0.04045 ? c * 0.0773993808 : Math.pow(c * 0.9478672986 + 0.0521327014, 2.4)\n        }\n      }\n\n      return function LinearToLinear(c) {\n        return c\n      }\n    }\n\n    console.warn('THREE.GLTFExporter: Merged metalnessMap and roughnessMap textures.')\n\n    if (metalnessMap instanceof CompressedTexture) {\n      metalnessMap = decompress(metalnessMap)\n    }\n\n    if (roughnessMap instanceof CompressedTexture) {\n      roughnessMap = decompress(roughnessMap)\n    }\n\n    const metalness = metalnessMap ? metalnessMap.image : null\n    const roughness = roughnessMap ? roughnessMap.image : null\n\n    const width = Math.max(metalness ? metalness.width : 0, roughness ? roughness.width : 0)\n    const height = Math.max(metalness ? metalness.height : 0, roughness ? roughness.height : 0)\n\n    const canvas = getCanvas()\n    canvas.width = width\n    canvas.height = height\n\n    const context = canvas.getContext('2d')\n    context.fillStyle = '#00ffff'\n    context.fillRect(0, 0, width, height)\n\n    const composite = context.getImageData(0, 0, width, height)\n\n    if (metalness) {\n      context.drawImage(metalness, 0, 0, width, height)\n\n      const convert = getEncodingConversion(metalnessMap)\n      const data = context.getImageData(0, 0, width, height).data\n\n      for (let i = 2; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256\n      }\n    }\n\n    if (roughness) {\n      context.drawImage(roughness, 0, 0, width, height)\n\n      const convert = getEncodingConversion(roughnessMap)\n      const data = context.getImageData(0, 0, width, height).data\n\n      for (let i = 1; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256\n      }\n    }\n\n    context.putImageData(composite, 0, 0)\n\n    //\n\n    const reference = metalnessMap || roughnessMap\n\n    const texture = reference.clone()\n\n    // TODO Use new Source() instead?\n    texture.source = new Texture(canvas).source\n    if ('colorSpace' in texture) texture.colorSpace = ''\n    else texture.encoding = 3000\n    texture.channel = (metalnessMap || roughnessMap).channel\n\n    if (metalnessMap && roughnessMap && metalnessMap.channel !== roughnessMap.channel) {\n      console.warn('THREE.GLTFExporter: UV channels for metalnessMap and roughnessMap textures must match.')\n    }\n\n    return texture\n  }\n\n  /**\n   * Process a buffer to append to the default one.\n   * @param  {ArrayBuffer} buffer\n   * @return {Integer}\n   */\n  processBuffer(buffer) {\n    const json = this.json\n    const buffers = this.buffers\n\n    if (!json.buffers) json.buffers = [{ byteLength: 0 }]\n\n    // All buffers are merged before export.\n    buffers.push(buffer)\n\n    return 0\n  }\n\n  /**\n   * Process and generate a BufferView\n   * @param  {BufferAttribute} attribute\n   * @param  {number} componentType\n   * @param  {number} start\n   * @param  {number} count\n   * @param  {number} target (Optional) Target usage of the BufferView\n   * @return {Object}\n   */\n  processBufferView(attribute, componentType, start, count, target) {\n    const json = this.json\n\n    if (!json.bufferViews) json.bufferViews = []\n\n    // Create a new dataview and dump the attribute's array into it\n\n    let componentSize\n\n    switch (componentType) {\n      case WEBGL_CONSTANTS.BYTE:\n      case WEBGL_CONSTANTS.UNSIGNED_BYTE:\n        componentSize = 1\n\n        break\n\n      case WEBGL_CONSTANTS.SHORT:\n      case WEBGL_CONSTANTS.UNSIGNED_SHORT:\n        componentSize = 2\n\n        break\n\n      default:\n        componentSize = 4\n    }\n\n    let byteStride = attribute.itemSize * componentSize\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      // Each element of a vertex attribute MUST be aligned to 4-byte boundaries\n      // inside a bufferView\n      byteStride = Math.ceil(byteStride / 4) * 4\n    }\n    const byteLength = getPaddedBufferSize(count * byteStride)\n    const dataView = new DataView(new ArrayBuffer(byteLength))\n    let offset = 0\n\n    for (let i = start; i < start + count; i++) {\n      for (let a = 0; a < attribute.itemSize; a++) {\n        let value\n\n        if (attribute.itemSize > 4) {\n          // no support for interleaved data for itemSize > 4\n\n          value = attribute.array[i * attribute.itemSize + a]\n        } else {\n          if (a === 0) value = attribute.getX(i)\n          else if (a === 1) value = attribute.getY(i)\n          else if (a === 2) value = attribute.getZ(i)\n          else if (a === 3) value = attribute.getW(i)\n\n          if (attribute.normalized === true) {\n            value = MathUtils.normalize(value, attribute.array)\n          }\n        }\n\n        if (componentType === WEBGL_CONSTANTS.FLOAT) {\n          dataView.setFloat32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.INT) {\n          dataView.setInt32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_INT) {\n          dataView.setUint32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.SHORT) {\n          dataView.setInt16(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_SHORT) {\n          dataView.setUint16(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.BYTE) {\n          dataView.setInt8(offset, value)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_BYTE) {\n          dataView.setUint8(offset, value)\n        }\n\n        offset += componentSize\n      }\n      if (offset % byteStride !== 0) {\n        offset += byteStride - (offset % byteStride)\n      }\n    }\n\n    const bufferViewDef = {\n      buffer: this.processBuffer(dataView.buffer),\n      byteOffset: this.byteOffset,\n      byteLength: byteLength,\n    }\n\n    if (target !== undefined) bufferViewDef.target = target\n\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      // Only define byteStride for vertex attributes.\n      bufferViewDef.byteStride = byteStride\n    }\n\n    this.byteOffset += byteLength\n\n    json.bufferViews.push(bufferViewDef)\n\n    // @TODO Merge bufferViews where possible.\n    const output = {\n      id: json.bufferViews.length - 1,\n      byteLength: 0,\n    }\n\n    return output\n  }\n\n  /**\n   * Process and generate a BufferView from an image Blob.\n   * @param {Blob} blob\n   * @return {Promise<Integer>}\n   */\n  processBufferViewImage(blob) {\n    const writer = this\n    const json = writer.json\n\n    if (!json.bufferViews) json.bufferViews = []\n\n    return blob.arrayBuffer().then((result) => {\n      const buffer = getPaddedArrayBuffer(result)\n\n      const bufferViewDef = {\n        buffer: writer.processBuffer(buffer),\n        byteOffset: writer.byteOffset,\n        byteLength: buffer.byteLength,\n      }\n\n      writer.byteOffset += buffer.byteLength\n      return json.bufferViews.push(bufferViewDef) - 1\n    })\n  }\n\n  /**\n   * Process attribute to generate an accessor\n   * @param  {BufferAttribute} attribute Attribute to process\n   * @param  {THREE.BufferGeometry} geometry (Optional) Geometry used for truncated draw range\n   * @param  {Integer} start (Optional)\n   * @param  {Integer} count (Optional)\n   * @return {Integer|null} Index of the processed accessor on the \"accessors\" array\n   */\n  processAccessor(attribute, geometry, start, count) {\n    const json = this.json\n\n    const types = {\n      1: 'SCALAR',\n      2: 'VEC2',\n      3: 'VEC3',\n      4: 'VEC4',\n      9: 'MAT3',\n      16: 'MAT4',\n    }\n\n    let componentType\n\n    // Detect the component type of the attribute array\n    if (attribute.array.constructor === Float32Array) {\n      componentType = WEBGL_CONSTANTS.FLOAT\n    } else if (attribute.array.constructor === Int32Array) {\n      componentType = WEBGL_CONSTANTS.INT\n    } else if (attribute.array.constructor === Uint32Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_INT\n    } else if (attribute.array.constructor === Int16Array) {\n      componentType = WEBGL_CONSTANTS.SHORT\n    } else if (attribute.array.constructor === Uint16Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_SHORT\n    } else if (attribute.array.constructor === Int8Array) {\n      componentType = WEBGL_CONSTANTS.BYTE\n    } else if (attribute.array.constructor === Uint8Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_BYTE\n    } else {\n      throw new Error(\n        'THREE.GLTFExporter: Unsupported bufferAttribute component type: ' + attribute.array.constructor.name,\n      )\n    }\n\n    if (start === undefined) start = 0\n    if (count === undefined) count = attribute.count\n\n    // Skip creating an accessor if the attribute doesn't have data to export\n    if (count === 0) return null\n\n    const minMax = getMinMax(attribute, start, count)\n    let bufferViewTarget\n\n    // If geometry isn't provided, don't infer the target usage of the bufferView. For\n    // animation samplers, target must not be set.\n    if (geometry !== undefined) {\n      bufferViewTarget =\n        attribute === geometry.index ? WEBGL_CONSTANTS.ELEMENT_ARRAY_BUFFER : WEBGL_CONSTANTS.ARRAY_BUFFER\n    }\n\n    const bufferView = this.processBufferView(attribute, componentType, start, count, bufferViewTarget)\n\n    const accessorDef = {\n      bufferView: bufferView.id,\n      byteOffset: bufferView.byteOffset,\n      componentType: componentType,\n      count: count,\n      max: minMax.max,\n      min: minMax.min,\n      type: types[attribute.itemSize],\n    }\n\n    if (attribute.normalized === true) accessorDef.normalized = true\n    if (!json.accessors) json.accessors = []\n\n    return json.accessors.push(accessorDef) - 1\n  }\n\n  /**\n   * Process image\n   * @param  {Image} image to process\n   * @param  {Integer} format of the image (RGBAFormat)\n   * @param  {Boolean} flipY before writing out the image\n   * @param  {String} mimeType export format\n   * @return {Integer}     Index of the processed texture in the \"images\" array\n   */\n  processImage(image, format, flipY, mimeType = 'image/png') {\n    if (image !== null) {\n      const writer = this\n      const cache = writer.cache\n      const json = writer.json\n      const options = writer.options\n      const pending = writer.pending\n\n      if (!cache.images.has(image)) cache.images.set(image, {})\n\n      const cachedImages = cache.images.get(image)\n\n      const key = mimeType + ':flipY/' + flipY.toString()\n\n      if (cachedImages[key] !== undefined) return cachedImages[key]\n\n      if (!json.images) json.images = []\n\n      const imageDef = { mimeType: mimeType }\n\n      const canvas = getCanvas()\n\n      canvas.width = Math.min(image.width, options.maxTextureSize)\n      canvas.height = Math.min(image.height, options.maxTextureSize)\n\n      const ctx = canvas.getContext('2d')\n\n      if (flipY === true) {\n        ctx.translate(0, canvas.height)\n        ctx.scale(1, -1)\n      }\n\n      if (image.data !== undefined) {\n        // THREE.DataTexture\n\n        if (format !== RGBAFormat) {\n          console.error('GLTFExporter: Only RGBAFormat is supported.', format)\n        }\n\n        if (image.width > options.maxTextureSize || image.height > options.maxTextureSize) {\n          console.warn('GLTFExporter: Image size is bigger than maxTextureSize', image)\n        }\n\n        const data = new Uint8ClampedArray(image.height * image.width * 4)\n\n        for (let i = 0; i < data.length; i += 4) {\n          data[i + 0] = image.data[i + 0]\n          data[i + 1] = image.data[i + 1]\n          data[i + 2] = image.data[i + 2]\n          data[i + 3] = image.data[i + 3]\n        }\n\n        ctx.putImageData(new ImageData(data, image.width, image.height), 0, 0)\n      } else {\n        ctx.drawImage(image, 0, 0, canvas.width, canvas.height)\n      }\n\n      if (options.binary === true) {\n        pending.push(\n          getToBlobPromise(canvas, mimeType)\n            .then((blob) => writer.processBufferViewImage(blob))\n            .then((bufferViewIndex) => {\n              imageDef.bufferView = bufferViewIndex\n            }),\n        )\n      } else {\n        if (canvas.toDataURL !== undefined) {\n          imageDef.uri = canvas.toDataURL(mimeType)\n        } else {\n          pending.push(\n            getToBlobPromise(canvas, mimeType)\n              .then(readAsDataURL)\n              .then((uri) => {\n                imageDef.uri = uri\n              }),\n          )\n        }\n      }\n\n      const index = json.images.push(imageDef) - 1\n      cachedImages[key] = index\n      return index\n    } else {\n      throw new Error('THREE.GLTFExporter: No valid image data found. Unable to process texture.')\n    }\n  }\n\n  /**\n   * Process sampler\n   * @param  {Texture} map Texture to process\n   * @return {Integer}     Index of the processed texture in the \"samplers\" array\n   */\n  processSampler(map) {\n    const json = this.json\n\n    if (!json.samplers) json.samplers = []\n\n    const samplerDef = {\n      magFilter: THREE_TO_WEBGL[map.magFilter],\n      minFilter: THREE_TO_WEBGL[map.minFilter],\n      wrapS: THREE_TO_WEBGL[map.wrapS],\n      wrapT: THREE_TO_WEBGL[map.wrapT],\n    }\n\n    return json.samplers.push(samplerDef) - 1\n  }\n\n  /**\n   * Process texture\n   * @param  {Texture} map Map to process\n   * @return {Integer} Index of the processed texture in the \"textures\" array\n   */\n  processTexture(map) {\n    const writer = this\n    const options = writer.options\n    const cache = this.cache\n    const json = this.json\n\n    if (cache.textures.has(map)) return cache.textures.get(map)\n\n    if (!json.textures) json.textures = []\n\n    // make non-readable textures (e.g. CompressedTexture) readable by blitting them into a new texture\n    if (map instanceof CompressedTexture) {\n      map = decompress(map, options.maxTextureSize)\n    }\n\n    let mimeType = map.userData.mimeType\n\n    if (mimeType === 'image/webp') mimeType = 'image/png'\n\n    const textureDef = {\n      sampler: this.processSampler(map),\n      source: this.processImage(map.image, map.format, map.flipY, mimeType),\n    }\n\n    if (map.name) textureDef.name = map.name\n\n    this._invokeAll(function (ext) {\n      ext.writeTexture && ext.writeTexture(map, textureDef)\n    })\n\n    const index = json.textures.push(textureDef) - 1\n    cache.textures.set(map, index)\n    return index\n  }\n\n  /**\n   * Process material\n   * @param  {THREE.Material} material Material to process\n   * @return {Integer|null} Index of the processed material in the \"materials\" array\n   */\n  processMaterial(material) {\n    const cache = this.cache\n    const json = this.json\n\n    if (cache.materials.has(material)) return cache.materials.get(material)\n\n    if (material.isShaderMaterial) {\n      console.warn('GLTFExporter: THREE.ShaderMaterial not supported.')\n      return null\n    }\n\n    if (!json.materials) json.materials = []\n\n    // @QUESTION Should we avoid including any attribute that has the default value?\n    const materialDef = { pbrMetallicRoughness: {} }\n\n    if (material.isMeshStandardMaterial !== true && material.isMeshBasicMaterial !== true) {\n      console.warn('GLTFExporter: Use MeshStandardMaterial or MeshBasicMaterial for best results.')\n    }\n\n    // pbrMetallicRoughness.baseColorFactor\n    const color = material.color.toArray().concat([material.opacity])\n\n    if (!equalArray(color, [1, 1, 1, 1])) {\n      materialDef.pbrMetallicRoughness.baseColorFactor = color\n    }\n\n    if (material.isMeshStandardMaterial) {\n      materialDef.pbrMetallicRoughness.metallicFactor = material.metalness\n      materialDef.pbrMetallicRoughness.roughnessFactor = material.roughness\n    } else {\n      materialDef.pbrMetallicRoughness.metallicFactor = 0.5\n      materialDef.pbrMetallicRoughness.roughnessFactor = 0.5\n    }\n\n    // pbrMetallicRoughness.metallicRoughnessTexture\n    if (material.metalnessMap || material.roughnessMap) {\n      const metalRoughTexture = this.buildMetalRoughTexture(material.metalnessMap, material.roughnessMap)\n\n      const metalRoughMapDef = {\n        index: this.processTexture(metalRoughTexture),\n        channel: metalRoughTexture.channel,\n      }\n      this.applyTextureTransform(metalRoughMapDef, metalRoughTexture)\n      materialDef.pbrMetallicRoughness.metallicRoughnessTexture = metalRoughMapDef\n    }\n\n    // pbrMetallicRoughness.baseColorTexture\n    if (material.map) {\n      const baseColorMapDef = {\n        index: this.processTexture(material.map),\n        texCoord: material.map.channel,\n      }\n      this.applyTextureTransform(baseColorMapDef, material.map)\n      materialDef.pbrMetallicRoughness.baseColorTexture = baseColorMapDef\n    }\n\n    if (material.emissive) {\n      const emissive = material.emissive\n      const maxEmissiveComponent = Math.max(emissive.r, emissive.g, emissive.b)\n\n      if (maxEmissiveComponent > 0) {\n        materialDef.emissiveFactor = material.emissive.toArray()\n      }\n\n      // emissiveTexture\n      if (material.emissiveMap) {\n        const emissiveMapDef = {\n          index: this.processTexture(material.emissiveMap),\n          texCoord: material.emissiveMap.channel,\n        }\n        this.applyTextureTransform(emissiveMapDef, material.emissiveMap)\n        materialDef.emissiveTexture = emissiveMapDef\n      }\n    }\n\n    // normalTexture\n    if (material.normalMap) {\n      const normalMapDef = {\n        index: this.processTexture(material.normalMap),\n        texCoord: material.normalMap.channel,\n      }\n\n      if (material.normalScale && material.normalScale.x !== 1) {\n        // glTF normal scale is univariate. Ignore `y`, which may be flipped.\n        // Context: https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n        normalMapDef.scale = material.normalScale.x\n      }\n\n      this.applyTextureTransform(normalMapDef, material.normalMap)\n      materialDef.normalTexture = normalMapDef\n    }\n\n    // occlusionTexture\n    if (material.aoMap) {\n      const occlusionMapDef = {\n        index: this.processTexture(material.aoMap),\n        texCoord: material.aoMap.channel,\n      }\n\n      if (material.aoMapIntensity !== 1.0) {\n        occlusionMapDef.strength = material.aoMapIntensity\n      }\n\n      this.applyTextureTransform(occlusionMapDef, material.aoMap)\n      materialDef.occlusionTexture = occlusionMapDef\n    }\n\n    // alphaMode\n    if (material.transparent) {\n      materialDef.alphaMode = 'BLEND'\n    } else {\n      if (material.alphaTest > 0.0) {\n        materialDef.alphaMode = 'MASK'\n        materialDef.alphaCutoff = material.alphaTest\n      }\n    }\n\n    // doubleSided\n    if (material.side === DoubleSide) materialDef.doubleSided = true\n    if (material.name !== '') materialDef.name = material.name\n\n    this.serializeUserData(material, materialDef)\n\n    this._invokeAll(function (ext) {\n      ext.writeMaterial && ext.writeMaterial(material, materialDef)\n    })\n\n    const index = json.materials.push(materialDef) - 1\n    cache.materials.set(material, index)\n    return index\n  }\n\n  /**\n   * Process mesh\n   * @param  {THREE.Mesh} mesh Mesh to process\n   * @return {Integer|null} Index of the processed mesh in the \"meshes\" array\n   */\n  processMesh(mesh) {\n    const cache = this.cache\n    const json = this.json\n\n    const meshCacheKeyParts = [mesh.geometry.uuid]\n\n    if (Array.isArray(mesh.material)) {\n      for (let i = 0, l = mesh.material.length; i < l; i++) {\n        meshCacheKeyParts.push(mesh.material[i].uuid)\n      }\n    } else {\n      meshCacheKeyParts.push(mesh.material.uuid)\n    }\n\n    const meshCacheKey = meshCacheKeyParts.join(':')\n\n    if (cache.meshes.has(meshCacheKey)) return cache.meshes.get(meshCacheKey)\n\n    const geometry = mesh.geometry\n\n    let mode\n\n    // Use the correct mode\n    if (mesh.isLineSegments) {\n      mode = WEBGL_CONSTANTS.LINES\n    } else if (mesh.isLineLoop) {\n      mode = WEBGL_CONSTANTS.LINE_LOOP\n    } else if (mesh.isLine) {\n      mode = WEBGL_CONSTANTS.LINE_STRIP\n    } else if (mesh.isPoints) {\n      mode = WEBGL_CONSTANTS.POINTS\n    } else {\n      mode = mesh.material.wireframe ? WEBGL_CONSTANTS.LINES : WEBGL_CONSTANTS.TRIANGLES\n    }\n\n    const meshDef = {}\n    const attributes = {}\n    const primitives = []\n    const targets = []\n\n    // Conversion between attributes names in threejs and gltf spec\n    const nameConversion = {\n      ...(version >= 152\n        ? {\n            uv: 'TEXCOORD_0',\n            uv1: 'TEXCOORD_1',\n            uv2: 'TEXCOORD_2',\n            uv3: 'TEXCOORD_3',\n          }\n        : {\n            uv: 'TEXCOORD_0',\n            uv2: 'TEXCOORD_1',\n          }),\n      color: 'COLOR_0',\n      skinWeight: 'WEIGHTS_0',\n      skinIndex: 'JOINTS_0',\n    }\n\n    const originalNormal = geometry.getAttribute('normal')\n\n    if (originalNormal !== undefined && !this.isNormalizedNormalAttribute(originalNormal)) {\n      console.warn('THREE.GLTFExporter: Creating normalized normal attribute from the non-normalized one.')\n\n      geometry.setAttribute('normal', this.createNormalizedNormalAttribute(originalNormal))\n    }\n\n    // @QUESTION Detect if .vertexColors = true?\n    // For every attribute create an accessor\n    let modifiedAttribute = null\n\n    for (let attributeName in geometry.attributes) {\n      // Ignore morph target attributes, which are exported later.\n      if (attributeName.slice(0, 5) === 'morph') continue\n\n      const attribute = geometry.attributes[attributeName]\n      attributeName = nameConversion[attributeName] || attributeName.toUpperCase()\n\n      // Prefix all geometry attributes except the ones specifically\n      // listed in the spec; non-spec attributes are considered custom.\n      const validVertexAttributes = /^(POSITION|NORMAL|TANGENT|TEXCOORD_\\d+|COLOR_\\d+|JOINTS_\\d+|WEIGHTS_\\d+)$/\n\n      if (!validVertexAttributes.test(attributeName)) attributeName = '_' + attributeName\n\n      if (cache.attributes.has(this.getUID(attribute))) {\n        attributes[attributeName] = cache.attributes.get(this.getUID(attribute))\n        continue\n      }\n\n      // JOINTS_0 must be UNSIGNED_BYTE or UNSIGNED_SHORT.\n      modifiedAttribute = null\n      const array = attribute.array\n\n      if (attributeName === 'JOINTS_0' && !(array instanceof Uint16Array) && !(array instanceof Uint8Array)) {\n        console.warn('GLTFExporter: Attribute \"skinIndex\" converted to type UNSIGNED_SHORT.')\n        modifiedAttribute = new BufferAttribute(new Uint16Array(array), attribute.itemSize, attribute.normalized)\n      }\n\n      const accessor = this.processAccessor(modifiedAttribute || attribute, geometry)\n\n      if (accessor !== null) {\n        if (!attributeName.startsWith('_')) {\n          this.detectMeshQuantization(attributeName, attribute)\n        }\n\n        attributes[attributeName] = accessor\n        cache.attributes.set(this.getUID(attribute), accessor)\n      }\n    }\n\n    if (originalNormal !== undefined) geometry.setAttribute('normal', originalNormal)\n\n    // Skip if no exportable attributes found\n    if (Object.keys(attributes).length === 0) return null\n\n    // Morph targets\n    if (mesh.morphTargetInfluences !== undefined && mesh.morphTargetInfluences.length > 0) {\n      const weights = []\n      const targetNames = []\n      const reverseDictionary = {}\n\n      if (mesh.morphTargetDictionary !== undefined) {\n        for (const key in mesh.morphTargetDictionary) {\n          reverseDictionary[mesh.morphTargetDictionary[key]] = key\n        }\n      }\n\n      for (let i = 0; i < mesh.morphTargetInfluences.length; ++i) {\n        const target = {}\n        let warned = false\n\n        for (const attributeName in geometry.morphAttributes) {\n          // glTF 2.0 morph supports only POSITION/NORMAL/TANGENT.\n          // Three.js doesn't support TANGENT yet.\n\n          if (attributeName !== 'position' && attributeName !== 'normal') {\n            if (!warned) {\n              console.warn('GLTFExporter: Only POSITION and NORMAL morph are supported.')\n              warned = true\n            }\n\n            continue\n          }\n\n          const attribute = geometry.morphAttributes[attributeName][i]\n          const gltfAttributeName = attributeName.toUpperCase()\n\n          // Three.js morph attribute has absolute values while the one of glTF has relative values.\n          //\n          // glTF 2.0 Specification:\n          // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#morph-targets\n\n          const baseAttribute = geometry.attributes[attributeName]\n\n          if (cache.attributes.has(this.getUID(attribute, true))) {\n            target[gltfAttributeName] = cache.attributes.get(this.getUID(attribute, true))\n            continue\n          }\n\n          // Clones attribute not to override\n          const relativeAttribute = attribute.clone()\n\n          if (!geometry.morphTargetsRelative) {\n            for (let j = 0, jl = attribute.count; j < jl; j++) {\n              for (let a = 0; a < attribute.itemSize; a++) {\n                if (a === 0) relativeAttribute.setX(j, attribute.getX(j) - baseAttribute.getX(j))\n                if (a === 1) relativeAttribute.setY(j, attribute.getY(j) - baseAttribute.getY(j))\n                if (a === 2) relativeAttribute.setZ(j, attribute.getZ(j) - baseAttribute.getZ(j))\n                if (a === 3) relativeAttribute.setW(j, attribute.getW(j) - baseAttribute.getW(j))\n              }\n            }\n          }\n\n          target[gltfAttributeName] = this.processAccessor(relativeAttribute, geometry)\n          cache.attributes.set(this.getUID(baseAttribute, true), target[gltfAttributeName])\n        }\n\n        targets.push(target)\n\n        weights.push(mesh.morphTargetInfluences[i])\n\n        if (mesh.morphTargetDictionary !== undefined) targetNames.push(reverseDictionary[i])\n      }\n\n      meshDef.weights = weights\n\n      if (targetNames.length > 0) {\n        meshDef.extras = {}\n        meshDef.extras.targetNames = targetNames\n      }\n    }\n\n    const isMultiMaterial = Array.isArray(mesh.material)\n\n    if (isMultiMaterial && geometry.groups.length === 0) return null\n\n    const materials = isMultiMaterial ? mesh.material : [mesh.material]\n    const groups = isMultiMaterial ? geometry.groups : [{ materialIndex: 0, start: undefined, count: undefined }]\n\n    for (let i = 0, il = groups.length; i < il; i++) {\n      const primitive = {\n        mode: mode,\n        attributes: attributes,\n      }\n\n      this.serializeUserData(geometry, primitive)\n\n      if (targets.length > 0) primitive.targets = targets\n\n      if (geometry.index !== null) {\n        let cacheKey = this.getUID(geometry.index)\n\n        if (groups[i].start !== undefined || groups[i].count !== undefined) {\n          cacheKey += ':' + groups[i].start + ':' + groups[i].count\n        }\n\n        if (cache.attributes.has(cacheKey)) {\n          primitive.indices = cache.attributes.get(cacheKey)\n        } else {\n          primitive.indices = this.processAccessor(geometry.index, geometry, groups[i].start, groups[i].count)\n          cache.attributes.set(cacheKey, primitive.indices)\n        }\n\n        if (primitive.indices === null) delete primitive.indices\n      }\n\n      const material = this.processMaterial(materials[groups[i].materialIndex])\n\n      if (material !== null) primitive.material = material\n\n      primitives.push(primitive)\n    }\n\n    meshDef.primitives = primitives\n\n    if (!json.meshes) json.meshes = []\n\n    this._invokeAll(function (ext) {\n      ext.writeMesh && ext.writeMesh(mesh, meshDef)\n    })\n\n    const index = json.meshes.push(meshDef) - 1\n    cache.meshes.set(meshCacheKey, index)\n    return index\n  }\n\n  /**\n   * If a vertex attribute with a\n   * [non-standard data type](https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html#meshes-overview)\n   * is used, it is checked whether it is a valid data type according to the\n   * [KHR_mesh_quantization](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md)\n   * extension.\n   * In this case the extension is automatically added to the list of used extensions.\n   *\n   * @param {string} attributeName\n   * @param {THREE.BufferAttribute} attribute\n   */\n  detectMeshQuantization(attributeName, attribute) {\n    if (this.extensionsUsed[KHR_MESH_QUANTIZATION]) return\n\n    let attrType = undefined\n\n    switch (attribute.array.constructor) {\n      case Int8Array:\n        attrType = 'byte'\n\n        break\n\n      case Uint8Array:\n        attrType = 'unsigned byte'\n\n        break\n\n      case Int16Array:\n        attrType = 'short'\n\n        break\n\n      case Uint16Array:\n        attrType = 'unsigned short'\n\n        break\n\n      default:\n        return\n    }\n\n    if (attribute.normalized) attrType += ' normalized'\n\n    const attrNamePrefix = attributeName.split('_', 1)[0]\n\n    if (\n      KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix] &&\n      KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix].includes(attrType)\n    ) {\n      this.extensionsUsed[KHR_MESH_QUANTIZATION] = true\n      this.extensionsRequired[KHR_MESH_QUANTIZATION] = true\n    }\n  }\n\n  /**\n   * Process camera\n   * @param  {THREE.Camera} camera Camera to process\n   * @return {Integer}      Index of the processed mesh in the \"camera\" array\n   */\n  processCamera(camera) {\n    const json = this.json\n\n    if (!json.cameras) json.cameras = []\n\n    const isOrtho = camera.isOrthographicCamera\n\n    const cameraDef = {\n      type: isOrtho ? 'orthographic' : 'perspective',\n    }\n\n    if (isOrtho) {\n      cameraDef.orthographic = {\n        xmag: camera.right * 2,\n        ymag: camera.top * 2,\n        zfar: camera.far <= 0 ? 0.001 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near,\n      }\n    } else {\n      cameraDef.perspective = {\n        aspectRatio: camera.aspect,\n        yfov: MathUtils.degToRad(camera.fov),\n        zfar: camera.far <= 0 ? 0.001 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near,\n      }\n    }\n\n    // Question: Is saving \"type\" as name intentional?\n    if (camera.name !== '') cameraDef.name = camera.type\n\n    return json.cameras.push(cameraDef) - 1\n  }\n\n  /**\n   * Creates glTF animation entry from AnimationClip object.\n   *\n   * Status:\n   * - Only properties listed in PATH_PROPERTIES may be animated.\n   *\n   * @param {THREE.AnimationClip} clip\n   * @param {THREE.Object3D} root\n   * @return {number|null}\n   */\n  processAnimation(clip, root) {\n    const json = this.json\n    const nodeMap = this.nodeMap\n\n    if (!json.animations) json.animations = []\n\n    clip = GLTFExporter.Utils.mergeMorphTargetTracks(clip.clone(), root)\n\n    const tracks = clip.tracks\n    const channels = []\n    const samplers = []\n\n    for (let i = 0; i < tracks.length; ++i) {\n      const track = tracks[i]\n      const trackBinding = PropertyBinding.parseTrackName(track.name)\n      let trackNode = PropertyBinding.findNode(root, trackBinding.nodeName)\n      const trackProperty = PATH_PROPERTIES[trackBinding.propertyName]\n\n      if (trackBinding.objectName === 'bones') {\n        if (trackNode.isSkinnedMesh === true) {\n          trackNode = trackNode.skeleton.getBoneByName(trackBinding.objectIndex)\n        } else {\n          trackNode = undefined\n        }\n      }\n\n      if (!trackNode || !trackProperty) {\n        console.warn('THREE.GLTFExporter: Could not export animation track \"%s\".', track.name)\n        return null\n      }\n\n      const inputItemSize = 1\n      let outputItemSize = track.values.length / track.times.length\n\n      if (trackProperty === PATH_PROPERTIES.morphTargetInfluences) {\n        outputItemSize /= trackNode.morphTargetInfluences.length\n      }\n\n      let interpolation\n\n      // @TODO export CubicInterpolant(InterpolateSmooth) as CUBICSPLINE\n\n      // Detecting glTF cubic spline interpolant by checking factory method's special property\n      // GLTFCubicSplineInterpolant is a custom interpolant and track doesn't return\n      // valid value from .getInterpolation().\n      if (track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline === true) {\n        interpolation = 'CUBICSPLINE'\n\n        // itemSize of CUBICSPLINE keyframe is 9\n        // (VEC3 * 3: inTangent, splineVertex, and outTangent)\n        // but needs to be stored as VEC3 so dividing by 3 here.\n        outputItemSize /= 3\n      } else if (track.getInterpolation() === InterpolateDiscrete) {\n        interpolation = 'STEP'\n      } else {\n        interpolation = 'LINEAR'\n      }\n\n      samplers.push({\n        input: this.processAccessor(new BufferAttribute(track.times, inputItemSize)),\n        output: this.processAccessor(new BufferAttribute(track.values, outputItemSize)),\n        interpolation: interpolation,\n      })\n\n      channels.push({\n        sampler: samplers.length - 1,\n        target: {\n          node: nodeMap.get(trackNode),\n          path: trackProperty,\n        },\n      })\n    }\n\n    json.animations.push({\n      name: clip.name || 'clip_' + json.animations.length,\n      samplers: samplers,\n      channels: channels,\n    })\n\n    return json.animations.length - 1\n  }\n\n  /**\n   * @param {THREE.Object3D} object\n   * @return {number|null}\n   */\n  processSkin(object) {\n    const json = this.json\n    const nodeMap = this.nodeMap\n\n    const node = json.nodes[nodeMap.get(object)]\n\n    const skeleton = object.skeleton\n\n    if (skeleton === undefined) return null\n\n    const rootJoint = object.skeleton.bones[0]\n\n    if (rootJoint === undefined) return null\n\n    const joints = []\n    const inverseBindMatrices = new Float32Array(skeleton.bones.length * 16)\n    const temporaryBoneInverse = new Matrix4()\n\n    for (let i = 0; i < skeleton.bones.length; ++i) {\n      joints.push(nodeMap.get(skeleton.bones[i]))\n      temporaryBoneInverse.copy(skeleton.boneInverses[i])\n      temporaryBoneInverse.multiply(object.bindMatrix).toArray(inverseBindMatrices, i * 16)\n    }\n\n    if (json.skins === undefined) json.skins = []\n\n    json.skins.push({\n      inverseBindMatrices: this.processAccessor(new BufferAttribute(inverseBindMatrices, 16)),\n      joints: joints,\n      skeleton: nodeMap.get(rootJoint),\n    })\n\n    const skinIndex = (node.skin = json.skins.length - 1)\n\n    return skinIndex\n  }\n\n  /**\n   * Process Object3D node\n   * @param  {THREE.Object3D} node Object3D to processNode\n   * @return {Integer} Index of the node in the nodes list\n   */\n  processNode(object) {\n    const json = this.json\n    const options = this.options\n    const nodeMap = this.nodeMap\n\n    if (!json.nodes) json.nodes = []\n\n    const nodeDef = {}\n\n    if (options.trs) {\n      const rotation = object.quaternion.toArray()\n      const position = object.position.toArray()\n      const scale = object.scale.toArray()\n\n      if (!equalArray(rotation, [0, 0, 0, 1])) {\n        nodeDef.rotation = rotation\n      }\n\n      if (!equalArray(position, [0, 0, 0])) {\n        nodeDef.translation = position\n      }\n\n      if (!equalArray(scale, [1, 1, 1])) {\n        nodeDef.scale = scale\n      }\n    } else {\n      if (object.matrixAutoUpdate) {\n        object.updateMatrix()\n      }\n\n      if (isIdentityMatrix(object.matrix) === false) {\n        nodeDef.matrix = object.matrix.elements\n      }\n    }\n\n    // We don't export empty strings name because it represents no-name in Three.js.\n    if (object.name !== '') nodeDef.name = String(object.name)\n\n    this.serializeUserData(object, nodeDef)\n\n    if (object.isMesh || object.isLine || object.isPoints) {\n      const meshIndex = this.processMesh(object)\n\n      if (meshIndex !== null) nodeDef.mesh = meshIndex\n    } else if (object.isCamera) {\n      nodeDef.camera = this.processCamera(object)\n    }\n\n    if (object.isSkinnedMesh) this.skins.push(object)\n\n    if (object.children.length > 0) {\n      const children = []\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        const child = object.children[i]\n\n        if (child.visible || options.onlyVisible === false) {\n          const nodeIndex = this.processNode(child)\n\n          if (nodeIndex !== null) children.push(nodeIndex)\n        }\n      }\n\n      if (children.length > 0) nodeDef.children = children\n    }\n\n    this._invokeAll(function (ext) {\n      ext.writeNode && ext.writeNode(object, nodeDef)\n    })\n\n    const nodeIndex = json.nodes.push(nodeDef) - 1\n    nodeMap.set(object, nodeIndex)\n    return nodeIndex\n  }\n\n  /**\n   * Process Scene\n   * @param  {Scene} node Scene to process\n   */\n  processScene(scene) {\n    const json = this.json\n    const options = this.options\n\n    if (!json.scenes) {\n      json.scenes = []\n      json.scene = 0\n    }\n\n    const sceneDef = {}\n\n    if (scene.name !== '') sceneDef.name = scene.name\n\n    json.scenes.push(sceneDef)\n\n    const nodes = []\n\n    for (let i = 0, l = scene.children.length; i < l; i++) {\n      const child = scene.children[i]\n\n      if (child.visible || options.onlyVisible === false) {\n        const nodeIndex = this.processNode(child)\n\n        if (nodeIndex !== null) nodes.push(nodeIndex)\n      }\n    }\n\n    if (nodes.length > 0) sceneDef.nodes = nodes\n\n    this.serializeUserData(scene, sceneDef)\n  }\n\n  /**\n   * Creates a Scene to hold a list of objects and parse it\n   * @param  {Array} objects List of objects to process\n   */\n  processObjects(objects) {\n    const scene = new Scene()\n    scene.name = 'AuxScene'\n\n    for (let i = 0; i < objects.length; i++) {\n      // We push directly to children instead of calling `add` to prevent\n      // modify the .parent and break its original scene and hierarchy\n      scene.children.push(objects[i])\n    }\n\n    this.processScene(scene)\n  }\n\n  /**\n   * @param {THREE.Object3D|Array<THREE.Object3D>} input\n   */\n  processInput(input) {\n    const options = this.options\n\n    input = input instanceof Array ? input : [input]\n\n    this._invokeAll(function (ext) {\n      ext.beforeParse && ext.beforeParse(input)\n    })\n\n    const objectsWithoutScene = []\n\n    for (let i = 0; i < input.length; i++) {\n      if (input[i] instanceof Scene) {\n        this.processScene(input[i])\n      } else {\n        objectsWithoutScene.push(input[i])\n      }\n    }\n\n    if (objectsWithoutScene.length > 0) this.processObjects(objectsWithoutScene)\n\n    for (let i = 0; i < this.skins.length; ++i) {\n      this.processSkin(this.skins[i])\n    }\n\n    for (let i = 0; i < options.animations.length; ++i) {\n      this.processAnimation(options.animations[i], input[0])\n    }\n\n    this._invokeAll(function (ext) {\n      ext.afterParse && ext.afterParse(input)\n    })\n  }\n\n  _invokeAll(func) {\n    for (let i = 0, il = this.plugins.length; i < il; i++) {\n      func(this.plugins[i])\n    }\n  }\n}\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_lights_punctual'\n  }\n\n  writeNode(light, nodeDef) {\n    if (!light.isLight) return\n\n    if (!light.isDirectionalLight && !light.isPointLight && !light.isSpotLight) {\n      console.warn('THREE.GLTFExporter: Only directional, point, and spot lights are supported.', light)\n      return\n    }\n\n    const writer = this.writer\n    const json = writer.json\n    const extensionsUsed = writer.extensionsUsed\n\n    const lightDef = {}\n\n    if (light.name) lightDef.name = light.name\n\n    lightDef.color = light.color.toArray()\n\n    lightDef.intensity = light.intensity\n\n    if (light.isDirectionalLight) {\n      lightDef.type = 'directional'\n    } else if (light.isPointLight) {\n      lightDef.type = 'point'\n\n      if (light.distance > 0) lightDef.range = light.distance\n    } else if (light.isSpotLight) {\n      lightDef.type = 'spot'\n\n      if (light.distance > 0) lightDef.range = light.distance\n\n      lightDef.spot = {}\n      lightDef.spot.innerConeAngle = (light.penumbra - 1.0) * light.angle * -1.0\n      lightDef.spot.outerConeAngle = light.angle\n    }\n\n    if (light.decay !== undefined && light.decay !== 2) {\n      console.warn(\n        'THREE.GLTFExporter: Light decay may be lost. glTF is physically-based, ' + 'and expects light.decay=2.',\n      )\n    }\n\n    if (\n      light.target &&\n      (light.target.parent !== light ||\n        light.target.position.x !== 0 ||\n        light.target.position.y !== 0 ||\n        light.target.position.z !== -1)\n    ) {\n      console.warn(\n        'THREE.GLTFExporter: Light direction may be lost. For best results, ' +\n          'make light.target a child of the light with position 0,0,-1.',\n      )\n    }\n\n    if (!extensionsUsed[this.name]) {\n      json.extensions = json.extensions || {}\n      json.extensions[this.name] = { lights: [] }\n      extensionsUsed[this.name] = true\n    }\n\n    const lights = json.extensions[this.name].lights\n    lights.push(lightDef)\n\n    nodeDef.extensions = nodeDef.extensions || {}\n    nodeDef.extensions[this.name] = { light: lights.length - 1 }\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_unlit'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshBasicMaterial) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = {}\n\n    extensionsUsed[this.name] = true\n\n    materialDef.pbrMetallicRoughness.metallicFactor = 0.0\n    materialDef.pbrMetallicRoughness.roughnessFactor = 0.9\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_clearcoat'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.clearcoat === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.clearcoatFactor = material.clearcoat\n\n    if (material.clearcoatMap) {\n      const clearcoatMapDef = {\n        index: writer.processTexture(material.clearcoatMap),\n        texCoord: material.clearcoatMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatMapDef, material.clearcoatMap)\n      extensionDef.clearcoatTexture = clearcoatMapDef\n    }\n\n    extensionDef.clearcoatRoughnessFactor = material.clearcoatRoughness\n\n    if (material.clearcoatRoughnessMap) {\n      const clearcoatRoughnessMapDef = {\n        index: writer.processTexture(material.clearcoatRoughnessMap),\n        texCoord: material.clearcoatRoughnessMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatRoughnessMapDef, material.clearcoatRoughnessMap)\n      extensionDef.clearcoatRoughnessTexture = clearcoatRoughnessMapDef\n    }\n\n    if (material.clearcoatNormalMap) {\n      const clearcoatNormalMapDef = {\n        index: writer.processTexture(material.clearcoatNormalMap),\n        texCoord: material.clearcoatNormalMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatNormalMapDef, material.clearcoatNormalMap)\n      extensionDef.clearcoatNormalTexture = clearcoatNormalMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_iridescence'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.iridescence === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.iridescenceFactor = material.iridescence\n\n    if (material.iridescenceMap) {\n      const iridescenceMapDef = {\n        index: writer.processTexture(material.iridescenceMap),\n        texCoord: material.iridescenceMap.channel,\n      }\n      writer.applyTextureTransform(iridescenceMapDef, material.iridescenceMap)\n      extensionDef.iridescenceTexture = iridescenceMapDef\n    }\n\n    extensionDef.iridescenceIor = material.iridescenceIOR\n    extensionDef.iridescenceThicknessMinimum = material.iridescenceThicknessRange[0]\n    extensionDef.iridescenceThicknessMaximum = material.iridescenceThicknessRange[1]\n\n    if (material.iridescenceThicknessMap) {\n      const iridescenceThicknessMapDef = {\n        index: writer.processTexture(material.iridescenceThicknessMap),\n        texCoord: material.iridescenceThicknessMap.channel,\n      }\n      writer.applyTextureTransform(iridescenceThicknessMapDef, material.iridescenceThicknessMap)\n      extensionDef.iridescenceThicknessTexture = iridescenceThicknessMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_transmission'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.transmissionFactor = material.transmission\n\n    if (material.transmissionMap) {\n      const transmissionMapDef = {\n        index: writer.processTexture(material.transmissionMap),\n        texCoord: material.transmissionMap.channel,\n      }\n      writer.applyTextureTransform(transmissionMapDef, material.transmissionMap)\n      extensionDef.transmissionTexture = transmissionMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_volume'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.thicknessFactor = material.thickness\n\n    if (material.thicknessMap) {\n      const thicknessMapDef = {\n        index: writer.processTexture(material.thicknessMap),\n        texCoord: material.thicknessMap.channel,\n      }\n      writer.applyTextureTransform(thicknessMapDef, material.thicknessMap)\n      extensionDef.thicknessTexture = thicknessMapDef\n    }\n\n    extensionDef.attenuationDistance = material.attenuationDistance\n    extensionDef.attenuationColor = material.attenuationColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_ior'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.ior === 1.5) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.ior = material.ior\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_specular'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (\n      !material.isMeshPhysicalMaterial ||\n      (material.specularIntensity === 1.0 &&\n        material.specularColor.equals(DEFAULT_SPECULAR_COLOR) &&\n        !material.specularIntensityMap &&\n        !material.specularColorTexture)\n    )\n      return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.specularIntensityMap) {\n      const specularIntensityMapDef = {\n        index: writer.processTexture(material.specularIntensityMap),\n        texCoord: material.specularIntensityMap.channel,\n      }\n      writer.applyTextureTransform(specularIntensityMapDef, material.specularIntensityMap)\n      extensionDef.specularTexture = specularIntensityMapDef\n    }\n\n    if (material.specularColorMap) {\n      const specularColorMapDef = {\n        index: writer.processTexture(material.specularColorMap),\n        texCoord: material.specularColorMap.channel,\n      }\n      writer.applyTextureTransform(specularColorMapDef, material.specularColorMap)\n      extensionDef.specularColorTexture = specularColorMapDef\n    }\n\n    extensionDef.specularFactor = material.specularIntensity\n    extensionDef.specularColorFactor = material.specularColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_sheen'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.sheen == 0.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.sheenRoughnessMap) {\n      const sheenRoughnessMapDef = {\n        index: writer.processTexture(material.sheenRoughnessMap),\n        texCoord: material.sheenRoughnessMap.channel,\n      }\n      writer.applyTextureTransform(sheenRoughnessMapDef, material.sheenRoughnessMap)\n      extensionDef.sheenRoughnessTexture = sheenRoughnessMapDef\n    }\n\n    if (material.sheenColorMap) {\n      const sheenColorMapDef = {\n        index: writer.processTexture(material.sheenColorMap),\n        texCoord: material.sheenColorMap.channel,\n      }\n      writer.applyTextureTransform(sheenColorMapDef, material.sheenColorMap)\n      extensionDef.sheenColorTexture = sheenColorMapDef\n    }\n\n    extensionDef.sheenRoughnessFactor = material.sheenRoughness\n    extensionDef.sheenColorFactor = material.sheenColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Anisotropy Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_anisotropy'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.anisotropy == 0.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.anisotropyMap) {\n      const anisotropyMapDef = { index: writer.processTexture(material.anisotropyMap) }\n      writer.applyTextureTransform(anisotropyMapDef, material.anisotropyMap)\n      extensionDef.anisotropyTexture = anisotropyMapDef\n    }\n\n    extensionDef.anisotropyStrength = material.anisotropy\n    extensionDef.anisotropyRotation = material.anisotropyRotation\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_emissive_strength'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshStandardMaterial || material.emissiveIntensity === 1.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.emissiveStrength = material.emissiveIntensity\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\nexport { GLTFExporter }\n"], "mappings": ";;;;;;;;;;;;;AAiCA,eAAeA,cAAcC,IAAA,EAAM;EACjC,MAAMC,MAAA,GAAS,MAAMD,IAAA,CAAKE,WAAA,CAAa;EACvC,MAAMC,IAAA,GAAOC,IAAA,CAAKC,MAAA,CAAOC,YAAA,CAAa,GAAG,IAAIC,UAAA,CAAWN,MAAM,CAAC,CAAC;EAChE,OAAO,QAAQD,IAAA,CAAKQ,IAAA,IAAQ,aAAaL,IAAA;AAC3C;AAEA,IAAIM,SAAA;AACJ,IAAIC,sBAAA;AACJ,IAAIC,sBAAA;AACJ,IAAIC,cAAA;AAEJ,SAASC,WAAWC,OAAA,EAASC,cAAA,GAAiBC,QAAA,EAAUC,QAAA,GAAW,MAAM;EACvE,IAAI,CAACP,sBAAA,EAAwBA,sBAAA,GAAyB,IAAIQ,aAAA,CAAc,GAAG,GAAG,GAAG,CAAC;EAClF,IAAI,CAACP,sBAAA,EACHA,sBAAA,GAAyB,IAAIQ,cAAA,CAAe;IAC1CC,QAAA,EAAU;MAAEC,WAAA,EAAa,IAAIC,OAAA,CAAQR,OAAO;IAAG;IAC/CS,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAOzBC,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcjC,CAAK;EAEHb,sBAAA,CAAuBS,QAAA,CAASC,WAAA,CAAYI,KAAA,GAAQX,OAAA;EACpDH,sBAAA,CAAuBe,OAAA,CAAQC,OAAA,GAC7B,gBAAgBb,OAAA,GAAUA,OAAA,CAAQc,UAAA,KAAe,SAASd,OAAA,CAAQe,QAAA,KAAa;EACjFlB,sBAAA,CAAuBmB,WAAA,GAAc;EAErC,IAAI,CAAClB,cAAA,EAAgB;IACnBA,cAAA,GAAiB,IAAImB,IAAA,CAAKrB,sBAAA,EAAwBC,sBAAsB;IACxEC,cAAA,CAAeoB,cAAA,GAAiB;EACjC;EAED,MAAMC,OAAA,GAAU,IAAIC,iBAAA,CAAmB;EACvC,MAAMC,MAAA,GAAS,IAAIC,KAAA,CAAO;EAC1BD,MAAA,CAAOE,GAAA,CAAIzB,cAAc;EAEzB,IAAI,CAACK,QAAA,EAAU;IACbA,QAAA,GAAWR,SAAA,GAAY,IAAI6B,aAAA,CAAc;MAAEC,SAAA,EAAW;IAAK,CAAE;EAC9D;EAEDtB,QAAA,CAASuB,OAAA,CAAQC,IAAA,CAAKC,GAAA,CAAI5B,OAAA,CAAQ6B,KAAA,CAAMC,KAAA,EAAO7B,cAAc,GAAG0B,IAAA,CAAKC,GAAA,CAAI5B,OAAA,CAAQ6B,KAAA,CAAME,MAAA,EAAQ9B,cAAc,CAAC;EAC9GE,QAAA,CAAS6B,KAAA,CAAO;EAChB7B,QAAA,CAAS8B,MAAA,CAAOZ,MAAA,EAAQF,OAAO;EAE/B,MAAMe,eAAA,GAAkB,IAAIC,OAAA,CAAQhC,QAAA,CAASiC,UAAU;EAEvDF,eAAA,CAAgBG,SAAA,GAAYrC,OAAA,CAAQqC,SAAA;EACpCH,eAAA,CAAgBI,SAAA,GAAYtC,OAAA,CAAQsC,SAAA;EACpCJ,eAAA,CAAgBK,KAAA,GAAQvC,OAAA,CAAQuC,KAAA;EAChCL,eAAA,CAAgBM,KAAA,GAAQxC,OAAA,CAAQwC,KAAA;EAChCN,eAAA,CAAgBO,IAAA,GAAOzC,OAAA,CAAQyC,IAAA;EAE/B,IAAI9C,SAAA,EAAW;IACbA,SAAA,CAAU+C,OAAA,CAAS;IACnB/C,SAAA,GAAY;EACb;EAED,OAAOuC,eAAA;AACT;AAOA,MAAMS,oCAAA,GAAuC;EAC3CC,QAAA,EAAU,CACR,QACA,mBACA,iBACA,4BACA,SACA,oBACA,kBACA,4BACD;EACDC,MAAA,EAAQ,CAAC,mBAAmB,kBAAkB;EAC9CC,OAAA,EAAS,CAAC,mBAAmB,kBAAkB;EAC/CC,QAAA,EAAU,CAAC,QAAQ,mBAAmB,iBAAiB,SAAS,oBAAoB,gBAAgB;AACtG;AAEK,MAACC,YAAA,GAAgC,sBAAM;EAC1C,MAAMC,aAAA,CAAa;IAkKjBC,YAAA,EAAc;MACZ,KAAKC,eAAA,GAAkB,EAAE;MAEzB,KAAKC,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIC,kBAAA,CAAmBD,MAAM;MAC5C,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIE,2BAAA,CAA4BF,MAAM;MACrD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIG,kCAAA,CAAmCH,MAAM;MAC5D,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAII,4BAAA,CAA6BJ,MAAM;MACtD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIK,yBAAA,CAA0BL,MAAM;MACnD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIM,8BAAA,CAA+BN,MAAM;MACxD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIO,+BAAA,CAAgCP,MAAM;MACzD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIQ,iCAAA,CAAkCR,MAAM;MAC3D,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIS,2BAAA,CAA4BT,MAAM;MACrD,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIU,gCAAA,CAAiCV,MAAM;MAC1D,CAAO;MAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,OAAO,IAAIW,sCAAA,CAAuCX,MAAM;MAChE,CAAO;IACF;IAEDD,SAASa,QAAA,EAAU;MACjB,IAAI,KAAKd,eAAA,CAAgBe,OAAA,CAAQD,QAAQ,MAAM,IAAI;QACjD,KAAKd,eAAA,CAAgBgB,IAAA,CAAKF,QAAQ;MACnC;MAED,OAAO;IACR;IAEDG,WAAWH,QAAA,EAAU;MACnB,IAAI,KAAKd,eAAA,CAAgBe,OAAA,CAAQD,QAAQ,MAAM,IAAI;QACjD,KAAKd,eAAA,CAAgBkB,MAAA,CAAO,KAAKlB,eAAA,CAAgBe,OAAA,CAAQD,QAAQ,GAAG,CAAC;MACtE;MAED,OAAO;IACR;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASDK,MAAMC,KAAA,EAAOC,MAAA,EAAQC,OAAA,EAASC,OAAA,EAAS;MACrC,MAAMrB,MAAA,GAAS,IAAIsB,UAAA,CAAY;MAC/B,MAAMC,OAAA,GAAU,EAAE;MAElB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAK3B,eAAA,CAAgB4B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7DD,OAAA,CAAQT,IAAA,CAAK,KAAKhB,eAAA,CAAgB0B,CAAC,EAAExB,MAAM,CAAC;MAC7C;MAEDA,MAAA,CAAO2B,UAAA,CAAWJ,OAAO;MACzBvB,MAAA,CAAO4B,KAAA,CAAMV,KAAA,EAAOC,MAAA,EAAQE,OAAO,EAAEQ,KAAA,CAAMT,OAAO;IACnD;IAEDU,WAAWZ,KAAA,EAAOG,OAAA,EAAS;MACzB,MAAMU,KAAA,GAAQ;MAEd,OAAO,IAAIC,OAAA,CAAQ,UAAUC,OAAA,EAASC,MAAA,EAAQ;QAC5CH,KAAA,CAAMd,KAAA,CAAMC,KAAA,EAAOe,OAAA,EAASC,MAAA,EAAQb,OAAO;MACnD,CAAO;IACF;EACF;EAxPC;AAAA;AAAA;EAAAc,aAAA,CAJIvC,aAAA,EAIG,SAAQ;IACbwC,cAAA,EAAgB,SAAAA,CAAUC,KAAA,EAAOC,IAAA,EAAM;MACrC,MAAMC,SAAA,GAAY;MAClB,MAAMC,SAAA,GAAYH,KAAA,CAAMI,YAAA,CAAc;MAEtC,MAAMC,KAAA,GAAQ,IAAIL,KAAA,CAAMM,cAAA,CAAeN,KAAA,CAAMK,KAAA,CAAMhB,MAAA,GAAS,CAAC;MAC7D,MAAMkB,MAAA,GAAS,IAAIP,KAAA,CAAMQ,eAAA,CAAgBR,KAAA,CAAMO,MAAA,CAAOlB,MAAA,GAASc,SAAS;MACxE,MAAMM,WAAA,GAAcT,KAAA,CAAMU,iBAAA,CAAkB,IAAIV,KAAA,CAAMQ,eAAA,CAAgBL,SAAS,CAAC;MAEhF,IAAIQ,KAAA;MAEJ,IAAIX,KAAA,CAAMK,KAAA,CAAMhB,MAAA,KAAW,GAAG;QAC5BgB,KAAA,CAAM,CAAC,IAAIJ,IAAA;QAEX,SAASd,CAAA,GAAI,GAAGA,CAAA,GAAIgB,SAAA,EAAWhB,CAAA,IAAK;UAClCoB,MAAA,CAAOpB,CAAC,IAAI;QACb;QAEDwB,KAAA,GAAQ;MACT,WAAUV,IAAA,GAAOD,KAAA,CAAMK,KAAA,CAAM,CAAC,GAAG;QAChC,IAAIpE,IAAA,CAAK2E,GAAA,CAAIZ,KAAA,CAAMK,KAAA,CAAM,CAAC,IAAIJ,IAAI,IAAIC,SAAA,EAAW,OAAO;QAExDG,KAAA,CAAM,CAAC,IAAIJ,IAAA;QACXI,KAAA,CAAMQ,GAAA,CAAIb,KAAA,CAAMK,KAAA,EAAO,CAAC;QAExBE,MAAA,CAAOM,GAAA,CAAIJ,WAAA,CAAYK,QAAA,CAASb,IAAI,GAAG,CAAC;QACxCM,MAAA,CAAOM,GAAA,CAAIb,KAAA,CAAMO,MAAA,EAAQJ,SAAS;QAElCQ,KAAA,GAAQ;MAClB,WAAmBV,IAAA,GAAOD,KAAA,CAAMK,KAAA,CAAML,KAAA,CAAMK,KAAA,CAAMhB,MAAA,GAAS,CAAC,GAAG;QACrD,IAAIpD,IAAA,CAAK2E,GAAA,CAAIZ,KAAA,CAAMK,KAAA,CAAML,KAAA,CAAMK,KAAA,CAAMhB,MAAA,GAAS,CAAC,IAAIY,IAAI,IAAIC,SAAA,EAAW;UACpE,OAAOF,KAAA,CAAMK,KAAA,CAAMhB,MAAA,GAAS;QAC7B;QAEDgB,KAAA,CAAMA,KAAA,CAAMhB,MAAA,GAAS,CAAC,IAAIY,IAAA;QAC1BI,KAAA,CAAMQ,GAAA,CAAIb,KAAA,CAAMK,KAAA,EAAO,CAAC;QAExBE,MAAA,CAAOM,GAAA,CAAIb,KAAA,CAAMO,MAAA,EAAQ,CAAC;QAC1BA,MAAA,CAAOM,GAAA,CAAIJ,WAAA,CAAYK,QAAA,CAASb,IAAI,GAAGD,KAAA,CAAMO,MAAA,CAAOlB,MAAM;QAE1DsB,KAAA,GAAQN,KAAA,CAAMhB,MAAA,GAAS;MACjC,OAAe;QACL,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAIa,KAAA,CAAMK,KAAA,CAAMhB,MAAA,EAAQF,CAAA,IAAK;UAC3C,IAAIlD,IAAA,CAAK2E,GAAA,CAAIZ,KAAA,CAAMK,KAAA,CAAMlB,CAAC,IAAIc,IAAI,IAAIC,SAAA,EAAW,OAAOf,CAAA;UAExD,IAAIa,KAAA,CAAMK,KAAA,CAAMlB,CAAC,IAAIc,IAAA,IAAQD,KAAA,CAAMK,KAAA,CAAMlB,CAAA,GAAI,CAAC,IAAIc,IAAA,EAAM;YACtDI,KAAA,CAAMQ,GAAA,CAAIb,KAAA,CAAMK,KAAA,CAAMU,KAAA,CAAM,GAAG5B,CAAA,GAAI,CAAC,GAAG,CAAC;YACxCkB,KAAA,CAAMlB,CAAA,GAAI,CAAC,IAAIc,IAAA;YACfI,KAAA,CAAMQ,GAAA,CAAIb,KAAA,CAAMK,KAAA,CAAMU,KAAA,CAAM5B,CAAA,GAAI,CAAC,GAAGA,CAAA,GAAI,CAAC;YAEzCoB,MAAA,CAAOM,GAAA,CAAIb,KAAA,CAAMO,MAAA,CAAOQ,KAAA,CAAM,IAAI5B,CAAA,GAAI,KAAKgB,SAAS,GAAG,CAAC;YACxDI,MAAA,CAAOM,GAAA,CAAIJ,WAAA,CAAYK,QAAA,CAASb,IAAI,IAAId,CAAA,GAAI,KAAKgB,SAAS;YAC1DI,MAAA,CAAOM,GAAA,CAAIb,KAAA,CAAMO,MAAA,CAAOQ,KAAA,EAAO5B,CAAA,GAAI,KAAKgB,SAAS,IAAIhB,CAAA,GAAI,KAAKgB,SAAS;YAEvEQ,KAAA,GAAQxB,CAAA,GAAI;YAEZ;UACD;QACF;MACF;MAEDa,KAAA,CAAMK,KAAA,GAAQA,KAAA;MACdL,KAAA,CAAMO,MAAA,GAASA,MAAA;MAEf,OAAOI,KAAA;IACR;IAEDK,sBAAA,EAAwB,SAAAA,CAAUC,IAAA,EAAMC,IAAA,EAAM;MAC5C,MAAMC,MAAA,GAAS,EAAE;MACjB,MAAMC,YAAA,GAAe,CAAE;MACvB,MAAMC,YAAA,GAAeJ,IAAA,CAAKE,MAAA;MAE1B,SAAShC,CAAA,GAAI,GAAGA,CAAA,GAAIkC,YAAA,CAAahC,MAAA,EAAQ,EAAEF,CAAA,EAAG;QAC5C,IAAImC,WAAA,GAAcD,YAAA,CAAalC,CAAC;QAChC,MAAMoC,kBAAA,GAAqBC,eAAA,CAAgBC,cAAA,CAAeH,WAAA,CAAYvE,IAAI;QAC1E,MAAM2E,eAAA,GAAkBF,eAAA,CAAgBG,QAAA,CAAST,IAAA,EAAMK,kBAAA,CAAmBK,QAAQ;QAElF,IACEL,kBAAA,CAAmBM,YAAA,KAAiB,2BACpCN,kBAAA,CAAmBO,aAAA,KAAkB,QACrC;UAEAX,MAAA,CAAO1C,IAAA,CAAK6C,WAAW;UACvB;QACD;QAED,IACEA,WAAA,CAAYZ,iBAAA,KAAsBY,WAAA,CAAYS,gCAAA,IAC9CT,WAAA,CAAYZ,iBAAA,KAAsBY,WAAA,CAAYU,8BAAA,EAC9C;UACA,IAAIV,WAAA,CAAYZ,iBAAA,CAAkBuB,yCAAA,EAA2C;YAG3E,MAAM,IAAIC,KAAA,CAAM,8EAA8E;UAC/F;UAEDC,OAAA,CAAQC,IAAA,CAAK,8FAA8F;UAE3Gd,WAAA,GAAcA,WAAA,CAAYe,KAAA,CAAO;UACjCf,WAAA,CAAYgB,gBAAA,CAAiBC,iBAAiB;QAC/C;QAED,MAAMC,WAAA,GAAcd,eAAA,CAAgBe,qBAAA,CAAsBpD,MAAA;QAC1D,MAAMqD,WAAA,GAAchB,eAAA,CAAgBiB,qBAAA,CAAsBpB,kBAAA,CAAmBO,aAAa;QAE1F,IAAIY,WAAA,KAAgB,QAAW;UAC7B,MAAM,IAAIR,KAAA,CAAM,sDAAsDX,kBAAA,CAAmBO,aAAa;QACvG;QAED,IAAIc,WAAA;QAIJ,IAAIxB,YAAA,CAAaM,eAAA,CAAgBmB,IAAI,MAAM,QAAW;UACpDD,WAAA,GAActB,WAAA,CAAYe,KAAA,CAAO;UAEjC,MAAM9B,MAAA,GAAS,IAAIqC,WAAA,CAAYpC,eAAA,CAAgBgC,WAAA,GAAcI,WAAA,CAAYvC,KAAA,CAAMhB,MAAM;UAErF,SAASyD,CAAA,GAAI,GAAGA,CAAA,GAAIF,WAAA,CAAYvC,KAAA,CAAMhB,MAAA,EAAQyD,CAAA,IAAK;YACjDvC,MAAA,CAAOuC,CAAA,GAAIN,WAAA,GAAcE,WAAW,IAAIE,WAAA,CAAYrC,MAAA,CAAOuC,CAAC;UAC7D;UAIDF,WAAA,CAAY7F,IAAA,IAAQwE,kBAAA,CAAmBK,QAAA,IAAY,MAAM;UACzDgB,WAAA,CAAYrC,MAAA,GAASA,MAAA;UAErBa,YAAA,CAAaM,eAAA,CAAgBmB,IAAI,IAAID,WAAA;UACrCzB,MAAA,CAAO1C,IAAA,CAAKmE,WAAW;UAEvB;QACD;QAED,MAAMG,iBAAA,GAAoBzB,WAAA,CAAYZ,iBAAA,CAAkB,IAAIY,WAAA,CAAYd,eAAA,CAAgB,CAAC,CAAC;QAE1FoC,WAAA,GAAcxB,YAAA,CAAaM,eAAA,CAAgBmB,IAAI;QAI/C,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,WAAA,CAAYvC,KAAA,CAAMhB,MAAA,EAAQyD,CAAA,IAAK;UACjDF,WAAA,CAAYrC,MAAA,CAAOuC,CAAA,GAAIN,WAAA,GAAcE,WAAW,IAAIK,iBAAA,CAAkBjC,QAAA,CAAS8B,WAAA,CAAYvC,KAAA,CAAMyC,CAAC,CAAC;QACpG;QAKD,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIxB,WAAA,CAAYjB,KAAA,CAAMhB,MAAA,EAAQyD,CAAA,IAAK;UACjD,MAAME,aAAA,GAAgB,KAAKjD,cAAA,CAAe6C,WAAA,EAAatB,WAAA,CAAYjB,KAAA,CAAMyC,CAAC,CAAC;UAC3EF,WAAA,CAAYrC,MAAA,CAAOyC,aAAA,GAAgBR,WAAA,GAAcE,WAAW,IAAIpB,WAAA,CAAYf,MAAA,CAAOuC,CAAC;QACrF;MACF;MAED7B,IAAA,CAAKE,MAAA,GAASA,MAAA;MAEd,OAAOF,IAAA;IACR;EACF;EA8FH,OAAO1D,aAAA;AACT,GAAI;AAMJ,MAAM0F,eAAA,GAAkB;EACtBC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,cAAA,EAAgB;EAChBC,YAAA,EAAc;EAEdC,IAAA,EAAM;EACNC,aAAA,EAAe;EACfC,KAAA,EAAO;EACPC,cAAA,EAAgB;EAChBC,GAAA,EAAK;EACLC,YAAA,EAAc;EACdC,KAAA,EAAO;EAEPC,YAAA,EAAc;EACdC,oBAAA,EAAsB;EAEtBC,OAAA,EAAS;EACTC,MAAA,EAAQ;EACRC,sBAAA,EAAwB;EACxBC,qBAAA,EAAuB;EACvBC,qBAAA,EAAuB;EACvBC,oBAAA,EAAsB;EAEtBC,aAAA,EAAe;EACfC,eAAA,EAAiB;EACjBC,MAAA,EAAQ;AACV;AAEA,MAAMC,qBAAA,GAAwB;AAE9B,MAAMC,cAAA,GAAiB,CAAE;AAEzBA,cAAA,CAAeC,aAAa,IAAI5B,eAAA,CAAgBiB,OAAA;AAChDU,cAAA,CAAeE,0BAA0B,IAAI7B,eAAA,CAAgBmB,sBAAA;AAC7DQ,cAAA,CAAeG,yBAAyB,IAAI9B,eAAA,CAAgBqB,qBAAA;AAC5DM,cAAA,CAAeI,YAAY,IAAI/B,eAAA,CAAgBkB,MAAA;AAC/CS,cAAA,CAAeK,yBAAyB,IAAIhC,eAAA,CAAgBoB,qBAAA;AAC5DO,cAAA,CAAeM,wBAAwB,IAAIjC,eAAA,CAAgBsB,oBAAA;AAE3DK,cAAA,CAAeO,mBAAmB,IAAIlC,eAAA,CAAgBuB,aAAA;AACtDI,cAAA,CAAeQ,cAAc,IAAInC,eAAA,CAAgByB,MAAA;AACjDE,cAAA,CAAeS,sBAAsB,IAAIpC,eAAA,CAAgBwB,eAAA;AAEzD,MAAMa,eAAA,GAAkB;EACtBC,KAAA,EAAO;EACPC,QAAA,EAAU;EACVC,UAAA,EAAY;EACZhD,qBAAA,EAAuB;AACzB;AAEA,MAAMiD,sBAAA,GAAyC,mBAAIC,KAAA,CAAO;AAK1D,MAAMC,gBAAA,GAAmB;AACzB,MAAMC,gBAAA,GAAmB;AACzB,MAAMC,WAAA,GAAc;AAEpB,MAAMC,sBAAA,GAAyB;AAC/B,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,kBAAA,GAAqB;AAY3B,SAASC,WAAWC,MAAA,EAAQC,MAAA,EAAQ;EAClC,OACED,MAAA,CAAO9G,MAAA,KAAW+G,MAAA,CAAO/G,MAAA,IACzB8G,MAAA,CAAOE,KAAA,CAAM,UAAUC,OAAA,EAAS3F,KAAA,EAAO;IACrC,OAAO2F,OAAA,KAAYF,MAAA,CAAOzF,KAAK;EACrC,CAAK;AAEL;AAOA,SAAS4F,oBAAoBC,IAAA,EAAM;EACjC,OAAO,IAAIC,WAAA,CAAa,EAACC,MAAA,CAAOF,IAAI,EAAE/M,MAAA;AACxC;AAQA,SAASkN,iBAAiBC,MAAA,EAAQ;EAChC,OAAOV,UAAA,CAAWU,MAAA,CAAOC,QAAA,EAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACrF;AASA,SAASC,UAAUC,SAAA,EAAWC,KAAA,EAAOC,KAAA,EAAO;EAC1C,MAAMC,MAAA,GAAS;IACbhL,GAAA,EAAK,IAAIiL,KAAA,CAAMJ,SAAA,CAAUK,QAAQ,EAAEC,IAAA,CAAKC,MAAA,CAAOC,iBAAiB;IAChEC,GAAA,EAAK,IAAIL,KAAA,CAAMJ,SAAA,CAAUK,QAAQ,EAAEC,IAAA,CAAKC,MAAA,CAAOG,iBAAiB;EACjE;EAED,SAAStI,CAAA,GAAI6H,KAAA,EAAO7H,CAAA,GAAI6H,KAAA,GAAQC,KAAA,EAAO9H,CAAA,IAAK;IAC1C,SAASuI,CAAA,GAAI,GAAGA,CAAA,GAAIX,SAAA,CAAUK,QAAA,EAAUM,CAAA,IAAK;MAC3C,IAAIzM,KAAA;MAEJ,IAAI8L,SAAA,CAAUK,QAAA,GAAW,GAAG;QAG1BnM,KAAA,GAAQ8L,SAAA,CAAUY,KAAA,CAAMxI,CAAA,GAAI4H,SAAA,CAAUK,QAAA,GAAWM,CAAC;MAC1D,OAAa;QACL,IAAIA,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUa,IAAA,CAAKzI,CAAC,WAC5BuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUc,IAAA,CAAK1I,CAAC,WACjCuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUe,IAAA,CAAK3I,CAAC,WACjCuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUgB,IAAA,CAAK5I,CAAC;QAE1C,IAAI4H,SAAA,CAAUiB,UAAA,KAAe,MAAM;UACjC/M,KAAA,GAAQgN,SAAA,CAAUC,SAAA,CAAUjN,KAAA,EAAO8L,SAAA,CAAUY,KAAK;QACnD;MACF;MAEDT,MAAA,CAAOhL,GAAA,CAAIwL,CAAC,IAAIzL,IAAA,CAAKC,GAAA,CAAIgL,MAAA,CAAOhL,GAAA,CAAIwL,CAAC,GAAGzM,KAAK;MAC7CiM,MAAA,CAAOM,GAAA,CAAIE,CAAC,IAAIzL,IAAA,CAAKuL,GAAA,CAAIN,MAAA,CAAOM,GAAA,CAAIE,CAAC,GAAGzM,KAAK;IAC9C;EACF;EAED,OAAOiM,MAAA;AACT;AAUA,SAASiB,oBAAoBC,UAAA,EAAY;EACvC,OAAOnM,IAAA,CAAKoM,IAAA,CAAKD,UAAA,GAAa,CAAC,IAAI;AACrC;AASA,SAASE,qBAAqB5O,WAAA,EAAa6O,WAAA,GAAc,GAAG;EAC1D,MAAMC,YAAA,GAAeL,mBAAA,CAAoBzO,WAAA,CAAY+O,UAAU;EAE/D,IAAID,YAAA,KAAiB9O,WAAA,CAAY+O,UAAA,EAAY;IAC3C,MAAMd,KAAA,GAAQ,IAAI5N,UAAA,CAAWyO,YAAY;IACzCb,KAAA,CAAM9G,GAAA,CAAI,IAAI9G,UAAA,CAAWL,WAAW,CAAC;IAErC,IAAI6O,WAAA,KAAgB,GAAG;MACrB,SAASpJ,CAAA,GAAIzF,WAAA,CAAY+O,UAAA,EAAYtJ,CAAA,GAAIqJ,YAAA,EAAcrJ,CAAA,IAAK;QAC1DwI,KAAA,CAAMxI,CAAC,IAAIoJ,WAAA;MACZ;IACF;IAED,OAAOZ,KAAA,CAAMlO,MAAA;EACd;EAED,OAAOC,WAAA;AACT;AAEA,SAASgP,UAAA,EAAY;EACnB,IAAI,OAAOC,QAAA,KAAa,eAAe,OAAOC,eAAA,KAAoB,aAAa;IAC7E,OAAO,IAAIA,eAAA,CAAgB,GAAG,CAAC;EAChC;EAED,OAAOD,QAAA,CAASE,aAAA,CAAc,QAAQ;AACxC;AAEA,SAASC,iBAAiBC,MAAA,EAAQC,QAAA,EAAU;EAC1C,IAAID,MAAA,CAAOE,MAAA,KAAW,QAAW;IAC/B,OAAO,IAAItJ,OAAA,CAASC,OAAA,IAAYmJ,MAAA,CAAOE,MAAA,CAAOrJ,OAAA,EAASoJ,QAAQ,CAAC;EACjE;EAED,IAAIE,OAAA;EAIJ,IAAIF,QAAA,KAAa,cAAc;IAC7BE,OAAA,GAAU;EACd,WAAaF,QAAA,KAAa,cAAc;IACpCE,OAAA,GAAU;EACX;EAED,OAAOH,MAAA,CAAOI,aAAA,CAAc;IAC1BnP,IAAA,EAAMgP,QAAA;IACNE;EACJ,CAAG;AACH;AAKA,MAAMjK,UAAA,CAAW;EACfzB,YAAA,EAAc;IACZ,KAAK0B,OAAA,GAAU,EAAE;IAEjB,KAAKF,OAAA,GAAU,CAAE;IACjB,KAAKoK,OAAA,GAAU,EAAE;IACjB,KAAKC,OAAA,GAAU,EAAE;IAEjB,KAAKC,UAAA,GAAa;IAClB,KAAKD,OAAA,GAAU,EAAE;IACjB,KAAKE,OAAA,GAAU,mBAAIC,GAAA,CAAK;IACxB,KAAKC,KAAA,GAAQ,EAAE;IAEf,KAAKC,cAAA,GAAiB,CAAE;IACxB,KAAKC,kBAAA,GAAqB,CAAE;IAE5B,KAAKC,IAAA,GAAO,mBAAIJ,GAAA,CAAK;IACrB,KAAKK,GAAA,GAAM;IAEX,KAAKC,IAAA,GAAO;MACVC,KAAA,EAAO;QACLC,OAAA,EAAS;QACTC,SAAA,EAAW;MACZ;IACF;IAED,KAAKC,KAAA,GAAQ;MACXC,MAAA,EAAQ,mBAAIX,GAAA,CAAK;MACjBY,UAAA,EAAY,mBAAIZ,GAAA,CAAK;MACrBa,oBAAA,EAAsB,mBAAIb,GAAA,CAAK;MAC/Bc,SAAA,EAAW,mBAAId,GAAA,CAAK;MACpBe,QAAA,EAAU,mBAAIf,GAAA,CAAK;MACnBgB,MAAA,EAAQ,mBAAIhB,GAAA,CAAK;IAClB;EACF;EAEDlK,WAAWJ,OAAA,EAAS;IAClB,KAAKA,OAAA,GAAUA,OAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQD,MAAMK,MAAMV,KAAA,EAAOC,MAAA,EAAQE,OAAA,GAAU,IAAI;IACvC,KAAKA,OAAA,GAAUyL,MAAA,CAAOC,MAAA,CACpB;MAAA;MAEEC,MAAA,EAAQ;MACRC,GAAA,EAAK;MACLC,WAAA,EAAa;MACbtQ,cAAA,EAAgBC,QAAA;MAChBsQ,UAAA,EAAY,EAAE;MACdC,uBAAA,EAAyB;IAC1B,GACD/L,OACD;IAED,IAAI,KAAKA,OAAA,CAAQ8L,UAAA,CAAWzL,MAAA,GAAS,GAAG;MAEtC,KAAKL,OAAA,CAAQ4L,GAAA,GAAM;IACpB;IAED,KAAKI,YAAA,CAAanM,KAAK;IAEvB,MAAMc,OAAA,CAAQsL,GAAA,CAAI,KAAK7B,OAAO;IAE9B,MAAMzL,MAAA,GAAS;IACf,MAAM0L,OAAA,GAAU1L,MAAA,CAAO0L,OAAA;IACvB,MAAMS,IAAA,GAAOnM,MAAA,CAAOmM,IAAA;IACpB9K,OAAA,GAAUrB,MAAA,CAAOqB,OAAA;IAEjB,MAAM0K,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAC9B,MAAMC,kBAAA,GAAqBhM,MAAA,CAAOgM,kBAAA;IAGlC,MAAMnQ,IAAA,GAAO,IAAI0R,IAAA,CAAK7B,OAAA,EAAS;MAAErP,IAAA,EAAM;IAAA,CAA4B;IAGnE,MAAMmR,kBAAA,GAAqBV,MAAA,CAAOW,IAAA,CAAK1B,cAAc;IACrD,MAAM2B,sBAAA,GAAyBZ,MAAA,CAAOW,IAAA,CAAKzB,kBAAkB;IAE7D,IAAIwB,kBAAA,CAAmB9L,MAAA,GAAS,GAAGyK,IAAA,CAAKJ,cAAA,GAAiByB,kBAAA;IACzD,IAAIE,sBAAA,CAAuBhM,MAAA,GAAS,GAAGyK,IAAA,CAAKH,kBAAA,GAAqB0B,sBAAA;IAGjE,IAAIvB,IAAA,CAAKT,OAAA,IAAWS,IAAA,CAAKT,OAAA,CAAQhK,MAAA,GAAS,GAAGyK,IAAA,CAAKT,OAAA,CAAQ,CAAC,EAAEZ,UAAA,GAAajP,IAAA,CAAK8R,IAAA;IAE/E,IAAItM,OAAA,CAAQ2L,MAAA,KAAW,MAAM;MAG3BnR,IAAA,CAAKE,WAAA,CAAW,EAAG6R,IAAA,CAAMC,MAAA,IAAW;QAElC,MAAMC,WAAA,GAAcnD,oBAAA,CAAqBkD,MAAM;QAC/C,MAAME,iBAAA,GAAoB,IAAIC,QAAA,CAAS,IAAIC,WAAA,CAAY7F,sBAAsB,CAAC;QAC9E2F,iBAAA,CAAkBG,SAAA,CAAU,GAAGJ,WAAA,CAAYhD,UAAA,EAAY,IAAI;QAC3DiD,iBAAA,CAAkBG,SAAA,CAAU,GAAG5F,kBAAA,EAAoB,IAAI;QAGvD,MAAM6F,SAAA,GAAYxD,oBAAA,CAAqB/B,mBAAA,CAAoBwF,IAAA,CAAKC,SAAA,CAAUlC,IAAI,CAAC,GAAG,EAAI;QACtF,MAAMmC,eAAA,GAAkB,IAAIN,QAAA,CAAS,IAAIC,WAAA,CAAY7F,sBAAsB,CAAC;QAC5EkG,eAAA,CAAgBJ,SAAA,CAAU,GAAGC,SAAA,CAAUrD,UAAA,EAAY,IAAI;QACvDwD,eAAA,CAAgBJ,SAAA,CAAU,GAAG7F,mBAAA,EAAqB,IAAI;QAGtD,MAAMkG,MAAA,GAAS,IAAIN,WAAA,CAAYhG,gBAAgB;QAC/C,MAAMuG,UAAA,GAAa,IAAIR,QAAA,CAASO,MAAM;QACtCC,UAAA,CAAWN,SAAA,CAAU,GAAGhG,gBAAA,EAAkB,IAAI;QAC9CsG,UAAA,CAAWN,SAAA,CAAU,GAAG/F,WAAA,EAAa,IAAI;QACzC,MAAMsG,eAAA,GACJxG,gBAAA,GACAqG,eAAA,CAAgBxD,UAAA,GAChBqD,SAAA,CAAUrD,UAAA,GACViD,iBAAA,CAAkBjD,UAAA,GAClBgD,WAAA,CAAYhD,UAAA;QACd0D,UAAA,CAAWN,SAAA,CAAU,GAAGO,eAAA,EAAiB,IAAI;QAE7C,MAAMC,OAAA,GAAU,IAAInB,IAAA,CAAK,CAACgB,MAAA,EAAQD,eAAA,EAAiBH,SAAA,EAAWJ,iBAAA,EAAmBD,WAAW,GAAG;UAC7FzR,IAAA,EAAM;QAChB,CAAS;QAEDqS,OAAA,CAAQ3S,WAAA,CAAW,EAAG6R,IAAA,CAAKzM,MAAM;MACzC,CAAO;IACP,OAAW;MACL,IAAIgL,IAAA,CAAKT,OAAA,IAAWS,IAAA,CAAKT,OAAA,CAAQhK,MAAA,GAAS,GAAG;QAC3C9F,aAAA,CAAcC,IAAI,EAAE+R,IAAA,CAAMe,GAAA,IAAQ;UAChCxC,IAAA,CAAKT,OAAA,CAAQ,CAAC,EAAEiD,GAAA,GAAMA,GAAA;UACtBxN,MAAA,CAAOgL,IAAI;QACrB,CAAS;MACT,OAAa;QACLhL,MAAA,CAAOgL,IAAI;MACZ;IACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDyC,kBAAkBC,MAAA,EAAQC,SAAA,EAAW;IACnC,IAAIhC,MAAA,CAAOW,IAAA,CAAKoB,MAAA,CAAOE,QAAQ,EAAErN,MAAA,KAAW,GAAG;IAE/C,MAAML,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAM0K,cAAA,GAAiB,KAAKA,cAAA;IAE5B,IAAI;MACF,MAAMI,IAAA,GAAOiC,IAAA,CAAKnN,KAAA,CAAMmN,IAAA,CAAKC,SAAA,CAAUQ,MAAA,CAAOE,QAAQ,CAAC;MAEvD,IAAI1N,OAAA,CAAQ+L,uBAAA,IAA2BjB,IAAA,CAAK6C,cAAA,EAAgB;QAC1D,IAAIF,SAAA,CAAUG,UAAA,KAAe,QAAWH,SAAA,CAAUG,UAAA,GAAa,CAAE;QAEjE,WAAWC,aAAA,IAAiB/C,IAAA,CAAK6C,cAAA,EAAgB;UAC/CF,SAAA,CAAUG,UAAA,CAAWC,aAAa,IAAI/C,IAAA,CAAK6C,cAAA,CAAeE,aAAa;UACvEnD,cAAA,CAAemD,aAAa,IAAI;QACjC;QAED,OAAO/C,IAAA,CAAK6C,cAAA;MACb;MAED,IAAIlC,MAAA,CAAOW,IAAA,CAAKtB,IAAI,EAAEzK,MAAA,GAAS,GAAGoN,SAAA,CAAUK,MAAA,GAAShD,IAAA;IACtD,SAAQiD,KAAA,EAAP;MACA5K,OAAA,CAAQC,IAAA,CACN,sCACEoK,MAAA,CAAOzP,IAAA,GACP,6DAEAgQ,KAAA,CAAMC,OACT;IACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAODC,OAAOlG,SAAA,EAAWmG,cAAA,GAAiB,OAAO;IACxC,IAAI,KAAKtD,IAAA,CAAKuD,GAAA,CAAIpG,SAAS,MAAM,OAAO;MACtC,MAAMqG,KAAA,GAAO,mBAAI5D,GAAA,CAAK;MAEtB4D,KAAA,CAAKvM,GAAA,CAAI,MAAM,KAAKgJ,GAAA,EAAK;MACzBuD,KAAA,CAAKvM,GAAA,CAAI,OAAO,KAAKgJ,GAAA,EAAK;MAE1B,KAAKD,IAAA,CAAK/I,GAAA,CAAIkG,SAAA,EAAWqG,KAAI;IAC9B;IAED,MAAMxD,IAAA,GAAO,KAAKA,IAAA,CAAKyD,GAAA,CAAItG,SAAS;IAEpC,OAAO6C,IAAA,CAAKyD,GAAA,CAAIH,cAAc;EAC/B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDI,4BAA4BC,MAAA,EAAQ;IAClC,MAAMrD,KAAA,GAAQ,KAAKA,KAAA;IAEnB,IAAIA,KAAA,CAAMG,oBAAA,CAAqB8C,GAAA,CAAII,MAAM,GAAG,OAAO;IAEnD,MAAMC,CAAA,GAAI,IAAIC,OAAA,CAAS;IAEvB,SAAStO,CAAA,GAAI,GAAGC,EAAA,GAAKmO,MAAA,CAAOtG,KAAA,EAAO9H,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAE9C,IAAIlD,IAAA,CAAK2E,GAAA,CAAI4M,CAAA,CAAEE,mBAAA,CAAoBH,MAAA,EAAQpO,CAAC,EAAEE,MAAA,CAAM,IAAK,CAAG,IAAI,MAAQ,OAAO;IAChF;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDsO,gCAAgCJ,MAAA,EAAQ;IACtC,MAAMrD,KAAA,GAAQ,KAAKA,KAAA;IAEnB,IAAIA,KAAA,CAAMG,oBAAA,CAAqB8C,GAAA,CAAII,MAAM,GAAG,OAAOrD,KAAA,CAAMG,oBAAA,CAAqBgD,GAAA,CAAIE,MAAM;IAExF,MAAMxG,SAAA,GAAYwG,MAAA,CAAOlL,KAAA,CAAO;IAChC,MAAMmL,CAAA,GAAI,IAAIC,OAAA,CAAS;IAEvB,SAAStO,CAAA,GAAI,GAAGC,EAAA,GAAK2H,SAAA,CAAUE,KAAA,EAAO9H,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACjDqO,CAAA,CAAEE,mBAAA,CAAoB3G,SAAA,EAAW5H,CAAC;MAElC,IAAIqO,CAAA,CAAEI,CAAA,KAAM,KAAKJ,CAAA,CAAEK,CAAA,KAAM,KAAKL,CAAA,CAAEM,CAAA,KAAM,GAAG;QAEvCN,CAAA,CAAEO,IAAA,CAAK,CAAG;MAClB,OAAa;QACLP,CAAA,CAAEtF,SAAA,CAAW;MACd;MAEDnB,SAAA,CAAUiH,MAAA,CAAO7O,CAAA,EAAGqO,CAAA,CAAEI,CAAA,EAAGJ,CAAA,CAAEK,CAAA,EAAGL,CAAA,CAAEM,CAAC;IAClC;IAED5D,KAAA,CAAMG,oBAAA,CAAqBxJ,GAAA,CAAI0M,MAAA,EAAQxG,SAAS;IAEhD,OAAOA,SAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDkH,sBAAsBC,MAAA,EAAQ5T,OAAA,EAAS;IACrC,IAAI6T,YAAA,GAAe;IACnB,MAAMC,YAAA,GAAe,CAAE;IAEvB,IAAI9T,OAAA,CAAQ+T,MAAA,CAAOT,CAAA,KAAM,KAAKtT,OAAA,CAAQ+T,MAAA,CAAOR,CAAA,KAAM,GAAG;MACpDO,YAAA,CAAaC,MAAA,GAAS/T,OAAA,CAAQ+T,MAAA,CAAOC,OAAA,CAAS;MAC9CH,YAAA,GAAe;IAChB;IAED,IAAI7T,OAAA,CAAQiU,QAAA,KAAa,GAAG;MAC1BH,YAAA,CAAaG,QAAA,GAAWjU,OAAA,CAAQiU,QAAA;MAChCJ,YAAA,GAAe;IAChB;IAED,IAAI7T,OAAA,CAAQkU,MAAA,CAAOZ,CAAA,KAAM,KAAKtT,OAAA,CAAQkU,MAAA,CAAOX,CAAA,KAAM,GAAG;MACpDO,YAAA,CAAa7I,KAAA,GAAQjL,OAAA,CAAQkU,MAAA,CAAOF,OAAA,CAAS;MAC7CH,YAAA,GAAe;IAChB;IAED,IAAIA,YAAA,EAAc;MAChBD,MAAA,CAAOtB,UAAA,GAAasB,MAAA,CAAOtB,UAAA,IAAc,CAAE;MAC3CsB,MAAA,CAAOtB,UAAA,CAAW,uBAAuB,IAAIwB,YAAA;MAC7C,KAAK1E,cAAA,CAAe,uBAAuB,IAAI;IAChD;EACF;EAED+E,uBAAuBC,YAAA,EAAcC,YAAA,EAAc;IACjD,IAAID,YAAA,KAAiBC,YAAA,EAAc,OAAOD,YAAA;IAE1C,SAASE,sBAAsBC,GAAA,EAAK;MAClC,IAAI,gBAAgBA,GAAA,GAAMA,GAAA,CAAIzT,UAAA,KAAe,SAASyT,GAAA,CAAIxT,QAAA,KAAa,MAAM;QAC3E,OAAO,SAASyT,aAAaC,CAAA,EAAG;UAC9B,OAAOA,CAAA,GAAI,UAAUA,CAAA,GAAI,eAAe9S,IAAA,CAAK+S,GAAA,CAAID,CAAA,GAAI,eAAe,cAAc,GAAG;QACtF;MACF;MAED,OAAO,SAASE,eAAeF,CAAA,EAAG;QAChC,OAAOA,CAAA;MACR;IACF;IAED5M,OAAA,CAAQC,IAAA,CAAK,oEAAoE;IAEjF,IAAIsM,YAAA,YAAwBQ,iBAAA,EAAmB;MAC7CR,YAAA,GAAerU,UAAA,CAAWqU,YAAY;IACvC;IAED,IAAIC,YAAA,YAAwBO,iBAAA,EAAmB;MAC7CP,YAAA,GAAetU,UAAA,CAAWsU,YAAY;IACvC;IAED,MAAMQ,SAAA,GAAYT,YAAA,GAAeA,YAAA,CAAavS,KAAA,GAAQ;IACtD,MAAMiT,SAAA,GAAYT,YAAA,GAAeA,YAAA,CAAaxS,KAAA,GAAQ;IAEtD,MAAMC,KAAA,GAAQH,IAAA,CAAKuL,GAAA,CAAI2H,SAAA,GAAYA,SAAA,CAAU/S,KAAA,GAAQ,GAAGgT,SAAA,GAAYA,SAAA,CAAUhT,KAAA,GAAQ,CAAC;IACvF,MAAMC,MAAA,GAASJ,IAAA,CAAKuL,GAAA,CAAI2H,SAAA,GAAYA,SAAA,CAAU9S,MAAA,GAAS,GAAG+S,SAAA,GAAYA,SAAA,CAAU/S,MAAA,GAAS,CAAC;IAE1F,MAAM0M,MAAA,GAASL,SAAA,CAAW;IAC1BK,MAAA,CAAO3M,KAAA,GAAQA,KAAA;IACf2M,MAAA,CAAO1M,MAAA,GAASA,MAAA;IAEhB,MAAMgT,OAAA,GAAUtG,MAAA,CAAOuG,UAAA,CAAW,IAAI;IACtCD,OAAA,CAAQE,SAAA,GAAY;IACpBF,OAAA,CAAQG,QAAA,CAAS,GAAG,GAAGpT,KAAA,EAAOC,MAAM;IAEpC,MAAMoT,SAAA,GAAYJ,OAAA,CAAQK,YAAA,CAAa,GAAG,GAAGtT,KAAA,EAAOC,MAAM;IAE1D,IAAI8S,SAAA,EAAW;MACbE,OAAA,CAAQM,SAAA,CAAUR,SAAA,EAAW,GAAG,GAAG/S,KAAA,EAAOC,MAAM;MAEhD,MAAMuT,OAAA,GAAUhB,qBAAA,CAAsBF,YAAY;MAClD,MAAM/U,IAAA,GAAO0V,OAAA,CAAQK,YAAA,CAAa,GAAG,GAAGtT,KAAA,EAAOC,MAAM,EAAE1C,IAAA;MAEvD,SAASwF,CAAA,GAAI,GAAGA,CAAA,GAAIxF,IAAA,CAAK0F,MAAA,EAAQF,CAAA,IAAK,GAAG;QACvCsQ,SAAA,CAAU9V,IAAA,CAAKwF,CAAC,IAAIyQ,OAAA,CAAQjW,IAAA,CAAKwF,CAAC,IAAI,GAAG,IAAI;MAC9C;IACF;IAED,IAAIiQ,SAAA,EAAW;MACbC,OAAA,CAAQM,SAAA,CAAUP,SAAA,EAAW,GAAG,GAAGhT,KAAA,EAAOC,MAAM;MAEhD,MAAMuT,OAAA,GAAUhB,qBAAA,CAAsBD,YAAY;MAClD,MAAMhV,IAAA,GAAO0V,OAAA,CAAQK,YAAA,CAAa,GAAG,GAAGtT,KAAA,EAAOC,MAAM,EAAE1C,IAAA;MAEvD,SAASwF,CAAA,GAAI,GAAGA,CAAA,GAAIxF,IAAA,CAAK0F,MAAA,EAAQF,CAAA,IAAK,GAAG;QACvCsQ,SAAA,CAAU9V,IAAA,CAAKwF,CAAC,IAAIyQ,OAAA,CAAQjW,IAAA,CAAKwF,CAAC,IAAI,GAAG,IAAI;MAC9C;IACF;IAEDkQ,OAAA,CAAQQ,YAAA,CAAaJ,SAAA,EAAW,GAAG,CAAC;IAIpC,MAAMK,SAAA,GAAYpB,YAAA,IAAgBC,YAAA;IAElC,MAAMrU,OAAA,GAAUwV,SAAA,CAAUzN,KAAA,CAAO;IAGjC/H,OAAA,CAAQyV,MAAA,GAAS,IAAItT,OAAA,CAAQsM,MAAM,EAAEgH,MAAA;IACrC,IAAI,gBAAgBzV,OAAA,EAASA,OAAA,CAAQc,UAAA,GAAa,QAC7Cd,OAAA,CAAQe,QAAA,GAAW;IACxBf,OAAA,CAAQ0V,OAAA,IAAWtB,YAAA,IAAgBC,YAAA,EAAcqB,OAAA;IAEjD,IAAItB,YAAA,IAAgBC,YAAA,IAAgBD,YAAA,CAAasB,OAAA,KAAYrB,YAAA,CAAaqB,OAAA,EAAS;MACjF7N,OAAA,CAAQC,IAAA,CAAK,wFAAwF;IACtG;IAED,OAAO9H,OAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD2V,cAAcxW,MAAA,EAAQ;IACpB,MAAMqQ,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMT,OAAA,GAAU,KAAKA,OAAA;IAErB,IAAI,CAACS,IAAA,CAAKT,OAAA,EAASS,IAAA,CAAKT,OAAA,GAAU,CAAC;MAAEZ,UAAA,EAAY;IAAA,CAAG;IAGpDY,OAAA,CAAQ5K,IAAA,CAAKhF,MAAM;IAEnB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWDyW,kBAAkBnJ,SAAA,EAAWoJ,aAAA,EAAenJ,KAAA,EAAOC,KAAA,EAAOmJ,MAAA,EAAQ;IAChE,MAAMtG,IAAA,GAAO,KAAKA,IAAA;IAElB,IAAI,CAACA,IAAA,CAAKuG,WAAA,EAAavG,IAAA,CAAKuG,WAAA,GAAc,EAAE;IAI5C,IAAIC,aAAA;IAEJ,QAAQH,aAAA;MACN,KAAKlN,eAAA,CAAgBQ,IAAA;MACrB,KAAKR,eAAA,CAAgBS,aAAA;QACnB4M,aAAA,GAAgB;QAEhB;MAEF,KAAKrN,eAAA,CAAgBU,KAAA;MACrB,KAAKV,eAAA,CAAgBW,cAAA;QACnB0M,aAAA,GAAgB;QAEhB;MAEF;QACEA,aAAA,GAAgB;IACnB;IAED,IAAIC,UAAA,GAAaxJ,SAAA,CAAUK,QAAA,GAAWkJ,aAAA;IACtC,IAAIF,MAAA,KAAWnN,eAAA,CAAgBe,YAAA,EAAc;MAG3CuM,UAAA,GAAatU,IAAA,CAAKoM,IAAA,CAAKkI,UAAA,GAAa,CAAC,IAAI;IAC1C;IACD,MAAM9H,UAAA,GAAaN,mBAAA,CAAoBlB,KAAA,GAAQsJ,UAAU;IACzD,MAAMC,QAAA,GAAW,IAAI7E,QAAA,CAAS,IAAIC,WAAA,CAAYnD,UAAU,CAAC;IACzD,IAAI4F,MAAA,GAAS;IAEb,SAASlP,CAAA,GAAI6H,KAAA,EAAO7H,CAAA,GAAI6H,KAAA,GAAQC,KAAA,EAAO9H,CAAA,IAAK;MAC1C,SAASuI,CAAA,GAAI,GAAGA,CAAA,GAAIX,SAAA,CAAUK,QAAA,EAAUM,CAAA,IAAK;QAC3C,IAAIzM,KAAA;QAEJ,IAAI8L,SAAA,CAAUK,QAAA,GAAW,GAAG;UAG1BnM,KAAA,GAAQ8L,SAAA,CAAUY,KAAA,CAAMxI,CAAA,GAAI4H,SAAA,CAAUK,QAAA,GAAWM,CAAC;QAC5D,OAAe;UACL,IAAIA,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUa,IAAA,CAAKzI,CAAC,WAC5BuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUc,IAAA,CAAK1I,CAAC,WACjCuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUe,IAAA,CAAK3I,CAAC,WACjCuI,CAAA,KAAM,GAAGzM,KAAA,GAAQ8L,SAAA,CAAUgB,IAAA,CAAK5I,CAAC;UAE1C,IAAI4H,SAAA,CAAUiB,UAAA,KAAe,MAAM;YACjC/M,KAAA,GAAQgN,SAAA,CAAUC,SAAA,CAAUjN,KAAA,EAAO8L,SAAA,CAAUY,KAAK;UACnD;QACF;QAED,IAAIwI,aAAA,KAAkBlN,eAAA,CAAgBc,KAAA,EAAO;UAC3CyM,QAAA,CAASC,UAAA,CAAWpC,MAAA,EAAQpT,KAAA,EAAO,IAAI;QACjD,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBY,GAAA,EAAK;UAChD2M,QAAA,CAASE,QAAA,CAASrC,MAAA,EAAQpT,KAAA,EAAO,IAAI;QAC/C,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBa,YAAA,EAAc;UACzD0M,QAAA,CAAS3E,SAAA,CAAUwC,MAAA,EAAQpT,KAAA,EAAO,IAAI;QAChD,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBU,KAAA,EAAO;UAClD6M,QAAA,CAASG,QAAA,CAAStC,MAAA,EAAQpT,KAAA,EAAO,IAAI;QAC/C,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBW,cAAA,EAAgB;UAC3D4M,QAAA,CAASI,SAAA,CAAUvC,MAAA,EAAQpT,KAAA,EAAO,IAAI;QAChD,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBQ,IAAA,EAAM;UACjD+M,QAAA,CAASK,OAAA,CAAQxC,MAAA,EAAQpT,KAAK;QACxC,WAAmBkV,aAAA,KAAkBlN,eAAA,CAAgBS,aAAA,EAAe;UAC1D8M,QAAA,CAASM,QAAA,CAASzC,MAAA,EAAQpT,KAAK;QAChC;QAEDoT,MAAA,IAAUiC,aAAA;MACX;MACD,IAAIjC,MAAA,GAASkC,UAAA,KAAe,GAAG;QAC7BlC,MAAA,IAAUkC,UAAA,GAAclC,MAAA,GAASkC,UAAA;MAClC;IACF;IAED,MAAMQ,aAAA,GAAgB;MACpBtX,MAAA,EAAQ,KAAKwW,aAAA,CAAcO,QAAA,CAAS/W,MAAM;MAC1C6P,UAAA,EAAY,KAAKA,UAAA;MACjBb;IACD;IAED,IAAI2H,MAAA,KAAW,QAAWW,aAAA,CAAcX,MAAA,GAASA,MAAA;IAEjD,IAAIA,MAAA,KAAWnN,eAAA,CAAgBe,YAAA,EAAc;MAE3C+M,aAAA,CAAcR,UAAA,GAAaA,UAAA;IAC5B;IAED,KAAKjH,UAAA,IAAcb,UAAA;IAEnBqB,IAAA,CAAKuG,WAAA,CAAY5R,IAAA,CAAKsS,aAAa;IAGnC,MAAM7J,MAAA,GAAS;MACb8J,EAAA,EAAIlH,IAAA,CAAKuG,WAAA,CAAYhR,MAAA,GAAS;MAC9BoJ,UAAA,EAAY;IACb;IAED,OAAOvB,MAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD+J,uBAAuBzX,IAAA,EAAM;IAC3B,MAAMmE,MAAA,GAAS;IACf,MAAMmM,IAAA,GAAOnM,MAAA,CAAOmM,IAAA;IAEpB,IAAI,CAACA,IAAA,CAAKuG,WAAA,EAAavG,IAAA,CAAKuG,WAAA,GAAc,EAAE;IAE5C,OAAO7W,IAAA,CAAKE,WAAA,CAAW,EAAG6R,IAAA,CAAMC,MAAA,IAAW;MACzC,MAAM/R,MAAA,GAAS6O,oBAAA,CAAqBkD,MAAM;MAE1C,MAAMuF,aAAA,GAAgB;QACpBtX,MAAA,EAAQkE,MAAA,CAAOsS,aAAA,CAAcxW,MAAM;QACnC6P,UAAA,EAAY3L,MAAA,CAAO2L,UAAA;QACnBb,UAAA,EAAYhP,MAAA,CAAOgP;MACpB;MAED9K,MAAA,CAAO2L,UAAA,IAAc7P,MAAA,CAAOgP,UAAA;MAC5B,OAAOqB,IAAA,CAAKuG,WAAA,CAAY5R,IAAA,CAAKsS,aAAa,IAAI;IACpD,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDG,gBAAgBnK,SAAA,EAAWoK,QAAA,EAAUnK,KAAA,EAAOC,KAAA,EAAO;IACjD,MAAM6C,IAAA,GAAO,KAAKA,IAAA;IAElB,MAAMsH,KAAA,GAAQ;MACZ,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;MACH,IAAI;IACL;IAED,IAAIjB,aAAA;IAGJ,IAAIpJ,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgB6T,YAAA,EAAc;MAChDlB,aAAA,GAAgBlN,eAAA,CAAgBc,KAAA;IACjC,WAAUgD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgB8T,UAAA,EAAY;MACrDnB,aAAA,GAAgBlN,eAAA,CAAgBY,GAAA;IACjC,WAAUkD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgB+T,WAAA,EAAa;MACtDpB,aAAA,GAAgBlN,eAAA,CAAgBa,YAAA;IACjC,WAAUiD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgBgU,UAAA,EAAY;MACrDrB,aAAA,GAAgBlN,eAAA,CAAgBU,KAAA;IACjC,WAAUoD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgBiU,WAAA,EAAa;MACtDtB,aAAA,GAAgBlN,eAAA,CAAgBW,cAAA;IACjC,WAAUmD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgBkU,SAAA,EAAW;MACpDvB,aAAA,GAAgBlN,eAAA,CAAgBQ,IAAA;IACjC,WAAUsD,SAAA,CAAUY,KAAA,CAAMnK,WAAA,KAAgBzD,UAAA,EAAY;MACrDoW,aAAA,GAAgBlN,eAAA,CAAgBS,aAAA;IACtC,OAAW;MACL,MAAM,IAAIxB,KAAA,CACR,qEAAqE6E,SAAA,CAAUY,KAAA,CAAMnK,WAAA,CAAYT,IAClG;IACF;IAED,IAAIiK,KAAA,KAAU,QAAWA,KAAA,GAAQ;IACjC,IAAIC,KAAA,KAAU,QAAWA,KAAA,GAAQF,SAAA,CAAUE,KAAA;IAG3C,IAAIA,KAAA,KAAU,GAAG,OAAO;IAExB,MAAM0K,MAAA,GAAS7K,SAAA,CAAUC,SAAA,EAAWC,KAAA,EAAOC,KAAK;IAChD,IAAI2K,gBAAA;IAIJ,IAAIT,QAAA,KAAa,QAAW;MAC1BS,gBAAA,GACE7K,SAAA,KAAcoK,QAAA,CAASxQ,KAAA,GAAQsC,eAAA,CAAgBgB,oBAAA,GAAuBhB,eAAA,CAAgBe,YAAA;IACzF;IAED,MAAM6N,UAAA,GAAa,KAAK3B,iBAAA,CAAkBnJ,SAAA,EAAWoJ,aAAA,EAAenJ,KAAA,EAAOC,KAAA,EAAO2K,gBAAgB;IAElG,MAAME,WAAA,GAAc;MAClBD,UAAA,EAAYA,UAAA,CAAWb,EAAA;MACvB1H,UAAA,EAAYuI,UAAA,CAAWvI,UAAA;MACvB6G,aAAA;MACAlJ,KAAA;MACAO,GAAA,EAAKmK,MAAA,CAAOnK,GAAA;MACZtL,GAAA,EAAKyV,MAAA,CAAOzV,GAAA;MACZlC,IAAA,EAAMoX,KAAA,CAAMrK,SAAA,CAAUK,QAAQ;IAC/B;IAED,IAAIL,SAAA,CAAUiB,UAAA,KAAe,MAAM8J,WAAA,CAAY9J,UAAA,GAAa;IAC5D,IAAI,CAAC8B,IAAA,CAAKiI,SAAA,EAAWjI,IAAA,CAAKiI,SAAA,GAAY,EAAE;IAExC,OAAOjI,IAAA,CAAKiI,SAAA,CAAUtT,IAAA,CAAKqT,WAAW,IAAI;EAC3C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDE,aAAa7V,KAAA,EAAO8V,MAAA,EAAQC,KAAA,EAAOlJ,QAAA,GAAW,aAAa;IACzD,IAAI7M,KAAA,KAAU,MAAM;MAClB,MAAMwB,MAAA,GAAS;MACf,MAAMuM,KAAA,GAAQvM,MAAA,CAAOuM,KAAA;MACrB,MAAMJ,IAAA,GAAOnM,MAAA,CAAOmM,IAAA;MACpB,MAAM9K,OAAA,GAAUrB,MAAA,CAAOqB,OAAA;MACvB,MAAMoK,OAAA,GAAUzL,MAAA,CAAOyL,OAAA;MAEvB,IAAI,CAACc,KAAA,CAAMM,MAAA,CAAO2C,GAAA,CAAIhR,KAAK,GAAG+N,KAAA,CAAMM,MAAA,CAAO3J,GAAA,CAAI1E,KAAA,EAAO,EAAE;MAExD,MAAMgW,YAAA,GAAejI,KAAA,CAAMM,MAAA,CAAO6C,GAAA,CAAIlR,KAAK;MAE3C,MAAMiW,GAAA,GAAMpJ,QAAA,GAAW,YAAYkJ,KAAA,CAAMG,QAAA,CAAU;MAEnD,IAAIF,YAAA,CAAaC,GAAG,MAAM,QAAW,OAAOD,YAAA,CAAaC,GAAG;MAE5D,IAAI,CAACtI,IAAA,CAAKU,MAAA,EAAQV,IAAA,CAAKU,MAAA,GAAS,EAAE;MAElC,MAAM8H,QAAA,GAAW;QAAEtJ;MAAoB;MAEvC,MAAMD,MAAA,GAASL,SAAA,CAAW;MAE1BK,MAAA,CAAO3M,KAAA,GAAQH,IAAA,CAAKC,GAAA,CAAIC,KAAA,CAAMC,KAAA,EAAO4C,OAAA,CAAQzE,cAAc;MAC3DwO,MAAA,CAAO1M,MAAA,GAASJ,IAAA,CAAKC,GAAA,CAAIC,KAAA,CAAME,MAAA,EAAQ2C,OAAA,CAAQzE,cAAc;MAE7D,MAAMgY,GAAA,GAAMxJ,MAAA,CAAOuG,UAAA,CAAW,IAAI;MAElC,IAAI4C,KAAA,KAAU,MAAM;QAClBK,GAAA,CAAIC,SAAA,CAAU,GAAGzJ,MAAA,CAAO1M,MAAM;QAC9BkW,GAAA,CAAIhN,KAAA,CAAM,GAAG,EAAE;MAChB;MAED,IAAIpJ,KAAA,CAAMxC,IAAA,KAAS,QAAW;QAG5B,IAAIsY,MAAA,KAAWQ,UAAA,EAAY;UACzBtQ,OAAA,CAAQ4K,KAAA,CAAM,+CAA+CkF,MAAM;QACpE;QAED,IAAI9V,KAAA,CAAMC,KAAA,GAAQ4C,OAAA,CAAQzE,cAAA,IAAkB4B,KAAA,CAAME,MAAA,GAAS2C,OAAA,CAAQzE,cAAA,EAAgB;UACjF4H,OAAA,CAAQC,IAAA,CAAK,0DAA0DjG,KAAK;QAC7E;QAED,MAAMxC,IAAA,GAAO,IAAI+Y,iBAAA,CAAkBvW,KAAA,CAAME,MAAA,GAASF,KAAA,CAAMC,KAAA,GAAQ,CAAC;QAEjE,SAAS+C,CAAA,GAAI,GAAGA,CAAA,GAAIxF,IAAA,CAAK0F,MAAA,EAAQF,CAAA,IAAK,GAAG;UACvCxF,IAAA,CAAKwF,CAAA,GAAI,CAAC,IAAIhD,KAAA,CAAMxC,IAAA,CAAKwF,CAAA,GAAI,CAAC;UAC9BxF,IAAA,CAAKwF,CAAA,GAAI,CAAC,IAAIhD,KAAA,CAAMxC,IAAA,CAAKwF,CAAA,GAAI,CAAC;UAC9BxF,IAAA,CAAKwF,CAAA,GAAI,CAAC,IAAIhD,KAAA,CAAMxC,IAAA,CAAKwF,CAAA,GAAI,CAAC;UAC9BxF,IAAA,CAAKwF,CAAA,GAAI,CAAC,IAAIhD,KAAA,CAAMxC,IAAA,CAAKwF,CAAA,GAAI,CAAC;QAC/B;QAEDoT,GAAA,CAAI1C,YAAA,CAAa,IAAI8C,SAAA,CAAUhZ,IAAA,EAAMwC,KAAA,CAAMC,KAAA,EAAOD,KAAA,CAAME,MAAM,GAAG,GAAG,CAAC;MAC7E,OAAa;QACLkW,GAAA,CAAI5C,SAAA,CAAUxT,KAAA,EAAO,GAAG,GAAG4M,MAAA,CAAO3M,KAAA,EAAO2M,MAAA,CAAO1M,MAAM;MACvD;MAED,IAAI2C,OAAA,CAAQ2L,MAAA,KAAW,MAAM;QAC3BvB,OAAA,CAAQ3K,IAAA,CACNqK,gBAAA,CAAiBC,MAAA,EAAQC,QAAQ,EAC9BuC,IAAA,CAAM/R,IAAA,IAASmE,MAAA,CAAOsT,sBAAA,CAAuBzX,IAAI,CAAC,EAClD+R,IAAA,CAAMqH,eAAA,IAAoB;UACzBN,QAAA,CAAST,UAAA,GAAae,eAAA;QACpC,CAAa,CACJ;MACT,OAAa;QACL,IAAI7J,MAAA,CAAO8J,SAAA,KAAc,QAAW;UAClCP,QAAA,CAAShG,GAAA,GAAMvD,MAAA,CAAO8J,SAAA,CAAU7J,QAAQ;QAClD,OAAe;UACLI,OAAA,CAAQ3K,IAAA,CACNqK,gBAAA,CAAiBC,MAAA,EAAQC,QAAQ,EAC9BuC,IAAA,CAAKhS,aAAa,EAClBgS,IAAA,CAAMe,GAAA,IAAQ;YACbgG,QAAA,CAAShG,GAAA,GAAMA,GAAA;UAC/B,CAAe,CACJ;QACF;MACF;MAED,MAAM3L,KAAA,GAAQmJ,IAAA,CAAKU,MAAA,CAAO/L,IAAA,CAAK6T,QAAQ,IAAI;MAC3CH,YAAA,CAAaC,GAAG,IAAIzR,KAAA;MACpB,OAAOA,KAAA;IACb,OAAW;MACL,MAAM,IAAIuB,KAAA,CAAM,2EAA2E;IAC5F;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD4Q,eAAejE,GAAA,EAAK;IAClB,MAAM/E,IAAA,GAAO,KAAKA,IAAA;IAElB,IAAI,CAACA,IAAA,CAAKiJ,QAAA,EAAUjJ,IAAA,CAAKiJ,QAAA,GAAW,EAAE;IAEtC,MAAMC,UAAA,GAAa;MACjBpW,SAAA,EAAWgI,cAAA,CAAeiK,GAAA,CAAIjS,SAAS;MACvCD,SAAA,EAAWiI,cAAA,CAAeiK,GAAA,CAAIlS,SAAS;MACvCE,KAAA,EAAO+H,cAAA,CAAeiK,GAAA,CAAIhS,KAAK;MAC/BC,KAAA,EAAO8H,cAAA,CAAeiK,GAAA,CAAI/R,KAAK;IAChC;IAED,OAAOgN,IAAA,CAAKiJ,QAAA,CAAStU,IAAA,CAAKuU,UAAU,IAAI;EACzC;EAAA;AAAA;AAAA;AAAA;AAAA;EAODC,eAAepE,GAAA,EAAK;IAClB,MAAMlR,MAAA,GAAS;IACf,MAAMqB,OAAA,GAAUrB,MAAA,CAAOqB,OAAA;IACvB,MAAMkL,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAMJ,IAAA,GAAO,KAAKA,IAAA;IAElB,IAAII,KAAA,CAAMK,QAAA,CAAS4C,GAAA,CAAI0B,GAAG,GAAG,OAAO3E,KAAA,CAAMK,QAAA,CAAS8C,GAAA,CAAIwB,GAAG;IAE1D,IAAI,CAAC/E,IAAA,CAAKS,QAAA,EAAUT,IAAA,CAAKS,QAAA,GAAW,EAAE;IAGtC,IAAIsE,GAAA,YAAeK,iBAAA,EAAmB;MACpCL,GAAA,GAAMxU,UAAA,CAAWwU,GAAA,EAAK7P,OAAA,CAAQzE,cAAc;IAC7C;IAED,IAAIyO,QAAA,GAAW6F,GAAA,CAAInC,QAAA,CAAS1D,QAAA;IAE5B,IAAIA,QAAA,KAAa,cAAcA,QAAA,GAAW;IAE1C,MAAMkK,UAAA,GAAa;MACjBC,OAAA,EAAS,KAAKL,cAAA,CAAejE,GAAG;MAChCkB,MAAA,EAAQ,KAAKiC,YAAA,CAAanD,GAAA,CAAI1S,KAAA,EAAO0S,GAAA,CAAIoD,MAAA,EAAQpD,GAAA,CAAIqD,KAAA,EAAOlJ,QAAQ;IACrE;IAED,IAAI6F,GAAA,CAAI9R,IAAA,EAAMmW,UAAA,CAAWnW,IAAA,GAAO8R,GAAA,CAAI9R,IAAA;IAEpC,KAAKqW,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAIC,YAAA,IAAgBD,GAAA,CAAIC,YAAA,CAAazE,GAAA,EAAKqE,UAAU;IAC1D,CAAK;IAED,MAAMvS,KAAA,GAAQmJ,IAAA,CAAKS,QAAA,CAAS9L,IAAA,CAAKyU,UAAU,IAAI;IAC/ChJ,KAAA,CAAMK,QAAA,CAAS1J,GAAA,CAAIgO,GAAA,EAAKlO,KAAK;IAC7B,OAAOA,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD4S,gBAAgBC,QAAA,EAAU;IACxB,MAAMtJ,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAMJ,IAAA,GAAO,KAAKA,IAAA;IAElB,IAAII,KAAA,CAAMI,SAAA,CAAU6C,GAAA,CAAIqG,QAAQ,GAAG,OAAOtJ,KAAA,CAAMI,SAAA,CAAU+C,GAAA,CAAImG,QAAQ;IAEtE,IAAIA,QAAA,CAASC,gBAAA,EAAkB;MAC7BtR,OAAA,CAAQC,IAAA,CAAK,mDAAmD;MAChE,OAAO;IACR;IAED,IAAI,CAAC0H,IAAA,CAAKQ,SAAA,EAAWR,IAAA,CAAKQ,SAAA,GAAY,EAAE;IAGxC,MAAMoJ,WAAA,GAAc;MAAEC,oBAAA,EAAsB;IAAI;IAEhD,IAAIH,QAAA,CAASI,sBAAA,KAA2B,QAAQJ,QAAA,CAASK,mBAAA,KAAwB,MAAM;MACrF1R,OAAA,CAAQC,IAAA,CAAK,+EAA+E;IAC7F;IAGD,MAAM0R,KAAA,GAAQN,QAAA,CAASM,KAAA,CAAMxF,OAAA,CAAS,EAACyF,MAAA,CAAO,CAACP,QAAA,CAASQ,OAAO,CAAC;IAEhE,IAAI,CAAC9N,UAAA,CAAW4N,KAAA,EAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG;MACpCJ,WAAA,CAAYC,oBAAA,CAAqBM,eAAA,GAAkBH,KAAA;IACpD;IAED,IAAIN,QAAA,CAASI,sBAAA,EAAwB;MACnCF,WAAA,CAAYC,oBAAA,CAAqBO,cAAA,GAAiBV,QAAA,CAASrE,SAAA;MAC3DuE,WAAA,CAAYC,oBAAA,CAAqBQ,eAAA,GAAkBX,QAAA,CAASpE,SAAA;IAClE,OAAW;MACLsE,WAAA,CAAYC,oBAAA,CAAqBO,cAAA,GAAiB;MAClDR,WAAA,CAAYC,oBAAA,CAAqBQ,eAAA,GAAkB;IACpD;IAGD,IAAIX,QAAA,CAAS9E,YAAA,IAAgB8E,QAAA,CAAS7E,YAAA,EAAc;MAClD,MAAMyF,iBAAA,GAAoB,KAAK3F,sBAAA,CAAuB+E,QAAA,CAAS9E,YAAA,EAAc8E,QAAA,CAAS7E,YAAY;MAElG,MAAM0F,gBAAA,GAAmB;QACvB1T,KAAA,EAAO,KAAKsS,cAAA,CAAemB,iBAAiB;QAC5CpE,OAAA,EAASoE,iBAAA,CAAkBpE;MAC5B;MACD,KAAK/B,qBAAA,CAAsBoG,gBAAA,EAAkBD,iBAAiB;MAC9DV,WAAA,CAAYC,oBAAA,CAAqBW,wBAAA,GAA2BD,gBAAA;IAC7D;IAGD,IAAIb,QAAA,CAAS3E,GAAA,EAAK;MAChB,MAAM0F,eAAA,GAAkB;QACtB5T,KAAA,EAAO,KAAKsS,cAAA,CAAeO,QAAA,CAAS3E,GAAG;QACvC2F,QAAA,EAAUhB,QAAA,CAAS3E,GAAA,CAAImB;MACxB;MACD,KAAK/B,qBAAA,CAAsBsG,eAAA,EAAiBf,QAAA,CAAS3E,GAAG;MACxD6E,WAAA,CAAYC,oBAAA,CAAqBc,gBAAA,GAAmBF,eAAA;IACrD;IAED,IAAIf,QAAA,CAASkB,QAAA,EAAU;MACrB,MAAMA,QAAA,GAAWlB,QAAA,CAASkB,QAAA;MAC1B,MAAMC,oBAAA,GAAuB1Y,IAAA,CAAKuL,GAAA,CAAIkN,QAAA,CAASE,CAAA,EAAGF,QAAA,CAASG,CAAA,EAAGH,QAAA,CAASI,CAAC;MAExE,IAAIH,oBAAA,GAAuB,GAAG;QAC5BjB,WAAA,CAAYqB,cAAA,GAAiBvB,QAAA,CAASkB,QAAA,CAASpG,OAAA,CAAS;MACzD;MAGD,IAAIkF,QAAA,CAASwB,WAAA,EAAa;QACxB,MAAMC,cAAA,GAAiB;UACrBtU,KAAA,EAAO,KAAKsS,cAAA,CAAeO,QAAA,CAASwB,WAAW;UAC/CR,QAAA,EAAUhB,QAAA,CAASwB,WAAA,CAAYhF;QAChC;QACD,KAAK/B,qBAAA,CAAsBgH,cAAA,EAAgBzB,QAAA,CAASwB,WAAW;QAC/DtB,WAAA,CAAYwB,eAAA,GAAkBD,cAAA;MAC/B;IACF;IAGD,IAAIzB,QAAA,CAAS2B,SAAA,EAAW;MACtB,MAAMC,YAAA,GAAe;QACnBzU,KAAA,EAAO,KAAKsS,cAAA,CAAeO,QAAA,CAAS2B,SAAS;QAC7CX,QAAA,EAAUhB,QAAA,CAAS2B,SAAA,CAAUnF;MAC9B;MAED,IAAIwD,QAAA,CAAS6B,WAAA,IAAe7B,QAAA,CAAS6B,WAAA,CAAYzH,CAAA,KAAM,GAAG;QAGxDwH,YAAA,CAAa7P,KAAA,GAAQiO,QAAA,CAAS6B,WAAA,CAAYzH,CAAA;MAC3C;MAED,KAAKK,qBAAA,CAAsBmH,YAAA,EAAc5B,QAAA,CAAS2B,SAAS;MAC3DzB,WAAA,CAAY4B,aAAA,GAAgBF,YAAA;IAC7B;IAGD,IAAI5B,QAAA,CAAS+B,KAAA,EAAO;MAClB,MAAMC,eAAA,GAAkB;QACtB7U,KAAA,EAAO,KAAKsS,cAAA,CAAeO,QAAA,CAAS+B,KAAK;QACzCf,QAAA,EAAUhB,QAAA,CAAS+B,KAAA,CAAMvF;MAC1B;MAED,IAAIwD,QAAA,CAASiC,cAAA,KAAmB,GAAK;QACnCD,eAAA,CAAgBE,QAAA,GAAWlC,QAAA,CAASiC,cAAA;MACrC;MAED,KAAKxH,qBAAA,CAAsBuH,eAAA,EAAiBhC,QAAA,CAAS+B,KAAK;MAC1D7B,WAAA,CAAYiC,gBAAA,GAAmBH,eAAA;IAChC;IAGD,IAAIhC,QAAA,CAASoC,WAAA,EAAa;MACxBlC,WAAA,CAAYmC,SAAA,GAAY;IAC9B,OAAW;MACL,IAAIrC,QAAA,CAASsC,SAAA,GAAY,GAAK;QAC5BpC,WAAA,CAAYmC,SAAA,GAAY;QACxBnC,WAAA,CAAYqC,WAAA,GAAcvC,QAAA,CAASsC,SAAA;MACpC;IACF;IAGD,IAAItC,QAAA,CAASwC,IAAA,KAASC,UAAA,EAAYvC,WAAA,CAAYwC,WAAA,GAAc;IAC5D,IAAI1C,QAAA,CAASzW,IAAA,KAAS,IAAI2W,WAAA,CAAY3W,IAAA,GAAOyW,QAAA,CAASzW,IAAA;IAEtD,KAAKwP,iBAAA,CAAkBiH,QAAA,EAAUE,WAAW;IAE5C,KAAKN,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAI8C,aAAA,IAAiB9C,GAAA,CAAI8C,aAAA,CAAc3C,QAAA,EAAUE,WAAW;IAClE,CAAK;IAED,MAAM/S,KAAA,GAAQmJ,IAAA,CAAKQ,SAAA,CAAU7L,IAAA,CAAKiV,WAAW,IAAI;IACjDxJ,KAAA,CAAMI,SAAA,CAAUzJ,GAAA,CAAI2S,QAAA,EAAU7S,KAAK;IACnC,OAAOA,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODyV,YAAYC,IAAA,EAAM;IAChB,MAAMnM,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAMJ,IAAA,GAAO,KAAKA,IAAA;IAElB,MAAMwM,iBAAA,GAAoB,CAACD,IAAA,CAAKlF,QAAA,CAAStO,IAAI;IAE7C,IAAIsE,KAAA,CAAMoP,OAAA,CAAQF,IAAA,CAAK7C,QAAQ,GAAG;MAChC,SAASrU,CAAA,GAAI,GAAGqX,CAAA,GAAIH,IAAA,CAAK7C,QAAA,CAASnU,MAAA,EAAQF,CAAA,GAAIqX,CAAA,EAAGrX,CAAA,IAAK;QACpDmX,iBAAA,CAAkB7X,IAAA,CAAK4X,IAAA,CAAK7C,QAAA,CAASrU,CAAC,EAAE0D,IAAI;MAC7C;IACP,OAAW;MACLyT,iBAAA,CAAkB7X,IAAA,CAAK4X,IAAA,CAAK7C,QAAA,CAAS3Q,IAAI;IAC1C;IAED,MAAM4T,YAAA,GAAeH,iBAAA,CAAkBI,IAAA,CAAK,GAAG;IAE/C,IAAIxM,KAAA,CAAMC,MAAA,CAAOgD,GAAA,CAAIsJ,YAAY,GAAG,OAAOvM,KAAA,CAAMC,MAAA,CAAOkD,GAAA,CAAIoJ,YAAY;IAExE,MAAMtF,QAAA,GAAWkF,IAAA,CAAKlF,QAAA;IAEtB,IAAIwF,IAAA;IAGJ,IAAIN,IAAA,CAAKO,cAAA,EAAgB;MACvBD,IAAA,GAAO1T,eAAA,CAAgBE,KAAA;IAC7B,WAAekT,IAAA,CAAKQ,UAAA,EAAY;MAC1BF,IAAA,GAAO1T,eAAA,CAAgBG,SAAA;IAC7B,WAAeiT,IAAA,CAAKS,MAAA,EAAQ;MACtBH,IAAA,GAAO1T,eAAA,CAAgBI,UAAA;IAC7B,WAAegT,IAAA,CAAKU,QAAA,EAAU;MACxBJ,IAAA,GAAO1T,eAAA,CAAgBC,MAAA;IAC7B,OAAW;MACLyT,IAAA,GAAON,IAAA,CAAK7C,QAAA,CAASwD,SAAA,GAAY/T,eAAA,CAAgBE,KAAA,GAAQF,eAAA,CAAgBK,SAAA;IAC1E;IAED,MAAM2T,OAAA,GAAU,CAAE;IAClB,MAAM7M,UAAA,GAAa,CAAE;IACrB,MAAM8M,UAAA,GAAa,EAAE;IACrB,MAAMC,OAAA,GAAU,EAAE;IAGlB,MAAMC,cAAA,GAAiB;MACrB,IAAIpN,OAAA,IAAW,MACX;QACEqN,EAAA,EAAI;QACJC,GAAA,EAAK;QACLC,GAAA,EAAK;QACLC,GAAA,EAAK;MACN,IACD;QACEH,EAAA,EAAI;QACJE,GAAA,EAAK;MACjB;MACMzD,KAAA,EAAO;MACP2D,UAAA,EAAY;MACZC,SAAA,EAAW;IACZ;IAED,MAAMC,cAAA,GAAiBxG,QAAA,CAASyG,YAAA,CAAa,QAAQ;IAErD,IAAID,cAAA,KAAmB,UAAa,CAAC,KAAKrK,2BAAA,CAA4BqK,cAAc,GAAG;MACrFxV,OAAA,CAAQC,IAAA,CAAK,uFAAuF;MAEpG+O,QAAA,CAAS0G,YAAA,CAAa,UAAU,KAAKlK,+BAAA,CAAgCgK,cAAc,CAAC;IACrF;IAID,IAAIG,iBAAA,GAAoB;IAExB,SAASC,aAAA,IAAiB5G,QAAA,CAAS/G,UAAA,EAAY;MAE7C,IAAI2N,aAAA,CAAchX,KAAA,CAAM,GAAG,CAAC,MAAM,SAAS;MAE3C,MAAMgG,SAAA,GAAYoK,QAAA,CAAS/G,UAAA,CAAW2N,aAAa;MACnDA,aAAA,GAAgBX,cAAA,CAAeW,aAAa,KAAKA,aAAA,CAAcC,WAAA,CAAa;MAI5E,MAAMC,qBAAA,GAAwB;MAE9B,IAAI,CAACA,qBAAA,CAAsBC,IAAA,CAAKH,aAAa,GAAGA,aAAA,GAAgB,MAAMA,aAAA;MAEtE,IAAI7N,KAAA,CAAME,UAAA,CAAW+C,GAAA,CAAI,KAAKF,MAAA,CAAOlG,SAAS,CAAC,GAAG;QAChDqD,UAAA,CAAW2N,aAAa,IAAI7N,KAAA,CAAME,UAAA,CAAWiD,GAAA,CAAI,KAAKJ,MAAA,CAAOlG,SAAS,CAAC;QACvE;MACD;MAGD+Q,iBAAA,GAAoB;MACpB,MAAMnQ,KAAA,GAAQZ,SAAA,CAAUY,KAAA;MAExB,IAAIoQ,aAAA,KAAkB,cAAc,EAAEpQ,KAAA,YAAiB8J,WAAA,KAAgB,EAAE9J,KAAA,YAAiB5N,UAAA,GAAa;QACrGoI,OAAA,CAAQC,IAAA,CAAK,uEAAuE;QACpF0V,iBAAA,GAAoB,IAAIK,eAAA,CAAgB,IAAI1G,WAAA,CAAY9J,KAAK,GAAGZ,SAAA,CAAUK,QAAA,EAAUL,SAAA,CAAUiB,UAAU;MACzG;MAED,MAAMoQ,QAAA,GAAW,KAAKlH,eAAA,CAAgB4G,iBAAA,IAAqB/Q,SAAA,EAAWoK,QAAQ;MAE9E,IAAIiH,QAAA,KAAa,MAAM;QACrB,IAAI,CAACL,aAAA,CAAcM,UAAA,CAAW,GAAG,GAAG;UAClC,KAAKC,sBAAA,CAAuBP,aAAA,EAAehR,SAAS;QACrD;QAEDqD,UAAA,CAAW2N,aAAa,IAAIK,QAAA;QAC5BlO,KAAA,CAAME,UAAA,CAAWvJ,GAAA,CAAI,KAAKoM,MAAA,CAAOlG,SAAS,GAAGqR,QAAQ;MACtD;IACF;IAED,IAAIT,cAAA,KAAmB,QAAWxG,QAAA,CAAS0G,YAAA,CAAa,UAAUF,cAAc;IAGhF,IAAIlN,MAAA,CAAOW,IAAA,CAAKhB,UAAU,EAAE/K,MAAA,KAAW,GAAG,OAAO;IAGjD,IAAIgX,IAAA,CAAK5T,qBAAA,KAA0B,UAAa4T,IAAA,CAAK5T,qBAAA,CAAsBpD,MAAA,GAAS,GAAG;MACrF,MAAMkZ,OAAA,GAAU,EAAE;MAClB,MAAMC,WAAA,GAAc,EAAE;MACtB,MAAMC,iBAAA,GAAoB,CAAE;MAE5B,IAAIpC,IAAA,CAAK1T,qBAAA,KAA0B,QAAW;QAC5C,WAAWyP,GAAA,IAAOiE,IAAA,CAAK1T,qBAAA,EAAuB;UAC5C8V,iBAAA,CAAkBpC,IAAA,CAAK1T,qBAAA,CAAsByP,GAAG,CAAC,IAAIA,GAAA;QACtD;MACF;MAED,SAASjT,CAAA,GAAI,GAAGA,CAAA,GAAIkX,IAAA,CAAK5T,qBAAA,CAAsBpD,MAAA,EAAQ,EAAEF,CAAA,EAAG;QAC1D,MAAMiR,MAAA,GAAS,CAAE;QACjB,IAAIsI,MAAA,GAAS;QAEb,WAAWX,aAAA,IAAiB5G,QAAA,CAASwH,eAAA,EAAiB;UAIpD,IAAIZ,aAAA,KAAkB,cAAcA,aAAA,KAAkB,UAAU;YAC9D,IAAI,CAACW,MAAA,EAAQ;cACXvW,OAAA,CAAQC,IAAA,CAAK,6DAA6D;cAC1EsW,MAAA,GAAS;YACV;YAED;UACD;UAED,MAAM3R,SAAA,GAAYoK,QAAA,CAASwH,eAAA,CAAgBZ,aAAa,EAAE5Y,CAAC;UAC3D,MAAMyZ,iBAAA,GAAoBb,aAAA,CAAcC,WAAA,CAAa;UAOrD,MAAMa,aAAA,GAAgB1H,QAAA,CAAS/G,UAAA,CAAW2N,aAAa;UAEvD,IAAI7N,KAAA,CAAME,UAAA,CAAW+C,GAAA,CAAI,KAAKF,MAAA,CAAOlG,SAAA,EAAW,IAAI,CAAC,GAAG;YACtDqJ,MAAA,CAAOwI,iBAAiB,IAAI1O,KAAA,CAAME,UAAA,CAAWiD,GAAA,CAAI,KAAKJ,MAAA,CAAOlG,SAAA,EAAW,IAAI,CAAC;YAC7E;UACD;UAGD,MAAM+R,iBAAA,GAAoB/R,SAAA,CAAU1E,KAAA,CAAO;UAE3C,IAAI,CAAC8O,QAAA,CAAS4H,oBAAA,EAAsB;YAClC,SAASjW,CAAA,GAAI,GAAGkW,EAAA,GAAKjS,SAAA,CAAUE,KAAA,EAAOnE,CAAA,GAAIkW,EAAA,EAAIlW,CAAA,IAAK;cACjD,SAAS4E,CAAA,GAAI,GAAGA,CAAA,GAAIX,SAAA,CAAUK,QAAA,EAAUM,CAAA,IAAK;gBAC3C,IAAIA,CAAA,KAAM,GAAGoR,iBAAA,CAAkB/K,IAAA,CAAKjL,CAAA,EAAGiE,SAAA,CAAUa,IAAA,CAAK9E,CAAC,IAAI+V,aAAA,CAAcjR,IAAA,CAAK9E,CAAC,CAAC;gBAChF,IAAI4E,CAAA,KAAM,GAAGoR,iBAAA,CAAkBG,IAAA,CAAKnW,CAAA,EAAGiE,SAAA,CAAUc,IAAA,CAAK/E,CAAC,IAAI+V,aAAA,CAAchR,IAAA,CAAK/E,CAAC,CAAC;gBAChF,IAAI4E,CAAA,KAAM,GAAGoR,iBAAA,CAAkBI,IAAA,CAAKpW,CAAA,EAAGiE,SAAA,CAAUe,IAAA,CAAKhF,CAAC,IAAI+V,aAAA,CAAc/Q,IAAA,CAAKhF,CAAC,CAAC;gBAChF,IAAI4E,CAAA,KAAM,GAAGoR,iBAAA,CAAkBK,IAAA,CAAKrW,CAAA,EAAGiE,SAAA,CAAUgB,IAAA,CAAKjF,CAAC,IAAI+V,aAAA,CAAc9Q,IAAA,CAAKjF,CAAC,CAAC;cACjF;YACF;UACF;UAEDsN,MAAA,CAAOwI,iBAAiB,IAAI,KAAK1H,eAAA,CAAgB4H,iBAAA,EAAmB3H,QAAQ;UAC5EjH,KAAA,CAAME,UAAA,CAAWvJ,GAAA,CAAI,KAAKoM,MAAA,CAAO4L,aAAA,EAAe,IAAI,GAAGzI,MAAA,CAAOwI,iBAAiB,CAAC;QACjF;QAEDzB,OAAA,CAAQ1Y,IAAA,CAAK2R,MAAM;QAEnBmI,OAAA,CAAQ9Z,IAAA,CAAK4X,IAAA,CAAK5T,qBAAA,CAAsBtD,CAAC,CAAC;QAE1C,IAAIkX,IAAA,CAAK1T,qBAAA,KAA0B,QAAW6V,WAAA,CAAY/Z,IAAA,CAAKga,iBAAA,CAAkBtZ,CAAC,CAAC;MACpF;MAED8X,OAAA,CAAQsB,OAAA,GAAUA,OAAA;MAElB,IAAIC,WAAA,CAAYnZ,MAAA,GAAS,GAAG;QAC1B4X,OAAA,CAAQnK,MAAA,GAAS,CAAE;QACnBmK,OAAA,CAAQnK,MAAA,CAAO0L,WAAA,GAAcA,WAAA;MAC9B;IACF;IAED,MAAMY,eAAA,GAAkBjS,KAAA,CAAMoP,OAAA,CAAQF,IAAA,CAAK7C,QAAQ;IAEnD,IAAI4F,eAAA,IAAmBjI,QAAA,CAASkI,MAAA,CAAOha,MAAA,KAAW,GAAG,OAAO;IAE5D,MAAMiL,SAAA,GAAY8O,eAAA,GAAkB/C,IAAA,CAAK7C,QAAA,GAAW,CAAC6C,IAAA,CAAK7C,QAAQ;IAClE,MAAM6F,MAAA,GAASD,eAAA,GAAkBjI,QAAA,CAASkI,MAAA,GAAS,CAAC;MAAEC,aAAA,EAAe;MAAGtS,KAAA,EAAO;MAAWC,KAAA,EAAO;IAAS,CAAE;IAE5G,SAAS9H,CAAA,GAAI,GAAGC,EAAA,GAAKia,MAAA,CAAOha,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC/C,MAAMoa,SAAA,GAAY;QAChB5C,IAAA;QACAvM;MACD;MAED,KAAKmC,iBAAA,CAAkB4E,QAAA,EAAUoI,SAAS;MAE1C,IAAIpC,OAAA,CAAQ9X,MAAA,GAAS,GAAGka,SAAA,CAAUpC,OAAA,GAAUA,OAAA;MAE5C,IAAIhG,QAAA,CAASxQ,KAAA,KAAU,MAAM;QAC3B,IAAI6Y,QAAA,GAAW,KAAKvM,MAAA,CAAOkE,QAAA,CAASxQ,KAAK;QAEzC,IAAI0Y,MAAA,CAAOla,CAAC,EAAE6H,KAAA,KAAU,UAAaqS,MAAA,CAAOla,CAAC,EAAE8H,KAAA,KAAU,QAAW;UAClEuS,QAAA,IAAY,MAAMH,MAAA,CAAOla,CAAC,EAAE6H,KAAA,GAAQ,MAAMqS,MAAA,CAAOla,CAAC,EAAE8H,KAAA;QACrD;QAED,IAAIiD,KAAA,CAAME,UAAA,CAAW+C,GAAA,CAAIqM,QAAQ,GAAG;UAClCD,SAAA,CAAUE,OAAA,GAAUvP,KAAA,CAAME,UAAA,CAAWiD,GAAA,CAAImM,QAAQ;QAC3D,OAAe;UACLD,SAAA,CAAUE,OAAA,GAAU,KAAKvI,eAAA,CAAgBC,QAAA,CAASxQ,KAAA,EAAOwQ,QAAA,EAAUkI,MAAA,CAAOla,CAAC,EAAE6H,KAAA,EAAOqS,MAAA,CAAOla,CAAC,EAAE8H,KAAK;UACnGiD,KAAA,CAAME,UAAA,CAAWvJ,GAAA,CAAI2Y,QAAA,EAAUD,SAAA,CAAUE,OAAO;QACjD;QAED,IAAIF,SAAA,CAAUE,OAAA,KAAY,MAAM,OAAOF,SAAA,CAAUE,OAAA;MAClD;MAED,MAAMjG,QAAA,GAAW,KAAKD,eAAA,CAAgBjJ,SAAA,CAAU+O,MAAA,CAAOla,CAAC,EAAEma,aAAa,CAAC;MAExE,IAAI9F,QAAA,KAAa,MAAM+F,SAAA,CAAU/F,QAAA,GAAWA,QAAA;MAE5C0D,UAAA,CAAWzY,IAAA,CAAK8a,SAAS;IAC1B;IAEDtC,OAAA,CAAQC,UAAA,GAAaA,UAAA;IAErB,IAAI,CAACpN,IAAA,CAAKK,MAAA,EAAQL,IAAA,CAAKK,MAAA,GAAS,EAAE;IAElC,KAAKiJ,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAIqG,SAAA,IAAarG,GAAA,CAAIqG,SAAA,CAAUrD,IAAA,EAAMY,OAAO;IAClD,CAAK;IAED,MAAMtW,KAAA,GAAQmJ,IAAA,CAAKK,MAAA,CAAO1L,IAAA,CAAKwY,OAAO,IAAI;IAC1C/M,KAAA,CAAMC,MAAA,CAAOtJ,GAAA,CAAI4V,YAAA,EAAc9V,KAAK;IACpC,OAAOA,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaD2X,uBAAuBP,aAAA,EAAehR,SAAA,EAAW;IAC/C,IAAI,KAAK2C,cAAA,CAAe/E,qBAAqB,GAAG;IAEhD,IAAIgV,QAAA,GAAW;IAEf,QAAQ5S,SAAA,CAAUY,KAAA,CAAMnK,WAAA;MACtB,KAAKkU,SAAA;QACHiI,QAAA,GAAW;QAEX;MAEF,KAAK5f,UAAA;QACH4f,QAAA,GAAW;QAEX;MAEF,KAAKnI,UAAA;QACHmI,QAAA,GAAW;QAEX;MAEF,KAAKlI,WAAA;QACHkI,QAAA,GAAW;QAEX;MAEF;QACE;IACH;IAED,IAAI5S,SAAA,CAAUiB,UAAA,EAAY2R,QAAA,IAAY;IAEtC,MAAMC,cAAA,GAAiB7B,aAAA,CAAc8B,KAAA,CAAM,KAAK,CAAC,EAAE,CAAC;IAEpD,IACE5c,oCAAA,CAAqC2c,cAAc,KACnD3c,oCAAA,CAAqC2c,cAAc,EAAEE,QAAA,CAASH,QAAQ,GACtE;MACA,KAAKjQ,cAAA,CAAe/E,qBAAqB,IAAI;MAC7C,KAAKgF,kBAAA,CAAmBhF,qBAAqB,IAAI;IAClD;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAODoV,cAAcC,MAAA,EAAQ;IACpB,MAAMlQ,IAAA,GAAO,KAAKA,IAAA;IAElB,IAAI,CAACA,IAAA,CAAKmQ,OAAA,EAASnQ,IAAA,CAAKmQ,OAAA,GAAU,EAAE;IAEpC,MAAMC,OAAA,GAAUF,MAAA,CAAOG,oBAAA;IAEvB,MAAMC,SAAA,GAAY;MAChBpgB,IAAA,EAAMkgB,OAAA,GAAU,iBAAiB;IAClC;IAED,IAAIA,OAAA,EAAS;MACXE,SAAA,CAAUC,YAAA,GAAe;QACvBC,IAAA,EAAMN,MAAA,CAAOO,KAAA,GAAQ;QACrBC,IAAA,EAAMR,MAAA,CAAOS,GAAA,GAAM;QACnBC,IAAA,EAAMV,MAAA,CAAOW,GAAA,IAAO,IAAI,OAAQX,MAAA,CAAOW,GAAA;QACvCC,KAAA,EAAOZ,MAAA,CAAOa,IAAA,GAAO,IAAI,IAAIb,MAAA,CAAOa;MACrC;IACP,OAAW;MACLT,SAAA,CAAUU,WAAA,GAAc;QACtBC,WAAA,EAAaf,MAAA,CAAOgB,MAAA;QACpBC,IAAA,EAAMhT,SAAA,CAAUiT,QAAA,CAASlB,MAAA,CAAOmB,GAAG;QACnCT,IAAA,EAAMV,MAAA,CAAOW,GAAA,IAAO,IAAI,OAAQX,MAAA,CAAOW,GAAA;QACvCC,KAAA,EAAOZ,MAAA,CAAOa,IAAA,GAAO,IAAI,IAAIb,MAAA,CAAOa;MACrC;IACF;IAGD,IAAIb,MAAA,CAAOjd,IAAA,KAAS,IAAIqd,SAAA,CAAUrd,IAAA,GAAOid,MAAA,CAAOhgB,IAAA;IAEhD,OAAO8P,IAAA,CAAKmQ,OAAA,CAAQxb,IAAA,CAAK2b,SAAS,IAAI;EACvC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYDgB,iBAAiBna,IAAA,EAAMC,IAAA,EAAM;IAC3B,MAAM4I,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMP,OAAA,GAAU,KAAKA,OAAA;IAErB,IAAI,CAACO,IAAA,CAAKgB,UAAA,EAAYhB,IAAA,CAAKgB,UAAA,GAAa,EAAE;IAE1C7J,IAAA,GAAO3D,YAAA,CAAa+d,KAAA,CAAMra,sBAAA,CAAuBC,IAAA,CAAKoB,KAAA,CAAO,GAAEnB,IAAI;IAEnE,MAAMC,MAAA,GAASF,IAAA,CAAKE,MAAA;IACpB,MAAMma,QAAA,GAAW,EAAE;IACnB,MAAMvI,QAAA,GAAW,EAAE;IAEnB,SAAS5T,CAAA,GAAI,GAAGA,CAAA,GAAIgC,MAAA,CAAO9B,MAAA,EAAQ,EAAEF,CAAA,EAAG;MACtC,MAAMa,KAAA,GAAQmB,MAAA,CAAOhC,CAAC;MACtB,MAAMoc,YAAA,GAAe/Z,eAAA,CAAgBC,cAAA,CAAezB,KAAA,CAAMjD,IAAI;MAC9D,IAAIye,SAAA,GAAYha,eAAA,CAAgBG,QAAA,CAAST,IAAA,EAAMqa,YAAA,CAAa3Z,QAAQ;MACpE,MAAM6Z,aAAA,GAAgBnW,eAAA,CAAgBiW,YAAA,CAAa1Z,YAAY;MAE/D,IAAI0Z,YAAA,CAAaG,UAAA,KAAe,SAAS;QACvC,IAAIF,SAAA,CAAUG,aAAA,KAAkB,MAAM;UACpCH,SAAA,GAAYA,SAAA,CAAUI,QAAA,CAASC,aAAA,CAAcN,YAAA,CAAaO,WAAW;QAC/E,OAAe;UACLN,SAAA,GAAY;QACb;MACF;MAED,IAAI,CAACA,SAAA,IAAa,CAACC,aAAA,EAAe;QAChCtZ,OAAA,CAAQC,IAAA,CAAK,8DAA8DpC,KAAA,CAAMjD,IAAI;QACrF,OAAO;MACR;MAED,MAAMgf,aAAA,GAAgB;MACtB,IAAIC,cAAA,GAAiBhc,KAAA,CAAMO,MAAA,CAAOlB,MAAA,GAASW,KAAA,CAAMK,KAAA,CAAMhB,MAAA;MAEvD,IAAIoc,aAAA,KAAkBnW,eAAA,CAAgB7C,qBAAA,EAAuB;QAC3DuZ,cAAA,IAAkBR,SAAA,CAAU/Y,qBAAA,CAAsBpD,MAAA;MACnD;MAED,IAAI4c,aAAA;MAOJ,IAAIjc,KAAA,CAAMU,iBAAA,CAAkBuB,yCAAA,KAA8C,MAAM;QAC9Ega,aAAA,GAAgB;QAKhBD,cAAA,IAAkB;MACnB,WAAUhc,KAAA,CAAMkc,gBAAA,CAAkB,MAAKC,mBAAA,EAAqB;QAC3DF,aAAA,GAAgB;MACxB,OAAa;QACLA,aAAA,GAAgB;MACjB;MAEDlJ,QAAA,CAAStU,IAAA,CAAK;QACZI,KAAA,EAAO,KAAKqS,eAAA,CAAgB,IAAIiH,eAAA,CAAgBnY,KAAA,CAAMK,KAAA,EAAO0b,aAAa,CAAC;QAC3E7U,MAAA,EAAQ,KAAKgK,eAAA,CAAgB,IAAIiH,eAAA,CAAgBnY,KAAA,CAAMO,MAAA,EAAQyb,cAAc,CAAC;QAC9EC;MACR,CAAO;MAEDX,QAAA,CAAS7c,IAAA,CAAK;QACZ0U,OAAA,EAASJ,QAAA,CAAS1T,MAAA,GAAS;QAC3B+Q,MAAA,EAAQ;UACNgM,IAAA,EAAM7S,OAAA,CAAQ8D,GAAA,CAAImO,SAAS;UAC3Ba,IAAA,EAAMZ;QACP;MACT,CAAO;IACF;IAED3R,IAAA,CAAKgB,UAAA,CAAWrM,IAAA,CAAK;MACnB1B,IAAA,EAAMkE,IAAA,CAAKlE,IAAA,IAAQ,UAAU+M,IAAA,CAAKgB,UAAA,CAAWzL,MAAA;MAC7C0T,QAAA;MACAuI;IACN,CAAK;IAED,OAAOxR,IAAA,CAAKgB,UAAA,CAAWzL,MAAA,GAAS;EACjC;EAAA;AAAA;AAAA;AAAA;EAMDid,YAAY9P,MAAA,EAAQ;IAClB,MAAM1C,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMP,OAAA,GAAU,KAAKA,OAAA;IAErB,MAAM6S,IAAA,GAAOtS,IAAA,CAAKyS,KAAA,CAAMhT,OAAA,CAAQ8D,GAAA,CAAIb,MAAM,CAAC;IAE3C,MAAMoP,QAAA,GAAWpP,MAAA,CAAOoP,QAAA;IAExB,IAAIA,QAAA,KAAa,QAAW,OAAO;IAEnC,MAAMY,SAAA,GAAYhQ,MAAA,CAAOoP,QAAA,CAASa,KAAA,CAAM,CAAC;IAEzC,IAAID,SAAA,KAAc,QAAW,OAAO;IAEpC,MAAME,MAAA,GAAS,EAAE;IACjB,MAAMC,mBAAA,GAAsB,IAAItL,YAAA,CAAauK,QAAA,CAASa,KAAA,CAAMpd,MAAA,GAAS,EAAE;IACvE,MAAMud,oBAAA,GAAuB,IAAIC,OAAA,CAAS;IAE1C,SAAS1d,CAAA,GAAI,GAAGA,CAAA,GAAIyc,QAAA,CAASa,KAAA,CAAMpd,MAAA,EAAQ,EAAEF,CAAA,EAAG;MAC9Cud,MAAA,CAAOje,IAAA,CAAK8K,OAAA,CAAQ8D,GAAA,CAAIuO,QAAA,CAASa,KAAA,CAAMtd,CAAC,CAAC,CAAC;MAC1Cyd,oBAAA,CAAqBE,IAAA,CAAKlB,QAAA,CAASmB,YAAA,CAAa5d,CAAC,CAAC;MAClDyd,oBAAA,CAAqBI,QAAA,CAASxQ,MAAA,CAAOyQ,UAAU,EAAE3O,OAAA,CAAQqO,mBAAA,EAAqBxd,CAAA,GAAI,EAAE;IACrF;IAED,IAAI2K,IAAA,CAAKL,KAAA,KAAU,QAAWK,IAAA,CAAKL,KAAA,GAAQ,EAAE;IAE7CK,IAAA,CAAKL,KAAA,CAAMhL,IAAA,CAAK;MACdke,mBAAA,EAAqB,KAAKzL,eAAA,CAAgB,IAAIiH,eAAA,CAAgBwE,mBAAA,EAAqB,EAAE,CAAC;MACtFD,MAAA;MACAd,QAAA,EAAUrS,OAAA,CAAQ8D,GAAA,CAAImP,SAAS;IACrC,CAAK;IAED,MAAM9E,SAAA,GAAa0E,IAAA,CAAKc,IAAA,GAAOpT,IAAA,CAAKL,KAAA,CAAMpK,MAAA,GAAS;IAEnD,OAAOqY,SAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODyF,YAAY3Q,MAAA,EAAQ;IAClB,MAAM1C,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM9K,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMuK,OAAA,GAAU,KAAKA,OAAA;IAErB,IAAI,CAACO,IAAA,CAAKyS,KAAA,EAAOzS,IAAA,CAAKyS,KAAA,GAAQ,EAAE;IAEhC,MAAMa,OAAA,GAAU,CAAE;IAElB,IAAIpe,OAAA,CAAQ4L,GAAA,EAAK;MACf,MAAM2D,QAAA,GAAW/B,MAAA,CAAO/G,UAAA,CAAW6I,OAAA,CAAS;MAC5C,MAAM9I,QAAA,GAAWgH,MAAA,CAAOhH,QAAA,CAAS8I,OAAA,CAAS;MAC1C,MAAM/I,KAAA,GAAQiH,MAAA,CAAOjH,KAAA,CAAM+I,OAAA,CAAS;MAEpC,IAAI,CAACpI,UAAA,CAAWqI,QAAA,EAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG;QACvC6O,OAAA,CAAQ7O,QAAA,GAAWA,QAAA;MACpB;MAED,IAAI,CAACrI,UAAA,CAAWV,QAAA,EAAU,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;QACpC4X,OAAA,CAAQC,WAAA,GAAc7X,QAAA;MACvB;MAED,IAAI,CAACU,UAAA,CAAWX,KAAA,EAAO,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;QACjC6X,OAAA,CAAQ7X,KAAA,GAAQA,KAAA;MACjB;IACP,OAAW;MACL,IAAIiH,MAAA,CAAO8Q,gBAAA,EAAkB;QAC3B9Q,MAAA,CAAO+Q,YAAA,CAAc;MACtB;MAED,IAAI5W,gBAAA,CAAiB6F,MAAA,CAAO5F,MAAM,MAAM,OAAO;QAC7CwW,OAAA,CAAQxW,MAAA,GAAS4F,MAAA,CAAO5F,MAAA,CAAOC,QAAA;MAChC;IACF;IAGD,IAAI2F,MAAA,CAAOzP,IAAA,KAAS,IAAIqgB,OAAA,CAAQrgB,IAAA,GAAOlD,MAAA,CAAO2S,MAAA,CAAOzP,IAAI;IAEzD,KAAKwP,iBAAA,CAAkBC,MAAA,EAAQ4Q,OAAO;IAEtC,IAAI5Q,MAAA,CAAOgR,MAAA,IAAUhR,MAAA,CAAOsK,MAAA,IAAUtK,MAAA,CAAOuK,QAAA,EAAU;MACrD,MAAM0G,SAAA,GAAY,KAAKrH,WAAA,CAAY5J,MAAM;MAEzC,IAAIiR,SAAA,KAAc,MAAML,OAAA,CAAQ/G,IAAA,GAAOoH,SAAA;IAC7C,WAAejR,MAAA,CAAOkR,QAAA,EAAU;MAC1BN,OAAA,CAAQpD,MAAA,GAAS,KAAKD,aAAA,CAAcvN,MAAM;IAC3C;IAED,IAAIA,MAAA,CAAOmP,aAAA,EAAe,KAAKlS,KAAA,CAAMhL,IAAA,CAAK+N,MAAM;IAEhD,IAAIA,MAAA,CAAOmR,QAAA,CAASte,MAAA,GAAS,GAAG;MAC9B,MAAMse,QAAA,GAAW,EAAE;MAEnB,SAASxe,CAAA,GAAI,GAAGqX,CAAA,GAAIhK,MAAA,CAAOmR,QAAA,CAASte,MAAA,EAAQF,CAAA,GAAIqX,CAAA,EAAGrX,CAAA,IAAK;QACtD,MAAMye,KAAA,GAAQpR,MAAA,CAAOmR,QAAA,CAASxe,CAAC;QAE/B,IAAIye,KAAA,CAAMC,OAAA,IAAW7e,OAAA,CAAQ6L,WAAA,KAAgB,OAAO;UAClD,MAAMiT,UAAA,GAAY,KAAKX,WAAA,CAAYS,KAAK;UAExC,IAAIE,UAAA,KAAc,MAAMH,QAAA,CAASlf,IAAA,CAAKqf,UAAS;QAChD;MACF;MAED,IAAIH,QAAA,CAASte,MAAA,GAAS,GAAG+d,OAAA,CAAQO,QAAA,GAAWA,QAAA;IAC7C;IAED,KAAKvK,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAI0K,SAAA,IAAa1K,GAAA,CAAI0K,SAAA,CAAUvR,MAAA,EAAQ4Q,OAAO;IACpD,CAAK;IAED,MAAMY,SAAA,GAAYlU,IAAA,CAAKyS,KAAA,CAAM9d,IAAA,CAAK2e,OAAO,IAAI;IAC7C7T,OAAA,CAAQ1I,GAAA,CAAI2L,MAAA,EAAQwR,SAAS;IAC7B,OAAOA,SAAA;EACR;EAAA;AAAA;AAAA;AAAA;EAMDC,aAAaC,KAAA,EAAO;IAClB,MAAMpU,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM9K,OAAA,GAAU,KAAKA,OAAA;IAErB,IAAI,CAAC8K,IAAA,CAAKqU,MAAA,EAAQ;MAChBrU,IAAA,CAAKqU,MAAA,GAAS,EAAE;MAChBrU,IAAA,CAAKoU,KAAA,GAAQ;IACd;IAED,MAAME,QAAA,GAAW,CAAE;IAEnB,IAAIF,KAAA,CAAMnhB,IAAA,KAAS,IAAIqhB,QAAA,CAASrhB,IAAA,GAAOmhB,KAAA,CAAMnhB,IAAA;IAE7C+M,IAAA,CAAKqU,MAAA,CAAO1f,IAAA,CAAK2f,QAAQ;IAEzB,MAAM7B,KAAA,GAAQ,EAAE;IAEhB,SAASpd,CAAA,GAAI,GAAGqX,CAAA,GAAI0H,KAAA,CAAMP,QAAA,CAASte,MAAA,EAAQF,CAAA,GAAIqX,CAAA,EAAGrX,CAAA,IAAK;MACrD,MAAMye,KAAA,GAAQM,KAAA,CAAMP,QAAA,CAASxe,CAAC;MAE9B,IAAIye,KAAA,CAAMC,OAAA,IAAW7e,OAAA,CAAQ6L,WAAA,KAAgB,OAAO;QAClD,MAAMmT,SAAA,GAAY,KAAKb,WAAA,CAAYS,KAAK;QAExC,IAAII,SAAA,KAAc,MAAMzB,KAAA,CAAM9d,IAAA,CAAKuf,SAAS;MAC7C;IACF;IAED,IAAIzB,KAAA,CAAMld,MAAA,GAAS,GAAG+e,QAAA,CAAS7B,KAAA,GAAQA,KAAA;IAEvC,KAAKhQ,iBAAA,CAAkB2R,KAAA,EAAOE,QAAQ;EACvC;EAAA;AAAA;AAAA;AAAA;EAMDC,eAAeC,OAAA,EAAS;IACtB,MAAMJ,KAAA,GAAQ,IAAItiB,KAAA,CAAO;IACzBsiB,KAAA,CAAMnhB,IAAA,GAAO;IAEb,SAASoC,CAAA,GAAI,GAAGA,CAAA,GAAImf,OAAA,CAAQjf,MAAA,EAAQF,CAAA,IAAK;MAGvC+e,KAAA,CAAMP,QAAA,CAASlf,IAAA,CAAK6f,OAAA,CAAQnf,CAAC,CAAC;IAC/B;IAED,KAAK8e,YAAA,CAAaC,KAAK;EACxB;EAAA;AAAA;AAAA;EAKDlT,aAAanM,KAAA,EAAO;IAClB,MAAMG,OAAA,GAAU,KAAKA,OAAA;IAErBH,KAAA,GAAQA,KAAA,YAAiBsI,KAAA,GAAQtI,KAAA,GAAQ,CAACA,KAAK;IAE/C,KAAKuU,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAIkL,WAAA,IAAelL,GAAA,CAAIkL,WAAA,CAAY1f,KAAK;IAC9C,CAAK;IAED,MAAM2f,mBAAA,GAAsB,EAAE;IAE9B,SAASrf,CAAA,GAAI,GAAGA,CAAA,GAAIN,KAAA,CAAMQ,MAAA,EAAQF,CAAA,IAAK;MACrC,IAAIN,KAAA,CAAMM,CAAC,aAAavD,KAAA,EAAO;QAC7B,KAAKqiB,YAAA,CAAapf,KAAA,CAAMM,CAAC,CAAC;MAClC,OAAa;QACLqf,mBAAA,CAAoB/f,IAAA,CAAKI,KAAA,CAAMM,CAAC,CAAC;MAClC;IACF;IAED,IAAIqf,mBAAA,CAAoBnf,MAAA,GAAS,GAAG,KAAKgf,cAAA,CAAeG,mBAAmB;IAE3E,SAASrf,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKsK,KAAA,CAAMpK,MAAA,EAAQ,EAAEF,CAAA,EAAG;MAC1C,KAAKmd,WAAA,CAAY,KAAK7S,KAAA,CAAMtK,CAAC,CAAC;IAC/B;IAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIH,OAAA,CAAQ8L,UAAA,CAAWzL,MAAA,EAAQ,EAAEF,CAAA,EAAG;MAClD,KAAKic,gBAAA,CAAiBpc,OAAA,CAAQ8L,UAAA,CAAW3L,CAAC,GAAGN,KAAA,CAAM,CAAC,CAAC;IACtD;IAED,KAAKuU,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7BA,GAAA,CAAIoL,UAAA,IAAcpL,GAAA,CAAIoL,UAAA,CAAW5f,KAAK;IAC5C,CAAK;EACF;EAEDuU,WAAWsL,IAAA,EAAM;IACf,SAASvf,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKF,OAAA,CAAQG,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACrDuf,IAAA,CAAK,KAAKxf,OAAA,CAAQC,CAAC,CAAC;IACrB;EACF;AACH;AAOA,MAAMvB,kBAAA,CAAmB;EACvBJ,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDghB,UAAUY,KAAA,EAAOvB,OAAA,EAAS;IACxB,IAAI,CAACuB,KAAA,CAAMC,OAAA,EAAS;IAEpB,IAAI,CAACD,KAAA,CAAME,kBAAA,IAAsB,CAACF,KAAA,CAAMG,YAAA,IAAgB,CAACH,KAAA,CAAMI,WAAA,EAAa;MAC1E5c,OAAA,CAAQC,IAAA,CAAK,+EAA+Euc,KAAK;MACjG;IACD;IAED,MAAMhhB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMmM,IAAA,GAAOnM,MAAA,CAAOmM,IAAA;IACpB,MAAMJ,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMsV,QAAA,GAAW,CAAE;IAEnB,IAAIL,KAAA,CAAM5hB,IAAA,EAAMiiB,QAAA,CAASjiB,IAAA,GAAO4hB,KAAA,CAAM5hB,IAAA;IAEtCiiB,QAAA,CAASlL,KAAA,GAAQ6K,KAAA,CAAM7K,KAAA,CAAMxF,OAAA,CAAS;IAEtC0Q,QAAA,CAASC,SAAA,GAAYN,KAAA,CAAMM,SAAA;IAE3B,IAAIN,KAAA,CAAME,kBAAA,EAAoB;MAC5BG,QAAA,CAAShlB,IAAA,GAAO;IACtB,WAAe2kB,KAAA,CAAMG,YAAA,EAAc;MAC7BE,QAAA,CAAShlB,IAAA,GAAO;MAEhB,IAAI2kB,KAAA,CAAMO,QAAA,GAAW,GAAGF,QAAA,CAASG,KAAA,GAAQR,KAAA,CAAMO,QAAA;IACrD,WAAeP,KAAA,CAAMI,WAAA,EAAa;MAC5BC,QAAA,CAAShlB,IAAA,GAAO;MAEhB,IAAI2kB,KAAA,CAAMO,QAAA,GAAW,GAAGF,QAAA,CAASG,KAAA,GAAQR,KAAA,CAAMO,QAAA;MAE/CF,QAAA,CAASI,IAAA,GAAO,CAAE;MAClBJ,QAAA,CAASI,IAAA,CAAKC,cAAA,IAAkBV,KAAA,CAAMW,QAAA,GAAW,KAAOX,KAAA,CAAMY,KAAA,GAAQ;MACtEP,QAAA,CAASI,IAAA,CAAKI,cAAA,GAAiBb,KAAA,CAAMY,KAAA;IACtC;IAED,IAAIZ,KAAA,CAAMc,KAAA,KAAU,UAAad,KAAA,CAAMc,KAAA,KAAU,GAAG;MAClDtd,OAAA,CAAQC,IAAA,CACN,mGACD;IACF;IAED,IACEuc,KAAA,CAAMvO,MAAA,KACLuO,KAAA,CAAMvO,MAAA,CAAOsP,MAAA,KAAWf,KAAA,IACvBA,KAAA,CAAMvO,MAAA,CAAO5K,QAAA,CAASoI,CAAA,KAAM,KAC5B+Q,KAAA,CAAMvO,MAAA,CAAO5K,QAAA,CAASqI,CAAA,KAAM,KAC5B8Q,KAAA,CAAMvO,MAAA,CAAO5K,QAAA,CAASsI,CAAA,KAAM,KAC9B;MACA3L,OAAA,CAAQC,IAAA,CACN,iIAED;IACF;IAED,IAAI,CAACsH,cAAA,CAAe,KAAK3M,IAAI,GAAG;MAC9B+M,IAAA,CAAK8C,UAAA,GAAa9C,IAAA,CAAK8C,UAAA,IAAc,CAAE;MACvC9C,IAAA,CAAK8C,UAAA,CAAW,KAAK7P,IAAI,IAAI;QAAE4iB,MAAA,EAAQ;MAAI;MAC3CjW,cAAA,CAAe,KAAK3M,IAAI,IAAI;IAC7B;IAED,MAAM4iB,MAAA,GAAS7V,IAAA,CAAK8C,UAAA,CAAW,KAAK7P,IAAI,EAAE4iB,MAAA;IAC1CA,MAAA,CAAOlhB,IAAA,CAAKugB,QAAQ;IAEpB5B,OAAA,CAAQxQ,UAAA,GAAawQ,OAAA,CAAQxQ,UAAA,IAAc,CAAE;IAC7CwQ,OAAA,CAAQxQ,UAAA,CAAW,KAAK7P,IAAI,IAAI;MAAE4hB,KAAA,EAAOgB,MAAA,CAAOtgB,MAAA,GAAS;IAAG;EAC7D;AACH;AAOA,MAAMxB,2BAAA,CAA4B;EAChCL,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASK,mBAAA,EAAqB;IAEnC,MAAMlW,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9BgK,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI,CAAE;IAEtC2M,cAAA,CAAe,KAAK3M,IAAI,IAAI;IAE5B2W,WAAA,CAAYC,oBAAA,CAAqBO,cAAA,GAAiB;IAClDR,WAAA,CAAYC,oBAAA,CAAqBQ,eAAA,GAAkB;EACpD;AACH;AAOA,MAAMjW,+BAAA,CAAgC;EACpCV,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASqM,SAAA,KAAc,GAAG;IAElE,MAAMliB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAaC,eAAA,GAAkBvM,QAAA,CAASqM,SAAA;IAExC,IAAIrM,QAAA,CAASwM,YAAA,EAAc;MACzB,MAAMC,eAAA,GAAkB;QACtBtf,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASwM,YAAY;QAClDxL,QAAA,EAAUhB,QAAA,CAASwM,YAAA,CAAahQ;MACjC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBgS,eAAA,EAAiBzM,QAAA,CAASwM,YAAY;MACnEF,YAAA,CAAaI,gBAAA,GAAmBD,eAAA;IACjC;IAEDH,YAAA,CAAaK,wBAAA,GAA2B3M,QAAA,CAAS4M,kBAAA;IAEjD,IAAI5M,QAAA,CAAS6M,qBAAA,EAAuB;MAClC,MAAMC,wBAAA,GAA2B;QAC/B3f,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAAS6M,qBAAqB;QAC3D7L,QAAA,EAAUhB,QAAA,CAAS6M,qBAAA,CAAsBrQ;MAC1C;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBqS,wBAAA,EAA0B9M,QAAA,CAAS6M,qBAAqB;MACrFP,YAAA,CAAaS,yBAAA,GAA4BD,wBAAA;IAC1C;IAED,IAAI9M,QAAA,CAASgN,kBAAA,EAAoB;MAC/B,MAAMC,qBAAA,GAAwB;QAC5B9f,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASgN,kBAAkB;QACxDhM,QAAA,EAAUhB,QAAA,CAASgN,kBAAA,CAAmBxQ;MACvC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBwS,qBAAA,EAAuBjN,QAAA,CAASgN,kBAAkB;MAC/EV,YAAA,CAAaY,sBAAA,GAAyBD,qBAAA;IACvC;IAED/M,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMoB,iCAAA,CAAkC;EACtCX,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASmN,WAAA,KAAgB,GAAG;IAEpE,MAAMhjB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAac,iBAAA,GAAoBpN,QAAA,CAASmN,WAAA;IAE1C,IAAInN,QAAA,CAASqN,cAAA,EAAgB;MAC3B,MAAMC,iBAAA,GAAoB;QACxBngB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASqN,cAAc;QACpDrM,QAAA,EAAUhB,QAAA,CAASqN,cAAA,CAAe7Q;MACnC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsB6S,iBAAA,EAAmBtN,QAAA,CAASqN,cAAc;MACvEf,YAAA,CAAaiB,kBAAA,GAAqBD,iBAAA;IACnC;IAEDhB,YAAA,CAAakB,cAAA,GAAiBxN,QAAA,CAASyN,cAAA;IACvCnB,YAAA,CAAaoB,2BAAA,GAA8B1N,QAAA,CAAS2N,yBAAA,CAA0B,CAAC;IAC/ErB,YAAA,CAAasB,2BAAA,GAA8B5N,QAAA,CAAS2N,yBAAA,CAA0B,CAAC;IAE/E,IAAI3N,QAAA,CAAS6N,uBAAA,EAAyB;MACpC,MAAMC,0BAAA,GAA6B;QACjC3gB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAAS6N,uBAAuB;QAC7D7M,QAAA,EAAUhB,QAAA,CAAS6N,uBAAA,CAAwBrR;MAC5C;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBqT,0BAAA,EAA4B9N,QAAA,CAAS6N,uBAAuB;MACzFvB,YAAA,CAAayB,2BAAA,GAA8BD,0BAAA;IAC5C;IAED5N,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMe,kCAAA,CAAmC;EACvCN,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASgO,YAAA,KAAiB,GAAG;IAErE,MAAM7jB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAa2B,kBAAA,GAAqBjO,QAAA,CAASgO,YAAA;IAE3C,IAAIhO,QAAA,CAASkO,eAAA,EAAiB;MAC5B,MAAMC,kBAAA,GAAqB;QACzBhhB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASkO,eAAe;QACrDlN,QAAA,EAAUhB,QAAA,CAASkO,eAAA,CAAgB1R;MACpC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsB0T,kBAAA,EAAoBnO,QAAA,CAASkO,eAAe;MACzE5B,YAAA,CAAa8B,mBAAA,GAAsBD,kBAAA;IACpC;IAEDjO,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMgB,4BAAA,CAA6B;EACjCP,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASgO,YAAA,KAAiB,GAAG;IAErE,MAAM7jB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAa+B,eAAA,GAAkBrO,QAAA,CAASsO,SAAA;IAExC,IAAItO,QAAA,CAASuO,YAAA,EAAc;MACzB,MAAMC,eAAA,GAAkB;QACtBrhB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASuO,YAAY;QAClDvN,QAAA,EAAUhB,QAAA,CAASuO,YAAA,CAAa/R;MACjC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsB+T,eAAA,EAAiBxO,QAAA,CAASuO,YAAY;MACnEjC,YAAA,CAAamC,gBAAA,GAAmBD,eAAA;IACjC;IAEDlC,YAAA,CAAaoC,mBAAA,GAAsB1O,QAAA,CAAS0O,mBAAA;IAC5CpC,YAAA,CAAaqC,gBAAA,GAAmB3O,QAAA,CAAS2O,gBAAA,CAAiB7T,OAAA,CAAS;IAEnEoF,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMiB,yBAAA,CAA0B;EAC9BR,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAAS4O,GAAA,KAAQ,KAAK;IAE9D,MAAMzkB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAasC,GAAA,GAAM5O,QAAA,CAAS4O,GAAA;IAE5B1O,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMkB,8BAAA,CAA+B;EACnCT,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IACE,CAACF,QAAA,CAASoM,sBAAA,IACTpM,QAAA,CAAS6O,iBAAA,KAAsB,KAC9B7O,QAAA,CAAS8O,aAAA,CAAcC,MAAA,CAAO7c,sBAAsB,KACpD,CAAC8N,QAAA,CAASgP,oBAAA,IACV,CAAChP,QAAA,CAASiP,oBAAA,EAEZ;IAEF,MAAM9kB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvB,IAAItM,QAAA,CAASgP,oBAAA,EAAsB;MACjC,MAAME,uBAAA,GAA0B;QAC9B/hB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASgP,oBAAoB;QAC1DhO,QAAA,EAAUhB,QAAA,CAASgP,oBAAA,CAAqBxS;MACzC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsByU,uBAAA,EAAyBlP,QAAA,CAASgP,oBAAoB;MACnF1C,YAAA,CAAa6C,eAAA,GAAkBD,uBAAA;IAChC;IAED,IAAIlP,QAAA,CAASoP,gBAAA,EAAkB;MAC7B,MAAMC,mBAAA,GAAsB;QAC1BliB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASoP,gBAAgB;QACtDpO,QAAA,EAAUhB,QAAA,CAASoP,gBAAA,CAAiB5S;MACrC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsB4U,mBAAA,EAAqBrP,QAAA,CAASoP,gBAAgB;MAC3E9C,YAAA,CAAa2C,oBAAA,GAAuBI,mBAAA;IACrC;IAED/C,YAAA,CAAagD,cAAA,GAAiBtP,QAAA,CAAS6O,iBAAA;IACvCvC,YAAA,CAAaiD,mBAAA,GAAsBvP,QAAA,CAAS8O,aAAA,CAAchU,OAAA,CAAS;IAEnEoF,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMqB,2BAAA,CAA4B;EAChCZ,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASwP,KAAA,IAAS,GAAK;IAE/D,MAAMrlB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvB,IAAItM,QAAA,CAASyP,iBAAA,EAAmB;MAC9B,MAAMC,oBAAA,GAAuB;QAC3BviB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASyP,iBAAiB;QACvDzO,QAAA,EAAUhB,QAAA,CAASyP,iBAAA,CAAkBjT;MACtC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBiV,oBAAA,EAAsB1P,QAAA,CAASyP,iBAAiB;MAC7EnD,YAAA,CAAaqD,qBAAA,GAAwBD,oBAAA;IACtC;IAED,IAAI1P,QAAA,CAAS4P,aAAA,EAAe;MAC1B,MAAMC,gBAAA,GAAmB;QACvB1iB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAAS4P,aAAa;QACnD5O,QAAA,EAAUhB,QAAA,CAAS4P,aAAA,CAAcpT;MAClC;MACDrS,MAAA,CAAOsQ,qBAAA,CAAsBoV,gBAAA,EAAkB7P,QAAA,CAAS4P,aAAa;MACrEtD,YAAA,CAAawD,iBAAA,GAAoBD,gBAAA;IAClC;IAEDvD,YAAA,CAAayD,oBAAA,GAAuB/P,QAAA,CAASgQ,cAAA;IAC7C1D,YAAA,CAAa2D,gBAAA,GAAmBjQ,QAAA,CAASkQ,UAAA,CAAWpV,OAAA,CAAS;IAE7DoF,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMsB,gCAAA,CAAiC;EACrCb,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASoM,sBAAA,IAA0BpM,QAAA,CAASmQ,UAAA,IAAc,GAAK;IAEpE,MAAMhmB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvB,IAAItM,QAAA,CAASoQ,aAAA,EAAe;MAC1B,MAAMC,gBAAA,GAAmB;QAAEljB,KAAA,EAAOhD,MAAA,CAAOsV,cAAA,CAAeO,QAAA,CAASoQ,aAAa;MAAG;MACjFjmB,MAAA,CAAOsQ,qBAAA,CAAsB4V,gBAAA,EAAkBrQ,QAAA,CAASoQ,aAAa;MACrE9D,YAAA,CAAagE,iBAAA,GAAoBD,gBAAA;IAClC;IAED/D,YAAA,CAAaiE,kBAAA,GAAqBvQ,QAAA,CAASmQ,UAAA;IAC3C7D,YAAA,CAAakE,kBAAA,GAAqBxQ,QAAA,CAASwQ,kBAAA;IAE3CtQ,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH;AAOA,MAAMuB,sCAAA,CAAuC;EAC3Cd,YAAYG,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKZ,IAAA,GAAO;EACb;EAEDoZ,cAAc3C,QAAA,EAAUE,WAAA,EAAa;IACnC,IAAI,CAACF,QAAA,CAASI,sBAAA,IAA0BJ,QAAA,CAASyQ,iBAAA,KAAsB,GAAK;IAE5E,MAAMtmB,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+L,cAAA,GAAiB/L,MAAA,CAAO+L,cAAA;IAE9B,MAAMoW,YAAA,GAAe,CAAE;IAEvBA,YAAA,CAAaoE,gBAAA,GAAmB1Q,QAAA,CAASyQ,iBAAA;IAEzCvQ,WAAA,CAAY9G,UAAA,GAAa8G,WAAA,CAAY9G,UAAA,IAAc,CAAE;IACrD8G,WAAA,CAAY9G,UAAA,CAAW,KAAK7P,IAAI,IAAI+iB,YAAA;IAEpCpW,cAAA,CAAe,KAAK3M,IAAI,IAAI;EAC7B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}