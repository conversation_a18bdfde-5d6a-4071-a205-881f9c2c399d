{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Euler } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nconst _euler = /* @__PURE__ */new Euler(0, 0, 0, \"YXZ\");\nconst _vector = /* @__PURE__ */new Vector3();\nconst _changeEvent = {\n  type: \"change\"\n};\nconst _lockEvent = {\n  type: \"lock\"\n};\nconst _unlockEvent = {\n  type: \"unlock\"\n};\nconst _MOUSE_SENSITIVITY = 2e-3;\nconst _PI_2 = Math.PI / 2;\nclass PointerLockControls extends EventDispatcher {\n  constructor(camera, domElement) {\n    super();\n    __publicField(this, \"camera\");\n    __publicField(this, \"domElement\");\n    __publicField(this, \"isLocked\");\n    __publicField(this, \"minPolarAngle\");\n    __publicField(this, \"maxPolarAngle\");\n    __publicField(this, \"pointerSpeed\");\n    __publicField(this, \"onMouseMove\", event => {\n      if (!this.domElement || this.isLocked === false) return;\n      _euler.setFromQuaternion(this.camera.quaternion);\n      _euler.y -= event.movementX * _MOUSE_SENSITIVITY * this.pointerSpeed;\n      _euler.x -= event.movementY * _MOUSE_SENSITIVITY * this.pointerSpeed;\n      _euler.x = Math.max(_PI_2 - this.maxPolarAngle, Math.min(_PI_2 - this.minPolarAngle, _euler.x));\n      this.camera.quaternion.setFromEuler(_euler);\n      this.dispatchEvent(_changeEvent);\n    });\n    __publicField(this, \"onPointerlockChange\", () => {\n      if (!this.domElement) return;\n      if (this.domElement.ownerDocument.pointerLockElement === this.domElement) {\n        this.dispatchEvent(_lockEvent);\n        this.isLocked = true;\n      } else {\n        this.dispatchEvent(_unlockEvent);\n        this.isLocked = false;\n      }\n    });\n    __publicField(this, \"onPointerlockError\", () => {\n      console.error(\"THREE.PointerLockControls: Unable to use Pointer Lock API\");\n    });\n    __publicField(this, \"connect\", domElement => {\n      this.domElement = domElement || this.domElement;\n      if (!this.domElement) return;\n      this.domElement.ownerDocument.addEventListener(\"mousemove\", this.onMouseMove);\n      this.domElement.ownerDocument.addEventListener(\"pointerlockchange\", this.onPointerlockChange);\n      this.domElement.ownerDocument.addEventListener(\"pointerlockerror\", this.onPointerlockError);\n    });\n    __publicField(this, \"disconnect\", () => {\n      if (!this.domElement) return;\n      this.domElement.ownerDocument.removeEventListener(\"mousemove\", this.onMouseMove);\n      this.domElement.ownerDocument.removeEventListener(\"pointerlockchange\", this.onPointerlockChange);\n      this.domElement.ownerDocument.removeEventListener(\"pointerlockerror\", this.onPointerlockError);\n    });\n    __publicField(this, \"dispose\", () => {\n      this.disconnect();\n    });\n    __publicField(this, \"getObject\", () => {\n      return this.camera;\n    });\n    __publicField(this, \"direction\", new Vector3(0, 0, -1));\n    __publicField(this, \"getDirection\", v => {\n      return v.copy(this.direction).applyQuaternion(this.camera.quaternion);\n    });\n    __publicField(this, \"moveForward\", distance => {\n      _vector.setFromMatrixColumn(this.camera.matrix, 0);\n      _vector.crossVectors(this.camera.up, _vector);\n      this.camera.position.addScaledVector(_vector, distance);\n    });\n    __publicField(this, \"moveRight\", distance => {\n      _vector.setFromMatrixColumn(this.camera.matrix, 0);\n      this.camera.position.addScaledVector(_vector, distance);\n    });\n    __publicField(this, \"lock\", () => {\n      if (this.domElement) this.domElement.requestPointerLock();\n    });\n    __publicField(this, \"unlock\", () => {\n      if (this.domElement) this.domElement.ownerDocument.exitPointerLock();\n    });\n    this.camera = camera;\n    this.domElement = domElement;\n    this.isLocked = false;\n    this.minPolarAngle = 0;\n    this.maxPolarAngle = Math.PI;\n    this.pointerSpeed = 1;\n    if (domElement) this.connect(domElement);\n  }\n}\nexport { PointerLockControls };", "map": {"version": 3, "names": ["_euler", "<PERSON>uler", "_vector", "Vector3", "_changeEvent", "type", "_lockEvent", "_unlockEvent", "_MOUSE_SENSITIVITY", "_PI_2", "Math", "PI", "PointerLockControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "camera", "dom<PERSON>lement", "__publicField", "event", "isLocked", "setFromQuaternion", "quaternion", "y", "movementX", "pointerSpeed", "x", "movementY", "max", "maxPolarAngle", "min", "minPolarAngle", "setFromEuler", "dispatchEvent", "ownerDocument", "pointerLockElement", "console", "error", "addEventListener", "onMouseMove", "onPointerlockChange", "onPointerlockError", "removeEventListener", "disconnect", "v", "copy", "direction", "applyQuaternion", "distance", "setFromMatrixColumn", "matrix", "crossVectors", "up", "position", "addScaledVector", "requestPointerLock", "exitPointerLock", "connect"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/controls/PointerLockControls.ts"], "sourcesContent": ["import { <PERSON>ule<PERSON>, <PERSON>, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nconst _euler = /* @__PURE__ */ new Euler(0, 0, 0, 'YXZ')\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _changeEvent = { type: 'change' }\nconst _lockEvent = { type: 'lock' }\nconst _unlockEvent = { type: 'unlock' }\nconst _MOUSE_SENSITIVITY = 0.002\nconst _PI_2 = Math.PI / 2\n\nexport interface PointerLockControlsEventMap {\n  /**\n   * Fires when the user moves the mouse.\n   */\n  change: {}\n\n  /**\n   * Fires when the pointer lock status is \"locked\" (in other words: the mouse is captured).\n   */\n  lock: {}\n\n  /**\n   * Fires when the pointer lock status is \"unlocked\" (in other words: the mouse is not captured anymore).\n   */\n  unlock: {}\n}\n\nclass PointerLockControls extends EventDispatcher<PointerLockControlsEventMap> {\n  public camera: Camera\n  public domElement?: HTMLElement\n  public isLocked: boolean\n  public minPolarAngle: number\n  public maxPolarAngle: number\n  public pointerSpeed: number\n\n  constructor(camera: Camera, domElement?: HTMLElement) {\n    super()\n\n    this.camera = camera\n    this.domElement = domElement\n    this.isLocked = false\n\n    // Set to constrain the pitch of the camera\n    // Range is 0 to Math.PI radians\n    this.minPolarAngle = 0 // radians\n    this.maxPolarAngle = Math.PI // radians\n\n    this.pointerSpeed = 1.0\n    if (domElement) this.connect(domElement)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (!this.domElement || this.isLocked === false) return\n    _euler.setFromQuaternion(this.camera.quaternion)\n    _euler.y -= event.movementX * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x -= event.movementY * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x = Math.max(_PI_2 - this.maxPolarAngle, Math.min(_PI_2 - this.minPolarAngle, _euler.x))\n    this.camera.quaternion.setFromEuler(_euler)\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onPointerlockChange = (): void => {\n    if (!this.domElement) return\n    if (this.domElement.ownerDocument.pointerLockElement === this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_lockEvent)\n      this.isLocked = true\n    } else {\n      // @ts-ignore\n      this.dispatchEvent(_unlockEvent)\n      this.isLocked = false\n    }\n  }\n\n  private onPointerlockError = (): void => {\n    console.error('THREE.PointerLockControls: Unable to use Pointer Lock API')\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    this.domElement = domElement || this.domElement\n    if (!this.domElement) return\n    this.domElement.ownerDocument.addEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.addEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.addEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public disconnect = (): void => {\n    if (!this.domElement) return\n    this.domElement.ownerDocument.removeEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.removeEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.removeEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public dispose = (): void => {\n    this.disconnect()\n  }\n\n  public getObject = (): Camera => {\n    // retaining this method for backward compatibility\n    return this.camera\n  }\n\n  private direction = new Vector3(0, 0, -1)\n  public getDirection = (v: Vector3): Vector3 => {\n    return v.copy(this.direction).applyQuaternion(this.camera.quaternion)\n  }\n\n  public moveForward = (distance: number): void => {\n    // move forward parallel to the xz-plane\n    // assumes camera.up is y-up\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    _vector.crossVectors(this.camera.up, _vector)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public moveRight = (distance: number): void => {\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public lock = (): void => {\n    if (this.domElement) this.domElement.requestPointerLock()\n  }\n\n  public unlock = (): void => {\n    if (this.domElement) this.domElement.ownerDocument.exitPointerLock()\n  }\n}\n\nexport { PointerLockControls }\n"], "mappings": ";;;;;;;;;;;;;AAGA,MAAMA,MAAA,GAA6B,mBAAAC,KAAA,CAAM,GAAG,GAAG,GAAG,KAAK;AACvD,MAAMC,OAAA,sBAA8BC,OAAA;AACpC,MAAMC,YAAA,GAAe;EAAEC,IAAA,EAAM;AAAA;AAC7B,MAAMC,UAAA,GAAa;EAAED,IAAA,EAAM;AAAA;AAC3B,MAAME,YAAA,GAAe;EAAEF,IAAA,EAAM;AAAA;AAC7B,MAAMG,kBAAA,GAAqB;AAC3B,MAAMC,KAAA,GAAQC,IAAA,CAAKC,EAAA,GAAK;AAmBxB,MAAMC,mBAAA,SAA4BC,eAAA,CAA6C;EAQ7EC,YAAYC,MAAA,EAAgBC,UAAA,EAA0B;IAC9C;IARDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAkBCA,aAAA,sBAAeC,KAAA,IAA4B;MACjD,IAAI,CAAC,KAAKF,UAAA,IAAc,KAAKG,QAAA,KAAa,OAAO;MAC1CnB,MAAA,CAAAoB,iBAAA,CAAkB,KAAKL,MAAA,CAAOM,UAAU;MAC/CrB,MAAA,CAAOsB,CAAA,IAAKJ,KAAA,CAAMK,SAAA,GAAYf,kBAAA,GAAqB,KAAKgB,YAAA;MACxDxB,MAAA,CAAOyB,CAAA,IAAKP,KAAA,CAAMQ,SAAA,GAAYlB,kBAAA,GAAqB,KAAKgB,YAAA;MACxDxB,MAAA,CAAOyB,CAAA,GAAIf,IAAA,CAAKiB,GAAA,CAAIlB,KAAA,GAAQ,KAAKmB,aAAA,EAAelB,IAAA,CAAKmB,GAAA,CAAIpB,KAAA,GAAQ,KAAKqB,aAAA,EAAe9B,MAAA,CAAOyB,CAAC,CAAC;MACzF,KAAAV,MAAA,CAAOM,UAAA,CAAWU,YAAA,CAAa/B,MAAM;MAE1C,KAAKgC,aAAA,CAAc5B,YAAY;IAAA;IAGzBa,aAAA,8BAAsB,MAAY;MACxC,IAAI,CAAC,KAAKD,UAAA,EAAY;MACtB,IAAI,KAAKA,UAAA,CAAWiB,aAAA,CAAcC,kBAAA,KAAuB,KAAKlB,UAAA,EAAY;QAExE,KAAKgB,aAAA,CAAc1B,UAAU;QAC7B,KAAKa,QAAA,GAAW;MAAA,OACX;QAEL,KAAKa,aAAA,CAAczB,YAAY;QAC/B,KAAKY,QAAA,GAAW;MAClB;IAAA;IAGMF,aAAA,6BAAqB,MAAY;MACvCkB,OAAA,CAAQC,KAAA,CAAM,2DAA2D;IAAA;IAGpEnB,aAAA,kBAAWD,UAAA,IAAkC;MAC7C,KAAAA,UAAA,GAAaA,UAAA,IAAc,KAAKA,UAAA;MACrC,IAAI,CAAC,KAAKA,UAAA,EAAY;MACtB,KAAKA,UAAA,CAAWiB,aAAA,CAAcI,gBAAA,CAAiB,aAAa,KAAKC,WAAW;MAC5E,KAAKtB,UAAA,CAAWiB,aAAA,CAAcI,gBAAA,CAAiB,qBAAqB,KAAKE,mBAAmB;MAC5F,KAAKvB,UAAA,CAAWiB,aAAA,CAAcI,gBAAA,CAAiB,oBAAoB,KAAKG,kBAAkB;IAAA;IAGrFvB,aAAA,qBAAa,MAAY;MAC9B,IAAI,CAAC,KAAKD,UAAA,EAAY;MACtB,KAAKA,UAAA,CAAWiB,aAAA,CAAcQ,mBAAA,CAAoB,aAAa,KAAKH,WAAW;MAC/E,KAAKtB,UAAA,CAAWiB,aAAA,CAAcQ,mBAAA,CAAoB,qBAAqB,KAAKF,mBAAmB;MAC/F,KAAKvB,UAAA,CAAWiB,aAAA,CAAcQ,mBAAA,CAAoB,oBAAoB,KAAKD,kBAAkB;IAAA;IAGxFvB,aAAA,kBAAU,MAAY;MAC3B,KAAKyB,UAAA,CAAW;IAAA;IAGXzB,aAAA,oBAAY,MAAc;MAE/B,OAAO,KAAKF,MAAA;IAAA;IAGNE,aAAA,oBAAY,IAAId,OAAA,CAAQ,GAAG,GAAG,EAAE;IACjCc,aAAA,uBAAgB0B,CAAA,IAAwB;MACtC,OAAAA,CAAA,CAAEC,IAAA,CAAK,KAAKC,SAAS,EAAEC,eAAA,CAAgB,KAAK/B,MAAA,CAAOM,UAAU;IAAA;IAG/DJ,aAAA,sBAAe8B,QAAA,IAA2B;MAG/C7C,OAAA,CAAQ8C,mBAAA,CAAoB,KAAKjC,MAAA,CAAOkC,MAAA,EAAQ,CAAC;MACjD/C,OAAA,CAAQgD,YAAA,CAAa,KAAKnC,MAAA,CAAOoC,EAAA,EAAIjD,OAAO;MAC5C,KAAKa,MAAA,CAAOqC,QAAA,CAASC,eAAA,CAAgBnD,OAAA,EAAS6C,QAAQ;IAAA;IAGjD9B,aAAA,oBAAa8B,QAAA,IAA2B;MAC7C7C,OAAA,CAAQ8C,mBAAA,CAAoB,KAAKjC,MAAA,CAAOkC,MAAA,EAAQ,CAAC;MACjD,KAAKlC,MAAA,CAAOqC,QAAA,CAASC,eAAA,CAAgBnD,OAAA,EAAS6C,QAAQ;IAAA;IAGjD9B,aAAA,eAAO,MAAY;MACxB,IAAI,KAAKD,UAAA,EAAY,KAAKA,UAAA,CAAWsC,kBAAA;IAAmB;IAGnDrC,aAAA,iBAAS,MAAY;MAC1B,IAAI,KAAKD,UAAA,EAAiB,KAAAA,UAAA,CAAWiB,aAAA,CAAcsB,eAAA;IAAgB;IAxFnE,KAAKxC,MAAA,GAASA,MAAA;IACd,KAAKC,UAAA,GAAaA,UAAA;IAClB,KAAKG,QAAA,GAAW;IAIhB,KAAKW,aAAA,GAAgB;IACrB,KAAKF,aAAA,GAAgBlB,IAAA,CAAKC,EAAA;IAE1B,KAAKa,YAAA,GAAe;IAChB,IAAAR,UAAA,EAAY,KAAKwC,OAAA,CAAQxC,UAAU;EACzC;AA+EF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}