{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { SkeletonUtils } from 'three-stdlib';\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = {};\n  for (const key of keys) {\n    spread[key] = child[key];\n  }\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/React.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /* @__PURE__ */React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]);\n\n  // Deal with arrayed clones\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  }\n\n  // Singleton clones\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), object.children.map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\nexport { Clone };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "SkeletonUtils", "createSpread", "child", "keys", "deep", "inject", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "spread", "key", "geometry", "clone", "material", "children", "isValidElement", "<PERSON><PERSON>", "<PERSON><PERSON>", "forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "object", "props", "config", "useMemo", "Array", "isArray", "isSkinned", "traverse", "isSkinnedMesh", "createElement", "ref", "map", "o", "uuid", "injectChildren", "Element", "type", "toLowerCase", "slice"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Clone.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { SkeletonUtils } from 'three-stdlib';\n\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = {};\n  for (const key of keys) {\n    spread[key] = child[key];\n  }\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/React.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /* @__PURE__ */React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]);\n\n  // Deal with arrayed clones\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  }\n\n  // Singleton clones\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), object.children.map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\n\nexport { Clone };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3BC,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,CAAC;EAC5UC,IAAI;EACJC,MAAM;EACNC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,MAAMC,GAAG,IAAIN,IAAI,EAAE;IACtBK,MAAM,CAACC,GAAG,CAAC,GAAGP,KAAK,CAACO,GAAG,CAAC;EAC1B;EACA,IAAIL,IAAI,EAAE;IACR,IAAII,MAAM,CAACE,QAAQ,IAAIN,IAAI,KAAK,eAAe,EAAEI,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC;IAC1F,IAAIH,MAAM,CAACI,QAAQ,IAAIR,IAAI,KAAK,gBAAgB,EAAEI,MAAM,CAACI,QAAQ,GAAGJ,MAAM,CAACI,QAAQ,CAACD,KAAK,CAAC,CAAC;EAC7F;EACA,IAAIN,MAAM,EAAE;IACV,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAEG,MAAM,GAAG;MACzC,GAAGA,MAAM;MACTK,QAAQ,EAAER,MAAM,CAACH,KAAK;IACxB,CAAC,CAAC,KAAK,IAAI,aAAaH,KAAK,CAACe,cAAc,CAACT,MAAM,CAAC,EAAEG,MAAM,GAAG;MAC7D,GAAGA,MAAM;MACTK,QAAQ,EAAER;IACZ,CAAC,CAAC,KAAKG,MAAM,GAAG;MACd,GAAGA,MAAM;MACT,GAAGH;IACL,CAAC;EACH;EACA,IAAIH,KAAK,YAAYJ,KAAK,CAACiB,IAAI,EAAE;IAC/B,IAAIT,UAAU,EAAEE,MAAM,CAACF,UAAU,GAAG,IAAI;IACxC,IAAIC,aAAa,EAAEC,MAAM,CAACD,aAAa,GAAG,IAAI;EAChD;EACA,OAAOC,MAAM;AACf;AACA,MAAMQ,KAAK,GAAG,eAAejB,KAAK,CAACkB,UAAU,CAAC,CAAC;EAC7CC,OAAO,GAAG,KAAK;EACfC,MAAM;EACNN,QAAQ;EACRT,IAAI;EACJE,UAAU;EACVC,aAAa;EACbF,MAAM;EACNF,IAAI;EACJ,GAAGiB;AACL,CAAC,EAAEH,UAAU,KAAK;EAChB,MAAMI,MAAM,GAAG;IACblB,IAAI;IACJC,IAAI;IACJC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EACDY,MAAM,GAAGpB,KAAK,CAACuB,OAAO,CAAC,MAAM;IAC3B,IAAIJ,OAAO,KAAK,KAAK,IAAI,CAACK,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MAC/C,IAAIM,SAAS,GAAG,KAAK;MACrBN,MAAM,CAACO,QAAQ,CAACP,MAAM,IAAI;QACxB,IAAIA,MAAM,CAACQ,aAAa,EAAEF,SAAS,GAAG,IAAI;MAC5C,CAAC,CAAC;MACF,IAAIA,SAAS,EAAE,OAAOzB,aAAa,CAACW,KAAK,CAACQ,MAAM,CAAC;IACnD;IACA,OAAOA,MAAM;EACf,CAAC,EAAE,CAACA,MAAM,EAAED,OAAO,CAAC,CAAC;;EAErB;EACA,IAAIK,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;IACzB,OAAO,aAAapB,KAAK,CAAC6B,aAAa,CAAC,OAAO,EAAE/B,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;MACnES,GAAG,EAAEZ;IACP,CAAC,CAAC,EAAEE,MAAM,CAACW,GAAG,CAACC,CAAC,IAAI,aAAahC,KAAK,CAAC6B,aAAa,CAACZ,KAAK,EAAEnB,QAAQ,CAAC;MACnEY,GAAG,EAAEsB,CAAC,CAACC,IAAI;MACXb,MAAM,EAAEY;IACV,CAAC,EAAEV,MAAM,CAAC,CAAC,CAAC,EAAER,QAAQ,CAAC;EACzB;;EAEA;EACA,MAAM;IACJA,QAAQ,EAAEoB,cAAc;IACxB,GAAGzB;EACL,CAAC,GAAGP,YAAY,CAACkB,MAAM,EAAEE,MAAM,CAAC;EAChC,MAAMa,OAAO,GAAGf,MAAM,CAACgB,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGjB,MAAM,CAACgB,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EACnE,OAAO,aAAatC,KAAK,CAAC6B,aAAa,CAACM,OAAO,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEW,MAAM,EAAEY,KAAK,EAAE;IAC3ES,GAAG,EAAEZ;EACP,CAAC,CAAC,EAAEE,MAAM,CAACN,QAAQ,CAACiB,GAAG,CAAC5B,KAAK,IAAI;IAC/B,IAAIA,KAAK,CAACiC,IAAI,KAAK,MAAM,EAAE,OAAO,aAAapC,KAAK,CAAC6B,aAAa,CAAC,WAAW,EAAE/B,QAAQ,CAAC;MACvFY,GAAG,EAAEP,KAAK,CAAC8B,IAAI;MACfb,MAAM,EAAEjB;IACV,CAAC,EAAEmB,MAAM,CAAC,CAAC;IACX,OAAO,aAAatB,KAAK,CAAC6B,aAAa,CAACZ,KAAK,EAAEnB,QAAQ,CAAC;MACtDY,GAAG,EAAEP,KAAK,CAAC8B,IAAI;MACfb,MAAM,EAAEjB;IACV,CAAC,EAAEmB,MAAM,EAAE;MACTH,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,EAAEL,QAAQ,EAAEoB,cAAc,CAAC;AAC/B,CAAC,CAAC;AAEF,SAASjB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}