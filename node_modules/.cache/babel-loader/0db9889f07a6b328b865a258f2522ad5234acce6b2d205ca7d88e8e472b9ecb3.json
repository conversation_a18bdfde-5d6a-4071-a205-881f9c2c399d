{"ast": null, "code": "class LWO3Parser {\n  constructor(IFFParser) {\n    this.IFF = IFFParser;\n  }\n  parseBlock() {\n    this.IFF.debugger.offset = this.IFF.reader.offset;\n    this.IFF.debugger.closeForms();\n    const blockID = this.IFF.reader.getIDTag();\n    const length = this.IFF.reader.getUint32();\n    this.IFF.debugger.dataOffset = this.IFF.reader.offset;\n    this.IFF.debugger.length = length;\n    switch (blockID) {\n      case \"FORM\":\n        this.IFF.parseForm(length);\n        break;\n      case \"ICON\":\n      case \"VMPA\":\n      case \"BBOX\":\n      case \"NORM\":\n      case \"PRE \":\n      case \"POST\":\n      case \"KEY \":\n      case \"SPAN\":\n      case \"TIME\":\n      case \"CLRS\":\n      case \"CLRA\":\n      case \"FILT\":\n      case \"DITH\":\n      case \"CONT\":\n      case \"BRIT\":\n      case \"SATR\":\n      case \"HUE \":\n      case \"GAMM\":\n      case \"NEGA\":\n      case \"IFLT\":\n      case \"PFLT\":\n      case \"PROJ\":\n      case \"AXIS\":\n      case \"AAST\":\n      case \"PIXB\":\n      case \"STCK\":\n      case \"VALU\":\n      case \"PNAM\":\n      case \"INAM\":\n      case \"GRST\":\n      case \"GREN\":\n      case \"GRPT\":\n      case \"FKEY\":\n      case \"IKEY\":\n      case \"CSYS\":\n      case \"OPAQ\":\n      case \"CMAP\":\n      case \"NLOC\":\n      case \"NZOM\":\n      case \"NVER\":\n      case \"NSRV\":\n      case \"NCRD\":\n      case \"NMOD\":\n      case \"NSEL\":\n      case \"NPRW\":\n      case \"NPLA\":\n      case \"VERS\":\n      case \"ENUM\":\n      case \"TAG \":\n      case \"CGMD\":\n      case \"CGTY\":\n      case \"CGST\":\n      case \"CGEN\":\n      case \"CGTS\":\n      case \"CGTE\":\n      case \"OSMP\":\n      case \"OMDE\":\n      case \"OUTR\":\n      case \"FLAG\":\n      case \"TRNL\":\n      case \"SHRP\":\n      case \"RFOP\":\n      case \"RSAN\":\n      case \"TROP\":\n      case \"RBLR\":\n      case \"TBLR\":\n      case \"CLRH\":\n      case \"CLRF\":\n      case \"ADTR\":\n      case \"GLOW\":\n      case \"LINE\":\n      case \"ALPH\":\n      case \"VCOL\":\n      case \"ENAB\":\n        this.IFF.debugger.skipped = true;\n        this.IFF.reader.skip(length);\n        break;\n      case \"IPIX\":\n      case \"IMIP\":\n      case \"IMOD\":\n      case \"AMOD\":\n      case \"IINV\":\n      case \"INCR\":\n      case \"IAXS\":\n      case \"IFOT\":\n      case \"ITIM\":\n      case \"IWRL\":\n      case \"IUTI\":\n      case \"IINX\":\n      case \"IINY\":\n      case \"IINZ\":\n      case \"IREF\":\n        if (length === 4) this.IFF.currentNode[blockID] = this.IFF.reader.getInt32();else this.IFF.reader.skip(length);\n        break;\n      case \"OTAG\":\n        this.IFF.parseObjectTag();\n        break;\n      case \"LAYR\":\n        this.IFF.parseLayer(length);\n        break;\n      case \"PNTS\":\n        this.IFF.parsePoints(length);\n        break;\n      case \"VMAP\":\n        this.IFF.parseVertexMapping(length);\n        break;\n      case \"POLS\":\n        this.IFF.parsePolygonList(length);\n        break;\n      case \"TAGS\":\n        this.IFF.parseTagStrings(length);\n        break;\n      case \"PTAG\":\n        this.IFF.parsePolygonTagMapping(length);\n        break;\n      case \"VMAD\":\n        this.IFF.parseVertexMapping(length, true);\n        break;\n      case \"DESC\":\n        this.IFF.currentForm.description = this.IFF.reader.getString();\n        break;\n      case \"TEXT\":\n      case \"CMNT\":\n      case \"NCOM\":\n        this.IFF.currentForm.comment = this.IFF.reader.getString();\n        break;\n      case \"NAME\":\n        this.IFF.currentForm.channelName = this.IFF.reader.getString();\n        break;\n      case \"WRAP\":\n        this.IFF.currentForm.wrap = {\n          w: this.IFF.reader.getUint16(),\n          h: this.IFF.reader.getUint16()\n        };\n        break;\n      case \"IMAG\":\n        const index = this.IFF.reader.getVariableLengthIndex();\n        this.IFF.currentForm.imageIndex = index;\n        break;\n      case \"OREF\":\n        this.IFF.currentForm.referenceObject = this.IFF.reader.getString();\n        break;\n      case \"ROID\":\n        this.IFF.currentForm.referenceObjectID = this.IFF.reader.getUint32();\n        break;\n      case \"SSHN\":\n        this.IFF.currentSurface.surfaceShaderName = this.IFF.reader.getString();\n        break;\n      case \"AOVN\":\n        this.IFF.currentSurface.surfaceCustomAOVName = this.IFF.reader.getString();\n        break;\n      case \"NSTA\":\n        this.IFF.currentForm.disabled = this.IFF.reader.getUint16();\n        break;\n      case \"NRNM\":\n        this.IFF.currentForm.realName = this.IFF.reader.getString();\n        break;\n      case \"NNME\":\n        this.IFF.currentForm.refName = this.IFF.reader.getString();\n        this.IFF.currentSurface.nodes[this.IFF.currentForm.refName] = this.IFF.currentForm;\n        break;\n      case \"INME\":\n        if (!this.IFF.currentForm.nodeName) this.IFF.currentForm.nodeName = [];\n        this.IFF.currentForm.nodeName.push(this.IFF.reader.getString());\n        break;\n      case \"IINN\":\n        if (!this.IFF.currentForm.inputNodeName) this.IFF.currentForm.inputNodeName = [];\n        this.IFF.currentForm.inputNodeName.push(this.IFF.reader.getString());\n        break;\n      case \"IINM\":\n        if (!this.IFF.currentForm.inputName) this.IFF.currentForm.inputName = [];\n        this.IFF.currentForm.inputName.push(this.IFF.reader.getString());\n        break;\n      case \"IONM\":\n        if (!this.IFF.currentForm.inputOutputName) this.IFF.currentForm.inputOutputName = [];\n        this.IFF.currentForm.inputOutputName.push(this.IFF.reader.getString());\n        break;\n      case \"FNAM\":\n        this.IFF.currentForm.fileName = this.IFF.reader.getString();\n        break;\n      case \"CHAN\":\n        if (length === 4) this.IFF.currentForm.textureChannel = this.IFF.reader.getIDTag();else this.IFF.reader.skip(length);\n        break;\n      case \"SMAN\":\n        const maxSmoothingAngle = this.IFF.reader.getFloat32();\n        this.IFF.currentSurface.attributes.smooth = maxSmoothingAngle < 0 ? false : true;\n        break;\n      case \"COLR\":\n        this.IFF.currentSurface.attributes.Color = {\n          value: this.IFF.reader.getFloat32Array(3)\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"LUMI\":\n        this.IFF.currentSurface.attributes.Luminosity = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"SPEC\":\n        this.IFF.currentSurface.attributes.Specular = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"DIFF\":\n        this.IFF.currentSurface.attributes.Diffuse = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"REFL\":\n        this.IFF.currentSurface.attributes.Reflection = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"GLOS\":\n        this.IFF.currentSurface.attributes.Glossiness = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"TRAN\":\n        this.IFF.currentSurface.attributes.opacity = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"BUMP\":\n        this.IFF.currentSurface.attributes.bumpStrength = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"SIDE\":\n        this.IFF.currentSurface.attributes.side = this.IFF.reader.getUint16();\n        break;\n      case \"RIMG\":\n        this.IFF.currentSurface.attributes.reflectionMap = this.IFF.reader.getVariableLengthIndex();\n        break;\n      case \"RIND\":\n        this.IFF.currentSurface.attributes.refractiveIndex = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"TIMG\":\n        this.IFF.currentSurface.attributes.refractionMap = this.IFF.reader.getVariableLengthIndex();\n        break;\n      case \"IMAP\":\n        this.IFF.currentSurface.attributes.imageMapIndex = this.IFF.reader.getUint32();\n        break;\n      case \"IUVI\":\n        this.IFF.currentNode.UVChannel = this.IFF.reader.getString(length);\n        break;\n      case \"IUTL\":\n        this.IFF.currentNode.widthWrappingMode = this.IFF.reader.getUint32();\n        break;\n      case \"IVTL\":\n        this.IFF.currentNode.heightWrappingMode = this.IFF.reader.getUint32();\n        break;\n      default:\n        this.IFF.parseUnknownCHUNK(blockID, length);\n    }\n    if (blockID != \"FORM\") {\n      this.IFF.debugger.node = 1;\n      this.IFF.debugger.nodeID = blockID;\n      this.IFF.debugger.log();\n    }\n    if (this.IFF.reader.offset >= this.IFF.currentFormEnd) {\n      this.IFF.currentForm = this.IFF.parentForm;\n    }\n  }\n}\nexport { LWO3Parser };", "map": {"version": 3, "names": ["LWO3Parser", "constructor", "IFFParser", "IFF", "parseBlock", "debugger", "offset", "reader", "closeForms", "blockID", "getIDTag", "length", "getUint32", "dataOffset", "parseForm", "skipped", "skip", "currentNode", "getInt32", "parseObjectTag", "<PERSON>se<PERSON><PERSON><PERSON>", "parsePoints", "parseVertexMapping", "parsePolygonList", "parseTagStrings", "parsePolygonTagMapping", "currentForm", "description", "getString", "comment", "channelName", "wrap", "w", "getUint16", "h", "index", "getVariableLengthIndex", "imageIndex", "referenceObject", "referenceObjectID", "currentSurface", "surfaceShaderName", "surfaceCustomAOVName", "disabled", "realName", "refName", "nodes", "nodeName", "push", "inputNodeName", "inputName", "inputOutputName", "fileName", "textureChannel", "maxSmoothingAngle", "getFloat32", "attributes", "smooth", "Color", "value", "getFloat32Array", "Luminosity", "Specular", "Diffuse", "Reflection", "Glossiness", "opacity", "bumpStrength", "side", "reflectionMap", "refractiveIndex", "refractionMap", "imageMapIndex", "UVChannel", "widthWrappingMode", "heightWrappingMode", "parseUnknownCHUNK", "node", "nodeID", "log", "currentFormEnd", "parentForm"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/lwo/LWO3Parser.js"], "sourcesContent": ["class LWO3Parser {\n  constructor(IFFParser) {\n    this.IFF = IFFParser\n  }\n\n  parseBlock() {\n    this.IFF.debugger.offset = this.IFF.reader.offset\n    this.IFF.debugger.closeForms()\n\n    const blockID = this.IFF.reader.getIDTag()\n    const length = this.IFF.reader.getUint32() // size of data in bytes\n\n    this.IFF.debugger.dataOffset = this.IFF.reader.offset\n    this.IFF.debugger.length = length\n\n    // Data types may be found in either LWO2 OR LWO3 spec\n    switch (blockID) {\n      case 'FORM': // form blocks may consist of sub -chunks or sub-forms\n        this.IFF.parseForm(length)\n        break\n\n      // SKIPPED CHUNKS\n      // MISC skipped\n      case 'ICON': // Thumbnail Icon Image\n      case 'VMPA': // Vertex Map Parameter\n      case 'BBOX': // bounding box\n      // case 'VMMD':\n      // case 'VTYP':\n\n      // normal maps can be specified, normally on models imported from other applications. Currently ignored\n      case 'NORM':\n\n      // ENVL FORM skipped\n      case 'PRE ': // Pre-loop behavior for the keyframe\n      case 'POST': // Post-loop behavior for the keyframe\n      case 'KEY ':\n      case 'SPAN':\n\n      // CLIP FORM skipped\n      case 'TIME':\n      case 'CLRS':\n      case 'CLRA':\n      case 'FILT':\n      case 'DITH':\n      case 'CONT':\n      case 'BRIT':\n      case 'SATR':\n      case 'HUE ':\n      case 'GAMM':\n      case 'NEGA':\n      case 'IFLT':\n      case 'PFLT':\n\n      // Image Map Layer skipped\n      case 'PROJ':\n      case 'AXIS':\n      case 'AAST':\n      case 'PIXB':\n      case 'STCK':\n\n      // Procedural Textures skipped\n      case 'VALU':\n\n      // Gradient Textures skipped\n      case 'PNAM':\n      case 'INAM':\n      case 'GRST':\n      case 'GREN':\n      case 'GRPT':\n      case 'FKEY':\n      case 'IKEY':\n\n      // Texture Mapping Form skipped\n      case 'CSYS':\n\n      // Surface CHUNKs skipped\n      case 'OPAQ': // top level 'opacity' checkbox\n      case 'CMAP': // clip map\n\n      // Surface node CHUNKS skipped\n      // These mainly specify the node editor setup in LW\n      case 'NLOC':\n      case 'NZOM':\n      case 'NVER':\n      case 'NSRV':\n      case 'NCRD':\n      case 'NMOD':\n      case 'NSEL':\n      case 'NPRW':\n      case 'NPLA':\n      case 'VERS':\n      case 'ENUM':\n      case 'TAG ':\n\n      // Car Material CHUNKS\n      case 'CGMD':\n      case 'CGTY':\n      case 'CGST':\n      case 'CGEN':\n      case 'CGTS':\n      case 'CGTE':\n      case 'OSMP':\n      case 'OMDE':\n      case 'OUTR':\n      case 'FLAG':\n\n      case 'TRNL':\n      case 'SHRP':\n      case 'RFOP':\n      case 'RSAN':\n      case 'TROP':\n      case 'RBLR':\n      case 'TBLR':\n      case 'CLRH':\n      case 'CLRF':\n      case 'ADTR':\n      case 'GLOW':\n      case 'LINE':\n      case 'ALPH':\n      case 'VCOL':\n      case 'ENAB':\n        this.IFF.debugger.skipped = true\n        this.IFF.reader.skip(length)\n        break\n\n      // Texture node chunks (not in spec)\n      case 'IPIX': // usePixelBlending\n      case 'IMIP': // useMipMaps\n      case 'IMOD': // imageBlendingMode\n      case 'AMOD': // unknown\n      case 'IINV': // imageInvertAlpha\n      case 'INCR': // imageInvertColor\n      case 'IAXS': // imageAxis ( for non-UV maps)\n      case 'IFOT': // imageFallofType\n      case 'ITIM': // timing for animated textures\n      case 'IWRL':\n      case 'IUTI':\n      case 'IINX':\n      case 'IINY':\n      case 'IINZ':\n      case 'IREF': // possibly a VX for reused texture nodes\n        if (length === 4) this.IFF.currentNode[blockID] = this.IFF.reader.getInt32()\n        else this.IFF.reader.skip(length)\n        break\n\n      case 'OTAG':\n        this.IFF.parseObjectTag()\n        break\n\n      case 'LAYR':\n        this.IFF.parseLayer(length)\n        break\n\n      case 'PNTS':\n        this.IFF.parsePoints(length)\n        break\n\n      case 'VMAP':\n        this.IFF.parseVertexMapping(length)\n        break\n\n      case 'POLS':\n        this.IFF.parsePolygonList(length)\n        break\n\n      case 'TAGS':\n        this.IFF.parseTagStrings(length)\n        break\n\n      case 'PTAG':\n        this.IFF.parsePolygonTagMapping(length)\n        break\n\n      case 'VMAD':\n        this.IFF.parseVertexMapping(length, true)\n        break\n\n      // Misc CHUNKS\n      case 'DESC': // Description Line\n        this.IFF.currentForm.description = this.IFF.reader.getString()\n        break\n\n      case 'TEXT':\n      case 'CMNT':\n      case 'NCOM':\n        this.IFF.currentForm.comment = this.IFF.reader.getString()\n        break\n\n      // Envelope Form\n      case 'NAME':\n        this.IFF.currentForm.channelName = this.IFF.reader.getString()\n        break\n\n      // Image Map Layer\n      case 'WRAP':\n        this.IFF.currentForm.wrap = { w: this.IFF.reader.getUint16(), h: this.IFF.reader.getUint16() }\n        break\n\n      case 'IMAG':\n        const index = this.IFF.reader.getVariableLengthIndex()\n        this.IFF.currentForm.imageIndex = index\n        break\n\n      // Texture Mapping Form\n      case 'OREF':\n        this.IFF.currentForm.referenceObject = this.IFF.reader.getString()\n        break\n\n      case 'ROID':\n        this.IFF.currentForm.referenceObjectID = this.IFF.reader.getUint32()\n        break\n\n      // Surface Blocks\n      case 'SSHN':\n        this.IFF.currentSurface.surfaceShaderName = this.IFF.reader.getString()\n        break\n\n      case 'AOVN':\n        this.IFF.currentSurface.surfaceCustomAOVName = this.IFF.reader.getString()\n        break\n\n      // Nodal Blocks\n      case 'NSTA':\n        this.IFF.currentForm.disabled = this.IFF.reader.getUint16()\n        break\n\n      case 'NRNM':\n        this.IFF.currentForm.realName = this.IFF.reader.getString()\n        break\n\n      case 'NNME':\n        this.IFF.currentForm.refName = this.IFF.reader.getString()\n        this.IFF.currentSurface.nodes[this.IFF.currentForm.refName] = this.IFF.currentForm\n        break\n\n      // Nodal Blocks : connections\n      case 'INME':\n        if (!this.IFF.currentForm.nodeName) this.IFF.currentForm.nodeName = []\n        this.IFF.currentForm.nodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINN':\n        if (!this.IFF.currentForm.inputNodeName) this.IFF.currentForm.inputNodeName = []\n        this.IFF.currentForm.inputNodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINM':\n        if (!this.IFF.currentForm.inputName) this.IFF.currentForm.inputName = []\n        this.IFF.currentForm.inputName.push(this.IFF.reader.getString())\n        break\n\n      case 'IONM':\n        if (!this.IFF.currentForm.inputOutputName) this.IFF.currentForm.inputOutputName = []\n        this.IFF.currentForm.inputOutputName.push(this.IFF.reader.getString())\n        break\n\n      case 'FNAM':\n        this.IFF.currentForm.fileName = this.IFF.reader.getString()\n        break\n\n      case 'CHAN': // NOTE: ENVL Forms may also have CHAN chunk, however ENVL is currently ignored\n        if (length === 4) this.IFF.currentForm.textureChannel = this.IFF.reader.getIDTag()\n        else this.IFF.reader.skip(length)\n        break\n\n      // LWO2 Spec chunks: these are needed since the SURF FORMs are often in LWO2 format\n      case 'SMAN':\n        const maxSmoothingAngle = this.IFF.reader.getFloat32()\n        this.IFF.currentSurface.attributes.smooth = maxSmoothingAngle < 0 ? false : true\n        break\n\n      // LWO2: Basic Surface Parameters\n      case 'COLR':\n        this.IFF.currentSurface.attributes.Color = { value: this.IFF.reader.getFloat32Array(3) }\n        this.IFF.reader.skip(2) // VX: envelope\n        break\n\n      case 'LUMI':\n        this.IFF.currentSurface.attributes.Luminosity = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SPEC':\n        this.IFF.currentSurface.attributes.Specular = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'DIFF':\n        this.IFF.currentSurface.attributes.Diffuse = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'REFL':\n        this.IFF.currentSurface.attributes.Reflection = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'GLOS':\n        this.IFF.currentSurface.attributes.Glossiness = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TRAN':\n        this.IFF.currentSurface.attributes.opacity = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'BUMP':\n        this.IFF.currentSurface.attributes.bumpStrength = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SIDE':\n        this.IFF.currentSurface.attributes.side = this.IFF.reader.getUint16()\n        break\n\n      case 'RIMG':\n        this.IFF.currentSurface.attributes.reflectionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'RIND':\n        this.IFF.currentSurface.attributes.refractiveIndex = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TIMG':\n        this.IFF.currentSurface.attributes.refractionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'IMAP':\n        this.IFF.currentSurface.attributes.imageMapIndex = this.IFF.reader.getUint32()\n        break\n\n      case 'IUVI': // uv channel name\n        this.IFF.currentNode.UVChannel = this.IFF.reader.getString(length)\n        break\n\n      case 'IUTL': // widthWrappingMode: 0 = Reset, 1 = Repeat, 2 = Mirror, 3 = Edge\n        this.IFF.currentNode.widthWrappingMode = this.IFF.reader.getUint32()\n        break\n      case 'IVTL': // heightWrappingMode\n        this.IFF.currentNode.heightWrappingMode = this.IFF.reader.getUint32()\n        break\n\n      default:\n        this.IFF.parseUnknownCHUNK(blockID, length)\n    }\n\n    if (blockID != 'FORM') {\n      this.IFF.debugger.node = 1\n      this.IFF.debugger.nodeID = blockID\n      this.IFF.debugger.log()\n    }\n\n    if (this.IFF.reader.offset >= this.IFF.currentFormEnd) {\n      this.IFF.currentForm = this.IFF.parentForm\n    }\n  }\n}\n\nexport { LWO3Parser }\n"], "mappings": "AAAA,MAAMA,UAAA,CAAW;EACfC,YAAYC,SAAA,EAAW;IACrB,KAAKC,GAAA,GAAMD,SAAA;EACZ;EAEDE,WAAA,EAAa;IACX,KAAKD,GAAA,CAAIE,QAAA,CAASC,MAAA,GAAS,KAAKH,GAAA,CAAII,MAAA,CAAOD,MAAA;IAC3C,KAAKH,GAAA,CAAIE,QAAA,CAASG,UAAA,CAAY;IAE9B,MAAMC,OAAA,GAAU,KAAKN,GAAA,CAAII,MAAA,CAAOG,QAAA,CAAU;IAC1C,MAAMC,MAAA,GAAS,KAAKR,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;IAE1C,KAAKT,GAAA,CAAIE,QAAA,CAASQ,UAAA,GAAa,KAAKV,GAAA,CAAII,MAAA,CAAOD,MAAA;IAC/C,KAAKH,GAAA,CAAIE,QAAA,CAASM,MAAA,GAASA,MAAA;IAG3B,QAAQF,OAAA;MACN,KAAK;QACH,KAAKN,GAAA,CAAIW,SAAA,CAAUH,MAAM;QACzB;MAIF,KAAK;MACL,KAAK;MACL,KAAK;MAKL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MAGL,KAAK;MACL,KAAK;MAIL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAEL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKR,GAAA,CAAIE,QAAA,CAASU,OAAA,GAAU;QAC5B,KAAKZ,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAKL,MAAM;QAC3B;MAGF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,IAAIA,MAAA,KAAW,GAAG,KAAKR,GAAA,CAAIc,WAAA,CAAYR,OAAO,IAAI,KAAKN,GAAA,CAAII,MAAA,CAAOW,QAAA,CAAU,OACvE,KAAKf,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAKL,MAAM;QAChC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIgB,cAAA,CAAgB;QACzB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIiB,UAAA,CAAWT,MAAM;QAC1B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIkB,WAAA,CAAYV,MAAM;QAC3B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAImB,kBAAA,CAAmBX,MAAM;QAClC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIoB,gBAAA,CAAiBZ,MAAM;QAChC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIqB,eAAA,CAAgBb,MAAM;QAC/B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIsB,sBAAA,CAAuBd,MAAM;QACtC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAImB,kBAAA,CAAmBX,MAAA,EAAQ,IAAI;QACxC;MAGF,KAAK;QACH,KAAKR,GAAA,CAAIuB,WAAA,CAAYC,WAAA,GAAc,KAAKxB,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC9D;MAEF,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYG,OAAA,GAAU,KAAK1B,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC1D;MAGF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYI,WAAA,GAAc,KAAK3B,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC9D;MAGF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYK,IAAA,GAAO;UAAEC,CAAA,EAAG,KAAK7B,GAAA,CAAII,MAAA,CAAO0B,SAAA,CAAS;UAAIC,CAAA,EAAG,KAAK/B,GAAA,CAAII,MAAA,CAAO0B,SAAA;QAAa;QAC9F;MAEF,KAAK;QACH,MAAME,KAAA,GAAQ,KAAKhC,GAAA,CAAII,MAAA,CAAO6B,sBAAA,CAAwB;QACtD,KAAKjC,GAAA,CAAIuB,WAAA,CAAYW,UAAA,GAAaF,KAAA;QAClC;MAGF,KAAK;QACH,KAAKhC,GAAA,CAAIuB,WAAA,CAAYY,eAAA,GAAkB,KAAKnC,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAClE;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYa,iBAAA,GAAoB,KAAKpC,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACpE;MAGF,KAAK;QACH,KAAKT,GAAA,CAAIqC,cAAA,CAAeC,iBAAA,GAAoB,KAAKtC,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QACvE;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAIqC,cAAA,CAAeE,oBAAA,GAAuB,KAAKvC,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC1E;MAGF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYiB,QAAA,GAAW,KAAKxC,GAAA,CAAII,MAAA,CAAO0B,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,KAAK9B,GAAA,CAAIuB,WAAA,CAAYkB,QAAA,GAAW,KAAKzC,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAYmB,OAAA,GAAU,KAAK1C,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC1D,KAAKzB,GAAA,CAAIqC,cAAA,CAAeM,KAAA,CAAM,KAAK3C,GAAA,CAAIuB,WAAA,CAAYmB,OAAO,IAAI,KAAK1C,GAAA,CAAIuB,WAAA;QACvE;MAGF,KAAK;QACH,IAAI,CAAC,KAAKvB,GAAA,CAAIuB,WAAA,CAAYqB,QAAA,EAAU,KAAK5C,GAAA,CAAIuB,WAAA,CAAYqB,QAAA,GAAW,EAAE;QACtE,KAAK5C,GAAA,CAAIuB,WAAA,CAAYqB,QAAA,CAASC,IAAA,CAAK,KAAK7C,GAAA,CAAII,MAAA,CAAOqB,SAAA,EAAW;QAC9D;MAEF,KAAK;QACH,IAAI,CAAC,KAAKzB,GAAA,CAAIuB,WAAA,CAAYuB,aAAA,EAAe,KAAK9C,GAAA,CAAIuB,WAAA,CAAYuB,aAAA,GAAgB,EAAE;QAChF,KAAK9C,GAAA,CAAIuB,WAAA,CAAYuB,aAAA,CAAcD,IAAA,CAAK,KAAK7C,GAAA,CAAII,MAAA,CAAOqB,SAAA,EAAW;QACnE;MAEF,KAAK;QACH,IAAI,CAAC,KAAKzB,GAAA,CAAIuB,WAAA,CAAYwB,SAAA,EAAW,KAAK/C,GAAA,CAAIuB,WAAA,CAAYwB,SAAA,GAAY,EAAE;QACxE,KAAK/C,GAAA,CAAIuB,WAAA,CAAYwB,SAAA,CAAUF,IAAA,CAAK,KAAK7C,GAAA,CAAII,MAAA,CAAOqB,SAAA,EAAW;QAC/D;MAEF,KAAK;QACH,IAAI,CAAC,KAAKzB,GAAA,CAAIuB,WAAA,CAAYyB,eAAA,EAAiB,KAAKhD,GAAA,CAAIuB,WAAA,CAAYyB,eAAA,GAAkB,EAAE;QACpF,KAAKhD,GAAA,CAAIuB,WAAA,CAAYyB,eAAA,CAAgBH,IAAA,CAAK,KAAK7C,GAAA,CAAII,MAAA,CAAOqB,SAAA,EAAW;QACrE;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAIuB,WAAA,CAAY0B,QAAA,GAAW,KAAKjD,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,IAAIjB,MAAA,KAAW,GAAG,KAAKR,GAAA,CAAIuB,WAAA,CAAY2B,cAAA,GAAiB,KAAKlD,GAAA,CAAII,MAAA,CAAOG,QAAA,CAAU,OAC7E,KAAKP,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAKL,MAAM;QAChC;MAGF,KAAK;QACH,MAAM2C,iBAAA,GAAoB,KAAKnD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAY;QACtD,KAAKpD,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWC,MAAA,GAASH,iBAAA,GAAoB,IAAI,QAAQ;QAC5E;MAGF,KAAK;QACH,KAAKnD,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWE,KAAA,GAAQ;UAAEC,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOqD,eAAA,CAAgB,CAAC;QAAG;QACxF,KAAKzD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWK,UAAA,GAAa;UAAEF,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAU;QAAI;QACvF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWM,QAAA,GAAW;UAAEH,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAU;QAAI;QACrF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWO,OAAA,GAAU;UAAEJ,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAU;QAAI;QACpF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWQ,UAAA,GAAa;UAAEL,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAU;QAAI;QACvF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWS,UAAA,GAAa;UAAEN,KAAA,EAAO,KAAKxD,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAU;QAAI;QACvF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWU,OAAA,GAAU,KAAK/D,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAY;QACzE,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWW,YAAA,GAAe,KAAKhE,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAY;QAC9E,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWY,IAAA,GAAO,KAAKjE,GAAA,CAAII,MAAA,CAAO0B,SAAA,CAAW;QACrE;MAEF,KAAK;QACH,KAAK9B,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWa,aAAA,GAAgB,KAAKlE,GAAA,CAAII,MAAA,CAAO6B,sBAAA,CAAwB;QAC3F;MAEF,KAAK;QACH,KAAKjC,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWc,eAAA,GAAkB,KAAKnE,GAAA,CAAII,MAAA,CAAOgD,UAAA,CAAY;QACjF,KAAKpD,GAAA,CAAII,MAAA,CAAOS,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKb,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWe,aAAA,GAAgB,KAAKpE,GAAA,CAAII,MAAA,CAAO6B,sBAAA,CAAwB;QAC3F;MAEF,KAAK;QACH,KAAKjC,GAAA,CAAIqC,cAAA,CAAegB,UAAA,CAAWgB,aAAA,GAAgB,KAAKrE,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QAC9E;MAEF,KAAK;QACH,KAAKT,GAAA,CAAIc,WAAA,CAAYwD,SAAA,GAAY,KAAKtE,GAAA,CAAII,MAAA,CAAOqB,SAAA,CAAUjB,MAAM;QACjE;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIc,WAAA,CAAYyD,iBAAA,GAAoB,KAAKvE,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACpE;MACF,KAAK;QACH,KAAKT,GAAA,CAAIc,WAAA,CAAY0D,kBAAA,GAAqB,KAAKxE,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACrE;MAEF;QACE,KAAKT,GAAA,CAAIyE,iBAAA,CAAkBnE,OAAA,EAASE,MAAM;IAC7C;IAED,IAAIF,OAAA,IAAW,QAAQ;MACrB,KAAKN,GAAA,CAAIE,QAAA,CAASwE,IAAA,GAAO;MACzB,KAAK1E,GAAA,CAAIE,QAAA,CAASyE,MAAA,GAASrE,OAAA;MAC3B,KAAKN,GAAA,CAAIE,QAAA,CAAS0E,GAAA,CAAK;IACxB;IAED,IAAI,KAAK5E,GAAA,CAAII,MAAA,CAAOD,MAAA,IAAU,KAAKH,GAAA,CAAI6E,cAAA,EAAgB;MACrD,KAAK7E,GAAA,CAAIuB,WAAA,GAAc,KAAKvB,GAAA,CAAI8E,UAAA;IACjC;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}