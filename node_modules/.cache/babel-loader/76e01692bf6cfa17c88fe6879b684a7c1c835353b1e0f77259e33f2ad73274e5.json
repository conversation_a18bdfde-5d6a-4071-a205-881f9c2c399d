{"ast": null, "code": "import * as React from 'react';\nimport { DefaultLoadingManager } from 'three';\nimport { create } from 'zustand';\nlet saveLastTotalLoaded = 0;\nconst useProgress = /* @__PURE__ */create(set => {\n  DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n  DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n  DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n  DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\n\n//\n\nfunction Progress({\n  children\n}) {\n  const result = useProgress();\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(result));\n}\nexport { Progress, useProgress };", "map": {"version": 3, "names": ["React", "DefaultLoadingManager", "create", "saveLastTotalLoaded", "useProgress", "set", "onStart", "item", "loaded", "total", "active", "progress", "onLoad", "onError", "state", "errors", "onProgress", "Progress", "children", "result", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Progress.js"], "sourcesContent": ["import * as React from 'react';\nimport { DefaultLoadingManager } from 'three';\nimport { create } from 'zustand';\n\nlet saveLastTotalLoaded = 0;\nconst useProgress = /* @__PURE__ */create(set => {\n  DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n  DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n  DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n  DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\n\n//\n\nfunction Progress({\n  children\n}) {\n  const result = useProgress();\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(result));\n}\n\nexport { Progress, useProgress };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,OAAO;AAC7C,SAASC,MAAM,QAAQ,SAAS;AAEhC,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,MAAMC,WAAW,GAAG,eAAeF,MAAM,CAACG,GAAG,IAAI;EAC/CJ,qBAAqB,CAACK,OAAO,GAAG,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACvDJ,GAAG,CAAC;MACFK,MAAM,EAAE,IAAI;MACZH,IAAI;MACJC,MAAM;MACNC,KAAK;MACLE,QAAQ,EAAE,CAACH,MAAM,GAAGL,mBAAmB,KAAKM,KAAK,GAAGN,mBAAmB,CAAC,GAAG;IAC7E,CAAC,CAAC;EACJ,CAAC;EACDF,qBAAqB,CAACW,MAAM,GAAG,MAAM;IACnCP,GAAG,CAAC;MACFK,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACDT,qBAAqB,CAACY,OAAO,GAAGN,IAAI,IAAIF,GAAG,CAACS,KAAK,KAAK;IACpDC,MAAM,EAAE,CAAC,GAAGD,KAAK,CAACC,MAAM,EAAER,IAAI;EAChC,CAAC,CAAC,CAAC;EACHN,qBAAqB,CAACe,UAAU,GAAG,CAACT,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK;IAC1D,IAAID,MAAM,KAAKC,KAAK,EAAE;MACpBN,mBAAmB,GAAGM,KAAK;IAC7B;IACAJ,GAAG,CAAC;MACFK,MAAM,EAAE,IAAI;MACZH,IAAI;MACJC,MAAM;MACNC,KAAK;MACLE,QAAQ,EAAE,CAACH,MAAM,GAAGL,mBAAmB,KAAKM,KAAK,GAAGN,mBAAmB,CAAC,GAAG,GAAG,IAAI;IACpF,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLY,MAAM,EAAE,EAAE;IACVL,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,CAAC;IACXJ,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC;;AAEF;;AAEA,SAASQ,QAAQA,CAAC;EAChBC;AACF,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGf,WAAW,CAAC,CAAC;EAC5B,OAAO,aAAaJ,KAAK,CAACoB,aAAa,CAACpB,KAAK,CAACqB,QAAQ,EAAE,IAAI,EAAEH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,MAAM,CAAC,CAAC;AAC7G;AAEA,SAASF,QAAQ,EAAEb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}