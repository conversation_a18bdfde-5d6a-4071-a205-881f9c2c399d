{"ast": null, "code": "/*!\n * camera-controls\n * https://github.com/yomotsu/camera-controls\n * (c) 2017 @yomotsu\n * Released under the MIT License.\n */\n// see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#value\nconst MOUSE_BUTTON = {\n  LEFT: 1,\n  RIGHT: 2,\n  MIDDLE: 4\n};\nconst ACTION = Object.freeze({\n  NONE: 0b0,\n  ROTATE: 0b1,\n  TRUCK: 0b10,\n  SCREEN_PAN: 0b100,\n  OFFSET: 0b1000,\n  DOLLY: 0b10000,\n  ZOOM: 0b100000,\n  TOUCH_ROTATE: 0b1000000,\n  TOUCH_TRUCK: 0b10000000,\n  TOUCH_SCREEN_PAN: 0b100000000,\n  TOUCH_OFFSET: 0b1000000000,\n  TOUCH_DOLLY: 0b10000000000,\n  TOUCH_ZOOM: 0b100000000000,\n  TOUCH_DOLLY_TRUCK: 0b1000000000000,\n  TOUCH_DOLLY_SCREEN_PAN: 0b10000000000000,\n  TOUCH_DOLLY_OFFSET: 0b100000000000000,\n  TOUCH_DOLLY_ROTATE: 0b1000000000000000,\n  TOUCH_ZOOM_TRUCK: 0b10000000000000000,\n  TOUCH_ZOOM_OFFSET: 0b100000000000000000,\n  TOUCH_ZOOM_SCREEN_PAN: 0b1000000000000000000,\n  TOUCH_ZOOM_ROTATE: 0b10000000000000000000\n});\nconst DOLLY_DIRECTION = {\n  NONE: 0,\n  IN: 1,\n  OUT: -1\n};\nfunction isPerspectiveCamera(camera) {\n  return camera.isPerspectiveCamera;\n}\nfunction isOrthographicCamera(camera) {\n  return camera.isOrthographicCamera;\n}\nconst PI_2 = Math.PI * 2;\nconst PI_HALF = Math.PI / 2;\nconst EPSILON = 1e-5;\nconst DEG2RAD = Math.PI / 180;\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction approxZero(number, error = EPSILON) {\n  return Math.abs(number) < error;\n}\nfunction approxEquals(a, b, error = EPSILON) {\n  return approxZero(a - b, error);\n}\nfunction roundToStep(value, step) {\n  return Math.round(value / step) * step;\n}\nfunction infinityToMaxNumber(value) {\n  if (isFinite(value)) return value;\n  if (value < 0) return -Number.MAX_VALUE;\n  return Number.MAX_VALUE;\n}\nfunction maxNumberToInfinity(value) {\n  if (Math.abs(value) < Number.MAX_VALUE) return value;\n  return value * Infinity;\n}\n// https://docs.unity3d.com/ScriptReference/Mathf.SmoothDamp.html\n// https://github.com/Unity-Technologies/UnityCsReference/blob/a2bdfe9b3c4cd4476f44bf52f848063bfaf7b6b9/Runtime/Export/Math/Mathf.cs#L308\nfunction smoothDamp(current, target, currentVelocityRef, smoothTime, maxSpeed = Infinity, deltaTime) {\n  // Based on Game Programming Gems 4 Chapter 1.10\n  smoothTime = Math.max(0.0001, smoothTime);\n  const omega = 2 / smoothTime;\n  const x = omega * deltaTime;\n  const exp = 1 / (1 + x + 0.48 * x * x + 0.235 * x * x * x);\n  let change = current - target;\n  const originalTo = target;\n  // Clamp maximum speed\n  const maxChange = maxSpeed * smoothTime;\n  change = clamp(change, -maxChange, maxChange);\n  target = current - change;\n  const temp = (currentVelocityRef.value + omega * change) * deltaTime;\n  currentVelocityRef.value = (currentVelocityRef.value - omega * temp) * exp;\n  let output = target + (change + temp) * exp;\n  // Prevent overshooting\n  if (originalTo - current > 0.0 === output > originalTo) {\n    output = originalTo;\n    currentVelocityRef.value = (output - originalTo) / deltaTime;\n  }\n  return output;\n}\n// https://docs.unity3d.com/ScriptReference/Vector3.SmoothDamp.html\n// https://github.com/Unity-Technologies/UnityCsReference/blob/a2bdfe9b3c4cd4476f44bf52f848063bfaf7b6b9/Runtime/Export/Math/Vector3.cs#L97\nfunction smoothDampVec3(current, target, currentVelocityRef, smoothTime, maxSpeed = Infinity, deltaTime, out) {\n  // Based on Game Programming Gems 4 Chapter 1.10\n  smoothTime = Math.max(0.0001, smoothTime);\n  const omega = 2 / smoothTime;\n  const x = omega * deltaTime;\n  const exp = 1 / (1 + x + 0.48 * x * x + 0.235 * x * x * x);\n  let targetX = target.x;\n  let targetY = target.y;\n  let targetZ = target.z;\n  let changeX = current.x - targetX;\n  let changeY = current.y - targetY;\n  let changeZ = current.z - targetZ;\n  const originalToX = targetX;\n  const originalToY = targetY;\n  const originalToZ = targetZ;\n  // Clamp maximum speed\n  const maxChange = maxSpeed * smoothTime;\n  const maxChangeSq = maxChange * maxChange;\n  const magnitudeSq = changeX * changeX + changeY * changeY + changeZ * changeZ;\n  if (magnitudeSq > maxChangeSq) {\n    const magnitude = Math.sqrt(magnitudeSq);\n    changeX = changeX / magnitude * maxChange;\n    changeY = changeY / magnitude * maxChange;\n    changeZ = changeZ / magnitude * maxChange;\n  }\n  targetX = current.x - changeX;\n  targetY = current.y - changeY;\n  targetZ = current.z - changeZ;\n  const tempX = (currentVelocityRef.x + omega * changeX) * deltaTime;\n  const tempY = (currentVelocityRef.y + omega * changeY) * deltaTime;\n  const tempZ = (currentVelocityRef.z + omega * changeZ) * deltaTime;\n  currentVelocityRef.x = (currentVelocityRef.x - omega * tempX) * exp;\n  currentVelocityRef.y = (currentVelocityRef.y - omega * tempY) * exp;\n  currentVelocityRef.z = (currentVelocityRef.z - omega * tempZ) * exp;\n  out.x = targetX + (changeX + tempX) * exp;\n  out.y = targetY + (changeY + tempY) * exp;\n  out.z = targetZ + (changeZ + tempZ) * exp;\n  // Prevent overshooting\n  const origMinusCurrentX = originalToX - current.x;\n  const origMinusCurrentY = originalToY - current.y;\n  const origMinusCurrentZ = originalToZ - current.z;\n  const outMinusOrigX = out.x - originalToX;\n  const outMinusOrigY = out.y - originalToY;\n  const outMinusOrigZ = out.z - originalToZ;\n  if (origMinusCurrentX * outMinusOrigX + origMinusCurrentY * outMinusOrigY + origMinusCurrentZ * outMinusOrigZ > 0) {\n    out.x = originalToX;\n    out.y = originalToY;\n    out.z = originalToZ;\n    currentVelocityRef.x = (out.x - originalToX) / deltaTime;\n    currentVelocityRef.y = (out.y - originalToY) / deltaTime;\n    currentVelocityRef.z = (out.z - originalToZ) / deltaTime;\n  }\n  return out;\n}\nfunction extractClientCoordFromEvent(pointers, out) {\n  out.set(0, 0);\n  pointers.forEach(pointer => {\n    out.x += pointer.clientX;\n    out.y += pointer.clientY;\n  });\n  out.x /= pointers.length;\n  out.y /= pointers.length;\n}\nfunction notSupportedInOrthographicCamera(camera, message) {\n  if (isOrthographicCamera(camera)) {\n    console.warn(`${message} is not supported in OrthographicCamera`);\n    return true;\n  }\n  return false;\n}\nclass EventDispatcher {\n  constructor() {\n    this._listeners = {};\n  }\n  /**\n   * Adds the specified event listener.\n   * @param type event name\n   * @param listener handler function\n   * @category Methods\n   */\n  addEventListener(type, listener) {\n    const listeners = this._listeners;\n    if (listeners[type] === undefined) listeners[type] = [];\n    if (listeners[type].indexOf(listener) === -1) listeners[type].push(listener);\n  }\n  /**\n   * Presence of the specified event listener.\n   * @param type event name\n   * @param listener handler function\n   * @category Methods\n   */\n  hasEventListener(type, listener) {\n    const listeners = this._listeners;\n    return listeners[type] !== undefined && listeners[type].indexOf(listener) !== -1;\n  }\n  /**\n   * Removes the specified event listener\n   * @param type event name\n   * @param listener handler function\n   * @category Methods\n   */\n  removeEventListener(type, listener) {\n    const listeners = this._listeners;\n    const listenerArray = listeners[type];\n    if (listenerArray !== undefined) {\n      const index = listenerArray.indexOf(listener);\n      if (index !== -1) listenerArray.splice(index, 1);\n    }\n  }\n  /**\n   * Removes all event listeners\n   * @param type event name\n   * @category Methods\n   */\n  removeAllEventListeners(type) {\n    if (!type) {\n      this._listeners = {};\n      return;\n    }\n    if (Array.isArray(this._listeners[type])) this._listeners[type].length = 0;\n  }\n  /**\n   * Fire an event type.\n   * @param event DispatcherEvent\n   * @category Methods\n   */\n  dispatchEvent(event) {\n    const listeners = this._listeners;\n    const listenerArray = listeners[event.type];\n    if (listenerArray !== undefined) {\n      event.target = this;\n      const array = listenerArray.slice(0);\n      for (let i = 0, l = array.length; i < l; i++) {\n        array[i].call(this, event);\n      }\n    }\n  }\n}\nvar _a;\nconst VERSION = '2.10.1'; // will be replaced with `version` in package.json during the build process.\nconst TOUCH_DOLLY_FACTOR = 1 / 8;\nconst isMac = /Mac/.test((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.platform);\nlet THREE;\nlet _ORIGIN;\nlet _AXIS_Y;\nlet _AXIS_Z;\nlet _v2;\nlet _v3A;\nlet _v3B;\nlet _v3C;\nlet _cameraDirection;\nlet _xColumn;\nlet _yColumn;\nlet _zColumn;\nlet _deltaTarget;\nlet _deltaOffset;\nlet _sphericalA;\nlet _sphericalB;\nlet _box3A;\nlet _box3B;\nlet _sphere;\nlet _quaternionA;\nlet _quaternionB;\nlet _rotationMatrix;\nlet _raycaster;\nclass CameraControls extends EventDispatcher {\n  /**\n   * Injects THREE as the dependency. You can then proceed to use CameraControls.\n   *\n   * e.g\n   * ```javascript\n   * CameraControls.install( { THREE: THREE } );\n   * ```\n   *\n   * Note: If you do not wish to use enter three.js to reduce file size(tree-shaking for example), make a subset to install.\n   *\n   * ```js\n   * import {\n   * \tVector2,\n   * \tVector3,\n   * \tVector4,\n   * \tQuaternion,\n   * \tMatrix4,\n   * \tSpherical,\n   * \tBox3,\n   * \tSphere,\n   * \tRaycaster,\n   * \tMathUtils,\n   * } from 'three';\n   *\n   * const subsetOfTHREE = {\n   * \tVector2   : Vector2,\n   * \tVector3   : Vector3,\n   * \tVector4   : Vector4,\n   * \tQuaternion: Quaternion,\n   * \tMatrix4   : Matrix4,\n   * \tSpherical : Spherical,\n   * \tBox3      : Box3,\n   * \tSphere    : Sphere,\n   * \tRaycaster : Raycaster,\n   * };\n    * CameraControls.install( { THREE: subsetOfTHREE } );\n   * ```\n   * @category Statics\n   */\n  static install(libs) {\n    THREE = libs.THREE;\n    _ORIGIN = Object.freeze(new THREE.Vector3(0, 0, 0));\n    _AXIS_Y = Object.freeze(new THREE.Vector3(0, 1, 0));\n    _AXIS_Z = Object.freeze(new THREE.Vector3(0, 0, 1));\n    _v2 = new THREE.Vector2();\n    _v3A = new THREE.Vector3();\n    _v3B = new THREE.Vector3();\n    _v3C = new THREE.Vector3();\n    _cameraDirection = new THREE.Vector3();\n    _xColumn = new THREE.Vector3();\n    _yColumn = new THREE.Vector3();\n    _zColumn = new THREE.Vector3();\n    _deltaTarget = new THREE.Vector3();\n    _deltaOffset = new THREE.Vector3();\n    _sphericalA = new THREE.Spherical();\n    _sphericalB = new THREE.Spherical();\n    _box3A = new THREE.Box3();\n    _box3B = new THREE.Box3();\n    _sphere = new THREE.Sphere();\n    _quaternionA = new THREE.Quaternion();\n    _quaternionB = new THREE.Quaternion();\n    _rotationMatrix = new THREE.Matrix4();\n    _raycaster = new THREE.Raycaster();\n  }\n  /**\n   * list all ACTIONs\n   * @category Statics\n   */\n  static get ACTION() {\n    return ACTION;\n  }\n  /**\n   * @deprecated Use `cameraControls.mouseButtons.left = CameraControls.ACTION.SCREEN_PAN` instead.\n   */\n  set verticalDragToForward(_) {\n    console.warn('camera-controls: `verticalDragToForward` was removed. Use `mouseButtons.left = CameraControls.ACTION.SCREEN_PAN` instead.');\n  }\n  /**\n   * Creates a `CameraControls` instance.\n   *\n   * Note:\n   * You **must install** three.js before using camera-controls. see [#install](#install)\n   * Not doing so will lead to runtime errors (`undefined` references to THREE).\n   *\n   * e.g.\n   * ```\n   * CameraControls.install( { THREE } );\n   * const cameraControls = new CameraControls( camera, domElement );\n   * ```\n   *\n   * @param camera A `THREE.PerspectiveCamera` or `THREE.OrthographicCamera` to be controlled.\n   * @param domElement A `HTMLElement` for the draggable area, usually `renderer.domElement`.\n   * @category Constructor\n   */\n  constructor(camera, domElement) {\n    super();\n    /**\n     * Minimum vertical angle in radians.\n     * The angle has to be between `0` and `.maxPolarAngle` inclusive.\n     * The default value is `0`.\n     *\n     * e.g.\n     * ```\n     * cameraControls.maxPolarAngle = 0;\n     * ```\n     * @category Properties\n     */\n    this.minPolarAngle = 0; // radians\n    /**\n     * Maximum vertical angle in radians.\n     * The angle has to be between `.maxPolarAngle` and `Math.PI` inclusive.\n     * The default value is `Math.PI`.\n     *\n     * e.g.\n     * ```\n     * cameraControls.maxPolarAngle = Math.PI;\n     * ```\n     * @category Properties\n     */\n    this.maxPolarAngle = Math.PI; // radians\n    /**\n     * Minimum horizontal angle in radians.\n     * The angle has to be less than `.maxAzimuthAngle`.\n     * The default value is `- Infinity`.\n     *\n     * e.g.\n     * ```\n     * cameraControls.minAzimuthAngle = - Infinity;\n     * ```\n     * @category Properties\n     */\n    this.minAzimuthAngle = -Infinity; // radians\n    /**\n     * Maximum horizontal angle in radians.\n     * The angle has to be greater than `.minAzimuthAngle`.\n     * The default value is `Infinity`.\n     *\n     * e.g.\n     * ```\n     * cameraControls.maxAzimuthAngle = Infinity;\n     * ```\n     * @category Properties\n     */\n    this.maxAzimuthAngle = Infinity; // radians\n    // How far you can dolly in and out ( PerspectiveCamera only )\n    /**\n     * Minimum distance for dolly. The value must be higher than `0`. Default is `Number.EPSILON`.\n     * PerspectiveCamera only.\n     * @category Properties\n     */\n    this.minDistance = Number.EPSILON;\n    /**\n     * Maximum distance for dolly. The value must be higher than `minDistance`. Default is `Infinity`.\n     * PerspectiveCamera only.\n     * @category Properties\n     */\n    this.maxDistance = Infinity;\n    /**\n     * `true` to enable Infinity Dolly for wheel and pinch. Use this with `minDistance` and `maxDistance`\n     * If the Dolly distance is less (or over) than the `minDistance` (or `maxDistance`), `infinityDolly` will keep the distance and pushes the target position instead.\n     * @category Properties\n     */\n    this.infinityDolly = false;\n    /**\n     * Minimum camera zoom.\n     * @category Properties\n     */\n    this.minZoom = 0.01;\n    /**\n     * Maximum camera zoom.\n     * @category Properties\n     */\n    this.maxZoom = Infinity;\n    /**\n     * Approximate time in seconds to reach the target. A smaller value will reach the target faster.\n     * @category Properties\n     */\n    this.smoothTime = 0.25;\n    /**\n     * the smoothTime while dragging\n     * @category Properties\n     */\n    this.draggingSmoothTime = 0.125;\n    /**\n     * Max transition speed in unit-per-seconds\n     * @category Properties\n     */\n    this.maxSpeed = Infinity;\n    /**\n     * Speed of azimuth (horizontal) rotation.\n     * @category Properties\n     */\n    this.azimuthRotateSpeed = 1.0;\n    /**\n     * Speed of polar (vertical) rotation.\n     * @category Properties\n     */\n    this.polarRotateSpeed = 1.0;\n    /**\n     * Speed of mouse-wheel dollying.\n     * @category Properties\n     */\n    this.dollySpeed = 1.0;\n    /**\n     * `true` to invert direction when dollying or zooming via drag\n     * @category Properties\n     */\n    this.dollyDragInverted = false;\n    /**\n     * Speed of drag for truck and pedestal.\n     * @category Properties\n     */\n    this.truckSpeed = 2.0;\n    /**\n     * `true` to enable Dolly-in to the mouse cursor coords.\n     * @category Properties\n     */\n    this.dollyToCursor = false;\n    /**\n     * @category Properties\n     */\n    this.dragToOffset = false;\n    /**\n     * Friction ratio of the boundary.\n     * @category Properties\n     */\n    this.boundaryFriction = 0.0;\n    /**\n     * Controls how soon the `rest` event fires as the camera slows.\n     * @category Properties\n     */\n    this.restThreshold = 0.01;\n    /**\n     * An array of Meshes to collide with camera.\n     * Be aware colliderMeshes may decrease performance. The collision test uses 4 raycasters from the camera since the near plane has 4 corners.\n     * @category Properties\n     */\n    this.colliderMeshes = [];\n    /**\n     * Force cancel user dragging.\n     * @category Methods\n     */\n    // cancel will be overwritten in the constructor.\n    this.cancel = () => {};\n    this._enabled = true;\n    this._state = ACTION.NONE;\n    this._viewport = null;\n    this._changedDolly = 0;\n    this._changedZoom = 0;\n    this._hasRested = true;\n    this._boundaryEnclosesCamera = false;\n    this._needsUpdate = true;\n    this._updatedLastTime = false;\n    this._elementRect = new DOMRect();\n    this._isDragging = false;\n    this._dragNeedsUpdate = true;\n    this._activePointers = [];\n    this._lockedPointer = null;\n    this._interactiveArea = new DOMRect(0, 0, 1, 1);\n    // Use draggingSmoothTime over smoothTime while true.\n    // set automatically true on user-dragging start.\n    // set automatically false on programmable methods call.\n    this._isUserControllingRotate = false;\n    this._isUserControllingDolly = false;\n    this._isUserControllingTruck = false;\n    this._isUserControllingOffset = false;\n    this._isUserControllingZoom = false;\n    this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n    // velocities for smoothDamp\n    this._thetaVelocity = {\n      value: 0\n    };\n    this._phiVelocity = {\n      value: 0\n    };\n    this._radiusVelocity = {\n      value: 0\n    };\n    this._targetVelocity = new THREE.Vector3();\n    this._focalOffsetVelocity = new THREE.Vector3();\n    this._zoomVelocity = {\n      value: 0\n    };\n    this._truckInternal = (deltaX, deltaY, dragToOffset, screenSpacePanning) => {\n      let truckX;\n      let pedestalY;\n      if (isPerspectiveCamera(this._camera)) {\n        const offset = _v3A.copy(this._camera.position).sub(this._target);\n        // half of the fov is center to top of screen\n        const fov = this._camera.getEffectiveFOV() * DEG2RAD;\n        const targetDistance = offset.length() * Math.tan(fov * 0.5);\n        truckX = this.truckSpeed * deltaX * targetDistance / this._elementRect.height;\n        pedestalY = this.truckSpeed * deltaY * targetDistance / this._elementRect.height;\n      } else if (isOrthographicCamera(this._camera)) {\n        const camera = this._camera;\n        truckX = this.truckSpeed * deltaX * (camera.right - camera.left) / camera.zoom / this._elementRect.width;\n        pedestalY = this.truckSpeed * deltaY * (camera.top - camera.bottom) / camera.zoom / this._elementRect.height;\n      } else {\n        return;\n      }\n      if (screenSpacePanning) {\n        dragToOffset ? this.setFocalOffset(this._focalOffsetEnd.x + truckX, this._focalOffsetEnd.y, this._focalOffsetEnd.z, true) : this.truck(truckX, 0, true);\n        this.forward(-pedestalY, true);\n      } else {\n        dragToOffset ? this.setFocalOffset(this._focalOffsetEnd.x + truckX, this._focalOffsetEnd.y + pedestalY, this._focalOffsetEnd.z, true) : this.truck(truckX, pedestalY, true);\n      }\n    };\n    this._rotateInternal = (deltaX, deltaY) => {\n      const theta = PI_2 * this.azimuthRotateSpeed * deltaX / this._elementRect.height; // divide by *height* to refer the resolution\n      const phi = PI_2 * this.polarRotateSpeed * deltaY / this._elementRect.height;\n      this.rotate(theta, phi, true);\n    };\n    this._dollyInternal = (delta, x, y) => {\n      const dollyScale = Math.pow(0.95, -delta * this.dollySpeed);\n      const lastDistance = this._sphericalEnd.radius;\n      const distance = this._sphericalEnd.radius * dollyScale;\n      const clampedDistance = clamp(distance, this.minDistance, this.maxDistance);\n      const overflowedDistance = clampedDistance - distance;\n      if (this.infinityDolly && this.dollyToCursor) {\n        this._dollyToNoClamp(distance, true);\n      } else if (this.infinityDolly && !this.dollyToCursor) {\n        this.dollyInFixed(overflowedDistance, true);\n        this._dollyToNoClamp(clampedDistance, true);\n      } else {\n        this._dollyToNoClamp(clampedDistance, true);\n      }\n      if (this.dollyToCursor) {\n        this._changedDolly += (this.infinityDolly ? distance : clampedDistance) - lastDistance;\n        this._dollyControlCoord.set(x, y);\n      }\n      this._lastDollyDirection = Math.sign(-delta);\n    };\n    this._zoomInternal = (delta, x, y) => {\n      const zoomScale = Math.pow(0.95, delta * this.dollySpeed);\n      const lastZoom = this._zoom;\n      const zoom = this._zoom * zoomScale;\n      // for both PerspectiveCamera and OrthographicCamera\n      this.zoomTo(zoom, true);\n      if (this.dollyToCursor) {\n        this._changedZoom += zoom - lastZoom;\n        this._dollyControlCoord.set(x, y);\n      }\n    };\n    // Check if the user has installed THREE\n    if (typeof THREE === 'undefined') {\n      console.error('camera-controls: `THREE` is undefined. You must first run `CameraControls.install( { THREE: THREE } )`. Check the docs for further information.');\n    }\n    this._camera = camera;\n    this._yAxisUpSpace = new THREE.Quaternion().setFromUnitVectors(this._camera.up, _AXIS_Y);\n    this._yAxisUpSpaceInverse = this._yAxisUpSpace.clone().invert();\n    this._state = ACTION.NONE;\n    // the location\n    this._target = new THREE.Vector3();\n    this._targetEnd = this._target.clone();\n    this._focalOffset = new THREE.Vector3();\n    this._focalOffsetEnd = this._focalOffset.clone();\n    // rotation\n    this._spherical = new THREE.Spherical().setFromVector3(_v3A.copy(this._camera.position).applyQuaternion(this._yAxisUpSpace));\n    this._sphericalEnd = this._spherical.clone();\n    this._lastDistance = this._spherical.radius;\n    this._zoom = this._camera.zoom;\n    this._zoomEnd = this._zoom;\n    this._lastZoom = this._zoom;\n    // collisionTest uses nearPlane.s\n    this._nearPlaneCorners = [new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()];\n    this._updateNearPlaneCorners();\n    // Target cannot move outside of this box\n    this._boundary = new THREE.Box3(new THREE.Vector3(-Infinity, -Infinity, -Infinity), new THREE.Vector3(Infinity, Infinity, Infinity));\n    // reset\n    this._cameraUp0 = this._camera.up.clone();\n    this._target0 = this._target.clone();\n    this._position0 = this._camera.position.clone();\n    this._zoom0 = this._zoom;\n    this._focalOffset0 = this._focalOffset.clone();\n    this._dollyControlCoord = new THREE.Vector2();\n    // configs\n    this.mouseButtons = {\n      left: ACTION.ROTATE,\n      middle: ACTION.DOLLY,\n      right: ACTION.TRUCK,\n      wheel: isPerspectiveCamera(this._camera) ? ACTION.DOLLY : isOrthographicCamera(this._camera) ? ACTION.ZOOM : ACTION.NONE\n    };\n    this.touches = {\n      one: ACTION.TOUCH_ROTATE,\n      two: isPerspectiveCamera(this._camera) ? ACTION.TOUCH_DOLLY_TRUCK : isOrthographicCamera(this._camera) ? ACTION.TOUCH_ZOOM_TRUCK : ACTION.NONE,\n      three: ACTION.TOUCH_TRUCK\n    };\n    const dragStartPosition = new THREE.Vector2();\n    const lastDragPosition = new THREE.Vector2();\n    const dollyStart = new THREE.Vector2();\n    const onPointerDown = event => {\n      if (!this._enabled || !this._domElement) return;\n      if (this._interactiveArea.left !== 0 || this._interactiveArea.top !== 0 || this._interactiveArea.width !== 1 || this._interactiveArea.height !== 1) {\n        const elRect = this._domElement.getBoundingClientRect();\n        const left = event.clientX / elRect.width;\n        const top = event.clientY / elRect.height;\n        // check if the interactiveArea contains the drag start position.\n        if (left < this._interactiveArea.left || left > this._interactiveArea.right || top < this._interactiveArea.top || top > this._interactiveArea.bottom) return;\n      }\n      // Don't call `event.preventDefault()` on the pointerdown event\n      // to keep receiving pointermove evens outside dragging iframe\n      // https://taye.me/blog/tips/2015/11/16/mouse-drag-outside-iframe/\n      const mouseButton = event.pointerType !== 'mouse' ? null : (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT ? MOUSE_BUTTON.LEFT : (event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE ? MOUSE_BUTTON.MIDDLE : (event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT ? MOUSE_BUTTON.RIGHT : null;\n      if (mouseButton !== null) {\n        const zombiePointer = this._findPointerByMouseButton(mouseButton);\n        zombiePointer && this._disposePointer(zombiePointer);\n      }\n      if ((event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT && this._lockedPointer) return;\n      const pointer = {\n        pointerId: event.pointerId,\n        clientX: event.clientX,\n        clientY: event.clientY,\n        deltaX: 0,\n        deltaY: 0,\n        mouseButton\n      };\n      this._activePointers.push(pointer);\n      // eslint-disable-next-line no-undef\n      this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, {\n        passive: false\n      });\n      this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n      this._domElement.ownerDocument.addEventListener('pointermove', onPointerMove, {\n        passive: false\n      });\n      this._domElement.ownerDocument.addEventListener('pointerup', onPointerUp);\n      this._isDragging = true;\n      startDragging(event);\n    };\n    const onPointerMove = event => {\n      if (event.cancelable) event.preventDefault();\n      const pointerId = event.pointerId;\n      const pointer = this._lockedPointer || this._findPointerById(pointerId);\n      if (!pointer) return;\n      pointer.clientX = event.clientX;\n      pointer.clientY = event.clientY;\n      pointer.deltaX = event.movementX;\n      pointer.deltaY = event.movementY;\n      this._state = 0;\n      if (event.pointerType === 'touch') {\n        switch (this._activePointers.length) {\n          case 1:\n            this._state = this.touches.one;\n            break;\n          case 2:\n            this._state = this.touches.two;\n            break;\n          case 3:\n            this._state = this.touches.three;\n            break;\n        }\n      } else {\n        if (!this._isDragging && this._lockedPointer || this._isDragging && (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT) {\n          this._state = this._state | this.mouseButtons.left;\n        }\n        if (this._isDragging && (event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE) {\n          this._state = this._state | this.mouseButtons.middle;\n        }\n        if (this._isDragging && (event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT) {\n          this._state = this._state | this.mouseButtons.right;\n        }\n      }\n      dragging();\n    };\n    const onPointerUp = event => {\n      const pointer = this._findPointerById(event.pointerId);\n      if (pointer && pointer === this._lockedPointer) return;\n      pointer && this._disposePointer(pointer);\n      if (event.pointerType === 'touch') {\n        switch (this._activePointers.length) {\n          case 0:\n            this._state = ACTION.NONE;\n            break;\n          case 1:\n            this._state = this.touches.one;\n            break;\n          case 2:\n            this._state = this.touches.two;\n            break;\n          case 3:\n            this._state = this.touches.three;\n            break;\n        }\n      } else {\n        this._state = ACTION.NONE;\n      }\n      endDragging();\n    };\n    let lastScrollTimeStamp = -1;\n    const onMouseWheel = event => {\n      if (!this._domElement) return;\n      if (!this._enabled || this.mouseButtons.wheel === ACTION.NONE) return;\n      if (this._interactiveArea.left !== 0 || this._interactiveArea.top !== 0 || this._interactiveArea.width !== 1 || this._interactiveArea.height !== 1) {\n        const elRect = this._domElement.getBoundingClientRect();\n        const left = event.clientX / elRect.width;\n        const top = event.clientY / elRect.height;\n        // check if the interactiveArea contains the drag start position.\n        if (left < this._interactiveArea.left || left > this._interactiveArea.right || top < this._interactiveArea.top || top > this._interactiveArea.bottom) return;\n      }\n      event.preventDefault();\n      if (this.dollyToCursor || this.mouseButtons.wheel === ACTION.ROTATE || this.mouseButtons.wheel === ACTION.TRUCK) {\n        const now = performance.now();\n        // only need to fire this at scroll start.\n        if (lastScrollTimeStamp - now < 1000) this._getClientRect(this._elementRect);\n        lastScrollTimeStamp = now;\n      }\n      // Ref: https://github.com/cedricpinson/osgjs/blob/00e5a7e9d9206c06fdde0436e1d62ab7cb5ce853/sources/osgViewer/input/source/InputSourceMouse.js#L89-L103\n      const deltaYFactor = isMac ? -1 : -3;\n      // Checks event.ctrlKey to detect multi-touch gestures on a trackpad.\n      const delta = event.deltaMode === 1 || event.ctrlKey ? event.deltaY / deltaYFactor : event.deltaY / (deltaYFactor * 10);\n      const x = this.dollyToCursor ? (event.clientX - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n      const y = this.dollyToCursor ? (event.clientY - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n      switch (this.mouseButtons.wheel) {\n        case ACTION.ROTATE:\n          {\n            this._rotateInternal(event.deltaX, event.deltaY);\n            this._isUserControllingRotate = true;\n            break;\n          }\n        case ACTION.TRUCK:\n          {\n            this._truckInternal(event.deltaX, event.deltaY, false, false);\n            this._isUserControllingTruck = true;\n            break;\n          }\n        case ACTION.SCREEN_PAN:\n          {\n            this._truckInternal(event.deltaX, event.deltaY, false, true);\n            this._isUserControllingTruck = true;\n            break;\n          }\n        case ACTION.OFFSET:\n          {\n            this._truckInternal(event.deltaX, event.deltaY, true, false);\n            this._isUserControllingOffset = true;\n            break;\n          }\n        case ACTION.DOLLY:\n          {\n            this._dollyInternal(-delta, x, y);\n            this._isUserControllingDolly = true;\n            break;\n          }\n        case ACTION.ZOOM:\n          {\n            this._zoomInternal(-delta, x, y);\n            this._isUserControllingZoom = true;\n            break;\n          }\n      }\n      this.dispatchEvent({\n        type: 'control'\n      });\n    };\n    const onContextMenu = event => {\n      if (!this._domElement || !this._enabled) return;\n      // contextmenu event is fired right after pointerdown\n      // remove attached handlers and active pointer, if interrupted by contextmenu.\n      if (this.mouseButtons.right === CameraControls.ACTION.NONE) {\n        const pointerId = event instanceof PointerEvent ? event.pointerId : 0;\n        const pointer = this._findPointerById(pointerId);\n        pointer && this._disposePointer(pointer);\n        // eslint-disable-next-line no-undef\n        this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, {\n          passive: false\n        });\n        this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n        return;\n      }\n      event.preventDefault();\n    };\n    const startDragging = event => {\n      if (!this._enabled) return;\n      extractClientCoordFromEvent(this._activePointers, _v2);\n      this._getClientRect(this._elementRect);\n      dragStartPosition.copy(_v2);\n      lastDragPosition.copy(_v2);\n      const isMultiTouch = this._activePointers.length >= 2;\n      if (isMultiTouch) {\n        // 2 finger pinch\n        const dx = _v2.x - this._activePointers[1].clientX;\n        const dy = _v2.y - this._activePointers[1].clientY;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        dollyStart.set(0, distance);\n        // center coords of 2 finger truck\n        const x = (this._activePointers[0].clientX + this._activePointers[1].clientX) * 0.5;\n        const y = (this._activePointers[0].clientY + this._activePointers[1].clientY) * 0.5;\n        lastDragPosition.set(x, y);\n      }\n      this._state = 0;\n      if (!event) {\n        if (this._lockedPointer) this._state = this._state | this.mouseButtons.left;\n      } else if ('pointerType' in event && event.pointerType === 'touch') {\n        switch (this._activePointers.length) {\n          case 1:\n            this._state = this.touches.one;\n            break;\n          case 2:\n            this._state = this.touches.two;\n            break;\n          case 3:\n            this._state = this.touches.three;\n            break;\n        }\n      } else {\n        if (!this._lockedPointer && (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT) {\n          this._state = this._state | this.mouseButtons.left;\n        }\n        if ((event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE) {\n          this._state = this._state | this.mouseButtons.middle;\n        }\n        if ((event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT) {\n          this._state = this._state | this.mouseButtons.right;\n        }\n      }\n      // stop current movement on drag start\n      // - rotate\n      if ((this._state & ACTION.ROTATE) === ACTION.ROTATE || (this._state & ACTION.TOUCH_ROTATE) === ACTION.TOUCH_ROTATE || (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE || (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n        this._sphericalEnd.theta = this._spherical.theta;\n        this._sphericalEnd.phi = this._spherical.phi;\n        this._thetaVelocity.value = 0;\n        this._phiVelocity.value = 0;\n      }\n      // - truck and screen-pan\n      if ((this._state & ACTION.TRUCK) === ACTION.TRUCK || (this._state & ACTION.SCREEN_PAN) === ACTION.SCREEN_PAN || (this._state & ACTION.TOUCH_TRUCK) === ACTION.TOUCH_TRUCK || (this._state & ACTION.TOUCH_SCREEN_PAN) === ACTION.TOUCH_SCREEN_PAN || (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK || (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN || (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK || (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN) {\n        this._targetEnd.copy(this._target);\n        this._targetVelocity.set(0, 0, 0);\n      }\n      // - dolly\n      if ((this._state & ACTION.DOLLY) === ACTION.DOLLY || (this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY || (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK || (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN || (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET || (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE) {\n        this._sphericalEnd.radius = this._spherical.radius;\n        this._radiusVelocity.value = 0;\n      }\n      // - zoom\n      if ((this._state & ACTION.ZOOM) === ACTION.ZOOM || (this._state & ACTION.TOUCH_ZOOM) === ACTION.TOUCH_ZOOM || (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK || (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN || (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET || (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n        this._zoomEnd = this._zoom;\n        this._zoomVelocity.value = 0;\n      }\n      // - offset\n      if ((this._state & ACTION.OFFSET) === ACTION.OFFSET || (this._state & ACTION.TOUCH_OFFSET) === ACTION.TOUCH_OFFSET || (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET || (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET) {\n        this._focalOffsetEnd.copy(this._focalOffset);\n        this._focalOffsetVelocity.set(0, 0, 0);\n      }\n      this.dispatchEvent({\n        type: 'controlstart'\n      });\n    };\n    const dragging = () => {\n      if (!this._enabled || !this._dragNeedsUpdate) return;\n      this._dragNeedsUpdate = false;\n      extractClientCoordFromEvent(this._activePointers, _v2);\n      // When pointer lock is enabled clientX, clientY, screenX, and screenY remain 0.\n      // If pointer lock is enabled, use the Delta directory, and assume active-pointer is not multiple.\n      const isPointerLockActive = this._domElement && this._domElement.ownerDocument.pointerLockElement === this._domElement;\n      const lockedPointer = isPointerLockActive ? this._lockedPointer || this._activePointers[0] : null;\n      const deltaX = lockedPointer ? -lockedPointer.deltaX : lastDragPosition.x - _v2.x;\n      const deltaY = lockedPointer ? -lockedPointer.deltaY : lastDragPosition.y - _v2.y;\n      lastDragPosition.copy(_v2);\n      // rotate\n      if ((this._state & ACTION.ROTATE) === ACTION.ROTATE || (this._state & ACTION.TOUCH_ROTATE) === ACTION.TOUCH_ROTATE || (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE || (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n        this._rotateInternal(deltaX, deltaY);\n        this._isUserControllingRotate = true;\n      }\n      // mouse dolly or zoom\n      if ((this._state & ACTION.DOLLY) === ACTION.DOLLY || (this._state & ACTION.ZOOM) === ACTION.ZOOM) {\n        const dollyX = this.dollyToCursor ? (dragStartPosition.x - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n        const dollyY = this.dollyToCursor ? (dragStartPosition.y - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n        const dollyDirection = this.dollyDragInverted ? -1 : 1;\n        if ((this._state & ACTION.DOLLY) === ACTION.DOLLY) {\n          this._dollyInternal(dollyDirection * deltaY * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n          this._isUserControllingDolly = true;\n        } else {\n          this._zoomInternal(dollyDirection * deltaY * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n          this._isUserControllingZoom = true;\n        }\n      }\n      // touch dolly or zoom\n      if ((this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY || (this._state & ACTION.TOUCH_ZOOM) === ACTION.TOUCH_ZOOM || (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK || (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK || (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN || (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN || (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET || (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET || (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE || (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n        const dx = _v2.x - this._activePointers[1].clientX;\n        const dy = _v2.y - this._activePointers[1].clientY;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        const dollyDelta = dollyStart.y - distance;\n        dollyStart.set(0, distance);\n        const dollyX = this.dollyToCursor ? (lastDragPosition.x - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n        const dollyY = this.dollyToCursor ? (lastDragPosition.y - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n        if ((this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY || (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE || (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK || (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN || (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET) {\n          this._dollyInternal(dollyDelta * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n          this._isUserControllingDolly = true;\n        } else {\n          this._zoomInternal(dollyDelta * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n          this._isUserControllingZoom = true;\n        }\n      }\n      // truck\n      if ((this._state & ACTION.TRUCK) === ACTION.TRUCK || (this._state & ACTION.TOUCH_TRUCK) === ACTION.TOUCH_TRUCK || (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK || (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK) {\n        this._truckInternal(deltaX, deltaY, false, false);\n        this._isUserControllingTruck = true;\n      }\n      // screen-pan\n      if ((this._state & ACTION.SCREEN_PAN) === ACTION.SCREEN_PAN || (this._state & ACTION.TOUCH_SCREEN_PAN) === ACTION.TOUCH_SCREEN_PAN || (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN || (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN) {\n        this._truckInternal(deltaX, deltaY, false, true);\n        this._isUserControllingTruck = true;\n      }\n      // offset\n      if ((this._state & ACTION.OFFSET) === ACTION.OFFSET || (this._state & ACTION.TOUCH_OFFSET) === ACTION.TOUCH_OFFSET || (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET || (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET) {\n        this._truckInternal(deltaX, deltaY, true, false);\n        this._isUserControllingOffset = true;\n      }\n      this.dispatchEvent({\n        type: 'control'\n      });\n    };\n    const endDragging = () => {\n      extractClientCoordFromEvent(this._activePointers, _v2);\n      lastDragPosition.copy(_v2);\n      this._dragNeedsUpdate = false;\n      if (this._activePointers.length === 0 || this._activePointers.length === 1 && this._activePointers[0] === this._lockedPointer) {\n        this._isDragging = false;\n      }\n      if (this._activePointers.length === 0 && this._domElement) {\n        // eslint-disable-next-line no-undef\n        this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, {\n          passive: false\n        });\n        this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n        this.dispatchEvent({\n          type: 'controlend'\n        });\n      }\n    };\n    this.lockPointer = () => {\n      if (!this._enabled || !this._domElement) return;\n      this.cancel();\n      // Element.requestPointerLock is allowed to happen without any pointer active - create a faux one for compatibility with controls\n      this._lockedPointer = {\n        pointerId: -1,\n        clientX: 0,\n        clientY: 0,\n        deltaX: 0,\n        deltaY: 0,\n        mouseButton: null\n      };\n      this._activePointers.push(this._lockedPointer);\n      // eslint-disable-next-line no-undef\n      this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, {\n        passive: false\n      });\n      this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n      this._domElement.requestPointerLock();\n      this._domElement.ownerDocument.addEventListener('pointerlockchange', onPointerLockChange);\n      this._domElement.ownerDocument.addEventListener('pointerlockerror', onPointerLockError);\n      this._domElement.ownerDocument.addEventListener('pointermove', onPointerMove, {\n        passive: false\n      });\n      this._domElement.ownerDocument.addEventListener('pointerup', onPointerUp);\n      startDragging();\n    };\n    this.unlockPointer = () => {\n      var _a, _b, _c;\n      if (this._lockedPointer !== null) {\n        this._disposePointer(this._lockedPointer);\n        this._lockedPointer = null;\n      }\n      (_a = this._domElement) === null || _a === void 0 ? void 0 : _a.ownerDocument.exitPointerLock();\n      (_b = this._domElement) === null || _b === void 0 ? void 0 : _b.ownerDocument.removeEventListener('pointerlockchange', onPointerLockChange);\n      (_c = this._domElement) === null || _c === void 0 ? void 0 : _c.ownerDocument.removeEventListener('pointerlockerror', onPointerLockError);\n      this.cancel();\n    };\n    const onPointerLockChange = () => {\n      const isPointerLockActive = this._domElement && this._domElement.ownerDocument.pointerLockElement === this._domElement;\n      if (!isPointerLockActive) this.unlockPointer();\n    };\n    const onPointerLockError = () => {\n      this.unlockPointer();\n    };\n    this._addAllEventListeners = domElement => {\n      this._domElement = domElement;\n      this._domElement.style.touchAction = 'none';\n      this._domElement.style.userSelect = 'none';\n      this._domElement.style.webkitUserSelect = 'none';\n      this._domElement.addEventListener('pointerdown', onPointerDown);\n      this._domElement.addEventListener('pointercancel', onPointerUp);\n      this._domElement.addEventListener('wheel', onMouseWheel, {\n        passive: false\n      });\n      this._domElement.addEventListener('contextmenu', onContextMenu);\n    };\n    this._removeAllEventListeners = () => {\n      if (!this._domElement) return;\n      this._domElement.style.touchAction = '';\n      this._domElement.style.userSelect = '';\n      this._domElement.style.webkitUserSelect = '';\n      this._domElement.removeEventListener('pointerdown', onPointerDown);\n      this._domElement.removeEventListener('pointercancel', onPointerUp);\n      // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener#matching_event_listeners_for_removal\n      // > it's probably wise to use the same values used for the call to `addEventListener()` when calling `removeEventListener()`\n      // see https://github.com/microsoft/TypeScript/issues/32912#issuecomment-522142969\n      // eslint-disable-next-line no-undef\n      this._domElement.removeEventListener('wheel', onMouseWheel, {\n        passive: false\n      });\n      this._domElement.removeEventListener('contextmenu', onContextMenu);\n      // eslint-disable-next-line no-undef\n      this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, {\n        passive: false\n      });\n      this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n      this._domElement.ownerDocument.removeEventListener('pointerlockchange', onPointerLockChange);\n      this._domElement.ownerDocument.removeEventListener('pointerlockerror', onPointerLockError);\n    };\n    this.cancel = () => {\n      if (this._state === ACTION.NONE) return;\n      this._state = ACTION.NONE;\n      this._activePointers.length = 0;\n      endDragging();\n    };\n    if (domElement) this.connect(domElement);\n    this.update(0);\n  }\n  /**\n   * The camera to be controlled\n   * @category Properties\n   */\n  get camera() {\n    return this._camera;\n  }\n  set camera(camera) {\n    this._camera = camera;\n    this.updateCameraUp();\n    this._camera.updateProjectionMatrix();\n    this._updateNearPlaneCorners();\n    this._needsUpdate = true;\n  }\n  /**\n   * Whether or not the controls are enabled.\n   * `false` to disable user dragging/touch-move, but all methods works.\n   * @category Properties\n   */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(enabled) {\n    this._enabled = enabled;\n    if (!this._domElement) return;\n    if (enabled) {\n      this._domElement.style.touchAction = 'none';\n      this._domElement.style.userSelect = 'none';\n      this._domElement.style.webkitUserSelect = 'none';\n    } else {\n      this.cancel();\n      this._domElement.style.touchAction = '';\n      this._domElement.style.userSelect = '';\n      this._domElement.style.webkitUserSelect = '';\n    }\n  }\n  /**\n   * Returns `true` if the controls are active updating.\n   * readonly value.\n   * @category Properties\n   */\n  get active() {\n    return !this._hasRested;\n  }\n  /**\n   * Getter for the current `ACTION`.\n   * readonly value.\n   * @category Properties\n   */\n  get currentAction() {\n    return this._state;\n  }\n  /**\n   * get/set Current distance.\n   * @category Properties\n   */\n  get distance() {\n    return this._spherical.radius;\n  }\n  set distance(distance) {\n    if (this._spherical.radius === distance && this._sphericalEnd.radius === distance) return;\n    this._spherical.radius = distance;\n    this._sphericalEnd.radius = distance;\n    this._needsUpdate = true;\n  }\n  // horizontal angle\n  /**\n   * get/set the azimuth angle (horizontal) in radians.\n   * Every 360 degrees turn is added to `.azimuthAngle` value, which is accumulative.\n   * @category Properties\n   */\n  get azimuthAngle() {\n    return this._spherical.theta;\n  }\n  set azimuthAngle(azimuthAngle) {\n    if (this._spherical.theta === azimuthAngle && this._sphericalEnd.theta === azimuthAngle) return;\n    this._spherical.theta = azimuthAngle;\n    this._sphericalEnd.theta = azimuthAngle;\n    this._needsUpdate = true;\n  }\n  // vertical angle\n  /**\n   * get/set the polar angle (vertical) in radians.\n   * @category Properties\n   */\n  get polarAngle() {\n    return this._spherical.phi;\n  }\n  set polarAngle(polarAngle) {\n    if (this._spherical.phi === polarAngle && this._sphericalEnd.phi === polarAngle) return;\n    this._spherical.phi = polarAngle;\n    this._sphericalEnd.phi = polarAngle;\n    this._needsUpdate = true;\n  }\n  /**\n   * Whether camera position should be enclosed in the boundary or not.\n   * @category Properties\n   */\n  get boundaryEnclosesCamera() {\n    return this._boundaryEnclosesCamera;\n  }\n  set boundaryEnclosesCamera(boundaryEnclosesCamera) {\n    this._boundaryEnclosesCamera = boundaryEnclosesCamera;\n    this._needsUpdate = true;\n  }\n  /**\n   * Set drag-start, touches and wheel enable area in the domElement.\n   * each values are between `0` and `1` inclusive, where `0` is left/top and `1` is right/bottom of the screen.\n   * e.g. `{ x: 0, y: 0, width: 1, height: 1 }` for entire area.\n   * @category Properties\n   */\n  set interactiveArea(interactiveArea) {\n    this._interactiveArea.width = clamp(interactiveArea.width, 0, 1);\n    this._interactiveArea.height = clamp(interactiveArea.height, 0, 1);\n    this._interactiveArea.x = clamp(interactiveArea.x, 0, 1 - this._interactiveArea.width);\n    this._interactiveArea.y = clamp(interactiveArea.y, 0, 1 - this._interactiveArea.height);\n  }\n  /**\n   * Adds the specified event listener.\n   * Applicable event types (which is `K`) are:\n   * | Event name          | Timing |\n   * | ------------------- | ------ |\n   * | `'controlstart'`    | When the user starts to control the camera via mouse / touches. ¹ |\n   * | `'control'`         | When the user controls the camera (dragging). |\n   * | `'controlend'`      | When the user ends to control the camera. ¹ |\n   * | `'transitionstart'` | When any kind of transition starts, either user control or using a method with `enableTransition = true` |\n   * | `'update'`          | When the camera position is updated. |\n   * | `'wake'`            | When the camera starts moving. |\n   * | `'rest'`            | When the camera movement is below `.restThreshold` ². |\n   * | `'sleep'`           | When the camera end moving. |\n   *\n   * 1. `mouseButtons.wheel` (Mouse wheel control) does not emit `'controlstart'` and `'controlend'`. `mouseButtons.wheel` uses scroll-event internally, and scroll-event happens intermittently. That means \"start\" and \"end\" cannot be detected.\n   * 2. Due to damping, `sleep` will usually fire a few seconds after the camera _appears_ to have stopped moving. If you want to do something (e.g. enable UI, perform another transition) at the point when the camera has stopped, you probably want the `rest` event. This can be fine tuned using the `.restThreshold` parameter. See the [Rest and Sleep Example](https://yomotsu.github.io/camera-controls/examples/rest-and-sleep.html).\n   *\n   * e.g.\n   * ```\n   * cameraControl.addEventListener( 'controlstart', myCallbackFunction );\n   * ```\n   * @param type event name\n   * @param listener handler function\n   * @category Methods\n   */\n  addEventListener(type, listener) {\n    super.addEventListener(type, listener);\n  }\n  /**\n   * Removes the specified event listener\n   * e.g.\n   * ```\n   * cameraControl.addEventListener( 'controlstart', myCallbackFunction );\n   * ```\n   * @param type event name\n   * @param listener handler function\n   * @category Methods\n   */\n  removeEventListener(type, listener) {\n    super.removeEventListener(type, listener);\n  }\n  /**\n   * Rotate azimuthal angle(horizontal) and polar angle(vertical).\n   * Every value is added to the current value.\n   * @param azimuthAngle Azimuth rotate angle. In radian.\n   * @param polarAngle Polar rotate angle. In radian.\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  rotate(azimuthAngle, polarAngle, enableTransition = false) {\n    return this.rotateTo(this._sphericalEnd.theta + azimuthAngle, this._sphericalEnd.phi + polarAngle, enableTransition);\n  }\n  /**\n   * Rotate azimuthal angle(horizontal) to the given angle and keep the same polar angle(vertical) target.\n   *\n   * e.g.\n   * ```\n   * cameraControls.rotateAzimuthTo( 30 * THREE.MathUtils.DEG2RAD, true );\n   * ```\n   * @param azimuthAngle Azimuth rotate angle. In radian.\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  rotateAzimuthTo(azimuthAngle, enableTransition = false) {\n    return this.rotateTo(azimuthAngle, this._sphericalEnd.phi, enableTransition);\n  }\n  /**\n   * Rotate polar angle(vertical) to the given angle and keep the same azimuthal angle(horizontal) target.\n   *\n   * e.g.\n   * ```\n   * cameraControls.rotatePolarTo( 30 * THREE.MathUtils.DEG2RAD, true );\n   * ```\n   * @param polarAngle Polar rotate angle. In radian.\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  rotatePolarTo(polarAngle, enableTransition = false) {\n    return this.rotateTo(this._sphericalEnd.theta, polarAngle, enableTransition);\n  }\n  /**\n   * Rotate azimuthal angle(horizontal) and polar angle(vertical) to the given angle.\n   * Camera view will rotate over the orbit pivot absolutely:\n   *\n   * azimuthAngle\n   * ```\n   *       0º\n   *         \\\n   * 90º -----+----- -90º\n   *           \\\n   *           180º\n   * ```\n   * | direction | angle                  |\n   * | --------- | ---------------------- |\n   * | front     | 0º                     |\n   * | left      | 90º (`Math.PI / 2`)    |\n   * | right     | -90º (`- Math.PI / 2`) |\n   * | back      | 180º (`Math.PI`)       |\n   *\n   * polarAngle\n   * ```\n   *     180º\n   *      |\n   *      90º\n   *      |\n   *      0º\n   * ```\n   * | direction            | angle                  |\n   * | -------------------- | ---------------------- |\n   * | top/sky              | 180º (`Math.PI`)       |\n   * | horizontal from view | 90º (`Math.PI / 2`)    |\n   * | bottom/floor         | 0º                     |\n   *\n   * @param azimuthAngle Azimuth rotate angle to. In radian.\n   * @param polarAngle Polar rotate angle to. In radian.\n   * @param enableTransition  Whether to move smoothly or immediately\n   * @category Methods\n   */\n  rotateTo(azimuthAngle, polarAngle, enableTransition = false) {\n    this._isUserControllingRotate = false;\n    const theta = clamp(azimuthAngle, this.minAzimuthAngle, this.maxAzimuthAngle);\n    const phi = clamp(polarAngle, this.minPolarAngle, this.maxPolarAngle);\n    this._sphericalEnd.theta = theta;\n    this._sphericalEnd.phi = phi;\n    this._sphericalEnd.makeSafe();\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._spherical.theta = this._sphericalEnd.theta;\n      this._spherical.phi = this._sphericalEnd.phi;\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) && approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Dolly in/out camera position.\n   * @param distance Distance of dollyIn. Negative number for dollyOut.\n   * @param enableTransition Whether to move smoothly or immediately.\n   * @category Methods\n   */\n  dolly(distance, enableTransition = false) {\n    return this.dollyTo(this._sphericalEnd.radius - distance, enableTransition);\n  }\n  /**\n   * Dolly in/out camera position to given distance.\n   * @param distance Distance of dolly.\n   * @param enableTransition Whether to move smoothly or immediately.\n   * @category Methods\n   */\n  dollyTo(distance, enableTransition = false) {\n    this._isUserControllingDolly = false;\n    this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n    this._changedDolly = 0;\n    return this._dollyToNoClamp(clamp(distance, this.minDistance, this.maxDistance), enableTransition);\n  }\n  _dollyToNoClamp(distance, enableTransition = false) {\n    const lastRadius = this._sphericalEnd.radius;\n    const hasCollider = this.colliderMeshes.length >= 1;\n    if (hasCollider) {\n      const maxDistanceByCollisionTest = this._collisionTest();\n      const isCollided = approxEquals(maxDistanceByCollisionTest, this._spherical.radius);\n      const isDollyIn = lastRadius > distance;\n      if (!isDollyIn && isCollided) return Promise.resolve();\n      this._sphericalEnd.radius = Math.min(distance, maxDistanceByCollisionTest);\n    } else {\n      this._sphericalEnd.radius = distance;\n    }\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._spherical.radius = this._sphericalEnd.radius;\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Dolly in, but does not change the distance between the target and the camera, and moves the target position instead.\n   * Specify a negative value for dolly out.\n   * @param distance Distance of dolly.\n   * @param enableTransition Whether to move smoothly or immediately.\n   * @category Methods\n   */\n  dollyInFixed(distance, enableTransition = false) {\n    this._targetEnd.add(this._getCameraDirection(_cameraDirection).multiplyScalar(distance));\n    if (!enableTransition) {\n      this._target.copy(this._targetEnd);\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) && approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) && approxEquals(this._target.z, this._targetEnd.z, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Zoom in/out camera. The value is added to camera zoom.\n   * Limits set with `.minZoom` and `.maxZoom`\n   * @param zoomStep zoom scale\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  zoom(zoomStep, enableTransition = false) {\n    return this.zoomTo(this._zoomEnd + zoomStep, enableTransition);\n  }\n  /**\n   * Zoom in/out camera to given scale. The value overwrites camera zoom.\n   * Limits set with .minZoom and .maxZoom\n   * @param zoom\n   * @param enableTransition\n   * @category Methods\n   */\n  zoomTo(zoom, enableTransition = false) {\n    this._isUserControllingZoom = false;\n    this._zoomEnd = clamp(zoom, this.minZoom, this.maxZoom);\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._zoom = this._zoomEnd;\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._zoom, this._zoomEnd, this.restThreshold);\n    this._changedZoom = 0;\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * @deprecated `pan()` has been renamed to `truck()`\n   * @category Methods\n   */\n  pan(x, y, enableTransition = false) {\n    console.warn('`pan` has been renamed to `truck`');\n    return this.truck(x, y, enableTransition);\n  }\n  /**\n   * Truck and pedestal camera using current azimuthal angle\n   * @param x Horizontal translate amount\n   * @param y Vertical translate amount\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  truck(x, y, enableTransition = false) {\n    this._camera.updateMatrix();\n    _xColumn.setFromMatrixColumn(this._camera.matrix, 0);\n    _yColumn.setFromMatrixColumn(this._camera.matrix, 1);\n    _xColumn.multiplyScalar(x);\n    _yColumn.multiplyScalar(-y);\n    const offset = _v3A.copy(_xColumn).add(_yColumn);\n    const to = _v3B.copy(this._targetEnd).add(offset);\n    return this.moveTo(to.x, to.y, to.z, enableTransition);\n  }\n  /**\n   * Move forward / backward.\n   * @param distance Amount to move forward / backward. Negative value to move backward\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  forward(distance, enableTransition = false) {\n    _v3A.setFromMatrixColumn(this._camera.matrix, 0);\n    _v3A.crossVectors(this._camera.up, _v3A);\n    _v3A.multiplyScalar(distance);\n    const to = _v3B.copy(this._targetEnd).add(_v3A);\n    return this.moveTo(to.x, to.y, to.z, enableTransition);\n  }\n  /**\n   * Move up / down.\n   * @param height Amount to move up / down. Negative value to move down\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  elevate(height, enableTransition = false) {\n    _v3A.copy(this._camera.up).multiplyScalar(height);\n    return this.moveTo(this._targetEnd.x + _v3A.x, this._targetEnd.y + _v3A.y, this._targetEnd.z + _v3A.z, enableTransition);\n  }\n  /**\n   * Move target position to given point.\n   * @param x x coord to move center position\n   * @param y y coord to move center position\n   * @param z z coord to move center position\n   * @param enableTransition Whether to move smoothly or immediately\n   * @category Methods\n   */\n  moveTo(x, y, z, enableTransition = false) {\n    this._isUserControllingTruck = false;\n    const offset = _v3A.set(x, y, z).sub(this._targetEnd);\n    this._encloseToBoundary(this._targetEnd, offset, this.boundaryFriction);\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._target.copy(this._targetEnd);\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) && approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) && approxEquals(this._target.z, this._targetEnd.z, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Look in the given point direction.\n   * @param x point x.\n   * @param y point y.\n   * @param z point z.\n   * @param enableTransition Whether to move smoothly or immediately.\n   * @returns Transition end promise\n   * @category Methods\n   */\n  lookInDirectionOf(x, y, z, enableTransition = false) {\n    const point = _v3A.set(x, y, z);\n    const direction = point.sub(this._targetEnd).normalize();\n    const position = direction.multiplyScalar(-this._sphericalEnd.radius).add(this._targetEnd);\n    return this.setPosition(position.x, position.y, position.z, enableTransition);\n  }\n  /**\n   * Fit the viewport to the box or the bounding box of the object, using the nearest axis. paddings are in unit.\n   * set `cover: true` to fill enter screen.\n   * e.g.\n   * ```\n   * cameraControls.fitToBox( myMesh );\n   * ```\n   * @param box3OrObject Axis aligned bounding box to fit the view.\n   * @param enableTransition Whether to move smoothly or immediately.\n   * @param options | `<object>` { cover: boolean, paddingTop: number, paddingLeft: number, paddingBottom: number, paddingRight: number }\n   * @returns Transition end promise\n   * @category Methods\n   */\n  fitToBox(box3OrObject, enableTransition, {\n    cover = false,\n    paddingLeft = 0,\n    paddingRight = 0,\n    paddingBottom = 0,\n    paddingTop = 0\n  } = {}) {\n    const promises = [];\n    const aabb = box3OrObject.isBox3 ? _box3A.copy(box3OrObject) : _box3A.setFromObject(box3OrObject);\n    if (aabb.isEmpty()) {\n      console.warn('camera-controls: fitTo() cannot be used with an empty box. Aborting');\n      Promise.resolve();\n    }\n    // round to closest axis ( forward | backward | right | left | top | bottom )\n    const theta = roundToStep(this._sphericalEnd.theta, PI_HALF);\n    const phi = roundToStep(this._sphericalEnd.phi, PI_HALF);\n    promises.push(this.rotateTo(theta, phi, enableTransition));\n    const normal = _v3A.setFromSpherical(this._sphericalEnd).normalize();\n    const rotation = _quaternionA.setFromUnitVectors(normal, _AXIS_Z);\n    const viewFromPolar = approxEquals(Math.abs(normal.y), 1);\n    if (viewFromPolar) {\n      rotation.multiply(_quaternionB.setFromAxisAngle(_AXIS_Y, theta));\n    }\n    rotation.multiply(this._yAxisUpSpaceInverse);\n    // make oriented bounding box\n    const bb = _box3B.makeEmpty();\n    // left bottom back corner\n    _v3B.copy(aabb.min).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // right bottom back corner\n    _v3B.copy(aabb.min).setX(aabb.max.x).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // left top back corner\n    _v3B.copy(aabb.min).setY(aabb.max.y).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // right top back corner\n    _v3B.copy(aabb.max).setZ(aabb.min.z).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // left bottom front corner\n    _v3B.copy(aabb.min).setZ(aabb.max.z).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // right bottom front corner\n    _v3B.copy(aabb.max).setY(aabb.min.y).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // left top front corner\n    _v3B.copy(aabb.max).setX(aabb.min.x).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // right top front corner\n    _v3B.copy(aabb.max).applyQuaternion(rotation);\n    bb.expandByPoint(_v3B);\n    // add padding\n    bb.min.x -= paddingLeft;\n    bb.min.y -= paddingBottom;\n    bb.max.x += paddingRight;\n    bb.max.y += paddingTop;\n    rotation.setFromUnitVectors(_AXIS_Z, normal);\n    if (viewFromPolar) {\n      rotation.premultiply(_quaternionB.invert());\n    }\n    rotation.premultiply(this._yAxisUpSpace);\n    const bbSize = bb.getSize(_v3A);\n    const center = bb.getCenter(_v3B).applyQuaternion(rotation);\n    if (isPerspectiveCamera(this._camera)) {\n      const distance = this.getDistanceToFitBox(bbSize.x, bbSize.y, bbSize.z, cover);\n      promises.push(this.moveTo(center.x, center.y, center.z, enableTransition));\n      promises.push(this.dollyTo(distance, enableTransition));\n      promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n    } else if (isOrthographicCamera(this._camera)) {\n      const camera = this._camera;\n      const width = camera.right - camera.left;\n      const height = camera.top - camera.bottom;\n      const zoom = cover ? Math.max(width / bbSize.x, height / bbSize.y) : Math.min(width / bbSize.x, height / bbSize.y);\n      promises.push(this.moveTo(center.x, center.y, center.z, enableTransition));\n      promises.push(this.zoomTo(zoom, enableTransition));\n      promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n    }\n    return Promise.all(promises);\n  }\n  /**\n   * Fit the viewport to the sphere or the bounding sphere of the object.\n   * @param sphereOrMesh\n   * @param enableTransition\n   * @category Methods\n   */\n  fitToSphere(sphereOrMesh, enableTransition) {\n    const promises = [];\n    const isObject3D = 'isObject3D' in sphereOrMesh;\n    const boundingSphere = isObject3D ? CameraControls.createBoundingSphere(sphereOrMesh, _sphere) : _sphere.copy(sphereOrMesh);\n    promises.push(this.moveTo(boundingSphere.center.x, boundingSphere.center.y, boundingSphere.center.z, enableTransition));\n    if (isPerspectiveCamera(this._camera)) {\n      const distanceToFit = this.getDistanceToFitSphere(boundingSphere.radius);\n      promises.push(this.dollyTo(distanceToFit, enableTransition));\n    } else if (isOrthographicCamera(this._camera)) {\n      const width = this._camera.right - this._camera.left;\n      const height = this._camera.top - this._camera.bottom;\n      const diameter = 2 * boundingSphere.radius;\n      const zoom = Math.min(width / diameter, height / diameter);\n      promises.push(this.zoomTo(zoom, enableTransition));\n    }\n    promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n    return Promise.all(promises);\n  }\n  /**\n   * Look at the `target` from the `position`.\n   * @param positionX\n   * @param positionY\n   * @param positionZ\n   * @param targetX\n   * @param targetY\n   * @param targetZ\n   * @param enableTransition\n   * @category Methods\n   */\n  setLookAt(positionX, positionY, positionZ, targetX, targetY, targetZ, enableTransition = false) {\n    this._isUserControllingRotate = false;\n    this._isUserControllingDolly = false;\n    this._isUserControllingTruck = false;\n    this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n    this._changedDolly = 0;\n    const target = _v3B.set(targetX, targetY, targetZ);\n    const position = _v3A.set(positionX, positionY, positionZ);\n    this._targetEnd.copy(target);\n    this._sphericalEnd.setFromVector3(position.sub(target).applyQuaternion(this._yAxisUpSpace));\n    this.normalizeRotations();\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._target.copy(this._targetEnd);\n      this._spherical.copy(this._sphericalEnd);\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) && approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) && approxEquals(this._target.z, this._targetEnd.z, this.restThreshold) && approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) && approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold) && approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Similar to setLookAt, but it interpolates between two states.\n   * @param positionAX\n   * @param positionAY\n   * @param positionAZ\n   * @param targetAX\n   * @param targetAY\n   * @param targetAZ\n   * @param positionBX\n   * @param positionBY\n   * @param positionBZ\n   * @param targetBX\n   * @param targetBY\n   * @param targetBZ\n   * @param t\n   * @param enableTransition\n   * @category Methods\n   */\n  lerpLookAt(positionAX, positionAY, positionAZ, targetAX, targetAY, targetAZ, positionBX, positionBY, positionBZ, targetBX, targetBY, targetBZ, t, enableTransition = false) {\n    this._isUserControllingRotate = false;\n    this._isUserControllingDolly = false;\n    this._isUserControllingTruck = false;\n    this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n    this._changedDolly = 0;\n    const targetA = _v3A.set(targetAX, targetAY, targetAZ);\n    const positionA = _v3B.set(positionAX, positionAY, positionAZ);\n    _sphericalA.setFromVector3(positionA.sub(targetA).applyQuaternion(this._yAxisUpSpace));\n    const targetB = _v3C.set(targetBX, targetBY, targetBZ);\n    const positionB = _v3B.set(positionBX, positionBY, positionBZ);\n    _sphericalB.setFromVector3(positionB.sub(targetB).applyQuaternion(this._yAxisUpSpace));\n    this._targetEnd.copy(targetA.lerp(targetB, t)); // tricky\n    const deltaTheta = _sphericalB.theta - _sphericalA.theta;\n    const deltaPhi = _sphericalB.phi - _sphericalA.phi;\n    const deltaRadius = _sphericalB.radius - _sphericalA.radius;\n    this._sphericalEnd.set(_sphericalA.radius + deltaRadius * t, _sphericalA.phi + deltaPhi * t, _sphericalA.theta + deltaTheta * t);\n    this.normalizeRotations();\n    this._needsUpdate = true;\n    if (!enableTransition) {\n      this._target.copy(this._targetEnd);\n      this._spherical.copy(this._sphericalEnd);\n    }\n    const resolveImmediately = !enableTransition || approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) && approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) && approxEquals(this._target.z, this._targetEnd.z, this.restThreshold) && approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) && approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold) && approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Set angle and distance by given position.\n   * An alias of `setLookAt()`, without target change. Thus keep gazing at the current target\n   * @param positionX\n   * @param positionY\n   * @param positionZ\n   * @param enableTransition\n   * @category Methods\n   */\n  setPosition(positionX, positionY, positionZ, enableTransition = false) {\n    return this.setLookAt(positionX, positionY, positionZ, this._targetEnd.x, this._targetEnd.y, this._targetEnd.z, enableTransition);\n  }\n  /**\n   * Set the target position where gaze at.\n   * An alias of `setLookAt()`, without position change. Thus keep the same position.\n   * @param targetX\n   * @param targetY\n   * @param targetZ\n   * @param enableTransition\n   * @category Methods\n   */\n  setTarget(targetX, targetY, targetZ, enableTransition = false) {\n    const pos = this.getPosition(_v3A);\n    const promise = this.setLookAt(pos.x, pos.y, pos.z, targetX, targetY, targetZ, enableTransition);\n    // see https://github.com/yomotsu/camera-controls/issues/335\n    this._sphericalEnd.phi = clamp(this._sphericalEnd.phi, this.minPolarAngle, this.maxPolarAngle);\n    return promise;\n  }\n  /**\n   * Set focal offset using the screen parallel coordinates. z doesn't affect in Orthographic as with Dolly.\n   * @param x\n   * @param y\n   * @param z\n   * @param enableTransition\n   * @category Methods\n   */\n  setFocalOffset(x, y, z, enableTransition = false) {\n    this._isUserControllingOffset = false;\n    this._focalOffsetEnd.set(x, y, z);\n    this._needsUpdate = true;\n    if (!enableTransition) this._focalOffset.copy(this._focalOffsetEnd);\n    const resolveImmediately = !enableTransition || approxEquals(this._focalOffset.x, this._focalOffsetEnd.x, this.restThreshold) && approxEquals(this._focalOffset.y, this._focalOffsetEnd.y, this.restThreshold) && approxEquals(this._focalOffset.z, this._focalOffsetEnd.z, this.restThreshold);\n    return this._createOnRestPromise(resolveImmediately);\n  }\n  /**\n   * Set orbit point without moving the camera.\n   * SHOULD NOT RUN DURING ANIMATIONS. `setOrbitPoint()` will immediately fix the positions.\n   * @param targetX\n   * @param targetY\n   * @param targetZ\n   * @category Methods\n   */\n  setOrbitPoint(targetX, targetY, targetZ) {\n    this._camera.updateMatrixWorld();\n    _xColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 0);\n    _yColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 1);\n    _zColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 2);\n    const position = _v3A.set(targetX, targetY, targetZ);\n    const distance = position.distanceTo(this._camera.position);\n    const cameraToPoint = position.sub(this._camera.position);\n    _xColumn.multiplyScalar(cameraToPoint.x);\n    _yColumn.multiplyScalar(cameraToPoint.y);\n    _zColumn.multiplyScalar(cameraToPoint.z);\n    _v3A.copy(_xColumn).add(_yColumn).add(_zColumn);\n    _v3A.z = _v3A.z + distance;\n    this.dollyTo(distance, false);\n    this.setFocalOffset(-_v3A.x, _v3A.y, -_v3A.z, false);\n    this.moveTo(targetX, targetY, targetZ, false);\n  }\n  /**\n   * Set the boundary box that encloses the target of the camera. box3 is in THREE.Box3\n   * @param box3\n   * @category Methods\n   */\n  setBoundary(box3) {\n    if (!box3) {\n      this._boundary.min.set(-Infinity, -Infinity, -Infinity);\n      this._boundary.max.set(Infinity, Infinity, Infinity);\n      this._needsUpdate = true;\n      return;\n    }\n    this._boundary.copy(box3);\n    this._boundary.clampPoint(this._targetEnd, this._targetEnd);\n    this._needsUpdate = true;\n  }\n  /**\n   * Set (or unset) the current viewport.\n   * Set this when you want to use renderer viewport and .dollyToCursor feature at the same time.\n   * @param viewportOrX\n   * @param y\n   * @param width\n   * @param height\n   * @category Methods\n   */\n  setViewport(viewportOrX, y, width, height) {\n    if (viewportOrX === null) {\n      // null\n      this._viewport = null;\n      return;\n    }\n    this._viewport = this._viewport || new THREE.Vector4();\n    if (typeof viewportOrX === 'number') {\n      // number\n      this._viewport.set(viewportOrX, y, width, height);\n    } else {\n      // Vector4\n      this._viewport.copy(viewportOrX);\n    }\n  }\n  /**\n   * Calculate the distance to fit the box.\n   * @param width box width\n   * @param height box height\n   * @param depth box depth\n   * @returns distance\n   * @category Methods\n   */\n  getDistanceToFitBox(width, height, depth, cover = false) {\n    if (notSupportedInOrthographicCamera(this._camera, 'getDistanceToFitBox')) return this._spherical.radius;\n    const boundingRectAspect = width / height;\n    const fov = this._camera.getEffectiveFOV() * DEG2RAD;\n    const aspect = this._camera.aspect;\n    const heightToFit = (cover ? boundingRectAspect > aspect : boundingRectAspect < aspect) ? height : width / aspect;\n    return heightToFit * 0.5 / Math.tan(fov * 0.5) + depth * 0.5;\n  }\n  /**\n   * Calculate the distance to fit the sphere.\n   * @param radius sphere radius\n   * @returns distance\n   * @category Methods\n   */\n  getDistanceToFitSphere(radius) {\n    if (notSupportedInOrthographicCamera(this._camera, 'getDistanceToFitSphere')) return this._spherical.radius;\n    // https://stackoverflow.com/a/44849975\n    const vFOV = this._camera.getEffectiveFOV() * DEG2RAD;\n    const hFOV = Math.atan(Math.tan(vFOV * 0.5) * this._camera.aspect) * 2;\n    const fov = 1 < this._camera.aspect ? vFOV : hFOV;\n    return radius / Math.sin(fov * 0.5);\n  }\n  /**\n   * Returns the orbit center position, where the camera looking at.\n   * @param out The receiving Vector3 instance to copy the result\n   * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n   * @category Methods\n   */\n  getTarget(out, receiveEndValue = true) {\n    const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n    return _out.copy(receiveEndValue ? this._targetEnd : this._target);\n  }\n  /**\n   * Returns the camera position.\n   * @param out The receiving Vector3 instance to copy the result\n   * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n   * @category Methods\n   */\n  getPosition(out, receiveEndValue = true) {\n    const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n    return _out.setFromSpherical(receiveEndValue ? this._sphericalEnd : this._spherical).applyQuaternion(this._yAxisUpSpaceInverse).add(receiveEndValue ? this._targetEnd : this._target);\n  }\n  /**\n   * Returns the spherical coordinates of the orbit.\n   * @param out The receiving Spherical instance to copy the result\n   * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n   * @category Methods\n   */\n  getSpherical(out, receiveEndValue = true) {\n    const _out = out || new THREE.Spherical();\n    return _out.copy(receiveEndValue ? this._sphericalEnd : this._spherical);\n  }\n  /**\n   * Returns the focal offset, which is how much the camera appears to be translated in screen parallel coordinates.\n   * @param out The receiving Vector3 instance to copy the result\n   * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n   * @category Methods\n   */\n  getFocalOffset(out, receiveEndValue = true) {\n    const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n    return _out.copy(receiveEndValue ? this._focalOffsetEnd : this._focalOffset);\n  }\n  /**\n   * Normalize camera azimuth angle rotation between 0 and 360 degrees.\n   * @category Methods\n   */\n  normalizeRotations() {\n    this._sphericalEnd.theta = this._sphericalEnd.theta % PI_2;\n    if (this._sphericalEnd.theta < 0) this._sphericalEnd.theta += PI_2;\n    this._spherical.theta += PI_2 * Math.round((this._sphericalEnd.theta - this._spherical.theta) / PI_2);\n  }\n  /**\n   * stop all transitions.\n   */\n  stop() {\n    this._focalOffset.copy(this._focalOffsetEnd);\n    this._target.copy(this._targetEnd);\n    this._spherical.copy(this._sphericalEnd);\n    this._zoom = this._zoomEnd;\n  }\n  /**\n   * Reset all rotation and position to defaults.\n   * @param enableTransition\n   * @category Methods\n   */\n  reset(enableTransition = false) {\n    if (!approxEquals(this._camera.up.x, this._cameraUp0.x) || !approxEquals(this._camera.up.y, this._cameraUp0.y) || !approxEquals(this._camera.up.z, this._cameraUp0.z)) {\n      this._camera.up.copy(this._cameraUp0);\n      const position = this.getPosition(_v3A);\n      this.updateCameraUp();\n      this.setPosition(position.x, position.y, position.z);\n    }\n    const promises = [this.setLookAt(this._position0.x, this._position0.y, this._position0.z, this._target0.x, this._target0.y, this._target0.z, enableTransition), this.setFocalOffset(this._focalOffset0.x, this._focalOffset0.y, this._focalOffset0.z, enableTransition), this.zoomTo(this._zoom0, enableTransition)];\n    return Promise.all(promises);\n  }\n  /**\n   * Set current camera position as the default position.\n   * @category Methods\n   */\n  saveState() {\n    this._cameraUp0.copy(this._camera.up);\n    this.getTarget(this._target0);\n    this.getPosition(this._position0);\n    this._zoom0 = this._zoom;\n    this._focalOffset0.copy(this._focalOffset);\n  }\n  /**\n   * Sync camera-up direction.\n   * When camera-up vector is changed, `.updateCameraUp()` must be called.\n   * @category Methods\n   */\n  updateCameraUp() {\n    this._yAxisUpSpace.setFromUnitVectors(this._camera.up, _AXIS_Y);\n    this._yAxisUpSpaceInverse.copy(this._yAxisUpSpace).invert();\n  }\n  /**\n   * Apply current camera-up direction to the camera.\n   * The orbit system will be re-initialized with the current position.\n   * @category Methods\n   */\n  applyCameraUp() {\n    const cameraDirection = _v3A.subVectors(this._target, this._camera.position).normalize();\n    // So first find the vector off to the side, orthogonal to both this.object.up and\n    // the \"view\" vector.\n    const side = _v3B.crossVectors(cameraDirection, this._camera.up);\n    // Then find the vector orthogonal to both this \"side\" vector and the \"view\" vector.\n    // This vector will be the new \"up\" vector.\n    this._camera.up.crossVectors(side, cameraDirection).normalize();\n    this._camera.updateMatrixWorld();\n    const position = this.getPosition(_v3A);\n    this.updateCameraUp();\n    this.setPosition(position.x, position.y, position.z);\n  }\n  /**\n   * Update camera position and directions.\n   * This should be called in your tick loop every time, and returns true if re-rendering is needed.\n   * @param delta\n   * @returns updated\n   * @category Methods\n   */\n  update(delta) {\n    const deltaTheta = this._sphericalEnd.theta - this._spherical.theta;\n    const deltaPhi = this._sphericalEnd.phi - this._spherical.phi;\n    const deltaRadius = this._sphericalEnd.radius - this._spherical.radius;\n    const deltaTarget = _deltaTarget.subVectors(this._targetEnd, this._target);\n    const deltaOffset = _deltaOffset.subVectors(this._focalOffsetEnd, this._focalOffset);\n    const deltaZoom = this._zoomEnd - this._zoom;\n    // update theta\n    if (approxZero(deltaTheta)) {\n      this._thetaVelocity.value = 0;\n      this._spherical.theta = this._sphericalEnd.theta;\n    } else {\n      const smoothTime = this._isUserControllingRotate ? this.draggingSmoothTime : this.smoothTime;\n      this._spherical.theta = smoothDamp(this._spherical.theta, this._sphericalEnd.theta, this._thetaVelocity, smoothTime, Infinity, delta);\n      this._needsUpdate = true;\n    }\n    // update phi\n    if (approxZero(deltaPhi)) {\n      this._phiVelocity.value = 0;\n      this._spherical.phi = this._sphericalEnd.phi;\n    } else {\n      const smoothTime = this._isUserControllingRotate ? this.draggingSmoothTime : this.smoothTime;\n      this._spherical.phi = smoothDamp(this._spherical.phi, this._sphericalEnd.phi, this._phiVelocity, smoothTime, Infinity, delta);\n      this._needsUpdate = true;\n    }\n    // update distance\n    if (approxZero(deltaRadius)) {\n      this._radiusVelocity.value = 0;\n      this._spherical.radius = this._sphericalEnd.radius;\n    } else {\n      const smoothTime = this._isUserControllingDolly ? this.draggingSmoothTime : this.smoothTime;\n      this._spherical.radius = smoothDamp(this._spherical.radius, this._sphericalEnd.radius, this._radiusVelocity, smoothTime, this.maxSpeed, delta);\n      this._needsUpdate = true;\n    }\n    // update target position\n    if (approxZero(deltaTarget.x) && approxZero(deltaTarget.y) && approxZero(deltaTarget.z)) {\n      this._targetVelocity.set(0, 0, 0);\n      this._target.copy(this._targetEnd);\n    } else {\n      const smoothTime = this._isUserControllingTruck ? this.draggingSmoothTime : this.smoothTime;\n      smoothDampVec3(this._target, this._targetEnd, this._targetVelocity, smoothTime, this.maxSpeed, delta, this._target);\n      this._needsUpdate = true;\n    }\n    // update focalOffset\n    if (approxZero(deltaOffset.x) && approxZero(deltaOffset.y) && approxZero(deltaOffset.z)) {\n      this._focalOffsetVelocity.set(0, 0, 0);\n      this._focalOffset.copy(this._focalOffsetEnd);\n    } else {\n      const smoothTime = this._isUserControllingOffset ? this.draggingSmoothTime : this.smoothTime;\n      smoothDampVec3(this._focalOffset, this._focalOffsetEnd, this._focalOffsetVelocity, smoothTime, this.maxSpeed, delta, this._focalOffset);\n      this._needsUpdate = true;\n    }\n    // update zoom\n    if (approxZero(deltaZoom)) {\n      this._zoomVelocity.value = 0;\n      this._zoom = this._zoomEnd;\n    } else {\n      const smoothTime = this._isUserControllingZoom ? this.draggingSmoothTime : this.smoothTime;\n      this._zoom = smoothDamp(this._zoom, this._zoomEnd, this._zoomVelocity, smoothTime, Infinity, delta);\n    }\n    if (this.dollyToCursor) {\n      if (isPerspectiveCamera(this._camera) && this._changedDolly !== 0) {\n        const dollyControlAmount = this._spherical.radius - this._lastDistance;\n        const camera = this._camera;\n        const cameraDirection = this._getCameraDirection(_cameraDirection);\n        const planeX = _v3A.copy(cameraDirection).cross(camera.up).normalize();\n        if (planeX.lengthSq() === 0) planeX.x = 1.0;\n        const planeY = _v3B.crossVectors(planeX, cameraDirection);\n        const worldToScreen = this._sphericalEnd.radius * Math.tan(camera.getEffectiveFOV() * DEG2RAD * 0.5);\n        const prevRadius = this._sphericalEnd.radius - dollyControlAmount;\n        const lerpRatio = (prevRadius - this._sphericalEnd.radius) / this._sphericalEnd.radius;\n        const cursor = _v3C.copy(this._targetEnd).add(planeX.multiplyScalar(this._dollyControlCoord.x * worldToScreen * camera.aspect)).add(planeY.multiplyScalar(this._dollyControlCoord.y * worldToScreen));\n        const newTargetEnd = _v3A.copy(this._targetEnd).lerp(cursor, lerpRatio);\n        const isMin = this._lastDollyDirection === DOLLY_DIRECTION.IN && this._spherical.radius <= this.minDistance;\n        const isMax = this._lastDollyDirection === DOLLY_DIRECTION.OUT && this.maxDistance <= this._spherical.radius;\n        if (this.infinityDolly && (isMin || isMax)) {\n          this._sphericalEnd.radius -= dollyControlAmount;\n          this._spherical.radius -= dollyControlAmount;\n          const dollyAmount = _v3B.copy(cameraDirection).multiplyScalar(-dollyControlAmount);\n          newTargetEnd.add(dollyAmount);\n        }\n        // target position may be moved beyond boundary.\n        this._boundary.clampPoint(newTargetEnd, newTargetEnd);\n        const targetEndDiff = _v3B.subVectors(newTargetEnd, this._targetEnd);\n        this._targetEnd.copy(newTargetEnd);\n        this._target.add(targetEndDiff);\n        this._changedDolly -= dollyControlAmount;\n        if (approxZero(this._changedDolly)) this._changedDolly = 0;\n      } else if (isOrthographicCamera(this._camera) && this._changedZoom !== 0) {\n        const dollyControlAmount = this._zoom - this._lastZoom;\n        const camera = this._camera;\n        const worldCursorPosition = _v3A.set(this._dollyControlCoord.x, this._dollyControlCoord.y, (camera.near + camera.far) / (camera.near - camera.far)).unproject(camera);\n        const quaternion = _v3B.set(0, 0, -1).applyQuaternion(camera.quaternion);\n        const cursor = _v3C.copy(worldCursorPosition).add(quaternion.multiplyScalar(-worldCursorPosition.dot(camera.up)));\n        const prevZoom = this._zoom - dollyControlAmount;\n        const lerpRatio = -(prevZoom - this._zoom) / this._zoom;\n        // find the \"distance\" (aka plane constant in three.js) of Plane\n        // from a given position (this._targetEnd) and normal vector (cameraDirection)\n        // https://www.maplesoft.com/support/help/maple/view.aspx?path=MathApps%2FEquationOfAPlaneNormal#bkmrk0\n        const cameraDirection = this._getCameraDirection(_cameraDirection);\n        const prevPlaneConstant = this._targetEnd.dot(cameraDirection);\n        const newTargetEnd = _v3A.copy(this._targetEnd).lerp(cursor, lerpRatio);\n        const newPlaneConstant = newTargetEnd.dot(cameraDirection);\n        // Pull back the camera depth that has moved, to be the camera stationary as zoom\n        const pullBack = cameraDirection.multiplyScalar(newPlaneConstant - prevPlaneConstant);\n        newTargetEnd.sub(pullBack);\n        // target position may be moved beyond boundary.\n        this._boundary.clampPoint(newTargetEnd, newTargetEnd);\n        const targetEndDiff = _v3B.subVectors(newTargetEnd, this._targetEnd);\n        this._targetEnd.copy(newTargetEnd);\n        this._target.add(targetEndDiff);\n        // this._target.copy( this._targetEnd );\n        this._changedZoom -= dollyControlAmount;\n        if (approxZero(this._changedZoom)) this._changedZoom = 0;\n      }\n    }\n    if (this._camera.zoom !== this._zoom) {\n      this._camera.zoom = this._zoom;\n      this._camera.updateProjectionMatrix();\n      this._updateNearPlaneCorners();\n      this._needsUpdate = true;\n    }\n    this._dragNeedsUpdate = true;\n    // collision detection\n    const maxDistance = this._collisionTest();\n    this._spherical.radius = Math.min(this._spherical.radius, maxDistance);\n    // decompose spherical to the camera position\n    this._spherical.makeSafe();\n    this._camera.position.setFromSpherical(this._spherical).applyQuaternion(this._yAxisUpSpaceInverse).add(this._target);\n    this._camera.lookAt(this._target);\n    // set offset after the orbit movement\n    const affectOffset = !approxZero(this._focalOffset.x) || !approxZero(this._focalOffset.y) || !approxZero(this._focalOffset.z);\n    if (affectOffset) {\n      _xColumn.setFromMatrixColumn(this._camera.matrix, 0);\n      _yColumn.setFromMatrixColumn(this._camera.matrix, 1);\n      _zColumn.setFromMatrixColumn(this._camera.matrix, 2);\n      _xColumn.multiplyScalar(this._focalOffset.x);\n      _yColumn.multiplyScalar(-this._focalOffset.y);\n      _zColumn.multiplyScalar(this._focalOffset.z); // notice: z-offset will not affect in Orthographic.\n      _v3A.copy(_xColumn).add(_yColumn).add(_zColumn);\n      this._camera.position.add(_v3A);\n      this._camera.updateMatrixWorld();\n    }\n    if (this._boundaryEnclosesCamera) {\n      this._encloseToBoundary(this._camera.position.copy(this._target), _v3A.setFromSpherical(this._spherical).applyQuaternion(this._yAxisUpSpaceInverse), 1.0);\n    }\n    const updated = this._needsUpdate;\n    if (updated && !this._updatedLastTime) {\n      this._hasRested = false;\n      this.dispatchEvent({\n        type: 'wake'\n      });\n      this.dispatchEvent({\n        type: 'update'\n      });\n    } else if (updated) {\n      this.dispatchEvent({\n        type: 'update'\n      });\n      if (approxZero(deltaTheta, this.restThreshold) && approxZero(deltaPhi, this.restThreshold) && approxZero(deltaRadius, this.restThreshold) && approxZero(deltaTarget.x, this.restThreshold) && approxZero(deltaTarget.y, this.restThreshold) && approxZero(deltaTarget.z, this.restThreshold) && approxZero(deltaOffset.x, this.restThreshold) && approxZero(deltaOffset.y, this.restThreshold) && approxZero(deltaOffset.z, this.restThreshold) && approxZero(deltaZoom, this.restThreshold) && !this._hasRested) {\n        this._hasRested = true;\n        this.dispatchEvent({\n          type: 'rest'\n        });\n      }\n    } else if (!updated && this._updatedLastTime) {\n      this.dispatchEvent({\n        type: 'sleep'\n      });\n    }\n    this._lastDistance = this._spherical.radius;\n    this._lastZoom = this._zoom;\n    this._updatedLastTime = updated;\n    this._needsUpdate = false;\n    return updated;\n  }\n  /**\n   * Get all state in JSON string\n   * @category Methods\n   */\n  toJSON() {\n    return JSON.stringify({\n      enabled: this._enabled,\n      minDistance: this.minDistance,\n      maxDistance: infinityToMaxNumber(this.maxDistance),\n      minZoom: this.minZoom,\n      maxZoom: infinityToMaxNumber(this.maxZoom),\n      minPolarAngle: this.minPolarAngle,\n      maxPolarAngle: infinityToMaxNumber(this.maxPolarAngle),\n      minAzimuthAngle: infinityToMaxNumber(this.minAzimuthAngle),\n      maxAzimuthAngle: infinityToMaxNumber(this.maxAzimuthAngle),\n      smoothTime: this.smoothTime,\n      draggingSmoothTime: this.draggingSmoothTime,\n      dollySpeed: this.dollySpeed,\n      truckSpeed: this.truckSpeed,\n      dollyToCursor: this.dollyToCursor,\n      target: this._targetEnd.toArray(),\n      position: _v3A.setFromSpherical(this._sphericalEnd).add(this._targetEnd).toArray(),\n      zoom: this._zoomEnd,\n      focalOffset: this._focalOffsetEnd.toArray(),\n      target0: this._target0.toArray(),\n      position0: this._position0.toArray(),\n      zoom0: this._zoom0,\n      focalOffset0: this._focalOffset0.toArray()\n    });\n  }\n  /**\n   * Reproduce the control state with JSON. enableTransition is where anim or not in a boolean.\n   * @param json\n   * @param enableTransition\n   * @category Methods\n   */\n  fromJSON(json, enableTransition = false) {\n    const obj = JSON.parse(json);\n    this.enabled = obj.enabled;\n    this.minDistance = obj.minDistance;\n    this.maxDistance = maxNumberToInfinity(obj.maxDistance);\n    this.minZoom = obj.minZoom;\n    this.maxZoom = maxNumberToInfinity(obj.maxZoom);\n    this.minPolarAngle = obj.minPolarAngle;\n    this.maxPolarAngle = maxNumberToInfinity(obj.maxPolarAngle);\n    this.minAzimuthAngle = maxNumberToInfinity(obj.minAzimuthAngle);\n    this.maxAzimuthAngle = maxNumberToInfinity(obj.maxAzimuthAngle);\n    this.smoothTime = obj.smoothTime;\n    this.draggingSmoothTime = obj.draggingSmoothTime;\n    this.dollySpeed = obj.dollySpeed;\n    this.truckSpeed = obj.truckSpeed;\n    this.dollyToCursor = obj.dollyToCursor;\n    this._target0.fromArray(obj.target0);\n    this._position0.fromArray(obj.position0);\n    this._zoom0 = obj.zoom0;\n    this._focalOffset0.fromArray(obj.focalOffset0);\n    this.moveTo(obj.target[0], obj.target[1], obj.target[2], enableTransition);\n    _sphericalA.setFromVector3(_v3A.fromArray(obj.position).sub(this._targetEnd).applyQuaternion(this._yAxisUpSpace));\n    this.rotateTo(_sphericalA.theta, _sphericalA.phi, enableTransition);\n    this.dollyTo(_sphericalA.radius, enableTransition);\n    this.zoomTo(obj.zoom, enableTransition);\n    this.setFocalOffset(obj.focalOffset[0], obj.focalOffset[1], obj.focalOffset[2], enableTransition);\n    this._needsUpdate = true;\n  }\n  /**\n   * Attach all internal event handlers to enable drag control.\n   * @category Methods\n   */\n  connect(domElement) {\n    if (this._domElement) {\n      console.warn('camera-controls is already connected.');\n      return;\n    }\n    domElement.setAttribute('data-camera-controls-version', VERSION);\n    this._addAllEventListeners(domElement);\n    this._getClientRect(this._elementRect);\n  }\n  /**\n   * Detach all internal event handlers to disable drag control.\n   */\n  disconnect() {\n    this.cancel();\n    this._removeAllEventListeners();\n    if (this._domElement) {\n      this._domElement.removeAttribute('data-camera-controls-version');\n      this._domElement = undefined;\n    }\n  }\n  /**\n   * Dispose the cameraControls instance itself, remove all eventListeners.\n   * @category Methods\n   */\n  dispose() {\n    // remove all user event listeners\n    this.removeAllEventListeners();\n    // remove all internal event listeners\n    this.disconnect();\n  }\n  // it's okay to expose public though\n  _getTargetDirection(out) {\n    // divide by distance to normalize, lighter than `Vector3.prototype.normalize()`\n    return out.setFromSpherical(this._spherical).divideScalar(this._spherical.radius).applyQuaternion(this._yAxisUpSpaceInverse);\n  }\n  // it's okay to expose public though\n  _getCameraDirection(out) {\n    return this._getTargetDirection(out).negate();\n  }\n  _findPointerById(pointerId) {\n    return this._activePointers.find(activePointer => activePointer.pointerId === pointerId);\n  }\n  _findPointerByMouseButton(mouseButton) {\n    return this._activePointers.find(activePointer => activePointer.mouseButton === mouseButton);\n  }\n  _disposePointer(pointer) {\n    this._activePointers.splice(this._activePointers.indexOf(pointer), 1);\n  }\n  _encloseToBoundary(position, offset, friction) {\n    const offsetLength2 = offset.lengthSq();\n    if (offsetLength2 === 0.0) {\n      // sanity check\n      return position;\n    }\n    // See: https://twitter.com/FMS_Cat/status/1106508958640988161\n    const newTarget = _v3B.copy(offset).add(position); // target\n    const clampedTarget = this._boundary.clampPoint(newTarget, _v3C); // clamped target\n    const deltaClampedTarget = clampedTarget.sub(newTarget); // newTarget -> clampedTarget\n    const deltaClampedTargetLength2 = deltaClampedTarget.lengthSq(); // squared length of deltaClampedTarget\n    if (deltaClampedTargetLength2 === 0.0) {\n      // when the position doesn't have to be clamped\n      return position.add(offset);\n    } else if (deltaClampedTargetLength2 === offsetLength2) {\n      // when the position is completely stuck\n      return position;\n    } else if (friction === 0.0) {\n      return position.add(offset).add(deltaClampedTarget);\n    } else {\n      const offsetFactor = 1.0 + friction * deltaClampedTargetLength2 / offset.dot(deltaClampedTarget);\n      return position.add(_v3B.copy(offset).multiplyScalar(offsetFactor)).add(deltaClampedTarget.multiplyScalar(1.0 - friction));\n    }\n  }\n  _updateNearPlaneCorners() {\n    if (isPerspectiveCamera(this._camera)) {\n      const camera = this._camera;\n      const near = camera.near;\n      const fov = camera.getEffectiveFOV() * DEG2RAD;\n      const heightHalf = Math.tan(fov * 0.5) * near; // near plain half height\n      const widthHalf = heightHalf * camera.aspect; // near plain half width\n      this._nearPlaneCorners[0].set(-widthHalf, -heightHalf, 0);\n      this._nearPlaneCorners[1].set(widthHalf, -heightHalf, 0);\n      this._nearPlaneCorners[2].set(widthHalf, heightHalf, 0);\n      this._nearPlaneCorners[3].set(-widthHalf, heightHalf, 0);\n    } else if (isOrthographicCamera(this._camera)) {\n      const camera = this._camera;\n      const zoomInv = 1 / camera.zoom;\n      const left = camera.left * zoomInv;\n      const right = camera.right * zoomInv;\n      const top = camera.top * zoomInv;\n      const bottom = camera.bottom * zoomInv;\n      this._nearPlaneCorners[0].set(left, top, 0);\n      this._nearPlaneCorners[1].set(right, top, 0);\n      this._nearPlaneCorners[2].set(right, bottom, 0);\n      this._nearPlaneCorners[3].set(left, bottom, 0);\n    }\n  }\n  // lateUpdate\n  _collisionTest() {\n    let distance = Infinity;\n    const hasCollider = this.colliderMeshes.length >= 1;\n    if (!hasCollider) return distance;\n    if (notSupportedInOrthographicCamera(this._camera, '_collisionTest')) return distance;\n    const rayDirection = this._getTargetDirection(_cameraDirection);\n    _rotationMatrix.lookAt(_ORIGIN, rayDirection, this._camera.up);\n    for (let i = 0; i < 4; i++) {\n      const nearPlaneCorner = _v3B.copy(this._nearPlaneCorners[i]);\n      nearPlaneCorner.applyMatrix4(_rotationMatrix);\n      const origin = _v3C.addVectors(this._target, nearPlaneCorner);\n      _raycaster.set(origin, rayDirection);\n      _raycaster.far = this._spherical.radius + 1;\n      const intersects = _raycaster.intersectObjects(this.colliderMeshes);\n      if (intersects.length !== 0 && intersects[0].distance < distance) {\n        distance = intersects[0].distance;\n      }\n    }\n    return distance;\n  }\n  /**\n   * Get its client rect and package into given `DOMRect` .\n   */\n  _getClientRect(target) {\n    if (!this._domElement) return;\n    const rect = this._domElement.getBoundingClientRect();\n    target.x = rect.left;\n    target.y = rect.top;\n    if (this._viewport) {\n      target.x += this._viewport.x;\n      target.y += rect.height - this._viewport.w - this._viewport.y;\n      target.width = this._viewport.z;\n      target.height = this._viewport.w;\n    } else {\n      target.width = rect.width;\n      target.height = rect.height;\n    }\n    return target;\n  }\n  _createOnRestPromise(resolveImmediately) {\n    if (resolveImmediately) return Promise.resolve();\n    this._hasRested = false;\n    this.dispatchEvent({\n      type: 'transitionstart'\n    });\n    return new Promise(resolve => {\n      const onResolve = () => {\n        this.removeEventListener('rest', onResolve);\n        resolve();\n      };\n      this.addEventListener('rest', onResolve);\n    });\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  _addAllEventListeners(_domElement) {}\n  _removeAllEventListeners() {}\n  /**\n   * backward compatible\n   * @deprecated use smoothTime (in seconds) instead\n   * @category Properties\n   */\n  get dampingFactor() {\n    console.warn('.dampingFactor has been deprecated. use smoothTime (in seconds) instead.');\n    return 0;\n  }\n  /**\n   * backward compatible\n   * @deprecated use smoothTime (in seconds) instead\n   * @category Properties\n   */\n  set dampingFactor(_) {\n    console.warn('.dampingFactor has been deprecated. use smoothTime (in seconds) instead.');\n  }\n  /**\n   * backward compatible\n   * @deprecated use draggingSmoothTime (in seconds) instead\n   * @category Properties\n   */\n  get draggingDampingFactor() {\n    console.warn('.draggingDampingFactor has been deprecated. use draggingSmoothTime (in seconds) instead.');\n    return 0;\n  }\n  /**\n   * backward compatible\n   * @deprecated use draggingSmoothTime (in seconds) instead\n   * @category Properties\n   */\n  set draggingDampingFactor(_) {\n    console.warn('.draggingDampingFactor has been deprecated. use draggingSmoothTime (in seconds) instead.');\n  }\n  static createBoundingSphere(object3d, out = new THREE.Sphere()) {\n    const boundingSphere = out;\n    const center = boundingSphere.center;\n    _box3A.makeEmpty();\n    // find the center\n    object3d.traverseVisible(object => {\n      if (!object.isMesh) return;\n      _box3A.expandByObject(object);\n    });\n    _box3A.getCenter(center);\n    // find the radius\n    let maxRadiusSq = 0;\n    object3d.traverseVisible(object => {\n      if (!object.isMesh) return;\n      const mesh = object;\n      if (!mesh.geometry) return;\n      const geometry = mesh.geometry.clone();\n      geometry.applyMatrix4(mesh.matrixWorld);\n      const bufferGeometry = geometry;\n      const position = bufferGeometry.attributes.position;\n      for (let i = 0, l = position.count; i < l; i++) {\n        _v3A.fromBufferAttribute(position, i);\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_v3A));\n      }\n    });\n    boundingSphere.radius = Math.sqrt(maxRadiusSq);\n    return boundingSphere;\n  }\n}\nexport { EventDispatcher, CameraControls as default };", "map": {"version": 3, "names": ["MOUSE_BUTTON", "LEFT", "RIGHT", "MIDDLE", "ACTION", "Object", "freeze", "NONE", "ROTATE", "TRUCK", "SCREEN_PAN", "OFFSET", "DOLLY", "ZOOM", "TOUCH_ROTATE", "TOUCH_TRUCK", "TOUCH_SCREEN_PAN", "TOUCH_OFFSET", "TOUCH_DOLLY", "TOUCH_ZOOM", "TOUCH_DOLLY_TRUCK", "TOUCH_DOLLY_SCREEN_PAN", "TOUCH_DOLLY_OFFSET", "TOUCH_DOLLY_ROTATE", "TOUCH_ZOOM_TRUCK", "TOUCH_ZOOM_OFFSET", "TOUCH_ZOOM_SCREEN_PAN", "TOUCH_ZOOM_ROTATE", "DOLLY_DIRECTION", "IN", "OUT", "isPerspectiveCamera", "camera", "isOrthographicCamera", "PI_2", "Math", "PI", "PI_HALF", "EPSILON", "DEG2RAD", "clamp", "value", "min", "max", "approxZero", "number", "error", "abs", "approxEquals", "a", "b", "roundToStep", "step", "round", "infinityToMaxNumber", "isFinite", "Number", "MAX_VALUE", "maxNumberToInfinity", "Infinity", "smoothDamp", "current", "target", "currentVelocityRef", "smoothTime", "maxSpeed", "deltaTime", "omega", "x", "exp", "change", "originalTo", "max<PERSON><PERSON><PERSON>", "temp", "output", "smoothDampVec3", "out", "targetX", "targetY", "y", "targetZ", "z", "changeX", "changeY", "changeZ", "originalToX", "originalToY", "originalToZ", "maxChangeSq", "magnitudeSq", "magnitude", "sqrt", "tempX", "tempY", "tempZ", "origMinusCurrentX", "origMinusCurrentY", "origMinusCurrentZ", "outMinusOrigX", "outMinusOrigY", "outMinusOrigZ", "extractClientCoordFromEvent", "pointers", "set", "for<PERSON>ach", "pointer", "clientX", "clientY", "length", "notSupportedInOrthographicCamera", "message", "console", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_listeners", "addEventListener", "type", "listener", "listeners", "undefined", "indexOf", "push", "hasEventListener", "removeEventListener", "listenerArray", "index", "splice", "removeAllEventListeners", "Array", "isArray", "dispatchEvent", "event", "array", "slice", "i", "l", "call", "_a", "VERSION", "TOUCH_DOLLY_FACTOR", "isMac", "test", "globalThis", "navigator", "platform", "THREE", "_ORIGIN", "_AXIS_Y", "_AXIS_Z", "_v2", "_v3A", "_v3B", "_v3C", "_cameraDirection", "_xColumn", "_yColumn", "_zColumn", "_deltaTarget", "_deltaOffset", "_sphericalA", "_sphericalB", "_box3A", "_box3B", "_sphere", "_quaternionA", "_quaternionB", "_rotationMatrix", "_raycaster", "CameraControls", "install", "libs", "Vector3", "Vector2", "Spherical", "Box3", "Sphere", "Quaternion", "Matrix4", "Raycaster", "verticalDragToForward", "_", "dom<PERSON>lement", "minPolarAngle", "maxPolarAngle", "minAzimuthAngle", "maxAzimuthAngle", "minDistance", "maxDistance", "infinityDolly", "minZoom", "max<PERSON><PERSON>", "draggingSmoothTime", "azimuthRotateSpeed", "polarRotateSpeed", "dollySpeed", "dollyDragInverted", "truckSpeed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragToOffset", "boundaryFriction", "restThreshold", "collider<PERSON><PERSON><PERSON>", "cancel", "_enabled", "_state", "_viewport", "_changedDolly", "_changedZoom", "_hasRested", "_boundaryEnclosesCamera", "_needsUpdate", "_updatedLastTime", "_elementRect", "DOMRect", "_isDragging", "_dragNeedsUpdate", "_activePointers", "_lockedPointer", "_interactiveArea", "_isUserControllingRotate", "_isUserControllingDolly", "_isUserControllingTruck", "_isUserControllingOffset", "_isUserControllingZoom", "_lastDollyDirection", "_thetaVelocity", "_phiVelocity", "_radiusVelocity", "_targetVelocity", "_focalOffsetVelocity", "_zoomVelocity", "_truckInternal", "deltaX", "deltaY", "screenSpacePanning", "truckX", "pedestalY", "_camera", "offset", "copy", "position", "sub", "_target", "fov", "getEffectiveFOV", "targetDistance", "tan", "height", "right", "left", "zoom", "width", "top", "bottom", "setFocalOffset", "_focalOffsetEnd", "truck", "forward", "_rotateInternal", "theta", "phi", "rotate", "_dollyInternal", "delta", "dollyScale", "pow", "lastDistance", "_sphericalEnd", "radius", "distance", "clampedDistance", "overflowedDistance", "_dollyToNoClamp", "dollyInFixed", "_dollyControlCoord", "sign", "_zoomInternal", "zoomScale", "lastZoom", "_zoom", "zoomTo", "_yAxisUpSpace", "setFromUnitVectors", "up", "_yAxisUpSpaceInverse", "clone", "invert", "_targetEnd", "_focalOffset", "_spherical", "setFromVector3", "applyQuaternion", "_lastDistance", "_zoomEnd", "_last<PERSON><PERSON>", "_nearPlaneCorners", "_updateNearPlaneCorners", "_boundary", "_cameraUp0", "_target0", "_position0", "_zoom0", "_focalOffset0", "mouseButtons", "middle", "wheel", "touches", "one", "two", "three", "dragStartPosition", "lastDragPosition", "dolly<PERSON><PERSON><PERSON>", "onPointerDown", "_domElement", "elRect", "getBoundingClientRect", "mouseButton", "pointerType", "buttons", "zombiePointer", "_findPointerByMouseButton", "_disposePointer", "pointerId", "ownerDocument", "onPointerMove", "passive", "onPointerUp", "startDragging", "cancelable", "preventDefault", "_findPointerById", "movementX", "movementY", "dragging", "endDragging", "lastScrollTimeStamp", "onMouseWheel", "now", "performance", "_getClientRect", "deltaYFactor", "deltaMode", "ctrl<PERSON>ey", "onContextMenu", "PointerEvent", "isMultiTouch", "dx", "dy", "isPointerLockActive", "pointerLockElement", "lockedPointer", "dollyX", "dollyY", "dollyDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lockPointer", "requestPointerLock", "onPointerLockChange", "onPointerLockError", "unlockPointer", "_b", "_c", "exitPointerLock", "_addAllEventListeners", "style", "touchAction", "userSelect", "webkitUserSelect", "_removeAllEventListeners", "connect", "update", "updateCameraUp", "updateProjectionMatrix", "enabled", "active", "currentAction", "azimuthAngle", "polarAngle", "boundaryEnclosesCamera", "interactiveArea", "enableTransition", "rotateTo", "rotateAzimuthTo", "rotatePolarTo", "makeSafe", "resolveImmediately", "_createOnRestPromise", "dolly", "dolly<PERSON>o", "lastRadius", "hasCollider", "maxDistanceByCollisionTest", "_collisionTest", "isCollided", "isDollyIn", "Promise", "resolve", "add", "_getCameraDirection", "multiplyScalar", "zoomStep", "pan", "updateMatrix", "setFromMatrixColumn", "matrix", "to", "moveTo", "crossVectors", "elevate", "_encloseToBoundary", "lookInDirectionOf", "point", "direction", "normalize", "setPosition", "fitToBox", "box3OrObject", "cover", "paddingLeft", "paddingRight", "paddingBottom", "paddingTop", "promises", "aabb", "isBox3", "setFromObject", "isEmpty", "normal", "setFromSpherical", "rotation", "viewFromPolar", "multiply", "setFromAxisAngle", "bb", "makeEmpty", "expandByPoint", "setX", "setY", "setZ", "premultiply", "bbSize", "getSize", "center", "getCenter", "getDistanceToFitBox", "all", "fitToSphere", "sphere<PERSON><PERSON><PERSON><PERSON>", "isObject3D", "boundingSphere", "createBoundingSphere", "distanceToFit", "getDistanceToFitSphere", "diameter", "setLookAt", "positionX", "positionY", "positionZ", "normalizeRotations", "lerpLookAt", "positionAX", "positionAY", "positionAZ", "targetAX", "targetAY", "targetAZ", "positionBX", "positionBY", "positionBZ", "targetBX", "targetBY", "targetBZ", "t", "targetA", "positionA", "targetB", "positionB", "lerp", "deltaTheta", "deltaPhi", "deltaRadius", "<PERSON><PERSON><PERSON><PERSON>", "pos", "getPosition", "promise", "setOrbitPoint", "updateMatrixWorld", "matrixWorldInverse", "distanceTo", "cameraToPoint", "setBoundary", "box3", "clampPoint", "setViewport", "viewportOrX", "Vector4", "depth", "boundingRectAspect", "aspect", "heightToFit", "vFOV", "hFOV", "atan", "sin", "get<PERSON><PERSON><PERSON>", "receiveEndValue", "_out", "isVector3", "getSpherical", "getFocalOffset", "stop", "reset", "saveState", "applyCameraUp", "cameraDirection", "subVectors", "side", "deltaTarget", "deltaOffset", "deltaZoom", "dollyControlAmount", "planeX", "cross", "lengthSq", "planeY", "worldToScreen", "prevRadius", "lerpRatio", "cursor", "newTargetEnd", "isMin", "isMax", "dolly<PERSON><PERSON>", "targetEndDiff", "worldCursorPosition", "near", "far", "unproject", "quaternion", "dot", "prevZoom", "prevPlaneConstant", "newPlaneConstant", "pullBack", "lookAt", "affectOffset", "updated", "toJSON", "JSON", "stringify", "toArray", "focalOffset", "target0", "position0", "zoom0", "focalOffset0", "fromJSON", "json", "obj", "parse", "fromArray", "setAttribute", "disconnect", "removeAttribute", "dispose", "_getTargetDirection", "divideScalar", "negate", "find", "activePointer", "friction", "offsetLength2", "newTarget", "<PERSON><PERSON><PERSON><PERSON>", "deltaClampedTarget", "deltaClampedTargetLength2", "offsetFactor", "heightHalf", "widthHalf", "zoomInv", "rayDirection", "nearPlaneCorner", "applyMatrix4", "origin", "addVectors", "intersects", "intersectObjects", "rect", "w", "onResolve", "dampingFactor", "draggingDampingFactor", "object3d", "traverseVisible", "object", "<PERSON><PERSON><PERSON>", "expandByObject", "maxRadiusSq", "mesh", "geometry", "matrixWorld", "bufferGeometry", "attributes", "count", "fromBufferAttribute", "distanceToSquared", "default"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/camera-controls/dist/camera-controls.module.js"], "sourcesContent": ["/*!\n * camera-controls\n * https://github.com/yomotsu/camera-controls\n * (c) 2017 @yomotsu\n * Released under the MIT License.\n */\n// see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#value\nconst MOUSE_BUTTON = {\n    LEFT: 1,\n    RIGHT: 2,\n    MIDDLE: 4,\n};\nconst ACTION = Object.freeze({\n    NONE: 0b0,\n    ROTATE: 0b1,\n    TRUCK: 0b10,\n    SCREEN_PAN: 0b100,\n    OFFSET: 0b1000,\n    DOLLY: 0b10000,\n    ZOOM: 0b100000,\n    TOUCH_ROTATE: 0b1000000,\n    TOUCH_TRUCK: 0b10000000,\n    TOUCH_SCREEN_PAN: 0b100000000,\n    TOUCH_OFFSET: 0b1000000000,\n    TOUCH_DOLLY: 0b10000000000,\n    TOUCH_ZOOM: 0b100000000000,\n    TOUCH_DOLLY_TRUCK: 0b1000000000000,\n    TOUCH_DOLLY_SCREEN_PAN: 0b10000000000000,\n    TOUCH_DOLLY_OFFSET: 0b100000000000000,\n    TOUCH_DOLLY_ROTATE: 0b1000000000000000,\n    TOUCH_ZOOM_TRUCK: 0b10000000000000000,\n    TOUCH_ZOOM_OFFSET: 0b100000000000000000,\n    TOUCH_ZOOM_SCREEN_PAN: 0b1000000000000000000,\n    TOUCH_ZOOM_ROTATE: 0b10000000000000000000,\n});\nconst DOLLY_DIRECTION = {\n    NONE: 0,\n    IN: 1,\n    OUT: -1,\n};\nfunction isPerspectiveCamera(camera) {\n    return camera.isPerspectiveCamera;\n}\nfunction isOrthographicCamera(camera) {\n    return camera.isOrthographicCamera;\n}\n\nconst PI_2 = Math.PI * 2;\nconst PI_HALF = Math.PI / 2;\n\nconst EPSILON = 1e-5;\nconst DEG2RAD = Math.PI / 180;\nfunction clamp(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\nfunction approxZero(number, error = EPSILON) {\n    return Math.abs(number) < error;\n}\nfunction approxEquals(a, b, error = EPSILON) {\n    return approxZero(a - b, error);\n}\nfunction roundToStep(value, step) {\n    return Math.round(value / step) * step;\n}\nfunction infinityToMaxNumber(value) {\n    if (isFinite(value))\n        return value;\n    if (value < 0)\n        return -Number.MAX_VALUE;\n    return Number.MAX_VALUE;\n}\nfunction maxNumberToInfinity(value) {\n    if (Math.abs(value) < Number.MAX_VALUE)\n        return value;\n    return value * Infinity;\n}\n// https://docs.unity3d.com/ScriptReference/Mathf.SmoothDamp.html\n// https://github.com/Unity-Technologies/UnityCsReference/blob/a2bdfe9b3c4cd4476f44bf52f848063bfaf7b6b9/Runtime/Export/Math/Mathf.cs#L308\nfunction smoothDamp(current, target, currentVelocityRef, smoothTime, maxSpeed = Infinity, deltaTime) {\n    // Based on Game Programming Gems 4 Chapter 1.10\n    smoothTime = Math.max(0.0001, smoothTime);\n    const omega = 2 / smoothTime;\n    const x = omega * deltaTime;\n    const exp = 1 / (1 + x + 0.48 * x * x + 0.235 * x * x * x);\n    let change = current - target;\n    const originalTo = target;\n    // Clamp maximum speed\n    const maxChange = maxSpeed * smoothTime;\n    change = clamp(change, -maxChange, maxChange);\n    target = current - change;\n    const temp = (currentVelocityRef.value + omega * change) * deltaTime;\n    currentVelocityRef.value = (currentVelocityRef.value - omega * temp) * exp;\n    let output = target + (change + temp) * exp;\n    // Prevent overshooting\n    if (originalTo - current > 0.0 === output > originalTo) {\n        output = originalTo;\n        currentVelocityRef.value = (output - originalTo) / deltaTime;\n    }\n    return output;\n}\n// https://docs.unity3d.com/ScriptReference/Vector3.SmoothDamp.html\n// https://github.com/Unity-Technologies/UnityCsReference/blob/a2bdfe9b3c4cd4476f44bf52f848063bfaf7b6b9/Runtime/Export/Math/Vector3.cs#L97\nfunction smoothDampVec3(current, target, currentVelocityRef, smoothTime, maxSpeed = Infinity, deltaTime, out) {\n    // Based on Game Programming Gems 4 Chapter 1.10\n    smoothTime = Math.max(0.0001, smoothTime);\n    const omega = 2 / smoothTime;\n    const x = omega * deltaTime;\n    const exp = 1 / (1 + x + 0.48 * x * x + 0.235 * x * x * x);\n    let targetX = target.x;\n    let targetY = target.y;\n    let targetZ = target.z;\n    let changeX = current.x - targetX;\n    let changeY = current.y - targetY;\n    let changeZ = current.z - targetZ;\n    const originalToX = targetX;\n    const originalToY = targetY;\n    const originalToZ = targetZ;\n    // Clamp maximum speed\n    const maxChange = maxSpeed * smoothTime;\n    const maxChangeSq = maxChange * maxChange;\n    const magnitudeSq = changeX * changeX + changeY * changeY + changeZ * changeZ;\n    if (magnitudeSq > maxChangeSq) {\n        const magnitude = Math.sqrt(magnitudeSq);\n        changeX = changeX / magnitude * maxChange;\n        changeY = changeY / magnitude * maxChange;\n        changeZ = changeZ / magnitude * maxChange;\n    }\n    targetX = current.x - changeX;\n    targetY = current.y - changeY;\n    targetZ = current.z - changeZ;\n    const tempX = (currentVelocityRef.x + omega * changeX) * deltaTime;\n    const tempY = (currentVelocityRef.y + omega * changeY) * deltaTime;\n    const tempZ = (currentVelocityRef.z + omega * changeZ) * deltaTime;\n    currentVelocityRef.x = (currentVelocityRef.x - omega * tempX) * exp;\n    currentVelocityRef.y = (currentVelocityRef.y - omega * tempY) * exp;\n    currentVelocityRef.z = (currentVelocityRef.z - omega * tempZ) * exp;\n    out.x = targetX + (changeX + tempX) * exp;\n    out.y = targetY + (changeY + tempY) * exp;\n    out.z = targetZ + (changeZ + tempZ) * exp;\n    // Prevent overshooting\n    const origMinusCurrentX = originalToX - current.x;\n    const origMinusCurrentY = originalToY - current.y;\n    const origMinusCurrentZ = originalToZ - current.z;\n    const outMinusOrigX = out.x - originalToX;\n    const outMinusOrigY = out.y - originalToY;\n    const outMinusOrigZ = out.z - originalToZ;\n    if (origMinusCurrentX * outMinusOrigX + origMinusCurrentY * outMinusOrigY + origMinusCurrentZ * outMinusOrigZ > 0) {\n        out.x = originalToX;\n        out.y = originalToY;\n        out.z = originalToZ;\n        currentVelocityRef.x = (out.x - originalToX) / deltaTime;\n        currentVelocityRef.y = (out.y - originalToY) / deltaTime;\n        currentVelocityRef.z = (out.z - originalToZ) / deltaTime;\n    }\n    return out;\n}\n\nfunction extractClientCoordFromEvent(pointers, out) {\n    out.set(0, 0);\n    pointers.forEach((pointer) => {\n        out.x += pointer.clientX;\n        out.y += pointer.clientY;\n    });\n    out.x /= pointers.length;\n    out.y /= pointers.length;\n}\n\nfunction notSupportedInOrthographicCamera(camera, message) {\n    if (isOrthographicCamera(camera)) {\n        console.warn(`${message} is not supported in OrthographicCamera`);\n        return true;\n    }\n    return false;\n}\n\nclass EventDispatcher {\n    constructor() {\n        this._listeners = {};\n    }\n    /**\n     * Adds the specified event listener.\n     * @param type event name\n     * @param listener handler function\n     * @category Methods\n     */\n    addEventListener(type, listener) {\n        const listeners = this._listeners;\n        if (listeners[type] === undefined)\n            listeners[type] = [];\n        if (listeners[type].indexOf(listener) === -1)\n            listeners[type].push(listener);\n    }\n    /**\n     * Presence of the specified event listener.\n     * @param type event name\n     * @param listener handler function\n     * @category Methods\n     */\n    hasEventListener(type, listener) {\n        const listeners = this._listeners;\n        return listeners[type] !== undefined && listeners[type].indexOf(listener) !== -1;\n    }\n    /**\n     * Removes the specified event listener\n     * @param type event name\n     * @param listener handler function\n     * @category Methods\n     */\n    removeEventListener(type, listener) {\n        const listeners = this._listeners;\n        const listenerArray = listeners[type];\n        if (listenerArray !== undefined) {\n            const index = listenerArray.indexOf(listener);\n            if (index !== -1)\n                listenerArray.splice(index, 1);\n        }\n    }\n    /**\n     * Removes all event listeners\n     * @param type event name\n     * @category Methods\n     */\n    removeAllEventListeners(type) {\n        if (!type) {\n            this._listeners = {};\n            return;\n        }\n        if (Array.isArray(this._listeners[type]))\n            this._listeners[type].length = 0;\n    }\n    /**\n     * Fire an event type.\n     * @param event DispatcherEvent\n     * @category Methods\n     */\n    dispatchEvent(event) {\n        const listeners = this._listeners;\n        const listenerArray = listeners[event.type];\n        if (listenerArray !== undefined) {\n            event.target = this;\n            const array = listenerArray.slice(0);\n            for (let i = 0, l = array.length; i < l; i++) {\n                array[i].call(this, event);\n            }\n        }\n    }\n}\n\nvar _a;\nconst VERSION = '2.10.1'; // will be replaced with `version` in package.json during the build process.\nconst TOUCH_DOLLY_FACTOR = 1 / 8;\nconst isMac = /Mac/.test((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.platform);\nlet THREE;\nlet _ORIGIN;\nlet _AXIS_Y;\nlet _AXIS_Z;\nlet _v2;\nlet _v3A;\nlet _v3B;\nlet _v3C;\nlet _cameraDirection;\nlet _xColumn;\nlet _yColumn;\nlet _zColumn;\nlet _deltaTarget;\nlet _deltaOffset;\nlet _sphericalA;\nlet _sphericalB;\nlet _box3A;\nlet _box3B;\nlet _sphere;\nlet _quaternionA;\nlet _quaternionB;\nlet _rotationMatrix;\nlet _raycaster;\nclass CameraControls extends EventDispatcher {\n    /**\n     * Injects THREE as the dependency. You can then proceed to use CameraControls.\n     *\n     * e.g\n     * ```javascript\n     * CameraControls.install( { THREE: THREE } );\n     * ```\n     *\n     * Note: If you do not wish to use enter three.js to reduce file size(tree-shaking for example), make a subset to install.\n     *\n     * ```js\n     * import {\n     * \tVector2,\n     * \tVector3,\n     * \tVector4,\n     * \tQuaternion,\n     * \tMatrix4,\n     * \tSpherical,\n     * \tBox3,\n     * \tSphere,\n     * \tRaycaster,\n     * \tMathUtils,\n     * } from 'three';\n     *\n     * const subsetOfTHREE = {\n     * \tVector2   : Vector2,\n     * \tVector3   : Vector3,\n     * \tVector4   : Vector4,\n     * \tQuaternion: Quaternion,\n     * \tMatrix4   : Matrix4,\n     * \tSpherical : Spherical,\n     * \tBox3      : Box3,\n     * \tSphere    : Sphere,\n     * \tRaycaster : Raycaster,\n     * };\n\n     * CameraControls.install( { THREE: subsetOfTHREE } );\n     * ```\n     * @category Statics\n     */\n    static install(libs) {\n        THREE = libs.THREE;\n        _ORIGIN = Object.freeze(new THREE.Vector3(0, 0, 0));\n        _AXIS_Y = Object.freeze(new THREE.Vector3(0, 1, 0));\n        _AXIS_Z = Object.freeze(new THREE.Vector3(0, 0, 1));\n        _v2 = new THREE.Vector2();\n        _v3A = new THREE.Vector3();\n        _v3B = new THREE.Vector3();\n        _v3C = new THREE.Vector3();\n        _cameraDirection = new THREE.Vector3();\n        _xColumn = new THREE.Vector3();\n        _yColumn = new THREE.Vector3();\n        _zColumn = new THREE.Vector3();\n        _deltaTarget = new THREE.Vector3();\n        _deltaOffset = new THREE.Vector3();\n        _sphericalA = new THREE.Spherical();\n        _sphericalB = new THREE.Spherical();\n        _box3A = new THREE.Box3();\n        _box3B = new THREE.Box3();\n        _sphere = new THREE.Sphere();\n        _quaternionA = new THREE.Quaternion();\n        _quaternionB = new THREE.Quaternion();\n        _rotationMatrix = new THREE.Matrix4();\n        _raycaster = new THREE.Raycaster();\n    }\n    /**\n     * list all ACTIONs\n     * @category Statics\n     */\n    static get ACTION() {\n        return ACTION;\n    }\n    /**\n     * @deprecated Use `cameraControls.mouseButtons.left = CameraControls.ACTION.SCREEN_PAN` instead.\n     */\n    set verticalDragToForward(_) {\n        console.warn('camera-controls: `verticalDragToForward` was removed. Use `mouseButtons.left = CameraControls.ACTION.SCREEN_PAN` instead.');\n    }\n    /**\n     * Creates a `CameraControls` instance.\n     *\n     * Note:\n     * You **must install** three.js before using camera-controls. see [#install](#install)\n     * Not doing so will lead to runtime errors (`undefined` references to THREE).\n     *\n     * e.g.\n     * ```\n     * CameraControls.install( { THREE } );\n     * const cameraControls = new CameraControls( camera, domElement );\n     * ```\n     *\n     * @param camera A `THREE.PerspectiveCamera` or `THREE.OrthographicCamera` to be controlled.\n     * @param domElement A `HTMLElement` for the draggable area, usually `renderer.domElement`.\n     * @category Constructor\n     */\n    constructor(camera, domElement) {\n        super();\n        /**\n         * Minimum vertical angle in radians.\n         * The angle has to be between `0` and `.maxPolarAngle` inclusive.\n         * The default value is `0`.\n         *\n         * e.g.\n         * ```\n         * cameraControls.maxPolarAngle = 0;\n         * ```\n         * @category Properties\n         */\n        this.minPolarAngle = 0; // radians\n        /**\n         * Maximum vertical angle in radians.\n         * The angle has to be between `.maxPolarAngle` and `Math.PI` inclusive.\n         * The default value is `Math.PI`.\n         *\n         * e.g.\n         * ```\n         * cameraControls.maxPolarAngle = Math.PI;\n         * ```\n         * @category Properties\n         */\n        this.maxPolarAngle = Math.PI; // radians\n        /**\n         * Minimum horizontal angle in radians.\n         * The angle has to be less than `.maxAzimuthAngle`.\n         * The default value is `- Infinity`.\n         *\n         * e.g.\n         * ```\n         * cameraControls.minAzimuthAngle = - Infinity;\n         * ```\n         * @category Properties\n         */\n        this.minAzimuthAngle = -Infinity; // radians\n        /**\n         * Maximum horizontal angle in radians.\n         * The angle has to be greater than `.minAzimuthAngle`.\n         * The default value is `Infinity`.\n         *\n         * e.g.\n         * ```\n         * cameraControls.maxAzimuthAngle = Infinity;\n         * ```\n         * @category Properties\n         */\n        this.maxAzimuthAngle = Infinity; // radians\n        // How far you can dolly in and out ( PerspectiveCamera only )\n        /**\n         * Minimum distance for dolly. The value must be higher than `0`. Default is `Number.EPSILON`.\n         * PerspectiveCamera only.\n         * @category Properties\n         */\n        this.minDistance = Number.EPSILON;\n        /**\n         * Maximum distance for dolly. The value must be higher than `minDistance`. Default is `Infinity`.\n         * PerspectiveCamera only.\n         * @category Properties\n         */\n        this.maxDistance = Infinity;\n        /**\n         * `true` to enable Infinity Dolly for wheel and pinch. Use this with `minDistance` and `maxDistance`\n         * If the Dolly distance is less (or over) than the `minDistance` (or `maxDistance`), `infinityDolly` will keep the distance and pushes the target position instead.\n         * @category Properties\n         */\n        this.infinityDolly = false;\n        /**\n         * Minimum camera zoom.\n         * @category Properties\n         */\n        this.minZoom = 0.01;\n        /**\n         * Maximum camera zoom.\n         * @category Properties\n         */\n        this.maxZoom = Infinity;\n        /**\n         * Approximate time in seconds to reach the target. A smaller value will reach the target faster.\n         * @category Properties\n         */\n        this.smoothTime = 0.25;\n        /**\n         * the smoothTime while dragging\n         * @category Properties\n         */\n        this.draggingSmoothTime = 0.125;\n        /**\n         * Max transition speed in unit-per-seconds\n         * @category Properties\n         */\n        this.maxSpeed = Infinity;\n        /**\n         * Speed of azimuth (horizontal) rotation.\n         * @category Properties\n         */\n        this.azimuthRotateSpeed = 1.0;\n        /**\n         * Speed of polar (vertical) rotation.\n         * @category Properties\n         */\n        this.polarRotateSpeed = 1.0;\n        /**\n         * Speed of mouse-wheel dollying.\n         * @category Properties\n         */\n        this.dollySpeed = 1.0;\n        /**\n         * `true` to invert direction when dollying or zooming via drag\n         * @category Properties\n         */\n        this.dollyDragInverted = false;\n        /**\n         * Speed of drag for truck and pedestal.\n         * @category Properties\n         */\n        this.truckSpeed = 2.0;\n        /**\n         * `true` to enable Dolly-in to the mouse cursor coords.\n         * @category Properties\n         */\n        this.dollyToCursor = false;\n        /**\n         * @category Properties\n         */\n        this.dragToOffset = false;\n        /**\n         * Friction ratio of the boundary.\n         * @category Properties\n         */\n        this.boundaryFriction = 0.0;\n        /**\n         * Controls how soon the `rest` event fires as the camera slows.\n         * @category Properties\n         */\n        this.restThreshold = 0.01;\n        /**\n         * An array of Meshes to collide with camera.\n         * Be aware colliderMeshes may decrease performance. The collision test uses 4 raycasters from the camera since the near plane has 4 corners.\n         * @category Properties\n         */\n        this.colliderMeshes = [];\n        /**\n         * Force cancel user dragging.\n         * @category Methods\n         */\n        // cancel will be overwritten in the constructor.\n        this.cancel = () => { };\n        this._enabled = true;\n        this._state = ACTION.NONE;\n        this._viewport = null;\n        this._changedDolly = 0;\n        this._changedZoom = 0;\n        this._hasRested = true;\n        this._boundaryEnclosesCamera = false;\n        this._needsUpdate = true;\n        this._updatedLastTime = false;\n        this._elementRect = new DOMRect();\n        this._isDragging = false;\n        this._dragNeedsUpdate = true;\n        this._activePointers = [];\n        this._lockedPointer = null;\n        this._interactiveArea = new DOMRect(0, 0, 1, 1);\n        // Use draggingSmoothTime over smoothTime while true.\n        // set automatically true on user-dragging start.\n        // set automatically false on programmable methods call.\n        this._isUserControllingRotate = false;\n        this._isUserControllingDolly = false;\n        this._isUserControllingTruck = false;\n        this._isUserControllingOffset = false;\n        this._isUserControllingZoom = false;\n        this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n        // velocities for smoothDamp\n        this._thetaVelocity = { value: 0 };\n        this._phiVelocity = { value: 0 };\n        this._radiusVelocity = { value: 0 };\n        this._targetVelocity = new THREE.Vector3();\n        this._focalOffsetVelocity = new THREE.Vector3();\n        this._zoomVelocity = { value: 0 };\n        this._truckInternal = (deltaX, deltaY, dragToOffset, screenSpacePanning) => {\n            let truckX;\n            let pedestalY;\n            if (isPerspectiveCamera(this._camera)) {\n                const offset = _v3A.copy(this._camera.position).sub(this._target);\n                // half of the fov is center to top of screen\n                const fov = this._camera.getEffectiveFOV() * DEG2RAD;\n                const targetDistance = offset.length() * Math.tan(fov * 0.5);\n                truckX = (this.truckSpeed * deltaX * targetDistance / this._elementRect.height);\n                pedestalY = (this.truckSpeed * deltaY * targetDistance / this._elementRect.height);\n            }\n            else if (isOrthographicCamera(this._camera)) {\n                const camera = this._camera;\n                truckX = this.truckSpeed * deltaX * (camera.right - camera.left) / camera.zoom / this._elementRect.width;\n                pedestalY = this.truckSpeed * deltaY * (camera.top - camera.bottom) / camera.zoom / this._elementRect.height;\n            }\n            else {\n                return;\n            }\n            if (screenSpacePanning) {\n                dragToOffset ?\n                    this.setFocalOffset(this._focalOffsetEnd.x + truckX, this._focalOffsetEnd.y, this._focalOffsetEnd.z, true) :\n                    this.truck(truckX, 0, true);\n                this.forward(-pedestalY, true);\n            }\n            else {\n                dragToOffset ?\n                    this.setFocalOffset(this._focalOffsetEnd.x + truckX, this._focalOffsetEnd.y + pedestalY, this._focalOffsetEnd.z, true) :\n                    this.truck(truckX, pedestalY, true);\n            }\n        };\n        this._rotateInternal = (deltaX, deltaY) => {\n            const theta = PI_2 * this.azimuthRotateSpeed * deltaX / this._elementRect.height; // divide by *height* to refer the resolution\n            const phi = PI_2 * this.polarRotateSpeed * deltaY / this._elementRect.height;\n            this.rotate(theta, phi, true);\n        };\n        this._dollyInternal = (delta, x, y) => {\n            const dollyScale = Math.pow(0.95, -delta * this.dollySpeed);\n            const lastDistance = this._sphericalEnd.radius;\n            const distance = this._sphericalEnd.radius * dollyScale;\n            const clampedDistance = clamp(distance, this.minDistance, this.maxDistance);\n            const overflowedDistance = clampedDistance - distance;\n            if (this.infinityDolly && this.dollyToCursor) {\n                this._dollyToNoClamp(distance, true);\n            }\n            else if (this.infinityDolly && !this.dollyToCursor) {\n                this.dollyInFixed(overflowedDistance, true);\n                this._dollyToNoClamp(clampedDistance, true);\n            }\n            else {\n                this._dollyToNoClamp(clampedDistance, true);\n            }\n            if (this.dollyToCursor) {\n                this._changedDolly += (this.infinityDolly ? distance : clampedDistance) - lastDistance;\n                this._dollyControlCoord.set(x, y);\n            }\n            this._lastDollyDirection = Math.sign(-delta);\n        };\n        this._zoomInternal = (delta, x, y) => {\n            const zoomScale = Math.pow(0.95, delta * this.dollySpeed);\n            const lastZoom = this._zoom;\n            const zoom = this._zoom * zoomScale;\n            // for both PerspectiveCamera and OrthographicCamera\n            this.zoomTo(zoom, true);\n            if (this.dollyToCursor) {\n                this._changedZoom += zoom - lastZoom;\n                this._dollyControlCoord.set(x, y);\n            }\n        };\n        // Check if the user has installed THREE\n        if (typeof THREE === 'undefined') {\n            console.error('camera-controls: `THREE` is undefined. You must first run `CameraControls.install( { THREE: THREE } )`. Check the docs for further information.');\n        }\n        this._camera = camera;\n        this._yAxisUpSpace = new THREE.Quaternion().setFromUnitVectors(this._camera.up, _AXIS_Y);\n        this._yAxisUpSpaceInverse = this._yAxisUpSpace.clone().invert();\n        this._state = ACTION.NONE;\n        // the location\n        this._target = new THREE.Vector3();\n        this._targetEnd = this._target.clone();\n        this._focalOffset = new THREE.Vector3();\n        this._focalOffsetEnd = this._focalOffset.clone();\n        // rotation\n        this._spherical = new THREE.Spherical().setFromVector3(_v3A.copy(this._camera.position).applyQuaternion(this._yAxisUpSpace));\n        this._sphericalEnd = this._spherical.clone();\n        this._lastDistance = this._spherical.radius;\n        this._zoom = this._camera.zoom;\n        this._zoomEnd = this._zoom;\n        this._lastZoom = this._zoom;\n        // collisionTest uses nearPlane.s\n        this._nearPlaneCorners = [\n            new THREE.Vector3(),\n            new THREE.Vector3(),\n            new THREE.Vector3(),\n            new THREE.Vector3(),\n        ];\n        this._updateNearPlaneCorners();\n        // Target cannot move outside of this box\n        this._boundary = new THREE.Box3(new THREE.Vector3(-Infinity, -Infinity, -Infinity), new THREE.Vector3(Infinity, Infinity, Infinity));\n        // reset\n        this._cameraUp0 = this._camera.up.clone();\n        this._target0 = this._target.clone();\n        this._position0 = this._camera.position.clone();\n        this._zoom0 = this._zoom;\n        this._focalOffset0 = this._focalOffset.clone();\n        this._dollyControlCoord = new THREE.Vector2();\n        // configs\n        this.mouseButtons = {\n            left: ACTION.ROTATE,\n            middle: ACTION.DOLLY,\n            right: ACTION.TRUCK,\n            wheel: isPerspectiveCamera(this._camera) ? ACTION.DOLLY :\n                isOrthographicCamera(this._camera) ? ACTION.ZOOM :\n                    ACTION.NONE,\n        };\n        this.touches = {\n            one: ACTION.TOUCH_ROTATE,\n            two: isPerspectiveCamera(this._camera) ? ACTION.TOUCH_DOLLY_TRUCK :\n                isOrthographicCamera(this._camera) ? ACTION.TOUCH_ZOOM_TRUCK :\n                    ACTION.NONE,\n            three: ACTION.TOUCH_TRUCK,\n        };\n        const dragStartPosition = new THREE.Vector2();\n        const lastDragPosition = new THREE.Vector2();\n        const dollyStart = new THREE.Vector2();\n        const onPointerDown = (event) => {\n            if (!this._enabled || !this._domElement)\n                return;\n            if (this._interactiveArea.left !== 0 ||\n                this._interactiveArea.top !== 0 ||\n                this._interactiveArea.width !== 1 ||\n                this._interactiveArea.height !== 1) {\n                const elRect = this._domElement.getBoundingClientRect();\n                const left = event.clientX / elRect.width;\n                const top = event.clientY / elRect.height;\n                // check if the interactiveArea contains the drag start position.\n                if (left < this._interactiveArea.left ||\n                    left > this._interactiveArea.right ||\n                    top < this._interactiveArea.top ||\n                    top > this._interactiveArea.bottom)\n                    return;\n            }\n            // Don't call `event.preventDefault()` on the pointerdown event\n            // to keep receiving pointermove evens outside dragging iframe\n            // https://taye.me/blog/tips/2015/11/16/mouse-drag-outside-iframe/\n            const mouseButton = event.pointerType !== 'mouse' ? null :\n                (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT ? MOUSE_BUTTON.LEFT :\n                    (event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE ? MOUSE_BUTTON.MIDDLE :\n                        (event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT ? MOUSE_BUTTON.RIGHT :\n                            null;\n            if (mouseButton !== null) {\n                const zombiePointer = this._findPointerByMouseButton(mouseButton);\n                zombiePointer && this._disposePointer(zombiePointer);\n            }\n            if ((event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT && this._lockedPointer)\n                return;\n            const pointer = {\n                pointerId: event.pointerId,\n                clientX: event.clientX,\n                clientY: event.clientY,\n                deltaX: 0,\n                deltaY: 0,\n                mouseButton,\n            };\n            this._activePointers.push(pointer);\n            // eslint-disable-next-line no-undef\n            this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, { passive: false });\n            this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n            this._domElement.ownerDocument.addEventListener('pointermove', onPointerMove, { passive: false });\n            this._domElement.ownerDocument.addEventListener('pointerup', onPointerUp);\n            this._isDragging = true;\n            startDragging(event);\n        };\n        const onPointerMove = (event) => {\n            if (event.cancelable)\n                event.preventDefault();\n            const pointerId = event.pointerId;\n            const pointer = this._lockedPointer || this._findPointerById(pointerId);\n            if (!pointer)\n                return;\n            pointer.clientX = event.clientX;\n            pointer.clientY = event.clientY;\n            pointer.deltaX = event.movementX;\n            pointer.deltaY = event.movementY;\n            this._state = 0;\n            if (event.pointerType === 'touch') {\n                switch (this._activePointers.length) {\n                    case 1:\n                        this._state = this.touches.one;\n                        break;\n                    case 2:\n                        this._state = this.touches.two;\n                        break;\n                    case 3:\n                        this._state = this.touches.three;\n                        break;\n                }\n            }\n            else {\n                if ((!this._isDragging && this._lockedPointer) ||\n                    this._isDragging && (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT) {\n                    this._state = this._state | this.mouseButtons.left;\n                }\n                if (this._isDragging && (event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE) {\n                    this._state = this._state | this.mouseButtons.middle;\n                }\n                if (this._isDragging && (event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT) {\n                    this._state = this._state | this.mouseButtons.right;\n                }\n            }\n            dragging();\n        };\n        const onPointerUp = (event) => {\n            const pointer = this._findPointerById(event.pointerId);\n            if (pointer && pointer === this._lockedPointer)\n                return;\n            pointer && this._disposePointer(pointer);\n            if (event.pointerType === 'touch') {\n                switch (this._activePointers.length) {\n                    case 0:\n                        this._state = ACTION.NONE;\n                        break;\n                    case 1:\n                        this._state = this.touches.one;\n                        break;\n                    case 2:\n                        this._state = this.touches.two;\n                        break;\n                    case 3:\n                        this._state = this.touches.three;\n                        break;\n                }\n            }\n            else {\n                this._state = ACTION.NONE;\n            }\n            endDragging();\n        };\n        let lastScrollTimeStamp = -1;\n        const onMouseWheel = (event) => {\n            if (!this._domElement)\n                return;\n            if (!this._enabled || this.mouseButtons.wheel === ACTION.NONE)\n                return;\n            if (this._interactiveArea.left !== 0 ||\n                this._interactiveArea.top !== 0 ||\n                this._interactiveArea.width !== 1 ||\n                this._interactiveArea.height !== 1) {\n                const elRect = this._domElement.getBoundingClientRect();\n                const left = event.clientX / elRect.width;\n                const top = event.clientY / elRect.height;\n                // check if the interactiveArea contains the drag start position.\n                if (left < this._interactiveArea.left ||\n                    left > this._interactiveArea.right ||\n                    top < this._interactiveArea.top ||\n                    top > this._interactiveArea.bottom)\n                    return;\n            }\n            event.preventDefault();\n            if (this.dollyToCursor ||\n                this.mouseButtons.wheel === ACTION.ROTATE ||\n                this.mouseButtons.wheel === ACTION.TRUCK) {\n                const now = performance.now();\n                // only need to fire this at scroll start.\n                if (lastScrollTimeStamp - now < 1000)\n                    this._getClientRect(this._elementRect);\n                lastScrollTimeStamp = now;\n            }\n            // Ref: https://github.com/cedricpinson/osgjs/blob/00e5a7e9d9206c06fdde0436e1d62ab7cb5ce853/sources/osgViewer/input/source/InputSourceMouse.js#L89-L103\n            const deltaYFactor = isMac ? -1 : -3;\n            // Checks event.ctrlKey to detect multi-touch gestures on a trackpad.\n            const delta = (event.deltaMode === 1 || event.ctrlKey) ? event.deltaY / deltaYFactor : event.deltaY / (deltaYFactor * 10);\n            const x = this.dollyToCursor ? (event.clientX - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n            const y = this.dollyToCursor ? (event.clientY - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n            switch (this.mouseButtons.wheel) {\n                case ACTION.ROTATE: {\n                    this._rotateInternal(event.deltaX, event.deltaY);\n                    this._isUserControllingRotate = true;\n                    break;\n                }\n                case ACTION.TRUCK: {\n                    this._truckInternal(event.deltaX, event.deltaY, false, false);\n                    this._isUserControllingTruck = true;\n                    break;\n                }\n                case ACTION.SCREEN_PAN: {\n                    this._truckInternal(event.deltaX, event.deltaY, false, true);\n                    this._isUserControllingTruck = true;\n                    break;\n                }\n                case ACTION.OFFSET: {\n                    this._truckInternal(event.deltaX, event.deltaY, true, false);\n                    this._isUserControllingOffset = true;\n                    break;\n                }\n                case ACTION.DOLLY: {\n                    this._dollyInternal(-delta, x, y);\n                    this._isUserControllingDolly = true;\n                    break;\n                }\n                case ACTION.ZOOM: {\n                    this._zoomInternal(-delta, x, y);\n                    this._isUserControllingZoom = true;\n                    break;\n                }\n            }\n            this.dispatchEvent({ type: 'control' });\n        };\n        const onContextMenu = (event) => {\n            if (!this._domElement || !this._enabled)\n                return;\n            // contextmenu event is fired right after pointerdown\n            // remove attached handlers and active pointer, if interrupted by contextmenu.\n            if (this.mouseButtons.right === CameraControls.ACTION.NONE) {\n                const pointerId = event instanceof PointerEvent ? event.pointerId : 0;\n                const pointer = this._findPointerById(pointerId);\n                pointer && this._disposePointer(pointer);\n                // eslint-disable-next-line no-undef\n                this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, { passive: false });\n                this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n                return;\n            }\n            event.preventDefault();\n        };\n        const startDragging = (event) => {\n            if (!this._enabled)\n                return;\n            extractClientCoordFromEvent(this._activePointers, _v2);\n            this._getClientRect(this._elementRect);\n            dragStartPosition.copy(_v2);\n            lastDragPosition.copy(_v2);\n            const isMultiTouch = this._activePointers.length >= 2;\n            if (isMultiTouch) {\n                // 2 finger pinch\n                const dx = _v2.x - this._activePointers[1].clientX;\n                const dy = _v2.y - this._activePointers[1].clientY;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                dollyStart.set(0, distance);\n                // center coords of 2 finger truck\n                const x = (this._activePointers[0].clientX + this._activePointers[1].clientX) * 0.5;\n                const y = (this._activePointers[0].clientY + this._activePointers[1].clientY) * 0.5;\n                lastDragPosition.set(x, y);\n            }\n            this._state = 0;\n            if (!event) {\n                if (this._lockedPointer)\n                    this._state = this._state | this.mouseButtons.left;\n            }\n            else if ('pointerType' in event && event.pointerType === 'touch') {\n                switch (this._activePointers.length) {\n                    case 1:\n                        this._state = this.touches.one;\n                        break;\n                    case 2:\n                        this._state = this.touches.two;\n                        break;\n                    case 3:\n                        this._state = this.touches.three;\n                        break;\n                }\n            }\n            else {\n                if (!this._lockedPointer && (event.buttons & MOUSE_BUTTON.LEFT) === MOUSE_BUTTON.LEFT) {\n                    this._state = this._state | this.mouseButtons.left;\n                }\n                if ((event.buttons & MOUSE_BUTTON.MIDDLE) === MOUSE_BUTTON.MIDDLE) {\n                    this._state = this._state | this.mouseButtons.middle;\n                }\n                if ((event.buttons & MOUSE_BUTTON.RIGHT) === MOUSE_BUTTON.RIGHT) {\n                    this._state = this._state | this.mouseButtons.right;\n                }\n            }\n            // stop current movement on drag start\n            // - rotate\n            if ((this._state & ACTION.ROTATE) === ACTION.ROTATE ||\n                (this._state & ACTION.TOUCH_ROTATE) === ACTION.TOUCH_ROTATE ||\n                (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE ||\n                (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n                this._sphericalEnd.theta = this._spherical.theta;\n                this._sphericalEnd.phi = this._spherical.phi;\n                this._thetaVelocity.value = 0;\n                this._phiVelocity.value = 0;\n            }\n            // - truck and screen-pan\n            if ((this._state & ACTION.TRUCK) === ACTION.TRUCK ||\n                (this._state & ACTION.SCREEN_PAN) === ACTION.SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_TRUCK) === ACTION.TOUCH_TRUCK ||\n                (this._state & ACTION.TOUCH_SCREEN_PAN) === ACTION.TOUCH_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK ||\n                (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK ||\n                (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN) {\n                this._targetEnd.copy(this._target);\n                this._targetVelocity.set(0, 0, 0);\n            }\n            // - dolly\n            if ((this._state & ACTION.DOLLY) === ACTION.DOLLY ||\n                (this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY ||\n                (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK ||\n                (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET ||\n                (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE) {\n                this._sphericalEnd.radius = this._spherical.radius;\n                this._radiusVelocity.value = 0;\n            }\n            // - zoom\n            if ((this._state & ACTION.ZOOM) === ACTION.ZOOM ||\n                (this._state & ACTION.TOUCH_ZOOM) === ACTION.TOUCH_ZOOM ||\n                (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK ||\n                (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET ||\n                (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n                this._zoomEnd = this._zoom;\n                this._zoomVelocity.value = 0;\n            }\n            // - offset\n            if ((this._state & ACTION.OFFSET) === ACTION.OFFSET ||\n                (this._state & ACTION.TOUCH_OFFSET) === ACTION.TOUCH_OFFSET ||\n                (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET ||\n                (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET) {\n                this._focalOffsetEnd.copy(this._focalOffset);\n                this._focalOffsetVelocity.set(0, 0, 0);\n            }\n            this.dispatchEvent({ type: 'controlstart' });\n        };\n        const dragging = () => {\n            if (!this._enabled || !this._dragNeedsUpdate)\n                return;\n            this._dragNeedsUpdate = false;\n            extractClientCoordFromEvent(this._activePointers, _v2);\n            // When pointer lock is enabled clientX, clientY, screenX, and screenY remain 0.\n            // If pointer lock is enabled, use the Delta directory, and assume active-pointer is not multiple.\n            const isPointerLockActive = this._domElement && this._domElement.ownerDocument.pointerLockElement === this._domElement;\n            const lockedPointer = isPointerLockActive ? this._lockedPointer || this._activePointers[0] : null;\n            const deltaX = lockedPointer ? -lockedPointer.deltaX : lastDragPosition.x - _v2.x;\n            const deltaY = lockedPointer ? -lockedPointer.deltaY : lastDragPosition.y - _v2.y;\n            lastDragPosition.copy(_v2);\n            // rotate\n            if ((this._state & ACTION.ROTATE) === ACTION.ROTATE ||\n                (this._state & ACTION.TOUCH_ROTATE) === ACTION.TOUCH_ROTATE ||\n                (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE ||\n                (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n                this._rotateInternal(deltaX, deltaY);\n                this._isUserControllingRotate = true;\n            }\n            // mouse dolly or zoom\n            if ((this._state & ACTION.DOLLY) === ACTION.DOLLY ||\n                (this._state & ACTION.ZOOM) === ACTION.ZOOM) {\n                const dollyX = this.dollyToCursor ? (dragStartPosition.x - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n                const dollyY = this.dollyToCursor ? (dragStartPosition.y - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n                const dollyDirection = this.dollyDragInverted ? -1 : 1;\n                if ((this._state & ACTION.DOLLY) === ACTION.DOLLY) {\n                    this._dollyInternal(dollyDirection * deltaY * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n                    this._isUserControllingDolly = true;\n                }\n                else {\n                    this._zoomInternal(dollyDirection * deltaY * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n                    this._isUserControllingZoom = true;\n                }\n            }\n            // touch dolly or zoom\n            if ((this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY ||\n                (this._state & ACTION.TOUCH_ZOOM) === ACTION.TOUCH_ZOOM ||\n                (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK ||\n                (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK ||\n                (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET ||\n                (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET ||\n                (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE ||\n                (this._state & ACTION.TOUCH_ZOOM_ROTATE) === ACTION.TOUCH_ZOOM_ROTATE) {\n                const dx = _v2.x - this._activePointers[1].clientX;\n                const dy = _v2.y - this._activePointers[1].clientY;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const dollyDelta = dollyStart.y - distance;\n                dollyStart.set(0, distance);\n                const dollyX = this.dollyToCursor ? (lastDragPosition.x - this._elementRect.x) / this._elementRect.width * 2 - 1 : 0;\n                const dollyY = this.dollyToCursor ? (lastDragPosition.y - this._elementRect.y) / this._elementRect.height * -2 + 1 : 0;\n                if ((this._state & ACTION.TOUCH_DOLLY) === ACTION.TOUCH_DOLLY ||\n                    (this._state & ACTION.TOUCH_DOLLY_ROTATE) === ACTION.TOUCH_DOLLY_ROTATE ||\n                    (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK ||\n                    (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN ||\n                    (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET) {\n                    this._dollyInternal(dollyDelta * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n                    this._isUserControllingDolly = true;\n                }\n                else {\n                    this._zoomInternal(dollyDelta * TOUCH_DOLLY_FACTOR, dollyX, dollyY);\n                    this._isUserControllingZoom = true;\n                }\n            }\n            // truck\n            if ((this._state & ACTION.TRUCK) === ACTION.TRUCK ||\n                (this._state & ACTION.TOUCH_TRUCK) === ACTION.TOUCH_TRUCK ||\n                (this._state & ACTION.TOUCH_DOLLY_TRUCK) === ACTION.TOUCH_DOLLY_TRUCK ||\n                (this._state & ACTION.TOUCH_ZOOM_TRUCK) === ACTION.TOUCH_ZOOM_TRUCK) {\n                this._truckInternal(deltaX, deltaY, false, false);\n                this._isUserControllingTruck = true;\n            }\n            // screen-pan\n            if ((this._state & ACTION.SCREEN_PAN) === ACTION.SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_SCREEN_PAN) === ACTION.TOUCH_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_DOLLY_SCREEN_PAN) === ACTION.TOUCH_DOLLY_SCREEN_PAN ||\n                (this._state & ACTION.TOUCH_ZOOM_SCREEN_PAN) === ACTION.TOUCH_ZOOM_SCREEN_PAN) {\n                this._truckInternal(deltaX, deltaY, false, true);\n                this._isUserControllingTruck = true;\n            }\n            // offset\n            if ((this._state & ACTION.OFFSET) === ACTION.OFFSET ||\n                (this._state & ACTION.TOUCH_OFFSET) === ACTION.TOUCH_OFFSET ||\n                (this._state & ACTION.TOUCH_DOLLY_OFFSET) === ACTION.TOUCH_DOLLY_OFFSET ||\n                (this._state & ACTION.TOUCH_ZOOM_OFFSET) === ACTION.TOUCH_ZOOM_OFFSET) {\n                this._truckInternal(deltaX, deltaY, true, false);\n                this._isUserControllingOffset = true;\n            }\n            this.dispatchEvent({ type: 'control' });\n        };\n        const endDragging = () => {\n            extractClientCoordFromEvent(this._activePointers, _v2);\n            lastDragPosition.copy(_v2);\n            this._dragNeedsUpdate = false;\n            if (this._activePointers.length === 0 ||\n                (this._activePointers.length === 1 && this._activePointers[0] === this._lockedPointer)) {\n                this._isDragging = false;\n            }\n            if (this._activePointers.length === 0 && this._domElement) {\n                // eslint-disable-next-line no-undef\n                this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, { passive: false });\n                this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n                this.dispatchEvent({ type: 'controlend' });\n            }\n        };\n        this.lockPointer = () => {\n            if (!this._enabled || !this._domElement)\n                return;\n            this.cancel();\n            // Element.requestPointerLock is allowed to happen without any pointer active - create a faux one for compatibility with controls\n            this._lockedPointer = {\n                pointerId: -1,\n                clientX: 0,\n                clientY: 0,\n                deltaX: 0,\n                deltaY: 0,\n                mouseButton: null,\n            };\n            this._activePointers.push(this._lockedPointer);\n            // eslint-disable-next-line no-undef\n            this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, { passive: false });\n            this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n            this._domElement.requestPointerLock();\n            this._domElement.ownerDocument.addEventListener('pointerlockchange', onPointerLockChange);\n            this._domElement.ownerDocument.addEventListener('pointerlockerror', onPointerLockError);\n            this._domElement.ownerDocument.addEventListener('pointermove', onPointerMove, { passive: false });\n            this._domElement.ownerDocument.addEventListener('pointerup', onPointerUp);\n            startDragging();\n        };\n        this.unlockPointer = () => {\n            var _a, _b, _c;\n            if (this._lockedPointer !== null) {\n                this._disposePointer(this._lockedPointer);\n                this._lockedPointer = null;\n            }\n            (_a = this._domElement) === null || _a === void 0 ? void 0 : _a.ownerDocument.exitPointerLock();\n            (_b = this._domElement) === null || _b === void 0 ? void 0 : _b.ownerDocument.removeEventListener('pointerlockchange', onPointerLockChange);\n            (_c = this._domElement) === null || _c === void 0 ? void 0 : _c.ownerDocument.removeEventListener('pointerlockerror', onPointerLockError);\n            this.cancel();\n        };\n        const onPointerLockChange = () => {\n            const isPointerLockActive = this._domElement && this._domElement.ownerDocument.pointerLockElement === this._domElement;\n            if (!isPointerLockActive)\n                this.unlockPointer();\n        };\n        const onPointerLockError = () => {\n            this.unlockPointer();\n        };\n        this._addAllEventListeners = (domElement) => {\n            this._domElement = domElement;\n            this._domElement.style.touchAction = 'none';\n            this._domElement.style.userSelect = 'none';\n            this._domElement.style.webkitUserSelect = 'none';\n            this._domElement.addEventListener('pointerdown', onPointerDown);\n            this._domElement.addEventListener('pointercancel', onPointerUp);\n            this._domElement.addEventListener('wheel', onMouseWheel, { passive: false });\n            this._domElement.addEventListener('contextmenu', onContextMenu);\n        };\n        this._removeAllEventListeners = () => {\n            if (!this._domElement)\n                return;\n            this._domElement.style.touchAction = '';\n            this._domElement.style.userSelect = '';\n            this._domElement.style.webkitUserSelect = '';\n            this._domElement.removeEventListener('pointerdown', onPointerDown);\n            this._domElement.removeEventListener('pointercancel', onPointerUp);\n            // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener#matching_event_listeners_for_removal\n            // > it's probably wise to use the same values used for the call to `addEventListener()` when calling `removeEventListener()`\n            // see https://github.com/microsoft/TypeScript/issues/32912#issuecomment-522142969\n            // eslint-disable-next-line no-undef\n            this._domElement.removeEventListener('wheel', onMouseWheel, { passive: false });\n            this._domElement.removeEventListener('contextmenu', onContextMenu);\n            // eslint-disable-next-line no-undef\n            this._domElement.ownerDocument.removeEventListener('pointermove', onPointerMove, { passive: false });\n            this._domElement.ownerDocument.removeEventListener('pointerup', onPointerUp);\n            this._domElement.ownerDocument.removeEventListener('pointerlockchange', onPointerLockChange);\n            this._domElement.ownerDocument.removeEventListener('pointerlockerror', onPointerLockError);\n        };\n        this.cancel = () => {\n            if (this._state === ACTION.NONE)\n                return;\n            this._state = ACTION.NONE;\n            this._activePointers.length = 0;\n            endDragging();\n        };\n        if (domElement)\n            this.connect(domElement);\n        this.update(0);\n    }\n    /**\n     * The camera to be controlled\n     * @category Properties\n     */\n    get camera() {\n        return this._camera;\n    }\n    set camera(camera) {\n        this._camera = camera;\n        this.updateCameraUp();\n        this._camera.updateProjectionMatrix();\n        this._updateNearPlaneCorners();\n        this._needsUpdate = true;\n    }\n    /**\n     * Whether or not the controls are enabled.\n     * `false` to disable user dragging/touch-move, but all methods works.\n     * @category Properties\n     */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(enabled) {\n        this._enabled = enabled;\n        if (!this._domElement)\n            return;\n        if (enabled) {\n            this._domElement.style.touchAction = 'none';\n            this._domElement.style.userSelect = 'none';\n            this._domElement.style.webkitUserSelect = 'none';\n        }\n        else {\n            this.cancel();\n            this._domElement.style.touchAction = '';\n            this._domElement.style.userSelect = '';\n            this._domElement.style.webkitUserSelect = '';\n        }\n    }\n    /**\n     * Returns `true` if the controls are active updating.\n     * readonly value.\n     * @category Properties\n     */\n    get active() {\n        return !this._hasRested;\n    }\n    /**\n     * Getter for the current `ACTION`.\n     * readonly value.\n     * @category Properties\n     */\n    get currentAction() {\n        return this._state;\n    }\n    /**\n     * get/set Current distance.\n     * @category Properties\n     */\n    get distance() {\n        return this._spherical.radius;\n    }\n    set distance(distance) {\n        if (this._spherical.radius === distance &&\n            this._sphericalEnd.radius === distance)\n            return;\n        this._spherical.radius = distance;\n        this._sphericalEnd.radius = distance;\n        this._needsUpdate = true;\n    }\n    // horizontal angle\n    /**\n     * get/set the azimuth angle (horizontal) in radians.\n     * Every 360 degrees turn is added to `.azimuthAngle` value, which is accumulative.\n     * @category Properties\n     */\n    get azimuthAngle() {\n        return this._spherical.theta;\n    }\n    set azimuthAngle(azimuthAngle) {\n        if (this._spherical.theta === azimuthAngle &&\n            this._sphericalEnd.theta === azimuthAngle)\n            return;\n        this._spherical.theta = azimuthAngle;\n        this._sphericalEnd.theta = azimuthAngle;\n        this._needsUpdate = true;\n    }\n    // vertical angle\n    /**\n     * get/set the polar angle (vertical) in radians.\n     * @category Properties\n     */\n    get polarAngle() {\n        return this._spherical.phi;\n    }\n    set polarAngle(polarAngle) {\n        if (this._spherical.phi === polarAngle &&\n            this._sphericalEnd.phi === polarAngle)\n            return;\n        this._spherical.phi = polarAngle;\n        this._sphericalEnd.phi = polarAngle;\n        this._needsUpdate = true;\n    }\n    /**\n     * Whether camera position should be enclosed in the boundary or not.\n     * @category Properties\n     */\n    get boundaryEnclosesCamera() {\n        return this._boundaryEnclosesCamera;\n    }\n    set boundaryEnclosesCamera(boundaryEnclosesCamera) {\n        this._boundaryEnclosesCamera = boundaryEnclosesCamera;\n        this._needsUpdate = true;\n    }\n    /**\n     * Set drag-start, touches and wheel enable area in the domElement.\n     * each values are between `0` and `1` inclusive, where `0` is left/top and `1` is right/bottom of the screen.\n     * e.g. `{ x: 0, y: 0, width: 1, height: 1 }` for entire area.\n     * @category Properties\n     */\n    set interactiveArea(interactiveArea) {\n        this._interactiveArea.width = clamp(interactiveArea.width, 0, 1);\n        this._interactiveArea.height = clamp(interactiveArea.height, 0, 1);\n        this._interactiveArea.x = clamp(interactiveArea.x, 0, 1 - this._interactiveArea.width);\n        this._interactiveArea.y = clamp(interactiveArea.y, 0, 1 - this._interactiveArea.height);\n    }\n    /**\n     * Adds the specified event listener.\n     * Applicable event types (which is `K`) are:\n     * | Event name          | Timing |\n     * | ------------------- | ------ |\n     * | `'controlstart'`    | When the user starts to control the camera via mouse / touches. ¹ |\n     * | `'control'`         | When the user controls the camera (dragging). |\n     * | `'controlend'`      | When the user ends to control the camera. ¹ |\n     * | `'transitionstart'` | When any kind of transition starts, either user control or using a method with `enableTransition = true` |\n     * | `'update'`          | When the camera position is updated. |\n     * | `'wake'`            | When the camera starts moving. |\n     * | `'rest'`            | When the camera movement is below `.restThreshold` ². |\n     * | `'sleep'`           | When the camera end moving. |\n     *\n     * 1. `mouseButtons.wheel` (Mouse wheel control) does not emit `'controlstart'` and `'controlend'`. `mouseButtons.wheel` uses scroll-event internally, and scroll-event happens intermittently. That means \"start\" and \"end\" cannot be detected.\n     * 2. Due to damping, `sleep` will usually fire a few seconds after the camera _appears_ to have stopped moving. If you want to do something (e.g. enable UI, perform another transition) at the point when the camera has stopped, you probably want the `rest` event. This can be fine tuned using the `.restThreshold` parameter. See the [Rest and Sleep Example](https://yomotsu.github.io/camera-controls/examples/rest-and-sleep.html).\n     *\n     * e.g.\n     * ```\n     * cameraControl.addEventListener( 'controlstart', myCallbackFunction );\n     * ```\n     * @param type event name\n     * @param listener handler function\n     * @category Methods\n     */\n    addEventListener(type, listener) {\n        super.addEventListener(type, listener);\n    }\n    /**\n     * Removes the specified event listener\n     * e.g.\n     * ```\n     * cameraControl.addEventListener( 'controlstart', myCallbackFunction );\n     * ```\n     * @param type event name\n     * @param listener handler function\n     * @category Methods\n     */\n    removeEventListener(type, listener) {\n        super.removeEventListener(type, listener);\n    }\n    /**\n     * Rotate azimuthal angle(horizontal) and polar angle(vertical).\n     * Every value is added to the current value.\n     * @param azimuthAngle Azimuth rotate angle. In radian.\n     * @param polarAngle Polar rotate angle. In radian.\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    rotate(azimuthAngle, polarAngle, enableTransition = false) {\n        return this.rotateTo(this._sphericalEnd.theta + azimuthAngle, this._sphericalEnd.phi + polarAngle, enableTransition);\n    }\n    /**\n     * Rotate azimuthal angle(horizontal) to the given angle and keep the same polar angle(vertical) target.\n     *\n     * e.g.\n     * ```\n     * cameraControls.rotateAzimuthTo( 30 * THREE.MathUtils.DEG2RAD, true );\n     * ```\n     * @param azimuthAngle Azimuth rotate angle. In radian.\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    rotateAzimuthTo(azimuthAngle, enableTransition = false) {\n        return this.rotateTo(azimuthAngle, this._sphericalEnd.phi, enableTransition);\n    }\n    /**\n     * Rotate polar angle(vertical) to the given angle and keep the same azimuthal angle(horizontal) target.\n     *\n     * e.g.\n     * ```\n     * cameraControls.rotatePolarTo( 30 * THREE.MathUtils.DEG2RAD, true );\n     * ```\n     * @param polarAngle Polar rotate angle. In radian.\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    rotatePolarTo(polarAngle, enableTransition = false) {\n        return this.rotateTo(this._sphericalEnd.theta, polarAngle, enableTransition);\n    }\n    /**\n     * Rotate azimuthal angle(horizontal) and polar angle(vertical) to the given angle.\n     * Camera view will rotate over the orbit pivot absolutely:\n     *\n     * azimuthAngle\n     * ```\n     *       0º\n     *         \\\n     * 90º -----+----- -90º\n     *           \\\n     *           180º\n     * ```\n     * | direction | angle                  |\n     * | --------- | ---------------------- |\n     * | front     | 0º                     |\n     * | left      | 90º (`Math.PI / 2`)    |\n     * | right     | -90º (`- Math.PI / 2`) |\n     * | back      | 180º (`Math.PI`)       |\n     *\n     * polarAngle\n     * ```\n     *     180º\n     *      |\n     *      90º\n     *      |\n     *      0º\n     * ```\n     * | direction            | angle                  |\n     * | -------------------- | ---------------------- |\n     * | top/sky              | 180º (`Math.PI`)       |\n     * | horizontal from view | 90º (`Math.PI / 2`)    |\n     * | bottom/floor         | 0º                     |\n     *\n     * @param azimuthAngle Azimuth rotate angle to. In radian.\n     * @param polarAngle Polar rotate angle to. In radian.\n     * @param enableTransition  Whether to move smoothly or immediately\n     * @category Methods\n     */\n    rotateTo(azimuthAngle, polarAngle, enableTransition = false) {\n        this._isUserControllingRotate = false;\n        const theta = clamp(azimuthAngle, this.minAzimuthAngle, this.maxAzimuthAngle);\n        const phi = clamp(polarAngle, this.minPolarAngle, this.maxPolarAngle);\n        this._sphericalEnd.theta = theta;\n        this._sphericalEnd.phi = phi;\n        this._sphericalEnd.makeSafe();\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._spherical.theta = this._sphericalEnd.theta;\n            this._spherical.phi = this._sphericalEnd.phi;\n        }\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) &&\n                approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Dolly in/out camera position.\n     * @param distance Distance of dollyIn. Negative number for dollyOut.\n     * @param enableTransition Whether to move smoothly or immediately.\n     * @category Methods\n     */\n    dolly(distance, enableTransition = false) {\n        return this.dollyTo(this._sphericalEnd.radius - distance, enableTransition);\n    }\n    /**\n     * Dolly in/out camera position to given distance.\n     * @param distance Distance of dolly.\n     * @param enableTransition Whether to move smoothly or immediately.\n     * @category Methods\n     */\n    dollyTo(distance, enableTransition = false) {\n        this._isUserControllingDolly = false;\n        this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n        this._changedDolly = 0;\n        return this._dollyToNoClamp(clamp(distance, this.minDistance, this.maxDistance), enableTransition);\n    }\n    _dollyToNoClamp(distance, enableTransition = false) {\n        const lastRadius = this._sphericalEnd.radius;\n        const hasCollider = this.colliderMeshes.length >= 1;\n        if (hasCollider) {\n            const maxDistanceByCollisionTest = this._collisionTest();\n            const isCollided = approxEquals(maxDistanceByCollisionTest, this._spherical.radius);\n            const isDollyIn = lastRadius > distance;\n            if (!isDollyIn && isCollided)\n                return Promise.resolve();\n            this._sphericalEnd.radius = Math.min(distance, maxDistanceByCollisionTest);\n        }\n        else {\n            this._sphericalEnd.radius = distance;\n        }\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._spherical.radius = this._sphericalEnd.radius;\n        }\n        const resolveImmediately = !enableTransition || approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Dolly in, but does not change the distance between the target and the camera, and moves the target position instead.\n     * Specify a negative value for dolly out.\n     * @param distance Distance of dolly.\n     * @param enableTransition Whether to move smoothly or immediately.\n     * @category Methods\n     */\n    dollyInFixed(distance, enableTransition = false) {\n        this._targetEnd.add(this._getCameraDirection(_cameraDirection).multiplyScalar(distance));\n        if (!enableTransition) {\n            this._target.copy(this._targetEnd);\n        }\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) &&\n                approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) &&\n                approxEquals(this._target.z, this._targetEnd.z, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Zoom in/out camera. The value is added to camera zoom.\n     * Limits set with `.minZoom` and `.maxZoom`\n     * @param zoomStep zoom scale\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    zoom(zoomStep, enableTransition = false) {\n        return this.zoomTo(this._zoomEnd + zoomStep, enableTransition);\n    }\n    /**\n     * Zoom in/out camera to given scale. The value overwrites camera zoom.\n     * Limits set with .minZoom and .maxZoom\n     * @param zoom\n     * @param enableTransition\n     * @category Methods\n     */\n    zoomTo(zoom, enableTransition = false) {\n        this._isUserControllingZoom = false;\n        this._zoomEnd = clamp(zoom, this.minZoom, this.maxZoom);\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._zoom = this._zoomEnd;\n        }\n        const resolveImmediately = !enableTransition || approxEquals(this._zoom, this._zoomEnd, this.restThreshold);\n        this._changedZoom = 0;\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * @deprecated `pan()` has been renamed to `truck()`\n     * @category Methods\n     */\n    pan(x, y, enableTransition = false) {\n        console.warn('`pan` has been renamed to `truck`');\n        return this.truck(x, y, enableTransition);\n    }\n    /**\n     * Truck and pedestal camera using current azimuthal angle\n     * @param x Horizontal translate amount\n     * @param y Vertical translate amount\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    truck(x, y, enableTransition = false) {\n        this._camera.updateMatrix();\n        _xColumn.setFromMatrixColumn(this._camera.matrix, 0);\n        _yColumn.setFromMatrixColumn(this._camera.matrix, 1);\n        _xColumn.multiplyScalar(x);\n        _yColumn.multiplyScalar(-y);\n        const offset = _v3A.copy(_xColumn).add(_yColumn);\n        const to = _v3B.copy(this._targetEnd).add(offset);\n        return this.moveTo(to.x, to.y, to.z, enableTransition);\n    }\n    /**\n     * Move forward / backward.\n     * @param distance Amount to move forward / backward. Negative value to move backward\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    forward(distance, enableTransition = false) {\n        _v3A.setFromMatrixColumn(this._camera.matrix, 0);\n        _v3A.crossVectors(this._camera.up, _v3A);\n        _v3A.multiplyScalar(distance);\n        const to = _v3B.copy(this._targetEnd).add(_v3A);\n        return this.moveTo(to.x, to.y, to.z, enableTransition);\n    }\n    /**\n     * Move up / down.\n     * @param height Amount to move up / down. Negative value to move down\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    elevate(height, enableTransition = false) {\n        _v3A.copy(this._camera.up).multiplyScalar(height);\n        return this.moveTo(this._targetEnd.x + _v3A.x, this._targetEnd.y + _v3A.y, this._targetEnd.z + _v3A.z, enableTransition);\n    }\n    /**\n     * Move target position to given point.\n     * @param x x coord to move center position\n     * @param y y coord to move center position\n     * @param z z coord to move center position\n     * @param enableTransition Whether to move smoothly or immediately\n     * @category Methods\n     */\n    moveTo(x, y, z, enableTransition = false) {\n        this._isUserControllingTruck = false;\n        const offset = _v3A.set(x, y, z).sub(this._targetEnd);\n        this._encloseToBoundary(this._targetEnd, offset, this.boundaryFriction);\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._target.copy(this._targetEnd);\n        }\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) &&\n                approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) &&\n                approxEquals(this._target.z, this._targetEnd.z, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Look in the given point direction.\n     * @param x point x.\n     * @param y point y.\n     * @param z point z.\n     * @param enableTransition Whether to move smoothly or immediately.\n     * @returns Transition end promise\n     * @category Methods\n     */\n    lookInDirectionOf(x, y, z, enableTransition = false) {\n        const point = _v3A.set(x, y, z);\n        const direction = point.sub(this._targetEnd).normalize();\n        const position = direction.multiplyScalar(-this._sphericalEnd.radius).add(this._targetEnd);\n        return this.setPosition(position.x, position.y, position.z, enableTransition);\n    }\n    /**\n     * Fit the viewport to the box or the bounding box of the object, using the nearest axis. paddings are in unit.\n     * set `cover: true` to fill enter screen.\n     * e.g.\n     * ```\n     * cameraControls.fitToBox( myMesh );\n     * ```\n     * @param box3OrObject Axis aligned bounding box to fit the view.\n     * @param enableTransition Whether to move smoothly or immediately.\n     * @param options | `<object>` { cover: boolean, paddingTop: number, paddingLeft: number, paddingBottom: number, paddingRight: number }\n     * @returns Transition end promise\n     * @category Methods\n     */\n    fitToBox(box3OrObject, enableTransition, { cover = false, paddingLeft = 0, paddingRight = 0, paddingBottom = 0, paddingTop = 0 } = {}) {\n        const promises = [];\n        const aabb = box3OrObject.isBox3\n            ? _box3A.copy(box3OrObject)\n            : _box3A.setFromObject(box3OrObject);\n        if (aabb.isEmpty()) {\n            console.warn('camera-controls: fitTo() cannot be used with an empty box. Aborting');\n            Promise.resolve();\n        }\n        // round to closest axis ( forward | backward | right | left | top | bottom )\n        const theta = roundToStep(this._sphericalEnd.theta, PI_HALF);\n        const phi = roundToStep(this._sphericalEnd.phi, PI_HALF);\n        promises.push(this.rotateTo(theta, phi, enableTransition));\n        const normal = _v3A.setFromSpherical(this._sphericalEnd).normalize();\n        const rotation = _quaternionA.setFromUnitVectors(normal, _AXIS_Z);\n        const viewFromPolar = approxEquals(Math.abs(normal.y), 1);\n        if (viewFromPolar) {\n            rotation.multiply(_quaternionB.setFromAxisAngle(_AXIS_Y, theta));\n        }\n        rotation.multiply(this._yAxisUpSpaceInverse);\n        // make oriented bounding box\n        const bb = _box3B.makeEmpty();\n        // left bottom back corner\n        _v3B.copy(aabb.min).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // right bottom back corner\n        _v3B.copy(aabb.min).setX(aabb.max.x).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // left top back corner\n        _v3B.copy(aabb.min).setY(aabb.max.y).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // right top back corner\n        _v3B.copy(aabb.max).setZ(aabb.min.z).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // left bottom front corner\n        _v3B.copy(aabb.min).setZ(aabb.max.z).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // right bottom front corner\n        _v3B.copy(aabb.max).setY(aabb.min.y).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // left top front corner\n        _v3B.copy(aabb.max).setX(aabb.min.x).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // right top front corner\n        _v3B.copy(aabb.max).applyQuaternion(rotation);\n        bb.expandByPoint(_v3B);\n        // add padding\n        bb.min.x -= paddingLeft;\n        bb.min.y -= paddingBottom;\n        bb.max.x += paddingRight;\n        bb.max.y += paddingTop;\n        rotation.setFromUnitVectors(_AXIS_Z, normal);\n        if (viewFromPolar) {\n            rotation.premultiply(_quaternionB.invert());\n        }\n        rotation.premultiply(this._yAxisUpSpace);\n        const bbSize = bb.getSize(_v3A);\n        const center = bb.getCenter(_v3B).applyQuaternion(rotation);\n        if (isPerspectiveCamera(this._camera)) {\n            const distance = this.getDistanceToFitBox(bbSize.x, bbSize.y, bbSize.z, cover);\n            promises.push(this.moveTo(center.x, center.y, center.z, enableTransition));\n            promises.push(this.dollyTo(distance, enableTransition));\n            promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n        }\n        else if (isOrthographicCamera(this._camera)) {\n            const camera = this._camera;\n            const width = camera.right - camera.left;\n            const height = camera.top - camera.bottom;\n            const zoom = cover ? Math.max(width / bbSize.x, height / bbSize.y) : Math.min(width / bbSize.x, height / bbSize.y);\n            promises.push(this.moveTo(center.x, center.y, center.z, enableTransition));\n            promises.push(this.zoomTo(zoom, enableTransition));\n            promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n        }\n        return Promise.all(promises);\n    }\n    /**\n     * Fit the viewport to the sphere or the bounding sphere of the object.\n     * @param sphereOrMesh\n     * @param enableTransition\n     * @category Methods\n     */\n    fitToSphere(sphereOrMesh, enableTransition) {\n        const promises = [];\n        const isObject3D = 'isObject3D' in sphereOrMesh;\n        const boundingSphere = isObject3D ?\n            CameraControls.createBoundingSphere(sphereOrMesh, _sphere) :\n            _sphere.copy(sphereOrMesh);\n        promises.push(this.moveTo(boundingSphere.center.x, boundingSphere.center.y, boundingSphere.center.z, enableTransition));\n        if (isPerspectiveCamera(this._camera)) {\n            const distanceToFit = this.getDistanceToFitSphere(boundingSphere.radius);\n            promises.push(this.dollyTo(distanceToFit, enableTransition));\n        }\n        else if (isOrthographicCamera(this._camera)) {\n            const width = this._camera.right - this._camera.left;\n            const height = this._camera.top - this._camera.bottom;\n            const diameter = 2 * boundingSphere.radius;\n            const zoom = Math.min(width / diameter, height / diameter);\n            promises.push(this.zoomTo(zoom, enableTransition));\n        }\n        promises.push(this.setFocalOffset(0, 0, 0, enableTransition));\n        return Promise.all(promises);\n    }\n    /**\n     * Look at the `target` from the `position`.\n     * @param positionX\n     * @param positionY\n     * @param positionZ\n     * @param targetX\n     * @param targetY\n     * @param targetZ\n     * @param enableTransition\n     * @category Methods\n     */\n    setLookAt(positionX, positionY, positionZ, targetX, targetY, targetZ, enableTransition = false) {\n        this._isUserControllingRotate = false;\n        this._isUserControllingDolly = false;\n        this._isUserControllingTruck = false;\n        this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n        this._changedDolly = 0;\n        const target = _v3B.set(targetX, targetY, targetZ);\n        const position = _v3A.set(positionX, positionY, positionZ);\n        this._targetEnd.copy(target);\n        this._sphericalEnd.setFromVector3(position.sub(target).applyQuaternion(this._yAxisUpSpace));\n        this.normalizeRotations();\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._target.copy(this._targetEnd);\n            this._spherical.copy(this._sphericalEnd);\n        }\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) &&\n                approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) &&\n                approxEquals(this._target.z, this._targetEnd.z, this.restThreshold) &&\n                approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) &&\n                approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold) &&\n                approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Similar to setLookAt, but it interpolates between two states.\n     * @param positionAX\n     * @param positionAY\n     * @param positionAZ\n     * @param targetAX\n     * @param targetAY\n     * @param targetAZ\n     * @param positionBX\n     * @param positionBY\n     * @param positionBZ\n     * @param targetBX\n     * @param targetBY\n     * @param targetBZ\n     * @param t\n     * @param enableTransition\n     * @category Methods\n     */\n    lerpLookAt(positionAX, positionAY, positionAZ, targetAX, targetAY, targetAZ, positionBX, positionBY, positionBZ, targetBX, targetBY, targetBZ, t, enableTransition = false) {\n        this._isUserControllingRotate = false;\n        this._isUserControllingDolly = false;\n        this._isUserControllingTruck = false;\n        this._lastDollyDirection = DOLLY_DIRECTION.NONE;\n        this._changedDolly = 0;\n        const targetA = _v3A.set(targetAX, targetAY, targetAZ);\n        const positionA = _v3B.set(positionAX, positionAY, positionAZ);\n        _sphericalA.setFromVector3(positionA.sub(targetA).applyQuaternion(this._yAxisUpSpace));\n        const targetB = _v3C.set(targetBX, targetBY, targetBZ);\n        const positionB = _v3B.set(positionBX, positionBY, positionBZ);\n        _sphericalB.setFromVector3(positionB.sub(targetB).applyQuaternion(this._yAxisUpSpace));\n        this._targetEnd.copy(targetA.lerp(targetB, t)); // tricky\n        const deltaTheta = _sphericalB.theta - _sphericalA.theta;\n        const deltaPhi = _sphericalB.phi - _sphericalA.phi;\n        const deltaRadius = _sphericalB.radius - _sphericalA.radius;\n        this._sphericalEnd.set(_sphericalA.radius + deltaRadius * t, _sphericalA.phi + deltaPhi * t, _sphericalA.theta + deltaTheta * t);\n        this.normalizeRotations();\n        this._needsUpdate = true;\n        if (!enableTransition) {\n            this._target.copy(this._targetEnd);\n            this._spherical.copy(this._sphericalEnd);\n        }\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._target.x, this._targetEnd.x, this.restThreshold) &&\n                approxEquals(this._target.y, this._targetEnd.y, this.restThreshold) &&\n                approxEquals(this._target.z, this._targetEnd.z, this.restThreshold) &&\n                approxEquals(this._spherical.theta, this._sphericalEnd.theta, this.restThreshold) &&\n                approxEquals(this._spherical.phi, this._sphericalEnd.phi, this.restThreshold) &&\n                approxEquals(this._spherical.radius, this._sphericalEnd.radius, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Set angle and distance by given position.\n     * An alias of `setLookAt()`, without target change. Thus keep gazing at the current target\n     * @param positionX\n     * @param positionY\n     * @param positionZ\n     * @param enableTransition\n     * @category Methods\n     */\n    setPosition(positionX, positionY, positionZ, enableTransition = false) {\n        return this.setLookAt(positionX, positionY, positionZ, this._targetEnd.x, this._targetEnd.y, this._targetEnd.z, enableTransition);\n    }\n    /**\n     * Set the target position where gaze at.\n     * An alias of `setLookAt()`, without position change. Thus keep the same position.\n     * @param targetX\n     * @param targetY\n     * @param targetZ\n     * @param enableTransition\n     * @category Methods\n     */\n    setTarget(targetX, targetY, targetZ, enableTransition = false) {\n        const pos = this.getPosition(_v3A);\n        const promise = this.setLookAt(pos.x, pos.y, pos.z, targetX, targetY, targetZ, enableTransition);\n        // see https://github.com/yomotsu/camera-controls/issues/335\n        this._sphericalEnd.phi = clamp(this._sphericalEnd.phi, this.minPolarAngle, this.maxPolarAngle);\n        return promise;\n    }\n    /**\n     * Set focal offset using the screen parallel coordinates. z doesn't affect in Orthographic as with Dolly.\n     * @param x\n     * @param y\n     * @param z\n     * @param enableTransition\n     * @category Methods\n     */\n    setFocalOffset(x, y, z, enableTransition = false) {\n        this._isUserControllingOffset = false;\n        this._focalOffsetEnd.set(x, y, z);\n        this._needsUpdate = true;\n        if (!enableTransition)\n            this._focalOffset.copy(this._focalOffsetEnd);\n        const resolveImmediately = !enableTransition ||\n            approxEquals(this._focalOffset.x, this._focalOffsetEnd.x, this.restThreshold) &&\n                approxEquals(this._focalOffset.y, this._focalOffsetEnd.y, this.restThreshold) &&\n                approxEquals(this._focalOffset.z, this._focalOffsetEnd.z, this.restThreshold);\n        return this._createOnRestPromise(resolveImmediately);\n    }\n    /**\n     * Set orbit point without moving the camera.\n     * SHOULD NOT RUN DURING ANIMATIONS. `setOrbitPoint()` will immediately fix the positions.\n     * @param targetX\n     * @param targetY\n     * @param targetZ\n     * @category Methods\n     */\n    setOrbitPoint(targetX, targetY, targetZ) {\n        this._camera.updateMatrixWorld();\n        _xColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 0);\n        _yColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 1);\n        _zColumn.setFromMatrixColumn(this._camera.matrixWorldInverse, 2);\n        const position = _v3A.set(targetX, targetY, targetZ);\n        const distance = position.distanceTo(this._camera.position);\n        const cameraToPoint = position.sub(this._camera.position);\n        _xColumn.multiplyScalar(cameraToPoint.x);\n        _yColumn.multiplyScalar(cameraToPoint.y);\n        _zColumn.multiplyScalar(cameraToPoint.z);\n        _v3A.copy(_xColumn).add(_yColumn).add(_zColumn);\n        _v3A.z = _v3A.z + distance;\n        this.dollyTo(distance, false);\n        this.setFocalOffset(-_v3A.x, _v3A.y, -_v3A.z, false);\n        this.moveTo(targetX, targetY, targetZ, false);\n    }\n    /**\n     * Set the boundary box that encloses the target of the camera. box3 is in THREE.Box3\n     * @param box3\n     * @category Methods\n     */\n    setBoundary(box3) {\n        if (!box3) {\n            this._boundary.min.set(-Infinity, -Infinity, -Infinity);\n            this._boundary.max.set(Infinity, Infinity, Infinity);\n            this._needsUpdate = true;\n            return;\n        }\n        this._boundary.copy(box3);\n        this._boundary.clampPoint(this._targetEnd, this._targetEnd);\n        this._needsUpdate = true;\n    }\n    /**\n     * Set (or unset) the current viewport.\n     * Set this when you want to use renderer viewport and .dollyToCursor feature at the same time.\n     * @param viewportOrX\n     * @param y\n     * @param width\n     * @param height\n     * @category Methods\n     */\n    setViewport(viewportOrX, y, width, height) {\n        if (viewportOrX === null) { // null\n            this._viewport = null;\n            return;\n        }\n        this._viewport = this._viewport || new THREE.Vector4();\n        if (typeof viewportOrX === 'number') { // number\n            this._viewport.set(viewportOrX, y, width, height);\n        }\n        else { // Vector4\n            this._viewport.copy(viewportOrX);\n        }\n    }\n    /**\n     * Calculate the distance to fit the box.\n     * @param width box width\n     * @param height box height\n     * @param depth box depth\n     * @returns distance\n     * @category Methods\n     */\n    getDistanceToFitBox(width, height, depth, cover = false) {\n        if (notSupportedInOrthographicCamera(this._camera, 'getDistanceToFitBox'))\n            return this._spherical.radius;\n        const boundingRectAspect = width / height;\n        const fov = this._camera.getEffectiveFOV() * DEG2RAD;\n        const aspect = this._camera.aspect;\n        const heightToFit = (cover ? boundingRectAspect > aspect : boundingRectAspect < aspect) ? height : width / aspect;\n        return heightToFit * 0.5 / Math.tan(fov * 0.5) + depth * 0.5;\n    }\n    /**\n     * Calculate the distance to fit the sphere.\n     * @param radius sphere radius\n     * @returns distance\n     * @category Methods\n     */\n    getDistanceToFitSphere(radius) {\n        if (notSupportedInOrthographicCamera(this._camera, 'getDistanceToFitSphere'))\n            return this._spherical.radius;\n        // https://stackoverflow.com/a/44849975\n        const vFOV = this._camera.getEffectiveFOV() * DEG2RAD;\n        const hFOV = Math.atan(Math.tan(vFOV * 0.5) * this._camera.aspect) * 2;\n        const fov = 1 < this._camera.aspect ? vFOV : hFOV;\n        return radius / (Math.sin(fov * 0.5));\n    }\n    /**\n     * Returns the orbit center position, where the camera looking at.\n     * @param out The receiving Vector3 instance to copy the result\n     * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n     * @category Methods\n     */\n    getTarget(out, receiveEndValue = true) {\n        const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n        return _out.copy(receiveEndValue ? this._targetEnd : this._target);\n    }\n    /**\n     * Returns the camera position.\n     * @param out The receiving Vector3 instance to copy the result\n     * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n     * @category Methods\n     */\n    getPosition(out, receiveEndValue = true) {\n        const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n        return _out.setFromSpherical(receiveEndValue ? this._sphericalEnd : this._spherical).applyQuaternion(this._yAxisUpSpaceInverse).add(receiveEndValue ? this._targetEnd : this._target);\n    }\n    /**\n     * Returns the spherical coordinates of the orbit.\n     * @param out The receiving Spherical instance to copy the result\n     * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n     * @category Methods\n     */\n    getSpherical(out, receiveEndValue = true) {\n        const _out = out || new THREE.Spherical();\n        return _out.copy(receiveEndValue ? this._sphericalEnd : this._spherical);\n    }\n    /**\n     * Returns the focal offset, which is how much the camera appears to be translated in screen parallel coordinates.\n     * @param out The receiving Vector3 instance to copy the result\n     * @param receiveEndValue Whether receive the transition end coords or current. default is `true`\n     * @category Methods\n     */\n    getFocalOffset(out, receiveEndValue = true) {\n        const _out = !!out && out.isVector3 ? out : new THREE.Vector3();\n        return _out.copy(receiveEndValue ? this._focalOffsetEnd : this._focalOffset);\n    }\n    /**\n     * Normalize camera azimuth angle rotation between 0 and 360 degrees.\n     * @category Methods\n     */\n    normalizeRotations() {\n        this._sphericalEnd.theta = this._sphericalEnd.theta % PI_2;\n        if (this._sphericalEnd.theta < 0)\n            this._sphericalEnd.theta += PI_2;\n        this._spherical.theta += PI_2 * Math.round((this._sphericalEnd.theta - this._spherical.theta) / PI_2);\n    }\n    /**\n     * stop all transitions.\n     */\n    stop() {\n        this._focalOffset.copy(this._focalOffsetEnd);\n        this._target.copy(this._targetEnd);\n        this._spherical.copy(this._sphericalEnd);\n        this._zoom = this._zoomEnd;\n    }\n    /**\n     * Reset all rotation and position to defaults.\n     * @param enableTransition\n     * @category Methods\n     */\n    reset(enableTransition = false) {\n        if (!approxEquals(this._camera.up.x, this._cameraUp0.x) ||\n            !approxEquals(this._camera.up.y, this._cameraUp0.y) ||\n            !approxEquals(this._camera.up.z, this._cameraUp0.z)) {\n            this._camera.up.copy(this._cameraUp0);\n            const position = this.getPosition(_v3A);\n            this.updateCameraUp();\n            this.setPosition(position.x, position.y, position.z);\n        }\n        const promises = [\n            this.setLookAt(this._position0.x, this._position0.y, this._position0.z, this._target0.x, this._target0.y, this._target0.z, enableTransition),\n            this.setFocalOffset(this._focalOffset0.x, this._focalOffset0.y, this._focalOffset0.z, enableTransition),\n            this.zoomTo(this._zoom0, enableTransition),\n        ];\n        return Promise.all(promises);\n    }\n    /**\n     * Set current camera position as the default position.\n     * @category Methods\n     */\n    saveState() {\n        this._cameraUp0.copy(this._camera.up);\n        this.getTarget(this._target0);\n        this.getPosition(this._position0);\n        this._zoom0 = this._zoom;\n        this._focalOffset0.copy(this._focalOffset);\n    }\n    /**\n     * Sync camera-up direction.\n     * When camera-up vector is changed, `.updateCameraUp()` must be called.\n     * @category Methods\n     */\n    updateCameraUp() {\n        this._yAxisUpSpace.setFromUnitVectors(this._camera.up, _AXIS_Y);\n        this._yAxisUpSpaceInverse.copy(this._yAxisUpSpace).invert();\n    }\n    /**\n     * Apply current camera-up direction to the camera.\n     * The orbit system will be re-initialized with the current position.\n     * @category Methods\n     */\n    applyCameraUp() {\n        const cameraDirection = _v3A.subVectors(this._target, this._camera.position).normalize();\n        // So first find the vector off to the side, orthogonal to both this.object.up and\n        // the \"view\" vector.\n        const side = _v3B.crossVectors(cameraDirection, this._camera.up);\n        // Then find the vector orthogonal to both this \"side\" vector and the \"view\" vector.\n        // This vector will be the new \"up\" vector.\n        this._camera.up.crossVectors(side, cameraDirection).normalize();\n        this._camera.updateMatrixWorld();\n        const position = this.getPosition(_v3A);\n        this.updateCameraUp();\n        this.setPosition(position.x, position.y, position.z);\n    }\n    /**\n     * Update camera position and directions.\n     * This should be called in your tick loop every time, and returns true if re-rendering is needed.\n     * @param delta\n     * @returns updated\n     * @category Methods\n     */\n    update(delta) {\n        const deltaTheta = this._sphericalEnd.theta - this._spherical.theta;\n        const deltaPhi = this._sphericalEnd.phi - this._spherical.phi;\n        const deltaRadius = this._sphericalEnd.radius - this._spherical.radius;\n        const deltaTarget = _deltaTarget.subVectors(this._targetEnd, this._target);\n        const deltaOffset = _deltaOffset.subVectors(this._focalOffsetEnd, this._focalOffset);\n        const deltaZoom = this._zoomEnd - this._zoom;\n        // update theta\n        if (approxZero(deltaTheta)) {\n            this._thetaVelocity.value = 0;\n            this._spherical.theta = this._sphericalEnd.theta;\n        }\n        else {\n            const smoothTime = this._isUserControllingRotate ? this.draggingSmoothTime : this.smoothTime;\n            this._spherical.theta = smoothDamp(this._spherical.theta, this._sphericalEnd.theta, this._thetaVelocity, smoothTime, Infinity, delta);\n            this._needsUpdate = true;\n        }\n        // update phi\n        if (approxZero(deltaPhi)) {\n            this._phiVelocity.value = 0;\n            this._spherical.phi = this._sphericalEnd.phi;\n        }\n        else {\n            const smoothTime = this._isUserControllingRotate ? this.draggingSmoothTime : this.smoothTime;\n            this._spherical.phi = smoothDamp(this._spherical.phi, this._sphericalEnd.phi, this._phiVelocity, smoothTime, Infinity, delta);\n            this._needsUpdate = true;\n        }\n        // update distance\n        if (approxZero(deltaRadius)) {\n            this._radiusVelocity.value = 0;\n            this._spherical.radius = this._sphericalEnd.radius;\n        }\n        else {\n            const smoothTime = this._isUserControllingDolly ? this.draggingSmoothTime : this.smoothTime;\n            this._spherical.radius = smoothDamp(this._spherical.radius, this._sphericalEnd.radius, this._radiusVelocity, smoothTime, this.maxSpeed, delta);\n            this._needsUpdate = true;\n        }\n        // update target position\n        if (approxZero(deltaTarget.x) && approxZero(deltaTarget.y) && approxZero(deltaTarget.z)) {\n            this._targetVelocity.set(0, 0, 0);\n            this._target.copy(this._targetEnd);\n        }\n        else {\n            const smoothTime = this._isUserControllingTruck ? this.draggingSmoothTime : this.smoothTime;\n            smoothDampVec3(this._target, this._targetEnd, this._targetVelocity, smoothTime, this.maxSpeed, delta, this._target);\n            this._needsUpdate = true;\n        }\n        // update focalOffset\n        if (approxZero(deltaOffset.x) && approxZero(deltaOffset.y) && approxZero(deltaOffset.z)) {\n            this._focalOffsetVelocity.set(0, 0, 0);\n            this._focalOffset.copy(this._focalOffsetEnd);\n        }\n        else {\n            const smoothTime = this._isUserControllingOffset ? this.draggingSmoothTime : this.smoothTime;\n            smoothDampVec3(this._focalOffset, this._focalOffsetEnd, this._focalOffsetVelocity, smoothTime, this.maxSpeed, delta, this._focalOffset);\n            this._needsUpdate = true;\n        }\n        // update zoom\n        if (approxZero(deltaZoom)) {\n            this._zoomVelocity.value = 0;\n            this._zoom = this._zoomEnd;\n        }\n        else {\n            const smoothTime = this._isUserControllingZoom ? this.draggingSmoothTime : this.smoothTime;\n            this._zoom = smoothDamp(this._zoom, this._zoomEnd, this._zoomVelocity, smoothTime, Infinity, delta);\n        }\n        if (this.dollyToCursor) {\n            if (isPerspectiveCamera(this._camera) && this._changedDolly !== 0) {\n                const dollyControlAmount = this._spherical.radius - this._lastDistance;\n                const camera = this._camera;\n                const cameraDirection = this._getCameraDirection(_cameraDirection);\n                const planeX = _v3A.copy(cameraDirection).cross(camera.up).normalize();\n                if (planeX.lengthSq() === 0)\n                    planeX.x = 1.0;\n                const planeY = _v3B.crossVectors(planeX, cameraDirection);\n                const worldToScreen = this._sphericalEnd.radius * Math.tan(camera.getEffectiveFOV() * DEG2RAD * 0.5);\n                const prevRadius = this._sphericalEnd.radius - dollyControlAmount;\n                const lerpRatio = (prevRadius - this._sphericalEnd.radius) / this._sphericalEnd.radius;\n                const cursor = _v3C.copy(this._targetEnd)\n                    .add(planeX.multiplyScalar(this._dollyControlCoord.x * worldToScreen * camera.aspect))\n                    .add(planeY.multiplyScalar(this._dollyControlCoord.y * worldToScreen));\n                const newTargetEnd = _v3A.copy(this._targetEnd).lerp(cursor, lerpRatio);\n                const isMin = this._lastDollyDirection === DOLLY_DIRECTION.IN && this._spherical.radius <= this.minDistance;\n                const isMax = this._lastDollyDirection === DOLLY_DIRECTION.OUT && this.maxDistance <= this._spherical.radius;\n                if (this.infinityDolly && (isMin || isMax)) {\n                    this._sphericalEnd.radius -= dollyControlAmount;\n                    this._spherical.radius -= dollyControlAmount;\n                    const dollyAmount = _v3B.copy(cameraDirection).multiplyScalar(-dollyControlAmount);\n                    newTargetEnd.add(dollyAmount);\n                }\n                // target position may be moved beyond boundary.\n                this._boundary.clampPoint(newTargetEnd, newTargetEnd);\n                const targetEndDiff = _v3B.subVectors(newTargetEnd, this._targetEnd);\n                this._targetEnd.copy(newTargetEnd);\n                this._target.add(targetEndDiff);\n                this._changedDolly -= dollyControlAmount;\n                if (approxZero(this._changedDolly))\n                    this._changedDolly = 0;\n            }\n            else if (isOrthographicCamera(this._camera) && this._changedZoom !== 0) {\n                const dollyControlAmount = this._zoom - this._lastZoom;\n                const camera = this._camera;\n                const worldCursorPosition = _v3A.set(this._dollyControlCoord.x, this._dollyControlCoord.y, (camera.near + camera.far) / (camera.near - camera.far)).unproject(camera);\n                const quaternion = _v3B.set(0, 0, -1).applyQuaternion(camera.quaternion);\n                const cursor = _v3C.copy(worldCursorPosition).add(quaternion.multiplyScalar(-worldCursorPosition.dot(camera.up)));\n                const prevZoom = this._zoom - dollyControlAmount;\n                const lerpRatio = -(prevZoom - this._zoom) / this._zoom;\n                // find the \"distance\" (aka plane constant in three.js) of Plane\n                // from a given position (this._targetEnd) and normal vector (cameraDirection)\n                // https://www.maplesoft.com/support/help/maple/view.aspx?path=MathApps%2FEquationOfAPlaneNormal#bkmrk0\n                const cameraDirection = this._getCameraDirection(_cameraDirection);\n                const prevPlaneConstant = this._targetEnd.dot(cameraDirection);\n                const newTargetEnd = _v3A.copy(this._targetEnd).lerp(cursor, lerpRatio);\n                const newPlaneConstant = newTargetEnd.dot(cameraDirection);\n                // Pull back the camera depth that has moved, to be the camera stationary as zoom\n                const pullBack = cameraDirection.multiplyScalar(newPlaneConstant - prevPlaneConstant);\n                newTargetEnd.sub(pullBack);\n                // target position may be moved beyond boundary.\n                this._boundary.clampPoint(newTargetEnd, newTargetEnd);\n                const targetEndDiff = _v3B.subVectors(newTargetEnd, this._targetEnd);\n                this._targetEnd.copy(newTargetEnd);\n                this._target.add(targetEndDiff);\n                // this._target.copy( this._targetEnd );\n                this._changedZoom -= dollyControlAmount;\n                if (approxZero(this._changedZoom))\n                    this._changedZoom = 0;\n            }\n        }\n        if (this._camera.zoom !== this._zoom) {\n            this._camera.zoom = this._zoom;\n            this._camera.updateProjectionMatrix();\n            this._updateNearPlaneCorners();\n            this._needsUpdate = true;\n        }\n        this._dragNeedsUpdate = true;\n        // collision detection\n        const maxDistance = this._collisionTest();\n        this._spherical.radius = Math.min(this._spherical.radius, maxDistance);\n        // decompose spherical to the camera position\n        this._spherical.makeSafe();\n        this._camera.position.setFromSpherical(this._spherical).applyQuaternion(this._yAxisUpSpaceInverse).add(this._target);\n        this._camera.lookAt(this._target);\n        // set offset after the orbit movement\n        const affectOffset = !approxZero(this._focalOffset.x) ||\n            !approxZero(this._focalOffset.y) ||\n            !approxZero(this._focalOffset.z);\n        if (affectOffset) {\n            _xColumn.setFromMatrixColumn(this._camera.matrix, 0);\n            _yColumn.setFromMatrixColumn(this._camera.matrix, 1);\n            _zColumn.setFromMatrixColumn(this._camera.matrix, 2);\n            _xColumn.multiplyScalar(this._focalOffset.x);\n            _yColumn.multiplyScalar(-this._focalOffset.y);\n            _zColumn.multiplyScalar(this._focalOffset.z); // notice: z-offset will not affect in Orthographic.\n            _v3A.copy(_xColumn).add(_yColumn).add(_zColumn);\n            this._camera.position.add(_v3A);\n            this._camera.updateMatrixWorld();\n        }\n        if (this._boundaryEnclosesCamera) {\n            this._encloseToBoundary(this._camera.position.copy(this._target), _v3A.setFromSpherical(this._spherical).applyQuaternion(this._yAxisUpSpaceInverse), 1.0);\n        }\n        const updated = this._needsUpdate;\n        if (updated && !this._updatedLastTime) {\n            this._hasRested = false;\n            this.dispatchEvent({ type: 'wake' });\n            this.dispatchEvent({ type: 'update' });\n        }\n        else if (updated) {\n            this.dispatchEvent({ type: 'update' });\n            if (approxZero(deltaTheta, this.restThreshold) &&\n                approxZero(deltaPhi, this.restThreshold) &&\n                approxZero(deltaRadius, this.restThreshold) &&\n                approxZero(deltaTarget.x, this.restThreshold) &&\n                approxZero(deltaTarget.y, this.restThreshold) &&\n                approxZero(deltaTarget.z, this.restThreshold) &&\n                approxZero(deltaOffset.x, this.restThreshold) &&\n                approxZero(deltaOffset.y, this.restThreshold) &&\n                approxZero(deltaOffset.z, this.restThreshold) &&\n                approxZero(deltaZoom, this.restThreshold) &&\n                !this._hasRested) {\n                this._hasRested = true;\n                this.dispatchEvent({ type: 'rest' });\n            }\n        }\n        else if (!updated && this._updatedLastTime) {\n            this.dispatchEvent({ type: 'sleep' });\n        }\n        this._lastDistance = this._spherical.radius;\n        this._lastZoom = this._zoom;\n        this._updatedLastTime = updated;\n        this._needsUpdate = false;\n        return updated;\n    }\n    /**\n     * Get all state in JSON string\n     * @category Methods\n     */\n    toJSON() {\n        return JSON.stringify({\n            enabled: this._enabled,\n            minDistance: this.minDistance,\n            maxDistance: infinityToMaxNumber(this.maxDistance),\n            minZoom: this.minZoom,\n            maxZoom: infinityToMaxNumber(this.maxZoom),\n            minPolarAngle: this.minPolarAngle,\n            maxPolarAngle: infinityToMaxNumber(this.maxPolarAngle),\n            minAzimuthAngle: infinityToMaxNumber(this.minAzimuthAngle),\n            maxAzimuthAngle: infinityToMaxNumber(this.maxAzimuthAngle),\n            smoothTime: this.smoothTime,\n            draggingSmoothTime: this.draggingSmoothTime,\n            dollySpeed: this.dollySpeed,\n            truckSpeed: this.truckSpeed,\n            dollyToCursor: this.dollyToCursor,\n            target: this._targetEnd.toArray(),\n            position: _v3A.setFromSpherical(this._sphericalEnd).add(this._targetEnd).toArray(),\n            zoom: this._zoomEnd,\n            focalOffset: this._focalOffsetEnd.toArray(),\n            target0: this._target0.toArray(),\n            position0: this._position0.toArray(),\n            zoom0: this._zoom0,\n            focalOffset0: this._focalOffset0.toArray(),\n        });\n    }\n    /**\n     * Reproduce the control state with JSON. enableTransition is where anim or not in a boolean.\n     * @param json\n     * @param enableTransition\n     * @category Methods\n     */\n    fromJSON(json, enableTransition = false) {\n        const obj = JSON.parse(json);\n        this.enabled = obj.enabled;\n        this.minDistance = obj.minDistance;\n        this.maxDistance = maxNumberToInfinity(obj.maxDistance);\n        this.minZoom = obj.minZoom;\n        this.maxZoom = maxNumberToInfinity(obj.maxZoom);\n        this.minPolarAngle = obj.minPolarAngle;\n        this.maxPolarAngle = maxNumberToInfinity(obj.maxPolarAngle);\n        this.minAzimuthAngle = maxNumberToInfinity(obj.minAzimuthAngle);\n        this.maxAzimuthAngle = maxNumberToInfinity(obj.maxAzimuthAngle);\n        this.smoothTime = obj.smoothTime;\n        this.draggingSmoothTime = obj.draggingSmoothTime;\n        this.dollySpeed = obj.dollySpeed;\n        this.truckSpeed = obj.truckSpeed;\n        this.dollyToCursor = obj.dollyToCursor;\n        this._target0.fromArray(obj.target0);\n        this._position0.fromArray(obj.position0);\n        this._zoom0 = obj.zoom0;\n        this._focalOffset0.fromArray(obj.focalOffset0);\n        this.moveTo(obj.target[0], obj.target[1], obj.target[2], enableTransition);\n        _sphericalA.setFromVector3(_v3A.fromArray(obj.position).sub(this._targetEnd).applyQuaternion(this._yAxisUpSpace));\n        this.rotateTo(_sphericalA.theta, _sphericalA.phi, enableTransition);\n        this.dollyTo(_sphericalA.radius, enableTransition);\n        this.zoomTo(obj.zoom, enableTransition);\n        this.setFocalOffset(obj.focalOffset[0], obj.focalOffset[1], obj.focalOffset[2], enableTransition);\n        this._needsUpdate = true;\n    }\n    /**\n     * Attach all internal event handlers to enable drag control.\n     * @category Methods\n     */\n    connect(domElement) {\n        if (this._domElement) {\n            console.warn('camera-controls is already connected.');\n            return;\n        }\n        domElement.setAttribute('data-camera-controls-version', VERSION);\n        this._addAllEventListeners(domElement);\n        this._getClientRect(this._elementRect);\n    }\n    /**\n     * Detach all internal event handlers to disable drag control.\n     */\n    disconnect() {\n        this.cancel();\n        this._removeAllEventListeners();\n        if (this._domElement) {\n            this._domElement.removeAttribute('data-camera-controls-version');\n            this._domElement = undefined;\n        }\n    }\n    /**\n     * Dispose the cameraControls instance itself, remove all eventListeners.\n     * @category Methods\n     */\n    dispose() {\n        // remove all user event listeners\n        this.removeAllEventListeners();\n        // remove all internal event listeners\n        this.disconnect();\n    }\n    // it's okay to expose public though\n    _getTargetDirection(out) {\n        // divide by distance to normalize, lighter than `Vector3.prototype.normalize()`\n        return out.setFromSpherical(this._spherical).divideScalar(this._spherical.radius).applyQuaternion(this._yAxisUpSpaceInverse);\n    }\n    // it's okay to expose public though\n    _getCameraDirection(out) {\n        return this._getTargetDirection(out).negate();\n    }\n    _findPointerById(pointerId) {\n        return this._activePointers.find((activePointer) => activePointer.pointerId === pointerId);\n    }\n    _findPointerByMouseButton(mouseButton) {\n        return this._activePointers.find((activePointer) => activePointer.mouseButton === mouseButton);\n    }\n    _disposePointer(pointer) {\n        this._activePointers.splice(this._activePointers.indexOf(pointer), 1);\n    }\n    _encloseToBoundary(position, offset, friction) {\n        const offsetLength2 = offset.lengthSq();\n        if (offsetLength2 === 0.0) { // sanity check\n            return position;\n        }\n        // See: https://twitter.com/FMS_Cat/status/1106508958640988161\n        const newTarget = _v3B.copy(offset).add(position); // target\n        const clampedTarget = this._boundary.clampPoint(newTarget, _v3C); // clamped target\n        const deltaClampedTarget = clampedTarget.sub(newTarget); // newTarget -> clampedTarget\n        const deltaClampedTargetLength2 = deltaClampedTarget.lengthSq(); // squared length of deltaClampedTarget\n        if (deltaClampedTargetLength2 === 0.0) { // when the position doesn't have to be clamped\n            return position.add(offset);\n        }\n        else if (deltaClampedTargetLength2 === offsetLength2) { // when the position is completely stuck\n            return position;\n        }\n        else if (friction === 0.0) {\n            return position.add(offset).add(deltaClampedTarget);\n        }\n        else {\n            const offsetFactor = 1.0 + friction * deltaClampedTargetLength2 / offset.dot(deltaClampedTarget);\n            return position\n                .add(_v3B.copy(offset).multiplyScalar(offsetFactor))\n                .add(deltaClampedTarget.multiplyScalar(1.0 - friction));\n        }\n    }\n    _updateNearPlaneCorners() {\n        if (isPerspectiveCamera(this._camera)) {\n            const camera = this._camera;\n            const near = camera.near;\n            const fov = camera.getEffectiveFOV() * DEG2RAD;\n            const heightHalf = Math.tan(fov * 0.5) * near; // near plain half height\n            const widthHalf = heightHalf * camera.aspect; // near plain half width\n            this._nearPlaneCorners[0].set(-widthHalf, -heightHalf, 0);\n            this._nearPlaneCorners[1].set(widthHalf, -heightHalf, 0);\n            this._nearPlaneCorners[2].set(widthHalf, heightHalf, 0);\n            this._nearPlaneCorners[3].set(-widthHalf, heightHalf, 0);\n        }\n        else if (isOrthographicCamera(this._camera)) {\n            const camera = this._camera;\n            const zoomInv = 1 / camera.zoom;\n            const left = camera.left * zoomInv;\n            const right = camera.right * zoomInv;\n            const top = camera.top * zoomInv;\n            const bottom = camera.bottom * zoomInv;\n            this._nearPlaneCorners[0].set(left, top, 0);\n            this._nearPlaneCorners[1].set(right, top, 0);\n            this._nearPlaneCorners[2].set(right, bottom, 0);\n            this._nearPlaneCorners[3].set(left, bottom, 0);\n        }\n    }\n    // lateUpdate\n    _collisionTest() {\n        let distance = Infinity;\n        const hasCollider = this.colliderMeshes.length >= 1;\n        if (!hasCollider)\n            return distance;\n        if (notSupportedInOrthographicCamera(this._camera, '_collisionTest'))\n            return distance;\n        const rayDirection = this._getTargetDirection(_cameraDirection);\n        _rotationMatrix.lookAt(_ORIGIN, rayDirection, this._camera.up);\n        for (let i = 0; i < 4; i++) {\n            const nearPlaneCorner = _v3B.copy(this._nearPlaneCorners[i]);\n            nearPlaneCorner.applyMatrix4(_rotationMatrix);\n            const origin = _v3C.addVectors(this._target, nearPlaneCorner);\n            _raycaster.set(origin, rayDirection);\n            _raycaster.far = this._spherical.radius + 1;\n            const intersects = _raycaster.intersectObjects(this.colliderMeshes);\n            if (intersects.length !== 0 && intersects[0].distance < distance) {\n                distance = intersects[0].distance;\n            }\n        }\n        return distance;\n    }\n    /**\n     * Get its client rect and package into given `DOMRect` .\n     */\n    _getClientRect(target) {\n        if (!this._domElement)\n            return;\n        const rect = this._domElement.getBoundingClientRect();\n        target.x = rect.left;\n        target.y = rect.top;\n        if (this._viewport) {\n            target.x += this._viewport.x;\n            target.y += rect.height - this._viewport.w - this._viewport.y;\n            target.width = this._viewport.z;\n            target.height = this._viewport.w;\n        }\n        else {\n            target.width = rect.width;\n            target.height = rect.height;\n        }\n        return target;\n    }\n    _createOnRestPromise(resolveImmediately) {\n        if (resolveImmediately)\n            return Promise.resolve();\n        this._hasRested = false;\n        this.dispatchEvent({ type: 'transitionstart' });\n        return new Promise((resolve) => {\n            const onResolve = () => {\n                this.removeEventListener('rest', onResolve);\n                resolve();\n            };\n            this.addEventListener('rest', onResolve);\n        });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _addAllEventListeners(_domElement) { }\n    _removeAllEventListeners() { }\n    /**\n     * backward compatible\n     * @deprecated use smoothTime (in seconds) instead\n     * @category Properties\n     */\n    get dampingFactor() {\n        console.warn('.dampingFactor has been deprecated. use smoothTime (in seconds) instead.');\n        return 0;\n    }\n    /**\n     * backward compatible\n     * @deprecated use smoothTime (in seconds) instead\n     * @category Properties\n     */\n    set dampingFactor(_) {\n        console.warn('.dampingFactor has been deprecated. use smoothTime (in seconds) instead.');\n    }\n    /**\n     * backward compatible\n     * @deprecated use draggingSmoothTime (in seconds) instead\n     * @category Properties\n     */\n    get draggingDampingFactor() {\n        console.warn('.draggingDampingFactor has been deprecated. use draggingSmoothTime (in seconds) instead.');\n        return 0;\n    }\n    /**\n     * backward compatible\n     * @deprecated use draggingSmoothTime (in seconds) instead\n     * @category Properties\n     */\n    set draggingDampingFactor(_) {\n        console.warn('.draggingDampingFactor has been deprecated. use draggingSmoothTime (in seconds) instead.');\n    }\n    static createBoundingSphere(object3d, out = new THREE.Sphere()) {\n        const boundingSphere = out;\n        const center = boundingSphere.center;\n        _box3A.makeEmpty();\n        // find the center\n        object3d.traverseVisible((object) => {\n            if (!object.isMesh)\n                return;\n            _box3A.expandByObject(object);\n        });\n        _box3A.getCenter(center);\n        // find the radius\n        let maxRadiusSq = 0;\n        object3d.traverseVisible((object) => {\n            if (!object.isMesh)\n                return;\n            const mesh = object;\n            if (!mesh.geometry)\n                return;\n            const geometry = mesh.geometry.clone();\n            geometry.applyMatrix4(mesh.matrixWorld);\n            const bufferGeometry = geometry;\n            const position = bufferGeometry.attributes.position;\n            for (let i = 0, l = position.count; i < l; i++) {\n                _v3A.fromBufferAttribute(position, i);\n                maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_v3A));\n            }\n        });\n        boundingSphere.radius = Math.sqrt(maxRadiusSq);\n        return boundingSphere;\n    }\n}\n\nexport { EventDispatcher, CameraControls as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,YAAY,GAAG;EACjBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC;EACzBC,IAAI,EAAE,GAAG;EACTC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE,WAAW;EAC7BC,YAAY,EAAE,YAAY;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,iBAAiB,EAAE,eAAe;EAClCC,sBAAsB,EAAE,gBAAgB;EACxCC,kBAAkB,EAAE,iBAAiB;EACrCC,kBAAkB,EAAE,kBAAkB;EACtCC,gBAAgB,EAAE,mBAAmB;EACrCC,iBAAiB,EAAE,oBAAoB;EACvCC,qBAAqB,EAAE,qBAAqB;EAC5CC,iBAAiB,EAAE;AACvB,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG;EACpBrB,IAAI,EAAE,CAAC;EACPsB,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,CAAC;AACV,CAAC;AACD,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACjC,OAAOA,MAAM,CAACD,mBAAmB;AACrC;AACA,SAASE,oBAAoBA,CAACD,MAAM,EAAE;EAClC,OAAOA,MAAM,CAACC,oBAAoB;AACtC;AAEA,MAAMC,IAAI,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACxB,MAAMC,OAAO,GAAGF,IAAI,CAACC,EAAE,GAAG,CAAC;AAE3B,MAAME,OAAO,GAAG,IAAI;AACpB,MAAMC,OAAO,GAAGJ,IAAI,CAACC,EAAE,GAAG,GAAG;AAC7B,SAASI,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5B,OAAOR,IAAI,CAACQ,GAAG,CAACD,GAAG,EAAEP,IAAI,CAACO,GAAG,CAACC,GAAG,EAAEF,KAAK,CAAC,CAAC;AAC9C;AACA,SAASG,UAAUA,CAACC,MAAM,EAAEC,KAAK,GAAGR,OAAO,EAAE;EACzC,OAAOH,IAAI,CAACY,GAAG,CAACF,MAAM,CAAC,GAAGC,KAAK;AACnC;AACA,SAASE,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEJ,KAAK,GAAGR,OAAO,EAAE;EACzC,OAAOM,UAAU,CAACK,CAAC,GAAGC,CAAC,EAAEJ,KAAK,CAAC;AACnC;AACA,SAASK,WAAWA,CAACV,KAAK,EAAEW,IAAI,EAAE;EAC9B,OAAOjB,IAAI,CAACkB,KAAK,CAACZ,KAAK,GAAGW,IAAI,CAAC,GAAGA,IAAI;AAC1C;AACA,SAASE,mBAAmBA,CAACb,KAAK,EAAE;EAChC,IAAIc,QAAQ,CAACd,KAAK,CAAC,EACf,OAAOA,KAAK;EAChB,IAAIA,KAAK,GAAG,CAAC,EACT,OAAO,CAACe,MAAM,CAACC,SAAS;EAC5B,OAAOD,MAAM,CAACC,SAAS;AAC3B;AACA,SAASC,mBAAmBA,CAACjB,KAAK,EAAE;EAChC,IAAIN,IAAI,CAACY,GAAG,CAACN,KAAK,CAAC,GAAGe,MAAM,CAACC,SAAS,EAClC,OAAOhB,KAAK;EAChB,OAAOA,KAAK,GAAGkB,QAAQ;AAC3B;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,GAAGN,QAAQ,EAAEO,SAAS,EAAE;EACjG;EACAF,UAAU,GAAG7B,IAAI,CAACQ,GAAG,CAAC,MAAM,EAAEqB,UAAU,CAAC;EACzC,MAAMG,KAAK,GAAG,CAAC,GAAGH,UAAU;EAC5B,MAAMI,CAAC,GAAGD,KAAK,GAAGD,SAAS;EAC3B,MAAMG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAAC;EAC1D,IAAIE,MAAM,GAAGT,OAAO,GAAGC,MAAM;EAC7B,MAAMS,UAAU,GAAGT,MAAM;EACzB;EACA,MAAMU,SAAS,GAAGP,QAAQ,GAAGD,UAAU;EACvCM,MAAM,GAAG9B,KAAK,CAAC8B,MAAM,EAAE,CAACE,SAAS,EAAEA,SAAS,CAAC;EAC7CV,MAAM,GAAGD,OAAO,GAAGS,MAAM;EACzB,MAAMG,IAAI,GAAG,CAACV,kBAAkB,CAACtB,KAAK,GAAG0B,KAAK,GAAGG,MAAM,IAAIJ,SAAS;EACpEH,kBAAkB,CAACtB,KAAK,GAAG,CAACsB,kBAAkB,CAACtB,KAAK,GAAG0B,KAAK,GAAGM,IAAI,IAAIJ,GAAG;EAC1E,IAAIK,MAAM,GAAGZ,MAAM,GAAG,CAACQ,MAAM,GAAGG,IAAI,IAAIJ,GAAG;EAC3C;EACA,IAAIE,UAAU,GAAGV,OAAO,GAAG,GAAG,KAAKa,MAAM,GAAGH,UAAU,EAAE;IACpDG,MAAM,GAAGH,UAAU;IACnBR,kBAAkB,CAACtB,KAAK,GAAG,CAACiC,MAAM,GAAGH,UAAU,IAAIL,SAAS;EAChE;EACA,OAAOQ,MAAM;AACjB;AACA;AACA;AACA,SAASC,cAAcA,CAACd,OAAO,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,GAAGN,QAAQ,EAAEO,SAAS,EAAEU,GAAG,EAAE;EAC1G;EACAZ,UAAU,GAAG7B,IAAI,CAACQ,GAAG,CAAC,MAAM,EAAEqB,UAAU,CAAC;EACzC,MAAMG,KAAK,GAAG,CAAC,GAAGH,UAAU;EAC5B,MAAMI,CAAC,GAAGD,KAAK,GAAGD,SAAS;EAC3B,MAAMG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAAC;EAC1D,IAAIS,OAAO,GAAGf,MAAM,CAACM,CAAC;EACtB,IAAIU,OAAO,GAAGhB,MAAM,CAACiB,CAAC;EACtB,IAAIC,OAAO,GAAGlB,MAAM,CAACmB,CAAC;EACtB,IAAIC,OAAO,GAAGrB,OAAO,CAACO,CAAC,GAAGS,OAAO;EACjC,IAAIM,OAAO,GAAGtB,OAAO,CAACkB,CAAC,GAAGD,OAAO;EACjC,IAAIM,OAAO,GAAGvB,OAAO,CAACoB,CAAC,GAAGD,OAAO;EACjC,MAAMK,WAAW,GAAGR,OAAO;EAC3B,MAAMS,WAAW,GAAGR,OAAO;EAC3B,MAAMS,WAAW,GAAGP,OAAO;EAC3B;EACA,MAAMR,SAAS,GAAGP,QAAQ,GAAGD,UAAU;EACvC,MAAMwB,WAAW,GAAGhB,SAAS,GAAGA,SAAS;EACzC,MAAMiB,WAAW,GAAGP,OAAO,GAAGA,OAAO,GAAGC,OAAO,GAAGA,OAAO,GAAGC,OAAO,GAAGA,OAAO;EAC7E,IAAIK,WAAW,GAAGD,WAAW,EAAE;IAC3B,MAAME,SAAS,GAAGvD,IAAI,CAACwD,IAAI,CAACF,WAAW,CAAC;IACxCP,OAAO,GAAGA,OAAO,GAAGQ,SAAS,GAAGlB,SAAS;IACzCW,OAAO,GAAGA,OAAO,GAAGO,SAAS,GAAGlB,SAAS;IACzCY,OAAO,GAAGA,OAAO,GAAGM,SAAS,GAAGlB,SAAS;EAC7C;EACAK,OAAO,GAAGhB,OAAO,CAACO,CAAC,GAAGc,OAAO;EAC7BJ,OAAO,GAAGjB,OAAO,CAACkB,CAAC,GAAGI,OAAO;EAC7BH,OAAO,GAAGnB,OAAO,CAACoB,CAAC,GAAGG,OAAO;EAC7B,MAAMQ,KAAK,GAAG,CAAC7B,kBAAkB,CAACK,CAAC,GAAGD,KAAK,GAAGe,OAAO,IAAIhB,SAAS;EAClE,MAAM2B,KAAK,GAAG,CAAC9B,kBAAkB,CAACgB,CAAC,GAAGZ,KAAK,GAAGgB,OAAO,IAAIjB,SAAS;EAClE,MAAM4B,KAAK,GAAG,CAAC/B,kBAAkB,CAACkB,CAAC,GAAGd,KAAK,GAAGiB,OAAO,IAAIlB,SAAS;EAClEH,kBAAkB,CAACK,CAAC,GAAG,CAACL,kBAAkB,CAACK,CAAC,GAAGD,KAAK,GAAGyB,KAAK,IAAIvB,GAAG;EACnEN,kBAAkB,CAACgB,CAAC,GAAG,CAAChB,kBAAkB,CAACgB,CAAC,GAAGZ,KAAK,GAAG0B,KAAK,IAAIxB,GAAG;EACnEN,kBAAkB,CAACkB,CAAC,GAAG,CAAClB,kBAAkB,CAACkB,CAAC,GAAGd,KAAK,GAAG2B,KAAK,IAAIzB,GAAG;EACnEO,GAAG,CAACR,CAAC,GAAGS,OAAO,GAAG,CAACK,OAAO,GAAGU,KAAK,IAAIvB,GAAG;EACzCO,GAAG,CAACG,CAAC,GAAGD,OAAO,GAAG,CAACK,OAAO,GAAGU,KAAK,IAAIxB,GAAG;EACzCO,GAAG,CAACK,CAAC,GAAGD,OAAO,GAAG,CAACI,OAAO,GAAGU,KAAK,IAAIzB,GAAG;EACzC;EACA,MAAM0B,iBAAiB,GAAGV,WAAW,GAAGxB,OAAO,CAACO,CAAC;EACjD,MAAM4B,iBAAiB,GAAGV,WAAW,GAAGzB,OAAO,CAACkB,CAAC;EACjD,MAAMkB,iBAAiB,GAAGV,WAAW,GAAG1B,OAAO,CAACoB,CAAC;EACjD,MAAMiB,aAAa,GAAGtB,GAAG,CAACR,CAAC,GAAGiB,WAAW;EACzC,MAAMc,aAAa,GAAGvB,GAAG,CAACG,CAAC,GAAGO,WAAW;EACzC,MAAMc,aAAa,GAAGxB,GAAG,CAACK,CAAC,GAAGM,WAAW;EACzC,IAAIQ,iBAAiB,GAAGG,aAAa,GAAGF,iBAAiB,GAAGG,aAAa,GAAGF,iBAAiB,GAAGG,aAAa,GAAG,CAAC,EAAE;IAC/GxB,GAAG,CAACR,CAAC,GAAGiB,WAAW;IACnBT,GAAG,CAACG,CAAC,GAAGO,WAAW;IACnBV,GAAG,CAACK,CAAC,GAAGM,WAAW;IACnBxB,kBAAkB,CAACK,CAAC,GAAG,CAACQ,GAAG,CAACR,CAAC,GAAGiB,WAAW,IAAInB,SAAS;IACxDH,kBAAkB,CAACgB,CAAC,GAAG,CAACH,GAAG,CAACG,CAAC,GAAGO,WAAW,IAAIpB,SAAS;IACxDH,kBAAkB,CAACkB,CAAC,GAAG,CAACL,GAAG,CAACK,CAAC,GAAGM,WAAW,IAAIrB,SAAS;EAC5D;EACA,OAAOU,GAAG;AACd;AAEA,SAASyB,2BAA2BA,CAACC,QAAQ,EAAE1B,GAAG,EAAE;EAChDA,GAAG,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACbD,QAAQ,CAACE,OAAO,CAAEC,OAAO,IAAK;IAC1B7B,GAAG,CAACR,CAAC,IAAIqC,OAAO,CAACC,OAAO;IACxB9B,GAAG,CAACG,CAAC,IAAI0B,OAAO,CAACE,OAAO;EAC5B,CAAC,CAAC;EACF/B,GAAG,CAACR,CAAC,IAAIkC,QAAQ,CAACM,MAAM;EACxBhC,GAAG,CAACG,CAAC,IAAIuB,QAAQ,CAACM,MAAM;AAC5B;AAEA,SAASC,gCAAgCA,CAAC7E,MAAM,EAAE8E,OAAO,EAAE;EACvD,IAAI7E,oBAAoB,CAACD,MAAM,CAAC,EAAE;IAC9B+E,OAAO,CAACC,IAAI,CAAC,GAAGF,OAAO,yCAAyC,CAAC;IACjE,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AAEA,MAAMG,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACJ,UAAU;IACjC,IAAII,SAAS,CAACF,IAAI,CAAC,KAAKG,SAAS,EAC7BD,SAAS,CAACF,IAAI,CAAC,GAAG,EAAE;IACxB,IAAIE,SAAS,CAACF,IAAI,CAAC,CAACI,OAAO,CAACH,QAAQ,CAAC,KAAK,CAAC,CAAC,EACxCC,SAAS,CAACF,IAAI,CAAC,CAACK,IAAI,CAACJ,QAAQ,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,gBAAgBA,CAACN,IAAI,EAAEC,QAAQ,EAAE;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACJ,UAAU;IACjC,OAAOI,SAAS,CAACF,IAAI,CAAC,KAAKG,SAAS,IAAID,SAAS,CAACF,IAAI,CAAC,CAACI,OAAO,CAACH,QAAQ,CAAC,KAAK,CAAC,CAAC;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,mBAAmBA,CAACP,IAAI,EAAEC,QAAQ,EAAE;IAChC,MAAMC,SAAS,GAAG,IAAI,CAACJ,UAAU;IACjC,MAAMU,aAAa,GAAGN,SAAS,CAACF,IAAI,CAAC;IACrC,IAAIQ,aAAa,KAAKL,SAAS,EAAE;MAC7B,MAAMM,KAAK,GAAGD,aAAa,CAACJ,OAAO,CAACH,QAAQ,CAAC;MAC7C,IAAIQ,KAAK,KAAK,CAAC,CAAC,EACZD,aAAa,CAACE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,uBAAuBA,CAACX,IAAI,EAAE;IAC1B,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACF,UAAU,GAAG,CAAC,CAAC;MACpB;IACJ;IACA,IAAIc,KAAK,CAACC,OAAO,CAAC,IAAI,CAACf,UAAU,CAACE,IAAI,CAAC,CAAC,EACpC,IAAI,CAACF,UAAU,CAACE,IAAI,CAAC,CAACT,MAAM,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIuB,aAAaA,CAACC,KAAK,EAAE;IACjB,MAAMb,SAAS,GAAG,IAAI,CAACJ,UAAU;IACjC,MAAMU,aAAa,GAAGN,SAAS,CAACa,KAAK,CAACf,IAAI,CAAC;IAC3C,IAAIQ,aAAa,KAAKL,SAAS,EAAE;MAC7BY,KAAK,CAACtE,MAAM,GAAG,IAAI;MACnB,MAAMuE,KAAK,GAAGR,aAAa,CAACS,KAAK,CAAC,CAAC,CAAC;MACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,KAAK,CAACzB,MAAM,EAAE2B,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC1CF,KAAK,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,EAAEL,KAAK,CAAC;MAC9B;IACJ;EACJ;AACJ;AAEA,IAAIM,EAAE;AACN,MAAMC,OAAO,GAAG,QAAQ,CAAC,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMC,KAAK,GAAG,KAAK,CAACC,IAAI,CAAC,CAACJ,EAAE,GAAGK,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,SAAS,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,QAAQ,CAAC;AAC9J,IAAIC,KAAK;AACT,IAAIC,OAAO;AACX,IAAIC,OAAO;AACX,IAAIC,OAAO;AACX,IAAIC,GAAG;AACP,IAAIC,IAAI;AACR,IAAIC,IAAI;AACR,IAAIC,IAAI;AACR,IAAIC,gBAAgB;AACpB,IAAIC,QAAQ;AACZ,IAAIC,QAAQ;AACZ,IAAIC,QAAQ;AACZ,IAAIC,YAAY;AAChB,IAAIC,YAAY;AAChB,IAAIC,WAAW;AACf,IAAIC,WAAW;AACf,IAAIC,MAAM;AACV,IAAIC,MAAM;AACV,IAAIC,OAAO;AACX,IAAIC,YAAY;AAChB,IAAIC,YAAY;AAChB,IAAIC,eAAe;AACnB,IAAIC,UAAU;AACd,MAAMC,cAAc,SAASxD,eAAe,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEI,OAAOyD,OAAOA,CAACC,IAAI,EAAE;IACjBzB,KAAK,GAAGyB,IAAI,CAACzB,KAAK;IAClBC,OAAO,GAAG9I,MAAM,CAACC,MAAM,CAAC,IAAI4I,KAAK,CAAC0B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnDxB,OAAO,GAAG/I,MAAM,CAACC,MAAM,CAAC,IAAI4I,KAAK,CAAC0B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnDvB,OAAO,GAAGhJ,MAAM,CAACC,MAAM,CAAC,IAAI4I,KAAK,CAAC0B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnDtB,GAAG,GAAG,IAAIJ,KAAK,CAAC2B,OAAO,CAAC,CAAC;IACzBtB,IAAI,GAAG,IAAIL,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC1BpB,IAAI,GAAG,IAAIN,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC1BnB,IAAI,GAAG,IAAIP,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC1BlB,gBAAgB,GAAG,IAAIR,KAAK,CAAC0B,OAAO,CAAC,CAAC;IACtCjB,QAAQ,GAAG,IAAIT,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC9BhB,QAAQ,GAAG,IAAIV,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC9Bf,QAAQ,GAAG,IAAIX,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC9Bd,YAAY,GAAG,IAAIZ,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAClCb,YAAY,GAAG,IAAIb,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAClCZ,WAAW,GAAG,IAAId,KAAK,CAAC4B,SAAS,CAAC,CAAC;IACnCb,WAAW,GAAG,IAAIf,KAAK,CAAC4B,SAAS,CAAC,CAAC;IACnCZ,MAAM,GAAG,IAAIhB,KAAK,CAAC6B,IAAI,CAAC,CAAC;IACzBZ,MAAM,GAAG,IAAIjB,KAAK,CAAC6B,IAAI,CAAC,CAAC;IACzBX,OAAO,GAAG,IAAIlB,KAAK,CAAC8B,MAAM,CAAC,CAAC;IAC5BX,YAAY,GAAG,IAAInB,KAAK,CAAC+B,UAAU,CAAC,CAAC;IACrCX,YAAY,GAAG,IAAIpB,KAAK,CAAC+B,UAAU,CAAC,CAAC;IACrCV,eAAe,GAAG,IAAIrB,KAAK,CAACgC,OAAO,CAAC,CAAC;IACrCV,UAAU,GAAG,IAAItB,KAAK,CAACiC,SAAS,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACI,WAAW/K,MAAMA,CAAA,EAAG;IAChB,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;EACI,IAAIgL,qBAAqBA,CAACC,CAAC,EAAE;IACzBtE,OAAO,CAACC,IAAI,CAAC,2HAA2H,CAAC;EAC7I;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAAClF,MAAM,EAAEsJ,UAAU,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC,CAAC;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAGrJ,IAAI,CAACC,EAAE,CAAC,CAAC;IAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqJ,eAAe,GAAG,CAAC9H,QAAQ,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+H,eAAe,GAAG/H,QAAQ,CAAC,CAAC;IACjC;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgI,WAAW,GAAGnI,MAAM,CAAClB,OAAO;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACsJ,WAAW,GAAGjI,QAAQ;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkI,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAGpI,QAAQ;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACK,UAAU,GAAG,IAAI;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACgI,kBAAkB,GAAG,KAAK;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAAC/H,QAAQ,GAAGN,QAAQ;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACsI,kBAAkB,GAAG,GAAG;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,GAAG;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,GAAG;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB;AACR;AACA;AACA;IACQ;IACA,IAAI,CAACC,MAAM,GAAG,MAAM,CAAE,CAAC;IACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAGzM,MAAM,CAACG,IAAI;IACzB,IAAI,CAACuM,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAAC;IACjC,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAIL,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C;IACA;IACA;IACA,IAAI,CAACM,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,mBAAmB,GAAGrM,eAAe,CAACrB,IAAI;IAC/C;IACA,IAAI,CAAC2N,cAAc,GAAG;MAAEzL,KAAK,EAAE;IAAE,CAAC;IAClC,IAAI,CAAC0L,YAAY,GAAG;MAAE1L,KAAK,EAAE;IAAE,CAAC;IAChC,IAAI,CAAC2L,eAAe,GAAG;MAAE3L,KAAK,EAAE;IAAE,CAAC;IACnC,IAAI,CAAC4L,eAAe,GAAG,IAAInF,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC1C,IAAI,CAAC0D,oBAAoB,GAAG,IAAIpF,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC/C,IAAI,CAAC2D,aAAa,GAAG;MAAE9L,KAAK,EAAE;IAAE,CAAC;IACjC,IAAI,CAAC+L,cAAc,GAAG,CAACC,MAAM,EAAEC,MAAM,EAAEnC,YAAY,EAAEoC,kBAAkB,KAAK;MACxE,IAAIC,MAAM;MACV,IAAIC,SAAS;MACb,IAAI9M,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,EAAE;QACnC,MAAMC,MAAM,GAAGxF,IAAI,CAACyF,IAAI,CAAC,IAAI,CAACF,OAAO,CAACG,QAAQ,CAAC,CAACC,GAAG,CAAC,IAAI,CAACC,OAAO,CAAC;QACjE;QACA,MAAMC,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,eAAe,CAAC,CAAC,GAAG9M,OAAO;QACpD,MAAM+M,cAAc,GAAGP,MAAM,CAACnI,MAAM,CAAC,CAAC,GAAGzE,IAAI,CAACoN,GAAG,CAACH,GAAG,GAAG,GAAG,CAAC;QAC5DR,MAAM,GAAI,IAAI,CAACvC,UAAU,GAAGoC,MAAM,GAAGa,cAAc,GAAG,IAAI,CAACjC,YAAY,CAACmC,MAAO;QAC/EX,SAAS,GAAI,IAAI,CAACxC,UAAU,GAAGqC,MAAM,GAAGY,cAAc,GAAG,IAAI,CAACjC,YAAY,CAACmC,MAAO;MACtF,CAAC,MACI,IAAIvN,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,EAAE;QACzC,MAAM9M,MAAM,GAAG,IAAI,CAAC8M,OAAO;QAC3BF,MAAM,GAAG,IAAI,CAACvC,UAAU,GAAGoC,MAAM,IAAIzM,MAAM,CAACyN,KAAK,GAAGzN,MAAM,CAAC0N,IAAI,CAAC,GAAG1N,MAAM,CAAC2N,IAAI,GAAG,IAAI,CAACtC,YAAY,CAACuC,KAAK;QACxGf,SAAS,GAAG,IAAI,CAACxC,UAAU,GAAGqC,MAAM,IAAI1M,MAAM,CAAC6N,GAAG,GAAG7N,MAAM,CAAC8N,MAAM,CAAC,GAAG9N,MAAM,CAAC2N,IAAI,GAAG,IAAI,CAACtC,YAAY,CAACmC,MAAM;MAChH,CAAC,MACI;QACD;MACJ;MACA,IAAIb,kBAAkB,EAAE;QACpBpC,YAAY,GACR,IAAI,CAACwD,cAAc,CAAC,IAAI,CAACC,eAAe,CAAC5L,CAAC,GAAGwK,MAAM,EAAE,IAAI,CAACoB,eAAe,CAACjL,CAAC,EAAE,IAAI,CAACiL,eAAe,CAAC/K,CAAC,EAAE,IAAI,CAAC,GAC1G,IAAI,CAACgL,KAAK,CAACrB,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;QAC/B,IAAI,CAACsB,OAAO,CAAC,CAACrB,SAAS,EAAE,IAAI,CAAC;MAClC,CAAC,MACI;QACDtC,YAAY,GACR,IAAI,CAACwD,cAAc,CAAC,IAAI,CAACC,eAAe,CAAC5L,CAAC,GAAGwK,MAAM,EAAE,IAAI,CAACoB,eAAe,CAACjL,CAAC,GAAG8J,SAAS,EAAE,IAAI,CAACmB,eAAe,CAAC/K,CAAC,EAAE,IAAI,CAAC,GACtH,IAAI,CAACgL,KAAK,CAACrB,MAAM,EAAEC,SAAS,EAAE,IAAI,CAAC;MAC3C;IACJ,CAAC;IACD,IAAI,CAACsB,eAAe,GAAG,CAAC1B,MAAM,EAAEC,MAAM,KAAK;MACvC,MAAM0B,KAAK,GAAGlO,IAAI,GAAG,IAAI,CAAC+J,kBAAkB,GAAGwC,MAAM,GAAG,IAAI,CAACpB,YAAY,CAACmC,MAAM,CAAC,CAAC;MAClF,MAAMa,GAAG,GAAGnO,IAAI,GAAG,IAAI,CAACgK,gBAAgB,GAAGwC,MAAM,GAAG,IAAI,CAACrB,YAAY,CAACmC,MAAM;MAC5E,IAAI,CAACc,MAAM,CAACF,KAAK,EAAEC,GAAG,EAAE,IAAI,CAAC;IACjC,CAAC;IACD,IAAI,CAACE,cAAc,GAAG,CAACC,KAAK,EAAEpM,CAAC,EAAEW,CAAC,KAAK;MACnC,MAAM0L,UAAU,GAAGtO,IAAI,CAACuO,GAAG,CAAC,IAAI,EAAE,CAACF,KAAK,GAAG,IAAI,CAACrE,UAAU,CAAC;MAC3D,MAAMwE,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,MAAM;MAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACF,aAAa,CAACC,MAAM,GAAGJ,UAAU;MACvD,MAAMM,eAAe,GAAGvO,KAAK,CAACsO,QAAQ,EAAE,IAAI,CAACnF,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;MAC3E,MAAMoF,kBAAkB,GAAGD,eAAe,GAAGD,QAAQ;MACrD,IAAI,IAAI,CAACjF,aAAa,IAAI,IAAI,CAACS,aAAa,EAAE;QAC1C,IAAI,CAAC2E,eAAe,CAACH,QAAQ,EAAE,IAAI,CAAC;MACxC,CAAC,MACI,IAAI,IAAI,CAACjF,aAAa,IAAI,CAAC,IAAI,CAACS,aAAa,EAAE;QAChD,IAAI,CAAC4E,YAAY,CAACF,kBAAkB,EAAE,IAAI,CAAC;QAC3C,IAAI,CAACC,eAAe,CAACF,eAAe,EAAE,IAAI,CAAC;MAC/C,CAAC,MACI;QACD,IAAI,CAACE,eAAe,CAACF,eAAe,EAAE,IAAI,CAAC;MAC/C;MACA,IAAI,IAAI,CAACzE,aAAa,EAAE;QACpB,IAAI,CAACS,aAAa,IAAI,CAAC,IAAI,CAAClB,aAAa,GAAGiF,QAAQ,GAAGC,eAAe,IAAIJ,YAAY;QACtF,IAAI,CAACQ,kBAAkB,CAAC5K,GAAG,CAACnC,CAAC,EAAEW,CAAC,CAAC;MACrC;MACA,IAAI,CAACkJ,mBAAmB,GAAG9L,IAAI,CAACiP,IAAI,CAAC,CAACZ,KAAK,CAAC;IAChD,CAAC;IACD,IAAI,CAACa,aAAa,GAAG,CAACb,KAAK,EAAEpM,CAAC,EAAEW,CAAC,KAAK;MAClC,MAAMuM,SAAS,GAAGnP,IAAI,CAACuO,GAAG,CAAC,IAAI,EAAEF,KAAK,GAAG,IAAI,CAACrE,UAAU,CAAC;MACzD,MAAMoF,QAAQ,GAAG,IAAI,CAACC,KAAK;MAC3B,MAAM7B,IAAI,GAAG,IAAI,CAAC6B,KAAK,GAAGF,SAAS;MACnC;MACA,IAAI,CAACG,MAAM,CAAC9B,IAAI,EAAE,IAAI,CAAC;MACvB,IAAI,IAAI,CAACrD,aAAa,EAAE;QACpB,IAAI,CAACU,YAAY,IAAI2C,IAAI,GAAG4B,QAAQ;QACpC,IAAI,CAACJ,kBAAkB,CAAC5K,GAAG,CAACnC,CAAC,EAAEW,CAAC,CAAC;MACrC;IACJ,CAAC;IACD;IACA,IAAI,OAAOmE,KAAK,KAAK,WAAW,EAAE;MAC9BnC,OAAO,CAACjE,KAAK,CAAC,iJAAiJ,CAAC;IACpK;IACA,IAAI,CAACgM,OAAO,GAAG9M,MAAM;IACrB,IAAI,CAAC0P,aAAa,GAAG,IAAIxI,KAAK,CAAC+B,UAAU,CAAC,CAAC,CAAC0G,kBAAkB,CAAC,IAAI,CAAC7C,OAAO,CAAC8C,EAAE,EAAExI,OAAO,CAAC;IACxF,IAAI,CAACyI,oBAAoB,GAAG,IAAI,CAACH,aAAa,CAACI,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAC/D,IAAI,CAAClF,MAAM,GAAGzM,MAAM,CAACG,IAAI;IACzB;IACA,IAAI,CAAC4O,OAAO,GAAG,IAAIjG,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAClC,IAAI,CAACoH,UAAU,GAAG,IAAI,CAAC7C,OAAO,CAAC2C,KAAK,CAAC,CAAC;IACtC,IAAI,CAACG,YAAY,GAAG,IAAI/I,KAAK,CAAC0B,OAAO,CAAC,CAAC;IACvC,IAAI,CAACoF,eAAe,GAAG,IAAI,CAACiC,YAAY,CAACH,KAAK,CAAC,CAAC;IAChD;IACA,IAAI,CAACI,UAAU,GAAG,IAAIhJ,KAAK,CAAC4B,SAAS,CAAC,CAAC,CAACqH,cAAc,CAAC5I,IAAI,CAACyF,IAAI,CAAC,IAAI,CAACF,OAAO,CAACG,QAAQ,CAAC,CAACmD,eAAe,CAAC,IAAI,CAACV,aAAa,CAAC,CAAC;IAC5H,IAAI,CAACd,aAAa,GAAG,IAAI,CAACsB,UAAU,CAACJ,KAAK,CAAC,CAAC;IAC5C,IAAI,CAACO,aAAa,GAAG,IAAI,CAACH,UAAU,CAACrB,MAAM;IAC3C,IAAI,CAACW,KAAK,GAAG,IAAI,CAAC1C,OAAO,CAACa,IAAI;IAC9B,IAAI,CAAC2C,QAAQ,GAAG,IAAI,CAACd,KAAK;IAC1B,IAAI,CAACe,SAAS,GAAG,IAAI,CAACf,KAAK;IAC3B;IACA,IAAI,CAACgB,iBAAiB,GAAG,CACrB,IAAItJ,KAAK,CAAC0B,OAAO,CAAC,CAAC,EACnB,IAAI1B,KAAK,CAAC0B,OAAO,CAAC,CAAC,EACnB,IAAI1B,KAAK,CAAC0B,OAAO,CAAC,CAAC,EACnB,IAAI1B,KAAK,CAAC0B,OAAO,CAAC,CAAC,CACtB;IACD,IAAI,CAAC6H,uBAAuB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,SAAS,GAAG,IAAIxJ,KAAK,CAAC6B,IAAI,CAAC,IAAI7B,KAAK,CAAC0B,OAAO,CAAC,CAACjH,QAAQ,EAAE,CAACA,QAAQ,EAAE,CAACA,QAAQ,CAAC,EAAE,IAAIuF,KAAK,CAAC0B,OAAO,CAACjH,QAAQ,EAAEA,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IACpI;IACA,IAAI,CAACgP,UAAU,GAAG,IAAI,CAAC7D,OAAO,CAAC8C,EAAE,CAACE,KAAK,CAAC,CAAC;IACzC,IAAI,CAACc,QAAQ,GAAG,IAAI,CAACzD,OAAO,CAAC2C,KAAK,CAAC,CAAC;IACpC,IAAI,CAACe,UAAU,GAAG,IAAI,CAAC/D,OAAO,CAACG,QAAQ,CAAC6C,KAAK,CAAC,CAAC;IAC/C,IAAI,CAACgB,MAAM,GAAG,IAAI,CAACtB,KAAK;IACxB,IAAI,CAACuB,aAAa,GAAG,IAAI,CAACd,YAAY,CAACH,KAAK,CAAC,CAAC;IAC9C,IAAI,CAACX,kBAAkB,GAAG,IAAIjI,KAAK,CAAC2B,OAAO,CAAC,CAAC;IAC7C;IACA,IAAI,CAACmI,YAAY,GAAG;MAChBtD,IAAI,EAAEtP,MAAM,CAACI,MAAM;MACnByS,MAAM,EAAE7S,MAAM,CAACQ,KAAK;MACpB6O,KAAK,EAAErP,MAAM,CAACK,KAAK;MACnByS,KAAK,EAAEnR,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,GAAG1O,MAAM,CAACQ,KAAK,GACnDqB,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,GAAG1O,MAAM,CAACS,IAAI,GAC5CT,MAAM,CAACG;IACnB,CAAC;IACD,IAAI,CAAC4S,OAAO,GAAG;MACXC,GAAG,EAAEhT,MAAM,CAACU,YAAY;MACxBuS,GAAG,EAAEtR,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,GAAG1O,MAAM,CAACgB,iBAAiB,GAC7Da,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,GAAG1O,MAAM,CAACoB,gBAAgB,GACxDpB,MAAM,CAACG,IAAI;MACnB+S,KAAK,EAAElT,MAAM,CAACW;IAClB,CAAC;IACD,MAAMwS,iBAAiB,GAAG,IAAIrK,KAAK,CAAC2B,OAAO,CAAC,CAAC;IAC7C,MAAM2I,gBAAgB,GAAG,IAAItK,KAAK,CAAC2B,OAAO,CAAC,CAAC;IAC5C,MAAM4I,UAAU,GAAG,IAAIvK,KAAK,CAAC2B,OAAO,CAAC,CAAC;IACtC,MAAM6I,aAAa,GAAItL,KAAK,IAAK;MAC7B,IAAI,CAAC,IAAI,CAACwE,QAAQ,IAAI,CAAC,IAAI,CAAC+G,WAAW,EACnC;MACJ,IAAI,IAAI,CAAChG,gBAAgB,CAAC+B,IAAI,KAAK,CAAC,IAChC,IAAI,CAAC/B,gBAAgB,CAACkC,GAAG,KAAK,CAAC,IAC/B,IAAI,CAAClC,gBAAgB,CAACiC,KAAK,KAAK,CAAC,IACjC,IAAI,CAACjC,gBAAgB,CAAC6B,MAAM,KAAK,CAAC,EAAE;QACpC,MAAMoE,MAAM,GAAG,IAAI,CAACD,WAAW,CAACE,qBAAqB,CAAC,CAAC;QACvD,MAAMnE,IAAI,GAAGtH,KAAK,CAAC1B,OAAO,GAAGkN,MAAM,CAAChE,KAAK;QACzC,MAAMC,GAAG,GAAGzH,KAAK,CAACzB,OAAO,GAAGiN,MAAM,CAACpE,MAAM;QACzC;QACA,IAAIE,IAAI,GAAG,IAAI,CAAC/B,gBAAgB,CAAC+B,IAAI,IACjCA,IAAI,GAAG,IAAI,CAAC/B,gBAAgB,CAAC8B,KAAK,IAClCI,GAAG,GAAG,IAAI,CAAClC,gBAAgB,CAACkC,GAAG,IAC/BA,GAAG,GAAG,IAAI,CAAClC,gBAAgB,CAACmC,MAAM,EAClC;MACR;MACA;MACA;MACA;MACA,MAAMgE,WAAW,GAAG1L,KAAK,CAAC2L,WAAW,KAAK,OAAO,GAAG,IAAI,GACpD,CAAC3L,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACC,IAAI,MAAMD,YAAY,CAACC,IAAI,GAAGD,YAAY,CAACC,IAAI,GACzE,CAACmI,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACG,MAAM,MAAMH,YAAY,CAACG,MAAM,GAAGH,YAAY,CAACG,MAAM,GAC/E,CAACiI,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACE,KAAK,MAAMF,YAAY,CAACE,KAAK,GAAGF,YAAY,CAACE,KAAK,GAC5E,IAAI;MACpB,IAAI4T,WAAW,KAAK,IAAI,EAAE;QACtB,MAAMG,aAAa,GAAG,IAAI,CAACC,yBAAyB,CAACJ,WAAW,CAAC;QACjEG,aAAa,IAAI,IAAI,CAACE,eAAe,CAACF,aAAa,CAAC;MACxD;MACA,IAAI,CAAC7L,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACC,IAAI,MAAMD,YAAY,CAACC,IAAI,IAAI,IAAI,CAACyN,cAAc,EAChF;MACJ,MAAMjH,OAAO,GAAG;QACZ2N,SAAS,EAAEhM,KAAK,CAACgM,SAAS;QAC1B1N,OAAO,EAAE0B,KAAK,CAAC1B,OAAO;QACtBC,OAAO,EAAEyB,KAAK,CAACzB,OAAO;QACtB8H,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACToF;MACJ,CAAC;MACD,IAAI,CAACrG,eAAe,CAAC/F,IAAI,CAACjB,OAAO,CAAC;MAClC;MACA,IAAI,CAACkN,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,aAAa,EAAE0M,aAAa,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACpG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,WAAW,EAAE4M,WAAW,CAAC;MAC5E,IAAI,CAACb,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,aAAa,EAAEkN,aAAa,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,WAAW,EAAEoN,WAAW,CAAC;MACzE,IAAI,CAACjH,WAAW,GAAG,IAAI;MACvBkH,aAAa,CAACrM,KAAK,CAAC;IACxB,CAAC;IACD,MAAMkM,aAAa,GAAIlM,KAAK,IAAK;MAC7B,IAAIA,KAAK,CAACsM,UAAU,EAChBtM,KAAK,CAACuM,cAAc,CAAC,CAAC;MAC1B,MAAMP,SAAS,GAAGhM,KAAK,CAACgM,SAAS;MACjC,MAAM3N,OAAO,GAAG,IAAI,CAACiH,cAAc,IAAI,IAAI,CAACkH,gBAAgB,CAACR,SAAS,CAAC;MACvE,IAAI,CAAC3N,OAAO,EACR;MACJA,OAAO,CAACC,OAAO,GAAG0B,KAAK,CAAC1B,OAAO;MAC/BD,OAAO,CAACE,OAAO,GAAGyB,KAAK,CAACzB,OAAO;MAC/BF,OAAO,CAACgI,MAAM,GAAGrG,KAAK,CAACyM,SAAS;MAChCpO,OAAO,CAACiI,MAAM,GAAGtG,KAAK,CAAC0M,SAAS;MAChC,IAAI,CAACjI,MAAM,GAAG,CAAC;MACf,IAAIzE,KAAK,CAAC2L,WAAW,KAAK,OAAO,EAAE;QAC/B,QAAQ,IAAI,CAACtG,eAAe,CAAC7G,MAAM;UAC/B,KAAK,CAAC;YACF,IAAI,CAACiG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACC,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACvG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACE,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACxG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACG,KAAK;YAChC;QACR;MACJ,CAAC,MACI;QACD,IAAK,CAAC,IAAI,CAAC/F,WAAW,IAAI,IAAI,CAACG,cAAc,IACzC,IAAI,CAACH,WAAW,IAAI,CAACnF,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACC,IAAI,MAAMD,YAAY,CAACC,IAAI,EAAE;UAC/E,IAAI,CAAC4M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACtD,IAAI;QACtD;QACA,IAAI,IAAI,CAACnC,WAAW,IAAI,CAACnF,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACG,MAAM,MAAMH,YAAY,CAACG,MAAM,EAAE;UACnF,IAAI,CAAC0M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACC,MAAM;QACxD;QACA,IAAI,IAAI,CAAC1F,WAAW,IAAI,CAACnF,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACE,KAAK,MAAMF,YAAY,CAACE,KAAK,EAAE;UACjF,IAAI,CAAC2M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACvD,KAAK;QACvD;MACJ;MACAsF,QAAQ,CAAC,CAAC;IACd,CAAC;IACD,MAAMP,WAAW,GAAIpM,KAAK,IAAK;MAC3B,MAAM3B,OAAO,GAAG,IAAI,CAACmO,gBAAgB,CAACxM,KAAK,CAACgM,SAAS,CAAC;MACtD,IAAI3N,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACiH,cAAc,EAC1C;MACJjH,OAAO,IAAI,IAAI,CAAC0N,eAAe,CAAC1N,OAAO,CAAC;MACxC,IAAI2B,KAAK,CAAC2L,WAAW,KAAK,OAAO,EAAE;QAC/B,QAAQ,IAAI,CAACtG,eAAe,CAAC7G,MAAM;UAC/B,KAAK,CAAC;YACF,IAAI,CAACiG,MAAM,GAAGzM,MAAM,CAACG,IAAI;YACzB;UACJ,KAAK,CAAC;YACF,IAAI,CAACsM,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACC,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACvG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACE,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACxG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACG,KAAK;YAChC;QACR;MACJ,CAAC,MACI;QACD,IAAI,CAACzG,MAAM,GAAGzM,MAAM,CAACG,IAAI;MAC7B;MACAyU,WAAW,CAAC,CAAC;IACjB,CAAC;IACD,IAAIC,mBAAmB,GAAG,CAAC,CAAC;IAC5B,MAAMC,YAAY,GAAI9M,KAAK,IAAK;MAC5B,IAAI,CAAC,IAAI,CAACuL,WAAW,EACjB;MACJ,IAAI,CAAC,IAAI,CAAC/G,QAAQ,IAAI,IAAI,CAACoG,YAAY,CAACE,KAAK,KAAK9S,MAAM,CAACG,IAAI,EACzD;MACJ,IAAI,IAAI,CAACoN,gBAAgB,CAAC+B,IAAI,KAAK,CAAC,IAChC,IAAI,CAAC/B,gBAAgB,CAACkC,GAAG,KAAK,CAAC,IAC/B,IAAI,CAAClC,gBAAgB,CAACiC,KAAK,KAAK,CAAC,IACjC,IAAI,CAACjC,gBAAgB,CAAC6B,MAAM,KAAK,CAAC,EAAE;QACpC,MAAMoE,MAAM,GAAG,IAAI,CAACD,WAAW,CAACE,qBAAqB,CAAC,CAAC;QACvD,MAAMnE,IAAI,GAAGtH,KAAK,CAAC1B,OAAO,GAAGkN,MAAM,CAAChE,KAAK;QACzC,MAAMC,GAAG,GAAGzH,KAAK,CAACzB,OAAO,GAAGiN,MAAM,CAACpE,MAAM;QACzC;QACA,IAAIE,IAAI,GAAG,IAAI,CAAC/B,gBAAgB,CAAC+B,IAAI,IACjCA,IAAI,GAAG,IAAI,CAAC/B,gBAAgB,CAAC8B,KAAK,IAClCI,GAAG,GAAG,IAAI,CAAClC,gBAAgB,CAACkC,GAAG,IAC/BA,GAAG,GAAG,IAAI,CAAClC,gBAAgB,CAACmC,MAAM,EAClC;MACR;MACA1H,KAAK,CAACuM,cAAc,CAAC,CAAC;MACtB,IAAI,IAAI,CAACrI,aAAa,IAClB,IAAI,CAAC0G,YAAY,CAACE,KAAK,KAAK9S,MAAM,CAACI,MAAM,IACzC,IAAI,CAACwS,YAAY,CAACE,KAAK,KAAK9S,MAAM,CAACK,KAAK,EAAE;QAC1C,MAAM0U,GAAG,GAAGC,WAAW,CAACD,GAAG,CAAC,CAAC;QAC7B;QACA,IAAIF,mBAAmB,GAAGE,GAAG,GAAG,IAAI,EAChC,IAAI,CAACE,cAAc,CAAC,IAAI,CAAChI,YAAY,CAAC;QAC1C4H,mBAAmB,GAAGE,GAAG;MAC7B;MACA;MACA,MAAMG,YAAY,GAAGzM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACpC;MACA,MAAM2H,KAAK,GAAIpI,KAAK,CAACmN,SAAS,KAAK,CAAC,IAAInN,KAAK,CAACoN,OAAO,GAAIpN,KAAK,CAACsG,MAAM,GAAG4G,YAAY,GAAGlN,KAAK,CAACsG,MAAM,IAAI4G,YAAY,GAAG,EAAE,CAAC;MACzH,MAAMlR,CAAC,GAAG,IAAI,CAACkI,aAAa,GAAG,CAAClE,KAAK,CAAC1B,OAAO,GAAG,IAAI,CAAC2G,YAAY,CAACjJ,CAAC,IAAI,IAAI,CAACiJ,YAAY,CAACuC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;MAC1G,MAAM7K,CAAC,GAAG,IAAI,CAACuH,aAAa,GAAG,CAAClE,KAAK,CAACzB,OAAO,GAAG,IAAI,CAAC0G,YAAY,CAACtI,CAAC,IAAI,IAAI,CAACsI,YAAY,CAACmC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC5G,QAAQ,IAAI,CAACwD,YAAY,CAACE,KAAK;QAC3B,KAAK9S,MAAM,CAACI,MAAM;UAAE;YAChB,IAAI,CAAC2P,eAAe,CAAC/H,KAAK,CAACqG,MAAM,EAAErG,KAAK,CAACsG,MAAM,CAAC;YAChD,IAAI,CAACd,wBAAwB,GAAG,IAAI;YACpC;UACJ;QACA,KAAKxN,MAAM,CAACK,KAAK;UAAE;YACf,IAAI,CAAC+N,cAAc,CAACpG,KAAK,CAACqG,MAAM,EAAErG,KAAK,CAACsG,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;YAC7D,IAAI,CAACZ,uBAAuB,GAAG,IAAI;YACnC;UACJ;QACA,KAAK1N,MAAM,CAACM,UAAU;UAAE;YACpB,IAAI,CAAC8N,cAAc,CAACpG,KAAK,CAACqG,MAAM,EAAErG,KAAK,CAACsG,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;YAC5D,IAAI,CAACZ,uBAAuB,GAAG,IAAI;YACnC;UACJ;QACA,KAAK1N,MAAM,CAACO,MAAM;UAAE;YAChB,IAAI,CAAC6N,cAAc,CAACpG,KAAK,CAACqG,MAAM,EAAErG,KAAK,CAACsG,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;YAC5D,IAAI,CAACX,wBAAwB,GAAG,IAAI;YACpC;UACJ;QACA,KAAK3N,MAAM,CAACQ,KAAK;UAAE;YACf,IAAI,CAAC2P,cAAc,CAAC,CAACC,KAAK,EAAEpM,CAAC,EAAEW,CAAC,CAAC;YACjC,IAAI,CAAC8I,uBAAuB,GAAG,IAAI;YACnC;UACJ;QACA,KAAKzN,MAAM,CAACS,IAAI;UAAE;YACd,IAAI,CAACwQ,aAAa,CAAC,CAACb,KAAK,EAAEpM,CAAC,EAAEW,CAAC,CAAC;YAChC,IAAI,CAACiJ,sBAAsB,GAAG,IAAI;YAClC;UACJ;MACJ;MACA,IAAI,CAAC7F,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAU,CAAC,CAAC;IAC3C,CAAC;IACD,MAAMoO,aAAa,GAAIrN,KAAK,IAAK;MAC7B,IAAI,CAAC,IAAI,CAACuL,WAAW,IAAI,CAAC,IAAI,CAAC/G,QAAQ,EACnC;MACJ;MACA;MACA,IAAI,IAAI,CAACoG,YAAY,CAACvD,KAAK,KAAKhF,cAAc,CAACrK,MAAM,CAACG,IAAI,EAAE;QACxD,MAAM6T,SAAS,GAAGhM,KAAK,YAAYsN,YAAY,GAAGtN,KAAK,CAACgM,SAAS,GAAG,CAAC;QACrE,MAAM3N,OAAO,GAAG,IAAI,CAACmO,gBAAgB,CAACR,SAAS,CAAC;QAChD3N,OAAO,IAAI,IAAI,CAAC0N,eAAe,CAAC1N,OAAO,CAAC;QACxC;QACA,IAAI,CAACkN,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,aAAa,EAAE0M,aAAa,EAAE;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACpG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,WAAW,EAAE4M,WAAW,CAAC;QAC5E;MACJ;MACApM,KAAK,CAACuM,cAAc,CAAC,CAAC;IAC1B,CAAC;IACD,MAAMF,aAAa,GAAIrM,KAAK,IAAK;MAC7B,IAAI,CAAC,IAAI,CAACwE,QAAQ,EACd;MACJvG,2BAA2B,CAAC,IAAI,CAACoH,eAAe,EAAEnE,GAAG,CAAC;MACtD,IAAI,CAAC+L,cAAc,CAAC,IAAI,CAAChI,YAAY,CAAC;MACtCkG,iBAAiB,CAACvE,IAAI,CAAC1F,GAAG,CAAC;MAC3BkK,gBAAgB,CAACxE,IAAI,CAAC1F,GAAG,CAAC;MAC1B,MAAMqM,YAAY,GAAG,IAAI,CAAClI,eAAe,CAAC7G,MAAM,IAAI,CAAC;MACrD,IAAI+O,YAAY,EAAE;QACd;QACA,MAAMC,EAAE,GAAGtM,GAAG,CAAClF,CAAC,GAAG,IAAI,CAACqJ,eAAe,CAAC,CAAC,CAAC,CAAC/G,OAAO;QAClD,MAAMmP,EAAE,GAAGvM,GAAG,CAACvE,CAAC,GAAG,IAAI,CAAC0I,eAAe,CAAC,CAAC,CAAC,CAAC9G,OAAO;QAClD,MAAMmK,QAAQ,GAAG3O,IAAI,CAACwD,IAAI,CAACiQ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAC7CpC,UAAU,CAAClN,GAAG,CAAC,CAAC,EAAEuK,QAAQ,CAAC;QAC3B;QACA,MAAM1M,CAAC,GAAG,CAAC,IAAI,CAACqJ,eAAe,CAAC,CAAC,CAAC,CAAC/G,OAAO,GAAG,IAAI,CAAC+G,eAAe,CAAC,CAAC,CAAC,CAAC/G,OAAO,IAAI,GAAG;QACnF,MAAM3B,CAAC,GAAG,CAAC,IAAI,CAAC0I,eAAe,CAAC,CAAC,CAAC,CAAC9G,OAAO,GAAG,IAAI,CAAC8G,eAAe,CAAC,CAAC,CAAC,CAAC9G,OAAO,IAAI,GAAG;QACnF6M,gBAAgB,CAACjN,GAAG,CAACnC,CAAC,EAAEW,CAAC,CAAC;MAC9B;MACA,IAAI,CAAC8H,MAAM,GAAG,CAAC;MACf,IAAI,CAACzE,KAAK,EAAE;QACR,IAAI,IAAI,CAACsF,cAAc,EACnB,IAAI,CAACb,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACtD,IAAI;MAC1D,CAAC,MACI,IAAI,aAAa,IAAItH,KAAK,IAAIA,KAAK,CAAC2L,WAAW,KAAK,OAAO,EAAE;QAC9D,QAAQ,IAAI,CAACtG,eAAe,CAAC7G,MAAM;UAC/B,KAAK,CAAC;YACF,IAAI,CAACiG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACC,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACvG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACE,GAAG;YAC9B;UACJ,KAAK,CAAC;YACF,IAAI,CAACxG,MAAM,GAAG,IAAI,CAACsG,OAAO,CAACG,KAAK;YAChC;QACR;MACJ,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAAC5F,cAAc,IAAI,CAACtF,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACC,IAAI,MAAMD,YAAY,CAACC,IAAI,EAAE;UACnF,IAAI,CAAC4M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACtD,IAAI;QACtD;QACA,IAAI,CAACtH,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACG,MAAM,MAAMH,YAAY,CAACG,MAAM,EAAE;UAC/D,IAAI,CAAC0M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACC,MAAM;QACxD;QACA,IAAI,CAAC7K,KAAK,CAAC4L,OAAO,GAAGhU,YAAY,CAACE,KAAK,MAAMF,YAAY,CAACE,KAAK,EAAE;UAC7D,IAAI,CAAC2M,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmG,YAAY,CAACvD,KAAK;QACvD;MACJ;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC5C,MAAM,GAAGzM,MAAM,CAACI,MAAM,MAAMJ,MAAM,CAACI,MAAM,IAC/C,CAAC,IAAI,CAACqM,MAAM,GAAGzM,MAAM,CAACU,YAAY,MAAMV,MAAM,CAACU,YAAY,IAC3D,CAAC,IAAI,CAAC+L,MAAM,GAAGzM,MAAM,CAACmB,kBAAkB,MAAMnB,MAAM,CAACmB,kBAAkB,IACvE,CAAC,IAAI,CAACsL,MAAM,GAAGzM,MAAM,CAACuB,iBAAiB,MAAMvB,MAAM,CAACuB,iBAAiB,EAAE;QACvE,IAAI,CAACiP,aAAa,CAACR,KAAK,GAAG,IAAI,CAAC8B,UAAU,CAAC9B,KAAK;QAChD,IAAI,CAACQ,aAAa,CAACP,GAAG,GAAG,IAAI,CAAC6B,UAAU,CAAC7B,GAAG;QAC5C,IAAI,CAACnC,cAAc,CAACzL,KAAK,GAAG,CAAC;QAC7B,IAAI,CAAC0L,YAAY,CAAC1L,KAAK,GAAG,CAAC;MAC/B;MACA;MACA,IAAI,CAAC,IAAI,CAACoK,MAAM,GAAGzM,MAAM,CAACK,KAAK,MAAML,MAAM,CAACK,KAAK,IAC7C,CAAC,IAAI,CAACoM,MAAM,GAAGzM,MAAM,CAACM,UAAU,MAAMN,MAAM,CAACM,UAAU,IACvD,CAAC,IAAI,CAACmM,MAAM,GAAGzM,MAAM,CAACW,WAAW,MAAMX,MAAM,CAACW,WAAW,IACzD,CAAC,IAAI,CAAC8L,MAAM,GAAGzM,MAAM,CAACY,gBAAgB,MAAMZ,MAAM,CAACY,gBAAgB,IACnE,CAAC,IAAI,CAAC6L,MAAM,GAAGzM,MAAM,CAACgB,iBAAiB,MAAMhB,MAAM,CAACgB,iBAAiB,IACrE,CAAC,IAAI,CAACyL,MAAM,GAAGzM,MAAM,CAACiB,sBAAsB,MAAMjB,MAAM,CAACiB,sBAAsB,IAC/E,CAAC,IAAI,CAACwL,MAAM,GAAGzM,MAAM,CAACoB,gBAAgB,MAAMpB,MAAM,CAACoB,gBAAgB,IACnE,CAAC,IAAI,CAACqL,MAAM,GAAGzM,MAAM,CAACsB,qBAAqB,MAAMtB,MAAM,CAACiB,sBAAsB,EAAE;QAChF,IAAI,CAAC2Q,UAAU,CAAChD,IAAI,CAAC,IAAI,CAACG,OAAO,CAAC;QAClC,IAAI,CAACd,eAAe,CAAC9H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrC;MACA;MACA,IAAI,CAAC,IAAI,CAACsG,MAAM,GAAGzM,MAAM,CAACQ,KAAK,MAAMR,MAAM,CAACQ,KAAK,IAC7C,CAAC,IAAI,CAACiM,MAAM,GAAGzM,MAAM,CAACc,WAAW,MAAMd,MAAM,CAACc,WAAW,IACzD,CAAC,IAAI,CAAC2L,MAAM,GAAGzM,MAAM,CAACgB,iBAAiB,MAAMhB,MAAM,CAACgB,iBAAiB,IACrE,CAAC,IAAI,CAACyL,MAAM,GAAGzM,MAAM,CAACiB,sBAAsB,MAAMjB,MAAM,CAACiB,sBAAsB,IAC/E,CAAC,IAAI,CAACwL,MAAM,GAAGzM,MAAM,CAACkB,kBAAkB,MAAMlB,MAAM,CAACkB,kBAAkB,IACvE,CAAC,IAAI,CAACuL,MAAM,GAAGzM,MAAM,CAACmB,kBAAkB,MAAMnB,MAAM,CAACmB,kBAAkB,EAAE;QACzE,IAAI,CAACqP,aAAa,CAACC,MAAM,GAAG,IAAI,CAACqB,UAAU,CAACrB,MAAM;QAClD,IAAI,CAACzC,eAAe,CAAC3L,KAAK,GAAG,CAAC;MAClC;MACA;MACA,IAAI,CAAC,IAAI,CAACoK,MAAM,GAAGzM,MAAM,CAACS,IAAI,MAAMT,MAAM,CAACS,IAAI,IAC3C,CAAC,IAAI,CAACgM,MAAM,GAAGzM,MAAM,CAACe,UAAU,MAAMf,MAAM,CAACe,UAAU,IACvD,CAAC,IAAI,CAAC0L,MAAM,GAAGzM,MAAM,CAACoB,gBAAgB,MAAMpB,MAAM,CAACoB,gBAAgB,IACnE,CAAC,IAAI,CAACqL,MAAM,GAAGzM,MAAM,CAACsB,qBAAqB,MAAMtB,MAAM,CAACsB,qBAAqB,IAC7E,CAAC,IAAI,CAACmL,MAAM,GAAGzM,MAAM,CAACqB,iBAAiB,MAAMrB,MAAM,CAACqB,iBAAiB,IACrE,CAAC,IAAI,CAACoL,MAAM,GAAGzM,MAAM,CAACuB,iBAAiB,MAAMvB,MAAM,CAACuB,iBAAiB,EAAE;QACvE,IAAI,CAAC2Q,QAAQ,GAAG,IAAI,CAACd,KAAK;QAC1B,IAAI,CAACjD,aAAa,CAAC9L,KAAK,GAAG,CAAC;MAChC;MACA;MACA,IAAI,CAAC,IAAI,CAACoK,MAAM,GAAGzM,MAAM,CAACO,MAAM,MAAMP,MAAM,CAACO,MAAM,IAC/C,CAAC,IAAI,CAACkM,MAAM,GAAGzM,MAAM,CAACa,YAAY,MAAMb,MAAM,CAACa,YAAY,IAC3D,CAAC,IAAI,CAAC4L,MAAM,GAAGzM,MAAM,CAACkB,kBAAkB,MAAMlB,MAAM,CAACkB,kBAAkB,IACvE,CAAC,IAAI,CAACuL,MAAM,GAAGzM,MAAM,CAACqB,iBAAiB,MAAMrB,MAAM,CAACqB,iBAAiB,EAAE;QACvE,IAAI,CAACuO,eAAe,CAAChB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC;QAC5C,IAAI,CAAC3D,oBAAoB,CAAC/H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C;MACA,IAAI,CAAC4B,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAe,CAAC,CAAC;IAChD,CAAC;IACD,MAAM0N,QAAQ,GAAGA,CAAA,KAAM;MACnB,IAAI,CAAC,IAAI,CAACnI,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EACxC;MACJ,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7BnH,2BAA2B,CAAC,IAAI,CAACoH,eAAe,EAAEnE,GAAG,CAAC;MACtD;MACA;MACA,MAAMwM,mBAAmB,GAAG,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACU,aAAa,CAAC0B,kBAAkB,KAAK,IAAI,CAACpC,WAAW;MACtH,MAAMqC,aAAa,GAAGF,mBAAmB,GAAG,IAAI,CAACpI,cAAc,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;MACjG,MAAMgB,MAAM,GAAGuH,aAAa,GAAG,CAACA,aAAa,CAACvH,MAAM,GAAG+E,gBAAgB,CAACpP,CAAC,GAAGkF,GAAG,CAAClF,CAAC;MACjF,MAAMsK,MAAM,GAAGsH,aAAa,GAAG,CAACA,aAAa,CAACtH,MAAM,GAAG8E,gBAAgB,CAACzO,CAAC,GAAGuE,GAAG,CAACvE,CAAC;MACjFyO,gBAAgB,CAACxE,IAAI,CAAC1F,GAAG,CAAC;MAC1B;MACA,IAAI,CAAC,IAAI,CAACuD,MAAM,GAAGzM,MAAM,CAACI,MAAM,MAAMJ,MAAM,CAACI,MAAM,IAC/C,CAAC,IAAI,CAACqM,MAAM,GAAGzM,MAAM,CAACU,YAAY,MAAMV,MAAM,CAACU,YAAY,IAC3D,CAAC,IAAI,CAAC+L,MAAM,GAAGzM,MAAM,CAACmB,kBAAkB,MAAMnB,MAAM,CAACmB,kBAAkB,IACvE,CAAC,IAAI,CAACsL,MAAM,GAAGzM,MAAM,CAACuB,iBAAiB,MAAMvB,MAAM,CAACuB,iBAAiB,EAAE;QACvE,IAAI,CAACwO,eAAe,CAAC1B,MAAM,EAAEC,MAAM,CAAC;QACpC,IAAI,CAACd,wBAAwB,GAAG,IAAI;MACxC;MACA;MACA,IAAI,CAAC,IAAI,CAACf,MAAM,GAAGzM,MAAM,CAACQ,KAAK,MAAMR,MAAM,CAACQ,KAAK,IAC7C,CAAC,IAAI,CAACiM,MAAM,GAAGzM,MAAM,CAACS,IAAI,MAAMT,MAAM,CAACS,IAAI,EAAE;QAC7C,MAAMoV,MAAM,GAAG,IAAI,CAAC3J,aAAa,GAAG,CAACiH,iBAAiB,CAACnP,CAAC,GAAG,IAAI,CAACiJ,YAAY,CAACjJ,CAAC,IAAI,IAAI,CAACiJ,YAAY,CAACuC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;QACrH,MAAMsG,MAAM,GAAG,IAAI,CAAC5J,aAAa,GAAG,CAACiH,iBAAiB,CAACxO,CAAC,GAAG,IAAI,CAACsI,YAAY,CAACtI,CAAC,IAAI,IAAI,CAACsI,YAAY,CAACmC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACvH,MAAM2G,cAAc,GAAG,IAAI,CAAC/J,iBAAiB,GAAG,CAAC,CAAC,GAAG,CAAC;QACtD,IAAI,CAAC,IAAI,CAACS,MAAM,GAAGzM,MAAM,CAACQ,KAAK,MAAMR,MAAM,CAACQ,KAAK,EAAE;UAC/C,IAAI,CAAC2P,cAAc,CAAC4F,cAAc,GAAGzH,MAAM,GAAG9F,kBAAkB,EAAEqN,MAAM,EAAEC,MAAM,CAAC;UACjF,IAAI,CAACrI,uBAAuB,GAAG,IAAI;QACvC,CAAC,MACI;UACD,IAAI,CAACwD,aAAa,CAAC8E,cAAc,GAAGzH,MAAM,GAAG9F,kBAAkB,EAAEqN,MAAM,EAAEC,MAAM,CAAC;UAChF,IAAI,CAAClI,sBAAsB,GAAG,IAAI;QACtC;MACJ;MACA;MACA,IAAI,CAAC,IAAI,CAACnB,MAAM,GAAGzM,MAAM,CAACc,WAAW,MAAMd,MAAM,CAACc,WAAW,IACzD,CAAC,IAAI,CAAC2L,MAAM,GAAGzM,MAAM,CAACe,UAAU,MAAMf,MAAM,CAACe,UAAU,IACvD,CAAC,IAAI,CAAC0L,MAAM,GAAGzM,MAAM,CAACgB,iBAAiB,MAAMhB,MAAM,CAACgB,iBAAiB,IACrE,CAAC,IAAI,CAACyL,MAAM,GAAGzM,MAAM,CAACoB,gBAAgB,MAAMpB,MAAM,CAACoB,gBAAgB,IACnE,CAAC,IAAI,CAACqL,MAAM,GAAGzM,MAAM,CAACiB,sBAAsB,MAAMjB,MAAM,CAACiB,sBAAsB,IAC/E,CAAC,IAAI,CAACwL,MAAM,GAAGzM,MAAM,CAACsB,qBAAqB,MAAMtB,MAAM,CAACsB,qBAAqB,IAC7E,CAAC,IAAI,CAACmL,MAAM,GAAGzM,MAAM,CAACkB,kBAAkB,MAAMlB,MAAM,CAACkB,kBAAkB,IACvE,CAAC,IAAI,CAACuL,MAAM,GAAGzM,MAAM,CAACqB,iBAAiB,MAAMrB,MAAM,CAACqB,iBAAiB,IACrE,CAAC,IAAI,CAACoL,MAAM,GAAGzM,MAAM,CAACmB,kBAAkB,MAAMnB,MAAM,CAACmB,kBAAkB,IACvE,CAAC,IAAI,CAACsL,MAAM,GAAGzM,MAAM,CAACuB,iBAAiB,MAAMvB,MAAM,CAACuB,iBAAiB,EAAE;QACvE,MAAMiU,EAAE,GAAGtM,GAAG,CAAClF,CAAC,GAAG,IAAI,CAACqJ,eAAe,CAAC,CAAC,CAAC,CAAC/G,OAAO;QAClD,MAAMmP,EAAE,GAAGvM,GAAG,CAACvE,CAAC,GAAG,IAAI,CAAC0I,eAAe,CAAC,CAAC,CAAC,CAAC9G,OAAO;QAClD,MAAMmK,QAAQ,GAAG3O,IAAI,CAACwD,IAAI,CAACiQ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAC7C,MAAMO,UAAU,GAAG3C,UAAU,CAAC1O,CAAC,GAAG+L,QAAQ;QAC1C2C,UAAU,CAAClN,GAAG,CAAC,CAAC,EAAEuK,QAAQ,CAAC;QAC3B,MAAMmF,MAAM,GAAG,IAAI,CAAC3J,aAAa,GAAG,CAACkH,gBAAgB,CAACpP,CAAC,GAAG,IAAI,CAACiJ,YAAY,CAACjJ,CAAC,IAAI,IAAI,CAACiJ,YAAY,CAACuC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;QACpH,MAAMsG,MAAM,GAAG,IAAI,CAAC5J,aAAa,GAAG,CAACkH,gBAAgB,CAACzO,CAAC,GAAG,IAAI,CAACsI,YAAY,CAACtI,CAAC,IAAI,IAAI,CAACsI,YAAY,CAACmC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACtH,IAAI,CAAC,IAAI,CAAC3C,MAAM,GAAGzM,MAAM,CAACc,WAAW,MAAMd,MAAM,CAACc,WAAW,IACzD,CAAC,IAAI,CAAC2L,MAAM,GAAGzM,MAAM,CAACmB,kBAAkB,MAAMnB,MAAM,CAACmB,kBAAkB,IACvE,CAAC,IAAI,CAACsL,MAAM,GAAGzM,MAAM,CAACgB,iBAAiB,MAAMhB,MAAM,CAACgB,iBAAiB,IACrE,CAAC,IAAI,CAACyL,MAAM,GAAGzM,MAAM,CAACiB,sBAAsB,MAAMjB,MAAM,CAACiB,sBAAsB,IAC/E,CAAC,IAAI,CAACwL,MAAM,GAAGzM,MAAM,CAACkB,kBAAkB,MAAMlB,MAAM,CAACkB,kBAAkB,EAAE;UACzE,IAAI,CAACiP,cAAc,CAAC6F,UAAU,GAAGxN,kBAAkB,EAAEqN,MAAM,EAAEC,MAAM,CAAC;UACpE,IAAI,CAACrI,uBAAuB,GAAG,IAAI;QACvC,CAAC,MACI;UACD,IAAI,CAACwD,aAAa,CAAC+E,UAAU,GAAGxN,kBAAkB,EAAEqN,MAAM,EAAEC,MAAM,CAAC;UACnE,IAAI,CAAClI,sBAAsB,GAAG,IAAI;QACtC;MACJ;MACA;MACA,IAAI,CAAC,IAAI,CAACnB,MAAM,GAAGzM,MAAM,CAACK,KAAK,MAAML,MAAM,CAACK,KAAK,IAC7C,CAAC,IAAI,CAACoM,MAAM,GAAGzM,MAAM,CAACW,WAAW,MAAMX,MAAM,CAACW,WAAW,IACzD,CAAC,IAAI,CAAC8L,MAAM,GAAGzM,MAAM,CAACgB,iBAAiB,MAAMhB,MAAM,CAACgB,iBAAiB,IACrE,CAAC,IAAI,CAACyL,MAAM,GAAGzM,MAAM,CAACoB,gBAAgB,MAAMpB,MAAM,CAACoB,gBAAgB,EAAE;QACrE,IAAI,CAACgN,cAAc,CAACC,MAAM,EAAEC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;QACjD,IAAI,CAACZ,uBAAuB,GAAG,IAAI;MACvC;MACA;MACA,IAAI,CAAC,IAAI,CAACjB,MAAM,GAAGzM,MAAM,CAACM,UAAU,MAAMN,MAAM,CAACM,UAAU,IACvD,CAAC,IAAI,CAACmM,MAAM,GAAGzM,MAAM,CAACY,gBAAgB,MAAMZ,MAAM,CAACY,gBAAgB,IACnE,CAAC,IAAI,CAAC6L,MAAM,GAAGzM,MAAM,CAACiB,sBAAsB,MAAMjB,MAAM,CAACiB,sBAAsB,IAC/E,CAAC,IAAI,CAACwL,MAAM,GAAGzM,MAAM,CAACsB,qBAAqB,MAAMtB,MAAM,CAACsB,qBAAqB,EAAE;QAC/E,IAAI,CAAC8M,cAAc,CAACC,MAAM,EAAEC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;QAChD,IAAI,CAACZ,uBAAuB,GAAG,IAAI;MACvC;MACA;MACA,IAAI,CAAC,IAAI,CAACjB,MAAM,GAAGzM,MAAM,CAACO,MAAM,MAAMP,MAAM,CAACO,MAAM,IAC/C,CAAC,IAAI,CAACkM,MAAM,GAAGzM,MAAM,CAACa,YAAY,MAAMb,MAAM,CAACa,YAAY,IAC3D,CAAC,IAAI,CAAC4L,MAAM,GAAGzM,MAAM,CAACkB,kBAAkB,MAAMlB,MAAM,CAACkB,kBAAkB,IACvE,CAAC,IAAI,CAACuL,MAAM,GAAGzM,MAAM,CAACqB,iBAAiB,MAAMrB,MAAM,CAACqB,iBAAiB,EAAE;QACvE,IAAI,CAAC+M,cAAc,CAACC,MAAM,EAAEC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;QAChD,IAAI,CAACX,wBAAwB,GAAG,IAAI;MACxC;MACA,IAAI,CAAC5F,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAU,CAAC,CAAC;IAC3C,CAAC;IACD,MAAM2N,WAAW,GAAGA,CAAA,KAAM;MACtB3O,2BAA2B,CAAC,IAAI,CAACoH,eAAe,EAAEnE,GAAG,CAAC;MACtDkK,gBAAgB,CAACxE,IAAI,CAAC1F,GAAG,CAAC;MAC1B,IAAI,CAACkE,gBAAgB,GAAG,KAAK;MAC7B,IAAI,IAAI,CAACC,eAAe,CAAC7G,MAAM,KAAK,CAAC,IAChC,IAAI,CAAC6G,eAAe,CAAC7G,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC6G,eAAe,CAAC,CAAC,CAAC,KAAK,IAAI,CAACC,cAAe,EAAE;QACxF,IAAI,CAACH,WAAW,GAAG,KAAK;MAC5B;MACA,IAAI,IAAI,CAACE,eAAe,CAAC7G,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC+M,WAAW,EAAE;QACvD;QACA,IAAI,CAACA,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,aAAa,EAAE0M,aAAa,EAAE;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACpG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,WAAW,EAAE4M,WAAW,CAAC;QAC5E,IAAI,CAACrM,aAAa,CAAC;UAAEd,IAAI,EAAE;QAAa,CAAC,CAAC;MAC9C;IACJ,CAAC;IACD,IAAI,CAACgP,WAAW,GAAG,MAAM;MACrB,IAAI,CAAC,IAAI,CAACzJ,QAAQ,IAAI,CAAC,IAAI,CAAC+G,WAAW,EACnC;MACJ,IAAI,CAAChH,MAAM,CAAC,CAAC;MACb;MACA,IAAI,CAACe,cAAc,GAAG;QAClB0G,SAAS,EAAE,CAAC,CAAC;QACb1N,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE,CAAC;QACV8H,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACToF,WAAW,EAAE;MACjB,CAAC;MACD,IAAI,CAACrG,eAAe,CAAC/F,IAAI,CAAC,IAAI,CAACgG,cAAc,CAAC;MAC9C;MACA,IAAI,CAACiG,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,aAAa,EAAE0M,aAAa,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACpG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,WAAW,EAAE4M,WAAW,CAAC;MAC5E,IAAI,CAACb,WAAW,CAAC2C,kBAAkB,CAAC,CAAC;MACrC,IAAI,CAAC3C,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,mBAAmB,EAAEmP,mBAAmB,CAAC;MACzF,IAAI,CAAC5C,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,kBAAkB,EAAEoP,kBAAkB,CAAC;MACvF,IAAI,CAAC7C,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,aAAa,EAAEkN,aAAa,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACjN,gBAAgB,CAAC,WAAW,EAAEoN,WAAW,CAAC;MACzEC,aAAa,CAAC,CAAC;IACnB,CAAC;IACD,IAAI,CAACgC,aAAa,GAAG,MAAM;MACvB,IAAI/N,EAAE,EAAEgO,EAAE,EAAEC,EAAE;MACd,IAAI,IAAI,CAACjJ,cAAc,KAAK,IAAI,EAAE;QAC9B,IAAI,CAACyG,eAAe,CAAC,IAAI,CAACzG,cAAc,CAAC;QACzC,IAAI,CAACA,cAAc,GAAG,IAAI;MAC9B;MACA,CAAChF,EAAE,GAAG,IAAI,CAACiL,WAAW,MAAM,IAAI,IAAIjL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2L,aAAa,CAACuC,eAAe,CAAC,CAAC;MAC/F,CAACF,EAAE,GAAG,IAAI,CAAC/C,WAAW,MAAM,IAAI,IAAI+C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,aAAa,CAACzM,mBAAmB,CAAC,mBAAmB,EAAE2O,mBAAmB,CAAC;MAC3I,CAACI,EAAE,GAAG,IAAI,CAAChD,WAAW,MAAM,IAAI,IAAIgD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACtC,aAAa,CAACzM,mBAAmB,CAAC,kBAAkB,EAAE4O,kBAAkB,CAAC;MACzI,IAAI,CAAC7J,MAAM,CAAC,CAAC;IACjB,CAAC;IACD,MAAM4J,mBAAmB,GAAGA,CAAA,KAAM;MAC9B,MAAMT,mBAAmB,GAAG,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACU,aAAa,CAAC0B,kBAAkB,KAAK,IAAI,CAACpC,WAAW;MACtH,IAAI,CAACmC,mBAAmB,EACpB,IAAI,CAACW,aAAa,CAAC,CAAC;IAC5B,CAAC;IACD,MAAMD,kBAAkB,GAAGA,CAAA,KAAM;MAC7B,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACI,qBAAqB,GAAIvL,UAAU,IAAK;MACzC,IAAI,CAACqI,WAAW,GAAGrI,UAAU;MAC7B,IAAI,CAACqI,WAAW,CAACmD,KAAK,CAACC,WAAW,GAAG,MAAM;MAC3C,IAAI,CAACpD,WAAW,CAACmD,KAAK,CAACE,UAAU,GAAG,MAAM;MAC1C,IAAI,CAACrD,WAAW,CAACmD,KAAK,CAACG,gBAAgB,GAAG,MAAM;MAChD,IAAI,CAACtD,WAAW,CAACvM,gBAAgB,CAAC,aAAa,EAAEsM,aAAa,CAAC;MAC/D,IAAI,CAACC,WAAW,CAACvM,gBAAgB,CAAC,eAAe,EAAEoN,WAAW,CAAC;MAC/D,IAAI,CAACb,WAAW,CAACvM,gBAAgB,CAAC,OAAO,EAAE8N,YAAY,EAAE;QAAEX,OAAO,EAAE;MAAM,CAAC,CAAC;MAC5E,IAAI,CAACZ,WAAW,CAACvM,gBAAgB,CAAC,aAAa,EAAEqO,aAAa,CAAC;IACnE,CAAC;IACD,IAAI,CAACyB,wBAAwB,GAAG,MAAM;MAClC,IAAI,CAAC,IAAI,CAACvD,WAAW,EACjB;MACJ,IAAI,CAACA,WAAW,CAACmD,KAAK,CAACC,WAAW,GAAG,EAAE;MACvC,IAAI,CAACpD,WAAW,CAACmD,KAAK,CAACE,UAAU,GAAG,EAAE;MACtC,IAAI,CAACrD,WAAW,CAACmD,KAAK,CAACG,gBAAgB,GAAG,EAAE;MAC5C,IAAI,CAACtD,WAAW,CAAC/L,mBAAmB,CAAC,aAAa,EAAE8L,aAAa,CAAC;MAClE,IAAI,CAACC,WAAW,CAAC/L,mBAAmB,CAAC,eAAe,EAAE4M,WAAW,CAAC;MAClE;MACA;MACA;MACA;MACA,IAAI,CAACb,WAAW,CAAC/L,mBAAmB,CAAC,OAAO,EAAEsN,YAAY,EAAE;QAAEX,OAAO,EAAE;MAAM,CAAC,CAAC;MAC/E,IAAI,CAACZ,WAAW,CAAC/L,mBAAmB,CAAC,aAAa,EAAE6N,aAAa,CAAC;MAClE;MACA,IAAI,CAAC9B,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,aAAa,EAAE0M,aAAa,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACpG,IAAI,CAACZ,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,WAAW,EAAE4M,WAAW,CAAC;MAC5E,IAAI,CAACb,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,mBAAmB,EAAE2O,mBAAmB,CAAC;MAC5F,IAAI,CAAC5C,WAAW,CAACU,aAAa,CAACzM,mBAAmB,CAAC,kBAAkB,EAAE4O,kBAAkB,CAAC;IAC9F,CAAC;IACD,IAAI,CAAC7J,MAAM,GAAG,MAAM;MAChB,IAAI,IAAI,CAACE,MAAM,KAAKzM,MAAM,CAACG,IAAI,EAC3B;MACJ,IAAI,CAACsM,MAAM,GAAGzM,MAAM,CAACG,IAAI;MACzB,IAAI,CAACkN,eAAe,CAAC7G,MAAM,GAAG,CAAC;MAC/BoO,WAAW,CAAC,CAAC;IACjB,CAAC;IACD,IAAI1J,UAAU,EACV,IAAI,CAAC6L,OAAO,CAAC7L,UAAU,CAAC;IAC5B,IAAI,CAAC8L,MAAM,CAAC,CAAC,CAAC;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAIpV,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC8M,OAAO;EACvB;EACA,IAAI9M,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAAC8M,OAAO,GAAG9M,MAAM;IACrB,IAAI,CAACqV,cAAc,CAAC,CAAC;IACrB,IAAI,CAACvI,OAAO,CAACwI,sBAAsB,CAAC,CAAC;IACrC,IAAI,CAAC7E,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACtF,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIoK,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3K,QAAQ;EACxB;EACA,IAAI2K,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC3K,QAAQ,GAAG2K,OAAO;IACvB,IAAI,CAAC,IAAI,CAAC5D,WAAW,EACjB;IACJ,IAAI4D,OAAO,EAAE;MACT,IAAI,CAAC5D,WAAW,CAACmD,KAAK,CAACC,WAAW,GAAG,MAAM;MAC3C,IAAI,CAACpD,WAAW,CAACmD,KAAK,CAACE,UAAU,GAAG,MAAM;MAC1C,IAAI,CAACrD,WAAW,CAACmD,KAAK,CAACG,gBAAgB,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAACtK,MAAM,CAAC,CAAC;MACb,IAAI,CAACgH,WAAW,CAACmD,KAAK,CAACC,WAAW,GAAG,EAAE;MACvC,IAAI,CAACpD,WAAW,CAACmD,KAAK,CAACE,UAAU,GAAG,EAAE;MACtC,IAAI,CAACrD,WAAW,CAACmD,KAAK,CAACG,gBAAgB,GAAG,EAAE;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIO,MAAMA,CAAA,EAAG;IACT,OAAO,CAAC,IAAI,CAACvK,UAAU;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIwK,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC5K,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACI,IAAIiE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoB,UAAU,CAACrB,MAAM;EACjC;EACA,IAAIC,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACoB,UAAU,CAACrB,MAAM,KAAKC,QAAQ,IACnC,IAAI,CAACF,aAAa,CAACC,MAAM,KAAKC,QAAQ,EACtC;IACJ,IAAI,CAACoB,UAAU,CAACrB,MAAM,GAAGC,QAAQ;IACjC,IAAI,CAACF,aAAa,CAACC,MAAM,GAAGC,QAAQ;IACpC,IAAI,CAAC3D,YAAY,GAAG,IAAI;EAC5B;EACA;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIuK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACxF,UAAU,CAAC9B,KAAK;EAChC;EACA,IAAIsH,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACxF,UAAU,CAAC9B,KAAK,KAAKsH,YAAY,IACtC,IAAI,CAAC9G,aAAa,CAACR,KAAK,KAAKsH,YAAY,EACzC;IACJ,IAAI,CAACxF,UAAU,CAAC9B,KAAK,GAAGsH,YAAY;IACpC,IAAI,CAAC9G,aAAa,CAACR,KAAK,GAAGsH,YAAY;IACvC,IAAI,CAACvK,YAAY,GAAG,IAAI;EAC5B;EACA;EACA;AACJ;AACA;AACA;EACI,IAAIwK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACzF,UAAU,CAAC7B,GAAG;EAC9B;EACA,IAAIsH,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACzF,UAAU,CAAC7B,GAAG,KAAKsH,UAAU,IAClC,IAAI,CAAC/G,aAAa,CAACP,GAAG,KAAKsH,UAAU,EACrC;IACJ,IAAI,CAACzF,UAAU,CAAC7B,GAAG,GAAGsH,UAAU;IAChC,IAAI,CAAC/G,aAAa,CAACP,GAAG,GAAGsH,UAAU;IACnC,IAAI,CAACxK,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIyK,sBAAsBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAAC1K,uBAAuB;EACvC;EACA,IAAI0K,sBAAsBA,CAACA,sBAAsB,EAAE;IAC/C,IAAI,CAAC1K,uBAAuB,GAAG0K,sBAAsB;IACrD,IAAI,CAACzK,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI0K,eAAeA,CAACA,eAAe,EAAE;IACjC,IAAI,CAAClK,gBAAgB,CAACiC,KAAK,GAAGpN,KAAK,CAACqV,eAAe,CAACjI,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAChE,IAAI,CAACjC,gBAAgB,CAAC6B,MAAM,GAAGhN,KAAK,CAACqV,eAAe,CAACrI,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC7B,gBAAgB,CAACvJ,CAAC,GAAG5B,KAAK,CAACqV,eAAe,CAACzT,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAACuJ,gBAAgB,CAACiC,KAAK,CAAC;IACtF,IAAI,CAACjC,gBAAgB,CAAC5I,CAAC,GAAGvC,KAAK,CAACqV,eAAe,CAAC9S,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC4I,gBAAgB,CAAC6B,MAAM,CAAC;EAC3F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpI,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC7B,KAAK,CAACF,gBAAgB,CAACC,IAAI,EAAEC,QAAQ,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,mBAAmBA,CAACP,IAAI,EAAEC,QAAQ,EAAE;IAChC,KAAK,CAACM,mBAAmB,CAACP,IAAI,EAAEC,QAAQ,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgJ,MAAMA,CAACoH,YAAY,EAAEC,UAAU,EAAEG,gBAAgB,GAAG,KAAK,EAAE;IACvD,OAAO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnH,aAAa,CAACR,KAAK,GAAGsH,YAAY,EAAE,IAAI,CAAC9G,aAAa,CAACP,GAAG,GAAGsH,UAAU,EAAEG,gBAAgB,CAAC;EACxH;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,eAAeA,CAACN,YAAY,EAAEI,gBAAgB,GAAG,KAAK,EAAE;IACpD,OAAO,IAAI,CAACC,QAAQ,CAACL,YAAY,EAAE,IAAI,CAAC9G,aAAa,CAACP,GAAG,EAAEyH,gBAAgB,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,aAAaA,CAACN,UAAU,EAAEG,gBAAgB,GAAG,KAAK,EAAE;IAChD,OAAO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnH,aAAa,CAACR,KAAK,EAAEuH,UAAU,EAAEG,gBAAgB,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAACL,YAAY,EAAEC,UAAU,EAAEG,gBAAgB,GAAG,KAAK,EAAE;IACzD,IAAI,CAAClK,wBAAwB,GAAG,KAAK;IACrC,MAAMwC,KAAK,GAAG5N,KAAK,CAACkV,YAAY,EAAE,IAAI,CAACjM,eAAe,EAAE,IAAI,CAACC,eAAe,CAAC;IAC7E,MAAM2E,GAAG,GAAG7N,KAAK,CAACmV,UAAU,EAAE,IAAI,CAACpM,aAAa,EAAE,IAAI,CAACC,aAAa,CAAC;IACrE,IAAI,CAACoF,aAAa,CAACR,KAAK,GAAGA,KAAK;IAChC,IAAI,CAACQ,aAAa,CAACP,GAAG,GAAGA,GAAG;IAC5B,IAAI,CAACO,aAAa,CAACsH,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC/K,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAAC5F,UAAU,CAAC9B,KAAK,GAAG,IAAI,CAACQ,aAAa,CAACR,KAAK;MAChD,IAAI,CAAC8B,UAAU,CAAC7B,GAAG,GAAG,IAAI,CAACO,aAAa,CAACP,GAAG;IAChD;IACA,MAAM8H,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC9B,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,EAAE,IAAI,CAAC3D,aAAa,CAAC,IAC7EzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC7B,GAAG,EAAE,IAAI,CAACO,aAAa,CAACP,GAAG,EAAE,IAAI,CAAC5D,aAAa,CAAC;IACrF,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,KAAKA,CAACvH,QAAQ,EAAEgH,gBAAgB,GAAG,KAAK,EAAE;IACtC,OAAO,IAAI,CAACQ,OAAO,CAAC,IAAI,CAAC1H,aAAa,CAACC,MAAM,GAAGC,QAAQ,EAAEgH,gBAAgB,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,OAAOA,CAACxH,QAAQ,EAAEgH,gBAAgB,GAAG,KAAK,EAAE;IACxC,IAAI,CAACjK,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACI,mBAAmB,GAAGrM,eAAe,CAACrB,IAAI;IAC/C,IAAI,CAACwM,aAAa,GAAG,CAAC;IACtB,OAAO,IAAI,CAACkE,eAAe,CAACzO,KAAK,CAACsO,QAAQ,EAAE,IAAI,CAACnF,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC,EAAEkM,gBAAgB,CAAC;EACtG;EACA7G,eAAeA,CAACH,QAAQ,EAAEgH,gBAAgB,GAAG,KAAK,EAAE;IAChD,MAAMS,UAAU,GAAG,IAAI,CAAC3H,aAAa,CAACC,MAAM;IAC5C,MAAM2H,WAAW,GAAG,IAAI,CAAC9L,cAAc,CAAC9F,MAAM,IAAI,CAAC;IACnD,IAAI4R,WAAW,EAAE;MACb,MAAMC,0BAA0B,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACxD,MAAMC,UAAU,GAAG3V,YAAY,CAACyV,0BAA0B,EAAE,IAAI,CAACvG,UAAU,CAACrB,MAAM,CAAC;MACnF,MAAM+H,SAAS,GAAGL,UAAU,GAAGzH,QAAQ;MACvC,IAAI,CAAC8H,SAAS,IAAID,UAAU,EACxB,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B,IAAI,CAAClI,aAAa,CAACC,MAAM,GAAG1O,IAAI,CAACO,GAAG,CAACoO,QAAQ,EAAE2H,0BAA0B,CAAC;IAC9E,CAAC,MACI;MACD,IAAI,CAAC7H,aAAa,CAACC,MAAM,GAAGC,QAAQ;IACxC;IACA,IAAI,CAAC3D,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAAC5F,UAAU,CAACrB,MAAM,GAAG,IAAI,CAACD,aAAa,CAACC,MAAM;IACtD;IACA,MAAMsH,kBAAkB,GAAG,CAACL,gBAAgB,IAAI9U,YAAY,CAAC,IAAI,CAACkP,UAAU,CAACrB,MAAM,EAAE,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,IAAI,CAACpE,aAAa,CAAC;IACnI,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjH,YAAYA,CAACJ,QAAQ,EAAEgH,gBAAgB,GAAG,KAAK,EAAE;IAC7C,IAAI,CAAC9F,UAAU,CAAC+G,GAAG,CAAC,IAAI,CAACC,mBAAmB,CAACtP,gBAAgB,CAAC,CAACuP,cAAc,CAACnI,QAAQ,CAAC,CAAC;IACxF,IAAI,CAACgH,gBAAgB,EAAE;MACnB,IAAI,CAAC3I,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;IACtC;IACA,MAAMmG,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAC/K,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC/DzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAACpK,CAAC,EAAE,IAAI,CAACiN,UAAU,CAACjN,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAClK,CAAC,EAAE,IAAI,CAAC+M,UAAU,CAAC/M,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC;IAC3E,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIxI,IAAIA,CAACuJ,QAAQ,EAAEpB,gBAAgB,GAAG,KAAK,EAAE;IACrC,OAAO,IAAI,CAACrG,MAAM,CAAC,IAAI,CAACa,QAAQ,GAAG4G,QAAQ,EAAEpB,gBAAgB,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIrG,MAAMA,CAAC9B,IAAI,EAAEmI,gBAAgB,GAAG,KAAK,EAAE;IACnC,IAAI,CAAC9J,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACsE,QAAQ,GAAG9P,KAAK,CAACmN,IAAI,EAAE,IAAI,CAAC7D,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC;IACvD,IAAI,CAACoB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAACtG,KAAK,GAAG,IAAI,CAACc,QAAQ;IAC9B;IACA,MAAM6F,kBAAkB,GAAG,CAACL,gBAAgB,IAAI9U,YAAY,CAAC,IAAI,CAACwO,KAAK,EAAE,IAAI,CAACc,QAAQ,EAAE,IAAI,CAAC7F,aAAa,CAAC;IAC3G,IAAI,CAACO,YAAY,GAAG,CAAC;IACrB,OAAO,IAAI,CAACoL,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;EACIgB,GAAGA,CAAC/U,CAAC,EAAEW,CAAC,EAAE+S,gBAAgB,GAAG,KAAK,EAAE;IAChC/Q,OAAO,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACjD,OAAO,IAAI,CAACiJ,KAAK,CAAC7L,CAAC,EAAEW,CAAC,EAAE+S,gBAAgB,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7H,KAAKA,CAAC7L,CAAC,EAAEW,CAAC,EAAE+S,gBAAgB,GAAG,KAAK,EAAE;IAClC,IAAI,CAAChJ,OAAO,CAACsK,YAAY,CAAC,CAAC;IAC3BzP,QAAQ,CAAC0P,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;IACpD1P,QAAQ,CAACyP,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;IACpD3P,QAAQ,CAACsP,cAAc,CAAC7U,CAAC,CAAC;IAC1BwF,QAAQ,CAACqP,cAAc,CAAC,CAAClU,CAAC,CAAC;IAC3B,MAAMgK,MAAM,GAAGxF,IAAI,CAACyF,IAAI,CAACrF,QAAQ,CAAC,CAACoP,GAAG,CAACnP,QAAQ,CAAC;IAChD,MAAM2P,EAAE,GAAG/P,IAAI,CAACwF,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC+G,GAAG,CAAChK,MAAM,CAAC;IACjD,OAAO,IAAI,CAACyK,MAAM,CAACD,EAAE,CAACnV,CAAC,EAAEmV,EAAE,CAACxU,CAAC,EAAEwU,EAAE,CAACtU,CAAC,EAAE6S,gBAAgB,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5H,OAAOA,CAACY,QAAQ,EAAEgH,gBAAgB,GAAG,KAAK,EAAE;IACxCvO,IAAI,CAAC8P,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;IAChD/P,IAAI,CAACkQ,YAAY,CAAC,IAAI,CAAC3K,OAAO,CAAC8C,EAAE,EAAErI,IAAI,CAAC;IACxCA,IAAI,CAAC0P,cAAc,CAACnI,QAAQ,CAAC;IAC7B,MAAMyI,EAAE,GAAG/P,IAAI,CAACwF,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC+G,GAAG,CAACxP,IAAI,CAAC;IAC/C,OAAO,IAAI,CAACiQ,MAAM,CAACD,EAAE,CAACnV,CAAC,EAAEmV,EAAE,CAACxU,CAAC,EAAEwU,EAAE,CAACtU,CAAC,EAAE6S,gBAAgB,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4B,OAAOA,CAAClK,MAAM,EAAEsI,gBAAgB,GAAG,KAAK,EAAE;IACtCvO,IAAI,CAACyF,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC8C,EAAE,CAAC,CAACqH,cAAc,CAACzJ,MAAM,CAAC;IACjD,OAAO,IAAI,CAACgK,MAAM,CAAC,IAAI,CAACxH,UAAU,CAAC5N,CAAC,GAAGmF,IAAI,CAACnF,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAACjN,CAAC,GAAGwE,IAAI,CAACxE,CAAC,EAAE,IAAI,CAACiN,UAAU,CAAC/M,CAAC,GAAGsE,IAAI,CAACtE,CAAC,EAAE6S,gBAAgB,CAAC;EAC5H;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI0B,MAAMA,CAACpV,CAAC,EAAEW,CAAC,EAAEE,CAAC,EAAE6S,gBAAgB,GAAG,KAAK,EAAE;IACtC,IAAI,CAAChK,uBAAuB,GAAG,KAAK;IACpC,MAAMiB,MAAM,GAAGxF,IAAI,CAAChD,GAAG,CAACnC,CAAC,EAAEW,CAAC,EAAEE,CAAC,CAAC,CAACiK,GAAG,CAAC,IAAI,CAAC8C,UAAU,CAAC;IACrD,IAAI,CAAC2H,kBAAkB,CAAC,IAAI,CAAC3H,UAAU,EAAEjD,MAAM,EAAE,IAAI,CAACvC,gBAAgB,CAAC;IACvE,IAAI,CAACW,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAAC3I,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;IACtC;IACA,MAAMmG,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAC/K,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC/DzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAACpK,CAAC,EAAE,IAAI,CAACiN,UAAU,CAACjN,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAClK,CAAC,EAAE,IAAI,CAAC+M,UAAU,CAAC/M,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC;IAC3E,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyB,iBAAiBA,CAACxV,CAAC,EAAEW,CAAC,EAAEE,CAAC,EAAE6S,gBAAgB,GAAG,KAAK,EAAE;IACjD,MAAM+B,KAAK,GAAGtQ,IAAI,CAAChD,GAAG,CAACnC,CAAC,EAAEW,CAAC,EAAEE,CAAC,CAAC;IAC/B,MAAM6U,SAAS,GAAGD,KAAK,CAAC3K,GAAG,CAAC,IAAI,CAAC8C,UAAU,CAAC,CAAC+H,SAAS,CAAC,CAAC;IACxD,MAAM9K,QAAQ,GAAG6K,SAAS,CAACb,cAAc,CAAC,CAAC,IAAI,CAACrI,aAAa,CAACC,MAAM,CAAC,CAACkI,GAAG,CAAC,IAAI,CAAC/G,UAAU,CAAC;IAC1F,OAAO,IAAI,CAACgI,WAAW,CAAC/K,QAAQ,CAAC7K,CAAC,EAAE6K,QAAQ,CAAClK,CAAC,EAAEkK,QAAQ,CAAChK,CAAC,EAAE6S,gBAAgB,CAAC;EACjF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImC,QAAQA,CAACC,YAAY,EAAEpC,gBAAgB,EAAE;IAAEqC,KAAK,GAAG,KAAK;IAAEC,WAAW,GAAG,CAAC;IAAEC,YAAY,GAAG,CAAC;IAAEC,aAAa,GAAG,CAAC;IAAEC,UAAU,GAAG;EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;IACnI,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,IAAI,GAAGP,YAAY,CAACQ,MAAM,GAC1BxQ,MAAM,CAAC8E,IAAI,CAACkL,YAAY,CAAC,GACzBhQ,MAAM,CAACyQ,aAAa,CAACT,YAAY,CAAC;IACxC,IAAIO,IAAI,CAACG,OAAO,CAAC,CAAC,EAAE;MAChB7T,OAAO,CAACC,IAAI,CAAC,qEAAqE,CAAC;MACnF6R,OAAO,CAACC,OAAO,CAAC,CAAC;IACrB;IACA;IACA,MAAM1I,KAAK,GAAGjN,WAAW,CAAC,IAAI,CAACyN,aAAa,CAACR,KAAK,EAAE/N,OAAO,CAAC;IAC5D,MAAMgO,GAAG,GAAGlN,WAAW,CAAC,IAAI,CAACyN,aAAa,CAACP,GAAG,EAAEhO,OAAO,CAAC;IACxDmY,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAACqQ,QAAQ,CAAC3H,KAAK,EAAEC,GAAG,EAAEyH,gBAAgB,CAAC,CAAC;IAC1D,MAAM+C,MAAM,GAAGtR,IAAI,CAACuR,gBAAgB,CAAC,IAAI,CAAClK,aAAa,CAAC,CAACmJ,SAAS,CAAC,CAAC;IACpE,MAAMgB,QAAQ,GAAG1Q,YAAY,CAACsH,kBAAkB,CAACkJ,MAAM,EAAExR,OAAO,CAAC;IACjE,MAAM2R,aAAa,GAAGhY,YAAY,CAACb,IAAI,CAACY,GAAG,CAAC8X,MAAM,CAAC9V,CAAC,CAAC,EAAE,CAAC,CAAC;IACzD,IAAIiW,aAAa,EAAE;MACfD,QAAQ,CAACE,QAAQ,CAAC3Q,YAAY,CAAC4Q,gBAAgB,CAAC9R,OAAO,EAAEgH,KAAK,CAAC,CAAC;IACpE;IACA2K,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAACpJ,oBAAoB,CAAC;IAC5C;IACA,MAAMsJ,EAAE,GAAGhR,MAAM,CAACiR,SAAS,CAAC,CAAC;IAC7B;IACA5R,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC/X,GAAG,CAAC,CAAC0P,eAAe,CAAC2I,QAAQ,CAAC;IAC7CI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC/X,GAAG,CAAC,CAAC4Y,IAAI,CAACb,IAAI,CAAC9X,GAAG,CAACyB,CAAC,CAAC,CAACgO,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC/X,GAAG,CAAC,CAAC6Y,IAAI,CAACd,IAAI,CAAC9X,GAAG,CAACoC,CAAC,CAAC,CAACqN,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC9X,GAAG,CAAC,CAAC6Y,IAAI,CAACf,IAAI,CAAC/X,GAAG,CAACuC,CAAC,CAAC,CAACmN,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC/X,GAAG,CAAC,CAAC8Y,IAAI,CAACf,IAAI,CAAC9X,GAAG,CAACsC,CAAC,CAAC,CAACmN,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC9X,GAAG,CAAC,CAAC4Y,IAAI,CAACd,IAAI,CAAC/X,GAAG,CAACqC,CAAC,CAAC,CAACqN,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC9X,GAAG,CAAC,CAAC2Y,IAAI,CAACb,IAAI,CAAC/X,GAAG,CAAC0B,CAAC,CAAC,CAACgO,eAAe,CAAC2I,QAAQ,CAAC;IAC9DI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACAA,IAAI,CAACwF,IAAI,CAACyL,IAAI,CAAC9X,GAAG,CAAC,CAACyP,eAAe,CAAC2I,QAAQ,CAAC;IAC7CI,EAAE,CAACE,aAAa,CAAC7R,IAAI,CAAC;IACtB;IACA2R,EAAE,CAACzY,GAAG,CAAC0B,CAAC,IAAIgW,WAAW;IACvBe,EAAE,CAACzY,GAAG,CAACqC,CAAC,IAAIuV,aAAa;IACzBa,EAAE,CAACxY,GAAG,CAACyB,CAAC,IAAIiW,YAAY;IACxBc,EAAE,CAACxY,GAAG,CAACoC,CAAC,IAAIwV,UAAU;IACtBQ,QAAQ,CAACpJ,kBAAkB,CAACtI,OAAO,EAAEwR,MAAM,CAAC;IAC5C,IAAIG,aAAa,EAAE;MACfD,QAAQ,CAACU,WAAW,CAACnR,YAAY,CAACyH,MAAM,CAAC,CAAC,CAAC;IAC/C;IACAgJ,QAAQ,CAACU,WAAW,CAAC,IAAI,CAAC/J,aAAa,CAAC;IACxC,MAAMgK,MAAM,GAAGP,EAAE,CAACQ,OAAO,CAACpS,IAAI,CAAC;IAC/B,MAAMqS,MAAM,GAAGT,EAAE,CAACU,SAAS,CAACrS,IAAI,CAAC,CAAC4I,eAAe,CAAC2I,QAAQ,CAAC;IAC3D,IAAIhZ,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,EAAE;MACnC,MAAMgC,QAAQ,GAAG,IAAI,CAACgL,mBAAmB,CAACJ,MAAM,CAACtX,CAAC,EAAEsX,MAAM,CAAC3W,CAAC,EAAE2W,MAAM,CAACzW,CAAC,EAAEkV,KAAK,CAAC;MAC9EK,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC8R,MAAM,CAACoC,MAAM,CAACxX,CAAC,EAAEwX,MAAM,CAAC7W,CAAC,EAAE6W,MAAM,CAAC3W,CAAC,EAAE6S,gBAAgB,CAAC,CAAC;MAC1E0C,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC4Q,OAAO,CAACxH,QAAQ,EAAEgH,gBAAgB,CAAC,CAAC;MACvD0C,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAACqI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE+H,gBAAgB,CAAC,CAAC;IACjE,CAAC,MACI,IAAI7V,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,EAAE;MACzC,MAAM9M,MAAM,GAAG,IAAI,CAAC8M,OAAO;MAC3B,MAAMc,KAAK,GAAG5N,MAAM,CAACyN,KAAK,GAAGzN,MAAM,CAAC0N,IAAI;MACxC,MAAMF,MAAM,GAAGxN,MAAM,CAAC6N,GAAG,GAAG7N,MAAM,CAAC8N,MAAM;MACzC,MAAMH,IAAI,GAAGwK,KAAK,GAAGhY,IAAI,CAACQ,GAAG,CAACiN,KAAK,GAAG8L,MAAM,CAACtX,CAAC,EAAEoL,MAAM,GAAGkM,MAAM,CAAC3W,CAAC,CAAC,GAAG5C,IAAI,CAACO,GAAG,CAACkN,KAAK,GAAG8L,MAAM,CAACtX,CAAC,EAAEoL,MAAM,GAAGkM,MAAM,CAAC3W,CAAC,CAAC;MAClHyV,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC8R,MAAM,CAACoC,MAAM,CAACxX,CAAC,EAAEwX,MAAM,CAAC7W,CAAC,EAAE6W,MAAM,CAAC3W,CAAC,EAAE6S,gBAAgB,CAAC,CAAC;MAC1E0C,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC+J,MAAM,CAAC9B,IAAI,EAAEmI,gBAAgB,CAAC,CAAC;MAClD0C,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAACqI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE+H,gBAAgB,CAAC,CAAC;IACjE;IACA,OAAOe,OAAO,CAACkD,GAAG,CAACvB,QAAQ,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwB,WAAWA,CAACC,YAAY,EAAEnE,gBAAgB,EAAE;IACxC,MAAM0C,QAAQ,GAAG,EAAE;IACnB,MAAM0B,UAAU,GAAG,YAAY,IAAID,YAAY;IAC/C,MAAME,cAAc,GAAGD,UAAU,GAC7BzR,cAAc,CAAC2R,oBAAoB,CAACH,YAAY,EAAE7R,OAAO,CAAC,GAC1DA,OAAO,CAAC4E,IAAI,CAACiN,YAAY,CAAC;IAC9BzB,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC8R,MAAM,CAAC2C,cAAc,CAACP,MAAM,CAACxX,CAAC,EAAE+X,cAAc,CAACP,MAAM,CAAC7W,CAAC,EAAEoX,cAAc,CAACP,MAAM,CAAC3W,CAAC,EAAE6S,gBAAgB,CAAC,CAAC;IACvH,IAAI/V,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,EAAE;MACnC,MAAMuN,aAAa,GAAG,IAAI,CAACC,sBAAsB,CAACH,cAAc,CAACtL,MAAM,CAAC;MACxE2J,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC4Q,OAAO,CAAC+D,aAAa,EAAEvE,gBAAgB,CAAC,CAAC;IAChE,CAAC,MACI,IAAI7V,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,EAAE;MACzC,MAAMc,KAAK,GAAG,IAAI,CAACd,OAAO,CAACW,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,IAAI;MACpD,MAAMF,MAAM,GAAG,IAAI,CAACV,OAAO,CAACe,GAAG,GAAG,IAAI,CAACf,OAAO,CAACgB,MAAM;MACrD,MAAMyM,QAAQ,GAAG,CAAC,GAAGJ,cAAc,CAACtL,MAAM;MAC1C,MAAMlB,IAAI,GAAGxN,IAAI,CAACO,GAAG,CAACkN,KAAK,GAAG2M,QAAQ,EAAE/M,MAAM,GAAG+M,QAAQ,CAAC;MAC1D/B,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAAC+J,MAAM,CAAC9B,IAAI,EAAEmI,gBAAgB,CAAC,CAAC;IACtD;IACA0C,QAAQ,CAAC9S,IAAI,CAAC,IAAI,CAACqI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE+H,gBAAgB,CAAC,CAAC;IAC7D,OAAOe,OAAO,CAACkD,GAAG,CAACvB,QAAQ,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgC,SAASA,CAACC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE9X,OAAO,EAAEC,OAAO,EAAEE,OAAO,EAAE8S,gBAAgB,GAAG,KAAK,EAAE;IAC5F,IAAI,CAAClK,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACG,mBAAmB,GAAGrM,eAAe,CAACrB,IAAI;IAC/C,IAAI,CAACwM,aAAa,GAAG,CAAC;IACtB,MAAMjJ,MAAM,GAAG0F,IAAI,CAACjD,GAAG,CAAC1B,OAAO,EAAEC,OAAO,EAAEE,OAAO,CAAC;IAClD,MAAMiK,QAAQ,GAAG1F,IAAI,CAAChD,GAAG,CAACkW,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC;IAC1D,IAAI,CAAC3K,UAAU,CAAChD,IAAI,CAAClL,MAAM,CAAC;IAC5B,IAAI,CAAC8M,aAAa,CAACuB,cAAc,CAAClD,QAAQ,CAACC,GAAG,CAACpL,MAAM,CAAC,CAACsO,eAAe,CAAC,IAAI,CAACV,aAAa,CAAC,CAAC;IAC3F,IAAI,CAACkL,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACzP,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAAC3I,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;MAClC,IAAI,CAACE,UAAU,CAAClD,IAAI,CAAC,IAAI,CAAC4B,aAAa,CAAC;IAC5C;IACA,MAAMuH,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAC/K,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC/DzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAACpK,CAAC,EAAE,IAAI,CAACiN,UAAU,CAACjN,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAClK,CAAC,EAAE,IAAI,CAAC+M,UAAU,CAAC/M,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC9B,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,EAAE,IAAI,CAAC3D,aAAa,CAAC,IACjFzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC7B,GAAG,EAAE,IAAI,CAACO,aAAa,CAACP,GAAG,EAAE,IAAI,CAAC5D,aAAa,CAAC,IAC7EzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAACrB,MAAM,EAAE,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,IAAI,CAACpE,aAAa,CAAC;IAC3F,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0E,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,CAAC,EAAE5F,gBAAgB,GAAG,KAAK,EAAE;IACxK,IAAI,CAAClK,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACG,mBAAmB,GAAGrM,eAAe,CAACrB,IAAI;IAC/C,IAAI,CAACwM,aAAa,GAAG,CAAC;IACtB,MAAM4Q,OAAO,GAAGpU,IAAI,CAAChD,GAAG,CAAC0W,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;IACtD,MAAMS,SAAS,GAAGpU,IAAI,CAACjD,GAAG,CAACuW,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAC9DhT,WAAW,CAACmI,cAAc,CAACyL,SAAS,CAAC1O,GAAG,CAACyO,OAAO,CAAC,CAACvL,eAAe,CAAC,IAAI,CAACV,aAAa,CAAC,CAAC;IACtF,MAAMmM,OAAO,GAAGpU,IAAI,CAAClD,GAAG,CAACgX,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;IACtD,MAAMK,SAAS,GAAGtU,IAAI,CAACjD,GAAG,CAAC6W,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAC9DrT,WAAW,CAACkI,cAAc,CAAC2L,SAAS,CAAC5O,GAAG,CAAC2O,OAAO,CAAC,CAACzL,eAAe,CAAC,IAAI,CAACV,aAAa,CAAC,CAAC;IACtF,IAAI,CAACM,UAAU,CAAChD,IAAI,CAAC2O,OAAO,CAACI,IAAI,CAACF,OAAO,EAAEH,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,MAAMM,UAAU,GAAG/T,WAAW,CAACmG,KAAK,GAAGpG,WAAW,CAACoG,KAAK;IACxD,MAAM6N,QAAQ,GAAGhU,WAAW,CAACoG,GAAG,GAAGrG,WAAW,CAACqG,GAAG;IAClD,MAAM6N,WAAW,GAAGjU,WAAW,CAAC4G,MAAM,GAAG7G,WAAW,CAAC6G,MAAM;IAC3D,IAAI,CAACD,aAAa,CAACrK,GAAG,CAACyD,WAAW,CAAC6G,MAAM,GAAGqN,WAAW,GAAGR,CAAC,EAAE1T,WAAW,CAACqG,GAAG,GAAG4N,QAAQ,GAAGP,CAAC,EAAE1T,WAAW,CAACoG,KAAK,GAAG4N,UAAU,GAAGN,CAAC,CAAC;IAChI,IAAI,CAACd,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACzP,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EAAE;MACnB,IAAI,CAAC3I,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;MAClC,IAAI,CAACE,UAAU,CAAClD,IAAI,CAAC,IAAI,CAAC4B,aAAa,CAAC;IAC5C;IACA,MAAMuH,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAC/K,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC/DzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAACpK,CAAC,EAAE,IAAI,CAACiN,UAAU,CAACjN,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACmM,OAAO,CAAClK,CAAC,EAAE,IAAI,CAAC+M,UAAU,CAAC/M,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC,IACnEzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC9B,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,EAAE,IAAI,CAAC3D,aAAa,CAAC,IACjFzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAAC7B,GAAG,EAAE,IAAI,CAACO,aAAa,CAACP,GAAG,EAAE,IAAI,CAAC5D,aAAa,CAAC,IAC7EzJ,YAAY,CAAC,IAAI,CAACkP,UAAU,CAACrB,MAAM,EAAE,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,IAAI,CAACpE,aAAa,CAAC;IAC3F,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,WAAWA,CAACyC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE7E,gBAAgB,GAAG,KAAK,EAAE;IACnE,OAAO,IAAI,CAAC0E,SAAS,CAACC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE,IAAI,CAAC3K,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAACjN,CAAC,EAAE,IAAI,CAACiN,UAAU,CAAC/M,CAAC,EAAE6S,gBAAgB,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqG,SAASA,CAACtZ,OAAO,EAAEC,OAAO,EAAEE,OAAO,EAAE8S,gBAAgB,GAAG,KAAK,EAAE;IAC3D,MAAMsG,GAAG,GAAG,IAAI,CAACC,WAAW,CAAC9U,IAAI,CAAC;IAClC,MAAM+U,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC4B,GAAG,CAACha,CAAC,EAAEga,GAAG,CAACrZ,CAAC,EAAEqZ,GAAG,CAACnZ,CAAC,EAAEJ,OAAO,EAAEC,OAAO,EAAEE,OAAO,EAAE8S,gBAAgB,CAAC;IAChG;IACA,IAAI,CAAClH,aAAa,CAACP,GAAG,GAAG7N,KAAK,CAAC,IAAI,CAACoO,aAAa,CAACP,GAAG,EAAE,IAAI,CAAC9E,aAAa,EAAE,IAAI,CAACC,aAAa,CAAC;IAC9F,OAAO8S,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvO,cAAcA,CAAC3L,CAAC,EAAEW,CAAC,EAAEE,CAAC,EAAE6S,gBAAgB,GAAG,KAAK,EAAE;IAC9C,IAAI,CAAC/J,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACiC,eAAe,CAACzJ,GAAG,CAACnC,CAAC,EAAEW,CAAC,EAAEE,CAAC,CAAC;IACjC,IAAI,CAACkI,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC2K,gBAAgB,EACjB,IAAI,CAAC7F,YAAY,CAACjD,IAAI,CAAC,IAAI,CAACgB,eAAe,CAAC;IAChD,MAAMmI,kBAAkB,GAAG,CAACL,gBAAgB,IACxC9U,YAAY,CAAC,IAAI,CAACiP,YAAY,CAAC7N,CAAC,EAAE,IAAI,CAAC4L,eAAe,CAAC5L,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IACzEzJ,YAAY,CAAC,IAAI,CAACiP,YAAY,CAAClN,CAAC,EAAE,IAAI,CAACiL,eAAe,CAACjL,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IAC7EzJ,YAAY,CAAC,IAAI,CAACiP,YAAY,CAAChN,CAAC,EAAE,IAAI,CAAC+K,eAAe,CAAC/K,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC;IACrF,OAAO,IAAI,CAAC2L,oBAAoB,CAACD,kBAAkB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIoG,aAAaA,CAAC1Z,OAAO,EAAEC,OAAO,EAAEE,OAAO,EAAE;IACrC,IAAI,CAAC8J,OAAO,CAAC0P,iBAAiB,CAAC,CAAC;IAChC7U,QAAQ,CAAC0P,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAAC2P,kBAAkB,EAAE,CAAC,CAAC;IAChE7U,QAAQ,CAACyP,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAAC2P,kBAAkB,EAAE,CAAC,CAAC;IAChE5U,QAAQ,CAACwP,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAAC2P,kBAAkB,EAAE,CAAC,CAAC;IAChE,MAAMxP,QAAQ,GAAG1F,IAAI,CAAChD,GAAG,CAAC1B,OAAO,EAAEC,OAAO,EAAEE,OAAO,CAAC;IACpD,MAAM8L,QAAQ,GAAG7B,QAAQ,CAACyP,UAAU,CAAC,IAAI,CAAC5P,OAAO,CAACG,QAAQ,CAAC;IAC3D,MAAM0P,aAAa,GAAG1P,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACG,QAAQ,CAAC;IACzDtF,QAAQ,CAACsP,cAAc,CAAC0F,aAAa,CAACva,CAAC,CAAC;IACxCwF,QAAQ,CAACqP,cAAc,CAAC0F,aAAa,CAAC5Z,CAAC,CAAC;IACxC8E,QAAQ,CAACoP,cAAc,CAAC0F,aAAa,CAAC1Z,CAAC,CAAC;IACxCsE,IAAI,CAACyF,IAAI,CAACrF,QAAQ,CAAC,CAACoP,GAAG,CAACnP,QAAQ,CAAC,CAACmP,GAAG,CAAClP,QAAQ,CAAC;IAC/CN,IAAI,CAACtE,CAAC,GAAGsE,IAAI,CAACtE,CAAC,GAAG6L,QAAQ;IAC1B,IAAI,CAACwH,OAAO,CAACxH,QAAQ,EAAE,KAAK,CAAC;IAC7B,IAAI,CAACf,cAAc,CAAC,CAACxG,IAAI,CAACnF,CAAC,EAAEmF,IAAI,CAACxE,CAAC,EAAE,CAACwE,IAAI,CAACtE,CAAC,EAAE,KAAK,CAAC;IACpD,IAAI,CAACuU,MAAM,CAAC3U,OAAO,EAAEC,OAAO,EAAEE,OAAO,EAAE,KAAK,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;EACI4Z,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACnM,SAAS,CAAChQ,GAAG,CAAC6D,GAAG,CAAC,CAAC5C,QAAQ,EAAE,CAACA,QAAQ,EAAE,CAACA,QAAQ,CAAC;MACvD,IAAI,CAAC+O,SAAS,CAAC/P,GAAG,CAAC4D,GAAG,CAAC5C,QAAQ,EAAEA,QAAQ,EAAEA,QAAQ,CAAC;MACpD,IAAI,CAACwJ,YAAY,GAAG,IAAI;MACxB;IACJ;IACA,IAAI,CAACuF,SAAS,CAAC1D,IAAI,CAAC6P,IAAI,CAAC;IACzB,IAAI,CAACnM,SAAS,CAACoM,UAAU,CAAC,IAAI,CAAC9M,UAAU,EAAE,IAAI,CAACA,UAAU,CAAC;IAC3D,IAAI,CAAC7E,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4R,WAAWA,CAACC,WAAW,EAAEja,CAAC,EAAE6K,KAAK,EAAEJ,MAAM,EAAE;IACvC,IAAIwP,WAAW,KAAK,IAAI,EAAE;MAAE;MACxB,IAAI,CAAClS,SAAS,GAAG,IAAI;MACrB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI5D,KAAK,CAAC+V,OAAO,CAAC,CAAC;IACtD,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MAAE;MACnC,IAAI,CAAClS,SAAS,CAACvG,GAAG,CAACyY,WAAW,EAAEja,CAAC,EAAE6K,KAAK,EAAEJ,MAAM,CAAC;IACrD,CAAC,MACI;MAAE;MACH,IAAI,CAAC1C,SAAS,CAACkC,IAAI,CAACgQ,WAAW,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,mBAAmBA,CAAClM,KAAK,EAAEJ,MAAM,EAAE0P,KAAK,EAAE/E,KAAK,GAAG,KAAK,EAAE;IACrD,IAAItT,gCAAgC,CAAC,IAAI,CAACiI,OAAO,EAAE,qBAAqB,CAAC,EACrE,OAAO,IAAI,CAACoD,UAAU,CAACrB,MAAM;IACjC,MAAMsO,kBAAkB,GAAGvP,KAAK,GAAGJ,MAAM;IACzC,MAAMJ,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,eAAe,CAAC,CAAC,GAAG9M,OAAO;IACpD,MAAM6c,MAAM,GAAG,IAAI,CAACtQ,OAAO,CAACsQ,MAAM;IAClC,MAAMC,WAAW,GAAG,CAAClF,KAAK,GAAGgF,kBAAkB,GAAGC,MAAM,GAAGD,kBAAkB,GAAGC,MAAM,IAAI5P,MAAM,GAAGI,KAAK,GAAGwP,MAAM;IACjH,OAAOC,WAAW,GAAG,GAAG,GAAGld,IAAI,CAACoN,GAAG,CAACH,GAAG,GAAG,GAAG,CAAC,GAAG8P,KAAK,GAAG,GAAG;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5C,sBAAsBA,CAACzL,MAAM,EAAE;IAC3B,IAAIhK,gCAAgC,CAAC,IAAI,CAACiI,OAAO,EAAE,wBAAwB,CAAC,EACxE,OAAO,IAAI,CAACoD,UAAU,CAACrB,MAAM;IACjC;IACA,MAAMyO,IAAI,GAAG,IAAI,CAACxQ,OAAO,CAACO,eAAe,CAAC,CAAC,GAAG9M,OAAO;IACrD,MAAMgd,IAAI,GAAGpd,IAAI,CAACqd,IAAI,CAACrd,IAAI,CAACoN,GAAG,CAAC+P,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAACxQ,OAAO,CAACsQ,MAAM,CAAC,GAAG,CAAC;IACtE,MAAMhQ,GAAG,GAAG,CAAC,GAAG,IAAI,CAACN,OAAO,CAACsQ,MAAM,GAAGE,IAAI,GAAGC,IAAI;IACjD,OAAO1O,MAAM,GAAI1O,IAAI,CAACsd,GAAG,CAACrQ,GAAG,GAAG,GAAG,CAAE;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsQ,SAASA,CAAC9a,GAAG,EAAE+a,eAAe,GAAG,IAAI,EAAE;IACnC,MAAMC,IAAI,GAAG,CAAC,CAAChb,GAAG,IAAIA,GAAG,CAACib,SAAS,GAAGjb,GAAG,GAAG,IAAIsE,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC/D,OAAOgV,IAAI,CAAC5Q,IAAI,CAAC2Q,eAAe,GAAG,IAAI,CAAC3N,UAAU,GAAG,IAAI,CAAC7C,OAAO,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkP,WAAWA,CAACzZ,GAAG,EAAE+a,eAAe,GAAG,IAAI,EAAE;IACrC,MAAMC,IAAI,GAAG,CAAC,CAAChb,GAAG,IAAIA,GAAG,CAACib,SAAS,GAAGjb,GAAG,GAAG,IAAIsE,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC/D,OAAOgV,IAAI,CAAC9E,gBAAgB,CAAC6E,eAAe,GAAG,IAAI,CAAC/O,aAAa,GAAG,IAAI,CAACsB,UAAU,CAAC,CAACE,eAAe,CAAC,IAAI,CAACP,oBAAoB,CAAC,CAACkH,GAAG,CAAC4G,eAAe,GAAG,IAAI,CAAC3N,UAAU,GAAG,IAAI,CAAC7C,OAAO,CAAC;EACzL;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2Q,YAAYA,CAAClb,GAAG,EAAE+a,eAAe,GAAG,IAAI,EAAE;IACtC,MAAMC,IAAI,GAAGhb,GAAG,IAAI,IAAIsE,KAAK,CAAC4B,SAAS,CAAC,CAAC;IACzC,OAAO8U,IAAI,CAAC5Q,IAAI,CAAC2Q,eAAe,GAAG,IAAI,CAAC/O,aAAa,GAAG,IAAI,CAACsB,UAAU,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6N,cAAcA,CAACnb,GAAG,EAAE+a,eAAe,GAAG,IAAI,EAAE;IACxC,MAAMC,IAAI,GAAG,CAAC,CAAChb,GAAG,IAAIA,GAAG,CAACib,SAAS,GAAGjb,GAAG,GAAG,IAAIsE,KAAK,CAAC0B,OAAO,CAAC,CAAC;IAC/D,OAAOgV,IAAI,CAAC5Q,IAAI,CAAC2Q,eAAe,GAAG,IAAI,CAAC3P,eAAe,GAAG,IAAI,CAACiC,YAAY,CAAC;EAChF;EACA;AACJ;AACA;AACA;EACI2K,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAChM,aAAa,CAACR,KAAK,GAAG,IAAI,CAACQ,aAAa,CAACR,KAAK,GAAGlO,IAAI;IAC1D,IAAI,IAAI,CAAC0O,aAAa,CAACR,KAAK,GAAG,CAAC,EAC5B,IAAI,CAACQ,aAAa,CAACR,KAAK,IAAIlO,IAAI;IACpC,IAAI,CAACgQ,UAAU,CAAC9B,KAAK,IAAIlO,IAAI,GAAGC,IAAI,CAACkB,KAAK,CAAC,CAAC,IAAI,CAACuN,aAAa,CAACR,KAAK,GAAG,IAAI,CAAC8B,UAAU,CAAC9B,KAAK,IAAIlO,IAAI,CAAC;EACzG;EACA;AACJ;AACA;EACI8d,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC/N,YAAY,CAACjD,IAAI,CAAC,IAAI,CAACgB,eAAe,CAAC;IAC5C,IAAI,CAACb,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;IAClC,IAAI,CAACE,UAAU,CAAClD,IAAI,CAAC,IAAI,CAAC4B,aAAa,CAAC;IACxC,IAAI,CAACY,KAAK,GAAG,IAAI,CAACc,QAAQ;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACI2N,KAAKA,CAACnI,gBAAgB,GAAG,KAAK,EAAE;IAC5B,IAAI,CAAC9U,YAAY,CAAC,IAAI,CAAC8L,OAAO,CAAC8C,EAAE,CAACxN,CAAC,EAAE,IAAI,CAACuO,UAAU,CAACvO,CAAC,CAAC,IACnD,CAACpB,YAAY,CAAC,IAAI,CAAC8L,OAAO,CAAC8C,EAAE,CAAC7M,CAAC,EAAE,IAAI,CAAC4N,UAAU,CAAC5N,CAAC,CAAC,IACnD,CAAC/B,YAAY,CAAC,IAAI,CAAC8L,OAAO,CAAC8C,EAAE,CAAC3M,CAAC,EAAE,IAAI,CAAC0N,UAAU,CAAC1N,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC6J,OAAO,CAAC8C,EAAE,CAAC5C,IAAI,CAAC,IAAI,CAAC2D,UAAU,CAAC;MACrC,MAAM1D,QAAQ,GAAG,IAAI,CAACoP,WAAW,CAAC9U,IAAI,CAAC;MACvC,IAAI,CAAC8N,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC2C,WAAW,CAAC/K,QAAQ,CAAC7K,CAAC,EAAE6K,QAAQ,CAAClK,CAAC,EAAEkK,QAAQ,CAAChK,CAAC,CAAC;IACxD;IACA,MAAMuV,QAAQ,GAAG,CACb,IAAI,CAACgC,SAAS,CAAC,IAAI,CAAC3J,UAAU,CAACzO,CAAC,EAAE,IAAI,CAACyO,UAAU,CAAC9N,CAAC,EAAE,IAAI,CAAC8N,UAAU,CAAC5N,CAAC,EAAE,IAAI,CAAC2N,QAAQ,CAACxO,CAAC,EAAE,IAAI,CAACwO,QAAQ,CAAC7N,CAAC,EAAE,IAAI,CAAC6N,QAAQ,CAAC3N,CAAC,EAAE6S,gBAAgB,CAAC,EAC5I,IAAI,CAAC/H,cAAc,CAAC,IAAI,CAACgD,aAAa,CAAC3O,CAAC,EAAE,IAAI,CAAC2O,aAAa,CAAChO,CAAC,EAAE,IAAI,CAACgO,aAAa,CAAC9N,CAAC,EAAE6S,gBAAgB,CAAC,EACvG,IAAI,CAACrG,MAAM,CAAC,IAAI,CAACqB,MAAM,EAAEgF,gBAAgB,CAAC,CAC7C;IACD,OAAOe,OAAO,CAACkD,GAAG,CAACvB,QAAQ,CAAC;EAChC;EACA;AACJ;AACA;AACA;EACI0F,SAASA,CAAA,EAAG;IACR,IAAI,CAACvN,UAAU,CAAC3D,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC8C,EAAE,CAAC;IACrC,IAAI,CAAC8N,SAAS,CAAC,IAAI,CAAC9M,QAAQ,CAAC;IAC7B,IAAI,CAACyL,WAAW,CAAC,IAAI,CAACxL,UAAU,CAAC;IACjC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACtB,KAAK;IACxB,IAAI,CAACuB,aAAa,CAAC/D,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIoF,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC3F,aAAa,CAACC,kBAAkB,CAAC,IAAI,CAAC7C,OAAO,CAAC8C,EAAE,EAAExI,OAAO,CAAC;IAC/D,IAAI,CAACyI,oBAAoB,CAAC7C,IAAI,CAAC,IAAI,CAAC0C,aAAa,CAAC,CAACK,MAAM,CAAC,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACIoO,aAAaA,CAAA,EAAG;IACZ,MAAMC,eAAe,GAAG7W,IAAI,CAAC8W,UAAU,CAAC,IAAI,CAAClR,OAAO,EAAE,IAAI,CAACL,OAAO,CAACG,QAAQ,CAAC,CAAC8K,SAAS,CAAC,CAAC;IACxF;IACA;IACA,MAAMuG,IAAI,GAAG9W,IAAI,CAACiQ,YAAY,CAAC2G,eAAe,EAAE,IAAI,CAACtR,OAAO,CAAC8C,EAAE,CAAC;IAChE;IACA;IACA,IAAI,CAAC9C,OAAO,CAAC8C,EAAE,CAAC6H,YAAY,CAAC6G,IAAI,EAAEF,eAAe,CAAC,CAACrG,SAAS,CAAC,CAAC;IAC/D,IAAI,CAACjL,OAAO,CAAC0P,iBAAiB,CAAC,CAAC;IAChC,MAAMvP,QAAQ,GAAG,IAAI,CAACoP,WAAW,CAAC9U,IAAI,CAAC;IACvC,IAAI,CAAC8N,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC2C,WAAW,CAAC/K,QAAQ,CAAC7K,CAAC,EAAE6K,QAAQ,CAAClK,CAAC,EAAEkK,QAAQ,CAAChK,CAAC,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImS,MAAMA,CAAC5G,KAAK,EAAE;IACV,MAAMwN,UAAU,GAAG,IAAI,CAACpN,aAAa,CAACR,KAAK,GAAG,IAAI,CAAC8B,UAAU,CAAC9B,KAAK;IACnE,MAAM6N,QAAQ,GAAG,IAAI,CAACrN,aAAa,CAACP,GAAG,GAAG,IAAI,CAAC6B,UAAU,CAAC7B,GAAG;IAC7D,MAAM6N,WAAW,GAAG,IAAI,CAACtN,aAAa,CAACC,MAAM,GAAG,IAAI,CAACqB,UAAU,CAACrB,MAAM;IACtE,MAAM0P,WAAW,GAAGzW,YAAY,CAACuW,UAAU,CAAC,IAAI,CAACrO,UAAU,EAAE,IAAI,CAAC7C,OAAO,CAAC;IAC1E,MAAMqR,WAAW,GAAGzW,YAAY,CAACsW,UAAU,CAAC,IAAI,CAACrQ,eAAe,EAAE,IAAI,CAACiC,YAAY,CAAC;IACpF,MAAMwO,SAAS,GAAG,IAAI,CAACnO,QAAQ,GAAG,IAAI,CAACd,KAAK;IAC5C;IACA,IAAI5O,UAAU,CAACob,UAAU,CAAC,EAAE;MACxB,IAAI,CAAC9P,cAAc,CAACzL,KAAK,GAAG,CAAC;MAC7B,IAAI,CAACyP,UAAU,CAAC9B,KAAK,GAAG,IAAI,CAACQ,aAAa,CAACR,KAAK;IACpD,CAAC,MACI;MACD,MAAMpM,UAAU,GAAG,IAAI,CAAC4J,wBAAwB,GAAG,IAAI,CAAC5B,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC5F,IAAI,CAACkO,UAAU,CAAC9B,KAAK,GAAGxM,UAAU,CAAC,IAAI,CAACsO,UAAU,CAAC9B,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,EAAE,IAAI,CAAClC,cAAc,EAAElK,UAAU,EAAEL,QAAQ,EAAE6M,KAAK,CAAC;MACrI,IAAI,CAACrD,YAAY,GAAG,IAAI;IAC5B;IACA;IACA,IAAIvK,UAAU,CAACqb,QAAQ,CAAC,EAAE;MACtB,IAAI,CAAC9P,YAAY,CAAC1L,KAAK,GAAG,CAAC;MAC3B,IAAI,CAACyP,UAAU,CAAC7B,GAAG,GAAG,IAAI,CAACO,aAAa,CAACP,GAAG;IAChD,CAAC,MACI;MACD,MAAMrM,UAAU,GAAG,IAAI,CAAC4J,wBAAwB,GAAG,IAAI,CAAC5B,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC5F,IAAI,CAACkO,UAAU,CAAC7B,GAAG,GAAGzM,UAAU,CAAC,IAAI,CAACsO,UAAU,CAAC7B,GAAG,EAAE,IAAI,CAACO,aAAa,CAACP,GAAG,EAAE,IAAI,CAAClC,YAAY,EAAEnK,UAAU,EAAEL,QAAQ,EAAE6M,KAAK,CAAC;MAC7H,IAAI,CAACrD,YAAY,GAAG,IAAI;IAC5B;IACA;IACA,IAAIvK,UAAU,CAACsb,WAAW,CAAC,EAAE;MACzB,IAAI,CAAC9P,eAAe,CAAC3L,KAAK,GAAG,CAAC;MAC9B,IAAI,CAACyP,UAAU,CAACrB,MAAM,GAAG,IAAI,CAACD,aAAa,CAACC,MAAM;IACtD,CAAC,MACI;MACD,MAAM7M,UAAU,GAAG,IAAI,CAAC6J,uBAAuB,GAAG,IAAI,CAAC7B,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC3F,IAAI,CAACkO,UAAU,CAACrB,MAAM,GAAGjN,UAAU,CAAC,IAAI,CAACsO,UAAU,CAACrB,MAAM,EAAE,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,IAAI,CAACzC,eAAe,EAAEpK,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAEuM,KAAK,CAAC;MAC9I,IAAI,CAACrD,YAAY,GAAG,IAAI;IAC5B;IACA;IACA,IAAIvK,UAAU,CAAC2d,WAAW,CAACnc,CAAC,CAAC,IAAIxB,UAAU,CAAC2d,WAAW,CAACxb,CAAC,CAAC,IAAInC,UAAU,CAAC2d,WAAW,CAACtb,CAAC,CAAC,EAAE;MACrF,IAAI,CAACoJ,eAAe,CAAC9H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,IAAI,CAAC4I,OAAO,CAACH,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC;IACtC,CAAC,MACI;MACD,MAAMhO,UAAU,GAAG,IAAI,CAAC8J,uBAAuB,GAAG,IAAI,CAAC9B,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC3FW,cAAc,CAAC,IAAI,CAACwK,OAAO,EAAE,IAAI,CAAC6C,UAAU,EAAE,IAAI,CAAC3D,eAAe,EAAErK,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAEuM,KAAK,EAAE,IAAI,CAACrB,OAAO,CAAC;MACnH,IAAI,CAAChC,YAAY,GAAG,IAAI;IAC5B;IACA;IACA,IAAIvK,UAAU,CAAC4d,WAAW,CAACpc,CAAC,CAAC,IAAIxB,UAAU,CAAC4d,WAAW,CAACzb,CAAC,CAAC,IAAInC,UAAU,CAAC4d,WAAW,CAACvb,CAAC,CAAC,EAAE;MACrF,IAAI,CAACqJ,oBAAoB,CAAC/H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtC,IAAI,CAAC0L,YAAY,CAACjD,IAAI,CAAC,IAAI,CAACgB,eAAe,CAAC;IAChD,CAAC,MACI;MACD,MAAMhM,UAAU,GAAG,IAAI,CAAC+J,wBAAwB,GAAG,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC5FW,cAAc,CAAC,IAAI,CAACsN,YAAY,EAAE,IAAI,CAACjC,eAAe,EAAE,IAAI,CAAC1B,oBAAoB,EAAEtK,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAEuM,KAAK,EAAE,IAAI,CAACyB,YAAY,CAAC;MACvI,IAAI,CAAC9E,YAAY,GAAG,IAAI;IAC5B;IACA;IACA,IAAIvK,UAAU,CAAC6d,SAAS,CAAC,EAAE;MACvB,IAAI,CAAClS,aAAa,CAAC9L,KAAK,GAAG,CAAC;MAC5B,IAAI,CAAC+O,KAAK,GAAG,IAAI,CAACc,QAAQ;IAC9B,CAAC,MACI;MACD,MAAMtO,UAAU,GAAG,IAAI,CAACgK,sBAAsB,GAAG,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAAChI,UAAU;MAC1F,IAAI,CAACwN,KAAK,GAAG5N,UAAU,CAAC,IAAI,CAAC4N,KAAK,EAAE,IAAI,CAACc,QAAQ,EAAE,IAAI,CAAC/D,aAAa,EAAEvK,UAAU,EAAEL,QAAQ,EAAE6M,KAAK,CAAC;IACvG;IACA,IAAI,IAAI,CAAClE,aAAa,EAAE;MACpB,IAAIvK,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,IAAI,IAAI,CAAC/B,aAAa,KAAK,CAAC,EAAE;QAC/D,MAAM2T,kBAAkB,GAAG,IAAI,CAACxO,UAAU,CAACrB,MAAM,GAAG,IAAI,CAACwB,aAAa;QACtE,MAAMrQ,MAAM,GAAG,IAAI,CAAC8M,OAAO;QAC3B,MAAMsR,eAAe,GAAG,IAAI,CAACpH,mBAAmB,CAACtP,gBAAgB,CAAC;QAClE,MAAMiX,MAAM,GAAGpX,IAAI,CAACyF,IAAI,CAACoR,eAAe,CAAC,CAACQ,KAAK,CAAC5e,MAAM,CAAC4P,EAAE,CAAC,CAACmI,SAAS,CAAC,CAAC;QACtE,IAAI4G,MAAM,CAACE,QAAQ,CAAC,CAAC,KAAK,CAAC,EACvBF,MAAM,CAACvc,CAAC,GAAG,GAAG;QAClB,MAAM0c,MAAM,GAAGtX,IAAI,CAACiQ,YAAY,CAACkH,MAAM,EAAEP,eAAe,CAAC;QACzD,MAAMW,aAAa,GAAG,IAAI,CAACnQ,aAAa,CAACC,MAAM,GAAG1O,IAAI,CAACoN,GAAG,CAACvN,MAAM,CAACqN,eAAe,CAAC,CAAC,GAAG9M,OAAO,GAAG,GAAG,CAAC;QACpG,MAAMye,UAAU,GAAG,IAAI,CAACpQ,aAAa,CAACC,MAAM,GAAG6P,kBAAkB;QACjE,MAAMO,SAAS,GAAG,CAACD,UAAU,GAAG,IAAI,CAACpQ,aAAa,CAACC,MAAM,IAAI,IAAI,CAACD,aAAa,CAACC,MAAM;QACtF,MAAMqQ,MAAM,GAAGzX,IAAI,CAACuF,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC,CACpC+G,GAAG,CAAC4H,MAAM,CAAC1H,cAAc,CAAC,IAAI,CAAC9H,kBAAkB,CAAC/M,CAAC,GAAG2c,aAAa,GAAG/e,MAAM,CAACod,MAAM,CAAC,CAAC,CACrFrG,GAAG,CAAC+H,MAAM,CAAC7H,cAAc,CAAC,IAAI,CAAC9H,kBAAkB,CAACpM,CAAC,GAAGgc,aAAa,CAAC,CAAC;QAC1E,MAAMI,YAAY,GAAG5X,IAAI,CAACyF,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC+L,IAAI,CAACmD,MAAM,EAAED,SAAS,CAAC;QACvE,MAAMG,KAAK,GAAG,IAAI,CAACnT,mBAAmB,KAAKrM,eAAe,CAACC,EAAE,IAAI,IAAI,CAACqQ,UAAU,CAACrB,MAAM,IAAI,IAAI,CAAClF,WAAW;QAC3G,MAAM0V,KAAK,GAAG,IAAI,CAACpT,mBAAmB,KAAKrM,eAAe,CAACE,GAAG,IAAI,IAAI,CAAC8J,WAAW,IAAI,IAAI,CAACsG,UAAU,CAACrB,MAAM;QAC5G,IAAI,IAAI,CAAChF,aAAa,KAAKuV,KAAK,IAAIC,KAAK,CAAC,EAAE;UACxC,IAAI,CAACzQ,aAAa,CAACC,MAAM,IAAI6P,kBAAkB;UAC/C,IAAI,CAACxO,UAAU,CAACrB,MAAM,IAAI6P,kBAAkB;UAC5C,MAAMY,WAAW,GAAG9X,IAAI,CAACwF,IAAI,CAACoR,eAAe,CAAC,CAACnH,cAAc,CAAC,CAACyH,kBAAkB,CAAC;UAClFS,YAAY,CAACpI,GAAG,CAACuI,WAAW,CAAC;QACjC;QACA;QACA,IAAI,CAAC5O,SAAS,CAACoM,UAAU,CAACqC,YAAY,EAAEA,YAAY,CAAC;QACrD,MAAMI,aAAa,GAAG/X,IAAI,CAAC6W,UAAU,CAACc,YAAY,EAAE,IAAI,CAACnP,UAAU,CAAC;QACpE,IAAI,CAACA,UAAU,CAAChD,IAAI,CAACmS,YAAY,CAAC;QAClC,IAAI,CAAChS,OAAO,CAAC4J,GAAG,CAACwI,aAAa,CAAC;QAC/B,IAAI,CAACxU,aAAa,IAAI2T,kBAAkB;QACxC,IAAI9d,UAAU,CAAC,IAAI,CAACmK,aAAa,CAAC,EAC9B,IAAI,CAACA,aAAa,GAAG,CAAC;MAC9B,CAAC,MACI,IAAI9K,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,IAAI,IAAI,CAAC9B,YAAY,KAAK,CAAC,EAAE;QACpE,MAAM0T,kBAAkB,GAAG,IAAI,CAAClP,KAAK,GAAG,IAAI,CAACe,SAAS;QACtD,MAAMvQ,MAAM,GAAG,IAAI,CAAC8M,OAAO;QAC3B,MAAM0S,mBAAmB,GAAGjY,IAAI,CAAChD,GAAG,CAAC,IAAI,CAAC4K,kBAAkB,CAAC/M,CAAC,EAAE,IAAI,CAAC+M,kBAAkB,CAACpM,CAAC,EAAE,CAAC/C,MAAM,CAACyf,IAAI,GAAGzf,MAAM,CAAC0f,GAAG,KAAK1f,MAAM,CAACyf,IAAI,GAAGzf,MAAM,CAAC0f,GAAG,CAAC,CAAC,CAACC,SAAS,CAAC3f,MAAM,CAAC;QACrK,MAAM4f,UAAU,GAAGpY,IAAI,CAACjD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC6L,eAAe,CAACpQ,MAAM,CAAC4f,UAAU,CAAC;QACxE,MAAMV,MAAM,GAAGzX,IAAI,CAACuF,IAAI,CAACwS,mBAAmB,CAAC,CAACzI,GAAG,CAAC6I,UAAU,CAAC3I,cAAc,CAAC,CAACuI,mBAAmB,CAACK,GAAG,CAAC7f,MAAM,CAAC4P,EAAE,CAAC,CAAC,CAAC;QACjH,MAAMkQ,QAAQ,GAAG,IAAI,CAACtQ,KAAK,GAAGkP,kBAAkB;QAChD,MAAMO,SAAS,GAAG,EAAEa,QAAQ,GAAG,IAAI,CAACtQ,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK;QACvD;QACA;QACA;QACA,MAAM4O,eAAe,GAAG,IAAI,CAACpH,mBAAmB,CAACtP,gBAAgB,CAAC;QAClE,MAAMqY,iBAAiB,GAAG,IAAI,CAAC/P,UAAU,CAAC6P,GAAG,CAACzB,eAAe,CAAC;QAC9D,MAAMe,YAAY,GAAG5X,IAAI,CAACyF,IAAI,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC+L,IAAI,CAACmD,MAAM,EAAED,SAAS,CAAC;QACvE,MAAMe,gBAAgB,GAAGb,YAAY,CAACU,GAAG,CAACzB,eAAe,CAAC;QAC1D;QACA,MAAM6B,QAAQ,GAAG7B,eAAe,CAACnH,cAAc,CAAC+I,gBAAgB,GAAGD,iBAAiB,CAAC;QACrFZ,YAAY,CAACjS,GAAG,CAAC+S,QAAQ,CAAC;QAC1B;QACA,IAAI,CAACvP,SAAS,CAACoM,UAAU,CAACqC,YAAY,EAAEA,YAAY,CAAC;QACrD,MAAMI,aAAa,GAAG/X,IAAI,CAAC6W,UAAU,CAACc,YAAY,EAAE,IAAI,CAACnP,UAAU,CAAC;QACpE,IAAI,CAACA,UAAU,CAAChD,IAAI,CAACmS,YAAY,CAAC;QAClC,IAAI,CAAChS,OAAO,CAAC4J,GAAG,CAACwI,aAAa,CAAC;QAC/B;QACA,IAAI,CAACvU,YAAY,IAAI0T,kBAAkB;QACvC,IAAI9d,UAAU,CAAC,IAAI,CAACoK,YAAY,CAAC,EAC7B,IAAI,CAACA,YAAY,GAAG,CAAC;MAC7B;IACJ;IACA,IAAI,IAAI,CAAC8B,OAAO,CAACa,IAAI,KAAK,IAAI,CAAC6B,KAAK,EAAE;MAClC,IAAI,CAAC1C,OAAO,CAACa,IAAI,GAAG,IAAI,CAAC6B,KAAK;MAC9B,IAAI,CAAC1C,OAAO,CAACwI,sBAAsB,CAAC,CAAC;MACrC,IAAI,CAAC7E,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACtF,YAAY,GAAG,IAAI;IAC5B;IACA,IAAI,CAACK,gBAAgB,GAAG,IAAI;IAC5B;IACA,MAAM5B,WAAW,GAAG,IAAI,CAAC8M,cAAc,CAAC,CAAC;IACzC,IAAI,CAACxG,UAAU,CAACrB,MAAM,GAAG1O,IAAI,CAACO,GAAG,CAAC,IAAI,CAACwP,UAAU,CAACrB,MAAM,EAAEjF,WAAW,CAAC;IACtE;IACA,IAAI,CAACsG,UAAU,CAACgG,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACpJ,OAAO,CAACG,QAAQ,CAAC6L,gBAAgB,CAAC,IAAI,CAAC5I,UAAU,CAAC,CAACE,eAAe,CAAC,IAAI,CAACP,oBAAoB,CAAC,CAACkH,GAAG,CAAC,IAAI,CAAC5J,OAAO,CAAC;IACpH,IAAI,CAACL,OAAO,CAACoT,MAAM,CAAC,IAAI,CAAC/S,OAAO,CAAC;IACjC;IACA,MAAMgT,YAAY,GAAG,CAACvf,UAAU,CAAC,IAAI,CAACqP,YAAY,CAAC7N,CAAC,CAAC,IACjD,CAACxB,UAAU,CAAC,IAAI,CAACqP,YAAY,CAAClN,CAAC,CAAC,IAChC,CAACnC,UAAU,CAAC,IAAI,CAACqP,YAAY,CAAChN,CAAC,CAAC;IACpC,IAAIkd,YAAY,EAAE;MACdxY,QAAQ,CAAC0P,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;MACpD1P,QAAQ,CAACyP,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;MACpDzP,QAAQ,CAACwP,mBAAmB,CAAC,IAAI,CAACvK,OAAO,CAACwK,MAAM,EAAE,CAAC,CAAC;MACpD3P,QAAQ,CAACsP,cAAc,CAAC,IAAI,CAAChH,YAAY,CAAC7N,CAAC,CAAC;MAC5CwF,QAAQ,CAACqP,cAAc,CAAC,CAAC,IAAI,CAAChH,YAAY,CAAClN,CAAC,CAAC;MAC7C8E,QAAQ,CAACoP,cAAc,CAAC,IAAI,CAAChH,YAAY,CAAChN,CAAC,CAAC,CAAC,CAAC;MAC9CsE,IAAI,CAACyF,IAAI,CAACrF,QAAQ,CAAC,CAACoP,GAAG,CAACnP,QAAQ,CAAC,CAACmP,GAAG,CAAClP,QAAQ,CAAC;MAC/C,IAAI,CAACiF,OAAO,CAACG,QAAQ,CAAC8J,GAAG,CAACxP,IAAI,CAAC;MAC/B,IAAI,CAACuF,OAAO,CAAC0P,iBAAiB,CAAC,CAAC;IACpC;IACA,IAAI,IAAI,CAACtR,uBAAuB,EAAE;MAC9B,IAAI,CAACyM,kBAAkB,CAAC,IAAI,CAAC7K,OAAO,CAACG,QAAQ,CAACD,IAAI,CAAC,IAAI,CAACG,OAAO,CAAC,EAAE5F,IAAI,CAACuR,gBAAgB,CAAC,IAAI,CAAC5I,UAAU,CAAC,CAACE,eAAe,CAAC,IAAI,CAACP,oBAAoB,CAAC,EAAE,GAAG,CAAC;IAC7J;IACA,MAAMuQ,OAAO,GAAG,IAAI,CAACjV,YAAY;IACjC,IAAIiV,OAAO,IAAI,CAAC,IAAI,CAAChV,gBAAgB,EAAE;MACnC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC9E,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAO,CAAC,CAAC;MACpC,IAAI,CAACc,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAS,CAAC,CAAC;IAC1C,CAAC,MACI,IAAI+a,OAAO,EAAE;MACd,IAAI,CAACja,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAS,CAAC,CAAC;MACtC,IAAIzE,UAAU,CAACob,UAAU,EAAE,IAAI,CAACvR,aAAa,CAAC,IAC1C7J,UAAU,CAACqb,QAAQ,EAAE,IAAI,CAACxR,aAAa,CAAC,IACxC7J,UAAU,CAACsb,WAAW,EAAE,IAAI,CAACzR,aAAa,CAAC,IAC3C7J,UAAU,CAAC2d,WAAW,CAACnc,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC7C7J,UAAU,CAAC2d,WAAW,CAACxb,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IAC7C7J,UAAU,CAAC2d,WAAW,CAACtb,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC,IAC7C7J,UAAU,CAAC4d,WAAW,CAACpc,CAAC,EAAE,IAAI,CAACqI,aAAa,CAAC,IAC7C7J,UAAU,CAAC4d,WAAW,CAACzb,CAAC,EAAE,IAAI,CAAC0H,aAAa,CAAC,IAC7C7J,UAAU,CAAC4d,WAAW,CAACvb,CAAC,EAAE,IAAI,CAACwH,aAAa,CAAC,IAC7C7J,UAAU,CAAC6d,SAAS,EAAE,IAAI,CAAChU,aAAa,CAAC,IACzC,CAAC,IAAI,CAACQ,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC9E,aAAa,CAAC;UAAEd,IAAI,EAAE;QAAO,CAAC,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,CAAC+a,OAAO,IAAI,IAAI,CAAChV,gBAAgB,EAAE;MACxC,IAAI,CAACjF,aAAa,CAAC;QAAEd,IAAI,EAAE;MAAQ,CAAC,CAAC;IACzC;IACA,IAAI,CAACgL,aAAa,GAAG,IAAI,CAACH,UAAU,CAACrB,MAAM;IAC3C,IAAI,CAAC0B,SAAS,GAAG,IAAI,CAACf,KAAK;IAC3B,IAAI,CAACpE,gBAAgB,GAAGgV,OAAO;IAC/B,IAAI,CAACjV,YAAY,GAAG,KAAK;IACzB,OAAOiV,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAOC,IAAI,CAACC,SAAS,CAAC;MAClBhL,OAAO,EAAE,IAAI,CAAC3K,QAAQ;MACtBjB,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,WAAW,EAAEtI,mBAAmB,CAAC,IAAI,CAACsI,WAAW,CAAC;MAClDE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,OAAO,EAAEzI,mBAAmB,CAAC,IAAI,CAACyI,OAAO,CAAC;MAC1CR,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,aAAa,EAAElI,mBAAmB,CAAC,IAAI,CAACkI,aAAa,CAAC;MACtDC,eAAe,EAAEnI,mBAAmB,CAAC,IAAI,CAACmI,eAAe,CAAC;MAC1DC,eAAe,EAAEpI,mBAAmB,CAAC,IAAI,CAACoI,eAAe,CAAC;MAC1D1H,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BgI,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CG,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BE,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCxI,MAAM,EAAE,IAAI,CAACkO,UAAU,CAACwQ,OAAO,CAAC,CAAC;MACjCvT,QAAQ,EAAE1F,IAAI,CAACuR,gBAAgB,CAAC,IAAI,CAAClK,aAAa,CAAC,CAACmI,GAAG,CAAC,IAAI,CAAC/G,UAAU,CAAC,CAACwQ,OAAO,CAAC,CAAC;MAClF7S,IAAI,EAAE,IAAI,CAAC2C,QAAQ;MACnBmQ,WAAW,EAAE,IAAI,CAACzS,eAAe,CAACwS,OAAO,CAAC,CAAC;MAC3CE,OAAO,EAAE,IAAI,CAAC9P,QAAQ,CAAC4P,OAAO,CAAC,CAAC;MAChCG,SAAS,EAAE,IAAI,CAAC9P,UAAU,CAAC2P,OAAO,CAAC,CAAC;MACpCI,KAAK,EAAE,IAAI,CAAC9P,MAAM;MAClB+P,YAAY,EAAE,IAAI,CAAC9P,aAAa,CAACyP,OAAO,CAAC;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,QAAQA,CAACC,IAAI,EAAEjL,gBAAgB,GAAG,KAAK,EAAE;IACrC,MAAMkL,GAAG,GAAGV,IAAI,CAACW,KAAK,CAACF,IAAI,CAAC;IAC5B,IAAI,CAACxL,OAAO,GAAGyL,GAAG,CAACzL,OAAO;IAC1B,IAAI,CAAC5L,WAAW,GAAGqX,GAAG,CAACrX,WAAW;IAClC,IAAI,CAACC,WAAW,GAAGlI,mBAAmB,CAACsf,GAAG,CAACpX,WAAW,CAAC;IACvD,IAAI,CAACE,OAAO,GAAGkX,GAAG,CAAClX,OAAO;IAC1B,IAAI,CAACC,OAAO,GAAGrI,mBAAmB,CAACsf,GAAG,CAACjX,OAAO,CAAC;IAC/C,IAAI,CAACR,aAAa,GAAGyX,GAAG,CAACzX,aAAa;IACtC,IAAI,CAACC,aAAa,GAAG9H,mBAAmB,CAACsf,GAAG,CAACxX,aAAa,CAAC;IAC3D,IAAI,CAACC,eAAe,GAAG/H,mBAAmB,CAACsf,GAAG,CAACvX,eAAe,CAAC;IAC/D,IAAI,CAACC,eAAe,GAAGhI,mBAAmB,CAACsf,GAAG,CAACtX,eAAe,CAAC;IAC/D,IAAI,CAAC1H,UAAU,GAAGgf,GAAG,CAAChf,UAAU;IAChC,IAAI,CAACgI,kBAAkB,GAAGgX,GAAG,CAAChX,kBAAkB;IAChD,IAAI,CAACG,UAAU,GAAG6W,GAAG,CAAC7W,UAAU;IAChC,IAAI,CAACE,UAAU,GAAG2W,GAAG,CAAC3W,UAAU;IAChC,IAAI,CAACC,aAAa,GAAG0W,GAAG,CAAC1W,aAAa;IACtC,IAAI,CAACsG,QAAQ,CAACsQ,SAAS,CAACF,GAAG,CAACN,OAAO,CAAC;IACpC,IAAI,CAAC7P,UAAU,CAACqQ,SAAS,CAACF,GAAG,CAACL,SAAS,CAAC;IACxC,IAAI,CAAC7P,MAAM,GAAGkQ,GAAG,CAACJ,KAAK;IACvB,IAAI,CAAC7P,aAAa,CAACmQ,SAAS,CAACF,GAAG,CAACH,YAAY,CAAC;IAC9C,IAAI,CAACrJ,MAAM,CAACwJ,GAAG,CAAClf,MAAM,CAAC,CAAC,CAAC,EAAEkf,GAAG,CAAClf,MAAM,CAAC,CAAC,CAAC,EAAEkf,GAAG,CAAClf,MAAM,CAAC,CAAC,CAAC,EAAEgU,gBAAgB,CAAC;IAC1E9N,WAAW,CAACmI,cAAc,CAAC5I,IAAI,CAAC2Z,SAAS,CAACF,GAAG,CAAC/T,QAAQ,CAAC,CAACC,GAAG,CAAC,IAAI,CAAC8C,UAAU,CAAC,CAACI,eAAe,CAAC,IAAI,CAACV,aAAa,CAAC,CAAC;IACjH,IAAI,CAACqG,QAAQ,CAAC/N,WAAW,CAACoG,KAAK,EAAEpG,WAAW,CAACqG,GAAG,EAAEyH,gBAAgB,CAAC;IACnE,IAAI,CAACQ,OAAO,CAACtO,WAAW,CAAC6G,MAAM,EAAEiH,gBAAgB,CAAC;IAClD,IAAI,CAACrG,MAAM,CAACuR,GAAG,CAACrT,IAAI,EAAEmI,gBAAgB,CAAC;IACvC,IAAI,CAAC/H,cAAc,CAACiT,GAAG,CAACP,WAAW,CAAC,CAAC,CAAC,EAAEO,GAAG,CAACP,WAAW,CAAC,CAAC,CAAC,EAAEO,GAAG,CAACP,WAAW,CAAC,CAAC,CAAC,EAAE3K,gBAAgB,CAAC;IACjG,IAAI,CAAC3K,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;EACIgK,OAAOA,CAAC7L,UAAU,EAAE;IAChB,IAAI,IAAI,CAACqI,WAAW,EAAE;MAClB5M,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;MACrD;IACJ;IACAsE,UAAU,CAAC6X,YAAY,CAAC,8BAA8B,EAAExa,OAAO,CAAC;IAChE,IAAI,CAACkO,qBAAqB,CAACvL,UAAU,CAAC;IACtC,IAAI,CAAC+J,cAAc,CAAC,IAAI,CAAChI,YAAY,CAAC;EAC1C;EACA;AACJ;AACA;EACI+V,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzW,MAAM,CAAC,CAAC;IACb,IAAI,CAACuK,wBAAwB,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACvD,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC0P,eAAe,CAAC,8BAA8B,CAAC;MAChE,IAAI,CAAC1P,WAAW,GAAGnM,SAAS;IAChC;EACJ;EACA;AACJ;AACA;AACA;EACI8b,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACtb,uBAAuB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACob,UAAU,CAAC,CAAC;EACrB;EACA;EACAG,mBAAmBA,CAAC3e,GAAG,EAAE;IACrB;IACA,OAAOA,GAAG,CAACkW,gBAAgB,CAAC,IAAI,CAAC5I,UAAU,CAAC,CAACsR,YAAY,CAAC,IAAI,CAACtR,UAAU,CAACrB,MAAM,CAAC,CAACuB,eAAe,CAAC,IAAI,CAACP,oBAAoB,CAAC;EAChI;EACA;EACAmH,mBAAmBA,CAACpU,GAAG,EAAE;IACrB,OAAO,IAAI,CAAC2e,mBAAmB,CAAC3e,GAAG,CAAC,CAAC6e,MAAM,CAAC,CAAC;EACjD;EACA7O,gBAAgBA,CAACR,SAAS,EAAE;IACxB,OAAO,IAAI,CAAC3G,eAAe,CAACiW,IAAI,CAAEC,aAAa,IAAKA,aAAa,CAACvP,SAAS,KAAKA,SAAS,CAAC;EAC9F;EACAF,yBAAyBA,CAACJ,WAAW,EAAE;IACnC,OAAO,IAAI,CAACrG,eAAe,CAACiW,IAAI,CAAEC,aAAa,IAAKA,aAAa,CAAC7P,WAAW,KAAKA,WAAW,CAAC;EAClG;EACAK,eAAeA,CAAC1N,OAAO,EAAE;IACrB,IAAI,CAACgH,eAAe,CAAC1F,MAAM,CAAC,IAAI,CAAC0F,eAAe,CAAChG,OAAO,CAAChB,OAAO,CAAC,EAAE,CAAC,CAAC;EACzE;EACAkT,kBAAkBA,CAAC1K,QAAQ,EAAEF,MAAM,EAAE6U,QAAQ,EAAE;IAC3C,MAAMC,aAAa,GAAG9U,MAAM,CAAC8R,QAAQ,CAAC,CAAC;IACvC,IAAIgD,aAAa,KAAK,GAAG,EAAE;MAAE;MACzB,OAAO5U,QAAQ;IACnB;IACA;IACA,MAAM6U,SAAS,GAAGta,IAAI,CAACwF,IAAI,CAACD,MAAM,CAAC,CAACgK,GAAG,CAAC9J,QAAQ,CAAC,CAAC,CAAC;IACnD,MAAM8U,aAAa,GAAG,IAAI,CAACrR,SAAS,CAACoM,UAAU,CAACgF,SAAS,EAAEra,IAAI,CAAC,CAAC,CAAC;IAClE,MAAMua,kBAAkB,GAAGD,aAAa,CAAC7U,GAAG,CAAC4U,SAAS,CAAC,CAAC,CAAC;IACzD,MAAMG,yBAAyB,GAAGD,kBAAkB,CAACnD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjE,IAAIoD,yBAAyB,KAAK,GAAG,EAAE;MAAE;MACrC,OAAOhV,QAAQ,CAAC8J,GAAG,CAAChK,MAAM,CAAC;IAC/B,CAAC,MACI,IAAIkV,yBAAyB,KAAKJ,aAAa,EAAE;MAAE;MACpD,OAAO5U,QAAQ;IACnB,CAAC,MACI,IAAI2U,QAAQ,KAAK,GAAG,EAAE;MACvB,OAAO3U,QAAQ,CAAC8J,GAAG,CAAChK,MAAM,CAAC,CAACgK,GAAG,CAACiL,kBAAkB,CAAC;IACvD,CAAC,MACI;MACD,MAAME,YAAY,GAAG,GAAG,GAAGN,QAAQ,GAAGK,yBAAyB,GAAGlV,MAAM,CAAC8S,GAAG,CAACmC,kBAAkB,CAAC;MAChG,OAAO/U,QAAQ,CACV8J,GAAG,CAACvP,IAAI,CAACwF,IAAI,CAACD,MAAM,CAAC,CAACkK,cAAc,CAACiL,YAAY,CAAC,CAAC,CACnDnL,GAAG,CAACiL,kBAAkB,CAAC/K,cAAc,CAAC,GAAG,GAAG2K,QAAQ,CAAC,CAAC;IAC/D;EACJ;EACAnR,uBAAuBA,CAAA,EAAG;IACtB,IAAI1Q,mBAAmB,CAAC,IAAI,CAAC+M,OAAO,CAAC,EAAE;MACnC,MAAM9M,MAAM,GAAG,IAAI,CAAC8M,OAAO;MAC3B,MAAM2S,IAAI,GAAGzf,MAAM,CAACyf,IAAI;MACxB,MAAMrS,GAAG,GAAGpN,MAAM,CAACqN,eAAe,CAAC,CAAC,GAAG9M,OAAO;MAC9C,MAAM4hB,UAAU,GAAGhiB,IAAI,CAACoN,GAAG,CAACH,GAAG,GAAG,GAAG,CAAC,GAAGqS,IAAI,CAAC,CAAC;MAC/C,MAAM2C,SAAS,GAAGD,UAAU,GAAGniB,MAAM,CAACod,MAAM,CAAC,CAAC;MAC9C,IAAI,CAAC5M,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAAC,CAAC6d,SAAS,EAAE,CAACD,UAAU,EAAE,CAAC,CAAC;MACzD,IAAI,CAAC3R,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAAC6d,SAAS,EAAE,CAACD,UAAU,EAAE,CAAC,CAAC;MACxD,IAAI,CAAC3R,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAAC6d,SAAS,EAAED,UAAU,EAAE,CAAC,CAAC;MACvD,IAAI,CAAC3R,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAAC,CAAC6d,SAAS,EAAED,UAAU,EAAE,CAAC,CAAC;IAC5D,CAAC,MACI,IAAIliB,oBAAoB,CAAC,IAAI,CAAC6M,OAAO,CAAC,EAAE;MACzC,MAAM9M,MAAM,GAAG,IAAI,CAAC8M,OAAO;MAC3B,MAAMuV,OAAO,GAAG,CAAC,GAAGriB,MAAM,CAAC2N,IAAI;MAC/B,MAAMD,IAAI,GAAG1N,MAAM,CAAC0N,IAAI,GAAG2U,OAAO;MAClC,MAAM5U,KAAK,GAAGzN,MAAM,CAACyN,KAAK,GAAG4U,OAAO;MACpC,MAAMxU,GAAG,GAAG7N,MAAM,CAAC6N,GAAG,GAAGwU,OAAO;MAChC,MAAMvU,MAAM,GAAG9N,MAAM,CAAC8N,MAAM,GAAGuU,OAAO;MACtC,IAAI,CAAC7R,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAACmJ,IAAI,EAAEG,GAAG,EAAE,CAAC,CAAC;MAC3C,IAAI,CAAC2C,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAACkJ,KAAK,EAAEI,GAAG,EAAE,CAAC,CAAC;MAC5C,IAAI,CAAC2C,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAACkJ,KAAK,EAAEK,MAAM,EAAE,CAAC,CAAC;MAC/C,IAAI,CAAC0C,iBAAiB,CAAC,CAAC,CAAC,CAACjM,GAAG,CAACmJ,IAAI,EAAEI,MAAM,EAAE,CAAC,CAAC;IAClD;EACJ;EACA;EACA4I,cAAcA,CAAA,EAAG;IACb,IAAI5H,QAAQ,GAAGnN,QAAQ;IACvB,MAAM6U,WAAW,GAAG,IAAI,CAAC9L,cAAc,CAAC9F,MAAM,IAAI,CAAC;IACnD,IAAI,CAAC4R,WAAW,EACZ,OAAO1H,QAAQ;IACnB,IAAIjK,gCAAgC,CAAC,IAAI,CAACiI,OAAO,EAAE,gBAAgB,CAAC,EAChE,OAAOgC,QAAQ;IACnB,MAAMwT,YAAY,GAAG,IAAI,CAACf,mBAAmB,CAAC7Z,gBAAgB,CAAC;IAC/Da,eAAe,CAAC2X,MAAM,CAAC/Y,OAAO,EAAEmb,YAAY,EAAE,IAAI,CAACxV,OAAO,CAAC8C,EAAE,CAAC;IAC9D,KAAK,IAAIrJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,MAAMgc,eAAe,GAAG/a,IAAI,CAACwF,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAACjK,CAAC,CAAC,CAAC;MAC5Dgc,eAAe,CAACC,YAAY,CAACja,eAAe,CAAC;MAC7C,MAAMka,MAAM,GAAGhb,IAAI,CAACib,UAAU,CAAC,IAAI,CAACvV,OAAO,EAAEoV,eAAe,CAAC;MAC7D/Z,UAAU,CAACjE,GAAG,CAACke,MAAM,EAAEH,YAAY,CAAC;MACpC9Z,UAAU,CAACkX,GAAG,GAAG,IAAI,CAACxP,UAAU,CAACrB,MAAM,GAAG,CAAC;MAC3C,MAAM8T,UAAU,GAAGna,UAAU,CAACoa,gBAAgB,CAAC,IAAI,CAAClY,cAAc,CAAC;MACnE,IAAIiY,UAAU,CAAC/d,MAAM,KAAK,CAAC,IAAI+d,UAAU,CAAC,CAAC,CAAC,CAAC7T,QAAQ,GAAGA,QAAQ,EAAE;QAC9DA,QAAQ,GAAG6T,UAAU,CAAC,CAAC,CAAC,CAAC7T,QAAQ;MACrC;IACJ;IACA,OAAOA,QAAQ;EACnB;EACA;AACJ;AACA;EACIuE,cAAcA,CAACvR,MAAM,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC6P,WAAW,EACjB;IACJ,MAAMkR,IAAI,GAAG,IAAI,CAAClR,WAAW,CAACE,qBAAqB,CAAC,CAAC;IACrD/P,MAAM,CAACM,CAAC,GAAGygB,IAAI,CAACnV,IAAI;IACpB5L,MAAM,CAACiB,CAAC,GAAG8f,IAAI,CAAChV,GAAG;IACnB,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAChBhJ,MAAM,CAACM,CAAC,IAAI,IAAI,CAAC0I,SAAS,CAAC1I,CAAC;MAC5BN,MAAM,CAACiB,CAAC,IAAI8f,IAAI,CAACrV,MAAM,GAAG,IAAI,CAAC1C,SAAS,CAACgY,CAAC,GAAG,IAAI,CAAChY,SAAS,CAAC/H,CAAC;MAC7DjB,MAAM,CAAC8L,KAAK,GAAG,IAAI,CAAC9C,SAAS,CAAC7H,CAAC;MAC/BnB,MAAM,CAAC0L,MAAM,GAAG,IAAI,CAAC1C,SAAS,CAACgY,CAAC;IACpC,CAAC,MACI;MACDhhB,MAAM,CAAC8L,KAAK,GAAGiV,IAAI,CAACjV,KAAK;MACzB9L,MAAM,CAAC0L,MAAM,GAAGqV,IAAI,CAACrV,MAAM;IAC/B;IACA,OAAO1L,MAAM;EACjB;EACAsU,oBAAoBA,CAACD,kBAAkB,EAAE;IACrC,IAAIA,kBAAkB,EAClB,OAAOU,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B,IAAI,CAAC7L,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC9E,aAAa,CAAC;MAAEd,IAAI,EAAE;IAAkB,CAAC,CAAC;IAC/C,OAAO,IAAIwR,OAAO,CAAEC,OAAO,IAAK;MAC5B,MAAMiM,SAAS,GAAGA,CAAA,KAAM;QACpB,IAAI,CAACnd,mBAAmB,CAAC,MAAM,EAAEmd,SAAS,CAAC;QAC3CjM,OAAO,CAAC,CAAC;MACb,CAAC;MACD,IAAI,CAAC1R,gBAAgB,CAAC,MAAM,EAAE2d,SAAS,CAAC;IAC5C,CAAC,CAAC;EACN;EACA;EACAlO,qBAAqBA,CAAClD,WAAW,EAAE,CAAE;EACrCuD,wBAAwBA,CAAA,EAAG,CAAE;EAC7B;AACJ;AACA;AACA;AACA;EACI,IAAI8N,aAAaA,CAAA,EAAG;IAChBje,OAAO,CAACC,IAAI,CAAC,0EAA0E,CAAC;IACxF,OAAO,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIge,aAAaA,CAAC3Z,CAAC,EAAE;IACjBtE,OAAO,CAACC,IAAI,CAAC,0EAA0E,CAAC;EAC5F;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIie,qBAAqBA,CAAA,EAAG;IACxBle,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;IACxG,OAAO,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIie,qBAAqBA,CAAC5Z,CAAC,EAAE;IACzBtE,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;EAC5G;EACA,OAAOoV,oBAAoBA,CAAC8I,QAAQ,EAAEtgB,GAAG,GAAG,IAAIsE,KAAK,CAAC8B,MAAM,CAAC,CAAC,EAAE;IAC5D,MAAMmR,cAAc,GAAGvX,GAAG;IAC1B,MAAMgX,MAAM,GAAGO,cAAc,CAACP,MAAM;IACpC1R,MAAM,CAACkR,SAAS,CAAC,CAAC;IAClB;IACA8J,QAAQ,CAACC,eAAe,CAAEC,MAAM,IAAK;MACjC,IAAI,CAACA,MAAM,CAACC,MAAM,EACd;MACJnb,MAAM,CAACob,cAAc,CAACF,MAAM,CAAC;IACjC,CAAC,CAAC;IACFlb,MAAM,CAAC2R,SAAS,CAACD,MAAM,CAAC;IACxB;IACA,IAAI2J,WAAW,GAAG,CAAC;IACnBL,QAAQ,CAACC,eAAe,CAAEC,MAAM,IAAK;MACjC,IAAI,CAACA,MAAM,CAACC,MAAM,EACd;MACJ,MAAMG,IAAI,GAAGJ,MAAM;MACnB,IAAI,CAACI,IAAI,CAACC,QAAQ,EACd;MACJ,MAAMA,QAAQ,GAAGD,IAAI,CAACC,QAAQ,CAAC3T,KAAK,CAAC,CAAC;MACtC2T,QAAQ,CAACjB,YAAY,CAACgB,IAAI,CAACE,WAAW,CAAC;MACvC,MAAMC,cAAc,GAAGF,QAAQ;MAC/B,MAAMxW,QAAQ,GAAG0W,cAAc,CAACC,UAAU,CAAC3W,QAAQ;MACnD,KAAK,IAAI1G,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGyG,QAAQ,CAAC4W,KAAK,EAAEtd,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC5CgB,IAAI,CAACuc,mBAAmB,CAAC7W,QAAQ,EAAE1G,CAAC,CAAC;QACrCgd,WAAW,GAAGpjB,IAAI,CAACQ,GAAG,CAAC4iB,WAAW,EAAE3J,MAAM,CAACmK,iBAAiB,CAACxc,IAAI,CAAC,CAAC;MACvE;IACJ,CAAC,CAAC;IACF4S,cAAc,CAACtL,MAAM,GAAG1O,IAAI,CAACwD,IAAI,CAAC4f,WAAW,CAAC;IAC9C,OAAOpJ,cAAc;EACzB;AACJ;AAEA,SAASlV,eAAe,EAAEwD,cAAc,IAAIub,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}