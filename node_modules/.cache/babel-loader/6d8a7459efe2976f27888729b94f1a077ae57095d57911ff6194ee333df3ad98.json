{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { CanvasTexture } from 'three';\nimport { useGizmoContext } from './GizmoHelper.js';\nfunction Axis({\n  scale = [0.8, 0.05, 0.05],\n  color,\n  rotation\n}) {\n  return /*#__PURE__*/React.createElement(\"group\", {\n    rotation: rotation\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    position: [0.4, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: scale\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    color: color,\n    toneMapped: false\n  })));\n}\nfunction AxisHead({\n  onClick,\n  font,\n  disabled,\n  arcStyle,\n  label,\n  labelColor,\n  axisHeadScale = 1,\n  ...props\n}) {\n  const gl = useThree(state => state.gl);\n  const texture = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 64;\n    canvas.height = 64;\n    const context = canvas.getContext('2d');\n    context.beginPath();\n    context.arc(32, 32, 16, 0, 2 * Math.PI);\n    context.closePath();\n    context.fillStyle = arcStyle;\n    context.fill();\n    if (label) {\n      context.font = font;\n      context.textAlign = 'center';\n      context.fillStyle = labelColor;\n      context.fillText(label, 32, 41);\n    }\n    return new CanvasTexture(canvas);\n  }, [arcStyle, label, labelColor, font]);\n  const [active, setActive] = React.useState(false);\n  const scale = (label ? 1 : 0.75) * (active ? 1.2 : 1) * axisHeadScale;\n  const handlePointerOver = e => {\n    e.stopPropagation();\n    setActive(true);\n  };\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setActive(false);\n  };\n  return /*#__PURE__*/React.createElement(\"sprite\", _extends({\n    scale: scale,\n    onPointerOver: !disabled ? handlePointerOver : undefined,\n    onPointerOut: !disabled ? onClick || handlePointerOut : undefined\n  }, props), /*#__PURE__*/React.createElement(\"spriteMaterial\", {\n    map: texture,\n    \"map-anisotropy\": gl.capabilities.getMaxAnisotropy() || 1,\n    alphaTest: 0.3,\n    opacity: label ? 1 : 0.75,\n    toneMapped: false\n  }));\n}\nconst GizmoViewport = ({\n  hideNegativeAxes,\n  hideAxisHeads,\n  disabled,\n  font = '18px Inter var, Arial, sans-serif',\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  axisHeadScale = 1,\n  axisScale,\n  labels = ['X', 'Y', 'Z'],\n  labelColor = '#000',\n  onClick,\n  ...props\n}) => {\n  const [colorX, colorY, colorZ] = axisColors;\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const axisHeadProps = {\n    font,\n    disabled,\n    labelColor,\n    onClick,\n    axisHeadScale,\n    onPointerDown: !disabled ? e => {\n      tweenCamera(e.object.position);\n      e.stopPropagation();\n    } : undefined\n  };\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    scale: 40\n  }, props), /*#__PURE__*/React.createElement(Axis, {\n    color: colorX,\n    rotation: [0, 0, 0],\n    scale: axisScale\n  }), /*#__PURE__*/React.createElement(Axis, {\n    color: colorY,\n    rotation: [0, 0, Math.PI / 2],\n    scale: axisScale\n  }), /*#__PURE__*/React.createElement(Axis, {\n    color: colorZ,\n    rotation: [0, -Math.PI / 2, 0],\n    scale: axisScale\n  }), !hideAxisHeads && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorX,\n    position: [1, 0, 0],\n    label: labels[0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorY,\n    position: [0, 1, 0],\n    label: labels[1]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorZ,\n    position: [0, 0, 1],\n    label: labels[2]\n  }, axisHeadProps)), !hideNegativeAxes && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorX,\n    position: [-1, 0, 0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorY,\n    position: [0, -1, 0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorZ,\n    position: [0, 0, -1]\n  }, axisHeadProps)))));\n};\nexport { GizmoViewport };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "CanvasTexture", "useGizmoContext", "Axis", "scale", "color", "rotation", "createElement", "position", "args", "toneMapped", "AxisHead", "onClick", "font", "disabled", "arcStyle", "label", "labelColor", "axisHeadScale", "props", "gl", "state", "texture", "useMemo", "canvas", "document", "width", "height", "context", "getContext", "beginPath", "arc", "Math", "PI", "closePath", "fillStyle", "fill", "textAlign", "fillText", "active", "setActive", "useState", "handlePointerOver", "e", "stopPropagation", "handlePointerOut", "onPointerOver", "undefined", "onPointerOut", "map", "capabilities", "getMaxAnisotropy", "alphaTest", "opacity", "GizmoViewport", "hideNegativeAxes", "hideAxisHeads", "axisColors", "axisScale", "labels", "colorX", "colorY", "colorZ", "tweenCamera", "axisHeadProps", "onPointerDown", "object", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/GizmoViewport.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { CanvasTexture } from 'three';\nimport { useGizmoContext } from './GizmoHelper.js';\n\nfunction Axis({\n  scale = [0.8, 0.05, 0.05],\n  color,\n  rotation\n}) {\n  return /*#__PURE__*/React.createElement(\"group\", {\n    rotation: rotation\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    position: [0.4, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", {\n    args: scale\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    color: color,\n    toneMapped: false\n  })));\n}\nfunction AxisHead({\n  onClick,\n  font,\n  disabled,\n  arcStyle,\n  label,\n  labelColor,\n  axisHeadScale = 1,\n  ...props\n}) {\n  const gl = useThree(state => state.gl);\n  const texture = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 64;\n    canvas.height = 64;\n    const context = canvas.getContext('2d');\n    context.beginPath();\n    context.arc(32, 32, 16, 0, 2 * Math.PI);\n    context.closePath();\n    context.fillStyle = arcStyle;\n    context.fill();\n    if (label) {\n      context.font = font;\n      context.textAlign = 'center';\n      context.fillStyle = labelColor;\n      context.fillText(label, 32, 41);\n    }\n    return new CanvasTexture(canvas);\n  }, [arcStyle, label, labelColor, font]);\n  const [active, setActive] = React.useState(false);\n  const scale = (label ? 1 : 0.75) * (active ? 1.2 : 1) * axisHeadScale;\n  const handlePointerOver = e => {\n    e.stopPropagation();\n    setActive(true);\n  };\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setActive(false);\n  };\n  return /*#__PURE__*/React.createElement(\"sprite\", _extends({\n    scale: scale,\n    onPointerOver: !disabled ? handlePointerOver : undefined,\n    onPointerOut: !disabled ? onClick || handlePointerOut : undefined\n  }, props), /*#__PURE__*/React.createElement(\"spriteMaterial\", {\n    map: texture,\n    \"map-anisotropy\": gl.capabilities.getMaxAnisotropy() || 1,\n    alphaTest: 0.3,\n    opacity: label ? 1 : 0.75,\n    toneMapped: false\n  }));\n}\nconst GizmoViewport = ({\n  hideNegativeAxes,\n  hideAxisHeads,\n  disabled,\n  font = '18px Inter var, Arial, sans-serif',\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  axisHeadScale = 1,\n  axisScale,\n  labels = ['X', 'Y', 'Z'],\n  labelColor = '#000',\n  onClick,\n  ...props\n}) => {\n  const [colorX, colorY, colorZ] = axisColors;\n  const {\n    tweenCamera\n  } = useGizmoContext();\n  const axisHeadProps = {\n    font,\n    disabled,\n    labelColor,\n    onClick,\n    axisHeadScale,\n    onPointerDown: !disabled ? e => {\n      tweenCamera(e.object.position);\n      e.stopPropagation();\n    } : undefined\n  };\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    scale: 40\n  }, props), /*#__PURE__*/React.createElement(Axis, {\n    color: colorX,\n    rotation: [0, 0, 0],\n    scale: axisScale\n  }), /*#__PURE__*/React.createElement(Axis, {\n    color: colorY,\n    rotation: [0, 0, Math.PI / 2],\n    scale: axisScale\n  }), /*#__PURE__*/React.createElement(Axis, {\n    color: colorZ,\n    rotation: [0, -Math.PI / 2, 0],\n    scale: axisScale\n  }), !hideAxisHeads && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorX,\n    position: [1, 0, 0],\n    label: labels[0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorY,\n    position: [0, 1, 0],\n    label: labels[1]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorZ,\n    position: [0, 0, 1],\n    label: labels[2]\n  }, axisHeadProps)), !hideNegativeAxes && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorX,\n    position: [-1, 0, 0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorY,\n    position: [0, -1, 0]\n  }, axisHeadProps)), /*#__PURE__*/React.createElement(AxisHead, _extends({\n    arcStyle: colorZ,\n    position: [0, 0, -1]\n  }, axisHeadProps)))));\n};\n\nexport { GizmoViewport };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,aAAa,QAAQ,OAAO;AACrC,SAASC,eAAe,QAAQ,kBAAkB;AAElD,SAASC,IAAIA,CAAC;EACZC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;EACzBC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAAC,OAAO,EAAE;IAC/CD,QAAQ,EAAEA;EACZ,CAAC,EAAE,aAAaP,KAAK,CAACQ,aAAa,CAAC,MAAM,EAAE;IAC1CC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;EACtB,CAAC,EAAE,aAAaT,KAAK,CAACQ,aAAa,CAAC,aAAa,EAAE;IACjDE,IAAI,EAAEL;EACR,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACQ,aAAa,CAAC,mBAAmB,EAAE;IACxDF,KAAK,EAAEA,KAAK;IACZK,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASC,QAAQA,CAAC;EAChBC,OAAO;EACPC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,KAAK;EACLC,UAAU;EACVC,aAAa,GAAG,CAAC;EACjB,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGpB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,OAAO,GAAGvB,KAAK,CAACwB,OAAO,CAAC,MAAM;IAClC,MAAMC,MAAM,GAAGC,QAAQ,CAAClB,aAAa,CAAC,QAAQ,CAAC;IAC/CiB,MAAM,CAACE,KAAK,GAAG,EAAE;IACjBF,MAAM,CAACG,MAAM,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAGJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAAC;IACvCD,OAAO,CAACE,SAAS,CAAC,CAAC;IACnBF,OAAO,CAACG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,EAAE,CAAC;IACvCL,OAAO,CAACM,SAAS,CAAC,CAAC;IACnBN,OAAO,CAACO,SAAS,GAAGpB,QAAQ;IAC5Ba,OAAO,CAACQ,IAAI,CAAC,CAAC;IACd,IAAIpB,KAAK,EAAE;MACTY,OAAO,CAACf,IAAI,GAAGA,IAAI;MACnBe,OAAO,CAACS,SAAS,GAAG,QAAQ;MAC5BT,OAAO,CAACO,SAAS,GAAGlB,UAAU;MAC9BW,OAAO,CAACU,QAAQ,CAACtB,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC;IACjC;IACA,OAAO,IAAIf,aAAa,CAACuB,MAAM,CAAC;EAClC,CAAC,EAAE,CAACT,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEJ,IAAI,CAAC,CAAC;EACvC,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAGzC,KAAK,CAAC0C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMrC,KAAK,GAAG,CAACY,KAAK,GAAG,CAAC,GAAG,IAAI,KAAKuB,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGrB,aAAa;EACrE,MAAMwB,iBAAiB,GAAGC,CAAC,IAAI;IAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC;EACD,MAAMK,gBAAgB,GAAGF,CAAC,IAAI;IAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBJ,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,OAAO,aAAazC,KAAK,CAACQ,aAAa,CAAC,QAAQ,EAAET,QAAQ,CAAC;IACzDM,KAAK,EAAEA,KAAK;IACZ0C,aAAa,EAAE,CAAChC,QAAQ,GAAG4B,iBAAiB,GAAGK,SAAS;IACxDC,YAAY,EAAE,CAAClC,QAAQ,GAAGF,OAAO,IAAIiC,gBAAgB,GAAGE;EAC1D,CAAC,EAAE5B,KAAK,CAAC,EAAE,aAAapB,KAAK,CAACQ,aAAa,CAAC,gBAAgB,EAAE;IAC5D0C,GAAG,EAAE3B,OAAO;IACZ,gBAAgB,EAAEF,EAAE,CAAC8B,YAAY,CAACC,gBAAgB,CAAC,CAAC,IAAI,CAAC;IACzDC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAErC,KAAK,GAAG,CAAC,GAAG,IAAI;IACzBN,UAAU,EAAE;EACd,CAAC,CAAC,CAAC;AACL;AACA,MAAM4C,aAAa,GAAGA,CAAC;EACrBC,gBAAgB;EAChBC,aAAa;EACb1C,QAAQ;EACRD,IAAI,GAAG,mCAAmC;EAC1C4C,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC9CvC,aAAa,GAAG,CAAC;EACjBwC,SAAS;EACTC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxB1C,UAAU,GAAG,MAAM;EACnBL,OAAO;EACP,GAAGO;AACL,CAAC,KAAK;EACJ,MAAM,CAACyC,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,GAAGL,UAAU;EAC3C,MAAM;IACJM;EACF,CAAC,GAAG7D,eAAe,CAAC,CAAC;EACrB,MAAM8D,aAAa,GAAG;IACpBnD,IAAI;IACJC,QAAQ;IACRG,UAAU;IACVL,OAAO;IACPM,aAAa;IACb+C,aAAa,EAAE,CAACnD,QAAQ,GAAG6B,CAAC,IAAI;MAC9BoB,WAAW,CAACpB,CAAC,CAACuB,MAAM,CAAC1D,QAAQ,CAAC;MAC9BmC,CAAC,CAACC,eAAe,CAAC,CAAC;IACrB,CAAC,GAAGG;EACN,CAAC;EACD,OAAO,aAAahD,KAAK,CAACQ,aAAa,CAAC,OAAO,EAAET,QAAQ,CAAC;IACxDM,KAAK,EAAE;EACT,CAAC,EAAEe,KAAK,CAAC,EAAE,aAAapB,KAAK,CAACQ,aAAa,CAACJ,IAAI,EAAE;IAChDE,KAAK,EAAEuD,MAAM;IACbtD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBF,KAAK,EAAEsD;EACT,CAAC,CAAC,EAAE,aAAa3D,KAAK,CAACQ,aAAa,CAACJ,IAAI,EAAE;IACzCE,KAAK,EAAEwD,MAAM;IACbvD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE0B,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;IAC7B7B,KAAK,EAAEsD;EACT,CAAC,CAAC,EAAE,aAAa3D,KAAK,CAACQ,aAAa,CAACJ,IAAI,EAAE;IACzCE,KAAK,EAAEyD,MAAM;IACbxD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC0B,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B7B,KAAK,EAAEsD;EACT,CAAC,CAAC,EAAE,CAACF,aAAa,IAAI,aAAazD,KAAK,CAACQ,aAAa,CAACR,KAAK,CAACoE,QAAQ,EAAE,IAAI,EAAE,aAAapE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IAC/HiB,QAAQ,EAAE6C,MAAM;IAChBpD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBQ,KAAK,EAAE2C,MAAM,CAAC,CAAC;EACjB,CAAC,EAAEK,aAAa,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IACtEiB,QAAQ,EAAE8C,MAAM;IAChBrD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBQ,KAAK,EAAE2C,MAAM,CAAC,CAAC;EACjB,CAAC,EAAEK,aAAa,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IACtEiB,QAAQ,EAAE+C,MAAM;IAChBtD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBQ,KAAK,EAAE2C,MAAM,CAAC,CAAC;EACjB,CAAC,EAAEK,aAAa,CAAC,CAAC,EAAE,CAACT,gBAAgB,IAAI,aAAaxD,KAAK,CAACQ,aAAa,CAACR,KAAK,CAACoE,QAAQ,EAAE,IAAI,EAAE,aAAapE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IAClJiB,QAAQ,EAAE6C,MAAM;IAChBpD,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACrB,CAAC,EAAEwD,aAAa,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IACtEiB,QAAQ,EAAE8C,MAAM;IAChBrD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;EACrB,CAAC,EAAEwD,aAAa,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACQ,aAAa,CAACI,QAAQ,EAAEb,QAAQ,CAAC;IACtEiB,QAAQ,EAAE+C,MAAM;IAChBtD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrB,CAAC,EAAEwD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAED,SAASV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}