{"ast": null, "code": "import * as React from 'react';\nimport { useMemo, useCallback } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Texture } from 'three';\n\n// smooth new sample (measurement) based on previous sample (current)\nfunction smoothAverage(current, measurement, smoothing = 0.9) {\n  return measurement * smoothing + current * (1.0 - smoothing);\n}\n\n// default ease\nconst easeCircleOut = x => Math.sqrt(1 - Math.pow(x - 1, 2));\nclass TrailTextureImpl {\n  constructor({\n    size = 256,\n    maxAge = 750,\n    radius = 0.3,\n    intensity = 0.2,\n    interpolate = 0,\n    smoothing = 0,\n    minForce = 0.3,\n    blend = 'screen',\n    // source-over is canvas default. Others are slower\n    ease = easeCircleOut\n  } = {}) {\n    this.size = size;\n    this.maxAge = maxAge;\n    this.radius = radius;\n    this.intensity = intensity;\n    this.ease = ease;\n    this.interpolate = interpolate;\n    this.smoothing = smoothing;\n    this.minForce = minForce;\n    this.blend = blend;\n    this.trail = [];\n    this.force = 0;\n    this.initTexture();\n  }\n  initTexture() {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = this.canvas.height = this.size;\n    const ctx = this.canvas.getContext('2d');\n    if (ctx === null) {\n      throw new Error('2D not available');\n    }\n    this.ctx = ctx;\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n    this.texture = new Texture(this.canvas);\n    this.canvas.id = 'touchTexture';\n    this.canvas.style.width = this.canvas.style.height = `${this.canvas.width}px`;\n  }\n  update(delta) {\n    this.clear();\n\n    // age points\n    this.trail.forEach((point, i) => {\n      point.age += delta * 1000;\n      // remove old\n      if (point.age > this.maxAge) {\n        this.trail.splice(i, 1);\n      }\n    });\n\n    // reset force when empty (when smoothing)\n    if (!this.trail.length) this.force = 0;\n    this.trail.forEach(point => {\n      this.drawTouch(point);\n    });\n    this.texture.needsUpdate = true;\n  }\n  clear() {\n    this.ctx.globalCompositeOperation = 'source-over';\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n  }\n  addTouch(point) {\n    const last = this.trail[this.trail.length - 1];\n    if (last) {\n      const dx = last.x - point.x;\n      const dy = last.y - point.y;\n      const dd = dx * dx + dy * dy;\n      const force = Math.max(this.minForce, Math.min(dd * 10000, 1));\n      this.force = smoothAverage(force, this.force, this.smoothing);\n      if (!!this.interpolate) {\n        const lines = Math.ceil(dd / Math.pow(this.radius * 0.5 / this.interpolate, 2));\n        if (lines > 1) {\n          for (let i = 1; i < lines; i++) {\n            this.trail.push({\n              x: last.x - dx / lines * i,\n              y: last.y - dy / lines * i,\n              age: 0,\n              force\n            });\n          }\n        }\n      }\n    }\n    this.trail.push({\n      x: point.x,\n      y: point.y,\n      age: 0,\n      force: this.force\n    });\n  }\n  drawTouch(point) {\n    const pos = {\n      x: point.x * this.size,\n      y: (1 - point.y) * this.size\n    };\n    let intensity = 1;\n    if (point.age < this.maxAge * 0.3) {\n      intensity = this.ease(point.age / (this.maxAge * 0.3));\n    } else {\n      intensity = this.ease(1 - (point.age - this.maxAge * 0.3) / (this.maxAge * 0.7));\n    }\n    intensity *= point.force;\n\n    // apply blending\n    this.ctx.globalCompositeOperation = this.blend;\n    const radius = this.size * this.radius * intensity;\n    const grd = this.ctx.createRadialGradient(pos.x, pos.y, Math.max(0, radius * 0.25), pos.x, pos.y, Math.max(0, radius));\n    grd.addColorStop(0, `rgba(255, 255, 255, ${this.intensity})`);\n    grd.addColorStop(1, `rgba(0, 0, 0, 0.0)`);\n    this.ctx.beginPath();\n    this.ctx.fillStyle = grd;\n    this.ctx.arc(pos.x, pos.y, Math.max(0, radius), 0, Math.PI * 2);\n    this.ctx.fill();\n  }\n}\nfunction useTrailTexture(config = {}) {\n  const {\n    size,\n    maxAge,\n    radius,\n    intensity,\n    interpolate,\n    smoothing,\n    minForce,\n    blend,\n    ease\n  } = config;\n  const trail = useMemo(() => new TrailTextureImpl(config), [size, maxAge, radius, intensity, interpolate, smoothing, minForce, blend, ease]);\n  useFrame((_, delta) => void trail.update(delta));\n  const onMove = useCallback(e => trail.addTouch(e.uv), [trail]);\n  return [trail.texture, onMove];\n}\n\n//\n\nconst TrailTexture = ({\n  children,\n  ...config\n}) => {\n  const ret = useTrailTexture(config);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\nexport { TrailTexture, useTrailTexture };", "map": {"version": 3, "names": ["React", "useMemo", "useCallback", "useFrame", "Texture", "smoothAverage", "current", "measurement", "smoothing", "easeCircleOut", "x", "Math", "sqrt", "pow", "TrailTextureImpl", "constructor", "size", "maxAge", "radius", "intensity", "interpolate", "minForce", "blend", "ease", "trail", "force", "initTexture", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "Error", "fillStyle", "fillRect", "texture", "id", "style", "update", "delta", "clear", "for<PERSON>ach", "point", "i", "age", "splice", "length", "drawTouch", "needsUpdate", "globalCompositeOperation", "addTouch", "last", "dx", "dy", "y", "dd", "max", "min", "lines", "ceil", "push", "pos", "grd", "createRadialGradient", "addColorStop", "beginPath", "arc", "PI", "fill", "useTrailTexture", "config", "_", "onMove", "e", "uv", "TrailTexture", "children", "ret", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/TrailTexture.js"], "sourcesContent": ["import * as React from 'react';\nimport { useMemo, useCallback } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Texture } from 'three';\n\n// smooth new sample (measurement) based on previous sample (current)\nfunction smoothAverage(current, measurement, smoothing = 0.9) {\n  return measurement * smoothing + current * (1.0 - smoothing);\n}\n\n// default ease\nconst easeCircleOut = x => Math.sqrt(1 - Math.pow(x - 1, 2));\nclass TrailTextureImpl {\n  constructor({\n    size = 256,\n    maxAge = 750,\n    radius = 0.3,\n    intensity = 0.2,\n    interpolate = 0,\n    smoothing = 0,\n    minForce = 0.3,\n    blend = 'screen',\n    // source-over is canvas default. Others are slower\n    ease = easeCircleOut\n  } = {}) {\n    this.size = size;\n    this.maxAge = maxAge;\n    this.radius = radius;\n    this.intensity = intensity;\n    this.ease = ease;\n    this.interpolate = interpolate;\n    this.smoothing = smoothing;\n    this.minForce = minForce;\n    this.blend = blend;\n    this.trail = [];\n    this.force = 0;\n    this.initTexture();\n  }\n  initTexture() {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = this.canvas.height = this.size;\n    const ctx = this.canvas.getContext('2d');\n    if (ctx === null) {\n      throw new Error('2D not available');\n    }\n    this.ctx = ctx;\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n    this.texture = new Texture(this.canvas);\n    this.canvas.id = 'touchTexture';\n    this.canvas.style.width = this.canvas.style.height = `${this.canvas.width}px`;\n  }\n  update(delta) {\n    this.clear();\n\n    // age points\n    this.trail.forEach((point, i) => {\n      point.age += delta * 1000;\n      // remove old\n      if (point.age > this.maxAge) {\n        this.trail.splice(i, 1);\n      }\n    });\n\n    // reset force when empty (when smoothing)\n    if (!this.trail.length) this.force = 0;\n    this.trail.forEach(point => {\n      this.drawTouch(point);\n    });\n    this.texture.needsUpdate = true;\n  }\n  clear() {\n    this.ctx.globalCompositeOperation = 'source-over';\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n  }\n  addTouch(point) {\n    const last = this.trail[this.trail.length - 1];\n    if (last) {\n      const dx = last.x - point.x;\n      const dy = last.y - point.y;\n      const dd = dx * dx + dy * dy;\n      const force = Math.max(this.minForce, Math.min(dd * 10000, 1));\n      this.force = smoothAverage(force, this.force, this.smoothing);\n      if (!!this.interpolate) {\n        const lines = Math.ceil(dd / Math.pow(this.radius * 0.5 / this.interpolate, 2));\n        if (lines > 1) {\n          for (let i = 1; i < lines; i++) {\n            this.trail.push({\n              x: last.x - dx / lines * i,\n              y: last.y - dy / lines * i,\n              age: 0,\n              force\n            });\n          }\n        }\n      }\n    }\n    this.trail.push({\n      x: point.x,\n      y: point.y,\n      age: 0,\n      force: this.force\n    });\n  }\n  drawTouch(point) {\n    const pos = {\n      x: point.x * this.size,\n      y: (1 - point.y) * this.size\n    };\n    let intensity = 1;\n    if (point.age < this.maxAge * 0.3) {\n      intensity = this.ease(point.age / (this.maxAge * 0.3));\n    } else {\n      intensity = this.ease(1 - (point.age - this.maxAge * 0.3) / (this.maxAge * 0.7));\n    }\n    intensity *= point.force;\n\n    // apply blending\n    this.ctx.globalCompositeOperation = this.blend;\n    const radius = this.size * this.radius * intensity;\n    const grd = this.ctx.createRadialGradient(pos.x, pos.y, Math.max(0, radius * 0.25), pos.x, pos.y, Math.max(0, radius));\n    grd.addColorStop(0, `rgba(255, 255, 255, ${this.intensity})`);\n    grd.addColorStop(1, `rgba(0, 0, 0, 0.0)`);\n    this.ctx.beginPath();\n    this.ctx.fillStyle = grd;\n    this.ctx.arc(pos.x, pos.y, Math.max(0, radius), 0, Math.PI * 2);\n    this.ctx.fill();\n  }\n}\nfunction useTrailTexture(config = {}) {\n  const {\n    size,\n    maxAge,\n    radius,\n    intensity,\n    interpolate,\n    smoothing,\n    minForce,\n    blend,\n    ease\n  } = config;\n  const trail = useMemo(() => new TrailTextureImpl(config), [size, maxAge, radius, intensity, interpolate, smoothing, minForce, blend, ease]);\n  useFrame((_, delta) => void trail.update(delta));\n  const onMove = useCallback(e => trail.addTouch(e.uv), [trail]);\n  return [trail.texture, onMove];\n}\n\n//\n\nconst TrailTexture = ({\n  children,\n  ...config\n}) => {\n  const ret = useTrailTexture(config);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(ret));\n};\n\nexport { TrailTexture, useTrailTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC5C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,OAAO;;AAE/B;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEC,WAAW,EAAEC,SAAS,GAAG,GAAG,EAAE;EAC5D,OAAOD,WAAW,GAAGC,SAAS,GAAGF,OAAO,IAAI,GAAG,GAAGE,SAAS,CAAC;AAC9D;;AAEA;AACA,MAAMC,aAAa,GAAGC,CAAC,IAAIC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACH,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,MAAMI,gBAAgB,CAAC;EACrBC,WAAWA,CAAC;IACVC,IAAI,GAAG,GAAG;IACVC,MAAM,GAAG,GAAG;IACZC,MAAM,GAAG,GAAG;IACZC,SAAS,GAAG,GAAG;IACfC,WAAW,GAAG,CAAC;IACfZ,SAAS,GAAG,CAAC;IACba,QAAQ,GAAG,GAAG;IACdC,KAAK,GAAG,QAAQ;IAChB;IACAC,IAAI,GAAGd;EACT,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAACO,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACa,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EACAA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC9C,IAAI,CAACF,MAAM,CAACG,KAAK,GAAG,IAAI,CAACH,MAAM,CAACI,MAAM,GAAG,IAAI,CAACf,IAAI;IAClD,MAAMgB,GAAG,GAAG,IAAI,CAACL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;IACxC,IAAID,GAAG,KAAK,IAAI,EAAE;MAChB,MAAM,IAAIE,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACA,GAAG,CAACG,SAAS,GAAG,OAAO;IAC5B,IAAI,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACT,MAAM,CAACG,KAAK,EAAE,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC;IAC9D,IAAI,CAACM,OAAO,GAAG,IAAIjC,OAAO,CAAC,IAAI,CAACuB,MAAM,CAAC;IACvC,IAAI,CAACA,MAAM,CAACW,EAAE,GAAG,cAAc;IAC/B,IAAI,CAACX,MAAM,CAACY,KAAK,CAACT,KAAK,GAAG,IAAI,CAACH,MAAM,CAACY,KAAK,CAACR,MAAM,GAAG,GAAG,IAAI,CAACJ,MAAM,CAACG,KAAK,IAAI;EAC/E;EACAU,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,KAAK,CAAC,CAAC;;IAEZ;IACA,IAAI,CAAClB,KAAK,CAACmB,OAAO,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;MAC/BD,KAAK,CAACE,GAAG,IAAIL,KAAK,GAAG,IAAI;MACzB;MACA,IAAIG,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC7B,MAAM,EAAE;QAC3B,IAAI,CAACO,KAAK,CAACuB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC,IAAI,CAACrB,KAAK,CAACwB,MAAM,EAAE,IAAI,CAACvB,KAAK,GAAG,CAAC;IACtC,IAAI,CAACD,KAAK,CAACmB,OAAO,CAACC,KAAK,IAAI;MAC1B,IAAI,CAACK,SAAS,CAACL,KAAK,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACP,OAAO,CAACa,WAAW,GAAG,IAAI;EACjC;EACAR,KAAKA,CAAA,EAAG;IACN,IAAI,CAACV,GAAG,CAACmB,wBAAwB,GAAG,aAAa;IACjD,IAAI,CAACnB,GAAG,CAACG,SAAS,GAAG,OAAO;IAC5B,IAAI,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACT,MAAM,CAACG,KAAK,EAAE,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC;EAChE;EACAqB,QAAQA,CAACR,KAAK,EAAE;IACd,MAAMS,IAAI,GAAG,IAAI,CAAC7B,KAAK,CAAC,IAAI,CAACA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC;IAC9C,IAAIK,IAAI,EAAE;MACR,MAAMC,EAAE,GAAGD,IAAI,CAAC3C,CAAC,GAAGkC,KAAK,CAAClC,CAAC;MAC3B,MAAM6C,EAAE,GAAGF,IAAI,CAACG,CAAC,GAAGZ,KAAK,CAACY,CAAC;MAC3B,MAAMC,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;MAC5B,MAAM9B,KAAK,GAAGd,IAAI,CAAC+C,GAAG,CAAC,IAAI,CAACrC,QAAQ,EAAEV,IAAI,CAACgD,GAAG,CAACF,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;MAC9D,IAAI,CAAChC,KAAK,GAAGpB,aAAa,CAACoB,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAACjB,SAAS,CAAC;MAC7D,IAAI,CAAC,CAAC,IAAI,CAACY,WAAW,EAAE;QACtB,MAAMwC,KAAK,GAAGjD,IAAI,CAACkD,IAAI,CAACJ,EAAE,GAAG9C,IAAI,CAACE,GAAG,CAAC,IAAI,CAACK,MAAM,GAAG,GAAG,GAAG,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC,CAAC;QAC/E,IAAIwC,KAAK,GAAG,CAAC,EAAE;UACb,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,EAAEf,CAAC,EAAE,EAAE;YAC9B,IAAI,CAACrB,KAAK,CAACsC,IAAI,CAAC;cACdpD,CAAC,EAAE2C,IAAI,CAAC3C,CAAC,GAAG4C,EAAE,GAAGM,KAAK,GAAGf,CAAC;cAC1BW,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGD,EAAE,GAAGK,KAAK,GAAGf,CAAC;cAC1BC,GAAG,EAAE,CAAC;cACNrB;YACF,CAAC,CAAC;UACJ;QACF;MACF;IACF;IACA,IAAI,CAACD,KAAK,CAACsC,IAAI,CAAC;MACdpD,CAAC,EAAEkC,KAAK,CAAClC,CAAC;MACV8C,CAAC,EAAEZ,KAAK,CAACY,CAAC;MACVV,GAAG,EAAE,CAAC;MACNrB,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC;EACJ;EACAwB,SAASA,CAACL,KAAK,EAAE;IACf,MAAMmB,GAAG,GAAG;MACVrD,CAAC,EAAEkC,KAAK,CAAClC,CAAC,GAAG,IAAI,CAACM,IAAI;MACtBwC,CAAC,EAAE,CAAC,CAAC,GAAGZ,KAAK,CAACY,CAAC,IAAI,IAAI,CAACxC;IAC1B,CAAC;IACD,IAAIG,SAAS,GAAG,CAAC;IACjB,IAAIyB,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC7B,MAAM,GAAG,GAAG,EAAE;MACjCE,SAAS,GAAG,IAAI,CAACI,IAAI,CAACqB,KAAK,CAACE,GAAG,IAAI,IAAI,CAAC7B,MAAM,GAAG,GAAG,CAAC,CAAC;IACxD,CAAC,MAAM;MACLE,SAAS,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,GAAG,CAACqB,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC7B,MAAM,GAAG,GAAG,KAAK,IAAI,CAACA,MAAM,GAAG,GAAG,CAAC,CAAC;IAClF;IACAE,SAAS,IAAIyB,KAAK,CAACnB,KAAK;;IAExB;IACA,IAAI,CAACO,GAAG,CAACmB,wBAAwB,GAAG,IAAI,CAAC7B,KAAK;IAC9C,MAAMJ,MAAM,GAAG,IAAI,CAACF,IAAI,GAAG,IAAI,CAACE,MAAM,GAAGC,SAAS;IAClD,MAAM6C,GAAG,GAAG,IAAI,CAAChC,GAAG,CAACiC,oBAAoB,CAACF,GAAG,CAACrD,CAAC,EAAEqD,GAAG,CAACP,CAAC,EAAE7C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAExC,MAAM,GAAG,IAAI,CAAC,EAAE6C,GAAG,CAACrD,CAAC,EAAEqD,GAAG,CAACP,CAAC,EAAE7C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAExC,MAAM,CAAC,CAAC;IACtH8C,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,uBAAuB,IAAI,CAAC/C,SAAS,GAAG,CAAC;IAC7D6C,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,oBAAoB,CAAC;IACzC,IAAI,CAAClC,GAAG,CAACmC,SAAS,CAAC,CAAC;IACpB,IAAI,CAACnC,GAAG,CAACG,SAAS,GAAG6B,GAAG;IACxB,IAAI,CAAChC,GAAG,CAACoC,GAAG,CAACL,GAAG,CAACrD,CAAC,EAAEqD,GAAG,CAACP,CAAC,EAAE7C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAExC,MAAM,CAAC,EAAE,CAAC,EAAEP,IAAI,CAAC0D,EAAE,GAAG,CAAC,CAAC;IAC/D,IAAI,CAACrC,GAAG,CAACsC,IAAI,CAAC,CAAC;EACjB;AACF;AACA,SAASC,eAAeA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IACJxD,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,WAAW;IACXZ,SAAS;IACTa,QAAQ;IACRC,KAAK;IACLC;EACF,CAAC,GAAGiD,MAAM;EACV,MAAMhD,KAAK,GAAGvB,OAAO,CAAC,MAAM,IAAIa,gBAAgB,CAAC0D,MAAM,CAAC,EAAE,CAACxD,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEZ,SAAS,EAAEa,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAC;EAC3IpB,QAAQ,CAAC,CAACsE,CAAC,EAAEhC,KAAK,KAAK,KAAKjB,KAAK,CAACgB,MAAM,CAACC,KAAK,CAAC,CAAC;EAChD,MAAMiC,MAAM,GAAGxE,WAAW,CAACyE,CAAC,IAAInD,KAAK,CAAC4B,QAAQ,CAACuB,CAAC,CAACC,EAAE,CAAC,EAAE,CAACpD,KAAK,CAAC,CAAC;EAC9D,OAAO,CAACA,KAAK,CAACa,OAAO,EAAEqC,MAAM,CAAC;AAChC;;AAEA;;AAEA,MAAMG,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACR,GAAGN;AACL,CAAC,KAAK;EACJ,MAAMO,GAAG,GAAGR,eAAe,CAACC,MAAM,CAAC;EACnC,OAAO,aAAaxE,KAAK,CAAC6B,aAAa,CAAC7B,KAAK,CAACgF,QAAQ,EAAE,IAAI,EAAEF,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,GAAG,CAAC,CAAC;AAC1G,CAAC;AAED,SAASF,YAAY,EAAEN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}