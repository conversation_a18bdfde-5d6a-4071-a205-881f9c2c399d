{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer, SiNodedotjs, SiPython, SiExpress, SiGraphql, SiMongodb, SiPostgresql, SiRedis, SiGit, SiDocker, SiWebpack, SiAmazon, SiVercel } from 'react-icons/si';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Icon mapping\nconst iconMap = {\n  SiReact,\n  SiTypescript,\n  SiNextdotjs,\n  SiTailwindcss,\n  SiThreedotjs,\n  Si<PERSON>ramer,\n  SiNodedotjs,\n  SiPython,\n  SiExpress,\n  SiGraphql,\n  SiMongodb,\n  SiPostgresql,\n  Si<PERSON>ed<PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  SiAmazonaws: SiAmazon,\n  SiVercel\n};\nconst TechStackCard = ({\n  item,\n  index\n}) => {\n  const IconComponent = iconMap[item.icon];\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"relative group\",\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      delay: index * 0.1\n    },\n    whileHover: {\n      y: -5,\n      scale: 1.02\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center text-center\",\n        children: [IconComponent && /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.2,\n            rotate: 5\n          },\n          transition: {\n            type: \"spring\",\n            stiffness: 300\n          },\n          children: /*#__PURE__*/React.createElement(IconComponent, {\n            className: \"text-4xl mb-3 transition-all duration-300\",\n            style: {\n              color: item.color\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 dark:text-gray-300 mb-3\",\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 capitalize\",\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = TechStackCard;\nexport default TechStackCard;\nvar _c;\n$RefreshReg$(_c, \"TechStackCard\");", "map": {"version": 3, "names": ["React", "motion", "SiReact", "SiTypescript", "SiNextdotjs", "SiTailwindcss", "SiT<PERSON><PERSON>otjs", "<PERSON><PERSON><PERSON><PERSON>", "SiNodedotjs", "SiPython", "SiExpress", "SiGraphql", "SiMongodb", "SiPostgresql", "SiRedis", "SiGit", "<PERSON><PERSON><PERSON><PERSON>", "SiWebpack", "SiAmazon", "SiVercel", "jsxDEV", "_jsxDEV", "iconMap", "SiAmazonaws", "TechStackCard", "item", "index", "IconComponent", "icon", "div", "className", "initial", "opacity", "y", "animate", "transition", "delay", "whileHover", "scale", "children", "rotate", "type", "stiffness", "createElement", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "description", "category", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { TechStackItem } from '../../constants/TechStack';\nimport {\n  SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiThreedotjs, SiFramer,\n  SiNodedotjs, SiPython, SiExpress, SiGraphql,\n  SiMongodb, SiPostgresql, SiRedis,\n  SiGit, SiDocker, SiWebpack,\n  SiAmazon, SiVercel\n} from 'react-icons/si';\nimport { IconType } from 'react-icons';\n\n// Icon mapping\nconst iconMap: { [key: string]: IconType } = {\n  SiReact,\n  SiTypescript,\n  SiNextdotjs,\n  SiTailwindcss,\n  SiThreedotjs,\n  SiFramer,\n  SiNodedotjs,\n  SiPython,\n  SiExpress,\n  SiGraphql,\n  SiMongodb,\n  SiPostgresql,\n  SiRedis,\n  SiG<PERSON>,\n  <PERSON><PERSON>ock<PERSON>,\n  <PERSON>Web<PERSON>,\n  SiAmazonaws: SiAmazon,\n  SiVercel\n};\n\ninterface TechStackCardProps {\n  item: TechStackItem;\n  index: number;\n}\n\nconst TechStackCard: React.FC<TechStackCardProps> = ({ item, index }) => {\n  const IconComponent = iconMap[item.icon];\n\n  return (\n    <motion.div\n      className=\"relative group\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: index * 0.1 }}\n      whileHover={{ y: -5, scale: 1.02 }}\n    >\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\">\n        <div className=\"flex flex-col items-center text-center\">\n          {IconComponent && (\n            <motion.div\n              whileHover={{ scale: 1.2, rotate: 5 }}\n              transition={{ type: \"spring\", stiffness: 300 }}\n            >\n              {React.createElement(IconComponent, {\n                className: \"text-4xl mb-3 transition-all duration-300\",\n                style: { color: item.color }\n              })}\n            </motion.div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n            {item.name}\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-3\">\n            {item.description}\n          </p>\n          <span className=\"inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 capitalize\">\n            {item.category}\n          </span>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TechStackCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAEtC,SACEC,OAAO,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EACzEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAC3CC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAChCC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAC1BC,QAAQ,EAAEC,QAAQ,QACb,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxB;AACA,MAAMC,OAAoC,GAAG;EAC3CpB,OAAO;EACPC,YAAY;EACZC,WAAW;EACXC,aAAa;EACbC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,QAAQ;EACRC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,SAAS;EACTM,WAAW,EAAEL,QAAQ;EACrBC;AACF,CAAC;AAOD,MAAMK,aAA2C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EACvE,MAAMC,aAAa,GAAGL,OAAO,CAACG,IAAI,CAACG,IAAI,CAAC;EAExC,oBACEP,OAAA,CAACpB,MAAM,CAAC4B,GAAG;IACTC,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,KAAK,EAAEV,KAAK,GAAG;IAAI,CAAE;IACnCW,UAAU,EAAE;MAAEJ,CAAC,EAAE,CAAC,CAAC;MAAEK,KAAK,EAAE;IAAK,CAAE;IAAAC,QAAA,eAEnClB,OAAA;MAAKS,SAAS,EAAC,6LAA6L;MAAAS,QAAA,eAC1MlB,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAS,QAAA,GACpDZ,aAAa,iBACZN,OAAA,CAACpB,MAAM,CAAC4B,GAAG;UACTQ,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEE,MAAM,EAAE;UAAE,CAAE;UACtCL,UAAU,EAAE;YAAEM,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAAAH,QAAA,eAE9CvC,KAAK,CAAC2C,aAAa,CAAChB,aAAa,EAAE;YAClCG,SAAS,EAAE,2CAA2C;YACtDc,KAAK,EAAE;cAAEC,KAAK,EAAEpB,IAAI,CAACoB;YAAM;UAC7B,CAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CACb,eACD5B,OAAA;UAAIS,SAAS,EAAC,0DAA0D;UAAAS,QAAA,EACrEd,IAAI,CAACyB;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACL5B,OAAA;UAAGS,SAAS,EAAC,+CAA+C;UAAAS,QAAA,EACzDd,IAAI,CAAC0B;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACJ5B,OAAA;UAAMS,SAAS,EAAC,kIAAkI;UAAAS,QAAA,EAC/Id,IAAI,CAAC2B;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACI,EAAA,GArCI7B,aAA2C;AAuCjD,eAAeA,aAAa;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}