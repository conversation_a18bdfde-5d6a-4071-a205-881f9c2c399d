{"ast": null, "code": "const BasicShader = {\n  uniforms: {},\n  vertexShader: (/* glsl */\n  `\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    void main() {\n\n      gl_FragColor = vec4( 1.0, 0.0, 0.0, 0.5 );\n\n    }\n  `)\n};\nexport { BasicShader };", "map": {"version": 3, "names": ["BasicShader", "uniforms", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/BasicShader.ts"], "sourcesContent": ["/**\n * Simple test shader\n */\n\nimport type { IShader } from './types'\n\nexport type BasicShaderUniforms = {}\n\nexport interface IBasicShader extends IShader<BasicShaderUniforms> {}\n\nexport const BasicShader: IBasicShader = {\n  uniforms: {},\n\n  vertexShader: /* glsl */ `\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    void main() {\n\n      gl_FragColor = vec4( 1.0, 0.0, 0.0, 0.5 );\n\n    }\n  `,\n}\n"], "mappings": "AAUO,MAAMA,WAAA,GAA4B;EACvCC,QAAA,EAAU,CAAC;EAEXC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}