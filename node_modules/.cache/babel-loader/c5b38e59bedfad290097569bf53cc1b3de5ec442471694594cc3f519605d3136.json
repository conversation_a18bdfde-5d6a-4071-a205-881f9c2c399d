{"ast": null, "code": "import * as React from 'react';\nimport { getGPUTier } from 'detect-gpu';\nimport { suspend } from 'suspend-react';\nconst useDetectGPU = props => suspend(() => getGPUTier(props), ['useDetectGPU']);\nfunction DetectGPU({\n  children,\n  ...options\n}) {\n  const result = useDetectGPU(options);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(result));\n}\nexport { DetectGPU, useDetectGPU };", "map": {"version": 3, "names": ["React", "getGPUTier", "suspend", "useDetectGPU", "props", "DetectGPU", "children", "options", "result", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/DetectGPU.js"], "sourcesContent": ["import * as React from 'react';\nimport { getGPUTier } from 'detect-gpu';\nimport { suspend } from 'suspend-react';\n\nconst useDetectGPU = props => suspend(() => getGPUTier(props), ['useDetectGPU']);\nfunction DetectGPU({\n  children,\n  ...options\n}) {\n  const result = useDetectGPU(options);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(result));\n}\n\nexport { DetectGPU, useDetectGPU };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,OAAO,QAAQ,eAAe;AAEvC,MAAMC,YAAY,GAAGC,KAAK,IAAIF,OAAO,CAAC,MAAMD,UAAU,CAACG,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;AAChF,SAASC,SAASA,CAAC;EACjBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGL,YAAY,CAACI,OAAO,CAAC;EACpC,OAAO,aAAaP,KAAK,CAACS,aAAa,CAACT,KAAK,CAACU,QAAQ,EAAE,IAAI,EAAEJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,MAAM,CAAC,CAAC;AAC7G;AAEA,SAASH,SAAS,EAAEF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}