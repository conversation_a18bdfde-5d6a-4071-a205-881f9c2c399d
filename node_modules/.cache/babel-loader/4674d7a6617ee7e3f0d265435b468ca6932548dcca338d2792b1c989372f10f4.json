{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { createPortal } from '@react-three/fiber';\nimport { Flow } from 'three-stdlib';\nconst CurveModifier = /* @__PURE__ */React.forwardRef(({\n  children,\n  curve\n}, ref) => {\n  const [scene] = React.useState(() => new THREE.Scene());\n  const [obj, set] = React.useState();\n  const modifier = React.useRef(null);\n  React.useLayoutEffect(() => {\n    modifier.current = new Flow(scene.children[0]);\n    set(modifier.current.object3D);\n  }, [children]);\n  React.useEffect(() => {\n    var _modifier$current;\n    if (curve) (_modifier$current = modifier.current) == null || _modifier$current.updateCurve(0, curve);\n  }, [curve]);\n  React.useImperativeHandle(ref, () => modifier.current);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(children, scene), obj && /*#__PURE__*/React.createElement(\"primitive\", {\n    object: obj\n  }));\n});\nexport { CurveModifier };", "map": {"version": 3, "names": ["React", "THREE", "createPortal", "Flow", "CurveModifier", "forwardRef", "children", "curve", "ref", "scene", "useState", "Scene", "obj", "set", "modifier", "useRef", "useLayoutEffect", "current", "object3D", "useEffect", "_modifier$current", "updateCurve", "useImperativeHandle", "createElement", "Fragment", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/CurveModifier.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { createPortal } from '@react-three/fiber';\nimport { Flow } from 'three-stdlib';\n\nconst CurveModifier = /* @__PURE__ */React.forwardRef(({\n  children,\n  curve\n}, ref) => {\n  const [scene] = React.useState(() => new THREE.Scene());\n  const [obj, set] = React.useState();\n  const modifier = React.useRef(null);\n  React.useLayoutEffect(() => {\n    modifier.current = new Flow(scene.children[0]);\n    set(modifier.current.object3D);\n  }, [children]);\n  React.useEffect(() => {\n    var _modifier$current;\n    if (curve) (_modifier$current = modifier.current) == null || _modifier$current.updateCurve(0, curve);\n  }, [curve]);\n  React.useImperativeHandle(ref, () => modifier.current);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(children, scene), obj && /*#__PURE__*/React.createElement(\"primitive\", {\n    object: obj\n  }));\n});\n\nexport { CurveModifier };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,IAAI,QAAQ,cAAc;AAEnC,MAAMC,aAAa,GAAG,eAAeJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACrDC,QAAQ;EACRC;AACF,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAACC,KAAK,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,MAAM,IAAIT,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGb,KAAK,CAACU,QAAQ,CAAC,CAAC;EACnC,MAAMI,QAAQ,GAAGd,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EACnCf,KAAK,CAACgB,eAAe,CAAC,MAAM;IAC1BF,QAAQ,CAACG,OAAO,GAAG,IAAId,IAAI,CAACM,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC9CO,GAAG,CAACC,QAAQ,CAACG,OAAO,CAACC,QAAQ,CAAC;EAChC,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EACdN,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpB,IAAIC,iBAAiB;IACrB,IAAIb,KAAK,EAAE,CAACa,iBAAiB,GAAGN,QAAQ,CAACG,OAAO,KAAK,IAAI,IAAIG,iBAAiB,CAACC,WAAW,CAAC,CAAC,EAAEd,KAAK,CAAC;EACtG,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXP,KAAK,CAACsB,mBAAmB,CAACd,GAAG,EAAE,MAAMM,QAAQ,CAACG,OAAO,CAAC;EACtD,OAAO,aAAajB,KAAK,CAACuB,aAAa,CAACvB,KAAK,CAACwB,QAAQ,EAAE,IAAI,EAAEtB,YAAY,CAACI,QAAQ,EAAEG,KAAK,CAAC,EAAEG,GAAG,IAAI,aAAaZ,KAAK,CAACuB,aAAa,CAAC,WAAW,EAAE;IAChJE,MAAM,EAAEb;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}