{"ast": null, "code": "import * as React from 'react';\nimport { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\nimport { Events } from 'hls.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst IS_BROWSER = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && typeof ((_window$document = window.document) == null ? void 0 : _window$document.createElement) === 'function' && typeof ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.userAgent) === 'string')();\nlet _HLSModule = null;\nasync function getHls(...args) {\n  var _HLSModule2;\n  (_HLSModule2 = _HLSModule) !== null && _HLSModule2 !== void 0 ? _HLSModule2 : _HLSModule = await import('hls.js'); // singleton\n  const Ctor = _HLSModule.default;\n  if (Ctor.isSupported()) {\n    return new Ctor(...args);\n  }\n  return null;\n}\nfunction useVideoTexture(srcOrSrcObject, {\n  unsuspend = 'loadedmetadata',\n  start = true,\n  hls: hlsConfig = {},\n  crossOrigin = 'anonymous',\n  muted = true,\n  loop = true,\n  playsInline = true,\n  onVideoFrame,\n  ...videoProps\n} = {}) {\n  const gl = useThree(state => state.gl);\n  const hlsRef = useRef(null);\n  const texture = suspend(() => new Promise(async res => {\n    let src = undefined;\n    let srcObject = undefined;\n    if (typeof srcOrSrcObject === 'string') {\n      src = srcOrSrcObject;\n    } else {\n      srcObject = srcOrSrcObject;\n    }\n    const video = Object.assign(document.createElement('video'), {\n      src,\n      srcObject,\n      crossOrigin,\n      loop,\n      muted,\n      playsInline,\n      ...videoProps\n    });\n\n    // hlsjs extension\n    if (src && IS_BROWSER && src.endsWith('.m3u8')) {\n      const hls = hlsRef.current = await getHls(hlsConfig);\n      if (hls) {\n        hls.on(Events.MEDIA_ATTACHED, () => void hls.loadSource(src));\n        hls.attachMedia(video);\n      }\n    }\n    const texture = new THREE.VideoTexture(video);\n    texture.colorSpace = gl.outputColorSpace;\n    video.addEventListener(unsuspend, () => res(texture));\n  }), [srcOrSrcObject]);\n  const video = texture.source.data;\n  useVideoFrame(video, onVideoFrame);\n  useEffect(() => {\n    start && texture.image.play();\n    return () => {\n      if (hlsRef.current) {\n        hlsRef.current.destroy();\n        hlsRef.current = null;\n      }\n    };\n  }, [texture, start]);\n  return texture;\n}\n\n//\n// VideoTexture\n//\n\nconst VideoTexture = /* @__PURE__ */forwardRef(({\n  children,\n  src,\n  ...config\n}, fref) => {\n  const texture = useVideoTexture(src, config);\n  useEffect(() => {\n    return () => void texture.dispose();\n  }, [texture]);\n  useImperativeHandle(fref, () => texture, [texture]); // expose texture through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n});\n\n// rVFC hook\n\nconst useVideoFrame = (video, f) => {\n  useEffect(() => {\n    if (!f) return;\n    if (!video.requestVideoFrameCallback) return;\n    let handle;\n    const callback = (...args) => {\n      f(...args);\n      handle = video.requestVideoFrameCallback(callback);\n    };\n    video.requestVideoFrameCallback(callback);\n    return () => video.cancelVideoFrameCallback(handle);\n  }, [video, f]);\n};\nexport { VideoTexture, useVideoTexture };", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "forwardRef", "useImperativeHandle", "THREE", "useThree", "suspend", "Events", "IS_BROWSER", "_window$document", "_window$navigator", "window", "document", "createElement", "navigator", "userAgent", "_HLSModule", "getHls", "args", "_HLSModule2", "Ctor", "default", "isSupported", "useVideoTexture", "srcOrSrcObject", "unsuspend", "start", "hls", "hlsConfig", "crossOrigin", "muted", "loop", "playsInline", "onVideoFrame", "videoProps", "gl", "state", "hlsRef", "texture", "Promise", "res", "src", "undefined", "srcObject", "video", "Object", "assign", "endsWith", "current", "on", "MEDIA_ATTACHED", "loadSource", "attachMedia", "VideoTexture", "colorSpace", "outputColorSpace", "addEventListener", "source", "data", "useVideoFrame", "image", "play", "destroy", "children", "config", "fref", "dispose", "Fragment", "f", "requestVideoFrameCallback", "handle", "callback", "cancelVideoFrameCallback"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/VideoTexture.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\nimport { Events } from 'hls.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst IS_BROWSER = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && typeof ((_window$document = window.document) == null ? void 0 : _window$document.createElement) === 'function' && typeof ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.userAgent) === 'string')();\nlet _HLSModule = null;\nasync function getHls(...args) {\n  var _HLSModule2;\n  (_HLSModule2 = _HLSModule) !== null && _HLSModule2 !== void 0 ? _HLSModule2 : _HLSModule = await import('hls.js'); // singleton\n  const Ctor = _HLSModule.default;\n  if (Ctor.isSupported()) {\n    return new Ctor(...args);\n  }\n  return null;\n}\nfunction useVideoTexture(srcOrSrcObject, {\n  unsuspend = 'loadedmetadata',\n  start = true,\n  hls: hlsConfig = {},\n  crossOrigin = 'anonymous',\n  muted = true,\n  loop = true,\n  playsInline = true,\n  onVideoFrame,\n  ...videoProps\n} = {}) {\n  const gl = useThree(state => state.gl);\n  const hlsRef = useRef(null);\n  const texture = suspend(() => new Promise(async res => {\n    let src = undefined;\n    let srcObject = undefined;\n    if (typeof srcOrSrcObject === 'string') {\n      src = srcOrSrcObject;\n    } else {\n      srcObject = srcOrSrcObject;\n    }\n    const video = Object.assign(document.createElement('video'), {\n      src,\n      srcObject,\n      crossOrigin,\n      loop,\n      muted,\n      playsInline,\n      ...videoProps\n    });\n\n    // hlsjs extension\n    if (src && IS_BROWSER && src.endsWith('.m3u8')) {\n      const hls = hlsRef.current = await getHls(hlsConfig);\n      if (hls) {\n        hls.on(Events.MEDIA_ATTACHED, () => void hls.loadSource(src));\n        hls.attachMedia(video);\n      }\n    }\n    const texture = new THREE.VideoTexture(video);\n    texture.colorSpace = gl.outputColorSpace;\n    video.addEventListener(unsuspend, () => res(texture));\n  }), [srcOrSrcObject]);\n  const video = texture.source.data;\n  useVideoFrame(video, onVideoFrame);\n  useEffect(() => {\n    start && texture.image.play();\n    return () => {\n      if (hlsRef.current) {\n        hlsRef.current.destroy();\n        hlsRef.current = null;\n      }\n    };\n  }, [texture, start]);\n  return texture;\n}\n\n//\n// VideoTexture\n//\n\nconst VideoTexture = /* @__PURE__ */forwardRef(({\n  children,\n  src,\n  ...config\n}, fref) => {\n  const texture = useVideoTexture(src, config);\n  useEffect(() => {\n    return () => void texture.dispose();\n  }, [texture]);\n  useImperativeHandle(fref, () => texture, [texture]); // expose texture through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n});\n\n// rVFC hook\n\nconst useVideoFrame = (video, f) => {\n  useEffect(() => {\n    if (!f) return;\n    if (!video.requestVideoFrameCallback) return;\n    let handle;\n    const callback = (...args) => {\n      f(...args);\n      handle = video.requestVideoFrameCallback(callback);\n    };\n    video.requestVideoFrameCallback(callback);\n    return () => video.cancelVideoFrameCallback(handle);\n  }, [video, f]);\n};\n\nexport { VideoTexture, useVideoTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAC1E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,MAAM,QAAQ,QAAQ;;AAE/B;AACA,MAAMC,UAAU,GAAG,eAAe,CAAC,CAACC,gBAAgB,EAAEC,iBAAiB,KAAK,OAAOC,MAAM,KAAK,WAAW,IAAI,QAAQ,CAACF,gBAAgB,GAAGE,MAAM,CAACC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,gBAAgB,CAACI,aAAa,CAAC,KAAK,UAAU,IAAI,QAAQ,CAACH,iBAAiB,GAAGC,MAAM,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,iBAAiB,CAACK,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;AAC7U,IAAIC,UAAU,GAAG,IAAI;AACrB,eAAeC,MAAMA,CAAC,GAAGC,IAAI,EAAE;EAC7B,IAAIC,WAAW;EACf,CAACA,WAAW,GAAGH,UAAU,MAAM,IAAI,IAAIG,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGH,UAAU,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;EACnH,MAAMI,IAAI,GAAGJ,UAAU,CAACK,OAAO;EAC/B,IAAID,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;IACtB,OAAO,IAAIF,IAAI,CAAC,GAAGF,IAAI,CAAC;EAC1B;EACA,OAAO,IAAI;AACb;AACA,SAASK,eAAeA,CAACC,cAAc,EAAE;EACvCC,SAAS,GAAG,gBAAgB;EAC5BC,KAAK,GAAG,IAAI;EACZC,GAAG,EAAEC,SAAS,GAAG,CAAC,CAAC;EACnBC,WAAW,GAAG,WAAW;EACzBC,KAAK,GAAG,IAAI;EACZC,IAAI,GAAG,IAAI;EACXC,WAAW,GAAG,IAAI;EAClBC,YAAY;EACZ,GAAGC;AACL,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,EAAE,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,MAAM,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMsC,OAAO,GAAGhC,OAAO,CAAC,MAAM,IAAIiC,OAAO,CAAC,MAAMC,GAAG,IAAI;IACrD,IAAIC,GAAG,GAAGC,SAAS;IACnB,IAAIC,SAAS,GAAGD,SAAS;IACzB,IAAI,OAAOlB,cAAc,KAAK,QAAQ,EAAE;MACtCiB,GAAG,GAAGjB,cAAc;IACtB,CAAC,MAAM;MACLmB,SAAS,GAAGnB,cAAc;IAC5B;IACA,MAAMoB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAClC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,EAAE;MAC3D4B,GAAG;MACHE,SAAS;MACTd,WAAW;MACXE,IAAI;MACJD,KAAK;MACLE,WAAW;MACX,GAAGE;IACL,CAAC,CAAC;;IAEF;IACA,IAAIO,GAAG,IAAIjC,UAAU,IAAIiC,GAAG,CAACM,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9C,MAAMpB,GAAG,GAAGU,MAAM,CAACW,OAAO,GAAG,MAAM/B,MAAM,CAACW,SAAS,CAAC;MACpD,IAAID,GAAG,EAAE;QACPA,GAAG,CAACsB,EAAE,CAAC1C,MAAM,CAAC2C,cAAc,EAAE,MAAM,KAAKvB,GAAG,CAACwB,UAAU,CAACV,GAAG,CAAC,CAAC;QAC7Dd,GAAG,CAACyB,WAAW,CAACR,KAAK,CAAC;MACxB;IACF;IACA,MAAMN,OAAO,GAAG,IAAIlC,KAAK,CAACiD,YAAY,CAACT,KAAK,CAAC;IAC7CN,OAAO,CAACgB,UAAU,GAAGnB,EAAE,CAACoB,gBAAgB;IACxCX,KAAK,CAACY,gBAAgB,CAAC/B,SAAS,EAAE,MAAMe,GAAG,CAACF,OAAO,CAAC,CAAC;EACvD,CAAC,CAAC,EAAE,CAACd,cAAc,CAAC,CAAC;EACrB,MAAMoB,KAAK,GAAGN,OAAO,CAACmB,MAAM,CAACC,IAAI;EACjCC,aAAa,CAACf,KAAK,EAAEX,YAAY,CAAC;EAClChC,SAAS,CAAC,MAAM;IACdyB,KAAK,IAAIY,OAAO,CAACsB,KAAK,CAACC,IAAI,CAAC,CAAC;IAC7B,OAAO,MAAM;MACX,IAAIxB,MAAM,CAACW,OAAO,EAAE;QAClBX,MAAM,CAACW,OAAO,CAACc,OAAO,CAAC,CAAC;QACxBzB,MAAM,CAACW,OAAO,GAAG,IAAI;MACvB;IACF,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,EAAEZ,KAAK,CAAC,CAAC;EACpB,OAAOY,OAAO;AAChB;;AAEA;AACA;AACA;;AAEA,MAAMe,YAAY,GAAG,eAAenD,UAAU,CAAC,CAAC;EAC9C6D,QAAQ;EACRtB,GAAG;EACH,GAAGuB;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAM3B,OAAO,GAAGf,eAAe,CAACkB,GAAG,EAAEuB,MAAM,CAAC;EAC5C/D,SAAS,CAAC,MAAM;IACd,OAAO,MAAM,KAAKqC,OAAO,CAAC4B,OAAO,CAAC,CAAC;EACrC,CAAC,EAAE,CAAC5B,OAAO,CAAC,CAAC;EACbnC,mBAAmB,CAAC8D,IAAI,EAAE,MAAM3B,OAAO,EAAE,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC;;EAErD,OAAO,aAAavC,KAAK,CAACc,aAAa,CAACd,KAAK,CAACoE,QAAQ,EAAE,IAAI,EAAEJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACzB,OAAO,CAAC,CAAC;AAC9G,CAAC,CAAC;;AAEF;;AAEA,MAAMqB,aAAa,GAAGA,CAACf,KAAK,EAAEwB,CAAC,KAAK;EAClCnE,SAAS,CAAC,MAAM;IACd,IAAI,CAACmE,CAAC,EAAE;IACR,IAAI,CAACxB,KAAK,CAACyB,yBAAyB,EAAE;IACtC,IAAIC,MAAM;IACV,MAAMC,QAAQ,GAAGA,CAAC,GAAGrD,IAAI,KAAK;MAC5BkD,CAAC,CAAC,GAAGlD,IAAI,CAAC;MACVoD,MAAM,GAAG1B,KAAK,CAACyB,yBAAyB,CAACE,QAAQ,CAAC;IACpD,CAAC;IACD3B,KAAK,CAACyB,yBAAyB,CAACE,QAAQ,CAAC;IACzC,OAAO,MAAM3B,KAAK,CAAC4B,wBAAwB,CAACF,MAAM,CAAC;EACrD,CAAC,EAAE,CAAC1B,KAAK,EAAEwB,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,SAASf,YAAY,EAAE9B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}