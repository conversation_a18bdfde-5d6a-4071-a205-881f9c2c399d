{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Color } from \"three\";\nimport { Pass } from \"./Pass.js\";\nclass RenderPass extends Pass {\n  constructor(scene, camera, overrideMaterial, clearColor, clearAlpha = 0) {\n    super();\n    __publicField(this, \"scene\");\n    __publicField(this, \"camera\");\n    __publicField(this, \"overrideMaterial\");\n    __publicField(this, \"clearColor\");\n    __publicField(this, \"clearAlpha\");\n    __publicField(this, \"clearDepth\", false);\n    __publicField(this, \"_oldClearColor\", new Color());\n    this.scene = scene;\n    this.camera = camera;\n    this.overrideMaterial = overrideMaterial;\n    this.clearColor = clearColor;\n    this.clearAlpha = clearAlpha;\n    this.clear = true;\n    this.needsSwap = false;\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    let oldAutoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    let oldClearAlpha;\n    let oldOverrideMaterial = null;\n    if (this.overrideMaterial !== void 0) {\n      oldOverrideMaterial = this.scene.overrideMaterial;\n      this.scene.overrideMaterial = this.overrideMaterial;\n    }\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor);\n      oldClearAlpha = renderer.getClearAlpha();\n      renderer.setClearColor(this.clearColor, this.clearAlpha);\n    }\n    if (this.clearDepth) {\n      renderer.clearDepth();\n    }\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer);\n    if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil);\n    renderer.render(this.scene, this.camera);\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha);\n    }\n    if (this.overrideMaterial !== void 0) {\n      this.scene.overrideMaterial = oldOverrideMaterial;\n    }\n    renderer.autoClear = oldAutoClear;\n  }\n}\nexport { RenderPass };", "map": {"version": 3, "names": ["RenderPass", "Pass", "constructor", "scene", "camera", "overrideMaterial", "clearColor", "clearAlpha", "__publicField", "Color", "clear", "needsSwap", "render", "renderer", "writeBuffer", "readBuffer", "oldAutoClear", "autoClear", "oldClearAlpha", "oldOverrideMaterial", "getClearColor", "_oldClearColor", "getClearAlpha", "setClearColor", "clear<PERSON><PERSON>h", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToScreen", "autoClearColor", "autoClearDepth", "autoClearStencil"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/RenderPass.ts"], "sourcesContent": ["import { Camera, Color, Material, Scene, WebGLRenderTarget, WebGLRenderer } from 'three'\nimport { Pass } from './Pass'\n\nclass RenderPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public overrideMaterial: Material | undefined\n  public clearColor: Color | undefined\n  public clearAlpha: number\n  public clearDepth = false\n  private _oldClearColor = new Color()\n\n  constructor(scene: Scene, camera: Camera, overrideMaterial?: Material, clearColor?: Color, clearAlpha = 0) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.overrideMaterial = overrideMaterial\n\n    this.clearColor = clearColor\n    this.clearAlpha = clearAlpha\n\n    this.clear = true\n    this.needsSwap = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    let oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    let oldClearAlpha\n    let oldOverrideMaterial: Material | null = null\n\n    if (this.overrideMaterial !== undefined) {\n      oldOverrideMaterial = this.scene.overrideMaterial\n\n      this.scene.overrideMaterial = this.overrideMaterial\n    }\n\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor)\n      oldClearAlpha = renderer.getClearAlpha()\n\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n    }\n\n    if (this.clearDepth) {\n      renderer.clearDepth()\n    }\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n\n    // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n    if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n    renderer.render(this.scene, this.camera)\n\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n    }\n\n    if (this.overrideMaterial !== undefined) {\n      this.scene.overrideMaterial = oldOverrideMaterial\n    }\n\n    renderer.autoClear = oldAutoClear\n  }\n}\n\nexport { RenderPass }\n"], "mappings": ";;;;;;;;;;;;;AAGA,MAAMA,UAAA,SAAmBC,IAAA,CAAK;EAS5BC,YAAYC,KAAA,EAAcC,MAAA,EAAgBC,gBAAA,EAA6BC,UAAA,EAAoBC,UAAA,GAAa,GAAG;IACnG;IATDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA,qBAAa;IACZA,aAAA,yBAAiB,IAAIC,KAAA;IAK3B,KAAKN,KAAA,GAAQA,KAAA;IACb,KAAKC,MAAA,GAASA,MAAA;IAEd,KAAKC,gBAAA,GAAmBA,gBAAA;IAExB,KAAKC,UAAA,GAAaA,UAAA;IAClB,KAAKC,UAAA,GAAaA,UAAA;IAElB,KAAKG,KAAA,GAAQ;IACb,KAAKC,SAAA,GAAY;EACnB;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACM;IACN,IAAIC,YAAA,GAAeH,QAAA,CAASI,SAAA;IAC5BJ,QAAA,CAASI,SAAA,GAAY;IAEjB,IAAAC,aAAA;IACJ,IAAIC,mBAAA,GAAuC;IAEvC,SAAKd,gBAAA,KAAqB,QAAW;MACvCc,mBAAA,GAAsB,KAAKhB,KAAA,CAAME,gBAAA;MAE5B,KAAAF,KAAA,CAAME,gBAAA,GAAmB,KAAKA,gBAAA;IACrC;IAEA,IAAI,KAAKC,UAAA,EAAY;MACVO,QAAA,CAAAO,aAAA,CAAc,KAAKC,cAAc;MAC1CH,aAAA,GAAgBL,QAAA,CAASS,aAAA;MAEzBT,QAAA,CAASU,aAAA,CAAc,KAAKjB,UAAA,EAAY,KAAKC,UAAU;IACzD;IAEA,IAAI,KAAKiB,UAAA,EAAY;MACnBX,QAAA,CAASW,UAAA,CAAW;IACtB;IAEAX,QAAA,CAASY,eAAA,CAAgB,KAAKC,cAAA,GAAiB,OAAOX,UAAU;IAGhE,IAAI,KAAKL,KAAA,EAAOG,QAAA,CAASH,KAAA,CAAMG,QAAA,CAASc,cAAA,EAAgBd,QAAA,CAASe,cAAA,EAAgBf,QAAA,CAASgB,gBAAgB;IAC1GhB,QAAA,CAASD,MAAA,CAAO,KAAKT,KAAA,EAAO,KAAKC,MAAM;IAEvC,IAAI,KAAKE,UAAA,EAAY;MACVO,QAAA,CAAAU,aAAA,CAAc,KAAKF,cAAA,EAAgBH,aAAa;IAC3D;IAEI,SAAKb,gBAAA,KAAqB,QAAW;MACvC,KAAKF,KAAA,CAAME,gBAAA,GAAmBc,mBAAA;IAChC;IAEAN,QAAA,CAASI,SAAA,GAAYD,YAAA;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}