{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { QuadraticBezierCurve3, Vector3 } from 'three';\nimport { Line } from './Line.js';\nconst v = /* @__PURE__ */new Vector3();\nconst QuadraticBezierLine = /* @__PURE__ */React.forwardRef(function QuadraticBezierLine({\n  start = [0, 0, 0],\n  end = [0, 0, 0],\n  mid,\n  segments = 20,\n  ...rest\n}, forwardref) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardref, () => ref.current);\n  const [curve] = React.useState(() => new QuadraticBezierCurve3(undefined, undefined, undefined));\n  const getPoints = React.useCallback((start, end, mid, segments = 20) => {\n    if (start instanceof Vector3) curve.v0.copy(start);else curve.v0.set(...start);\n    if (end instanceof Vector3) curve.v2.copy(end);else curve.v2.set(...end);\n    if (mid instanceof Vector3) {\n      curve.v1.copy(mid);\n    } else if (Array.isArray(mid)) {\n      curve.v1.set(...mid);\n    } else {\n      curve.v1.copy(curve.v0.clone().add(curve.v2.clone().sub(curve.v0)).add(v.set(0, curve.v0.y - curve.v2.y, 0)));\n    }\n    return curve.getPoints(segments);\n  }, []);\n  React.useLayoutEffect(() => {\n    ref.current.setPoints = (start, end, mid) => {\n      const points = getPoints(start, end, mid);\n      if (ref.current.geometry) ref.current.geometry.setPositions(points.map(p => p.toArray()).flat());\n    };\n  }, []);\n  const points = React.useMemo(() => getPoints(start, end, mid, segments), [start, end, mid, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: points\n  }, rest));\n});\nexport { QuadraticBezierLine };", "map": {"version": 3, "names": ["_extends", "React", "QuadraticBezierCurve3", "Vector3", "Line", "v", "QuadraticBezierLine", "forwardRef", "start", "end", "mid", "segments", "rest", "forwardref", "ref", "useRef", "useImperativeHandle", "current", "curve", "useState", "undefined", "getPoints", "useCallback", "v0", "copy", "set", "v2", "v1", "Array", "isArray", "clone", "add", "sub", "y", "useLayoutEffect", "setPoints", "points", "geometry", "setPositions", "map", "p", "toArray", "flat", "useMemo", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/QuadraticBezierLine.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { QuadraticBezierCurve3, Vector3 } from 'three';\nimport { Line } from './Line.js';\n\nconst v = /* @__PURE__ */new Vector3();\nconst QuadraticBezierLine = /* @__PURE__ */React.forwardRef(function QuadraticBezierLine({\n  start = [0, 0, 0],\n  end = [0, 0, 0],\n  mid,\n  segments = 20,\n  ...rest\n}, forwardref) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardref, () => ref.current);\n  const [curve] = React.useState(() => new QuadraticBezierCurve3(undefined, undefined, undefined));\n  const getPoints = React.useCallback((start, end, mid, segments = 20) => {\n    if (start instanceof Vector3) curve.v0.copy(start);else curve.v0.set(...start);\n    if (end instanceof Vector3) curve.v2.copy(end);else curve.v2.set(...end);\n    if (mid instanceof Vector3) {\n      curve.v1.copy(mid);\n    } else if (Array.isArray(mid)) {\n      curve.v1.set(...mid);\n    } else {\n      curve.v1.copy(curve.v0.clone().add(curve.v2.clone().sub(curve.v0)).add(v.set(0, curve.v0.y - curve.v2.y, 0)));\n    }\n    return curve.getPoints(segments);\n  }, []);\n  React.useLayoutEffect(() => {\n    ref.current.setPoints = (start, end, mid) => {\n      const points = getPoints(start, end, mid);\n      if (ref.current.geometry) ref.current.geometry.setPositions(points.map(p => p.toArray()).flat());\n    };\n  }, []);\n  const points = React.useMemo(() => getPoints(start, end, mid, segments), [start, end, mid, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: points\n  }, rest));\n});\n\nexport { QuadraticBezierLine };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,EAAEC,OAAO,QAAQ,OAAO;AACtD,SAASC,IAAI,QAAQ,WAAW;AAEhC,MAAMC,CAAC,GAAG,eAAe,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,mBAAmB,GAAG,eAAeL,KAAK,CAACM,UAAU,CAAC,SAASD,mBAAmBA,CAAC;EACvFE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjBC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACfC,GAAG;EACHC,QAAQ,GAAG,EAAE;EACb,GAAGC;AACL,CAAC,EAAEC,UAAU,EAAE;EACb,MAAMC,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC,IAAI,CAAC;EAC9Bd,KAAK,CAACe,mBAAmB,CAACH,UAAU,EAAE,MAAMC,GAAG,CAACG,OAAO,CAAC;EACxD,MAAM,CAACC,KAAK,CAAC,GAAGjB,KAAK,CAACkB,QAAQ,CAAC,MAAM,IAAIjB,qBAAqB,CAACkB,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,CAAC;EAChG,MAAMC,SAAS,GAAGpB,KAAK,CAACqB,WAAW,CAAC,CAACd,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,GAAG,EAAE,KAAK;IACtE,IAAIH,KAAK,YAAYL,OAAO,EAAEe,KAAK,CAACK,EAAE,CAACC,IAAI,CAAChB,KAAK,CAAC,CAAC,KAAKU,KAAK,CAACK,EAAE,CAACE,GAAG,CAAC,GAAGjB,KAAK,CAAC;IAC9E,IAAIC,GAAG,YAAYN,OAAO,EAAEe,KAAK,CAACQ,EAAE,CAACF,IAAI,CAACf,GAAG,CAAC,CAAC,KAAKS,KAAK,CAACQ,EAAE,CAACD,GAAG,CAAC,GAAGhB,GAAG,CAAC;IACxE,IAAIC,GAAG,YAAYP,OAAO,EAAE;MAC1Be,KAAK,CAACS,EAAE,CAACH,IAAI,CAACd,GAAG,CAAC;IACpB,CAAC,MAAM,IAAIkB,KAAK,CAACC,OAAO,CAACnB,GAAG,CAAC,EAAE;MAC7BQ,KAAK,CAACS,EAAE,CAACF,GAAG,CAAC,GAAGf,GAAG,CAAC;IACtB,CAAC,MAAM;MACLQ,KAAK,CAACS,EAAE,CAACH,IAAI,CAACN,KAAK,CAACK,EAAE,CAACO,KAAK,CAAC,CAAC,CAACC,GAAG,CAACb,KAAK,CAACQ,EAAE,CAACI,KAAK,CAAC,CAAC,CAACE,GAAG,CAACd,KAAK,CAACK,EAAE,CAAC,CAAC,CAACQ,GAAG,CAAC1B,CAAC,CAACoB,GAAG,CAAC,CAAC,EAAEP,KAAK,CAACK,EAAE,CAACU,CAAC,GAAGf,KAAK,CAACQ,EAAE,CAACO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/G;IACA,OAAOf,KAAK,CAACG,SAAS,CAACV,QAAQ,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EACNV,KAAK,CAACiC,eAAe,CAAC,MAAM;IAC1BpB,GAAG,CAACG,OAAO,CAACkB,SAAS,GAAG,CAAC3B,KAAK,EAAEC,GAAG,EAAEC,GAAG,KAAK;MAC3C,MAAM0B,MAAM,GAAGf,SAAS,CAACb,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACzC,IAAII,GAAG,CAACG,OAAO,CAACoB,QAAQ,EAAEvB,GAAG,CAACG,OAAO,CAACoB,QAAQ,CAACC,YAAY,CAACF,MAAM,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAClG,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMN,MAAM,GAAGnC,KAAK,CAAC0C,OAAO,CAAC,MAAMtB,SAAS,CAACb,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,EAAE,CAACH,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,CAAC;EACrG,OAAO,aAAaV,KAAK,CAAC2C,aAAa,CAACxC,IAAI,EAAEJ,QAAQ,CAAC;IACrDc,GAAG,EAAEA,GAAG;IACRsB,MAAM,EAAEA;EACV,CAAC,EAAExB,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}