{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Shape } from 'three';\nimport { toCreasedNormals } from 'three-stdlib';\nconst eps = 0.00001;\nfunction createShape(width, height, radius0) {\n  const shape = new Shape();\n  const radius = radius0 - eps;\n  shape.absarc(eps, eps, eps, -Math.PI / 2, -Math.PI, true);\n  shape.absarc(eps, height - radius * 2, eps, Math.PI, Math.PI / 2, true);\n  shape.absarc(width - radius * 2, height - radius * 2, eps, Math.PI / 2, 0, true);\n  shape.absarc(width - radius * 2, eps, eps, 0, -Math.PI / 2, true);\n  return shape;\n}\nconst RoundedBox = /* @__PURE__ */React.forwardRef(function RoundedBox({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  children,\n  ...rest\n}, ref) {\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(RoundedBoxGeometry, {\n    args: [width, height, depth],\n    radius: radius,\n    steps: steps,\n    smoothness: smoothness,\n    bevelSegments: bevelSegments,\n    creaseAngle: creaseAngle\n  }), children);\n});\nconst RoundedBoxGeometry = /* @__PURE__ */React.forwardRef(function RoundedBoxGeometry({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  ...rest\n}, ref) {\n  const shape = React.useMemo(() => createShape(width, height, radius), [width, height, radius]);\n  const params = React.useMemo(() => ({\n    depth: depth - radius * 2,\n    bevelEnabled: true,\n    bevelSegments: bevelSegments * 2,\n    steps,\n    bevelSize: radius - eps,\n    bevelThickness: radius,\n    curveSegments: smoothness\n  }), [depth, radius, smoothness, bevelSegments, steps]);\n  const geomRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (geomRef.current) {\n      geomRef.current.center();\n      toCreasedNormals(geomRef.current, creaseAngle);\n    }\n  }, [shape, params, creaseAngle]);\n  React.useImperativeHandle(ref, () => geomRef.current);\n  return /*#__PURE__*/React.createElement(\"extrudeGeometry\", _extends({\n    ref: geomRef,\n    args: [shape, params]\n  }, rest));\n});\nexport { RoundedBox, RoundedBoxGeometry };", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON>", "toCreasedNormals", "eps", "createShape", "width", "height", "radius0", "shape", "radius", "absarc", "Math", "PI", "RoundedBox", "forwardRef", "args", "depth", "steps", "smoothness", "bevelSegments", "creaseAngle", "children", "rest", "ref", "createElement", "RoundedBoxGeometry", "useMemo", "params", "bevelEnabled", "bevelSize", "bevelThickness", "curveSegments", "geomRef", "useRef", "useLayoutEffect", "current", "center", "useImperativeHandle"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/RoundedBox.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Shape } from 'three';\nimport { toCreasedNormals } from 'three-stdlib';\n\nconst eps = 0.00001;\nfunction createShape(width, height, radius0) {\n  const shape = new Shape();\n  const radius = radius0 - eps;\n  shape.absarc(eps, eps, eps, -Math.PI / 2, -Math.PI, true);\n  shape.absarc(eps, height - radius * 2, eps, Math.PI, Math.PI / 2, true);\n  shape.absarc(width - radius * 2, height - radius * 2, eps, Math.PI / 2, 0, true);\n  shape.absarc(width - radius * 2, eps, eps, 0, -Math.PI / 2, true);\n  return shape;\n}\nconst RoundedBox = /* @__PURE__ */React.forwardRef(function RoundedBox({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  children,\n  ...rest\n}, ref) {\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(RoundedBoxGeometry, {\n    args: [width, height, depth],\n    radius: radius,\n    steps: steps,\n    smoothness: smoothness,\n    bevelSegments: bevelSegments,\n    creaseAngle: creaseAngle\n  }), children);\n});\nconst RoundedBoxGeometry = /* @__PURE__ */React.forwardRef(function RoundedBoxGeometry({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  ...rest\n}, ref) {\n  const shape = React.useMemo(() => createShape(width, height, radius), [width, height, radius]);\n  const params = React.useMemo(() => ({\n    depth: depth - radius * 2,\n    bevelEnabled: true,\n    bevelSegments: bevelSegments * 2,\n    steps,\n    bevelSize: radius - eps,\n    bevelThickness: radius,\n    curveSegments: smoothness\n  }), [depth, radius, smoothness, bevelSegments, steps]);\n  const geomRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (geomRef.current) {\n      geomRef.current.center();\n      toCreasedNormals(geomRef.current, creaseAngle);\n    }\n  }, [shape, params, creaseAngle]);\n  React.useImperativeHandle(ref, () => geomRef.current);\n  return /*#__PURE__*/React.createElement(\"extrudeGeometry\", _extends({\n    ref: geomRef,\n    args: [shape, params]\n  }, rest));\n});\n\nexport { RoundedBox, RoundedBoxGeometry };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,QAAQ,OAAO;AAC7B,SAASC,gBAAgB,QAAQ,cAAc;AAE/C,MAAMC,GAAG,GAAG,OAAO;AACnB,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAG,IAAIP,KAAK,CAAC,CAAC;EACzB,MAAMQ,MAAM,GAAGF,OAAO,GAAGJ,GAAG;EAC5BK,KAAK,CAACE,MAAM,CAACP,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAE,CAACQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAACD,IAAI,CAACC,EAAE,EAAE,IAAI,CAAC;EACzDJ,KAAK,CAACE,MAAM,CAACP,GAAG,EAAEG,MAAM,GAAGG,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEQ,IAAI,CAACC,EAAE,EAAED,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;EACvEJ,KAAK,CAACE,MAAM,CAACL,KAAK,GAAGI,MAAM,GAAG,CAAC,EAAEH,MAAM,GAAGG,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAChFJ,KAAK,CAACE,MAAM,CAACL,KAAK,GAAGI,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEA,GAAG,EAAE,CAAC,EAAE,CAACQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;EACjE,OAAOJ,KAAK;AACd;AACA,MAAMK,UAAU,GAAG,eAAeb,KAAK,CAACc,UAAU,CAAC,SAASD,UAAUA,CAAC;EACrEE,IAAI,EAAE,CAACV,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEU,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE;EAC7CP,MAAM,GAAG,IAAI;EACbQ,KAAK,GAAG,CAAC;EACTC,UAAU,GAAG,CAAC;EACdC,aAAa,GAAG,CAAC;EACjBC,WAAW,GAAG,GAAG;EACjBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,OAAO,aAAavB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAEzB,QAAQ,CAAC;IACvDwB,GAAG,EAAEA;EACP,CAAC,EAAED,IAAI,CAAC,EAAE,aAAatB,KAAK,CAACwB,aAAa,CAACC,kBAAkB,EAAE;IAC7DV,IAAI,EAAE,CAACV,KAAK,EAAEC,MAAM,EAAEU,KAAK,CAAC;IAC5BP,MAAM,EAAEA,MAAM;IACdQ,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBC,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA;EACf,CAAC,CAAC,EAAEC,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,MAAMI,kBAAkB,GAAG,eAAezB,KAAK,CAACc,UAAU,CAAC,SAASW,kBAAkBA,CAAC;EACrFV,IAAI,EAAE,CAACV,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEU,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE;EAC7CP,MAAM,GAAG,IAAI;EACbQ,KAAK,GAAG,CAAC;EACTC,UAAU,GAAG,CAAC;EACdC,aAAa,GAAG,CAAC;EACjBC,WAAW,GAAG,GAAG;EACjB,GAAGE;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMf,KAAK,GAAGR,KAAK,CAAC0B,OAAO,CAAC,MAAMtB,WAAW,CAACC,KAAK,EAAEC,MAAM,EAAEG,MAAM,CAAC,EAAE,CAACJ,KAAK,EAAEC,MAAM,EAAEG,MAAM,CAAC,CAAC;EAC9F,MAAMkB,MAAM,GAAG3B,KAAK,CAAC0B,OAAO,CAAC,OAAO;IAClCV,KAAK,EAAEA,KAAK,GAAGP,MAAM,GAAG,CAAC;IACzBmB,YAAY,EAAE,IAAI;IAClBT,aAAa,EAAEA,aAAa,GAAG,CAAC;IAChCF,KAAK;IACLY,SAAS,EAAEpB,MAAM,GAAGN,GAAG;IACvB2B,cAAc,EAAErB,MAAM;IACtBsB,aAAa,EAAEb;EACjB,CAAC,CAAC,EAAE,CAACF,KAAK,EAAEP,MAAM,EAAES,UAAU,EAAEC,aAAa,EAAEF,KAAK,CAAC,CAAC;EACtD,MAAMe,OAAO,GAAGhC,KAAK,CAACiC,MAAM,CAAC,IAAI,CAAC;EAClCjC,KAAK,CAACkC,eAAe,CAAC,MAAM;IAC1B,IAAIF,OAAO,CAACG,OAAO,EAAE;MACnBH,OAAO,CAACG,OAAO,CAACC,MAAM,CAAC,CAAC;MACxBlC,gBAAgB,CAAC8B,OAAO,CAACG,OAAO,EAAEf,WAAW,CAAC;IAChD;EACF,CAAC,EAAE,CAACZ,KAAK,EAAEmB,MAAM,EAAEP,WAAW,CAAC,CAAC;EAChCpB,KAAK,CAACqC,mBAAmB,CAACd,GAAG,EAAE,MAAMS,OAAO,CAACG,OAAO,CAAC;EACrD,OAAO,aAAanC,KAAK,CAACwB,aAAa,CAAC,iBAAiB,EAAEzB,QAAQ,CAAC;IAClEwB,GAAG,EAAES,OAAO;IACZjB,IAAI,EAAE,CAACP,KAAK,EAAEmB,MAAM;EACtB,CAAC,EAAEL,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAAST,UAAU,EAAEY,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}