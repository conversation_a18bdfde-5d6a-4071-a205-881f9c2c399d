{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nconst MotionControllerConstants = {\n  Handedness: {\n    NONE: \"none\",\n    LEFT: \"left\",\n    RIGHT: \"right\"\n  },\n  ComponentState: {\n    DEFAULT: \"default\",\n    TOUCHED: \"touched\",\n    PRESSED: \"pressed\"\n  },\n  ComponentProperty: {\n    BUTTON: \"button\",\n    X_AXIS: \"xAxis\",\n    Y_AXIS: \"yAxis\",\n    STATE: \"state\"\n  },\n  ComponentType: {\n    TRIGGER: \"trigger\",\n    SQUEEZE: \"squeeze\",\n    TOUCHPAD: \"touchpad\",\n    THUMBSTICK: \"thumbstick\",\n    BUTTON: \"button\"\n  },\n  ButtonTouchThreshold: 0.05,\n  AxisTouchThreshold: 0.1,\n  VisualResponseProperty: {\n    TRANSFORM: \"transform\",\n    VISIBILITY: \"visibility\"\n  }\n};\nasync function fetchJsonFile(path) {\n  const response = await fetch(path);\n  if (!response.ok) {\n    throw new Error(response.statusText);\n  } else {\n    return response.json();\n  }\n}\nasync function fetchProfilesList(basePath) {\n  if (!basePath) {\n    throw new Error(\"No basePath supplied\");\n  }\n  const profileListFileName = \"profilesList.json\";\n  const profilesList = await fetchJsonFile(`${basePath}/${profileListFileName}`);\n  return profilesList;\n}\nasync function fetchProfile(xrInputSource, basePath, defaultProfile = null, getAssetPath = true) {\n  if (!xrInputSource) {\n    throw new Error(\"No xrInputSource supplied\");\n  }\n  if (!basePath) {\n    throw new Error(\"No basePath supplied\");\n  }\n  const supportedProfilesList = await fetchProfilesList(basePath);\n  let match = void 0;\n  xrInputSource.profiles.some(profileId => {\n    const supportedProfile = supportedProfilesList[profileId];\n    if (supportedProfile) {\n      match = {\n        profileId,\n        profilePath: `${basePath}/${supportedProfile.path}`,\n        deprecated: !!supportedProfile.deprecated\n      };\n    }\n    return !!match;\n  });\n  if (!match) {\n    if (!defaultProfile) {\n      throw new Error(\"No matching profile name found\");\n    }\n    const supportedProfile = supportedProfilesList[defaultProfile];\n    if (!supportedProfile) {\n      throw new Error(`No matching profile name found and default profile \"${defaultProfile}\" missing.`);\n    }\n    match = {\n      profileId: defaultProfile,\n      profilePath: `${basePath}/${supportedProfile.path}`,\n      deprecated: !!supportedProfile.deprecated\n    };\n  }\n  const profile = await fetchJsonFile(match.profilePath);\n  let assetPath = void 0;\n  if (getAssetPath) {\n    let layout;\n    if (xrInputSource.handedness === \"any\") {\n      layout = profile.layouts[Object.keys(profile.layouts)[0]];\n    } else {\n      layout = profile.layouts[xrInputSource.handedness];\n    }\n    if (!layout) {\n      throw new Error(`No matching handedness, ${xrInputSource.handedness}, in profile ${match.profileId}`);\n    }\n    if (layout.assetPath) {\n      assetPath = match.profilePath.replace(\"profile.json\", layout.assetPath);\n    }\n  }\n  return {\n    profile,\n    assetPath\n  };\n}\nconst defaultComponentValues = {\n  xAxis: 0,\n  yAxis: 0,\n  button: 0,\n  state: MotionControllerConstants.ComponentState.DEFAULT\n};\nfunction normalizeAxes(x = 0, y = 0) {\n  let xAxis = x;\n  let yAxis = y;\n  const hypotenuse = Math.sqrt(x * x + y * y);\n  if (hypotenuse > 1) {\n    const theta = Math.atan2(y, x);\n    xAxis = Math.cos(theta);\n    yAxis = Math.sin(theta);\n  }\n  const result = {\n    normalizedXAxis: xAxis * 0.5 + 0.5,\n    normalizedYAxis: yAxis * 0.5 + 0.5\n  };\n  return result;\n}\nclass VisualResponse {\n  constructor(visualResponseDescription) {\n    __publicField(this, \"value\");\n    __publicField(this, \"componentProperty\");\n    __publicField(this, \"states\");\n    __publicField(this, \"valueNodeName\");\n    __publicField(this, \"valueNodeProperty\");\n    __publicField(this, \"minNodeName\");\n    __publicField(this, \"maxNodeName\");\n    __publicField(this, \"valueNode\");\n    __publicField(this, \"minNode\");\n    __publicField(this, \"maxNode\");\n    this.componentProperty = visualResponseDescription.componentProperty;\n    this.states = visualResponseDescription.states;\n    this.valueNodeName = visualResponseDescription.valueNodeName;\n    this.valueNodeProperty = visualResponseDescription.valueNodeProperty;\n    if (this.valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM) {\n      this.minNodeName = visualResponseDescription.minNodeName;\n      this.maxNodeName = visualResponseDescription.maxNodeName;\n    }\n    this.value = 0;\n    this.updateFromComponent(defaultComponentValues);\n  }\n  /**\n   * Computes the visual response's interpolation weight based on component state\n   * @param {Object} componentValues - The component from which to update\n   * @param {number | undefined} xAxis - The reported X axis value of the component\n   * @param {number | undefined} yAxis - The reported Y axis value of the component\n   * @param {number | undefined} button - The reported value of the component's button\n   * @param {string} state - The component's active state\n   */\n  updateFromComponent({\n    xAxis,\n    yAxis,\n    button,\n    state\n  }) {\n    const {\n      normalizedXAxis,\n      normalizedYAxis\n    } = normalizeAxes(xAxis, yAxis);\n    switch (this.componentProperty) {\n      case MotionControllerConstants.ComponentProperty.X_AXIS:\n        this.value = this.states.includes(state) ? normalizedXAxis : 0.5;\n        break;\n      case MotionControllerConstants.ComponentProperty.Y_AXIS:\n        this.value = this.states.includes(state) ? normalizedYAxis : 0.5;\n        break;\n      case MotionControllerConstants.ComponentProperty.BUTTON:\n        this.value = this.states.includes(state) && button ? button : 0;\n        break;\n      case MotionControllerConstants.ComponentProperty.STATE:\n        if (this.valueNodeProperty === MotionControllerConstants.VisualResponseProperty.VISIBILITY) {\n          this.value = this.states.includes(state);\n        } else {\n          this.value = this.states.includes(state) ? 1 : 0;\n        }\n        break;\n      default:\n        throw new Error(`Unexpected visualResponse componentProperty ${this.componentProperty}`);\n    }\n  }\n}\nclass Component {\n  /**\n   * @param {string} componentId - Id of the component\n   * @param {InputProfileComponent} componentDescription - Description of the component to be created\n   */\n  constructor(componentId, componentDescription) {\n    __publicField(this, \"id\");\n    __publicField(this, \"values\");\n    __publicField(this, \"type\");\n    __publicField(this, \"gamepadIndices\");\n    __publicField(this, \"rootNodeName\");\n    __publicField(this, \"visualResponses\");\n    __publicField(this, \"touchPointNodeName\");\n    __publicField(this, \"touchPointNode\");\n    if (!componentId || !componentDescription || !componentDescription.visualResponses || !componentDescription.gamepadIndices || Object.keys(componentDescription.gamepadIndices).length === 0) {\n      throw new Error(\"Invalid arguments supplied\");\n    }\n    this.id = componentId;\n    this.type = componentDescription.type;\n    this.rootNodeName = componentDescription.rootNodeName;\n    this.touchPointNodeName = componentDescription.touchPointNodeName;\n    this.visualResponses = {};\n    Object.keys(componentDescription.visualResponses).forEach(responseName => {\n      const visualResponse = new VisualResponse(componentDescription.visualResponses[responseName]);\n      this.visualResponses[responseName] = visualResponse;\n    });\n    this.gamepadIndices = Object.assign({}, componentDescription.gamepadIndices);\n    this.values = {\n      state: MotionControllerConstants.ComponentState.DEFAULT,\n      button: this.gamepadIndices.button !== void 0 ? 0 : void 0,\n      xAxis: this.gamepadIndices.xAxis !== void 0 ? 0 : void 0,\n      yAxis: this.gamepadIndices.yAxis !== void 0 ? 0 : void 0\n    };\n  }\n  get data() {\n    const data = {\n      id: this.id,\n      ...this.values\n    };\n    return data;\n  }\n  /**\n   * @description Poll for updated data based on current gamepad state\n   * @param {Object} gamepad - The gamepad object from which the component data should be polled\n   */\n  updateFromGamepad(gamepad) {\n    this.values.state = MotionControllerConstants.ComponentState.DEFAULT;\n    if (this.gamepadIndices.button !== void 0 && gamepad.buttons.length > this.gamepadIndices.button) {\n      const gamepadButton = gamepad.buttons[this.gamepadIndices.button];\n      this.values.button = gamepadButton.value;\n      this.values.button = this.values.button < 0 ? 0 : this.values.button;\n      this.values.button = this.values.button > 1 ? 1 : this.values.button;\n      if (gamepadButton.pressed || this.values.button === 1) {\n        this.values.state = MotionControllerConstants.ComponentState.PRESSED;\n      } else if (gamepadButton.touched || this.values.button > MotionControllerConstants.ButtonTouchThreshold) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED;\n      }\n    }\n    if (this.gamepadIndices.xAxis !== void 0 && gamepad.axes.length > this.gamepadIndices.xAxis) {\n      this.values.xAxis = gamepad.axes[this.gamepadIndices.xAxis];\n      this.values.xAxis = this.values.xAxis < -1 ? -1 : this.values.xAxis;\n      this.values.xAxis = this.values.xAxis > 1 ? 1 : this.values.xAxis;\n      if (this.values.state === MotionControllerConstants.ComponentState.DEFAULT && Math.abs(this.values.xAxis) > MotionControllerConstants.AxisTouchThreshold) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED;\n      }\n    }\n    if (this.gamepadIndices.yAxis !== void 0 && gamepad.axes.length > this.gamepadIndices.yAxis) {\n      this.values.yAxis = gamepad.axes[this.gamepadIndices.yAxis];\n      this.values.yAxis = this.values.yAxis < -1 ? -1 : this.values.yAxis;\n      this.values.yAxis = this.values.yAxis > 1 ? 1 : this.values.yAxis;\n      if (this.values.state === MotionControllerConstants.ComponentState.DEFAULT && Math.abs(this.values.yAxis) > MotionControllerConstants.AxisTouchThreshold) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED;\n      }\n    }\n    Object.values(this.visualResponses).forEach(visualResponse => {\n      visualResponse.updateFromComponent(this.values);\n    });\n  }\n}\nclass MotionController {\n  /**\n   * @param {XRInputSource} xrInputSource - The XRInputSource to build the MotionController around\n   * @param {Profile} profile - The best matched profile description for the supplied xrInputSource\n   * @param {string} assetUrl\n   */\n  constructor(xrInputSource, profile, assetUrl) {\n    __publicField(this, \"xrInputSource\");\n    __publicField(this, \"assetUrl\");\n    __publicField(this, \"layoutDescription\");\n    __publicField(this, \"id\");\n    __publicField(this, \"components\");\n    if (!xrInputSource) {\n      throw new Error(\"No xrInputSource supplied\");\n    }\n    if (!profile) {\n      throw new Error(\"No profile supplied\");\n    }\n    if (!profile.layouts[xrInputSource.handedness]) {\n      throw new Error(\"No layout for \" + xrInputSource.handedness + \" handedness\");\n    }\n    this.xrInputSource = xrInputSource;\n    this.assetUrl = assetUrl;\n    this.id = profile.profileId;\n    this.layoutDescription = profile.layouts[xrInputSource.handedness];\n    this.components = {};\n    Object.keys(this.layoutDescription.components).forEach(componentId => {\n      const componentDescription = this.layoutDescription.components[componentId];\n      this.components[componentId] = new Component(componentId, componentDescription);\n    });\n    this.updateFromGamepad();\n  }\n  get gripSpace() {\n    return this.xrInputSource.gripSpace;\n  }\n  get targetRaySpace() {\n    return this.xrInputSource.targetRaySpace;\n  }\n  /**\n   * @description Returns a subset of component data for simplified debugging\n   */\n  get data() {\n    const data = [];\n    Object.values(this.components).forEach(component => {\n      data.push(component.data);\n    });\n    return data;\n  }\n  /**\n   * @description Poll for updated data based on current gamepad state\n   */\n  updateFromGamepad() {\n    Object.values(this.components).forEach(component => {\n      component.updateFromGamepad(this.xrInputSource.gamepad);\n    });\n  }\n}\nexport { MotionController, MotionControllerConstants, fetchProfile, fetchProfilesList };", "map": {"version": 3, "names": ["MotionControllerConstants", "Handedness", "NONE", "LEFT", "RIGHT", "ComponentState", "DEFAULT", "TOUCHED", "PRESSED", "ComponentProperty", "BUTTON", "X_AXIS", "Y_AXIS", "STATE", "ComponentType", "TRIGGER", "SQUEEZE", "TOUCHPAD", "THUMBSTICK", "ButtonTouchThreshold", "AxisTouchThreshold", "VisualResponseProperty", "TRANSFORM", "VISIBILITY", "fetchJsonFile", "path", "response", "fetch", "ok", "Error", "statusText", "json", "fetchProfilesList", "basePath", "profileListFileName", "profilesList", "fetchProfile", "xrInputSource", "defaultProfile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportedProfilesList", "match", "profiles", "some", "profileId", "supportedProfile", "profilePath", "deprecated", "profile", "assetPath", "layout", "handedness", "layouts", "Object", "keys", "replace", "defaultComponentValues", "xAxis", "yAxis", "button", "state", "normalizeAxes", "x", "y", "hypotenuse", "Math", "sqrt", "theta", "atan2", "cos", "sin", "result", "normalizedXAxis", "normalizedYAxis", "VisualResponse", "constructor", "visualResponseDescription", "__publicField", "componentProperty", "states", "valueNodeName", "valueNodeProperty", "minNodeName", "maxNodeName", "value", "updateFromComponent", "includes", "Component", "componentId", "componentDescription", "visualResponses", "gamepadIndices", "length", "id", "type", "rootNodeName", "touchPointNodeName", "for<PERSON>ach", "responseName", "visualResponse", "assign", "values", "data", "updateFromGamepad", "gamepad", "buttons", "gamepadButton", "pressed", "touched", "axes", "abs", "MotionController", "assetUrl", "layoutDescription", "components", "gripSpace", "targetRaySpace", "component", "push"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/libs/MotionControllers.ts"], "sourcesContent": ["/**\n * @webxr-input-profiles/motion-controllers 1.0.0 https://github.com/immersive-web/webxr-input-profiles\n */\n\nimport type { Object3D } from 'three'\n\ninterface GamepadIndices {\n  button: number\n  xAxis?: number\n  yAxis?: number\n}\n\ninterface VisualResponseDescription {\n  componentProperty: string\n  states: string[]\n  valueNodeProperty: string\n  valueNodeName: string\n  minNodeName?: string\n  maxNodeName?: string\n}\n\ntype VisualResponses = Record<string, VisualResponseDescription>\n\ninterface ComponentDescription {\n  type: string\n  gamepadIndices: GamepadIndices\n  rootNodeName: string\n  visualResponses: VisualResponses\n  touchPointNodeName?: string\n}\n\ninterface Components {\n  [componentKey: string]: ComponentDescription\n}\n\ninterface LayoutDescription {\n  selectComponentId: string\n  components: Components\n  gamepadMapping: string\n  rootNodeName: string\n  assetPath: string\n}\n\ntype Layouts = Partial<Record<XRHandedness, LayoutDescription>>\n\nexport interface Profile {\n  profileId: string\n  fallbackProfileIds: string[]\n  layouts: Layouts\n}\n\ninterface ProfilesList {\n  [profileId: string]: { path: string; deprecated?: boolean } | undefined\n}\n\nconst MotionControllerConstants = {\n  Handedness: {\n    NONE: 'none',\n    LEFT: 'left',\n    RIGHT: 'right',\n  },\n\n  ComponentState: {\n    DEFAULT: 'default',\n    TOUCHED: 'touched',\n    PRESSED: 'pressed',\n  },\n\n  ComponentProperty: {\n    BUTTON: 'button',\n    X_AXIS: 'xAxis',\n    Y_AXIS: 'yAxis',\n    STATE: 'state',\n  },\n\n  ComponentType: {\n    TRIGGER: 'trigger',\n    SQUEEZE: 'squeeze',\n    TOUCHPAD: 'touchpad',\n    THUMBSTICK: 'thumbstick',\n    BUTTON: 'button',\n  },\n\n  ButtonTouchThreshold: 0.05,\n\n  AxisTouchThreshold: 0.1,\n\n  VisualResponseProperty: {\n    TRANSFORM: 'transform',\n    VISIBILITY: 'visibility',\n  },\n}\n\n/**\n * @description Static helper function to fetch a JSON file and turn it into a JS object\n * @param {string} path - Path to JSON file to be fetched\n */\nasync function fetchJsonFile<T>(path: string): Promise<T> {\n  const response = await fetch(path)\n  if (!response.ok) {\n    throw new Error(response.statusText)\n  } else {\n    return response.json()\n  }\n}\n\nasync function fetchProfilesList(basePath: string): Promise<ProfilesList> {\n  if (!basePath) {\n    throw new Error('No basePath supplied')\n  }\n\n  const profileListFileName = 'profilesList.json'\n  const profilesList = await fetchJsonFile<ProfilesList>(`${basePath}/${profileListFileName}`)\n  return profilesList\n}\n\nasync function fetchProfile(\n  xrInputSource: XRInputSource,\n  basePath: string,\n  defaultProfile: string | null = null,\n  getAssetPath = true,\n): Promise<{ profile: Profile; assetPath: string | undefined }> {\n  if (!xrInputSource) {\n    throw new Error('No xrInputSource supplied')\n  }\n\n  if (!basePath) {\n    throw new Error('No basePath supplied')\n  }\n\n  // Get the list of profiles\n  const supportedProfilesList = await fetchProfilesList(basePath)\n\n  // Find the relative path to the first requested profile that is recognized\n  let match: { profileId: string; profilePath: string; deprecated: boolean } | undefined = undefined\n  xrInputSource.profiles.some((profileId: string) => {\n    const supportedProfile = supportedProfilesList[profileId]\n    if (supportedProfile) {\n      match = {\n        profileId,\n        profilePath: `${basePath}/${supportedProfile.path}`,\n        deprecated: !!supportedProfile.deprecated,\n      }\n    }\n    return !!match\n  })\n\n  if (!match) {\n    if (!defaultProfile) {\n      throw new Error('No matching profile name found')\n    }\n\n    const supportedProfile = supportedProfilesList[defaultProfile]\n    if (!supportedProfile) {\n      throw new Error(`No matching profile name found and default profile \"${defaultProfile}\" missing.`)\n    }\n\n    match = {\n      profileId: defaultProfile,\n      profilePath: `${basePath}/${supportedProfile.path}`,\n      deprecated: !!supportedProfile.deprecated,\n    }\n  }\n\n  const profile = await fetchJsonFile<Profile>(match.profilePath)\n\n  let assetPath: string | undefined = undefined\n  if (getAssetPath) {\n    let layout\n    if ((xrInputSource.handedness as string) === 'any') {\n      layout = profile.layouts[Object.keys(profile.layouts)[0] as XRHandedness]\n    } else {\n      layout = profile.layouts[xrInputSource.handedness]\n    }\n    if (!layout) {\n      throw new Error(`No matching handedness, ${xrInputSource.handedness}, in profile ${match.profileId}`)\n    }\n\n    if (layout.assetPath) {\n      assetPath = match.profilePath.replace('profile.json', layout.assetPath)\n    }\n  }\n\n  return { profile, assetPath }\n}\n\n/** @constant {Object} */\nconst defaultComponentValues = {\n  xAxis: 0,\n  yAxis: 0,\n  button: 0,\n  state: MotionControllerConstants.ComponentState.DEFAULT,\n}\n\n/**\n * @description Converts an X, Y coordinate from the range -1 to 1 (as reported by the Gamepad\n * API) to the range 0 to 1 (for interpolation). Also caps the X, Y values to be bounded within\n * a circle. This ensures that thumbsticks are not animated outside the bounds of their physical\n * range of motion and touchpads do not report touch locations off their physical bounds.\n * @param {number | undefined} x The original x coordinate in the range -1 to 1\n * @param {number | undefined} y The original y coordinate in the range -1 to 1\n */\nfunction normalizeAxes(\n  x: number | undefined = 0,\n  y: number | undefined = 0,\n): { normalizedXAxis: number; normalizedYAxis: number } {\n  let xAxis = x\n  let yAxis = y\n\n  // Determine if the point is outside the bounds of the circle\n  // and, if so, place it on the edge of the circle\n  const hypotenuse = Math.sqrt(x * x + y * y)\n  if (hypotenuse > 1) {\n    const theta = Math.atan2(y, x)\n    xAxis = Math.cos(theta)\n    yAxis = Math.sin(theta)\n  }\n\n  // Scale and move the circle so values are in the interpolation range.  The circle's origin moves\n  // from (0, 0) to (0.5, 0.5). The circle's radius scales from 1 to be 0.5.\n  const result = {\n    normalizedXAxis: xAxis * 0.5 + 0.5,\n    normalizedYAxis: yAxis * 0.5 + 0.5,\n  }\n  return result\n}\n\n/**\n * Contains the description of how the 3D model should visually respond to a specific user input.\n * This is accomplished by initializing the object with the name of a node in the 3D model and\n * property that need to be modified in response to user input, the name of the nodes representing\n * the allowable range of motion, and the name of the input which triggers the change. In response\n * to the named input changing, this object computes the appropriate weighting to use for\n * interpolating between the range of motion nodes.\n */\nclass VisualResponse implements VisualResponseDescription {\n  value: number | boolean\n  componentProperty: string\n  states: string[]\n  valueNodeName: string\n  valueNodeProperty: string\n  minNodeName?: string\n  maxNodeName?: string\n  valueNode: Object3D | undefined\n  minNode: Object3D | undefined\n  maxNode: Object3D | undefined\n  constructor(visualResponseDescription: VisualResponseDescription) {\n    this.componentProperty = visualResponseDescription.componentProperty\n    this.states = visualResponseDescription.states\n    this.valueNodeName = visualResponseDescription.valueNodeName\n    this.valueNodeProperty = visualResponseDescription.valueNodeProperty\n\n    if (this.valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM) {\n      this.minNodeName = visualResponseDescription.minNodeName\n      this.maxNodeName = visualResponseDescription.maxNodeName\n    }\n\n    // Initializes the response's current value based on default data\n    this.value = 0\n    this.updateFromComponent(defaultComponentValues)\n  }\n\n  /**\n   * Computes the visual response's interpolation weight based on component state\n   * @param {Object} componentValues - The component from which to update\n   * @param {number | undefined} xAxis - The reported X axis value of the component\n   * @param {number | undefined} yAxis - The reported Y axis value of the component\n   * @param {number | undefined} button - The reported value of the component's button\n   * @param {string} state - The component's active state\n   */\n  updateFromComponent({\n    xAxis,\n    yAxis,\n    button,\n    state,\n  }: {\n    xAxis?: number\n    yAxis?: number\n    button?: number\n    state: string\n  }): void {\n    const { normalizedXAxis, normalizedYAxis } = normalizeAxes(xAxis, yAxis)\n    switch (this.componentProperty) {\n      case MotionControllerConstants.ComponentProperty.X_AXIS:\n        this.value = this.states.includes(state) ? normalizedXAxis : 0.5\n        break\n      case MotionControllerConstants.ComponentProperty.Y_AXIS:\n        this.value = this.states.includes(state) ? normalizedYAxis : 0.5\n        break\n      case MotionControllerConstants.ComponentProperty.BUTTON:\n        this.value = this.states.includes(state) && button ? button : 0\n        break\n      case MotionControllerConstants.ComponentProperty.STATE:\n        if (this.valueNodeProperty === MotionControllerConstants.VisualResponseProperty.VISIBILITY) {\n          this.value = this.states.includes(state)\n        } else {\n          this.value = this.states.includes(state) ? 1.0 : 0.0\n        }\n        break\n      default:\n        throw new Error(`Unexpected visualResponse componentProperty ${this.componentProperty}`)\n    }\n  }\n}\n\nclass Component implements ComponentDescription {\n  id: string\n  values: {\n    state: string\n    button: number | undefined\n    xAxis: number | undefined\n    yAxis: number | undefined\n  }\n\n  type: string\n  gamepadIndices: GamepadIndices\n  rootNodeName: string\n  visualResponses: Record<string, VisualResponse>\n  touchPointNodeName?: string | undefined\n  touchPointNode?: Object3D\n\n  /**\n   * @param {string} componentId - Id of the component\n   * @param {InputProfileComponent} componentDescription - Description of the component to be created\n   */\n  constructor(componentId: string, componentDescription: ComponentDescription) {\n    if (\n      !componentId ||\n      !componentDescription ||\n      !componentDescription.visualResponses ||\n      !componentDescription.gamepadIndices ||\n      Object.keys(componentDescription.gamepadIndices).length === 0\n    ) {\n      throw new Error('Invalid arguments supplied')\n    }\n\n    this.id = componentId\n    this.type = componentDescription.type\n    this.rootNodeName = componentDescription.rootNodeName\n    this.touchPointNodeName = componentDescription.touchPointNodeName\n\n    // Build all the visual responses for this component\n    this.visualResponses = {}\n    Object.keys(componentDescription.visualResponses).forEach((responseName) => {\n      const visualResponse = new VisualResponse(componentDescription.visualResponses[responseName])\n      this.visualResponses[responseName] = visualResponse\n    })\n\n    // Set default values\n    this.gamepadIndices = Object.assign({}, componentDescription.gamepadIndices)\n\n    this.values = {\n      state: MotionControllerConstants.ComponentState.DEFAULT,\n      button: this.gamepadIndices.button !== undefined ? 0 : undefined,\n      xAxis: this.gamepadIndices.xAxis !== undefined ? 0 : undefined,\n      yAxis: this.gamepadIndices.yAxis !== undefined ? 0 : undefined,\n    }\n  }\n\n  get data(): { id: Component['id'] } & Component['values'] {\n    const data = { id: this.id, ...this.values }\n    return data\n  }\n\n  /**\n   * @description Poll for updated data based on current gamepad state\n   * @param {Object} gamepad - The gamepad object from which the component data should be polled\n   */\n  updateFromGamepad(gamepad: Gamepad): void {\n    // Set the state to default before processing other data sources\n    this.values.state = MotionControllerConstants.ComponentState.DEFAULT\n\n    // Get and normalize button\n    if (this.gamepadIndices.button !== undefined && gamepad.buttons.length > this.gamepadIndices.button) {\n      const gamepadButton = gamepad.buttons[this.gamepadIndices.button]\n      this.values.button = gamepadButton.value\n      this.values.button = this.values.button! < 0 ? 0 : this.values.button\n      this.values.button = this.values.button! > 1 ? 1 : this.values.button\n\n      // Set the state based on the button\n      if (gamepadButton.pressed || this.values.button === 1) {\n        this.values.state = MotionControllerConstants.ComponentState.PRESSED\n      } else if (gamepadButton.touched || this.values.button! > MotionControllerConstants.ButtonTouchThreshold) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED\n      }\n    }\n\n    // Get and normalize x axis value\n    if (this.gamepadIndices.xAxis !== undefined && gamepad.axes.length > this.gamepadIndices.xAxis) {\n      this.values.xAxis = gamepad.axes[this.gamepadIndices.xAxis]\n      this.values.xAxis = this.values.xAxis! < -1 ? -1 : this.values.xAxis\n      this.values.xAxis = this.values.xAxis! > 1 ? 1 : this.values.xAxis\n\n      // If the state is still default, check if the xAxis makes it touched\n      if (\n        this.values.state === MotionControllerConstants.ComponentState.DEFAULT &&\n        Math.abs(this.values.xAxis!) > MotionControllerConstants.AxisTouchThreshold\n      ) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED\n      }\n    }\n\n    // Get and normalize Y axis value\n    if (this.gamepadIndices.yAxis !== undefined && gamepad.axes.length > this.gamepadIndices.yAxis) {\n      this.values.yAxis = gamepad.axes[this.gamepadIndices.yAxis]\n      this.values.yAxis = this.values.yAxis! < -1 ? -1 : this.values.yAxis\n      this.values.yAxis = this.values.yAxis! > 1 ? 1 : this.values.yAxis\n\n      // If the state is still default, check if the yAxis makes it touched\n      if (\n        this.values.state === MotionControllerConstants.ComponentState.DEFAULT &&\n        Math.abs(this.values.yAxis!) > MotionControllerConstants.AxisTouchThreshold\n      ) {\n        this.values.state = MotionControllerConstants.ComponentState.TOUCHED\n      }\n    }\n\n    // Update the visual response weights based on the current component data\n    Object.values(this.visualResponses).forEach((visualResponse) => {\n      visualResponse.updateFromComponent(this.values)\n    })\n  }\n}\n/**\n * @description Builds a motion controller with components and visual responses based on the\n * supplied profile description. Data is polled from the xrInputSource's gamepad.\n * <AUTHOR> Waliczek / https://github.com/NellWaliczek\n */\nclass MotionController {\n  xrInputSource: XRInputSource\n  assetUrl: string\n  layoutDescription: LayoutDescription\n  id: string\n  components: Record<string, Component>\n  /**\n   * @param {XRInputSource} xrInputSource - The XRInputSource to build the MotionController around\n   * @param {Profile} profile - The best matched profile description for the supplied xrInputSource\n   * @param {string} assetUrl\n   */\n  constructor(xrInputSource: XRInputSource, profile: Profile, assetUrl: string) {\n    if (!xrInputSource) {\n      throw new Error('No xrInputSource supplied')\n    }\n\n    if (!profile) {\n      throw new Error('No profile supplied')\n    }\n\n    if (!profile.layouts[xrInputSource.handedness]) {\n      throw new Error('No layout for ' + xrInputSource.handedness + ' handedness')\n    }\n\n    this.xrInputSource = xrInputSource\n    this.assetUrl = assetUrl\n    this.id = profile.profileId\n\n    // Build child components as described in the profile description\n    this.layoutDescription = profile.layouts[xrInputSource.handedness]!\n\n    this.components = {}\n    Object.keys(this.layoutDescription.components).forEach((componentId) => {\n      const componentDescription = this.layoutDescription.components[componentId]\n      this.components[componentId] = new Component(componentId, componentDescription)\n    })\n\n    // Initialize components based on current gamepad state\n    this.updateFromGamepad()\n  }\n\n  get gripSpace(): XRInputSource['gripSpace'] {\n    return this.xrInputSource.gripSpace\n  }\n\n  get targetRaySpace(): XRInputSource['targetRaySpace'] {\n    return this.xrInputSource.targetRaySpace\n  }\n\n  /**\n   * @description Returns a subset of component data for simplified debugging\n   */\n  get data(): Array<Component['data']> {\n    const data: Array<Component['data']> = []\n    Object.values(this.components).forEach((component) => {\n      data.push(component.data)\n    })\n    return data\n  }\n\n  /**\n   * @description Poll for updated data based on current gamepad state\n   */\n  updateFromGamepad(): void {\n    Object.values(this.components).forEach((component) => {\n      component.updateFromGamepad(this.xrInputSource.gamepad!)\n    })\n  }\n}\n\nexport { MotionControllerConstants, MotionController, fetchProfile, fetchProfilesList }\n"], "mappings": ";;;;;;;;;;;AAuDA,MAAMA,yBAAA,GAA4B;EAChCC,UAAA,EAAY;IACVC,IAAA,EAAM;IACNC,IAAA,EAAM;IACNC,KAAA,EAAO;EACT;EAEAC,cAAA,EAAgB;IACdC,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,OAAA,EAAS;EACX;EAEAC,iBAAA,EAAmB;IACjBC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,KAAA,EAAO;EACT;EAEAC,aAAA,EAAe;IACbC,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,QAAA,EAAU;IACVC,UAAA,EAAY;IACZR,MAAA,EAAQ;EACV;EAEAS,oBAAA,EAAsB;EAEtBC,kBAAA,EAAoB;EAEpBC,sBAAA,EAAwB;IACtBC,SAAA,EAAW;IACXC,UAAA,EAAY;EACd;AACF;AAMA,eAAeC,cAAiBC,IAAA,EAA0B;EAClD,MAAAC,QAAA,GAAW,MAAMC,KAAA,CAAMF,IAAI;EAC7B,KAACC,QAAA,CAASE,EAAA,EAAI;IACV,UAAIC,KAAA,CAAMH,QAAA,CAASI,UAAU;EAAA,OAC9B;IACL,OAAOJ,QAAA,CAASK,IAAA;EAClB;AACF;AAEA,eAAeC,kBAAkBC,QAAA,EAAyC;EACxE,IAAI,CAACA,QAAA,EAAU;IACP,UAAIJ,KAAA,CAAM,sBAAsB;EACxC;EAEA,MAAMK,mBAAA,GAAsB;EAC5B,MAAMC,YAAA,GAAe,MAAMX,aAAA,CAA4B,GAAGS,QAAA,IAAYC,mBAAA,EAAqB;EACpF,OAAAC,YAAA;AACT;AAEA,eAAeC,aACbC,aAAA,EACAJ,QAAA,EACAK,cAAA,GAAgC,MAChCC,YAAA,GAAe,MAC+C;EAC9D,IAAI,CAACF,aAAA,EAAe;IACZ,UAAIR,KAAA,CAAM,2BAA2B;EAC7C;EAEA,IAAI,CAACI,QAAA,EAAU;IACP,UAAIJ,KAAA,CAAM,sBAAsB;EACxC;EAGM,MAAAW,qBAAA,GAAwB,MAAMR,iBAAA,CAAkBC,QAAQ;EAG9D,IAAIQ,KAAA,GAAqF;EAC3EJ,aAAA,CAAAK,QAAA,CAASC,IAAA,CAAMC,SAAA,IAAsB;IAC3C,MAAAC,gBAAA,GAAmBL,qBAAA,CAAsBI,SAAS;IACxD,IAAIC,gBAAA,EAAkB;MACZJ,KAAA;QACNG,SAAA;QACAE,WAAA,EAAa,GAAGb,QAAA,IAAYY,gBAAA,CAAiBpB,IAAA;QAC7CsB,UAAA,EAAY,CAAC,CAACF,gBAAA,CAAiBE;MAAA;IAEnC;IACA,OAAO,CAAC,CAACN,KAAA;EAAA,CACV;EAED,IAAI,CAACA,KAAA,EAAO;IACV,IAAI,CAACH,cAAA,EAAgB;MACb,UAAIT,KAAA,CAAM,gCAAgC;IAClD;IAEM,MAAAgB,gBAAA,GAAmBL,qBAAA,CAAsBF,cAAc;IAC7D,IAAI,CAACO,gBAAA,EAAkB;MACf,UAAIhB,KAAA,CAAM,uDAAuDS,cAAA,YAA0B;IACnG;IAEQG,KAAA;MACNG,SAAA,EAAWN,cAAA;MACXQ,WAAA,EAAa,GAAGb,QAAA,IAAYY,gBAAA,CAAiBpB,IAAA;MAC7CsB,UAAA,EAAY,CAAC,CAACF,gBAAA,CAAiBE;IAAA;EAEnC;EAEA,MAAMC,OAAA,GAAU,MAAMxB,aAAA,CAAuBiB,KAAA,CAAMK,WAAW;EAE9D,IAAIG,SAAA,GAAgC;EACpC,IAAIV,YAAA,EAAc;IACZ,IAAAW,MAAA;IACC,IAAAb,aAAA,CAAcc,UAAA,KAA0B,OAAO;MACzCD,MAAA,GAAAF,OAAA,CAAQI,OAAA,CAAQC,MAAA,CAAOC,IAAA,CAAKN,OAAA,CAAQI,OAAO,EAAE,CAAC,CAAiB;IAAA,OACnE;MACIF,MAAA,GAAAF,OAAA,CAAQI,OAAA,CAAQf,aAAA,CAAcc,UAAU;IACnD;IACA,IAAI,CAACD,MAAA,EAAQ;MACX,MAAM,IAAIrB,KAAA,CAAM,2BAA2BQ,aAAA,CAAcc,UAAA,gBAA0BV,KAAA,CAAMG,SAAA,EAAW;IACtG;IAEA,IAAIM,MAAA,CAAOD,SAAA,EAAW;MACpBA,SAAA,GAAYR,KAAA,CAAMK,WAAA,CAAYS,OAAA,CAAQ,gBAAgBL,MAAA,CAAOD,SAAS;IACxE;EACF;EAEO;IAAED,OAAA;IAASC;EAAA;AACpB;AAGA,MAAMO,sBAAA,GAAyB;EAC7BC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,KAAA,EAAO5D,yBAAA,CAA0BK,cAAA,CAAeC;AAClD;AAUA,SAASuD,cACPC,CAAA,GAAwB,GACxBC,CAAA,GAAwB,GAC8B;EACtD,IAAIN,KAAA,GAAQK,CAAA;EACZ,IAAIJ,KAAA,GAAQK,CAAA;EAIZ,MAAMC,UAAA,GAAaC,IAAA,CAAKC,IAAA,CAAKJ,CAAA,GAAIA,CAAA,GAAIC,CAAA,GAAIA,CAAC;EAC1C,IAAIC,UAAA,GAAa,GAAG;IAClB,MAAMG,KAAA,GAAQF,IAAA,CAAKG,KAAA,CAAML,CAAA,EAAGD,CAAC;IACrBL,KAAA,GAAAQ,IAAA,CAAKI,GAAA,CAAIF,KAAK;IACdT,KAAA,GAAAO,IAAA,CAAKK,GAAA,CAAIH,KAAK;EACxB;EAIA,MAAMI,MAAA,GAAS;IACbC,eAAA,EAAiBf,KAAA,GAAQ,MAAM;IAC/BgB,eAAA,EAAiBf,KAAA,GAAQ,MAAM;EAAA;EAE1B,OAAAa,MAAA;AACT;AAUA,MAAMG,cAAA,CAAoD;EAWxDC,YAAYC,yBAAA,EAAsD;IAVlEC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEE,KAAKC,iBAAA,GAAoBF,yBAAA,CAA0BE,iBAAA;IACnD,KAAKC,MAAA,GAASH,yBAAA,CAA0BG,MAAA;IACxC,KAAKC,aAAA,GAAgBJ,yBAAA,CAA0BI,aAAA;IAC/C,KAAKC,iBAAA,GAAoBL,yBAAA,CAA0BK,iBAAA;IAEnD,IAAI,KAAKA,iBAAA,KAAsBjF,yBAAA,CAA0BqB,sBAAA,CAAuBC,SAAA,EAAW;MACzF,KAAK4D,WAAA,GAAcN,yBAAA,CAA0BM,WAAA;MAC7C,KAAKC,WAAA,GAAcP,yBAAA,CAA0BO,WAAA;IAC/C;IAGA,KAAKC,KAAA,GAAQ;IACb,KAAKC,mBAAA,CAAoB7B,sBAAsB;EACjD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA6B,oBAAoB;IAClB5B,KAAA;IACAC,KAAA;IACAC,MAAA;IACAC;EAAA,GAMO;IACP,MAAM;MAAEY,eAAA;MAAiBC;IAAA,IAAoBZ,aAAA,CAAcJ,KAAA,EAAOC,KAAK;IACvE,QAAQ,KAAKoB,iBAAA;MACX,KAAK9E,yBAAA,CAA0BS,iBAAA,CAAkBE,MAAA;QAC/C,KAAKyE,KAAA,GAAQ,KAAKL,MAAA,CAAOO,QAAA,CAAS1B,KAAK,IAAIY,eAAA,GAAkB;QAC7D;MACF,KAAKxE,yBAAA,CAA0BS,iBAAA,CAAkBG,MAAA;QAC/C,KAAKwE,KAAA,GAAQ,KAAKL,MAAA,CAAOO,QAAA,CAAS1B,KAAK,IAAIa,eAAA,GAAkB;QAC7D;MACF,KAAKzE,yBAAA,CAA0BS,iBAAA,CAAkBC,MAAA;QAC/C,KAAK0E,KAAA,GAAQ,KAAKL,MAAA,CAAOO,QAAA,CAAS1B,KAAK,KAAKD,MAAA,GAASA,MAAA,GAAS;QAC9D;MACF,KAAK3D,yBAAA,CAA0BS,iBAAA,CAAkBI,KAAA;QAC/C,IAAI,KAAKoE,iBAAA,KAAsBjF,yBAAA,CAA0BqB,sBAAA,CAAuBE,UAAA,EAAY;UAC1F,KAAK6D,KAAA,GAAQ,KAAKL,MAAA,CAAOO,QAAA,CAAS1B,KAAK;QAAA,OAClC;UACL,KAAKwB,KAAA,GAAQ,KAAKL,MAAA,CAAOO,QAAA,CAAS1B,KAAK,IAAI,IAAM;QACnD;QACA;MACF;QACE,MAAM,IAAI/B,KAAA,CAAM,+CAA+C,KAAKiD,iBAAA,EAAmB;IAC3F;EACF;AACF;AAEA,MAAMS,SAAA,CAA0C;EAAA;AAAA;AAAA;AAAA;EAoB9CZ,YAAYa,WAAA,EAAqBC,oBAAA,EAA4C;IAnB7EZ,aAAA;IACAA,aAAA;IAOAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAOE,IACE,CAACW,WAAA,IACD,CAACC,oBAAA,IACD,CAACA,oBAAA,CAAqBC,eAAA,IACtB,CAACD,oBAAA,CAAqBE,cAAA,IACtBtC,MAAA,CAAOC,IAAA,CAAKmC,oBAAA,CAAqBE,cAAc,EAAEC,MAAA,KAAW,GAC5D;MACM,UAAI/D,KAAA,CAAM,4BAA4B;IAC9C;IAEA,KAAKgE,EAAA,GAAKL,WAAA;IACV,KAAKM,IAAA,GAAOL,oBAAA,CAAqBK,IAAA;IACjC,KAAKC,YAAA,GAAeN,oBAAA,CAAqBM,YAAA;IACzC,KAAKC,kBAAA,GAAqBP,oBAAA,CAAqBO,kBAAA;IAG/C,KAAKN,eAAA,GAAkB;IACvBrC,MAAA,CAAOC,IAAA,CAAKmC,oBAAA,CAAqBC,eAAe,EAAEO,OAAA,CAASC,YAAA,IAAiB;MAC1E,MAAMC,cAAA,GAAiB,IAAIzB,cAAA,CAAee,oBAAA,CAAqBC,eAAA,CAAgBQ,YAAY,CAAC;MACvF,KAAAR,eAAA,CAAgBQ,YAAY,IAAIC,cAAA;IAAA,CACtC;IAGD,KAAKR,cAAA,GAAiBtC,MAAA,CAAO+C,MAAA,CAAO,IAAIX,oBAAA,CAAqBE,cAAc;IAE3E,KAAKU,MAAA,GAAS;MACZzC,KAAA,EAAO5D,yBAAA,CAA0BK,cAAA,CAAeC,OAAA;MAChDqD,MAAA,EAAQ,KAAKgC,cAAA,CAAehC,MAAA,KAAW,SAAY,IAAI;MACvDF,KAAA,EAAO,KAAKkC,cAAA,CAAelC,KAAA,KAAU,SAAY,IAAI;MACrDC,KAAA,EAAO,KAAKiC,cAAA,CAAejC,KAAA,KAAU,SAAY,IAAI;IAAA;EAEzD;EAEA,IAAI4C,KAAA,EAAsD;IACxD,MAAMA,IAAA,GAAO;MAAET,EAAA,EAAI,KAAKA,EAAA;MAAI,GAAG,KAAKQ;IAAA;IAC7B,OAAAC,IAAA;EACT;EAAA;AAAA;AAAA;AAAA;EAMAC,kBAAkBC,OAAA,EAAwB;IAEnC,KAAAH,MAAA,CAAOzC,KAAA,GAAQ5D,yBAAA,CAA0BK,cAAA,CAAeC,OAAA;IAGzD,SAAKqF,cAAA,CAAehC,MAAA,KAAW,UAAa6C,OAAA,CAAQC,OAAA,CAAQb,MAAA,GAAS,KAAKD,cAAA,CAAehC,MAAA,EAAQ;MACnG,MAAM+C,aAAA,GAAgBF,OAAA,CAAQC,OAAA,CAAQ,KAAKd,cAAA,CAAehC,MAAM;MAC3D,KAAA0C,MAAA,CAAO1C,MAAA,GAAS+C,aAAA,CAActB,KAAA;MAC9B,KAAAiB,MAAA,CAAO1C,MAAA,GAAS,KAAK0C,MAAA,CAAO1C,MAAA,GAAU,IAAI,IAAI,KAAK0C,MAAA,CAAO1C,MAAA;MAC1D,KAAA0C,MAAA,CAAO1C,MAAA,GAAS,KAAK0C,MAAA,CAAO1C,MAAA,GAAU,IAAI,IAAI,KAAK0C,MAAA,CAAO1C,MAAA;MAG/D,IAAI+C,aAAA,CAAcC,OAAA,IAAW,KAAKN,MAAA,CAAO1C,MAAA,KAAW,GAAG;QAChD,KAAA0C,MAAA,CAAOzC,KAAA,GAAQ5D,yBAAA,CAA0BK,cAAA,CAAeG,OAAA;MAAA,WACpDkG,aAAA,CAAcE,OAAA,IAAW,KAAKP,MAAA,CAAO1C,MAAA,GAAU3D,yBAAA,CAA0BmB,oBAAA,EAAsB;QACnG,KAAAkF,MAAA,CAAOzC,KAAA,GAAQ5D,yBAAA,CAA0BK,cAAA,CAAeE,OAAA;MAC/D;IACF;IAGI,SAAKoF,cAAA,CAAelC,KAAA,KAAU,UAAa+C,OAAA,CAAQK,IAAA,CAAKjB,MAAA,GAAS,KAAKD,cAAA,CAAelC,KAAA,EAAO;MAC9F,KAAK4C,MAAA,CAAO5C,KAAA,GAAQ+C,OAAA,CAAQK,IAAA,CAAK,KAAKlB,cAAA,CAAelC,KAAK;MACrD,KAAA4C,MAAA,CAAO5C,KAAA,GAAQ,KAAK4C,MAAA,CAAO5C,KAAA,GAAS,KAAK,KAAK,KAAK4C,MAAA,CAAO5C,KAAA;MAC1D,KAAA4C,MAAA,CAAO5C,KAAA,GAAQ,KAAK4C,MAAA,CAAO5C,KAAA,GAAS,IAAI,IAAI,KAAK4C,MAAA,CAAO5C,KAAA;MAG7D,IACE,KAAK4C,MAAA,CAAOzC,KAAA,KAAU5D,yBAAA,CAA0BK,cAAA,CAAeC,OAAA,IAC/D2D,IAAA,CAAK6C,GAAA,CAAI,KAAKT,MAAA,CAAO5C,KAAM,IAAIzD,yBAAA,CAA0BoB,kBAAA,EACzD;QACK,KAAAiF,MAAA,CAAOzC,KAAA,GAAQ5D,yBAAA,CAA0BK,cAAA,CAAeE,OAAA;MAC/D;IACF;IAGI,SAAKoF,cAAA,CAAejC,KAAA,KAAU,UAAa8C,OAAA,CAAQK,IAAA,CAAKjB,MAAA,GAAS,KAAKD,cAAA,CAAejC,KAAA,EAAO;MAC9F,KAAK2C,MAAA,CAAO3C,KAAA,GAAQ8C,OAAA,CAAQK,IAAA,CAAK,KAAKlB,cAAA,CAAejC,KAAK;MACrD,KAAA2C,MAAA,CAAO3C,KAAA,GAAQ,KAAK2C,MAAA,CAAO3C,KAAA,GAAS,KAAK,KAAK,KAAK2C,MAAA,CAAO3C,KAAA;MAC1D,KAAA2C,MAAA,CAAO3C,KAAA,GAAQ,KAAK2C,MAAA,CAAO3C,KAAA,GAAS,IAAI,IAAI,KAAK2C,MAAA,CAAO3C,KAAA;MAG7D,IACE,KAAK2C,MAAA,CAAOzC,KAAA,KAAU5D,yBAAA,CAA0BK,cAAA,CAAeC,OAAA,IAC/D2D,IAAA,CAAK6C,GAAA,CAAI,KAAKT,MAAA,CAAO3C,KAAM,IAAI1D,yBAAA,CAA0BoB,kBAAA,EACzD;QACK,KAAAiF,MAAA,CAAOzC,KAAA,GAAQ5D,yBAAA,CAA0BK,cAAA,CAAeE,OAAA;MAC/D;IACF;IAGA8C,MAAA,CAAOgD,MAAA,CAAO,KAAKX,eAAe,EAAEO,OAAA,CAASE,cAAA,IAAmB;MAC/CA,cAAA,CAAAd,mBAAA,CAAoB,KAAKgB,MAAM;IAAA,CAC/C;EACH;AACF;AAMA,MAAMU,gBAAA,CAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAWrBpC,YAAYtC,aAAA,EAA8BW,OAAA,EAAkBgE,QAAA,EAAkB;IAV9EnC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAOE,IAAI,CAACxC,aAAA,EAAe;MACZ,UAAIR,KAAA,CAAM,2BAA2B;IAC7C;IAEA,IAAI,CAACmB,OAAA,EAAS;MACN,UAAInB,KAAA,CAAM,qBAAqB;IACvC;IAEA,IAAI,CAACmB,OAAA,CAAQI,OAAA,CAAQf,aAAA,CAAcc,UAAU,GAAG;MAC9C,MAAM,IAAItB,KAAA,CAAM,mBAAmBQ,aAAA,CAAcc,UAAA,GAAa,aAAa;IAC7E;IAEA,KAAKd,aAAA,GAAgBA,aAAA;IACrB,KAAK2E,QAAA,GAAWA,QAAA;IAChB,KAAKnB,EAAA,GAAK7C,OAAA,CAAQJ,SAAA;IAGlB,KAAKqE,iBAAA,GAAoBjE,OAAA,CAAQI,OAAA,CAAQf,aAAA,CAAcc,UAAU;IAEjE,KAAK+D,UAAA,GAAa;IAClB7D,MAAA,CAAOC,IAAA,CAAK,KAAK2D,iBAAA,CAAkBC,UAAU,EAAEjB,OAAA,CAAST,WAAA,IAAgB;MACtE,MAAMC,oBAAA,GAAuB,KAAKwB,iBAAA,CAAkBC,UAAA,CAAW1B,WAAW;MAC1E,KAAK0B,UAAA,CAAW1B,WAAW,IAAI,IAAID,SAAA,CAAUC,WAAA,EAAaC,oBAAoB;IAAA,CAC/E;IAGD,KAAKc,iBAAA,CAAkB;EACzB;EAEA,IAAIY,UAAA,EAAwC;IAC1C,OAAO,KAAK9E,aAAA,CAAc8E,SAAA;EAC5B;EAEA,IAAIC,eAAA,EAAkD;IACpD,OAAO,KAAK/E,aAAA,CAAc+E,cAAA;EAC5B;EAAA;AAAA;AAAA;EAKA,IAAId,KAAA,EAAiC;IACnC,MAAMA,IAAA,GAAiC;IACvCjD,MAAA,CAAOgD,MAAA,CAAO,KAAKa,UAAU,EAAEjB,OAAA,CAASoB,SAAA,IAAc;MAC/Cf,IAAA,CAAAgB,IAAA,CAAKD,SAAA,CAAUf,IAAI;IAAA,CACzB;IACM,OAAAA,IAAA;EACT;EAAA;AAAA;AAAA;EAKAC,kBAAA,EAA0B;IACxBlD,MAAA,CAAOgD,MAAA,CAAO,KAAKa,UAAU,EAAEjB,OAAA,CAASoB,SAAA,IAAc;MAC1CA,SAAA,CAAAd,iBAAA,CAAkB,KAAKlE,aAAA,CAAcmE,OAAQ;IAAA,CACxD;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}