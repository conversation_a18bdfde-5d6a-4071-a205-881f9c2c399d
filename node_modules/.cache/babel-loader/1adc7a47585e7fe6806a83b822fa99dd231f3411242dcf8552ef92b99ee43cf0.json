{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oader } from \"three\";\nimport { parse as parseBuffer } from \"../libs/opentype.js\";\nclass TTFLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.reversed = false;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(parseBuffer(buffer));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(arraybuffer) {\n    function convert(font, reversed) {\n      const round = Math.round;\n      const glyphs = {};\n      const scale = 1e5 / ((font.unitsPerEm || 2048) * 72);\n      const glyphIndexMap = font.encoding.cmap.glyphIndexMap;\n      const unicodes = Object.keys(glyphIndexMap);\n      for (let i = 0; i < unicodes.length; i++) {\n        const unicode = unicodes[i];\n        const glyph = font.glyphs.glyphs[glyphIndexMap[unicode]];\n        if (unicode !== void 0) {\n          const token = {\n            ha: round(glyph.advanceWidth * scale),\n            x_min: round(glyph.xMin * scale),\n            x_max: round(glyph.xMax * scale),\n            o: \"\"\n          };\n          if (reversed) {\n            glyph.path.commands = reverseCommands(glyph.path.commands);\n          }\n          glyph.path.commands.forEach(function (command) {\n            if (command.type.toLowerCase() === \"c\") {\n              command.type = \"b\";\n            }\n            token.o += command.type.toLowerCase() + \" \";\n            if (command.x !== void 0 && command.y !== void 0) {\n              token.o += round(command.x * scale) + \" \" + round(command.y * scale) + \" \";\n            }\n            if (command.x1 !== void 0 && command.y1 !== void 0) {\n              token.o += round(command.x1 * scale) + \" \" + round(command.y1 * scale) + \" \";\n            }\n            if (command.x2 !== void 0 && command.y2 !== void 0) {\n              token.o += round(command.x2 * scale) + \" \" + round(command.y2 * scale) + \" \";\n            }\n          });\n          glyphs[String.fromCodePoint(glyph.unicode)] = token;\n        }\n      }\n      return {\n        glyphs,\n        familyName: font.getEnglishName(\"fullName\"),\n        ascender: round(font.ascender * scale),\n        descender: round(font.descender * scale),\n        underlinePosition: font.tables.post.underlinePosition,\n        underlineThickness: font.tables.post.underlineThickness,\n        boundingBox: {\n          xMin: font.tables.head.xMin,\n          xMax: font.tables.head.xMax,\n          yMin: font.tables.head.yMin,\n          yMax: font.tables.head.yMax\n        },\n        resolution: 1e3,\n        original_font_information: font.tables.name\n      };\n    }\n    function reverseCommands(commands) {\n      const paths = [];\n      let path;\n      commands.forEach(function (c) {\n        if (c.type.toLowerCase() === \"m\") {\n          path = [c];\n          paths.push(path);\n        } else if (c.type.toLowerCase() !== \"z\") {\n          path.push(c);\n        }\n      });\n      const reversed = [];\n      paths.forEach(function (p) {\n        const result = {\n          type: \"m\",\n          x: p[p.length - 1].x,\n          y: p[p.length - 1].y\n        };\n        reversed.push(result);\n        for (let i = p.length - 1; i > 0; i--) {\n          const command = p[i];\n          const result2 = {\n            type: command.type\n          };\n          if (command.x2 !== void 0 && command.y2 !== void 0) {\n            result2.x1 = command.x2;\n            result2.y1 = command.y2;\n            result2.x2 = command.x1;\n            result2.y2 = command.y1;\n          } else if (command.x1 !== void 0 && command.y1 !== void 0) {\n            result2.x1 = command.x1;\n            result2.y1 = command.y1;\n          }\n          result2.x = p[i - 1].x;\n          result2.y = p[i - 1].y;\n          reversed.push(result2);\n        }\n      });\n      return reversed;\n    }\n    return convert(parseBuffer(arraybuffer), this.reversed);\n  }\n}\nexport { TTFLoader };", "map": {"version": 3, "names": ["TTFLoader", "Loader", "constructor", "manager", "reversed", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parse<PERSON><PERSON>er", "e", "console", "error", "itemError", "parse", "arraybuffer", "convert", "font", "round", "Math", "glyphs", "scale", "unitsPerEm", "glyphIndexMap", "encoding", "cmap", "unicodes", "Object", "keys", "i", "length", "unicode", "glyph", "token", "ha", "advanceWidth", "x_min", "xMin", "x_max", "xMax", "o", "commands", "reverseCommands", "for<PERSON>ach", "command", "type", "toLowerCase", "x", "y", "x1", "y1", "x2", "y2", "String", "fromCodePoint", "<PERSON><PERSON>ame", "getEnglishName", "ascender", "descender", "underlinePosition", "tables", "post", "underlineThickness", "boundingBox", "head", "yMin", "yMax", "resolution", "original_font_information", "name", "paths", "c", "push", "p", "result", "result2"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/TTFLoader.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, Lo<PERSON> } from 'three'\nimport { parse } from '../libs/opentype.js'\n\n/**\n * Requires opentype.js to be included in the project.\n * Loads TTF files and converts them into typeface JSON that can be used directly\n * to create THREE.Font objects.\n */\n\nclass TTFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.reversed = false\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(arraybuffer) {\n    function convert(font, reversed) {\n      const round = Math.round\n\n      const glyphs = {}\n      const scale = 100000 / ((font.unitsPerEm || 2048) * 72)\n\n      const glyphIndexMap = font.encoding.cmap.glyphIndexMap\n      const unicodes = Object.keys(glyphIndexMap)\n\n      for (let i = 0; i < unicodes.length; i++) {\n        const unicode = unicodes[i]\n        const glyph = font.glyphs.glyphs[glyphIndexMap[unicode]]\n\n        if (unicode !== undefined) {\n          const token = {\n            ha: round(glyph.advanceWidth * scale),\n            x_min: round(glyph.xMin * scale),\n            x_max: round(glyph.xMax * scale),\n            o: '',\n          }\n\n          if (reversed) {\n            glyph.path.commands = reverseCommands(glyph.path.commands)\n          }\n\n          glyph.path.commands.forEach(function (command) {\n            if (command.type.toLowerCase() === 'c') {\n              command.type = 'b'\n            }\n\n            token.o += command.type.toLowerCase() + ' '\n\n            if (command.x !== undefined && command.y !== undefined) {\n              token.o += round(command.x * scale) + ' ' + round(command.y * scale) + ' '\n            }\n\n            if (command.x1 !== undefined && command.y1 !== undefined) {\n              token.o += round(command.x1 * scale) + ' ' + round(command.y1 * scale) + ' '\n            }\n\n            if (command.x2 !== undefined && command.y2 !== undefined) {\n              token.o += round(command.x2 * scale) + ' ' + round(command.y2 * scale) + ' '\n            }\n          })\n\n          glyphs[String.fromCodePoint(glyph.unicode)] = token\n        }\n      }\n\n      return {\n        glyphs: glyphs,\n        familyName: font.getEnglishName('fullName'),\n        ascender: round(font.ascender * scale),\n        descender: round(font.descender * scale),\n        underlinePosition: font.tables.post.underlinePosition,\n        underlineThickness: font.tables.post.underlineThickness,\n        boundingBox: {\n          xMin: font.tables.head.xMin,\n          xMax: font.tables.head.xMax,\n          yMin: font.tables.head.yMin,\n          yMax: font.tables.head.yMax,\n        },\n        resolution: 1000,\n        original_font_information: font.tables.name,\n      }\n    }\n\n    function reverseCommands(commands) {\n      const paths = []\n      let path\n\n      commands.forEach(function (c) {\n        if (c.type.toLowerCase() === 'm') {\n          path = [c]\n          paths.push(path)\n        } else if (c.type.toLowerCase() !== 'z') {\n          path.push(c)\n        }\n      })\n\n      const reversed = []\n\n      paths.forEach(function (p) {\n        const result = {\n          type: 'm',\n          x: p[p.length - 1].x,\n          y: p[p.length - 1].y,\n        }\n\n        reversed.push(result)\n\n        for (let i = p.length - 1; i > 0; i--) {\n          const command = p[i]\n          const result = { type: command.type }\n\n          if (command.x2 !== undefined && command.y2 !== undefined) {\n            result.x1 = command.x2\n            result.y1 = command.y2\n            result.x2 = command.x1\n            result.y2 = command.y1\n          } else if (command.x1 !== undefined && command.y1 !== undefined) {\n            result.x1 = command.x1\n            result.y1 = command.y1\n          }\n\n          result.x = p[i - 1].x\n          result.y = p[i - 1].y\n          reversed.push(result)\n        }\n      })\n\n      return reversed\n    }\n\n    return convert(parse(arraybuffer), this.reversed)\n  }\n}\n\nexport { TTFLoader }\n"], "mappings": ";;AASA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,QAAA,GAAW;EACjB;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKT,OAAO;IAC1CQ,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;IACxBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiB,KAAKC,aAAa;IAC1CN,MAAA,CAAOO,kBAAA,CAAmB,KAAKC,eAAe;IAC9CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,MAAA,EAAQ;MAChB,IAAI;QACFb,MAAA,CAAOc,WAAA,CAAMD,MAAM,CAAC;MACrB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMP,OAAA,CAAQsB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDiB,MAAMC,WAAA,EAAa;IACjB,SAASC,QAAQC,IAAA,EAAMzB,QAAA,EAAU;MAC/B,MAAM0B,KAAA,GAAQC,IAAA,CAAKD,KAAA;MAEnB,MAAME,MAAA,GAAS,CAAE;MACjB,MAAMC,KAAA,GAAQ,QAAWJ,IAAA,CAAKK,UAAA,IAAc,QAAQ;MAEpD,MAAMC,aAAA,GAAgBN,IAAA,CAAKO,QAAA,CAASC,IAAA,CAAKF,aAAA;MACzC,MAAMG,QAAA,GAAWC,MAAA,CAAOC,IAAA,CAAKL,aAAa;MAE1C,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAIH,QAAA,CAASI,MAAA,EAAQD,CAAA,IAAK;QACxC,MAAME,OAAA,GAAUL,QAAA,CAASG,CAAC;QAC1B,MAAMG,KAAA,GAAQf,IAAA,CAAKG,MAAA,CAAOA,MAAA,CAAOG,aAAA,CAAcQ,OAAO,CAAC;QAEvD,IAAIA,OAAA,KAAY,QAAW;UACzB,MAAME,KAAA,GAAQ;YACZC,EAAA,EAAIhB,KAAA,CAAMc,KAAA,CAAMG,YAAA,GAAed,KAAK;YACpCe,KAAA,EAAOlB,KAAA,CAAMc,KAAA,CAAMK,IAAA,GAAOhB,KAAK;YAC/BiB,KAAA,EAAOpB,KAAA,CAAMc,KAAA,CAAMO,IAAA,GAAOlB,KAAK;YAC/BmB,CAAA,EAAG;UACJ;UAED,IAAIhD,QAAA,EAAU;YACZwC,KAAA,CAAM9B,IAAA,CAAKuC,QAAA,GAAWC,eAAA,CAAgBV,KAAA,CAAM9B,IAAA,CAAKuC,QAAQ;UAC1D;UAEDT,KAAA,CAAM9B,IAAA,CAAKuC,QAAA,CAASE,OAAA,CAAQ,UAAUC,OAAA,EAAS;YAC7C,IAAIA,OAAA,CAAQC,IAAA,CAAKC,WAAA,CAAW,MAAO,KAAK;cACtCF,OAAA,CAAQC,IAAA,GAAO;YAChB;YAEDZ,KAAA,CAAMO,CAAA,IAAKI,OAAA,CAAQC,IAAA,CAAKC,WAAA,CAAa,IAAG;YAExC,IAAIF,OAAA,CAAQG,CAAA,KAAM,UAAaH,OAAA,CAAQI,CAAA,KAAM,QAAW;cACtDf,KAAA,CAAMO,CAAA,IAAKtB,KAAA,CAAM0B,OAAA,CAAQG,CAAA,GAAI1B,KAAK,IAAI,MAAMH,KAAA,CAAM0B,OAAA,CAAQI,CAAA,GAAI3B,KAAK,IAAI;YACxE;YAED,IAAIuB,OAAA,CAAQK,EAAA,KAAO,UAAaL,OAAA,CAAQM,EAAA,KAAO,QAAW;cACxDjB,KAAA,CAAMO,CAAA,IAAKtB,KAAA,CAAM0B,OAAA,CAAQK,EAAA,GAAK5B,KAAK,IAAI,MAAMH,KAAA,CAAM0B,OAAA,CAAQM,EAAA,GAAK7B,KAAK,IAAI;YAC1E;YAED,IAAIuB,OAAA,CAAQO,EAAA,KAAO,UAAaP,OAAA,CAAQQ,EAAA,KAAO,QAAW;cACxDnB,KAAA,CAAMO,CAAA,IAAKtB,KAAA,CAAM0B,OAAA,CAAQO,EAAA,GAAK9B,KAAK,IAAI,MAAMH,KAAA,CAAM0B,OAAA,CAAQQ,EAAA,GAAK/B,KAAK,IAAI;YAC1E;UACb,CAAW;UAEDD,MAAA,CAAOiC,MAAA,CAAOC,aAAA,CAActB,KAAA,CAAMD,OAAO,CAAC,IAAIE,KAAA;QAC/C;MACF;MAED,OAAO;QACLb,MAAA;QACAmC,UAAA,EAAYtC,IAAA,CAAKuC,cAAA,CAAe,UAAU;QAC1CC,QAAA,EAAUvC,KAAA,CAAMD,IAAA,CAAKwC,QAAA,GAAWpC,KAAK;QACrCqC,SAAA,EAAWxC,KAAA,CAAMD,IAAA,CAAKyC,SAAA,GAAYrC,KAAK;QACvCsC,iBAAA,EAAmB1C,IAAA,CAAK2C,MAAA,CAAOC,IAAA,CAAKF,iBAAA;QACpCG,kBAAA,EAAoB7C,IAAA,CAAK2C,MAAA,CAAOC,IAAA,CAAKC,kBAAA;QACrCC,WAAA,EAAa;UACX1B,IAAA,EAAMpB,IAAA,CAAK2C,MAAA,CAAOI,IAAA,CAAK3B,IAAA;UACvBE,IAAA,EAAMtB,IAAA,CAAK2C,MAAA,CAAOI,IAAA,CAAKzB,IAAA;UACvB0B,IAAA,EAAMhD,IAAA,CAAK2C,MAAA,CAAOI,IAAA,CAAKC,IAAA;UACvBC,IAAA,EAAMjD,IAAA,CAAK2C,MAAA,CAAOI,IAAA,CAAKE;QACxB;QACDC,UAAA,EAAY;QACZC,yBAAA,EAA2BnD,IAAA,CAAK2C,MAAA,CAAOS;MACxC;IACF;IAED,SAAS3B,gBAAgBD,QAAA,EAAU;MACjC,MAAM6B,KAAA,GAAQ,EAAE;MAChB,IAAIpE,IAAA;MAEJuC,QAAA,CAASE,OAAA,CAAQ,UAAU4B,CAAA,EAAG;QAC5B,IAAIA,CAAA,CAAE1B,IAAA,CAAKC,WAAA,CAAW,MAAO,KAAK;UAChC5C,IAAA,GAAO,CAACqE,CAAC;UACTD,KAAA,CAAME,IAAA,CAAKtE,IAAI;QAChB,WAAUqE,CAAA,CAAE1B,IAAA,CAAKC,WAAA,CAAW,MAAO,KAAK;UACvC5C,IAAA,CAAKsE,IAAA,CAAKD,CAAC;QACZ;MACT,CAAO;MAED,MAAM/E,QAAA,GAAW,EAAE;MAEnB8E,KAAA,CAAM3B,OAAA,CAAQ,UAAU8B,CAAA,EAAG;QACzB,MAAMC,MAAA,GAAS;UACb7B,IAAA,EAAM;UACNE,CAAA,EAAG0B,CAAA,CAAEA,CAAA,CAAE3C,MAAA,GAAS,CAAC,EAAEiB,CAAA;UACnBC,CAAA,EAAGyB,CAAA,CAAEA,CAAA,CAAE3C,MAAA,GAAS,CAAC,EAAEkB;QACpB;QAEDxD,QAAA,CAASgF,IAAA,CAAKE,MAAM;QAEpB,SAAS7C,CAAA,GAAI4C,CAAA,CAAE3C,MAAA,GAAS,GAAGD,CAAA,GAAI,GAAGA,CAAA,IAAK;UACrC,MAAMe,OAAA,GAAU6B,CAAA,CAAE5C,CAAC;UACnB,MAAM8C,OAAA,GAAS;YAAE9B,IAAA,EAAMD,OAAA,CAAQC;UAAM;UAErC,IAAID,OAAA,CAAQO,EAAA,KAAO,UAAaP,OAAA,CAAQQ,EAAA,KAAO,QAAW;YACxDuB,OAAA,CAAO1B,EAAA,GAAKL,OAAA,CAAQO,EAAA;YACpBwB,OAAA,CAAOzB,EAAA,GAAKN,OAAA,CAAQQ,EAAA;YACpBuB,OAAA,CAAOxB,EAAA,GAAKP,OAAA,CAAQK,EAAA;YACpB0B,OAAA,CAAOvB,EAAA,GAAKR,OAAA,CAAQM,EAAA;UAChC,WAAqBN,OAAA,CAAQK,EAAA,KAAO,UAAaL,OAAA,CAAQM,EAAA,KAAO,QAAW;YAC/DyB,OAAA,CAAO1B,EAAA,GAAKL,OAAA,CAAQK,EAAA;YACpB0B,OAAA,CAAOzB,EAAA,GAAKN,OAAA,CAAQM,EAAA;UACrB;UAEDyB,OAAA,CAAO5B,CAAA,GAAI0B,CAAA,CAAE5C,CAAA,GAAI,CAAC,EAAEkB,CAAA;UACpB4B,OAAA,CAAO3B,CAAA,GAAIyB,CAAA,CAAE5C,CAAA,GAAI,CAAC,EAAEmB,CAAA;UACpBxD,QAAA,CAASgF,IAAA,CAAKG,OAAM;QACrB;MACT,CAAO;MAED,OAAOnF,QAAA;IACR;IAED,OAAOwB,OAAA,CAAQP,WAAA,CAAMM,WAAW,GAAG,KAAKvB,QAAQ;EACjD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}