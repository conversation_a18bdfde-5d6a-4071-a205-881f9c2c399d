{"ast": null, "code": "import * as THREE from 'three';\nimport { WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport * as React from 'react';\nimport { useMemo, useEffect } from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nfunction useCubeCamera({\n  resolution = 256,\n  near = 0.1,\n  far = 1000,\n  envMap,\n  fog\n} = {}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  const fbo = useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  const camera = useMemo(() => new THREE.CubeCamera(near, far, fbo), [near, far, fbo]);\n  let originalFog;\n  let originalBackground;\n  const update = React.useCallback(() => {\n    originalFog = scene.fog;\n    originalBackground = scene.background;\n    scene.background = envMap || originalBackground;\n    scene.fog = fog || originalFog;\n    camera.update(gl, scene);\n    scene.fog = originalFog;\n    scene.background = originalBackground;\n  }, [gl, scene, camera]);\n  return {\n    fbo,\n    camera,\n    update\n  };\n}\nfunction CubeCamera({\n  children,\n  frames = Infinity,\n  resolution,\n  near,\n  far,\n  envMap,\n  fog,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  const {\n    fbo,\n    camera,\n    update\n  } = useCubeCamera({\n    resolution,\n    near,\n    far,\n    envMap,\n    fog\n  });\n  let count = 0;\n  useFrame(() => {\n    if (ref.current && (frames === Infinity || count < frames)) {\n      ref.current.visible = false;\n      update();\n      ref.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: camera\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children == null ? void 0 : children(fbo.texture)));\n}\nexport { CubeCamera, useCubeCamera };", "map": {"version": 3, "names": ["THREE", "WebGLCubeRenderTarget", "HalfFloatType", "React", "useMemo", "useEffect", "useThree", "useFrame", "useCubeCamera", "resolution", "near", "far", "envMap", "fog", "gl", "scene", "fbo", "texture", "type", "dispose", "camera", "CubeCamera", "originalFog", "originalBackground", "update", "useCallback", "background", "children", "frames", "Infinity", "props", "ref", "useRef", "count", "current", "visible", "createElement", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/CubeCamera.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport * as React from 'react';\nimport { useMemo, useEffect } from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nfunction useCubeCamera({\n  resolution = 256,\n  near = 0.1,\n  far = 1000,\n  envMap,\n  fog\n} = {}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  const fbo = useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  const camera = useMemo(() => new THREE.CubeCamera(near, far, fbo), [near, far, fbo]);\n  let originalFog;\n  let originalBackground;\n  const update = React.useCallback(() => {\n    originalFog = scene.fog;\n    originalBackground = scene.background;\n    scene.background = envMap || originalBackground;\n    scene.fog = fog || originalFog;\n    camera.update(gl, scene);\n    scene.fog = originalFog;\n    scene.background = originalBackground;\n  }, [gl, scene, camera]);\n  return {\n    fbo,\n    camera,\n    update\n  };\n}\nfunction CubeCamera({\n  children,\n  frames = Infinity,\n  resolution,\n  near,\n  far,\n  envMap,\n  fog,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  const {\n    fbo,\n    camera,\n    update\n  } = useCubeCamera({\n    resolution,\n    near,\n    far,\n    envMap,\n    fog\n  });\n  let count = 0;\n  useFrame(() => {\n    if (ref.current && (frames === Infinity || count < frames)) {\n      ref.current.visible = false;\n      update();\n      ref.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: camera\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children == null ? void 0 : children(fbo.texture)));\n}\n\nexport { CubeCamera, useCubeCamera };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,OAAO;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AAC1C,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,SAASC,aAAaA,CAAC;EACrBC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,IAAI;EACVC,MAAM;EACNC;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,EAAE,GAAGR,QAAQ,CAAC,CAAC;IACnBQ;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;IACtBS;EACF,CAAC,KAAKA,KAAK,CAAC;EACZ,MAAMC,GAAG,GAAGZ,OAAO,CAAC,MAAM;IACxB,MAAMY,GAAG,GAAG,IAAIf,qBAAqB,CAACQ,UAAU,CAAC;IACjDO,GAAG,CAACC,OAAO,CAACC,IAAI,GAAGhB,aAAa;IAChC,OAAOc,GAAG;EACZ,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAChBJ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXW,GAAG,CAACG,OAAO,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACH,GAAG,CAAC,CAAC;EACT,MAAMI,MAAM,GAAGhB,OAAO,CAAC,MAAM,IAAIJ,KAAK,CAACqB,UAAU,CAACX,IAAI,EAAEC,GAAG,EAAEK,GAAG,CAAC,EAAE,CAACN,IAAI,EAAEC,GAAG,EAAEK,GAAG,CAAC,CAAC;EACpF,IAAIM,WAAW;EACf,IAAIC,kBAAkB;EACtB,MAAMC,MAAM,GAAGrB,KAAK,CAACsB,WAAW,CAAC,MAAM;IACrCH,WAAW,GAAGP,KAAK,CAACF,GAAG;IACvBU,kBAAkB,GAAGR,KAAK,CAACW,UAAU;IACrCX,KAAK,CAACW,UAAU,GAAGd,MAAM,IAAIW,kBAAkB;IAC/CR,KAAK,CAACF,GAAG,GAAGA,GAAG,IAAIS,WAAW;IAC9BF,MAAM,CAACI,MAAM,CAACV,EAAE,EAAEC,KAAK,CAAC;IACxBA,KAAK,CAACF,GAAG,GAAGS,WAAW;IACvBP,KAAK,CAACW,UAAU,GAAGH,kBAAkB;EACvC,CAAC,EAAE,CAACT,EAAE,EAAEC,KAAK,EAAEK,MAAM,CAAC,CAAC;EACvB,OAAO;IACLJ,GAAG;IACHI,MAAM;IACNI;EACF,CAAC;AACH;AACA,SAASH,UAAUA,CAAC;EAClBM,QAAQ;EACRC,MAAM,GAAGC,QAAQ;EACjBpB,UAAU;EACVC,IAAI;EACJC,GAAG;EACHC,MAAM;EACNC,GAAG;EACH,GAAGiB;AACL,CAAC,EAAE;EACD,MAAMC,GAAG,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM;IACJhB,GAAG;IACHI,MAAM;IACNI;EACF,CAAC,GAAGhB,aAAa,CAAC;IAChBC,UAAU;IACVC,IAAI;IACJC,GAAG;IACHC,MAAM;IACNC;EACF,CAAC,CAAC;EACF,IAAIoB,KAAK,GAAG,CAAC;EACb1B,QAAQ,CAAC,MAAM;IACb,IAAIwB,GAAG,CAACG,OAAO,KAAKN,MAAM,KAAKC,QAAQ,IAAII,KAAK,GAAGL,MAAM,CAAC,EAAE;MAC1DG,GAAG,CAACG,OAAO,CAACC,OAAO,GAAG,KAAK;MAC3BX,MAAM,CAAC,CAAC;MACRO,GAAG,CAACG,OAAO,CAACC,OAAO,GAAG,IAAI;MAC1BF,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAa9B,KAAK,CAACiC,aAAa,CAAC,OAAO,EAAEN,KAAK,EAAE,aAAa3B,KAAK,CAACiC,aAAa,CAAC,WAAW,EAAE;IACpGC,MAAM,EAAEjB;EACV,CAAC,CAAC,EAAE,aAAajB,KAAK,CAACiC,aAAa,CAAC,OAAO,EAAE;IAC5CL,GAAG,EAAEA;EACP,CAAC,EAAEJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACX,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;AACxD;AAEA,SAASI,UAAU,EAAEb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}