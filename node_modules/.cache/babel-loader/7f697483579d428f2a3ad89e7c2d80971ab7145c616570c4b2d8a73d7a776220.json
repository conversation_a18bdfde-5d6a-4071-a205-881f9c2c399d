{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Header.tsx\";\nimport { GradientBg, InitialAppearance } from \"../../components/Animation\";\nimport { TitleText } from \"../../components/common/Text\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeHeader = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"relative w-full min-h-screen flex items-center justify-center font-bold text-white\",\n    children: [/*#__PURE__*/_jsxDEV(GradientBg, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InitialAppearance, {\n      className: \"w-full max-w-7xl mx-auto px-8 z-10\",\n      time: 2,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row items-center justify-between gap-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/2 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-400 text-2xl\",\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TitleText, {\n              className: \"leading-tight mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"white-text-shadow text-[48px] sm:text-[64px] lg:text-[80px] xl:text-[96px] font-bold block\",\n                children: \"Full Stack\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 16,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-neutral-200 text-[24px] sm:text-[32px] lg:text-[40px] font-normal block mt-2\",\n                children: \"Building digital experiences that blend design and technology\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#projects\",\n              className: \"inline-flex items-center px-6 py-3 bg-transparent border border-gray-400 rounded-lg text-white font-medium hover:bg-gray-800 transition-colors\",\n              children: \"View Projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/2 flex justify-center lg:justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-gradient-to-r from-blue-400 via-purple-500 to-pink-500 p-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full rounded-full overflow-hidden bg-black\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/assets/images/id-photo.jpg\",\n                  alt: \"Do Quoc Dat\",\n                  className: \"w-full h-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-blue-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = HomeHeader;\nexport default HomeHeader;\nvar _c;\n$RefreshReg$(_c, \"HomeHeader\");", "map": {"version": 3, "names": ["GradientBg", "InitialAppearance", "TitleText", "jsxDEV", "_jsxDEV", "HomeHeader", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "time", "href", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Header.tsx"], "sourcesContent": ["import { GradientBg, InitialAppearance } from \"../../components/Animation\";\nimport { NormalText, TitleText } from \"../../components/common/Text\";\nimport { ProfilePhoto } from \"../../components/common/Image\";\n\nconst HomeHeader = () => {\n  return (\n    <section id=\"home\" className=\"relative w-full min-h-screen flex items-center justify-center font-bold text-white\">\n      <GradientBg />\n      <InitialAppearance className=\"w-full max-w-7xl mx-auto px-8 z-10\" time={2}>\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n          {/* Left side - Content */}\n          <div className=\"lg:w-1/2 text-left\">\n            <div className=\"mb-8\">\n              <span className=\"text-yellow-400 text-2xl\">⭐</span>\n              <TitleText className=\"leading-tight mt-4\">\n                <span className=\"white-text-shadow text-[48px] sm:text-[64px] lg:text-[80px] xl:text-[96px] font-bold block\">\n                  Full Stack\n                </span>\n                <span className=\"text-neutral-200 text-[24px] sm:text-[32px] lg:text-[40px] font-normal block mt-2\">\n                  Building digital experiences that blend design and technology\n                </span>\n              </TitleText>\n            </div>\n\n            <div className=\"mb-8\">\n              <a\n                href=\"#projects\"\n                className=\"inline-flex items-center px-6 py-3 bg-transparent border border-gray-400 rounded-lg text-white font-medium hover:bg-gray-800 transition-colors\"\n              >\n                View Projects\n              </a>\n            </div>\n          </div>\n\n          {/* Right side - Profile Photo */}\n          <div className=\"lg:w-1/2 flex justify-center lg:justify-end\">\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-gradient-to-r from-blue-400 via-purple-500 to-pink-500 p-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500\">\n                <div className=\"w-full h-full rounded-full overflow-hidden bg-black\">\n                  <img\n                    src=\"/assets/images/id-photo.jpg\"\n                    alt=\"Do Quoc Dat\"\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n              </div>\n              {/* Decorative elements */}\n              <div className=\"absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-blue-400 rounded-full\"></div>\n            </div>\n          </div>\n        </div>\n      </InitialAppearance>\n    </section>\n  );\n};\n\nexport default HomeHeader;\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC1E,SAAqBC,SAAS,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAASE,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAC/GJ,OAAA,CAACJ,UAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdR,OAAA,CAACH,iBAAiB;MAACM,SAAS,EAAC,oCAAoC;MAACM,IAAI,EAAE,CAAE;MAAAL,QAAA,eACxEJ,OAAA;QAAKG,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAE5EJ,OAAA;UAAKG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCJ,OAAA;YAAKG,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBJ,OAAA;cAAMG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDR,OAAA,CAACF,SAAS;cAACK,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACvCJ,OAAA;gBAAMG,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EAAC;cAE7G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPR,OAAA;gBAAMG,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAAC;cAEpG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENR,OAAA;YAAKG,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBJ,OAAA;cACEU,IAAI,EAAC,WAAW;cAChBP,SAAS,EAAC,gJAAgJ;cAAAC,QAAA,EAC3J;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNR,OAAA;UAAKG,SAAS,EAAC,6CAA6C;UAAAC,QAAA,eAC1DJ,OAAA;YAAKG,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBJ,OAAA;cAAKG,SAAS,EAAC,6LAA6L;cAAAC,QAAA,eAC1MJ,OAAA;gBAAKG,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,eAClEJ,OAAA;kBACEW,GAAG,EAAC,6BAA6B;kBACjCC,GAAG,EAAC,aAAa;kBACjBT,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENR,OAAA;cAAKG,SAAS,EAAC;YAA6D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFR,OAAA;cAAKG,SAAS,EAAC;YAA6D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEd,CAAC;AAACK,EAAA,GAnDIZ,UAAU;AAqDhB,eAAeA,UAAU;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}