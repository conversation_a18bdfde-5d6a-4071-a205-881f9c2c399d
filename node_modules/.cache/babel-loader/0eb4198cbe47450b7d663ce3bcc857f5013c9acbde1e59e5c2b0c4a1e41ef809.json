{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useLoader, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { version } from '../helpers/constants.js';\nconst SplatMaterial = /* @__PURE__ */shaderMaterial({\n  alphaTest: 0,\n  viewport: /* @__PURE__ */new THREE.Vector2(1980, 1080),\n  focal: 1000.0,\n  centerAndScaleTexture: null,\n  covAndColorTexture: null\n}, /*glsl*/`\n    precision highp sampler2D;\n    precision highp usampler2D;\n    out vec4 vColor;\n    out vec3 vPosition;\n    uniform vec2 resolution;\n    uniform vec2 viewport;\n    uniform float focal;\n    attribute uint splatIndex;\n    uniform sampler2D centerAndScaleTexture;\n    uniform usampler2D covAndColorTexture;    \n\n    vec2 unpackInt16(in uint value) {\n      int v = int(value);\n      int v0 = v >> 16;\n      int v1 = (v & 0xFFFF);\n      if((v & 0x8000) != 0)\n        v1 |= 0xFFFF0000;\n      return vec2(float(v1), float(v0));\n    }\n\n    void main () {\n      ivec2 texSize = textureSize(centerAndScaleTexture, 0);\n      ivec2 texPos = ivec2(splatIndex%uint(texSize.x), splatIndex/uint(texSize.x));\n      vec4 centerAndScaleData = texelFetch(centerAndScaleTexture, texPos, 0);\n      vec4 center = vec4(centerAndScaleData.xyz, 1);\n      vec4 camspace = modelViewMatrix * center;\n      vec4 pos2d = projectionMatrix * camspace;\n\n      float bounds = 1.2 * pos2d.w;\n      if (pos2d.z < -pos2d.w || pos2d.x < -bounds || pos2d.x > bounds\n        || pos2d.y < -bounds || pos2d.y > bounds) {\n        gl_Position = vec4(0.0, 0.0, 2.0, 1.0);\n        return;\n      }\n\n      uvec4 covAndColorData = texelFetch(covAndColorTexture, texPos, 0);\n      vec2 cov3D_M11_M12 = unpackInt16(covAndColorData.x) * centerAndScaleData.w;\n      vec2 cov3D_M13_M22 = unpackInt16(covAndColorData.y) * centerAndScaleData.w;\n      vec2 cov3D_M23_M33 = unpackInt16(covAndColorData.z) * centerAndScaleData.w;\n      mat3 Vrk = mat3(\n        cov3D_M11_M12.x, cov3D_M11_M12.y, cov3D_M13_M22.x,\n        cov3D_M11_M12.y, cov3D_M13_M22.y, cov3D_M23_M33.x,\n        cov3D_M13_M22.x, cov3D_M23_M33.x, cov3D_M23_M33.y\n      );\n\n      mat3 J = mat3(\n        focal / camspace.z, 0., -(focal * camspace.x) / (camspace.z * camspace.z),\n        0., focal / camspace.z, -(focal * camspace.y) / (camspace.z * camspace.z),\n        0., 0., 0.\n      );\n\n      mat3 W = transpose(mat3(modelViewMatrix));\n      mat3 T = W * J;\n      mat3 cov = transpose(T) * Vrk * T;\n      vec2 vCenter = vec2(pos2d) / pos2d.w;\n      float diagonal1 = cov[0][0] + 0.3;\n      float offDiagonal = cov[0][1];\n      float diagonal2 = cov[1][1] + 0.3;\n      float mid = 0.5 * (diagonal1 + diagonal2);\n      float radius = length(vec2((diagonal1 - diagonal2) / 2.0, offDiagonal));\n      float lambda1 = mid + radius;\n      float lambda2 = max(mid - radius, 0.1);\n      vec2 diagonalVector = normalize(vec2(offDiagonal, lambda1 - diagonal1));\n      vec2 v1 = min(sqrt(2.0 * lambda1), 1024.0) * diagonalVector;\n      vec2 v2 = min(sqrt(2.0 * lambda2), 1024.0) * vec2(diagonalVector.y, -diagonalVector.x);\n      uint colorUint = covAndColorData.w;\n      vColor = vec4(\n        float(colorUint & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(8)) & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(16)) & uint(0xFF)) / 255.0,\n        float(colorUint >> uint(24)) / 255.0\n      );\n      vPosition = position;\n\n      gl_Position = vec4(\n        vCenter \n          + position.x * v2 / viewport * 2.0 \n          + position.y * v1 / viewport * 2.0, pos2d.z / pos2d.w, 1.0);\n    }\n    `, /*glsl*/`\n    #include <alphatest_pars_fragment>\n    #include <alphahash_pars_fragment>\n    in vec4 vColor;\n    in vec3 vPosition;\n    void main () {\n      float A = -dot(vPosition.xy, vPosition.xy);\n      if (A < -4.0) discard;\n      float B = exp(A) * vColor.a;\n      vec4 diffuseColor = vec4(vColor.rgb, B);\n      #include <alphatest_fragment>\n      #include <alphahash_fragment>\n      gl_FragColor = diffuseColor;\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n    }\n  `);\nfunction createWorker(self) {\n  let matrices = null;\n  let offset = 0;\n  function sortSplats(view, hashed = false) {\n    const vertexCount = matrices.length / 16;\n    const threshold = -0.0001;\n    let maxDepth = -Infinity;\n    let minDepth = Infinity;\n    const depthList = new Float32Array(vertexCount);\n    const sizeList = new Int32Array(depthList.buffer);\n    const validIndexList = new Int32Array(vertexCount);\n    let validCount = 0;\n    for (let i = 0; i < vertexCount; i++) {\n      // Sign of depth is reversed\n      const depth = view[0] * matrices[i * 16 + 12] + view[1] * matrices[i * 16 + 13] + view[2] * matrices[i * 16 + 14] + view[3];\n      // Skip behind of camera and small, transparent splat\n      if (hashed || depth < 0 && matrices[i * 16 + 15] > threshold * depth) {\n        depthList[validCount] = depth;\n        validIndexList[validCount] = i;\n        validCount++;\n        if (depth > maxDepth) maxDepth = depth;\n        if (depth < minDepth) minDepth = depth;\n      }\n    }\n\n    // This is a 16 bit single-pass counting sort\n    const depthInv = (256 * 256 - 1) / (maxDepth - minDepth);\n    const counts0 = new Uint32Array(256 * 256);\n    for (let i = 0; i < validCount; i++) {\n      sizeList[i] = (depthList[i] - minDepth) * depthInv | 0;\n      counts0[sizeList[i]]++;\n    }\n    const starts0 = new Uint32Array(256 * 256);\n    for (let i = 1; i < 256 * 256; i++) starts0[i] = starts0[i - 1] + counts0[i - 1];\n    const depthIndex = new Uint32Array(validCount);\n    for (let i = 0; i < validCount; i++) depthIndex[starts0[sizeList[i]]++] = validIndexList[i];\n    return depthIndex;\n  }\n  self.onmessage = e => {\n    if (e.data.method == 'push') {\n      if (offset === 0) matrices = new Float32Array(e.data.length);\n      const new_matrices = new Float32Array(e.data.matrices);\n      matrices.set(new_matrices, offset);\n      offset += new_matrices.length;\n    } else if (e.data.method == 'sort') {\n      if (matrices !== null) {\n        const indices = sortSplats(new Float32Array(e.data.view), e.data.hashed);\n        // @ts-ignore\n        self.postMessage({\n          indices,\n          key: e.data.key\n        }, [indices.buffer]);\n      }\n    }\n  };\n}\nclass SplatLoader extends THREE.Loader {\n  constructor(...args) {\n    super(...args);\n    // WebGLRenderer, needs to be filled out!\n    this.gl = null;\n    // Default chunk size for lazy loading\n    this.chunkSize = 25000;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const shared = {\n      gl: this.gl,\n      url: this.manager.resolveURL(url),\n      worker: new Worker(URL.createObjectURL(new Blob(['(', createWorker.toString(), ')(self)'], {\n        type: 'application/javascript'\n      }))),\n      manager: this.manager,\n      update: (target, camera, hashed) => update(camera, shared, target, hashed),\n      connect: target => connect(shared, target),\n      loading: false,\n      loaded: false,\n      loadedVertexCount: 0,\n      chunkSize: this.chunkSize,\n      totalDownloadBytes: 0,\n      numVertices: 0,\n      rowLength: 3 * 4 + 3 * 4 + 4 + 4,\n      maxVertexes: 0,\n      bufferTextureWidth: 0,\n      bufferTextureHeight: 0,\n      stream: null,\n      centerAndScaleData: null,\n      covAndColorData: null,\n      covAndColorTexture: null,\n      centerAndScaleTexture: null,\n      onProgress\n    };\n    load(shared).then(onLoad).catch(e => {\n      onError == null || onError(e);\n      shared.manager.itemError(shared.url);\n    });\n  }\n}\nasync function load(shared) {\n  shared.manager.itemStart(shared.url);\n  const data = await fetch(shared.url);\n  if (data.body === null) throw 'Failed to fetch file';\n  let _totalDownloadBytes = data.headers.get('Content-Length');\n  const totalDownloadBytes = _totalDownloadBytes ? parseInt(_totalDownloadBytes) : undefined;\n  if (totalDownloadBytes == undefined) throw 'Failed to get content length';\n  shared.stream = data.body.getReader();\n  shared.totalDownloadBytes = totalDownloadBytes;\n  shared.numVertices = Math.floor(shared.totalDownloadBytes / shared.rowLength);\n  const context = shared.gl.getContext();\n  let maxTextureSize = context.getParameter(context.MAX_TEXTURE_SIZE);\n  shared.maxVertexes = maxTextureSize * maxTextureSize;\n  if (shared.numVertices > shared.maxVertexes) shared.numVertices = shared.maxVertexes;\n  shared.bufferTextureWidth = maxTextureSize;\n  shared.bufferTextureHeight = Math.floor((shared.numVertices - 1) / maxTextureSize) + 1;\n  shared.centerAndScaleData = new Float32Array(shared.bufferTextureWidth * shared.bufferTextureHeight * 4);\n  shared.covAndColorData = new Uint32Array(shared.bufferTextureWidth * shared.bufferTextureHeight * 4);\n  shared.centerAndScaleTexture = new THREE.DataTexture(shared.centerAndScaleData, shared.bufferTextureWidth, shared.bufferTextureHeight, THREE.RGBAFormat, THREE.FloatType);\n  shared.centerAndScaleTexture.needsUpdate = true;\n  shared.covAndColorTexture = new THREE.DataTexture(shared.covAndColorData, shared.bufferTextureWidth, shared.bufferTextureHeight, THREE.RGBAIntegerFormat, THREE.UnsignedIntType);\n  shared.covAndColorTexture.internalFormat = 'RGBA32UI';\n  shared.covAndColorTexture.needsUpdate = true;\n  return shared;\n}\nasync function lazyLoad(shared) {\n  shared.loading = true;\n  let bytesDownloaded = 0;\n  let bytesProcessed = 0;\n  const chunks = [];\n  let lastReportedProgress = 0;\n  const lengthComputable = shared.totalDownloadBytes !== 0;\n  while (true) {\n    try {\n      const {\n        value,\n        done\n      } = await shared.stream.read();\n      if (done) break;\n      bytesDownloaded += value.length;\n      if (shared.totalDownloadBytes != undefined) {\n        const percent = bytesDownloaded / shared.totalDownloadBytes * 100;\n        if (shared.onProgress && percent - lastReportedProgress > 1) {\n          const event = new ProgressEvent('progress', {\n            lengthComputable,\n            loaded: bytesDownloaded,\n            total: shared.totalDownloadBytes\n          });\n          shared.onProgress(event);\n          lastReportedProgress = percent;\n        }\n      }\n      chunks.push(value);\n      const bytesRemains = bytesDownloaded - bytesProcessed;\n      if (shared.totalDownloadBytes != undefined && bytesRemains > shared.rowLength * shared.chunkSize) {\n        let vertexCount = Math.floor(bytesRemains / shared.rowLength);\n        const concatenatedChunksbuffer = new Uint8Array(bytesRemains);\n        let offset = 0;\n        for (const chunk of chunks) {\n          concatenatedChunksbuffer.set(chunk, offset);\n          offset += chunk.length;\n        }\n        chunks.length = 0;\n        if (bytesRemains > vertexCount * shared.rowLength) {\n          const extra_data = new Uint8Array(bytesRemains - vertexCount * shared.rowLength);\n          extra_data.set(concatenatedChunksbuffer.subarray(bytesRemains - extra_data.length, bytesRemains), 0);\n          chunks.push(extra_data);\n        }\n        const buffer = new Uint8Array(vertexCount * shared.rowLength);\n        buffer.set(concatenatedChunksbuffer.subarray(0, buffer.byteLength), 0);\n        const matrices = pushDataBuffer(shared, buffer.buffer, vertexCount);\n        shared.worker.postMessage({\n          method: 'push',\n          src: shared.url,\n          length: shared.numVertices * 16,\n          matrices: matrices.buffer\n        }, [matrices.buffer]);\n        bytesProcessed += vertexCount * shared.rowLength;\n        if (shared.onProgress) {\n          const event = new ProgressEvent('progress', {\n            lengthComputable,\n            loaded: shared.totalDownloadBytes,\n            total: shared.totalDownloadBytes\n          });\n          shared.onProgress(event);\n        }\n      }\n    } catch (error) {\n      console.error(error);\n      break;\n    }\n  }\n  if (bytesDownloaded - bytesProcessed > 0) {\n    // Concatenate the chunks into a single Uint8Array\n    let concatenatedChunks = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));\n    let offset = 0;\n    for (const chunk of chunks) {\n      concatenatedChunks.set(chunk, offset);\n      offset += chunk.length;\n    }\n    let numVertices = Math.floor(concatenatedChunks.byteLength / shared.rowLength);\n    const matrices = pushDataBuffer(shared, concatenatedChunks.buffer, numVertices);\n    shared.worker.postMessage({\n      method: 'push',\n      src: shared.url,\n      length: numVertices * 16,\n      matrices: matrices.buffer\n    }, [matrices.buffer]);\n  }\n  shared.loaded = true;\n  shared.manager.itemEnd(shared.url);\n}\nfunction update(camera, shared, target, hashed) {\n  camera.updateMatrixWorld();\n  shared.gl.getCurrentViewport(target.viewport);\n  // @ts-ignore\n  target.material.viewport.x = target.viewport.z;\n  // @ts-ignore\n  target.material.viewport.y = target.viewport.w;\n  target.material.focal = target.viewport.w / 2.0 * Math.abs(camera.projectionMatrix.elements[5]);\n  if (target.ready) {\n    if (hashed && target.sorted) return;\n    target.ready = false;\n    const view = new Float32Array([target.modelViewMatrix.elements[2], -target.modelViewMatrix.elements[6], target.modelViewMatrix.elements[10], target.modelViewMatrix.elements[14]]);\n    shared.worker.postMessage({\n      method: 'sort',\n      src: shared.url,\n      key: target.uuid,\n      view: view.buffer,\n      hashed\n    }, [view.buffer]);\n    if (hashed && shared.loaded) target.sorted = true;\n  }\n}\nfunction connect(shared, target) {\n  if (!shared.loading) lazyLoad(shared);\n  target.ready = false;\n  target.pm = new THREE.Matrix4();\n  target.vm1 = new THREE.Matrix4();\n  target.vm2 = new THREE.Matrix4();\n  target.viewport = new THREE.Vector4();\n  let splatIndexArray = new Uint32Array(shared.bufferTextureWidth * shared.bufferTextureHeight);\n  const splatIndexes = new THREE.InstancedBufferAttribute(splatIndexArray, 1, false);\n  splatIndexes.setUsage(THREE.DynamicDrawUsage);\n  const geometry = target.geometry = new THREE.InstancedBufferGeometry();\n  const positionsArray = new Float32Array(6 * 3);\n  const positions = new THREE.BufferAttribute(positionsArray, 3);\n  geometry.setAttribute('position', positions);\n  positions.setXYZ(2, -2.0, 2.0, 0.0);\n  positions.setXYZ(1, 2.0, 2.0, 0.0);\n  positions.setXYZ(0, -2.0, -2.0, 0.0);\n  positions.setXYZ(5, -2.0, -2.0, 0.0);\n  positions.setXYZ(4, 2.0, 2.0, 0.0);\n  positions.setXYZ(3, 2.0, -2.0, 0.0);\n  positions.needsUpdate = true;\n  geometry.setAttribute('splatIndex', splatIndexes);\n  geometry.instanceCount = 1;\n  function listener(e) {\n    if (target && e.data.key === target.uuid) {\n      let indexes = new Uint32Array(e.data.indices);\n      // @ts-ignore\n      geometry.attributes.splatIndex.set(indexes);\n      geometry.attributes.splatIndex.needsUpdate = true;\n      geometry.instanceCount = indexes.length;\n      target.ready = true;\n    }\n  }\n  shared.worker.addEventListener('message', listener);\n  async function wait() {\n    while (true) {\n      const centerAndScaleTextureProperties = shared.gl.properties.get(shared.centerAndScaleTexture);\n      const covAndColorTextureProperties = shared.gl.properties.get(shared.covAndColorTexture);\n      if (centerAndScaleTextureProperties != null && centerAndScaleTextureProperties.__webglTexture && covAndColorTextureProperties != null && covAndColorTextureProperties.__webglTexture && shared.loadedVertexCount > 0) break;\n      await new Promise(resolve => setTimeout(resolve, 10));\n    }\n    target.ready = true;\n  }\n  wait();\n  return () => shared.worker.removeEventListener('message', listener);\n}\nfunction pushDataBuffer(shared, buffer, vertexCount) {\n  const context = shared.gl.getContext();\n  if (shared.loadedVertexCount + vertexCount > shared.maxVertexes) vertexCount = shared.maxVertexes - shared.loadedVertexCount;\n  if (vertexCount <= 0) throw 'Failed to parse file';\n  const u_buffer = new Uint8Array(buffer);\n  const f_buffer = new Float32Array(buffer);\n  const matrices = new Float32Array(vertexCount * 16);\n  const covAndColorData_uint8 = new Uint8Array(shared.covAndColorData.buffer);\n  const covAndColorData_int16 = new Int16Array(shared.covAndColorData.buffer);\n  for (let i = 0; i < vertexCount; i++) {\n    const quat = new THREE.Quaternion(-(u_buffer[32 * i + 28 + 1] - 128) / 128.0, (u_buffer[32 * i + 28 + 2] - 128) / 128.0, (u_buffer[32 * i + 28 + 3] - 128) / 128.0, -(u_buffer[32 * i + 28 + 0] - 128) / 128.0);\n    quat.invert();\n    const center = new THREE.Vector3(f_buffer[8 * i + 0], f_buffer[8 * i + 1], -f_buffer[8 * i + 2]);\n    const scale = new THREE.Vector3(f_buffer[8 * i + 3 + 0], f_buffer[8 * i + 3 + 1], f_buffer[8 * i + 3 + 2]);\n    const mtx = new THREE.Matrix4();\n    mtx.makeRotationFromQuaternion(quat);\n    mtx.transpose();\n    mtx.scale(scale);\n    const mtx_t = mtx.clone();\n    mtx.transpose();\n    mtx.premultiply(mtx_t);\n    mtx.setPosition(center);\n    const cov_indexes = [0, 1, 2, 5, 6, 10];\n    let max_value = 0.0;\n    for (let j = 0; j < cov_indexes.length; j++) if (Math.abs(mtx.elements[cov_indexes[j]]) > max_value) max_value = Math.abs(mtx.elements[cov_indexes[j]]);\n    let destOffset = shared.loadedVertexCount * 4 + i * 4;\n    shared.centerAndScaleData[destOffset + 0] = center.x;\n    shared.centerAndScaleData[destOffset + 1] = -center.y;\n    shared.centerAndScaleData[destOffset + 2] = center.z;\n    shared.centerAndScaleData[destOffset + 3] = max_value / 32767.0;\n    destOffset = shared.loadedVertexCount * 8 + i * 4 * 2;\n    for (let j = 0; j < cov_indexes.length; j++) covAndColorData_int16[destOffset + j] = mtx.elements[cov_indexes[j]] * 32767.0 / max_value;\n\n    // RGBA\n    destOffset = shared.loadedVertexCount * 16 + (i * 4 + 3) * 4;\n    const col = new THREE.Color(u_buffer[32 * i + 24 + 0] / 255, u_buffer[32 * i + 24 + 1] / 255, u_buffer[32 * i + 24 + 2] / 255);\n    col.convertSRGBToLinear();\n    covAndColorData_uint8[destOffset + 0] = col.r * 255;\n    covAndColorData_uint8[destOffset + 1] = col.g * 255;\n    covAndColorData_uint8[destOffset + 2] = col.b * 255;\n    covAndColorData_uint8[destOffset + 3] = u_buffer[32 * i + 24 + 3];\n\n    // Store scale and transparent to remove splat in sorting process\n    mtx.elements[15] = Math.max(scale.x, scale.y, scale.z) * u_buffer[32 * i + 24 + 3] / 255.0;\n    for (let j = 0; j < 16; j++) matrices[i * 16 + j] = mtx.elements[j];\n  }\n  while (vertexCount > 0) {\n    let width = 0;\n    let height = 0;\n    const xoffset = shared.loadedVertexCount % shared.bufferTextureWidth;\n    const yoffset = Math.floor(shared.loadedVertexCount / shared.bufferTextureWidth);\n    if (shared.loadedVertexCount % shared.bufferTextureWidth != 0) {\n      width = Math.min(shared.bufferTextureWidth, xoffset + vertexCount) - xoffset;\n      height = 1;\n    } else if (Math.floor(vertexCount / shared.bufferTextureWidth) > 0) {\n      width = shared.bufferTextureWidth;\n      height = Math.floor(vertexCount / shared.bufferTextureWidth);\n    } else {\n      width = vertexCount % shared.bufferTextureWidth;\n      height = 1;\n    }\n    const centerAndScaleTextureProperties = shared.gl.properties.get(shared.centerAndScaleTexture);\n    context.bindTexture(context.TEXTURE_2D, centerAndScaleTextureProperties.__webglTexture);\n    context.texSubImage2D(context.TEXTURE_2D, 0, xoffset, yoffset, width, height, context.RGBA, context.FLOAT, shared.centerAndScaleData, shared.loadedVertexCount * 4);\n    const covAndColorTextureProperties = shared.gl.properties.get(shared.covAndColorTexture);\n    context.bindTexture(context.TEXTURE_2D, covAndColorTextureProperties.__webglTexture);\n    context.texSubImage2D(context.TEXTURE_2D, 0, xoffset, yoffset, width, height,\n    // @ts-ignore\n    context.RGBA_INTEGER, context.UNSIGNED_INT, shared.covAndColorData, shared.loadedVertexCount * 4);\n    shared.gl.resetState();\n    shared.loadedVertexCount += width * height;\n    vertexCount -= width * height;\n  }\n  return matrices;\n}\nfunction Splat({\n  src,\n  toneMapped = false,\n  alphaTest = 0,\n  alphaHash = false,\n  chunkSize = 25000,\n  ...props\n}) {\n  extend({\n    SplatMaterial\n  });\n  const ref = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const camera = useThree(state => state.camera);\n\n  // Shared state, globally memoized, the same url re-uses the same daza\n  const shared = useLoader(SplatLoader, src, loader => {\n    loader.gl = gl;\n    loader.chunkSize = chunkSize;\n  });\n\n  // Listen to worker results, apply them to the target mesh\n  React.useLayoutEffect(() => shared.connect(ref.current), [src]);\n  // Update the worker\n  useFrame(() => shared.update(ref.current, camera, alphaHash));\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"splatMaterial\", {\n    key: `${src}/${alphaTest}/${alphaHash}${SplatMaterial.key}`,\n    transparent: !alphaHash,\n    depthTest: true,\n    alphaTest: alphaHash ? 0 : alphaTest,\n    centerAndScaleTexture: shared.centerAndScaleTexture,\n    covAndColorTexture: shared.covAndColorTexture,\n    depthWrite: alphaHash ? true : alphaTest > 0,\n    blending: alphaHash ? THREE.NormalBlending : THREE.CustomBlending,\n    blendSrcAlpha: THREE.OneFactor,\n    alphaHash: !!alphaHash,\n    toneMapped: toneMapped\n  }));\n}\nexport { Splat };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useLoader", "useFrame", "shaderMaterial", "version", "SplatMaterial", "alphaTest", "viewport", "Vector2", "focal", "centerAndScaleTexture", "covAndColorTexture", "createWorker", "self", "matrices", "offset", "sortSplats", "view", "hashed", "vertexCount", "length", "threshold", "max<PERSON><PERSON><PERSON>", "Infinity", "<PERSON><PERSON><PERSON><PERSON>", "depthList", "Float32Array", "sizeList", "Int32Array", "buffer", "validIndexList", "validCount", "i", "depth", "depthInv", "counts0", "Uint32Array", "starts0", "depthIndex", "onmessage", "e", "data", "method", "new_matrices", "set", "indices", "postMessage", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "args", "gl", "chunkSize", "load", "url", "onLoad", "onProgress", "onError", "shared", "manager", "resolveURL", "worker", "Worker", "URL", "createObjectURL", "Blob", "toString", "type", "update", "target", "camera", "connect", "loading", "loaded", "loadedVertexCount", "totalDownloadBytes", "numVertices", "<PERSON><PERSON><PERSON><PERSON>", "maxVertexes", "bufferTextureWidth", "bufferTextureHeight", "stream", "centerAndScaleData", "covAndColorData", "then", "catch", "itemError", "itemStart", "fetch", "body", "_totalDownloadBytes", "headers", "get", "parseInt", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "context", "getContext", "maxTextureSize", "getParameter", "MAX_TEXTURE_SIZE", "DataTexture", "RGBAFormat", "FloatType", "needsUpdate", "RGBAIntegerFormat", "UnsignedIntType", "internalFormat", "lazyLoad", "bytesDownloaded", "bytesProcessed", "chunks", "lastReportedProgress", "lengthComputable", "value", "done", "read", "percent", "event", "ProgressEvent", "total", "push", "bytesRemains", "concatenatedChunksbuffer", "Uint8Array", "chunk", "extra_data", "subarray", "byteLength", "pushDataBuffer", "src", "error", "console", "concatenatedChunks", "reduce", "acc", "itemEnd", "updateMatrixWorld", "getCurrentViewport", "material", "x", "z", "y", "w", "abs", "projectionMatrix", "elements", "ready", "sorted", "modelViewMatrix", "uuid", "pm", "Matrix4", "vm1", "vm2", "Vector4", "splatIndexArray", "splatIndexes", "InstancedBufferAttribute", "setUsage", "DynamicDrawUsage", "geometry", "InstancedBufferGeometry", "positionsArray", "positions", "BufferAttribute", "setAttribute", "setXYZ", "instanceCount", "listener", "indexes", "attributes", "splatIndex", "addEventListener", "wait", "centerAndScaleTextureProperties", "properties", "covAndColorTextureProperties", "__webglTexture", "Promise", "resolve", "setTimeout", "removeEventListener", "u_buffer", "f_buffer", "covAndColorData_uint8", "covAndColorData_int16", "Int16Array", "quat", "Quaternion", "invert", "center", "Vector3", "scale", "mtx", "makeRotationFromQuaternion", "transpose", "mtx_t", "clone", "premultiply", "setPosition", "cov_indexes", "max_value", "j", "destOffset", "col", "Color", "convertSRGBToLinear", "r", "g", "b", "max", "width", "height", "xoffset", "yoffset", "min", "bindTexture", "TEXTURE_2D", "texSubImage2D", "RGBA", "FLOAT", "RGBA_INTEGER", "UNSIGNED_INT", "resetState", "<PERSON>p<PERSON>", "toneMapped", "alphaHash", "props", "ref", "useRef", "state", "loader", "useLayoutEffect", "current", "createElement", "frustumCulled", "transparent", "depthTest", "depthWrite", "blending", "NormalBlending", "CustomBlending", "blendSrcAlpha", "OneFactor"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Splat.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useLoader, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { version } from '../helpers/constants.js';\n\nconst SplatMaterial = /* @__PURE__ */shaderMaterial({\n  alphaTest: 0,\n  viewport: /* @__PURE__ */new THREE.Vector2(1980, 1080),\n  focal: 1000.0,\n  centerAndScaleTexture: null,\n  covAndColorTexture: null\n}, /*glsl*/`\n    precision highp sampler2D;\n    precision highp usampler2D;\n    out vec4 vColor;\n    out vec3 vPosition;\n    uniform vec2 resolution;\n    uniform vec2 viewport;\n    uniform float focal;\n    attribute uint splatIndex;\n    uniform sampler2D centerAndScaleTexture;\n    uniform usampler2D covAndColorTexture;    \n\n    vec2 unpackInt16(in uint value) {\n      int v = int(value);\n      int v0 = v >> 16;\n      int v1 = (v & 0xFFFF);\n      if((v & 0x8000) != 0)\n        v1 |= 0xFFFF0000;\n      return vec2(float(v1), float(v0));\n    }\n\n    void main () {\n      ivec2 texSize = textureSize(centerAndScaleTexture, 0);\n      ivec2 texPos = ivec2(splatIndex%uint(texSize.x), splatIndex/uint(texSize.x));\n      vec4 centerAndScaleData = texelFetch(centerAndScaleTexture, texPos, 0);\n      vec4 center = vec4(centerAndScaleData.xyz, 1);\n      vec4 camspace = modelViewMatrix * center;\n      vec4 pos2d = projectionMatrix * camspace;\n\n      float bounds = 1.2 * pos2d.w;\n      if (pos2d.z < -pos2d.w || pos2d.x < -bounds || pos2d.x > bounds\n        || pos2d.y < -bounds || pos2d.y > bounds) {\n        gl_Position = vec4(0.0, 0.0, 2.0, 1.0);\n        return;\n      }\n\n      uvec4 covAndColorData = texelFetch(covAndColorTexture, texPos, 0);\n      vec2 cov3D_M11_M12 = unpackInt16(covAndColorData.x) * centerAndScaleData.w;\n      vec2 cov3D_M13_M22 = unpackInt16(covAndColorData.y) * centerAndScaleData.w;\n      vec2 cov3D_M23_M33 = unpackInt16(covAndColorData.z) * centerAndScaleData.w;\n      mat3 Vrk = mat3(\n        cov3D_M11_M12.x, cov3D_M11_M12.y, cov3D_M13_M22.x,\n        cov3D_M11_M12.y, cov3D_M13_M22.y, cov3D_M23_M33.x,\n        cov3D_M13_M22.x, cov3D_M23_M33.x, cov3D_M23_M33.y\n      );\n\n      mat3 J = mat3(\n        focal / camspace.z, 0., -(focal * camspace.x) / (camspace.z * camspace.z),\n        0., focal / camspace.z, -(focal * camspace.y) / (camspace.z * camspace.z),\n        0., 0., 0.\n      );\n\n      mat3 W = transpose(mat3(modelViewMatrix));\n      mat3 T = W * J;\n      mat3 cov = transpose(T) * Vrk * T;\n      vec2 vCenter = vec2(pos2d) / pos2d.w;\n      float diagonal1 = cov[0][0] + 0.3;\n      float offDiagonal = cov[0][1];\n      float diagonal2 = cov[1][1] + 0.3;\n      float mid = 0.5 * (diagonal1 + diagonal2);\n      float radius = length(vec2((diagonal1 - diagonal2) / 2.0, offDiagonal));\n      float lambda1 = mid + radius;\n      float lambda2 = max(mid - radius, 0.1);\n      vec2 diagonalVector = normalize(vec2(offDiagonal, lambda1 - diagonal1));\n      vec2 v1 = min(sqrt(2.0 * lambda1), 1024.0) * diagonalVector;\n      vec2 v2 = min(sqrt(2.0 * lambda2), 1024.0) * vec2(diagonalVector.y, -diagonalVector.x);\n      uint colorUint = covAndColorData.w;\n      vColor = vec4(\n        float(colorUint & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(8)) & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(16)) & uint(0xFF)) / 255.0,\n        float(colorUint >> uint(24)) / 255.0\n      );\n      vPosition = position;\n\n      gl_Position = vec4(\n        vCenter \n          + position.x * v2 / viewport * 2.0 \n          + position.y * v1 / viewport * 2.0, pos2d.z / pos2d.w, 1.0);\n    }\n    `, /*glsl*/`\n    #include <alphatest_pars_fragment>\n    #include <alphahash_pars_fragment>\n    in vec4 vColor;\n    in vec3 vPosition;\n    void main () {\n      float A = -dot(vPosition.xy, vPosition.xy);\n      if (A < -4.0) discard;\n      float B = exp(A) * vColor.a;\n      vec4 diffuseColor = vec4(vColor.rgb, B);\n      #include <alphatest_fragment>\n      #include <alphahash_fragment>\n      gl_FragColor = diffuseColor;\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n    }\n  `);\nfunction createWorker(self) {\n  let matrices = null;\n  let offset = 0;\n  function sortSplats(view, hashed = false) {\n    const vertexCount = matrices.length / 16;\n    const threshold = -0.0001;\n    let maxDepth = -Infinity;\n    let minDepth = Infinity;\n    const depthList = new Float32Array(vertexCount);\n    const sizeList = new Int32Array(depthList.buffer);\n    const validIndexList = new Int32Array(vertexCount);\n    let validCount = 0;\n    for (let i = 0; i < vertexCount; i++) {\n      // Sign of depth is reversed\n      const depth = view[0] * matrices[i * 16 + 12] + view[1] * matrices[i * 16 + 13] + view[2] * matrices[i * 16 + 14] + view[3];\n      // Skip behind of camera and small, transparent splat\n      if (hashed || depth < 0 && matrices[i * 16 + 15] > threshold * depth) {\n        depthList[validCount] = depth;\n        validIndexList[validCount] = i;\n        validCount++;\n        if (depth > maxDepth) maxDepth = depth;\n        if (depth < minDepth) minDepth = depth;\n      }\n    }\n\n    // This is a 16 bit single-pass counting sort\n    const depthInv = (256 * 256 - 1) / (maxDepth - minDepth);\n    const counts0 = new Uint32Array(256 * 256);\n    for (let i = 0; i < validCount; i++) {\n      sizeList[i] = (depthList[i] - minDepth) * depthInv | 0;\n      counts0[sizeList[i]]++;\n    }\n    const starts0 = new Uint32Array(256 * 256);\n    for (let i = 1; i < 256 * 256; i++) starts0[i] = starts0[i - 1] + counts0[i - 1];\n    const depthIndex = new Uint32Array(validCount);\n    for (let i = 0; i < validCount; i++) depthIndex[starts0[sizeList[i]]++] = validIndexList[i];\n    return depthIndex;\n  }\n  self.onmessage = e => {\n    if (e.data.method == 'push') {\n      if (offset === 0) matrices = new Float32Array(e.data.length);\n      const new_matrices = new Float32Array(e.data.matrices);\n      matrices.set(new_matrices, offset);\n      offset += new_matrices.length;\n    } else if (e.data.method == 'sort') {\n      if (matrices !== null) {\n        const indices = sortSplats(new Float32Array(e.data.view), e.data.hashed);\n        // @ts-ignore\n        self.postMessage({\n          indices,\n          key: e.data.key\n        }, [indices.buffer]);\n      }\n    }\n  };\n}\nclass SplatLoader extends THREE.Loader {\n  constructor(...args) {\n    super(...args);\n    // WebGLRenderer, needs to be filled out!\n    this.gl = null;\n    // Default chunk size for lazy loading\n    this.chunkSize = 25000;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const shared = {\n      gl: this.gl,\n      url: this.manager.resolveURL(url),\n      worker: new Worker(URL.createObjectURL(new Blob(['(', createWorker.toString(), ')(self)'], {\n        type: 'application/javascript'\n      }))),\n      manager: this.manager,\n      update: (target, camera, hashed) => update(camera, shared, target, hashed),\n      connect: target => connect(shared, target),\n      loading: false,\n      loaded: false,\n      loadedVertexCount: 0,\n      chunkSize: this.chunkSize,\n      totalDownloadBytes: 0,\n      numVertices: 0,\n      rowLength: 3 * 4 + 3 * 4 + 4 + 4,\n      maxVertexes: 0,\n      bufferTextureWidth: 0,\n      bufferTextureHeight: 0,\n      stream: null,\n      centerAndScaleData: null,\n      covAndColorData: null,\n      covAndColorTexture: null,\n      centerAndScaleTexture: null,\n      onProgress\n    };\n    load(shared).then(onLoad).catch(e => {\n      onError == null || onError(e);\n      shared.manager.itemError(shared.url);\n    });\n  }\n}\nasync function load(shared) {\n  shared.manager.itemStart(shared.url);\n  const data = await fetch(shared.url);\n  if (data.body === null) throw 'Failed to fetch file';\n  let _totalDownloadBytes = data.headers.get('Content-Length');\n  const totalDownloadBytes = _totalDownloadBytes ? parseInt(_totalDownloadBytes) : undefined;\n  if (totalDownloadBytes == undefined) throw 'Failed to get content length';\n  shared.stream = data.body.getReader();\n  shared.totalDownloadBytes = totalDownloadBytes;\n  shared.numVertices = Math.floor(shared.totalDownloadBytes / shared.rowLength);\n  const context = shared.gl.getContext();\n  let maxTextureSize = context.getParameter(context.MAX_TEXTURE_SIZE);\n  shared.maxVertexes = maxTextureSize * maxTextureSize;\n  if (shared.numVertices > shared.maxVertexes) shared.numVertices = shared.maxVertexes;\n  shared.bufferTextureWidth = maxTextureSize;\n  shared.bufferTextureHeight = Math.floor((shared.numVertices - 1) / maxTextureSize) + 1;\n  shared.centerAndScaleData = new Float32Array(shared.bufferTextureWidth * shared.bufferTextureHeight * 4);\n  shared.covAndColorData = new Uint32Array(shared.bufferTextureWidth * shared.bufferTextureHeight * 4);\n  shared.centerAndScaleTexture = new THREE.DataTexture(shared.centerAndScaleData, shared.bufferTextureWidth, shared.bufferTextureHeight, THREE.RGBAFormat, THREE.FloatType);\n  shared.centerAndScaleTexture.needsUpdate = true;\n  shared.covAndColorTexture = new THREE.DataTexture(shared.covAndColorData, shared.bufferTextureWidth, shared.bufferTextureHeight, THREE.RGBAIntegerFormat, THREE.UnsignedIntType);\n  shared.covAndColorTexture.internalFormat = 'RGBA32UI';\n  shared.covAndColorTexture.needsUpdate = true;\n  return shared;\n}\nasync function lazyLoad(shared) {\n  shared.loading = true;\n  let bytesDownloaded = 0;\n  let bytesProcessed = 0;\n  const chunks = [];\n  let lastReportedProgress = 0;\n  const lengthComputable = shared.totalDownloadBytes !== 0;\n  while (true) {\n    try {\n      const {\n        value,\n        done\n      } = await shared.stream.read();\n      if (done) break;\n      bytesDownloaded += value.length;\n      if (shared.totalDownloadBytes != undefined) {\n        const percent = bytesDownloaded / shared.totalDownloadBytes * 100;\n        if (shared.onProgress && percent - lastReportedProgress > 1) {\n          const event = new ProgressEvent('progress', {\n            lengthComputable,\n            loaded: bytesDownloaded,\n            total: shared.totalDownloadBytes\n          });\n          shared.onProgress(event);\n          lastReportedProgress = percent;\n        }\n      }\n      chunks.push(value);\n      const bytesRemains = bytesDownloaded - bytesProcessed;\n      if (shared.totalDownloadBytes != undefined && bytesRemains > shared.rowLength * shared.chunkSize) {\n        let vertexCount = Math.floor(bytesRemains / shared.rowLength);\n        const concatenatedChunksbuffer = new Uint8Array(bytesRemains);\n        let offset = 0;\n        for (const chunk of chunks) {\n          concatenatedChunksbuffer.set(chunk, offset);\n          offset += chunk.length;\n        }\n        chunks.length = 0;\n        if (bytesRemains > vertexCount * shared.rowLength) {\n          const extra_data = new Uint8Array(bytesRemains - vertexCount * shared.rowLength);\n          extra_data.set(concatenatedChunksbuffer.subarray(bytesRemains - extra_data.length, bytesRemains), 0);\n          chunks.push(extra_data);\n        }\n        const buffer = new Uint8Array(vertexCount * shared.rowLength);\n        buffer.set(concatenatedChunksbuffer.subarray(0, buffer.byteLength), 0);\n        const matrices = pushDataBuffer(shared, buffer.buffer, vertexCount);\n        shared.worker.postMessage({\n          method: 'push',\n          src: shared.url,\n          length: shared.numVertices * 16,\n          matrices: matrices.buffer\n        }, [matrices.buffer]);\n        bytesProcessed += vertexCount * shared.rowLength;\n        if (shared.onProgress) {\n          const event = new ProgressEvent('progress', {\n            lengthComputable,\n            loaded: shared.totalDownloadBytes,\n            total: shared.totalDownloadBytes\n          });\n          shared.onProgress(event);\n        }\n      }\n    } catch (error) {\n      console.error(error);\n      break;\n    }\n  }\n  if (bytesDownloaded - bytesProcessed > 0) {\n    // Concatenate the chunks into a single Uint8Array\n    let concatenatedChunks = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));\n    let offset = 0;\n    for (const chunk of chunks) {\n      concatenatedChunks.set(chunk, offset);\n      offset += chunk.length;\n    }\n    let numVertices = Math.floor(concatenatedChunks.byteLength / shared.rowLength);\n    const matrices = pushDataBuffer(shared, concatenatedChunks.buffer, numVertices);\n    shared.worker.postMessage({\n      method: 'push',\n      src: shared.url,\n      length: numVertices * 16,\n      matrices: matrices.buffer\n    }, [matrices.buffer]);\n  }\n  shared.loaded = true;\n  shared.manager.itemEnd(shared.url);\n}\nfunction update(camera, shared, target, hashed) {\n  camera.updateMatrixWorld();\n  shared.gl.getCurrentViewport(target.viewport);\n  // @ts-ignore\n  target.material.viewport.x = target.viewport.z;\n  // @ts-ignore\n  target.material.viewport.y = target.viewport.w;\n  target.material.focal = target.viewport.w / 2.0 * Math.abs(camera.projectionMatrix.elements[5]);\n  if (target.ready) {\n    if (hashed && target.sorted) return;\n    target.ready = false;\n    const view = new Float32Array([target.modelViewMatrix.elements[2], -target.modelViewMatrix.elements[6], target.modelViewMatrix.elements[10], target.modelViewMatrix.elements[14]]);\n    shared.worker.postMessage({\n      method: 'sort',\n      src: shared.url,\n      key: target.uuid,\n      view: view.buffer,\n      hashed\n    }, [view.buffer]);\n    if (hashed && shared.loaded) target.sorted = true;\n  }\n}\nfunction connect(shared, target) {\n  if (!shared.loading) lazyLoad(shared);\n  target.ready = false;\n  target.pm = new THREE.Matrix4();\n  target.vm1 = new THREE.Matrix4();\n  target.vm2 = new THREE.Matrix4();\n  target.viewport = new THREE.Vector4();\n  let splatIndexArray = new Uint32Array(shared.bufferTextureWidth * shared.bufferTextureHeight);\n  const splatIndexes = new THREE.InstancedBufferAttribute(splatIndexArray, 1, false);\n  splatIndexes.setUsage(THREE.DynamicDrawUsage);\n  const geometry = target.geometry = new THREE.InstancedBufferGeometry();\n  const positionsArray = new Float32Array(6 * 3);\n  const positions = new THREE.BufferAttribute(positionsArray, 3);\n  geometry.setAttribute('position', positions);\n  positions.setXYZ(2, -2.0, 2.0, 0.0);\n  positions.setXYZ(1, 2.0, 2.0, 0.0);\n  positions.setXYZ(0, -2.0, -2.0, 0.0);\n  positions.setXYZ(5, -2.0, -2.0, 0.0);\n  positions.setXYZ(4, 2.0, 2.0, 0.0);\n  positions.setXYZ(3, 2.0, -2.0, 0.0);\n  positions.needsUpdate = true;\n  geometry.setAttribute('splatIndex', splatIndexes);\n  geometry.instanceCount = 1;\n  function listener(e) {\n    if (target && e.data.key === target.uuid) {\n      let indexes = new Uint32Array(e.data.indices);\n      // @ts-ignore\n      geometry.attributes.splatIndex.set(indexes);\n      geometry.attributes.splatIndex.needsUpdate = true;\n      geometry.instanceCount = indexes.length;\n      target.ready = true;\n    }\n  }\n  shared.worker.addEventListener('message', listener);\n  async function wait() {\n    while (true) {\n      const centerAndScaleTextureProperties = shared.gl.properties.get(shared.centerAndScaleTexture);\n      const covAndColorTextureProperties = shared.gl.properties.get(shared.covAndColorTexture);\n      if (centerAndScaleTextureProperties != null && centerAndScaleTextureProperties.__webglTexture && covAndColorTextureProperties != null && covAndColorTextureProperties.__webglTexture && shared.loadedVertexCount > 0) break;\n      await new Promise(resolve => setTimeout(resolve, 10));\n    }\n    target.ready = true;\n  }\n  wait();\n  return () => shared.worker.removeEventListener('message', listener);\n}\nfunction pushDataBuffer(shared, buffer, vertexCount) {\n  const context = shared.gl.getContext();\n  if (shared.loadedVertexCount + vertexCount > shared.maxVertexes) vertexCount = shared.maxVertexes - shared.loadedVertexCount;\n  if (vertexCount <= 0) throw 'Failed to parse file';\n  const u_buffer = new Uint8Array(buffer);\n  const f_buffer = new Float32Array(buffer);\n  const matrices = new Float32Array(vertexCount * 16);\n  const covAndColorData_uint8 = new Uint8Array(shared.covAndColorData.buffer);\n  const covAndColorData_int16 = new Int16Array(shared.covAndColorData.buffer);\n  for (let i = 0; i < vertexCount; i++) {\n    const quat = new THREE.Quaternion(-(u_buffer[32 * i + 28 + 1] - 128) / 128.0, (u_buffer[32 * i + 28 + 2] - 128) / 128.0, (u_buffer[32 * i + 28 + 3] - 128) / 128.0, -(u_buffer[32 * i + 28 + 0] - 128) / 128.0);\n    quat.invert();\n    const center = new THREE.Vector3(f_buffer[8 * i + 0], f_buffer[8 * i + 1], -f_buffer[8 * i + 2]);\n    const scale = new THREE.Vector3(f_buffer[8 * i + 3 + 0], f_buffer[8 * i + 3 + 1], f_buffer[8 * i + 3 + 2]);\n    const mtx = new THREE.Matrix4();\n    mtx.makeRotationFromQuaternion(quat);\n    mtx.transpose();\n    mtx.scale(scale);\n    const mtx_t = mtx.clone();\n    mtx.transpose();\n    mtx.premultiply(mtx_t);\n    mtx.setPosition(center);\n    const cov_indexes = [0, 1, 2, 5, 6, 10];\n    let max_value = 0.0;\n    for (let j = 0; j < cov_indexes.length; j++) if (Math.abs(mtx.elements[cov_indexes[j]]) > max_value) max_value = Math.abs(mtx.elements[cov_indexes[j]]);\n    let destOffset = shared.loadedVertexCount * 4 + i * 4;\n    shared.centerAndScaleData[destOffset + 0] = center.x;\n    shared.centerAndScaleData[destOffset + 1] = -center.y;\n    shared.centerAndScaleData[destOffset + 2] = center.z;\n    shared.centerAndScaleData[destOffset + 3] = max_value / 32767.0;\n    destOffset = shared.loadedVertexCount * 8 + i * 4 * 2;\n    for (let j = 0; j < cov_indexes.length; j++) covAndColorData_int16[destOffset + j] = mtx.elements[cov_indexes[j]] * 32767.0 / max_value;\n\n    // RGBA\n    destOffset = shared.loadedVertexCount * 16 + (i * 4 + 3) * 4;\n    const col = new THREE.Color(u_buffer[32 * i + 24 + 0] / 255, u_buffer[32 * i + 24 + 1] / 255, u_buffer[32 * i + 24 + 2] / 255);\n    col.convertSRGBToLinear();\n    covAndColorData_uint8[destOffset + 0] = col.r * 255;\n    covAndColorData_uint8[destOffset + 1] = col.g * 255;\n    covAndColorData_uint8[destOffset + 2] = col.b * 255;\n    covAndColorData_uint8[destOffset + 3] = u_buffer[32 * i + 24 + 3];\n\n    // Store scale and transparent to remove splat in sorting process\n    mtx.elements[15] = Math.max(scale.x, scale.y, scale.z) * u_buffer[32 * i + 24 + 3] / 255.0;\n    for (let j = 0; j < 16; j++) matrices[i * 16 + j] = mtx.elements[j];\n  }\n  while (vertexCount > 0) {\n    let width = 0;\n    let height = 0;\n    const xoffset = shared.loadedVertexCount % shared.bufferTextureWidth;\n    const yoffset = Math.floor(shared.loadedVertexCount / shared.bufferTextureWidth);\n    if (shared.loadedVertexCount % shared.bufferTextureWidth != 0) {\n      width = Math.min(shared.bufferTextureWidth, xoffset + vertexCount) - xoffset;\n      height = 1;\n    } else if (Math.floor(vertexCount / shared.bufferTextureWidth) > 0) {\n      width = shared.bufferTextureWidth;\n      height = Math.floor(vertexCount / shared.bufferTextureWidth);\n    } else {\n      width = vertexCount % shared.bufferTextureWidth;\n      height = 1;\n    }\n    const centerAndScaleTextureProperties = shared.gl.properties.get(shared.centerAndScaleTexture);\n    context.bindTexture(context.TEXTURE_2D, centerAndScaleTextureProperties.__webglTexture);\n    context.texSubImage2D(context.TEXTURE_2D, 0, xoffset, yoffset, width, height, context.RGBA, context.FLOAT, shared.centerAndScaleData, shared.loadedVertexCount * 4);\n    const covAndColorTextureProperties = shared.gl.properties.get(shared.covAndColorTexture);\n    context.bindTexture(context.TEXTURE_2D, covAndColorTextureProperties.__webglTexture);\n    context.texSubImage2D(context.TEXTURE_2D, 0, xoffset, yoffset, width, height,\n    // @ts-ignore\n    context.RGBA_INTEGER, context.UNSIGNED_INT, shared.covAndColorData, shared.loadedVertexCount * 4);\n    shared.gl.resetState();\n    shared.loadedVertexCount += width * height;\n    vertexCount -= width * height;\n  }\n  return matrices;\n}\nfunction Splat({\n  src,\n  toneMapped = false,\n  alphaTest = 0,\n  alphaHash = false,\n  chunkSize = 25000,\n  ...props\n}) {\n  extend({\n    SplatMaterial\n  });\n  const ref = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const camera = useThree(state => state.camera);\n\n  // Shared state, globally memoized, the same url re-uses the same daza\n  const shared = useLoader(SplatLoader, src, loader => {\n    loader.gl = gl;\n    loader.chunkSize = chunkSize;\n  });\n\n  // Listen to worker results, apply them to the target mesh\n  React.useLayoutEffect(() => shared.connect(ref.current), [src]);\n  // Update the worker\n  useFrame(() => shared.update(ref.current, camera, alphaHash));\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"splatMaterial\", {\n    key: `${src}/${alphaTest}/${alphaHash}${SplatMaterial.key}`,\n    transparent: !alphaHash,\n    depthTest: true,\n    alphaTest: alphaHash ? 0 : alphaTest,\n    centerAndScaleTexture: shared.centerAndScaleTexture,\n    covAndColorTexture: shared.covAndColorTexture,\n    depthWrite: alphaHash ? true : alphaTest > 0,\n    blending: alphaHash ? THREE.NormalBlending : THREE.CustomBlending,\n    blendSrcAlpha: THREE.OneFactor,\n    alphaHash: !!alphaHash,\n    toneMapped: toneMapped\n  }));\n}\n\nexport { Splat };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,oBAAoB;AAC1E,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,aAAa,GAAG,eAAeF,cAAc,CAAC;EAClDG,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,eAAe,IAAIV,KAAK,CAACW,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;EACtDC,KAAK,EAAE,MAAM;EACbC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE;AACtB,CAAC,EAAE,QAAQ;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EAAE,QAAQ;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBP,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC/E;AACA,GAAG,CAAC;AACJ,SAASQ,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIC,MAAM,GAAG,CAAC;EACd,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,GAAG,KAAK,EAAE;IACxC,MAAMC,WAAW,GAAGL,QAAQ,CAACM,MAAM,GAAG,EAAE;IACxC,MAAMC,SAAS,GAAG,CAAC,MAAM;IACzB,IAAIC,QAAQ,GAAG,CAACC,QAAQ;IACxB,IAAIC,QAAQ,GAAGD,QAAQ;IACvB,MAAME,SAAS,GAAG,IAAIC,YAAY,CAACP,WAAW,CAAC;IAC/C,MAAMQ,QAAQ,GAAG,IAAIC,UAAU,CAACH,SAAS,CAACI,MAAM,CAAC;IACjD,MAAMC,cAAc,GAAG,IAAIF,UAAU,CAACT,WAAW,CAAC;IAClD,IAAIY,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,WAAW,EAAEa,CAAC,EAAE,EAAE;MACpC;MACA,MAAMC,KAAK,GAAGhB,IAAI,CAAC,CAAC,CAAC,GAAGH,QAAQ,CAACkB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAGf,IAAI,CAAC,CAAC,CAAC,GAAGH,QAAQ,CAACkB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAGf,IAAI,CAAC,CAAC,CAAC,GAAGH,QAAQ,CAACkB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAGf,IAAI,CAAC,CAAC,CAAC;MAC3H;MACA,IAAIC,MAAM,IAAIe,KAAK,GAAG,CAAC,IAAInB,QAAQ,CAACkB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAGX,SAAS,GAAGY,KAAK,EAAE;QACpER,SAAS,CAACM,UAAU,CAAC,GAAGE,KAAK;QAC7BH,cAAc,CAACC,UAAU,CAAC,GAAGC,CAAC;QAC9BD,UAAU,EAAE;QACZ,IAAIE,KAAK,GAAGX,QAAQ,EAAEA,QAAQ,GAAGW,KAAK;QACtC,IAAIA,KAAK,GAAGT,QAAQ,EAAEA,QAAQ,GAAGS,KAAK;MACxC;IACF;;IAEA;IACA,MAAMC,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,KAAKZ,QAAQ,GAAGE,QAAQ,CAAC;IACxD,MAAMW,OAAO,GAAG,IAAIC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;IAC1C,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;MACnCL,QAAQ,CAACK,CAAC,CAAC,GAAG,CAACP,SAAS,CAACO,CAAC,CAAC,GAAGR,QAAQ,IAAIU,QAAQ,GAAG,CAAC;MACtDC,OAAO,CAACR,QAAQ,CAACK,CAAC,CAAC,CAAC,EAAE;IACxB;IACA,MAAMK,OAAO,GAAG,IAAID,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;IAC1C,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAEK,OAAO,CAACL,CAAC,CAAC,GAAGK,OAAO,CAACL,CAAC,GAAG,CAAC,CAAC,GAAGG,OAAO,CAACH,CAAC,GAAG,CAAC,CAAC;IAChF,MAAMM,UAAU,GAAG,IAAIF,WAAW,CAACL,UAAU,CAAC;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAEM,UAAU,CAACD,OAAO,CAACV,QAAQ,CAACK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGF,cAAc,CAACE,CAAC,CAAC;IAC3F,OAAOM,UAAU;EACnB;EACAzB,IAAI,CAAC0B,SAAS,GAAGC,CAAC,IAAI;IACpB,IAAIA,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI,MAAM,EAAE;MAC3B,IAAI3B,MAAM,KAAK,CAAC,EAAED,QAAQ,GAAG,IAAIY,YAAY,CAACc,CAAC,CAACC,IAAI,CAACrB,MAAM,CAAC;MAC5D,MAAMuB,YAAY,GAAG,IAAIjB,YAAY,CAACc,CAAC,CAACC,IAAI,CAAC3B,QAAQ,CAAC;MACtDA,QAAQ,CAAC8B,GAAG,CAACD,YAAY,EAAE5B,MAAM,CAAC;MAClCA,MAAM,IAAI4B,YAAY,CAACvB,MAAM;IAC/B,CAAC,MAAM,IAAIoB,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI,MAAM,EAAE;MAClC,IAAI5B,QAAQ,KAAK,IAAI,EAAE;QACrB,MAAM+B,OAAO,GAAG7B,UAAU,CAAC,IAAIU,YAAY,CAACc,CAAC,CAACC,IAAI,CAACxB,IAAI,CAAC,EAAEuB,CAAC,CAACC,IAAI,CAACvB,MAAM,CAAC;QACxE;QACAL,IAAI,CAACiC,WAAW,CAAC;UACfD,OAAO;UACPE,GAAG,EAAEP,CAAC,CAACC,IAAI,CAACM;QACd,CAAC,EAAE,CAACF,OAAO,CAAChB,MAAM,CAAC,CAAC;MACtB;IACF;EACF,CAAC;AACH;AACA,MAAMmB,WAAW,SAASnD,KAAK,CAACoD,MAAM,CAAC;EACrCC,WAAWA,CAAC,GAAGC,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd;IACA,IAAI,CAACC,EAAE,GAAG,IAAI;IACd;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB;EACAC,IAAIA,CAACC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACrC,MAAMC,MAAM,GAAG;MACbP,EAAE,EAAE,IAAI,CAACA,EAAE;MACXG,GAAG,EAAE,IAAI,CAACK,OAAO,CAACC,UAAU,CAACN,GAAG,CAAC;MACjCO,MAAM,EAAE,IAAIC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,GAAG,EAAEtD,YAAY,CAACuD,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE;QACzFC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC,CAAC;MACJR,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBS,MAAM,EAAEA,CAACC,MAAM,EAAEC,MAAM,EAAErD,MAAM,KAAKmD,MAAM,CAACE,MAAM,EAAEZ,MAAM,EAAEW,MAAM,EAAEpD,MAAM,CAAC;MAC1EsD,OAAO,EAAEF,MAAM,IAAIE,OAAO,CAACb,MAAM,EAAEW,MAAM,CAAC;MAC1CG,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,KAAK;MACbC,iBAAiB,EAAE,CAAC;MACpBtB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBuB,kBAAkB,EAAE,CAAC;MACrBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;MAChCC,WAAW,EAAE,CAAC;MACdC,kBAAkB,EAAE,CAAC;MACrBC,mBAAmB,EAAE,CAAC;MACtBC,MAAM,EAAE,IAAI;MACZC,kBAAkB,EAAE,IAAI;MACxBC,eAAe,EAAE,IAAI;MACrBzE,kBAAkB,EAAE,IAAI;MACxBD,qBAAqB,EAAE,IAAI;MAC3B+C;IACF,CAAC;IACDH,IAAI,CAACK,MAAM,CAAC,CAAC0B,IAAI,CAAC7B,MAAM,CAAC,CAAC8B,KAAK,CAAC9C,CAAC,IAAI;MACnCkB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAClB,CAAC,CAAC;MAC7BmB,MAAM,CAACC,OAAO,CAAC2B,SAAS,CAAC5B,MAAM,CAACJ,GAAG,CAAC;IACtC,CAAC,CAAC;EACJ;AACF;AACA,eAAeD,IAAIA,CAACK,MAAM,EAAE;EAC1BA,MAAM,CAACC,OAAO,CAAC4B,SAAS,CAAC7B,MAAM,CAACJ,GAAG,CAAC;EACpC,MAAMd,IAAI,GAAG,MAAMgD,KAAK,CAAC9B,MAAM,CAACJ,GAAG,CAAC;EACpC,IAAId,IAAI,CAACiD,IAAI,KAAK,IAAI,EAAE,MAAM,sBAAsB;EACpD,IAAIC,mBAAmB,GAAGlD,IAAI,CAACmD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAC5D,MAAMjB,kBAAkB,GAAGe,mBAAmB,GAAGG,QAAQ,CAACH,mBAAmB,CAAC,GAAGI,SAAS;EAC1F,IAAInB,kBAAkB,IAAImB,SAAS,EAAE,MAAM,8BAA8B;EACzEpC,MAAM,CAACuB,MAAM,GAAGzC,IAAI,CAACiD,IAAI,CAACM,SAAS,CAAC,CAAC;EACrCrC,MAAM,CAACiB,kBAAkB,GAAGA,kBAAkB;EAC9CjB,MAAM,CAACkB,WAAW,GAAGoB,IAAI,CAACC,KAAK,CAACvC,MAAM,CAACiB,kBAAkB,GAAGjB,MAAM,CAACmB,SAAS,CAAC;EAC7E,MAAMqB,OAAO,GAAGxC,MAAM,CAACP,EAAE,CAACgD,UAAU,CAAC,CAAC;EACtC,IAAIC,cAAc,GAAGF,OAAO,CAACG,YAAY,CAACH,OAAO,CAACI,gBAAgB,CAAC;EACnE5C,MAAM,CAACoB,WAAW,GAAGsB,cAAc,GAAGA,cAAc;EACpD,IAAI1C,MAAM,CAACkB,WAAW,GAAGlB,MAAM,CAACoB,WAAW,EAAEpB,MAAM,CAACkB,WAAW,GAAGlB,MAAM,CAACoB,WAAW;EACpFpB,MAAM,CAACqB,kBAAkB,GAAGqB,cAAc;EAC1C1C,MAAM,CAACsB,mBAAmB,GAAGgB,IAAI,CAACC,KAAK,CAAC,CAACvC,MAAM,CAACkB,WAAW,GAAG,CAAC,IAAIwB,cAAc,CAAC,GAAG,CAAC;EACtF1C,MAAM,CAACwB,kBAAkB,GAAG,IAAIzD,YAAY,CAACiC,MAAM,CAACqB,kBAAkB,GAAGrB,MAAM,CAACsB,mBAAmB,GAAG,CAAC,CAAC;EACxGtB,MAAM,CAACyB,eAAe,GAAG,IAAIhD,WAAW,CAACuB,MAAM,CAACqB,kBAAkB,GAAGrB,MAAM,CAACsB,mBAAmB,GAAG,CAAC,CAAC;EACpGtB,MAAM,CAACjD,qBAAqB,GAAG,IAAIb,KAAK,CAAC2G,WAAW,CAAC7C,MAAM,CAACwB,kBAAkB,EAAExB,MAAM,CAACqB,kBAAkB,EAAErB,MAAM,CAACsB,mBAAmB,EAAEpF,KAAK,CAAC4G,UAAU,EAAE5G,KAAK,CAAC6G,SAAS,CAAC;EACzK/C,MAAM,CAACjD,qBAAqB,CAACiG,WAAW,GAAG,IAAI;EAC/ChD,MAAM,CAAChD,kBAAkB,GAAG,IAAId,KAAK,CAAC2G,WAAW,CAAC7C,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACqB,kBAAkB,EAAErB,MAAM,CAACsB,mBAAmB,EAAEpF,KAAK,CAAC+G,iBAAiB,EAAE/G,KAAK,CAACgH,eAAe,CAAC;EAChLlD,MAAM,CAAChD,kBAAkB,CAACmG,cAAc,GAAG,UAAU;EACrDnD,MAAM,CAAChD,kBAAkB,CAACgG,WAAW,GAAG,IAAI;EAC5C,OAAOhD,MAAM;AACf;AACA,eAAeoD,QAAQA,CAACpD,MAAM,EAAE;EAC9BA,MAAM,CAACc,OAAO,GAAG,IAAI;EACrB,IAAIuC,eAAe,GAAG,CAAC;EACvB,IAAIC,cAAc,GAAG,CAAC;EACtB,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,MAAMC,gBAAgB,GAAGzD,MAAM,CAACiB,kBAAkB,KAAK,CAAC;EACxD,OAAO,IAAI,EAAE;IACX,IAAI;MACF,MAAM;QACJyC,KAAK;QACLC;MACF,CAAC,GAAG,MAAM3D,MAAM,CAACuB,MAAM,CAACqC,IAAI,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;MACVN,eAAe,IAAIK,KAAK,CAACjG,MAAM;MAC/B,IAAIuC,MAAM,CAACiB,kBAAkB,IAAImB,SAAS,EAAE;QAC1C,MAAMyB,OAAO,GAAGR,eAAe,GAAGrD,MAAM,CAACiB,kBAAkB,GAAG,GAAG;QACjE,IAAIjB,MAAM,CAACF,UAAU,IAAI+D,OAAO,GAAGL,oBAAoB,GAAG,CAAC,EAAE;UAC3D,MAAMM,KAAK,GAAG,IAAIC,aAAa,CAAC,UAAU,EAAE;YAC1CN,gBAAgB;YAChB1C,MAAM,EAAEsC,eAAe;YACvBW,KAAK,EAAEhE,MAAM,CAACiB;UAChB,CAAC,CAAC;UACFjB,MAAM,CAACF,UAAU,CAACgE,KAAK,CAAC;UACxBN,oBAAoB,GAAGK,OAAO;QAChC;MACF;MACAN,MAAM,CAACU,IAAI,CAACP,KAAK,CAAC;MAClB,MAAMQ,YAAY,GAAGb,eAAe,GAAGC,cAAc;MACrD,IAAItD,MAAM,CAACiB,kBAAkB,IAAImB,SAAS,IAAI8B,YAAY,GAAGlE,MAAM,CAACmB,SAAS,GAAGnB,MAAM,CAACN,SAAS,EAAE;QAChG,IAAIlC,WAAW,GAAG8E,IAAI,CAACC,KAAK,CAAC2B,YAAY,GAAGlE,MAAM,CAACmB,SAAS,CAAC;QAC7D,MAAMgD,wBAAwB,GAAG,IAAIC,UAAU,CAACF,YAAY,CAAC;QAC7D,IAAI9G,MAAM,GAAG,CAAC;QACd,KAAK,MAAMiH,KAAK,IAAId,MAAM,EAAE;UAC1BY,wBAAwB,CAAClF,GAAG,CAACoF,KAAK,EAAEjH,MAAM,CAAC;UAC3CA,MAAM,IAAIiH,KAAK,CAAC5G,MAAM;QACxB;QACA8F,MAAM,CAAC9F,MAAM,GAAG,CAAC;QACjB,IAAIyG,YAAY,GAAG1G,WAAW,GAAGwC,MAAM,CAACmB,SAAS,EAAE;UACjD,MAAMmD,UAAU,GAAG,IAAIF,UAAU,CAACF,YAAY,GAAG1G,WAAW,GAAGwC,MAAM,CAACmB,SAAS,CAAC;UAChFmD,UAAU,CAACrF,GAAG,CAACkF,wBAAwB,CAACI,QAAQ,CAACL,YAAY,GAAGI,UAAU,CAAC7G,MAAM,EAAEyG,YAAY,CAAC,EAAE,CAAC,CAAC;UACpGX,MAAM,CAACU,IAAI,CAACK,UAAU,CAAC;QACzB;QACA,MAAMpG,MAAM,GAAG,IAAIkG,UAAU,CAAC5G,WAAW,GAAGwC,MAAM,CAACmB,SAAS,CAAC;QAC7DjD,MAAM,CAACe,GAAG,CAACkF,wBAAwB,CAACI,QAAQ,CAAC,CAAC,EAAErG,MAAM,CAACsG,UAAU,CAAC,EAAE,CAAC,CAAC;QACtE,MAAMrH,QAAQ,GAAGsH,cAAc,CAACzE,MAAM,EAAE9B,MAAM,CAACA,MAAM,EAAEV,WAAW,CAAC;QACnEwC,MAAM,CAACG,MAAM,CAAChB,WAAW,CAAC;UACxBJ,MAAM,EAAE,MAAM;UACd2F,GAAG,EAAE1E,MAAM,CAACJ,GAAG;UACfnC,MAAM,EAAEuC,MAAM,CAACkB,WAAW,GAAG,EAAE;UAC/B/D,QAAQ,EAAEA,QAAQ,CAACe;QACrB,CAAC,EAAE,CAACf,QAAQ,CAACe,MAAM,CAAC,CAAC;QACrBoF,cAAc,IAAI9F,WAAW,GAAGwC,MAAM,CAACmB,SAAS;QAChD,IAAInB,MAAM,CAACF,UAAU,EAAE;UACrB,MAAMgE,KAAK,GAAG,IAAIC,aAAa,CAAC,UAAU,EAAE;YAC1CN,gBAAgB;YAChB1C,MAAM,EAAEf,MAAM,CAACiB,kBAAkB;YACjC+C,KAAK,EAAEhE,MAAM,CAACiB;UAChB,CAAC,CAAC;UACFjB,MAAM,CAACF,UAAU,CAACgE,KAAK,CAAC;QAC1B;MACF;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;MACpB;IACF;EACF;EACA,IAAItB,eAAe,GAAGC,cAAc,GAAG,CAAC,EAAE;IACxC;IACA,IAAIuB,kBAAkB,GAAG,IAAIT,UAAU,CAACb,MAAM,CAACuB,MAAM,CAAC,CAACC,GAAG,EAAEV,KAAK,KAAKU,GAAG,GAAGV,KAAK,CAAC5G,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7F,IAAIL,MAAM,GAAG,CAAC;IACd,KAAK,MAAMiH,KAAK,IAAId,MAAM,EAAE;MAC1BsB,kBAAkB,CAAC5F,GAAG,CAACoF,KAAK,EAAEjH,MAAM,CAAC;MACrCA,MAAM,IAAIiH,KAAK,CAAC5G,MAAM;IACxB;IACA,IAAIyD,WAAW,GAAGoB,IAAI,CAACC,KAAK,CAACsC,kBAAkB,CAACL,UAAU,GAAGxE,MAAM,CAACmB,SAAS,CAAC;IAC9E,MAAMhE,QAAQ,GAAGsH,cAAc,CAACzE,MAAM,EAAE6E,kBAAkB,CAAC3G,MAAM,EAAEgD,WAAW,CAAC;IAC/ElB,MAAM,CAACG,MAAM,CAAChB,WAAW,CAAC;MACxBJ,MAAM,EAAE,MAAM;MACd2F,GAAG,EAAE1E,MAAM,CAACJ,GAAG;MACfnC,MAAM,EAAEyD,WAAW,GAAG,EAAE;MACxB/D,QAAQ,EAAEA,QAAQ,CAACe;IACrB,CAAC,EAAE,CAACf,QAAQ,CAACe,MAAM,CAAC,CAAC;EACvB;EACA8B,MAAM,CAACe,MAAM,GAAG,IAAI;EACpBf,MAAM,CAACC,OAAO,CAAC+E,OAAO,CAAChF,MAAM,CAACJ,GAAG,CAAC;AACpC;AACA,SAASc,MAAMA,CAACE,MAAM,EAAEZ,MAAM,EAAEW,MAAM,EAAEpD,MAAM,EAAE;EAC9CqD,MAAM,CAACqE,iBAAiB,CAAC,CAAC;EAC1BjF,MAAM,CAACP,EAAE,CAACyF,kBAAkB,CAACvE,MAAM,CAAC/D,QAAQ,CAAC;EAC7C;EACA+D,MAAM,CAACwE,QAAQ,CAACvI,QAAQ,CAACwI,CAAC,GAAGzE,MAAM,CAAC/D,QAAQ,CAACyI,CAAC;EAC9C;EACA1E,MAAM,CAACwE,QAAQ,CAACvI,QAAQ,CAAC0I,CAAC,GAAG3E,MAAM,CAAC/D,QAAQ,CAAC2I,CAAC;EAC9C5E,MAAM,CAACwE,QAAQ,CAACrI,KAAK,GAAG6D,MAAM,CAAC/D,QAAQ,CAAC2I,CAAC,GAAG,GAAG,GAAGjD,IAAI,CAACkD,GAAG,CAAC5E,MAAM,CAAC6E,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC/F,IAAI/E,MAAM,CAACgF,KAAK,EAAE;IAChB,IAAIpI,MAAM,IAAIoD,MAAM,CAACiF,MAAM,EAAE;IAC7BjF,MAAM,CAACgF,KAAK,GAAG,KAAK;IACpB,MAAMrI,IAAI,GAAG,IAAIS,YAAY,CAAC,CAAC4C,MAAM,CAACkF,eAAe,CAACH,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC/E,MAAM,CAACkF,eAAe,CAACH,QAAQ,CAAC,CAAC,CAAC,EAAE/E,MAAM,CAACkF,eAAe,CAACH,QAAQ,CAAC,EAAE,CAAC,EAAE/E,MAAM,CAACkF,eAAe,CAACH,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAClL1F,MAAM,CAACG,MAAM,CAAChB,WAAW,CAAC;MACxBJ,MAAM,EAAE,MAAM;MACd2F,GAAG,EAAE1E,MAAM,CAACJ,GAAG;MACfR,GAAG,EAAEuB,MAAM,CAACmF,IAAI;MAChBxI,IAAI,EAAEA,IAAI,CAACY,MAAM;MACjBX;IACF,CAAC,EAAE,CAACD,IAAI,CAACY,MAAM,CAAC,CAAC;IACjB,IAAIX,MAAM,IAAIyC,MAAM,CAACe,MAAM,EAAEJ,MAAM,CAACiF,MAAM,GAAG,IAAI;EACnD;AACF;AACA,SAAS/E,OAAOA,CAACb,MAAM,EAAEW,MAAM,EAAE;EAC/B,IAAI,CAACX,MAAM,CAACc,OAAO,EAAEsC,QAAQ,CAACpD,MAAM,CAAC;EACrCW,MAAM,CAACgF,KAAK,GAAG,KAAK;EACpBhF,MAAM,CAACoF,EAAE,GAAG,IAAI7J,KAAK,CAAC8J,OAAO,CAAC,CAAC;EAC/BrF,MAAM,CAACsF,GAAG,GAAG,IAAI/J,KAAK,CAAC8J,OAAO,CAAC,CAAC;EAChCrF,MAAM,CAACuF,GAAG,GAAG,IAAIhK,KAAK,CAAC8J,OAAO,CAAC,CAAC;EAChCrF,MAAM,CAAC/D,QAAQ,GAAG,IAAIV,KAAK,CAACiK,OAAO,CAAC,CAAC;EACrC,IAAIC,eAAe,GAAG,IAAI3H,WAAW,CAACuB,MAAM,CAACqB,kBAAkB,GAAGrB,MAAM,CAACsB,mBAAmB,CAAC;EAC7F,MAAM+E,YAAY,GAAG,IAAInK,KAAK,CAACoK,wBAAwB,CAACF,eAAe,EAAE,CAAC,EAAE,KAAK,CAAC;EAClFC,YAAY,CAACE,QAAQ,CAACrK,KAAK,CAACsK,gBAAgB,CAAC;EAC7C,MAAMC,QAAQ,GAAG9F,MAAM,CAAC8F,QAAQ,GAAG,IAAIvK,KAAK,CAACwK,uBAAuB,CAAC,CAAC;EACtE,MAAMC,cAAc,GAAG,IAAI5I,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9C,MAAM6I,SAAS,GAAG,IAAI1K,KAAK,CAAC2K,eAAe,CAACF,cAAc,EAAE,CAAC,CAAC;EAC9DF,QAAQ,CAACK,YAAY,CAAC,UAAU,EAAEF,SAAS,CAAC;EAC5CA,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnCH,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAClCH,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpCH,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpCH,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAClCH,SAAS,CAACG,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACnCH,SAAS,CAAC5D,WAAW,GAAG,IAAI;EAC5ByD,QAAQ,CAACK,YAAY,CAAC,YAAY,EAAET,YAAY,CAAC;EACjDI,QAAQ,CAACO,aAAa,GAAG,CAAC;EAC1B,SAASC,QAAQA,CAACpI,CAAC,EAAE;IACnB,IAAI8B,MAAM,IAAI9B,CAAC,CAACC,IAAI,CAACM,GAAG,KAAKuB,MAAM,CAACmF,IAAI,EAAE;MACxC,IAAIoB,OAAO,GAAG,IAAIzI,WAAW,CAACI,CAAC,CAACC,IAAI,CAACI,OAAO,CAAC;MAC7C;MACAuH,QAAQ,CAACU,UAAU,CAACC,UAAU,CAACnI,GAAG,CAACiI,OAAO,CAAC;MAC3CT,QAAQ,CAACU,UAAU,CAACC,UAAU,CAACpE,WAAW,GAAG,IAAI;MACjDyD,QAAQ,CAACO,aAAa,GAAGE,OAAO,CAACzJ,MAAM;MACvCkD,MAAM,CAACgF,KAAK,GAAG,IAAI;IACrB;EACF;EACA3F,MAAM,CAACG,MAAM,CAACkH,gBAAgB,CAAC,SAAS,EAAEJ,QAAQ,CAAC;EACnD,eAAeK,IAAIA,CAAA,EAAG;IACpB,OAAO,IAAI,EAAE;MACX,MAAMC,+BAA+B,GAAGvH,MAAM,CAACP,EAAE,CAAC+H,UAAU,CAACtF,GAAG,CAAClC,MAAM,CAACjD,qBAAqB,CAAC;MAC9F,MAAM0K,4BAA4B,GAAGzH,MAAM,CAACP,EAAE,CAAC+H,UAAU,CAACtF,GAAG,CAAClC,MAAM,CAAChD,kBAAkB,CAAC;MACxF,IAAIuK,+BAA+B,IAAI,IAAI,IAAIA,+BAA+B,CAACG,cAAc,IAAID,4BAA4B,IAAI,IAAI,IAAIA,4BAA4B,CAACC,cAAc,IAAI1H,MAAM,CAACgB,iBAAiB,GAAG,CAAC,EAAE;MACtN,MAAM,IAAI2G,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;IACvD;IACAjH,MAAM,CAACgF,KAAK,GAAG,IAAI;EACrB;EACA2B,IAAI,CAAC,CAAC;EACN,OAAO,MAAMtH,MAAM,CAACG,MAAM,CAAC2H,mBAAmB,CAAC,SAAS,EAAEb,QAAQ,CAAC;AACrE;AACA,SAASxC,cAAcA,CAACzE,MAAM,EAAE9B,MAAM,EAAEV,WAAW,EAAE;EACnD,MAAMgF,OAAO,GAAGxC,MAAM,CAACP,EAAE,CAACgD,UAAU,CAAC,CAAC;EACtC,IAAIzC,MAAM,CAACgB,iBAAiB,GAAGxD,WAAW,GAAGwC,MAAM,CAACoB,WAAW,EAAE5D,WAAW,GAAGwC,MAAM,CAACoB,WAAW,GAAGpB,MAAM,CAACgB,iBAAiB;EAC5H,IAAIxD,WAAW,IAAI,CAAC,EAAE,MAAM,sBAAsB;EAClD,MAAMuK,QAAQ,GAAG,IAAI3D,UAAU,CAAClG,MAAM,CAAC;EACvC,MAAM8J,QAAQ,GAAG,IAAIjK,YAAY,CAACG,MAAM,CAAC;EACzC,MAAMf,QAAQ,GAAG,IAAIY,YAAY,CAACP,WAAW,GAAG,EAAE,CAAC;EACnD,MAAMyK,qBAAqB,GAAG,IAAI7D,UAAU,CAACpE,MAAM,CAACyB,eAAe,CAACvD,MAAM,CAAC;EAC3E,MAAMgK,qBAAqB,GAAG,IAAIC,UAAU,CAACnI,MAAM,CAACyB,eAAe,CAACvD,MAAM,CAAC;EAC3E,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,WAAW,EAAEa,CAAC,EAAE,EAAE;IACpC,MAAM+J,IAAI,GAAG,IAAIlM,KAAK,CAACmM,UAAU,CAAC,EAAEN,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC0J,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC0J,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,EAAE0J,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;IAC/M+J,IAAI,CAACE,MAAM,CAAC,CAAC;IACb,MAAMC,MAAM,GAAG,IAAIrM,KAAK,CAACsM,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,CAAC,EAAE2J,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC2J,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,CAAC,CAAC;IAChG,MAAMoK,KAAK,GAAG,IAAIvM,KAAK,CAACsM,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE2J,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE2J,QAAQ,CAAC,CAAC,GAAG3J,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1G,MAAMqK,GAAG,GAAG,IAAIxM,KAAK,CAAC8J,OAAO,CAAC,CAAC;IAC/B0C,GAAG,CAACC,0BAA0B,CAACP,IAAI,CAAC;IACpCM,GAAG,CAACE,SAAS,CAAC,CAAC;IACfF,GAAG,CAACD,KAAK,CAACA,KAAK,CAAC;IAChB,MAAMI,KAAK,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;IACzBJ,GAAG,CAACE,SAAS,CAAC,CAAC;IACfF,GAAG,CAACK,WAAW,CAACF,KAAK,CAAC;IACtBH,GAAG,CAACM,WAAW,CAACT,MAAM,CAAC;IACvB,MAAMU,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACvC,IAAIC,SAAS,GAAG,GAAG;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACxL,MAAM,EAAE0L,CAAC,EAAE,EAAE,IAAI7G,IAAI,CAACkD,GAAG,CAACkD,GAAG,CAAChD,QAAQ,CAACuD,WAAW,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGD,SAAS,EAAEA,SAAS,GAAG5G,IAAI,CAACkD,GAAG,CAACkD,GAAG,CAAChD,QAAQ,CAACuD,WAAW,CAACE,CAAC,CAAC,CAAC,CAAC;IACvJ,IAAIC,UAAU,GAAGpJ,MAAM,CAACgB,iBAAiB,GAAG,CAAC,GAAG3C,CAAC,GAAG,CAAC;IACrD2B,MAAM,CAACwB,kBAAkB,CAAC4H,UAAU,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACnD,CAAC;IACpDpF,MAAM,CAACwB,kBAAkB,CAAC4H,UAAU,GAAG,CAAC,CAAC,GAAG,CAACb,MAAM,CAACjD,CAAC;IACrDtF,MAAM,CAACwB,kBAAkB,CAAC4H,UAAU,GAAG,CAAC,CAAC,GAAGb,MAAM,CAAClD,CAAC;IACpDrF,MAAM,CAACwB,kBAAkB,CAAC4H,UAAU,GAAG,CAAC,CAAC,GAAGF,SAAS,GAAG,OAAO;IAC/DE,UAAU,GAAGpJ,MAAM,CAACgB,iBAAiB,GAAG,CAAC,GAAG3C,CAAC,GAAG,CAAC,GAAG,CAAC;IACrD,KAAK,IAAI8K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACxL,MAAM,EAAE0L,CAAC,EAAE,EAAEjB,qBAAqB,CAACkB,UAAU,GAAGD,CAAC,CAAC,GAAGT,GAAG,CAAChD,QAAQ,CAACuD,WAAW,CAACE,CAAC,CAAC,CAAC,GAAG,OAAO,GAAGD,SAAS;;IAEvI;IACAE,UAAU,GAAGpJ,MAAM,CAACgB,iBAAiB,GAAG,EAAE,GAAG,CAAC3C,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;IAC5D,MAAMgL,GAAG,GAAG,IAAInN,KAAK,CAACoN,KAAK,CAACvB,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE0J,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE0J,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAC9HgL,GAAG,CAACE,mBAAmB,CAAC,CAAC;IACzBtB,qBAAqB,CAACmB,UAAU,GAAG,CAAC,CAAC,GAAGC,GAAG,CAACG,CAAC,GAAG,GAAG;IACnDvB,qBAAqB,CAACmB,UAAU,GAAG,CAAC,CAAC,GAAGC,GAAG,CAACI,CAAC,GAAG,GAAG;IACnDxB,qBAAqB,CAACmB,UAAU,GAAG,CAAC,CAAC,GAAGC,GAAG,CAACK,CAAC,GAAG,GAAG;IACnDzB,qBAAqB,CAACmB,UAAU,GAAG,CAAC,CAAC,GAAGrB,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;;IAEjE;IACAqK,GAAG,CAAChD,QAAQ,CAAC,EAAE,CAAC,GAAGpD,IAAI,CAACqH,GAAG,CAAClB,KAAK,CAACrD,CAAC,EAAEqD,KAAK,CAACnD,CAAC,EAAEmD,KAAK,CAACpD,CAAC,CAAC,GAAG0C,QAAQ,CAAC,EAAE,GAAG1J,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK;IAC1F,KAAK,IAAI8K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEhM,QAAQ,CAACkB,CAAC,GAAG,EAAE,GAAG8K,CAAC,CAAC,GAAGT,GAAG,CAAChD,QAAQ,CAACyD,CAAC,CAAC;EACrE;EACA,OAAO3L,WAAW,GAAG,CAAC,EAAE;IACtB,IAAIoM,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAG,CAAC;IACd,MAAMC,OAAO,GAAG9J,MAAM,CAACgB,iBAAiB,GAAGhB,MAAM,CAACqB,kBAAkB;IACpE,MAAM0I,OAAO,GAAGzH,IAAI,CAACC,KAAK,CAACvC,MAAM,CAACgB,iBAAiB,GAAGhB,MAAM,CAACqB,kBAAkB,CAAC;IAChF,IAAIrB,MAAM,CAACgB,iBAAiB,GAAGhB,MAAM,CAACqB,kBAAkB,IAAI,CAAC,EAAE;MAC7DuI,KAAK,GAAGtH,IAAI,CAAC0H,GAAG,CAAChK,MAAM,CAACqB,kBAAkB,EAAEyI,OAAO,GAAGtM,WAAW,CAAC,GAAGsM,OAAO;MAC5ED,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAIvH,IAAI,CAACC,KAAK,CAAC/E,WAAW,GAAGwC,MAAM,CAACqB,kBAAkB,CAAC,GAAG,CAAC,EAAE;MAClEuI,KAAK,GAAG5J,MAAM,CAACqB,kBAAkB;MACjCwI,MAAM,GAAGvH,IAAI,CAACC,KAAK,CAAC/E,WAAW,GAAGwC,MAAM,CAACqB,kBAAkB,CAAC;IAC9D,CAAC,MAAM;MACLuI,KAAK,GAAGpM,WAAW,GAAGwC,MAAM,CAACqB,kBAAkB;MAC/CwI,MAAM,GAAG,CAAC;IACZ;IACA,MAAMtC,+BAA+B,GAAGvH,MAAM,CAACP,EAAE,CAAC+H,UAAU,CAACtF,GAAG,CAAClC,MAAM,CAACjD,qBAAqB,CAAC;IAC9FyF,OAAO,CAACyH,WAAW,CAACzH,OAAO,CAAC0H,UAAU,EAAE3C,+BAA+B,CAACG,cAAc,CAAC;IACvFlF,OAAO,CAAC2H,aAAa,CAAC3H,OAAO,CAAC0H,UAAU,EAAE,CAAC,EAAEJ,OAAO,EAAEC,OAAO,EAAEH,KAAK,EAAEC,MAAM,EAAErH,OAAO,CAAC4H,IAAI,EAAE5H,OAAO,CAAC6H,KAAK,EAAErK,MAAM,CAACwB,kBAAkB,EAAExB,MAAM,CAACgB,iBAAiB,GAAG,CAAC,CAAC;IACnK,MAAMyG,4BAA4B,GAAGzH,MAAM,CAACP,EAAE,CAAC+H,UAAU,CAACtF,GAAG,CAAClC,MAAM,CAAChD,kBAAkB,CAAC;IACxFwF,OAAO,CAACyH,WAAW,CAACzH,OAAO,CAAC0H,UAAU,EAAEzC,4BAA4B,CAACC,cAAc,CAAC;IACpFlF,OAAO,CAAC2H,aAAa,CAAC3H,OAAO,CAAC0H,UAAU,EAAE,CAAC,EAAEJ,OAAO,EAAEC,OAAO,EAAEH,KAAK,EAAEC,MAAM;IAC5E;IACArH,OAAO,CAAC8H,YAAY,EAAE9H,OAAO,CAAC+H,YAAY,EAAEvK,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACgB,iBAAiB,GAAG,CAAC,CAAC;IACjGhB,MAAM,CAACP,EAAE,CAAC+K,UAAU,CAAC,CAAC;IACtBxK,MAAM,CAACgB,iBAAiB,IAAI4I,KAAK,GAAGC,MAAM;IAC1CrM,WAAW,IAAIoM,KAAK,GAAGC,MAAM;EAC/B;EACA,OAAO1M,QAAQ;AACjB;AACA,SAASsN,KAAKA,CAAC;EACb/F,GAAG;EACHgG,UAAU,GAAG,KAAK;EAClB/N,SAAS,GAAG,CAAC;EACbgO,SAAS,GAAG,KAAK;EACjBjL,SAAS,GAAG,KAAK;EACjB,GAAGkL;AACL,CAAC,EAAE;EACDxO,MAAM,CAAC;IACLM;EACF,CAAC,CAAC;EACF,MAAMmO,GAAG,GAAG1O,KAAK,CAAC2O,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMrL,EAAE,GAAGpD,QAAQ,CAAC0O,KAAK,IAAIA,KAAK,CAACtL,EAAE,CAAC;EACtC,MAAMmB,MAAM,GAAGvE,QAAQ,CAAC0O,KAAK,IAAIA,KAAK,CAACnK,MAAM,CAAC;;EAE9C;EACA,MAAMZ,MAAM,GAAG1D,SAAS,CAAC+C,WAAW,EAAEqF,GAAG,EAAEsG,MAAM,IAAI;IACnDA,MAAM,CAACvL,EAAE,GAAGA,EAAE;IACduL,MAAM,CAACtL,SAAS,GAAGA,SAAS;EAC9B,CAAC,CAAC;;EAEF;EACAvD,KAAK,CAAC8O,eAAe,CAAC,MAAMjL,MAAM,CAACa,OAAO,CAACgK,GAAG,CAACK,OAAO,CAAC,EAAE,CAACxG,GAAG,CAAC,CAAC;EAC/D;EACAnI,QAAQ,CAAC,MAAMyD,MAAM,CAACU,MAAM,CAACmK,GAAG,CAACK,OAAO,EAAEtK,MAAM,EAAE+J,SAAS,CAAC,CAAC;EAC7D,OAAO,aAAaxO,KAAK,CAACgP,aAAa,CAAC,MAAM,EAAElP,QAAQ,CAAC;IACvD4O,GAAG,EAAEA,GAAG;IACRO,aAAa,EAAE;EACjB,CAAC,EAAER,KAAK,CAAC,EAAE,aAAazO,KAAK,CAACgP,aAAa,CAAC,eAAe,EAAE;IAC3D/L,GAAG,EAAE,GAAGsF,GAAG,IAAI/H,SAAS,IAAIgO,SAAS,GAAGjO,aAAa,CAAC0C,GAAG,EAAE;IAC3DiM,WAAW,EAAE,CAACV,SAAS;IACvBW,SAAS,EAAE,IAAI;IACf3O,SAAS,EAAEgO,SAAS,GAAG,CAAC,GAAGhO,SAAS;IACpCI,qBAAqB,EAAEiD,MAAM,CAACjD,qBAAqB;IACnDC,kBAAkB,EAAEgD,MAAM,CAAChD,kBAAkB;IAC7CuO,UAAU,EAAEZ,SAAS,GAAG,IAAI,GAAGhO,SAAS,GAAG,CAAC;IAC5C6O,QAAQ,EAAEb,SAAS,GAAGzO,KAAK,CAACuP,cAAc,GAAGvP,KAAK,CAACwP,cAAc;IACjEC,aAAa,EAAEzP,KAAK,CAAC0P,SAAS;IAC9BjB,SAAS,EAAE,CAAC,CAACA,SAAS;IACtBD,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC;AACL;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}