{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { UniformsUtils, WebGLRenderTarget, LinearFilter, NearestFilter, RGBAFormat, ShaderMaterial, MeshBasicMaterial } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { AfterimageShader } from \"../shaders/AfterimageShader.js\";\nclass AfterimagePass extends Pass {\n  constructor(damp = 0.96, shader = AfterimageShader) {\n    super();\n    __publicField(this, \"shader\");\n    __publicField(this, \"uniforms\");\n    __publicField(this, \"textureComp\");\n    __publicField(this, \"textureOld\");\n    __publicField(this, \"shaderMaterial\");\n    __publicField(this, \"compFsQuad\");\n    __publicField(this, \"copyFsQuad\");\n    this.shader = shader;\n    this.uniforms = UniformsUtils.clone(shader.uniforms);\n    this.uniforms[\"damp\"].value = damp;\n    this.textureComp = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat\n    });\n    this.textureOld = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat\n    });\n    this.shaderMaterial = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: this.shader.vertexShader,\n      fragmentShader: this.shader.fragmentShader\n    });\n    this.compFsQuad = new FullScreenQuad(this.shaderMaterial);\n    let material = new MeshBasicMaterial();\n    this.copyFsQuad = new FullScreenQuad(material);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.uniforms[\"tOld\"].value = this.textureOld.texture;\n    this.uniforms[\"tNew\"].value = readBuffer.texture;\n    renderer.setRenderTarget(this.textureComp);\n    this.compFsQuad.render(renderer);\n    this.copyFsQuad.material.map = this.textureComp.texture;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.copyFsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.copyFsQuad.render(renderer);\n    }\n    let temp = this.textureOld;\n    this.textureOld = this.textureComp;\n    this.textureComp = temp;\n  }\n  setSize(width, height) {\n    this.textureComp.setSize(width, height);\n    this.textureOld.setSize(width, height);\n  }\n}\nexport { AfterimagePass };", "map": {"version": 3, "names": ["AfterimagePass", "Pass", "constructor", "damp", "shader", "AfterimageShader", "__publicField", "uniforms", "UniformsUtils", "clone", "value", "textureComp", "WebGLRenderTarget", "window", "innerWidth", "innerHeight", "minFilter", "LinearFilter", "magFilter", "NearestFilter", "format", "RGBAFormat", "textureOld", "shaderMaterial", "ShaderMaterial", "vertexShader", "fragmentShader", "compFsQuad", "FullScreenQuad", "material", "MeshBasicMaterial", "copyFsQuad", "render", "renderer", "writeBuffer", "readBuffer", "texture", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "renderToScreen", "clear", "temp", "setSize", "width", "height"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/AfterimagePass.ts"], "sourcesContent": ["import {\n  LinearFilter,\n  MeshBasicMaterial,\n  NearestFilter,\n  RGBAFormat,\n  WebGLRenderer,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { AfterimageShader } from '../shaders/AfterimageShader'\n\nclass AfterimagePass extends Pass {\n  public shader\n  public uniforms\n  public textureComp: WebGLRenderTarget\n  public textureOld: WebGLRenderTarget\n  public shaderMaterial: ShaderMaterial\n  public compFsQuad: FullScreenQuad<ShaderMaterial>\n  public copyFsQuad: FullScreenQuad<MeshBasicMaterial>\n\n  constructor(damp = 0.96, shader = AfterimageShader) {\n    super()\n\n    this.shader = shader\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n    this.uniforms['damp'].value = damp\n\n    this.textureComp = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat,\n    })\n\n    this.textureOld = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat,\n    })\n\n    this.shaderMaterial = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: this.shader.vertexShader,\n      fragmentShader: this.shader.fragmentShader,\n    })\n\n    this.compFsQuad = new FullScreenQuad(this.shaderMaterial)\n\n    let material = new MeshBasicMaterial()\n    this.copyFsQuad = new FullScreenQuad(material)\n  }\n\n  public render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget): void {\n    this.uniforms['tOld'].value = this.textureOld.texture\n    this.uniforms['tNew'].value = readBuffer.texture\n\n    renderer.setRenderTarget(this.textureComp)\n    this.compFsQuad.render(renderer)\n\n    this.copyFsQuad.material.map = this.textureComp.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.copyFsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n\n      if (this.clear) renderer.clear()\n\n      this.copyFsQuad.render(renderer)\n    }\n\n    // Swap buffers.\n    let temp = this.textureOld\n    this.textureOld = this.textureComp\n    this.textureComp = temp\n    // Now textureOld contains the latest image, ready for the next frame.\n  }\n\n  public setSize(width: number, height: number): void {\n    this.textureComp.setSize(width, height)\n    this.textureOld.setSize(width, height)\n  }\n}\n\nexport { AfterimagePass }\n"], "mappings": ";;;;;;;;;;;;;;AAaA,MAAMA,cAAA,SAAuBC,IAAA,CAAK;EAShCC,YAAYC,IAAA,GAAO,MAAMC,MAAA,GAASC,gBAAA,EAAkB;IAC5C;IATDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAKL,KAAKF,MAAA,GAASA,MAAA;IACd,KAAKG,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAML,MAAA,CAAOG,QAAQ;IAC9C,KAAAA,QAAA,CAAS,MAAM,EAAEG,KAAA,GAAQP,IAAA;IAE9B,KAAKQ,WAAA,GAAc,IAAIC,iBAAA,CAAkBC,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAA,EAAa;MAC9EC,SAAA,EAAWC,YAAA;MACXC,SAAA,EAAWC,aAAA;MACXC,MAAA,EAAQC;IAAA,CACT;IAED,KAAKC,UAAA,GAAa,IAAIV,iBAAA,CAAkBC,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAA,EAAa;MAC7EC,SAAA,EAAWC,YAAA;MACXC,SAAA,EAAWC,aAAA;MACXC,MAAA,EAAQC;IAAA,CACT;IAEI,KAAAE,cAAA,GAAiB,IAAIC,cAAA,CAAe;MACvCjB,QAAA,EAAU,KAAKA,QAAA;MACfkB,YAAA,EAAc,KAAKrB,MAAA,CAAOqB,YAAA;MAC1BC,cAAA,EAAgB,KAAKtB,MAAA,CAAOsB;IAAA,CAC7B;IAED,KAAKC,UAAA,GAAa,IAAIC,cAAA,CAAe,KAAKL,cAAc;IAEpD,IAAAM,QAAA,GAAW,IAAIC,iBAAA;IACd,KAAAC,UAAA,GAAa,IAAIH,cAAA,CAAeC,QAAQ;EAC/C;EAEOG,OAAOC,QAAA,EAAyBC,WAAA,EAAgCC,UAAA,EAAqC;IAC1G,KAAK5B,QAAA,CAAS,MAAM,EAAEG,KAAA,GAAQ,KAAKY,UAAA,CAAWc,OAAA;IAC9C,KAAK7B,QAAA,CAAS,MAAM,EAAEG,KAAA,GAAQyB,UAAA,CAAWC,OAAA;IAEhCH,QAAA,CAAAI,eAAA,CAAgB,KAAK1B,WAAW;IACpC,KAAAgB,UAAA,CAAWK,MAAA,CAAOC,QAAQ;IAE/B,KAAKF,UAAA,CAAWF,QAAA,CAASS,GAAA,GAAM,KAAK3B,WAAA,CAAYyB,OAAA;IAEhD,IAAI,KAAKG,cAAA,EAAgB;MACvBN,QAAA,CAASI,eAAA,CAAgB,IAAI;MACxB,KAAAN,UAAA,CAAWC,MAAA,CAAOC,QAAQ;IAAA,OAC1B;MACLA,QAAA,CAASI,eAAA,CAAgBH,WAAW;MAEpC,IAAI,KAAKM,KAAA,EAAOP,QAAA,CAASO,KAAA,CAAM;MAE1B,KAAAT,UAAA,CAAWC,MAAA,CAAOC,QAAQ;IACjC;IAGA,IAAIQ,IAAA,GAAO,KAAKnB,UAAA;IAChB,KAAKA,UAAA,GAAa,KAAKX,WAAA;IACvB,KAAKA,WAAA,GAAc8B,IAAA;EAErB;EAEOC,QAAQC,KAAA,EAAeC,MAAA,EAAsB;IAC7C,KAAAjC,WAAA,CAAY+B,OAAA,CAAQC,KAAA,EAAOC,MAAM;IACjC,KAAAtB,UAAA,CAAWoB,OAAA,CAAQC,KAAA,EAAOC,MAAM;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}