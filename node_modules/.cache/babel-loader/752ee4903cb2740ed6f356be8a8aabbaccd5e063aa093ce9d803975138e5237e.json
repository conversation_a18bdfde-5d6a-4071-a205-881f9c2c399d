{"ast": null, "code": "import { Loader, LoaderUtils, FileLoader, TextureLoader, RepeatWrapping, ClampToEdgeWrapping, Texture, MeshPhongMaterial, MeshLambertMaterial, Color, EquirectangularReflectionMapping, Matrix4, Group, Bone, PropertyBinding, Object3D, OrthographicCamera, PerspectiveCamera, PointLight, MathUtils, SpotLight, DirectionalLight, SkinnedMesh, Mesh, LineBasicMaterial, Line, Vector3, Skeleton, AmbientLight, BufferGeometry, Float32BufferAttribute, Uint16BufferAttribute, Matrix3, Vector4, AnimationClip, Quaternion, Euler, VectorKeyframeTrack, QuaternionKeyframeTrack, NumberKeyframeTrack } from \"three\";\nimport { unzlibSync } from \"fflate\";\nimport { NURBSCurve } from \"../curves/NURBSCurve.js\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nlet fbxTree;\nlet connections;\nlet sceneGraph;\nclass FBXLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = scope.path === \"\" ? LoaderUtils.extractUrlBase(url) : scope.path;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(FBXBuffer, path) {\n    if (isFbxFormatBinary(FBXBuffer)) {\n      fbxTree = new BinaryParser().parse(FBXBuffer);\n    } else {\n      const FBXText = convertArrayBufferToString(FBXBuffer);\n      if (!isFbxFormatASCII(FBXText)) {\n        throw new Error(\"THREE.FBXLoader: Unknown format.\");\n      }\n      if (getFbxVersion(FBXText) < 7e3) {\n        throw new Error(\"THREE.FBXLoader: FBX version not supported, FileVersion: \" + getFbxVersion(FBXText));\n      }\n      fbxTree = new TextParser().parse(FBXText);\n    }\n    const textureLoader = new TextureLoader(this.manager).setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    return new FBXTreeParser(textureLoader, this.manager).parse(fbxTree);\n  }\n}\nclass FBXTreeParser {\n  constructor(textureLoader, manager) {\n    this.textureLoader = textureLoader;\n    this.manager = manager;\n  }\n  parse() {\n    connections = this.parseConnections();\n    const images = this.parseImages();\n    const textures = this.parseTextures(images);\n    const materials = this.parseMaterials(textures);\n    const deformers = this.parseDeformers();\n    const geometryMap = new GeometryParser().parse(deformers);\n    this.parseScene(deformers, geometryMap, materials);\n    return sceneGraph;\n  }\n  // Parses FBXTree.Connections which holds parent-child connections between objects (e.g. material -> texture, model->geometry )\n  // and details the connection type\n  parseConnections() {\n    const connectionMap = /* @__PURE__ */new Map();\n    if (\"Connections\" in fbxTree) {\n      const rawConnections = fbxTree.Connections.connections;\n      rawConnections.forEach(function (rawConnection) {\n        const fromID = rawConnection[0];\n        const toID = rawConnection[1];\n        const relationship = rawConnection[2];\n        if (!connectionMap.has(fromID)) {\n          connectionMap.set(fromID, {\n            parents: [],\n            children: []\n          });\n        }\n        const parentRelationship = {\n          ID: toID,\n          relationship\n        };\n        connectionMap.get(fromID).parents.push(parentRelationship);\n        if (!connectionMap.has(toID)) {\n          connectionMap.set(toID, {\n            parents: [],\n            children: []\n          });\n        }\n        const childRelationship = {\n          ID: fromID,\n          relationship\n        };\n        connectionMap.get(toID).children.push(childRelationship);\n      });\n    }\n    return connectionMap;\n  }\n  // Parse FBXTree.Objects.Video for embedded image data\n  // These images are connected to textures in FBXTree.Objects.Textures\n  // via FBXTree.Connections.\n  parseImages() {\n    const images = {};\n    const blobs = {};\n    if (\"Video\" in fbxTree.Objects) {\n      const videoNodes = fbxTree.Objects.Video;\n      for (const nodeID in videoNodes) {\n        const videoNode = videoNodes[nodeID];\n        const id = parseInt(nodeID);\n        images[id] = videoNode.RelativeFilename || videoNode.Filename;\n        if (\"Content\" in videoNode) {\n          const arrayBufferContent = videoNode.Content instanceof ArrayBuffer && videoNode.Content.byteLength > 0;\n          const base64Content = typeof videoNode.Content === \"string\" && videoNode.Content !== \"\";\n          if (arrayBufferContent || base64Content) {\n            const image = this.parseImage(videoNodes[nodeID]);\n            blobs[videoNode.RelativeFilename || videoNode.Filename] = image;\n          }\n        }\n      }\n    }\n    for (const id in images) {\n      const filename = images[id];\n      if (blobs[filename] !== void 0) images[id] = blobs[filename];else images[id] = images[id].split(\"\\\\\").pop();\n    }\n    return images;\n  }\n  // Parse embedded image data in FBXTree.Video.Content\n  parseImage(videoNode) {\n    const content = videoNode.Content;\n    const fileName = videoNode.RelativeFilename || videoNode.Filename;\n    const extension = fileName.slice(fileName.lastIndexOf(\".\") + 1).toLowerCase();\n    let type;\n    switch (extension) {\n      case \"bmp\":\n        type = \"image/bmp\";\n        break;\n      case \"jpg\":\n      case \"jpeg\":\n        type = \"image/jpeg\";\n        break;\n      case \"png\":\n        type = \"image/png\";\n        break;\n      case \"tif\":\n        type = \"image/tiff\";\n        break;\n      case \"tga\":\n        if (this.manager.getHandler(\".tga\") === null) {\n          console.warn(\"FBXLoader: TGA loader not found, skipping \", fileName);\n        }\n        type = \"image/tga\";\n        break;\n      default:\n        console.warn('FBXLoader: Image type \"' + extension + '\" is not supported.');\n        return;\n    }\n    if (typeof content === \"string\") {\n      return \"data:\" + type + \";base64,\" + content;\n    } else {\n      const array = new Uint8Array(content);\n      return window.URL.createObjectURL(new Blob([array], {\n        type\n      }));\n    }\n  }\n  // Parse nodes in FBXTree.Objects.Texture\n  // These contain details such as UV scaling, cropping, rotation etc and are connected\n  // to images in FBXTree.Objects.Video\n  parseTextures(images) {\n    const textureMap = /* @__PURE__ */new Map();\n    if (\"Texture\" in fbxTree.Objects) {\n      const textureNodes = fbxTree.Objects.Texture;\n      for (const nodeID in textureNodes) {\n        const texture = this.parseTexture(textureNodes[nodeID], images);\n        textureMap.set(parseInt(nodeID), texture);\n      }\n    }\n    return textureMap;\n  }\n  // Parse individual node in FBXTree.Objects.Texture\n  parseTexture(textureNode, images) {\n    const texture = this.loadTexture(textureNode, images);\n    texture.ID = textureNode.id;\n    texture.name = textureNode.attrName;\n    const wrapModeU = textureNode.WrapModeU;\n    const wrapModeV = textureNode.WrapModeV;\n    const valueU = wrapModeU !== void 0 ? wrapModeU.value : 0;\n    const valueV = wrapModeV !== void 0 ? wrapModeV.value : 0;\n    texture.wrapS = valueU === 0 ? RepeatWrapping : ClampToEdgeWrapping;\n    texture.wrapT = valueV === 0 ? RepeatWrapping : ClampToEdgeWrapping;\n    if (\"Scaling\" in textureNode) {\n      const values = textureNode.Scaling.value;\n      texture.repeat.x = values[0];\n      texture.repeat.y = values[1];\n    }\n    return texture;\n  }\n  // load a texture specified as a blob or data URI, or via an external URL using TextureLoader\n  loadTexture(textureNode, images) {\n    let fileName;\n    const currentPath = this.textureLoader.path;\n    const children = connections.get(textureNode.id).children;\n    if (children !== void 0 && children.length > 0 && images[children[0].ID] !== void 0) {\n      fileName = images[children[0].ID];\n      if (fileName.indexOf(\"blob:\") === 0 || fileName.indexOf(\"data:\") === 0) {\n        this.textureLoader.setPath(void 0);\n      }\n    }\n    let texture;\n    const extension = textureNode.FileName.slice(-3).toLowerCase();\n    if (extension === \"tga\") {\n      const loader = this.manager.getHandler(\".tga\");\n      if (loader === null) {\n        console.warn(\"FBXLoader: TGA loader not found, creating placeholder texture for\", textureNode.RelativeFilename);\n        texture = new Texture();\n      } else {\n        loader.setPath(this.textureLoader.path);\n        texture = loader.load(fileName);\n      }\n    } else if (extension === \"psd\") {\n      console.warn(\"FBXLoader: PSD textures are not supported, creating placeholder texture for\", textureNode.RelativeFilename);\n      texture = new Texture();\n    } else {\n      texture = this.textureLoader.load(fileName);\n    }\n    this.textureLoader.setPath(currentPath);\n    return texture;\n  }\n  // Parse nodes in FBXTree.Objects.Material\n  parseMaterials(textureMap) {\n    const materialMap = /* @__PURE__ */new Map();\n    if (\"Material\" in fbxTree.Objects) {\n      const materialNodes = fbxTree.Objects.Material;\n      for (const nodeID in materialNodes) {\n        const material = this.parseMaterial(materialNodes[nodeID], textureMap);\n        if (material !== null) materialMap.set(parseInt(nodeID), material);\n      }\n    }\n    return materialMap;\n  }\n  // Parse single node in FBXTree.Objects.Material\n  // Materials are connected to texture maps in FBXTree.Objects.Textures\n  // FBX format currently only supports Lambert and Phong shading models\n  parseMaterial(materialNode, textureMap) {\n    const ID = materialNode.id;\n    const name = materialNode.attrName;\n    let type = materialNode.ShadingModel;\n    if (typeof type === \"object\") {\n      type = type.value;\n    }\n    if (!connections.has(ID)) return null;\n    const parameters = this.parseParameters(materialNode, textureMap, ID);\n    let material;\n    switch (type.toLowerCase()) {\n      case \"phong\":\n        material = new MeshPhongMaterial();\n        break;\n      case \"lambert\":\n        material = new MeshLambertMaterial();\n        break;\n      default:\n        console.warn('THREE.FBXLoader: unknown material type \"%s\". Defaulting to MeshPhongMaterial.', type);\n        material = new MeshPhongMaterial();\n        break;\n    }\n    material.setValues(parameters);\n    material.name = name;\n    return material;\n  }\n  // Parse FBX material and return parameters suitable for a three.js material\n  // Also parse the texture map and return any textures associated with the material\n  parseParameters(materialNode, textureMap, ID) {\n    const parameters = {};\n    if (materialNode.BumpFactor) {\n      parameters.bumpScale = materialNode.BumpFactor.value;\n    }\n    if (materialNode.Diffuse) {\n      parameters.color = new Color().fromArray(materialNode.Diffuse.value);\n    } else if (materialNode.DiffuseColor && (materialNode.DiffuseColor.type === \"Color\" || materialNode.DiffuseColor.type === \"ColorRGB\")) {\n      parameters.color = new Color().fromArray(materialNode.DiffuseColor.value);\n    }\n    if (materialNode.DisplacementFactor) {\n      parameters.displacementScale = materialNode.DisplacementFactor.value;\n    }\n    if (materialNode.Emissive) {\n      parameters.emissive = new Color().fromArray(materialNode.Emissive.value);\n    } else if (materialNode.EmissiveColor && (materialNode.EmissiveColor.type === \"Color\" || materialNode.EmissiveColor.type === \"ColorRGB\")) {\n      parameters.emissive = new Color().fromArray(materialNode.EmissiveColor.value);\n    }\n    if (materialNode.EmissiveFactor) {\n      parameters.emissiveIntensity = parseFloat(materialNode.EmissiveFactor.value);\n    }\n    if (materialNode.Opacity) {\n      parameters.opacity = parseFloat(materialNode.Opacity.value);\n    }\n    if (parameters.opacity < 1) {\n      parameters.transparent = true;\n    }\n    if (materialNode.ReflectionFactor) {\n      parameters.reflectivity = materialNode.ReflectionFactor.value;\n    }\n    if (materialNode.Shininess) {\n      parameters.shininess = materialNode.Shininess.value;\n    }\n    if (materialNode.Specular) {\n      parameters.specular = new Color().fromArray(materialNode.Specular.value);\n    } else if (materialNode.SpecularColor && materialNode.SpecularColor.type === \"Color\") {\n      parameters.specular = new Color().fromArray(materialNode.SpecularColor.value);\n    }\n    const scope = this;\n    connections.get(ID).children.forEach(function (child) {\n      const type = child.relationship;\n      switch (type) {\n        case \"Bump\":\n          parameters.bumpMap = scope.getTexture(textureMap, child.ID);\n          break;\n        case \"Maya|TEX_ao_map\":\n          parameters.aoMap = scope.getTexture(textureMap, child.ID);\n          break;\n        case \"DiffuseColor\":\n        case \"Maya|TEX_color_map\":\n          parameters.map = scope.getTexture(textureMap, child.ID);\n          if (parameters.map !== void 0) {\n            if (\"colorSpace\" in parameters.map) parameters.map.colorSpace = \"srgb\";else parameters.map.encoding = 3001;\n          }\n          break;\n        case \"DisplacementColor\":\n          parameters.displacementMap = scope.getTexture(textureMap, child.ID);\n          break;\n        case \"EmissiveColor\":\n          parameters.emissiveMap = scope.getTexture(textureMap, child.ID);\n          if (parameters.emissiveMap !== void 0) {\n            if (\"colorSpace\" in parameters.emissiveMap) parameters.emissiveMap.colorSpace = \"srgb\";else parameters.emissiveMap.encoding = 3001;\n          }\n          break;\n        case \"NormalMap\":\n        case \"Maya|TEX_normal_map\":\n          parameters.normalMap = scope.getTexture(textureMap, child.ID);\n          break;\n        case \"ReflectionColor\":\n          parameters.envMap = scope.getTexture(textureMap, child.ID);\n          if (parameters.envMap !== void 0) {\n            parameters.envMap.mapping = EquirectangularReflectionMapping;\n            if (\"colorSpace\" in parameters.envMap) parameters.envMap.colorSpace = \"srgb\";else parameters.envMap.encoding = 3001;\n          }\n          break;\n        case \"SpecularColor\":\n          parameters.specularMap = scope.getTexture(textureMap, child.ID);\n          if (parameters.specularMap !== void 0) {\n            if (\"colorSpace\" in parameters.specularMap) parameters.specularMap.colorSpace = \"srgb\";else parameters.specularMap.encoding = 3001;\n          }\n          break;\n        case \"TransparentColor\":\n        case \"TransparencyFactor\":\n          parameters.alphaMap = scope.getTexture(textureMap, child.ID);\n          parameters.transparent = true;\n          break;\n        case \"AmbientColor\":\n        case \"ShininessExponent\":\n        case \"SpecularFactor\":\n        case \"VectorDisplacementColor\":\n        default:\n          console.warn(\"THREE.FBXLoader: %s map is not supported in three.js, skipping texture.\", type);\n          break;\n      }\n    });\n    return parameters;\n  }\n  // get a texture from the textureMap for use by a material.\n  getTexture(textureMap, id) {\n    if (\"LayeredTexture\" in fbxTree.Objects && id in fbxTree.Objects.LayeredTexture) {\n      console.warn(\"THREE.FBXLoader: layered textures are not supported in three.js. Discarding all but first layer.\");\n      id = connections.get(id).children[0].ID;\n    }\n    return textureMap.get(id);\n  }\n  // Parse nodes in FBXTree.Objects.Deformer\n  // Deformer node can contain skinning or Vertex Cache animation data, however only skinning is supported here\n  // Generates map of Skeleton-like objects for use later when generating and binding skeletons.\n  parseDeformers() {\n    const skeletons = {};\n    const morphTargets = {};\n    if (\"Deformer\" in fbxTree.Objects) {\n      const DeformerNodes = fbxTree.Objects.Deformer;\n      for (const nodeID in DeformerNodes) {\n        const deformerNode = DeformerNodes[nodeID];\n        const relationships = connections.get(parseInt(nodeID));\n        if (deformerNode.attrType === \"Skin\") {\n          const skeleton = this.parseSkeleton(relationships, DeformerNodes);\n          skeleton.ID = nodeID;\n          if (relationships.parents.length > 1) {\n            console.warn(\"THREE.FBXLoader: skeleton attached to more than one geometry is not supported.\");\n          }\n          skeleton.geometryID = relationships.parents[0].ID;\n          skeletons[nodeID] = skeleton;\n        } else if (deformerNode.attrType === \"BlendShape\") {\n          const morphTarget = {\n            id: nodeID\n          };\n          morphTarget.rawTargets = this.parseMorphTargets(relationships, DeformerNodes);\n          morphTarget.id = nodeID;\n          if (relationships.parents.length > 1) {\n            console.warn(\"THREE.FBXLoader: morph target attached to more than one geometry is not supported.\");\n          }\n          morphTargets[nodeID] = morphTarget;\n        }\n      }\n    }\n    return {\n      skeletons,\n      morphTargets\n    };\n  }\n  // Parse single nodes in FBXTree.Objects.Deformer\n  // The top level skeleton node has type 'Skin' and sub nodes have type 'Cluster'\n  // Each skin node represents a skeleton and each cluster node represents a bone\n  parseSkeleton(relationships, deformerNodes) {\n    const rawBones = [];\n    relationships.children.forEach(function (child) {\n      const boneNode = deformerNodes[child.ID];\n      if (boneNode.attrType !== \"Cluster\") return;\n      const rawBone = {\n        ID: child.ID,\n        indices: [],\n        weights: [],\n        transformLink: new Matrix4().fromArray(boneNode.TransformLink.a)\n        // transform: new Matrix4().fromArray( boneNode.Transform.a ),\n        // linkMode: boneNode.Mode,\n      };\n      if (\"Indexes\" in boneNode) {\n        rawBone.indices = boneNode.Indexes.a;\n        rawBone.weights = boneNode.Weights.a;\n      }\n      rawBones.push(rawBone);\n    });\n    return {\n      rawBones,\n      bones: []\n    };\n  }\n  // The top level morph deformer node has type \"BlendShape\" and sub nodes have type \"BlendShapeChannel\"\n  parseMorphTargets(relationships, deformerNodes) {\n    const rawMorphTargets = [];\n    for (let i = 0; i < relationships.children.length; i++) {\n      const child = relationships.children[i];\n      const morphTargetNode = deformerNodes[child.ID];\n      const rawMorphTarget = {\n        name: morphTargetNode.attrName,\n        initialWeight: morphTargetNode.DeformPercent,\n        id: morphTargetNode.id,\n        fullWeights: morphTargetNode.FullWeights.a\n      };\n      if (morphTargetNode.attrType !== \"BlendShapeChannel\") return;\n      rawMorphTarget.geoID = connections.get(parseInt(child.ID)).children.filter(function (child2) {\n        return child2.relationship === void 0;\n      })[0].ID;\n      rawMorphTargets.push(rawMorphTarget);\n    }\n    return rawMorphTargets;\n  }\n  // create the main Group() to be returned by the loader\n  parseScene(deformers, geometryMap, materialMap) {\n    sceneGraph = new Group();\n    const modelMap = this.parseModels(deformers.skeletons, geometryMap, materialMap);\n    const modelNodes = fbxTree.Objects.Model;\n    const scope = this;\n    modelMap.forEach(function (model) {\n      const modelNode = modelNodes[model.ID];\n      scope.setLookAtProperties(model, modelNode);\n      const parentConnections = connections.get(model.ID).parents;\n      parentConnections.forEach(function (connection) {\n        const parent = modelMap.get(connection.ID);\n        if (parent !== void 0) parent.add(model);\n      });\n      if (model.parent === null) {\n        sceneGraph.add(model);\n      }\n    });\n    this.bindSkeleton(deformers.skeletons, geometryMap, modelMap);\n    this.createAmbientLight();\n    sceneGraph.traverse(function (node) {\n      if (node.userData.transformData) {\n        if (node.parent) {\n          node.userData.transformData.parentMatrix = node.parent.matrix;\n          node.userData.transformData.parentMatrixWorld = node.parent.matrixWorld;\n        }\n        const transform = generateTransform(node.userData.transformData);\n        node.applyMatrix4(transform);\n        node.updateWorldMatrix();\n      }\n    });\n    const animations = new AnimationParser().parse();\n    if (sceneGraph.children.length === 1 && sceneGraph.children[0].isGroup) {\n      sceneGraph.children[0].animations = animations;\n      sceneGraph = sceneGraph.children[0];\n    }\n    sceneGraph.animations = animations;\n  }\n  // parse nodes in FBXTree.Objects.Model\n  parseModels(skeletons, geometryMap, materialMap) {\n    const modelMap = /* @__PURE__ */new Map();\n    const modelNodes = fbxTree.Objects.Model;\n    for (const nodeID in modelNodes) {\n      const id = parseInt(nodeID);\n      const node = modelNodes[nodeID];\n      const relationships = connections.get(id);\n      let model = this.buildSkeleton(relationships, skeletons, id, node.attrName);\n      if (!model) {\n        switch (node.attrType) {\n          case \"Camera\":\n            model = this.createCamera(relationships);\n            break;\n          case \"Light\":\n            model = this.createLight(relationships);\n            break;\n          case \"Mesh\":\n            model = this.createMesh(relationships, geometryMap, materialMap);\n            break;\n          case \"NurbsCurve\":\n            model = this.createCurve(relationships, geometryMap);\n            break;\n          case \"LimbNode\":\n          case \"Root\":\n            model = new Bone();\n            break;\n          case \"Null\":\n          default:\n            model = new Group();\n            break;\n        }\n        model.name = node.attrName ? PropertyBinding.sanitizeNodeName(node.attrName) : \"\";\n        model.ID = id;\n      }\n      this.getTransformData(model, node);\n      modelMap.set(id, model);\n    }\n    return modelMap;\n  }\n  buildSkeleton(relationships, skeletons, id, name) {\n    let bone = null;\n    relationships.parents.forEach(function (parent) {\n      for (const ID in skeletons) {\n        const skeleton = skeletons[ID];\n        skeleton.rawBones.forEach(function (rawBone, i) {\n          if (rawBone.ID === parent.ID) {\n            const subBone = bone;\n            bone = new Bone();\n            bone.matrixWorld.copy(rawBone.transformLink);\n            bone.name = name ? PropertyBinding.sanitizeNodeName(name) : \"\";\n            bone.ID = id;\n            skeleton.bones[i] = bone;\n            if (subBone !== null) {\n              bone.add(subBone);\n            }\n          }\n        });\n      }\n    });\n    return bone;\n  }\n  // create a PerspectiveCamera or OrthographicCamera\n  createCamera(relationships) {\n    let model;\n    let cameraAttribute;\n    relationships.children.forEach(function (child) {\n      const attr = fbxTree.Objects.NodeAttribute[child.ID];\n      if (attr !== void 0) {\n        cameraAttribute = attr;\n      }\n    });\n    if (cameraAttribute === void 0) {\n      model = new Object3D();\n    } else {\n      let type = 0;\n      if (cameraAttribute.CameraProjectionType !== void 0 && cameraAttribute.CameraProjectionType.value === 1) {\n        type = 1;\n      }\n      let nearClippingPlane = 1;\n      if (cameraAttribute.NearPlane !== void 0) {\n        nearClippingPlane = cameraAttribute.NearPlane.value / 1e3;\n      }\n      let farClippingPlane = 1e3;\n      if (cameraAttribute.FarPlane !== void 0) {\n        farClippingPlane = cameraAttribute.FarPlane.value / 1e3;\n      }\n      let width = window.innerWidth;\n      let height = window.innerHeight;\n      if (cameraAttribute.AspectWidth !== void 0 && cameraAttribute.AspectHeight !== void 0) {\n        width = cameraAttribute.AspectWidth.value;\n        height = cameraAttribute.AspectHeight.value;\n      }\n      const aspect = width / height;\n      let fov = 45;\n      if (cameraAttribute.FieldOfView !== void 0) {\n        fov = cameraAttribute.FieldOfView.value;\n      }\n      const focalLength = cameraAttribute.FocalLength ? cameraAttribute.FocalLength.value : null;\n      switch (type) {\n        case 0:\n          model = new PerspectiveCamera(fov, aspect, nearClippingPlane, farClippingPlane);\n          if (focalLength !== null) model.setFocalLength(focalLength);\n          break;\n        case 1:\n          model = new OrthographicCamera(-width / 2, width / 2, height / 2, -height / 2, nearClippingPlane, farClippingPlane);\n          break;\n        default:\n          console.warn(\"THREE.FBXLoader: Unknown camera type \" + type + \".\");\n          model = new Object3D();\n          break;\n      }\n    }\n    return model;\n  }\n  // Create a DirectionalLight, PointLight or SpotLight\n  createLight(relationships) {\n    let model;\n    let lightAttribute;\n    relationships.children.forEach(function (child) {\n      const attr = fbxTree.Objects.NodeAttribute[child.ID];\n      if (attr !== void 0) {\n        lightAttribute = attr;\n      }\n    });\n    if (lightAttribute === void 0) {\n      model = new Object3D();\n    } else {\n      let type;\n      if (lightAttribute.LightType === void 0) {\n        type = 0;\n      } else {\n        type = lightAttribute.LightType.value;\n      }\n      let color = 16777215;\n      if (lightAttribute.Color !== void 0) {\n        color = new Color().fromArray(lightAttribute.Color.value);\n      }\n      let intensity = lightAttribute.Intensity === void 0 ? 1 : lightAttribute.Intensity.value / 100;\n      if (lightAttribute.CastLightOnObject !== void 0 && lightAttribute.CastLightOnObject.value === 0) {\n        intensity = 0;\n      }\n      let distance = 0;\n      if (lightAttribute.FarAttenuationEnd !== void 0) {\n        if (lightAttribute.EnableFarAttenuation !== void 0 && lightAttribute.EnableFarAttenuation.value === 0) {\n          distance = 0;\n        } else {\n          distance = lightAttribute.FarAttenuationEnd.value;\n        }\n      }\n      const decay = 1;\n      switch (type) {\n        case 0:\n          model = new PointLight(color, intensity, distance, decay);\n          break;\n        case 1:\n          model = new DirectionalLight(color, intensity);\n          break;\n        case 2:\n          let angle = Math.PI / 3;\n          if (lightAttribute.InnerAngle !== void 0) {\n            angle = MathUtils.degToRad(lightAttribute.InnerAngle.value);\n          }\n          let penumbra = 0;\n          if (lightAttribute.OuterAngle !== void 0) {\n            penumbra = MathUtils.degToRad(lightAttribute.OuterAngle.value);\n            penumbra = Math.max(penumbra, 1);\n          }\n          model = new SpotLight(color, intensity, distance, angle, penumbra, decay);\n          break;\n        default:\n          console.warn(\"THREE.FBXLoader: Unknown light type \" + lightAttribute.LightType.value + \", defaulting to a PointLight.\");\n          model = new PointLight(color, intensity);\n          break;\n      }\n      if (lightAttribute.CastShadows !== void 0 && lightAttribute.CastShadows.value === 1) {\n        model.castShadow = true;\n      }\n    }\n    return model;\n  }\n  createMesh(relationships, geometryMap, materialMap) {\n    let model;\n    let geometry = null;\n    let material = null;\n    const materials = [];\n    relationships.children.forEach(function (child) {\n      if (geometryMap.has(child.ID)) {\n        geometry = geometryMap.get(child.ID);\n      }\n      if (materialMap.has(child.ID)) {\n        materials.push(materialMap.get(child.ID));\n      }\n    });\n    if (materials.length > 1) {\n      material = materials;\n    } else if (materials.length > 0) {\n      material = materials[0];\n    } else {\n      material = new MeshPhongMaterial({\n        color: 13421772\n      });\n      materials.push(material);\n    }\n    if (\"color\" in geometry.attributes) {\n      materials.forEach(function (material2) {\n        material2.vertexColors = true;\n      });\n    }\n    if (geometry.FBX_Deformer) {\n      model = new SkinnedMesh(geometry, material);\n      model.normalizeSkinWeights();\n    } else {\n      model = new Mesh(geometry, material);\n    }\n    return model;\n  }\n  createCurve(relationships, geometryMap) {\n    const geometry = relationships.children.reduce(function (geo, child) {\n      if (geometryMap.has(child.ID)) geo = geometryMap.get(child.ID);\n      return geo;\n    }, null);\n    const material = new LineBasicMaterial({\n      color: 3342591,\n      linewidth: 1\n    });\n    return new Line(geometry, material);\n  }\n  // parse the model node for transform data\n  getTransformData(model, modelNode) {\n    const transformData = {};\n    if (\"InheritType\" in modelNode) transformData.inheritType = parseInt(modelNode.InheritType.value);\n    if (\"RotationOrder\" in modelNode) transformData.eulerOrder = getEulerOrder(modelNode.RotationOrder.value);else transformData.eulerOrder = \"ZYX\";\n    if (\"Lcl_Translation\" in modelNode) transformData.translation = modelNode.Lcl_Translation.value;\n    if (\"PreRotation\" in modelNode) transformData.preRotation = modelNode.PreRotation.value;\n    if (\"Lcl_Rotation\" in modelNode) transformData.rotation = modelNode.Lcl_Rotation.value;\n    if (\"PostRotation\" in modelNode) transformData.postRotation = modelNode.PostRotation.value;\n    if (\"Lcl_Scaling\" in modelNode) transformData.scale = modelNode.Lcl_Scaling.value;\n    if (\"ScalingOffset\" in modelNode) transformData.scalingOffset = modelNode.ScalingOffset.value;\n    if (\"ScalingPivot\" in modelNode) transformData.scalingPivot = modelNode.ScalingPivot.value;\n    if (\"RotationOffset\" in modelNode) transformData.rotationOffset = modelNode.RotationOffset.value;\n    if (\"RotationPivot\" in modelNode) transformData.rotationPivot = modelNode.RotationPivot.value;\n    model.userData.transformData = transformData;\n  }\n  setLookAtProperties(model, modelNode) {\n    if (\"LookAtProperty\" in modelNode) {\n      const children = connections.get(model.ID).children;\n      children.forEach(function (child) {\n        if (child.relationship === \"LookAtProperty\") {\n          const lookAtTarget = fbxTree.Objects.Model[child.ID];\n          if (\"Lcl_Translation\" in lookAtTarget) {\n            const pos = lookAtTarget.Lcl_Translation.value;\n            if (model.target !== void 0) {\n              model.target.position.fromArray(pos);\n              sceneGraph.add(model.target);\n            } else {\n              model.lookAt(new Vector3().fromArray(pos));\n            }\n          }\n        }\n      });\n    }\n  }\n  bindSkeleton(skeletons, geometryMap, modelMap) {\n    const bindMatrices = this.parsePoseNodes();\n    for (const ID in skeletons) {\n      const skeleton = skeletons[ID];\n      const parents = connections.get(parseInt(skeleton.ID)).parents;\n      parents.forEach(function (parent) {\n        if (geometryMap.has(parent.ID)) {\n          const geoID = parent.ID;\n          const geoRelationships = connections.get(geoID);\n          geoRelationships.parents.forEach(function (geoConnParent) {\n            if (modelMap.has(geoConnParent.ID)) {\n              const model = modelMap.get(geoConnParent.ID);\n              model.bind(new Skeleton(skeleton.bones), bindMatrices[geoConnParent.ID]);\n            }\n          });\n        }\n      });\n    }\n  }\n  parsePoseNodes() {\n    const bindMatrices = {};\n    if (\"Pose\" in fbxTree.Objects) {\n      const BindPoseNode = fbxTree.Objects.Pose;\n      for (const nodeID in BindPoseNode) {\n        if (BindPoseNode[nodeID].attrType === \"BindPose\" && BindPoseNode[nodeID].NbPoseNodes > 0) {\n          const poseNodes = BindPoseNode[nodeID].PoseNode;\n          if (Array.isArray(poseNodes)) {\n            poseNodes.forEach(function (poseNode) {\n              bindMatrices[poseNode.Node] = new Matrix4().fromArray(poseNode.Matrix.a);\n            });\n          } else {\n            bindMatrices[poseNodes.Node] = new Matrix4().fromArray(poseNodes.Matrix.a);\n          }\n        }\n      }\n    }\n    return bindMatrices;\n  }\n  // Parse ambient color in FBXTree.GlobalSettings - if it's not set to black (default), create an ambient light\n  createAmbientLight() {\n    if (\"GlobalSettings\" in fbxTree && \"AmbientColor\" in fbxTree.GlobalSettings) {\n      const ambientColor = fbxTree.GlobalSettings.AmbientColor.value;\n      const r = ambientColor[0];\n      const g = ambientColor[1];\n      const b = ambientColor[2];\n      if (r !== 0 || g !== 0 || b !== 0) {\n        const color = new Color(r, g, b);\n        sceneGraph.add(new AmbientLight(color, 1));\n      }\n    }\n  }\n}\nclass GeometryParser {\n  // Parse nodes in FBXTree.Objects.Geometry\n  parse(deformers) {\n    const geometryMap = /* @__PURE__ */new Map();\n    if (\"Geometry\" in fbxTree.Objects) {\n      const geoNodes = fbxTree.Objects.Geometry;\n      for (const nodeID in geoNodes) {\n        const relationships = connections.get(parseInt(nodeID));\n        const geo = this.parseGeometry(relationships, geoNodes[nodeID], deformers);\n        geometryMap.set(parseInt(nodeID), geo);\n      }\n    }\n    return geometryMap;\n  }\n  // Parse single node in FBXTree.Objects.Geometry\n  parseGeometry(relationships, geoNode, deformers) {\n    switch (geoNode.attrType) {\n      case \"Mesh\":\n        return this.parseMeshGeometry(relationships, geoNode, deformers);\n      case \"NurbsCurve\":\n        return this.parseNurbsGeometry(geoNode);\n    }\n  }\n  // Parse single node mesh geometry in FBXTree.Objects.Geometry\n  parseMeshGeometry(relationships, geoNode, deformers) {\n    const skeletons = deformers.skeletons;\n    const morphTargets = [];\n    const modelNodes = relationships.parents.map(function (parent) {\n      return fbxTree.Objects.Model[parent.ID];\n    });\n    if (modelNodes.length === 0) return;\n    const skeleton = relationships.children.reduce(function (skeleton2, child) {\n      if (skeletons[child.ID] !== void 0) skeleton2 = skeletons[child.ID];\n      return skeleton2;\n    }, null);\n    relationships.children.forEach(function (child) {\n      if (deformers.morphTargets[child.ID] !== void 0) {\n        morphTargets.push(deformers.morphTargets[child.ID]);\n      }\n    });\n    const modelNode = modelNodes[0];\n    const transformData = {};\n    if (\"RotationOrder\" in modelNode) transformData.eulerOrder = getEulerOrder(modelNode.RotationOrder.value);\n    if (\"InheritType\" in modelNode) transformData.inheritType = parseInt(modelNode.InheritType.value);\n    if (\"GeometricTranslation\" in modelNode) transformData.translation = modelNode.GeometricTranslation.value;\n    if (\"GeometricRotation\" in modelNode) transformData.rotation = modelNode.GeometricRotation.value;\n    if (\"GeometricScaling\" in modelNode) transformData.scale = modelNode.GeometricScaling.value;\n    const transform = generateTransform(transformData);\n    return this.genGeometry(geoNode, skeleton, morphTargets, transform);\n  }\n  // Generate a BufferGeometry from a node in FBXTree.Objects.Geometry\n  genGeometry(geoNode, skeleton, morphTargets, preTransform) {\n    const geo = new BufferGeometry();\n    if (geoNode.attrName) geo.name = geoNode.attrName;\n    const geoInfo = this.parseGeoNode(geoNode, skeleton);\n    const buffers = this.genBuffers(geoInfo);\n    const positionAttribute = new Float32BufferAttribute(buffers.vertex, 3);\n    positionAttribute.applyMatrix4(preTransform);\n    geo.setAttribute(\"position\", positionAttribute);\n    if (buffers.colors.length > 0) {\n      geo.setAttribute(\"color\", new Float32BufferAttribute(buffers.colors, 3));\n    }\n    if (skeleton) {\n      geo.setAttribute(\"skinIndex\", new Uint16BufferAttribute(buffers.weightsIndices, 4));\n      geo.setAttribute(\"skinWeight\", new Float32BufferAttribute(buffers.vertexWeights, 4));\n      geo.FBX_Deformer = skeleton;\n    }\n    if (buffers.normal.length > 0) {\n      const normalMatrix = new Matrix3().getNormalMatrix(preTransform);\n      const normalAttribute = new Float32BufferAttribute(buffers.normal, 3);\n      normalAttribute.applyNormalMatrix(normalMatrix);\n      geo.setAttribute(\"normal\", normalAttribute);\n    }\n    buffers.uvs.forEach(function (uvBuffer, i) {\n      if (UV1 === \"uv2\") i++;\n      const name = i === 0 ? \"uv\" : `uv${i}`;\n      geo.setAttribute(name, new Float32BufferAttribute(buffers.uvs[i], 2));\n    });\n    if (geoInfo.material && geoInfo.material.mappingType !== \"AllSame\") {\n      let prevMaterialIndex = buffers.materialIndex[0];\n      let startIndex = 0;\n      buffers.materialIndex.forEach(function (currentIndex, i) {\n        if (currentIndex !== prevMaterialIndex) {\n          geo.addGroup(startIndex, i - startIndex, prevMaterialIndex);\n          prevMaterialIndex = currentIndex;\n          startIndex = i;\n        }\n      });\n      if (geo.groups.length > 0) {\n        const lastGroup = geo.groups[geo.groups.length - 1];\n        const lastIndex = lastGroup.start + lastGroup.count;\n        if (lastIndex !== buffers.materialIndex.length) {\n          geo.addGroup(lastIndex, buffers.materialIndex.length - lastIndex, prevMaterialIndex);\n        }\n      }\n      if (geo.groups.length === 0) {\n        geo.addGroup(0, buffers.materialIndex.length, buffers.materialIndex[0]);\n      }\n    }\n    this.addMorphTargets(geo, geoNode, morphTargets, preTransform);\n    return geo;\n  }\n  parseGeoNode(geoNode, skeleton) {\n    const geoInfo = {};\n    geoInfo.vertexPositions = geoNode.Vertices !== void 0 ? geoNode.Vertices.a : [];\n    geoInfo.vertexIndices = geoNode.PolygonVertexIndex !== void 0 ? geoNode.PolygonVertexIndex.a : [];\n    if (geoNode.LayerElementColor) {\n      geoInfo.color = this.parseVertexColors(geoNode.LayerElementColor[0]);\n    }\n    if (geoNode.LayerElementMaterial) {\n      geoInfo.material = this.parseMaterialIndices(geoNode.LayerElementMaterial[0]);\n    }\n    if (geoNode.LayerElementNormal) {\n      geoInfo.normal = this.parseNormals(geoNode.LayerElementNormal[0]);\n    }\n    if (geoNode.LayerElementUV) {\n      geoInfo.uv = [];\n      let i = 0;\n      while (geoNode.LayerElementUV[i]) {\n        if (geoNode.LayerElementUV[i].UV) {\n          geoInfo.uv.push(this.parseUVs(geoNode.LayerElementUV[i]));\n        }\n        i++;\n      }\n    }\n    geoInfo.weightTable = {};\n    if (skeleton !== null) {\n      geoInfo.skeleton = skeleton;\n      skeleton.rawBones.forEach(function (rawBone, i) {\n        rawBone.indices.forEach(function (index, j) {\n          if (geoInfo.weightTable[index] === void 0) geoInfo.weightTable[index] = [];\n          geoInfo.weightTable[index].push({\n            id: i,\n            weight: rawBone.weights[j]\n          });\n        });\n      });\n    }\n    return geoInfo;\n  }\n  genBuffers(geoInfo) {\n    const buffers = {\n      vertex: [],\n      normal: [],\n      colors: [],\n      uvs: [],\n      materialIndex: [],\n      vertexWeights: [],\n      weightsIndices: []\n    };\n    let polygonIndex = 0;\n    let faceLength = 0;\n    let displayedWeightsWarning = false;\n    let facePositionIndexes = [];\n    let faceNormals = [];\n    let faceColors = [];\n    let faceUVs = [];\n    let faceWeights = [];\n    let faceWeightIndices = [];\n    const scope = this;\n    geoInfo.vertexIndices.forEach(function (vertexIndex, polygonVertexIndex) {\n      let materialIndex;\n      let endOfFace = false;\n      if (vertexIndex < 0) {\n        vertexIndex = vertexIndex ^ -1;\n        endOfFace = true;\n      }\n      let weightIndices = [];\n      let weights = [];\n      facePositionIndexes.push(vertexIndex * 3, vertexIndex * 3 + 1, vertexIndex * 3 + 2);\n      if (geoInfo.color) {\n        const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.color);\n        faceColors.push(data[0], data[1], data[2]);\n      }\n      if (geoInfo.skeleton) {\n        if (geoInfo.weightTable[vertexIndex] !== void 0) {\n          geoInfo.weightTable[vertexIndex].forEach(function (wt) {\n            weights.push(wt.weight);\n            weightIndices.push(wt.id);\n          });\n        }\n        if (weights.length > 4) {\n          if (!displayedWeightsWarning) {\n            console.warn(\"THREE.FBXLoader: Vertex has more than 4 skinning weights assigned to vertex. Deleting additional weights.\");\n            displayedWeightsWarning = true;\n          }\n          const wIndex = [0, 0, 0, 0];\n          const Weight = [0, 0, 0, 0];\n          weights.forEach(function (weight, weightIndex) {\n            let currentWeight = weight;\n            let currentIndex = weightIndices[weightIndex];\n            Weight.forEach(function (comparedWeight, comparedWeightIndex, comparedWeightArray) {\n              if (currentWeight > comparedWeight) {\n                comparedWeightArray[comparedWeightIndex] = currentWeight;\n                currentWeight = comparedWeight;\n                const tmp = wIndex[comparedWeightIndex];\n                wIndex[comparedWeightIndex] = currentIndex;\n                currentIndex = tmp;\n              }\n            });\n          });\n          weightIndices = wIndex;\n          weights = Weight;\n        }\n        while (weights.length < 4) {\n          weights.push(0);\n          weightIndices.push(0);\n        }\n        for (let i = 0; i < 4; ++i) {\n          faceWeights.push(weights[i]);\n          faceWeightIndices.push(weightIndices[i]);\n        }\n      }\n      if (geoInfo.normal) {\n        const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.normal);\n        faceNormals.push(data[0], data[1], data[2]);\n      }\n      if (geoInfo.material && geoInfo.material.mappingType !== \"AllSame\") {\n        materialIndex = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.material)[0];\n      }\n      if (geoInfo.uv) {\n        geoInfo.uv.forEach(function (uv, i) {\n          const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, uv);\n          if (faceUVs[i] === void 0) {\n            faceUVs[i] = [];\n          }\n          faceUVs[i].push(data[0]);\n          faceUVs[i].push(data[1]);\n        });\n      }\n      faceLength++;\n      if (endOfFace) {\n        scope.genFace(buffers, geoInfo, facePositionIndexes, materialIndex, faceNormals, faceColors, faceUVs, faceWeights, faceWeightIndices, faceLength);\n        polygonIndex++;\n        faceLength = 0;\n        facePositionIndexes = [];\n        faceNormals = [];\n        faceColors = [];\n        faceUVs = [];\n        faceWeights = [];\n        faceWeightIndices = [];\n      }\n    });\n    return buffers;\n  }\n  // Generate data for a single face in a geometry. If the face is a quad then split it into 2 tris\n  genFace(buffers, geoInfo, facePositionIndexes, materialIndex, faceNormals, faceColors, faceUVs, faceWeights, faceWeightIndices, faceLength) {\n    for (let i = 2; i < faceLength; i++) {\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[0]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[1]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[2]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3 + 1]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3 + 2]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3 + 1]]);\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3 + 2]]);\n      if (geoInfo.skeleton) {\n        buffers.vertexWeights.push(faceWeights[0]);\n        buffers.vertexWeights.push(faceWeights[1]);\n        buffers.vertexWeights.push(faceWeights[2]);\n        buffers.vertexWeights.push(faceWeights[3]);\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4]);\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 1]);\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 2]);\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 3]);\n        buffers.vertexWeights.push(faceWeights[i * 4]);\n        buffers.vertexWeights.push(faceWeights[i * 4 + 1]);\n        buffers.vertexWeights.push(faceWeights[i * 4 + 2]);\n        buffers.vertexWeights.push(faceWeights[i * 4 + 3]);\n        buffers.weightsIndices.push(faceWeightIndices[0]);\n        buffers.weightsIndices.push(faceWeightIndices[1]);\n        buffers.weightsIndices.push(faceWeightIndices[2]);\n        buffers.weightsIndices.push(faceWeightIndices[3]);\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4]);\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 1]);\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 2]);\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 3]);\n        buffers.weightsIndices.push(faceWeightIndices[i * 4]);\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 1]);\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 2]);\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 3]);\n      }\n      if (geoInfo.color) {\n        buffers.colors.push(faceColors[0]);\n        buffers.colors.push(faceColors[1]);\n        buffers.colors.push(faceColors[2]);\n        buffers.colors.push(faceColors[(i - 1) * 3]);\n        buffers.colors.push(faceColors[(i - 1) * 3 + 1]);\n        buffers.colors.push(faceColors[(i - 1) * 3 + 2]);\n        buffers.colors.push(faceColors[i * 3]);\n        buffers.colors.push(faceColors[i * 3 + 1]);\n        buffers.colors.push(faceColors[i * 3 + 2]);\n      }\n      if (geoInfo.material && geoInfo.material.mappingType !== \"AllSame\") {\n        buffers.materialIndex.push(materialIndex);\n        buffers.materialIndex.push(materialIndex);\n        buffers.materialIndex.push(materialIndex);\n      }\n      if (geoInfo.normal) {\n        buffers.normal.push(faceNormals[0]);\n        buffers.normal.push(faceNormals[1]);\n        buffers.normal.push(faceNormals[2]);\n        buffers.normal.push(faceNormals[(i - 1) * 3]);\n        buffers.normal.push(faceNormals[(i - 1) * 3 + 1]);\n        buffers.normal.push(faceNormals[(i - 1) * 3 + 2]);\n        buffers.normal.push(faceNormals[i * 3]);\n        buffers.normal.push(faceNormals[i * 3 + 1]);\n        buffers.normal.push(faceNormals[i * 3 + 2]);\n      }\n      if (geoInfo.uv) {\n        geoInfo.uv.forEach(function (uv, j) {\n          if (buffers.uvs[j] === void 0) buffers.uvs[j] = [];\n          buffers.uvs[j].push(faceUVs[j][0]);\n          buffers.uvs[j].push(faceUVs[j][1]);\n          buffers.uvs[j].push(faceUVs[j][(i - 1) * 2]);\n          buffers.uvs[j].push(faceUVs[j][(i - 1) * 2 + 1]);\n          buffers.uvs[j].push(faceUVs[j][i * 2]);\n          buffers.uvs[j].push(faceUVs[j][i * 2 + 1]);\n        });\n      }\n    }\n  }\n  addMorphTargets(parentGeo, parentGeoNode, morphTargets, preTransform) {\n    if (morphTargets.length === 0) return;\n    parentGeo.morphTargetsRelative = true;\n    parentGeo.morphAttributes.position = [];\n    const scope = this;\n    morphTargets.forEach(function (morphTarget) {\n      morphTarget.rawTargets.forEach(function (rawTarget) {\n        const morphGeoNode = fbxTree.Objects.Geometry[rawTarget.geoID];\n        if (morphGeoNode !== void 0) {\n          scope.genMorphGeometry(parentGeo, parentGeoNode, morphGeoNode, preTransform, rawTarget.name);\n        }\n      });\n    });\n  }\n  // a morph geometry node is similar to a standard  node, and the node is also contained\n  // in FBXTree.Objects.Geometry, however it can only have attributes for position, normal\n  // and a special attribute Index defining which vertices of the original geometry are affected\n  // Normal and position attributes only have data for the vertices that are affected by the morph\n  genMorphGeometry(parentGeo, parentGeoNode, morphGeoNode, preTransform, name) {\n    const vertexIndices = parentGeoNode.PolygonVertexIndex !== void 0 ? parentGeoNode.PolygonVertexIndex.a : [];\n    const morphPositionsSparse = morphGeoNode.Vertices !== void 0 ? morphGeoNode.Vertices.a : [];\n    const indices = morphGeoNode.Indexes !== void 0 ? morphGeoNode.Indexes.a : [];\n    const length = parentGeo.attributes.position.count * 3;\n    const morphPositions = new Float32Array(length);\n    for (let i = 0; i < indices.length; i++) {\n      const morphIndex = indices[i] * 3;\n      morphPositions[morphIndex] = morphPositionsSparse[i * 3];\n      morphPositions[morphIndex + 1] = morphPositionsSparse[i * 3 + 1];\n      morphPositions[morphIndex + 2] = morphPositionsSparse[i * 3 + 2];\n    }\n    const morphGeoInfo = {\n      vertexIndices,\n      vertexPositions: morphPositions\n    };\n    const morphBuffers = this.genBuffers(morphGeoInfo);\n    const positionAttribute = new Float32BufferAttribute(morphBuffers.vertex, 3);\n    positionAttribute.name = name || morphGeoNode.attrName;\n    positionAttribute.applyMatrix4(preTransform);\n    parentGeo.morphAttributes.position.push(positionAttribute);\n  }\n  // Parse normal from FBXTree.Objects.Geometry.LayerElementNormal if it exists\n  parseNormals(NormalNode) {\n    const mappingType = NormalNode.MappingInformationType;\n    const referenceType = NormalNode.ReferenceInformationType;\n    const buffer = NormalNode.Normals.a;\n    let indexBuffer = [];\n    if (referenceType === \"IndexToDirect\") {\n      if (\"NormalIndex\" in NormalNode) {\n        indexBuffer = NormalNode.NormalIndex.a;\n      } else if (\"NormalsIndex\" in NormalNode) {\n        indexBuffer = NormalNode.NormalsIndex.a;\n      }\n    }\n    return {\n      dataSize: 3,\n      buffer,\n      indices: indexBuffer,\n      mappingType,\n      referenceType\n    };\n  }\n  // Parse UVs from FBXTree.Objects.Geometry.LayerElementUV if it exists\n  parseUVs(UVNode) {\n    const mappingType = UVNode.MappingInformationType;\n    const referenceType = UVNode.ReferenceInformationType;\n    const buffer = UVNode.UV.a;\n    let indexBuffer = [];\n    if (referenceType === \"IndexToDirect\") {\n      indexBuffer = UVNode.UVIndex.a;\n    }\n    return {\n      dataSize: 2,\n      buffer,\n      indices: indexBuffer,\n      mappingType,\n      referenceType\n    };\n  }\n  // Parse Vertex Colors from FBXTree.Objects.Geometry.LayerElementColor if it exists\n  parseVertexColors(ColorNode) {\n    const mappingType = ColorNode.MappingInformationType;\n    const referenceType = ColorNode.ReferenceInformationType;\n    const buffer = ColorNode.Colors.a;\n    let indexBuffer = [];\n    if (referenceType === \"IndexToDirect\") {\n      indexBuffer = ColorNode.ColorIndex.a;\n    }\n    return {\n      dataSize: 4,\n      buffer,\n      indices: indexBuffer,\n      mappingType,\n      referenceType\n    };\n  }\n  // Parse mapping and material data in FBXTree.Objects.Geometry.LayerElementMaterial if it exists\n  parseMaterialIndices(MaterialNode) {\n    const mappingType = MaterialNode.MappingInformationType;\n    const referenceType = MaterialNode.ReferenceInformationType;\n    if (mappingType === \"NoMappingInformation\") {\n      return {\n        dataSize: 1,\n        buffer: [0],\n        indices: [0],\n        mappingType: \"AllSame\",\n        referenceType\n      };\n    }\n    const materialIndexBuffer = MaterialNode.Materials.a;\n    const materialIndices = [];\n    for (let i = 0; i < materialIndexBuffer.length; ++i) {\n      materialIndices.push(i);\n    }\n    return {\n      dataSize: 1,\n      buffer: materialIndexBuffer,\n      indices: materialIndices,\n      mappingType,\n      referenceType\n    };\n  }\n  // Generate a NurbGeometry from a node in FBXTree.Objects.Geometry\n  parseNurbsGeometry(geoNode) {\n    if (NURBSCurve === void 0) {\n      console.error(\"THREE.FBXLoader: The loader relies on NURBSCurve for any nurbs present in the model. Nurbs will show up as empty geometry.\");\n      return new BufferGeometry();\n    }\n    const order = parseInt(geoNode.Order);\n    if (isNaN(order)) {\n      console.error(\"THREE.FBXLoader: Invalid Order %s given for geometry ID: %s\", geoNode.Order, geoNode.id);\n      return new BufferGeometry();\n    }\n    const degree = order - 1;\n    const knots = geoNode.KnotVector.a;\n    const controlPoints = [];\n    const pointsValues = geoNode.Points.a;\n    for (let i = 0, l = pointsValues.length; i < l; i += 4) {\n      controlPoints.push(new Vector4().fromArray(pointsValues, i));\n    }\n    let startKnot, endKnot;\n    if (geoNode.Form === \"Closed\") {\n      controlPoints.push(controlPoints[0]);\n    } else if (geoNode.Form === \"Periodic\") {\n      startKnot = degree;\n      endKnot = knots.length - 1 - startKnot;\n      for (let i = 0; i < degree; ++i) {\n        controlPoints.push(controlPoints[i]);\n      }\n    }\n    const curve = new NURBSCurve(degree, knots, controlPoints, startKnot, endKnot);\n    const points = curve.getPoints(controlPoints.length * 12);\n    return new BufferGeometry().setFromPoints(points);\n  }\n}\nclass AnimationParser {\n  // take raw animation clips and turn them into three.js animation clips\n  parse() {\n    const animationClips = [];\n    const rawClips = this.parseClips();\n    if (rawClips !== void 0) {\n      for (const key in rawClips) {\n        const rawClip = rawClips[key];\n        const clip = this.addClip(rawClip);\n        animationClips.push(clip);\n      }\n    }\n    return animationClips;\n  }\n  parseClips() {\n    if (fbxTree.Objects.AnimationCurve === void 0) return void 0;\n    const curveNodesMap = this.parseAnimationCurveNodes();\n    this.parseAnimationCurves(curveNodesMap);\n    const layersMap = this.parseAnimationLayers(curveNodesMap);\n    const rawClips = this.parseAnimStacks(layersMap);\n    return rawClips;\n  }\n  // parse nodes in FBXTree.Objects.AnimationCurveNode\n  // each AnimationCurveNode holds data for an animation transform for a model (e.g. left arm rotation )\n  // and is referenced by an AnimationLayer\n  parseAnimationCurveNodes() {\n    const rawCurveNodes = fbxTree.Objects.AnimationCurveNode;\n    const curveNodesMap = /* @__PURE__ */new Map();\n    for (const nodeID in rawCurveNodes) {\n      const rawCurveNode = rawCurveNodes[nodeID];\n      if (rawCurveNode.attrName.match(/S|R|T|DeformPercent/) !== null) {\n        const curveNode = {\n          id: rawCurveNode.id,\n          attr: rawCurveNode.attrName,\n          curves: {}\n        };\n        curveNodesMap.set(curveNode.id, curveNode);\n      }\n    }\n    return curveNodesMap;\n  }\n  // parse nodes in FBXTree.Objects.AnimationCurve and connect them up to\n  // previously parsed AnimationCurveNodes. Each AnimationCurve holds data for a single animated\n  // axis ( e.g. times and values of x rotation)\n  parseAnimationCurves(curveNodesMap) {\n    const rawCurves = fbxTree.Objects.AnimationCurve;\n    for (const nodeID in rawCurves) {\n      const animationCurve = {\n        id: rawCurves[nodeID].id,\n        times: rawCurves[nodeID].KeyTime.a.map(convertFBXTimeToSeconds),\n        values: rawCurves[nodeID].KeyValueFloat.a\n      };\n      const relationships = connections.get(animationCurve.id);\n      if (relationships !== void 0) {\n        const animationCurveID = relationships.parents[0].ID;\n        const animationCurveRelationship = relationships.parents[0].relationship;\n        if (animationCurveRelationship.match(/X/)) {\n          curveNodesMap.get(animationCurveID).curves[\"x\"] = animationCurve;\n        } else if (animationCurveRelationship.match(/Y/)) {\n          curveNodesMap.get(animationCurveID).curves[\"y\"] = animationCurve;\n        } else if (animationCurveRelationship.match(/Z/)) {\n          curveNodesMap.get(animationCurveID).curves[\"z\"] = animationCurve;\n        } else if (animationCurveRelationship.match(/d|DeformPercent/) && curveNodesMap.has(animationCurveID)) {\n          curveNodesMap.get(animationCurveID).curves[\"morph\"] = animationCurve;\n        }\n      }\n    }\n  }\n  // parse nodes in FBXTree.Objects.AnimationLayer. Each layers holds references\n  // to various AnimationCurveNodes and is referenced by an AnimationStack node\n  // note: theoretically a stack can have multiple layers, however in practice there always seems to be one per stack\n  parseAnimationLayers(curveNodesMap) {\n    const rawLayers = fbxTree.Objects.AnimationLayer;\n    const layersMap = /* @__PURE__ */new Map();\n    for (const nodeID in rawLayers) {\n      const layerCurveNodes = [];\n      const connection = connections.get(parseInt(nodeID));\n      if (connection !== void 0) {\n        const children = connection.children;\n        children.forEach(function (child, i) {\n          if (curveNodesMap.has(child.ID)) {\n            const curveNode = curveNodesMap.get(child.ID);\n            if (curveNode.curves.x !== void 0 || curveNode.curves.y !== void 0 || curveNode.curves.z !== void 0) {\n              if (layerCurveNodes[i] === void 0) {\n                const modelID = connections.get(child.ID).parents.filter(function (parent) {\n                  return parent.relationship !== void 0;\n                })[0].ID;\n                if (modelID !== void 0) {\n                  const rawModel = fbxTree.Objects.Model[modelID.toString()];\n                  if (rawModel === void 0) {\n                    console.warn(\"THREE.FBXLoader: Encountered a unused curve.\", child);\n                    return;\n                  }\n                  const node = {\n                    modelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName(rawModel.attrName) : \"\",\n                    ID: rawModel.id,\n                    initialPosition: [0, 0, 0],\n                    initialRotation: [0, 0, 0],\n                    initialScale: [1, 1, 1]\n                  };\n                  sceneGraph.traverse(function (child2) {\n                    if (child2.ID === rawModel.id) {\n                      node.transform = child2.matrix;\n                      if (child2.userData.transformData) node.eulerOrder = child2.userData.transformData.eulerOrder;\n                    }\n                  });\n                  if (!node.transform) node.transform = new Matrix4();\n                  if (\"PreRotation\" in rawModel) node.preRotation = rawModel.PreRotation.value;\n                  if (\"PostRotation\" in rawModel) node.postRotation = rawModel.PostRotation.value;\n                  layerCurveNodes[i] = node;\n                }\n              }\n              if (layerCurveNodes[i]) layerCurveNodes[i][curveNode.attr] = curveNode;\n            } else if (curveNode.curves.morph !== void 0) {\n              if (layerCurveNodes[i] === void 0) {\n                const deformerID = connections.get(child.ID).parents.filter(function (parent) {\n                  return parent.relationship !== void 0;\n                })[0].ID;\n                const morpherID = connections.get(deformerID).parents[0].ID;\n                const geoID = connections.get(morpherID).parents[0].ID;\n                const modelID = connections.get(geoID).parents[0].ID;\n                const rawModel = fbxTree.Objects.Model[modelID];\n                const node = {\n                  modelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName(rawModel.attrName) : \"\",\n                  morphName: fbxTree.Objects.Deformer[deformerID].attrName\n                };\n                layerCurveNodes[i] = node;\n              }\n              layerCurveNodes[i][curveNode.attr] = curveNode;\n            }\n          }\n        });\n        layersMap.set(parseInt(nodeID), layerCurveNodes);\n      }\n    }\n    return layersMap;\n  }\n  // parse nodes in FBXTree.Objects.AnimationStack. These are the top level node in the animation\n  // hierarchy. Each Stack node will be used to create a AnimationClip\n  parseAnimStacks(layersMap) {\n    const rawStacks = fbxTree.Objects.AnimationStack;\n    const rawClips = {};\n    for (const nodeID in rawStacks) {\n      const children = connections.get(parseInt(nodeID)).children;\n      if (children.length > 1) {\n        console.warn(\"THREE.FBXLoader: Encountered an animation stack with multiple layers, this is currently not supported. Ignoring subsequent layers.\");\n      }\n      const layer = layersMap.get(children[0].ID);\n      rawClips[nodeID] = {\n        name: rawStacks[nodeID].attrName,\n        layer\n      };\n    }\n    return rawClips;\n  }\n  addClip(rawClip) {\n    let tracks = [];\n    const scope = this;\n    rawClip.layer.forEach(function (rawTracks) {\n      tracks = tracks.concat(scope.generateTracks(rawTracks));\n    });\n    return new AnimationClip(rawClip.name, -1, tracks);\n  }\n  generateTracks(rawTracks) {\n    const tracks = [];\n    let initialPosition = new Vector3();\n    let initialRotation = new Quaternion();\n    let initialScale = new Vector3();\n    if (rawTracks.transform) rawTracks.transform.decompose(initialPosition, initialRotation, initialScale);\n    initialPosition = initialPosition.toArray();\n    initialRotation = new Euler().setFromQuaternion(initialRotation, rawTracks.eulerOrder).toArray();\n    initialScale = initialScale.toArray();\n    if (rawTracks.T !== void 0 && Object.keys(rawTracks.T.curves).length > 0) {\n      const positionTrack = this.generateVectorTrack(rawTracks.modelName, rawTracks.T.curves, initialPosition, \"position\");\n      if (positionTrack !== void 0) tracks.push(positionTrack);\n    }\n    if (rawTracks.R !== void 0 && Object.keys(rawTracks.R.curves).length > 0) {\n      const rotationTrack = this.generateRotationTrack(rawTracks.modelName, rawTracks.R.curves, initialRotation, rawTracks.preRotation, rawTracks.postRotation, rawTracks.eulerOrder);\n      if (rotationTrack !== void 0) tracks.push(rotationTrack);\n    }\n    if (rawTracks.S !== void 0 && Object.keys(rawTracks.S.curves).length > 0) {\n      const scaleTrack = this.generateVectorTrack(rawTracks.modelName, rawTracks.S.curves, initialScale, \"scale\");\n      if (scaleTrack !== void 0) tracks.push(scaleTrack);\n    }\n    if (rawTracks.DeformPercent !== void 0) {\n      const morphTrack = this.generateMorphTrack(rawTracks);\n      if (morphTrack !== void 0) tracks.push(morphTrack);\n    }\n    return tracks;\n  }\n  generateVectorTrack(modelName, curves, initialValue, type) {\n    const times = this.getTimesForAllAxes(curves);\n    const values = this.getKeyframeTrackValues(times, curves, initialValue);\n    return new VectorKeyframeTrack(modelName + \".\" + type, times, values);\n  }\n  generateRotationTrack(modelName, curves, initialValue, preRotation, postRotation, eulerOrder) {\n    if (curves.x !== void 0) {\n      this.interpolateRotations(curves.x);\n      curves.x.values = curves.x.values.map(MathUtils.degToRad);\n    }\n    if (curves.y !== void 0) {\n      this.interpolateRotations(curves.y);\n      curves.y.values = curves.y.values.map(MathUtils.degToRad);\n    }\n    if (curves.z !== void 0) {\n      this.interpolateRotations(curves.z);\n      curves.z.values = curves.z.values.map(MathUtils.degToRad);\n    }\n    const times = this.getTimesForAllAxes(curves);\n    const values = this.getKeyframeTrackValues(times, curves, initialValue);\n    if (preRotation !== void 0) {\n      preRotation = preRotation.map(MathUtils.degToRad);\n      preRotation.push(eulerOrder);\n      preRotation = new Euler().fromArray(preRotation);\n      preRotation = new Quaternion().setFromEuler(preRotation);\n    }\n    if (postRotation !== void 0) {\n      postRotation = postRotation.map(MathUtils.degToRad);\n      postRotation.push(eulerOrder);\n      postRotation = new Euler().fromArray(postRotation);\n      postRotation = new Quaternion().setFromEuler(postRotation).invert();\n    }\n    const quaternion = new Quaternion();\n    const euler = new Euler();\n    const quaternionValues = [];\n    for (let i = 0; i < values.length; i += 3) {\n      euler.set(values[i], values[i + 1], values[i + 2], eulerOrder);\n      quaternion.setFromEuler(euler);\n      if (preRotation !== void 0) quaternion.premultiply(preRotation);\n      if (postRotation !== void 0) quaternion.multiply(postRotation);\n      quaternion.toArray(quaternionValues, i / 3 * 4);\n    }\n    return new QuaternionKeyframeTrack(modelName + \".quaternion\", times, quaternionValues);\n  }\n  generateMorphTrack(rawTracks) {\n    const curves = rawTracks.DeformPercent.curves.morph;\n    const values = curves.values.map(function (val) {\n      return val / 100;\n    });\n    const morphNum = sceneGraph.getObjectByName(rawTracks.modelName).morphTargetDictionary[rawTracks.morphName];\n    return new NumberKeyframeTrack(rawTracks.modelName + \".morphTargetInfluences[\" + morphNum + \"]\", curves.times, values);\n  }\n  // For all animated objects, times are defined separately for each axis\n  // Here we'll combine the times into one sorted array without duplicates\n  getTimesForAllAxes(curves) {\n    let times = [];\n    if (curves.x !== void 0) times = times.concat(curves.x.times);\n    if (curves.y !== void 0) times = times.concat(curves.y.times);\n    if (curves.z !== void 0) times = times.concat(curves.z.times);\n    times = times.sort(function (a, b) {\n      return a - b;\n    });\n    if (times.length > 1) {\n      let targetIndex = 1;\n      let lastValue = times[0];\n      for (let i = 1; i < times.length; i++) {\n        const currentValue = times[i];\n        if (currentValue !== lastValue) {\n          times[targetIndex] = currentValue;\n          lastValue = currentValue;\n          targetIndex++;\n        }\n      }\n      times = times.slice(0, targetIndex);\n    }\n    return times;\n  }\n  getKeyframeTrackValues(times, curves, initialValue) {\n    const prevValue = initialValue;\n    const values = [];\n    let xIndex = -1;\n    let yIndex = -1;\n    let zIndex = -1;\n    times.forEach(function (time) {\n      if (curves.x) xIndex = curves.x.times.indexOf(time);\n      if (curves.y) yIndex = curves.y.times.indexOf(time);\n      if (curves.z) zIndex = curves.z.times.indexOf(time);\n      if (xIndex !== -1) {\n        const xValue = curves.x.values[xIndex];\n        values.push(xValue);\n        prevValue[0] = xValue;\n      } else {\n        values.push(prevValue[0]);\n      }\n      if (yIndex !== -1) {\n        const yValue = curves.y.values[yIndex];\n        values.push(yValue);\n        prevValue[1] = yValue;\n      } else {\n        values.push(prevValue[1]);\n      }\n      if (zIndex !== -1) {\n        const zValue = curves.z.values[zIndex];\n        values.push(zValue);\n        prevValue[2] = zValue;\n      } else {\n        values.push(prevValue[2]);\n      }\n    });\n    return values;\n  }\n  // Rotations are defined as Euler angles which can have values  of any size\n  // These will be converted to quaternions which don't support values greater than\n  // PI, so we'll interpolate large rotations\n  interpolateRotations(curve) {\n    for (let i = 1; i < curve.values.length; i++) {\n      const initialValue = curve.values[i - 1];\n      const valuesSpan = curve.values[i] - initialValue;\n      const absoluteSpan = Math.abs(valuesSpan);\n      if (absoluteSpan >= 180) {\n        const numSubIntervals = absoluteSpan / 180;\n        const step = valuesSpan / numSubIntervals;\n        let nextValue = initialValue + step;\n        const initialTime = curve.times[i - 1];\n        const timeSpan = curve.times[i] - initialTime;\n        const interval = timeSpan / numSubIntervals;\n        let nextTime = initialTime + interval;\n        const interpolatedTimes = [];\n        const interpolatedValues = [];\n        while (nextTime < curve.times[i]) {\n          interpolatedTimes.push(nextTime);\n          nextTime += interval;\n          interpolatedValues.push(nextValue);\n          nextValue += step;\n        }\n        curve.times = inject(curve.times, i, interpolatedTimes);\n        curve.values = inject(curve.values, i, interpolatedValues);\n      }\n    }\n  }\n}\nclass TextParser {\n  getPrevNode() {\n    return this.nodeStack[this.currentIndent - 2];\n  }\n  getCurrentNode() {\n    return this.nodeStack[this.currentIndent - 1];\n  }\n  getCurrentProp() {\n    return this.currentProp;\n  }\n  pushStack(node) {\n    this.nodeStack.push(node);\n    this.currentIndent += 1;\n  }\n  popStack() {\n    this.nodeStack.pop();\n    this.currentIndent -= 1;\n  }\n  setCurrentProp(val, name) {\n    this.currentProp = val;\n    this.currentPropName = name;\n  }\n  parse(text) {\n    this.currentIndent = 0;\n    this.allNodes = new FBXTree();\n    this.nodeStack = [];\n    this.currentProp = [];\n    this.currentPropName = \"\";\n    const scope = this;\n    const split = text.split(/[\\r\\n]+/);\n    split.forEach(function (line, i) {\n      const matchComment = line.match(/^[\\s\\t]*;/);\n      const matchEmpty = line.match(/^[\\s\\t]*$/);\n      if (matchComment || matchEmpty) return;\n      const matchBeginning = line.match(\"^\\\\t{\" + scope.currentIndent + \"}(\\\\w+):(.*){\", \"\");\n      const matchProperty = line.match(\"^\\\\t{\" + scope.currentIndent + \"}(\\\\w+):[\\\\s\\\\t\\\\r\\\\n](.*)\");\n      const matchEnd = line.match(\"^\\\\t{\" + (scope.currentIndent - 1) + \"}}\");\n      if (matchBeginning) {\n        scope.parseNodeBegin(line, matchBeginning);\n      } else if (matchProperty) {\n        scope.parseNodeProperty(line, matchProperty, split[++i]);\n      } else if (matchEnd) {\n        scope.popStack();\n      } else if (line.match(/^[^\\s\\t}]/)) {\n        scope.parseNodePropertyContinued(line);\n      }\n    });\n    return this.allNodes;\n  }\n  parseNodeBegin(line, property) {\n    const nodeName = property[1].trim().replace(/^\"/, \"\").replace(/\"$/, \"\");\n    const nodeAttrs = property[2].split(\",\").map(function (attr) {\n      return attr.trim().replace(/^\"/, \"\").replace(/\"$/, \"\");\n    });\n    const node = {\n      name: nodeName\n    };\n    const attrs = this.parseNodeAttr(nodeAttrs);\n    const currentNode = this.getCurrentNode();\n    if (this.currentIndent === 0) {\n      this.allNodes.add(nodeName, node);\n    } else {\n      if (nodeName in currentNode) {\n        if (nodeName === \"PoseNode\") {\n          currentNode.PoseNode.push(node);\n        } else if (currentNode[nodeName].id !== void 0) {\n          currentNode[nodeName] = {};\n          currentNode[nodeName][currentNode[nodeName].id] = currentNode[nodeName];\n        }\n        if (attrs.id !== \"\") currentNode[nodeName][attrs.id] = node;\n      } else if (typeof attrs.id === \"number\") {\n        currentNode[nodeName] = {};\n        currentNode[nodeName][attrs.id] = node;\n      } else if (nodeName !== \"Properties70\") {\n        if (nodeName === \"PoseNode\") currentNode[nodeName] = [node];else currentNode[nodeName] = node;\n      }\n    }\n    if (typeof attrs.id === \"number\") node.id = attrs.id;\n    if (attrs.name !== \"\") node.attrName = attrs.name;\n    if (attrs.type !== \"\") node.attrType = attrs.type;\n    this.pushStack(node);\n  }\n  parseNodeAttr(attrs) {\n    let id = attrs[0];\n    if (attrs[0] !== \"\") {\n      id = parseInt(attrs[0]);\n      if (isNaN(id)) {\n        id = attrs[0];\n      }\n    }\n    let name = \"\",\n      type = \"\";\n    if (attrs.length > 1) {\n      name = attrs[1].replace(/^(\\w+)::/, \"\");\n      type = attrs[2];\n    }\n    return {\n      id,\n      name,\n      type\n    };\n  }\n  parseNodeProperty(line, property, contentLine) {\n    let propName = property[1].replace(/^\"/, \"\").replace(/\"$/, \"\").trim();\n    let propValue = property[2].replace(/^\"/, \"\").replace(/\"$/, \"\").trim();\n    if (propName === \"Content\" && propValue === \",\") {\n      propValue = contentLine.replace(/\"/g, \"\").replace(/,$/, \"\").trim();\n    }\n    const currentNode = this.getCurrentNode();\n    const parentName = currentNode.name;\n    if (parentName === \"Properties70\") {\n      this.parseNodeSpecialProperty(line, propName, propValue);\n      return;\n    }\n    if (propName === \"C\") {\n      const connProps = propValue.split(\",\").slice(1);\n      const from = parseInt(connProps[0]);\n      const to = parseInt(connProps[1]);\n      let rest = propValue.split(\",\").slice(3);\n      rest = rest.map(function (elem) {\n        return elem.trim().replace(/^\"/, \"\");\n      });\n      propName = \"connections\";\n      propValue = [from, to];\n      append(propValue, rest);\n      if (currentNode[propName] === void 0) {\n        currentNode[propName] = [];\n      }\n    }\n    if (propName === \"Node\") currentNode.id = propValue;\n    if (propName in currentNode && Array.isArray(currentNode[propName])) {\n      currentNode[propName].push(propValue);\n    } else {\n      if (propName !== \"a\") currentNode[propName] = propValue;else currentNode.a = propValue;\n    }\n    this.setCurrentProp(currentNode, propName);\n    if (propName === \"a\" && propValue.slice(-1) !== \",\") {\n      currentNode.a = parseNumberArray(propValue);\n    }\n  }\n  parseNodePropertyContinued(line) {\n    const currentNode = this.getCurrentNode();\n    currentNode.a += line;\n    if (line.slice(-1) !== \",\") {\n      currentNode.a = parseNumberArray(currentNode.a);\n    }\n  }\n  // parse \"Property70\"\n  parseNodeSpecialProperty(line, propName, propValue) {\n    const props = propValue.split('\",').map(function (prop) {\n      return prop.trim().replace(/^\\\"/, \"\").replace(/\\s/, \"_\");\n    });\n    const innerPropName = props[0];\n    const innerPropType1 = props[1];\n    const innerPropType2 = props[2];\n    const innerPropFlag = props[3];\n    let innerPropValue = props[4];\n    switch (innerPropType1) {\n      case \"int\":\n      case \"enum\":\n      case \"bool\":\n      case \"ULongLong\":\n      case \"double\":\n      case \"Number\":\n      case \"FieldOfView\":\n        innerPropValue = parseFloat(innerPropValue);\n        break;\n      case \"Color\":\n      case \"ColorRGB\":\n      case \"Vector3D\":\n      case \"Lcl_Translation\":\n      case \"Lcl_Rotation\":\n      case \"Lcl_Scaling\":\n        innerPropValue = parseNumberArray(innerPropValue);\n        break;\n    }\n    this.getPrevNode()[innerPropName] = {\n      type: innerPropType1,\n      type2: innerPropType2,\n      flag: innerPropFlag,\n      value: innerPropValue\n    };\n    this.setCurrentProp(this.getPrevNode(), innerPropName);\n  }\n}\nclass BinaryParser {\n  parse(buffer) {\n    const reader = new BinaryReader(buffer);\n    reader.skip(23);\n    const version = reader.getUint32();\n    if (version < 6400) {\n      throw new Error(\"THREE.FBXLoader: FBX version not supported, FileVersion: \" + version);\n    }\n    const allNodes = new FBXTree();\n    while (!this.endOfContent(reader)) {\n      const node = this.parseNode(reader, version);\n      if (node !== null) allNodes.add(node.name, node);\n    }\n    return allNodes;\n  }\n  // Check if reader has reached the end of content.\n  endOfContent(reader) {\n    if (reader.size() % 16 === 0) {\n      return (reader.getOffset() + 160 + 16 & ~15) >= reader.size();\n    } else {\n      return reader.getOffset() + 160 + 16 >= reader.size();\n    }\n  }\n  // recursively parse nodes until the end of the file is reached\n  parseNode(reader, version) {\n    const node = {};\n    const endOffset = version >= 7500 ? reader.getUint64() : reader.getUint32();\n    const numProperties = version >= 7500 ? reader.getUint64() : reader.getUint32();\n    version >= 7500 ? reader.getUint64() : reader.getUint32();\n    const nameLen = reader.getUint8();\n    const name = reader.getString(nameLen);\n    if (endOffset === 0) return null;\n    const propertyList = [];\n    for (let i = 0; i < numProperties; i++) {\n      propertyList.push(this.parseProperty(reader));\n    }\n    const id = propertyList.length > 0 ? propertyList[0] : \"\";\n    const attrName = propertyList.length > 1 ? propertyList[1] : \"\";\n    const attrType = propertyList.length > 2 ? propertyList[2] : \"\";\n    node.singleProperty = numProperties === 1 && reader.getOffset() === endOffset ? true : false;\n    while (endOffset > reader.getOffset()) {\n      const subNode = this.parseNode(reader, version);\n      if (subNode !== null) this.parseSubNode(name, node, subNode);\n    }\n    node.propertyList = propertyList;\n    if (typeof id === \"number\") node.id = id;\n    if (attrName !== \"\") node.attrName = attrName;\n    if (attrType !== \"\") node.attrType = attrType;\n    if (name !== \"\") node.name = name;\n    return node;\n  }\n  parseSubNode(name, node, subNode) {\n    if (subNode.singleProperty === true) {\n      const value = subNode.propertyList[0];\n      if (Array.isArray(value)) {\n        node[subNode.name] = subNode;\n        subNode.a = value;\n      } else {\n        node[subNode.name] = value;\n      }\n    } else if (name === \"Connections\" && subNode.name === \"C\") {\n      const array = [];\n      subNode.propertyList.forEach(function (property, i) {\n        if (i !== 0) array.push(property);\n      });\n      if (node.connections === void 0) {\n        node.connections = [];\n      }\n      node.connections.push(array);\n    } else if (subNode.name === \"Properties70\") {\n      const keys = Object.keys(subNode);\n      keys.forEach(function (key) {\n        node[key] = subNode[key];\n      });\n    } else if (name === \"Properties70\" && subNode.name === \"P\") {\n      let innerPropName = subNode.propertyList[0];\n      let innerPropType1 = subNode.propertyList[1];\n      const innerPropType2 = subNode.propertyList[2];\n      const innerPropFlag = subNode.propertyList[3];\n      let innerPropValue;\n      if (innerPropName.indexOf(\"Lcl \") === 0) innerPropName = innerPropName.replace(\"Lcl \", \"Lcl_\");\n      if (innerPropType1.indexOf(\"Lcl \") === 0) innerPropType1 = innerPropType1.replace(\"Lcl \", \"Lcl_\");\n      if (innerPropType1 === \"Color\" || innerPropType1 === \"ColorRGB\" || innerPropType1 === \"Vector\" || innerPropType1 === \"Vector3D\" || innerPropType1.indexOf(\"Lcl_\") === 0) {\n        innerPropValue = [subNode.propertyList[4], subNode.propertyList[5], subNode.propertyList[6]];\n      } else {\n        innerPropValue = subNode.propertyList[4];\n      }\n      node[innerPropName] = {\n        type: innerPropType1,\n        type2: innerPropType2,\n        flag: innerPropFlag,\n        value: innerPropValue\n      };\n    } else if (node[subNode.name] === void 0) {\n      if (typeof subNode.id === \"number\") {\n        node[subNode.name] = {};\n        node[subNode.name][subNode.id] = subNode;\n      } else {\n        node[subNode.name] = subNode;\n      }\n    } else {\n      if (subNode.name === \"PoseNode\") {\n        if (!Array.isArray(node[subNode.name])) {\n          node[subNode.name] = [node[subNode.name]];\n        }\n        node[subNode.name].push(subNode);\n      } else if (node[subNode.name][subNode.id] === void 0) {\n        node[subNode.name][subNode.id] = subNode;\n      }\n    }\n  }\n  parseProperty(reader) {\n    const type = reader.getString(1);\n    let length;\n    switch (type) {\n      case \"C\":\n        return reader.getBoolean();\n      case \"D\":\n        return reader.getFloat64();\n      case \"F\":\n        return reader.getFloat32();\n      case \"I\":\n        return reader.getInt32();\n      case \"L\":\n        return reader.getInt64();\n      case \"R\":\n        length = reader.getUint32();\n        return reader.getArrayBuffer(length);\n      case \"S\":\n        length = reader.getUint32();\n        return reader.getString(length);\n      case \"Y\":\n        return reader.getInt16();\n      case \"b\":\n      case \"c\":\n      case \"d\":\n      case \"f\":\n      case \"i\":\n      case \"l\":\n        const arrayLength = reader.getUint32();\n        const encoding = reader.getUint32();\n        const compressedLength = reader.getUint32();\n        if (encoding === 0) {\n          switch (type) {\n            case \"b\":\n            case \"c\":\n              return reader.getBooleanArray(arrayLength);\n            case \"d\":\n              return reader.getFloat64Array(arrayLength);\n            case \"f\":\n              return reader.getFloat32Array(arrayLength);\n            case \"i\":\n              return reader.getInt32Array(arrayLength);\n            case \"l\":\n              return reader.getInt64Array(arrayLength);\n          }\n        }\n        const data = unzlibSync(new Uint8Array(reader.getArrayBuffer(compressedLength)));\n        const reader2 = new BinaryReader(data.buffer);\n        switch (type) {\n          case \"b\":\n          case \"c\":\n            return reader2.getBooleanArray(arrayLength);\n          case \"d\":\n            return reader2.getFloat64Array(arrayLength);\n          case \"f\":\n            return reader2.getFloat32Array(arrayLength);\n          case \"i\":\n            return reader2.getInt32Array(arrayLength);\n          case \"l\":\n            return reader2.getInt64Array(arrayLength);\n        }\n      default:\n        throw new Error(\"THREE.FBXLoader: Unknown property type \" + type);\n    }\n  }\n}\nclass BinaryReader {\n  constructor(buffer, littleEndian) {\n    this.dv = new DataView(buffer);\n    this.offset = 0;\n    this.littleEndian = littleEndian !== void 0 ? littleEndian : true;\n  }\n  getOffset() {\n    return this.offset;\n  }\n  size() {\n    return this.dv.buffer.byteLength;\n  }\n  skip(length) {\n    this.offset += length;\n  }\n  // seems like true/false representation depends on exporter.\n  // true: 1 or 'Y'(=0x59), false: 0 or 'T'(=0x54)\n  // then sees LSB.\n  getBoolean() {\n    return (this.getUint8() & 1) === 1;\n  }\n  getBooleanArray(size) {\n    const a = [];\n    for (let i = 0; i < size; i++) {\n      a.push(this.getBoolean());\n    }\n    return a;\n  }\n  getUint8() {\n    const value = this.dv.getUint8(this.offset);\n    this.offset += 1;\n    return value;\n  }\n  getInt16() {\n    const value = this.dv.getInt16(this.offset, this.littleEndian);\n    this.offset += 2;\n    return value;\n  }\n  getInt32() {\n    const value = this.dv.getInt32(this.offset, this.littleEndian);\n    this.offset += 4;\n    return value;\n  }\n  getInt32Array(size) {\n    const a = [];\n    for (let i = 0; i < size; i++) {\n      a.push(this.getInt32());\n    }\n    return a;\n  }\n  getUint32() {\n    const value = this.dv.getUint32(this.offset, this.littleEndian);\n    this.offset += 4;\n    return value;\n  }\n  // JavaScript doesn't support 64-bit integer so calculate this here\n  // 1 << 32 will return 1 so using multiply operation instead here.\n  // There's a possibility that this method returns wrong value if the value\n  // is out of the range between Number.MAX_SAFE_INTEGER and Number.MIN_SAFE_INTEGER.\n  // TODO: safely handle 64-bit integer\n  getInt64() {\n    let low, high;\n    if (this.littleEndian) {\n      low = this.getUint32();\n      high = this.getUint32();\n    } else {\n      high = this.getUint32();\n      low = this.getUint32();\n    }\n    if (high & 2147483648) {\n      high = ~high & 4294967295;\n      low = ~low & 4294967295;\n      if (low === 4294967295) high = high + 1 & 4294967295;\n      low = low + 1 & 4294967295;\n      return -(high * 4294967296 + low);\n    }\n    return high * 4294967296 + low;\n  }\n  getInt64Array(size) {\n    const a = [];\n    for (let i = 0; i < size; i++) {\n      a.push(this.getInt64());\n    }\n    return a;\n  }\n  // Note: see getInt64() comment\n  getUint64() {\n    let low, high;\n    if (this.littleEndian) {\n      low = this.getUint32();\n      high = this.getUint32();\n    } else {\n      high = this.getUint32();\n      low = this.getUint32();\n    }\n    return high * 4294967296 + low;\n  }\n  getFloat32() {\n    const value = this.dv.getFloat32(this.offset, this.littleEndian);\n    this.offset += 4;\n    return value;\n  }\n  getFloat32Array(size) {\n    const a = [];\n    for (let i = 0; i < size; i++) {\n      a.push(this.getFloat32());\n    }\n    return a;\n  }\n  getFloat64() {\n    const value = this.dv.getFloat64(this.offset, this.littleEndian);\n    this.offset += 8;\n    return value;\n  }\n  getFloat64Array(size) {\n    const a = [];\n    for (let i = 0; i < size; i++) {\n      a.push(this.getFloat64());\n    }\n    return a;\n  }\n  getArrayBuffer(size) {\n    const value = this.dv.buffer.slice(this.offset, this.offset + size);\n    this.offset += size;\n    return value;\n  }\n  getString(size) {\n    let a = [];\n    for (let i = 0; i < size; i++) {\n      a[i] = this.getUint8();\n    }\n    const nullByte = a.indexOf(0);\n    if (nullByte >= 0) a = a.slice(0, nullByte);\n    return decodeText(new Uint8Array(a));\n  }\n}\nclass FBXTree {\n  add(key, val) {\n    this[key] = val;\n  }\n}\nfunction isFbxFormatBinary(buffer) {\n  const CORRECT = \"Kaydara FBX Binary  \\0\";\n  return buffer.byteLength >= CORRECT.length && CORRECT === convertArrayBufferToString(buffer, 0, CORRECT.length);\n}\nfunction isFbxFormatASCII(text) {\n  const CORRECT = [\"K\", \"a\", \"y\", \"d\", \"a\", \"r\", \"a\", \"\\\\\", \"F\", \"B\", \"X\", \"\\\\\", \"B\", \"i\", \"n\", \"a\", \"r\", \"y\", \"\\\\\", \"\\\\\"];\n  let cursor = 0;\n  function read(offset) {\n    const result = text[offset - 1];\n    text = text.slice(cursor + offset);\n    cursor++;\n    return result;\n  }\n  for (let i = 0; i < CORRECT.length; ++i) {\n    const num = read(1);\n    if (num === CORRECT[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction getFbxVersion(text) {\n  const versionRegExp = /FBXVersion: (\\d+)/;\n  const match = text.match(versionRegExp);\n  if (match) {\n    const version = parseInt(match[1]);\n    return version;\n  }\n  throw new Error(\"THREE.FBXLoader: Cannot find the version number for the file given.\");\n}\nfunction convertFBXTimeToSeconds(time) {\n  return time / 46186158e3;\n}\nconst dataArray = [];\nfunction getData(polygonVertexIndex, polygonIndex, vertexIndex, infoObject) {\n  let index;\n  switch (infoObject.mappingType) {\n    case \"ByPolygonVertex\":\n      index = polygonVertexIndex;\n      break;\n    case \"ByPolygon\":\n      index = polygonIndex;\n      break;\n    case \"ByVertice\":\n      index = vertexIndex;\n      break;\n    case \"AllSame\":\n      index = infoObject.indices[0];\n      break;\n    default:\n      console.warn(\"THREE.FBXLoader: unknown attribute mapping type \" + infoObject.mappingType);\n  }\n  if (infoObject.referenceType === \"IndexToDirect\") index = infoObject.indices[index];\n  const from = index * infoObject.dataSize;\n  const to = from + infoObject.dataSize;\n  return slice(dataArray, infoObject.buffer, from, to);\n}\nconst tempEuler = /* @__PURE__ */new Euler();\nconst tempVec = /* @__PURE__ */new Vector3();\nfunction generateTransform(transformData) {\n  const lTranslationM = new Matrix4();\n  const lPreRotationM = new Matrix4();\n  const lRotationM = new Matrix4();\n  const lPostRotationM = new Matrix4();\n  const lScalingM = new Matrix4();\n  const lScalingPivotM = new Matrix4();\n  const lScalingOffsetM = new Matrix4();\n  const lRotationOffsetM = new Matrix4();\n  const lRotationPivotM = new Matrix4();\n  const lParentGX = new Matrix4();\n  const lParentLX = new Matrix4();\n  const lGlobalT = new Matrix4();\n  const inheritType = transformData.inheritType ? transformData.inheritType : 0;\n  if (transformData.translation) lTranslationM.setPosition(tempVec.fromArray(transformData.translation));\n  if (transformData.preRotation) {\n    const array = transformData.preRotation.map(MathUtils.degToRad);\n    array.push(transformData.eulerOrder);\n    lPreRotationM.makeRotationFromEuler(tempEuler.fromArray(array));\n  }\n  if (transformData.rotation) {\n    const array = transformData.rotation.map(MathUtils.degToRad);\n    array.push(transformData.eulerOrder);\n    lRotationM.makeRotationFromEuler(tempEuler.fromArray(array));\n  }\n  if (transformData.postRotation) {\n    const array = transformData.postRotation.map(MathUtils.degToRad);\n    array.push(transformData.eulerOrder);\n    lPostRotationM.makeRotationFromEuler(tempEuler.fromArray(array));\n    lPostRotationM.invert();\n  }\n  if (transformData.scale) lScalingM.scale(tempVec.fromArray(transformData.scale));\n  if (transformData.scalingOffset) lScalingOffsetM.setPosition(tempVec.fromArray(transformData.scalingOffset));\n  if (transformData.scalingPivot) lScalingPivotM.setPosition(tempVec.fromArray(transformData.scalingPivot));\n  if (transformData.rotationOffset) lRotationOffsetM.setPosition(tempVec.fromArray(transformData.rotationOffset));\n  if (transformData.rotationPivot) lRotationPivotM.setPosition(tempVec.fromArray(transformData.rotationPivot));\n  if (transformData.parentMatrixWorld) {\n    lParentLX.copy(transformData.parentMatrix);\n    lParentGX.copy(transformData.parentMatrixWorld);\n  }\n  const lLRM = lPreRotationM.clone().multiply(lRotationM).multiply(lPostRotationM);\n  const lParentGRM = new Matrix4();\n  lParentGRM.extractRotation(lParentGX);\n  const lParentTM = new Matrix4();\n  lParentTM.copyPosition(lParentGX);\n  const lParentGRSM = lParentTM.clone().invert().multiply(lParentGX);\n  const lParentGSM = lParentGRM.clone().invert().multiply(lParentGRSM);\n  const lLSM = lScalingM;\n  const lGlobalRS = new Matrix4();\n  if (inheritType === 0) {\n    lGlobalRS.copy(lParentGRM).multiply(lLRM).multiply(lParentGSM).multiply(lLSM);\n  } else if (inheritType === 1) {\n    lGlobalRS.copy(lParentGRM).multiply(lParentGSM).multiply(lLRM).multiply(lLSM);\n  } else {\n    const lParentLSM = new Matrix4().scale(new Vector3().setFromMatrixScale(lParentLX));\n    const lParentLSM_inv = lParentLSM.clone().invert();\n    const lParentGSM_noLocal = lParentGSM.clone().multiply(lParentLSM_inv);\n    lGlobalRS.copy(lParentGRM).multiply(lLRM).multiply(lParentGSM_noLocal).multiply(lLSM);\n  }\n  const lRotationPivotM_inv = lRotationPivotM.clone().invert();\n  const lScalingPivotM_inv = lScalingPivotM.clone().invert();\n  let lTransform = lTranslationM.clone().multiply(lRotationOffsetM).multiply(lRotationPivotM).multiply(lPreRotationM).multiply(lRotationM).multiply(lPostRotationM).multiply(lRotationPivotM_inv).multiply(lScalingOffsetM).multiply(lScalingPivotM).multiply(lScalingM).multiply(lScalingPivotM_inv);\n  const lLocalTWithAllPivotAndOffsetInfo = new Matrix4().copyPosition(lTransform);\n  const lGlobalTranslation = lParentGX.clone().multiply(lLocalTWithAllPivotAndOffsetInfo);\n  lGlobalT.copyPosition(lGlobalTranslation);\n  lTransform = lGlobalT.clone().multiply(lGlobalRS);\n  lTransform.premultiply(lParentGX.invert());\n  return lTransform;\n}\nfunction getEulerOrder(order) {\n  order = order || 0;\n  const enums = [\"ZYX\",\n  // -> XYZ extrinsic\n  \"YZX\",\n  // -> XZY extrinsic\n  \"XZY\",\n  // -> YZX extrinsic\n  \"ZXY\",\n  // -> YXZ extrinsic\n  \"YXZ\",\n  // -> ZXY extrinsic\n  \"XYZ\"\n  // -> ZYX extrinsic\n  //'SphericXYZ', // not possible to support\n  ];\n  if (order === 6) {\n    console.warn(\"THREE.FBXLoader: unsupported Euler Order: Spherical XYZ. Animations and rotations may be incorrect.\");\n    return enums[0];\n  }\n  return enums[order];\n}\nfunction parseNumberArray(value) {\n  const array = value.split(\",\").map(function (val) {\n    return parseFloat(val);\n  });\n  return array;\n}\nfunction convertArrayBufferToString(buffer, from, to) {\n  if (from === void 0) from = 0;\n  if (to === void 0) to = buffer.byteLength;\n  return decodeText(new Uint8Array(buffer, from, to));\n}\nfunction append(a, b) {\n  for (let i = 0, j = a.length, l = b.length; i < l; i++, j++) {\n    a[j] = b[i];\n  }\n}\nfunction slice(a, b, from, to) {\n  for (let i = from, j = 0; i < to; i++, j++) {\n    a[j] = b[i];\n  }\n  return a;\n}\nfunction inject(a1, index, a2) {\n  return a1.slice(0, index).concat(a2).concat(a1.slice(index));\n}\nexport { FBXLoader };", "map": {"version": 3, "names": ["fbxTree", "connections", "sceneGraph", "FBXLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parse", "e", "console", "error", "itemError", "FBXBuffer", "isFbxFormatBinary", "<PERSON>ary<PERSON><PERSON><PERSON>", "FBXText", "convertArrayBufferToString", "isFbxFormatASCII", "Error", "getFbxVersion", "<PERSON><PERSON><PERSON><PERSON>", "textureLoader", "TextureLoader", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "FBXTreeP<PERSON>er", "parseConnections", "images", "parseImages", "textures", "parseTextures", "materials", "parseMaterials", "deformers", "parseDeformers", "geometryMap", "Geo<PERSON><PERSON><PERSON><PERSON>", "parseScene", "connectionMap", "Map", "rawConnections", "Connections", "for<PERSON>ach", "rawConnection", "fromID", "toID", "relationship", "has", "set", "parents", "children", "parentRelationship", "ID", "get", "push", "childRelationship", "blobs", "Objects", "videoNodes", "Video", "nodeID", "videoNode", "id", "parseInt", "RelativeFilename", "Filename", "arrayBufferContent", "Content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "base64Content", "image", "parseImage", "filename", "split", "pop", "content", "fileName", "extension", "slice", "lastIndexOf", "toLowerCase", "type", "<PERSON><PERSON><PERSON><PERSON>", "warn", "array", "Uint8Array", "window", "URL", "createObjectURL", "Blob", "textureMap", "textureNodes", "Texture", "texture", "parseTexture", "textureNode", "loadTexture", "name", "attrName", "wrapModeU", "WrapModeU", "wrapModeV", "WrapModeV", "valueU", "value", "valueV", "wrapS", "RepeatWrapping", "ClampToEdgeWrapping", "wrapT", "values", "Sc<PERSON>", "repeat", "x", "y", "currentPath", "length", "indexOf", "FileName", "materialMap", "materialNodes", "Material", "material", "parseMaterial", "materialNode", "ShadingModel", "parameters", "parseParameters", "MeshPhongMaterial", "MeshLambertMaterial", "set<PERSON><PERSON><PERSON>", "BumpFactor", "bumpScale", "Diffuse", "color", "Color", "fromArray", "DiffuseColor", "DisplacementFactor", "displacementScale", "Emissive", "emissive", "EmissiveColor", "EmissiveFactor", "emissiveIntensity", "parseFloat", "Opacity", "opacity", "transparent", "ReflectionFactor", "reflectivity", "Shininess", "shininess", "Specular", "specular", "SpecularColor", "child", "bumpMap", "getTexture", "aoMap", "map", "colorSpace", "encoding", "displacementMap", "emissiveMap", "normalMap", "envMap", "mapping", "EquirectangularReflectionMapping", "specularMap", "alphaMap", "LayeredTexture", "skeletons", "morphTargets", "DeformerNodes", "Deformer", "deformerNode", "relationships", "attrType", "skeleton", "parseSkeleton", "geometryID", "morph<PERSON>arget", "rawTargets", "parseMorphTargets", "deformerNodes", "rawBones", "boneNode", "rawBone", "indices", "weights", "transformLink", "Matrix4", "TransformLink", "a", "Indexes", "Weights", "bones", "rawMorphTargets", "i", "morphTargetNode", "rawMorphTarget", "initialWeight", "DeformPercent", "fullWeights", "FullWeights", "geoID", "filter", "child2", "Group", "modelMap", "parseModels", "modelNodes", "Model", "model", "modelNode", "setLookAtProperties", "parentConnections", "connection", "parent", "add", "bindSkeleton", "createAmbientLight", "traverse", "node", "userData", "transformData", "parentMatrix", "matrix", "parentMatrixWorld", "matrixWorld", "transform", "generateTransform", "applyMatrix4", "updateWorldMatrix", "animations", "Animation<PERSON>arser", "isGroup", "buildSkeleton", "createCamera", "createLight", "<PERSON><PERSON><PERSON>", "createCurve", "Bone", "PropertyBinding", "sanitizeNodeName", "getTransformData", "bone", "subBone", "copy", "cameraAttribute", "attr", "NodeAttribute", "Object3D", "CameraProjectionType", "nearClippingPlane", "NearPlane", "farClippingPlane", "FarPlane", "width", "innerWidth", "height", "innerHeight", "AspectWidth", "AspectHeight", "aspect", "fov", "FieldOfView", "focal<PERSON>ength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PerspectiveCamera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OrthographicCamera", "lightAttribute", "LightType", "intensity", "Intensity", "CastLightOnObject", "distance", "FarAttenuationEnd", "EnableFarAttenuation", "decay", "PointLight", "DirectionalLight", "angle", "Math", "PI", "InnerAngle", "MathUtils", "degToRad", "penumbra", "OuterAngle", "max", "SpotLight", "CastShadows", "<PERSON><PERSON><PERSON><PERSON>", "geometry", "attributes", "material2", "vertexColors", "FBX_Deformer", "<PERSON><PERSON><PERSON><PERSON>", "normalizeSkinWeights", "<PERSON><PERSON>", "reduce", "geo", "LineBasicMaterial", "linewidth", "Line", "inheritType", "InheritType", "eulerOrder", "getEulerOrder", "RotationOrder", "translation", "Lcl_Translation", "preRotation", "PreRotation", "rotation", "Lcl_Rotation", "postRotation", "PostRotation", "scale", "Lcl_Scaling", "scalingOffset", "ScalingOffset", "scalingPivot", "ScalingPivot", "rotationOffset", "RotationOffset", "rotationPivot", "RotationPivot", "lookAtTarget", "pos", "target", "position", "lookAt", "Vector3", "bindMatrices", "parsePoseNodes", "geoRelationships", "geoConnParent", "bind", "Skeleton", "BindPoseNode", "Pose", "NbPoseNodes", "poseNodes", "PoseNode", "Array", "isArray", "poseNode", "Node", "Matrix", "GlobalSettings", "ambientColor", "AmbientColor", "r", "g", "b", "AmbientLight", "geoNodes", "Geometry", "parseGeometry", "geoNode", "parseMeshGeometry", "parseNurbsGeometry", "skeleton2", "GeometricTranslation", "GeometricRotation", "GeometricScaling", "genGeometry", "preTransform", "BufferGeometry", "geoInfo", "parseGeoNode", "buffers", "genBuffers", "positionAttribute", "Float32BufferAttribute", "vertex", "setAttribute", "colors", "Uint16BufferAttribute", "weightsIndices", "vertexWeights", "normal", "normalMatrix", "Matrix3", "getNormalMatrix", "normalAttribute", "applyNormalMatrix", "uvs", "uv<PERSON><PERSON><PERSON>", "UV1", "mappingType", "prevMaterialIndex", "materialIndex", "startIndex", "currentIndex", "addGroup", "groups", "lastGroup", "lastIndex", "start", "count", "addMorphTargets", "vertexPositions", "Vertices", "vertexIndices", "PolygonVertexIndex", "LayerElementColor", "parseVertexColors", "LayerElementMaterial", "parseMaterialIndices", "LayerElementNormal", "parseNormals", "LayerElementUV", "uv", "UV", "parseUVs", "weightTable", "index", "j", "weight", "polygonIndex", "face<PERSON><PERSON><PERSON>", "displayedWeightsWarning", "facePositionIndexes", "faceNormals", "faceColors", "faceUVs", "faceWeights", "faceWeightIndices", "vertexIndex", "polygonVertexIndex", "endOfFace", "weightIndices", "data", "getData", "wt", "wIndex", "Weight", "weightIndex", "currentWeight", "comparedWeight", "comparedWeightIndex", "comparedWeightArray", "tmp", "genFace", "parentGeo", "parentGeoNode", "morphTargetsRelative", "morphAttributes", "rawTarget", "morphGeoNode", "genMorphGeometry", "morphPositionsSparse", "morphPositions", "Float32Array", "morphIndex", "morphGeoInfo", "morphBuffers", "NormalNode", "MappingInformationType", "referenceType", "ReferenceInformationType", "Normals", "indexBuffer", "NormalIndex", "NormalsIndex", "dataSize", "UVNode", "UVIndex", "ColorNode", "Colors", "ColorIndex", "MaterialNode", "materialIndexBuffer", "Materials", "materialIndices", "NURBSCurve", "order", "Order", "isNaN", "degree", "knots", "KnotVector", "controlPoints", "pointsValues", "Points", "l", "Vector4", "startKnot", "endKnot", "Form", "curve", "points", "getPoints", "setFromPoints", "animationClips", "rawClips", "parseClips", "key", "rawClip", "clip", "addClip", "AnimationCurve", "curveNodesMap", "parseAnimationCurveNodes", "parseAnimationCurves", "layersMap", "parseAnimationLayers", "parseAnimStacks", "rawCurveNodes", "AnimationCurveNode", "rawCurveNode", "match", "curveNode", "curves", "rawCurves", "animationCurve", "times", "KeyTime", "convertFBXTimeToSeconds", "KeyValueFloat", "animationCurveID", "animationCurveRelationship", "rawLayers", "AnimationLayer", "layerCurveNodes", "z", "modelID", "rawModel", "toString", "modelName", "initialPosition", "initialRotation", "initialScale", "morph", "deformerID", "morpherID", "morphName", "rawStacks", "AnimationStack", "layer", "tracks", "rawTracks", "concat", "generateTracks", "AnimationClip", "Quaternion", "decompose", "toArray", "<PERSON>uler", "setFromQuaternion", "T", "Object", "keys", "positionTrack", "generateVectorTrack", "R", "rotationTrack", "generateRotationTrack", "S", "scaleTrack", "morphTrack", "generateMorphTrack", "initialValue", "getTimesForAllAxes", "getKeyframeTrackValues", "VectorKeyframeTrack", "interpolateRotations", "setFromEuler", "invert", "quaternion", "euler", "quaternionValues", "premultiply", "multiply", "QuaternionKeyframeTrack", "val", "morphNum", "getObjectByName", "morphTargetDictionary", "NumberKeyframeTrack", "sort", "targetIndex", "lastValue", "currentValue", "prevValue", "xIndex", "yIndex", "zIndex", "time", "xValue", "yValue", "zValue", "valuesSpan", "absoluteSpan", "abs", "numSubIntervals", "step", "nextValue", "initialTime", "timeSpan", "interval", "nextTime", "interpolatedTimes", "interpolatedValues", "inject", "getPrevNode", "nodeStack", "currentIndent", "getCurrentNode", "getCurrentProp", "currentProp", "pushStack", "popStack", "setCurrentProp", "currentPropName", "text", "allNodes", "FBXTree", "line", "matchComment", "matchEmpty", "matchBeginning", "matchProperty", "matchEnd", "parseNodeBegin", "parseNodeProperty", "parseNodePropertyContinued", "property", "nodeName", "trim", "replace", "nodeAttrs", "attrs", "parseNodeAttr", "currentNode", "contentLine", "propName", "propValue", "parentName", "parseNodeSpecialProperty", "connProps", "from", "to", "rest", "elem", "append", "parseNumberArray", "props", "prop", "innerPropName", "innerPropType1", "innerPropType2", "innerPropFlag", "innerPropValue", "type2", "flag", "reader", "BinaryReader", "skip", "version", "getUint32", "endOfContent", "parseNode", "size", "getOffset", "endOffset", "getUint64", "numProperties", "nameLen", "getUint8", "getString", "propertyList", "parseProperty", "singleProperty", "subNode", "parseSubNode", "getBoolean", "getFloat64", "getFloat32", "getInt32", "getInt64", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getInt16", "array<PERSON>ength", "compressed<PERSON>ength", "getBooleanArray", "getFloat64Array", "getFloat32Array", "getInt32Array", "getInt64Array", "unzlibSync", "reader2", "littleEndian", "dv", "DataView", "offset", "low", "high", "nullByte", "decodeText", "CORRECT", "cursor", "read", "result", "num", "versionRegExp", "dataArray", "infoObject", "tempEuler", "tempVec", "lTranslationM", "lPreRotationM", "lRotationM", "lPostRotationM", "lScalingM", "lScalingPivotM", "lScalingOffsetM", "lRotationOffsetM", "lRotationPivotM", "lParentGX", "lParentLX", "lGlobalT", "setPosition", "makeRotationFromEuler", "lLRM", "clone", "lParentGRM", "extractRotation", "lParentTM", "copyPosition", "lParentGRSM", "lParentGSM", "lLSM", "lGlobalRS", "lParentLSM", "setFromMatrixScale", "lParentLSM_inv", "lParentGSM_noLocal", "lRotationPivotM_inv", "lScalingPivotM_inv", "lTransform", "lLocalTWithAllPivotAndOffsetInfo", "lGlobalTranslation", "enums", "a1", "a2"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/FBXLoader.js"], "sourcesContent": ["import {\n  AmbientLight,\n  AnimationClip,\n  Bone,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  EquirectangularReflectionMapping,\n  Euler,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  Line,\n  LineBasicMaterial,\n  Loader,\n  LoaderUtils,\n  MathUtils,\n  Matrix3,\n  Matrix4,\n  Mesh,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  NumberKeyframeTrack,\n  Object3D,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  PropertyBinding,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Skeleton,\n  SkinnedMesh,\n  SpotLight,\n  Texture,\n  TextureLoader,\n  Uint16BufferAttribute,\n  Vector3,\n  Vector4,\n  VectorKeyframeTrack,\n} from 'three'\nimport { unzlibSync } from 'fflate'\nimport { NURBSCurve } from '../curves/NURBSCurve'\nimport { decodeText } from '../_polyfill/LoaderUtils'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * <PERSON><PERSON> loads FBX file and generates Group representing FBX scene.\n * Requires FBX file to be >= 7.0 and in ASCII or >= 6400 in Binary format\n * Versions lower than this may load but will probably have errors\n *\n * Needs Support:\n *  Morph normals / blend shape normals\n *\n * FBX format references:\n * \thttps://help.autodesk.com/view/FBX/2017/ENU/?guid=__cpp_ref_index_html (C++ SDK reference)\n *\n * Binary format specification:\n *\thttps://code.blender.org/2013/08/fbx-binary-file-format-specification/\n */\n\nlet fbxTree\nlet connections\nlet sceneGraph\n\nclass FBXLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(FBXBuffer, path) {\n    if (isFbxFormatBinary(FBXBuffer)) {\n      fbxTree = new BinaryParser().parse(FBXBuffer)\n    } else {\n      const FBXText = convertArrayBufferToString(FBXBuffer)\n\n      if (!isFbxFormatASCII(FBXText)) {\n        throw new Error('THREE.FBXLoader: Unknown format.')\n      }\n\n      if (getFbxVersion(FBXText) < 7000) {\n        throw new Error('THREE.FBXLoader: FBX version not supported, FileVersion: ' + getFbxVersion(FBXText))\n      }\n\n      fbxTree = new TextParser().parse(FBXText)\n    }\n\n    // console.log( fbxTree );\n\n    const textureLoader = new TextureLoader(this.manager)\n      .setPath(this.resourcePath || path)\n      .setCrossOrigin(this.crossOrigin)\n\n    return new FBXTreeParser(textureLoader, this.manager).parse(fbxTree)\n  }\n}\n\n// Parse the FBXTree object returned by the BinaryParser or TextParser and return a Group\nclass FBXTreeParser {\n  constructor(textureLoader, manager) {\n    this.textureLoader = textureLoader\n    this.manager = manager\n  }\n\n  parse() {\n    connections = this.parseConnections()\n\n    const images = this.parseImages()\n    const textures = this.parseTextures(images)\n    const materials = this.parseMaterials(textures)\n    const deformers = this.parseDeformers()\n    const geometryMap = new GeometryParser().parse(deformers)\n\n    this.parseScene(deformers, geometryMap, materials)\n\n    return sceneGraph\n  }\n\n  // Parses FBXTree.Connections which holds parent-child connections between objects (e.g. material -> texture, model->geometry )\n  // and details the connection type\n  parseConnections() {\n    const connectionMap = new Map()\n\n    if ('Connections' in fbxTree) {\n      const rawConnections = fbxTree.Connections.connections\n\n      rawConnections.forEach(function (rawConnection) {\n        const fromID = rawConnection[0]\n        const toID = rawConnection[1]\n        const relationship = rawConnection[2]\n\n        if (!connectionMap.has(fromID)) {\n          connectionMap.set(fromID, {\n            parents: [],\n            children: [],\n          })\n        }\n\n        const parentRelationship = { ID: toID, relationship: relationship }\n        connectionMap.get(fromID).parents.push(parentRelationship)\n\n        if (!connectionMap.has(toID)) {\n          connectionMap.set(toID, {\n            parents: [],\n            children: [],\n          })\n        }\n\n        const childRelationship = { ID: fromID, relationship: relationship }\n        connectionMap.get(toID).children.push(childRelationship)\n      })\n    }\n\n    return connectionMap\n  }\n\n  // Parse FBXTree.Objects.Video for embedded image data\n  // These images are connected to textures in FBXTree.Objects.Textures\n  // via FBXTree.Connections.\n  parseImages() {\n    const images = {}\n    const blobs = {}\n\n    if ('Video' in fbxTree.Objects) {\n      const videoNodes = fbxTree.Objects.Video\n\n      for (const nodeID in videoNodes) {\n        const videoNode = videoNodes[nodeID]\n\n        const id = parseInt(nodeID)\n\n        images[id] = videoNode.RelativeFilename || videoNode.Filename\n\n        // raw image data is in videoNode.Content\n        if ('Content' in videoNode) {\n          const arrayBufferContent = videoNode.Content instanceof ArrayBuffer && videoNode.Content.byteLength > 0\n          const base64Content = typeof videoNode.Content === 'string' && videoNode.Content !== ''\n\n          if (arrayBufferContent || base64Content) {\n            const image = this.parseImage(videoNodes[nodeID])\n\n            blobs[videoNode.RelativeFilename || videoNode.Filename] = image\n          }\n        }\n      }\n    }\n\n    for (const id in images) {\n      const filename = images[id]\n\n      if (blobs[filename] !== undefined) images[id] = blobs[filename]\n      else images[id] = images[id].split('\\\\').pop()\n    }\n\n    return images\n  }\n\n  // Parse embedded image data in FBXTree.Video.Content\n  parseImage(videoNode) {\n    const content = videoNode.Content\n    const fileName = videoNode.RelativeFilename || videoNode.Filename\n    const extension = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()\n\n    let type\n\n    switch (extension) {\n      case 'bmp':\n        type = 'image/bmp'\n        break\n\n      case 'jpg':\n      case 'jpeg':\n        type = 'image/jpeg'\n        break\n\n      case 'png':\n        type = 'image/png'\n        break\n\n      case 'tif':\n        type = 'image/tiff'\n        break\n\n      case 'tga':\n        if (this.manager.getHandler('.tga') === null) {\n          console.warn('FBXLoader: TGA loader not found, skipping ', fileName)\n        }\n\n        type = 'image/tga'\n        break\n\n      default:\n        console.warn('FBXLoader: Image type \"' + extension + '\" is not supported.')\n        return\n    }\n\n    if (typeof content === 'string') {\n      // ASCII format\n\n      return 'data:' + type + ';base64,' + content\n    } else {\n      // Binary Format\n\n      const array = new Uint8Array(content)\n      return window.URL.createObjectURL(new Blob([array], { type: type }))\n    }\n  }\n\n  // Parse nodes in FBXTree.Objects.Texture\n  // These contain details such as UV scaling, cropping, rotation etc and are connected\n  // to images in FBXTree.Objects.Video\n  parseTextures(images) {\n    const textureMap = new Map()\n\n    if ('Texture' in fbxTree.Objects) {\n      const textureNodes = fbxTree.Objects.Texture\n      for (const nodeID in textureNodes) {\n        const texture = this.parseTexture(textureNodes[nodeID], images)\n        textureMap.set(parseInt(nodeID), texture)\n      }\n    }\n\n    return textureMap\n  }\n\n  // Parse individual node in FBXTree.Objects.Texture\n  parseTexture(textureNode, images) {\n    const texture = this.loadTexture(textureNode, images)\n\n    texture.ID = textureNode.id\n\n    texture.name = textureNode.attrName\n\n    const wrapModeU = textureNode.WrapModeU\n    const wrapModeV = textureNode.WrapModeV\n\n    const valueU = wrapModeU !== undefined ? wrapModeU.value : 0\n    const valueV = wrapModeV !== undefined ? wrapModeV.value : 0\n\n    // http://download.autodesk.com/us/fbx/SDKdocs/FBX_SDK_Help/files/fbxsdkref/class_k_fbx_texture.html#889640e63e2e681259ea81061b85143a\n    // 0: repeat(default), 1: clamp\n\n    texture.wrapS = valueU === 0 ? RepeatWrapping : ClampToEdgeWrapping\n    texture.wrapT = valueV === 0 ? RepeatWrapping : ClampToEdgeWrapping\n\n    if ('Scaling' in textureNode) {\n      const values = textureNode.Scaling.value\n\n      texture.repeat.x = values[0]\n      texture.repeat.y = values[1]\n    }\n\n    return texture\n  }\n\n  // load a texture specified as a blob or data URI, or via an external URL using TextureLoader\n  loadTexture(textureNode, images) {\n    let fileName\n\n    const currentPath = this.textureLoader.path\n\n    const children = connections.get(textureNode.id).children\n\n    if (children !== undefined && children.length > 0 && images[children[0].ID] !== undefined) {\n      fileName = images[children[0].ID]\n\n      if (fileName.indexOf('blob:') === 0 || fileName.indexOf('data:') === 0) {\n        this.textureLoader.setPath(undefined)\n      }\n    }\n\n    let texture\n\n    const extension = textureNode.FileName.slice(-3).toLowerCase()\n\n    if (extension === 'tga') {\n      const loader = this.manager.getHandler('.tga')\n\n      if (loader === null) {\n        console.warn('FBXLoader: TGA loader not found, creating placeholder texture for', textureNode.RelativeFilename)\n        texture = new Texture()\n      } else {\n        loader.setPath(this.textureLoader.path)\n        texture = loader.load(fileName)\n      }\n    } else if (extension === 'psd') {\n      console.warn(\n        'FBXLoader: PSD textures are not supported, creating placeholder texture for',\n        textureNode.RelativeFilename,\n      )\n      texture = new Texture()\n    } else {\n      texture = this.textureLoader.load(fileName)\n    }\n\n    this.textureLoader.setPath(currentPath)\n\n    return texture\n  }\n\n  // Parse nodes in FBXTree.Objects.Material\n  parseMaterials(textureMap) {\n    const materialMap = new Map()\n\n    if ('Material' in fbxTree.Objects) {\n      const materialNodes = fbxTree.Objects.Material\n\n      for (const nodeID in materialNodes) {\n        const material = this.parseMaterial(materialNodes[nodeID], textureMap)\n\n        if (material !== null) materialMap.set(parseInt(nodeID), material)\n      }\n    }\n\n    return materialMap\n  }\n\n  // Parse single node in FBXTree.Objects.Material\n  // Materials are connected to texture maps in FBXTree.Objects.Textures\n  // FBX format currently only supports Lambert and Phong shading models\n  parseMaterial(materialNode, textureMap) {\n    const ID = materialNode.id\n    const name = materialNode.attrName\n    let type = materialNode.ShadingModel\n\n    // Case where FBX wraps shading model in property object.\n    if (typeof type === 'object') {\n      type = type.value\n    }\n\n    // Ignore unused materials which don't have any connections.\n    if (!connections.has(ID)) return null\n\n    const parameters = this.parseParameters(materialNode, textureMap, ID)\n\n    let material\n\n    switch (type.toLowerCase()) {\n      case 'phong':\n        material = new MeshPhongMaterial()\n        break\n      case 'lambert':\n        material = new MeshLambertMaterial()\n        break\n      default:\n        console.warn('THREE.FBXLoader: unknown material type \"%s\". Defaulting to MeshPhongMaterial.', type)\n        material = new MeshPhongMaterial()\n        break\n    }\n\n    material.setValues(parameters)\n    material.name = name\n\n    return material\n  }\n\n  // Parse FBX material and return parameters suitable for a three.js material\n  // Also parse the texture map and return any textures associated with the material\n  parseParameters(materialNode, textureMap, ID) {\n    const parameters = {}\n\n    if (materialNode.BumpFactor) {\n      parameters.bumpScale = materialNode.BumpFactor.value\n    }\n\n    if (materialNode.Diffuse) {\n      parameters.color = new Color().fromArray(materialNode.Diffuse.value)\n    } else if (\n      materialNode.DiffuseColor &&\n      (materialNode.DiffuseColor.type === 'Color' || materialNode.DiffuseColor.type === 'ColorRGB')\n    ) {\n      // The blender exporter exports diffuse here instead of in materialNode.Diffuse\n      parameters.color = new Color().fromArray(materialNode.DiffuseColor.value)\n    }\n\n    if (materialNode.DisplacementFactor) {\n      parameters.displacementScale = materialNode.DisplacementFactor.value\n    }\n\n    if (materialNode.Emissive) {\n      parameters.emissive = new Color().fromArray(materialNode.Emissive.value)\n    } else if (\n      materialNode.EmissiveColor &&\n      (materialNode.EmissiveColor.type === 'Color' || materialNode.EmissiveColor.type === 'ColorRGB')\n    ) {\n      // The blender exporter exports emissive color here instead of in materialNode.Emissive\n      parameters.emissive = new Color().fromArray(materialNode.EmissiveColor.value)\n    }\n\n    if (materialNode.EmissiveFactor) {\n      parameters.emissiveIntensity = parseFloat(materialNode.EmissiveFactor.value)\n    }\n\n    if (materialNode.Opacity) {\n      parameters.opacity = parseFloat(materialNode.Opacity.value)\n    }\n\n    if (parameters.opacity < 1.0) {\n      parameters.transparent = true\n    }\n\n    if (materialNode.ReflectionFactor) {\n      parameters.reflectivity = materialNode.ReflectionFactor.value\n    }\n\n    if (materialNode.Shininess) {\n      parameters.shininess = materialNode.Shininess.value\n    }\n\n    if (materialNode.Specular) {\n      parameters.specular = new Color().fromArray(materialNode.Specular.value)\n    } else if (materialNode.SpecularColor && materialNode.SpecularColor.type === 'Color') {\n      // The blender exporter exports specular color here instead of in materialNode.Specular\n      parameters.specular = new Color().fromArray(materialNode.SpecularColor.value)\n    }\n\n    const scope = this\n    connections.get(ID).children.forEach(function (child) {\n      const type = child.relationship\n\n      switch (type) {\n        case 'Bump':\n          parameters.bumpMap = scope.getTexture(textureMap, child.ID)\n          break\n\n        case 'Maya|TEX_ao_map':\n          parameters.aoMap = scope.getTexture(textureMap, child.ID)\n          break\n\n        case 'DiffuseColor':\n        case 'Maya|TEX_color_map':\n          parameters.map = scope.getTexture(textureMap, child.ID)\n          if (parameters.map !== undefined) {\n            if ('colorSpace' in parameters.map) parameters.map.colorSpace = 'srgb'\n            else parameters.map.encoding = 3001 // sRGBEncoding\n          }\n\n          break\n\n        case 'DisplacementColor':\n          parameters.displacementMap = scope.getTexture(textureMap, child.ID)\n          break\n\n        case 'EmissiveColor':\n          parameters.emissiveMap = scope.getTexture(textureMap, child.ID)\n          if (parameters.emissiveMap !== undefined) {\n            if ('colorSpace' in parameters.emissiveMap) parameters.emissiveMap.colorSpace = 'srgb'\n            else parameters.emissiveMap.encoding = 3001 // sRGBEncoding\n          }\n\n          break\n\n        case 'NormalMap':\n        case 'Maya|TEX_normal_map':\n          parameters.normalMap = scope.getTexture(textureMap, child.ID)\n          break\n\n        case 'ReflectionColor':\n          parameters.envMap = scope.getTexture(textureMap, child.ID)\n          if (parameters.envMap !== undefined) {\n            parameters.envMap.mapping = EquirectangularReflectionMapping\n\n            if ('colorSpace' in parameters.envMap) parameters.envMap.colorSpace = 'srgb'\n            else parameters.envMap.encoding = 3001 // sRGBEncoding\n          }\n\n          break\n\n        case 'SpecularColor':\n          parameters.specularMap = scope.getTexture(textureMap, child.ID)\n          if (parameters.specularMap !== undefined) {\n            if ('colorSpace' in parameters.specularMap) parameters.specularMap.colorSpace = 'srgb'\n            else parameters.specularMap.encoding = 3001 // sRGBEncoding\n          }\n\n          break\n\n        case 'TransparentColor':\n        case 'TransparencyFactor':\n          parameters.alphaMap = scope.getTexture(textureMap, child.ID)\n          parameters.transparent = true\n          break\n\n        case 'AmbientColor':\n        case 'ShininessExponent': // AKA glossiness map\n        case 'SpecularFactor': // AKA specularLevel\n        case 'VectorDisplacementColor': // NOTE: Seems to be a copy of DisplacementColor\n        default:\n          console.warn('THREE.FBXLoader: %s map is not supported in three.js, skipping texture.', type)\n          break\n      }\n    })\n\n    return parameters\n  }\n\n  // get a texture from the textureMap for use by a material.\n  getTexture(textureMap, id) {\n    // if the texture is a layered texture, just use the first layer and issue a warning\n    if ('LayeredTexture' in fbxTree.Objects && id in fbxTree.Objects.LayeredTexture) {\n      console.warn('THREE.FBXLoader: layered textures are not supported in three.js. Discarding all but first layer.')\n      id = connections.get(id).children[0].ID\n    }\n\n    return textureMap.get(id)\n  }\n\n  // Parse nodes in FBXTree.Objects.Deformer\n  // Deformer node can contain skinning or Vertex Cache animation data, however only skinning is supported here\n  // Generates map of Skeleton-like objects for use later when generating and binding skeletons.\n  parseDeformers() {\n    const skeletons = {}\n    const morphTargets = {}\n\n    if ('Deformer' in fbxTree.Objects) {\n      const DeformerNodes = fbxTree.Objects.Deformer\n\n      for (const nodeID in DeformerNodes) {\n        const deformerNode = DeformerNodes[nodeID]\n\n        const relationships = connections.get(parseInt(nodeID))\n\n        if (deformerNode.attrType === 'Skin') {\n          const skeleton = this.parseSkeleton(relationships, DeformerNodes)\n          skeleton.ID = nodeID\n\n          if (relationships.parents.length > 1) {\n            console.warn('THREE.FBXLoader: skeleton attached to more than one geometry is not supported.')\n          }\n          skeleton.geometryID = relationships.parents[0].ID\n\n          skeletons[nodeID] = skeleton\n        } else if (deformerNode.attrType === 'BlendShape') {\n          const morphTarget = {\n            id: nodeID,\n          }\n\n          morphTarget.rawTargets = this.parseMorphTargets(relationships, DeformerNodes)\n          morphTarget.id = nodeID\n\n          if (relationships.parents.length > 1) {\n            console.warn('THREE.FBXLoader: morph target attached to more than one geometry is not supported.')\n          }\n\n          morphTargets[nodeID] = morphTarget\n        }\n      }\n    }\n\n    return {\n      skeletons: skeletons,\n      morphTargets: morphTargets,\n    }\n  }\n\n  // Parse single nodes in FBXTree.Objects.Deformer\n  // The top level skeleton node has type 'Skin' and sub nodes have type 'Cluster'\n  // Each skin node represents a skeleton and each cluster node represents a bone\n  parseSkeleton(relationships, deformerNodes) {\n    const rawBones = []\n\n    relationships.children.forEach(function (child) {\n      const boneNode = deformerNodes[child.ID]\n\n      if (boneNode.attrType !== 'Cluster') return\n\n      const rawBone = {\n        ID: child.ID,\n        indices: [],\n        weights: [],\n        transformLink: new Matrix4().fromArray(boneNode.TransformLink.a),\n        // transform: new Matrix4().fromArray( boneNode.Transform.a ),\n        // linkMode: boneNode.Mode,\n      }\n\n      if ('Indexes' in boneNode) {\n        rawBone.indices = boneNode.Indexes.a\n        rawBone.weights = boneNode.Weights.a\n      }\n\n      rawBones.push(rawBone)\n    })\n\n    return {\n      rawBones: rawBones,\n      bones: [],\n    }\n  }\n\n  // The top level morph deformer node has type \"BlendShape\" and sub nodes have type \"BlendShapeChannel\"\n  parseMorphTargets(relationships, deformerNodes) {\n    const rawMorphTargets = []\n\n    for (let i = 0; i < relationships.children.length; i++) {\n      const child = relationships.children[i]\n\n      const morphTargetNode = deformerNodes[child.ID]\n\n      const rawMorphTarget = {\n        name: morphTargetNode.attrName,\n        initialWeight: morphTargetNode.DeformPercent,\n        id: morphTargetNode.id,\n        fullWeights: morphTargetNode.FullWeights.a,\n      }\n\n      if (morphTargetNode.attrType !== 'BlendShapeChannel') return\n\n      rawMorphTarget.geoID = connections.get(parseInt(child.ID)).children.filter(function (child) {\n        return child.relationship === undefined\n      })[0].ID\n\n      rawMorphTargets.push(rawMorphTarget)\n    }\n\n    return rawMorphTargets\n  }\n\n  // create the main Group() to be returned by the loader\n  parseScene(deformers, geometryMap, materialMap) {\n    sceneGraph = new Group()\n\n    const modelMap = this.parseModels(deformers.skeletons, geometryMap, materialMap)\n\n    const modelNodes = fbxTree.Objects.Model\n\n    const scope = this\n    modelMap.forEach(function (model) {\n      const modelNode = modelNodes[model.ID]\n      scope.setLookAtProperties(model, modelNode)\n\n      const parentConnections = connections.get(model.ID).parents\n\n      parentConnections.forEach(function (connection) {\n        const parent = modelMap.get(connection.ID)\n        if (parent !== undefined) parent.add(model)\n      })\n\n      if (model.parent === null) {\n        sceneGraph.add(model)\n      }\n    })\n\n    this.bindSkeleton(deformers.skeletons, geometryMap, modelMap)\n\n    this.createAmbientLight()\n\n    sceneGraph.traverse(function (node) {\n      if (node.userData.transformData) {\n        if (node.parent) {\n          node.userData.transformData.parentMatrix = node.parent.matrix\n          node.userData.transformData.parentMatrixWorld = node.parent.matrixWorld\n        }\n\n        const transform = generateTransform(node.userData.transformData)\n\n        node.applyMatrix4(transform)\n        node.updateWorldMatrix()\n      }\n    })\n\n    const animations = new AnimationParser().parse()\n\n    // if all the models where already combined in a single group, just return that\n    if (sceneGraph.children.length === 1 && sceneGraph.children[0].isGroup) {\n      sceneGraph.children[0].animations = animations\n      sceneGraph = sceneGraph.children[0]\n    }\n\n    sceneGraph.animations = animations\n  }\n\n  // parse nodes in FBXTree.Objects.Model\n  parseModels(skeletons, geometryMap, materialMap) {\n    const modelMap = new Map()\n    const modelNodes = fbxTree.Objects.Model\n\n    for (const nodeID in modelNodes) {\n      const id = parseInt(nodeID)\n      const node = modelNodes[nodeID]\n      const relationships = connections.get(id)\n\n      let model = this.buildSkeleton(relationships, skeletons, id, node.attrName)\n\n      if (!model) {\n        switch (node.attrType) {\n          case 'Camera':\n            model = this.createCamera(relationships)\n            break\n          case 'Light':\n            model = this.createLight(relationships)\n            break\n          case 'Mesh':\n            model = this.createMesh(relationships, geometryMap, materialMap)\n            break\n          case 'NurbsCurve':\n            model = this.createCurve(relationships, geometryMap)\n            break\n          case 'LimbNode':\n          case 'Root':\n            model = new Bone()\n            break\n          case 'Null':\n          default:\n            model = new Group()\n            break\n        }\n\n        model.name = node.attrName ? PropertyBinding.sanitizeNodeName(node.attrName) : ''\n\n        model.ID = id\n      }\n\n      this.getTransformData(model, node)\n      modelMap.set(id, model)\n    }\n\n    return modelMap\n  }\n\n  buildSkeleton(relationships, skeletons, id, name) {\n    let bone = null\n\n    relationships.parents.forEach(function (parent) {\n      for (const ID in skeletons) {\n        const skeleton = skeletons[ID]\n\n        skeleton.rawBones.forEach(function (rawBone, i) {\n          if (rawBone.ID === parent.ID) {\n            const subBone = bone\n            bone = new Bone()\n\n            bone.matrixWorld.copy(rawBone.transformLink)\n\n            // set name and id here - otherwise in cases where \"subBone\" is created it will not have a name / id\n\n            bone.name = name ? PropertyBinding.sanitizeNodeName(name) : ''\n            bone.ID = id\n\n            skeleton.bones[i] = bone\n\n            // In cases where a bone is shared between multiple meshes\n            // duplicate the bone here and and it as a child of the first bone\n            if (subBone !== null) {\n              bone.add(subBone)\n            }\n          }\n        })\n      }\n    })\n\n    return bone\n  }\n\n  // create a PerspectiveCamera or OrthographicCamera\n  createCamera(relationships) {\n    let model\n    let cameraAttribute\n\n    relationships.children.forEach(function (child) {\n      const attr = fbxTree.Objects.NodeAttribute[child.ID]\n\n      if (attr !== undefined) {\n        cameraAttribute = attr\n      }\n    })\n\n    if (cameraAttribute === undefined) {\n      model = new Object3D()\n    } else {\n      let type = 0\n      if (cameraAttribute.CameraProjectionType !== undefined && cameraAttribute.CameraProjectionType.value === 1) {\n        type = 1\n      }\n\n      let nearClippingPlane = 1\n      if (cameraAttribute.NearPlane !== undefined) {\n        nearClippingPlane = cameraAttribute.NearPlane.value / 1000\n      }\n\n      let farClippingPlane = 1000\n      if (cameraAttribute.FarPlane !== undefined) {\n        farClippingPlane = cameraAttribute.FarPlane.value / 1000\n      }\n\n      let width = window.innerWidth\n      let height = window.innerHeight\n\n      if (cameraAttribute.AspectWidth !== undefined && cameraAttribute.AspectHeight !== undefined) {\n        width = cameraAttribute.AspectWidth.value\n        height = cameraAttribute.AspectHeight.value\n      }\n\n      const aspect = width / height\n\n      let fov = 45\n      if (cameraAttribute.FieldOfView !== undefined) {\n        fov = cameraAttribute.FieldOfView.value\n      }\n\n      const focalLength = cameraAttribute.FocalLength ? cameraAttribute.FocalLength.value : null\n\n      switch (type) {\n        case 0: // Perspective\n          model = new PerspectiveCamera(fov, aspect, nearClippingPlane, farClippingPlane)\n          if (focalLength !== null) model.setFocalLength(focalLength)\n          break\n\n        case 1: // Orthographic\n          model = new OrthographicCamera(\n            -width / 2,\n            width / 2,\n            height / 2,\n            -height / 2,\n            nearClippingPlane,\n            farClippingPlane,\n          )\n          break\n\n        default:\n          console.warn('THREE.FBXLoader: Unknown camera type ' + type + '.')\n          model = new Object3D()\n          break\n      }\n    }\n\n    return model\n  }\n\n  // Create a DirectionalLight, PointLight or SpotLight\n  createLight(relationships) {\n    let model\n    let lightAttribute\n\n    relationships.children.forEach(function (child) {\n      const attr = fbxTree.Objects.NodeAttribute[child.ID]\n\n      if (attr !== undefined) {\n        lightAttribute = attr\n      }\n    })\n\n    if (lightAttribute === undefined) {\n      model = new Object3D()\n    } else {\n      let type\n\n      // LightType can be undefined for Point lights\n      if (lightAttribute.LightType === undefined) {\n        type = 0\n      } else {\n        type = lightAttribute.LightType.value\n      }\n\n      let color = 0xffffff\n\n      if (lightAttribute.Color !== undefined) {\n        color = new Color().fromArray(lightAttribute.Color.value)\n      }\n\n      let intensity = lightAttribute.Intensity === undefined ? 1 : lightAttribute.Intensity.value / 100\n\n      // light disabled\n      if (lightAttribute.CastLightOnObject !== undefined && lightAttribute.CastLightOnObject.value === 0) {\n        intensity = 0\n      }\n\n      let distance = 0\n      if (lightAttribute.FarAttenuationEnd !== undefined) {\n        if (lightAttribute.EnableFarAttenuation !== undefined && lightAttribute.EnableFarAttenuation.value === 0) {\n          distance = 0\n        } else {\n          distance = lightAttribute.FarAttenuationEnd.value\n        }\n      }\n\n      // TODO: could this be calculated linearly from FarAttenuationStart to FarAttenuationEnd?\n      const decay = 1\n\n      switch (type) {\n        case 0: // Point\n          model = new PointLight(color, intensity, distance, decay)\n          break\n\n        case 1: // Directional\n          model = new DirectionalLight(color, intensity)\n          break\n\n        case 2: // Spot\n          let angle = Math.PI / 3\n\n          if (lightAttribute.InnerAngle !== undefined) {\n            angle = MathUtils.degToRad(lightAttribute.InnerAngle.value)\n          }\n\n          let penumbra = 0\n          if (lightAttribute.OuterAngle !== undefined) {\n            // TODO: this is not correct - FBX calculates outer and inner angle in degrees\n            // with OuterAngle > InnerAngle && OuterAngle <= Math.PI\n            // while three.js uses a penumbra between (0, 1) to attenuate the inner angle\n            penumbra = MathUtils.degToRad(lightAttribute.OuterAngle.value)\n            penumbra = Math.max(penumbra, 1)\n          }\n\n          model = new SpotLight(color, intensity, distance, angle, penumbra, decay)\n          break\n\n        default:\n          console.warn(\n            'THREE.FBXLoader: Unknown light type ' + lightAttribute.LightType.value + ', defaulting to a PointLight.',\n          )\n          model = new PointLight(color, intensity)\n          break\n      }\n\n      if (lightAttribute.CastShadows !== undefined && lightAttribute.CastShadows.value === 1) {\n        model.castShadow = true\n      }\n    }\n\n    return model\n  }\n\n  createMesh(relationships, geometryMap, materialMap) {\n    let model\n    let geometry = null\n    let material = null\n    const materials = []\n\n    // get geometry and materials(s) from connections\n    relationships.children.forEach(function (child) {\n      if (geometryMap.has(child.ID)) {\n        geometry = geometryMap.get(child.ID)\n      }\n\n      if (materialMap.has(child.ID)) {\n        materials.push(materialMap.get(child.ID))\n      }\n    })\n\n    if (materials.length > 1) {\n      material = materials\n    } else if (materials.length > 0) {\n      material = materials[0]\n    } else {\n      material = new MeshPhongMaterial({ color: 0xcccccc })\n      materials.push(material)\n    }\n\n    if ('color' in geometry.attributes) {\n      materials.forEach(function (material) {\n        material.vertexColors = true\n      })\n    }\n\n    if (geometry.FBX_Deformer) {\n      model = new SkinnedMesh(geometry, material)\n      model.normalizeSkinWeights()\n    } else {\n      model = new Mesh(geometry, material)\n    }\n\n    return model\n  }\n\n  createCurve(relationships, geometryMap) {\n    const geometry = relationships.children.reduce(function (geo, child) {\n      if (geometryMap.has(child.ID)) geo = geometryMap.get(child.ID)\n\n      return geo\n    }, null)\n\n    // FBX does not list materials for Nurbs lines, so we'll just put our own in here.\n    const material = new LineBasicMaterial({ color: 0x3300ff, linewidth: 1 })\n    return new Line(geometry, material)\n  }\n\n  // parse the model node for transform data\n  getTransformData(model, modelNode) {\n    const transformData = {}\n\n    if ('InheritType' in modelNode) transformData.inheritType = parseInt(modelNode.InheritType.value)\n\n    if ('RotationOrder' in modelNode) transformData.eulerOrder = getEulerOrder(modelNode.RotationOrder.value)\n    else transformData.eulerOrder = 'ZYX'\n\n    if ('Lcl_Translation' in modelNode) transformData.translation = modelNode.Lcl_Translation.value\n\n    if ('PreRotation' in modelNode) transformData.preRotation = modelNode.PreRotation.value\n    if ('Lcl_Rotation' in modelNode) transformData.rotation = modelNode.Lcl_Rotation.value\n    if ('PostRotation' in modelNode) transformData.postRotation = modelNode.PostRotation.value\n\n    if ('Lcl_Scaling' in modelNode) transformData.scale = modelNode.Lcl_Scaling.value\n\n    if ('ScalingOffset' in modelNode) transformData.scalingOffset = modelNode.ScalingOffset.value\n    if ('ScalingPivot' in modelNode) transformData.scalingPivot = modelNode.ScalingPivot.value\n\n    if ('RotationOffset' in modelNode) transformData.rotationOffset = modelNode.RotationOffset.value\n    if ('RotationPivot' in modelNode) transformData.rotationPivot = modelNode.RotationPivot.value\n\n    model.userData.transformData = transformData\n  }\n\n  setLookAtProperties(model, modelNode) {\n    if ('LookAtProperty' in modelNode) {\n      const children = connections.get(model.ID).children\n\n      children.forEach(function (child) {\n        if (child.relationship === 'LookAtProperty') {\n          const lookAtTarget = fbxTree.Objects.Model[child.ID]\n\n          if ('Lcl_Translation' in lookAtTarget) {\n            const pos = lookAtTarget.Lcl_Translation.value\n\n            // DirectionalLight, SpotLight\n            if (model.target !== undefined) {\n              model.target.position.fromArray(pos)\n              sceneGraph.add(model.target)\n            } else {\n              // Cameras and other Object3Ds\n\n              model.lookAt(new Vector3().fromArray(pos))\n            }\n          }\n        }\n      })\n    }\n  }\n\n  bindSkeleton(skeletons, geometryMap, modelMap) {\n    const bindMatrices = this.parsePoseNodes()\n\n    for (const ID in skeletons) {\n      const skeleton = skeletons[ID]\n\n      const parents = connections.get(parseInt(skeleton.ID)).parents\n\n      parents.forEach(function (parent) {\n        if (geometryMap.has(parent.ID)) {\n          const geoID = parent.ID\n          const geoRelationships = connections.get(geoID)\n\n          geoRelationships.parents.forEach(function (geoConnParent) {\n            if (modelMap.has(geoConnParent.ID)) {\n              const model = modelMap.get(geoConnParent.ID)\n\n              model.bind(new Skeleton(skeleton.bones), bindMatrices[geoConnParent.ID])\n            }\n          })\n        }\n      })\n    }\n  }\n\n  parsePoseNodes() {\n    const bindMatrices = {}\n\n    if ('Pose' in fbxTree.Objects) {\n      const BindPoseNode = fbxTree.Objects.Pose\n\n      for (const nodeID in BindPoseNode) {\n        if (BindPoseNode[nodeID].attrType === 'BindPose' && BindPoseNode[nodeID].NbPoseNodes > 0) {\n          const poseNodes = BindPoseNode[nodeID].PoseNode\n\n          if (Array.isArray(poseNodes)) {\n            poseNodes.forEach(function (poseNode) {\n              bindMatrices[poseNode.Node] = new Matrix4().fromArray(poseNode.Matrix.a)\n            })\n          } else {\n            bindMatrices[poseNodes.Node] = new Matrix4().fromArray(poseNodes.Matrix.a)\n          }\n        }\n      }\n    }\n\n    return bindMatrices\n  }\n\n  // Parse ambient color in FBXTree.GlobalSettings - if it's not set to black (default), create an ambient light\n  createAmbientLight() {\n    if ('GlobalSettings' in fbxTree && 'AmbientColor' in fbxTree.GlobalSettings) {\n      const ambientColor = fbxTree.GlobalSettings.AmbientColor.value\n      const r = ambientColor[0]\n      const g = ambientColor[1]\n      const b = ambientColor[2]\n\n      if (r !== 0 || g !== 0 || b !== 0) {\n        const color = new Color(r, g, b)\n        sceneGraph.add(new AmbientLight(color, 1))\n      }\n    }\n  }\n}\n\n// parse Geometry data from FBXTree and return map of BufferGeometries\nclass GeometryParser {\n  // Parse nodes in FBXTree.Objects.Geometry\n  parse(deformers) {\n    const geometryMap = new Map()\n\n    if ('Geometry' in fbxTree.Objects) {\n      const geoNodes = fbxTree.Objects.Geometry\n\n      for (const nodeID in geoNodes) {\n        const relationships = connections.get(parseInt(nodeID))\n        const geo = this.parseGeometry(relationships, geoNodes[nodeID], deformers)\n\n        geometryMap.set(parseInt(nodeID), geo)\n      }\n    }\n\n    return geometryMap\n  }\n\n  // Parse single node in FBXTree.Objects.Geometry\n  parseGeometry(relationships, geoNode, deformers) {\n    switch (geoNode.attrType) {\n      case 'Mesh':\n        return this.parseMeshGeometry(relationships, geoNode, deformers)\n        break\n\n      case 'NurbsCurve':\n        return this.parseNurbsGeometry(geoNode)\n        break\n    }\n  }\n\n  // Parse single node mesh geometry in FBXTree.Objects.Geometry\n  parseMeshGeometry(relationships, geoNode, deformers) {\n    const skeletons = deformers.skeletons\n    const morphTargets = []\n\n    const modelNodes = relationships.parents.map(function (parent) {\n      return fbxTree.Objects.Model[parent.ID]\n    })\n\n    // don't create geometry if it is not associated with any models\n    if (modelNodes.length === 0) return\n\n    const skeleton = relationships.children.reduce(function (skeleton, child) {\n      if (skeletons[child.ID] !== undefined) skeleton = skeletons[child.ID]\n\n      return skeleton\n    }, null)\n\n    relationships.children.forEach(function (child) {\n      if (deformers.morphTargets[child.ID] !== undefined) {\n        morphTargets.push(deformers.morphTargets[child.ID])\n      }\n    })\n\n    // Assume one model and get the preRotation from that\n    // if there is more than one model associated with the geometry this may cause problems\n    const modelNode = modelNodes[0]\n\n    const transformData = {}\n\n    if ('RotationOrder' in modelNode) transformData.eulerOrder = getEulerOrder(modelNode.RotationOrder.value)\n    if ('InheritType' in modelNode) transformData.inheritType = parseInt(modelNode.InheritType.value)\n\n    if ('GeometricTranslation' in modelNode) transformData.translation = modelNode.GeometricTranslation.value\n    if ('GeometricRotation' in modelNode) transformData.rotation = modelNode.GeometricRotation.value\n    if ('GeometricScaling' in modelNode) transformData.scale = modelNode.GeometricScaling.value\n\n    const transform = generateTransform(transformData)\n\n    return this.genGeometry(geoNode, skeleton, morphTargets, transform)\n  }\n\n  // Generate a BufferGeometry from a node in FBXTree.Objects.Geometry\n  genGeometry(geoNode, skeleton, morphTargets, preTransform) {\n    const geo = new BufferGeometry()\n    if (geoNode.attrName) geo.name = geoNode.attrName\n\n    const geoInfo = this.parseGeoNode(geoNode, skeleton)\n    const buffers = this.genBuffers(geoInfo)\n\n    const positionAttribute = new Float32BufferAttribute(buffers.vertex, 3)\n\n    positionAttribute.applyMatrix4(preTransform)\n\n    geo.setAttribute('position', positionAttribute)\n\n    if (buffers.colors.length > 0) {\n      geo.setAttribute('color', new Float32BufferAttribute(buffers.colors, 3))\n    }\n\n    if (skeleton) {\n      geo.setAttribute('skinIndex', new Uint16BufferAttribute(buffers.weightsIndices, 4))\n\n      geo.setAttribute('skinWeight', new Float32BufferAttribute(buffers.vertexWeights, 4))\n\n      // used later to bind the skeleton to the model\n      geo.FBX_Deformer = skeleton\n    }\n\n    if (buffers.normal.length > 0) {\n      const normalMatrix = new Matrix3().getNormalMatrix(preTransform)\n\n      const normalAttribute = new Float32BufferAttribute(buffers.normal, 3)\n      normalAttribute.applyNormalMatrix(normalMatrix)\n\n      geo.setAttribute('normal', normalAttribute)\n    }\n\n    buffers.uvs.forEach(function (uvBuffer, i) {\n      if (UV1 === 'uv2') i++\n      const name = i === 0 ? 'uv' : `uv${i}`\n\n      geo.setAttribute(name, new Float32BufferAttribute(buffers.uvs[i], 2))\n    })\n\n    if (geoInfo.material && geoInfo.material.mappingType !== 'AllSame') {\n      // Convert the material indices of each vertex into rendering groups on the geometry.\n      let prevMaterialIndex = buffers.materialIndex[0]\n      let startIndex = 0\n\n      buffers.materialIndex.forEach(function (currentIndex, i) {\n        if (currentIndex !== prevMaterialIndex) {\n          geo.addGroup(startIndex, i - startIndex, prevMaterialIndex)\n\n          prevMaterialIndex = currentIndex\n          startIndex = i\n        }\n      })\n\n      // the loop above doesn't add the last group, do that here.\n      if (geo.groups.length > 0) {\n        const lastGroup = geo.groups[geo.groups.length - 1]\n        const lastIndex = lastGroup.start + lastGroup.count\n\n        if (lastIndex !== buffers.materialIndex.length) {\n          geo.addGroup(lastIndex, buffers.materialIndex.length - lastIndex, prevMaterialIndex)\n        }\n      }\n\n      // case where there are multiple materials but the whole geometry is only\n      // using one of them\n      if (geo.groups.length === 0) {\n        geo.addGroup(0, buffers.materialIndex.length, buffers.materialIndex[0])\n      }\n    }\n\n    this.addMorphTargets(geo, geoNode, morphTargets, preTransform)\n\n    return geo\n  }\n\n  parseGeoNode(geoNode, skeleton) {\n    const geoInfo = {}\n\n    geoInfo.vertexPositions = geoNode.Vertices !== undefined ? geoNode.Vertices.a : []\n    geoInfo.vertexIndices = geoNode.PolygonVertexIndex !== undefined ? geoNode.PolygonVertexIndex.a : []\n\n    if (geoNode.LayerElementColor) {\n      geoInfo.color = this.parseVertexColors(geoNode.LayerElementColor[0])\n    }\n\n    if (geoNode.LayerElementMaterial) {\n      geoInfo.material = this.parseMaterialIndices(geoNode.LayerElementMaterial[0])\n    }\n\n    if (geoNode.LayerElementNormal) {\n      geoInfo.normal = this.parseNormals(geoNode.LayerElementNormal[0])\n    }\n\n    if (geoNode.LayerElementUV) {\n      geoInfo.uv = []\n\n      let i = 0\n      while (geoNode.LayerElementUV[i]) {\n        if (geoNode.LayerElementUV[i].UV) {\n          geoInfo.uv.push(this.parseUVs(geoNode.LayerElementUV[i]))\n        }\n\n        i++\n      }\n    }\n\n    geoInfo.weightTable = {}\n\n    if (skeleton !== null) {\n      geoInfo.skeleton = skeleton\n\n      skeleton.rawBones.forEach(function (rawBone, i) {\n        // loop over the bone's vertex indices and weights\n        rawBone.indices.forEach(function (index, j) {\n          if (geoInfo.weightTable[index] === undefined) geoInfo.weightTable[index] = []\n\n          geoInfo.weightTable[index].push({\n            id: i,\n            weight: rawBone.weights[j],\n          })\n        })\n      })\n    }\n\n    return geoInfo\n  }\n\n  genBuffers(geoInfo) {\n    const buffers = {\n      vertex: [],\n      normal: [],\n      colors: [],\n      uvs: [],\n      materialIndex: [],\n      vertexWeights: [],\n      weightsIndices: [],\n    }\n\n    let polygonIndex = 0\n    let faceLength = 0\n    let displayedWeightsWarning = false\n\n    // these will hold data for a single face\n    let facePositionIndexes = []\n    let faceNormals = []\n    let faceColors = []\n    let faceUVs = []\n    let faceWeights = []\n    let faceWeightIndices = []\n\n    const scope = this\n    geoInfo.vertexIndices.forEach(function (vertexIndex, polygonVertexIndex) {\n      let materialIndex\n      let endOfFace = false\n\n      // Face index and vertex index arrays are combined in a single array\n      // A cube with quad faces looks like this:\n      // PolygonVertexIndex: *24 {\n      //  a: 0, 1, 3, -3, 2, 3, 5, -5, 4, 5, 7, -7, 6, 7, 1, -1, 1, 7, 5, -4, 6, 0, 2, -5\n      //  }\n      // Negative numbers mark the end of a face - first face here is 0, 1, 3, -3\n      // to find index of last vertex bit shift the index: ^ - 1\n      if (vertexIndex < 0) {\n        vertexIndex = vertexIndex ^ -1 // equivalent to ( x * -1 ) - 1\n        endOfFace = true\n      }\n\n      let weightIndices = []\n      let weights = []\n\n      facePositionIndexes.push(vertexIndex * 3, vertexIndex * 3 + 1, vertexIndex * 3 + 2)\n\n      if (geoInfo.color) {\n        const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.color)\n\n        faceColors.push(data[0], data[1], data[2])\n      }\n\n      if (geoInfo.skeleton) {\n        if (geoInfo.weightTable[vertexIndex] !== undefined) {\n          geoInfo.weightTable[vertexIndex].forEach(function (wt) {\n            weights.push(wt.weight)\n            weightIndices.push(wt.id)\n          })\n        }\n\n        if (weights.length > 4) {\n          if (!displayedWeightsWarning) {\n            console.warn(\n              'THREE.FBXLoader: Vertex has more than 4 skinning weights assigned to vertex. Deleting additional weights.',\n            )\n            displayedWeightsWarning = true\n          }\n\n          const wIndex = [0, 0, 0, 0]\n          const Weight = [0, 0, 0, 0]\n\n          weights.forEach(function (weight, weightIndex) {\n            let currentWeight = weight\n            let currentIndex = weightIndices[weightIndex]\n\n            Weight.forEach(function (comparedWeight, comparedWeightIndex, comparedWeightArray) {\n              if (currentWeight > comparedWeight) {\n                comparedWeightArray[comparedWeightIndex] = currentWeight\n                currentWeight = comparedWeight\n\n                const tmp = wIndex[comparedWeightIndex]\n                wIndex[comparedWeightIndex] = currentIndex\n                currentIndex = tmp\n              }\n            })\n          })\n\n          weightIndices = wIndex\n          weights = Weight\n        }\n\n        // if the weight array is shorter than 4 pad with 0s\n        while (weights.length < 4) {\n          weights.push(0)\n          weightIndices.push(0)\n        }\n\n        for (let i = 0; i < 4; ++i) {\n          faceWeights.push(weights[i])\n          faceWeightIndices.push(weightIndices[i])\n        }\n      }\n\n      if (geoInfo.normal) {\n        const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.normal)\n\n        faceNormals.push(data[0], data[1], data[2])\n      }\n\n      if (geoInfo.material && geoInfo.material.mappingType !== 'AllSame') {\n        materialIndex = getData(polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.material)[0]\n      }\n\n      if (geoInfo.uv) {\n        geoInfo.uv.forEach(function (uv, i) {\n          const data = getData(polygonVertexIndex, polygonIndex, vertexIndex, uv)\n\n          if (faceUVs[i] === undefined) {\n            faceUVs[i] = []\n          }\n\n          faceUVs[i].push(data[0])\n          faceUVs[i].push(data[1])\n        })\n      }\n\n      faceLength++\n\n      if (endOfFace) {\n        scope.genFace(\n          buffers,\n          geoInfo,\n          facePositionIndexes,\n          materialIndex,\n          faceNormals,\n          faceColors,\n          faceUVs,\n          faceWeights,\n          faceWeightIndices,\n          faceLength,\n        )\n\n        polygonIndex++\n        faceLength = 0\n\n        // reset arrays for the next face\n        facePositionIndexes = []\n        faceNormals = []\n        faceColors = []\n        faceUVs = []\n        faceWeights = []\n        faceWeightIndices = []\n      }\n    })\n\n    return buffers\n  }\n\n  // Generate data for a single face in a geometry. If the face is a quad then split it into 2 tris\n  genFace(\n    buffers,\n    geoInfo,\n    facePositionIndexes,\n    materialIndex,\n    faceNormals,\n    faceColors,\n    faceUVs,\n    faceWeights,\n    faceWeightIndices,\n    faceLength,\n  ) {\n    for (let i = 2; i < faceLength; i++) {\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[0]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[1]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[2]])\n\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3 + 1]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[(i - 1) * 3 + 2]])\n\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3 + 1]])\n      buffers.vertex.push(geoInfo.vertexPositions[facePositionIndexes[i * 3 + 2]])\n\n      if (geoInfo.skeleton) {\n        buffers.vertexWeights.push(faceWeights[0])\n        buffers.vertexWeights.push(faceWeights[1])\n        buffers.vertexWeights.push(faceWeights[2])\n        buffers.vertexWeights.push(faceWeights[3])\n\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4])\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 1])\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 2])\n        buffers.vertexWeights.push(faceWeights[(i - 1) * 4 + 3])\n\n        buffers.vertexWeights.push(faceWeights[i * 4])\n        buffers.vertexWeights.push(faceWeights[i * 4 + 1])\n        buffers.vertexWeights.push(faceWeights[i * 4 + 2])\n        buffers.vertexWeights.push(faceWeights[i * 4 + 3])\n\n        buffers.weightsIndices.push(faceWeightIndices[0])\n        buffers.weightsIndices.push(faceWeightIndices[1])\n        buffers.weightsIndices.push(faceWeightIndices[2])\n        buffers.weightsIndices.push(faceWeightIndices[3])\n\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4])\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 1])\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 2])\n        buffers.weightsIndices.push(faceWeightIndices[(i - 1) * 4 + 3])\n\n        buffers.weightsIndices.push(faceWeightIndices[i * 4])\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 1])\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 2])\n        buffers.weightsIndices.push(faceWeightIndices[i * 4 + 3])\n      }\n\n      if (geoInfo.color) {\n        buffers.colors.push(faceColors[0])\n        buffers.colors.push(faceColors[1])\n        buffers.colors.push(faceColors[2])\n\n        buffers.colors.push(faceColors[(i - 1) * 3])\n        buffers.colors.push(faceColors[(i - 1) * 3 + 1])\n        buffers.colors.push(faceColors[(i - 1) * 3 + 2])\n\n        buffers.colors.push(faceColors[i * 3])\n        buffers.colors.push(faceColors[i * 3 + 1])\n        buffers.colors.push(faceColors[i * 3 + 2])\n      }\n\n      if (geoInfo.material && geoInfo.material.mappingType !== 'AllSame') {\n        buffers.materialIndex.push(materialIndex)\n        buffers.materialIndex.push(materialIndex)\n        buffers.materialIndex.push(materialIndex)\n      }\n\n      if (geoInfo.normal) {\n        buffers.normal.push(faceNormals[0])\n        buffers.normal.push(faceNormals[1])\n        buffers.normal.push(faceNormals[2])\n\n        buffers.normal.push(faceNormals[(i - 1) * 3])\n        buffers.normal.push(faceNormals[(i - 1) * 3 + 1])\n        buffers.normal.push(faceNormals[(i - 1) * 3 + 2])\n\n        buffers.normal.push(faceNormals[i * 3])\n        buffers.normal.push(faceNormals[i * 3 + 1])\n        buffers.normal.push(faceNormals[i * 3 + 2])\n      }\n\n      if (geoInfo.uv) {\n        geoInfo.uv.forEach(function (uv, j) {\n          if (buffers.uvs[j] === undefined) buffers.uvs[j] = []\n\n          buffers.uvs[j].push(faceUVs[j][0])\n          buffers.uvs[j].push(faceUVs[j][1])\n\n          buffers.uvs[j].push(faceUVs[j][(i - 1) * 2])\n          buffers.uvs[j].push(faceUVs[j][(i - 1) * 2 + 1])\n\n          buffers.uvs[j].push(faceUVs[j][i * 2])\n          buffers.uvs[j].push(faceUVs[j][i * 2 + 1])\n        })\n      }\n    }\n  }\n\n  addMorphTargets(parentGeo, parentGeoNode, morphTargets, preTransform) {\n    if (morphTargets.length === 0) return\n\n    parentGeo.morphTargetsRelative = true\n\n    parentGeo.morphAttributes.position = []\n    // parentGeo.morphAttributes.normal = []; // not implemented\n\n    const scope = this\n    morphTargets.forEach(function (morphTarget) {\n      morphTarget.rawTargets.forEach(function (rawTarget) {\n        const morphGeoNode = fbxTree.Objects.Geometry[rawTarget.geoID]\n\n        if (morphGeoNode !== undefined) {\n          scope.genMorphGeometry(parentGeo, parentGeoNode, morphGeoNode, preTransform, rawTarget.name)\n        }\n      })\n    })\n  }\n\n  // a morph geometry node is similar to a standard  node, and the node is also contained\n  // in FBXTree.Objects.Geometry, however it can only have attributes for position, normal\n  // and a special attribute Index defining which vertices of the original geometry are affected\n  // Normal and position attributes only have data for the vertices that are affected by the morph\n  genMorphGeometry(parentGeo, parentGeoNode, morphGeoNode, preTransform, name) {\n    const vertexIndices = parentGeoNode.PolygonVertexIndex !== undefined ? parentGeoNode.PolygonVertexIndex.a : []\n\n    const morphPositionsSparse = morphGeoNode.Vertices !== undefined ? morphGeoNode.Vertices.a : []\n    const indices = morphGeoNode.Indexes !== undefined ? morphGeoNode.Indexes.a : []\n\n    const length = parentGeo.attributes.position.count * 3\n    const morphPositions = new Float32Array(length)\n\n    for (let i = 0; i < indices.length; i++) {\n      const morphIndex = indices[i] * 3\n\n      morphPositions[morphIndex] = morphPositionsSparse[i * 3]\n      morphPositions[morphIndex + 1] = morphPositionsSparse[i * 3 + 1]\n      morphPositions[morphIndex + 2] = morphPositionsSparse[i * 3 + 2]\n    }\n\n    // TODO: add morph normal support\n    const morphGeoInfo = {\n      vertexIndices: vertexIndices,\n      vertexPositions: morphPositions,\n    }\n\n    const morphBuffers = this.genBuffers(morphGeoInfo)\n\n    const positionAttribute = new Float32BufferAttribute(morphBuffers.vertex, 3)\n    positionAttribute.name = name || morphGeoNode.attrName\n\n    positionAttribute.applyMatrix4(preTransform)\n\n    parentGeo.morphAttributes.position.push(positionAttribute)\n  }\n\n  // Parse normal from FBXTree.Objects.Geometry.LayerElementNormal if it exists\n  parseNormals(NormalNode) {\n    const mappingType = NormalNode.MappingInformationType\n    const referenceType = NormalNode.ReferenceInformationType\n    const buffer = NormalNode.Normals.a\n    let indexBuffer = []\n    if (referenceType === 'IndexToDirect') {\n      if ('NormalIndex' in NormalNode) {\n        indexBuffer = NormalNode.NormalIndex.a\n      } else if ('NormalsIndex' in NormalNode) {\n        indexBuffer = NormalNode.NormalsIndex.a\n      }\n    }\n\n    return {\n      dataSize: 3,\n      buffer: buffer,\n      indices: indexBuffer,\n      mappingType: mappingType,\n      referenceType: referenceType,\n    }\n  }\n\n  // Parse UVs from FBXTree.Objects.Geometry.LayerElementUV if it exists\n  parseUVs(UVNode) {\n    const mappingType = UVNode.MappingInformationType\n    const referenceType = UVNode.ReferenceInformationType\n    const buffer = UVNode.UV.a\n    let indexBuffer = []\n    if (referenceType === 'IndexToDirect') {\n      indexBuffer = UVNode.UVIndex.a\n    }\n\n    return {\n      dataSize: 2,\n      buffer: buffer,\n      indices: indexBuffer,\n      mappingType: mappingType,\n      referenceType: referenceType,\n    }\n  }\n\n  // Parse Vertex Colors from FBXTree.Objects.Geometry.LayerElementColor if it exists\n  parseVertexColors(ColorNode) {\n    const mappingType = ColorNode.MappingInformationType\n    const referenceType = ColorNode.ReferenceInformationType\n    const buffer = ColorNode.Colors.a\n    let indexBuffer = []\n    if (referenceType === 'IndexToDirect') {\n      indexBuffer = ColorNode.ColorIndex.a\n    }\n\n    return {\n      dataSize: 4,\n      buffer: buffer,\n      indices: indexBuffer,\n      mappingType: mappingType,\n      referenceType: referenceType,\n    }\n  }\n\n  // Parse mapping and material data in FBXTree.Objects.Geometry.LayerElementMaterial if it exists\n  parseMaterialIndices(MaterialNode) {\n    const mappingType = MaterialNode.MappingInformationType\n    const referenceType = MaterialNode.ReferenceInformationType\n\n    if (mappingType === 'NoMappingInformation') {\n      return {\n        dataSize: 1,\n        buffer: [0],\n        indices: [0],\n        mappingType: 'AllSame',\n        referenceType: referenceType,\n      }\n    }\n\n    const materialIndexBuffer = MaterialNode.Materials.a\n\n    // Since materials are stored as indices, there's a bit of a mismatch between FBX and what\n    // we expect.So we create an intermediate buffer that points to the index in the buffer,\n    // for conforming with the other functions we've written for other data.\n    const materialIndices = []\n\n    for (let i = 0; i < materialIndexBuffer.length; ++i) {\n      materialIndices.push(i)\n    }\n\n    return {\n      dataSize: 1,\n      buffer: materialIndexBuffer,\n      indices: materialIndices,\n      mappingType: mappingType,\n      referenceType: referenceType,\n    }\n  }\n\n  // Generate a NurbGeometry from a node in FBXTree.Objects.Geometry\n  parseNurbsGeometry(geoNode) {\n    if (NURBSCurve === undefined) {\n      console.error(\n        'THREE.FBXLoader: The loader relies on NURBSCurve for any nurbs present in the model. Nurbs will show up as empty geometry.',\n      )\n      return new BufferGeometry()\n    }\n\n    const order = parseInt(geoNode.Order)\n\n    if (isNaN(order)) {\n      console.error('THREE.FBXLoader: Invalid Order %s given for geometry ID: %s', geoNode.Order, geoNode.id)\n      return new BufferGeometry()\n    }\n\n    const degree = order - 1\n\n    const knots = geoNode.KnotVector.a\n    const controlPoints = []\n    const pointsValues = geoNode.Points.a\n\n    for (let i = 0, l = pointsValues.length; i < l; i += 4) {\n      controlPoints.push(new Vector4().fromArray(pointsValues, i))\n    }\n\n    let startKnot, endKnot\n\n    if (geoNode.Form === 'Closed') {\n      controlPoints.push(controlPoints[0])\n    } else if (geoNode.Form === 'Periodic') {\n      startKnot = degree\n      endKnot = knots.length - 1 - startKnot\n\n      for (let i = 0; i < degree; ++i) {\n        controlPoints.push(controlPoints[i])\n      }\n    }\n\n    const curve = new NURBSCurve(degree, knots, controlPoints, startKnot, endKnot)\n    const points = curve.getPoints(controlPoints.length * 12)\n\n    return new BufferGeometry().setFromPoints(points)\n  }\n}\n\n// parse animation data from FBXTree\nclass AnimationParser {\n  // take raw animation clips and turn them into three.js animation clips\n  parse() {\n    const animationClips = []\n\n    const rawClips = this.parseClips()\n\n    if (rawClips !== undefined) {\n      for (const key in rawClips) {\n        const rawClip = rawClips[key]\n\n        const clip = this.addClip(rawClip)\n\n        animationClips.push(clip)\n      }\n    }\n\n    return animationClips\n  }\n\n  parseClips() {\n    // since the actual transformation data is stored in FBXTree.Objects.AnimationCurve,\n    // if this is undefined we can safely assume there are no animations\n    if (fbxTree.Objects.AnimationCurve === undefined) return undefined\n\n    const curveNodesMap = this.parseAnimationCurveNodes()\n\n    this.parseAnimationCurves(curveNodesMap)\n\n    const layersMap = this.parseAnimationLayers(curveNodesMap)\n    const rawClips = this.parseAnimStacks(layersMap)\n\n    return rawClips\n  }\n\n  // parse nodes in FBXTree.Objects.AnimationCurveNode\n  // each AnimationCurveNode holds data for an animation transform for a model (e.g. left arm rotation )\n  // and is referenced by an AnimationLayer\n  parseAnimationCurveNodes() {\n    const rawCurveNodes = fbxTree.Objects.AnimationCurveNode\n\n    const curveNodesMap = new Map()\n\n    for (const nodeID in rawCurveNodes) {\n      const rawCurveNode = rawCurveNodes[nodeID]\n\n      if (rawCurveNode.attrName.match(/S|R|T|DeformPercent/) !== null) {\n        const curveNode = {\n          id: rawCurveNode.id,\n          attr: rawCurveNode.attrName,\n          curves: {},\n        }\n\n        curveNodesMap.set(curveNode.id, curveNode)\n      }\n    }\n\n    return curveNodesMap\n  }\n\n  // parse nodes in FBXTree.Objects.AnimationCurve and connect them up to\n  // previously parsed AnimationCurveNodes. Each AnimationCurve holds data for a single animated\n  // axis ( e.g. times and values of x rotation)\n  parseAnimationCurves(curveNodesMap) {\n    const rawCurves = fbxTree.Objects.AnimationCurve\n\n    // TODO: Many values are identical up to roundoff error, but won't be optimised\n    // e.g. position times: [0, 0.4, 0. 8]\n    // position values: [7.23538335023477e-7, 93.67518615722656, -0.9982695579528809, 7.23538335023477e-7, 93.67518615722656, -0.9982695579528809, 7.235384487103147e-7, 93.67520904541016, -0.9982695579528809]\n    // clearly, this should be optimised to\n    // times: [0], positions [7.23538335023477e-7, 93.67518615722656, -0.9982695579528809]\n    // this shows up in nearly every FBX file, and generally time array is length > 100\n\n    for (const nodeID in rawCurves) {\n      const animationCurve = {\n        id: rawCurves[nodeID].id,\n        times: rawCurves[nodeID].KeyTime.a.map(convertFBXTimeToSeconds),\n        values: rawCurves[nodeID].KeyValueFloat.a,\n      }\n\n      const relationships = connections.get(animationCurve.id)\n\n      if (relationships !== undefined) {\n        const animationCurveID = relationships.parents[0].ID\n        const animationCurveRelationship = relationships.parents[0].relationship\n\n        if (animationCurveRelationship.match(/X/)) {\n          curveNodesMap.get(animationCurveID).curves['x'] = animationCurve\n        } else if (animationCurveRelationship.match(/Y/)) {\n          curveNodesMap.get(animationCurveID).curves['y'] = animationCurve\n        } else if (animationCurveRelationship.match(/Z/)) {\n          curveNodesMap.get(animationCurveID).curves['z'] = animationCurve\n        } else if (animationCurveRelationship.match(/d|DeformPercent/) && curveNodesMap.has(animationCurveID)) {\n          curveNodesMap.get(animationCurveID).curves['morph'] = animationCurve\n        }\n      }\n    }\n  }\n\n  // parse nodes in FBXTree.Objects.AnimationLayer. Each layers holds references\n  // to various AnimationCurveNodes and is referenced by an AnimationStack node\n  // note: theoretically a stack can have multiple layers, however in practice there always seems to be one per stack\n  parseAnimationLayers(curveNodesMap) {\n    const rawLayers = fbxTree.Objects.AnimationLayer\n\n    const layersMap = new Map()\n\n    for (const nodeID in rawLayers) {\n      const layerCurveNodes = []\n\n      const connection = connections.get(parseInt(nodeID))\n\n      if (connection !== undefined) {\n        // all the animationCurveNodes used in the layer\n        const children = connection.children\n\n        children.forEach(function (child, i) {\n          if (curveNodesMap.has(child.ID)) {\n            const curveNode = curveNodesMap.get(child.ID)\n\n            // check that the curves are defined for at least one axis, otherwise ignore the curveNode\n            if (\n              curveNode.curves.x !== undefined ||\n              curveNode.curves.y !== undefined ||\n              curveNode.curves.z !== undefined\n            ) {\n              if (layerCurveNodes[i] === undefined) {\n                const modelID = connections.get(child.ID).parents.filter(function (parent) {\n                  return parent.relationship !== undefined\n                })[0].ID\n\n                if (modelID !== undefined) {\n                  const rawModel = fbxTree.Objects.Model[modelID.toString()]\n\n                  if (rawModel === undefined) {\n                    console.warn('THREE.FBXLoader: Encountered a unused curve.', child)\n                    return\n                  }\n\n                  const node = {\n                    modelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName(rawModel.attrName) : '',\n                    ID: rawModel.id,\n                    initialPosition: [0, 0, 0],\n                    initialRotation: [0, 0, 0],\n                    initialScale: [1, 1, 1],\n                  }\n\n                  sceneGraph.traverse(function (child) {\n                    if (child.ID === rawModel.id) {\n                      node.transform = child.matrix\n\n                      if (child.userData.transformData) node.eulerOrder = child.userData.transformData.eulerOrder\n                    }\n                  })\n\n                  if (!node.transform) node.transform = new Matrix4()\n\n                  // if the animated model is pre rotated, we'll have to apply the pre rotations to every\n                  // animation value as well\n                  if ('PreRotation' in rawModel) node.preRotation = rawModel.PreRotation.value\n                  if ('PostRotation' in rawModel) node.postRotation = rawModel.PostRotation.value\n\n                  layerCurveNodes[i] = node\n                }\n              }\n\n              if (layerCurveNodes[i]) layerCurveNodes[i][curveNode.attr] = curveNode\n            } else if (curveNode.curves.morph !== undefined) {\n              if (layerCurveNodes[i] === undefined) {\n                const deformerID = connections.get(child.ID).parents.filter(function (parent) {\n                  return parent.relationship !== undefined\n                })[0].ID\n\n                const morpherID = connections.get(deformerID).parents[0].ID\n                const geoID = connections.get(morpherID).parents[0].ID\n\n                // assuming geometry is not used in more than one model\n                const modelID = connections.get(geoID).parents[0].ID\n\n                const rawModel = fbxTree.Objects.Model[modelID]\n\n                const node = {\n                  modelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName(rawModel.attrName) : '',\n                  morphName: fbxTree.Objects.Deformer[deformerID].attrName,\n                }\n\n                layerCurveNodes[i] = node\n              }\n\n              layerCurveNodes[i][curveNode.attr] = curveNode\n            }\n          }\n        })\n\n        layersMap.set(parseInt(nodeID), layerCurveNodes)\n      }\n    }\n\n    return layersMap\n  }\n\n  // parse nodes in FBXTree.Objects.AnimationStack. These are the top level node in the animation\n  // hierarchy. Each Stack node will be used to create a AnimationClip\n  parseAnimStacks(layersMap) {\n    const rawStacks = fbxTree.Objects.AnimationStack\n\n    // connect the stacks (clips) up to the layers\n    const rawClips = {}\n\n    for (const nodeID in rawStacks) {\n      const children = connections.get(parseInt(nodeID)).children\n\n      if (children.length > 1) {\n        // it seems like stacks will always be associated with a single layer. But just in case there are files\n        // where there are multiple layers per stack, we'll display a warning\n        console.warn(\n          'THREE.FBXLoader: Encountered an animation stack with multiple layers, this is currently not supported. Ignoring subsequent layers.',\n        )\n      }\n\n      const layer = layersMap.get(children[0].ID)\n\n      rawClips[nodeID] = {\n        name: rawStacks[nodeID].attrName,\n        layer: layer,\n      }\n    }\n\n    return rawClips\n  }\n\n  addClip(rawClip) {\n    let tracks = []\n\n    const scope = this\n    rawClip.layer.forEach(function (rawTracks) {\n      tracks = tracks.concat(scope.generateTracks(rawTracks))\n    })\n\n    return new AnimationClip(rawClip.name, -1, tracks)\n  }\n\n  generateTracks(rawTracks) {\n    const tracks = []\n\n    let initialPosition = new Vector3()\n    let initialRotation = new Quaternion()\n    let initialScale = new Vector3()\n\n    if (rawTracks.transform) rawTracks.transform.decompose(initialPosition, initialRotation, initialScale)\n\n    initialPosition = initialPosition.toArray()\n    initialRotation = new Euler().setFromQuaternion(initialRotation, rawTracks.eulerOrder).toArray()\n    initialScale = initialScale.toArray()\n\n    if (rawTracks.T !== undefined && Object.keys(rawTracks.T.curves).length > 0) {\n      const positionTrack = this.generateVectorTrack(\n        rawTracks.modelName,\n        rawTracks.T.curves,\n        initialPosition,\n        'position',\n      )\n      if (positionTrack !== undefined) tracks.push(positionTrack)\n    }\n\n    if (rawTracks.R !== undefined && Object.keys(rawTracks.R.curves).length > 0) {\n      const rotationTrack = this.generateRotationTrack(\n        rawTracks.modelName,\n        rawTracks.R.curves,\n        initialRotation,\n        rawTracks.preRotation,\n        rawTracks.postRotation,\n        rawTracks.eulerOrder,\n      )\n      if (rotationTrack !== undefined) tracks.push(rotationTrack)\n    }\n\n    if (rawTracks.S !== undefined && Object.keys(rawTracks.S.curves).length > 0) {\n      const scaleTrack = this.generateVectorTrack(rawTracks.modelName, rawTracks.S.curves, initialScale, 'scale')\n      if (scaleTrack !== undefined) tracks.push(scaleTrack)\n    }\n\n    if (rawTracks.DeformPercent !== undefined) {\n      const morphTrack = this.generateMorphTrack(rawTracks)\n      if (morphTrack !== undefined) tracks.push(morphTrack)\n    }\n\n    return tracks\n  }\n\n  generateVectorTrack(modelName, curves, initialValue, type) {\n    const times = this.getTimesForAllAxes(curves)\n    const values = this.getKeyframeTrackValues(times, curves, initialValue)\n\n    return new VectorKeyframeTrack(modelName + '.' + type, times, values)\n  }\n\n  generateRotationTrack(modelName, curves, initialValue, preRotation, postRotation, eulerOrder) {\n    if (curves.x !== undefined) {\n      this.interpolateRotations(curves.x)\n      curves.x.values = curves.x.values.map(MathUtils.degToRad)\n    }\n\n    if (curves.y !== undefined) {\n      this.interpolateRotations(curves.y)\n      curves.y.values = curves.y.values.map(MathUtils.degToRad)\n    }\n\n    if (curves.z !== undefined) {\n      this.interpolateRotations(curves.z)\n      curves.z.values = curves.z.values.map(MathUtils.degToRad)\n    }\n\n    const times = this.getTimesForAllAxes(curves)\n    const values = this.getKeyframeTrackValues(times, curves, initialValue)\n\n    if (preRotation !== undefined) {\n      preRotation = preRotation.map(MathUtils.degToRad)\n      preRotation.push(eulerOrder)\n\n      preRotation = new Euler().fromArray(preRotation)\n      preRotation = new Quaternion().setFromEuler(preRotation)\n    }\n\n    if (postRotation !== undefined) {\n      postRotation = postRotation.map(MathUtils.degToRad)\n      postRotation.push(eulerOrder)\n\n      postRotation = new Euler().fromArray(postRotation)\n      postRotation = new Quaternion().setFromEuler(postRotation).invert()\n    }\n\n    const quaternion = new Quaternion()\n    const euler = new Euler()\n\n    const quaternionValues = []\n\n    for (let i = 0; i < values.length; i += 3) {\n      euler.set(values[i], values[i + 1], values[i + 2], eulerOrder)\n\n      quaternion.setFromEuler(euler)\n\n      if (preRotation !== undefined) quaternion.premultiply(preRotation)\n      if (postRotation !== undefined) quaternion.multiply(postRotation)\n\n      quaternion.toArray(quaternionValues, (i / 3) * 4)\n    }\n\n    return new QuaternionKeyframeTrack(modelName + '.quaternion', times, quaternionValues)\n  }\n\n  generateMorphTrack(rawTracks) {\n    const curves = rawTracks.DeformPercent.curves.morph\n    const values = curves.values.map(function (val) {\n      return val / 100\n    })\n\n    const morphNum = sceneGraph.getObjectByName(rawTracks.modelName).morphTargetDictionary[rawTracks.morphName]\n\n    return new NumberKeyframeTrack(\n      rawTracks.modelName + '.morphTargetInfluences[' + morphNum + ']',\n      curves.times,\n      values,\n    )\n  }\n\n  // For all animated objects, times are defined separately for each axis\n  // Here we'll combine the times into one sorted array without duplicates\n  getTimesForAllAxes(curves) {\n    let times = []\n\n    // first join together the times for each axis, if defined\n    if (curves.x !== undefined) times = times.concat(curves.x.times)\n    if (curves.y !== undefined) times = times.concat(curves.y.times)\n    if (curves.z !== undefined) times = times.concat(curves.z.times)\n\n    // then sort them\n    times = times.sort(function (a, b) {\n      return a - b\n    })\n\n    // and remove duplicates\n    if (times.length > 1) {\n      let targetIndex = 1\n      let lastValue = times[0]\n      for (let i = 1; i < times.length; i++) {\n        const currentValue = times[i]\n        if (currentValue !== lastValue) {\n          times[targetIndex] = currentValue\n          lastValue = currentValue\n          targetIndex++\n        }\n      }\n\n      times = times.slice(0, targetIndex)\n    }\n\n    return times\n  }\n\n  getKeyframeTrackValues(times, curves, initialValue) {\n    const prevValue = initialValue\n\n    const values = []\n\n    let xIndex = -1\n    let yIndex = -1\n    let zIndex = -1\n\n    times.forEach(function (time) {\n      if (curves.x) xIndex = curves.x.times.indexOf(time)\n      if (curves.y) yIndex = curves.y.times.indexOf(time)\n      if (curves.z) zIndex = curves.z.times.indexOf(time)\n\n      // if there is an x value defined for this frame, use that\n      if (xIndex !== -1) {\n        const xValue = curves.x.values[xIndex]\n        values.push(xValue)\n        prevValue[0] = xValue\n      } else {\n        // otherwise use the x value from the previous frame\n        values.push(prevValue[0])\n      }\n\n      if (yIndex !== -1) {\n        const yValue = curves.y.values[yIndex]\n        values.push(yValue)\n        prevValue[1] = yValue\n      } else {\n        values.push(prevValue[1])\n      }\n\n      if (zIndex !== -1) {\n        const zValue = curves.z.values[zIndex]\n        values.push(zValue)\n        prevValue[2] = zValue\n      } else {\n        values.push(prevValue[2])\n      }\n    })\n\n    return values\n  }\n\n  // Rotations are defined as Euler angles which can have values  of any size\n  // These will be converted to quaternions which don't support values greater than\n  // PI, so we'll interpolate large rotations\n  interpolateRotations(curve) {\n    for (let i = 1; i < curve.values.length; i++) {\n      const initialValue = curve.values[i - 1]\n      const valuesSpan = curve.values[i] - initialValue\n\n      const absoluteSpan = Math.abs(valuesSpan)\n\n      if (absoluteSpan >= 180) {\n        const numSubIntervals = absoluteSpan / 180\n\n        const step = valuesSpan / numSubIntervals\n        let nextValue = initialValue + step\n\n        const initialTime = curve.times[i - 1]\n        const timeSpan = curve.times[i] - initialTime\n        const interval = timeSpan / numSubIntervals\n        let nextTime = initialTime + interval\n\n        const interpolatedTimes = []\n        const interpolatedValues = []\n\n        while (nextTime < curve.times[i]) {\n          interpolatedTimes.push(nextTime)\n          nextTime += interval\n\n          interpolatedValues.push(nextValue)\n          nextValue += step\n        }\n\n        curve.times = inject(curve.times, i, interpolatedTimes)\n        curve.values = inject(curve.values, i, interpolatedValues)\n      }\n    }\n  }\n}\n\n// parse an FBX file in ASCII format\nclass TextParser {\n  getPrevNode() {\n    return this.nodeStack[this.currentIndent - 2]\n  }\n\n  getCurrentNode() {\n    return this.nodeStack[this.currentIndent - 1]\n  }\n\n  getCurrentProp() {\n    return this.currentProp\n  }\n\n  pushStack(node) {\n    this.nodeStack.push(node)\n    this.currentIndent += 1\n  }\n\n  popStack() {\n    this.nodeStack.pop()\n    this.currentIndent -= 1\n  }\n\n  setCurrentProp(val, name) {\n    this.currentProp = val\n    this.currentPropName = name\n  }\n\n  parse(text) {\n    this.currentIndent = 0\n\n    this.allNodes = new FBXTree()\n    this.nodeStack = []\n    this.currentProp = []\n    this.currentPropName = ''\n\n    const scope = this\n\n    const split = text.split(/[\\r\\n]+/)\n\n    split.forEach(function (line, i) {\n      const matchComment = line.match(/^[\\s\\t]*;/)\n      const matchEmpty = line.match(/^[\\s\\t]*$/)\n\n      if (matchComment || matchEmpty) return\n\n      const matchBeginning = line.match('^\\\\t{' + scope.currentIndent + '}(\\\\w+):(.*){', '')\n      const matchProperty = line.match('^\\\\t{' + scope.currentIndent + '}(\\\\w+):[\\\\s\\\\t\\\\r\\\\n](.*)')\n      const matchEnd = line.match('^\\\\t{' + (scope.currentIndent - 1) + '}}')\n\n      if (matchBeginning) {\n        scope.parseNodeBegin(line, matchBeginning)\n      } else if (matchProperty) {\n        scope.parseNodeProperty(line, matchProperty, split[++i])\n      } else if (matchEnd) {\n        scope.popStack()\n      } else if (line.match(/^[^\\s\\t}]/)) {\n        // large arrays are split over multiple lines terminated with a ',' character\n        // if this is encountered the line needs to be joined to the previous line\n        scope.parseNodePropertyContinued(line)\n      }\n    })\n\n    return this.allNodes\n  }\n\n  parseNodeBegin(line, property) {\n    const nodeName = property[1].trim().replace(/^\"/, '').replace(/\"$/, '')\n\n    const nodeAttrs = property[2].split(',').map(function (attr) {\n      return attr.trim().replace(/^\"/, '').replace(/\"$/, '')\n    })\n\n    const node = { name: nodeName }\n    const attrs = this.parseNodeAttr(nodeAttrs)\n\n    const currentNode = this.getCurrentNode()\n\n    // a top node\n    if (this.currentIndent === 0) {\n      this.allNodes.add(nodeName, node)\n    } else {\n      // a subnode\n\n      // if the subnode already exists, append it\n      if (nodeName in currentNode) {\n        // special case Pose needs PoseNodes as an array\n        if (nodeName === 'PoseNode') {\n          currentNode.PoseNode.push(node)\n        } else if (currentNode[nodeName].id !== undefined) {\n          currentNode[nodeName] = {}\n          currentNode[nodeName][currentNode[nodeName].id] = currentNode[nodeName]\n        }\n\n        if (attrs.id !== '') currentNode[nodeName][attrs.id] = node\n      } else if (typeof attrs.id === 'number') {\n        currentNode[nodeName] = {}\n        currentNode[nodeName][attrs.id] = node\n      } else if (nodeName !== 'Properties70') {\n        if (nodeName === 'PoseNode') currentNode[nodeName] = [node]\n        else currentNode[nodeName] = node\n      }\n    }\n\n    if (typeof attrs.id === 'number') node.id = attrs.id\n    if (attrs.name !== '') node.attrName = attrs.name\n    if (attrs.type !== '') node.attrType = attrs.type\n\n    this.pushStack(node)\n  }\n\n  parseNodeAttr(attrs) {\n    let id = attrs[0]\n\n    if (attrs[0] !== '') {\n      id = parseInt(attrs[0])\n\n      if (isNaN(id)) {\n        id = attrs[0]\n      }\n    }\n\n    let name = '',\n      type = ''\n\n    if (attrs.length > 1) {\n      name = attrs[1].replace(/^(\\w+)::/, '')\n      type = attrs[2]\n    }\n\n    return { id: id, name: name, type: type }\n  }\n\n  parseNodeProperty(line, property, contentLine) {\n    let propName = property[1].replace(/^\"/, '').replace(/\"$/, '').trim()\n    let propValue = property[2].replace(/^\"/, '').replace(/\"$/, '').trim()\n\n    // for special case: base64 image data follows \"Content: ,\" line\n    //\tContent: ,\n    //\t \"/9j/4RDaRXhpZgAATU0A...\"\n    if (propName === 'Content' && propValue === ',') {\n      propValue = contentLine.replace(/\"/g, '').replace(/,$/, '').trim()\n    }\n\n    const currentNode = this.getCurrentNode()\n    const parentName = currentNode.name\n\n    if (parentName === 'Properties70') {\n      this.parseNodeSpecialProperty(line, propName, propValue)\n      return\n    }\n\n    // Connections\n    if (propName === 'C') {\n      const connProps = propValue.split(',').slice(1)\n      const from = parseInt(connProps[0])\n      const to = parseInt(connProps[1])\n\n      let rest = propValue.split(',').slice(3)\n\n      rest = rest.map(function (elem) {\n        return elem.trim().replace(/^\"/, '')\n      })\n\n      propName = 'connections'\n      propValue = [from, to]\n      append(propValue, rest)\n\n      if (currentNode[propName] === undefined) {\n        currentNode[propName] = []\n      }\n    }\n\n    // Node\n    if (propName === 'Node') currentNode.id = propValue\n\n    // connections\n    if (propName in currentNode && Array.isArray(currentNode[propName])) {\n      currentNode[propName].push(propValue)\n    } else {\n      if (propName !== 'a') currentNode[propName] = propValue\n      else currentNode.a = propValue\n    }\n\n    this.setCurrentProp(currentNode, propName)\n\n    // convert string to array, unless it ends in ',' in which case more will be added to it\n    if (propName === 'a' && propValue.slice(-1) !== ',') {\n      currentNode.a = parseNumberArray(propValue)\n    }\n  }\n\n  parseNodePropertyContinued(line) {\n    const currentNode = this.getCurrentNode()\n\n    currentNode.a += line\n\n    // if the line doesn't end in ',' we have reached the end of the property value\n    // so convert the string to an array\n    if (line.slice(-1) !== ',') {\n      currentNode.a = parseNumberArray(currentNode.a)\n    }\n  }\n\n  // parse \"Property70\"\n  parseNodeSpecialProperty(line, propName, propValue) {\n    // split this\n    // P: \"Lcl Scaling\", \"Lcl Scaling\", \"\", \"A\",1,1,1\n    // into array like below\n    // [\"Lcl Scaling\", \"Lcl Scaling\", \"\", \"A\", \"1,1,1\" ]\n    const props = propValue.split('\",').map(function (prop) {\n      return prop.trim().replace(/^\\\"/, '').replace(/\\s/, '_')\n    })\n\n    const innerPropName = props[0]\n    const innerPropType1 = props[1]\n    const innerPropType2 = props[2]\n    const innerPropFlag = props[3]\n    let innerPropValue = props[4]\n\n    // cast values where needed, otherwise leave as strings\n    switch (innerPropType1) {\n      case 'int':\n      case 'enum':\n      case 'bool':\n      case 'ULongLong':\n      case 'double':\n      case 'Number':\n      case 'FieldOfView':\n        innerPropValue = parseFloat(innerPropValue)\n        break\n\n      case 'Color':\n      case 'ColorRGB':\n      case 'Vector3D':\n      case 'Lcl_Translation':\n      case 'Lcl_Rotation':\n      case 'Lcl_Scaling':\n        innerPropValue = parseNumberArray(innerPropValue)\n        break\n    }\n\n    // CAUTION: these props must append to parent's parent\n    this.getPrevNode()[innerPropName] = {\n      type: innerPropType1,\n      type2: innerPropType2,\n      flag: innerPropFlag,\n      value: innerPropValue,\n    }\n\n    this.setCurrentProp(this.getPrevNode(), innerPropName)\n  }\n}\n\n// Parse an FBX file in Binary format\nclass BinaryParser {\n  parse(buffer) {\n    const reader = new BinaryReader(buffer)\n    reader.skip(23) // skip magic 23 bytes\n\n    const version = reader.getUint32()\n\n    if (version < 6400) {\n      throw new Error('THREE.FBXLoader: FBX version not supported, FileVersion: ' + version)\n    }\n\n    const allNodes = new FBXTree()\n\n    while (!this.endOfContent(reader)) {\n      const node = this.parseNode(reader, version)\n      if (node !== null) allNodes.add(node.name, node)\n    }\n\n    return allNodes\n  }\n\n  // Check if reader has reached the end of content.\n  endOfContent(reader) {\n    // footer size: 160bytes + 16-byte alignment padding\n    // - 16bytes: magic\n    // - padding til 16-byte alignment (at least 1byte?)\n    //\t(seems like some exporters embed fixed 15 or 16bytes?)\n    // - 4bytes: magic\n    // - 4bytes: version\n    // - 120bytes: zero\n    // - 16bytes: magic\n    if (reader.size() % 16 === 0) {\n      return ((reader.getOffset() + 160 + 16) & ~0xf) >= reader.size()\n    } else {\n      return reader.getOffset() + 160 + 16 >= reader.size()\n    }\n  }\n\n  // recursively parse nodes until the end of the file is reached\n  parseNode(reader, version) {\n    const node = {}\n\n    // The first three data sizes depends on version.\n    const endOffset = version >= 7500 ? reader.getUint64() : reader.getUint32()\n    const numProperties = version >= 7500 ? reader.getUint64() : reader.getUint32()\n\n    version >= 7500 ? reader.getUint64() : reader.getUint32() // the returned propertyListLen is not used\n\n    const nameLen = reader.getUint8()\n    const name = reader.getString(nameLen)\n\n    // Regards this node as NULL-record if endOffset is zero\n    if (endOffset === 0) return null\n\n    const propertyList = []\n\n    for (let i = 0; i < numProperties; i++) {\n      propertyList.push(this.parseProperty(reader))\n    }\n\n    // Regards the first three elements in propertyList as id, attrName, and attrType\n    const id = propertyList.length > 0 ? propertyList[0] : ''\n    const attrName = propertyList.length > 1 ? propertyList[1] : ''\n    const attrType = propertyList.length > 2 ? propertyList[2] : ''\n\n    // check if this node represents just a single property\n    // like (name, 0) set or (name2, [0, 1, 2]) set of {name: 0, name2: [0, 1, 2]}\n    node.singleProperty = numProperties === 1 && reader.getOffset() === endOffset ? true : false\n\n    while (endOffset > reader.getOffset()) {\n      const subNode = this.parseNode(reader, version)\n\n      if (subNode !== null) this.parseSubNode(name, node, subNode)\n    }\n\n    node.propertyList = propertyList // raw property list used by parent\n\n    if (typeof id === 'number') node.id = id\n    if (attrName !== '') node.attrName = attrName\n    if (attrType !== '') node.attrType = attrType\n    if (name !== '') node.name = name\n\n    return node\n  }\n\n  parseSubNode(name, node, subNode) {\n    // special case: child node is single property\n    if (subNode.singleProperty === true) {\n      const value = subNode.propertyList[0]\n\n      if (Array.isArray(value)) {\n        node[subNode.name] = subNode\n\n        subNode.a = value\n      } else {\n        node[subNode.name] = value\n      }\n    } else if (name === 'Connections' && subNode.name === 'C') {\n      const array = []\n\n      subNode.propertyList.forEach(function (property, i) {\n        // first Connection is FBX type (OO, OP, etc.). We'll discard these\n        if (i !== 0) array.push(property)\n      })\n\n      if (node.connections === undefined) {\n        node.connections = []\n      }\n\n      node.connections.push(array)\n    } else if (subNode.name === 'Properties70') {\n      const keys = Object.keys(subNode)\n\n      keys.forEach(function (key) {\n        node[key] = subNode[key]\n      })\n    } else if (name === 'Properties70' && subNode.name === 'P') {\n      let innerPropName = subNode.propertyList[0]\n      let innerPropType1 = subNode.propertyList[1]\n      const innerPropType2 = subNode.propertyList[2]\n      const innerPropFlag = subNode.propertyList[3]\n      let innerPropValue\n\n      if (innerPropName.indexOf('Lcl ') === 0) innerPropName = innerPropName.replace('Lcl ', 'Lcl_')\n      if (innerPropType1.indexOf('Lcl ') === 0) innerPropType1 = innerPropType1.replace('Lcl ', 'Lcl_')\n\n      if (\n        innerPropType1 === 'Color' ||\n        innerPropType1 === 'ColorRGB' ||\n        innerPropType1 === 'Vector' ||\n        innerPropType1 === 'Vector3D' ||\n        innerPropType1.indexOf('Lcl_') === 0\n      ) {\n        innerPropValue = [subNode.propertyList[4], subNode.propertyList[5], subNode.propertyList[6]]\n      } else {\n        innerPropValue = subNode.propertyList[4]\n      }\n\n      // this will be copied to parent, see above\n      node[innerPropName] = {\n        type: innerPropType1,\n        type2: innerPropType2,\n        flag: innerPropFlag,\n        value: innerPropValue,\n      }\n    } else if (node[subNode.name] === undefined) {\n      if (typeof subNode.id === 'number') {\n        node[subNode.name] = {}\n        node[subNode.name][subNode.id] = subNode\n      } else {\n        node[subNode.name] = subNode\n      }\n    } else {\n      if (subNode.name === 'PoseNode') {\n        if (!Array.isArray(node[subNode.name])) {\n          node[subNode.name] = [node[subNode.name]]\n        }\n\n        node[subNode.name].push(subNode)\n      } else if (node[subNode.name][subNode.id] === undefined) {\n        node[subNode.name][subNode.id] = subNode\n      }\n    }\n  }\n\n  parseProperty(reader) {\n    const type = reader.getString(1)\n    let length\n\n    switch (type) {\n      case 'C':\n        return reader.getBoolean()\n\n      case 'D':\n        return reader.getFloat64()\n\n      case 'F':\n        return reader.getFloat32()\n\n      case 'I':\n        return reader.getInt32()\n\n      case 'L':\n        return reader.getInt64()\n\n      case 'R':\n        length = reader.getUint32()\n        return reader.getArrayBuffer(length)\n\n      case 'S':\n        length = reader.getUint32()\n        return reader.getString(length)\n\n      case 'Y':\n        return reader.getInt16()\n\n      case 'b':\n      case 'c':\n      case 'd':\n      case 'f':\n      case 'i':\n      case 'l':\n        const arrayLength = reader.getUint32()\n        const encoding = reader.getUint32() // 0: non-compressed, 1: compressed\n        const compressedLength = reader.getUint32()\n\n        if (encoding === 0) {\n          switch (type) {\n            case 'b':\n            case 'c':\n              return reader.getBooleanArray(arrayLength)\n\n            case 'd':\n              return reader.getFloat64Array(arrayLength)\n\n            case 'f':\n              return reader.getFloat32Array(arrayLength)\n\n            case 'i':\n              return reader.getInt32Array(arrayLength)\n\n            case 'l':\n              return reader.getInt64Array(arrayLength)\n          }\n        }\n\n        const data = unzlibSync(new Uint8Array(reader.getArrayBuffer(compressedLength)))\n        const reader2 = new BinaryReader(data.buffer)\n\n        switch (type) {\n          case 'b':\n          case 'c':\n            return reader2.getBooleanArray(arrayLength)\n\n          case 'd':\n            return reader2.getFloat64Array(arrayLength)\n\n          case 'f':\n            return reader2.getFloat32Array(arrayLength)\n\n          case 'i':\n            return reader2.getInt32Array(arrayLength)\n\n          case 'l':\n            return reader2.getInt64Array(arrayLength)\n        }\n\n      default:\n        throw new Error('THREE.FBXLoader: Unknown property type ' + type)\n    }\n  }\n}\n\nclass BinaryReader {\n  constructor(buffer, littleEndian) {\n    this.dv = new DataView(buffer)\n    this.offset = 0\n    this.littleEndian = littleEndian !== undefined ? littleEndian : true\n  }\n\n  getOffset() {\n    return this.offset\n  }\n\n  size() {\n    return this.dv.buffer.byteLength\n  }\n\n  skip(length) {\n    this.offset += length\n  }\n\n  // seems like true/false representation depends on exporter.\n  // true: 1 or 'Y'(=0x59), false: 0 or 'T'(=0x54)\n  // then sees LSB.\n  getBoolean() {\n    return (this.getUint8() & 1) === 1\n  }\n\n  getBooleanArray(size) {\n    const a = []\n\n    for (let i = 0; i < size; i++) {\n      a.push(this.getBoolean())\n    }\n\n    return a\n  }\n\n  getUint8() {\n    const value = this.dv.getUint8(this.offset)\n    this.offset += 1\n    return value\n  }\n\n  getInt16() {\n    const value = this.dv.getInt16(this.offset, this.littleEndian)\n    this.offset += 2\n    return value\n  }\n\n  getInt32() {\n    const value = this.dv.getInt32(this.offset, this.littleEndian)\n    this.offset += 4\n    return value\n  }\n\n  getInt32Array(size) {\n    const a = []\n\n    for (let i = 0; i < size; i++) {\n      a.push(this.getInt32())\n    }\n\n    return a\n  }\n\n  getUint32() {\n    const value = this.dv.getUint32(this.offset, this.littleEndian)\n    this.offset += 4\n    return value\n  }\n\n  // JavaScript doesn't support 64-bit integer so calculate this here\n  // 1 << 32 will return 1 so using multiply operation instead here.\n  // There's a possibility that this method returns wrong value if the value\n  // is out of the range between Number.MAX_SAFE_INTEGER and Number.MIN_SAFE_INTEGER.\n  // TODO: safely handle 64-bit integer\n  getInt64() {\n    let low, high\n\n    if (this.littleEndian) {\n      low = this.getUint32()\n      high = this.getUint32()\n    } else {\n      high = this.getUint32()\n      low = this.getUint32()\n    }\n\n    // calculate negative value\n    if (high & 0x80000000) {\n      high = ~high & 0xffffffff\n      low = ~low & 0xffffffff\n\n      if (low === 0xffffffff) high = (high + 1) & 0xffffffff\n\n      low = (low + 1) & 0xffffffff\n\n      return -(high * 0x100000000 + low)\n    }\n\n    return high * 0x100000000 + low\n  }\n\n  getInt64Array(size) {\n    const a = []\n\n    for (let i = 0; i < size; i++) {\n      a.push(this.getInt64())\n    }\n\n    return a\n  }\n\n  // Note: see getInt64() comment\n  getUint64() {\n    let low, high\n\n    if (this.littleEndian) {\n      low = this.getUint32()\n      high = this.getUint32()\n    } else {\n      high = this.getUint32()\n      low = this.getUint32()\n    }\n\n    return high * 0x100000000 + low\n  }\n\n  getFloat32() {\n    const value = this.dv.getFloat32(this.offset, this.littleEndian)\n    this.offset += 4\n    return value\n  }\n\n  getFloat32Array(size) {\n    const a = []\n\n    for (let i = 0; i < size; i++) {\n      a.push(this.getFloat32())\n    }\n\n    return a\n  }\n\n  getFloat64() {\n    const value = this.dv.getFloat64(this.offset, this.littleEndian)\n    this.offset += 8\n    return value\n  }\n\n  getFloat64Array(size) {\n    const a = []\n\n    for (let i = 0; i < size; i++) {\n      a.push(this.getFloat64())\n    }\n\n    return a\n  }\n\n  getArrayBuffer(size) {\n    const value = this.dv.buffer.slice(this.offset, this.offset + size)\n    this.offset += size\n    return value\n  }\n\n  getString(size) {\n    // note: safari 9 doesn't support Uint8Array.indexOf; create intermediate array instead\n    let a = []\n\n    for (let i = 0; i < size; i++) {\n      a[i] = this.getUint8()\n    }\n\n    const nullByte = a.indexOf(0)\n    if (nullByte >= 0) a = a.slice(0, nullByte)\n\n    return decodeText(new Uint8Array(a))\n  }\n}\n\n// FBXTree holds a representation of the FBX data, returned by the TextParser ( FBX ASCII format)\n// and BinaryParser( FBX Binary format)\nclass FBXTree {\n  add(key, val) {\n    this[key] = val\n  }\n}\n\n// ************** UTILITY FUNCTIONS **************\n\nfunction isFbxFormatBinary(buffer) {\n  const CORRECT = 'Kaydara\\u0020FBX\\u0020Binary\\u0020\\u0020\\0'\n\n  return buffer.byteLength >= CORRECT.length && CORRECT === convertArrayBufferToString(buffer, 0, CORRECT.length)\n}\n\nfunction isFbxFormatASCII(text) {\n  const CORRECT = [\n    'K',\n    'a',\n    'y',\n    'd',\n    'a',\n    'r',\n    'a',\n    '\\\\',\n    'F',\n    'B',\n    'X',\n    '\\\\',\n    'B',\n    'i',\n    'n',\n    'a',\n    'r',\n    'y',\n    '\\\\',\n    '\\\\',\n  ]\n\n  let cursor = 0\n\n  function read(offset) {\n    const result = text[offset - 1]\n    text = text.slice(cursor + offset)\n    cursor++\n    return result\n  }\n\n  for (let i = 0; i < CORRECT.length; ++i) {\n    const num = read(1)\n    if (num === CORRECT[i]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nfunction getFbxVersion(text) {\n  const versionRegExp = /FBXVersion: (\\d+)/\n  const match = text.match(versionRegExp)\n\n  if (match) {\n    const version = parseInt(match[1])\n    return version\n  }\n\n  throw new Error('THREE.FBXLoader: Cannot find the version number for the file given.')\n}\n\n// Converts FBX ticks into real time seconds.\nfunction convertFBXTimeToSeconds(time) {\n  return time / 46186158000\n}\n\nconst dataArray = []\n\n// extracts the data from the correct position in the FBX array based on indexing type\nfunction getData(polygonVertexIndex, polygonIndex, vertexIndex, infoObject) {\n  let index\n\n  switch (infoObject.mappingType) {\n    case 'ByPolygonVertex':\n      index = polygonVertexIndex\n      break\n    case 'ByPolygon':\n      index = polygonIndex\n      break\n    case 'ByVertice':\n      index = vertexIndex\n      break\n    case 'AllSame':\n      index = infoObject.indices[0]\n      break\n    default:\n      console.warn('THREE.FBXLoader: unknown attribute mapping type ' + infoObject.mappingType)\n  }\n\n  if (infoObject.referenceType === 'IndexToDirect') index = infoObject.indices[index]\n\n  const from = index * infoObject.dataSize\n  const to = from + infoObject.dataSize\n\n  return slice(dataArray, infoObject.buffer, from, to)\n}\n\nconst tempEuler = /* @__PURE__ */ new Euler()\nconst tempVec = /* @__PURE__ */ new Vector3()\n\n// generate transformation from FBX transform data\n// ref: https://help.autodesk.com/view/FBX/2017/ENU/?guid=__files_GUID_10CDD63C_79C1_4F2D_BB28_AD2BE65A02ED_htm\n// ref: http://docs.autodesk.com/FBX/2014/ENU/FBX-SDK-Documentation/index.html?url=cpp_ref/_transformations_2main_8cxx-example.html,topicNumber=cpp_ref__transformations_2main_8cxx_example_htmlfc10a1e1-b18d-4e72-9dc0-70d0f1959f5e\nfunction generateTransform(transformData) {\n  const lTranslationM = new Matrix4()\n  const lPreRotationM = new Matrix4()\n  const lRotationM = new Matrix4()\n  const lPostRotationM = new Matrix4()\n\n  const lScalingM = new Matrix4()\n  const lScalingPivotM = new Matrix4()\n  const lScalingOffsetM = new Matrix4()\n  const lRotationOffsetM = new Matrix4()\n  const lRotationPivotM = new Matrix4()\n\n  const lParentGX = new Matrix4()\n  const lParentLX = new Matrix4()\n  const lGlobalT = new Matrix4()\n\n  const inheritType = transformData.inheritType ? transformData.inheritType : 0\n\n  if (transformData.translation) lTranslationM.setPosition(tempVec.fromArray(transformData.translation))\n\n  if (transformData.preRotation) {\n    const array = transformData.preRotation.map(MathUtils.degToRad)\n    array.push(transformData.eulerOrder)\n    lPreRotationM.makeRotationFromEuler(tempEuler.fromArray(array))\n  }\n\n  if (transformData.rotation) {\n    const array = transformData.rotation.map(MathUtils.degToRad)\n    array.push(transformData.eulerOrder)\n    lRotationM.makeRotationFromEuler(tempEuler.fromArray(array))\n  }\n\n  if (transformData.postRotation) {\n    const array = transformData.postRotation.map(MathUtils.degToRad)\n    array.push(transformData.eulerOrder)\n    lPostRotationM.makeRotationFromEuler(tempEuler.fromArray(array))\n    lPostRotationM.invert()\n  }\n\n  if (transformData.scale) lScalingM.scale(tempVec.fromArray(transformData.scale))\n\n  // Pivots and offsets\n  if (transformData.scalingOffset) lScalingOffsetM.setPosition(tempVec.fromArray(transformData.scalingOffset))\n  if (transformData.scalingPivot) lScalingPivotM.setPosition(tempVec.fromArray(transformData.scalingPivot))\n  if (transformData.rotationOffset) lRotationOffsetM.setPosition(tempVec.fromArray(transformData.rotationOffset))\n  if (transformData.rotationPivot) lRotationPivotM.setPosition(tempVec.fromArray(transformData.rotationPivot))\n\n  // parent transform\n  if (transformData.parentMatrixWorld) {\n    lParentLX.copy(transformData.parentMatrix)\n    lParentGX.copy(transformData.parentMatrixWorld)\n  }\n\n  const lLRM = lPreRotationM.clone().multiply(lRotationM).multiply(lPostRotationM)\n  // Global Rotation\n  const lParentGRM = new Matrix4()\n  lParentGRM.extractRotation(lParentGX)\n\n  // Global Shear*Scaling\n  const lParentTM = new Matrix4()\n  lParentTM.copyPosition(lParentGX)\n\n  const lParentGRSM = lParentTM.clone().invert().multiply(lParentGX)\n  const lParentGSM = lParentGRM.clone().invert().multiply(lParentGRSM)\n  const lLSM = lScalingM\n\n  const lGlobalRS = new Matrix4()\n\n  if (inheritType === 0) {\n    lGlobalRS.copy(lParentGRM).multiply(lLRM).multiply(lParentGSM).multiply(lLSM)\n  } else if (inheritType === 1) {\n    lGlobalRS.copy(lParentGRM).multiply(lParentGSM).multiply(lLRM).multiply(lLSM)\n  } else {\n    const lParentLSM = new Matrix4().scale(new Vector3().setFromMatrixScale(lParentLX))\n    const lParentLSM_inv = lParentLSM.clone().invert()\n    const lParentGSM_noLocal = lParentGSM.clone().multiply(lParentLSM_inv)\n\n    lGlobalRS.copy(lParentGRM).multiply(lLRM).multiply(lParentGSM_noLocal).multiply(lLSM)\n  }\n\n  const lRotationPivotM_inv = lRotationPivotM.clone().invert()\n  const lScalingPivotM_inv = lScalingPivotM.clone().invert()\n  // Calculate the local transform matrix\n  let lTransform = lTranslationM\n    .clone()\n    .multiply(lRotationOffsetM)\n    .multiply(lRotationPivotM)\n    .multiply(lPreRotationM)\n    .multiply(lRotationM)\n    .multiply(lPostRotationM)\n    .multiply(lRotationPivotM_inv)\n    .multiply(lScalingOffsetM)\n    .multiply(lScalingPivotM)\n    .multiply(lScalingM)\n    .multiply(lScalingPivotM_inv)\n\n  const lLocalTWithAllPivotAndOffsetInfo = new Matrix4().copyPosition(lTransform)\n\n  const lGlobalTranslation = lParentGX.clone().multiply(lLocalTWithAllPivotAndOffsetInfo)\n  lGlobalT.copyPosition(lGlobalTranslation)\n\n  lTransform = lGlobalT.clone().multiply(lGlobalRS)\n\n  // from global to local\n  lTransform.premultiply(lParentGX.invert())\n\n  return lTransform\n}\n\n// Returns the three.js intrinsic Euler order corresponding to FBX extrinsic Euler order\n// ref: http://help.autodesk.com/view/FBX/2017/ENU/?guid=__cpp_ref_class_fbx_euler_html\nfunction getEulerOrder(order) {\n  order = order || 0\n\n  const enums = [\n    'ZYX', // -> XYZ extrinsic\n    'YZX', // -> XZY extrinsic\n    'XZY', // -> YZX extrinsic\n    'ZXY', // -> YXZ extrinsic\n    'YXZ', // -> ZXY extrinsic\n    'XYZ', // -> ZYX extrinsic\n    //'SphericXYZ', // not possible to support\n  ]\n\n  if (order === 6) {\n    console.warn('THREE.FBXLoader: unsupported Euler Order: Spherical XYZ. Animations and rotations may be incorrect.')\n    return enums[0]\n  }\n\n  return enums[order]\n}\n\n// Parses comma separated list of numbers and returns them an array.\n// Used internally by the TextParser\nfunction parseNumberArray(value) {\n  const array = value.split(',').map(function (val) {\n    return parseFloat(val)\n  })\n\n  return array\n}\n\nfunction convertArrayBufferToString(buffer, from, to) {\n  if (from === undefined) from = 0\n  if (to === undefined) to = buffer.byteLength\n\n  return decodeText(new Uint8Array(buffer, from, to))\n}\n\nfunction append(a, b) {\n  for (let i = 0, j = a.length, l = b.length; i < l; i++, j++) {\n    a[j] = b[i]\n  }\n}\n\nfunction slice(a, b, from, to) {\n  for (let i = from, j = 0; i < to; i++, j++) {\n    a[j] = b[i]\n  }\n\n  return a\n}\n\n// inject array a2 into array a1 at index\nfunction inject(a1, index, a2) {\n  return a1.slice(0, index).concat(a2).concat(a1.slice(index))\n}\n\nexport { FBXLoader }\n"], "mappings": ";;;;;AA8DA,IAAIA,OAAA;AACJ,IAAIC,WAAA;AACJ,IAAIC,UAAA;AAEJ,MAAMC,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAII,KAAA,CAAMC,IAAA;IAEzE,MAAMG,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKX,OAAO;IAC1CU,MAAA,CAAOE,OAAA,CAAQN,KAAA,CAAMC,IAAI;IACzBG,MAAA,CAAOG,eAAA,CAAgB,aAAa;IACpCH,MAAA,CAAOI,gBAAA,CAAiBR,KAAA,CAAMS,aAAa;IAC3CL,MAAA,CAAOM,kBAAA,CAAmBV,KAAA,CAAMW,eAAe;IAE/CP,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUgB,MAAA,EAAQ;MAChB,IAAI;QACFf,MAAA,CAAOG,KAAA,CAAMa,KAAA,CAAMD,MAAA,EAAQX,IAAI,CAAC;MACjC,SAAQa,CAAA,EAAP;QACA,IAAIf,OAAA,EAAS;UACXA,OAAA,CAAQe,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDd,KAAA,CAAMN,OAAA,CAAQuB,SAAA,CAAUrB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDc,MAAMK,SAAA,EAAWjB,IAAA,EAAM;IACrB,IAAIkB,iBAAA,CAAkBD,SAAS,GAAG;MAChC9B,OAAA,GAAU,IAAIgC,YAAA,GAAeP,KAAA,CAAMK,SAAS;IAClD,OAAW;MACL,MAAMG,OAAA,GAAUC,0BAAA,CAA2BJ,SAAS;MAEpD,IAAI,CAACK,gBAAA,CAAiBF,OAAO,GAAG;QAC9B,MAAM,IAAIG,KAAA,CAAM,kCAAkC;MACnD;MAED,IAAIC,aAAA,CAAcJ,OAAO,IAAI,KAAM;QACjC,MAAM,IAAIG,KAAA,CAAM,8DAA8DC,aAAA,CAAcJ,OAAO,CAAC;MACrG;MAEDjC,OAAA,GAAU,IAAIsC,UAAA,GAAab,KAAA,CAAMQ,OAAO;IACzC;IAID,MAAMM,aAAA,GAAgB,IAAIC,aAAA,CAAc,KAAKlC,OAAO,EACjDY,OAAA,CAAQ,KAAKuB,YAAA,IAAgB5B,IAAI,EACjC6B,cAAA,CAAe,KAAKC,WAAW;IAElC,OAAO,IAAIC,aAAA,CAAcL,aAAA,EAAe,KAAKjC,OAAO,EAAEmB,KAAA,CAAMzB,OAAO;EACpE;AACH;AAGA,MAAM4C,aAAA,CAAc;EAClBvC,YAAYkC,aAAA,EAAejC,OAAA,EAAS;IAClC,KAAKiC,aAAA,GAAgBA,aAAA;IACrB,KAAKjC,OAAA,GAAUA,OAAA;EAChB;EAEDmB,MAAA,EAAQ;IACNxB,WAAA,GAAc,KAAK4C,gBAAA,CAAkB;IAErC,MAAMC,MAAA,GAAS,KAAKC,WAAA,CAAa;IACjC,MAAMC,QAAA,GAAW,KAAKC,aAAA,CAAcH,MAAM;IAC1C,MAAMI,SAAA,GAAY,KAAKC,cAAA,CAAeH,QAAQ;IAC9C,MAAMI,SAAA,GAAY,KAAKC,cAAA,CAAgB;IACvC,MAAMC,WAAA,GAAc,IAAIC,cAAA,GAAiB9B,KAAA,CAAM2B,SAAS;IAExD,KAAKI,UAAA,CAAWJ,SAAA,EAAWE,WAAA,EAAaJ,SAAS;IAEjD,OAAOhD,UAAA;EACR;EAAA;EAAA;EAID2C,iBAAA,EAAmB;IACjB,MAAMY,aAAA,GAAgB,mBAAIC,GAAA,CAAK;IAE/B,IAAI,iBAAiB1D,OAAA,EAAS;MAC5B,MAAM2D,cAAA,GAAiB3D,OAAA,CAAQ4D,WAAA,CAAY3D,WAAA;MAE3C0D,cAAA,CAAeE,OAAA,CAAQ,UAAUC,aAAA,EAAe;QAC9C,MAAMC,MAAA,GAASD,aAAA,CAAc,CAAC;QAC9B,MAAME,IAAA,GAAOF,aAAA,CAAc,CAAC;QAC5B,MAAMG,YAAA,GAAeH,aAAA,CAAc,CAAC;QAEpC,IAAI,CAACL,aAAA,CAAcS,GAAA,CAAIH,MAAM,GAAG;UAC9BN,aAAA,CAAcU,GAAA,CAAIJ,MAAA,EAAQ;YACxBK,OAAA,EAAS,EAAE;YACXC,QAAA,EAAU;UACtB,CAAW;QACF;QAED,MAAMC,kBAAA,GAAqB;UAAEC,EAAA,EAAIP,IAAA;UAAMC;QAA4B;QACnER,aAAA,CAAce,GAAA,CAAIT,MAAM,EAAEK,OAAA,CAAQK,IAAA,CAAKH,kBAAkB;QAEzD,IAAI,CAACb,aAAA,CAAcS,GAAA,CAAIF,IAAI,GAAG;UAC5BP,aAAA,CAAcU,GAAA,CAAIH,IAAA,EAAM;YACtBI,OAAA,EAAS,EAAE;YACXC,QAAA,EAAU;UACtB,CAAW;QACF;QAED,MAAMK,iBAAA,GAAoB;UAAEH,EAAA,EAAIR,MAAA;UAAQE;QAA4B;QACpER,aAAA,CAAce,GAAA,CAAIR,IAAI,EAAEK,QAAA,CAASI,IAAA,CAAKC,iBAAiB;MAC/D,CAAO;IACF;IAED,OAAOjB,aAAA;EACR;EAAA;EAAA;EAAA;EAKDV,YAAA,EAAc;IACZ,MAAMD,MAAA,GAAS,CAAE;IACjB,MAAM6B,KAAA,GAAQ,CAAE;IAEhB,IAAI,WAAW3E,OAAA,CAAQ4E,OAAA,EAAS;MAC9B,MAAMC,UAAA,GAAa7E,OAAA,CAAQ4E,OAAA,CAAQE,KAAA;MAEnC,WAAWC,MAAA,IAAUF,UAAA,EAAY;QAC/B,MAAMG,SAAA,GAAYH,UAAA,CAAWE,MAAM;QAEnC,MAAME,EAAA,GAAKC,QAAA,CAASH,MAAM;QAE1BjC,MAAA,CAAOmC,EAAE,IAAID,SAAA,CAAUG,gBAAA,IAAoBH,SAAA,CAAUI,QAAA;QAGrD,IAAI,aAAaJ,SAAA,EAAW;UAC1B,MAAMK,kBAAA,GAAqBL,SAAA,CAAUM,OAAA,YAAmBC,WAAA,IAAeP,SAAA,CAAUM,OAAA,CAAQE,UAAA,GAAa;UACtG,MAAMC,aAAA,GAAgB,OAAOT,SAAA,CAAUM,OAAA,KAAY,YAAYN,SAAA,CAAUM,OAAA,KAAY;UAErF,IAAID,kBAAA,IAAsBI,aAAA,EAAe;YACvC,MAAMC,KAAA,GAAQ,KAAKC,UAAA,CAAWd,UAAA,CAAWE,MAAM,CAAC;YAEhDJ,KAAA,CAAMK,SAAA,CAAUG,gBAAA,IAAoBH,SAAA,CAAUI,QAAQ,IAAIM,KAAA;UAC3D;QACF;MACF;IACF;IAED,WAAWT,EAAA,IAAMnC,MAAA,EAAQ;MACvB,MAAM8C,QAAA,GAAW9C,MAAA,CAAOmC,EAAE;MAE1B,IAAIN,KAAA,CAAMiB,QAAQ,MAAM,QAAW9C,MAAA,CAAOmC,EAAE,IAAIN,KAAA,CAAMiB,QAAQ,OACzD9C,MAAA,CAAOmC,EAAE,IAAInC,MAAA,CAAOmC,EAAE,EAAEY,KAAA,CAAM,IAAI,EAAEC,GAAA,CAAK;IAC/C;IAED,OAAOhD,MAAA;EACR;EAAA;EAGD6C,WAAWX,SAAA,EAAW;IACpB,MAAMe,OAAA,GAAUf,SAAA,CAAUM,OAAA;IAC1B,MAAMU,QAAA,GAAWhB,SAAA,CAAUG,gBAAA,IAAoBH,SAAA,CAAUI,QAAA;IACzD,MAAMa,SAAA,GAAYD,QAAA,CAASE,KAAA,CAAMF,QAAA,CAASG,WAAA,CAAY,GAAG,IAAI,CAAC,EAAEC,WAAA,CAAa;IAE7E,IAAIC,IAAA;IAEJ,QAAQJ,SAAA;MACN,KAAK;QACHI,IAAA,GAAO;QACP;MAEF,KAAK;MACL,KAAK;QACHA,IAAA,GAAO;QACP;MAEF,KAAK;QACHA,IAAA,GAAO;QACP;MAEF,KAAK;QACHA,IAAA,GAAO;QACP;MAEF,KAAK;QACH,IAAI,KAAK/F,OAAA,CAAQgG,UAAA,CAAW,MAAM,MAAM,MAAM;UAC5C3E,OAAA,CAAQ4E,IAAA,CAAK,8CAA8CP,QAAQ;QACpE;QAEDK,IAAA,GAAO;QACP;MAEF;QACE1E,OAAA,CAAQ4E,IAAA,CAAK,4BAA4BN,SAAA,GAAY,qBAAqB;QAC1E;IACH;IAED,IAAI,OAAOF,OAAA,KAAY,UAAU;MAG/B,OAAO,UAAUM,IAAA,GAAO,aAAaN,OAAA;IAC3C,OAAW;MAGL,MAAMS,KAAA,GAAQ,IAAIC,UAAA,CAAWV,OAAO;MACpC,OAAOW,MAAA,CAAOC,GAAA,CAAIC,eAAA,CAAgB,IAAIC,IAAA,CAAK,CAACL,KAAK,GAAG;QAAEH;MAAU,CAAE,CAAC;IACpE;EACF;EAAA;EAAA;EAAA;EAKDpD,cAAcH,MAAA,EAAQ;IACpB,MAAMgE,UAAA,GAAa,mBAAIpD,GAAA,CAAK;IAE5B,IAAI,aAAa1D,OAAA,CAAQ4E,OAAA,EAAS;MAChC,MAAMmC,YAAA,GAAe/G,OAAA,CAAQ4E,OAAA,CAAQoC,OAAA;MACrC,WAAWjC,MAAA,IAAUgC,YAAA,EAAc;QACjC,MAAME,OAAA,GAAU,KAAKC,YAAA,CAAaH,YAAA,CAAahC,MAAM,GAAGjC,MAAM;QAC9DgE,UAAA,CAAW3C,GAAA,CAAIe,QAAA,CAASH,MAAM,GAAGkC,OAAO;MACzC;IACF;IAED,OAAOH,UAAA;EACR;EAAA;EAGDI,aAAaC,WAAA,EAAarE,MAAA,EAAQ;IAChC,MAAMmE,OAAA,GAAU,KAAKG,WAAA,CAAYD,WAAA,EAAarE,MAAM;IAEpDmE,OAAA,CAAQ1C,EAAA,GAAK4C,WAAA,CAAYlC,EAAA;IAEzBgC,OAAA,CAAQI,IAAA,GAAOF,WAAA,CAAYG,QAAA;IAE3B,MAAMC,SAAA,GAAYJ,WAAA,CAAYK,SAAA;IAC9B,MAAMC,SAAA,GAAYN,WAAA,CAAYO,SAAA;IAE9B,MAAMC,MAAA,GAASJ,SAAA,KAAc,SAAYA,SAAA,CAAUK,KAAA,GAAQ;IAC3D,MAAMC,MAAA,GAASJ,SAAA,KAAc,SAAYA,SAAA,CAAUG,KAAA,GAAQ;IAK3DX,OAAA,CAAQa,KAAA,GAAQH,MAAA,KAAW,IAAII,cAAA,GAAiBC,mBAAA;IAChDf,OAAA,CAAQgB,KAAA,GAAQJ,MAAA,KAAW,IAAIE,cAAA,GAAiBC,mBAAA;IAEhD,IAAI,aAAab,WAAA,EAAa;MAC5B,MAAMe,MAAA,GAASf,WAAA,CAAYgB,OAAA,CAAQP,KAAA;MAEnCX,OAAA,CAAQmB,MAAA,CAAOC,CAAA,GAAIH,MAAA,CAAO,CAAC;MAC3BjB,OAAA,CAAQmB,MAAA,CAAOE,CAAA,GAAIJ,MAAA,CAAO,CAAC;IAC5B;IAED,OAAOjB,OAAA;EACR;EAAA;EAGDG,YAAYD,WAAA,EAAarE,MAAA,EAAQ;IAC/B,IAAIkD,QAAA;IAEJ,MAAMuC,WAAA,GAAc,KAAKhG,aAAA,CAAc1B,IAAA;IAEvC,MAAMwD,QAAA,GAAWpE,WAAA,CAAYuE,GAAA,CAAI2C,WAAA,CAAYlC,EAAE,EAAEZ,QAAA;IAEjD,IAAIA,QAAA,KAAa,UAAaA,QAAA,CAASmE,MAAA,GAAS,KAAK1F,MAAA,CAAOuB,QAAA,CAAS,CAAC,EAAEE,EAAE,MAAM,QAAW;MACzFyB,QAAA,GAAWlD,MAAA,CAAOuB,QAAA,CAAS,CAAC,EAAEE,EAAE;MAEhC,IAAIyB,QAAA,CAASyC,OAAA,CAAQ,OAAO,MAAM,KAAKzC,QAAA,CAASyC,OAAA,CAAQ,OAAO,MAAM,GAAG;QACtE,KAAKlG,aAAA,CAAcrB,OAAA,CAAQ,MAAS;MACrC;IACF;IAED,IAAI+F,OAAA;IAEJ,MAAMhB,SAAA,GAAYkB,WAAA,CAAYuB,QAAA,CAASxC,KAAA,CAAM,EAAE,EAAEE,WAAA,CAAa;IAE9D,IAAIH,SAAA,KAAc,OAAO;MACvB,MAAMjF,MAAA,GAAS,KAAKV,OAAA,CAAQgG,UAAA,CAAW,MAAM;MAE7C,IAAItF,MAAA,KAAW,MAAM;QACnBW,OAAA,CAAQ4E,IAAA,CAAK,qEAAqEY,WAAA,CAAYhC,gBAAgB;QAC9G8B,OAAA,GAAU,IAAID,OAAA,CAAS;MAC/B,OAAa;QACLhG,MAAA,CAAOE,OAAA,CAAQ,KAAKqB,aAAA,CAAc1B,IAAI;QACtCoG,OAAA,GAAUjG,MAAA,CAAOT,IAAA,CAAKyF,QAAQ;MAC/B;IACP,WAAeC,SAAA,KAAc,OAAO;MAC9BtE,OAAA,CAAQ4E,IAAA,CACN,+EACAY,WAAA,CAAYhC,gBACb;MACD8B,OAAA,GAAU,IAAID,OAAA,CAAS;IAC7B,OAAW;MACLC,OAAA,GAAU,KAAK1E,aAAA,CAAchC,IAAA,CAAKyF,QAAQ;IAC3C;IAED,KAAKzD,aAAA,CAAcrB,OAAA,CAAQqH,WAAW;IAEtC,OAAOtB,OAAA;EACR;EAAA;EAGD9D,eAAe2D,UAAA,EAAY;IACzB,MAAM6B,WAAA,GAAc,mBAAIjF,GAAA,CAAK;IAE7B,IAAI,cAAc1D,OAAA,CAAQ4E,OAAA,EAAS;MACjC,MAAMgE,aAAA,GAAgB5I,OAAA,CAAQ4E,OAAA,CAAQiE,QAAA;MAEtC,WAAW9D,MAAA,IAAU6D,aAAA,EAAe;QAClC,MAAME,QAAA,GAAW,KAAKC,aAAA,CAAcH,aAAA,CAAc7D,MAAM,GAAG+B,UAAU;QAErE,IAAIgC,QAAA,KAAa,MAAMH,WAAA,CAAYxE,GAAA,CAAIe,QAAA,CAASH,MAAM,GAAG+D,QAAQ;MAClE;IACF;IAED,OAAOH,WAAA;EACR;EAAA;EAAA;EAAA;EAKDI,cAAcC,YAAA,EAAclC,UAAA,EAAY;IACtC,MAAMvC,EAAA,GAAKyE,YAAA,CAAa/D,EAAA;IACxB,MAAMoC,IAAA,GAAO2B,YAAA,CAAa1B,QAAA;IAC1B,IAAIjB,IAAA,GAAO2C,YAAA,CAAaC,YAAA;IAGxB,IAAI,OAAO5C,IAAA,KAAS,UAAU;MAC5BA,IAAA,GAAOA,IAAA,CAAKuB,KAAA;IACb;IAGD,IAAI,CAAC3H,WAAA,CAAYiE,GAAA,CAAIK,EAAE,GAAG,OAAO;IAEjC,MAAM2E,UAAA,GAAa,KAAKC,eAAA,CAAgBH,YAAA,EAAclC,UAAA,EAAYvC,EAAE;IAEpE,IAAIuE,QAAA;IAEJ,QAAQzC,IAAA,CAAKD,WAAA,CAAa;MACxB,KAAK;QACH0C,QAAA,GAAW,IAAIM,iBAAA,CAAmB;QAClC;MACF,KAAK;QACHN,QAAA,GAAW,IAAIO,mBAAA,CAAqB;QACpC;MACF;QACE1H,OAAA,CAAQ4E,IAAA,CAAK,iFAAiFF,IAAI;QAClGyC,QAAA,GAAW,IAAIM,iBAAA,CAAmB;QAClC;IACH;IAEDN,QAAA,CAASQ,SAAA,CAAUJ,UAAU;IAC7BJ,QAAA,CAASzB,IAAA,GAAOA,IAAA;IAEhB,OAAOyB,QAAA;EACR;EAAA;EAAA;EAIDK,gBAAgBH,YAAA,EAAclC,UAAA,EAAYvC,EAAA,EAAI;IAC5C,MAAM2E,UAAA,GAAa,CAAE;IAErB,IAAIF,YAAA,CAAaO,UAAA,EAAY;MAC3BL,UAAA,CAAWM,SAAA,GAAYR,YAAA,CAAaO,UAAA,CAAW3B,KAAA;IAChD;IAED,IAAIoB,YAAA,CAAaS,OAAA,EAAS;MACxBP,UAAA,CAAWQ,KAAA,GAAQ,IAAIC,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAaS,OAAA,CAAQ7B,KAAK;IACzE,WACMoB,YAAA,CAAaa,YAAA,KACZb,YAAA,CAAaa,YAAA,CAAaxD,IAAA,KAAS,WAAW2C,YAAA,CAAaa,YAAA,CAAaxD,IAAA,KAAS,aAClF;MAEA6C,UAAA,CAAWQ,KAAA,GAAQ,IAAIC,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAaa,YAAA,CAAajC,KAAK;IACzE;IAED,IAAIoB,YAAA,CAAac,kBAAA,EAAoB;MACnCZ,UAAA,CAAWa,iBAAA,GAAoBf,YAAA,CAAac,kBAAA,CAAmBlC,KAAA;IAChE;IAED,IAAIoB,YAAA,CAAagB,QAAA,EAAU;MACzBd,UAAA,CAAWe,QAAA,GAAW,IAAIN,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAagB,QAAA,CAASpC,KAAK;IAC7E,WACMoB,YAAA,CAAakB,aAAA,KACZlB,YAAA,CAAakB,aAAA,CAAc7D,IAAA,KAAS,WAAW2C,YAAA,CAAakB,aAAA,CAAc7D,IAAA,KAAS,aACpF;MAEA6C,UAAA,CAAWe,QAAA,GAAW,IAAIN,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAakB,aAAA,CAActC,KAAK;IAC7E;IAED,IAAIoB,YAAA,CAAamB,cAAA,EAAgB;MAC/BjB,UAAA,CAAWkB,iBAAA,GAAoBC,UAAA,CAAWrB,YAAA,CAAamB,cAAA,CAAevC,KAAK;IAC5E;IAED,IAAIoB,YAAA,CAAasB,OAAA,EAAS;MACxBpB,UAAA,CAAWqB,OAAA,GAAUF,UAAA,CAAWrB,YAAA,CAAasB,OAAA,CAAQ1C,KAAK;IAC3D;IAED,IAAIsB,UAAA,CAAWqB,OAAA,GAAU,GAAK;MAC5BrB,UAAA,CAAWsB,WAAA,GAAc;IAC1B;IAED,IAAIxB,YAAA,CAAayB,gBAAA,EAAkB;MACjCvB,UAAA,CAAWwB,YAAA,GAAe1B,YAAA,CAAayB,gBAAA,CAAiB7C,KAAA;IACzD;IAED,IAAIoB,YAAA,CAAa2B,SAAA,EAAW;MAC1BzB,UAAA,CAAW0B,SAAA,GAAY5B,YAAA,CAAa2B,SAAA,CAAU/C,KAAA;IAC/C;IAED,IAAIoB,YAAA,CAAa6B,QAAA,EAAU;MACzB3B,UAAA,CAAW4B,QAAA,GAAW,IAAInB,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAa6B,QAAA,CAASjD,KAAK;IAC7E,WAAeoB,YAAA,CAAa+B,aAAA,IAAiB/B,YAAA,CAAa+B,aAAA,CAAc1E,IAAA,KAAS,SAAS;MAEpF6C,UAAA,CAAW4B,QAAA,GAAW,IAAInB,KAAA,CAAK,EAAGC,SAAA,CAAUZ,YAAA,CAAa+B,aAAA,CAAcnD,KAAK;IAC7E;IAED,MAAMhH,KAAA,GAAQ;IACdX,WAAA,CAAYuE,GAAA,CAAID,EAAE,EAAEF,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MACpD,MAAM3E,IAAA,GAAO2E,KAAA,CAAM/G,YAAA;MAEnB,QAAQoC,IAAA;QACN,KAAK;UACH6C,UAAA,CAAW+B,OAAA,GAAUrK,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAC1D;QAEF,KAAK;UACH2E,UAAA,CAAWiC,KAAA,GAAQvK,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UACxD;QAEF,KAAK;QACL,KAAK;UACH2E,UAAA,CAAWkC,GAAA,GAAMxK,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UACtD,IAAI2E,UAAA,CAAWkC,GAAA,KAAQ,QAAW;YAChC,IAAI,gBAAgBlC,UAAA,CAAWkC,GAAA,EAAKlC,UAAA,CAAWkC,GAAA,CAAIC,UAAA,GAAa,YAC3DnC,UAAA,CAAWkC,GAAA,CAAIE,QAAA,GAAW;UAChC;UAED;QAEF,KAAK;UACHpC,UAAA,CAAWqC,eAAA,GAAkB3K,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAClE;QAEF,KAAK;UACH2E,UAAA,CAAWsC,WAAA,GAAc5K,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAC9D,IAAI2E,UAAA,CAAWsC,WAAA,KAAgB,QAAW;YACxC,IAAI,gBAAgBtC,UAAA,CAAWsC,WAAA,EAAatC,UAAA,CAAWsC,WAAA,CAAYH,UAAA,GAAa,YAC3EnC,UAAA,CAAWsC,WAAA,CAAYF,QAAA,GAAW;UACxC;UAED;QAEF,KAAK;QACL,KAAK;UACHpC,UAAA,CAAWuC,SAAA,GAAY7K,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAC5D;QAEF,KAAK;UACH2E,UAAA,CAAWwC,MAAA,GAAS9K,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UACzD,IAAI2E,UAAA,CAAWwC,MAAA,KAAW,QAAW;YACnCxC,UAAA,CAAWwC,MAAA,CAAOC,OAAA,GAAUC,gCAAA;YAE5B,IAAI,gBAAgB1C,UAAA,CAAWwC,MAAA,EAAQxC,UAAA,CAAWwC,MAAA,CAAOL,UAAA,GAAa,YACjEnC,UAAA,CAAWwC,MAAA,CAAOJ,QAAA,GAAW;UACnC;UAED;QAEF,KAAK;UACHpC,UAAA,CAAW2C,WAAA,GAAcjL,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAC9D,IAAI2E,UAAA,CAAW2C,WAAA,KAAgB,QAAW;YACxC,IAAI,gBAAgB3C,UAAA,CAAW2C,WAAA,EAAa3C,UAAA,CAAW2C,WAAA,CAAYR,UAAA,GAAa,YAC3EnC,UAAA,CAAW2C,WAAA,CAAYP,QAAA,GAAW;UACxC;UAED;QAEF,KAAK;QACL,KAAK;UACHpC,UAAA,CAAW4C,QAAA,GAAWlL,KAAA,CAAMsK,UAAA,CAAWpE,UAAA,EAAYkE,KAAA,CAAMzG,EAAE;UAC3D2E,UAAA,CAAWsB,WAAA,GAAc;UACzB;QAEF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL;UACE7I,OAAA,CAAQ4E,IAAA,CAAK,2EAA2EF,IAAI;UAC5F;MACH;IACP,CAAK;IAED,OAAO6C,UAAA;EACR;EAAA;EAGDgC,WAAWpE,UAAA,EAAY7B,EAAA,EAAI;IAEzB,IAAI,oBAAoBjF,OAAA,CAAQ4E,OAAA,IAAWK,EAAA,IAAMjF,OAAA,CAAQ4E,OAAA,CAAQmH,cAAA,EAAgB;MAC/EpK,OAAA,CAAQ4E,IAAA,CAAK,kGAAkG;MAC/GtB,EAAA,GAAKhF,WAAA,CAAYuE,GAAA,CAAIS,EAAE,EAAEZ,QAAA,CAAS,CAAC,EAAEE,EAAA;IACtC;IAED,OAAOuC,UAAA,CAAWtC,GAAA,CAAIS,EAAE;EACzB;EAAA;EAAA;EAAA;EAKD5B,eAAA,EAAiB;IACf,MAAM2I,SAAA,GAAY,CAAE;IACpB,MAAMC,YAAA,GAAe,CAAE;IAEvB,IAAI,cAAcjM,OAAA,CAAQ4E,OAAA,EAAS;MACjC,MAAMsH,aAAA,GAAgBlM,OAAA,CAAQ4E,OAAA,CAAQuH,QAAA;MAEtC,WAAWpH,MAAA,IAAUmH,aAAA,EAAe;QAClC,MAAME,YAAA,GAAeF,aAAA,CAAcnH,MAAM;QAEzC,MAAMsH,aAAA,GAAgBpM,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAASH,MAAM,CAAC;QAEtD,IAAIqH,YAAA,CAAaE,QAAA,KAAa,QAAQ;UACpC,MAAMC,QAAA,GAAW,KAAKC,aAAA,CAAcH,aAAA,EAAeH,aAAa;UAChEK,QAAA,CAAShI,EAAA,GAAKQ,MAAA;UAEd,IAAIsH,aAAA,CAAcjI,OAAA,CAAQoE,MAAA,GAAS,GAAG;YACpC7G,OAAA,CAAQ4E,IAAA,CAAK,gFAAgF;UAC9F;UACDgG,QAAA,CAASE,UAAA,GAAaJ,aAAA,CAAcjI,OAAA,CAAQ,CAAC,EAAEG,EAAA;UAE/CyH,SAAA,CAAUjH,MAAM,IAAIwH,QAAA;QAC9B,WAAmBH,YAAA,CAAaE,QAAA,KAAa,cAAc;UACjD,MAAMI,WAAA,GAAc;YAClBzH,EAAA,EAAIF;UACL;UAED2H,WAAA,CAAYC,UAAA,GAAa,KAAKC,iBAAA,CAAkBP,aAAA,EAAeH,aAAa;UAC5EQ,WAAA,CAAYzH,EAAA,GAAKF,MAAA;UAEjB,IAAIsH,aAAA,CAAcjI,OAAA,CAAQoE,MAAA,GAAS,GAAG;YACpC7G,OAAA,CAAQ4E,IAAA,CAAK,oFAAoF;UAClG;UAED0F,YAAA,CAAalH,MAAM,IAAI2H,WAAA;QACxB;MACF;IACF;IAED,OAAO;MACLV,SAAA;MACAC;IACD;EACF;EAAA;EAAA;EAAA;EAKDO,cAAcH,aAAA,EAAeQ,aAAA,EAAe;IAC1C,MAAMC,QAAA,GAAW,EAAE;IAEnBT,aAAA,CAAchI,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MAC9C,MAAM+B,QAAA,GAAWF,aAAA,CAAc7B,KAAA,CAAMzG,EAAE;MAEvC,IAAIwI,QAAA,CAAST,QAAA,KAAa,WAAW;MAErC,MAAMU,OAAA,GAAU;QACdzI,EAAA,EAAIyG,KAAA,CAAMzG,EAAA;QACV0I,OAAA,EAAS,EAAE;QACXC,OAAA,EAAS,EAAE;QACXC,aAAA,EAAe,IAAIC,OAAA,CAAS,EAACxD,SAAA,CAAUmD,QAAA,CAASM,aAAA,CAAcC,CAAC;QAAA;QAAA;MAGhE;MAED,IAAI,aAAaP,QAAA,EAAU;QACzBC,OAAA,CAAQC,OAAA,GAAUF,QAAA,CAASQ,OAAA,CAAQD,CAAA;QACnCN,OAAA,CAAQE,OAAA,GAAUH,QAAA,CAASS,OAAA,CAAQF,CAAA;MACpC;MAEDR,QAAA,CAASrI,IAAA,CAAKuI,OAAO;IAC3B,CAAK;IAED,OAAO;MACLF,QAAA;MACAW,KAAA,EAAO;IACR;EACF;EAAA;EAGDb,kBAAkBP,aAAA,EAAeQ,aAAA,EAAe;IAC9C,MAAMa,eAAA,GAAkB,EAAE;IAE1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAItB,aAAA,CAAchI,QAAA,CAASmE,MAAA,EAAQmF,CAAA,IAAK;MACtD,MAAM3C,KAAA,GAAQqB,aAAA,CAAchI,QAAA,CAASsJ,CAAC;MAEtC,MAAMC,eAAA,GAAkBf,aAAA,CAAc7B,KAAA,CAAMzG,EAAE;MAE9C,MAAMsJ,cAAA,GAAiB;QACrBxG,IAAA,EAAMuG,eAAA,CAAgBtG,QAAA;QACtBwG,aAAA,EAAeF,eAAA,CAAgBG,aAAA;QAC/B9I,EAAA,EAAI2I,eAAA,CAAgB3I,EAAA;QACpB+I,WAAA,EAAaJ,eAAA,CAAgBK,WAAA,CAAYX;MAC1C;MAED,IAAIM,eAAA,CAAgBtB,QAAA,KAAa,qBAAqB;MAEtDuB,cAAA,CAAeK,KAAA,GAAQjO,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAAS8F,KAAA,CAAMzG,EAAE,CAAC,EAAEF,QAAA,CAAS8J,MAAA,CAAO,UAAUC,MAAA,EAAO;QAC1F,OAAOA,MAAA,CAAMnK,YAAA,KAAiB;MACtC,CAAO,EAAE,CAAC,EAAEM,EAAA;MAENmJ,eAAA,CAAgBjJ,IAAA,CAAKoJ,cAAc;IACpC;IAED,OAAOH,eAAA;EACR;EAAA;EAGDlK,WAAWJ,SAAA,EAAWE,WAAA,EAAaqF,WAAA,EAAa;IAC9CzI,UAAA,GAAa,IAAImO,KAAA,CAAO;IAExB,MAAMC,QAAA,GAAW,KAAKC,WAAA,CAAYnL,SAAA,CAAU4I,SAAA,EAAW1I,WAAA,EAAaqF,WAAW;IAE/E,MAAM6F,UAAA,GAAaxO,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA;IAEnC,MAAM7N,KAAA,GAAQ;IACd0N,QAAA,CAASzK,OAAA,CAAQ,UAAU6K,KAAA,EAAO;MAChC,MAAMC,SAAA,GAAYH,UAAA,CAAWE,KAAA,CAAMnK,EAAE;MACrC3D,KAAA,CAAMgO,mBAAA,CAAoBF,KAAA,EAAOC,SAAS;MAE1C,MAAME,iBAAA,GAAoB5O,WAAA,CAAYuE,GAAA,CAAIkK,KAAA,CAAMnK,EAAE,EAAEH,OAAA;MAEpDyK,iBAAA,CAAkBhL,OAAA,CAAQ,UAAUiL,UAAA,EAAY;QAC9C,MAAMC,MAAA,GAAST,QAAA,CAAS9J,GAAA,CAAIsK,UAAA,CAAWvK,EAAE;QACzC,IAAIwK,MAAA,KAAW,QAAWA,MAAA,CAAOC,GAAA,CAAIN,KAAK;MAClD,CAAO;MAED,IAAIA,KAAA,CAAMK,MAAA,KAAW,MAAM;QACzB7O,UAAA,CAAW8O,GAAA,CAAIN,KAAK;MACrB;IACP,CAAK;IAED,KAAKO,YAAA,CAAa7L,SAAA,CAAU4I,SAAA,EAAW1I,WAAA,EAAagL,QAAQ;IAE5D,KAAKY,kBAAA,CAAoB;IAEzBhP,UAAA,CAAWiP,QAAA,CAAS,UAAUC,IAAA,EAAM;MAClC,IAAIA,IAAA,CAAKC,QAAA,CAASC,aAAA,EAAe;QAC/B,IAAIF,IAAA,CAAKL,MAAA,EAAQ;UACfK,IAAA,CAAKC,QAAA,CAASC,aAAA,CAAcC,YAAA,GAAeH,IAAA,CAAKL,MAAA,CAAOS,MAAA;UACvDJ,IAAA,CAAKC,QAAA,CAASC,aAAA,CAAcG,iBAAA,GAAoBL,IAAA,CAAKL,MAAA,CAAOW,WAAA;QAC7D;QAED,MAAMC,SAAA,GAAYC,iBAAA,CAAkBR,IAAA,CAAKC,QAAA,CAASC,aAAa;QAE/DF,IAAA,CAAKS,YAAA,CAAaF,SAAS;QAC3BP,IAAA,CAAKU,iBAAA,CAAmB;MACzB;IACP,CAAK;IAED,MAAMC,UAAA,GAAa,IAAIC,eAAA,CAAiB,EAACvO,KAAA,CAAO;IAGhD,IAAIvB,UAAA,CAAWmE,QAAA,CAASmE,MAAA,KAAW,KAAKtI,UAAA,CAAWmE,QAAA,CAAS,CAAC,EAAE4L,OAAA,EAAS;MACtE/P,UAAA,CAAWmE,QAAA,CAAS,CAAC,EAAE0L,UAAA,GAAaA,UAAA;MACpC7P,UAAA,GAAaA,UAAA,CAAWmE,QAAA,CAAS,CAAC;IACnC;IAEDnE,UAAA,CAAW6P,UAAA,GAAaA,UAAA;EACzB;EAAA;EAGDxB,YAAYvC,SAAA,EAAW1I,WAAA,EAAaqF,WAAA,EAAa;IAC/C,MAAM2F,QAAA,GAAW,mBAAI5K,GAAA,CAAK;IAC1B,MAAM8K,UAAA,GAAaxO,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA;IAEnC,WAAW1J,MAAA,IAAUyJ,UAAA,EAAY;MAC/B,MAAMvJ,EAAA,GAAKC,QAAA,CAASH,MAAM;MAC1B,MAAMqK,IAAA,GAAOZ,UAAA,CAAWzJ,MAAM;MAC9B,MAAMsH,aAAA,GAAgBpM,WAAA,CAAYuE,GAAA,CAAIS,EAAE;MAExC,IAAIyJ,KAAA,GAAQ,KAAKwB,aAAA,CAAc7D,aAAA,EAAeL,SAAA,EAAW/G,EAAA,EAAImK,IAAA,CAAK9H,QAAQ;MAE1E,IAAI,CAACoH,KAAA,EAAO;QACV,QAAQU,IAAA,CAAK9C,QAAA;UACX,KAAK;YACHoC,KAAA,GAAQ,KAAKyB,YAAA,CAAa9D,aAAa;YACvC;UACF,KAAK;YACHqC,KAAA,GAAQ,KAAK0B,WAAA,CAAY/D,aAAa;YACtC;UACF,KAAK;YACHqC,KAAA,GAAQ,KAAK2B,UAAA,CAAWhE,aAAA,EAAe/I,WAAA,EAAaqF,WAAW;YAC/D;UACF,KAAK;YACH+F,KAAA,GAAQ,KAAK4B,WAAA,CAAYjE,aAAA,EAAe/I,WAAW;YACnD;UACF,KAAK;UACL,KAAK;YACHoL,KAAA,GAAQ,IAAI6B,IAAA,CAAM;YAClB;UACF,KAAK;UACL;YACE7B,KAAA,GAAQ,IAAIL,KAAA,CAAO;YACnB;QACH;QAEDK,KAAA,CAAMrH,IAAA,GAAO+H,IAAA,CAAK9H,QAAA,GAAWkJ,eAAA,CAAgBC,gBAAA,CAAiBrB,IAAA,CAAK9H,QAAQ,IAAI;QAE/EoH,KAAA,CAAMnK,EAAA,GAAKU,EAAA;MACZ;MAED,KAAKyL,gBAAA,CAAiBhC,KAAA,EAAOU,IAAI;MACjCd,QAAA,CAASnK,GAAA,CAAIc,EAAA,EAAIyJ,KAAK;IACvB;IAED,OAAOJ,QAAA;EACR;EAED4B,cAAc7D,aAAA,EAAeL,SAAA,EAAW/G,EAAA,EAAIoC,IAAA,EAAM;IAChD,IAAIsJ,IAAA,GAAO;IAEXtE,aAAA,CAAcjI,OAAA,CAAQP,OAAA,CAAQ,UAAUkL,MAAA,EAAQ;MAC9C,WAAWxK,EAAA,IAAMyH,SAAA,EAAW;QAC1B,MAAMO,QAAA,GAAWP,SAAA,CAAUzH,EAAE;QAE7BgI,QAAA,CAASO,QAAA,CAASjJ,OAAA,CAAQ,UAAUmJ,OAAA,EAASW,CAAA,EAAG;UAC9C,IAAIX,OAAA,CAAQzI,EAAA,KAAOwK,MAAA,CAAOxK,EAAA,EAAI;YAC5B,MAAMqM,OAAA,GAAUD,IAAA;YAChBA,IAAA,GAAO,IAAIJ,IAAA,CAAM;YAEjBI,IAAA,CAAKjB,WAAA,CAAYmB,IAAA,CAAK7D,OAAA,CAAQG,aAAa;YAI3CwD,IAAA,CAAKtJ,IAAA,GAAOA,IAAA,GAAOmJ,eAAA,CAAgBC,gBAAA,CAAiBpJ,IAAI,IAAI;YAC5DsJ,IAAA,CAAKpM,EAAA,GAAKU,EAAA;YAEVsH,QAAA,CAASkB,KAAA,CAAME,CAAC,IAAIgD,IAAA;YAIpB,IAAIC,OAAA,KAAY,MAAM;cACpBD,IAAA,CAAK3B,GAAA,CAAI4B,OAAO;YACjB;UACF;QACX,CAAS;MACF;IACP,CAAK;IAED,OAAOD,IAAA;EACR;EAAA;EAGDR,aAAa9D,aAAA,EAAe;IAC1B,IAAIqC,KAAA;IACJ,IAAIoC,eAAA;IAEJzE,aAAA,CAAchI,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MAC9C,MAAM+F,IAAA,GAAO/Q,OAAA,CAAQ4E,OAAA,CAAQoM,aAAA,CAAchG,KAAA,CAAMzG,EAAE;MAEnD,IAAIwM,IAAA,KAAS,QAAW;QACtBD,eAAA,GAAkBC,IAAA;MACnB;IACP,CAAK;IAED,IAAID,eAAA,KAAoB,QAAW;MACjCpC,KAAA,GAAQ,IAAIuC,QAAA,CAAU;IAC5B,OAAW;MACL,IAAI5K,IAAA,GAAO;MACX,IAAIyK,eAAA,CAAgBI,oBAAA,KAAyB,UAAaJ,eAAA,CAAgBI,oBAAA,CAAqBtJ,KAAA,KAAU,GAAG;QAC1GvB,IAAA,GAAO;MACR;MAED,IAAI8K,iBAAA,GAAoB;MACxB,IAAIL,eAAA,CAAgBM,SAAA,KAAc,QAAW;QAC3CD,iBAAA,GAAoBL,eAAA,CAAgBM,SAAA,CAAUxJ,KAAA,GAAQ;MACvD;MAED,IAAIyJ,gBAAA,GAAmB;MACvB,IAAIP,eAAA,CAAgBQ,QAAA,KAAa,QAAW;QAC1CD,gBAAA,GAAmBP,eAAA,CAAgBQ,QAAA,CAAS1J,KAAA,GAAQ;MACrD;MAED,IAAI2J,KAAA,GAAQ7K,MAAA,CAAO8K,UAAA;MACnB,IAAIC,MAAA,GAAS/K,MAAA,CAAOgL,WAAA;MAEpB,IAAIZ,eAAA,CAAgBa,WAAA,KAAgB,UAAab,eAAA,CAAgBc,YAAA,KAAiB,QAAW;QAC3FL,KAAA,GAAQT,eAAA,CAAgBa,WAAA,CAAY/J,KAAA;QACpC6J,MAAA,GAASX,eAAA,CAAgBc,YAAA,CAAahK,KAAA;MACvC;MAED,MAAMiK,MAAA,GAASN,KAAA,GAAQE,MAAA;MAEvB,IAAIK,GAAA,GAAM;MACV,IAAIhB,eAAA,CAAgBiB,WAAA,KAAgB,QAAW;QAC7CD,GAAA,GAAMhB,eAAA,CAAgBiB,WAAA,CAAYnK,KAAA;MACnC;MAED,MAAMoK,WAAA,GAAclB,eAAA,CAAgBmB,WAAA,GAAcnB,eAAA,CAAgBmB,WAAA,CAAYrK,KAAA,GAAQ;MAEtF,QAAQvB,IAAA;QACN,KAAK;UACHqI,KAAA,GAAQ,IAAIwD,iBAAA,CAAkBJ,GAAA,EAAKD,MAAA,EAAQV,iBAAA,EAAmBE,gBAAgB;UAC9E,IAAIW,WAAA,KAAgB,MAAMtD,KAAA,CAAMyD,cAAA,CAAeH,WAAW;UAC1D;QAEF,KAAK;UACHtD,KAAA,GAAQ,IAAI0D,kBAAA,CACV,CAACb,KAAA,GAAQ,GACTA,KAAA,GAAQ,GACRE,MAAA,GAAS,GACT,CAACA,MAAA,GAAS,GACVN,iBAAA,EACAE,gBACD;UACD;QAEF;UACE1P,OAAA,CAAQ4E,IAAA,CAAK,0CAA0CF,IAAA,GAAO,GAAG;UACjEqI,KAAA,GAAQ,IAAIuC,QAAA,CAAU;UACtB;MACH;IACF;IAED,OAAOvC,KAAA;EACR;EAAA;EAGD0B,YAAY/D,aAAA,EAAe;IACzB,IAAIqC,KAAA;IACJ,IAAI2D,cAAA;IAEJhG,aAAA,CAAchI,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MAC9C,MAAM+F,IAAA,GAAO/Q,OAAA,CAAQ4E,OAAA,CAAQoM,aAAA,CAAchG,KAAA,CAAMzG,EAAE;MAEnD,IAAIwM,IAAA,KAAS,QAAW;QACtBsB,cAAA,GAAiBtB,IAAA;MAClB;IACP,CAAK;IAED,IAAIsB,cAAA,KAAmB,QAAW;MAChC3D,KAAA,GAAQ,IAAIuC,QAAA,CAAU;IAC5B,OAAW;MACL,IAAI5K,IAAA;MAGJ,IAAIgM,cAAA,CAAeC,SAAA,KAAc,QAAW;QAC1CjM,IAAA,GAAO;MACf,OAAa;QACLA,IAAA,GAAOgM,cAAA,CAAeC,SAAA,CAAU1K,KAAA;MACjC;MAED,IAAI8B,KAAA,GAAQ;MAEZ,IAAI2I,cAAA,CAAe1I,KAAA,KAAU,QAAW;QACtCD,KAAA,GAAQ,IAAIC,KAAA,CAAO,EAACC,SAAA,CAAUyI,cAAA,CAAe1I,KAAA,CAAM/B,KAAK;MACzD;MAED,IAAI2K,SAAA,GAAYF,cAAA,CAAeG,SAAA,KAAc,SAAY,IAAIH,cAAA,CAAeG,SAAA,CAAU5K,KAAA,GAAQ;MAG9F,IAAIyK,cAAA,CAAeI,iBAAA,KAAsB,UAAaJ,cAAA,CAAeI,iBAAA,CAAkB7K,KAAA,KAAU,GAAG;QAClG2K,SAAA,GAAY;MACb;MAED,IAAIG,QAAA,GAAW;MACf,IAAIL,cAAA,CAAeM,iBAAA,KAAsB,QAAW;QAClD,IAAIN,cAAA,CAAeO,oBAAA,KAAyB,UAAaP,cAAA,CAAeO,oBAAA,CAAqBhL,KAAA,KAAU,GAAG;UACxG8K,QAAA,GAAW;QACrB,OAAe;UACLA,QAAA,GAAWL,cAAA,CAAeM,iBAAA,CAAkB/K,KAAA;QAC7C;MACF;MAGD,MAAMiL,KAAA,GAAQ;MAEd,QAAQxM,IAAA;QACN,KAAK;UACHqI,KAAA,GAAQ,IAAIoE,UAAA,CAAWpJ,KAAA,EAAO6I,SAAA,EAAWG,QAAA,EAAUG,KAAK;UACxD;QAEF,KAAK;UACHnE,KAAA,GAAQ,IAAIqE,gBAAA,CAAiBrJ,KAAA,EAAO6I,SAAS;UAC7C;QAEF,KAAK;UACH,IAAIS,KAAA,GAAQC,IAAA,CAAKC,EAAA,GAAK;UAEtB,IAAIb,cAAA,CAAec,UAAA,KAAe,QAAW;YAC3CH,KAAA,GAAQI,SAAA,CAAUC,QAAA,CAAShB,cAAA,CAAec,UAAA,CAAWvL,KAAK;UAC3D;UAED,IAAI0L,QAAA,GAAW;UACf,IAAIjB,cAAA,CAAekB,UAAA,KAAe,QAAW;YAI3CD,QAAA,GAAWF,SAAA,CAAUC,QAAA,CAAShB,cAAA,CAAekB,UAAA,CAAW3L,KAAK;YAC7D0L,QAAA,GAAWL,IAAA,CAAKO,GAAA,CAAIF,QAAA,EAAU,CAAC;UAChC;UAED5E,KAAA,GAAQ,IAAI+E,SAAA,CAAU/J,KAAA,EAAO6I,SAAA,EAAWG,QAAA,EAAUM,KAAA,EAAOM,QAAA,EAAUT,KAAK;UACxE;QAEF;UACElR,OAAA,CAAQ4E,IAAA,CACN,yCAAyC8L,cAAA,CAAeC,SAAA,CAAU1K,KAAA,GAAQ,+BAC3E;UACD8G,KAAA,GAAQ,IAAIoE,UAAA,CAAWpJ,KAAA,EAAO6I,SAAS;UACvC;MACH;MAED,IAAIF,cAAA,CAAeqB,WAAA,KAAgB,UAAarB,cAAA,CAAeqB,WAAA,CAAY9L,KAAA,KAAU,GAAG;QACtF8G,KAAA,CAAMiF,UAAA,GAAa;MACpB;IACF;IAED,OAAOjF,KAAA;EACR;EAED2B,WAAWhE,aAAA,EAAe/I,WAAA,EAAaqF,WAAA,EAAa;IAClD,IAAI+F,KAAA;IACJ,IAAIkF,QAAA,GAAW;IACf,IAAI9K,QAAA,GAAW;IACf,MAAM5F,SAAA,GAAY,EAAE;IAGpBmJ,aAAA,CAAchI,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MAC9C,IAAI1H,WAAA,CAAYY,GAAA,CAAI8G,KAAA,CAAMzG,EAAE,GAAG;QAC7BqP,QAAA,GAAWtQ,WAAA,CAAYkB,GAAA,CAAIwG,KAAA,CAAMzG,EAAE;MACpC;MAED,IAAIoE,WAAA,CAAYzE,GAAA,CAAI8G,KAAA,CAAMzG,EAAE,GAAG;QAC7BrB,SAAA,CAAUuB,IAAA,CAAKkE,WAAA,CAAYnE,GAAA,CAAIwG,KAAA,CAAMzG,EAAE,CAAC;MACzC;IACP,CAAK;IAED,IAAIrB,SAAA,CAAUsF,MAAA,GAAS,GAAG;MACxBM,QAAA,GAAW5F,SAAA;IACjB,WAAeA,SAAA,CAAUsF,MAAA,GAAS,GAAG;MAC/BM,QAAA,GAAW5F,SAAA,CAAU,CAAC;IAC5B,OAAW;MACL4F,QAAA,GAAW,IAAIM,iBAAA,CAAkB;QAAEM,KAAA,EAAO;MAAQ,CAAE;MACpDxG,SAAA,CAAUuB,IAAA,CAAKqE,QAAQ;IACxB;IAED,IAAI,WAAW8K,QAAA,CAASC,UAAA,EAAY;MAClC3Q,SAAA,CAAUW,OAAA,CAAQ,UAAUiQ,SAAA,EAAU;QACpCA,SAAA,CAASC,YAAA,GAAe;MAChC,CAAO;IACF;IAED,IAAIH,QAAA,CAASI,YAAA,EAAc;MACzBtF,KAAA,GAAQ,IAAIuF,WAAA,CAAYL,QAAA,EAAU9K,QAAQ;MAC1C4F,KAAA,CAAMwF,oBAAA,CAAsB;IAClC,OAAW;MACLxF,KAAA,GAAQ,IAAIyF,IAAA,CAAKP,QAAA,EAAU9K,QAAQ;IACpC;IAED,OAAO4F,KAAA;EACR;EAED4B,YAAYjE,aAAA,EAAe/I,WAAA,EAAa;IACtC,MAAMsQ,QAAA,GAAWvH,aAAA,CAAchI,QAAA,CAAS+P,MAAA,CAAO,UAAUC,GAAA,EAAKrJ,KAAA,EAAO;MACnE,IAAI1H,WAAA,CAAYY,GAAA,CAAI8G,KAAA,CAAMzG,EAAE,GAAG8P,GAAA,GAAM/Q,WAAA,CAAYkB,GAAA,CAAIwG,KAAA,CAAMzG,EAAE;MAE7D,OAAO8P,GAAA;IACR,GAAE,IAAI;IAGP,MAAMvL,QAAA,GAAW,IAAIwL,iBAAA,CAAkB;MAAE5K,KAAA,EAAO;MAAU6K,SAAA,EAAW;IAAA,CAAG;IACxE,OAAO,IAAIC,IAAA,CAAKZ,QAAA,EAAU9K,QAAQ;EACnC;EAAA;EAGD4H,iBAAiBhC,KAAA,EAAOC,SAAA,EAAW;IACjC,MAAMW,aAAA,GAAgB,CAAE;IAExB,IAAI,iBAAiBX,SAAA,EAAWW,aAAA,CAAcmF,WAAA,GAAcvP,QAAA,CAASyJ,SAAA,CAAU+F,WAAA,CAAY9M,KAAK;IAEhG,IAAI,mBAAmB+G,SAAA,EAAWW,aAAA,CAAcqF,UAAA,GAAaC,aAAA,CAAcjG,SAAA,CAAUkG,aAAA,CAAcjN,KAAK,OACnG0H,aAAA,CAAcqF,UAAA,GAAa;IAEhC,IAAI,qBAAqBhG,SAAA,EAAWW,aAAA,CAAcwF,WAAA,GAAcnG,SAAA,CAAUoG,eAAA,CAAgBnN,KAAA;IAE1F,IAAI,iBAAiB+G,SAAA,EAAWW,aAAA,CAAc0F,WAAA,GAAcrG,SAAA,CAAUsG,WAAA,CAAYrN,KAAA;IAClF,IAAI,kBAAkB+G,SAAA,EAAWW,aAAA,CAAc4F,QAAA,GAAWvG,SAAA,CAAUwG,YAAA,CAAavN,KAAA;IACjF,IAAI,kBAAkB+G,SAAA,EAAWW,aAAA,CAAc8F,YAAA,GAAezG,SAAA,CAAU0G,YAAA,CAAazN,KAAA;IAErF,IAAI,iBAAiB+G,SAAA,EAAWW,aAAA,CAAcgG,KAAA,GAAQ3G,SAAA,CAAU4G,WAAA,CAAY3N,KAAA;IAE5E,IAAI,mBAAmB+G,SAAA,EAAWW,aAAA,CAAckG,aAAA,GAAgB7G,SAAA,CAAU8G,aAAA,CAAc7N,KAAA;IACxF,IAAI,kBAAkB+G,SAAA,EAAWW,aAAA,CAAcoG,YAAA,GAAe/G,SAAA,CAAUgH,YAAA,CAAa/N,KAAA;IAErF,IAAI,oBAAoB+G,SAAA,EAAWW,aAAA,CAAcsG,cAAA,GAAiBjH,SAAA,CAAUkH,cAAA,CAAejO,KAAA;IAC3F,IAAI,mBAAmB+G,SAAA,EAAWW,aAAA,CAAcwG,aAAA,GAAgBnH,SAAA,CAAUoH,aAAA,CAAcnO,KAAA;IAExF8G,KAAA,CAAMW,QAAA,CAASC,aAAA,GAAgBA,aAAA;EAChC;EAEDV,oBAAoBF,KAAA,EAAOC,SAAA,EAAW;IACpC,IAAI,oBAAoBA,SAAA,EAAW;MACjC,MAAMtK,QAAA,GAAWpE,WAAA,CAAYuE,GAAA,CAAIkK,KAAA,CAAMnK,EAAE,EAAEF,QAAA;MAE3CA,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;QAChC,IAAIA,KAAA,CAAM/G,YAAA,KAAiB,kBAAkB;UAC3C,MAAM+R,YAAA,GAAehW,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA,CAAMzD,KAAA,CAAMzG,EAAE;UAEnD,IAAI,qBAAqByR,YAAA,EAAc;YACrC,MAAMC,GAAA,GAAMD,YAAA,CAAajB,eAAA,CAAgBnN,KAAA;YAGzC,IAAI8G,KAAA,CAAMwH,MAAA,KAAW,QAAW;cAC9BxH,KAAA,CAAMwH,MAAA,CAAOC,QAAA,CAASvM,SAAA,CAAUqM,GAAG;cACnC/V,UAAA,CAAW8O,GAAA,CAAIN,KAAA,CAAMwH,MAAM;YACzC,OAAmB;cAGLxH,KAAA,CAAM0H,MAAA,CAAO,IAAIC,OAAA,CAAS,EAACzM,SAAA,CAAUqM,GAAG,CAAC;YAC1C;UACF;QACF;MACT,CAAO;IACF;EACF;EAEDhH,aAAajD,SAAA,EAAW1I,WAAA,EAAagL,QAAA,EAAU;IAC7C,MAAMgI,YAAA,GAAe,KAAKC,cAAA,CAAgB;IAE1C,WAAWhS,EAAA,IAAMyH,SAAA,EAAW;MAC1B,MAAMO,QAAA,GAAWP,SAAA,CAAUzH,EAAE;MAE7B,MAAMH,OAAA,GAAUnE,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAASqH,QAAA,CAAShI,EAAE,CAAC,EAAEH,OAAA;MAEvDA,OAAA,CAAQP,OAAA,CAAQ,UAAUkL,MAAA,EAAQ;QAChC,IAAIzL,WAAA,CAAYY,GAAA,CAAI6K,MAAA,CAAOxK,EAAE,GAAG;UAC9B,MAAM2J,KAAA,GAAQa,MAAA,CAAOxK,EAAA;UACrB,MAAMiS,gBAAA,GAAmBvW,WAAA,CAAYuE,GAAA,CAAI0J,KAAK;UAE9CsI,gBAAA,CAAiBpS,OAAA,CAAQP,OAAA,CAAQ,UAAU4S,aAAA,EAAe;YACxD,IAAInI,QAAA,CAASpK,GAAA,CAAIuS,aAAA,CAAclS,EAAE,GAAG;cAClC,MAAMmK,KAAA,GAAQJ,QAAA,CAAS9J,GAAA,CAAIiS,aAAA,CAAclS,EAAE;cAE3CmK,KAAA,CAAMgI,IAAA,CAAK,IAAIC,QAAA,CAASpK,QAAA,CAASkB,KAAK,GAAG6I,YAAA,CAAaG,aAAA,CAAclS,EAAE,CAAC;YACxE;UACb,CAAW;QACF;MACT,CAAO;IACF;EACF;EAEDgS,eAAA,EAAiB;IACf,MAAMD,YAAA,GAAe,CAAE;IAEvB,IAAI,UAAUtW,OAAA,CAAQ4E,OAAA,EAAS;MAC7B,MAAMgS,YAAA,GAAe5W,OAAA,CAAQ4E,OAAA,CAAQiS,IAAA;MAErC,WAAW9R,MAAA,IAAU6R,YAAA,EAAc;QACjC,IAAIA,YAAA,CAAa7R,MAAM,EAAEuH,QAAA,KAAa,cAAcsK,YAAA,CAAa7R,MAAM,EAAE+R,WAAA,GAAc,GAAG;UACxF,MAAMC,SAAA,GAAYH,YAAA,CAAa7R,MAAM,EAAEiS,QAAA;UAEvC,IAAIC,KAAA,CAAMC,OAAA,CAAQH,SAAS,GAAG;YAC5BA,SAAA,CAAUlT,OAAA,CAAQ,UAAUsT,QAAA,EAAU;cACpCb,YAAA,CAAaa,QAAA,CAASC,IAAI,IAAI,IAAIhK,OAAA,CAAS,EAACxD,SAAA,CAAUuN,QAAA,CAASE,MAAA,CAAO/J,CAAC;YACrF,CAAa;UACb,OAAiB;YACLgJ,YAAA,CAAaS,SAAA,CAAUK,IAAI,IAAI,IAAIhK,OAAA,CAAS,EAACxD,SAAA,CAAUmN,SAAA,CAAUM,MAAA,CAAO/J,CAAC;UAC1E;QACF;MACF;IACF;IAED,OAAOgJ,YAAA;EACR;EAAA;EAGDpH,mBAAA,EAAqB;IACnB,IAAI,oBAAoBlP,OAAA,IAAW,kBAAkBA,OAAA,CAAQsX,cAAA,EAAgB;MAC3E,MAAMC,YAAA,GAAevX,OAAA,CAAQsX,cAAA,CAAeE,YAAA,CAAa5P,KAAA;MACzD,MAAM6P,CAAA,GAAIF,YAAA,CAAa,CAAC;MACxB,MAAMG,CAAA,GAAIH,YAAA,CAAa,CAAC;MACxB,MAAMI,CAAA,GAAIJ,YAAA,CAAa,CAAC;MAExB,IAAIE,CAAA,KAAM,KAAKC,CAAA,KAAM,KAAKC,CAAA,KAAM,GAAG;QACjC,MAAMjO,KAAA,GAAQ,IAAIC,KAAA,CAAM8N,CAAA,EAAGC,CAAA,EAAGC,CAAC;QAC/BzX,UAAA,CAAW8O,GAAA,CAAI,IAAI4I,YAAA,CAAalO,KAAA,EAAO,CAAC,CAAC;MAC1C;IACF;EACF;AACH;AAGA,MAAMnG,cAAA,CAAe;EAAA;EAEnB9B,MAAM2B,SAAA,EAAW;IACf,MAAME,WAAA,GAAc,mBAAII,GAAA,CAAK;IAE7B,IAAI,cAAc1D,OAAA,CAAQ4E,OAAA,EAAS;MACjC,MAAMiT,QAAA,GAAW7X,OAAA,CAAQ4E,OAAA,CAAQkT,QAAA;MAEjC,WAAW/S,MAAA,IAAU8S,QAAA,EAAU;QAC7B,MAAMxL,aAAA,GAAgBpM,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAASH,MAAM,CAAC;QACtD,MAAMsP,GAAA,GAAM,KAAK0D,aAAA,CAAc1L,aAAA,EAAewL,QAAA,CAAS9S,MAAM,GAAG3B,SAAS;QAEzEE,WAAA,CAAYa,GAAA,CAAIe,QAAA,CAASH,MAAM,GAAGsP,GAAG;MACtC;IACF;IAED,OAAO/Q,WAAA;EACR;EAAA;EAGDyU,cAAc1L,aAAA,EAAe2L,OAAA,EAAS5U,SAAA,EAAW;IAC/C,QAAQ4U,OAAA,CAAQ1L,QAAA;MACd,KAAK;QACH,OAAO,KAAK2L,iBAAA,CAAkB5L,aAAA,EAAe2L,OAAA,EAAS5U,SAAS;MAGjE,KAAK;QACH,OAAO,KAAK8U,kBAAA,CAAmBF,OAAO;IAEzC;EACF;EAAA;EAGDC,kBAAkB5L,aAAA,EAAe2L,OAAA,EAAS5U,SAAA,EAAW;IACnD,MAAM4I,SAAA,GAAY5I,SAAA,CAAU4I,SAAA;IAC5B,MAAMC,YAAA,GAAe,EAAE;IAEvB,MAAMuC,UAAA,GAAanC,aAAA,CAAcjI,OAAA,CAAQgH,GAAA,CAAI,UAAU2D,MAAA,EAAQ;MAC7D,OAAO/O,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA,CAAMM,MAAA,CAAOxK,EAAE;IAC5C,CAAK;IAGD,IAAIiK,UAAA,CAAWhG,MAAA,KAAW,GAAG;IAE7B,MAAM+D,QAAA,GAAWF,aAAA,CAAchI,QAAA,CAAS+P,MAAA,CAAO,UAAU+D,SAAA,EAAUnN,KAAA,EAAO;MACxE,IAAIgB,SAAA,CAAUhB,KAAA,CAAMzG,EAAE,MAAM,QAAW4T,SAAA,GAAWnM,SAAA,CAAUhB,KAAA,CAAMzG,EAAE;MAEpE,OAAO4T,SAAA;IACR,GAAE,IAAI;IAEP9L,aAAA,CAAchI,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO;MAC9C,IAAI5H,SAAA,CAAU6I,YAAA,CAAajB,KAAA,CAAMzG,EAAE,MAAM,QAAW;QAClD0H,YAAA,CAAaxH,IAAA,CAAKrB,SAAA,CAAU6I,YAAA,CAAajB,KAAA,CAAMzG,EAAE,CAAC;MACnD;IACP,CAAK;IAID,MAAMoK,SAAA,GAAYH,UAAA,CAAW,CAAC;IAE9B,MAAMc,aAAA,GAAgB,CAAE;IAExB,IAAI,mBAAmBX,SAAA,EAAWW,aAAA,CAAcqF,UAAA,GAAaC,aAAA,CAAcjG,SAAA,CAAUkG,aAAA,CAAcjN,KAAK;IACxG,IAAI,iBAAiB+G,SAAA,EAAWW,aAAA,CAAcmF,WAAA,GAAcvP,QAAA,CAASyJ,SAAA,CAAU+F,WAAA,CAAY9M,KAAK;IAEhG,IAAI,0BAA0B+G,SAAA,EAAWW,aAAA,CAAcwF,WAAA,GAAcnG,SAAA,CAAUyJ,oBAAA,CAAqBxQ,KAAA;IACpG,IAAI,uBAAuB+G,SAAA,EAAWW,aAAA,CAAc4F,QAAA,GAAWvG,SAAA,CAAU0J,iBAAA,CAAkBzQ,KAAA;IAC3F,IAAI,sBAAsB+G,SAAA,EAAWW,aAAA,CAAcgG,KAAA,GAAQ3G,SAAA,CAAU2J,gBAAA,CAAiB1Q,KAAA;IAEtF,MAAM+H,SAAA,GAAYC,iBAAA,CAAkBN,aAAa;IAEjD,OAAO,KAAKiJ,WAAA,CAAYP,OAAA,EAASzL,QAAA,EAAUN,YAAA,EAAc0D,SAAS;EACnE;EAAA;EAGD4I,YAAYP,OAAA,EAASzL,QAAA,EAAUN,YAAA,EAAcuM,YAAA,EAAc;IACzD,MAAMnE,GAAA,GAAM,IAAIoE,cAAA,CAAgB;IAChC,IAAIT,OAAA,CAAQ1Q,QAAA,EAAU+M,GAAA,CAAIhN,IAAA,GAAO2Q,OAAA,CAAQ1Q,QAAA;IAEzC,MAAMoR,OAAA,GAAU,KAAKC,YAAA,CAAaX,OAAA,EAASzL,QAAQ;IACnD,MAAMqM,OAAA,GAAU,KAAKC,UAAA,CAAWH,OAAO;IAEvC,MAAMI,iBAAA,GAAoB,IAAIC,sBAAA,CAAuBH,OAAA,CAAQI,MAAA,EAAQ,CAAC;IAEtEF,iBAAA,CAAkBjJ,YAAA,CAAa2I,YAAY;IAE3CnE,GAAA,CAAI4E,YAAA,CAAa,YAAYH,iBAAiB;IAE9C,IAAIF,OAAA,CAAQM,MAAA,CAAO1Q,MAAA,GAAS,GAAG;MAC7B6L,GAAA,CAAI4E,YAAA,CAAa,SAAS,IAAIF,sBAAA,CAAuBH,OAAA,CAAQM,MAAA,EAAQ,CAAC,CAAC;IACxE;IAED,IAAI3M,QAAA,EAAU;MACZ8H,GAAA,CAAI4E,YAAA,CAAa,aAAa,IAAIE,qBAAA,CAAsBP,OAAA,CAAQQ,cAAA,EAAgB,CAAC,CAAC;MAElF/E,GAAA,CAAI4E,YAAA,CAAa,cAAc,IAAIF,sBAAA,CAAuBH,OAAA,CAAQS,aAAA,EAAe,CAAC,CAAC;MAGnFhF,GAAA,CAAIL,YAAA,GAAezH,QAAA;IACpB;IAED,IAAIqM,OAAA,CAAQU,MAAA,CAAO9Q,MAAA,GAAS,GAAG;MAC7B,MAAM+Q,YAAA,GAAe,IAAIC,OAAA,GAAUC,eAAA,CAAgBjB,YAAY;MAE/D,MAAMkB,eAAA,GAAkB,IAAIX,sBAAA,CAAuBH,OAAA,CAAQU,MAAA,EAAQ,CAAC;MACpEI,eAAA,CAAgBC,iBAAA,CAAkBJ,YAAY;MAE9ClF,GAAA,CAAI4E,YAAA,CAAa,UAAUS,eAAe;IAC3C;IAEDd,OAAA,CAAQgB,GAAA,CAAI/V,OAAA,CAAQ,UAAUgW,QAAA,EAAUlM,CAAA,EAAG;MACzC,IAAImM,GAAA,KAAQ,OAAOnM,CAAA;MACnB,MAAMtG,IAAA,GAAOsG,CAAA,KAAM,IAAI,OAAO,KAAKA,CAAA;MAEnC0G,GAAA,CAAI4E,YAAA,CAAa5R,IAAA,EAAM,IAAI0R,sBAAA,CAAuBH,OAAA,CAAQgB,GAAA,CAAIjM,CAAC,GAAG,CAAC,CAAC;IAC1E,CAAK;IAED,IAAI+K,OAAA,CAAQ5P,QAAA,IAAY4P,OAAA,CAAQ5P,QAAA,CAASiR,WAAA,KAAgB,WAAW;MAElE,IAAIC,iBAAA,GAAoBpB,OAAA,CAAQqB,aAAA,CAAc,CAAC;MAC/C,IAAIC,UAAA,GAAa;MAEjBtB,OAAA,CAAQqB,aAAA,CAAcpW,OAAA,CAAQ,UAAUsW,YAAA,EAAcxM,CAAA,EAAG;QACvD,IAAIwM,YAAA,KAAiBH,iBAAA,EAAmB;UACtC3F,GAAA,CAAI+F,QAAA,CAASF,UAAA,EAAYvM,CAAA,GAAIuM,UAAA,EAAYF,iBAAiB;UAE1DA,iBAAA,GAAoBG,YAAA;UACpBD,UAAA,GAAavM,CAAA;QACd;MACT,CAAO;MAGD,IAAI0G,GAAA,CAAIgG,MAAA,CAAO7R,MAAA,GAAS,GAAG;QACzB,MAAM8R,SAAA,GAAYjG,GAAA,CAAIgG,MAAA,CAAOhG,GAAA,CAAIgG,MAAA,CAAO7R,MAAA,GAAS,CAAC;QAClD,MAAM+R,SAAA,GAAYD,SAAA,CAAUE,KAAA,GAAQF,SAAA,CAAUG,KAAA;QAE9C,IAAIF,SAAA,KAAc3B,OAAA,CAAQqB,aAAA,CAAczR,MAAA,EAAQ;UAC9C6L,GAAA,CAAI+F,QAAA,CAASG,SAAA,EAAW3B,OAAA,CAAQqB,aAAA,CAAczR,MAAA,GAAS+R,SAAA,EAAWP,iBAAiB;QACpF;MACF;MAID,IAAI3F,GAAA,CAAIgG,MAAA,CAAO7R,MAAA,KAAW,GAAG;QAC3B6L,GAAA,CAAI+F,QAAA,CAAS,GAAGxB,OAAA,CAAQqB,aAAA,CAAczR,MAAA,EAAQoQ,OAAA,CAAQqB,aAAA,CAAc,CAAC,CAAC;MACvE;IACF;IAED,KAAKS,eAAA,CAAgBrG,GAAA,EAAK2D,OAAA,EAAS/L,YAAA,EAAcuM,YAAY;IAE7D,OAAOnE,GAAA;EACR;EAEDsE,aAAaX,OAAA,EAASzL,QAAA,EAAU;IAC9B,MAAMmM,OAAA,GAAU,CAAE;IAElBA,OAAA,CAAQiC,eAAA,GAAkB3C,OAAA,CAAQ4C,QAAA,KAAa,SAAY5C,OAAA,CAAQ4C,QAAA,CAAStN,CAAA,GAAI,EAAE;IAClFoL,OAAA,CAAQmC,aAAA,GAAgB7C,OAAA,CAAQ8C,kBAAA,KAAuB,SAAY9C,OAAA,CAAQ8C,kBAAA,CAAmBxN,CAAA,GAAI,EAAE;IAEpG,IAAI0K,OAAA,CAAQ+C,iBAAA,EAAmB;MAC7BrC,OAAA,CAAQhP,KAAA,GAAQ,KAAKsR,iBAAA,CAAkBhD,OAAA,CAAQ+C,iBAAA,CAAkB,CAAC,CAAC;IACpE;IAED,IAAI/C,OAAA,CAAQiD,oBAAA,EAAsB;MAChCvC,OAAA,CAAQ5P,QAAA,GAAW,KAAKoS,oBAAA,CAAqBlD,OAAA,CAAQiD,oBAAA,CAAqB,CAAC,CAAC;IAC7E;IAED,IAAIjD,OAAA,CAAQmD,kBAAA,EAAoB;MAC9BzC,OAAA,CAAQY,MAAA,GAAS,KAAK8B,YAAA,CAAapD,OAAA,CAAQmD,kBAAA,CAAmB,CAAC,CAAC;IACjE;IAED,IAAInD,OAAA,CAAQqD,cAAA,EAAgB;MAC1B3C,OAAA,CAAQ4C,EAAA,GAAK,EAAE;MAEf,IAAI3N,CAAA,GAAI;MACR,OAAOqK,OAAA,CAAQqD,cAAA,CAAe1N,CAAC,GAAG;QAChC,IAAIqK,OAAA,CAAQqD,cAAA,CAAe1N,CAAC,EAAE4N,EAAA,EAAI;UAChC7C,OAAA,CAAQ4C,EAAA,CAAG7W,IAAA,CAAK,KAAK+W,QAAA,CAASxD,OAAA,CAAQqD,cAAA,CAAe1N,CAAC,CAAC,CAAC;QACzD;QAEDA,CAAA;MACD;IACF;IAED+K,OAAA,CAAQ+C,WAAA,GAAc,CAAE;IAExB,IAAIlP,QAAA,KAAa,MAAM;MACrBmM,OAAA,CAAQnM,QAAA,GAAWA,QAAA;MAEnBA,QAAA,CAASO,QAAA,CAASjJ,OAAA,CAAQ,UAAUmJ,OAAA,EAASW,CAAA,EAAG;QAE9CX,OAAA,CAAQC,OAAA,CAAQpJ,OAAA,CAAQ,UAAU6X,KAAA,EAAOC,CAAA,EAAG;UAC1C,IAAIjD,OAAA,CAAQ+C,WAAA,CAAYC,KAAK,MAAM,QAAWhD,OAAA,CAAQ+C,WAAA,CAAYC,KAAK,IAAI,EAAE;UAE7EhD,OAAA,CAAQ+C,WAAA,CAAYC,KAAK,EAAEjX,IAAA,CAAK;YAC9BQ,EAAA,EAAI0I,CAAA;YACJiO,MAAA,EAAQ5O,OAAA,CAAQE,OAAA,CAAQyO,CAAC;UACrC,CAAW;QACX,CAAS;MACT,CAAO;IACF;IAED,OAAOjD,OAAA;EACR;EAEDG,WAAWH,OAAA,EAAS;IAClB,MAAME,OAAA,GAAU;MACdI,MAAA,EAAQ,EAAE;MACVM,MAAA,EAAQ,EAAE;MACVJ,MAAA,EAAQ,EAAE;MACVU,GAAA,EAAK,EAAE;MACPK,aAAA,EAAe,EAAE;MACjBZ,aAAA,EAAe,EAAE;MACjBD,cAAA,EAAgB;IACjB;IAED,IAAIyC,YAAA,GAAe;IACnB,IAAIC,UAAA,GAAa;IACjB,IAAIC,uBAAA,GAA0B;IAG9B,IAAIC,mBAAA,GAAsB,EAAE;IAC5B,IAAIC,WAAA,GAAc,EAAE;IACpB,IAAIC,UAAA,GAAa,EAAE;IACnB,IAAIC,OAAA,GAAU,EAAE;IAChB,IAAIC,WAAA,GAAc,EAAE;IACpB,IAAIC,iBAAA,GAAoB,EAAE;IAE1B,MAAMzb,KAAA,GAAQ;IACd8X,OAAA,CAAQmC,aAAA,CAAchX,OAAA,CAAQ,UAAUyY,WAAA,EAAaC,kBAAA,EAAoB;MACvE,IAAItC,aAAA;MACJ,IAAIuC,SAAA,GAAY;MAShB,IAAIF,WAAA,GAAc,GAAG;QACnBA,WAAA,GAAcA,WAAA,GAAc;QAC5BE,SAAA,GAAY;MACb;MAED,IAAIC,aAAA,GAAgB,EAAE;MACtB,IAAIvP,OAAA,GAAU,EAAE;MAEhB8O,mBAAA,CAAoBvX,IAAA,CAAK6X,WAAA,GAAc,GAAGA,WAAA,GAAc,IAAI,GAAGA,WAAA,GAAc,IAAI,CAAC;MAElF,IAAI5D,OAAA,CAAQhP,KAAA,EAAO;QACjB,MAAMgT,IAAA,GAAOC,OAAA,CAAQJ,kBAAA,EAAoBV,YAAA,EAAcS,WAAA,EAAa5D,OAAA,CAAQhP,KAAK;QAEjFwS,UAAA,CAAWzX,IAAA,CAAKiY,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,CAAC;MAC1C;MAED,IAAIhE,OAAA,CAAQnM,QAAA,EAAU;QACpB,IAAImM,OAAA,CAAQ+C,WAAA,CAAYa,WAAW,MAAM,QAAW;UAClD5D,OAAA,CAAQ+C,WAAA,CAAYa,WAAW,EAAEzY,OAAA,CAAQ,UAAU+Y,EAAA,EAAI;YACrD1P,OAAA,CAAQzI,IAAA,CAAKmY,EAAA,CAAGhB,MAAM;YACtBa,aAAA,CAAchY,IAAA,CAAKmY,EAAA,CAAG3X,EAAE;UACpC,CAAW;QACF;QAED,IAAIiI,OAAA,CAAQ1E,MAAA,GAAS,GAAG;UACtB,IAAI,CAACuT,uBAAA,EAAyB;YAC5Bpa,OAAA,CAAQ4E,IAAA,CACN,2GACD;YACDwV,uBAAA,GAA0B;UAC3B;UAED,MAAMc,MAAA,GAAS,CAAC,GAAG,GAAG,GAAG,CAAC;UAC1B,MAAMC,MAAA,GAAS,CAAC,GAAG,GAAG,GAAG,CAAC;UAE1B5P,OAAA,CAAQrJ,OAAA,CAAQ,UAAU+X,MAAA,EAAQmB,WAAA,EAAa;YAC7C,IAAIC,aAAA,GAAgBpB,MAAA;YACpB,IAAIzB,YAAA,GAAesC,aAAA,CAAcM,WAAW;YAE5CD,MAAA,CAAOjZ,OAAA,CAAQ,UAAUoZ,cAAA,EAAgBC,mBAAA,EAAqBC,mBAAA,EAAqB;cACjF,IAAIH,aAAA,GAAgBC,cAAA,EAAgB;gBAClCE,mBAAA,CAAoBD,mBAAmB,IAAIF,aAAA;gBAC3CA,aAAA,GAAgBC,cAAA;gBAEhB,MAAMG,GAAA,GAAMP,MAAA,CAAOK,mBAAmB;gBACtCL,MAAA,CAAOK,mBAAmB,IAAI/C,YAAA;gBAC9BA,YAAA,GAAeiD,GAAA;cAChB;YACf,CAAa;UACb,CAAW;UAEDX,aAAA,GAAgBI,MAAA;UAChB3P,OAAA,GAAU4P,MAAA;QACX;QAGD,OAAO5P,OAAA,CAAQ1E,MAAA,GAAS,GAAG;UACzB0E,OAAA,CAAQzI,IAAA,CAAK,CAAC;UACdgY,aAAA,CAAchY,IAAA,CAAK,CAAC;QACrB;QAED,SAASkJ,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAG,EAAEA,CAAA,EAAG;UAC1ByO,WAAA,CAAY3X,IAAA,CAAKyI,OAAA,CAAQS,CAAC,CAAC;UAC3B0O,iBAAA,CAAkB5X,IAAA,CAAKgY,aAAA,CAAc9O,CAAC,CAAC;QACxC;MACF;MAED,IAAI+K,OAAA,CAAQY,MAAA,EAAQ;QAClB,MAAMoD,IAAA,GAAOC,OAAA,CAAQJ,kBAAA,EAAoBV,YAAA,EAAcS,WAAA,EAAa5D,OAAA,CAAQY,MAAM;QAElF2C,WAAA,CAAYxX,IAAA,CAAKiY,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,CAAC;MAC3C;MAED,IAAIhE,OAAA,CAAQ5P,QAAA,IAAY4P,OAAA,CAAQ5P,QAAA,CAASiR,WAAA,KAAgB,WAAW;QAClEE,aAAA,GAAgB0C,OAAA,CAAQJ,kBAAA,EAAoBV,YAAA,EAAcS,WAAA,EAAa5D,OAAA,CAAQ5P,QAAQ,EAAE,CAAC;MAC3F;MAED,IAAI4P,OAAA,CAAQ4C,EAAA,EAAI;QACd5C,OAAA,CAAQ4C,EAAA,CAAGzX,OAAA,CAAQ,UAAUyX,EAAA,EAAI3N,CAAA,EAAG;UAClC,MAAM+O,IAAA,GAAOC,OAAA,CAAQJ,kBAAA,EAAoBV,YAAA,EAAcS,WAAA,EAAahB,EAAE;UAEtE,IAAIa,OAAA,CAAQxO,CAAC,MAAM,QAAW;YAC5BwO,OAAA,CAAQxO,CAAC,IAAI,EAAE;UAChB;UAEDwO,OAAA,CAAQxO,CAAC,EAAElJ,IAAA,CAAKiY,IAAA,CAAK,CAAC,CAAC;UACvBP,OAAA,CAAQxO,CAAC,EAAElJ,IAAA,CAAKiY,IAAA,CAAK,CAAC,CAAC;QACjC,CAAS;MACF;MAEDZ,UAAA;MAEA,IAAIU,SAAA,EAAW;QACb5b,KAAA,CAAMyc,OAAA,CACJzE,OAAA,EACAF,OAAA,EACAsD,mBAAA,EACA/B,aAAA,EACAgC,WAAA,EACAC,UAAA,EACAC,OAAA,EACAC,WAAA,EACAC,iBAAA,EACAP,UACD;QAEDD,YAAA;QACAC,UAAA,GAAa;QAGbE,mBAAA,GAAsB,EAAE;QACxBC,WAAA,GAAc,EAAE;QAChBC,UAAA,GAAa,EAAE;QACfC,OAAA,GAAU,EAAE;QACZC,WAAA,GAAc,EAAE;QAChBC,iBAAA,GAAoB,EAAE;MACvB;IACP,CAAK;IAED,OAAOzD,OAAA;EACR;EAAA;EAGDyE,QACEzE,OAAA,EACAF,OAAA,EACAsD,mBAAA,EACA/B,aAAA,EACAgC,WAAA,EACAC,UAAA,EACAC,OAAA,EACAC,WAAA,EACAC,iBAAA,EACAP,UAAA,EACA;IACA,SAASnO,CAAA,GAAI,GAAGA,CAAA,GAAImO,UAAA,EAAYnO,CAAA,IAAK;MACnCiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoB,CAAC,CAAC,CAAC;MACnEpD,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoB,CAAC,CAAC,CAAC;MACnEpD,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoB,CAAC,CAAC,CAAC;MAEnEpD,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,EAAqBrO,CAAA,GAAI,KAAK,CAAC,CAAC,CAAC;MAC7EiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,EAAqBrO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC,CAAC;MACjFiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,EAAqBrO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC,CAAC;MAEjFiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoBrO,CAAA,GAAI,CAAC,CAAC,CAAC;MACvEiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoBrO,CAAA,GAAI,IAAI,CAAC,CAAC,CAAC;MAC3EiL,OAAA,CAAQI,MAAA,CAAOvU,IAAA,CAAKiU,OAAA,CAAQiC,eAAA,CAAgBqB,mBAAA,CAAoBrO,CAAA,GAAI,IAAI,CAAC,CAAC,CAAC;MAE3E,IAAI+K,OAAA,CAAQnM,QAAA,EAAU;QACpBqM,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAY,CAAC,CAAC;QACzCxD,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAY,CAAC,CAAC;QACzCxD,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAY,CAAC,CAAC;QACzCxD,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAY,CAAC,CAAC;QAEzCxD,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,EAAazO,CAAA,GAAI,KAAK,CAAC,CAAC;QACnDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,EAAazO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QACvDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,EAAazO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QACvDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,EAAazO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAEvDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAYzO,CAAA,GAAI,CAAC,CAAC;QAC7CiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAYzO,CAAA,GAAI,IAAI,CAAC,CAAC;QACjDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAYzO,CAAA,GAAI,IAAI,CAAC,CAAC;QACjDiL,OAAA,CAAQS,aAAA,CAAc5U,IAAA,CAAK2X,WAAA,CAAYzO,CAAA,GAAI,IAAI,CAAC,CAAC;QAEjDiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB,CAAC,CAAC;QAChDzD,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB,CAAC,CAAC;QAChDzD,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB,CAAC,CAAC;QAChDzD,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB,CAAC,CAAC;QAEhDzD,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,EAAmB1O,CAAA,GAAI,KAAK,CAAC,CAAC;QAC1DiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,EAAmB1O,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAC9DiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,EAAmB1O,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAC9DiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,EAAmB1O,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAE9DiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB1O,CAAA,GAAI,CAAC,CAAC;QACpDiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB1O,CAAA,GAAI,IAAI,CAAC,CAAC;QACxDiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB1O,CAAA,GAAI,IAAI,CAAC,CAAC;QACxDiL,OAAA,CAAQQ,cAAA,CAAe3U,IAAA,CAAK4X,iBAAA,CAAkB1O,CAAA,GAAI,IAAI,CAAC,CAAC;MACzD;MAED,IAAI+K,OAAA,CAAQhP,KAAA,EAAO;QACjBkP,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAW,CAAC,CAAC;QACjCtD,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAW,CAAC,CAAC;QACjCtD,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAW,CAAC,CAAC;QAEjCtD,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,EAAYvO,CAAA,GAAI,KAAK,CAAC,CAAC;QAC3CiL,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,EAAYvO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAC/CiL,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,EAAYvO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAE/CiL,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAWvO,CAAA,GAAI,CAAC,CAAC;QACrCiL,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAWvO,CAAA,GAAI,IAAI,CAAC,CAAC;QACzCiL,OAAA,CAAQM,MAAA,CAAOzU,IAAA,CAAKyX,UAAA,CAAWvO,CAAA,GAAI,IAAI,CAAC,CAAC;MAC1C;MAED,IAAI+K,OAAA,CAAQ5P,QAAA,IAAY4P,OAAA,CAAQ5P,QAAA,CAASiR,WAAA,KAAgB,WAAW;QAClEnB,OAAA,CAAQqB,aAAA,CAAcxV,IAAA,CAAKwV,aAAa;QACxCrB,OAAA,CAAQqB,aAAA,CAAcxV,IAAA,CAAKwV,aAAa;QACxCrB,OAAA,CAAQqB,aAAA,CAAcxV,IAAA,CAAKwV,aAAa;MACzC;MAED,IAAIvB,OAAA,CAAQY,MAAA,EAAQ;QAClBV,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAY,CAAC,CAAC;QAClCrD,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAY,CAAC,CAAC;QAClCrD,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAY,CAAC,CAAC;QAElCrD,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,EAAatO,CAAA,GAAI,KAAK,CAAC,CAAC;QAC5CiL,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,EAAatO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAChDiL,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,EAAatO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;QAEhDiL,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAYtO,CAAA,GAAI,CAAC,CAAC;QACtCiL,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAYtO,CAAA,GAAI,IAAI,CAAC,CAAC;QAC1CiL,OAAA,CAAQU,MAAA,CAAO7U,IAAA,CAAKwX,WAAA,CAAYtO,CAAA,GAAI,IAAI,CAAC,CAAC;MAC3C;MAED,IAAI+K,OAAA,CAAQ4C,EAAA,EAAI;QACd5C,OAAA,CAAQ4C,EAAA,CAAGzX,OAAA,CAAQ,UAAUyX,EAAA,EAAIK,CAAA,EAAG;UAClC,IAAI/C,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,MAAM,QAAW/C,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,IAAI,EAAE;UAErD/C,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,EAAE,CAAC,CAAC;UACjC/C,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,EAAE,CAAC,CAAC;UAEjC/C,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,GAAGhO,CAAA,GAAI,KAAK,CAAC,CAAC;UAC3CiL,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,GAAGhO,CAAA,GAAI,KAAK,IAAI,CAAC,CAAC;UAE/CiL,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,EAAEhO,CAAA,GAAI,CAAC,CAAC;UACrCiL,OAAA,CAAQgB,GAAA,CAAI+B,CAAC,EAAElX,IAAA,CAAK0X,OAAA,CAAQR,CAAC,EAAEhO,CAAA,GAAI,IAAI,CAAC,CAAC;QACnD,CAAS;MACF;IACF;EACF;EAED+M,gBAAgB4C,SAAA,EAAWC,aAAA,EAAetR,YAAA,EAAcuM,YAAA,EAAc;IACpE,IAAIvM,YAAA,CAAazD,MAAA,KAAW,GAAG;IAE/B8U,SAAA,CAAUE,oBAAA,GAAuB;IAEjCF,SAAA,CAAUG,eAAA,CAAgBtH,QAAA,GAAW,EAAE;IAGvC,MAAMvV,KAAA,GAAQ;IACdqL,YAAA,CAAapI,OAAA,CAAQ,UAAU6I,WAAA,EAAa;MAC1CA,WAAA,CAAYC,UAAA,CAAW9I,OAAA,CAAQ,UAAU6Z,SAAA,EAAW;QAClD,MAAMC,YAAA,GAAe3d,OAAA,CAAQ4E,OAAA,CAAQkT,QAAA,CAAS4F,SAAA,CAAUxP,KAAK;QAE7D,IAAIyP,YAAA,KAAiB,QAAW;UAC9B/c,KAAA,CAAMgd,gBAAA,CAAiBN,SAAA,EAAWC,aAAA,EAAeI,YAAA,EAAcnF,YAAA,EAAckF,SAAA,CAAUrW,IAAI;QAC5F;MACT,CAAO;IACP,CAAK;EACF;EAAA;EAAA;EAAA;EAAA;EAMDuW,iBAAiBN,SAAA,EAAWC,aAAA,EAAeI,YAAA,EAAcnF,YAAA,EAAcnR,IAAA,EAAM;IAC3E,MAAMwT,aAAA,GAAgB0C,aAAA,CAAczC,kBAAA,KAAuB,SAAYyC,aAAA,CAAczC,kBAAA,CAAmBxN,CAAA,GAAI,EAAE;IAE9G,MAAMuQ,oBAAA,GAAuBF,YAAA,CAAa/C,QAAA,KAAa,SAAY+C,YAAA,CAAa/C,QAAA,CAAStN,CAAA,GAAI,EAAE;IAC/F,MAAML,OAAA,GAAU0Q,YAAA,CAAapQ,OAAA,KAAY,SAAYoQ,YAAA,CAAapQ,OAAA,CAAQD,CAAA,GAAI,EAAE;IAEhF,MAAM9E,MAAA,GAAS8U,SAAA,CAAUzJ,UAAA,CAAWsC,QAAA,CAASsE,KAAA,GAAQ;IACrD,MAAMqD,cAAA,GAAiB,IAAIC,YAAA,CAAavV,MAAM;IAE9C,SAASmF,CAAA,GAAI,GAAGA,CAAA,GAAIV,OAAA,CAAQzE,MAAA,EAAQmF,CAAA,IAAK;MACvC,MAAMqQ,UAAA,GAAa/Q,OAAA,CAAQU,CAAC,IAAI;MAEhCmQ,cAAA,CAAeE,UAAU,IAAIH,oBAAA,CAAqBlQ,CAAA,GAAI,CAAC;MACvDmQ,cAAA,CAAeE,UAAA,GAAa,CAAC,IAAIH,oBAAA,CAAqBlQ,CAAA,GAAI,IAAI,CAAC;MAC/DmQ,cAAA,CAAeE,UAAA,GAAa,CAAC,IAAIH,oBAAA,CAAqBlQ,CAAA,GAAI,IAAI,CAAC;IAChE;IAGD,MAAMsQ,YAAA,GAAe;MACnBpD,aAAA;MACAF,eAAA,EAAiBmD;IAClB;IAED,MAAMI,YAAA,GAAe,KAAKrF,UAAA,CAAWoF,YAAY;IAEjD,MAAMnF,iBAAA,GAAoB,IAAIC,sBAAA,CAAuBmF,YAAA,CAAalF,MAAA,EAAQ,CAAC;IAC3EF,iBAAA,CAAkBzR,IAAA,GAAOA,IAAA,IAAQsW,YAAA,CAAarW,QAAA;IAE9CwR,iBAAA,CAAkBjJ,YAAA,CAAa2I,YAAY;IAE3C8E,SAAA,CAAUG,eAAA,CAAgBtH,QAAA,CAAS1R,IAAA,CAAKqU,iBAAiB;EAC1D;EAAA;EAGDsC,aAAa+C,UAAA,EAAY;IACvB,MAAMpE,WAAA,GAAcoE,UAAA,CAAWC,sBAAA;IAC/B,MAAMC,aAAA,GAAgBF,UAAA,CAAWG,wBAAA;IACjC,MAAM9c,MAAA,GAAS2c,UAAA,CAAWI,OAAA,CAAQjR,CAAA;IAClC,IAAIkR,WAAA,GAAc,EAAE;IACpB,IAAIH,aAAA,KAAkB,iBAAiB;MACrC,IAAI,iBAAiBF,UAAA,EAAY;QAC/BK,WAAA,GAAcL,UAAA,CAAWM,WAAA,CAAYnR,CAAA;MAC7C,WAAiB,kBAAkB6Q,UAAA,EAAY;QACvCK,WAAA,GAAcL,UAAA,CAAWO,YAAA,CAAapR,CAAA;MACvC;IACF;IAED,OAAO;MACLqR,QAAA,EAAU;MACVnd,MAAA;MACAyL,OAAA,EAASuR,WAAA;MACTzE,WAAA;MACAsE;IACD;EACF;EAAA;EAGD7C,SAASoD,MAAA,EAAQ;IACf,MAAM7E,WAAA,GAAc6E,MAAA,CAAOR,sBAAA;IAC3B,MAAMC,aAAA,GAAgBO,MAAA,CAAON,wBAAA;IAC7B,MAAM9c,MAAA,GAASod,MAAA,CAAOrD,EAAA,CAAGjO,CAAA;IACzB,IAAIkR,WAAA,GAAc,EAAE;IACpB,IAAIH,aAAA,KAAkB,iBAAiB;MACrCG,WAAA,GAAcI,MAAA,CAAOC,OAAA,CAAQvR,CAAA;IAC9B;IAED,OAAO;MACLqR,QAAA,EAAU;MACVnd,MAAA;MACAyL,OAAA,EAASuR,WAAA;MACTzE,WAAA;MACAsE;IACD;EACF;EAAA;EAGDrD,kBAAkB8D,SAAA,EAAW;IAC3B,MAAM/E,WAAA,GAAc+E,SAAA,CAAUV,sBAAA;IAC9B,MAAMC,aAAA,GAAgBS,SAAA,CAAUR,wBAAA;IAChC,MAAM9c,MAAA,GAASsd,SAAA,CAAUC,MAAA,CAAOzR,CAAA;IAChC,IAAIkR,WAAA,GAAc,EAAE;IACpB,IAAIH,aAAA,KAAkB,iBAAiB;MACrCG,WAAA,GAAcM,SAAA,CAAUE,UAAA,CAAW1R,CAAA;IACpC;IAED,OAAO;MACLqR,QAAA,EAAU;MACVnd,MAAA;MACAyL,OAAA,EAASuR,WAAA;MACTzE,WAAA;MACAsE;IACD;EACF;EAAA;EAGDnD,qBAAqB+D,YAAA,EAAc;IACjC,MAAMlF,WAAA,GAAckF,YAAA,CAAab,sBAAA;IACjC,MAAMC,aAAA,GAAgBY,YAAA,CAAaX,wBAAA;IAEnC,IAAIvE,WAAA,KAAgB,wBAAwB;MAC1C,OAAO;QACL4E,QAAA,EAAU;QACVnd,MAAA,EAAQ,CAAC,CAAC;QACVyL,OAAA,EAAS,CAAC,CAAC;QACX8M,WAAA,EAAa;QACbsE;MACD;IACF;IAED,MAAMa,mBAAA,GAAsBD,YAAA,CAAaE,SAAA,CAAU7R,CAAA;IAKnD,MAAM8R,eAAA,GAAkB,EAAE;IAE1B,SAASzR,CAAA,GAAI,GAAGA,CAAA,GAAIuR,mBAAA,CAAoB1W,MAAA,EAAQ,EAAEmF,CAAA,EAAG;MACnDyR,eAAA,CAAgB3a,IAAA,CAAKkJ,CAAC;IACvB;IAED,OAAO;MACLgR,QAAA,EAAU;MACVnd,MAAA,EAAQ0d,mBAAA;MACRjS,OAAA,EAASmS,eAAA;MACTrF,WAAA;MACAsE;IACD;EACF;EAAA;EAGDnG,mBAAmBF,OAAA,EAAS;IAC1B,IAAIqH,UAAA,KAAe,QAAW;MAC5B1d,OAAA,CAAQC,KAAA,CACN,4HACD;MACD,OAAO,IAAI6W,cAAA,CAAgB;IAC5B;IAED,MAAM6G,KAAA,GAAQpa,QAAA,CAAS8S,OAAA,CAAQuH,KAAK;IAEpC,IAAIC,KAAA,CAAMF,KAAK,GAAG;MAChB3d,OAAA,CAAQC,KAAA,CAAM,+DAA+DoW,OAAA,CAAQuH,KAAA,EAAOvH,OAAA,CAAQ/S,EAAE;MACtG,OAAO,IAAIwT,cAAA,CAAgB;IAC5B;IAED,MAAMgH,MAAA,GAASH,KAAA,GAAQ;IAEvB,MAAMI,KAAA,GAAQ1H,OAAA,CAAQ2H,UAAA,CAAWrS,CAAA;IACjC,MAAMsS,aAAA,GAAgB,EAAE;IACxB,MAAMC,YAAA,GAAe7H,OAAA,CAAQ8H,MAAA,CAAOxS,CAAA;IAEpC,SAASK,CAAA,GAAI,GAAGoS,CAAA,GAAIF,YAAA,CAAarX,MAAA,EAAQmF,CAAA,GAAIoS,CAAA,EAAGpS,CAAA,IAAK,GAAG;MACtDiS,aAAA,CAAcnb,IAAA,CAAK,IAAIub,OAAA,CAAO,EAAGpW,SAAA,CAAUiW,YAAA,EAAclS,CAAC,CAAC;IAC5D;IAED,IAAIsS,SAAA,EAAWC,OAAA;IAEf,IAAIlI,OAAA,CAAQmI,IAAA,KAAS,UAAU;MAC7BP,aAAA,CAAcnb,IAAA,CAAKmb,aAAA,CAAc,CAAC,CAAC;IACzC,WAAe5H,OAAA,CAAQmI,IAAA,KAAS,YAAY;MACtCF,SAAA,GAAYR,MAAA;MACZS,OAAA,GAAUR,KAAA,CAAMlX,MAAA,GAAS,IAAIyX,SAAA;MAE7B,SAAStS,CAAA,GAAI,GAAGA,CAAA,GAAI8R,MAAA,EAAQ,EAAE9R,CAAA,EAAG;QAC/BiS,aAAA,CAAcnb,IAAA,CAAKmb,aAAA,CAAcjS,CAAC,CAAC;MACpC;IACF;IAED,MAAMyS,KAAA,GAAQ,IAAIf,UAAA,CAAWI,MAAA,EAAQC,KAAA,EAAOE,aAAA,EAAeK,SAAA,EAAWC,OAAO;IAC7E,MAAMG,MAAA,GAASD,KAAA,CAAME,SAAA,CAAUV,aAAA,CAAcpX,MAAA,GAAS,EAAE;IAExD,OAAO,IAAIiQ,cAAA,CAAc,EAAG8H,aAAA,CAAcF,MAAM;EACjD;AACH;AAGA,MAAMrQ,eAAA,CAAgB;EAAA;EAEpBvO,MAAA,EAAQ;IACN,MAAM+e,cAAA,GAAiB,EAAE;IAEzB,MAAMC,QAAA,GAAW,KAAKC,UAAA,CAAY;IAElC,IAAID,QAAA,KAAa,QAAW;MAC1B,WAAWE,GAAA,IAAOF,QAAA,EAAU;QAC1B,MAAMG,OAAA,GAAUH,QAAA,CAASE,GAAG;QAE5B,MAAME,IAAA,GAAO,KAAKC,OAAA,CAAQF,OAAO;QAEjCJ,cAAA,CAAe/b,IAAA,CAAKoc,IAAI;MACzB;IACF;IAED,OAAOL,cAAA;EACR;EAEDE,WAAA,EAAa;IAGX,IAAI1gB,OAAA,CAAQ4E,OAAA,CAAQmc,cAAA,KAAmB,QAAW,OAAO;IAEzD,MAAMC,aAAA,GAAgB,KAAKC,wBAAA,CAA0B;IAErD,KAAKC,oBAAA,CAAqBF,aAAa;IAEvC,MAAMG,SAAA,GAAY,KAAKC,oBAAA,CAAqBJ,aAAa;IACzD,MAAMP,QAAA,GAAW,KAAKY,eAAA,CAAgBF,SAAS;IAE/C,OAAOV,QAAA;EACR;EAAA;EAAA;EAAA;EAKDQ,yBAAA,EAA2B;IACzB,MAAMK,aAAA,GAAgBthB,OAAA,CAAQ4E,OAAA,CAAQ2c,kBAAA;IAEtC,MAAMP,aAAA,GAAgB,mBAAItd,GAAA,CAAK;IAE/B,WAAWqB,MAAA,IAAUuc,aAAA,EAAe;MAClC,MAAME,YAAA,GAAeF,aAAA,CAAcvc,MAAM;MAEzC,IAAIyc,YAAA,CAAala,QAAA,CAASma,KAAA,CAAM,qBAAqB,MAAM,MAAM;QAC/D,MAAMC,SAAA,GAAY;UAChBzc,EAAA,EAAIuc,YAAA,CAAavc,EAAA;UACjB8L,IAAA,EAAMyQ,YAAA,CAAala,QAAA;UACnBqa,MAAA,EAAQ,CAAE;QACX;QAEDX,aAAA,CAAc7c,GAAA,CAAIud,SAAA,CAAUzc,EAAA,EAAIyc,SAAS;MAC1C;IACF;IAED,OAAOV,aAAA;EACR;EAAA;EAAA;EAAA;EAKDE,qBAAqBF,aAAA,EAAe;IAClC,MAAMY,SAAA,GAAY5hB,OAAA,CAAQ4E,OAAA,CAAQmc,cAAA;IASlC,WAAWhc,MAAA,IAAU6c,SAAA,EAAW;MAC9B,MAAMC,cAAA,GAAiB;QACrB5c,EAAA,EAAI2c,SAAA,CAAU7c,MAAM,EAAEE,EAAA;QACtB6c,KAAA,EAAOF,SAAA,CAAU7c,MAAM,EAAEgd,OAAA,CAAQzU,CAAA,CAAElC,GAAA,CAAI4W,uBAAuB;QAC9D9Z,MAAA,EAAQ0Z,SAAA,CAAU7c,MAAM,EAAEkd,aAAA,CAAc3U;MACzC;MAED,MAAMjB,aAAA,GAAgBpM,WAAA,CAAYuE,GAAA,CAAIqd,cAAA,CAAe5c,EAAE;MAEvD,IAAIoH,aAAA,KAAkB,QAAW;QAC/B,MAAM6V,gBAAA,GAAmB7V,aAAA,CAAcjI,OAAA,CAAQ,CAAC,EAAEG,EAAA;QAClD,MAAM4d,0BAAA,GAA6B9V,aAAA,CAAcjI,OAAA,CAAQ,CAAC,EAAEH,YAAA;QAE5D,IAAIke,0BAAA,CAA2BV,KAAA,CAAM,GAAG,GAAG;UACzCT,aAAA,CAAcxc,GAAA,CAAI0d,gBAAgB,EAAEP,MAAA,CAAO,GAAG,IAAIE,cAAA;QACnD,WAAUM,0BAAA,CAA2BV,KAAA,CAAM,GAAG,GAAG;UAChDT,aAAA,CAAcxc,GAAA,CAAI0d,gBAAgB,EAAEP,MAAA,CAAO,GAAG,IAAIE,cAAA;QACnD,WAAUM,0BAAA,CAA2BV,KAAA,CAAM,GAAG,GAAG;UAChDT,aAAA,CAAcxc,GAAA,CAAI0d,gBAAgB,EAAEP,MAAA,CAAO,GAAG,IAAIE,cAAA;QAC5D,WAAmBM,0BAAA,CAA2BV,KAAA,CAAM,iBAAiB,KAAKT,aAAA,CAAc9c,GAAA,CAAIge,gBAAgB,GAAG;UACrGlB,aAAA,CAAcxc,GAAA,CAAI0d,gBAAgB,EAAEP,MAAA,CAAO,OAAO,IAAIE,cAAA;QACvD;MACF;IACF;EACF;EAAA;EAAA;EAAA;EAKDT,qBAAqBJ,aAAA,EAAe;IAClC,MAAMoB,SAAA,GAAYpiB,OAAA,CAAQ4E,OAAA,CAAQyd,cAAA;IAElC,MAAMlB,SAAA,GAAY,mBAAIzd,GAAA,CAAK;IAE3B,WAAWqB,MAAA,IAAUqd,SAAA,EAAW;MAC9B,MAAME,eAAA,GAAkB,EAAE;MAE1B,MAAMxT,UAAA,GAAa7O,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAASH,MAAM,CAAC;MAEnD,IAAI+J,UAAA,KAAe,QAAW;QAE5B,MAAMzK,QAAA,GAAWyK,UAAA,CAAWzK,QAAA;QAE5BA,QAAA,CAASR,OAAA,CAAQ,UAAUmH,KAAA,EAAO2C,CAAA,EAAG;UACnC,IAAIqT,aAAA,CAAc9c,GAAA,CAAI8G,KAAA,CAAMzG,EAAE,GAAG;YAC/B,MAAMmd,SAAA,GAAYV,aAAA,CAAcxc,GAAA,CAAIwG,KAAA,CAAMzG,EAAE;YAG5C,IACEmd,SAAA,CAAUC,MAAA,CAAOtZ,CAAA,KAAM,UACvBqZ,SAAA,CAAUC,MAAA,CAAOrZ,CAAA,KAAM,UACvBoZ,SAAA,CAAUC,MAAA,CAAOY,CAAA,KAAM,QACvB;cACA,IAAID,eAAA,CAAgB3U,CAAC,MAAM,QAAW;gBACpC,MAAM6U,OAAA,GAAUviB,WAAA,CAAYuE,GAAA,CAAIwG,KAAA,CAAMzG,EAAE,EAAEH,OAAA,CAAQ+J,MAAA,CAAO,UAAUY,MAAA,EAAQ;kBACzE,OAAOA,MAAA,CAAO9K,YAAA,KAAiB;gBACjD,CAAiB,EAAE,CAAC,EAAEM,EAAA;gBAEN,IAAIie,OAAA,KAAY,QAAW;kBACzB,MAAMC,QAAA,GAAWziB,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA,CAAM+T,OAAA,CAAQE,QAAA,EAAU;kBAEzD,IAAID,QAAA,KAAa,QAAW;oBAC1B9gB,OAAA,CAAQ4E,IAAA,CAAK,gDAAgDyE,KAAK;oBAClE;kBACD;kBAED,MAAMoE,IAAA,GAAO;oBACXuT,SAAA,EAAWF,QAAA,CAASnb,QAAA,GAAWkJ,eAAA,CAAgBC,gBAAA,CAAiBgS,QAAA,CAASnb,QAAQ,IAAI;oBACrF/C,EAAA,EAAIke,QAAA,CAASxd,EAAA;oBACb2d,eAAA,EAAiB,CAAC,GAAG,GAAG,CAAC;oBACzBC,eAAA,EAAiB,CAAC,GAAG,GAAG,CAAC;oBACzBC,YAAA,EAAc,CAAC,GAAG,GAAG,CAAC;kBACvB;kBAED5iB,UAAA,CAAWiP,QAAA,CAAS,UAAUf,MAAA,EAAO;oBACnC,IAAIA,MAAA,CAAM7J,EAAA,KAAOke,QAAA,CAASxd,EAAA,EAAI;sBAC5BmK,IAAA,CAAKO,SAAA,GAAYvB,MAAA,CAAMoB,MAAA;sBAEvB,IAAIpB,MAAA,CAAMiB,QAAA,CAASC,aAAA,EAAeF,IAAA,CAAKuF,UAAA,GAAavG,MAAA,CAAMiB,QAAA,CAASC,aAAA,CAAcqF,UAAA;oBAClF;kBACrB,CAAmB;kBAED,IAAI,CAACvF,IAAA,CAAKO,SAAA,EAAWP,IAAA,CAAKO,SAAA,GAAY,IAAIvC,OAAA,CAAS;kBAInD,IAAI,iBAAiBqV,QAAA,EAAUrT,IAAA,CAAK4F,WAAA,GAAcyN,QAAA,CAASxN,WAAA,CAAYrN,KAAA;kBACvE,IAAI,kBAAkB6a,QAAA,EAAUrT,IAAA,CAAKgG,YAAA,GAAeqN,QAAA,CAASpN,YAAA,CAAazN,KAAA;kBAE1E0a,eAAA,CAAgB3U,CAAC,IAAIyB,IAAA;gBACtB;cACF;cAED,IAAIkT,eAAA,CAAgB3U,CAAC,GAAG2U,eAAA,CAAgB3U,CAAC,EAAE+T,SAAA,CAAU3Q,IAAI,IAAI2Q,SAAA;YAC9D,WAAUA,SAAA,CAAUC,MAAA,CAAOoB,KAAA,KAAU,QAAW;cAC/C,IAAIT,eAAA,CAAgB3U,CAAC,MAAM,QAAW;gBACpC,MAAMqV,UAAA,GAAa/iB,WAAA,CAAYuE,GAAA,CAAIwG,KAAA,CAAMzG,EAAE,EAAEH,OAAA,CAAQ+J,MAAA,CAAO,UAAUY,MAAA,EAAQ;kBAC5E,OAAOA,MAAA,CAAO9K,YAAA,KAAiB;gBACjD,CAAiB,EAAE,CAAC,EAAEM,EAAA;gBAEN,MAAM0e,SAAA,GAAYhjB,WAAA,CAAYuE,GAAA,CAAIwe,UAAU,EAAE5e,OAAA,CAAQ,CAAC,EAAEG,EAAA;gBACzD,MAAM2J,KAAA,GAAQjO,WAAA,CAAYuE,GAAA,CAAIye,SAAS,EAAE7e,OAAA,CAAQ,CAAC,EAAEG,EAAA;gBAGpD,MAAMie,OAAA,GAAUviB,WAAA,CAAYuE,GAAA,CAAI0J,KAAK,EAAE9J,OAAA,CAAQ,CAAC,EAAEG,EAAA;gBAElD,MAAMke,QAAA,GAAWziB,OAAA,CAAQ4E,OAAA,CAAQ6J,KAAA,CAAM+T,OAAO;gBAE9C,MAAMpT,IAAA,GAAO;kBACXuT,SAAA,EAAWF,QAAA,CAASnb,QAAA,GAAWkJ,eAAA,CAAgBC,gBAAA,CAAiBgS,QAAA,CAASnb,QAAQ,IAAI;kBACrF4b,SAAA,EAAWljB,OAAA,CAAQ4E,OAAA,CAAQuH,QAAA,CAAS6W,UAAU,EAAE1b;gBACjD;gBAEDgb,eAAA,CAAgB3U,CAAC,IAAIyB,IAAA;cACtB;cAEDkT,eAAA,CAAgB3U,CAAC,EAAE+T,SAAA,CAAU3Q,IAAI,IAAI2Q,SAAA;YACtC;UACF;QACX,CAAS;QAEDP,SAAA,CAAUhd,GAAA,CAAIe,QAAA,CAASH,MAAM,GAAGud,eAAe;MAChD;IACF;IAED,OAAOnB,SAAA;EACR;EAAA;EAAA;EAIDE,gBAAgBF,SAAA,EAAW;IACzB,MAAMgC,SAAA,GAAYnjB,OAAA,CAAQ4E,OAAA,CAAQwe,cAAA;IAGlC,MAAM3C,QAAA,GAAW,CAAE;IAEnB,WAAW1b,MAAA,IAAUoe,SAAA,EAAW;MAC9B,MAAM9e,QAAA,GAAWpE,WAAA,CAAYuE,GAAA,CAAIU,QAAA,CAASH,MAAM,CAAC,EAAEV,QAAA;MAEnD,IAAIA,QAAA,CAASmE,MAAA,GAAS,GAAG;QAGvB7G,OAAA,CAAQ4E,IAAA,CACN,oIACD;MACF;MAED,MAAM8c,KAAA,GAAQlC,SAAA,CAAU3c,GAAA,CAAIH,QAAA,CAAS,CAAC,EAAEE,EAAE;MAE1Ckc,QAAA,CAAS1b,MAAM,IAAI;QACjBsC,IAAA,EAAM8b,SAAA,CAAUpe,MAAM,EAAEuC,QAAA;QACxB+b;MACD;IACF;IAED,OAAO5C,QAAA;EACR;EAEDK,QAAQF,OAAA,EAAS;IACf,IAAI0C,MAAA,GAAS,EAAE;IAEf,MAAM1iB,KAAA,GAAQ;IACdggB,OAAA,CAAQyC,KAAA,CAAMxf,OAAA,CAAQ,UAAU0f,SAAA,EAAW;MACzCD,MAAA,GAASA,MAAA,CAAOE,MAAA,CAAO5iB,KAAA,CAAM6iB,cAAA,CAAeF,SAAS,CAAC;IAC5D,CAAK;IAED,OAAO,IAAIG,aAAA,CAAc9C,OAAA,CAAQvZ,IAAA,EAAM,IAAIic,MAAM;EAClD;EAEDG,eAAeF,SAAA,EAAW;IACxB,MAAMD,MAAA,GAAS,EAAE;IAEjB,IAAIV,eAAA,GAAkB,IAAIvM,OAAA,CAAS;IACnC,IAAIwM,eAAA,GAAkB,IAAIc,UAAA,CAAY;IACtC,IAAIb,YAAA,GAAe,IAAIzM,OAAA,CAAS;IAEhC,IAAIkN,SAAA,CAAU5T,SAAA,EAAW4T,SAAA,CAAU5T,SAAA,CAAUiU,SAAA,CAAUhB,eAAA,EAAiBC,eAAA,EAAiBC,YAAY;IAErGF,eAAA,GAAkBA,eAAA,CAAgBiB,OAAA,CAAS;IAC3ChB,eAAA,GAAkB,IAAIiB,KAAA,CAAK,EAAGC,iBAAA,CAAkBlB,eAAA,EAAiBU,SAAA,CAAU5O,UAAU,EAAEkP,OAAA,CAAS;IAChGf,YAAA,GAAeA,YAAA,CAAae,OAAA,CAAS;IAErC,IAAIN,SAAA,CAAUS,CAAA,KAAM,UAAaC,MAAA,CAAOC,IAAA,CAAKX,SAAA,CAAUS,CAAA,CAAErC,MAAM,EAAEnZ,MAAA,GAAS,GAAG;MAC3E,MAAM2b,aAAA,GAAgB,KAAKC,mBAAA,CACzBb,SAAA,CAAUZ,SAAA,EACVY,SAAA,CAAUS,CAAA,CAAErC,MAAA,EACZiB,eAAA,EACA,UACD;MACD,IAAIuB,aAAA,KAAkB,QAAWb,MAAA,CAAO7e,IAAA,CAAK0f,aAAa;IAC3D;IAED,IAAIZ,SAAA,CAAUc,CAAA,KAAM,UAAaJ,MAAA,CAAOC,IAAA,CAAKX,SAAA,CAAUc,CAAA,CAAE1C,MAAM,EAAEnZ,MAAA,GAAS,GAAG;MAC3E,MAAM8b,aAAA,GAAgB,KAAKC,qBAAA,CACzBhB,SAAA,CAAUZ,SAAA,EACVY,SAAA,CAAUc,CAAA,CAAE1C,MAAA,EACZkB,eAAA,EACAU,SAAA,CAAUvO,WAAA,EACVuO,SAAA,CAAUnO,YAAA,EACVmO,SAAA,CAAU5O,UACX;MACD,IAAI2P,aAAA,KAAkB,QAAWhB,MAAA,CAAO7e,IAAA,CAAK6f,aAAa;IAC3D;IAED,IAAIf,SAAA,CAAUiB,CAAA,KAAM,UAAaP,MAAA,CAAOC,IAAA,CAAKX,SAAA,CAAUiB,CAAA,CAAE7C,MAAM,EAAEnZ,MAAA,GAAS,GAAG;MAC3E,MAAMic,UAAA,GAAa,KAAKL,mBAAA,CAAoBb,SAAA,CAAUZ,SAAA,EAAWY,SAAA,CAAUiB,CAAA,CAAE7C,MAAA,EAAQmB,YAAA,EAAc,OAAO;MAC1G,IAAI2B,UAAA,KAAe,QAAWnB,MAAA,CAAO7e,IAAA,CAAKggB,UAAU;IACrD;IAED,IAAIlB,SAAA,CAAUxV,aAAA,KAAkB,QAAW;MACzC,MAAM2W,UAAA,GAAa,KAAKC,kBAAA,CAAmBpB,SAAS;MACpD,IAAImB,UAAA,KAAe,QAAWpB,MAAA,CAAO7e,IAAA,CAAKigB,UAAU;IACrD;IAED,OAAOpB,MAAA;EACR;EAEDc,oBAAoBzB,SAAA,EAAWhB,MAAA,EAAQiD,YAAA,EAAcve,IAAA,EAAM;IACzD,MAAMyb,KAAA,GAAQ,KAAK+C,kBAAA,CAAmBlD,MAAM;IAC5C,MAAMzZ,MAAA,GAAS,KAAK4c,sBAAA,CAAuBhD,KAAA,EAAOH,MAAA,EAAQiD,YAAY;IAEtE,OAAO,IAAIG,mBAAA,CAAoBpC,SAAA,GAAY,MAAMtc,IAAA,EAAMyb,KAAA,EAAO5Z,MAAM;EACrE;EAEDqc,sBAAsB5B,SAAA,EAAWhB,MAAA,EAAQiD,YAAA,EAAc5P,WAAA,EAAaI,YAAA,EAAcT,UAAA,EAAY;IAC5F,IAAIgN,MAAA,CAAOtZ,CAAA,KAAM,QAAW;MAC1B,KAAK2c,oBAAA,CAAqBrD,MAAA,CAAOtZ,CAAC;MAClCsZ,MAAA,CAAOtZ,CAAA,CAAEH,MAAA,GAASyZ,MAAA,CAAOtZ,CAAA,CAAEH,MAAA,CAAOkD,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IACzD;IAED,IAAIsO,MAAA,CAAOrZ,CAAA,KAAM,QAAW;MAC1B,KAAK0c,oBAAA,CAAqBrD,MAAA,CAAOrZ,CAAC;MAClCqZ,MAAA,CAAOrZ,CAAA,CAAEJ,MAAA,GAASyZ,MAAA,CAAOrZ,CAAA,CAAEJ,MAAA,CAAOkD,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IACzD;IAED,IAAIsO,MAAA,CAAOY,CAAA,KAAM,QAAW;MAC1B,KAAKyC,oBAAA,CAAqBrD,MAAA,CAAOY,CAAC;MAClCZ,MAAA,CAAOY,CAAA,CAAEra,MAAA,GAASyZ,MAAA,CAAOY,CAAA,CAAEra,MAAA,CAAOkD,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IACzD;IAED,MAAMyO,KAAA,GAAQ,KAAK+C,kBAAA,CAAmBlD,MAAM;IAC5C,MAAMzZ,MAAA,GAAS,KAAK4c,sBAAA,CAAuBhD,KAAA,EAAOH,MAAA,EAAQiD,YAAY;IAEtE,IAAI5P,WAAA,KAAgB,QAAW;MAC7BA,WAAA,GAAcA,WAAA,CAAY5J,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;MAChD2B,WAAA,CAAYvQ,IAAA,CAAKkQ,UAAU;MAE3BK,WAAA,GAAc,IAAI8O,KAAA,GAAQla,SAAA,CAAUoL,WAAW;MAC/CA,WAAA,GAAc,IAAI2O,UAAA,GAAasB,YAAA,CAAajQ,WAAW;IACxD;IAED,IAAII,YAAA,KAAiB,QAAW;MAC9BA,YAAA,GAAeA,YAAA,CAAahK,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;MAClD+B,YAAA,CAAa3Q,IAAA,CAAKkQ,UAAU;MAE5BS,YAAA,GAAe,IAAI0O,KAAA,GAAQla,SAAA,CAAUwL,YAAY;MACjDA,YAAA,GAAe,IAAIuO,UAAA,CAAY,EAACsB,YAAA,CAAa7P,YAAY,EAAE8P,MAAA,CAAQ;IACpE;IAED,MAAMC,UAAA,GAAa,IAAIxB,UAAA,CAAY;IACnC,MAAMyB,KAAA,GAAQ,IAAItB,KAAA,CAAO;IAEzB,MAAMuB,gBAAA,GAAmB,EAAE;IAE3B,SAAS1X,CAAA,GAAI,GAAGA,CAAA,GAAIzF,MAAA,CAAOM,MAAA,EAAQmF,CAAA,IAAK,GAAG;MACzCyX,KAAA,CAAMjhB,GAAA,CAAI+D,MAAA,CAAOyF,CAAC,GAAGzF,MAAA,CAAOyF,CAAA,GAAI,CAAC,GAAGzF,MAAA,CAAOyF,CAAA,GAAI,CAAC,GAAGgH,UAAU;MAE7DwQ,UAAA,CAAWF,YAAA,CAAaG,KAAK;MAE7B,IAAIpQ,WAAA,KAAgB,QAAWmQ,UAAA,CAAWG,WAAA,CAAYtQ,WAAW;MACjE,IAAII,YAAA,KAAiB,QAAW+P,UAAA,CAAWI,QAAA,CAASnQ,YAAY;MAEhE+P,UAAA,CAAWtB,OAAA,CAAQwB,gBAAA,EAAmB1X,CAAA,GAAI,IAAK,CAAC;IACjD;IAED,OAAO,IAAI6X,uBAAA,CAAwB7C,SAAA,GAAY,eAAeb,KAAA,EAAOuD,gBAAgB;EACtF;EAEDV,mBAAmBpB,SAAA,EAAW;IAC5B,MAAM5B,MAAA,GAAS4B,SAAA,CAAUxV,aAAA,CAAc4T,MAAA,CAAOoB,KAAA;IAC9C,MAAM7a,MAAA,GAASyZ,MAAA,CAAOzZ,MAAA,CAAOkD,GAAA,CAAI,UAAUqa,GAAA,EAAK;MAC9C,OAAOA,GAAA,GAAM;IACnB,CAAK;IAED,MAAMC,QAAA,GAAWxlB,UAAA,CAAWylB,eAAA,CAAgBpC,SAAA,CAAUZ,SAAS,EAAEiD,qBAAA,CAAsBrC,SAAA,CAAUL,SAAS;IAE1G,OAAO,IAAI2C,mBAAA,CACTtC,SAAA,CAAUZ,SAAA,GAAY,4BAA4B+C,QAAA,GAAW,KAC7D/D,MAAA,CAAOG,KAAA,EACP5Z,MACD;EACF;EAAA;EAAA;EAID2c,mBAAmBlD,MAAA,EAAQ;IACzB,IAAIG,KAAA,GAAQ,EAAE;IAGd,IAAIH,MAAA,CAAOtZ,CAAA,KAAM,QAAWyZ,KAAA,GAAQA,KAAA,CAAM0B,MAAA,CAAO7B,MAAA,CAAOtZ,CAAA,CAAEyZ,KAAK;IAC/D,IAAIH,MAAA,CAAOrZ,CAAA,KAAM,QAAWwZ,KAAA,GAAQA,KAAA,CAAM0B,MAAA,CAAO7B,MAAA,CAAOrZ,CAAA,CAAEwZ,KAAK;IAC/D,IAAIH,MAAA,CAAOY,CAAA,KAAM,QAAWT,KAAA,GAAQA,KAAA,CAAM0B,MAAA,CAAO7B,MAAA,CAAOY,CAAA,CAAET,KAAK;IAG/DA,KAAA,GAAQA,KAAA,CAAMgE,IAAA,CAAK,UAAUxY,CAAA,EAAGqK,CAAA,EAAG;MACjC,OAAOrK,CAAA,GAAIqK,CAAA;IACjB,CAAK;IAGD,IAAImK,KAAA,CAAMtZ,MAAA,GAAS,GAAG;MACpB,IAAIud,WAAA,GAAc;MAClB,IAAIC,SAAA,GAAYlE,KAAA,CAAM,CAAC;MACvB,SAASnU,CAAA,GAAI,GAAGA,CAAA,GAAImU,KAAA,CAAMtZ,MAAA,EAAQmF,CAAA,IAAK;QACrC,MAAMsY,YAAA,GAAenE,KAAA,CAAMnU,CAAC;QAC5B,IAAIsY,YAAA,KAAiBD,SAAA,EAAW;UAC9BlE,KAAA,CAAMiE,WAAW,IAAIE,YAAA;UACrBD,SAAA,GAAYC,YAAA;UACZF,WAAA;QACD;MACF;MAEDjE,KAAA,GAAQA,KAAA,CAAM5b,KAAA,CAAM,GAAG6f,WAAW;IACnC;IAED,OAAOjE,KAAA;EACR;EAEDgD,uBAAuBhD,KAAA,EAAOH,MAAA,EAAQiD,YAAA,EAAc;IAClD,MAAMsB,SAAA,GAAYtB,YAAA;IAElB,MAAM1c,MAAA,GAAS,EAAE;IAEjB,IAAIie,MAAA,GAAS;IACb,IAAIC,MAAA,GAAS;IACb,IAAIC,MAAA,GAAS;IAEbvE,KAAA,CAAMje,OAAA,CAAQ,UAAUyiB,IAAA,EAAM;MAC5B,IAAI3E,MAAA,CAAOtZ,CAAA,EAAG8d,MAAA,GAASxE,MAAA,CAAOtZ,CAAA,CAAEyZ,KAAA,CAAMrZ,OAAA,CAAQ6d,IAAI;MAClD,IAAI3E,MAAA,CAAOrZ,CAAA,EAAG8d,MAAA,GAASzE,MAAA,CAAOrZ,CAAA,CAAEwZ,KAAA,CAAMrZ,OAAA,CAAQ6d,IAAI;MAClD,IAAI3E,MAAA,CAAOY,CAAA,EAAG8D,MAAA,GAAS1E,MAAA,CAAOY,CAAA,CAAET,KAAA,CAAMrZ,OAAA,CAAQ6d,IAAI;MAGlD,IAAIH,MAAA,KAAW,IAAI;QACjB,MAAMI,MAAA,GAAS5E,MAAA,CAAOtZ,CAAA,CAAEH,MAAA,CAAOie,MAAM;QACrCje,MAAA,CAAOzD,IAAA,CAAK8hB,MAAM;QAClBL,SAAA,CAAU,CAAC,IAAIK,MAAA;MACvB,OAAa;QAELre,MAAA,CAAOzD,IAAA,CAAKyhB,SAAA,CAAU,CAAC,CAAC;MACzB;MAED,IAAIE,MAAA,KAAW,IAAI;QACjB,MAAMI,MAAA,GAAS7E,MAAA,CAAOrZ,CAAA,CAAEJ,MAAA,CAAOke,MAAM;QACrCle,MAAA,CAAOzD,IAAA,CAAK+hB,MAAM;QAClBN,SAAA,CAAU,CAAC,IAAIM,MAAA;MACvB,OAAa;QACLte,MAAA,CAAOzD,IAAA,CAAKyhB,SAAA,CAAU,CAAC,CAAC;MACzB;MAED,IAAIG,MAAA,KAAW,IAAI;QACjB,MAAMI,MAAA,GAAS9E,MAAA,CAAOY,CAAA,CAAEra,MAAA,CAAOme,MAAM;QACrCne,MAAA,CAAOzD,IAAA,CAAKgiB,MAAM;QAClBP,SAAA,CAAU,CAAC,IAAIO,MAAA;MACvB,OAAa;QACLve,MAAA,CAAOzD,IAAA,CAAKyhB,SAAA,CAAU,CAAC,CAAC;MACzB;IACP,CAAK;IAED,OAAOhe,MAAA;EACR;EAAA;EAAA;EAAA;EAKD8c,qBAAqB5E,KAAA,EAAO;IAC1B,SAASzS,CAAA,GAAI,GAAGA,CAAA,GAAIyS,KAAA,CAAMlY,MAAA,CAAOM,MAAA,EAAQmF,CAAA,IAAK;MAC5C,MAAMiX,YAAA,GAAexE,KAAA,CAAMlY,MAAA,CAAOyF,CAAA,GAAI,CAAC;MACvC,MAAM+Y,UAAA,GAAatG,KAAA,CAAMlY,MAAA,CAAOyF,CAAC,IAAIiX,YAAA;MAErC,MAAM+B,YAAA,GAAe1T,IAAA,CAAK2T,GAAA,CAAIF,UAAU;MAExC,IAAIC,YAAA,IAAgB,KAAK;QACvB,MAAME,eAAA,GAAkBF,YAAA,GAAe;QAEvC,MAAMG,IAAA,GAAOJ,UAAA,GAAaG,eAAA;QAC1B,IAAIE,SAAA,GAAYnC,YAAA,GAAekC,IAAA;QAE/B,MAAME,WAAA,GAAc5G,KAAA,CAAM0B,KAAA,CAAMnU,CAAA,GAAI,CAAC;QACrC,MAAMsZ,QAAA,GAAW7G,KAAA,CAAM0B,KAAA,CAAMnU,CAAC,IAAIqZ,WAAA;QAClC,MAAME,QAAA,GAAWD,QAAA,GAAWJ,eAAA;QAC5B,IAAIM,QAAA,GAAWH,WAAA,GAAcE,QAAA;QAE7B,MAAME,iBAAA,GAAoB,EAAE;QAC5B,MAAMC,kBAAA,GAAqB,EAAE;QAE7B,OAAOF,QAAA,GAAW/G,KAAA,CAAM0B,KAAA,CAAMnU,CAAC,GAAG;UAChCyZ,iBAAA,CAAkB3iB,IAAA,CAAK0iB,QAAQ;UAC/BA,QAAA,IAAYD,QAAA;UAEZG,kBAAA,CAAmB5iB,IAAA,CAAKsiB,SAAS;UACjCA,SAAA,IAAaD,IAAA;QACd;QAED1G,KAAA,CAAM0B,KAAA,GAAQwF,MAAA,CAAOlH,KAAA,CAAM0B,KAAA,EAAOnU,CAAA,EAAGyZ,iBAAiB;QACtDhH,KAAA,CAAMlY,MAAA,GAASof,MAAA,CAAOlH,KAAA,CAAMlY,MAAA,EAAQyF,CAAA,EAAG0Z,kBAAkB;MAC1D;IACF;EACF;AACH;AAGA,MAAM/kB,UAAA,CAAW;EACfilB,YAAA,EAAc;IACZ,OAAO,KAAKC,SAAA,CAAU,KAAKC,aAAA,GAAgB,CAAC;EAC7C;EAEDC,eAAA,EAAiB;IACf,OAAO,KAAKF,SAAA,CAAU,KAAKC,aAAA,GAAgB,CAAC;EAC7C;EAEDE,eAAA,EAAiB;IACf,OAAO,KAAKC,WAAA;EACb;EAEDC,UAAUzY,IAAA,EAAM;IACd,KAAKoY,SAAA,CAAU/iB,IAAA,CAAK2K,IAAI;IACxB,KAAKqY,aAAA,IAAiB;EACvB;EAEDK,SAAA,EAAW;IACT,KAAKN,SAAA,CAAU1hB,GAAA,CAAK;IACpB,KAAK2hB,aAAA,IAAiB;EACvB;EAEDM,eAAetC,GAAA,EAAKpe,IAAA,EAAM;IACxB,KAAKugB,WAAA,GAAcnC,GAAA;IACnB,KAAKuC,eAAA,GAAkB3gB,IAAA;EACxB;EAED5F,MAAMwmB,IAAA,EAAM;IACV,KAAKR,aAAA,GAAgB;IAErB,KAAKS,QAAA,GAAW,IAAIC,OAAA,CAAS;IAC7B,KAAKX,SAAA,GAAY,EAAE;IACnB,KAAKI,WAAA,GAAc,EAAE;IACrB,KAAKI,eAAA,GAAkB;IAEvB,MAAMpnB,KAAA,GAAQ;IAEd,MAAMiF,KAAA,GAAQoiB,IAAA,CAAKpiB,KAAA,CAAM,SAAS;IAElCA,KAAA,CAAMhC,OAAA,CAAQ,UAAUukB,IAAA,EAAMza,CAAA,EAAG;MAC/B,MAAM0a,YAAA,GAAeD,IAAA,CAAK3G,KAAA,CAAM,WAAW;MAC3C,MAAM6G,UAAA,GAAaF,IAAA,CAAK3G,KAAA,CAAM,WAAW;MAEzC,IAAI4G,YAAA,IAAgBC,UAAA,EAAY;MAEhC,MAAMC,cAAA,GAAiBH,IAAA,CAAK3G,KAAA,CAAM,UAAU7gB,KAAA,CAAM6mB,aAAA,GAAgB,iBAAiB,EAAE;MACrF,MAAMe,aAAA,GAAgBJ,IAAA,CAAK3G,KAAA,CAAM,UAAU7gB,KAAA,CAAM6mB,aAAA,GAAgB,4BAA4B;MAC7F,MAAMgB,QAAA,GAAWL,IAAA,CAAK3G,KAAA,CAAM,WAAW7gB,KAAA,CAAM6mB,aAAA,GAAgB,KAAK,IAAI;MAEtE,IAAIc,cAAA,EAAgB;QAClB3nB,KAAA,CAAM8nB,cAAA,CAAeN,IAAA,EAAMG,cAAc;MAC1C,WAAUC,aAAA,EAAe;QACxB5nB,KAAA,CAAM+nB,iBAAA,CAAkBP,IAAA,EAAMI,aAAA,EAAe3iB,KAAA,CAAM,EAAE8H,CAAC,CAAC;MACxD,WAAU8a,QAAA,EAAU;QACnB7nB,KAAA,CAAMknB,QAAA,CAAU;MACjB,WAAUM,IAAA,CAAK3G,KAAA,CAAM,WAAW,GAAG;QAGlC7gB,KAAA,CAAMgoB,0BAAA,CAA2BR,IAAI;MACtC;IACP,CAAK;IAED,OAAO,KAAKF,QAAA;EACb;EAEDQ,eAAeN,IAAA,EAAMS,QAAA,EAAU;IAC7B,MAAMC,QAAA,GAAWD,QAAA,CAAS,CAAC,EAAEE,IAAA,CAAM,EAACC,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,MAAM,EAAE;IAEtE,MAAMC,SAAA,GAAYJ,QAAA,CAAS,CAAC,EAAEhjB,KAAA,CAAM,GAAG,EAAEuF,GAAA,CAAI,UAAU2F,IAAA,EAAM;MAC3D,OAAOA,IAAA,CAAKgY,IAAA,CAAM,EAACC,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,MAAM,EAAE;IAC3D,CAAK;IAED,MAAM5Z,IAAA,GAAO;MAAE/H,IAAA,EAAMyhB;IAAU;IAC/B,MAAMI,KAAA,GAAQ,KAAKC,aAAA,CAAcF,SAAS;IAE1C,MAAMG,WAAA,GAAc,KAAK1B,cAAA,CAAgB;IAGzC,IAAI,KAAKD,aAAA,KAAkB,GAAG;MAC5B,KAAKS,QAAA,CAASlZ,GAAA,CAAI8Z,QAAA,EAAU1Z,IAAI;IACtC,OAAW;MAIL,IAAI0Z,QAAA,IAAYM,WAAA,EAAa;QAE3B,IAAIN,QAAA,KAAa,YAAY;UAC3BM,WAAA,CAAYpS,QAAA,CAASvS,IAAA,CAAK2K,IAAI;QAC/B,WAAUga,WAAA,CAAYN,QAAQ,EAAE7jB,EAAA,KAAO,QAAW;UACjDmkB,WAAA,CAAYN,QAAQ,IAAI,CAAE;UAC1BM,WAAA,CAAYN,QAAQ,EAAEM,WAAA,CAAYN,QAAQ,EAAE7jB,EAAE,IAAImkB,WAAA,CAAYN,QAAQ;QACvE;QAED,IAAII,KAAA,CAAMjkB,EAAA,KAAO,IAAImkB,WAAA,CAAYN,QAAQ,EAAEI,KAAA,CAAMjkB,EAAE,IAAImK,IAAA;MACxD,WAAU,OAAO8Z,KAAA,CAAMjkB,EAAA,KAAO,UAAU;QACvCmkB,WAAA,CAAYN,QAAQ,IAAI,CAAE;QAC1BM,WAAA,CAAYN,QAAQ,EAAEI,KAAA,CAAMjkB,EAAE,IAAImK,IAAA;MAC1C,WAAiB0Z,QAAA,KAAa,gBAAgB;QACtC,IAAIA,QAAA,KAAa,YAAYM,WAAA,CAAYN,QAAQ,IAAI,CAAC1Z,IAAI,OACrDga,WAAA,CAAYN,QAAQ,IAAI1Z,IAAA;MAC9B;IACF;IAED,IAAI,OAAO8Z,KAAA,CAAMjkB,EAAA,KAAO,UAAUmK,IAAA,CAAKnK,EAAA,GAAKikB,KAAA,CAAMjkB,EAAA;IAClD,IAAIikB,KAAA,CAAM7hB,IAAA,KAAS,IAAI+H,IAAA,CAAK9H,QAAA,GAAW4hB,KAAA,CAAM7hB,IAAA;IAC7C,IAAI6hB,KAAA,CAAM7iB,IAAA,KAAS,IAAI+I,IAAA,CAAK9C,QAAA,GAAW4c,KAAA,CAAM7iB,IAAA;IAE7C,KAAKwhB,SAAA,CAAUzY,IAAI;EACpB;EAED+Z,cAAcD,KAAA,EAAO;IACnB,IAAIjkB,EAAA,GAAKikB,KAAA,CAAM,CAAC;IAEhB,IAAIA,KAAA,CAAM,CAAC,MAAM,IAAI;MACnBjkB,EAAA,GAAKC,QAAA,CAASgkB,KAAA,CAAM,CAAC,CAAC;MAEtB,IAAI1J,KAAA,CAAMva,EAAE,GAAG;QACbA,EAAA,GAAKikB,KAAA,CAAM,CAAC;MACb;IACF;IAED,IAAI7hB,IAAA,GAAO;MACThB,IAAA,GAAO;IAET,IAAI6iB,KAAA,CAAM1gB,MAAA,GAAS,GAAG;MACpBnB,IAAA,GAAO6hB,KAAA,CAAM,CAAC,EAAEF,OAAA,CAAQ,YAAY,EAAE;MACtC3iB,IAAA,GAAO6iB,KAAA,CAAM,CAAC;IACf;IAED,OAAO;MAAEjkB,EAAA;MAAQoC,IAAA;MAAYhB;IAAY;EAC1C;EAEDsiB,kBAAkBP,IAAA,EAAMS,QAAA,EAAUQ,WAAA,EAAa;IAC7C,IAAIC,QAAA,GAAWT,QAAA,CAAS,CAAC,EAAEG,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,MAAM,EAAE,EAAED,IAAA,CAAM;IACrE,IAAIQ,SAAA,GAAYV,QAAA,CAAS,CAAC,EAAEG,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,MAAM,EAAE,EAAED,IAAA,CAAM;IAKtE,IAAIO,QAAA,KAAa,aAAaC,SAAA,KAAc,KAAK;MAC/CA,SAAA,GAAYF,WAAA,CAAYL,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,MAAM,EAAE,EAAED,IAAA,CAAM;IACnE;IAED,MAAMK,WAAA,GAAc,KAAK1B,cAAA,CAAgB;IACzC,MAAM8B,UAAA,GAAaJ,WAAA,CAAY/hB,IAAA;IAE/B,IAAImiB,UAAA,KAAe,gBAAgB;MACjC,KAAKC,wBAAA,CAAyBrB,IAAA,EAAMkB,QAAA,EAAUC,SAAS;MACvD;IACD;IAGD,IAAID,QAAA,KAAa,KAAK;MACpB,MAAMI,SAAA,GAAYH,SAAA,CAAU1jB,KAAA,CAAM,GAAG,EAAEK,KAAA,CAAM,CAAC;MAC9C,MAAMyjB,IAAA,GAAOzkB,QAAA,CAASwkB,SAAA,CAAU,CAAC,CAAC;MAClC,MAAME,EAAA,GAAK1kB,QAAA,CAASwkB,SAAA,CAAU,CAAC,CAAC;MAEhC,IAAIG,IAAA,GAAON,SAAA,CAAU1jB,KAAA,CAAM,GAAG,EAAEK,KAAA,CAAM,CAAC;MAEvC2jB,IAAA,GAAOA,IAAA,CAAKze,GAAA,CAAI,UAAU0e,IAAA,EAAM;QAC9B,OAAOA,IAAA,CAAKf,IAAA,CAAI,EAAGC,OAAA,CAAQ,MAAM,EAAE;MAC3C,CAAO;MAEDM,QAAA,GAAW;MACXC,SAAA,GAAY,CAACI,IAAA,EAAMC,EAAE;MACrBG,MAAA,CAAOR,SAAA,EAAWM,IAAI;MAEtB,IAAIT,WAAA,CAAYE,QAAQ,MAAM,QAAW;QACvCF,WAAA,CAAYE,QAAQ,IAAI,EAAE;MAC3B;IACF;IAGD,IAAIA,QAAA,KAAa,QAAQF,WAAA,CAAYnkB,EAAA,GAAKskB,SAAA;IAG1C,IAAID,QAAA,IAAYF,WAAA,IAAenS,KAAA,CAAMC,OAAA,CAAQkS,WAAA,CAAYE,QAAQ,CAAC,GAAG;MACnEF,WAAA,CAAYE,QAAQ,EAAE7kB,IAAA,CAAK8kB,SAAS;IAC1C,OAAW;MACL,IAAID,QAAA,KAAa,KAAKF,WAAA,CAAYE,QAAQ,IAAIC,SAAA,MACzCH,WAAA,CAAY9b,CAAA,GAAIic,SAAA;IACtB;IAED,KAAKxB,cAAA,CAAeqB,WAAA,EAAaE,QAAQ;IAGzC,IAAIA,QAAA,KAAa,OAAOC,SAAA,CAAUrjB,KAAA,CAAM,EAAE,MAAM,KAAK;MACnDkjB,WAAA,CAAY9b,CAAA,GAAI0c,gBAAA,CAAiBT,SAAS;IAC3C;EACF;EAEDX,2BAA2BR,IAAA,EAAM;IAC/B,MAAMgB,WAAA,GAAc,KAAK1B,cAAA,CAAgB;IAEzC0B,WAAA,CAAY9b,CAAA,IAAK8a,IAAA;IAIjB,IAAIA,IAAA,CAAKliB,KAAA,CAAM,EAAE,MAAM,KAAK;MAC1BkjB,WAAA,CAAY9b,CAAA,GAAI0c,gBAAA,CAAiBZ,WAAA,CAAY9b,CAAC;IAC/C;EACF;EAAA;EAGDmc,yBAAyBrB,IAAA,EAAMkB,QAAA,EAAUC,SAAA,EAAW;IAKlD,MAAMU,KAAA,GAAQV,SAAA,CAAU1jB,KAAA,CAAM,IAAI,EAAEuF,GAAA,CAAI,UAAU8e,IAAA,EAAM;MACtD,OAAOA,IAAA,CAAKnB,IAAA,CAAM,EAACC,OAAA,CAAQ,OAAO,EAAE,EAAEA,OAAA,CAAQ,MAAM,GAAG;IAC7D,CAAK;IAED,MAAMmB,aAAA,GAAgBF,KAAA,CAAM,CAAC;IAC7B,MAAMG,cAAA,GAAiBH,KAAA,CAAM,CAAC;IAC9B,MAAMI,cAAA,GAAiBJ,KAAA,CAAM,CAAC;IAC9B,MAAMK,aAAA,GAAgBL,KAAA,CAAM,CAAC;IAC7B,IAAIM,cAAA,GAAiBN,KAAA,CAAM,CAAC;IAG5B,QAAQG,cAAA;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACHG,cAAA,GAAiBlgB,UAAA,CAAWkgB,cAAc;QAC1C;MAEF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACHA,cAAA,GAAiBP,gBAAA,CAAiBO,cAAc;QAChD;IACH;IAGD,KAAKhD,WAAA,GAAc4C,aAAa,IAAI;MAClC9jB,IAAA,EAAM+jB,cAAA;MACNI,KAAA,EAAOH,cAAA;MACPI,IAAA,EAAMH,aAAA;MACN1iB,KAAA,EAAO2iB;IACR;IAED,KAAKxC,cAAA,CAAe,KAAKR,WAAA,CAAW,GAAI4C,aAAa;EACtD;AACH;AAGA,MAAMnoB,YAAA,CAAa;EACjBP,MAAMD,MAAA,EAAQ;IACZ,MAAMkpB,MAAA,GAAS,IAAIC,YAAA,CAAanpB,MAAM;IACtCkpB,MAAA,CAAOE,IAAA,CAAK,EAAE;IAEd,MAAMC,OAAA,GAAUH,MAAA,CAAOI,SAAA,CAAW;IAElC,IAAID,OAAA,GAAU,MAAM;MAClB,MAAM,IAAIzoB,KAAA,CAAM,8DAA8DyoB,OAAO;IACtF;IAED,MAAM3C,QAAA,GAAW,IAAIC,OAAA,CAAS;IAE9B,OAAO,CAAC,KAAK4C,YAAA,CAAaL,MAAM,GAAG;MACjC,MAAMtb,IAAA,GAAO,KAAK4b,SAAA,CAAUN,MAAA,EAAQG,OAAO;MAC3C,IAAIzb,IAAA,KAAS,MAAM8Y,QAAA,CAASlZ,GAAA,CAAII,IAAA,CAAK/H,IAAA,EAAM+H,IAAI;IAChD;IAED,OAAO8Y,QAAA;EACR;EAAA;EAGD6C,aAAaL,MAAA,EAAQ;IASnB,IAAIA,MAAA,CAAOO,IAAA,KAAS,OAAO,GAAG;MAC5B,QAASP,MAAA,CAAOQ,SAAA,KAAc,MAAM,KAAM,CAAC,OAAQR,MAAA,CAAOO,IAAA,CAAM;IACtE,OAAW;MACL,OAAOP,MAAA,CAAOQ,SAAA,CAAW,IAAG,MAAM,MAAMR,MAAA,CAAOO,IAAA,CAAM;IACtD;EACF;EAAA;EAGDD,UAAUN,MAAA,EAAQG,OAAA,EAAS;IACzB,MAAMzb,IAAA,GAAO,CAAE;IAGf,MAAM+b,SAAA,GAAYN,OAAA,IAAW,OAAOH,MAAA,CAAOU,SAAA,CAAW,IAAGV,MAAA,CAAOI,SAAA,CAAW;IAC3E,MAAMO,aAAA,GAAgBR,OAAA,IAAW,OAAOH,MAAA,CAAOU,SAAA,CAAW,IAAGV,MAAA,CAAOI,SAAA,CAAW;IAE/ED,OAAA,IAAW,OAAOH,MAAA,CAAOU,SAAA,CAAS,IAAKV,MAAA,CAAOI,SAAA,CAAW;IAEzD,MAAMQ,OAAA,GAAUZ,MAAA,CAAOa,QAAA,CAAU;IACjC,MAAMlkB,IAAA,GAAOqjB,MAAA,CAAOc,SAAA,CAAUF,OAAO;IAGrC,IAAIH,SAAA,KAAc,GAAG,OAAO;IAE5B,MAAMM,YAAA,GAAe,EAAE;IAEvB,SAAS9d,CAAA,GAAI,GAAGA,CAAA,GAAI0d,aAAA,EAAe1d,CAAA,IAAK;MACtC8d,YAAA,CAAahnB,IAAA,CAAK,KAAKinB,aAAA,CAAchB,MAAM,CAAC;IAC7C;IAGD,MAAMzlB,EAAA,GAAKwmB,YAAA,CAAajjB,MAAA,GAAS,IAAIijB,YAAA,CAAa,CAAC,IAAI;IACvD,MAAMnkB,QAAA,GAAWmkB,YAAA,CAAajjB,MAAA,GAAS,IAAIijB,YAAA,CAAa,CAAC,IAAI;IAC7D,MAAMnf,QAAA,GAAWmf,YAAA,CAAajjB,MAAA,GAAS,IAAIijB,YAAA,CAAa,CAAC,IAAI;IAI7Drc,IAAA,CAAKuc,cAAA,GAAiBN,aAAA,KAAkB,KAAKX,MAAA,CAAOQ,SAAA,CAAW,MAAKC,SAAA,GAAY,OAAO;IAEvF,OAAOA,SAAA,GAAYT,MAAA,CAAOQ,SAAA,IAAa;MACrC,MAAMU,OAAA,GAAU,KAAKZ,SAAA,CAAUN,MAAA,EAAQG,OAAO;MAE9C,IAAIe,OAAA,KAAY,MAAM,KAAKC,YAAA,CAAaxkB,IAAA,EAAM+H,IAAA,EAAMwc,OAAO;IAC5D;IAEDxc,IAAA,CAAKqc,YAAA,GAAeA,YAAA;IAEpB,IAAI,OAAOxmB,EAAA,KAAO,UAAUmK,IAAA,CAAKnK,EAAA,GAAKA,EAAA;IACtC,IAAIqC,QAAA,KAAa,IAAI8H,IAAA,CAAK9H,QAAA,GAAWA,QAAA;IACrC,IAAIgF,QAAA,KAAa,IAAI8C,IAAA,CAAK9C,QAAA,GAAWA,QAAA;IACrC,IAAIjF,IAAA,KAAS,IAAI+H,IAAA,CAAK/H,IAAA,GAAOA,IAAA;IAE7B,OAAO+H,IAAA;EACR;EAEDyc,aAAaxkB,IAAA,EAAM+H,IAAA,EAAMwc,OAAA,EAAS;IAEhC,IAAIA,OAAA,CAAQD,cAAA,KAAmB,MAAM;MACnC,MAAM/jB,KAAA,GAAQgkB,OAAA,CAAQH,YAAA,CAAa,CAAC;MAEpC,IAAIxU,KAAA,CAAMC,OAAA,CAAQtP,KAAK,GAAG;QACxBwH,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,IAAIukB,OAAA;QAErBA,OAAA,CAAQte,CAAA,GAAI1F,KAAA;MACpB,OAAa;QACLwH,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,IAAIO,KAAA;MACtB;IACF,WAAUP,IAAA,KAAS,iBAAiBukB,OAAA,CAAQvkB,IAAA,KAAS,KAAK;MACzD,MAAMb,KAAA,GAAQ,EAAE;MAEhBolB,OAAA,CAAQH,YAAA,CAAa5nB,OAAA,CAAQ,UAAUglB,QAAA,EAAUlb,CAAA,EAAG;QAElD,IAAIA,CAAA,KAAM,GAAGnH,KAAA,CAAM/B,IAAA,CAAKokB,QAAQ;MACxC,CAAO;MAED,IAAIzZ,IAAA,CAAKnP,WAAA,KAAgB,QAAW;QAClCmP,IAAA,CAAKnP,WAAA,GAAc,EAAE;MACtB;MAEDmP,IAAA,CAAKnP,WAAA,CAAYwE,IAAA,CAAK+B,KAAK;IACjC,WAAeolB,OAAA,CAAQvkB,IAAA,KAAS,gBAAgB;MAC1C,MAAM6c,IAAA,GAAOD,MAAA,CAAOC,IAAA,CAAK0H,OAAO;MAEhC1H,IAAA,CAAKrgB,OAAA,CAAQ,UAAU8c,GAAA,EAAK;QAC1BvR,IAAA,CAAKuR,GAAG,IAAIiL,OAAA,CAAQjL,GAAG;MAC/B,CAAO;IACF,WAAUtZ,IAAA,KAAS,kBAAkBukB,OAAA,CAAQvkB,IAAA,KAAS,KAAK;MAC1D,IAAI8iB,aAAA,GAAgByB,OAAA,CAAQH,YAAA,CAAa,CAAC;MAC1C,IAAIrB,cAAA,GAAiBwB,OAAA,CAAQH,YAAA,CAAa,CAAC;MAC3C,MAAMpB,cAAA,GAAiBuB,OAAA,CAAQH,YAAA,CAAa,CAAC;MAC7C,MAAMnB,aAAA,GAAgBsB,OAAA,CAAQH,YAAA,CAAa,CAAC;MAC5C,IAAIlB,cAAA;MAEJ,IAAIJ,aAAA,CAAc1hB,OAAA,CAAQ,MAAM,MAAM,GAAG0hB,aAAA,GAAgBA,aAAA,CAAcnB,OAAA,CAAQ,QAAQ,MAAM;MAC7F,IAAIoB,cAAA,CAAe3hB,OAAA,CAAQ,MAAM,MAAM,GAAG2hB,cAAA,GAAiBA,cAAA,CAAepB,OAAA,CAAQ,QAAQ,MAAM;MAEhG,IACEoB,cAAA,KAAmB,WACnBA,cAAA,KAAmB,cACnBA,cAAA,KAAmB,YACnBA,cAAA,KAAmB,cACnBA,cAAA,CAAe3hB,OAAA,CAAQ,MAAM,MAAM,GACnC;QACA8hB,cAAA,GAAiB,CAACqB,OAAA,CAAQH,YAAA,CAAa,CAAC,GAAGG,OAAA,CAAQH,YAAA,CAAa,CAAC,GAAGG,OAAA,CAAQH,YAAA,CAAa,CAAC,CAAC;MACnG,OAAa;QACLlB,cAAA,GAAiBqB,OAAA,CAAQH,YAAA,CAAa,CAAC;MACxC;MAGDrc,IAAA,CAAK+a,aAAa,IAAI;QACpB9jB,IAAA,EAAM+jB,cAAA;QACNI,KAAA,EAAOH,cAAA;QACPI,IAAA,EAAMH,aAAA;QACN1iB,KAAA,EAAO2iB;MACR;IACF,WAAUnb,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,MAAM,QAAW;MAC3C,IAAI,OAAOukB,OAAA,CAAQ3mB,EAAA,KAAO,UAAU;QAClCmK,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,IAAI,CAAE;QACvB+H,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,EAAEukB,OAAA,CAAQ3mB,EAAE,IAAI2mB,OAAA;MACzC,OAAa;QACLxc,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,IAAIukB,OAAA;MACtB;IACP,OAAW;MACL,IAAIA,OAAA,CAAQvkB,IAAA,KAAS,YAAY;QAC/B,IAAI,CAAC4P,KAAA,CAAMC,OAAA,CAAQ9H,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,CAAC,GAAG;UACtC+H,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,IAAI,CAAC+H,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,CAAC;QACzC;QAED+H,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,EAAE5C,IAAA,CAAKmnB,OAAO;MACvC,WAAiBxc,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,EAAEukB,OAAA,CAAQ3mB,EAAE,MAAM,QAAW;QACvDmK,IAAA,CAAKwc,OAAA,CAAQvkB,IAAI,EAAEukB,OAAA,CAAQ3mB,EAAE,IAAI2mB,OAAA;MAClC;IACF;EACF;EAEDF,cAAchB,MAAA,EAAQ;IACpB,MAAMrkB,IAAA,GAAOqkB,MAAA,CAAOc,SAAA,CAAU,CAAC;IAC/B,IAAIhjB,MAAA;IAEJ,QAAQnC,IAAA;MACN,KAAK;QACH,OAAOqkB,MAAA,CAAOoB,UAAA,CAAY;MAE5B,KAAK;QACH,OAAOpB,MAAA,CAAOqB,UAAA,CAAY;MAE5B,KAAK;QACH,OAAOrB,MAAA,CAAOsB,UAAA,CAAY;MAE5B,KAAK;QACH,OAAOtB,MAAA,CAAOuB,QAAA,CAAU;MAE1B,KAAK;QACH,OAAOvB,MAAA,CAAOwB,QAAA,CAAU;MAE1B,KAAK;QACH1jB,MAAA,GAASkiB,MAAA,CAAOI,SAAA,CAAW;QAC3B,OAAOJ,MAAA,CAAOyB,cAAA,CAAe3jB,MAAM;MAErC,KAAK;QACHA,MAAA,GAASkiB,MAAA,CAAOI,SAAA,CAAW;QAC3B,OAAOJ,MAAA,CAAOc,SAAA,CAAUhjB,MAAM;MAEhC,KAAK;QACH,OAAOkiB,MAAA,CAAO0B,QAAA,CAAU;MAE1B,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,MAAMC,WAAA,GAAc3B,MAAA,CAAOI,SAAA,CAAW;QACtC,MAAMxf,QAAA,GAAWof,MAAA,CAAOI,SAAA,CAAW;QACnC,MAAMwB,gBAAA,GAAmB5B,MAAA,CAAOI,SAAA,CAAW;QAE3C,IAAIxf,QAAA,KAAa,GAAG;UAClB,QAAQjF,IAAA;YACN,KAAK;YACL,KAAK;cACH,OAAOqkB,MAAA,CAAO6B,eAAA,CAAgBF,WAAW;YAE3C,KAAK;cACH,OAAO3B,MAAA,CAAO8B,eAAA,CAAgBH,WAAW;YAE3C,KAAK;cACH,OAAO3B,MAAA,CAAO+B,eAAA,CAAgBJ,WAAW;YAE3C,KAAK;cACH,OAAO3B,MAAA,CAAOgC,aAAA,CAAcL,WAAW;YAEzC,KAAK;cACH,OAAO3B,MAAA,CAAOiC,aAAA,CAAcN,WAAW;UAC1C;QACF;QAED,MAAM3P,IAAA,GAAOkQ,UAAA,CAAW,IAAInmB,UAAA,CAAWikB,MAAA,CAAOyB,cAAA,CAAeG,gBAAgB,CAAC,CAAC;QAC/E,MAAMO,OAAA,GAAU,IAAIlC,YAAA,CAAajO,IAAA,CAAKlb,MAAM;QAE5C,QAAQ6E,IAAA;UACN,KAAK;UACL,KAAK;YACH,OAAOwmB,OAAA,CAAQN,eAAA,CAAgBF,WAAW;UAE5C,KAAK;YACH,OAAOQ,OAAA,CAAQL,eAAA,CAAgBH,WAAW;UAE5C,KAAK;YACH,OAAOQ,OAAA,CAAQJ,eAAA,CAAgBJ,WAAW;UAE5C,KAAK;YACH,OAAOQ,OAAA,CAAQH,aAAA,CAAcL,WAAW;UAE1C,KAAK;YACH,OAAOQ,OAAA,CAAQF,aAAA,CAAcN,WAAW;QAC3C;MAEH;QACE,MAAM,IAAIjqB,KAAA,CAAM,4CAA4CiE,IAAI;IACnE;EACF;AACH;AAEA,MAAMskB,YAAA,CAAa;EACjBtqB,YAAYmB,MAAA,EAAQsrB,YAAA,EAAc;IAChC,KAAKC,EAAA,GAAK,IAAIC,QAAA,CAASxrB,MAAM;IAC7B,KAAKyrB,MAAA,GAAS;IACd,KAAKH,YAAA,GAAeA,YAAA,KAAiB,SAAYA,YAAA,GAAe;EACjE;EAED5B,UAAA,EAAY;IACV,OAAO,KAAK+B,MAAA;EACb;EAEDhC,KAAA,EAAO;IACL,OAAO,KAAK8B,EAAA,CAAGvrB,MAAA,CAAOgE,UAAA;EACvB;EAEDolB,KAAKpiB,MAAA,EAAQ;IACX,KAAKykB,MAAA,IAAUzkB,MAAA;EAChB;EAAA;EAAA;EAAA;EAKDsjB,WAAA,EAAa;IACX,QAAQ,KAAKP,QAAA,CAAU,IAAG,OAAO;EAClC;EAEDgB,gBAAgBtB,IAAA,EAAM;IACpB,MAAM3d,CAAA,GAAI,EAAE;IAEZ,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAE7I,IAAA,CAAK,KAAKqnB,UAAA,EAAY;IACzB;IAED,OAAOxe,CAAA;EACR;EAEDie,SAAA,EAAW;IACT,MAAM3jB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGxB,QAAA,CAAS,KAAK0B,MAAM;IAC1C,KAAKA,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAEDwkB,SAAA,EAAW;IACT,MAAMxkB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGX,QAAA,CAAS,KAAKa,MAAA,EAAQ,KAAKH,YAAY;IAC7D,KAAKG,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAEDqkB,SAAA,EAAW;IACT,MAAMrkB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGd,QAAA,CAAS,KAAKgB,MAAA,EAAQ,KAAKH,YAAY;IAC7D,KAAKG,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAED8kB,cAAczB,IAAA,EAAM;IAClB,MAAM3d,CAAA,GAAI,EAAE;IAEZ,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAE7I,IAAA,CAAK,KAAKwnB,QAAA,EAAU;IACvB;IAED,OAAO3e,CAAA;EACR;EAEDwd,UAAA,EAAY;IACV,MAAMljB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGjC,SAAA,CAAU,KAAKmC,MAAA,EAAQ,KAAKH,YAAY;IAC9D,KAAKG,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAAA;EAAA;EAAA;EAAA;EAAA;EAODskB,SAAA,EAAW;IACT,IAAIgB,GAAA,EAAKC,IAAA;IAET,IAAI,KAAKL,YAAA,EAAc;MACrBI,GAAA,GAAM,KAAKpC,SAAA,CAAW;MACtBqC,IAAA,GAAO,KAAKrC,SAAA,CAAW;IAC7B,OAAW;MACLqC,IAAA,GAAO,KAAKrC,SAAA,CAAW;MACvBoC,GAAA,GAAM,KAAKpC,SAAA,CAAW;IACvB;IAGD,IAAIqC,IAAA,GAAO,YAAY;MACrBA,IAAA,GAAO,CAACA,IAAA,GAAO;MACfD,GAAA,GAAM,CAACA,GAAA,GAAM;MAEb,IAAIA,GAAA,KAAQ,YAAYC,IAAA,GAAQA,IAAA,GAAO,IAAK;MAE5CD,GAAA,GAAOA,GAAA,GAAM,IAAK;MAElB,OAAO,EAAEC,IAAA,GAAO,aAAcD,GAAA;IAC/B;IAED,OAAOC,IAAA,GAAO,aAAcD,GAAA;EAC7B;EAEDP,cAAc1B,IAAA,EAAM;IAClB,MAAM3d,CAAA,GAAI,EAAE;IAEZ,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAE7I,IAAA,CAAK,KAAKynB,QAAA,EAAU;IACvB;IAED,OAAO5e,CAAA;EACR;EAAA;EAGD8d,UAAA,EAAY;IACV,IAAI8B,GAAA,EAAKC,IAAA;IAET,IAAI,KAAKL,YAAA,EAAc;MACrBI,GAAA,GAAM,KAAKpC,SAAA,CAAW;MACtBqC,IAAA,GAAO,KAAKrC,SAAA,CAAW;IAC7B,OAAW;MACLqC,IAAA,GAAO,KAAKrC,SAAA,CAAW;MACvBoC,GAAA,GAAM,KAAKpC,SAAA,CAAW;IACvB;IAED,OAAOqC,IAAA,GAAO,aAAcD,GAAA;EAC7B;EAEDlB,WAAA,EAAa;IACX,MAAMpkB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGf,UAAA,CAAW,KAAKiB,MAAA,EAAQ,KAAKH,YAAY;IAC/D,KAAKG,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAED6kB,gBAAgBxB,IAAA,EAAM;IACpB,MAAM3d,CAAA,GAAI,EAAE;IAEZ,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAE7I,IAAA,CAAK,KAAKunB,UAAA,EAAY;IACzB;IAED,OAAO1e,CAAA;EACR;EAEDye,WAAA,EAAa;IACX,MAAMnkB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGhB,UAAA,CAAW,KAAKkB,MAAA,EAAQ,KAAKH,YAAY;IAC/D,KAAKG,MAAA,IAAU;IACf,OAAOrlB,KAAA;EACR;EAED4kB,gBAAgBvB,IAAA,EAAM;IACpB,MAAM3d,CAAA,GAAI,EAAE;IAEZ,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAE7I,IAAA,CAAK,KAAKsnB,UAAA,EAAY;IACzB;IAED,OAAOze,CAAA;EACR;EAED6e,eAAelB,IAAA,EAAM;IACnB,MAAMrjB,KAAA,GAAQ,KAAKmlB,EAAA,CAAGvrB,MAAA,CAAO0E,KAAA,CAAM,KAAK+mB,MAAA,EAAQ,KAAKA,MAAA,GAAShC,IAAI;IAClE,KAAKgC,MAAA,IAAUhC,IAAA;IACf,OAAOrjB,KAAA;EACR;EAED4jB,UAAUP,IAAA,EAAM;IAEd,IAAI3d,CAAA,GAAI,EAAE;IAEV,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIsd,IAAA,EAAMtd,CAAA,IAAK;MAC7BL,CAAA,CAAEK,CAAC,IAAI,KAAK4d,QAAA,CAAU;IACvB;IAED,MAAM6B,QAAA,GAAW9f,CAAA,CAAE7E,OAAA,CAAQ,CAAC;IAC5B,IAAI2kB,QAAA,IAAY,GAAG9f,CAAA,GAAIA,CAAA,CAAEpH,KAAA,CAAM,GAAGknB,QAAQ;IAE1C,OAAOC,UAAA,CAAW,IAAI5mB,UAAA,CAAW6G,CAAC,CAAC;EACpC;AACH;AAIA,MAAM6a,OAAA,CAAQ;EACZnZ,IAAI2R,GAAA,EAAK8E,GAAA,EAAK;IACZ,KAAK9E,GAAG,IAAI8E,GAAA;EACb;AACH;AAIA,SAAS1jB,kBAAkBP,MAAA,EAAQ;EACjC,MAAM8rB,OAAA,GAAU;EAEhB,OAAO9rB,MAAA,CAAOgE,UAAA,IAAc8nB,OAAA,CAAQ9kB,MAAA,IAAU8kB,OAAA,KAAYprB,0BAAA,CAA2BV,MAAA,EAAQ,GAAG8rB,OAAA,CAAQ9kB,MAAM;AAChH;AAEA,SAASrG,iBAAiB8lB,IAAA,EAAM;EAC9B,MAAMqF,OAAA,GAAU,CACd,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MACA,KACA,KACA,KACA,MACA,KACA,KACA,KACA,KACA,KACA,KACA,MACA,KACD;EAED,IAAIC,MAAA,GAAS;EAEb,SAASC,KAAKP,MAAA,EAAQ;IACpB,MAAMQ,MAAA,GAASxF,IAAA,CAAKgF,MAAA,GAAS,CAAC;IAC9BhF,IAAA,GAAOA,IAAA,CAAK/hB,KAAA,CAAMqnB,MAAA,GAASN,MAAM;IACjCM,MAAA;IACA,OAAOE,MAAA;EACR;EAED,SAAS9f,CAAA,GAAI,GAAGA,CAAA,GAAI2f,OAAA,CAAQ9kB,MAAA,EAAQ,EAAEmF,CAAA,EAAG;IACvC,MAAM+f,GAAA,GAAMF,IAAA,CAAK,CAAC;IAClB,IAAIE,GAAA,KAAQJ,OAAA,CAAQ3f,CAAC,GAAG;MACtB,OAAO;IACR;EACF;EAED,OAAO;AACT;AAEA,SAAStL,cAAc4lB,IAAA,EAAM;EAC3B,MAAM0F,aAAA,GAAgB;EACtB,MAAMlM,KAAA,GAAQwG,IAAA,CAAKxG,KAAA,CAAMkM,aAAa;EAEtC,IAAIlM,KAAA,EAAO;IACT,MAAMoJ,OAAA,GAAU3lB,QAAA,CAASuc,KAAA,CAAM,CAAC,CAAC;IACjC,OAAOoJ,OAAA;EACR;EAED,MAAM,IAAIzoB,KAAA,CAAM,qEAAqE;AACvF;AAGA,SAAS4f,wBAAwBsE,IAAA,EAAM;EACrC,OAAOA,IAAA,GAAO;AAChB;AAEA,MAAMsH,SAAA,GAAY,EAAE;AAGpB,SAASjR,QAAQJ,kBAAA,EAAoBV,YAAA,EAAcS,WAAA,EAAauR,UAAA,EAAY;EAC1E,IAAInS,KAAA;EAEJ,QAAQmS,UAAA,CAAW9T,WAAA;IACjB,KAAK;MACH2B,KAAA,GAAQa,kBAAA;MACR;IACF,KAAK;MACHb,KAAA,GAAQG,YAAA;MACR;IACF,KAAK;MACHH,KAAA,GAAQY,WAAA;MACR;IACF,KAAK;MACHZ,KAAA,GAAQmS,UAAA,CAAW5gB,OAAA,CAAQ,CAAC;MAC5B;IACF;MACEtL,OAAA,CAAQ4E,IAAA,CAAK,qDAAqDsnB,UAAA,CAAW9T,WAAW;EAC3F;EAED,IAAI8T,UAAA,CAAWxP,aAAA,KAAkB,iBAAiB3C,KAAA,GAAQmS,UAAA,CAAW5gB,OAAA,CAAQyO,KAAK;EAElF,MAAMiO,IAAA,GAAOjO,KAAA,GAAQmS,UAAA,CAAWlP,QAAA;EAChC,MAAMiL,EAAA,GAAKD,IAAA,GAAOkE,UAAA,CAAWlP,QAAA;EAE7B,OAAOzY,KAAA,CAAM0nB,SAAA,EAAWC,UAAA,CAAWrsB,MAAA,EAAQmoB,IAAA,EAAMC,EAAE;AACrD;AAEA,MAAMkE,SAAA,GAA4B,mBAAIhK,KAAA,CAAO;AAC7C,MAAMiK,OAAA,GAA0B,mBAAI1X,OAAA,CAAS;AAK7C,SAASzG,kBAAkBN,aAAA,EAAe;EACxC,MAAM0e,aAAA,GAAgB,IAAI5gB,OAAA,CAAS;EACnC,MAAM6gB,aAAA,GAAgB,IAAI7gB,OAAA,CAAS;EACnC,MAAM8gB,UAAA,GAAa,IAAI9gB,OAAA,CAAS;EAChC,MAAM+gB,cAAA,GAAiB,IAAI/gB,OAAA,CAAS;EAEpC,MAAMghB,SAAA,GAAY,IAAIhhB,OAAA,CAAS;EAC/B,MAAMihB,cAAA,GAAiB,IAAIjhB,OAAA,CAAS;EACpC,MAAMkhB,eAAA,GAAkB,IAAIlhB,OAAA,CAAS;EACrC,MAAMmhB,gBAAA,GAAmB,IAAInhB,OAAA,CAAS;EACtC,MAAMohB,eAAA,GAAkB,IAAIphB,OAAA,CAAS;EAErC,MAAMqhB,SAAA,GAAY,IAAIrhB,OAAA,CAAS;EAC/B,MAAMshB,SAAA,GAAY,IAAIthB,OAAA,CAAS;EAC/B,MAAMuhB,QAAA,GAAW,IAAIvhB,OAAA,CAAS;EAE9B,MAAMqH,WAAA,GAAcnF,aAAA,CAAcmF,WAAA,GAAcnF,aAAA,CAAcmF,WAAA,GAAc;EAE5E,IAAInF,aAAA,CAAcwF,WAAA,EAAakZ,aAAA,CAAcY,WAAA,CAAYb,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAcwF,WAAW,CAAC;EAErG,IAAIxF,aAAA,CAAc0F,WAAA,EAAa;IAC7B,MAAMxO,KAAA,GAAQ8I,aAAA,CAAc0F,WAAA,CAAY5J,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IAC9D7M,KAAA,CAAM/B,IAAA,CAAK6K,aAAA,CAAcqF,UAAU;IACnCsZ,aAAA,CAAcY,qBAAA,CAAsBf,SAAA,CAAUlkB,SAAA,CAAUpD,KAAK,CAAC;EAC/D;EAED,IAAI8I,aAAA,CAAc4F,QAAA,EAAU;IAC1B,MAAM1O,KAAA,GAAQ8I,aAAA,CAAc4F,QAAA,CAAS9J,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IAC3D7M,KAAA,CAAM/B,IAAA,CAAK6K,aAAA,CAAcqF,UAAU;IACnCuZ,UAAA,CAAWW,qBAAA,CAAsBf,SAAA,CAAUlkB,SAAA,CAAUpD,KAAK,CAAC;EAC5D;EAED,IAAI8I,aAAA,CAAc8F,YAAA,EAAc;IAC9B,MAAM5O,KAAA,GAAQ8I,aAAA,CAAc8F,YAAA,CAAahK,GAAA,CAAIgI,SAAA,CAAUC,QAAQ;IAC/D7M,KAAA,CAAM/B,IAAA,CAAK6K,aAAA,CAAcqF,UAAU;IACnCwZ,cAAA,CAAeU,qBAAA,CAAsBf,SAAA,CAAUlkB,SAAA,CAAUpD,KAAK,CAAC;IAC/D2nB,cAAA,CAAejJ,MAAA,CAAQ;EACxB;EAED,IAAI5V,aAAA,CAAcgG,KAAA,EAAO8Y,SAAA,CAAU9Y,KAAA,CAAMyY,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAcgG,KAAK,CAAC;EAG/E,IAAIhG,aAAA,CAAckG,aAAA,EAAe8Y,eAAA,CAAgBM,WAAA,CAAYb,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAckG,aAAa,CAAC;EAC3G,IAAIlG,aAAA,CAAcoG,YAAA,EAAc2Y,cAAA,CAAeO,WAAA,CAAYb,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAcoG,YAAY,CAAC;EACxG,IAAIpG,aAAA,CAAcsG,cAAA,EAAgB2Y,gBAAA,CAAiBK,WAAA,CAAYb,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAcsG,cAAc,CAAC;EAC9G,IAAItG,aAAA,CAAcwG,aAAA,EAAe0Y,eAAA,CAAgBI,WAAA,CAAYb,OAAA,CAAQnkB,SAAA,CAAU0F,aAAA,CAAcwG,aAAa,CAAC;EAG3G,IAAIxG,aAAA,CAAcG,iBAAA,EAAmB;IACnCif,SAAA,CAAU7d,IAAA,CAAKvB,aAAA,CAAcC,YAAY;IACzCkf,SAAA,CAAU5d,IAAA,CAAKvB,aAAA,CAAcG,iBAAiB;EAC/C;EAED,MAAMqf,IAAA,GAAOb,aAAA,CAAcc,KAAA,CAAO,EAACxJ,QAAA,CAAS2I,UAAU,EAAE3I,QAAA,CAAS4I,cAAc;EAE/E,MAAMa,UAAA,GAAa,IAAI5hB,OAAA,CAAS;EAChC4hB,UAAA,CAAWC,eAAA,CAAgBR,SAAS;EAGpC,MAAMS,SAAA,GAAY,IAAI9hB,OAAA,CAAS;EAC/B8hB,SAAA,CAAUC,YAAA,CAAaV,SAAS;EAEhC,MAAMW,WAAA,GAAcF,SAAA,CAAUH,KAAA,CAAK,EAAG7J,MAAA,CAAQ,EAACK,QAAA,CAASkJ,SAAS;EACjE,MAAMY,UAAA,GAAaL,UAAA,CAAWD,KAAA,CAAK,EAAG7J,MAAA,CAAQ,EAACK,QAAA,CAAS6J,WAAW;EACnE,MAAME,IAAA,GAAOlB,SAAA;EAEb,MAAMmB,SAAA,GAAY,IAAIniB,OAAA,CAAS;EAE/B,IAAIqH,WAAA,KAAgB,GAAG;IACrB8a,SAAA,CAAU1e,IAAA,CAAKme,UAAU,EAAEzJ,QAAA,CAASuJ,IAAI,EAAEvJ,QAAA,CAAS8J,UAAU,EAAE9J,QAAA,CAAS+J,IAAI;EAChF,WAAa7a,WAAA,KAAgB,GAAG;IAC5B8a,SAAA,CAAU1e,IAAA,CAAKme,UAAU,EAAEzJ,QAAA,CAAS8J,UAAU,EAAE9J,QAAA,CAASuJ,IAAI,EAAEvJ,QAAA,CAAS+J,IAAI;EAChF,OAAS;IACL,MAAME,UAAA,GAAa,IAAIpiB,OAAA,GAAUkI,KAAA,CAAM,IAAIe,OAAA,CAAS,EAACoZ,kBAAA,CAAmBf,SAAS,CAAC;IAClF,MAAMgB,cAAA,GAAiBF,UAAA,CAAWT,KAAA,CAAK,EAAG7J,MAAA,CAAQ;IAClD,MAAMyK,kBAAA,GAAqBN,UAAA,CAAWN,KAAA,CAAK,EAAGxJ,QAAA,CAASmK,cAAc;IAErEH,SAAA,CAAU1e,IAAA,CAAKme,UAAU,EAAEzJ,QAAA,CAASuJ,IAAI,EAAEvJ,QAAA,CAASoK,kBAAkB,EAAEpK,QAAA,CAAS+J,IAAI;EACrF;EAED,MAAMM,mBAAA,GAAsBpB,eAAA,CAAgBO,KAAA,CAAK,EAAG7J,MAAA,CAAQ;EAC5D,MAAM2K,kBAAA,GAAqBxB,cAAA,CAAeU,KAAA,CAAK,EAAG7J,MAAA,CAAQ;EAE1D,IAAI4K,UAAA,GAAa9B,aAAA,CACde,KAAA,CAAO,EACPxJ,QAAA,CAASgJ,gBAAgB,EACzBhJ,QAAA,CAASiJ,eAAe,EACxBjJ,QAAA,CAAS0I,aAAa,EACtB1I,QAAA,CAAS2I,UAAU,EACnB3I,QAAA,CAAS4I,cAAc,EACvB5I,QAAA,CAASqK,mBAAmB,EAC5BrK,QAAA,CAAS+I,eAAe,EACxB/I,QAAA,CAAS8I,cAAc,EACvB9I,QAAA,CAAS6I,SAAS,EAClB7I,QAAA,CAASsK,kBAAkB;EAE9B,MAAME,gCAAA,GAAmC,IAAI3iB,OAAA,GAAU+hB,YAAA,CAAaW,UAAU;EAE9E,MAAME,kBAAA,GAAqBvB,SAAA,CAAUM,KAAA,CAAK,EAAGxJ,QAAA,CAASwK,gCAAgC;EACtFpB,QAAA,CAASQ,YAAA,CAAaa,kBAAkB;EAExCF,UAAA,GAAanB,QAAA,CAASI,KAAA,GAAQxJ,QAAA,CAASgK,SAAS;EAGhDO,UAAA,CAAWxK,WAAA,CAAYmJ,SAAA,CAAUvJ,MAAA,EAAQ;EAEzC,OAAO4K,UAAA;AACT;AAIA,SAASlb,cAAc0K,KAAA,EAAO;EAC5BA,KAAA,GAAQA,KAAA,IAAS;EAEjB,MAAM2Q,KAAA,GAAQ,CACZ;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EAAA;EAAA,CAED;EAED,IAAI3Q,KAAA,KAAU,GAAG;IACf3d,OAAA,CAAQ4E,IAAA,CAAK,qGAAqG;IAClH,OAAO0pB,KAAA,CAAM,CAAC;EACf;EAED,OAAOA,KAAA,CAAM3Q,KAAK;AACpB;AAIA,SAAS0K,iBAAiBpiB,KAAA,EAAO;EAC/B,MAAMpB,KAAA,GAAQoB,KAAA,CAAM/B,KAAA,CAAM,GAAG,EAAEuF,GAAA,CAAI,UAAUqa,GAAA,EAAK;IAChD,OAAOpb,UAAA,CAAWob,GAAG;EACzB,CAAG;EAED,OAAOjf,KAAA;AACT;AAEA,SAAStE,2BAA2BV,MAAA,EAAQmoB,IAAA,EAAMC,EAAA,EAAI;EACpD,IAAID,IAAA,KAAS,QAAWA,IAAA,GAAO;EAC/B,IAAIC,EAAA,KAAO,QAAWA,EAAA,GAAKpoB,MAAA,CAAOgE,UAAA;EAElC,OAAO6nB,UAAA,CAAW,IAAI5mB,UAAA,CAAWjF,MAAA,EAAQmoB,IAAA,EAAMC,EAAE,CAAC;AACpD;AAEA,SAASG,OAAOzc,CAAA,EAAGqK,CAAA,EAAG;EACpB,SAAShK,CAAA,GAAI,GAAGgO,CAAA,GAAIrO,CAAA,CAAE9E,MAAA,EAAQuX,CAAA,GAAIpI,CAAA,CAAEnP,MAAA,EAAQmF,CAAA,GAAIoS,CAAA,EAAGpS,CAAA,IAAKgO,CAAA,IAAK;IAC3DrO,CAAA,CAAEqO,CAAC,IAAIhE,CAAA,CAAEhK,CAAC;EACX;AACH;AAEA,SAASzH,MAAMoH,CAAA,EAAGqK,CAAA,EAAGgS,IAAA,EAAMC,EAAA,EAAI;EAC7B,SAASjc,CAAA,GAAIgc,IAAA,EAAMhO,CAAA,GAAI,GAAGhO,CAAA,GAAIic,EAAA,EAAIjc,CAAA,IAAKgO,CAAA,IAAK;IAC1CrO,CAAA,CAAEqO,CAAC,IAAIhE,CAAA,CAAEhK,CAAC;EACX;EAED,OAAOL,CAAA;AACT;AAGA,SAASga,OAAO4I,EAAA,EAAIxU,KAAA,EAAOyU,EAAA,EAAI;EAC7B,OAAOD,EAAA,CAAGhqB,KAAA,CAAM,GAAGwV,KAAK,EAAE8H,MAAA,CAAO2M,EAAE,EAAE3M,MAAA,CAAO0M,EAAA,CAAGhqB,KAAA,CAAMwV,KAAK,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}