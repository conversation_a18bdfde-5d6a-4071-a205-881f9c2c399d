{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Mesh, Vector4, Color, Vector2, Matrix4, Clock, ShaderMaterial, UniformsUtils, UniformsLib, RepeatWrapping } from \"three\";\nimport { Reflector } from \"./Reflector.js\";\nimport { Refractor } from \"./Refractor.js\";\nimport { version } from \"../_polyfill/constants.js\";\nconst Water2 = /* @__PURE__ */(() => {\n  const _Water2 = class extends Mesh {\n    constructor(geometry, options = {}) {\n      super(geometry);\n      this.isWater = true;\n      this.type = \"Water\";\n      const scope = this;\n      const color = options.color !== void 0 ? new Color(options.color) : new Color(16777215);\n      const textureWidth = options.textureWidth || 512;\n      const textureHeight = options.textureHeight || 512;\n      const clipBias = options.clipBias || 0;\n      const flowDirection = options.flowDirection || new Vector2(1, 0);\n      const flowSpeed = options.flowSpeed || 0.03;\n      const reflectivity = options.reflectivity || 0.02;\n      const scale = options.scale || 1;\n      const shader = options.shader || _Water2.WaterShader;\n      const encoding = options.encoding !== void 0 ? options.encoding : 3e3;\n      const flowMap = options.flowMap || void 0;\n      const normalMap0 = options.normalMap0;\n      const normalMap1 = options.normalMap1;\n      const cycle = 0.15;\n      const halfCycle = cycle * 0.5;\n      const textureMatrix = new Matrix4();\n      const clock = new Clock();\n      if (Reflector === void 0) {\n        console.error(\"THREE.Water: Required component Reflector not found.\");\n        return;\n      }\n      if (Refractor === void 0) {\n        console.error(\"THREE.Water: Required component Refractor not found.\");\n        return;\n      }\n      const reflector = new Reflector(geometry, {\n        textureWidth,\n        textureHeight,\n        clipBias,\n        encoding\n      });\n      const refractor = new Refractor(geometry, {\n        textureWidth,\n        textureHeight,\n        clipBias,\n        encoding\n      });\n      reflector.matrixAutoUpdate = false;\n      refractor.matrixAutoUpdate = false;\n      this.material = new ShaderMaterial({\n        uniforms: UniformsUtils.merge([UniformsLib[\"fog\"], shader.uniforms]),\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        transparent: true,\n        fog: true\n      });\n      if (flowMap !== void 0) {\n        this.material.defines.USE_FLOWMAP = \"\";\n        this.material.uniforms[\"tFlowMap\"] = {\n          type: \"t\",\n          value: flowMap\n        };\n      } else {\n        this.material.uniforms[\"flowDirection\"] = {\n          type: \"v2\",\n          value: flowDirection\n        };\n      }\n      normalMap0.wrapS = normalMap0.wrapT = RepeatWrapping;\n      normalMap1.wrapS = normalMap1.wrapT = RepeatWrapping;\n      this.material.uniforms[\"tReflectionMap\"].value = reflector.getRenderTarget().texture;\n      this.material.uniforms[\"tRefractionMap\"].value = refractor.getRenderTarget().texture;\n      this.material.uniforms[\"tNormalMap0\"].value = normalMap0;\n      this.material.uniforms[\"tNormalMap1\"].value = normalMap1;\n      this.material.uniforms[\"color\"].value = color;\n      this.material.uniforms[\"reflectivity\"].value = reflectivity;\n      this.material.uniforms[\"textureMatrix\"].value = textureMatrix;\n      this.material.uniforms[\"config\"].value.x = 0;\n      this.material.uniforms[\"config\"].value.y = halfCycle;\n      this.material.uniforms[\"config\"].value.z = halfCycle;\n      this.material.uniforms[\"config\"].value.w = scale;\n      function updateTextureMatrix(camera) {\n        textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n        textureMatrix.multiply(camera.projectionMatrix);\n        textureMatrix.multiply(camera.matrixWorldInverse);\n        textureMatrix.multiply(scope.matrixWorld);\n      }\n      function updateFlow() {\n        const delta = clock.getDelta();\n        const config = scope.material.uniforms[\"config\"];\n        config.value.x += flowSpeed * delta;\n        config.value.y = config.value.x + halfCycle;\n        if (config.value.x >= cycle) {\n          config.value.x = 0;\n          config.value.y = halfCycle;\n        } else if (config.value.y >= cycle) {\n          config.value.y = config.value.y - cycle;\n        }\n      }\n      this.onBeforeRender = function (renderer, scene, camera) {\n        updateTextureMatrix(camera);\n        updateFlow();\n        scope.visible = false;\n        reflector.matrixWorld.copy(scope.matrixWorld);\n        refractor.matrixWorld.copy(scope.matrixWorld);\n        reflector.onBeforeRender(renderer, scene, camera);\n        refractor.onBeforeRender(renderer, scene, camera);\n        scope.visible = true;\n      };\n    }\n  };\n  let Water22 = _Water2;\n  __publicField(Water22, \"WaterShader\", {\n    uniforms: {\n      color: {\n        value: null\n      },\n      reflectivity: {\n        value: 0\n      },\n      tReflectionMap: {\n        value: null\n      },\n      tRefractionMap: {\n        value: null\n      },\n      tNormalMap0: {\n        value: null\n      },\n      tNormalMap1: {\n        value: null\n      },\n      textureMatrix: {\n        value: null\n      },\n      config: {\n        value: /* @__PURE__ */new Vector4()\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\n\t\t#include <common>\n\t\t#include <fog_pars_vertex>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tuniform mat4 textureMatrix;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tvCoord = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n\t\t\tvToEye = cameraPosition - worldPosition.xyz;\n\n\t\t\tvec4 mvPosition =  viewMatrix * worldPosition; // used in fog_vertex\n\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t#include <logdepthbuf_vertex>\n\t\t\t#include <fog_vertex>\n\n\t\t}`),\n    fragmentShader: (/* glsl */\n    `\n\n\t\t#include <common>\n\t\t#include <fog_pars_fragment>\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tuniform sampler2D tReflectionMap;\n\t\tuniform sampler2D tRefractionMap;\n\t\tuniform sampler2D tNormalMap0;\n\t\tuniform sampler2D tNormalMap1;\n\n\t\t#ifdef USE_FLOWMAP\n\t\t\tuniform sampler2D tFlowMap;\n\t\t#else\n\t\t\tuniform vec2 flowDirection;\n\t\t#endif\n\n\t\tuniform vec3 color;\n\t\tuniform float reflectivity;\n\t\tuniform vec4 config;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tfloat flowMapOffset0 = config.x;\n\t\t\tfloat flowMapOffset1 = config.y;\n\t\t\tfloat halfCycle = config.z;\n\t\t\tfloat scale = config.w;\n\n\t\t\tvec3 toEye = normalize( vToEye );\n\n\t\t\t// determine flow direction\n\t\t\tvec2 flow;\n\t\t\t#ifdef USE_FLOWMAP\n\t\t\t\tflow = texture2D( tFlowMap, vUv ).rg * 2.0 - 1.0;\n\t\t\t#else\n\t\t\t\tflow = flowDirection;\n\t\t\t#endif\n\t\t\tflow.x *= - 1.0;\n\n\t\t\t// sample normal maps (distort uvs with flowdata)\n\t\t\tvec4 normalColor0 = texture2D( tNormalMap0, ( vUv * scale ) + flow * flowMapOffset0 );\n\t\t\tvec4 normalColor1 = texture2D( tNormalMap1, ( vUv * scale ) + flow * flowMapOffset1 );\n\n\t\t\t// linear interpolate to get the final normal color\n\t\t\tfloat flowLerp = abs( halfCycle - flowMapOffset0 ) / halfCycle;\n\t\t\tvec4 normalColor = mix( normalColor0, normalColor1, flowLerp );\n\n\t\t\t// calculate normal vector\n\t\t\tvec3 normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n\n\t\t\t// calculate the fresnel term to blend reflection and refraction maps\n\t\t\tfloat theta = max( dot( toEye, normal ), 0.0 );\n\t\t\tfloat reflectance = reflectivity + ( 1.0 - reflectivity ) * pow( ( 1.0 - theta ), 5.0 );\n\n\t\t\t// calculate final uv coords\n\t\t\tvec3 coord = vCoord.xyz / vCoord.w;\n\t\t\tvec2 uv = coord.xy + coord.z * normal.xz * 0.05;\n\n\t\t\tvec4 reflectColor = texture2D( tReflectionMap, vec2( 1.0 - uv.x, uv.y ) );\n\t\t\tvec4 refractColor = texture2D( tRefractionMap, uv );\n\n\t\t\t// multiply water color with the mix of both textures\n\t\t\tgl_FragColor = vec4( color, 1.0 ) * mix( refractColor, reflectColor, reflectance );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n\t\t\t#include <fog_fragment>\n\n\t\t}`)\n  });\n  return Water22;\n})();\nexport { Water2 };", "map": {"version": 3, "names": ["Water2", "_Water2", "<PERSON><PERSON>", "constructor", "geometry", "options", "isWater", "type", "scope", "color", "Color", "textureWidth", "textureHeight", "clipBias", "flowDirection", "Vector2", "flowSpeed", "reflectivity", "scale", "shader", "WaterShader", "encoding", "flowMap", "normalMap0", "normalMap1", "cycle", "halfCycle", "textureMatrix", "Matrix4", "clock", "Clock", "Reflector", "console", "error", "Refractor", "reflector", "refractor", "matrixAutoUpdate", "material", "ShaderMaterial", "uniforms", "UniformsUtils", "merge", "UniformsLib", "vertexShader", "fragmentShader", "transparent", "fog", "defines", "USE_FLOWMAP", "value", "wrapS", "wrapT", "RepeatWrapping", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "texture", "x", "y", "z", "w", "updateTextureMatrix", "camera", "set", "multiply", "projectionMatrix", "matrixWorldInverse", "matrixWorld", "updateFlow", "delta", "<PERSON><PERSON><PERSON><PERSON>", "config", "onBeforeRender", "renderer", "scene", "visible", "copy", "Water22", "__publicField", "tReflectionMap", "tRefractionMap", "tNormalMap0", "tNormalMap1", "Vector4", "version"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/objects/Water2.js"], "sourcesContent": ["import {\n  Clock,\n  Color,\n  Matrix4,\n  Mesh,\n  RepeatWrapping,\n  ShaderMaterial,\n  UniformsLib,\n  UniformsUtils,\n  Vector2,\n  Vector4,\n} from 'three'\nimport { Reflector } from './Reflector'\nimport { Refractor } from './Refractor'\nimport { version } from '../_polyfill/constants'\n\n/**\n * References:\n *\thttp://www.valvesoftware.com/publications/2010/siggraph2010_vlachos_waterflow.pdf\n * \thttp://graphicsrunner.blogspot.de/2010/08/water-using-flow-maps.html\n *\n */\n\nconst Water2 = /* @__PURE__ */ (() => {\n  class Water2 extends Mesh {\n    static WaterShader = {\n      uniforms: {\n        color: {\n          value: null,\n        },\n\n        reflectivity: {\n          value: 0,\n        },\n\n        tReflectionMap: {\n          value: null,\n        },\n\n        tRefractionMap: {\n          value: null,\n        },\n\n        tNormalMap0: {\n          value: null,\n        },\n\n        tNormalMap1: {\n          value: null,\n        },\n\n        textureMatrix: {\n          value: null,\n        },\n\n        config: {\n          value: /* @__PURE__ */ new Vector4(),\n        },\n      },\n\n      vertexShader: /* glsl */ `\n\n\t\t#include <common>\n\t\t#include <fog_pars_vertex>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tuniform mat4 textureMatrix;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tvCoord = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n\t\t\tvToEye = cameraPosition - worldPosition.xyz;\n\n\t\t\tvec4 mvPosition =  viewMatrix * worldPosition; // used in fog_vertex\n\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t#include <logdepthbuf_vertex>\n\t\t\t#include <fog_vertex>\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\n\t\t#include <common>\n\t\t#include <fog_pars_fragment>\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tuniform sampler2D tReflectionMap;\n\t\tuniform sampler2D tRefractionMap;\n\t\tuniform sampler2D tNormalMap0;\n\t\tuniform sampler2D tNormalMap1;\n\n\t\t#ifdef USE_FLOWMAP\n\t\t\tuniform sampler2D tFlowMap;\n\t\t#else\n\t\t\tuniform vec2 flowDirection;\n\t\t#endif\n\n\t\tuniform vec3 color;\n\t\tuniform float reflectivity;\n\t\tuniform vec4 config;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tfloat flowMapOffset0 = config.x;\n\t\t\tfloat flowMapOffset1 = config.y;\n\t\t\tfloat halfCycle = config.z;\n\t\t\tfloat scale = config.w;\n\n\t\t\tvec3 toEye = normalize( vToEye );\n\n\t\t\t// determine flow direction\n\t\t\tvec2 flow;\n\t\t\t#ifdef USE_FLOWMAP\n\t\t\t\tflow = texture2D( tFlowMap, vUv ).rg * 2.0 - 1.0;\n\t\t\t#else\n\t\t\t\tflow = flowDirection;\n\t\t\t#endif\n\t\t\tflow.x *= - 1.0;\n\n\t\t\t// sample normal maps (distort uvs with flowdata)\n\t\t\tvec4 normalColor0 = texture2D( tNormalMap0, ( vUv * scale ) + flow * flowMapOffset0 );\n\t\t\tvec4 normalColor1 = texture2D( tNormalMap1, ( vUv * scale ) + flow * flowMapOffset1 );\n\n\t\t\t// linear interpolate to get the final normal color\n\t\t\tfloat flowLerp = abs( halfCycle - flowMapOffset0 ) / halfCycle;\n\t\t\tvec4 normalColor = mix( normalColor0, normalColor1, flowLerp );\n\n\t\t\t// calculate normal vector\n\t\t\tvec3 normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n\n\t\t\t// calculate the fresnel term to blend reflection and refraction maps\n\t\t\tfloat theta = max( dot( toEye, normal ), 0.0 );\n\t\t\tfloat reflectance = reflectivity + ( 1.0 - reflectivity ) * pow( ( 1.0 - theta ), 5.0 );\n\n\t\t\t// calculate final uv coords\n\t\t\tvec3 coord = vCoord.xyz / vCoord.w;\n\t\t\tvec2 uv = coord.xy + coord.z * normal.xz * 0.05;\n\n\t\t\tvec4 reflectColor = texture2D( tReflectionMap, vec2( 1.0 - uv.x, uv.y ) );\n\t\t\tvec4 refractColor = texture2D( tRefractionMap, uv );\n\n\t\t\t// multiply water color with the mix of both textures\n\t\t\tgl_FragColor = vec4( color, 1.0 ) * mix( refractColor, reflectColor, reflectance );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t#include <fog_fragment>\n\n\t\t}`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isWater = true\n\n      this.type = 'Water'\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0xffffff)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const flowDirection = options.flowDirection || new Vector2(1, 0)\n      const flowSpeed = options.flowSpeed || 0.03\n      const reflectivity = options.reflectivity || 0.02\n      const scale = options.scale || 1\n      const shader = options.shader || Water2.WaterShader\n      const encoding = options.encoding !== undefined ? options.encoding : 3000\n\n      const flowMap = options.flowMap || undefined\n      const normalMap0 = options.normalMap0\n      const normalMap1 = options.normalMap1\n\n      const cycle = 0.15 // a cycle of a flow map phase\n      const halfCycle = cycle * 0.5\n      const textureMatrix = new Matrix4()\n      const clock = new Clock()\n\n      // internal components\n\n      if (Reflector === undefined) {\n        console.error('THREE.Water: Required component Reflector not found.')\n        return\n      }\n\n      if (Refractor === undefined) {\n        console.error('THREE.Water: Required component Refractor not found.')\n        return\n      }\n\n      const reflector = new Reflector(geometry, {\n        textureWidth: textureWidth,\n        textureHeight: textureHeight,\n        clipBias: clipBias,\n        encoding: encoding,\n      })\n\n      const refractor = new Refractor(geometry, {\n        textureWidth: textureWidth,\n        textureHeight: textureHeight,\n        clipBias: clipBias,\n        encoding: encoding,\n      })\n\n      reflector.matrixAutoUpdate = false\n      refractor.matrixAutoUpdate = false\n\n      // material\n\n      this.material = new ShaderMaterial({\n        uniforms: UniformsUtils.merge([UniformsLib['fog'], shader.uniforms]),\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        transparent: true,\n        fog: true,\n      })\n\n      if (flowMap !== undefined) {\n        this.material.defines.USE_FLOWMAP = ''\n        this.material.uniforms['tFlowMap'] = {\n          type: 't',\n          value: flowMap,\n        }\n      } else {\n        this.material.uniforms['flowDirection'] = {\n          type: 'v2',\n          value: flowDirection,\n        }\n      }\n\n      // maps\n\n      normalMap0.wrapS = normalMap0.wrapT = RepeatWrapping\n      normalMap1.wrapS = normalMap1.wrapT = RepeatWrapping\n\n      this.material.uniforms['tReflectionMap'].value = reflector.getRenderTarget().texture\n      this.material.uniforms['tRefractionMap'].value = refractor.getRenderTarget().texture\n      this.material.uniforms['tNormalMap0'].value = normalMap0\n      this.material.uniforms['tNormalMap1'].value = normalMap1\n\n      // water\n\n      this.material.uniforms['color'].value = color\n      this.material.uniforms['reflectivity'].value = reflectivity\n      this.material.uniforms['textureMatrix'].value = textureMatrix\n\n      // inital values\n\n      this.material.uniforms['config'].value.x = 0 // flowMapOffset0\n      this.material.uniforms['config'].value.y = halfCycle // flowMapOffset1\n      this.material.uniforms['config'].value.z = halfCycle // halfCycle\n      this.material.uniforms['config'].value.w = scale // scale\n\n      // functions\n\n      function updateTextureMatrix(camera) {\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n\n        textureMatrix.multiply(camera.projectionMatrix)\n        textureMatrix.multiply(camera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n      }\n\n      function updateFlow() {\n        const delta = clock.getDelta()\n        const config = scope.material.uniforms['config']\n\n        config.value.x += flowSpeed * delta // flowMapOffset0\n        config.value.y = config.value.x + halfCycle // flowMapOffset1\n\n        // Important: The distance between offsets should be always the value of \"halfCycle\".\n        // Moreover, both offsets should be in the range of [ 0, cycle ].\n        // This approach ensures a smooth water flow and avoids \"reset\" effects.\n\n        if (config.value.x >= cycle) {\n          config.value.x = 0\n          config.value.y = halfCycle\n        } else if (config.value.y >= cycle) {\n          config.value.y = config.value.y - cycle\n        }\n      }\n\n      //\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        updateTextureMatrix(camera)\n        updateFlow()\n\n        scope.visible = false\n\n        reflector.matrixWorld.copy(scope.matrixWorld)\n        refractor.matrixWorld.copy(scope.matrixWorld)\n\n        reflector.onBeforeRender(renderer, scene, camera)\n        refractor.onBeforeRender(renderer, scene, camera)\n\n        scope.visible = true\n      }\n    }\n  }\n\n  return Water2\n})()\n\nexport { Water2 }\n"], "mappings": ";;;;;;;;;;;;;;;AAuBK,MAACA,MAAA,GAA0B,sBAAM;EACpC,MAAMC,OAAA,GAAN,cAAqBC,IAAA,CAAK;IA6IxBC,YAAYC,QAAA,EAAUC,OAAA,GAAU,IAAI;MAClC,MAAMD,QAAQ;MAEd,KAAKE,OAAA,GAAU;MAEf,KAAKC,IAAA,GAAO;MAEZ,MAAMC,KAAA,GAAQ;MAEd,MAAMC,KAAA,GAAQJ,OAAA,CAAQI,KAAA,KAAU,SAAY,IAAIC,KAAA,CAAML,OAAA,CAAQI,KAAK,IAAI,IAAIC,KAAA,CAAM,QAAQ;MACzF,MAAMC,YAAA,GAAeN,OAAA,CAAQM,YAAA,IAAgB;MAC7C,MAAMC,aAAA,GAAgBP,OAAA,CAAQO,aAAA,IAAiB;MAC/C,MAAMC,QAAA,GAAWR,OAAA,CAAQQ,QAAA,IAAY;MACrC,MAAMC,aAAA,GAAgBT,OAAA,CAAQS,aAAA,IAAiB,IAAIC,OAAA,CAAQ,GAAG,CAAC;MAC/D,MAAMC,SAAA,GAAYX,OAAA,CAAQW,SAAA,IAAa;MACvC,MAAMC,YAAA,GAAeZ,OAAA,CAAQY,YAAA,IAAgB;MAC7C,MAAMC,KAAA,GAAQb,OAAA,CAAQa,KAAA,IAAS;MAC/B,MAAMC,MAAA,GAASd,OAAA,CAAQc,MAAA,IAAUlB,OAAA,CAAOmB,WAAA;MACxC,MAAMC,QAAA,GAAWhB,OAAA,CAAQgB,QAAA,KAAa,SAAYhB,OAAA,CAAQgB,QAAA,GAAW;MAErE,MAAMC,OAAA,GAAUjB,OAAA,CAAQiB,OAAA,IAAW;MACnC,MAAMC,UAAA,GAAalB,OAAA,CAAQkB,UAAA;MAC3B,MAAMC,UAAA,GAAanB,OAAA,CAAQmB,UAAA;MAE3B,MAAMC,KAAA,GAAQ;MACd,MAAMC,SAAA,GAAYD,KAAA,GAAQ;MAC1B,MAAME,aAAA,GAAgB,IAAIC,OAAA,CAAS;MACnC,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAO;MAIzB,IAAIC,SAAA,KAAc,QAAW;QAC3BC,OAAA,CAAQC,KAAA,CAAM,sDAAsD;QACpE;MACD;MAED,IAAIC,SAAA,KAAc,QAAW;QAC3BF,OAAA,CAAQC,KAAA,CAAM,sDAAsD;QACpE;MACD;MAED,MAAME,SAAA,GAAY,IAAIJ,SAAA,CAAU3B,QAAA,EAAU;QACxCO,YAAA;QACAC,aAAA;QACAC,QAAA;QACAQ;MACR,CAAO;MAED,MAAMe,SAAA,GAAY,IAAIF,SAAA,CAAU9B,QAAA,EAAU;QACxCO,YAAA;QACAC,aAAA;QACAC,QAAA;QACAQ;MACR,CAAO;MAEDc,SAAA,CAAUE,gBAAA,GAAmB;MAC7BD,SAAA,CAAUC,gBAAA,GAAmB;MAI7B,KAAKC,QAAA,GAAW,IAAIC,cAAA,CAAe;QACjCC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,CAACC,WAAA,CAAY,KAAK,GAAGxB,MAAA,CAAOqB,QAAQ,CAAC;QACnEI,YAAA,EAAczB,MAAA,CAAOyB,YAAA;QACrBC,cAAA,EAAgB1B,MAAA,CAAO0B,cAAA;QACvBC,WAAA,EAAa;QACbC,GAAA,EAAK;MACb,CAAO;MAED,IAAIzB,OAAA,KAAY,QAAW;QACzB,KAAKgB,QAAA,CAASU,OAAA,CAAQC,WAAA,GAAc;QACpC,KAAKX,QAAA,CAASE,QAAA,CAAS,UAAU,IAAI;UACnCjC,IAAA,EAAM;UACN2C,KAAA,EAAO5B;QACR;MACT,OAAa;QACL,KAAKgB,QAAA,CAASE,QAAA,CAAS,eAAe,IAAI;UACxCjC,IAAA,EAAM;UACN2C,KAAA,EAAOpC;QACR;MACF;MAIDS,UAAA,CAAW4B,KAAA,GAAQ5B,UAAA,CAAW6B,KAAA,GAAQC,cAAA;MACtC7B,UAAA,CAAW2B,KAAA,GAAQ3B,UAAA,CAAW4B,KAAA,GAAQC,cAAA;MAEtC,KAAKf,QAAA,CAASE,QAAA,CAAS,gBAAgB,EAAEU,KAAA,GAAQf,SAAA,CAAUmB,eAAA,CAAe,EAAGC,OAAA;MAC7E,KAAKjB,QAAA,CAASE,QAAA,CAAS,gBAAgB,EAAEU,KAAA,GAAQd,SAAA,CAAUkB,eAAA,CAAe,EAAGC,OAAA;MAC7E,KAAKjB,QAAA,CAASE,QAAA,CAAS,aAAa,EAAEU,KAAA,GAAQ3B,UAAA;MAC9C,KAAKe,QAAA,CAASE,QAAA,CAAS,aAAa,EAAEU,KAAA,GAAQ1B,UAAA;MAI9C,KAAKc,QAAA,CAASE,QAAA,CAAS,OAAO,EAAEU,KAAA,GAAQzC,KAAA;MACxC,KAAK6B,QAAA,CAASE,QAAA,CAAS,cAAc,EAAEU,KAAA,GAAQjC,YAAA;MAC/C,KAAKqB,QAAA,CAASE,QAAA,CAAS,eAAe,EAAEU,KAAA,GAAQvB,aAAA;MAIhD,KAAKW,QAAA,CAASE,QAAA,CAAS,QAAQ,EAAEU,KAAA,CAAMM,CAAA,GAAI;MAC3C,KAAKlB,QAAA,CAASE,QAAA,CAAS,QAAQ,EAAEU,KAAA,CAAMO,CAAA,GAAI/B,SAAA;MAC3C,KAAKY,QAAA,CAASE,QAAA,CAAS,QAAQ,EAAEU,KAAA,CAAMQ,CAAA,GAAIhC,SAAA;MAC3C,KAAKY,QAAA,CAASE,QAAA,CAAS,QAAQ,EAAEU,KAAA,CAAMS,CAAA,GAAIzC,KAAA;MAI3C,SAAS0C,oBAAoBC,MAAA,EAAQ;QACnClC,aAAA,CAAcmC,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;QAEhGnC,aAAA,CAAcoC,QAAA,CAASF,MAAA,CAAOG,gBAAgB;QAC9CrC,aAAA,CAAcoC,QAAA,CAASF,MAAA,CAAOI,kBAAkB;QAChDtC,aAAA,CAAcoC,QAAA,CAASvD,KAAA,CAAM0D,WAAW;MACzC;MAED,SAASC,WAAA,EAAa;QACpB,MAAMC,KAAA,GAAQvC,KAAA,CAAMwC,QAAA,CAAU;QAC9B,MAAMC,MAAA,GAAS9D,KAAA,CAAM8B,QAAA,CAASE,QAAA,CAAS,QAAQ;QAE/C8B,MAAA,CAAOpB,KAAA,CAAMM,CAAA,IAAKxC,SAAA,GAAYoD,KAAA;QAC9BE,MAAA,CAAOpB,KAAA,CAAMO,CAAA,GAAIa,MAAA,CAAOpB,KAAA,CAAMM,CAAA,GAAI9B,SAAA;QAMlC,IAAI4C,MAAA,CAAOpB,KAAA,CAAMM,CAAA,IAAK/B,KAAA,EAAO;UAC3B6C,MAAA,CAAOpB,KAAA,CAAMM,CAAA,GAAI;UACjBc,MAAA,CAAOpB,KAAA,CAAMO,CAAA,GAAI/B,SAAA;QAClB,WAAU4C,MAAA,CAAOpB,KAAA,CAAMO,CAAA,IAAKhC,KAAA,EAAO;UAClC6C,MAAA,CAAOpB,KAAA,CAAMO,CAAA,GAAIa,MAAA,CAAOpB,KAAA,CAAMO,CAAA,GAAIhC,KAAA;QACnC;MACF;MAID,KAAK8C,cAAA,GAAiB,UAAUC,QAAA,EAAUC,KAAA,EAAOZ,MAAA,EAAQ;QACvDD,mBAAA,CAAoBC,MAAM;QAC1BM,UAAA,CAAY;QAEZ3D,KAAA,CAAMkE,OAAA,GAAU;QAEhBvC,SAAA,CAAU+B,WAAA,CAAYS,IAAA,CAAKnE,KAAA,CAAM0D,WAAW;QAC5C9B,SAAA,CAAU8B,WAAA,CAAYS,IAAA,CAAKnE,KAAA,CAAM0D,WAAW;QAE5C/B,SAAA,CAAUoC,cAAA,CAAeC,QAAA,EAAUC,KAAA,EAAOZ,MAAM;QAChDzB,SAAA,CAAUmC,cAAA,CAAeC,QAAA,EAAUC,KAAA,EAAOZ,MAAM;QAEhDrD,KAAA,CAAMkE,OAAA,GAAU;MACjB;IACF;EACF;EAnSD,IAAME,OAAA,GAAN3E,OAAA;EACE4E,aAAA,CADID,OAAA,EACG,eAAc;IACnBpC,QAAA,EAAU;MACR/B,KAAA,EAAO;QACLyC,KAAA,EAAO;MACR;MAEDjC,YAAA,EAAc;QACZiC,KAAA,EAAO;MACR;MAED4B,cAAA,EAAgB;QACd5B,KAAA,EAAO;MACR;MAED6B,cAAA,EAAgB;QACd7B,KAAA,EAAO;MACR;MAED8B,WAAA,EAAa;QACX9B,KAAA,EAAO;MACR;MAED+B,WAAA,EAAa;QACX/B,KAAA,EAAO;MACR;MAEDvB,aAAA,EAAe;QACbuB,KAAA,EAAO;MACR;MAEDoB,MAAA,EAAQ;QACNpB,KAAA,EAAuB,mBAAIgC,OAAA,CAAS;MACrC;IACF;IAEDtC,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IA4BzBC,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAuElBsC,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;EAInD;EA0JH,OAAOP,OAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}