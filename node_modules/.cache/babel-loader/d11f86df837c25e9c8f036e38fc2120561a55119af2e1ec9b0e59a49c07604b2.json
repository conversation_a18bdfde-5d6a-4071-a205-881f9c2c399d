{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, use<PERSON>rame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\nimport { useHelper } from './Helper.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { Edges } from './Edges.js';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\nfunction createNormalMaterial(side = THREE.FrontSide) {\n  const viewMatrix = {\n    value: new THREE.Matrix4()\n  };\n  return Object.assign(new THREE.MeshNormalMaterial({\n    side\n  }), {\n    viewMatrix,\n    onBeforeCompile: shader => {\n      shader.uniforms.viewMatrix = viewMatrix;\n      shader.fragmentShader = `vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\\n` + shader.fragmentShader.replace('#include <normal_fragment_maps>', `#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\\n`);\n    }\n  });\n}\nconst CausticsProjectionMaterial = /* @__PURE__ */shaderMaterial({\n  causticsTexture: null,\n  causticsTextureB: null,\n  color: /* @__PURE__ */new THREE.Color(),\n  lightProjMatrix: /* @__PURE__ */new THREE.Matrix4(),\n  lightViewMatrix: /* @__PURE__ */new THREE.Matrix4()\n}, `varying vec3 vWorldPosition;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }`, `varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture;\n  uniform sampler2D causticsTextureB;\n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics\n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5;\n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst CausticsMaterial = /* @__PURE__ */shaderMaterial({\n  cameraMatrixWorld: /* @__PURE__ */new THREE.Matrix4(),\n  cameraProjectionMatrixInv: /* @__PURE__ */new THREE.Matrix4(),\n  normalTexture: null,\n  depthTexture: null,\n  lightDir: /* @__PURE__ */new THREE.Vector3(0, 1, 0),\n  lightPlaneNormal: /* @__PURE__ */new THREE.Vector3(0, 1, 0),\n  lightPlaneConstant: 0,\n  near: 0.1,\n  far: 100,\n  modelMatrix: /* @__PURE__ */new THREE.Matrix4(),\n  worldRadius: 1 / 40,\n  ior: 1.1,\n  bounces: 0,\n  resolution: 1024,\n  size: 10,\n  intensity: 0.5\n}, /* glsl */`\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }`, /* glsl */`\n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }\n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }`);\nconst NORMALPROPS = {\n  depth: true,\n  minFilter: THREE.LinearFilter,\n  magFilter: THREE.LinearFilter,\n  type: THREE.UnsignedByteType\n};\nconst CAUSTICPROPS = {\n  minFilter: THREE.LinearMipmapLinearFilter,\n  magFilter: THREE.LinearFilter,\n  type: THREE.FloatType,\n  generateMipmaps: true\n};\nconst Caustics = /* @__PURE__ */React.forwardRef(({\n  debug,\n  children,\n  frames = 1,\n  ior = 1.1,\n  color = 'white',\n  causticsOnly = false,\n  backside = false,\n  backsideIOR = 1.1,\n  worldRadius = 0.3125,\n  intensity = 0.05,\n  resolution = 2024,\n  lightSource = [5, 5, 5],\n  ...props\n}, fref) => {\n  extend({\n    CausticsProjectionMaterial\n  });\n  const ref = React.useRef(null);\n  const camera = React.useRef(null);\n  const scene = React.useRef(null);\n  const plane = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const helper = useHelper(debug && camera, THREE.CameraHelper);\n\n  // Buffers for front and back faces\n  const normalTarget = useFBO(resolution, resolution, NORMALPROPS);\n  const normalTargetB = useFBO(resolution, resolution, NORMALPROPS);\n  const causticsTarget = useFBO(resolution, resolution, CAUSTICPROPS);\n  const causticsTargetB = useFBO(resolution, resolution, CAUSTICPROPS);\n  // Normal materials for front and back faces\n  const [normalMat] = React.useState(() => createNormalMaterial());\n  const [normalMatB] = React.useState(() => createNormalMaterial(THREE.BackSide));\n  // The quad that catches the caustics\n  const [causticsMaterial] = React.useState(() => new CausticsMaterial());\n  const [causticsQuad] = React.useState(() => new FullScreenQuad(causticsMaterial));\n  React.useLayoutEffect(() => {\n    ref.current.updateWorldMatrix(false, true);\n  });\n  let count = 0;\n  const v = new THREE.Vector3();\n  const lpF = new THREE.Frustum();\n  const lpM = new THREE.Matrix4();\n  const lpP = new THREE.Plane();\n  const lightDir = new THREE.Vector3();\n  const lightDirInv = new THREE.Vector3();\n  const bounds = new THREE.Box3();\n  const focusPos = new THREE.Vector3();\n  const boundsVertices = [];\n  const worldVerts = [];\n  const projectedVerts = [];\n  const lightDirs = [];\n  const cameraPos = new THREE.Vector3();\n  for (let i = 0; i < 8; i++) {\n    boundsVertices.push(new THREE.Vector3());\n    worldVerts.push(new THREE.Vector3());\n    projectedVerts.push(new THREE.Vector3());\n    lightDirs.push(new THREE.Vector3());\n  }\n  useFrame(() => {\n    if (frames === Infinity || count++ < frames) {\n      var _scene$current$parent, _helper$current;\n      if (Array.isArray(lightSource)) lightDir.fromArray(lightSource).normalize();else lightDir.copy(ref.current.worldToLocal(lightSource.current.getWorldPosition(v)).normalize());\n      lightDirInv.copy(lightDir).multiplyScalar(-1);\n      (_scene$current$parent = scene.current.parent) == null || _scene$current$parent.matrixWorld.identity();\n      bounds.setFromObject(scene.current, true);\n      boundsVertices[0].set(bounds.min.x, bounds.min.y, bounds.min.z);\n      boundsVertices[1].set(bounds.min.x, bounds.min.y, bounds.max.z);\n      boundsVertices[2].set(bounds.min.x, bounds.max.y, bounds.min.z);\n      boundsVertices[3].set(bounds.min.x, bounds.max.y, bounds.max.z);\n      boundsVertices[4].set(bounds.max.x, bounds.min.y, bounds.min.z);\n      boundsVertices[5].set(bounds.max.x, bounds.min.y, bounds.max.z);\n      boundsVertices[6].set(bounds.max.x, bounds.max.y, bounds.min.z);\n      boundsVertices[7].set(bounds.max.x, bounds.max.y, bounds.max.z);\n      for (let i = 0; i < 8; i++) {\n        worldVerts[i].copy(boundsVertices[i]);\n      }\n      bounds.getCenter(focusPos);\n      boundsVertices.map(v => v.sub(focusPos));\n      const lightPlane = lpP.set(lightDirInv, 0);\n      boundsVertices.map((v, i) => lightPlane.projectPoint(v, projectedVerts[i]));\n      const centralVert = projectedVerts.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(projectedVerts.length);\n      const radius = projectedVerts.map(v => v.distanceTo(centralVert)).reduce((a, b) => Math.max(a, b));\n      const dirLength = boundsVertices.map(x => x.dot(lightDir)).reduce((a, b) => Math.max(a, b));\n      // Shadows\n      camera.current.position.copy(cameraPos.copy(lightDir).multiplyScalar(dirLength).add(focusPos));\n      camera.current.lookAt(scene.current.localToWorld(focusPos));\n      const dirMatrix = lpM.lookAt(camera.current.position, focusPos, v.set(0, 1, 0));\n      camera.current.left = -radius;\n      camera.current.right = radius;\n      camera.current.top = radius;\n      camera.current.bottom = -radius;\n      const yOffset = v.set(0, radius, 0).applyMatrix4(dirMatrix);\n      const yTime = (camera.current.position.y + yOffset.y) / lightDir.y;\n      camera.current.near = 0.1;\n      camera.current.far = yTime;\n      camera.current.updateProjectionMatrix();\n      camera.current.updateMatrixWorld();\n\n      // Now find size of ground plane\n      const groundProjectedCoords = worldVerts.map((v, i) => v.add(lightDirs[i].copy(lightDir).multiplyScalar(-v.y / lightDir.y)));\n      const centerPos = groundProjectedCoords.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(groundProjectedCoords.length);\n      const maxSize = 2 * groundProjectedCoords.map(v => Math.hypot(v.x - centerPos.x, v.z - centerPos.z)).reduce((a, b) => Math.max(a, b));\n      plane.current.scale.setScalar(maxSize);\n      plane.current.position.copy(centerPos);\n      if (debug) (_helper$current = helper.current) == null || _helper$current.update();\n\n      // Inject uniforms\n      normalMatB.viewMatrix.value = normalMat.viewMatrix.value = camera.current.matrixWorldInverse;\n      const dirLightNearPlane = lpF.setFromProjectionMatrix(lpM.multiplyMatrices(camera.current.projectionMatrix, camera.current.matrixWorldInverse)).planes[4];\n      causticsMaterial.cameraMatrixWorld = camera.current.matrixWorld;\n      causticsMaterial.cameraProjectionMatrixInv = camera.current.projectionMatrixInverse;\n      causticsMaterial.lightDir = lightDirInv;\n      causticsMaterial.lightPlaneNormal = dirLightNearPlane.normal;\n      causticsMaterial.lightPlaneConstant = dirLightNearPlane.constant;\n      causticsMaterial.near = camera.current.near;\n      causticsMaterial.far = camera.current.far;\n      causticsMaterial.resolution = resolution;\n      causticsMaterial.size = radius;\n      causticsMaterial.intensity = intensity;\n      causticsMaterial.worldRadius = worldRadius;\n\n      // Switch the scene on\n      scene.current.visible = true;\n\n      // Render front face normals\n      gl.setRenderTarget(normalTarget);\n      gl.clear();\n      scene.current.overrideMaterial = normalMat;\n      gl.render(scene.current, camera.current);\n\n      // Render back face normals, if enabled\n      gl.setRenderTarget(normalTargetB);\n      gl.clear();\n      if (backside) {\n        scene.current.overrideMaterial = normalMatB;\n        gl.render(scene.current, camera.current);\n      }\n\n      // Remove the override material\n      scene.current.overrideMaterial = null;\n\n      // Render front face caustics\n      causticsMaterial.ior = ior;\n      plane.current.material.lightProjMatrix = camera.current.projectionMatrix;\n      plane.current.material.lightViewMatrix = camera.current.matrixWorldInverse;\n      causticsMaterial.normalTexture = normalTarget.texture;\n      causticsMaterial.depthTexture = normalTarget.depthTexture;\n      gl.setRenderTarget(causticsTarget);\n      gl.clear();\n      causticsQuad.render(gl);\n\n      // Render back face caustics, if enabled\n      causticsMaterial.ior = backsideIOR;\n      causticsMaterial.normalTexture = normalTargetB.texture;\n      causticsMaterial.depthTexture = normalTargetB.depthTexture;\n      gl.setRenderTarget(causticsTargetB);\n      gl.clear();\n      if (backside) causticsQuad.render(gl);\n\n      // Reset render target\n      gl.setRenderTarget(null);\n\n      // Switch the scene off if caustics is all that's wanted\n      if (causticsOnly) scene.current.visible = false;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"scene\", {\n    ref: scene\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: camera,\n    up: [0, 1, 0]\n  }), children), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: 2,\n    ref: plane,\n    \"rotation-x\": -Math.PI / 2\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"causticsProjectionMaterial\", {\n    transparent: true,\n    color: color,\n    causticsTexture: causticsTarget.texture,\n    causticsTextureB: causticsTargetB.texture,\n    blending: THREE.CustomBlending,\n    blendSrc: THREE.OneFactor,\n    blendDst: THREE.SrcAlphaFactor,\n    depthWrite: false\n  }), debug && /*#__PURE__*/React.createElement(Edges, null, /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: \"#ffff00\",\n    toneMapped: false\n  }))));\n});\nexport { Caustics };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useFrame", "useFBO", "useHelper", "shaderMaterial", "<PERSON>s", "FullScreenQuad", "version", "createNormalMaterial", "side", "FrontSide", "viewMatrix", "value", "Matrix4", "Object", "assign", "MeshNormalMaterial", "onBeforeCompile", "shader", "uniforms", "fragmentShader", "replace", "CausticsProjectionMaterial", "causticsTexture", "causticsTextureB", "color", "Color", "lightProjMatrix", "lightViewMatrix", "CausticsMaterial", "cameraMatrixWorld", "cameraProjectionMatrixInv", "normalTexture", "depthTexture", "lightDir", "Vector3", "lightPlaneNormal", "lightPlaneConstant", "near", "far", "modelMatrix", "worldRadius", "ior", "bounces", "resolution", "size", "intensity", "NORMALPROPS", "depth", "minFilter", "LinearFilter", "magFilter", "type", "UnsignedByteType", "CAUSTICPROPS", "LinearMipmapLinearFilter", "FloatType", "generateMipmaps", "Caustics", "forwardRef", "debug", "children", "frames", "causticsOnly", "backside", "backsideIOR", "lightSource", "props", "fref", "ref", "useRef", "camera", "scene", "plane", "gl", "state", "helper", "CameraHelper", "normalTarget", "normalTargetB", "causticsTarget", "causticsTargetB", "normalMat", "useState", "normalMatB", "BackSide", "causticsMaterial", "causticsQuad", "useLayoutEffect", "current", "updateWorldMatrix", "count", "v", "lpF", "Frustum", "lpM", "lpP", "Plane", "lightDirInv", "bounds", "Box3", "focusPos", "boundsVertices", "worldVerts", "<PERSON><PERSON><PERSON><PERSON>", "lightDirs", "cameraPos", "i", "push", "Infinity", "_scene$current$parent", "_helper$current", "Array", "isArray", "fromArray", "normalize", "copy", "worldToLocal", "getWorldPosition", "multiplyScalar", "parent", "matrixWorld", "identity", "setFromObject", "set", "min", "x", "y", "z", "max", "getCenter", "map", "sub", "lightPlane", "projectPoint", "centralVert", "reduce", "a", "b", "add", "divideScalar", "length", "radius", "distanceTo", "Math", "<PERSON><PERSON><PERSON><PERSON>", "dot", "position", "lookAt", "localToWorld", "dirMatrix", "left", "right", "top", "bottom", "yOffset", "applyMatrix4", "yTime", "updateProjectionMatrix", "updateMatrixWorld", "groundProjectedCoords", "centerPos", "maxSize", "hypot", "scale", "setScalar", "update", "matrixWorldInverse", "dirLightNearPlane", "setFromProjectionMatrix", "multiplyMatrices", "projectionMatrix", "planes", "projectionMatrixInverse", "normal", "constant", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "overrideMaterial", "render", "material", "texture", "useImperativeHandle", "createElement", "up", "renderOrder", "PI", "transparent", "blending", "CustomBlending", "blendSrc", "OneFactor", "blendDst", "SrcAlphaFactor", "depthWrite", "toneMapped"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Caustics.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, use<PERSON>rame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\nimport { useHelper } from './Helper.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { Edges } from './Edges.js';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\n\nfunction createNormalMaterial(side = THREE.FrontSide) {\n  const viewMatrix = {\n    value: new THREE.Matrix4()\n  };\n  return Object.assign(new THREE.MeshNormalMaterial({\n    side\n  }), {\n    viewMatrix,\n    onBeforeCompile: shader => {\n      shader.uniforms.viewMatrix = viewMatrix;\n      shader.fragmentShader = `vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\\n` + shader.fragmentShader.replace('#include <normal_fragment_maps>', `#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\\n`);\n    }\n  });\n}\nconst CausticsProjectionMaterial = /* @__PURE__ */shaderMaterial({\n  causticsTexture: null,\n  causticsTextureB: null,\n  color: /* @__PURE__ */new THREE.Color(),\n  lightProjMatrix: /* @__PURE__ */new THREE.Matrix4(),\n  lightViewMatrix: /* @__PURE__ */new THREE.Matrix4()\n}, `varying vec3 vWorldPosition;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }`, `varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture;\n  uniform sampler2D causticsTextureB;\n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics\n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5;\n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst CausticsMaterial = /* @__PURE__ */shaderMaterial({\n  cameraMatrixWorld: /* @__PURE__ */new THREE.Matrix4(),\n  cameraProjectionMatrixInv: /* @__PURE__ */new THREE.Matrix4(),\n  normalTexture: null,\n  depthTexture: null,\n  lightDir: /* @__PURE__ */new THREE.Vector3(0, 1, 0),\n  lightPlaneNormal: /* @__PURE__ */new THREE.Vector3(0, 1, 0),\n  lightPlaneConstant: 0,\n  near: 0.1,\n  far: 100,\n  modelMatrix: /* @__PURE__ */new THREE.Matrix4(),\n  worldRadius: 1 / 40,\n  ior: 1.1,\n  bounces: 0,\n  resolution: 1024,\n  size: 10,\n  intensity: 0.5\n}, /* glsl */`\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }`, /* glsl */`\n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }\n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }`);\nconst NORMALPROPS = {\n  depth: true,\n  minFilter: THREE.LinearFilter,\n  magFilter: THREE.LinearFilter,\n  type: THREE.UnsignedByteType\n};\nconst CAUSTICPROPS = {\n  minFilter: THREE.LinearMipmapLinearFilter,\n  magFilter: THREE.LinearFilter,\n  type: THREE.FloatType,\n  generateMipmaps: true\n};\nconst Caustics = /* @__PURE__ */React.forwardRef(({\n  debug,\n  children,\n  frames = 1,\n  ior = 1.1,\n  color = 'white',\n  causticsOnly = false,\n  backside = false,\n  backsideIOR = 1.1,\n  worldRadius = 0.3125,\n  intensity = 0.05,\n  resolution = 2024,\n  lightSource = [5, 5, 5],\n  ...props\n}, fref) => {\n  extend({\n    CausticsProjectionMaterial\n  });\n  const ref = React.useRef(null);\n  const camera = React.useRef(null);\n  const scene = React.useRef(null);\n  const plane = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const helper = useHelper(debug && camera, THREE.CameraHelper);\n\n  // Buffers for front and back faces\n  const normalTarget = useFBO(resolution, resolution, NORMALPROPS);\n  const normalTargetB = useFBO(resolution, resolution, NORMALPROPS);\n  const causticsTarget = useFBO(resolution, resolution, CAUSTICPROPS);\n  const causticsTargetB = useFBO(resolution, resolution, CAUSTICPROPS);\n  // Normal materials for front and back faces\n  const [normalMat] = React.useState(() => createNormalMaterial());\n  const [normalMatB] = React.useState(() => createNormalMaterial(THREE.BackSide));\n  // The quad that catches the caustics\n  const [causticsMaterial] = React.useState(() => new CausticsMaterial());\n  const [causticsQuad] = React.useState(() => new FullScreenQuad(causticsMaterial));\n  React.useLayoutEffect(() => {\n    ref.current.updateWorldMatrix(false, true);\n  });\n  let count = 0;\n  const v = new THREE.Vector3();\n  const lpF = new THREE.Frustum();\n  const lpM = new THREE.Matrix4();\n  const lpP = new THREE.Plane();\n  const lightDir = new THREE.Vector3();\n  const lightDirInv = new THREE.Vector3();\n  const bounds = new THREE.Box3();\n  const focusPos = new THREE.Vector3();\n  const boundsVertices = [];\n  const worldVerts = [];\n  const projectedVerts = [];\n  const lightDirs = [];\n  const cameraPos = new THREE.Vector3();\n  for (let i = 0; i < 8; i++) {\n    boundsVertices.push(new THREE.Vector3());\n    worldVerts.push(new THREE.Vector3());\n    projectedVerts.push(new THREE.Vector3());\n    lightDirs.push(new THREE.Vector3());\n  }\n  useFrame(() => {\n    if (frames === Infinity || count++ < frames) {\n      var _scene$current$parent, _helper$current;\n      if (Array.isArray(lightSource)) lightDir.fromArray(lightSource).normalize();else lightDir.copy(ref.current.worldToLocal(lightSource.current.getWorldPosition(v)).normalize());\n      lightDirInv.copy(lightDir).multiplyScalar(-1);\n      (_scene$current$parent = scene.current.parent) == null || _scene$current$parent.matrixWorld.identity();\n      bounds.setFromObject(scene.current, true);\n      boundsVertices[0].set(bounds.min.x, bounds.min.y, bounds.min.z);\n      boundsVertices[1].set(bounds.min.x, bounds.min.y, bounds.max.z);\n      boundsVertices[2].set(bounds.min.x, bounds.max.y, bounds.min.z);\n      boundsVertices[3].set(bounds.min.x, bounds.max.y, bounds.max.z);\n      boundsVertices[4].set(bounds.max.x, bounds.min.y, bounds.min.z);\n      boundsVertices[5].set(bounds.max.x, bounds.min.y, bounds.max.z);\n      boundsVertices[6].set(bounds.max.x, bounds.max.y, bounds.min.z);\n      boundsVertices[7].set(bounds.max.x, bounds.max.y, bounds.max.z);\n      for (let i = 0; i < 8; i++) {\n        worldVerts[i].copy(boundsVertices[i]);\n      }\n      bounds.getCenter(focusPos);\n      boundsVertices.map(v => v.sub(focusPos));\n      const lightPlane = lpP.set(lightDirInv, 0);\n      boundsVertices.map((v, i) => lightPlane.projectPoint(v, projectedVerts[i]));\n      const centralVert = projectedVerts.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(projectedVerts.length);\n      const radius = projectedVerts.map(v => v.distanceTo(centralVert)).reduce((a, b) => Math.max(a, b));\n      const dirLength = boundsVertices.map(x => x.dot(lightDir)).reduce((a, b) => Math.max(a, b));\n      // Shadows\n      camera.current.position.copy(cameraPos.copy(lightDir).multiplyScalar(dirLength).add(focusPos));\n      camera.current.lookAt(scene.current.localToWorld(focusPos));\n      const dirMatrix = lpM.lookAt(camera.current.position, focusPos, v.set(0, 1, 0));\n      camera.current.left = -radius;\n      camera.current.right = radius;\n      camera.current.top = radius;\n      camera.current.bottom = -radius;\n      const yOffset = v.set(0, radius, 0).applyMatrix4(dirMatrix);\n      const yTime = (camera.current.position.y + yOffset.y) / lightDir.y;\n      camera.current.near = 0.1;\n      camera.current.far = yTime;\n      camera.current.updateProjectionMatrix();\n      camera.current.updateMatrixWorld();\n\n      // Now find size of ground plane\n      const groundProjectedCoords = worldVerts.map((v, i) => v.add(lightDirs[i].copy(lightDir).multiplyScalar(-v.y / lightDir.y)));\n      const centerPos = groundProjectedCoords.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(groundProjectedCoords.length);\n      const maxSize = 2 * groundProjectedCoords.map(v => Math.hypot(v.x - centerPos.x, v.z - centerPos.z)).reduce((a, b) => Math.max(a, b));\n      plane.current.scale.setScalar(maxSize);\n      plane.current.position.copy(centerPos);\n      if (debug) (_helper$current = helper.current) == null || _helper$current.update();\n\n      // Inject uniforms\n      normalMatB.viewMatrix.value = normalMat.viewMatrix.value = camera.current.matrixWorldInverse;\n      const dirLightNearPlane = lpF.setFromProjectionMatrix(lpM.multiplyMatrices(camera.current.projectionMatrix, camera.current.matrixWorldInverse)).planes[4];\n      causticsMaterial.cameraMatrixWorld = camera.current.matrixWorld;\n      causticsMaterial.cameraProjectionMatrixInv = camera.current.projectionMatrixInverse;\n      causticsMaterial.lightDir = lightDirInv;\n      causticsMaterial.lightPlaneNormal = dirLightNearPlane.normal;\n      causticsMaterial.lightPlaneConstant = dirLightNearPlane.constant;\n      causticsMaterial.near = camera.current.near;\n      causticsMaterial.far = camera.current.far;\n      causticsMaterial.resolution = resolution;\n      causticsMaterial.size = radius;\n      causticsMaterial.intensity = intensity;\n      causticsMaterial.worldRadius = worldRadius;\n\n      // Switch the scene on\n      scene.current.visible = true;\n\n      // Render front face normals\n      gl.setRenderTarget(normalTarget);\n      gl.clear();\n      scene.current.overrideMaterial = normalMat;\n      gl.render(scene.current, camera.current);\n\n      // Render back face normals, if enabled\n      gl.setRenderTarget(normalTargetB);\n      gl.clear();\n      if (backside) {\n        scene.current.overrideMaterial = normalMatB;\n        gl.render(scene.current, camera.current);\n      }\n\n      // Remove the override material\n      scene.current.overrideMaterial = null;\n\n      // Render front face caustics\n      causticsMaterial.ior = ior;\n      plane.current.material.lightProjMatrix = camera.current.projectionMatrix;\n      plane.current.material.lightViewMatrix = camera.current.matrixWorldInverse;\n      causticsMaterial.normalTexture = normalTarget.texture;\n      causticsMaterial.depthTexture = normalTarget.depthTexture;\n      gl.setRenderTarget(causticsTarget);\n      gl.clear();\n      causticsQuad.render(gl);\n\n      // Render back face caustics, if enabled\n      causticsMaterial.ior = backsideIOR;\n      causticsMaterial.normalTexture = normalTargetB.texture;\n      causticsMaterial.depthTexture = normalTargetB.depthTexture;\n      gl.setRenderTarget(causticsTargetB);\n      gl.clear();\n      if (backside) causticsQuad.render(gl);\n\n      // Reset render target\n      gl.setRenderTarget(null);\n\n      // Switch the scene off if caustics is all that's wanted\n      if (causticsOnly) scene.current.visible = false;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"scene\", {\n    ref: scene\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: camera,\n    up: [0, 1, 0]\n  }), children), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: 2,\n    ref: plane,\n    \"rotation-x\": -Math.PI / 2\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"causticsProjectionMaterial\", {\n    transparent: true,\n    color: color,\n    causticsTexture: causticsTarget.texture,\n    causticsTextureB: causticsTargetB.texture,\n    blending: THREE.CustomBlending,\n    blendSrc: THREE.OneFactor,\n    blendDst: THREE.SrcAlphaFactor,\n    depthWrite: false\n  }), debug && /*#__PURE__*/React.createElement(Edges, null, /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: \"#ffff00\",\n    toneMapped: false\n  }))));\n});\n\nexport { Caustics };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,oBAAoBA,CAACC,IAAI,GAAGZ,KAAK,CAACa,SAAS,EAAE;EACpD,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,IAAIf,KAAK,CAACgB,OAAO,CAAC;EAC3B,CAAC;EACD,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAIlB,KAAK,CAACmB,kBAAkB,CAAC;IAChDP;EACF,CAAC,CAAC,EAAE;IACFE,UAAU;IACVM,eAAe,EAAEC,MAAM,IAAI;MACzBA,MAAM,CAACC,QAAQ,CAACR,UAAU,GAAGA,UAAU;MACvCO,MAAM,CAACE,cAAc,GAAG;AAC9B;AACA,aAAa,GAAGF,MAAM,CAACE,cAAc,CAACC,OAAO,CAAC,iCAAiC,EAAE;AACjF,uEAAuE,CAAC;IACpE;EACF,CAAC,CAAC;AACJ;AACA,MAAMC,0BAA0B,GAAG,eAAelB,cAAc,CAAC;EAC/DmB,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,eAAe,IAAI5B,KAAK,CAAC6B,KAAK,CAAC,CAAC;EACvCC,eAAe,EAAE,eAAe,IAAI9B,KAAK,CAACgB,OAAO,CAAC,CAAC;EACnDe,eAAe,EAAE,eAAe,IAAI/B,KAAK,CAACgB,OAAO,CAAC;AACpD,CAAC,EAAE;AACH;AACA;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBN,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC7E,KAAK,CAAC;AACN,MAAMsB,gBAAgB,GAAG,eAAezB,cAAc,CAAC;EACrD0B,iBAAiB,EAAE,eAAe,IAAIjC,KAAK,CAACgB,OAAO,CAAC,CAAC;EACrDkB,yBAAyB,EAAE,eAAe,IAAIlC,KAAK,CAACgB,OAAO,CAAC,CAAC;EAC7DmB,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,eAAe,IAAIrC,KAAK,CAACsC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnDC,gBAAgB,EAAE,eAAe,IAAIvC,KAAK,CAACsC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3DE,kBAAkB,EAAE,CAAC;EACrBC,IAAI,EAAE,GAAG;EACTC,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,eAAe,IAAI3C,KAAK,CAACgB,OAAO,CAAC,CAAC;EAC/C4B,WAAW,EAAE,CAAC,GAAG,EAAE;EACnBC,GAAG,EAAE,GAAG;EACRC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,IAAI;EAChBC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE;AACb,CAAC,EAAE,UAAU;AACb;AACA;AACA;AACA;AACA,IAAI,EAAE,UAAU;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAEpD,KAAK,CAACqD,YAAY;EAC7BC,SAAS,EAAEtD,KAAK,CAACqD,YAAY;EAC7BE,IAAI,EAAEvD,KAAK,CAACwD;AACd,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBL,SAAS,EAAEpD,KAAK,CAAC0D,wBAAwB;EACzCJ,SAAS,EAAEtD,KAAK,CAACqD,YAAY;EAC7BE,IAAI,EAAEvD,KAAK,CAAC2D,SAAS;EACrBC,eAAe,EAAE;AACnB,CAAC;AACD,MAAMC,QAAQ,GAAG,eAAe5D,KAAK,CAAC6D,UAAU,CAAC,CAAC;EAChDC,KAAK;EACLC,QAAQ;EACRC,MAAM,GAAG,CAAC;EACVpB,GAAG,GAAG,GAAG;EACTjB,KAAK,GAAG,OAAO;EACfsC,YAAY,GAAG,KAAK;EACpBC,QAAQ,GAAG,KAAK;EAChBC,WAAW,GAAG,GAAG;EACjBxB,WAAW,GAAG,MAAM;EACpBK,SAAS,GAAG,IAAI;EAChBF,UAAU,GAAG,IAAI;EACjBsB,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACVrE,MAAM,CAAC;IACLuB;EACF,CAAC,CAAC;EACF,MAAM+C,GAAG,GAAGvE,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,MAAM,GAAGzE,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,KAAK,GAAG1E,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMG,KAAK,GAAG3E,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMI,EAAE,GAAG1E,QAAQ,CAAC2E,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,MAAM,GAAGzE,SAAS,CAACyD,KAAK,IAAIW,MAAM,EAAE1E,KAAK,CAACgF,YAAY,CAAC;;EAE7D;EACA,MAAMC,YAAY,GAAG5E,MAAM,CAAC0C,UAAU,EAAEA,UAAU,EAAEG,WAAW,CAAC;EAChE,MAAMgC,aAAa,GAAG7E,MAAM,CAAC0C,UAAU,EAAEA,UAAU,EAAEG,WAAW,CAAC;EACjE,MAAMiC,cAAc,GAAG9E,MAAM,CAAC0C,UAAU,EAAEA,UAAU,EAAEU,YAAY,CAAC;EACnE,MAAM2B,eAAe,GAAG/E,MAAM,CAAC0C,UAAU,EAAEA,UAAU,EAAEU,YAAY,CAAC;EACpE;EACA,MAAM,CAAC4B,SAAS,CAAC,GAAGpF,KAAK,CAACqF,QAAQ,CAAC,MAAM3E,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4E,UAAU,CAAC,GAAGtF,KAAK,CAACqF,QAAQ,CAAC,MAAM3E,oBAAoB,CAACX,KAAK,CAACwF,QAAQ,CAAC,CAAC;EAC/E;EACA,MAAM,CAACC,gBAAgB,CAAC,GAAGxF,KAAK,CAACqF,QAAQ,CAAC,MAAM,IAAItD,gBAAgB,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC0D,YAAY,CAAC,GAAGzF,KAAK,CAACqF,QAAQ,CAAC,MAAM,IAAI7E,cAAc,CAACgF,gBAAgB,CAAC,CAAC;EACjFxF,KAAK,CAAC0F,eAAe,CAAC,MAAM;IAC1BnB,GAAG,CAACoB,OAAO,CAACC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMC,CAAC,GAAG,IAAI/F,KAAK,CAACsC,OAAO,CAAC,CAAC;EAC7B,MAAM0D,GAAG,GAAG,IAAIhG,KAAK,CAACiG,OAAO,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAG,IAAIlG,KAAK,CAACgB,OAAO,CAAC,CAAC;EAC/B,MAAMmF,GAAG,GAAG,IAAInG,KAAK,CAACoG,KAAK,CAAC,CAAC;EAC7B,MAAM/D,QAAQ,GAAG,IAAIrC,KAAK,CAACsC,OAAO,CAAC,CAAC;EACpC,MAAM+D,WAAW,GAAG,IAAIrG,KAAK,CAACsC,OAAO,CAAC,CAAC;EACvC,MAAMgE,MAAM,GAAG,IAAItG,KAAK,CAACuG,IAAI,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAG,IAAIxG,KAAK,CAACsC,OAAO,CAAC,CAAC;EACpC,MAAMmE,cAAc,GAAG,EAAE;EACzB,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,cAAc,GAAG,EAAE;EACzB,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAMC,SAAS,GAAG,IAAI7G,KAAK,CAACsC,OAAO,CAAC,CAAC;EACrC,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BL,cAAc,CAACM,IAAI,CAAC,IAAI/G,KAAK,CAACsC,OAAO,CAAC,CAAC,CAAC;IACxCoE,UAAU,CAACK,IAAI,CAAC,IAAI/G,KAAK,CAACsC,OAAO,CAAC,CAAC,CAAC;IACpCqE,cAAc,CAACI,IAAI,CAAC,IAAI/G,KAAK,CAACsC,OAAO,CAAC,CAAC,CAAC;IACxCsE,SAAS,CAACG,IAAI,CAAC,IAAI/G,KAAK,CAACsC,OAAO,CAAC,CAAC,CAAC;EACrC;EACAlC,QAAQ,CAAC,MAAM;IACb,IAAI6D,MAAM,KAAK+C,QAAQ,IAAIlB,KAAK,EAAE,GAAG7B,MAAM,EAAE;MAC3C,IAAIgD,qBAAqB,EAAEC,eAAe;MAC1C,IAAIC,KAAK,CAACC,OAAO,CAAC/C,WAAW,CAAC,EAAEhC,QAAQ,CAACgF,SAAS,CAAChD,WAAW,CAAC,CAACiD,SAAS,CAAC,CAAC,CAAC,KAAKjF,QAAQ,CAACkF,IAAI,CAAC/C,GAAG,CAACoB,OAAO,CAAC4B,YAAY,CAACnD,WAAW,CAACuB,OAAO,CAAC6B,gBAAgB,CAAC1B,CAAC,CAAC,CAAC,CAACuB,SAAS,CAAC,CAAC,CAAC;MAC7KjB,WAAW,CAACkB,IAAI,CAAClF,QAAQ,CAAC,CAACqF,cAAc,CAAC,CAAC,CAAC,CAAC;MAC7C,CAACT,qBAAqB,GAAGtC,KAAK,CAACiB,OAAO,CAAC+B,MAAM,KAAK,IAAI,IAAIV,qBAAqB,CAACW,WAAW,CAACC,QAAQ,CAAC,CAAC;MACtGvB,MAAM,CAACwB,aAAa,CAACnD,KAAK,CAACiB,OAAO,EAAE,IAAI,CAAC;MACzCa,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC0B,GAAG,CAACC,CAAC,EAAE3B,MAAM,CAAC0B,GAAG,CAACE,CAAC,EAAE5B,MAAM,CAAC0B,GAAG,CAACG,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC0B,GAAG,CAACC,CAAC,EAAE3B,MAAM,CAAC0B,GAAG,CAACE,CAAC,EAAE5B,MAAM,CAAC8B,GAAG,CAACD,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC0B,GAAG,CAACC,CAAC,EAAE3B,MAAM,CAAC8B,GAAG,CAACF,CAAC,EAAE5B,MAAM,CAAC0B,GAAG,CAACG,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC0B,GAAG,CAACC,CAAC,EAAE3B,MAAM,CAAC8B,GAAG,CAACF,CAAC,EAAE5B,MAAM,CAAC8B,GAAG,CAACD,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC8B,GAAG,CAACH,CAAC,EAAE3B,MAAM,CAAC0B,GAAG,CAACE,CAAC,EAAE5B,MAAM,CAAC0B,GAAG,CAACG,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC8B,GAAG,CAACH,CAAC,EAAE3B,MAAM,CAAC0B,GAAG,CAACE,CAAC,EAAE5B,MAAM,CAAC8B,GAAG,CAACD,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC8B,GAAG,CAACH,CAAC,EAAE3B,MAAM,CAAC8B,GAAG,CAACF,CAAC,EAAE5B,MAAM,CAAC0B,GAAG,CAACG,CAAC,CAAC;MAC/D1B,cAAc,CAAC,CAAC,CAAC,CAACsB,GAAG,CAACzB,MAAM,CAAC8B,GAAG,CAACH,CAAC,EAAE3B,MAAM,CAAC8B,GAAG,CAACF,CAAC,EAAE5B,MAAM,CAAC8B,GAAG,CAACD,CAAC,CAAC;MAC/D,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1BJ,UAAU,CAACI,CAAC,CAAC,CAACS,IAAI,CAACd,cAAc,CAACK,CAAC,CAAC,CAAC;MACvC;MACAR,MAAM,CAAC+B,SAAS,CAAC7B,QAAQ,CAAC;MAC1BC,cAAc,CAAC6B,GAAG,CAACvC,CAAC,IAAIA,CAAC,CAACwC,GAAG,CAAC/B,QAAQ,CAAC,CAAC;MACxC,MAAMgC,UAAU,GAAGrC,GAAG,CAAC4B,GAAG,CAAC1B,WAAW,EAAE,CAAC,CAAC;MAC1CI,cAAc,CAAC6B,GAAG,CAAC,CAACvC,CAAC,EAAEe,CAAC,KAAK0B,UAAU,CAACC,YAAY,CAAC1C,CAAC,EAAEY,cAAc,CAACG,CAAC,CAAC,CAAC,CAAC;MAC3E,MAAM4B,WAAW,GAAG/B,cAAc,CAACgC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE9C,CAAC,CAACgC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACgB,YAAY,CAACpC,cAAc,CAACqC,MAAM,CAAC;MACjH,MAAMC,MAAM,GAAGtC,cAAc,CAAC2B,GAAG,CAACvC,CAAC,IAAIA,CAAC,CAACmD,UAAU,CAACR,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACf,GAAG,CAACQ,CAAC,EAAEC,CAAC,CAAC,CAAC;MAClG,MAAMO,SAAS,GAAG3C,cAAc,CAAC6B,GAAG,CAACL,CAAC,IAAIA,CAAC,CAACoB,GAAG,CAAChH,QAAQ,CAAC,CAAC,CAACsG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACf,GAAG,CAACQ,CAAC,EAAEC,CAAC,CAAC,CAAC;MAC3F;MACAnE,MAAM,CAACkB,OAAO,CAAC0D,QAAQ,CAAC/B,IAAI,CAACV,SAAS,CAACU,IAAI,CAAClF,QAAQ,CAAC,CAACqF,cAAc,CAAC0B,SAAS,CAAC,CAACN,GAAG,CAACtC,QAAQ,CAAC,CAAC;MAC9F9B,MAAM,CAACkB,OAAO,CAAC2D,MAAM,CAAC5E,KAAK,CAACiB,OAAO,CAAC4D,YAAY,CAAChD,QAAQ,CAAC,CAAC;MAC3D,MAAMiD,SAAS,GAAGvD,GAAG,CAACqD,MAAM,CAAC7E,MAAM,CAACkB,OAAO,CAAC0D,QAAQ,EAAE9C,QAAQ,EAAET,CAAC,CAACgC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/ErD,MAAM,CAACkB,OAAO,CAAC8D,IAAI,GAAG,CAACT,MAAM;MAC7BvE,MAAM,CAACkB,OAAO,CAAC+D,KAAK,GAAGV,MAAM;MAC7BvE,MAAM,CAACkB,OAAO,CAACgE,GAAG,GAAGX,MAAM;MAC3BvE,MAAM,CAACkB,OAAO,CAACiE,MAAM,GAAG,CAACZ,MAAM;MAC/B,MAAMa,OAAO,GAAG/D,CAAC,CAACgC,GAAG,CAAC,CAAC,EAAEkB,MAAM,EAAE,CAAC,CAAC,CAACc,YAAY,CAACN,SAAS,CAAC;MAC3D,MAAMO,KAAK,GAAG,CAACtF,MAAM,CAACkB,OAAO,CAAC0D,QAAQ,CAACpB,CAAC,GAAG4B,OAAO,CAAC5B,CAAC,IAAI7F,QAAQ,CAAC6F,CAAC;MAClExD,MAAM,CAACkB,OAAO,CAACnD,IAAI,GAAG,GAAG;MACzBiC,MAAM,CAACkB,OAAO,CAAClD,GAAG,GAAGsH,KAAK;MAC1BtF,MAAM,CAACkB,OAAO,CAACqE,sBAAsB,CAAC,CAAC;MACvCvF,MAAM,CAACkB,OAAO,CAACsE,iBAAiB,CAAC,CAAC;;MAElC;MACA,MAAMC,qBAAqB,GAAGzD,UAAU,CAAC4B,GAAG,CAAC,CAACvC,CAAC,EAAEe,CAAC,KAAKf,CAAC,CAAC+C,GAAG,CAAClC,SAAS,CAACE,CAAC,CAAC,CAACS,IAAI,CAAClF,QAAQ,CAAC,CAACqF,cAAc,CAAC,CAAC3B,CAAC,CAACmC,CAAC,GAAG7F,QAAQ,CAAC6F,CAAC,CAAC,CAAC,CAAC;MAC5H,MAAMkC,SAAS,GAAGD,qBAAqB,CAACxB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE9C,CAAC,CAACgC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACgB,YAAY,CAACoB,qBAAqB,CAACnB,MAAM,CAAC;MAC7H,MAAMqB,OAAO,GAAG,CAAC,GAAGF,qBAAqB,CAAC7B,GAAG,CAACvC,CAAC,IAAIoD,IAAI,CAACmB,KAAK,CAACvE,CAAC,CAACkC,CAAC,GAAGmC,SAAS,CAACnC,CAAC,EAAElC,CAAC,CAACoC,CAAC,GAAGiC,SAAS,CAACjC,CAAC,CAAC,CAAC,CAACQ,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACf,GAAG,CAACQ,CAAC,EAAEC,CAAC,CAAC,CAAC;MACrIjE,KAAK,CAACgB,OAAO,CAAC2E,KAAK,CAACC,SAAS,CAACH,OAAO,CAAC;MACtCzF,KAAK,CAACgB,OAAO,CAAC0D,QAAQ,CAAC/B,IAAI,CAAC6C,SAAS,CAAC;MACtC,IAAIrG,KAAK,EAAE,CAACmD,eAAe,GAAGnC,MAAM,CAACa,OAAO,KAAK,IAAI,IAAIsB,eAAe,CAACuD,MAAM,CAAC,CAAC;;MAEjF;MACAlF,UAAU,CAACzE,UAAU,CAACC,KAAK,GAAGsE,SAAS,CAACvE,UAAU,CAACC,KAAK,GAAG2D,MAAM,CAACkB,OAAO,CAAC8E,kBAAkB;MAC5F,MAAMC,iBAAiB,GAAG3E,GAAG,CAAC4E,uBAAuB,CAAC1E,GAAG,CAAC2E,gBAAgB,CAACnG,MAAM,CAACkB,OAAO,CAACkF,gBAAgB,EAAEpG,MAAM,CAACkB,OAAO,CAAC8E,kBAAkB,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;MACzJtF,gBAAgB,CAACxD,iBAAiB,GAAGyC,MAAM,CAACkB,OAAO,CAACgC,WAAW;MAC/DnC,gBAAgB,CAACvD,yBAAyB,GAAGwC,MAAM,CAACkB,OAAO,CAACoF,uBAAuB;MACnFvF,gBAAgB,CAACpD,QAAQ,GAAGgE,WAAW;MACvCZ,gBAAgB,CAAClD,gBAAgB,GAAGoI,iBAAiB,CAACM,MAAM;MAC5DxF,gBAAgB,CAACjD,kBAAkB,GAAGmI,iBAAiB,CAACO,QAAQ;MAChEzF,gBAAgB,CAAChD,IAAI,GAAGiC,MAAM,CAACkB,OAAO,CAACnD,IAAI;MAC3CgD,gBAAgB,CAAC/C,GAAG,GAAGgC,MAAM,CAACkB,OAAO,CAAClD,GAAG;MACzC+C,gBAAgB,CAAC1C,UAAU,GAAGA,UAAU;MACxC0C,gBAAgB,CAACzC,IAAI,GAAGiG,MAAM;MAC9BxD,gBAAgB,CAACxC,SAAS,GAAGA,SAAS;MACtCwC,gBAAgB,CAAC7C,WAAW,GAAGA,WAAW;;MAE1C;MACA+B,KAAK,CAACiB,OAAO,CAACuF,OAAO,GAAG,IAAI;;MAE5B;MACAtG,EAAE,CAACuG,eAAe,CAACnG,YAAY,CAAC;MAChCJ,EAAE,CAACwG,KAAK,CAAC,CAAC;MACV1G,KAAK,CAACiB,OAAO,CAAC0F,gBAAgB,GAAGjG,SAAS;MAC1CR,EAAE,CAAC0G,MAAM,CAAC5G,KAAK,CAACiB,OAAO,EAAElB,MAAM,CAACkB,OAAO,CAAC;;MAExC;MACAf,EAAE,CAACuG,eAAe,CAAClG,aAAa,CAAC;MACjCL,EAAE,CAACwG,KAAK,CAAC,CAAC;MACV,IAAIlH,QAAQ,EAAE;QACZQ,KAAK,CAACiB,OAAO,CAAC0F,gBAAgB,GAAG/F,UAAU;QAC3CV,EAAE,CAAC0G,MAAM,CAAC5G,KAAK,CAACiB,OAAO,EAAElB,MAAM,CAACkB,OAAO,CAAC;MAC1C;;MAEA;MACAjB,KAAK,CAACiB,OAAO,CAAC0F,gBAAgB,GAAG,IAAI;;MAErC;MACA7F,gBAAgB,CAAC5C,GAAG,GAAGA,GAAG;MAC1B+B,KAAK,CAACgB,OAAO,CAAC4F,QAAQ,CAAC1J,eAAe,GAAG4C,MAAM,CAACkB,OAAO,CAACkF,gBAAgB;MACxElG,KAAK,CAACgB,OAAO,CAAC4F,QAAQ,CAACzJ,eAAe,GAAG2C,MAAM,CAACkB,OAAO,CAAC8E,kBAAkB;MAC1EjF,gBAAgB,CAACtD,aAAa,GAAG8C,YAAY,CAACwG,OAAO;MACrDhG,gBAAgB,CAACrD,YAAY,GAAG6C,YAAY,CAAC7C,YAAY;MACzDyC,EAAE,CAACuG,eAAe,CAACjG,cAAc,CAAC;MAClCN,EAAE,CAACwG,KAAK,CAAC,CAAC;MACV3F,YAAY,CAAC6F,MAAM,CAAC1G,EAAE,CAAC;;MAEvB;MACAY,gBAAgB,CAAC5C,GAAG,GAAGuB,WAAW;MAClCqB,gBAAgB,CAACtD,aAAa,GAAG+C,aAAa,CAACuG,OAAO;MACtDhG,gBAAgB,CAACrD,YAAY,GAAG8C,aAAa,CAAC9C,YAAY;MAC1DyC,EAAE,CAACuG,eAAe,CAAChG,eAAe,CAAC;MACnCP,EAAE,CAACwG,KAAK,CAAC,CAAC;MACV,IAAIlH,QAAQ,EAAEuB,YAAY,CAAC6F,MAAM,CAAC1G,EAAE,CAAC;;MAErC;MACAA,EAAE,CAACuG,eAAe,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIlH,YAAY,EAAES,KAAK,CAACiB,OAAO,CAACuF,OAAO,GAAG,KAAK;IACjD;EACF,CAAC,CAAC;EACFlL,KAAK,CAACyL,mBAAmB,CAACnH,IAAI,EAAE,MAAMC,GAAG,CAACoB,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAa3F,KAAK,CAAC0L,aAAa,CAAC,OAAO,EAAE5L,QAAQ,CAAC;IACxDyE,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAarE,KAAK,CAAC0L,aAAa,CAAC,OAAO,EAAE;IACnDnH,GAAG,EAAEG;EACP,CAAC,EAAE,aAAa1E,KAAK,CAAC0L,aAAa,CAAC,oBAAoB,EAAE;IACxDnH,GAAG,EAAEE,MAAM;IACXkH,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACd,CAAC,CAAC,EAAE5H,QAAQ,CAAC,EAAE,aAAa/D,KAAK,CAAC0L,aAAa,CAAC,MAAM,EAAE;IACtDE,WAAW,EAAE,CAAC;IACdrH,GAAG,EAAEI,KAAK;IACV,YAAY,EAAE,CAACuE,IAAI,CAAC2C,EAAE,GAAG;EAC3B,CAAC,EAAE,aAAa7L,KAAK,CAAC0L,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAa1L,KAAK,CAAC0L,aAAa,CAAC,4BAA4B,EAAE;IACzHI,WAAW,EAAE,IAAI;IACjBnK,KAAK,EAAEA,KAAK;IACZF,eAAe,EAAEyD,cAAc,CAACsG,OAAO;IACvC9J,gBAAgB,EAAEyD,eAAe,CAACqG,OAAO;IACzCO,QAAQ,EAAEhM,KAAK,CAACiM,cAAc;IAC9BC,QAAQ,EAAElM,KAAK,CAACmM,SAAS;IACzBC,QAAQ,EAAEpM,KAAK,CAACqM,cAAc;IAC9BC,UAAU,EAAE;EACd,CAAC,CAAC,EAAEvI,KAAK,IAAI,aAAa9D,KAAK,CAAC0L,aAAa,CAACnL,KAAK,EAAE,IAAI,EAAE,aAAaP,KAAK,CAAC0L,aAAa,CAAC,mBAAmB,EAAE;IAC/G/J,KAAK,EAAE,SAAS;IAChB2K,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,SAAS1I,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}