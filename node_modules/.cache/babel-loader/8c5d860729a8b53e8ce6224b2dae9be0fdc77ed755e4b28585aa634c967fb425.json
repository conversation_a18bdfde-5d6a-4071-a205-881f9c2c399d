{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport { createWithEqualityFn } from 'zustand/traditional';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useFiber, useContextBridge, traverseFiber } from 'its-fine';\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\n * Returns the instance's initial (outmost) root.\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n/**\n * Safely flush async effects when testing, simulating a legacy root.\n * @deprecated Import from React instead. import { act } from 'react'\n */\n// Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = React['act' + ''];\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = value => value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\nconst useIsomorphicLayoutEffect = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && (((_window$document = window.document) == null ? void 0 : _window$document.createElement) || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative'))() ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\n * Bridges renderer Context and StrictMode from a primary renderer.\n */\nfunction useBridge() {\n  const fiber = useFiber();\n  const ContextBridge = useContextBridge();\n  return React.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!traverseFiber(fiber, true, node => node.type === React.StrictMode);\n    const Root = strict ? React.StrictMode : React.Fragment;\n    return /*#__PURE__*/jsx(Root, {\n      children: /*#__PURE__*/jsx(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\n\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */(_ErrorBoundary => (_ErrorBoundary = class ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}, _ErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n}), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\n * Returns instance root state\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  nul: a => a === null,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {},\n    meshes: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n      if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) object.__r3f = instance;\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  target = root;\n  for (const part of key.split('-')) {\n    var _target;\n    key = part;\n    root = target;\n    target = (_target = target) == null ? void 0 : _target[key];\n  }\n\n  // TODO: change key to 'foo-bar' if target is undefined?\n\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n  let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n  try {\n    if (!ctor) {\n      ctor = new root.constructor();\n      MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n    }\n  } catch (e) {\n    // ...\n  }\n  return ctor;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      const ctor = getMemoizedPrototype(root);\n      if (!is.und(ctor)) changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenColorMap', 'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  var _instance$object;\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n      continue;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Layers must be written to the mask property\n    if (target instanceof THREE.Layers && value instanceof THREE.Layers) {\n      target.mask = value.mask;\n    }\n    // Set colors if valid color representation for automatic conversion (copy)\n    else if (target instanceof THREE.Color && isColorRepresentation(value)) {\n      target.set(value);\n    }\n    // Copy if properties match signatures and implement math interface (likely read-only)\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n      target.copy(value);\n    }\n    // Set array types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n      if (typeof target.fromArray === 'function') target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n      // Allow setting array scalars\n      if (typeof target.setScalar === 'function') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n    }\n    // Else, just overwrite the value\n    else {\n      var _root$key;\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === THREE.RGBAFormat && root[key].type === THREE.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        root[key].colorSpace = THREE.SRGBColorSpace;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n    const object = instance.object;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && object.raycast !== null) {\n      rootState.internal.interaction.push(object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';else if (instance.object.isMaterial) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\n * Release pointer captures.\n * This is called by releasePointerCapture in the API, and when an object is removed.\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        let state = getRootState(hit.object);\n\n        // If the object is not managed by R3F, it might be parented to an element which is.\n        // Traverse upwards until we find a managed parent and use its state instead.\n        if (!state) {\n          hit.object.traverseAncestors(obj => {\n            const parentState = getRootState(obj);\n            if (parentState) {\n              state = parentState;\n              return false;\n            }\n          });\n        }\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\n        MAYBE TODO, DELETE IF NOT: \n          Check if the object is captured, captured events should not have intersects running in parallel\n          But wouldn't it be better to just replace capturedMap with a single entry?\n          Also, are we OK with straight up making picking up multiple objects impossible?\n          \n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \n        if (pointerId !== undefined) {\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\n          if (capturedMeshSet) {\n            const captured = capturedMeshSet.get(eventObject)\n            if (captured && captured.localState.stopped) return\n          }\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /* @__PURE__ */React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = createWithEqualityFn((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/React.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\n * Exposes an object's {@link Instance}.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\n *\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  React.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\n */\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\n * Executes a callback before render in a shared frame loop.\n * Can order effects with render priority or manually render with a positive priority.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\n * Returns a node graph of an object with named nodes & materials.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor$1(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\n * Synchronously loads and caches assets with a three loader.\n *\n * Note: this hook's caller must be wrapped with `React.Suspense`\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\n * Preloads an asset into cache as a side-effect.\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\n * Removes a loaded asset from cache.\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([loader, ...keys]);\n};\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nfunction createReconciler(config) {\n  const reconciler = Reconciler(config);\n  reconciler.injectIntoDevTools({\n    bundleType: typeof process !== 'undefined' && process.env.NODE_ENV !== 'production' ? 1 : 0,\n    rendererPackageName: '@react-three/fiber',\n    version: React.version\n  });\n  return reconciler;\n}\nconst NoEventPriority = 0;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = type => `${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = toPascalCase(type);\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  // Remove three* prefix from elements if native element not present\n  type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const target = catalogue[toPascalCase(child.type)];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n  }\n\n  // Set initial props\n  applyProps(child.object, child.props);\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      // If the child is already in the parent's children array, move it to the new position\n      // Otherwise, just insert it at the target position\n      const existingIndex = parent.object.children.indexOf(child.object);\n      if (existingIndex !== -1) {\n        parent.object.children.splice(existingIndex, 1);\n        const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n        parent.object.children.splice(adjustedIndex, 0, child.object);\n      } else {\n        child.object.parent = parent.object;\n        parent.object.children.splice(childIndex, 0, child.object);\n        child.object.dispatchEvent({\n          type: 'added'\n        });\n        parent.object.dispatchEvent({\n          type: 'childadded',\n          child: child.object\n        });\n      }\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else unstable_scheduleCallback(unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const target = catalogue[toPascalCase(instance.type)];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, make it no-op\nconst handleTextInstance = () => {};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition: () => false,\n  trackSchedulerEvent: () => {},\n  resolveEventType: () => null,\n  resolveEventTimeStamp: () => -1.1,\n  requestPostPaintCallback() {},\n  maySuspendCommit: () => false,\n  preloadInstance: () => true,\n  // true indicates already loaded\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady: () => null,\n  NotPendingTransition: null,\n  HostTransitionContext: /* @__PURE__ */React.createContext(null),\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return ContinuousEventPriority;\n      default:\n        return DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let lastCamera;\n  let configured = false;\n  let pending = null;\n  return {\n    async configure(props = {}) {\n      let resolve;\n      pending = new Promise(_resolve => resolve = _resolve);\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) {\n        const defaultProps = {\n          canvas: canvas,\n          powerPreference: 'high-performance',\n          antialias: true,\n          alpha: true\n        };\n        const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n        if (isRenderer(customRenderer)) {\n          gl = customRenderer;\n        } else {\n          gl = new THREE.WebGLRenderer({\n            ...defaultProps,\n            ...glConfig\n          });\n        }\n        state.set({\n          gl\n        });\n      }\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new THREE.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n      THREE.ColorManagement.enabled = !legacy;\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        gl.outputColorSpace = linear ? THREE.LinearSRGBColorSpace : THREE.SRGBColorSpace;\n        gl.toneMapping = flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping;\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      resolve();\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured && !pending) this.configure();\n      pending.then(() => {\n        reconciler.updateContainer(/*#__PURE__*/jsx(Provider, {\n          store: store,\n          children: children,\n          onCreated: onCreated,\n          rootElement: canvas\n        }), fiber, null, () => undefined);\n      });\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notify that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\n   *  <Canvas>\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = React.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = createWithEqualityFn((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return (/*#__PURE__*/\n    // @ts-ignore, reconciler types are not maintained\n    jsx(Fragment, {\n      children: reconciler.createPortal(/*#__PURE__*/jsx(context.Provider, {\n        value: usePortalStore,\n        children: children\n      }), usePortalStore, null)\n    })\n  );\n}\n\n/**\n * Force React to flush any updates inside the provided callback synchronously and immediately.\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\n * having to revert to a non-React solution.\n */\nfunction flushSync(fn) {\n  return reconciler.flushSync(fn);\n}\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\n * Adds a global render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\n * Adds a global after-render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\n * Adds a global callback which is called when rendering stops.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = false;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      if (events.handlers) {\n        for (const name in events.handlers) {\n          const event = events.handlers[name];\n          const [eventName, passive] = DOM_EVENTS[name];\n          target.addEventListener(eventName, event, {\n            passive\n          });\n        }\n      }\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        if (events.handlers) {\n          for (const name in events.handlers) {\n            const event = events.handlers[name];\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        }\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\nexport { useStore as A, Block as B, useThree as C, useFrame as D, ErrorBoundary as E, useGraph as F, useLoader as G, _roots as _, useMutableCallback as a, useIsomorphicLayoutEffect as b, createRoot as c, unmountComponentAtNode as d, extend as e, createPointerEvents as f, createEvents as g, flushGlobalEffects as h, isRef as i, addEffect as j, addAfterEffect as k, addTail as l, invalidate as m, advance as n, createPortal as o, flushSync as p, context as q, reconciler as r, applyProps as s, threeTypes as t, useBridge as u, getRootState as v, dispose as w, act as x, buildGraph as y, useInstanceHandle as z };", "map": {"version": 3, "names": ["THREE", "React", "DefaultEventPriority", "ContinuousEventPriority", "DiscreteEventPriority", "ConcurrentRoot", "createWithEqualityFn", "<PERSON><PERSON><PERSON><PERSON>", "unstable_scheduleCallback", "unstable_IdlePriority", "suspend", "preload", "clear", "jsx", "Fragment", "useFiber", "useContextBridge", "traverseFiber", "threeTypes", "Object", "freeze", "__proto__", "findInitialRoot", "instance", "root", "getState", "previousRoot", "act", "isOrthographicCamera", "def", "isRef", "obj", "hasOwnProperty", "isColorRepresentation", "value", "isColor", "useIsomorphicLayoutEffect", "_window$document", "_window$navigator", "window", "document", "createElement", "navigator", "product", "useLayoutEffect", "useEffect", "useMutableCallback", "fn", "ref", "useRef", "current", "useBridge", "fiber", "ContextBridge", "useMemo", "children", "strict", "node", "type", "StrictMode", "Root", "Block", "set", "Promise", "Error<PERSON>ou<PERSON><PERSON>", "_ErrorBoundary", "Component", "constructor", "args", "state", "error", "componentDidCatch", "err", "props", "render", "getDerivedStateFromError", "calculateDpr", "dpr", "_window$devicePixelRa", "target", "devicePixelRatio", "Array", "isArray", "Math", "min", "max", "getRootState", "_r3f", "__r3f", "is", "a", "arr", "fun", "str", "num", "boo", "und", "nul", "equ", "b", "arrays", "objects", "isObj", "isArr", "i", "length", "keys", "buildGraph", "object", "data", "nodes", "materials", "meshes", "traverse", "name", "material", "<PERSON><PERSON><PERSON>", "dispose", "p", "prop", "REACT_INTERNAL_PROPS", "getInstanceProps", "queue", "key", "includes", "prepare", "parent", "eventCount", "handlers", "isHidden", "resolve", "part", "split", "_target", "INDEX_REGEX", "attach", "child", "test", "index", "replace", "previousAttach", "detach", "previous", "undefined", "RESERVED_PROPS", "MEMOIZED_PROTOTYPES", "Map", "getMemoizedPrototype", "ctor", "get", "e", "diffProps", "newProps", "changedProps", "other", "startsWith", "colorMaps", "EVENT_REGEX", "applyProps", "_instance$object", "rootState", "prevHandlers", "Layers", "mask", "Color", "copy", "fromArray", "setScalar", "_root$key", "linear", "isTexture", "format", "RGBAFormat", "UnsignedByteType", "colorSpace", "SRGBColorSpace", "internal", "isObject3D", "interaction", "indexOf", "splice", "raycast", "push", "isBufferGeometry", "isMaterial", "invalidateInstance", "_instance$root", "onUpdate", "frames", "invalidate", "updateCamera", "camera", "size", "manual", "left", "width", "right", "top", "height", "bottom", "aspect", "updateProjectionMatrix", "makeId", "event", "eventObject", "uuid", "instanceId", "releaseInternalPointerCapture", "capturedMap", "captures", "pointerId", "captureData", "delete", "releasePointerCapture", "removeInteractivity", "store", "filter", "o", "initialHits", "hovered", "for<PERSON>ach", "createEvents", "calculateDistance", "dx", "offsetX", "initialClick", "dy", "offsetY", "round", "sqrt", "filterPointerEvents", "some", "intersect", "duplicates", "Set", "intersections", "eventsObjects", "raycaster", "events", "compute", "handleRaycast", "enabled", "_state$previousRoot", "intersectObject", "hits", "flatMap", "sort", "aState", "bState", "distance", "priority", "item", "id", "has", "add", "hit", "_r3f2", "values", "intersection", "handleIntersects", "delta", "callback", "localState", "stopped", "traverseAncestors", "parentState", "pointer", "unprojectedPoint", "Vector3", "x", "y", "unproject", "hasPointerCapture", "_internal$capturedMap", "_internal$capturedMap2", "setPointerCapture", "extractEventProps", "property", "raycastEvent", "ray", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "find", "higher", "slice", "cancelPointer", "currentTarget", "nativeEvent", "hovered<PERSON>bj", "onPointerOut", "onPointerLeave", "pointerMissed", "onPointerMissed", "handlePointer", "requestAnimationFrame", "handleEvent", "lastEvent", "isPointerMove", "isClickEvent", "map", "onIntersect", "onPointerOver", "onPointerEnter", "hoveredItem", "onPointerMove", "handler", "<PERSON><PERSON><PERSON><PERSON>", "context", "createContext", "createStore", "advance", "rootStore", "position", "defaultTarget", "tempTarget", "getCurrentViewport", "isVector3", "getWorldPosition", "distanceTo", "zoom", "factor", "fov", "PI", "h", "tan", "w", "performanceTimeout", "setPerformanceCurrent", "performance", "Vector2", "gl", "connected", "scene", "xr", "timestamp", "runGlobalEffects", "legacy", "flat", "controls", "clock", "Clock", "mouse", "frameloop", "debounce", "regress", "clearTimeout", "setTimeout", "viewport", "initialDpr", "setEvents", "setSize", "setDpr", "resolved", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop", "elapsedTime", "start", "subscribers", "createRef", "active", "subscribe", "s", "oldSize", "oldDpr", "oldCamera", "setPixelRatio", "updateStyle", "HTMLCanvasElement", "dom<PERSON>lement", "useInstanceHandle", "useImperativeHandle", "useStore", "useContext", "Error", "useThree", "selector", "equalityFn", "useFrame", "renderPriority", "useGraph", "memoizedLoaders", "WeakMap", "isConstructor$1", "_value$prototype", "prototype", "loadingFn", "extensions", "onProgress", "Proto", "input", "loader", "all", "res", "reject", "load", "assign", "message", "useLoader", "results", "equal", "createReconciler", "config", "reconciler", "injectIntoDevTools", "bundleType", "process", "env", "NODE_ENV", "rendererPackageName", "version", "NoEventPriority", "catalogue", "PREFIX_REGEX", "toPascalCase", "toUpperCase", "isConstructor", "extend", "validateInstance", "createInstance", "_props$object", "hideInstance", "_instance$parent", "visible", "unhideInstance", "_instance$parent2", "handleContainerEffects", "<PERSON><PERSON><PERSON><PERSON>", "_child$props$object", "_child$props$args", "childIndex", "existingIndex", "adjustedIndex", "dispatchEvent", "childInstance", "append<PERSON><PERSON><PERSON>", "insertBefore", "disposeOnIdle", "handleDispose", "IS_REACT_ACT_ENVIRONMENT", "<PERSON><PERSON><PERSON><PERSON>", "remove", "shouldDispose", "setFiberRef", "publicInstance", "_fiber", "alternate", "refCleanup", "cleanup", "reconstructed", "swapInstances", "_instance$props$objec", "_instance$props$args", "handleTextInstance", "NO_CONTEXT", "currentUpdatePriority", "NoFlags", "Update", "isPrimary<PERSON><PERSON><PERSON>", "warnsIfNotActing", "supportsMutation", "supportsPersistence", "supportsHydration", "appendInitialChild", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "<PERSON><PERSON><PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON>", "insertInContainerBefore", "getRootHostContext", "getChildHostContext", "commitUpdate", "oldProps", "_newProps$args", "_oldProps$args", "_newProps$args2", "reconstruct", "_oldProps$args2", "isTailSibling", "sibling", "flags", "finalizeInitialChildren", "commitMount", "getPublicInstance", "prepareForCommit", "preparePortalMount", "resetAfterCommit", "shouldSetTextContent", "clearContainer", "createTextInstance", "hideTextInstance", "unhideTextInstance", "scheduleTimeout", "cancelTimeout", "noTimeout", "getInstanceFromNode", "beforeActiveInstanceBlur", "afterActiveInstanceBlur", "detachDeletedInstance", "prepareScopeUpdate", "getInstanceFromScope", "shouldAttemptEagerTransition", "trackSchedulerEvent", "resolveEventType", "resolveEventTimeStamp", "requestPostPaintCallback", "maySuspendCommit", "preloadInstance", "startSuspendingCommit", "suspendInstance", "waitForCommitToBeReady", "NotPendingTransition", "HostTransitionContext", "setCurrentUpdatePriority", "newPriority", "getCurrentUpdatePriority", "resolveUpdatePriority", "_window$event", "resetFormInstance", "_roots", "shallowLoose", "computeInitialSize", "canvas", "parentElement", "getBoundingClientRect", "OffscreenCanvas", "createRoot", "prevRoot", "prevFiber", "prevStore", "console", "warn", "logRecoverableError", "reportError", "createContainer", "onCreated", "lastCamera", "configured", "pending", "configure", "_resolve", "glConfig", "propsSize", "sceneOptions", "onCreatedCallback", "shadows", "orthographic", "raycastOptions", "cameraOptions", "defaultProps", "powerPreference", "antialias", "alpha", "customRenderer", "WebGLRenderer", "Raycaster", "params", "options", "isCamera", "OrthographicCamera", "PerspectiveCamera", "z", "rotation", "lookAt", "isScene", "Scene", "_gl$xr", "handleXRFrame", "frame", "handleSessionChange", "isPresenting", "setAnimationLoop", "connect", "addEventListener", "disconnect", "removeEventListener", "shadowMap", "oldEnabled", "oldType", "PCFSoftShadowMap", "_types$shadows", "types", "basic", "BasicShadowMap", "percentage", "PCFShadowMap", "soft", "variance", "VSMShadowMap", "needsUpdate", "ColorManagement", "outputColorSpace", "LinearSRGBColorSpace", "toneMapping", "NoToneMapping", "ACESFilmicToneMapping", "then", "updateContainer", "Provider", "rootElement", "unmount", "unmountComponentAtNode", "_state$gl", "_state$gl$renderLists", "_state$gl2", "_state$gl3", "renderLists", "forceContextLoss", "createPortal", "Portal", "rest", "useState", "inject", "injectState", "usePortalStore", "onMutate", "prev", "setState", "flushSync", "createSubs", "subs", "sub", "globalEffects", "globalAfterEffects", "globalTailEffects", "addEffect", "addAfterEffect", "addTail", "run", "effects", "flushGlobalEffects", "subscription", "update", "<PERSON><PERSON><PERSON><PERSON>", "oldTime", "running", "useFrameInProgress", "repeat", "loop", "_state$gl$xr", "cancelAnimationFrame", "_state$gl$xr2", "DOM_EVENTS", "onClick", "onContextMenu", "onDoubleClick", "onWheel", "onPointerDown", "onPointerUp", "onPointerCancel", "onLostPointerCapture", "createPointerEvents", "setFromCamera", "reduce", "acc", "_internal$lastEvent", "eventName", "passive", "A", "B", "C", "D", "E", "F", "G", "_", "c", "d", "f", "g", "j", "k", "l", "m", "n", "q", "r", "t", "u", "v"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/fiber/dist/events-cf57b220.esm.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport { createWithEqualityFn } from 'zustand/traditional';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useFiber, useContextBridge, traverseFiber } from 'its-fine';\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\n * Returns the instance's initial (outmost) root.\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n/**\n * Safely flush async effects when testing, simulating a legacy root.\n * @deprecated Import from React instead. import { act } from 'react'\n */\n// Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = React['act' + ''];\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = value => value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\nconst useIsomorphicLayoutEffect = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && (((_window$document = window.document) == null ? void 0 : _window$document.createElement) || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative'))() ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\n * Bridges renderer Context and StrictMode from a primary renderer.\n */\nfunction useBridge() {\n  const fiber = useFiber();\n  const ContextBridge = useContextBridge();\n  return React.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!traverseFiber(fiber, true, node => node.type === React.StrictMode);\n    const Root = strict ? React.StrictMode : React.Fragment;\n    return /*#__PURE__*/jsx(Root, {\n      children: /*#__PURE__*/jsx(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\n\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */(_ErrorBoundary => (_ErrorBoundary = class ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}, _ErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n}), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\n * Returns instance root state\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  nul: a => a === null,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {},\n    meshes: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n      if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) object.__r3f = instance;\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  target = root;\n  for (const part of key.split('-')) {\n    var _target;\n    key = part;\n    root = target;\n    target = (_target = target) == null ? void 0 : _target[key];\n  }\n\n  // TODO: change key to 'foo-bar' if target is undefined?\n\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n  let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n  try {\n    if (!ctor) {\n      ctor = new root.constructor();\n      MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n    }\n  } catch (e) {\n    // ...\n  }\n  return ctor;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      const ctor = getMemoizedPrototype(root);\n      if (!is.und(ctor)) changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenColorMap', 'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  var _instance$object;\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n      continue;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Layers must be written to the mask property\n    if (target instanceof THREE.Layers && value instanceof THREE.Layers) {\n      target.mask = value.mask;\n    }\n    // Set colors if valid color representation for automatic conversion (copy)\n    else if (target instanceof THREE.Color && isColorRepresentation(value)) {\n      target.set(value);\n    }\n    // Copy if properties match signatures and implement math interface (likely read-only)\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n      target.copy(value);\n    }\n    // Set array types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n      if (typeof target.fromArray === 'function') target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n      // Allow setting array scalars\n      if (typeof target.setScalar === 'function') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n    }\n    // Else, just overwrite the value\n    else {\n      var _root$key;\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === THREE.RGBAFormat && root[key].type === THREE.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        root[key].colorSpace = THREE.SRGBColorSpace;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n    const object = instance.object;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && object.raycast !== null) {\n      rootState.internal.interaction.push(object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';else if (instance.object.isMaterial) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\n * Release pointer captures.\n * This is called by releasePointerCapture in the API, and when an object is removed.\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        let state = getRootState(hit.object);\n\n        // If the object is not managed by R3F, it might be parented to an element which is.\n        // Traverse upwards until we find a managed parent and use its state instead.\n        if (!state) {\n          hit.object.traverseAncestors(obj => {\n            const parentState = getRootState(obj);\n            if (parentState) {\n              state = parentState;\n              return false;\n            }\n          });\n        }\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\n        MAYBE TODO, DELETE IF NOT: \n          Check if the object is captured, captured events should not have intersects running in parallel\n          But wouldn't it be better to just replace capturedMap with a single entry?\n          Also, are we OK with straight up making picking up multiple objects impossible?\n          \n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \n        if (pointerId !== undefined) {\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\n          if (capturedMeshSet) {\n            const captured = capturedMeshSet.get(eventObject)\n            if (captured && captured.localState.stopped) return\n          }\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /* @__PURE__ */React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = createWithEqualityFn((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/React.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\n * Exposes an object's {@link Instance}.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\n *\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  React.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\n */\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\n * Executes a callback before render in a shared frame loop.\n * Can order effects with render priority or manually render with a positive priority.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\n * Returns a node graph of an object with named nodes & materials.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor$1(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\n * Synchronously loads and caches assets with a three loader.\n *\n * Note: this hook's caller must be wrapped with `React.Suspense`\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\n * Preloads an asset into cache as a side-effect.\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\n * Removes a loaded asset from cache.\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([loader, ...keys]);\n};\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nfunction createReconciler(config) {\n  const reconciler = Reconciler(config);\n  reconciler.injectIntoDevTools({\n    bundleType: typeof process !== 'undefined' && process.env.NODE_ENV !== 'production' ? 1 : 0,\n    rendererPackageName: '@react-three/fiber',\n    version: React.version\n  });\n  return reconciler;\n}\nconst NoEventPriority = 0;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = type => `${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = toPascalCase(type);\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  // Remove three* prefix from elements if native element not present\n  type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const target = catalogue[toPascalCase(child.type)];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n  }\n\n  // Set initial props\n  applyProps(child.object, child.props);\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      // If the child is already in the parent's children array, move it to the new position\n      // Otherwise, just insert it at the target position\n      const existingIndex = parent.object.children.indexOf(child.object);\n      if (existingIndex !== -1) {\n        parent.object.children.splice(existingIndex, 1);\n        const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n        parent.object.children.splice(adjustedIndex, 0, child.object);\n      } else {\n        child.object.parent = parent.object;\n        parent.object.children.splice(childIndex, 0, child.object);\n        child.object.dispatchEvent({\n          type: 'added'\n        });\n        parent.object.dispatchEvent({\n          type: 'childadded',\n          child: child.object\n        });\n      }\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else unstable_scheduleCallback(unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const target = catalogue[toPascalCase(instance.type)];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, make it no-op\nconst handleTextInstance = () => {};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition: () => false,\n  trackSchedulerEvent: () => {},\n  resolveEventType: () => null,\n  resolveEventTimeStamp: () => -1.1,\n  requestPostPaintCallback() {},\n  maySuspendCommit: () => false,\n  preloadInstance: () => true,\n  // true indicates already loaded\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady: () => null,\n  NotPendingTransition: null,\n  HostTransitionContext: /* @__PURE__ */React.createContext(null),\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return ContinuousEventPriority;\n      default:\n        return DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\n\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let lastCamera;\n  let configured = false;\n  let pending = null;\n  return {\n    async configure(props = {}) {\n      let resolve;\n      pending = new Promise(_resolve => resolve = _resolve);\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) {\n        const defaultProps = {\n          canvas: canvas,\n          powerPreference: 'high-performance',\n          antialias: true,\n          alpha: true\n        };\n        const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n        if (isRenderer(customRenderer)) {\n          gl = customRenderer;\n        } else {\n          gl = new THREE.WebGLRenderer({\n            ...defaultProps,\n            ...glConfig\n          });\n        }\n        state.set({\n          gl\n        });\n      }\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new THREE.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n      THREE.ColorManagement.enabled = !legacy;\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        gl.outputColorSpace = linear ? THREE.LinearSRGBColorSpace : THREE.SRGBColorSpace;\n        gl.toneMapping = flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping;\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      resolve();\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured && !pending) this.configure();\n      pending.then(() => {\n        reconciler.updateContainer( /*#__PURE__*/jsx(Provider, {\n          store: store,\n          children: children,\n          onCreated: onCreated,\n          rootElement: canvas\n        }), fiber, null, () => undefined);\n      });\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notify that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\n   *  <Canvas>\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = React.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = createWithEqualityFn((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return (\n    /*#__PURE__*/\n    // @ts-ignore, reconciler types are not maintained\n    jsx(Fragment, {\n      children: reconciler.createPortal( /*#__PURE__*/jsx(context.Provider, {\n        value: usePortalStore,\n        children: children\n      }), usePortalStore, null)\n    })\n  );\n}\n\n/**\n * Force React to flush any updates inside the provided callback synchronously and immediately.\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\n * having to revert to a non-React solution.\n */\nfunction flushSync(fn) {\n  return reconciler.flushSync(fn);\n}\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\n * Adds a global render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\n * Adds a global after-render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\n * Adds a global callback which is called when rendering stops.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = false;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      if (events.handlers) {\n        for (const name in events.handlers) {\n          const event = events.handlers[name];\n          const [eventName, passive] = DOM_EVENTS[name];\n          target.addEventListener(eventName, event, {\n            passive\n          });\n        }\n      }\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        if (events.handlers) {\n          for (const name in events.handlers) {\n            const event = events.handlers[name];\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        }\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\nexport { useStore as A, Block as B, useThree as C, useFrame as D, ErrorBoundary as E, useGraph as F, useLoader as G, _roots as _, useMutableCallback as a, useIsomorphicLayoutEffect as b, createRoot as c, unmountComponentAtNode as d, extend as e, createPointerEvents as f, createEvents as g, flushGlobalEffects as h, isRef as i, addEffect as j, addAfterEffect as k, addTail as l, invalidate as m, advance as n, createPortal as o, flushSync as p, context as q, reconciler as r, applyProps as s, threeTypes as t, useBridge as u, getRootState as v, dispose as w, act as x, buildGraph as y, useInstanceHandle as z };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ,4BAA4B;AACjI,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,yBAAyB,EAAEC,qBAAqB,QAAQ,WAAW;AAC5E,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AACvD,SAASC,GAAG,EAAEC,QAAQ,QAAQ,mBAAmB;AACjD,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,UAAU;AAEpE,IAAIC,UAAU,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EAC1CC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACjC,IAAIC,IAAI,GAAGD,QAAQ,CAACC,IAAI;EACxB,OAAOA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,YAAY,EAAEF,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,YAAY;EACxE,OAAOF,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,GAAG,GAAG1B,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,MAAM2B,oBAAoB,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,oBAAoB;AACnE,MAAME,KAAK,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,cAAc,CAAC,SAAS,CAAC;AACzD,MAAMC,qBAAqB,GAAGC,KAAK,IAAIA,KAAK,IAAI,IAAI,KAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,OAAO,CAAC;;AAEjI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,eAAe,CAAC,CAACC,gBAAgB,EAAEC,iBAAiB,KAAK,OAAOC,MAAM,KAAK,WAAW,KAAK,CAAC,CAACF,gBAAgB,GAAGE,MAAM,CAACC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,gBAAgB,CAACI,aAAa,KAAK,CAAC,CAACH,iBAAiB,GAAGC,MAAM,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,iBAAiB,CAACK,OAAO,MAAM,aAAa,CAAC,EAAE,CAAC,GAAG1C,KAAK,CAAC2C,eAAe,GAAG3C,KAAK,CAAC4C,SAAS;AAC9W,SAASC,kBAAkBA,CAACC,EAAE,EAAE;EAC9B,MAAMC,GAAG,GAAG/C,KAAK,CAACgD,MAAM,CAACF,EAAE,CAAC;EAC5BX,yBAAyB,CAAC,MAAM,MAAMY,GAAG,CAACE,OAAO,GAAGH,EAAE,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAC9D,OAAOC,GAAG;AACZ;AACA;AACA;AACA;AACA,SAASG,SAASA,CAAA,EAAG;EACnB,MAAMC,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EACxB,MAAMsC,aAAa,GAAGrC,gBAAgB,CAAC,CAAC;EACxC,OAAOf,KAAK,CAACqD,OAAO,CAAC,MAAM,CAAC;IAC1BC;EACF,CAAC,KAAK;IACJ,MAAMC,MAAM,GAAG,CAAC,CAACvC,aAAa,CAACmC,KAAK,EAAE,IAAI,EAAEK,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKzD,KAAK,CAAC0D,UAAU,CAAC;IACnF,MAAMC,IAAI,GAAGJ,MAAM,GAAGvD,KAAK,CAAC0D,UAAU,GAAG1D,KAAK,CAACa,QAAQ;IACvD,OAAO,aAAaD,GAAG,CAAC+C,IAAI,EAAE;MAC5BL,QAAQ,EAAE,aAAa1C,GAAG,CAACwC,aAAa,EAAE;QACxCE,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,KAAK,EAAEC,aAAa,CAAC,CAAC;AAC5B;AACA,SAASQ,KAAKA,CAAC;EACbC;AACF,CAAC,EAAE;EACD1B,yBAAyB,CAAC,MAAM;IAC9B0B,GAAG,CAAC,IAAIC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5B,OAAO,MAAMD,GAAG,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,OAAO,IAAI;AACb;;AAEA;AACA,MAAME,aAAa,GAAG,eAAe,CAACC,cAAc,KAAKA,cAAc,GAAG,MAAMD,aAAa,SAAS/D,KAAK,CAACiE,SAAS,CAAC;EACpHC,WAAWA,CAAC,GAAGC,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd,IAAI,CAACC,KAAK,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;EACH;EACAC,iBAAiBA,CAACC,GAAG,EAAE;IACrB,IAAI,CAACC,KAAK,CAACX,GAAG,CAACU,GAAG,CAAC;EACrB;EACAE,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACL,KAAK,CAACC,KAAK,GAAG,IAAI,GAAG,IAAI,CAACG,KAAK,CAAClB,QAAQ;EACtD;AACF,CAAC,EAAEU,cAAc,CAACU,wBAAwB,GAAG,OAAO;EAClDL,KAAK,EAAE;AACT,CAAC,CAAC,EAAEL,cAAc,CAAC,EAAE,CAAC;AACtB,SAASW,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIC,qBAAqB;EACzB;EACA;EACA,MAAMC,MAAM,GAAG,OAAOxC,MAAM,KAAK,WAAW,GAAG,CAACuC,qBAAqB,GAAGvC,MAAM,CAACyC,gBAAgB,KAAK,IAAI,GAAGF,qBAAqB,GAAG,CAAC,GAAG,CAAC;EACxI,OAAOG,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACR,GAAG,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,EAAEF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG;AAC9E;;AAEA;AACA;AACA;AACA,SAASS,YAAYA,CAACvD,GAAG,EAAE;EACzB,IAAIwD,IAAI;EACR,OAAO,CAACA,IAAI,GAAGxD,GAAG,CAACyD,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAAC/D,IAAI,CAACC,QAAQ,CAAC,CAAC;AACnE;AACA;AACA,MAAMgE,EAAE,GAAG;EACT1D,GAAG,EAAE2D,CAAC,IAAIA,CAAC,KAAKvE,MAAM,CAACuE,CAAC,CAAC,IAAI,CAACD,EAAE,CAACE,GAAG,CAACD,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EAClEE,GAAG,EAAEF,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EACjCG,GAAG,EAAEH,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BI,GAAG,EAAEJ,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BK,GAAG,EAAEL,CAAC,IAAI,OAAOA,CAAC,KAAK,SAAS;EAChCM,GAAG,EAAEN,CAAC,IAAIA,CAAC,KAAK,KAAK,CAAC;EACtBO,GAAG,EAAEP,CAAC,IAAIA,CAAC,KAAK,IAAI;EACpBC,GAAG,EAAED,CAAC,IAAIT,KAAK,CAACC,OAAO,CAACQ,CAAC,CAAC;EAC1BQ,GAAGA,CAACR,CAAC,EAAES,CAAC,EAAE;IACRC,MAAM,GAAG,SAAS;IAClBC,OAAO,GAAG,WAAW;IACrB7C,MAAM,GAAG;EACX,CAAC,GAAG,CAAC,CAAC,EAAE;IACN;IACA,IAAI,OAAOkC,CAAC,KAAK,OAAOS,CAAC,IAAI,CAAC,CAACT,CAAC,KAAK,CAAC,CAACS,CAAC,EAAE,OAAO,KAAK;IACtD;IACA,IAAIV,EAAE,CAACI,GAAG,CAACH,CAAC,CAAC,IAAID,EAAE,CAACK,GAAG,CAACJ,CAAC,CAAC,IAAID,EAAE,CAACM,GAAG,CAACL,CAAC,CAAC,EAAE,OAAOA,CAAC,KAAKS,CAAC;IACvD,MAAMG,KAAK,GAAGb,EAAE,CAAC1D,GAAG,CAAC2D,CAAC,CAAC;IACvB,IAAIY,KAAK,IAAID,OAAO,KAAK,WAAW,EAAE,OAAOX,CAAC,KAAKS,CAAC;IACpD,MAAMI,KAAK,GAAGd,EAAE,CAACE,GAAG,CAACD,CAAC,CAAC;IACvB,IAAIa,KAAK,IAAIH,MAAM,KAAK,WAAW,EAAE,OAAOV,CAAC,KAAKS,CAAC;IACnD;IACA,IAAI,CAACI,KAAK,IAAID,KAAK,KAAKZ,CAAC,KAAKS,CAAC,EAAE,OAAO,IAAI;IAC5C;IACA,IAAIK,CAAC;IACL;IACA,KAAKA,CAAC,IAAId,CAAC,EAAE,IAAI,EAAEc,CAAC,IAAIL,CAAC,CAAC,EAAE,OAAO,KAAK;IACxC;IACA,IAAIG,KAAK,IAAIF,MAAM,KAAK,SAAS,IAAIC,OAAO,KAAK,SAAS,EAAE;MAC1D,KAAKG,CAAC,IAAIhD,MAAM,GAAG2C,CAAC,GAAGT,CAAC,EAAE,IAAI,CAACD,EAAE,CAACS,GAAG,CAACR,CAAC,CAACc,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,CAAC,EAAE;QAChDhD,MAAM;QACN6C,OAAO,EAAE;MACX,CAAC,CAAC,EAAE,OAAO,KAAK;IAClB,CAAC,MAAM;MACL,KAAKG,CAAC,IAAIhD,MAAM,GAAG2C,CAAC,GAAGT,CAAC,EAAE,IAAIA,CAAC,CAACc,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,EAAE,OAAO,KAAK;IAC3D;IACA;IACA,IAAIf,EAAE,CAACO,GAAG,CAACQ,CAAC,CAAC,EAAE;MACb;MACA,IAAID,KAAK,IAAIb,CAAC,CAACe,MAAM,KAAK,CAAC,IAAIN,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC1D;MACA,IAAIH,KAAK,IAAInF,MAAM,CAACuF,IAAI,CAAChB,CAAC,CAAC,CAACe,MAAM,KAAK,CAAC,IAAItF,MAAM,CAACuF,IAAI,CAACP,CAAC,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACpF;MACA,IAAIf,CAAC,KAAKS,CAAC,EAAE,OAAO,KAAK;IAC3B;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,SAASQ,UAAUA,CAACC,MAAM,EAAE;EAC1B,MAAMC,IAAI,GAAG;IACXC,KAAK,EAAE,CAAC,CAAC;IACTC,SAAS,EAAE,CAAC,CAAC;IACbC,MAAM,EAAE,CAAC;EACX,CAAC;EACD,IAAIJ,MAAM,EAAE;IACVA,MAAM,CAACK,QAAQ,CAAClF,GAAG,IAAI;MACrB,IAAIA,GAAG,CAACmF,IAAI,EAAEL,IAAI,CAACC,KAAK,CAAC/E,GAAG,CAACmF,IAAI,CAAC,GAAGnF,GAAG;MACxC,IAAIA,GAAG,CAACoF,QAAQ,IAAI,CAACN,IAAI,CAACE,SAAS,CAAChF,GAAG,CAACoF,QAAQ,CAACD,IAAI,CAAC,EAAEL,IAAI,CAACE,SAAS,CAAChF,GAAG,CAACoF,QAAQ,CAACD,IAAI,CAAC,GAAGnF,GAAG,CAACoF,QAAQ;MACxG,IAAIpF,GAAG,CAACqF,MAAM,IAAI,CAACP,IAAI,CAACG,MAAM,CAACjF,GAAG,CAACmF,IAAI,CAAC,EAAEL,IAAI,CAACG,MAAM,CAACjF,GAAG,CAACmF,IAAI,CAAC,GAAGnF,GAAG;IACvE,CAAC,CAAC;EACJ;EACA,OAAO8E,IAAI;AACb;AACA;AACA,SAASQ,OAAOA,CAACtF,GAAG,EAAE;EACpB,IAAIA,GAAG,CAAC2B,IAAI,KAAK,OAAO,EAAE3B,GAAG,CAACsF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtF,GAAG,CAACsF,OAAO,CAAC,CAAC;EACtE,KAAK,MAAMC,CAAC,IAAIvF,GAAG,EAAE;IACnB,MAAMwF,IAAI,GAAGxF,GAAG,CAACuF,CAAC,CAAC;IACnB,IAAI,CAACC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC7D,IAAI,MAAM,OAAO,EAAE6D,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACF,OAAO,CAAC,CAAC;EAC7H;AACF;AACA,MAAMG,oBAAoB,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC;;AAEvD;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMjD,KAAK,GAAG,CAAC,CAAC;EAChB,KAAK,MAAMkD,GAAG,IAAID,KAAK,EAAE;IACvB,IAAI,CAACF,oBAAoB,CAACI,QAAQ,CAACD,GAAG,CAAC,EAAElD,KAAK,CAACkD,GAAG,CAAC,GAAGD,KAAK,CAACC,GAAG,CAAC;EAClE;EACA,OAAOlD,KAAK;AACd;;AAEA;AACA,SAASoD,OAAOA,CAAC9C,MAAM,EAAEvD,IAAI,EAAEkC,IAAI,EAAEe,KAAK,EAAE;EAC1C,MAAMmC,MAAM,GAAG7B,MAAM;;EAErB;EACA,IAAIxD,QAAQ,GAAGqF,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACpB,KAAK;EACrD,IAAI,CAACjE,QAAQ,EAAE;IACbA,QAAQ,GAAG;MACTC,IAAI;MACJkC,IAAI;MACJoE,MAAM,EAAE,IAAI;MACZvE,QAAQ,EAAE,EAAE;MACZkB,KAAK,EAAEgD,gBAAgB,CAAChD,KAAK,CAAC;MAC9BmC,MAAM;MACNmB,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC,CAAC;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIrB,MAAM,EAAEA,MAAM,CAACpB,KAAK,GAAGjE,QAAQ;EACrC;EACA,OAAOA,QAAQ;AACjB;AACA,SAAS2G,OAAOA,CAAC1G,IAAI,EAAEmG,GAAG,EAAE;EAC1B,IAAI5C,MAAM,GAAGvD,IAAI,CAACmG,GAAG,CAAC;EACtB,IAAI,CAACA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;IAC7BpG,IAAI;IACJmG,GAAG;IACH5C;EACF,CAAC;;EAED;EACAA,MAAM,GAAGvD,IAAI;EACb,KAAK,MAAM2G,IAAI,IAAIR,GAAG,CAACS,KAAK,CAAC,GAAG,CAAC,EAAE;IACjC,IAAIC,OAAO;IACXV,GAAG,GAAGQ,IAAI;IACV3G,IAAI,GAAGuD,MAAM;IACbA,MAAM,GAAG,CAACsD,OAAO,GAAGtD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsD,OAAO,CAACV,GAAG,CAAC;EAC7D;;EAEA;;EAEA,OAAO;IACLnG,IAAI;IACJmG,GAAG;IACH5C;EACF,CAAC;AACH;;AAEA;AACA,MAAMuD,WAAW,GAAG,OAAO;AAC3B,SAASC,MAAMA,CAACT,MAAM,EAAEU,KAAK,EAAE;EAC7B,IAAI/C,EAAE,CAACI,GAAG,CAAC2C,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC,EAAE;IAC9B;IACA,IAAID,WAAW,CAACG,IAAI,CAACD,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC,EAAE;MACxC,MAAMG,KAAK,GAAGF,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAACI,OAAO,CAACL,WAAW,EAAE,EAAE,CAAC;MACzD,MAAM;QACJ9G,IAAI;QACJmG;MACF,CAAC,GAAGO,OAAO,CAACJ,MAAM,CAAClB,MAAM,EAAE8B,KAAK,CAAC;MACjC,IAAI,CAACzD,KAAK,CAACC,OAAO,CAAC1D,IAAI,CAACmG,GAAG,CAAC,CAAC,EAAEnG,IAAI,CAACmG,GAAG,CAAC,GAAG,EAAE;IAC/C;IACA,MAAM;MACJnG,IAAI;MACJmG;IACF,CAAC,GAAGO,OAAO,CAACJ,MAAM,CAAClB,MAAM,EAAE4B,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC;IAC9CC,KAAK,CAACI,cAAc,GAAGpH,IAAI,CAACmG,GAAG,CAAC;IAChCnG,IAAI,CAACmG,GAAG,CAAC,GAAGa,KAAK,CAAC5B,MAAM;EAC1B,CAAC,MAAM,IAAInB,EAAE,CAACG,GAAG,CAAC4C,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC,EAAE;IACrCC,KAAK,CAACI,cAAc,GAAGJ,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAACT,MAAM,CAAClB,MAAM,EAAE4B,KAAK,CAAC5B,MAAM,CAAC;EACxE;AACF;AACA,SAASiC,MAAMA,CAACf,MAAM,EAAEU,KAAK,EAAE;EAC7B,IAAI/C,EAAE,CAACI,GAAG,CAAC2C,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC,EAAE;IAC9B,MAAM;MACJ/G,IAAI;MACJmG;IACF,CAAC,GAAGO,OAAO,CAACJ,MAAM,CAAClB,MAAM,EAAE4B,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,CAAC;IAC9C,MAAMO,QAAQ,GAAGN,KAAK,CAACI,cAAc;IACrC;IACA,IAAIE,QAAQ,KAAKC,SAAS,EAAE,OAAOvH,IAAI,CAACmG,GAAG,CAAC;IAC5C;IAAA,KACKnG,IAAI,CAACmG,GAAG,CAAC,GAAGmB,QAAQ;EAC3B,CAAC,MAAM;IACLN,KAAK,CAACI,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,KAAK,CAACI,cAAc,CAACd,MAAM,CAAClB,MAAM,EAAE4B,KAAK,CAAC5B,MAAM,CAAC;EAC3F;EACA,OAAO4B,KAAK,CAACI,cAAc;AAC7B;AACA,MAAMI,cAAc,GAAG,CAAC,GAAGxB,oBAAoB;AAC/C;AACA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;AACjD;AACA,SAAS,CAAC;AACV,MAAMyB,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC,SAASC,oBAAoBA,CAAC3H,IAAI,EAAE;EAClC,IAAI4H,IAAI,GAAGH,mBAAmB,CAACI,GAAG,CAAC7H,IAAI,CAAC2C,WAAW,CAAC;EACpD,IAAI;IACF,IAAI,CAACiF,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI5H,IAAI,CAAC2C,WAAW,CAAC,CAAC;MAC7B8E,mBAAmB,CAACnF,GAAG,CAACtC,IAAI,CAAC2C,WAAW,EAAEiF,IAAI,CAAC;IACjD;EACF,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV;EAAA;EAEF,OAAOF,IAAI;AACb;;AAEA;AACA,SAASG,SAASA,CAAChI,QAAQ,EAAEiI,QAAQ,EAAE;EACrC,MAAMC,YAAY,GAAG,CAAC,CAAC;;EAEvB;EACA,KAAK,MAAMlC,IAAI,IAAIiC,QAAQ,EAAE;IAC3B;IACA,IAAIR,cAAc,CAACpB,QAAQ,CAACL,IAAI,CAAC,EAAE;IACnC;IACA,IAAI9B,EAAE,CAACS,GAAG,CAACsD,QAAQ,CAACjC,IAAI,CAAC,EAAEhG,QAAQ,CAACkD,KAAK,CAAC8C,IAAI,CAAC,CAAC,EAAE;;IAElD;IACAkC,YAAY,CAAClC,IAAI,CAAC,GAAGiC,QAAQ,CAACjC,IAAI,CAAC;;IAEnC;IACA,KAAK,MAAMmC,KAAK,IAAIF,QAAQ,EAAE;MAC5B,IAAIE,KAAK,CAACC,UAAU,CAAC,GAAGpC,IAAI,GAAG,CAAC,EAAEkC,YAAY,CAACC,KAAK,CAAC,GAAGF,QAAQ,CAACE,KAAK,CAAC;IACzE;EACF;;EAEA;EACA,KAAK,MAAMnC,IAAI,IAAIhG,QAAQ,CAACkD,KAAK,EAAE;IACjC,IAAIuE,cAAc,CAACpB,QAAQ,CAACL,IAAI,CAAC,IAAIiC,QAAQ,CAACxH,cAAc,CAACuF,IAAI,CAAC,EAAE;IACpE,MAAM;MACJ/F,IAAI;MACJmG;IACF,CAAC,GAAGO,OAAO,CAAC3G,QAAQ,CAACqF,MAAM,EAAEW,IAAI,CAAC;;IAElC;IACA;IACA;IACA;IACA;IACA,IAAI/F,IAAI,CAAC2C,WAAW,IAAI3C,IAAI,CAAC2C,WAAW,CAACsC,MAAM,KAAK,CAAC,EAAE;MACrD;MACA,MAAM2C,IAAI,GAAGD,oBAAoB,CAAC3H,IAAI,CAAC;MACvC,IAAI,CAACiE,EAAE,CAACO,GAAG,CAACoD,IAAI,CAAC,EAAEK,YAAY,CAAC9B,GAAG,CAAC,GAAGyB,IAAI,CAACzB,GAAG,CAAC;IAClD,CAAC,MAAM;MACL;MACA8B,YAAY,CAAC9B,GAAG,CAAC,GAAG,CAAC;IACvB;EACF;EACA,OAAO8B,YAAY;AACrB;;AAEA;AACA;AACA,MAAMG,SAAS,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC;AACvF,MAAMC,WAAW,GAAG,kDAAkD;AACtE;AACA,SAASC,UAAUA,CAAClD,MAAM,EAAEnC,KAAK,EAAE;EACjC,IAAIsF,gBAAgB;EACpB,MAAMxI,QAAQ,GAAGqF,MAAM,CAACpB,KAAK;EAC7B,MAAMwE,SAAS,GAAGzI,QAAQ,IAAID,eAAe,CAACC,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAAC;EAClE,MAAMwI,YAAY,GAAG1I,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwG,UAAU;EACpE,KAAK,MAAMR,IAAI,IAAI9C,KAAK,EAAE;IACxB,IAAIvC,KAAK,GAAGuC,KAAK,CAAC8C,IAAI,CAAC;;IAEvB;IACA,IAAIyB,cAAc,CAACpB,QAAQ,CAACL,IAAI,CAAC,EAAE;;IAEnC;IACA,IAAIhG,QAAQ,IAAIsI,WAAW,CAACpB,IAAI,CAAClB,IAAI,CAAC,EAAE;MACtC,IAAI,OAAOrF,KAAK,KAAK,UAAU,EAAEX,QAAQ,CAACyG,QAAQ,CAACT,IAAI,CAAC,GAAGrF,KAAK,CAAC,KAAK,OAAOX,QAAQ,CAACyG,QAAQ,CAACT,IAAI,CAAC;MACpGhG,QAAQ,CAACwG,UAAU,GAAG5G,MAAM,CAACuF,IAAI,CAACnF,QAAQ,CAACyG,QAAQ,CAAC,CAACvB,MAAM;MAC3D;IACF;;IAEA;IACA;IACA,IAAIvE,KAAK,KAAK6G,SAAS,EAAE;IACzB,IAAI;MACFvH,IAAI;MACJmG,GAAG;MACH5C;IACF,CAAC,GAAGmD,OAAO,CAACtB,MAAM,EAAEW,IAAI,CAAC;;IAEzB;IACA,IAAIxC,MAAM,YAAY/E,KAAK,CAACkK,MAAM,IAAIhI,KAAK,YAAYlC,KAAK,CAACkK,MAAM,EAAE;MACnEnF,MAAM,CAACoF,IAAI,GAAGjI,KAAK,CAACiI,IAAI;IAC1B;IACA;IAAA,KACK,IAAIpF,MAAM,YAAY/E,KAAK,CAACoK,KAAK,IAAInI,qBAAqB,CAACC,KAAK,CAAC,EAAE;MACtE6C,MAAM,CAACjB,GAAG,CAAC5B,KAAK,CAAC;IACnB;IACA;IAAA,KACK,IAAI6C,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACjB,GAAG,KAAK,UAAU,IAAI,OAAOiB,MAAM,CAACsF,IAAI,KAAK,UAAU,IAAInI,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACiC,WAAW,IAAIY,MAAM,CAACZ,WAAW,KAAKjC,KAAK,CAACiC,WAAW,EAAE;MACjNY,MAAM,CAACsF,IAAI,CAACnI,KAAK,CAAC;IACpB;IACA;IAAA,KACK,IAAI6C,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACjB,GAAG,KAAK,UAAU,IAAImB,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;MAClH,IAAI,OAAO6C,MAAM,CAACuF,SAAS,KAAK,UAAU,EAAEvF,MAAM,CAACuF,SAAS,CAACpI,KAAK,CAAC,CAAC,KAAK6C,MAAM,CAACjB,GAAG,CAAC,GAAG5B,KAAK,CAAC;IAC/F;IACA;IAAA,KACK,IAAI6C,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACjB,GAAG,KAAK,UAAU,IAAI,OAAO5B,KAAK,KAAK,QAAQ,EAAE;MACvH;MACA,IAAI,OAAO6C,MAAM,CAACwF,SAAS,KAAK,UAAU,EAAExF,MAAM,CAACwF,SAAS,CAACrI,KAAK,CAAC;MACnE;MAAA,KACK6C,MAAM,CAACjB,GAAG,CAAC5B,KAAK,CAAC;IACxB;IACA;IAAA,KACK;MACH,IAAIsI,SAAS;MACbhJ,IAAI,CAACmG,GAAG,CAAC,GAAGzF,KAAK;;MAEjB;MACA;MACA;MACA,IAAI8H,SAAS,IAAI,CAACA,SAAS,CAACS,MAAM,IAAIb,SAAS,CAAChC,QAAQ,CAACD,GAAG,CAAC,IAAI,CAAC6C,SAAS,GAAGhJ,IAAI,CAACmG,GAAG,CAAC,KAAK,IAAI,IAAI6C,SAAS,CAACE,SAAS;MACvH;MACAlJ,IAAI,CAACmG,GAAG,CAAC,CAACgD,MAAM,KAAK3K,KAAK,CAAC4K,UAAU,IAAIpJ,IAAI,CAACmG,GAAG,CAAC,CAACjE,IAAI,KAAK1D,KAAK,CAAC6K,gBAAgB,EAAE;QAClF;QACArJ,IAAI,CAACmG,GAAG,CAAC,CAACmD,UAAU,GAAG9K,KAAK,CAAC+K,cAAc;MAC7C;IACF;EACF;;EAEA;EACA,IAAIxJ,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACuG,MAAM,IAAIkC,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACgB,QAAQ,IAAI,CAACjB,gBAAgB,GAAGxI,QAAQ,CAACqF,MAAM,KAAK,IAAI,IAAImD,gBAAgB,CAACkB,UAAU,IAAIhB,YAAY,KAAK1I,QAAQ,CAACwG,UAAU,EAAE;IACzM,MAAMnB,MAAM,GAAGrF,QAAQ,CAACqF,MAAM;IAC9B;IACA,MAAM8B,KAAK,GAAGsB,SAAS,CAACgB,QAAQ,CAACE,WAAW,CAACC,OAAO,CAACvE,MAAM,CAAC;IAC5D,IAAI8B,KAAK,GAAG,CAAC,CAAC,EAAEsB,SAAS,CAACgB,QAAQ,CAACE,WAAW,CAACE,MAAM,CAAC1C,KAAK,EAAE,CAAC,CAAC;IAC/D;IACA,IAAInH,QAAQ,CAACwG,UAAU,IAAInB,MAAM,CAACyE,OAAO,KAAK,IAAI,EAAE;MAClDrB,SAAS,CAACgB,QAAQ,CAACE,WAAW,CAACI,IAAI,CAAC1E,MAAM,CAAC;IAC7C;EACF;;EAEA;EACA,IAAIrF,QAAQ,IAAIA,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,KAAKQ,SAAS,EAAE;IACnD,IAAIxH,QAAQ,CAACqF,MAAM,CAAC2E,gBAAgB,EAAEhK,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,GAAG,UAAU,CAAC,KAAK,IAAIhH,QAAQ,CAACqF,MAAM,CAAC4E,UAAU,EAAEjK,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,GAAG,UAAU;EAClJ;;EAEA;EACA,IAAIhH,QAAQ,EAAEkK,kBAAkB,CAAClK,QAAQ,CAAC;EAC1C,OAAOqF,MAAM;AACf;AACA,SAAS6E,kBAAkBA,CAAClK,QAAQ,EAAE;EACpC,IAAImK,cAAc;EAClB,IAAI,CAACnK,QAAQ,CAACuG,MAAM,EAAE;EACtBvG,QAAQ,CAACkD,KAAK,CAACkH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpK,QAAQ,CAACkD,KAAK,CAACkH,QAAQ,CAACpK,QAAQ,CAACqF,MAAM,CAAC;EACnF,MAAMvC,KAAK,GAAG,CAACqH,cAAc,GAAGnK,QAAQ,CAACC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkK,cAAc,CAACjK,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGiK,cAAc,CAACjK,QAAQ,CAAC,CAAC;EACtI,IAAI4C,KAAK,IAAIA,KAAK,CAAC2G,QAAQ,CAACY,MAAM,KAAK,CAAC,EAAEvH,KAAK,CAACwH,UAAU,CAAC,CAAC;AAC9D;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAClC;EACA;EACA,IAAID,MAAM,CAACE,MAAM,EAAE;EACnB,IAAIrK,oBAAoB,CAACmK,MAAM,CAAC,EAAE;IAChCA,MAAM,CAACG,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IAC7BJ,MAAM,CAACK,KAAK,GAAGJ,IAAI,CAACG,KAAK,GAAG,CAAC;IAC7BJ,MAAM,CAACM,GAAG,GAAGL,IAAI,CAACM,MAAM,GAAG,CAAC;IAC5BP,MAAM,CAACQ,MAAM,GAAGP,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;EAClC,CAAC,MAAM;IACLP,MAAM,CAACS,MAAM,GAAGR,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACM,MAAM;EAC1C;EACAP,MAAM,CAACU,sBAAsB,CAAC,CAAC;AACjC;AACA,MAAMxB,UAAU,GAAGrE,MAAM,IAAIA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACqE,UAAU;AAExE,SAASyB,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAO,CAACA,KAAK,CAACC,WAAW,IAAID,KAAK,CAAC/F,MAAM,EAAEiG,IAAI,GAAG,GAAG,GAAGF,KAAK,CAACjE,KAAK,GAAGiE,KAAK,CAACG,UAAU;AACxF;;AAEA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,WAAW,EAAEjL,GAAG,EAAEkL,QAAQ,EAAEC,SAAS,EAAE;EAC5E,MAAMC,WAAW,GAAGF,QAAQ,CAAC5D,GAAG,CAACtH,GAAG,CAAC;EACrC,IAAIoL,WAAW,EAAE;IACfF,QAAQ,CAACG,MAAM,CAACrL,GAAG,CAAC;IACpB;IACA,IAAIkL,QAAQ,CAACjB,IAAI,KAAK,CAAC,EAAE;MACvBgB,WAAW,CAACI,MAAM,CAACF,SAAS,CAAC;MAC7BC,WAAW,CAACpI,MAAM,CAACsI,qBAAqB,CAACH,SAAS,CAAC;IACrD;EACF;AACF;AACA,SAASI,mBAAmBA,CAACC,KAAK,EAAE3G,MAAM,EAAE;EAC1C,MAAM;IACJoE;EACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;EACpB;EACAuJ,QAAQ,CAACE,WAAW,GAAGF,QAAQ,CAACE,WAAW,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK7G,MAAM,CAAC;EACrEoE,QAAQ,CAAC0C,WAAW,GAAG1C,QAAQ,CAAC0C,WAAW,CAACF,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK7G,MAAM,CAAC;EACrEoE,QAAQ,CAAC2C,OAAO,CAACC,OAAO,CAAC,CAAC1L,KAAK,EAAEyF,GAAG,KAAK;IACvC,IAAIzF,KAAK,CAAC0K,WAAW,KAAKhG,MAAM,IAAI1E,KAAK,CAAC0E,MAAM,KAAKA,MAAM,EAAE;MAC3D;MACAoE,QAAQ,CAAC2C,OAAO,CAACP,MAAM,CAACzF,GAAG,CAAC;IAC9B;EACF,CAAC,CAAC;EACFqD,QAAQ,CAACgC,WAAW,CAACY,OAAO,CAAC,CAACX,QAAQ,EAAEC,SAAS,KAAK;IACpDH,6BAA6B,CAAC/B,QAAQ,CAACgC,WAAW,EAAEpG,MAAM,EAAEqG,QAAQ,EAAEC,SAAS,CAAC;EAClF,CAAC,CAAC;AACJ;AACA,SAASW,YAAYA,CAACN,KAAK,EAAE;EAC3B;EACA,SAASO,iBAAiBA,CAACnB,KAAK,EAAE;IAChC,MAAM;MACJ3B;IACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;IACpB,MAAMsM,EAAE,GAAGpB,KAAK,CAACqB,OAAO,GAAGhD,QAAQ,CAACiD,YAAY,CAAC,CAAC,CAAC;IACnD,MAAMC,EAAE,GAAGvB,KAAK,CAACwB,OAAO,GAAGnD,QAAQ,CAACiD,YAAY,CAAC,CAAC,CAAC;IACnD,OAAO9I,IAAI,CAACiJ,KAAK,CAACjJ,IAAI,CAACkJ,IAAI,CAACN,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE,CAAC,CAAC;EACjD;;EAEA;EACA,SAASI,mBAAmBA,CAACjI,OAAO,EAAE;IACpC,OAAOA,OAAO,CAACmH,MAAM,CAACzL,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAACwM,IAAI,CAACrH,IAAI,IAAI;MAClF,IAAI3B,IAAI;MACR,OAAO,CAACA,IAAI,GAAGxD,GAAG,CAACyD,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACyC,QAAQ,CAAC,WAAW,GAAGd,IAAI,CAAC;IAChF,CAAC,CAAC,CAAC;EACL;EACA,SAASsH,SAASA,CAAC7B,KAAK,EAAEa,MAAM,EAAE;IAChC,MAAMnJ,KAAK,GAAGkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC;IAC9B,MAAMgN,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,MAAMC,aAAa,GAAG,EAAE;IACxB;IACA,MAAMC,aAAa,GAAGpB,MAAM,GAAGA,MAAM,CAACnJ,KAAK,CAAC2G,QAAQ,CAACE,WAAW,CAAC,GAAG7G,KAAK,CAAC2G,QAAQ,CAACE,WAAW;IAC9F;IACA,KAAK,IAAI1E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoI,aAAa,CAACnI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMnC,KAAK,GAAGiB,YAAY,CAACsJ,aAAa,CAACpI,CAAC,CAAC,CAAC;MAC5C,IAAInC,KAAK,EAAE;QACTA,KAAK,CAACwK,SAAS,CAAC9C,MAAM,GAAGhD,SAAS;MACpC;IACF;IACA,IAAI,CAAC1E,KAAK,CAAC3C,YAAY,EAAE;MACvB;MACA2C,KAAK,CAACyK,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG1K,KAAK,CAACyK,MAAM,CAACC,OAAO,CAACpC,KAAK,EAAEtI,KAAK,CAAC;IAC5E;IACA,SAAS2K,aAAaA,CAACjN,GAAG,EAAE;MAC1B,MAAMsC,KAAK,GAAGiB,YAAY,CAACvD,GAAG,CAAC;MAC/B;MACA,IAAI,CAACsC,KAAK,IAAI,CAACA,KAAK,CAACyK,MAAM,CAACG,OAAO,IAAI5K,KAAK,CAACwK,SAAS,CAAC9C,MAAM,KAAK,IAAI,EAAE,OAAO,EAAE;;MAEjF;MACA,IAAI1H,KAAK,CAACwK,SAAS,CAAC9C,MAAM,KAAKhD,SAAS,EAAE;QACxC,IAAImG,mBAAmB;QACvB7K,KAAK,CAACyK,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG1K,KAAK,CAACyK,MAAM,CAACC,OAAO,CAACpC,KAAK,EAAEtI,KAAK,EAAE,CAAC6K,mBAAmB,GAAG7K,KAAK,CAAC3C,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwN,mBAAmB,CAACzN,QAAQ,CAAC,CAAC,CAAC;QACxK;QACA,IAAI4C,KAAK,CAACwK,SAAS,CAAC9C,MAAM,KAAKhD,SAAS,EAAE1E,KAAK,CAACwK,SAAS,CAAC9C,MAAM,GAAG,IAAI;MACzE;;MAEA;MACA,OAAO1H,KAAK,CAACwK,SAAS,CAAC9C,MAAM,GAAG1H,KAAK,CAACwK,SAAS,CAACM,eAAe,CAACpN,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;IACjF;;IAEA;IACA,IAAIqN,IAAI,GAAGR;IACX;IAAA,CACCS,OAAO,CAACL,aAAa;IACtB;IAAA,CACCM,IAAI,CAAC,CAAC5J,CAAC,EAAES,CAAC,KAAK;MACd,MAAMoJ,MAAM,GAAGjK,YAAY,CAACI,CAAC,CAACkB,MAAM,CAAC;MACrC,MAAM4I,MAAM,GAAGlK,YAAY,CAACa,CAAC,CAACS,MAAM,CAAC;MACrC,IAAI,CAAC2I,MAAM,IAAI,CAACC,MAAM,EAAE,OAAO9J,CAAC,CAAC+J,QAAQ,GAAGtJ,CAAC,CAACsJ,QAAQ;MACtD,OAAOD,MAAM,CAACV,MAAM,CAACY,QAAQ,GAAGH,MAAM,CAACT,MAAM,CAACY,QAAQ,IAAIhK,CAAC,CAAC+J,QAAQ,GAAGtJ,CAAC,CAACsJ,QAAQ;IACnF,CAAC;IACD;IAAA,CACCjC,MAAM,CAACmC,IAAI,IAAI;MACd,MAAMC,EAAE,GAAGlD,MAAM,CAACiD,IAAI,CAAC;MACvB,IAAIlB,UAAU,CAACoB,GAAG,CAACD,EAAE,CAAC,EAAE,OAAO,KAAK;MACpCnB,UAAU,CAACqB,GAAG,CAACF,EAAE,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA;IACA,IAAIvL,KAAK,CAACyK,MAAM,CAACtB,MAAM,EAAE4B,IAAI,GAAG/K,KAAK,CAACyK,MAAM,CAACtB,MAAM,CAAC4B,IAAI,EAAE/K,KAAK,CAAC;;IAEhE;IACA,KAAK,MAAM0L,GAAG,IAAIX,IAAI,EAAE;MACtB,IAAIxC,WAAW,GAAGmD,GAAG,CAACnJ,MAAM;MAC5B;MACA,OAAOgG,WAAW,EAAE;QAClB,IAAIoD,KAAK;QACT,IAAI,CAACA,KAAK,GAAGpD,WAAW,CAACpH,KAAK,KAAK,IAAI,IAAIwK,KAAK,CAACjI,UAAU,EAAE4G,aAAa,CAACrD,IAAI,CAAC;UAC9E,GAAGyE,GAAG;UACNnD;QACF,CAAC,CAAC;QACFA,WAAW,GAAGA,WAAW,CAAC9E,MAAM;MAClC;IACF;;IAEA;IACA,IAAI,WAAW,IAAI6E,KAAK,IAAItI,KAAK,CAAC2G,QAAQ,CAACgC,WAAW,CAAC6C,GAAG,CAAClD,KAAK,CAACO,SAAS,CAAC,EAAE;MAC3E,KAAK,IAAIC,WAAW,IAAI9I,KAAK,CAAC2G,QAAQ,CAACgC,WAAW,CAAC3D,GAAG,CAACsD,KAAK,CAACO,SAAS,CAAC,CAAC+C,MAAM,CAAC,CAAC,EAAE;QAChF,IAAI,CAACxB,UAAU,CAACoB,GAAG,CAACnD,MAAM,CAACS,WAAW,CAAC+C,YAAY,CAAC,CAAC,EAAEvB,aAAa,CAACrD,IAAI,CAAC6B,WAAW,CAAC+C,YAAY,CAAC;MACrG;IACF;IACA,OAAOvB,aAAa;EACtB;;EAEA;EACA,SAASwB,gBAAgBA,CAACxB,aAAa,EAAEhC,KAAK,EAAEyD,KAAK,EAAEC,QAAQ,EAAE;IAC/D;IACA,IAAI1B,aAAa,CAAClI,MAAM,EAAE;MACxB,MAAM6J,UAAU,GAAG;QACjBC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,MAAMR,GAAG,IAAIpB,aAAa,EAAE;QAC/B,IAAItK,KAAK,GAAGiB,YAAY,CAACyK,GAAG,CAACnJ,MAAM,CAAC;;QAEpC;QACA;QACA,IAAI,CAACvC,KAAK,EAAE;UACV0L,GAAG,CAACnJ,MAAM,CAAC4J,iBAAiB,CAACzO,GAAG,IAAI;YAClC,MAAM0O,WAAW,GAAGnL,YAAY,CAACvD,GAAG,CAAC;YACrC,IAAI0O,WAAW,EAAE;cACfpM,KAAK,GAAGoM,WAAW;cACnB,OAAO,KAAK;YACd;UACF,CAAC,CAAC;QACJ;QACA,IAAIpM,KAAK,EAAE;UACT,MAAM;YACJwK,SAAS;YACT6B,OAAO;YACP3E,MAAM;YACNf;UACF,CAAC,GAAG3G,KAAK;UACT,MAAMsM,gBAAgB,GAAG,IAAI3Q,KAAK,CAAC4Q,OAAO,CAACF,OAAO,CAACG,CAAC,EAAEH,OAAO,CAACI,CAAC,EAAE,CAAC,CAAC,CAACC,SAAS,CAAChF,MAAM,CAAC;UACrF,MAAMiF,iBAAiB,GAAGpB,EAAE,IAAI;YAC9B,IAAIqB,qBAAqB,EAAEC,sBAAsB;YACjD,OAAO,CAACD,qBAAqB,GAAG,CAACC,sBAAsB,GAAGlG,QAAQ,CAACgC,WAAW,CAAC3D,GAAG,CAACuG,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,sBAAsB,CAACrB,GAAG,CAACE,GAAG,CAACnD,WAAW,CAAC,KAAK,IAAI,GAAGqE,qBAAqB,GAAG,KAAK;UACjM,CAAC;UACD,MAAME,iBAAiB,GAAGvB,EAAE,IAAI;YAC9B,MAAMzC,WAAW,GAAG;cAClB+C,YAAY,EAAEH,GAAG;cACjBhL,MAAM,EAAE4H,KAAK,CAAC5H;YAChB,CAAC;YACD,IAAIiG,QAAQ,CAACgC,WAAW,CAAC6C,GAAG,CAACD,EAAE,CAAC,EAAE;cAChC;cACA;cACA5E,QAAQ,CAACgC,WAAW,CAAC3D,GAAG,CAACuG,EAAE,CAAC,CAAC9L,GAAG,CAACiM,GAAG,CAACnD,WAAW,EAAEO,WAAW,CAAC;YAChE,CAAC,MAAM;cACL;cACA;cACA;cACAnC,QAAQ,CAACgC,WAAW,CAAClJ,GAAG,CAAC8L,EAAE,EAAE,IAAI1G,GAAG,CAAC,CAAC,CAAC6G,GAAG,CAACnD,WAAW,EAAEO,WAAW,CAAC,CAAC,CAAC,CAAC;YACzE;YACAR,KAAK,CAAC5H,MAAM,CAACoM,iBAAiB,CAACvB,EAAE,CAAC;UACpC,CAAC;UACD,MAAMvC,qBAAqB,GAAGuC,EAAE,IAAI;YAClC,MAAM3C,QAAQ,GAAGjC,QAAQ,CAACgC,WAAW,CAAC3D,GAAG,CAACuG,EAAE,CAAC;YAC7C,IAAI3C,QAAQ,EAAE;cACZF,6BAA6B,CAAC/B,QAAQ,CAACgC,WAAW,EAAE+C,GAAG,CAACnD,WAAW,EAAEK,QAAQ,EAAE2C,EAAE,CAAC;YACpF;UACF,CAAC;;UAED;UACA,IAAIwB,iBAAiB,GAAG,CAAC,CAAC;UAC1B;UACA,KAAK,IAAI7J,IAAI,IAAIoF,KAAK,EAAE;YACtB,IAAI0E,QAAQ,GAAG1E,KAAK,CAACpF,IAAI,CAAC;YAC1B;YACA;YACA,IAAI,OAAO8J,QAAQ,KAAK,UAAU,EAAED,iBAAiB,CAAC7J,IAAI,CAAC,GAAG8J,QAAQ;UACxE;UACA,IAAIC,YAAY,GAAG;YACjB,GAAGvB,GAAG;YACN,GAAGqB,iBAAiB;YACpBV,OAAO;YACP/B,aAAa;YACb4B,OAAO,EAAED,UAAU,CAACC,OAAO;YAC3BH,KAAK;YACLO,gBAAgB;YAChBY,GAAG,EAAE1C,SAAS,CAAC0C,GAAG;YAClBxF,MAAM,EAAEA,MAAM;YACd;YACAyF,eAAeA,CAAA,EAAG;cAChB;cACA;cACA,MAAMC,kBAAkB,GAAG,WAAW,IAAI9E,KAAK,IAAI3B,QAAQ,CAACgC,WAAW,CAAC3D,GAAG,CAACsD,KAAK,CAACO,SAAS,CAAC;;cAE5F;cACA;cACA;cACA,CAACuE,kBAAkB;cACnB;cACAA,kBAAkB,CAAC5B,GAAG,CAACE,GAAG,CAACnD,WAAW,CAAC,EAAE;gBACvC0E,YAAY,CAACf,OAAO,GAAGD,UAAU,CAACC,OAAO,GAAG,IAAI;gBAChD;gBACA;gBACA,IAAIvF,QAAQ,CAAC2C,OAAO,CAAC3B,IAAI,IAAI/G,KAAK,CAACyM,IAAI,CAAC1G,QAAQ,CAAC2C,OAAO,CAACsC,MAAM,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAACnL,CAAC,IAAIA,CAAC,CAACoG,WAAW,KAAKmD,GAAG,CAACnD,WAAW,CAAC,EAAE;kBAC/G;kBACA,MAAMgF,MAAM,GAAGjD,aAAa,CAACkD,KAAK,CAAC,CAAC,EAAElD,aAAa,CAACxD,OAAO,CAAC4E,GAAG,CAAC,CAAC;kBACjE+B,aAAa,CAAC,CAAC,GAAGF,MAAM,EAAE7B,GAAG,CAAC,CAAC;gBACjC;cACF;YACF,CAAC;YACD;YACAhL,MAAM,EAAE;cACNiM,iBAAiB;cACjBG,iBAAiB;cACjB9D;YACF,CAAC;YACD0E,aAAa,EAAE;cACbf,iBAAiB;cACjBG,iBAAiB;cACjB9D;YACF,CAAC;YACD2E,WAAW,EAAErF;UACf,CAAC;;UAED;UACA0D,QAAQ,CAACiB,YAAY,CAAC;UACtB;UACA,IAAIhB,UAAU,CAACC,OAAO,KAAK,IAAI,EAAE;QACnC;MACF;IACF;IACA,OAAO5B,aAAa;EACtB;EACA,SAASmD,aAAaA,CAACnD,aAAa,EAAE;IACpC,MAAM;MACJ3D;IACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;IACpB,KAAK,MAAMwQ,UAAU,IAAIjH,QAAQ,CAAC2C,OAAO,CAACsC,MAAM,CAAC,CAAC,EAAE;MAClD;MACA;MACA,IAAI,CAACtB,aAAa,CAAClI,MAAM,IAAI,CAACkI,aAAa,CAACgD,IAAI,CAAC5B,GAAG,IAAIA,GAAG,CAACnJ,MAAM,KAAKqL,UAAU,CAACrL,MAAM,IAAImJ,GAAG,CAACrH,KAAK,KAAKuJ,UAAU,CAACvJ,KAAK,IAAIqH,GAAG,CAACjD,UAAU,KAAKmF,UAAU,CAACnF,UAAU,CAAC,EAAE;QACvK,MAAMF,WAAW,GAAGqF,UAAU,CAACrF,WAAW;QAC1C,MAAMrL,QAAQ,GAAGqL,WAAW,CAACpH,KAAK;QAClCwF,QAAQ,CAAC2C,OAAO,CAACP,MAAM,CAACV,MAAM,CAACuF,UAAU,CAAC,CAAC;QAC3C,IAAI1Q,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACwG,UAAU,EAAE;UAC3C,MAAMC,QAAQ,GAAGzG,QAAQ,CAACyG,QAAQ;UAClC;UACA,MAAMnB,IAAI,GAAG;YACX,GAAGoL,UAAU;YACbtD;UACF,CAAC;UACD3G,QAAQ,CAACkK,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGlK,QAAQ,CAACkK,YAAY,CAACrL,IAAI,CAAC;UACpEmB,QAAQ,CAACmK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnK,QAAQ,CAACmK,cAAc,CAACtL,IAAI,CAAC;QAC1E;MACF;IACF;EACF;EACA,SAASuL,aAAaA,CAACzF,KAAK,EAAEtG,OAAO,EAAE;IACrC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,MAAMjF,QAAQ,GAAG8E,OAAO,CAACG,CAAC,CAAC,CAAChB,KAAK;MACjCjE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyG,QAAQ,CAACqK,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG9Q,QAAQ,CAACyG,QAAQ,CAACqK,eAAe,CAAC1F,KAAK,CAAC;IAC3H;EACF;EACA,SAAS2F,aAAaA,CAACpL,IAAI,EAAE;IAC3B;IACA,QAAQA,IAAI;MACV,KAAK,gBAAgB;MACrB,KAAK,iBAAiB;QACpB,OAAO,MAAM4K,aAAa,CAAC,EAAE,CAAC;MAChC,KAAK,sBAAsB;QACzB,OAAOnF,KAAK,IAAI;UACd,MAAM;YACJ3B;UACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;UACpB,IAAI,WAAW,IAAIkL,KAAK,IAAI3B,QAAQ,CAACgC,WAAW,CAAC6C,GAAG,CAAClD,KAAK,CAACO,SAAS,CAAC,EAAE;YACrE;YACA;YACA;YACA;YACAqF,qBAAqB,CAAC,MAAM;cAC1B;cACA,IAAIvH,QAAQ,CAACgC,WAAW,CAAC6C,GAAG,CAAClD,KAAK,CAACO,SAAS,CAAC,EAAE;gBAC7ClC,QAAQ,CAACgC,WAAW,CAACI,MAAM,CAACT,KAAK,CAACO,SAAS,CAAC;gBAC5C4E,aAAa,CAAC,EAAE,CAAC;cACnB;YACF,CAAC,CAAC;UACJ;QACF,CAAC;IACL;;IAEA;IACA,OAAO,SAASU,WAAWA,CAAC7F,KAAK,EAAE;MACjC,MAAM;QACJ0F,eAAe;QACfrH;MACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;;MAEpB;MACAuJ,QAAQ,CAACyH,SAAS,CAACvP,OAAO,GAAGyJ,KAAK;;MAElC;MACA,MAAM+F,aAAa,GAAGxL,IAAI,KAAK,eAAe;MAC9C,MAAMyL,YAAY,GAAGzL,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,eAAe;MAC/F,MAAMsG,MAAM,GAAGkF,aAAa,GAAGpE,mBAAmB,GAAGvF,SAAS;MAC9D,MAAMqG,IAAI,GAAGZ,SAAS,CAAC7B,KAAK,EAAEa,MAAM,CAAC;MACrC,MAAM4C,KAAK,GAAGuC,YAAY,GAAG7E,iBAAiB,CAACnB,KAAK,CAAC,GAAG,CAAC;;MAEzD;MACA,IAAIzF,IAAI,KAAK,eAAe,EAAE;QAC5B8D,QAAQ,CAACiD,YAAY,GAAG,CAACtB,KAAK,CAACqB,OAAO,EAAErB,KAAK,CAACwB,OAAO,CAAC;QACtDnD,QAAQ,CAAC0C,WAAW,GAAG0B,IAAI,CAACwD,GAAG,CAAC7C,GAAG,IAAIA,GAAG,CAACnD,WAAW,CAAC;MACzD;;MAEA;MACA;MACA,IAAI+F,YAAY,IAAI,CAACvD,IAAI,CAAC3I,MAAM,EAAE;QAChC,IAAI2J,KAAK,IAAI,CAAC,EAAE;UACdgC,aAAa,CAACzF,KAAK,EAAE3B,QAAQ,CAACE,WAAW,CAAC;UAC1C,IAAImH,eAAe,EAAEA,eAAe,CAAC1F,KAAK,CAAC;QAC7C;MACF;MACA;MACA,IAAI+F,aAAa,EAAEZ,aAAa,CAAC1C,IAAI,CAAC;MACtC,SAASyD,WAAWA,CAAChM,IAAI,EAAE;QACzB,MAAM+F,WAAW,GAAG/F,IAAI,CAAC+F,WAAW;QACpC,MAAMrL,QAAQ,GAAGqL,WAAW,CAACpH,KAAK;;QAElC;QACA,IAAI,EAAEjE,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACwG,UAAU,CAAC,EAAE;QAChD,MAAMC,QAAQ,GAAGzG,QAAQ,CAACyG,QAAQ;;QAElC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;QAEQ,IAAI0K,aAAa,EAAE;UACjB;UACA,IAAI1K,QAAQ,CAAC8K,aAAa,IAAI9K,QAAQ,CAAC+K,cAAc,IAAI/K,QAAQ,CAACkK,YAAY,IAAIlK,QAAQ,CAACmK,cAAc,EAAE;YACzG;YACA,MAAMvC,EAAE,GAAGlD,MAAM,CAAC7F,IAAI,CAAC;YACvB,MAAMmM,WAAW,GAAGhI,QAAQ,CAAC2C,OAAO,CAACtE,GAAG,CAACuG,EAAE,CAAC;YAC5C,IAAI,CAACoD,WAAW,EAAE;cAChB;cACAhI,QAAQ,CAAC2C,OAAO,CAAC7J,GAAG,CAAC8L,EAAE,EAAE/I,IAAI,CAAC;cAC9BmB,QAAQ,CAAC8K,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG9K,QAAQ,CAAC8K,aAAa,CAACjM,IAAI,CAAC;cACtEmB,QAAQ,CAAC+K,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG/K,QAAQ,CAAC+K,cAAc,CAAClM,IAAI,CAAC;YAC1E,CAAC,MAAM,IAAImM,WAAW,CAACzC,OAAO,EAAE;cAC9B;cACA1J,IAAI,CAAC2K,eAAe,CAAC,CAAC;YACxB;UACF;UACA;UACAxJ,QAAQ,CAACiL,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjL,QAAQ,CAACiL,aAAa,CAACpM,IAAI,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMqM,OAAO,GAAGlL,QAAQ,CAACd,IAAI,CAAC;UAC9B,IAAIgM,OAAO,EAAE;YACX;YACA;YACA,IAAI,CAACP,YAAY,IAAI3H,QAAQ,CAAC0C,WAAW,CAAC9F,QAAQ,CAACgF,WAAW,CAAC,EAAE;cAC/D;cACAwF,aAAa,CAACzF,KAAK,EAAE3B,QAAQ,CAACE,WAAW,CAACsC,MAAM,CAAC5G,MAAM,IAAI,CAACoE,QAAQ,CAAC0C,WAAW,CAAC9F,QAAQ,CAAChB,MAAM,CAAC,CAAC,CAAC;cACnG;cACAsM,OAAO,CAACrM,IAAI,CAAC;YACf;UACF,CAAC,MAAM;YACL;YACA,IAAI8L,YAAY,IAAI3H,QAAQ,CAAC0C,WAAW,CAAC9F,QAAQ,CAACgF,WAAW,CAAC,EAAE;cAC9DwF,aAAa,CAACzF,KAAK,EAAE3B,QAAQ,CAACE,WAAW,CAACsC,MAAM,CAAC5G,MAAM,IAAI,CAACoE,QAAQ,CAAC0C,WAAW,CAAC9F,QAAQ,CAAChB,MAAM,CAAC,CAAC,CAAC;YACrG;UACF;QACF;MACF;MACAuJ,gBAAgB,CAACf,IAAI,EAAEzC,KAAK,EAAEyD,KAAK,EAAEyC,WAAW,CAAC;IACnD,CAAC;EACH;EACA,OAAO;IACLP;EACF,CAAC;AACH;AAEA,MAAMa,UAAU,GAAGtR,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC6C,MAAM,CAAC;AACvD,MAAM0O,OAAO,GAAG,eAAenT,KAAK,CAACoT,aAAa,CAAC,IAAI,CAAC;AACxD,MAAMC,WAAW,GAAGA,CAACzH,UAAU,EAAE0H,OAAO,KAAK;EAC3C,MAAMC,SAAS,GAAGlT,oBAAoB,CAAC,CAACwD,GAAG,EAAEuF,GAAG,KAAK;IACnD,MAAMoK,QAAQ,GAAG,IAAIzT,KAAK,CAAC4Q,OAAO,CAAC,CAAC;IACpC,MAAM8C,aAAa,GAAG,IAAI1T,KAAK,CAAC4Q,OAAO,CAAC,CAAC;IACzC,MAAM+C,UAAU,GAAG,IAAI3T,KAAK,CAAC4Q,OAAO,CAAC,CAAC;IACtC,SAASgD,kBAAkBA,CAAC7H,MAAM,GAAG1C,GAAG,CAAC,CAAC,CAAC0C,MAAM,EAAEhH,MAAM,GAAG2O,aAAa,EAAE1H,IAAI,GAAG3C,GAAG,CAAC,CAAC,CAAC2C,IAAI,EAAE;MAC5F,MAAM;QACJG,KAAK;QACLG,MAAM;QACND,GAAG;QACHH;MACF,CAAC,GAAGF,IAAI;MACR,MAAMQ,MAAM,GAAGL,KAAK,GAAGG,MAAM;MAC7B,IAAIvH,MAAM,CAAC8O,SAAS,EAAEF,UAAU,CAACtJ,IAAI,CAACtF,MAAM,CAAC,CAAC,KAAK4O,UAAU,CAAC7P,GAAG,CAAC,GAAGiB,MAAM,CAAC;MAC5E,MAAM0K,QAAQ,GAAG1D,MAAM,CAAC+H,gBAAgB,CAACL,QAAQ,CAAC,CAACM,UAAU,CAACJ,UAAU,CAAC;MACzE,IAAI/R,oBAAoB,CAACmK,MAAM,CAAC,EAAE;QAChC,OAAO;UACLI,KAAK,EAAEA,KAAK,GAAGJ,MAAM,CAACiI,IAAI;UAC1B1H,MAAM,EAAEA,MAAM,GAAGP,MAAM,CAACiI,IAAI;UAC5B3H,GAAG;UACHH,IAAI;UACJ+H,MAAM,EAAE,CAAC;UACTxE,QAAQ;UACRjD;QACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM0H,GAAG,GAAGnI,MAAM,CAACmI,GAAG,GAAG/O,IAAI,CAACgP,EAAE,GAAG,GAAG,CAAC,CAAC;QACxC,MAAMC,CAAC,GAAG,CAAC,GAAGjP,IAAI,CAACkP,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGzE,QAAQ,CAAC,CAAC;QAC5C,MAAM6E,CAAC,GAAGF,CAAC,IAAIjI,KAAK,GAAGG,MAAM,CAAC;QAC9B,OAAO;UACLH,KAAK,EAAEmI,CAAC;UACRhI,MAAM,EAAE8H,CAAC;UACT/H,GAAG;UACHH,IAAI;UACJ+H,MAAM,EAAE9H,KAAK,GAAGmI,CAAC;UACjB7E,QAAQ;UACRjD;QACF,CAAC;MACH;IACF;IACA,IAAI+H,kBAAkB,GAAGxL,SAAS;IAClC,MAAMyL,qBAAqB,GAAGtR,OAAO,IAAIY,GAAG,CAACO,KAAK,KAAK;MACrDoQ,WAAW,EAAE;QACX,GAAGpQ,KAAK,CAACoQ,WAAW;QACpBvR;MACF;IACF,CAAC,CAAC,CAAC;IACH,MAAMwN,OAAO,GAAG,IAAI1Q,KAAK,CAAC0U,OAAO,CAAC,CAAC;IACnC,MAAM1K,SAAS,GAAG;MAChBlG,GAAG;MACHuF,GAAG;MACH;MACAsL,EAAE,EAAE,IAAI;MACR5I,MAAM,EAAE,IAAI;MACZ8C,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE;QACNY,QAAQ,EAAE,CAAC;QACXT,OAAO,EAAE,IAAI;QACb2F,SAAS,EAAE;MACb,CAAC;MACDC,KAAK,EAAE,IAAI;MACXC,EAAE,EAAE,IAAI;MACRjJ,UAAU,EAAEA,CAACD,MAAM,GAAG,CAAC,KAAKC,UAAU,CAACxC,GAAG,CAAC,CAAC,EAAEuC,MAAM,CAAC;MACrD2H,OAAO,EAAEA,CAACwB,SAAS,EAAEC,gBAAgB,KAAKzB,OAAO,CAACwB,SAAS,EAAEC,gBAAgB,EAAE3L,GAAG,CAAC,CAAC,CAAC;MACrF4L,MAAM,EAAE,KAAK;MACbxK,MAAM,EAAE,KAAK;MACbyK,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAIpV,KAAK,CAACqV,KAAK,CAAC,CAAC;MACxB3E,OAAO;MACP4E,KAAK,EAAE5E,OAAO;MACd6E,SAAS,EAAE,QAAQ;MACnBlD,eAAe,EAAEtJ,SAAS;MAC1B0L,WAAW,EAAE;QACXvR,OAAO,EAAE,CAAC;QACVkC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,CAAC;QACNmQ,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMpR,KAAK,GAAGgF,GAAG,CAAC,CAAC;UACnB;UACA,IAAIkL,kBAAkB,EAAEmB,YAAY,CAACnB,kBAAkB,CAAC;UACxD;UACA,IAAIlQ,KAAK,CAACoQ,WAAW,CAACvR,OAAO,KAAKmB,KAAK,CAACoQ,WAAW,CAACrP,GAAG,EAAEoP,qBAAqB,CAACnQ,KAAK,CAACoQ,WAAW,CAACrP,GAAG,CAAC;UACrG;UACAmP,kBAAkB,GAAGoB,UAAU,CAAC,MAAMnB,qBAAqB,CAACnL,GAAG,CAAC,CAAC,CAACoL,WAAW,CAACpP,GAAG,CAAC,EAAEhB,KAAK,CAACoQ,WAAW,CAACe,QAAQ,CAAC;QACjH;MACF,CAAC;MACDxJ,IAAI,EAAE;QACJG,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE;MACR,CAAC;MACD0J,QAAQ,EAAE;QACRC,UAAU,EAAE,CAAC;QACbhR,GAAG,EAAE,CAAC;QACNsH,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACPM,MAAM,EAAE,CAAC;QACTiD,QAAQ,EAAE,CAAC;QACXwE,MAAM,EAAE,CAAC;QACTL;MACF,CAAC;MACDkC,SAAS,EAAEhH,MAAM,IAAIhL,GAAG,CAACO,KAAK,KAAK;QACjC,GAAGA,KAAK;QACRyK,MAAM,EAAE;UACN,GAAGzK,KAAK,CAACyK,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;MACHiH,OAAO,EAAEA,CAAC5J,KAAK,EAAEG,MAAM,EAAED,GAAG,GAAG,CAAC,EAAEH,IAAI,GAAG,CAAC,KAAK;QAC7C,MAAMH,MAAM,GAAG1C,GAAG,CAAC,CAAC,CAAC0C,MAAM;QAC3B,MAAMC,IAAI,GAAG;UACXG,KAAK;UACLG,MAAM;UACND,GAAG;UACHH;QACF,CAAC;QACDpI,GAAG,CAACO,KAAK,KAAK;UACZ2H,IAAI;UACJ4J,QAAQ,EAAE;YACR,GAAGvR,KAAK,CAACuR,QAAQ;YACjB,GAAGhC,kBAAkB,CAAC7H,MAAM,EAAE2H,aAAa,EAAE1H,IAAI;UACnD;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACDgK,MAAM,EAAEnR,GAAG,IAAIf,GAAG,CAACO,KAAK,IAAI;QAC1B,MAAM4R,QAAQ,GAAGrR,YAAY,CAACC,GAAG,CAAC;QAClC,OAAO;UACL+Q,QAAQ,EAAE;YACR,GAAGvR,KAAK,CAACuR,QAAQ;YACjB/Q,GAAG,EAAEoR,QAAQ;YACbJ,UAAU,EAAExR,KAAK,CAACuR,QAAQ,CAACC,UAAU,IAAII;UAC3C;QACF,CAAC;MACH,CAAC,CAAC;MACFC,YAAY,EAAEA,CAACX,SAAS,GAAG,QAAQ,KAAK;QACtC,MAAMH,KAAK,GAAG/L,GAAG,CAAC,CAAC,CAAC+L,KAAK;;QAEzB;QACAA,KAAK,CAACe,IAAI,CAAC,CAAC;QACZf,KAAK,CAACgB,WAAW,GAAG,CAAC;QACrB,IAAIb,SAAS,KAAK,OAAO,EAAE;UACzBH,KAAK,CAACiB,KAAK,CAAC,CAAC;UACbjB,KAAK,CAACgB,WAAW,GAAG,CAAC;QACvB;QACAtS,GAAG,CAAC,OAAO;UACTyR;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACD7T,YAAY,EAAEqH,SAAS;MACvBiC,QAAQ,EAAE;QACR;QACAE,WAAW,EAAE,EAAE;QACfyC,OAAO,EAAE,IAAIzE,GAAG,CAAC,CAAC;QAClBoN,WAAW,EAAE,EAAE;QACfrI,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACpBP,WAAW,EAAE,EAAE;QACfV,WAAW,EAAE,IAAI9D,GAAG,CAAC,CAAC;QACtBuJ,SAAS,EAAE,aAAaxS,KAAK,CAACsW,SAAS,CAAC,CAAC;QACzC;QACAC,MAAM,EAAE,KAAK;QACb5K,MAAM,EAAE,CAAC;QACT8D,QAAQ,EAAE,CAAC;QACX+G,SAAS,EAAEA,CAACzT,GAAG,EAAE0M,QAAQ,EAAEnC,KAAK,KAAK;UACnC,MAAMvC,QAAQ,GAAG3B,GAAG,CAAC,CAAC,CAAC2B,QAAQ;UAC/B;UACA;UACA;UACA;UACAA,QAAQ,CAAC0E,QAAQ,GAAG1E,QAAQ,CAAC0E,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC9D1E,QAAQ,CAACsL,WAAW,CAAChL,IAAI,CAAC;YACxBtI,GAAG;YACH0M,QAAQ;YACRnC;UACF,CAAC,CAAC;UACF;UACA;UACAvC,QAAQ,CAACsL,WAAW,GAAGtL,QAAQ,CAACsL,WAAW,CAAChH,IAAI,CAAC,CAAC5J,CAAC,EAAES,CAAC,KAAKT,CAAC,CAACgK,QAAQ,GAAGvJ,CAAC,CAACuJ,QAAQ,CAAC;UACnF,OAAO,MAAM;YACX,MAAM1E,QAAQ,GAAG3B,GAAG,CAAC,CAAC,CAAC2B,QAAQ;YAC/B,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACsL,WAAW,EAAE;cAC5C;cACAtL,QAAQ,CAAC0E,QAAQ,GAAG1E,QAAQ,CAAC0E,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC9D;cACA1E,QAAQ,CAACsL,WAAW,GAAGtL,QAAQ,CAACsL,WAAW,CAAC9I,MAAM,CAACkJ,CAAC,IAAIA,CAAC,CAAC1T,GAAG,KAAKA,GAAG,CAAC;YACxE;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD,OAAOgH,SAAS;EAClB,CAAC,CAAC;EACF,MAAM3F,KAAK,GAAGmP,SAAS,CAAC/R,QAAQ,CAAC,CAAC;EAClC,IAAIkV,OAAO,GAAGtS,KAAK,CAAC2H,IAAI;EACxB,IAAI4K,MAAM,GAAGvS,KAAK,CAACuR,QAAQ,CAAC/Q,GAAG;EAC/B,IAAIgS,SAAS,GAAGxS,KAAK,CAAC0H,MAAM;EAC5ByH,SAAS,CAACiD,SAAS,CAAC,MAAM;IACxB,MAAM;MACJ1K,MAAM;MACNC,IAAI;MACJ4J,QAAQ;MACRjB,EAAE;MACF7Q;IACF,CAAC,GAAG0P,SAAS,CAAC/R,QAAQ,CAAC,CAAC;;IAExB;IACA,IAAIuK,IAAI,CAACG,KAAK,KAAKwK,OAAO,CAACxK,KAAK,IAAIH,IAAI,CAACM,MAAM,KAAKqK,OAAO,CAACrK,MAAM,IAAIsJ,QAAQ,CAAC/Q,GAAG,KAAK+R,MAAM,EAAE;MAC7FD,OAAO,GAAG3K,IAAI;MACd4K,MAAM,GAAGhB,QAAQ,CAAC/Q,GAAG;MACrB;MACAiH,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;MAC1B,IAAI4J,QAAQ,CAAC/Q,GAAG,GAAG,CAAC,EAAE8P,EAAE,CAACmC,aAAa,CAAClB,QAAQ,CAAC/Q,GAAG,CAAC;MACpD,MAAMkS,WAAW,GAAG,OAAOC,iBAAiB,KAAK,WAAW,IAAIrC,EAAE,CAACsC,UAAU,YAAYD,iBAAiB;MAC1GrC,EAAE,CAACoB,OAAO,CAAC/J,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAEyK,WAAW,CAAC;IAClD;;IAEA;IACA,IAAIhL,MAAM,KAAK8K,SAAS,EAAE;MACxBA,SAAS,GAAG9K,MAAM;MAClB;MACAjI,GAAG,CAACO,KAAK,KAAK;QACZuR,QAAQ,EAAE;UACR,GAAGvR,KAAK,CAACuR,QAAQ;UACjB,GAAGvR,KAAK,CAACuR,QAAQ,CAAChC,kBAAkB,CAAC7H,MAAM;QAC7C;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;;EAEF;EACAyH,SAAS,CAACiD,SAAS,CAACpS,KAAK,IAAIwH,UAAU,CAACxH,KAAK,CAAC,CAAC;;EAE/C;EACA,OAAOmP,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,iBAAiBA,CAAClU,GAAG,EAAE;EAC9B,MAAMzB,QAAQ,GAAGtB,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EACnChD,KAAK,CAACkX,mBAAmB,CAAC5V,QAAQ,EAAE,MAAMyB,GAAG,CAACE,OAAO,CAACsC,KAAK,EAAE,CAACxC,GAAG,CAAC,CAAC;EACnE,OAAOzB,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,SAAS6V,QAAQA,CAAA,EAAG;EAClB,MAAM7J,KAAK,GAAGtN,KAAK,CAACoX,UAAU,CAACjE,OAAO,CAAC;EACvC,IAAI,CAAC7F,KAAK,EAAE,MAAM,IAAI+J,KAAK,CAAC,0DAA0D,CAAC;EACvF,OAAO/J,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,SAASgK,QAAQA,CAACC,QAAQ,GAAGnT,KAAK,IAAIA,KAAK,EAAEoT,UAAU,EAAE;EACvD,OAAOL,QAAQ,CAAC,CAAC,CAACI,QAAQ,EAAEC,UAAU,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACrH,QAAQ,EAAEsH,cAAc,GAAG,CAAC,EAAE;EAC9C,MAAMpK,KAAK,GAAG6J,QAAQ,CAAC,CAAC;EACxB,MAAMX,SAAS,GAAGlJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC,CAACuJ,QAAQ,CAACyL,SAAS;EACrD;EACA,MAAMzT,GAAG,GAAGF,kBAAkB,CAACuN,QAAQ,CAAC;EACxC;EACAjO,yBAAyB,CAAC,MAAMqU,SAAS,CAACzT,GAAG,EAAE2U,cAAc,EAAEpK,KAAK,CAAC,EAAE,CAACoK,cAAc,EAAElB,SAAS,EAAElJ,KAAK,CAAC,CAAC;EAC1G,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,SAASqK,QAAQA,CAAChR,MAAM,EAAE;EACxB,OAAO3G,KAAK,CAACqD,OAAO,CAAC,MAAMqD,UAAU,CAACC,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;AAC1D;AACA,MAAMiR,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG7V,KAAK,IAAI;EAC/B,IAAI8V,gBAAgB;EACpB,OAAO,OAAO9V,KAAK,KAAK,UAAU,IAAI,CAACA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC8V,gBAAgB,GAAG9V,KAAK,CAAC+V,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,gBAAgB,CAAC7T,WAAW,MAAMjC,KAAK;AACjK,CAAC;AACD,SAASgW,SAASA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACzC,OAAO,UAAUC,KAAK,EAAE,GAAGC,KAAK,EAAE;IAChC,IAAIC,MAAM;;IAEV;IACA,IAAIR,eAAe,CAACM,KAAK,CAAC,EAAE;MAC1BE,MAAM,GAAGV,eAAe,CAACxO,GAAG,CAACgP,KAAK,CAAC;MACnC,IAAI,CAACE,MAAM,EAAE;QACXA,MAAM,GAAG,IAAIF,KAAK,CAAC,CAAC;QACpBR,eAAe,CAAC/T,GAAG,CAACuU,KAAK,EAAEE,MAAM,CAAC;MACpC;IACF,CAAC,MAAM;MACLA,MAAM,GAAGF,KAAK;IAChB;;IAEA;IACA,IAAIF,UAAU,EAAEA,UAAU,CAACI,MAAM,CAAC;;IAElC;IACA,OAAOxU,OAAO,CAACyU,GAAG,CAACF,KAAK,CAAC1F,GAAG,CAAC0F,KAAK,IAAI,IAAIvU,OAAO,CAAC,CAAC0U,GAAG,EAAEC,MAAM,KAAKH,MAAM,CAACI,IAAI,CAACL,KAAK,EAAEzR,IAAI,IAAI;MAC5F,IAAIoE,UAAU,CAACpE,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACgO,KAAK,CAAC,EAAE1T,MAAM,CAACyX,MAAM,CAAC/R,IAAI,EAAEF,UAAU,CAACE,IAAI,CAACgO,KAAK,CAAC,CAAC;MAC/F4D,GAAG,CAAC5R,IAAI,CAAC;IACX,CAAC,EAAEuR,UAAU,EAAE9T,KAAK,IAAIoU,MAAM,CAAC,IAAIpB,KAAK,CAAC,kBAAkBgB,KAAK,KAAKhU,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACuU,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACP,MAAM,EAAED,KAAK,EAAEH,UAAU,EAAEC,UAAU,EAAE;EACxD;EACA,MAAM1R,IAAI,GAAGzB,KAAK,CAACC,OAAO,CAACoT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,MAAMS,OAAO,GAAGrY,OAAO,CAACwX,SAAS,CAACC,UAAU,EAAEC,UAAU,CAAC,EAAE,CAACG,MAAM,EAAE,GAAG7R,IAAI,CAAC,EAAE;IAC5EsS,KAAK,EAAEvT,EAAE,CAACS;EACZ,CAAC,CAAC;EACF;EACA,OAAOjB,KAAK,CAACC,OAAO,CAACoT,KAAK,CAAC,GAAGS,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA;AACA;AACAD,SAAS,CAACnY,OAAO,GAAG,UAAU4X,MAAM,EAAED,KAAK,EAAEH,UAAU,EAAE;EACvD,MAAMzR,IAAI,GAAGzB,KAAK,CAACC,OAAO,CAACoT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAO3X,OAAO,CAACuX,SAAS,CAACC,UAAU,CAAC,EAAE,CAACI,MAAM,EAAE,GAAG7R,IAAI,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACAoS,SAAS,CAAClY,KAAK,GAAG,UAAU2X,MAAM,EAAED,KAAK,EAAE;EACzC,MAAM5R,IAAI,GAAGzB,KAAK,CAACC,OAAO,CAACoT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAO1X,KAAK,CAAC,CAAC2X,MAAM,EAAE,GAAG7R,IAAI,CAAC,CAAC;AACjC,CAAC;;AAED;AACA;;AAEA,SAASuS,gBAAgBA,CAACC,MAAM,EAAE;EAChC,MAAMC,UAAU,GAAG5Y,UAAU,CAAC2Y,MAAM,CAAC;EACrCC,UAAU,CAACC,kBAAkB,CAAC;IAC5BC,UAAU,EAAE,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC3FC,mBAAmB,EAAE,oBAAoB;IACzCC,OAAO,EAAEzZ,KAAK,CAACyZ;EACjB,CAAC,CAAC;EACF,OAAOP,UAAU;AACnB;AACA,MAAMQ,eAAe,GAAG,CAAC;;AAEzB;AACA;AACA;;AAEA,MAAMC,SAAS,GAAG,CAAC,CAAC;AACpB,MAAMC,YAAY,GAAG,iBAAiB;AACtC,MAAMC,YAAY,GAAGpW,IAAI,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACqW,WAAW,CAAC,CAAC,GAAGrW,IAAI,CAACmO,KAAK,CAAC,CAAC,CAAC,EAAE;AACvE,IAAIrL,CAAC,GAAG,CAAC;AACT,MAAMwT,aAAa,GAAGpT,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU;AAC5D,SAASqT,MAAMA,CAAC5T,OAAO,EAAE;EACvB,IAAI2T,aAAa,CAAC3T,OAAO,CAAC,EAAE;IAC1B,MAAMnC,SAAS,GAAG,GAAGsC,CAAC,EAAE,EAAE;IAC1BoT,SAAS,CAAC1V,SAAS,CAAC,GAAGmC,OAAO;IAC9B,OAAOnC,SAAS;EAClB,CAAC,MAAM;IACL/C,MAAM,CAACyX,MAAM,CAACgB,SAAS,EAAEvT,OAAO,CAAC;EACnC;AACF;AACA,SAAS6T,gBAAgBA,CAACxW,IAAI,EAAEe,KAAK,EAAE;EACrC;EACA,MAAMyC,IAAI,GAAG4S,YAAY,CAACpW,IAAI,CAAC;EAC/B,MAAMqB,MAAM,GAAG6U,SAAS,CAAC1S,IAAI,CAAC;;EAE9B;EACA,IAAIxD,IAAI,KAAK,WAAW,IAAI,CAACqB,MAAM,EAAE,MAAM,IAAIuS,KAAK,CAAC,QAAQpQ,IAAI,8JAA8J,CAAC;;EAEhO;EACA,IAAIxD,IAAI,KAAK,WAAW,IAAI,CAACe,KAAK,CAACmC,MAAM,EAAE,MAAM,IAAI0Q,KAAK,CAAC,+CAA+C,CAAC;;EAE3G;EACA,IAAI7S,KAAK,CAACL,IAAI,KAAK2E,SAAS,IAAI,CAAC9D,KAAK,CAACC,OAAO,CAACT,KAAK,CAACL,IAAI,CAAC,EAAE,MAAM,IAAIkT,KAAK,CAAC,sCAAsC,CAAC;AACrH;AACA,SAAS6C,cAAcA,CAACzW,IAAI,EAAEe,KAAK,EAAEjD,IAAI,EAAE;EACzC,IAAI4Y,aAAa;EACjB;EACA1W,IAAI,GAAGoW,YAAY,CAACpW,IAAI,CAAC,IAAIkW,SAAS,GAAGlW,IAAI,GAAGA,IAAI,CAACiF,OAAO,CAACkR,YAAY,EAAE,EAAE,CAAC;EAC9EK,gBAAgB,CAACxW,IAAI,EAAEe,KAAK,CAAC;;EAE7B;EACA,IAAIf,IAAI,KAAK,WAAW,IAAI,CAAC0W,aAAa,GAAG3V,KAAK,CAACmC,MAAM,KAAK,IAAI,IAAIwT,aAAa,CAAC5U,KAAK,EAAE,OAAOf,KAAK,CAACmC,MAAM,CAACpB,KAAK;EACpH,OAAOqC,OAAO,CAACpD,KAAK,CAACmC,MAAM,EAAEpF,IAAI,EAAEkC,IAAI,EAAEe,KAAK,CAAC;AACjD;AACA,SAAS4V,YAAYA,CAAC9Y,QAAQ,EAAE;EAC9B,IAAI,CAACA,QAAQ,CAAC0G,QAAQ,EAAE;IACtB,IAAIqS,gBAAgB;IACpB,IAAI/Y,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,IAAI,CAAC+R,gBAAgB,GAAG/Y,QAAQ,CAACuG,MAAM,KAAK,IAAI,IAAIwS,gBAAgB,CAAC1T,MAAM,EAAE;MACpGiC,MAAM,CAACtH,QAAQ,CAACuG,MAAM,EAAEvG,QAAQ,CAAC;IACnC,CAAC,MAAM,IAAI0J,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,EAAE;MACtCrF,QAAQ,CAACqF,MAAM,CAAC2T,OAAO,GAAG,KAAK;IACjC;IACAhZ,QAAQ,CAAC0G,QAAQ,GAAG,IAAI;IACxBwD,kBAAkB,CAAClK,QAAQ,CAAC;EAC9B;AACF;AACA,SAASiZ,cAAcA,CAACjZ,QAAQ,EAAE;EAChC,IAAIA,QAAQ,CAAC0G,QAAQ,EAAE;IACrB,IAAIwS,iBAAiB;IACrB,IAAIlZ,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,IAAI,CAACkS,iBAAiB,GAAGlZ,QAAQ,CAACuG,MAAM,KAAK,IAAI,IAAI2S,iBAAiB,CAAC7T,MAAM,EAAE;MACtG2B,MAAM,CAAChH,QAAQ,CAACuG,MAAM,EAAEvG,QAAQ,CAAC;IACnC,CAAC,MAAM,IAAI0J,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,IAAIrF,QAAQ,CAACkD,KAAK,CAAC8V,OAAO,KAAK,KAAK,EAAE;MAC1EhZ,QAAQ,CAACqF,MAAM,CAAC2T,OAAO,GAAG,IAAI;IAChC;IACAhZ,QAAQ,CAAC0G,QAAQ,GAAG,KAAK;IACzBwD,kBAAkB,CAAClK,QAAQ,CAAC;EAC9B;AACF;;AAEA;AACA;AACA,SAASmZ,sBAAsBA,CAAC5S,MAAM,EAAEU,KAAK,EAAEmS,WAAW,EAAE;EAC1D;EACA;EACA,MAAMtW,KAAK,GAAGmE,KAAK,CAAChH,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnC,IAAI,CAACqG,MAAM,CAACA,MAAM,IAAIA,MAAM,CAAClB,MAAM,KAAKvC,KAAK,CAACwQ,KAAK,EAAE;;EAErD;EACA,IAAI,CAACrM,KAAK,CAAC5B,MAAM,EAAE;IACjB,IAAIgU,mBAAmB,EAAEC,iBAAiB;IAC1C;IACA,MAAM9V,MAAM,GAAG6U,SAAS,CAACE,YAAY,CAACtR,KAAK,CAAC9E,IAAI,CAAC,CAAC;;IAElD;IACA8E,KAAK,CAAC5B,MAAM,GAAG,CAACgU,mBAAmB,GAAGpS,KAAK,CAAC/D,KAAK,CAACmC,MAAM,KAAK,IAAI,GAAGgU,mBAAmB,GAAG,IAAI7V,MAAM,CAAC,IAAI,CAAC8V,iBAAiB,GAAGrS,KAAK,CAAC/D,KAAK,CAACL,IAAI,KAAK,IAAI,GAAGyW,iBAAiB,GAAG,EAAE,CAAC,CAAC;IAClLrS,KAAK,CAAC5B,MAAM,CAACpB,KAAK,GAAGgD,KAAK;EAC5B;;EAEA;EACAsB,UAAU,CAACtB,KAAK,CAAC5B,MAAM,EAAE4B,KAAK,CAAC/D,KAAK,CAAC;;EAErC;EACA,IAAI+D,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,EAAE;IACtBA,MAAM,CAACT,MAAM,EAAEU,KAAK,CAAC;EACvB,CAAC,MAAM,IAAIyC,UAAU,CAACzC,KAAK,CAAC5B,MAAM,CAAC,IAAIqE,UAAU,CAACnD,MAAM,CAAClB,MAAM,CAAC,EAAE;IAChE,MAAMkU,UAAU,GAAGhT,MAAM,CAAClB,MAAM,CAACrD,QAAQ,CAAC4H,OAAO,CAACwP,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/T,MAAM,CAAC;IACpG,IAAI+T,WAAW,IAAIG,UAAU,KAAK,CAAC,CAAC,EAAE;MACpC;MACA;MACA,MAAMC,aAAa,GAAGjT,MAAM,CAAClB,MAAM,CAACrD,QAAQ,CAAC4H,OAAO,CAAC3C,KAAK,CAAC5B,MAAM,CAAC;MAClE,IAAImU,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBjT,MAAM,CAAClB,MAAM,CAACrD,QAAQ,CAAC6H,MAAM,CAAC2P,aAAa,EAAE,CAAC,CAAC;QAC/C,MAAMC,aAAa,GAAGD,aAAa,GAAGD,UAAU,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU;QAC9EhT,MAAM,CAAClB,MAAM,CAACrD,QAAQ,CAAC6H,MAAM,CAAC4P,aAAa,EAAE,CAAC,EAAExS,KAAK,CAAC5B,MAAM,CAAC;MAC/D,CAAC,MAAM;QACL4B,KAAK,CAAC5B,MAAM,CAACkB,MAAM,GAAGA,MAAM,CAAClB,MAAM;QACnCkB,MAAM,CAAClB,MAAM,CAACrD,QAAQ,CAAC6H,MAAM,CAAC0P,UAAU,EAAE,CAAC,EAAEtS,KAAK,CAAC5B,MAAM,CAAC;QAC1D4B,KAAK,CAAC5B,MAAM,CAACqU,aAAa,CAAC;UACzBvX,IAAI,EAAE;QACR,CAAC,CAAC;QACFoE,MAAM,CAAClB,MAAM,CAACqU,aAAa,CAAC;UAC1BvX,IAAI,EAAE,YAAY;UAClB8E,KAAK,EAAEA,KAAK,CAAC5B;QACf,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLkB,MAAM,CAAClB,MAAM,CAACkJ,GAAG,CAACtH,KAAK,CAAC5B,MAAM,CAAC;IACjC;EACF;;EAEA;EACA,KAAK,MAAMsU,aAAa,IAAI1S,KAAK,CAACjF,QAAQ,EAAEmX,sBAAsB,CAAClS,KAAK,EAAE0S,aAAa,CAAC;;EAExF;EACAzP,kBAAkB,CAACjD,KAAK,CAAC;AAC3B;AACA,SAAS2S,WAAWA,CAACrT,MAAM,EAAEU,KAAK,EAAE;EAClC,IAAI,CAACA,KAAK,EAAE;;EAEZ;EACAA,KAAK,CAACV,MAAM,GAAGA,MAAM;EACrBA,MAAM,CAACvE,QAAQ,CAAC+H,IAAI,CAAC9C,KAAK,CAAC;;EAE3B;EACAkS,sBAAsB,CAAC5S,MAAM,EAAEU,KAAK,CAAC;AACvC;AACA,SAAS4S,YAAYA,CAACtT,MAAM,EAAEU,KAAK,EAAEmS,WAAW,EAAE;EAChD,IAAI,CAACnS,KAAK,IAAI,CAACmS,WAAW,EAAE;;EAE5B;EACAnS,KAAK,CAACV,MAAM,GAAGA,MAAM;EACrB,MAAMgT,UAAU,GAAGhT,MAAM,CAACvE,QAAQ,CAAC4H,OAAO,CAACwP,WAAW,CAAC;EACvD,IAAIG,UAAU,KAAK,CAAC,CAAC,EAAEhT,MAAM,CAACvE,QAAQ,CAAC6H,MAAM,CAAC0P,UAAU,EAAE,CAAC,EAAEtS,KAAK,CAAC,CAAC,KAAKV,MAAM,CAACvE,QAAQ,CAAC+H,IAAI,CAAC9C,KAAK,CAAC;;EAEpG;EACAkS,sBAAsB,CAAC5S,MAAM,EAAEU,KAAK,EAAEmS,WAAW,CAAC;AACpD;AACA,SAASU,aAAaA,CAACzU,MAAM,EAAE;EAC7B,IAAI,OAAOA,MAAM,CAACS,OAAO,KAAK,UAAU,EAAE;IACxC,MAAMiU,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI;QACF1U,MAAM,CAACS,OAAO,CAAC,CAAC;MAClB,CAAC,CAAC,MAAM;QACN;MAAA;IAEJ,CAAC;;IAED;IACA,IAAI,OAAOkU,wBAAwB,KAAK,WAAW,EAAED,aAAa,CAAC,CAAC;IACpE;IAAA,KACK9a,yBAAyB,CAACC,qBAAqB,EAAE6a,aAAa,CAAC;EACtE;AACF;AACA,SAASE,WAAWA,CAAC1T,MAAM,EAAEU,KAAK,EAAEnB,OAAO,EAAE;EAC3C,IAAI,CAACmB,KAAK,EAAE;;EAEZ;EACAA,KAAK,CAACV,MAAM,GAAG,IAAI;EACnB,MAAMgT,UAAU,GAAGhT,MAAM,CAACvE,QAAQ,CAAC4H,OAAO,CAAC3C,KAAK,CAAC;EACjD,IAAIsS,UAAU,KAAK,CAAC,CAAC,EAAEhT,MAAM,CAACvE,QAAQ,CAAC6H,MAAM,CAAC0P,UAAU,EAAE,CAAC,CAAC;;EAE5D;EACA,IAAItS,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,EAAE;IACtBM,MAAM,CAACf,MAAM,EAAEU,KAAK,CAAC;EACvB,CAAC,MAAM,IAAIyC,UAAU,CAACzC,KAAK,CAAC5B,MAAM,CAAC,IAAIqE,UAAU,CAACnD,MAAM,CAAClB,MAAM,CAAC,EAAE;IAChEkB,MAAM,CAAClB,MAAM,CAAC6U,MAAM,CAACjT,KAAK,CAAC5B,MAAM,CAAC;IAClC0G,mBAAmB,CAAChM,eAAe,CAACkH,KAAK,CAAC,EAAEA,KAAK,CAAC5B,MAAM,CAAC;EAC3D;;EAEA;EACA,MAAM8U,aAAa,GAAGlT,KAAK,CAAC/D,KAAK,CAAC4C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK;;EAEvE;EACA,KAAK,IAAIb,CAAC,GAAGgC,KAAK,CAACjF,QAAQ,CAACkD,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,MAAM/C,IAAI,GAAG+E,KAAK,CAACjF,QAAQ,CAACiD,CAAC,CAAC;IAC9BgV,WAAW,CAAChT,KAAK,EAAE/E,IAAI,EAAEiY,aAAa,CAAC;EACzC;EACAlT,KAAK,CAACjF,QAAQ,CAACkD,MAAM,GAAG,CAAC;;EAEzB;EACA,OAAO+B,KAAK,CAAC5B,MAAM,CAACpB,KAAK;;EAEzB;EACA;EACA;EACA;EACA;EACA;EACA,IAAIkW,aAAa,IAAIlT,KAAK,CAAC9E,IAAI,KAAK,WAAW,IAAI8E,KAAK,CAAC5B,MAAM,CAAClD,IAAI,KAAK,OAAO,EAAE;IAChF2X,aAAa,CAAC7S,KAAK,CAAC5B,MAAM,CAAC;EAC7B;;EAEA;EACA,IAAIS,OAAO,KAAK0B,SAAS,EAAE0C,kBAAkB,CAACjD,KAAK,CAAC;AACtD;AACA,SAASmT,WAAWA,CAACvY,KAAK,EAAEwY,cAAc,EAAE;EAC1C,KAAK,MAAMC,MAAM,IAAI,CAACzY,KAAK,EAAEA,KAAK,CAAC0Y,SAAS,CAAC,EAAE;IAC7C,IAAID,MAAM,KAAK,IAAI,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAC7Y,GAAG,KAAK,UAAU,EAAE;QACpC6Y,MAAM,CAACE,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,MAAM,CAACE,UAAU,CAAC,CAAC;QACxD,MAAMC,OAAO,GAAGH,MAAM,CAAC7Y,GAAG,CAAC4Y,cAAc,CAAC;QAC1C,IAAI,OAAOI,OAAO,KAAK,UAAU,EAAEH,MAAM,CAACE,UAAU,GAAGC,OAAO;MAChE,CAAC,MAAM,IAAIH,MAAM,CAAC7Y,GAAG,EAAE;QACrB6Y,MAAM,CAAC7Y,GAAG,CAACE,OAAO,GAAG0Y,cAAc;MACrC;IACF;EACF;AACF;AACA,MAAMK,aAAa,GAAG,EAAE;AACxB,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA,KAAK,MAAM,CAAC3a,QAAQ,CAAC,IAAI0a,aAAa,EAAE;IACtC,MAAMnU,MAAM,GAAGvG,QAAQ,CAACuG,MAAM;IAC9B,IAAIA,MAAM,EAAE;MACV,IAAIvG,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,EAAE;QACzBM,MAAM,CAACf,MAAM,EAAEvG,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAI0J,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,IAAIqE,UAAU,CAACnD,MAAM,CAAClB,MAAM,CAAC,EAAE;QACnEkB,MAAM,CAAClB,MAAM,CAAC6U,MAAM,CAACla,QAAQ,CAACqF,MAAM,CAAC;MACvC;MACA,KAAK,MAAM4B,KAAK,IAAIjH,QAAQ,CAACgC,QAAQ,EAAE;QACrC,IAAIiF,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,EAAE;UACtBM,MAAM,CAACtH,QAAQ,EAAEiH,KAAK,CAAC;QACzB,CAAC,MAAM,IAAIyC,UAAU,CAACzC,KAAK,CAAC5B,MAAM,CAAC,IAAIqE,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,EAAE;UAClErF,QAAQ,CAACqF,MAAM,CAAC6U,MAAM,CAACjT,KAAK,CAAC5B,MAAM,CAAC;QACtC;MACF;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAIrF,QAAQ,CAAC0G,QAAQ,EAAEuS,cAAc,CAACjZ,QAAQ,CAAC;;IAE/C;IACA,IAAIA,QAAQ,CAACqF,MAAM,CAACpB,KAAK,EAAE,OAAOjE,QAAQ,CAACqF,MAAM,CAACpB,KAAK;IACvD,IAAIjE,QAAQ,CAACmC,IAAI,KAAK,WAAW,EAAE2X,aAAa,CAAC9Z,QAAQ,CAACqF,MAAM,CAAC;EACnE;;EAEA;EACA,KAAK,MAAM,CAACrF,QAAQ,EAAEkD,KAAK,EAAErB,KAAK,CAAC,IAAI6Y,aAAa,EAAE;IACpD1a,QAAQ,CAACkD,KAAK,GAAGA,KAAK;IACtB,MAAMqD,MAAM,GAAGvG,QAAQ,CAACuG,MAAM;IAC9B,IAAIA,MAAM,EAAE;MACV,IAAIqU,qBAAqB,EAAEC,oBAAoB;MAC/C;MACA,MAAMrX,MAAM,GAAG6U,SAAS,CAACE,YAAY,CAACvY,QAAQ,CAACmC,IAAI,CAAC,CAAC;;MAErD;MACAnC,QAAQ,CAACqF,MAAM,GAAG,CAACuV,qBAAqB,GAAG5a,QAAQ,CAACkD,KAAK,CAACmC,MAAM,KAAK,IAAI,GAAGuV,qBAAqB,GAAG,IAAIpX,MAAM,CAAC,IAAI,CAACqX,oBAAoB,GAAG7a,QAAQ,CAACkD,KAAK,CAACL,IAAI,KAAK,IAAI,GAAGgY,oBAAoB,GAAG,EAAE,CAAC,CAAC;MACrM7a,QAAQ,CAACqF,MAAM,CAACpB,KAAK,GAAGjE,QAAQ;MAChCoa,WAAW,CAACvY,KAAK,EAAE7B,QAAQ,CAACqF,MAAM,CAAC;;MAEnC;MACAkD,UAAU,CAACvI,QAAQ,CAACqF,MAAM,EAAErF,QAAQ,CAACkD,KAAK,CAAC;MAC3C,IAAIlD,QAAQ,CAACkD,KAAK,CAAC8D,MAAM,EAAE;QACzBA,MAAM,CAACT,MAAM,EAAEvG,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAI0J,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,IAAIqE,UAAU,CAACnD,MAAM,CAAClB,MAAM,CAAC,EAAE;QACnEkB,MAAM,CAAClB,MAAM,CAACkJ,GAAG,CAACvO,QAAQ,CAACqF,MAAM,CAAC;MACpC;MACA,KAAK,MAAM4B,KAAK,IAAIjH,QAAQ,CAACgC,QAAQ,EAAE;QACrC,IAAIiF,KAAK,CAAC/D,KAAK,CAAC8D,MAAM,EAAE;UACtBA,MAAM,CAAChH,QAAQ,EAAEiH,KAAK,CAAC;QACzB,CAAC,MAAM,IAAIyC,UAAU,CAACzC,KAAK,CAAC5B,MAAM,CAAC,IAAIqE,UAAU,CAAC1J,QAAQ,CAACqF,MAAM,CAAC,EAAE;UAClErF,QAAQ,CAACqF,MAAM,CAACkJ,GAAG,CAACtH,KAAK,CAAC5B,MAAM,CAAC;QACnC;MACF;;MAEA;MACA6E,kBAAkB,CAAClK,QAAQ,CAAC;IAC9B;EACF;EACA0a,aAAa,CAACxV,MAAM,GAAG,CAAC;AAC1B;;AAEA;AACA,MAAM4V,kBAAkB,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnC,MAAMC,UAAU,GAAG,CAAC,CAAC;AACrB,IAAIC,qBAAqB,GAAG5C,eAAe;;AAE3C;AACA,MAAM6C,OAAO,GAAG,CAAC;AACjB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMtD,UAAU,GAAG,eAAeF,gBAAgB,CAAC;EACjDyD,iBAAiB,EAAE,KAAK;EACxBC,gBAAgB,EAAE,KAAK;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxB3C,cAAc;EACdqB,WAAW;EACXL,WAAW;EACX4B,kBAAkB,EAAE5B,WAAW;EAC/BC,YAAY;EACZ4B,sBAAsBA,CAACC,SAAS,EAAEzU,KAAK,EAAE;IACvC,MAAMqM,KAAK,GAAGoI,SAAS,CAACxb,QAAQ,CAAC,CAAC,CAACoT,KAAK,CAACrP,KAAK;IAC9C,IAAI,CAACgD,KAAK,IAAI,CAACqM,KAAK,EAAE;IACtBsG,WAAW,CAACtG,KAAK,EAAErM,KAAK,CAAC;EAC3B,CAAC;EACD0U,wBAAwBA,CAACD,SAAS,EAAEzU,KAAK,EAAE;IACzC,MAAMqM,KAAK,GAAGoI,SAAS,CAACxb,QAAQ,CAAC,CAAC,CAACoT,KAAK,CAACrP,KAAK;IAC9C,IAAI,CAACgD,KAAK,IAAI,CAACqM,KAAK,EAAE;IACtB2G,WAAW,CAAC3G,KAAK,EAAErM,KAAK,CAAC;EAC3B,CAAC;EACD2U,uBAAuBA,CAACF,SAAS,EAAEzU,KAAK,EAAEmS,WAAW,EAAE;IACrD,MAAM9F,KAAK,GAAGoI,SAAS,CAACxb,QAAQ,CAAC,CAAC,CAACoT,KAAK,CAACrP,KAAK;IAC9C,IAAI,CAACgD,KAAK,IAAI,CAACmS,WAAW,IAAI,CAAC9F,KAAK,EAAE;IACtCuG,YAAY,CAACvG,KAAK,EAAErM,KAAK,EAAEmS,WAAW,CAAC;EACzC,CAAC;EACDyC,kBAAkB,EAAEA,CAAA,KAAMd,UAAU;EACpCe,mBAAmB,EAAEA,CAAA,KAAMf,UAAU;EACrCgB,YAAYA,CAAC/b,QAAQ,EAAEmC,IAAI,EAAE6Z,QAAQ,EAAE/T,QAAQ,EAAEpG,KAAK,EAAE;IACtD,IAAIoa,cAAc,EAAEC,cAAc,EAAEC,eAAe;IACnDxD,gBAAgB,CAACxW,IAAI,EAAE8F,QAAQ,CAAC;IAChC,IAAImU,WAAW,GAAG,KAAK;;IAEvB;IACA,IAAIpc,QAAQ,CAACmC,IAAI,KAAK,WAAW,IAAI6Z,QAAQ,CAAC3W,MAAM,KAAK4C,QAAQ,CAAC5C,MAAM,EAAE+W,WAAW,GAAG,IAAI;IAC5F;IAAA,KACK,IAAI,CAAC,CAACH,cAAc,GAAGhU,QAAQ,CAACpF,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoZ,cAAc,CAAC/W,MAAM,OAAO,CAACgX,cAAc,GAAGF,QAAQ,CAACnZ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqZ,cAAc,CAAChX,MAAM,CAAC,EAAEkX,WAAW,GAAG,IAAI;IACxL;IAAA,KACK,IAAI,CAACD,eAAe,GAAGlU,QAAQ,CAACpF,IAAI,KAAK,IAAI,IAAIsZ,eAAe,CAACnP,IAAI,CAAC,CAACrM,KAAK,EAAEwG,KAAK,KAAK;MAC3F,IAAIkV,eAAe;MACnB,OAAO1b,KAAK,MAAM,CAAC0b,eAAe,GAAGL,QAAQ,CAACnZ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwZ,eAAe,CAAClV,KAAK,CAAC,CAAC;IAChG,CAAC,CAAC,EAAEiV,WAAW,GAAG,IAAI;;IAEtB;IACA,IAAIA,WAAW,EAAE;MACf1B,aAAa,CAAC3Q,IAAI,CAAC,CAAC/J,QAAQ,EAAE;QAC5B,GAAGiI;MACL,CAAC,EAAEpG,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA,MAAMqG,YAAY,GAAGF,SAAS,CAAChI,QAAQ,EAAEiI,QAAQ,CAAC;MAClD,IAAIrI,MAAM,CAACuF,IAAI,CAAC+C,YAAY,CAAC,CAAChD,MAAM,EAAE;QACpCtF,MAAM,CAACyX,MAAM,CAACrX,QAAQ,CAACkD,KAAK,EAAEgF,YAAY,CAAC;QAC3CK,UAAU,CAACvI,QAAQ,CAACqF,MAAM,EAAE6C,YAAY,CAAC;MAC3C;IACF;;IAEA;IACA,MAAMoU,aAAa,GAAGza,KAAK,CAAC0a,OAAO,KAAK,IAAI,IAAI,CAAC1a,KAAK,CAAC2a,KAAK,GAAGtB,MAAM,MAAMD,OAAO;IAClF,IAAIqB,aAAa,EAAE3B,aAAa,CAAC,CAAC;EACpC,CAAC;EACD8B,uBAAuB,EAAEA,CAAA,KAAM,KAAK;EACpCC,WAAWA,CAAA,EAAG,CAAC,CAAC;EAChBC,iBAAiB,EAAE3c,QAAQ,IAAIA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACqF,MAAM;EAC1EuX,gBAAgB,EAAEA,CAAA,KAAM,IAAI;EAC5BC,kBAAkB,EAAEnB,SAAS,IAAIpV,OAAO,CAACoV,SAAS,CAACxb,QAAQ,CAAC,CAAC,CAACoT,KAAK,EAAEoI,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACvFoB,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;EAC1BC,oBAAoB,EAAEA,CAAA,KAAM,KAAK;EACjCC,cAAc,EAAEA,CAAA,KAAM,KAAK;EAC3BlE,YAAY;EACZG,cAAc;EACdgE,kBAAkB,EAAEnC,kBAAkB;EACtCoC,gBAAgB,EAAEpC,kBAAkB;EACpCqC,kBAAkB,EAAErC,kBAAkB;EACtCsC,eAAe,EAAE,OAAOhJ,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAG5M,SAAS;EAC1E6V,aAAa,EAAE,OAAOlJ,YAAY,KAAK,UAAU,GAAGA,YAAY,GAAG3M,SAAS;EAC5E8V,SAAS,EAAE,CAAC,CAAC;EACbC,mBAAmB,EAAEA,CAAA,KAAM,IAAI;EAC/BC,wBAAwBA,CAAA,EAAG,CAAC,CAAC;EAC7BC,uBAAuBA,CAAA,EAAG,CAAC,CAAC;EAC5BC,qBAAqBA,CAAA,EAAG,CAAC,CAAC;EAC1BC,kBAAkBA,CAAA,EAAG,CAAC,CAAC;EACvBC,oBAAoB,EAAEA,CAAA,KAAM,IAAI;EAChCC,4BAA4B,EAAEA,CAAA,KAAM,KAAK;EACzCC,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAC;EAC7BC,gBAAgB,EAAEA,CAAA,KAAM,IAAI;EAC5BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,GAAG;EACjCC,wBAAwBA,CAAA,EAAG,CAAC,CAAC;EAC7BC,gBAAgB,EAAEA,CAAA,KAAM,KAAK;EAC7BC,eAAe,EAAEA,CAAA,KAAM,IAAI;EAC3B;EACAC,qBAAqBA,CAAA,EAAG,CAAC,CAAC;EAC1BC,eAAeA,CAAA,EAAG,CAAC,CAAC;EACpBC,sBAAsB,EAAEA,CAAA,KAAM,IAAI;EAClCC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,eAAe9f,KAAK,CAACoT,aAAa,CAAC,IAAI,CAAC;EAC/D2M,wBAAwBA,CAACC,WAAW,EAAE;IACpC1D,qBAAqB,GAAG0D,WAAW;EACrC,CAAC;EACDC,wBAAwBA,CAAA,EAAG;IACzB,OAAO3D,qBAAqB;EAC9B,CAAC;EACD4D,qBAAqBA,CAAA,EAAG;IACtB,IAAIC,aAAa;IACjB,IAAI7D,qBAAqB,KAAK5C,eAAe,EAAE,OAAO4C,qBAAqB;IAC3E,QAAQ,OAAOha,MAAM,KAAK,WAAW,KAAK,CAAC6d,aAAa,GAAG7d,MAAM,CAACoK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyT,aAAa,CAAC1c,IAAI,CAAC;MAC7G,KAAK,OAAO;MACZ,KAAK,aAAa;MAClB,KAAK,UAAU;MACf,KAAK,eAAe;MACpB,KAAK,aAAa;MAClB,KAAK,WAAW;QACd,OAAOtD,qBAAqB;MAC9B,KAAK,aAAa;MAClB,KAAK,YAAY;MACjB,KAAK,aAAa;MAClB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,OAAO;QACV,OAAOD,uBAAuB;MAChC;QACE,OAAOD,oBAAoB;IAC/B;EACF,CAAC;EACDmgB,iBAAiBA,CAAA,EAAG,CAAC;AACvB,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAG,IAAIpX,GAAG,CAAC,CAAC;AACxB,MAAMqX,YAAY,GAAG;EACnBla,OAAO,EAAE,SAAS;EAClB7C,MAAM,EAAE;AACV,CAAC;AACD,SAASgd,kBAAkBA,CAACC,MAAM,EAAEzU,IAAI,EAAE;EACxC,IAAI,CAACA,IAAI,IAAI,OAAOgL,iBAAiB,KAAK,WAAW,IAAIyJ,MAAM,YAAYzJ,iBAAiB,IAAIyJ,MAAM,CAACC,aAAa,EAAE;IACpH,MAAM;MACJvU,KAAK;MACLG,MAAM;MACND,GAAG;MACHH;IACF,CAAC,GAAGuU,MAAM,CAACC,aAAa,CAACC,qBAAqB,CAAC,CAAC;IAChD,OAAO;MACLxU,KAAK;MACLG,MAAM;MACND,GAAG;MACHH;IACF,CAAC;EACH,CAAC,MAAM,IAAI,CAACF,IAAI,IAAI,OAAO4U,eAAe,KAAK,WAAW,IAAIH,MAAM,YAAYG,eAAe,EAAE;IAC/F,OAAO;MACLzU,KAAK,EAAEsU,MAAM,CAACtU,KAAK;MACnBG,MAAM,EAAEmU,MAAM,CAACnU,MAAM;MACrBD,GAAG,EAAE,CAAC;MACNH,IAAI,EAAE;IACR,CAAC;EACH;EACA,OAAO;IACLC,KAAK,EAAE,CAAC;IACRG,MAAM,EAAE,CAAC;IACTD,GAAG,EAAE,CAAC;IACNH,IAAI,EAAE,CAAC;IACP,GAAGF;EACL,CAAC;AACH;AACA,SAAS6U,UAAUA,CAACJ,MAAM,EAAE;EAC1B;EACA,MAAMK,QAAQ,GAAGR,MAAM,CAACjX,GAAG,CAACoX,MAAM,CAAC;EACnC,MAAMM,SAAS,GAAGD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC1d,KAAK;EAC5D,MAAM4d,SAAS,GAAGF,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvT,KAAK;EAC5D,IAAIuT,QAAQ,EAAEG,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;;EAExE;EACA;EACA,MAAMC,mBAAmB,GAAG,OAAOC,WAAW,KAAK,UAAU;EAC7D;EACA;EACAA,WAAW;EACX;EACAH,OAAO,CAAC3c,KAAK;;EAEb;EACA,MAAMiJ,KAAK,GAAGyT,SAAS,IAAI1N,WAAW,CAACzH,UAAU,EAAE0H,OAAO,CAAC;EAC3D;EACA,MAAMnQ,KAAK,GAAG2d,SAAS,IAAI5H,UAAU,CAACkI,eAAe,CAAC9T,KAAK;EAC3D;EACAlN,cAAc;EACd;EACA,IAAI;EACJ;EACA,KAAK;EACL;EACA,IAAI;EACJ;EACA,EAAE;EACF;EACA8gB,mBAAmB;EACnB;EACAA,mBAAmB;EACnB;EACAA,mBAAmB;EACnB;EACA,IAAI,CAAC;EACL,CAAC;EACD;EACA,IAAI,CAACL,QAAQ,EAAER,MAAM,CAACxc,GAAG,CAAC2c,MAAM,EAAE;IAChCrd,KAAK;IACLmK;EACF,CAAC,CAAC;;EAEF;EACA,IAAI+T,SAAS;EACb,IAAIC,UAAU;EACd,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,OAAO,GAAG,IAAI;EAClB,OAAO;IACL,MAAMC,SAASA,CAACjd,KAAK,GAAG,CAAC,CAAC,EAAE;MAC1B,IAAIyD,OAAO;MACXuZ,OAAO,GAAG,IAAI1d,OAAO,CAAC4d,QAAQ,IAAIzZ,OAAO,GAAGyZ,QAAQ,CAAC;MACrD,IAAI;QACFhN,EAAE,EAAEiN,QAAQ;QACZ5V,IAAI,EAAE6V,SAAS;QACfhN,KAAK,EAAEiN,YAAY;QACnBhT,MAAM;QACNwS,SAAS,EAAES,iBAAiB;QAC5BC,OAAO,GAAG,KAAK;QACfvX,MAAM,GAAG,KAAK;QACdyK,IAAI,GAAG,KAAK;QACZD,MAAM,GAAG,KAAK;QACdgN,YAAY,GAAG,KAAK;QACpB1M,SAAS,GAAG,QAAQ;QACpB1Q,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ4P,WAAW;QACX5F,SAAS,EAAEqT,cAAc;QACzBnW,MAAM,EAAEoW,aAAa;QACrB9P;MACF,CAAC,GAAG5N,KAAK;MACT,IAAIJ,KAAK,GAAGkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC;;MAE5B;MACA,IAAIkT,EAAE,GAAGtQ,KAAK,CAACsQ,EAAE;MACjB,IAAI,CAACtQ,KAAK,CAACsQ,EAAE,EAAE;QACb,MAAMyN,YAAY,GAAG;UACnB3B,MAAM,EAAEA,MAAM;UACd4B,eAAe,EAAE,kBAAkB;UACnCC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE;QACT,CAAC;QACD,MAAMC,cAAc,GAAG,OAAOZ,QAAQ,KAAK,UAAU,GAAG,MAAMA,QAAQ,CAACQ,YAAY,CAAC,GAAGR,QAAQ;QAC/F,IAAIzO,UAAU,CAACqP,cAAc,CAAC,EAAE;UAC9B7N,EAAE,GAAG6N,cAAc;QACrB,CAAC,MAAM;UACL7N,EAAE,GAAG,IAAI3U,KAAK,CAACyiB,aAAa,CAAC;YAC3B,GAAGL,YAAY;YACf,GAAGR;UACL,CAAC,CAAC;QACJ;QACAvd,KAAK,CAACP,GAAG,CAAC;UACR6Q;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI9F,SAAS,GAAGxK,KAAK,CAACwK,SAAS;MAC/B,IAAI,CAACA,SAAS,EAAExK,KAAK,CAACP,GAAG,CAAC;QACxB+K,SAAS,EAAEA,SAAS,GAAG,IAAI7O,KAAK,CAAC0iB,SAAS,CAAC;MAC7C,CAAC,CAAC;;MAEF;MACA,MAAM;QACJC,MAAM;QACN,GAAGC;MACL,CAAC,GAAGV,cAAc,IAAI,CAAC,CAAC;MACxB,IAAI,CAACzc,EAAE,CAACS,GAAG,CAAC0c,OAAO,EAAE/T,SAAS,EAAE0R,YAAY,CAAC,EAAEzW,UAAU,CAAC+E,SAAS,EAAE;QACnE,GAAG+T;MACL,CAAC,CAAC;MACF,IAAI,CAACnd,EAAE,CAACS,GAAG,CAACyc,MAAM,EAAE9T,SAAS,CAAC8T,MAAM,EAAEpC,YAAY,CAAC,EAAEzW,UAAU,CAAC+E,SAAS,EAAE;QACzE8T,MAAM,EAAE;UACN,GAAG9T,SAAS,CAAC8T,MAAM;UACnB,GAAGA;QACL;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACte,KAAK,CAAC0H,MAAM,IAAI1H,KAAK,CAAC0H,MAAM,KAAKwV,UAAU,IAAI,CAAC9b,EAAE,CAACS,GAAG,CAACqb,UAAU,EAAEY,aAAa,EAAE5B,YAAY,CAAC,EAAE;QACpGgB,UAAU,GAAGY,aAAa;QAC1B,MAAMU,QAAQ,GAAGV,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACU,QAAQ;QACxE,MAAM9W,MAAM,GAAG8W,QAAQ,GAAGV,aAAa,GAAGF,YAAY,GAAG,IAAIjiB,KAAK,CAAC8iB,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI9iB,KAAK,CAAC+iB,iBAAiB,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;QAC5J,IAAI,CAACF,QAAQ,EAAE;UACb9W,MAAM,CAAC0H,QAAQ,CAACuP,CAAC,GAAG,CAAC;UACrB,IAAIb,aAAa,EAAE;YACjBrY,UAAU,CAACiC,MAAM,EAAEoW,aAAa,CAAC;YACjC;YACA;YACA,IAAI,CAACpW,MAAM,CAACE,MAAM,EAAE;cAClB,IAAI,QAAQ,IAAIkW,aAAa,IAAI,MAAM,IAAIA,aAAa,IAAI,OAAO,IAAIA,aAAa,IAAI,QAAQ,IAAIA,aAAa,IAAI,KAAK,IAAIA,aAAa,EAAE;gBAC3IpW,MAAM,CAACE,MAAM,GAAG,IAAI;gBACpBF,MAAM,CAACU,sBAAsB,CAAC,CAAC;cACjC;YACF;UACF;UACA;UACA,IAAI,CAACpI,KAAK,CAAC0H,MAAM,IAAI,EAAEoW,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACc,QAAQ,CAAC,EAAElX,MAAM,CAACmX,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjG;QACA7e,KAAK,CAACP,GAAG,CAAC;UACRiI;QACF,CAAC,CAAC;;QAEF;QACA;QACA8C,SAAS,CAAC9C,MAAM,GAAGA,MAAM;MAC3B;;MAEA;MACA,IAAI,CAAC1H,KAAK,CAACwQ,KAAK,EAAE;QAChB,IAAIA,KAAK;QACT,IAAIiN,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACqB,OAAO,EAAE;UAChDtO,KAAK,GAAGiN,YAAY;UACpBja,OAAO,CAACgN,KAAK,EAAEtH,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/B,CAAC,MAAM;UACLsH,KAAK,GAAG,IAAI7U,KAAK,CAACojB,KAAK,CAAC,CAAC;UACzBvb,OAAO,CAACgN,KAAK,EAAEtH,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;UAC7B,IAAIuU,YAAY,EAAEhY,UAAU,CAAC+K,KAAK,EAAEiN,YAAY,CAAC;QACnD;QACAzd,KAAK,CAACP,GAAG,CAAC;UACR+Q;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI/F,MAAM,IAAI,CAACzK,KAAK,CAACyK,MAAM,CAAC9G,QAAQ,EAAE3D,KAAK,CAACP,GAAG,CAAC;QAC9CgL,MAAM,EAAEA,MAAM,CAACvB,KAAK;MACtB,CAAC,CAAC;MACF;MACA,MAAMvB,IAAI,GAAGwU,kBAAkB,CAACC,MAAM,EAAEoB,SAAS,CAAC;MAClD,IAAI,CAACpc,EAAE,CAACS,GAAG,CAAC8F,IAAI,EAAE3H,KAAK,CAAC2H,IAAI,EAAEuU,YAAY,CAAC,EAAE;QAC3Clc,KAAK,CAAC0R,OAAO,CAAC/J,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACK,GAAG,EAAEL,IAAI,CAACE,IAAI,CAAC;MAC7D;MACA;MACA,IAAIrH,GAAG,IAAIR,KAAK,CAACuR,QAAQ,CAAC/Q,GAAG,KAAKD,YAAY,CAACC,GAAG,CAAC,EAAER,KAAK,CAAC2R,MAAM,CAACnR,GAAG,CAAC;MACtE;MACA,IAAIR,KAAK,CAACkR,SAAS,KAAKA,SAAS,EAAElR,KAAK,CAAC6R,YAAY,CAACX,SAAS,CAAC;MAChE;MACA,IAAI,CAAClR,KAAK,CAACgO,eAAe,EAAEhO,KAAK,CAACP,GAAG,CAAC;QACpCuO;MACF,CAAC,CAAC;MACF;MACA,IAAIoC,WAAW,IAAI,CAAChP,EAAE,CAACS,GAAG,CAACuO,WAAW,EAAEpQ,KAAK,CAACoQ,WAAW,EAAE8L,YAAY,CAAC,EAAElc,KAAK,CAACP,GAAG,CAACO,KAAK,KAAK;QAC5FoQ,WAAW,EAAE;UACX,GAAGpQ,KAAK,CAACoQ,WAAW;UACpB,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,IAAI,CAACpQ,KAAK,CAACyQ,EAAE,EAAE;QACb,IAAIuO,MAAM;QACV;QACA,MAAMC,aAAa,GAAGA,CAACvO,SAAS,EAAEwO,KAAK,KAAK;UAC1C,MAAMlf,KAAK,GAAGkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC;UAC9B,IAAI4C,KAAK,CAACkR,SAAS,KAAK,OAAO,EAAE;UACjChC,OAAO,CAACwB,SAAS,EAAE,IAAI,EAAE1Q,KAAK,EAAEkf,KAAK,CAAC;QACxC,CAAC;;QAED;QACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;UAChC,MAAMnf,KAAK,GAAGkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC;UAC9B4C,KAAK,CAACsQ,EAAE,CAACG,EAAE,CAAC7F,OAAO,GAAG5K,KAAK,CAACsQ,EAAE,CAACG,EAAE,CAAC2O,YAAY;UAC9Cpf,KAAK,CAACsQ,EAAE,CAACG,EAAE,CAAC4O,gBAAgB,CAACrf,KAAK,CAACsQ,EAAE,CAACG,EAAE,CAAC2O,YAAY,GAAGH,aAAa,GAAG,IAAI,CAAC;UAC7E,IAAI,CAACjf,KAAK,CAACsQ,EAAE,CAACG,EAAE,CAAC2O,YAAY,EAAE5X,UAAU,CAACxH,KAAK,CAAC;QAClD,CAAC;;QAED;QACA,MAAMyQ,EAAE,GAAG;UACT6O,OAAOA,CAAA,EAAG;YACR,MAAMhP,EAAE,GAAGpH,KAAK,CAAC9L,QAAQ,CAAC,CAAC,CAACkT,EAAE;YAC9BA,EAAE,CAACG,EAAE,CAAC8O,gBAAgB,CAAC,cAAc,EAAEJ,mBAAmB,CAAC;YAC3D7O,EAAE,CAACG,EAAE,CAAC8O,gBAAgB,CAAC,YAAY,EAAEJ,mBAAmB,CAAC;UAC3D,CAAC;UACDK,UAAUA,CAAA,EAAG;YACX,MAAMlP,EAAE,GAAGpH,KAAK,CAAC9L,QAAQ,CAAC,CAAC,CAACkT,EAAE;YAC9BA,EAAE,CAACG,EAAE,CAACgP,mBAAmB,CAAC,cAAc,EAAEN,mBAAmB,CAAC;YAC9D7O,EAAE,CAACG,EAAE,CAACgP,mBAAmB,CAAC,YAAY,EAAEN,mBAAmB,CAAC;UAC9D;QACF,CAAC;;QAED;QACA,IAAI,QAAQ,CAACH,MAAM,GAAG1O,EAAE,CAACG,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuO,MAAM,CAACO,gBAAgB,CAAC,KAAK,UAAU,EAAE9O,EAAE,CAAC6O,OAAO,CAAC,CAAC;QACrGtf,KAAK,CAACP,GAAG,CAAC;UACRgR;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIH,EAAE,CAACoP,SAAS,EAAE;QAChB,MAAMC,UAAU,GAAGrP,EAAE,CAACoP,SAAS,CAAC9U,OAAO;QACvC,MAAMgV,OAAO,GAAGtP,EAAE,CAACoP,SAAS,CAACrgB,IAAI;QACjCiR,EAAE,CAACoP,SAAS,CAAC9U,OAAO,GAAG,CAAC,CAAC+S,OAAO;QAChC,IAAIvc,EAAE,CAACM,GAAG,CAACic,OAAO,CAAC,EAAE;UACnBrN,EAAE,CAACoP,SAAS,CAACrgB,IAAI,GAAG1D,KAAK,CAACkkB,gBAAgB;QAC5C,CAAC,MAAM,IAAIze,EAAE,CAACI,GAAG,CAACmc,OAAO,CAAC,EAAE;UAC1B,IAAImC,cAAc;UAClB,MAAMC,KAAK,GAAG;YACZC,KAAK,EAAErkB,KAAK,CAACskB,cAAc;YAC3BC,UAAU,EAAEvkB,KAAK,CAACwkB,YAAY;YAC9BC,IAAI,EAAEzkB,KAAK,CAACkkB,gBAAgB;YAC5BQ,QAAQ,EAAE1kB,KAAK,CAAC2kB;UAClB,CAAC;UACDhQ,EAAE,CAACoP,SAAS,CAACrgB,IAAI,GAAG,CAACygB,cAAc,GAAGC,KAAK,CAACpC,OAAO,CAAC,KAAK,IAAI,GAAGmC,cAAc,GAAGnkB,KAAK,CAACkkB,gBAAgB;QACzG,CAAC,MAAM,IAAIze,EAAE,CAAC1D,GAAG,CAACigB,OAAO,CAAC,EAAE;UAC1B7gB,MAAM,CAACyX,MAAM,CAACjE,EAAE,CAACoP,SAAS,EAAE/B,OAAO,CAAC;QACtC;QACA,IAAIgC,UAAU,KAAKrP,EAAE,CAACoP,SAAS,CAAC9U,OAAO,IAAIgV,OAAO,KAAKtP,EAAE,CAACoP,SAAS,CAACrgB,IAAI,EAAEiR,EAAE,CAACoP,SAAS,CAACa,WAAW,GAAG,IAAI;MAC3G;MACA5kB,KAAK,CAAC6kB,eAAe,CAAC5V,OAAO,GAAG,CAACgG,MAAM;;MAEvC;MACA,IAAI,CAACuM,UAAU,EAAE;QACf7M,EAAE,CAACmQ,gBAAgB,GAAGra,MAAM,GAAGzK,KAAK,CAAC+kB,oBAAoB,GAAG/kB,KAAK,CAAC+K,cAAc;QAChF4J,EAAE,CAACqQ,WAAW,GAAG9P,IAAI,GAAGlV,KAAK,CAACilB,aAAa,GAAGjlB,KAAK,CAACklB,qBAAqB;MAC3E;;MAEA;MACA,IAAI7gB,KAAK,CAAC4Q,MAAM,KAAKA,MAAM,EAAE5Q,KAAK,CAACP,GAAG,CAAC,OAAO;QAC5CmR;MACF,CAAC,CAAC,CAAC;MACH,IAAI5Q,KAAK,CAACoG,MAAM,KAAKA,MAAM,EAAEpG,KAAK,CAACP,GAAG,CAAC,OAAO;QAC5C2G;MACF,CAAC,CAAC,CAAC;MACH,IAAIpG,KAAK,CAAC6Q,IAAI,KAAKA,IAAI,EAAE7Q,KAAK,CAACP,GAAG,CAAC,OAAO;QACxCoR;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,IAAI0M,QAAQ,IAAI,CAACnc,EAAE,CAACG,GAAG,CAACgc,QAAQ,CAAC,IAAI,CAACzO,UAAU,CAACyO,QAAQ,CAAC,IAAI,CAACnc,EAAE,CAACS,GAAG,CAAC0b,QAAQ,EAAEjN,EAAE,EAAE4L,YAAY,CAAC,EAAEzW,UAAU,CAAC6K,EAAE,EAAEiN,QAAQ,CAAC;;MAE3H;MACAN,SAAS,GAAGS,iBAAiB;MAC7BP,UAAU,GAAG,IAAI;MACjBtZ,OAAO,CAAC,CAAC;MACT,OAAO,IAAI;IACb,CAAC;IACDxD,MAAMA,CAACnB,QAAQ,EAAE;MACf;MACA,IAAI,CAACie,UAAU,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;MAC7CD,OAAO,CAAC0D,IAAI,CAAC,MAAM;QACjBhM,UAAU,CAACiM,eAAe,CAAE,aAAavkB,GAAG,CAACwkB,QAAQ,EAAE;UACrD9X,KAAK,EAAEA,KAAK;UACZhK,QAAQ,EAAEA,QAAQ;UAClB+d,SAAS,EAAEA,SAAS;UACpBgE,WAAW,EAAE7E;QACf,CAAC,CAAC,EAAErd,KAAK,EAAE,IAAI,EAAE,MAAM2F,SAAS,CAAC;MACnC,CAAC,CAAC;MACF,OAAOwE,KAAK;IACd,CAAC;IACDgY,OAAOA,CAAA,EAAG;MACRC,sBAAsB,CAAC/E,MAAM,CAAC;IAChC;EACF,CAAC;AACH;AACA,SAAS4E,QAAQA,CAAC;EAChB9X,KAAK;EACLhK,QAAQ;EACR+d,SAAS;EACTgE;AACF,CAAC,EAAE;EACDljB,yBAAyB,CAAC,MAAM;IAC9B,MAAMiC,KAAK,GAAGkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC;IAC9B;IACA4C,KAAK,CAACP,GAAG,CAACO,KAAK,KAAK;MAClB2G,QAAQ,EAAE;QACR,GAAG3G,KAAK,CAAC2G,QAAQ;QACjBwL,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAAC;IACH;IACA,IAAI8K,SAAS,EAAEA,SAAS,CAACjd,KAAK,CAAC;IAC/B;IACA;IACA,IAAI,CAACkJ,KAAK,CAAC9L,QAAQ,CAAC,CAAC,CAACqN,MAAM,CAAC8F,SAAS,EAAEvQ,KAAK,CAACyK,MAAM,CAAC6U,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtf,KAAK,CAACyK,MAAM,CAAC6U,OAAO,CAAC2B,WAAW,CAAC;IACjH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAazkB,GAAG,CAACuS,OAAO,CAACiS,QAAQ,EAAE;IACxCnjB,KAAK,EAAEqL,KAAK;IACZhK,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,SAASiiB,sBAAsBA,CAAC/E,MAAM,EAAEpQ,QAAQ,EAAE;EAChD,MAAM7O,IAAI,GAAG8e,MAAM,CAACjX,GAAG,CAACoX,MAAM,CAAC;EAC/B,MAAMrd,KAAK,GAAG5B,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC4B,KAAK;EAChD,IAAIA,KAAK,EAAE;IACT,MAAMiB,KAAK,GAAG7C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC+L,KAAK,CAAC9L,QAAQ,CAAC,CAAC;IAC3D,IAAI4C,KAAK,EAAEA,KAAK,CAAC2G,QAAQ,CAACwL,MAAM,GAAG,KAAK;IACxC2C,UAAU,CAACiM,eAAe,CAAC,IAAI,EAAEhiB,KAAK,EAAE,IAAI,EAAE,MAAM;MAClD,IAAIiB,KAAK,EAAE;QACTsR,UAAU,CAAC,MAAM;UACf,IAAI;YACF,IAAI8P,SAAS,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,UAAU;YAC5DvhB,KAAK,CAACyK,MAAM,CAAC+U,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxf,KAAK,CAACyK,MAAM,CAAC+U,UAAU,CAAC,CAAC;YACpE,CAAC4B,SAAS,GAAGphB,KAAK,CAACsQ,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC+Q,qBAAqB,GAAGD,SAAS,CAACI,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACre,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGqe,qBAAqB,CAACre,OAAO,CAAC,CAAC;YAC7L,CAACse,UAAU,GAAGthB,KAAK,CAACsQ,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgR,UAAU,CAACG,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,UAAU,CAACG,gBAAgB,CAAC,CAAC;YACvH,IAAI,CAACF,UAAU,GAAGvhB,KAAK,CAACsQ,EAAE,KAAK,IAAI,IAAIiR,UAAU,CAAC9Q,EAAE,EAAEzQ,KAAK,CAACyQ,EAAE,CAAC+O,UAAU,CAAC,CAAC;YAC3Exc,OAAO,CAAChD,KAAK,CAACwQ,KAAK,CAAC;YACpByL,MAAM,CAAClT,MAAM,CAACqT,MAAM,CAAC;YACrB,IAAIpQ,QAAQ,EAAEA,QAAQ,CAACoQ,MAAM,CAAC;UAChC,CAAC,CAAC,OAAOnX,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;EACJ;AACF;AACA,SAASyc,YAAYA,CAACxiB,QAAQ,EAAE0Z,SAAS,EAAE5Y,KAAK,EAAE;EAChD,OAAO,aAAaxD,GAAG,CAACmlB,MAAM,EAAE;IAC9BziB,QAAQ,EAAEA,QAAQ;IAClB0Z,SAAS,EAAEA,SAAS;IACpB5Y,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACA,SAAS2hB,MAAMA,CAAC;EACd3hB,KAAK,GAAG,CAAC,CAAC;EACVd,QAAQ;EACR0Z;AACF,CAAC,EAAE;EACD;AACF;AACA;AACA;AACA;EACE,MAAM;IACJnO,MAAM;IACN9C,IAAI;IACJ,GAAGia;EACL,CAAC,GAAG5hB,KAAK;EACT,MAAM3C,YAAY,GAAG0V,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACvI,SAAS,CAAC,GAAG5O,KAAK,CAACimB,QAAQ,CAAC,MAAM,IAAIlmB,KAAK,CAAC0iB,SAAS,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAChS,OAAO,CAAC,GAAGzQ,KAAK,CAACimB,QAAQ,CAAC,MAAM,IAAIlmB,KAAK,CAAC0U,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAMyR,MAAM,GAAGrjB,kBAAkB,CAAC,CAACkH,SAAS,EAAEoc,WAAW,KAAK;IAC5D,IAAIxQ,QAAQ,GAAG7M,SAAS;IACxB,IAAIqd,WAAW,CAACra,MAAM,IAAIC,IAAI,EAAE;MAC9B,MAAMD,MAAM,GAAGqa,WAAW,CAACra,MAAM;MACjC;MACA6J,QAAQ,GAAG5L,SAAS,CAAC4L,QAAQ,CAAChC,kBAAkB,CAAC7H,MAAM,EAAE,IAAI/L,KAAK,CAAC4Q,OAAO,CAAC,CAAC,EAAE5E,IAAI,CAAC;MACnF;MACA,IAAID,MAAM,KAAK/B,SAAS,CAAC+B,MAAM,EAAED,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;IAC7D;IACA,OAAO;MACL;MACA,GAAGhC,SAAS;MACZ,GAAGoc,WAAW;MACd;MACAvR,KAAK,EAAEoI,SAAS;MAChBpO,SAAS;MACT6B,OAAO;MACP4E,KAAK,EAAE5E,OAAO;MACd;MACAhP,YAAY;MACZ;MACAoN,MAAM,EAAE;QACN,GAAG9E,SAAS,CAAC8E,MAAM;QACnB,GAAGsX,WAAW,CAACtX,MAAM;QACrB,GAAGA;MACL,CAAC;MACD9C,IAAI,EAAE;QACJ,GAAGhC,SAAS,CAACgC,IAAI;QACjB,GAAGA;MACL,CAAC;MACD4J,QAAQ,EAAE;QACR,GAAG5L,SAAS,CAAC4L,QAAQ;QACrB,GAAGA;MACL,CAAC;MACD;MACAE,SAAS,EAAEhH,MAAM,IAAIsX,WAAW,CAACtiB,GAAG,CAACO,KAAK,KAAK;QAC7C,GAAGA,KAAK;QACRyK,MAAM,EAAE;UACN,GAAGzK,KAAK,CAACyK,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;EACF,MAAMuX,cAAc,GAAGpmB,KAAK,CAACqD,OAAO,CAAC,MAAM;IACzC;IACA,MAAMiK,KAAK,GAAGjN,oBAAoB,CAAC,CAACwD,GAAG,EAAEuF,GAAG,MAAM;MAChD,GAAG4c,IAAI;MACPniB,GAAG;MACHuF;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMid,QAAQ,GAAGC,IAAI,IAAIhZ,KAAK,CAACiZ,QAAQ,CAACniB,KAAK,IAAI8hB,MAAM,CAACjjB,OAAO,CAACqjB,IAAI,EAAEliB,KAAK,CAAC,CAAC;IAC7EiiB,QAAQ,CAAC5kB,YAAY,CAACD,QAAQ,CAAC,CAAC,CAAC;IACjCC,YAAY,CAAC+U,SAAS,CAAC6P,QAAQ,CAAC;IAChC,OAAO/Y,KAAK;IACZ;EACF,CAAC,EAAE,CAAC7L,YAAY,EAAEub,SAAS,CAAC,CAAC;EAC7B,QACE;IACA;IACApc,GAAG,CAACC,QAAQ,EAAE;MACZyC,QAAQ,EAAE4V,UAAU,CAAC4M,YAAY,CAAE,aAAallB,GAAG,CAACuS,OAAO,CAACiS,QAAQ,EAAE;QACpEnjB,KAAK,EAAEmkB,cAAc;QACrB9iB,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAE8iB,cAAc,EAAE,IAAI;IAC1B,CAAC;EAAC;AAEN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAAC1jB,EAAE,EAAE;EACrB,OAAOoW,UAAU,CAACsN,SAAS,CAAC1jB,EAAE,CAAC;AACjC;AAEA,SAAS2jB,UAAUA,CAACrW,QAAQ,EAAEsW,IAAI,EAAE;EAClC,MAAMC,GAAG,GAAG;IACVvW;EACF,CAAC;EACDsW,IAAI,CAAC7W,GAAG,CAAC8W,GAAG,CAAC;EACb,OAAO,MAAM,KAAKD,IAAI,CAACvZ,MAAM,CAACwZ,GAAG,CAAC;AACpC;AACA,MAAMC,aAAa,GAAG,IAAInY,GAAG,CAAC,CAAC;AAC/B,MAAMoY,kBAAkB,GAAG,IAAIpY,GAAG,CAAC,CAAC;AACpC,MAAMqY,iBAAiB,GAAG,IAAIrY,GAAG,CAAC,CAAC;;AAEnC;AACA;AACA;AACA;AACA,MAAMsY,SAAS,GAAG3W,QAAQ,IAAIqW,UAAU,CAACrW,QAAQ,EAAEwW,aAAa,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAG5W,QAAQ,IAAIqW,UAAU,CAACrW,QAAQ,EAAEyW,kBAAkB,CAAC;;AAE3E;AACA;AACA;AACA;AACA,MAAMI,OAAO,GAAG7W,QAAQ,IAAIqW,UAAU,CAACrW,QAAQ,EAAE0W,iBAAiB,CAAC;AACnE,SAASI,GAAGA,CAACC,OAAO,EAAErS,SAAS,EAAE;EAC/B,IAAI,CAACqS,OAAO,CAACpb,IAAI,EAAE;EACnB,KAAK,MAAM;IACTqE;EACF,CAAC,IAAI+W,OAAO,CAACnX,MAAM,CAAC,CAAC,EAAE;IACrBI,QAAQ,CAAC0E,SAAS,CAAC;EACrB;AACF;AACA,SAASsS,kBAAkBA,CAAC3jB,IAAI,EAAEqR,SAAS,EAAE;EAC3C,QAAQrR,IAAI;IACV,KAAK,QAAQ;MACX,OAAOyjB,GAAG,CAACN,aAAa,EAAE9R,SAAS,CAAC;IACtC,KAAK,OAAO;MACV,OAAOoS,GAAG,CAACL,kBAAkB,EAAE/R,SAAS,CAAC;IAC3C,KAAK,MAAM;MACT,OAAOoS,GAAG,CAACJ,iBAAiB,EAAEhS,SAAS,CAAC;EAC5C;AACF;AACA,IAAIuB,WAAW;AACf,IAAIgR,YAAY;AAChB,SAASC,MAAMA,CAACxS,SAAS,EAAE1Q,KAAK,EAAEkf,KAAK,EAAE;EACvC;EACA,IAAInT,KAAK,GAAG/L,KAAK,CAAC+Q,KAAK,CAACoS,QAAQ,CAAC,CAAC;;EAElC;EACA,IAAInjB,KAAK,CAACkR,SAAS,KAAK,OAAO,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IAChE3E,KAAK,GAAG2E,SAAS,GAAG1Q,KAAK,CAAC+Q,KAAK,CAACgB,WAAW;IAC3C/R,KAAK,CAAC+Q,KAAK,CAACqS,OAAO,GAAGpjB,KAAK,CAAC+Q,KAAK,CAACgB,WAAW;IAC7C/R,KAAK,CAAC+Q,KAAK,CAACgB,WAAW,GAAGrB,SAAS;EACrC;;EAEA;EACAuB,WAAW,GAAGjS,KAAK,CAAC2G,QAAQ,CAACsL,WAAW;EACxC,KAAK,IAAI9P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,WAAW,CAAC7P,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C8gB,YAAY,GAAGhR,WAAW,CAAC9P,CAAC,CAAC;IAC7B8gB,YAAY,CAACtkB,GAAG,CAACE,OAAO,CAACokB,YAAY,CAAC/Z,KAAK,CAAC9L,QAAQ,CAAC,CAAC,EAAE2O,KAAK,EAAEmT,KAAK,CAAC;EACvE;;EAEA;EACA,IAAI,CAAClf,KAAK,CAAC2G,QAAQ,CAAC0E,QAAQ,IAAIrL,KAAK,CAACsQ,EAAE,CAACjQ,MAAM,EAAEL,KAAK,CAACsQ,EAAE,CAACjQ,MAAM,CAACL,KAAK,CAACwQ,KAAK,EAAExQ,KAAK,CAAC0H,MAAM,CAAC;;EAE3F;EACA1H,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAGzG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEhB,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAG,CAAC,CAAC;EAC9D,OAAOvH,KAAK,CAACkR,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAGlR,KAAK,CAAC2G,QAAQ,CAACY,MAAM;AACjE;AACA,IAAI8b,OAAO,GAAG,KAAK;AACnB,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,MAAM;AACV,IAAIrE,KAAK;AACT,IAAIlf,KAAK;AACT,SAASwjB,IAAIA,CAAC9S,SAAS,EAAE;EACvBwO,KAAK,GAAGhR,qBAAqB,CAACsV,IAAI,CAAC;EACnCH,OAAO,GAAG,IAAI;EACdE,MAAM,GAAG,CAAC;;EAEV;EACAP,kBAAkB,CAAC,QAAQ,EAAEtS,SAAS,CAAC;;EAEvC;EACA4S,kBAAkB,GAAG,IAAI;EACzB,KAAK,MAAMnmB,IAAI,IAAI8e,MAAM,CAACrQ,MAAM,CAAC,CAAC,EAAE;IAClC,IAAI6X,YAAY;IAChBzjB,KAAK,GAAG7C,IAAI,CAAC+L,KAAK,CAAC9L,QAAQ,CAAC,CAAC;;IAE7B;IACA,IAAI4C,KAAK,CAAC2G,QAAQ,CAACwL,MAAM,KAAKnS,KAAK,CAACkR,SAAS,KAAK,QAAQ,IAAIlR,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAACkc,YAAY,GAAGzjB,KAAK,CAACsQ,EAAE,CAACG,EAAE,KAAK,IAAI,IAAIgT,YAAY,CAACrE,YAAY,CAAC,EAAE;MAChKmE,MAAM,IAAIL,MAAM,CAACxS,SAAS,EAAE1Q,KAAK,CAAC;IACpC;EACF;EACAsjB,kBAAkB,GAAG,KAAK;;EAE1B;EACAN,kBAAkB,CAAC,OAAO,EAAEtS,SAAS,CAAC;;EAEtC;EACA,IAAI6S,MAAM,KAAK,CAAC,EAAE;IAChB;IACAP,kBAAkB,CAAC,MAAM,EAAEtS,SAAS,CAAC;;IAErC;IACA2S,OAAO,GAAG,KAAK;IACf,OAAOK,oBAAoB,CAACxE,KAAK,CAAC;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAS1X,UAAUA,CAACxH,KAAK,EAAEuH,MAAM,GAAG,CAAC,EAAE;EACrC,IAAIoc,aAAa;EACjB,IAAI,CAAC3jB,KAAK,EAAE,OAAOic,MAAM,CAAC1S,OAAO,CAACpM,IAAI,IAAIqK,UAAU,CAACrK,IAAI,CAAC+L,KAAK,CAAC9L,QAAQ,CAAC,CAAC,EAAEmK,MAAM,CAAC,CAAC;EACpF,IAAI,CAACoc,aAAa,GAAG3jB,KAAK,CAACsQ,EAAE,CAACG,EAAE,KAAK,IAAI,IAAIkT,aAAa,CAACvE,YAAY,IAAI,CAACpf,KAAK,CAAC2G,QAAQ,CAACwL,MAAM,IAAInS,KAAK,CAACkR,SAAS,KAAK,OAAO,EAAE;EAClI,IAAI3J,MAAM,GAAG,CAAC,EAAE;IACd;IACA;IACAvH,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAGzG,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEf,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAGA,MAAM,CAAC;EACtE,CAAC,MAAM;IACL,IAAI+b,kBAAkB,EAAE;MACtB;MACAtjB,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL;MACAvH,KAAK,CAAC2G,QAAQ,CAACY,MAAM,GAAG,CAAC;IAC3B;EACF;;EAEA;EACA,IAAI,CAAC8b,OAAO,EAAE;IACZA,OAAO,GAAG,IAAI;IACdnV,qBAAqB,CAACsV,IAAI,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAStU,OAAOA,CAACwB,SAAS,EAAEC,gBAAgB,GAAG,IAAI,EAAE3Q,KAAK,EAAEkf,KAAK,EAAE;EACjE,IAAIvO,gBAAgB,EAAEqS,kBAAkB,CAAC,QAAQ,EAAEtS,SAAS,CAAC;EAC7D,IAAI,CAAC1Q,KAAK,EAAE,KAAK,MAAM7C,IAAI,IAAI8e,MAAM,CAACrQ,MAAM,CAAC,CAAC,EAAEsX,MAAM,CAACxS,SAAS,EAAEvT,IAAI,CAAC+L,KAAK,CAAC9L,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK8lB,MAAM,CAACxS,SAAS,EAAE1Q,KAAK,EAAEkf,KAAK,CAAC;EAC7H,IAAIvO,gBAAgB,EAAEqS,kBAAkB,CAAC,OAAO,EAAEtS,SAAS,CAAC;AAC9D;AAEA,MAAMkT,UAAU,GAAG;EACjBC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;EACzBC,aAAa,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC;EACrCC,aAAa,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;EAClCC,OAAO,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC;EACxBC,aAAa,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC;EACpCC,WAAW,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC;EAChCpW,cAAc,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC;EACtCc,aAAa,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC;EACpCuV,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC;EACxCC,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,IAAI;AACnD,CAAC;;AAED;AACA,SAASC,mBAAmBA,CAACnb,KAAK,EAAE;EAClC,MAAM;IACJ+E;EACF,CAAC,GAAGzE,YAAY,CAACN,KAAK,CAAC;EACvB,OAAO;IACLmC,QAAQ,EAAE,CAAC;IACXT,OAAO,EAAE,IAAI;IACbF,OAAOA,CAACpC,KAAK,EAAEtI,KAAK,EAAEyE,QAAQ,EAAE;MAC9B;MACA;MACAzE,KAAK,CAACqM,OAAO,CAAC5M,GAAG,CAAC6I,KAAK,CAACqB,OAAO,GAAG3J,KAAK,CAAC2H,IAAI,CAACG,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEQ,KAAK,CAACwB,OAAO,GAAG9J,KAAK,CAAC2H,IAAI,CAACM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACzGjI,KAAK,CAACwK,SAAS,CAAC8Z,aAAa,CAACtkB,KAAK,CAACqM,OAAO,EAAErM,KAAK,CAAC0H,MAAM,CAAC;IAC5D,CAAC;IACD6I,SAAS,EAAE7L,SAAS;IACpBf,QAAQ,EAAE7G,MAAM,CAACuF,IAAI,CAACuhB,UAAU,CAAC,CAACW,MAAM,CAAC,CAACC,GAAG,EAAElhB,GAAG,MAAM;MACtD,GAAGkhB,GAAG;MACN,CAAClhB,GAAG,GAAG2K,aAAa,CAAC3K,GAAG;IAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACP4f,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIuB,mBAAmB;MACvB,MAAM;QACJha,MAAM;QACN9D;MACF,CAAC,GAAGuC,KAAK,CAAC9L,QAAQ,CAAC,CAAC;MACpB,IAAI,CAACqnB,mBAAmB,GAAG9d,QAAQ,CAACyH,SAAS,KAAK,IAAI,IAAIqW,mBAAmB,CAAC5lB,OAAO,IAAI4L,MAAM,CAAC9G,QAAQ,EAAE8G,MAAM,CAAC9G,QAAQ,CAACiL,aAAa,CAACjI,QAAQ,CAACyH,SAAS,CAACvP,OAAO,CAAC;IACrK,CAAC;IACDygB,OAAO,EAAE5e,MAAM,IAAI;MACjB,MAAM;QACJjB,GAAG;QACHgL;MACF,CAAC,GAAGvB,KAAK,CAAC9L,QAAQ,CAAC,CAAC;MACpBqN,MAAM,CAAC+U,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG/U,MAAM,CAAC+U,UAAU,CAAC,CAAC;MACxD/f,GAAG,CAACO,KAAK,KAAK;QACZyK,MAAM,EAAE;UACN,GAAGzK,KAAK,CAACyK,MAAM;UACf8F,SAAS,EAAE7P;QACb;MACF,CAAC,CAAC,CAAC;MACH,IAAI+J,MAAM,CAAC9G,QAAQ,EAAE;QACnB,KAAK,MAAMd,IAAI,IAAI4H,MAAM,CAAC9G,QAAQ,EAAE;UAClC,MAAM2E,KAAK,GAAGmC,MAAM,CAAC9G,QAAQ,CAACd,IAAI,CAAC;UACnC,MAAM,CAAC6hB,SAAS,EAAEC,OAAO,CAAC,GAAGf,UAAU,CAAC/gB,IAAI,CAAC;UAC7CnC,MAAM,CAAC6e,gBAAgB,CAACmF,SAAS,EAAEpc,KAAK,EAAE;YACxCqc;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACDnF,UAAU,EAAEA,CAAA,KAAM;MAChB,MAAM;QACJ/f,GAAG;QACHgL;MACF,CAAC,GAAGvB,KAAK,CAAC9L,QAAQ,CAAC,CAAC;MACpB,IAAIqN,MAAM,CAAC8F,SAAS,EAAE;QACpB,IAAI9F,MAAM,CAAC9G,QAAQ,EAAE;UACnB,KAAK,MAAMd,IAAI,IAAI4H,MAAM,CAAC9G,QAAQ,EAAE;YAClC,MAAM2E,KAAK,GAAGmC,MAAM,CAAC9G,QAAQ,CAACd,IAAI,CAAC;YACnC,MAAM,CAAC6hB,SAAS,CAAC,GAAGd,UAAU,CAAC/gB,IAAI,CAAC;YACpC4H,MAAM,CAAC8F,SAAS,CAACkP,mBAAmB,CAACiF,SAAS,EAAEpc,KAAK,CAAC;UACxD;QACF;QACA7I,GAAG,CAACO,KAAK,KAAK;UACZyK,MAAM,EAAE;YACN,GAAGzK,KAAK,CAACyK,MAAM;YACf8F,SAAS,EAAE7L;UACb;QACF,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;AACH;AAEA,SAASqO,QAAQ,IAAI6R,CAAC,EAAEplB,KAAK,IAAIqlB,CAAC,EAAE3R,QAAQ,IAAI4R,CAAC,EAAEzR,QAAQ,IAAI0R,CAAC,EAAEplB,aAAa,IAAIqlB,CAAC,EAAEzR,QAAQ,IAAI0R,CAAC,EAAExQ,SAAS,IAAIyQ,CAAC,EAAEjJ,MAAM,IAAIkJ,CAAC,EAAE1mB,kBAAkB,IAAI4C,CAAC,EAAEtD,yBAAyB,IAAI+D,CAAC,EAAE0a,UAAU,IAAI4I,CAAC,EAAEjE,sBAAsB,IAAIkE,CAAC,EAAEzP,MAAM,IAAI3Q,CAAC,EAAEof,mBAAmB,IAAIiB,CAAC,EAAE9b,YAAY,IAAI+b,CAAC,EAAEvC,kBAAkB,IAAIjT,CAAC,EAAEtS,KAAK,IAAI0E,CAAC,EAAEwgB,SAAS,IAAI6C,CAAC,EAAE5C,cAAc,IAAI6C,CAAC,EAAE5C,OAAO,IAAI6C,CAAC,EAAEle,UAAU,IAAIme,CAAC,EAAEzW,OAAO,IAAI0W,CAAC,EAAElE,YAAY,IAAItY,CAAC,EAAEgZ,SAAS,IAAInf,CAAC,EAAE8L,OAAO,IAAI8W,CAAC,EAAE/Q,UAAU,IAAIgR,CAAC,EAAErgB,UAAU,IAAI4M,CAAC,EAAExV,UAAU,IAAIkpB,CAAC,EAAEjnB,SAAS,IAAIknB,CAAC,EAAE/kB,YAAY,IAAIglB,CAAC,EAAEjjB,OAAO,IAAIiN,CAAC,EAAE3S,GAAG,IAAIkP,CAAC,EAAElK,UAAU,IAAImK,CAAC,EAAEoG,iBAAiB,IAAI8L,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}