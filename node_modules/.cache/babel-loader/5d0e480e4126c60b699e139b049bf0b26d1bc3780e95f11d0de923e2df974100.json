{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Mesh, Matrix4, Vector2, Color, Vector3, PerspectiveCamera, DepthTexture, UnsignedShortType, NearestFilter, WebGLRenderTarget, ShaderMaterial, UniformsUtils, Plane, HalfFloatType } from \"three\";\nconst ReflectorForSSRPass = /* @__PURE__ */(() => {\n  const _ReflectorForSSRPass = class extends Mesh {\n    constructor(geometry, options = {}) {\n      super(geometry);\n      this.isReflectorForSSRPass = true;\n      this.type = \"ReflectorForSSRPass\";\n      const scope = this;\n      const color = options.color !== void 0 ? new Color(options.color) : new Color(8355711);\n      const textureWidth = options.textureWidth || 512;\n      const textureHeight = options.textureHeight || 512;\n      const clipBias = options.clipBias || 0;\n      const shader = options.shader || _ReflectorForSSRPass.ReflectorShader;\n      const useDepthTexture = options.useDepthTexture === true;\n      const yAxis = new Vector3(0, 1, 0);\n      const vecTemp0 = new Vector3();\n      const vecTemp1 = new Vector3();\n      scope.needsUpdate = false;\n      scope.maxDistance = _ReflectorForSSRPass.ReflectorShader.uniforms.maxDistance.value;\n      scope.opacity = _ReflectorForSSRPass.ReflectorShader.uniforms.opacity.value;\n      scope.color = color;\n      scope.resolution = options.resolution || new Vector2(window.innerWidth, window.innerHeight);\n      scope._distanceAttenuation = _ReflectorForSSRPass.ReflectorShader.defines.DISTANCE_ATTENUATION;\n      Object.defineProperty(scope, \"distanceAttenuation\", {\n        get() {\n          return scope._distanceAttenuation;\n        },\n        set(val) {\n          if (scope._distanceAttenuation === val) return;\n          scope._distanceAttenuation = val;\n          scope.material.defines.DISTANCE_ATTENUATION = val;\n          scope.material.needsUpdate = true;\n        }\n      });\n      scope._fresnel = _ReflectorForSSRPass.ReflectorShader.defines.FRESNEL;\n      Object.defineProperty(scope, \"fresnel\", {\n        get() {\n          return scope._fresnel;\n        },\n        set(val) {\n          if (scope._fresnel === val) return;\n          scope._fresnel = val;\n          scope.material.defines.FRESNEL = val;\n          scope.material.needsUpdate = true;\n        }\n      });\n      const normal = new Vector3();\n      const reflectorWorldPosition = new Vector3();\n      const cameraWorldPosition = new Vector3();\n      const rotationMatrix = new Matrix4();\n      const lookAtPosition = new Vector3(0, 0, -1);\n      const view = new Vector3();\n      const target = new Vector3();\n      const textureMatrix = new Matrix4();\n      const virtualCamera = new PerspectiveCamera();\n      let depthTexture;\n      if (useDepthTexture) {\n        depthTexture = new DepthTexture();\n        depthTexture.type = UnsignedShortType;\n        depthTexture.minFilter = NearestFilter;\n        depthTexture.magFilter = NearestFilter;\n      }\n      const parameters = {\n        depthTexture: useDepthTexture ? depthTexture : null,\n        type: HalfFloatType\n      };\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, parameters);\n      const material = new ShaderMaterial({\n        transparent: useDepthTexture,\n        defines: Object.assign({}, _ReflectorForSSRPass.ReflectorShader.defines, {\n          useDepthTexture\n        }),\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        fragmentShader: shader.fragmentShader,\n        vertexShader: shader.vertexShader\n      });\n      material.uniforms[\"tDiffuse\"].value = renderTarget.texture;\n      material.uniforms[\"color\"].value = scope.color;\n      material.uniforms[\"textureMatrix\"].value = textureMatrix;\n      if (useDepthTexture) {\n        material.uniforms[\"tDepth\"].value = renderTarget.depthTexture;\n      }\n      this.material = material;\n      const globalPlane = new Plane(new Vector3(0, 1, 0), clipBias);\n      const globalPlanes = [globalPlane];\n      this.doRender = function (renderer, scene, camera) {\n        material.uniforms[\"maxDistance\"].value = scope.maxDistance;\n        material.uniforms[\"color\"].value = scope.color;\n        material.uniforms[\"opacity\"].value = scope.opacity;\n        vecTemp0.copy(camera.position).normalize();\n        vecTemp1.copy(vecTemp0).reflect(yAxis);\n        material.uniforms[\"fresnelCoe\"].value = (vecTemp0.dot(vecTemp1) + 1) / 2;\n        reflectorWorldPosition.setFromMatrixPosition(scope.matrixWorld);\n        cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n        rotationMatrix.extractRotation(scope.matrixWorld);\n        normal.set(0, 0, 1);\n        normal.applyMatrix4(rotationMatrix);\n        view.subVectors(reflectorWorldPosition, cameraWorldPosition);\n        if (view.dot(normal) > 0) return;\n        view.reflect(normal).negate();\n        view.add(reflectorWorldPosition);\n        rotationMatrix.extractRotation(camera.matrixWorld);\n        lookAtPosition.set(0, 0, -1);\n        lookAtPosition.applyMatrix4(rotationMatrix);\n        lookAtPosition.add(cameraWorldPosition);\n        target.subVectors(reflectorWorldPosition, lookAtPosition);\n        target.reflect(normal).negate();\n        target.add(reflectorWorldPosition);\n        virtualCamera.position.copy(view);\n        virtualCamera.up.set(0, 1, 0);\n        virtualCamera.up.applyMatrix4(rotationMatrix);\n        virtualCamera.up.reflect(normal);\n        virtualCamera.lookAt(target);\n        virtualCamera.far = camera.far;\n        virtualCamera.updateMatrixWorld();\n        virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n        material.uniforms[\"virtualCameraNear\"].value = camera.near;\n        material.uniforms[\"virtualCameraFar\"].value = camera.far;\n        material.uniforms[\"virtualCameraMatrixWorld\"].value = virtualCamera.matrixWorld;\n        material.uniforms[\"virtualCameraProjectionMatrix\"].value = camera.projectionMatrix;\n        material.uniforms[\"virtualCameraProjectionMatrixInverse\"].value = camera.projectionMatrixInverse;\n        material.uniforms[\"resolution\"].value = scope.resolution;\n        textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n        textureMatrix.multiply(virtualCamera.projectionMatrix);\n        textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n        textureMatrix.multiply(scope.matrixWorld);\n        const currentRenderTarget = renderer.getRenderTarget();\n        const currentXrEnabled = renderer.xr.enabled;\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;\n        const currentClippingPlanes = renderer.clippingPlanes;\n        renderer.xr.enabled = false;\n        renderer.shadowMap.autoUpdate = false;\n        renderer.clippingPlanes = globalPlanes;\n        renderer.setRenderTarget(renderTarget);\n        renderer.state.buffers.depth.setMask(true);\n        if (renderer.autoClear === false) renderer.clear();\n        renderer.render(scene, virtualCamera);\n        renderer.xr.enabled = currentXrEnabled;\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;\n        renderer.clippingPlanes = currentClippingPlanes;\n        renderer.setRenderTarget(currentRenderTarget);\n        const viewport = camera.viewport;\n        if (viewport !== void 0) {\n          renderer.state.viewport(viewport);\n        }\n      };\n      this.getRenderTarget = function () {\n        return renderTarget;\n      };\n    }\n  };\n  let ReflectorForSSRPass2 = _ReflectorForSSRPass;\n  __publicField(ReflectorForSSRPass2, \"ReflectorShader\", {\n    defines: {\n      DISTANCE_ATTENUATION: true,\n      FRESNEL: true\n    },\n    uniforms: {\n      color: {\n        value: null\n      },\n      tDiffuse: {\n        value: null\n      },\n      tDepth: {\n        value: null\n      },\n      textureMatrix: {\n        value: new Matrix4()\n      },\n      maxDistance: {\n        value: 180\n      },\n      opacity: {\n        value: 0.5\n      },\n      fresnelCoe: {\n        value: null\n      },\n      virtualCameraNear: {\n        value: null\n      },\n      virtualCameraFar: {\n        value: null\n      },\n      virtualCameraProjectionMatrix: {\n        value: new Matrix4()\n      },\n      virtualCameraMatrixWorld: {\n        value: new Matrix4()\n      },\n      virtualCameraProjectionMatrixInverse: {\n        value: new Matrix4()\n      },\n      resolution: {\n        value: new Vector2()\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\t\tuniform mat4 textureMatrix;\n\t\tvarying vec4 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`),\n    fragmentShader: (/* glsl */\n    `\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\t\tuniform sampler2D tDepth;\n\t\tuniform float maxDistance;\n\t\tuniform float opacity;\n\t\tuniform float fresnelCoe;\n\t\tuniform float virtualCameraNear;\n\t\tuniform float virtualCameraFar;\n\t\tuniform mat4 virtualCameraProjectionMatrix;\n\t\tuniform mat4 virtualCameraProjectionMatrixInverse;\n\t\tuniform mat4 virtualCameraMatrixWorld;\n\t\tuniform vec2 resolution;\n\t\tvarying vec4 vUv;\n\t\t#include <packing>\n\t\tfloat blendOverlay( float base, float blend ) {\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\t\t}\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\t\t}\n\t\tfloat getDepth( const in vec2 uv ) {\n\t\t\treturn texture2D( tDepth, uv ).x;\n\t\t}\n\t\tfloat getViewZ( const in float depth ) {\n\t\t\treturn perspectiveDepthToViewZ( depth, virtualCameraNear, virtualCameraFar );\n\t\t}\n\t\tvec3 getViewPosition( const in vec2 uv, const in float depth/*clip space*/, const in float clipW ) {\n\t\t\tvec4 clipPosition = vec4( ( vec3( uv, depth ) - 0.5 ) * 2.0, 1.0 );//ndc\n\t\t\tclipPosition *= clipW; //clip\n\t\t\treturn ( virtualCameraProjectionMatrixInverse * clipPosition ).xyz;//view\n\t\t}\n\t\tvoid main() {\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\t#ifdef useDepthTexture\n\t\t\t\tvec2 uv=(gl_FragCoord.xy-.5)/resolution.xy;\n\t\t\t\tuv.x=1.-uv.x;\n\t\t\t\tfloat depth = texture2DProj( tDepth, vUv ).r;\n\t\t\t\tfloat viewZ = getViewZ( depth );\n\t\t\t\tfloat clipW = virtualCameraProjectionMatrix[2][3] * viewZ+virtualCameraProjectionMatrix[3][3];\n\t\t\t\tvec3 viewPosition=getViewPosition( uv, depth, clipW );\n\t\t\t\tvec3 worldPosition=(virtualCameraMatrixWorld*vec4(viewPosition,1)).xyz;\n\t\t\t\tif(worldPosition.y>maxDistance) discard;\n\t\t\t\tfloat op=opacity;\n\t\t\t\t#ifdef DISTANCE_ATTENUATION\n\t\t\t\t\tfloat ratio=1.-(worldPosition.y/maxDistance);\n\t\t\t\t\tfloat attenuation=ratio*ratio;\n\t\t\t\t\top=opacity*attenuation;\n\t\t\t\t#endif\n\t\t\t\t#ifdef FRESNEL\n\t\t\t\t\top*=fresnelCoe;\n\t\t\t\t#endif\n\t\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), op );\n\t\t\t#else\n\t\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\t\t\t#endif\n\t\t}\n\t`)\n  });\n  return ReflectorForSSRPass2;\n})();\nexport { ReflectorForSSRPass };", "map": {"version": 3, "names": ["ReflectorForSSRPass", "_ReflectorForSSRPass", "<PERSON><PERSON>", "constructor", "geometry", "options", "isReflectorForSSRPass", "type", "scope", "color", "Color", "textureWidth", "textureHeight", "clipBias", "shader", "ReflectorShader", "useDepthTexture", "yAxis", "Vector3", "vecTemp0", "vecTemp1", "needsUpdate", "maxDistance", "uniforms", "value", "opacity", "resolution", "Vector2", "window", "innerWidth", "innerHeight", "_distanceAttenuation", "defines", "DISTANCE_ATTENUATION", "Object", "defineProperty", "get", "set", "val", "material", "_fresnel", "FRESNEL", "normal", "reflectorWorldPosition", "cameraWorldPosition", "rotationMatrix", "Matrix4", "lookAtPosition", "view", "target", "textureMatrix", "virtualCamera", "PerspectiveCamera", "depthTexture", "DepthTexture", "UnsignedShortType", "minFilter", "NearestFilter", "magFilter", "parameters", "HalfFloatType", "renderTarget", "WebGLRenderTarget", "ShaderMaterial", "transparent", "assign", "UniformsUtils", "clone", "fragmentShader", "vertexShader", "texture", "globalPlane", "Plane", "globalPlanes", "doR<PERSON>", "renderer", "scene", "camera", "copy", "position", "normalize", "reflect", "dot", "setFromMatrixPosition", "matrixWorld", "extractRotation", "applyMatrix4", "subVectors", "negate", "add", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "near", "projectionMatrixInverse", "multiply", "matrixWorldInverse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "currentClippingPlanes", "clippingPlanes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "viewport", "ReflectorForSSRPass2", "__publicField", "tDiffuse", "tD<PERSON>h", "fresnelCoe", "virtualCameraNear", "virtualCameraFar", "virtualCameraProjectionMatrix", "virtualCameraMatrixWorld", "virtualCameraProjectionMatrixInverse"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/objects/ReflectorForSSRPass.js"], "sourcesContent": ["import {\n  Color,\n  Matrix4,\n  Mesh,\n  PerspectiveC<PERSON>ra,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector2,\n  Vector3,\n  WebGLRenderTarget,\n  DepthTexture,\n  UnsignedShortType,\n  NearestFilter,\n  Plane,\n  HalfFloatType,\n} from 'three'\n\nconst ReflectorForSSRPass = /* @__PURE__ */ (() => {\n  class ReflectorForSSRPass extends Mesh {\n    static ReflectorShader = {\n      defines: {\n        DISTANCE_ATTENUATION: true,\n        FRESNEL: true,\n      },\n\n      uniforms: {\n        color: { value: null },\n        tDiffuse: { value: null },\n        tDepth: { value: null },\n        textureMatrix: { value: new Matrix4() },\n        maxDistance: { value: 180 },\n        opacity: { value: 0.5 },\n        fresnelCoe: { value: null },\n        virtualCameraNear: { value: null },\n        virtualCameraFar: { value: null },\n        virtualCameraProjectionMatrix: { value: new Matrix4() },\n        virtualCameraMatrixWorld: { value: new Matrix4() },\n        virtualCameraProjectionMatrixInverse: { value: new Matrix4() },\n        resolution: { value: new Vector2() },\n      },\n\n      vertexShader: /* glsl */ `\n\t\tuniform mat4 textureMatrix;\n\t\tvarying vec4 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\t\tuniform sampler2D tDepth;\n\t\tuniform float maxDistance;\n\t\tuniform float opacity;\n\t\tuniform float fresnelCoe;\n\t\tuniform float virtualCameraNear;\n\t\tuniform float virtualCameraFar;\n\t\tuniform mat4 virtualCameraProjectionMatrix;\n\t\tuniform mat4 virtualCameraProjectionMatrixInverse;\n\t\tuniform mat4 virtualCameraMatrixWorld;\n\t\tuniform vec2 resolution;\n\t\tvarying vec4 vUv;\n\t\t#include <packing>\n\t\tfloat blendOverlay( float base, float blend ) {\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\t\t}\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\t\t}\n\t\tfloat getDepth( const in vec2 uv ) {\n\t\t\treturn texture2D( tDepth, uv ).x;\n\t\t}\n\t\tfloat getViewZ( const in float depth ) {\n\t\t\treturn perspectiveDepthToViewZ( depth, virtualCameraNear, virtualCameraFar );\n\t\t}\n\t\tvec3 getViewPosition( const in vec2 uv, const in float depth/*clip space*/, const in float clipW ) {\n\t\t\tvec4 clipPosition = vec4( ( vec3( uv, depth ) - 0.5 ) * 2.0, 1.0 );//ndc\n\t\t\tclipPosition *= clipW; //clip\n\t\t\treturn ( virtualCameraProjectionMatrixInverse * clipPosition ).xyz;//view\n\t\t}\n\t\tvoid main() {\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\t#ifdef useDepthTexture\n\t\t\t\tvec2 uv=(gl_FragCoord.xy-.5)/resolution.xy;\n\t\t\t\tuv.x=1.-uv.x;\n\t\t\t\tfloat depth = texture2DProj( tDepth, vUv ).r;\n\t\t\t\tfloat viewZ = getViewZ( depth );\n\t\t\t\tfloat clipW = virtualCameraProjectionMatrix[2][3] * viewZ+virtualCameraProjectionMatrix[3][3];\n\t\t\t\tvec3 viewPosition=getViewPosition( uv, depth, clipW );\n\t\t\t\tvec3 worldPosition=(virtualCameraMatrixWorld*vec4(viewPosition,1)).xyz;\n\t\t\t\tif(worldPosition.y>maxDistance) discard;\n\t\t\t\tfloat op=opacity;\n\t\t\t\t#ifdef DISTANCE_ATTENUATION\n\t\t\t\t\tfloat ratio=1.-(worldPosition.y/maxDistance);\n\t\t\t\t\tfloat attenuation=ratio*ratio;\n\t\t\t\t\top=opacity*attenuation;\n\t\t\t\t#endif\n\t\t\t\t#ifdef FRESNEL\n\t\t\t\t\top*=fresnelCoe;\n\t\t\t\t#endif\n\t\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), op );\n\t\t\t#else\n\t\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\t\t\t#endif\n\t\t}\n\t`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isReflectorForSSRPass = true\n\n      this.type = 'ReflectorForSSRPass'\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0x7f7f7f)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const shader = options.shader || ReflectorForSSRPass.ReflectorShader\n      const useDepthTexture = options.useDepthTexture === true\n      const yAxis = new Vector3(0, 1, 0)\n      const vecTemp0 = new Vector3()\n      const vecTemp1 = new Vector3()\n\n      //\n\n      scope.needsUpdate = false\n      scope.maxDistance = ReflectorForSSRPass.ReflectorShader.uniforms.maxDistance.value\n      scope.opacity = ReflectorForSSRPass.ReflectorShader.uniforms.opacity.value\n      scope.color = color\n      scope.resolution = options.resolution || new Vector2(window.innerWidth, window.innerHeight)\n\n      scope._distanceAttenuation = ReflectorForSSRPass.ReflectorShader.defines.DISTANCE_ATTENUATION\n      Object.defineProperty(scope, 'distanceAttenuation', {\n        get() {\n          return scope._distanceAttenuation\n        },\n        set(val) {\n          if (scope._distanceAttenuation === val) return\n          scope._distanceAttenuation = val\n          scope.material.defines.DISTANCE_ATTENUATION = val\n          scope.material.needsUpdate = true\n        },\n      })\n\n      scope._fresnel = ReflectorForSSRPass.ReflectorShader.defines.FRESNEL\n      Object.defineProperty(scope, 'fresnel', {\n        get() {\n          return scope._fresnel\n        },\n        set(val) {\n          if (scope._fresnel === val) return\n          scope._fresnel = val\n          scope.material.defines.FRESNEL = val\n          scope.material.needsUpdate = true\n        },\n      })\n\n      const normal = new Vector3()\n      const reflectorWorldPosition = new Vector3()\n      const cameraWorldPosition = new Vector3()\n      const rotationMatrix = new Matrix4()\n      const lookAtPosition = new Vector3(0, 0, -1)\n\n      const view = new Vector3()\n      const target = new Vector3()\n\n      const textureMatrix = new Matrix4()\n      const virtualCamera = new PerspectiveCamera()\n\n      let depthTexture\n\n      if (useDepthTexture) {\n        depthTexture = new DepthTexture()\n        depthTexture.type = UnsignedShortType\n        depthTexture.minFilter = NearestFilter\n        depthTexture.magFilter = NearestFilter\n      }\n\n      const parameters = {\n        depthTexture: useDepthTexture ? depthTexture : null,\n        type: HalfFloatType,\n      }\n\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, parameters)\n\n      const material = new ShaderMaterial({\n        transparent: useDepthTexture,\n        defines: Object.assign({}, ReflectorForSSRPass.ReflectorShader.defines, {\n          useDepthTexture,\n        }),\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        fragmentShader: shader.fragmentShader,\n        vertexShader: shader.vertexShader,\n      })\n\n      material.uniforms['tDiffuse'].value = renderTarget.texture\n      material.uniforms['color'].value = scope.color\n      material.uniforms['textureMatrix'].value = textureMatrix\n      if (useDepthTexture) {\n        material.uniforms['tDepth'].value = renderTarget.depthTexture\n      }\n\n      this.material = material\n\n      const globalPlane = new Plane(new Vector3(0, 1, 0), clipBias)\n      const globalPlanes = [globalPlane]\n\n      this.doRender = function (renderer, scene, camera) {\n        material.uniforms['maxDistance'].value = scope.maxDistance\n        material.uniforms['color'].value = scope.color\n        material.uniforms['opacity'].value = scope.opacity\n\n        vecTemp0.copy(camera.position).normalize()\n        vecTemp1.copy(vecTemp0).reflect(yAxis)\n        material.uniforms['fresnelCoe'].value = (vecTemp0.dot(vecTemp1) + 1) / 2 // TODO: Also need to use glsl viewPosition and viewNormal per pixel.\n\n        reflectorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n        cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n        rotationMatrix.extractRotation(scope.matrixWorld)\n\n        normal.set(0, 0, 1)\n        normal.applyMatrix4(rotationMatrix)\n\n        view.subVectors(reflectorWorldPosition, cameraWorldPosition)\n\n        // Avoid rendering when reflector is facing away\n\n        if (view.dot(normal) > 0) return\n\n        view.reflect(normal).negate()\n        view.add(reflectorWorldPosition)\n\n        rotationMatrix.extractRotation(camera.matrixWorld)\n\n        lookAtPosition.set(0, 0, -1)\n        lookAtPosition.applyMatrix4(rotationMatrix)\n        lookAtPosition.add(cameraWorldPosition)\n\n        target.subVectors(reflectorWorldPosition, lookAtPosition)\n        target.reflect(normal).negate()\n        target.add(reflectorWorldPosition)\n\n        virtualCamera.position.copy(view)\n        virtualCamera.up.set(0, 1, 0)\n        virtualCamera.up.applyMatrix4(rotationMatrix)\n        virtualCamera.up.reflect(normal)\n        virtualCamera.lookAt(target)\n\n        virtualCamera.far = camera.far // Used in WebGLBackground\n\n        virtualCamera.updateMatrixWorld()\n        virtualCamera.projectionMatrix.copy(camera.projectionMatrix)\n\n        material.uniforms['virtualCameraNear'].value = camera.near\n        material.uniforms['virtualCameraFar'].value = camera.far\n        material.uniforms['virtualCameraMatrixWorld'].value = virtualCamera.matrixWorld\n        material.uniforms['virtualCameraProjectionMatrix'].value = camera.projectionMatrix\n        material.uniforms['virtualCameraProjectionMatrixInverse'].value = camera.projectionMatrixInverse\n        material.uniforms['resolution'].value = scope.resolution\n\n        // Update the texture matrix\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n        textureMatrix.multiply(virtualCamera.projectionMatrix)\n        textureMatrix.multiply(virtualCamera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n\n        // scope.visible = false;\n\n        const currentRenderTarget = renderer.getRenderTarget()\n\n        const currentXrEnabled = renderer.xr.enabled\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n        const currentClippingPlanes = renderer.clippingPlanes\n\n        renderer.xr.enabled = false // Avoid camera modification\n        renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n        renderer.clippingPlanes = globalPlanes\n\n        renderer.setRenderTarget(renderTarget)\n\n        renderer.state.buffers.depth.setMask(true) // make sure the depth buffer is writable so it can be properly cleared, see #18897\n\n        if (renderer.autoClear === false) renderer.clear()\n        renderer.render(scene, virtualCamera)\n\n        renderer.xr.enabled = currentXrEnabled\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n        renderer.clippingPlanes = currentClippingPlanes\n\n        renderer.setRenderTarget(currentRenderTarget)\n\n        // Restore viewport\n\n        const viewport = camera.viewport\n\n        if (viewport !== undefined) {\n          renderer.state.viewport(viewport)\n        }\n\n        // scope.visible = true;\n      }\n\n      this.getRenderTarget = function () {\n        return renderTarget\n      }\n    }\n  }\n\n  return ReflectorForSSRPass\n})()\n\nexport { ReflectorForSSRPass }\n"], "mappings": ";;;;;;;;;;;;AAiBK,MAACA,mBAAA,GAAuC,sBAAM;EACjD,MAAMC,oBAAA,GAAN,cAAkCC,IAAA,CAAK;IA+FrCC,YAAYC,QAAA,EAAUC,OAAA,GAAU,IAAI;MAClC,MAAMD,QAAQ;MAEd,KAAKE,qBAAA,GAAwB;MAE7B,KAAKC,IAAA,GAAO;MAEZ,MAAMC,KAAA,GAAQ;MAEd,MAAMC,KAAA,GAAQJ,OAAA,CAAQI,KAAA,KAAU,SAAY,IAAIC,KAAA,CAAML,OAAA,CAAQI,KAAK,IAAI,IAAIC,KAAA,CAAM,OAAQ;MACzF,MAAMC,YAAA,GAAeN,OAAA,CAAQM,YAAA,IAAgB;MAC7C,MAAMC,aAAA,GAAgBP,OAAA,CAAQO,aAAA,IAAiB;MAC/C,MAAMC,QAAA,GAAWR,OAAA,CAAQQ,QAAA,IAAY;MACrC,MAAMC,MAAA,GAAST,OAAA,CAAQS,MAAA,IAAUb,oBAAA,CAAoBc,eAAA;MACrD,MAAMC,eAAA,GAAkBX,OAAA,CAAQW,eAAA,KAAoB;MACpD,MAAMC,KAAA,GAAQ,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC;MACjC,MAAMC,QAAA,GAAW,IAAID,OAAA,CAAS;MAC9B,MAAME,QAAA,GAAW,IAAIF,OAAA,CAAS;MAI9BV,KAAA,CAAMa,WAAA,GAAc;MACpBb,KAAA,CAAMc,WAAA,GAAcrB,oBAAA,CAAoBc,eAAA,CAAgBQ,QAAA,CAASD,WAAA,CAAYE,KAAA;MAC7EhB,KAAA,CAAMiB,OAAA,GAAUxB,oBAAA,CAAoBc,eAAA,CAAgBQ,QAAA,CAASE,OAAA,CAAQD,KAAA;MACrEhB,KAAA,CAAMC,KAAA,GAAQA,KAAA;MACdD,KAAA,CAAMkB,UAAA,GAAarB,OAAA,CAAQqB,UAAA,IAAc,IAAIC,OAAA,CAAQC,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW;MAE1FtB,KAAA,CAAMuB,oBAAA,GAAuB9B,oBAAA,CAAoBc,eAAA,CAAgBiB,OAAA,CAAQC,oBAAA;MACzEC,MAAA,CAAOC,cAAA,CAAe3B,KAAA,EAAO,uBAAuB;QAClD4B,IAAA,EAAM;UACJ,OAAO5B,KAAA,CAAMuB,oBAAA;QACd;QACDM,IAAIC,GAAA,EAAK;UACP,IAAI9B,KAAA,CAAMuB,oBAAA,KAAyBO,GAAA,EAAK;UACxC9B,KAAA,CAAMuB,oBAAA,GAAuBO,GAAA;UAC7B9B,KAAA,CAAM+B,QAAA,CAASP,OAAA,CAAQC,oBAAA,GAAuBK,GAAA;UAC9C9B,KAAA,CAAM+B,QAAA,CAASlB,WAAA,GAAc;QAC9B;MACT,CAAO;MAEDb,KAAA,CAAMgC,QAAA,GAAWvC,oBAAA,CAAoBc,eAAA,CAAgBiB,OAAA,CAAQS,OAAA;MAC7DP,MAAA,CAAOC,cAAA,CAAe3B,KAAA,EAAO,WAAW;QACtC4B,IAAA,EAAM;UACJ,OAAO5B,KAAA,CAAMgC,QAAA;QACd;QACDH,IAAIC,GAAA,EAAK;UACP,IAAI9B,KAAA,CAAMgC,QAAA,KAAaF,GAAA,EAAK;UAC5B9B,KAAA,CAAMgC,QAAA,GAAWF,GAAA;UACjB9B,KAAA,CAAM+B,QAAA,CAASP,OAAA,CAAQS,OAAA,GAAUH,GAAA;UACjC9B,KAAA,CAAM+B,QAAA,CAASlB,WAAA,GAAc;QAC9B;MACT,CAAO;MAED,MAAMqB,MAAA,GAAS,IAAIxB,OAAA,CAAS;MAC5B,MAAMyB,sBAAA,GAAyB,IAAIzB,OAAA,CAAS;MAC5C,MAAM0B,mBAAA,GAAsB,IAAI1B,OAAA,CAAS;MACzC,MAAM2B,cAAA,GAAiB,IAAIC,OAAA,CAAS;MACpC,MAAMC,cAAA,GAAiB,IAAI7B,OAAA,CAAQ,GAAG,GAAG,EAAE;MAE3C,MAAM8B,IAAA,GAAO,IAAI9B,OAAA,CAAS;MAC1B,MAAM+B,MAAA,GAAS,IAAI/B,OAAA,CAAS;MAE5B,MAAMgC,aAAA,GAAgB,IAAIJ,OAAA,CAAS;MACnC,MAAMK,aAAA,GAAgB,IAAIC,iBAAA,CAAmB;MAE7C,IAAIC,YAAA;MAEJ,IAAIrC,eAAA,EAAiB;QACnBqC,YAAA,GAAe,IAAIC,YAAA,CAAc;QACjCD,YAAA,CAAa9C,IAAA,GAAOgD,iBAAA;QACpBF,YAAA,CAAaG,SAAA,GAAYC,aAAA;QACzBJ,YAAA,CAAaK,SAAA,GAAYD,aAAA;MAC1B;MAED,MAAME,UAAA,GAAa;QACjBN,YAAA,EAAcrC,eAAA,GAAkBqC,YAAA,GAAe;QAC/C9C,IAAA,EAAMqD;MACP;MAED,MAAMC,YAAA,GAAe,IAAIC,iBAAA,CAAkBnD,YAAA,EAAcC,aAAA,EAAe+C,UAAU;MAElF,MAAMpB,QAAA,GAAW,IAAIwB,cAAA,CAAe;QAClCC,WAAA,EAAahD,eAAA;QACbgB,OAAA,EAASE,MAAA,CAAO+B,MAAA,CAAO,CAAE,GAAEhE,oBAAA,CAAoBc,eAAA,CAAgBiB,OAAA,EAAS;UACtEhB;QACV,CAAS;QACDO,QAAA,EAAU2C,aAAA,CAAcC,KAAA,CAAMrD,MAAA,CAAOS,QAAQ;QAC7C6C,cAAA,EAAgBtD,MAAA,CAAOsD,cAAA;QACvBC,YAAA,EAAcvD,MAAA,CAAOuD;MAC7B,CAAO;MAED9B,QAAA,CAAShB,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQqC,YAAA,CAAaS,OAAA;MACnD/B,QAAA,CAAShB,QAAA,CAAS,OAAO,EAAEC,KAAA,GAAQhB,KAAA,CAAMC,KAAA;MACzC8B,QAAA,CAAShB,QAAA,CAAS,eAAe,EAAEC,KAAA,GAAQ0B,aAAA;MAC3C,IAAIlC,eAAA,EAAiB;QACnBuB,QAAA,CAAShB,QAAA,CAAS,QAAQ,EAAEC,KAAA,GAAQqC,YAAA,CAAaR,YAAA;MAClD;MAED,KAAKd,QAAA,GAAWA,QAAA;MAEhB,MAAMgC,WAAA,GAAc,IAAIC,KAAA,CAAM,IAAItD,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAGL,QAAQ;MAC5D,MAAM4D,YAAA,GAAe,CAACF,WAAW;MAEjC,KAAKG,QAAA,GAAW,UAAUC,QAAA,EAAUC,KAAA,EAAOC,MAAA,EAAQ;QACjDtC,QAAA,CAAShB,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQhB,KAAA,CAAMc,WAAA;QAC/CiB,QAAA,CAAShB,QAAA,CAAS,OAAO,EAAEC,KAAA,GAAQhB,KAAA,CAAMC,KAAA;QACzC8B,QAAA,CAAShB,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQhB,KAAA,CAAMiB,OAAA;QAE3CN,QAAA,CAAS2D,IAAA,CAAKD,MAAA,CAAOE,QAAQ,EAAEC,SAAA,CAAW;QAC1C5D,QAAA,CAAS0D,IAAA,CAAK3D,QAAQ,EAAE8D,OAAA,CAAQhE,KAAK;QACrCsB,QAAA,CAAShB,QAAA,CAAS,YAAY,EAAEC,KAAA,IAASL,QAAA,CAAS+D,GAAA,CAAI9D,QAAQ,IAAI,KAAK;QAEvEuB,sBAAA,CAAuBwC,qBAAA,CAAsB3E,KAAA,CAAM4E,WAAW;QAC9DxC,mBAAA,CAAoBuC,qBAAA,CAAsBN,MAAA,CAAOO,WAAW;QAE5DvC,cAAA,CAAewC,eAAA,CAAgB7E,KAAA,CAAM4E,WAAW;QAEhD1C,MAAA,CAAOL,GAAA,CAAI,GAAG,GAAG,CAAC;QAClBK,MAAA,CAAO4C,YAAA,CAAazC,cAAc;QAElCG,IAAA,CAAKuC,UAAA,CAAW5C,sBAAA,EAAwBC,mBAAmB;QAI3D,IAAII,IAAA,CAAKkC,GAAA,CAAIxC,MAAM,IAAI,GAAG;QAE1BM,IAAA,CAAKiC,OAAA,CAAQvC,MAAM,EAAE8C,MAAA,CAAQ;QAC7BxC,IAAA,CAAKyC,GAAA,CAAI9C,sBAAsB;QAE/BE,cAAA,CAAewC,eAAA,CAAgBR,MAAA,CAAOO,WAAW;QAEjDrC,cAAA,CAAeV,GAAA,CAAI,GAAG,GAAG,EAAE;QAC3BU,cAAA,CAAeuC,YAAA,CAAazC,cAAc;QAC1CE,cAAA,CAAe0C,GAAA,CAAI7C,mBAAmB;QAEtCK,MAAA,CAAOsC,UAAA,CAAW5C,sBAAA,EAAwBI,cAAc;QACxDE,MAAA,CAAOgC,OAAA,CAAQvC,MAAM,EAAE8C,MAAA,CAAQ;QAC/BvC,MAAA,CAAOwC,GAAA,CAAI9C,sBAAsB;QAEjCQ,aAAA,CAAc4B,QAAA,CAASD,IAAA,CAAK9B,IAAI;QAChCG,aAAA,CAAcuC,EAAA,CAAGrD,GAAA,CAAI,GAAG,GAAG,CAAC;QAC5Bc,aAAA,CAAcuC,EAAA,CAAGJ,YAAA,CAAazC,cAAc;QAC5CM,aAAA,CAAcuC,EAAA,CAAGT,OAAA,CAAQvC,MAAM;QAC/BS,aAAA,CAAcwC,MAAA,CAAO1C,MAAM;QAE3BE,aAAA,CAAcyC,GAAA,GAAMf,MAAA,CAAOe,GAAA;QAE3BzC,aAAA,CAAc0C,iBAAA,CAAmB;QACjC1C,aAAA,CAAc2C,gBAAA,CAAiBhB,IAAA,CAAKD,MAAA,CAAOiB,gBAAgB;QAE3DvD,QAAA,CAAShB,QAAA,CAAS,mBAAmB,EAAEC,KAAA,GAAQqD,MAAA,CAAOkB,IAAA;QACtDxD,QAAA,CAAShB,QAAA,CAAS,kBAAkB,EAAEC,KAAA,GAAQqD,MAAA,CAAOe,GAAA;QACrDrD,QAAA,CAAShB,QAAA,CAAS,0BAA0B,EAAEC,KAAA,GAAQ2B,aAAA,CAAciC,WAAA;QACpE7C,QAAA,CAAShB,QAAA,CAAS,+BAA+B,EAAEC,KAAA,GAAQqD,MAAA,CAAOiB,gBAAA;QAClEvD,QAAA,CAAShB,QAAA,CAAS,sCAAsC,EAAEC,KAAA,GAAQqD,MAAA,CAAOmB,uBAAA;QACzEzD,QAAA,CAAShB,QAAA,CAAS,YAAY,EAAEC,KAAA,GAAQhB,KAAA,CAAMkB,UAAA;QAG9CwB,aAAA,CAAcb,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;QAChGa,aAAA,CAAc+C,QAAA,CAAS9C,aAAA,CAAc2C,gBAAgB;QACrD5C,aAAA,CAAc+C,QAAA,CAAS9C,aAAA,CAAc+C,kBAAkB;QACvDhD,aAAA,CAAc+C,QAAA,CAASzF,KAAA,CAAM4E,WAAW;QAIxC,MAAMe,mBAAA,GAAsBxB,QAAA,CAASyB,eAAA,CAAiB;QAEtD,MAAMC,gBAAA,GAAmB1B,QAAA,CAAS2B,EAAA,CAAGC,OAAA;QACrC,MAAMC,uBAAA,GAA0B7B,QAAA,CAAS8B,SAAA,CAAUC,UAAA;QACnD,MAAMC,qBAAA,GAAwBhC,QAAA,CAASiC,cAAA;QAEvCjC,QAAA,CAAS2B,EAAA,CAAGC,OAAA,GAAU;QACtB5B,QAAA,CAAS8B,SAAA,CAAUC,UAAA,GAAa;QAChC/B,QAAA,CAASiC,cAAA,GAAiBnC,YAAA;QAE1BE,QAAA,CAASkC,eAAA,CAAgBhD,YAAY;QAErCc,QAAA,CAASmC,KAAA,CAAMC,OAAA,CAAQC,KAAA,CAAMC,OAAA,CAAQ,IAAI;QAEzC,IAAItC,QAAA,CAASuC,SAAA,KAAc,OAAOvC,QAAA,CAASwC,KAAA,CAAO;QAClDxC,QAAA,CAASyC,MAAA,CAAOxC,KAAA,EAAOzB,aAAa;QAEpCwB,QAAA,CAAS2B,EAAA,CAAGC,OAAA,GAAUF,gBAAA;QACtB1B,QAAA,CAAS8B,SAAA,CAAUC,UAAA,GAAaF,uBAAA;QAChC7B,QAAA,CAASiC,cAAA,GAAiBD,qBAAA;QAE1BhC,QAAA,CAASkC,eAAA,CAAgBV,mBAAmB;QAI5C,MAAMkB,QAAA,GAAWxC,MAAA,CAAOwC,QAAA;QAExB,IAAIA,QAAA,KAAa,QAAW;UAC1B1C,QAAA,CAASmC,KAAA,CAAMO,QAAA,CAASA,QAAQ;QACjC;MAGF;MAED,KAAKjB,eAAA,GAAkB,YAAY;QACjC,OAAOvC,YAAA;MACR;IACF;EACF;EA1SD,IAAMyD,oBAAA,GAANrH,oBAAA;EACEsH,aAAA,CADID,oBAAA,EACG,mBAAkB;IACvBtF,OAAA,EAAS;MACPC,oBAAA,EAAsB;MACtBQ,OAAA,EAAS;IACV;IAEDlB,QAAA,EAAU;MACRd,KAAA,EAAO;QAAEe,KAAA,EAAO;MAAM;MACtBgG,QAAA,EAAU;QAAEhG,KAAA,EAAO;MAAM;MACzBiG,MAAA,EAAQ;QAAEjG,KAAA,EAAO;MAAM;MACvB0B,aAAA,EAAe;QAAE1B,KAAA,EAAO,IAAIsB,OAAA;MAAW;MACvCxB,WAAA,EAAa;QAAEE,KAAA,EAAO;MAAK;MAC3BC,OAAA,EAAS;QAAED,KAAA,EAAO;MAAK;MACvBkG,UAAA,EAAY;QAAElG,KAAA,EAAO;MAAM;MAC3BmG,iBAAA,EAAmB;QAAEnG,KAAA,EAAO;MAAM;MAClCoG,gBAAA,EAAkB;QAAEpG,KAAA,EAAO;MAAM;MACjCqG,6BAAA,EAA+B;QAAErG,KAAA,EAAO,IAAIsB,OAAA;MAAW;MACvDgF,wBAAA,EAA0B;QAAEtG,KAAA,EAAO,IAAIsB,OAAA;MAAW;MAClDiF,oCAAA,EAAsC;QAAEvG,KAAA,EAAO,IAAIsB,OAAA;MAAW;MAC9DpB,UAAA,EAAY;QAAEF,KAAA,EAAO,IAAIG,OAAA;MAAW;IACrC;IAED0C,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYzBD,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA0D5B;EA+MH,OAAOkD,oBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}