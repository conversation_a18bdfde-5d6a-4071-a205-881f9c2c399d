{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport * as THREE from 'three';\nlet GradientType = /*#__PURE__*/function (GradientType) {\n  GradientType[\"Linear\"] = \"linear\";\n  GradientType[\"Radial\"] = \"radial\";\n  return GradientType;\n}({});\nfunction GradientTexture({\n  stops,\n  colors,\n  size = 1024,\n  width = 16,\n  type = GradientType.Linear,\n  innerCircleRadius = 0,\n  outerCircleRadius = 'auto',\n  ...props\n}) {\n  const gl = useThree(state => state.gl);\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    canvas.width = width;\n    canvas.height = size;\n    let gradient;\n    if (type === GradientType.Linear) {\n      gradient = context.createLinearGradient(0, 0, 0, size);\n    } else {\n      const canvasCenterX = canvas.width / 2;\n      const canvasCenterY = canvas.height / 2;\n      const radius = outerCircleRadius !== 'auto' ? Math.abs(Number(outerCircleRadius)) : Math.sqrt(canvasCenterX ** 2 + canvasCenterY ** 2);\n      gradient = context.createRadialGradient(canvasCenterX, canvasCenterY, Math.abs(innerCircleRadius), canvasCenterX, canvasCenterY, radius);\n    }\n    const tempColor = new THREE.Color(); // reuse instance for performance\n    let i = stops.length;\n    while (i--) {\n      gradient.addColorStop(stops[i], tempColor.set(colors[i]).getStyle());\n    }\n    context.save();\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, width, size);\n    context.restore();\n    return canvas;\n  }, [stops]);\n  return /*#__PURE__*/React.createElement(\"canvasTexture\", _extends({\n    colorSpace: gl.outputColorSpace,\n    args: [canvas],\n    attach: \"map\"\n  }, props));\n}\nexport { GradientTexture, GradientType };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "THREE", "GradientType", "GradientTexture", "stops", "colors", "size", "width", "type", "Linear", "innerCircleRadius", "outerCircleRadius", "props", "gl", "state", "canvas", "useMemo", "document", "createElement", "context", "getContext", "height", "gradient", "createLinearGradient", "canvasCenterX", "canvasCenterY", "radius", "Math", "abs", "Number", "sqrt", "createRadialGradient", "tempColor", "Color", "i", "length", "addColorStop", "set", "getStyle", "save", "fillStyle", "fillRect", "restore", "colorSpace", "outputColorSpace", "args", "attach"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/GradientTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nlet GradientType = /*#__PURE__*/function (GradientType) {\n  GradientType[\"Linear\"] = \"linear\";\n  GradientType[\"Radial\"] = \"radial\";\n  return GradientType;\n}({});\nfunction GradientTexture({\n  stops,\n  colors,\n  size = 1024,\n  width = 16,\n  type = GradientType.Linear,\n  innerCircleRadius = 0,\n  outerCircleRadius = 'auto',\n  ...props\n}) {\n  const gl = useThree(state => state.gl);\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    canvas.width = width;\n    canvas.height = size;\n    let gradient;\n    if (type === GradientType.Linear) {\n      gradient = context.createLinearGradient(0, 0, 0, size);\n    } else {\n      const canvasCenterX = canvas.width / 2;\n      const canvasCenterY = canvas.height / 2;\n      const radius = outerCircleRadius !== 'auto' ? Math.abs(Number(outerCircleRadius)) : Math.sqrt(canvasCenterX ** 2 + canvasCenterY ** 2);\n      gradient = context.createRadialGradient(canvasCenterX, canvasCenterY, Math.abs(innerCircleRadius), canvasCenterX, canvasCenterY, radius);\n    }\n    const tempColor = new THREE.Color(); // reuse instance for performance\n    let i = stops.length;\n    while (i--) {\n      gradient.addColorStop(stops[i], tempColor.set(colors[i]).getStyle());\n    }\n    context.save();\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, width, size);\n    context.restore();\n    return canvas;\n  }, [stops]);\n  return /*#__PURE__*/React.createElement(\"canvasTexture\", _extends({\n    colorSpace: gl.outputColorSpace,\n    args: [canvas],\n    attach: \"map\"\n  }, props));\n}\n\nexport { GradientTexture, GradientType };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,IAAIC,YAAY,GAAG,aAAa,UAAUA,YAAY,EAAE;EACtDA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjCA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjC,OAAOA,YAAY;AACrB,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,SAASC,eAAeA,CAAC;EACvBC,KAAK;EACLC,MAAM;EACNC,IAAI,GAAG,IAAI;EACXC,KAAK,GAAG,EAAE;EACVC,IAAI,GAAGN,YAAY,CAACO,MAAM;EAC1BC,iBAAiB,GAAG,CAAC;EACrBC,iBAAiB,GAAG,MAAM;EAC1B,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGb,QAAQ,CAACc,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,MAAM,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,OAAO,GAAGJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAAC;IACvCL,MAAM,CAACR,KAAK,GAAGA,KAAK;IACpBQ,MAAM,CAACM,MAAM,GAAGf,IAAI;IACpB,IAAIgB,QAAQ;IACZ,IAAId,IAAI,KAAKN,YAAY,CAACO,MAAM,EAAE;MAChCa,QAAQ,GAAGH,OAAO,CAACI,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEjB,IAAI,CAAC;IACxD,CAAC,MAAM;MACL,MAAMkB,aAAa,GAAGT,MAAM,CAACR,KAAK,GAAG,CAAC;MACtC,MAAMkB,aAAa,GAAGV,MAAM,CAACM,MAAM,GAAG,CAAC;MACvC,MAAMK,MAAM,GAAGf,iBAAiB,KAAK,MAAM,GAAGgB,IAAI,CAACC,GAAG,CAACC,MAAM,CAAClB,iBAAiB,CAAC,CAAC,GAAGgB,IAAI,CAACG,IAAI,CAACN,aAAa,IAAI,CAAC,GAAGC,aAAa,IAAI,CAAC,CAAC;MACtIH,QAAQ,GAAGH,OAAO,CAACY,oBAAoB,CAACP,aAAa,EAAEC,aAAa,EAAEE,IAAI,CAACC,GAAG,CAAClB,iBAAiB,CAAC,EAAEc,aAAa,EAAEC,aAAa,EAAEC,MAAM,CAAC;IAC1I;IACA,MAAMM,SAAS,GAAG,IAAI/B,KAAK,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrC,IAAIC,CAAC,GAAG9B,KAAK,CAAC+B,MAAM;IACpB,OAAOD,CAAC,EAAE,EAAE;MACVZ,QAAQ,CAACc,YAAY,CAAChC,KAAK,CAAC8B,CAAC,CAAC,EAAEF,SAAS,CAACK,GAAG,CAAChC,MAAM,CAAC6B,CAAC,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC;IACtE;IACAnB,OAAO,CAACoB,IAAI,CAAC,CAAC;IACdpB,OAAO,CAACqB,SAAS,GAAGlB,QAAQ;IAC5BH,OAAO,CAACsB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAElC,KAAK,EAAED,IAAI,CAAC;IACnCa,OAAO,CAACuB,OAAO,CAAC,CAAC;IACjB,OAAO3B,MAAM;EACf,CAAC,EAAE,CAACX,KAAK,CAAC,CAAC;EACX,OAAO,aAAaL,KAAK,CAACmB,aAAa,CAAC,eAAe,EAAEpB,QAAQ,CAAC;IAChE6C,UAAU,EAAE9B,EAAE,CAAC+B,gBAAgB;IAC/BC,IAAI,EAAE,CAAC9B,MAAM,CAAC;IACd+B,MAAM,EAAE;EACV,CAAC,EAAElC,KAAK,CAAC,CAAC;AACZ;AAEA,SAAST,eAAe,EAAED,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}