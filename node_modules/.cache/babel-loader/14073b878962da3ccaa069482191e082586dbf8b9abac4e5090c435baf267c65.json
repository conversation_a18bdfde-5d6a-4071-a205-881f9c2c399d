{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { TransformControls as TransformControls$1 } from 'three-stdlib';\nconst TransformControls = /* @__PURE__ */React.forwardRef(({\n  children,\n  domElement,\n  onChange,\n  onMouseDown,\n  onMouseUp,\n  onObjectChange,\n  object,\n  makeDefault,\n  camera,\n  // Transform\n  enabled,\n  axis,\n  mode,\n  translationSnap,\n  rotationSnap,\n  scaleSnap,\n  space,\n  size,\n  showX,\n  showY,\n  showZ,\n  ...props\n}, ref) => {\n  const defaultControls = useThree(state => state.controls);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TransformControls$1(explCamera, explDomElement), [explCamera, explDomElement]);\n  const group = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (object) {\n      controls.attach(object instanceof THREE.Object3D ? object : object.current);\n    } else if (group.current instanceof THREE.Object3D) {\n      controls.attach(group.current);\n    }\n    return () => void controls.detach();\n  }, [object, children, controls]);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = event => defaultControls.enabled = !event.value;\n      controls.addEventListener('dragging-changed', callback);\n      return () => controls.removeEventListener('dragging-changed', callback);\n    }\n  }, [controls, defaultControls]);\n  const onChangeRef = React.useRef(undefined);\n  const onMouseDownRef = React.useRef(undefined);\n  const onMouseUpRef = React.useRef(undefined);\n  const onObjectChangeRef = React.useRef(undefined);\n  React.useLayoutEffect(() => void (onChangeRef.current = onChange), [onChange]);\n  React.useLayoutEffect(() => void (onMouseDownRef.current = onMouseDown), [onMouseDown]);\n  React.useLayoutEffect(() => void (onMouseUpRef.current = onMouseUp), [onMouseUp]);\n  React.useLayoutEffect(() => void (onObjectChangeRef.current = onObjectChange), [onObjectChange]);\n  React.useEffect(() => {\n    const onChange = e => {\n      invalidate();\n      onChangeRef.current == null || onChangeRef.current(e);\n    };\n    const onMouseDown = e => onMouseDownRef.current == null ? void 0 : onMouseDownRef.current(e);\n    const onMouseUp = e => onMouseUpRef.current == null ? void 0 : onMouseUpRef.current(e);\n    const onObjectChange = e => onObjectChangeRef.current == null ? void 0 : onObjectChangeRef.current(e);\n    controls.addEventListener('change', onChange);\n    controls.addEventListener('mouseDown', onMouseDown);\n    controls.addEventListener('mouseUp', onMouseUp);\n    controls.addEventListener('objectChange', onObjectChange);\n    return () => {\n      controls.removeEventListener('change', onChange);\n      controls.removeEventListener('mouseDown', onMouseDown);\n      controls.removeEventListener('mouseUp', onMouseUp);\n      controls.removeEventListener('objectChange', onObjectChange);\n    };\n  }, [invalidate, controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", {\n    ref: ref,\n    object: controls,\n    enabled: enabled,\n    axis: axis,\n    mode: mode,\n    translationSnap: translationSnap,\n    rotationSnap: rotationSnap,\n    scaleSnap: scaleSnap,\n    space: space,\n    size: size,\n    showX: showX,\n    showY: showY,\n    showZ: showZ\n  }), /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props), children));\n});\nexport { TransformControls };", "map": {"version": 3, "names": ["_extends", "useThree", "React", "THREE", "TransformControls", "TransformControls$1", "forwardRef", "children", "dom<PERSON>lement", "onChange", "onMouseDown", "onMouseUp", "onObjectChange", "object", "makeDefault", "camera", "enabled", "axis", "mode", "translationSnap", "rotationSnap", "scaleSnap", "space", "size", "showX", "showY", "showZ", "props", "ref", "defaultControls", "state", "controls", "gl", "events", "defaultCamera", "invalidate", "get", "set", "explCamera", "explDomElement", "connected", "useMemo", "group", "useRef", "useLayoutEffect", "attach", "Object3D", "current", "detach", "useEffect", "callback", "event", "value", "addEventListener", "removeEventListener", "onChangeRef", "undefined", "onMouseDownRef", "onMouseUpRef", "onObjectChangeRef", "e", "old", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/TransformControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { TransformControls as TransformControls$1 } from 'three-stdlib';\n\nconst TransformControls = /* @__PURE__ */React.forwardRef(({\n  children,\n  domElement,\n  onChange,\n  onMouseDown,\n  onMouseUp,\n  onObjectChange,\n  object,\n  makeDefault,\n  camera,\n  // Transform\n  enabled,\n  axis,\n  mode,\n  translationSnap,\n  rotationSnap,\n  scaleSnap,\n  space,\n  size,\n  showX,\n  showY,\n  showZ,\n  ...props\n}, ref) => {\n  const defaultControls = useThree(state => state.controls);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TransformControls$1(explCamera, explDomElement), [explCamera, explDomElement]);\n  const group = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (object) {\n      controls.attach(object instanceof THREE.Object3D ? object : object.current);\n    } else if (group.current instanceof THREE.Object3D) {\n      controls.attach(group.current);\n    }\n    return () => void controls.detach();\n  }, [object, children, controls]);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = event => defaultControls.enabled = !event.value;\n      controls.addEventListener('dragging-changed', callback);\n      return () => controls.removeEventListener('dragging-changed', callback);\n    }\n  }, [controls, defaultControls]);\n  const onChangeRef = React.useRef(undefined);\n  const onMouseDownRef = React.useRef(undefined);\n  const onMouseUpRef = React.useRef(undefined);\n  const onObjectChangeRef = React.useRef(undefined);\n  React.useLayoutEffect(() => void (onChangeRef.current = onChange), [onChange]);\n  React.useLayoutEffect(() => void (onMouseDownRef.current = onMouseDown), [onMouseDown]);\n  React.useLayoutEffect(() => void (onMouseUpRef.current = onMouseUp), [onMouseUp]);\n  React.useLayoutEffect(() => void (onObjectChangeRef.current = onObjectChange), [onObjectChange]);\n  React.useEffect(() => {\n    const onChange = e => {\n      invalidate();\n      onChangeRef.current == null || onChangeRef.current(e);\n    };\n    const onMouseDown = e => onMouseDownRef.current == null ? void 0 : onMouseDownRef.current(e);\n    const onMouseUp = e => onMouseUpRef.current == null ? void 0 : onMouseUpRef.current(e);\n    const onObjectChange = e => onObjectChangeRef.current == null ? void 0 : onObjectChangeRef.current(e);\n    controls.addEventListener('change', onChange);\n    controls.addEventListener('mouseDown', onMouseDown);\n    controls.addEventListener('mouseUp', onMouseUp);\n    controls.addEventListener('objectChange', onObjectChange);\n    return () => {\n      controls.removeEventListener('change', onChange);\n      controls.removeEventListener('mouseDown', onMouseDown);\n      controls.removeEventListener('mouseUp', onMouseUp);\n      controls.removeEventListener('objectChange', onObjectChange);\n    };\n  }, [invalidate, controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", {\n    ref: ref,\n    object: controls,\n    enabled: enabled,\n    axis: axis,\n    mode: mode,\n    translationSnap: translationSnap,\n    rotationSnap: rotationSnap,\n    scaleSnap: scaleSnap,\n    space: space,\n    size: size,\n    showX: showX,\n    showY: showY,\n    showZ: showZ\n  }), /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, props), children));\n});\n\nexport { TransformControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,IAAIC,mBAAmB,QAAQ,cAAc;AAEvE,MAAMD,iBAAiB,GAAG,eAAeF,KAAK,CAACI,UAAU,CAAC,CAAC;EACzDC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,SAAS;EACTC,cAAc;EACdC,MAAM;EACNC,WAAW;EACXC,MAAM;EACN;EACAC,OAAO;EACPC,IAAI;EACJC,IAAI;EACJC,eAAe;EACfC,YAAY;EACZC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,KAAK;EACLC,KAAK;EACLC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,eAAe,GAAG5B,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACzD,MAAMC,EAAE,GAAG/B,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGhC,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,aAAa,GAAGjC,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACf,MAAM,CAAC;EACrD,MAAMoB,UAAU,GAAGlC,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACK,UAAU,CAAC;EACtD,MAAMC,GAAG,GAAGnC,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGpC,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACO,GAAG,CAAC;EACxC,MAAMC,UAAU,GAAGvB,MAAM,IAAImB,aAAa;EAC1C,MAAMK,cAAc,GAAG/B,UAAU,IAAIyB,MAAM,CAACO,SAAS,IAAIR,EAAE,CAACxB,UAAU;EACtE,MAAMuB,QAAQ,GAAG7B,KAAK,CAACuC,OAAO,CAAC,MAAM,IAAIpC,mBAAmB,CAACiC,UAAU,EAAEC,cAAc,CAAC,EAAE,CAACD,UAAU,EAAEC,cAAc,CAAC,CAAC;EACvH,MAAMG,KAAK,GAAGxC,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EAChCzC,KAAK,CAAC0C,eAAe,CAAC,MAAM;IAC1B,IAAI/B,MAAM,EAAE;MACVkB,QAAQ,CAACc,MAAM,CAAChC,MAAM,YAAYV,KAAK,CAAC2C,QAAQ,GAAGjC,MAAM,GAAGA,MAAM,CAACkC,OAAO,CAAC;IAC7E,CAAC,MAAM,IAAIL,KAAK,CAACK,OAAO,YAAY5C,KAAK,CAAC2C,QAAQ,EAAE;MAClDf,QAAQ,CAACc,MAAM,CAACH,KAAK,CAACK,OAAO,CAAC;IAChC;IACA,OAAO,MAAM,KAAKhB,QAAQ,CAACiB,MAAM,CAAC,CAAC;EACrC,CAAC,EAAE,CAACnC,MAAM,EAAEN,QAAQ,EAAEwB,QAAQ,CAAC,CAAC;EAChC7B,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,IAAIpB,eAAe,EAAE;MACnB,MAAMqB,QAAQ,GAAGC,KAAK,IAAItB,eAAe,CAACb,OAAO,GAAG,CAACmC,KAAK,CAACC,KAAK;MAChErB,QAAQ,CAACsB,gBAAgB,CAAC,kBAAkB,EAAEH,QAAQ,CAAC;MACvD,OAAO,MAAMnB,QAAQ,CAACuB,mBAAmB,CAAC,kBAAkB,EAAEJ,QAAQ,CAAC;IACzE;EACF,CAAC,EAAE,CAACnB,QAAQ,EAAEF,eAAe,CAAC,CAAC;EAC/B,MAAM0B,WAAW,GAAGrD,KAAK,CAACyC,MAAM,CAACa,SAAS,CAAC;EAC3C,MAAMC,cAAc,GAAGvD,KAAK,CAACyC,MAAM,CAACa,SAAS,CAAC;EAC9C,MAAME,YAAY,GAAGxD,KAAK,CAACyC,MAAM,CAACa,SAAS,CAAC;EAC5C,MAAMG,iBAAiB,GAAGzD,KAAK,CAACyC,MAAM,CAACa,SAAS,CAAC;EACjDtD,KAAK,CAAC0C,eAAe,CAAC,MAAM,MAAMW,WAAW,CAACR,OAAO,GAAGtC,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC9EP,KAAK,CAAC0C,eAAe,CAAC,MAAM,MAAMa,cAAc,CAACV,OAAO,GAAGrC,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACvFR,KAAK,CAAC0C,eAAe,CAAC,MAAM,MAAMc,YAAY,CAACX,OAAO,GAAGpC,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACjFT,KAAK,CAAC0C,eAAe,CAAC,MAAM,MAAMe,iBAAiB,CAACZ,OAAO,GAAGnC,cAAc,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAChGV,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,MAAMxC,QAAQ,GAAGmD,CAAC,IAAI;MACpBzB,UAAU,CAAC,CAAC;MACZoB,WAAW,CAACR,OAAO,IAAI,IAAI,IAAIQ,WAAW,CAACR,OAAO,CAACa,CAAC,CAAC;IACvD,CAAC;IACD,MAAMlD,WAAW,GAAGkD,CAAC,IAAIH,cAAc,CAACV,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGU,cAAc,CAACV,OAAO,CAACa,CAAC,CAAC;IAC5F,MAAMjD,SAAS,GAAGiD,CAAC,IAAIF,YAAY,CAACX,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGW,YAAY,CAACX,OAAO,CAACa,CAAC,CAAC;IACtF,MAAMhD,cAAc,GAAGgD,CAAC,IAAID,iBAAiB,CAACZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGY,iBAAiB,CAACZ,OAAO,CAACa,CAAC,CAAC;IACrG7B,QAAQ,CAACsB,gBAAgB,CAAC,QAAQ,EAAE5C,QAAQ,CAAC;IAC7CsB,QAAQ,CAACsB,gBAAgB,CAAC,WAAW,EAAE3C,WAAW,CAAC;IACnDqB,QAAQ,CAACsB,gBAAgB,CAAC,SAAS,EAAE1C,SAAS,CAAC;IAC/CoB,QAAQ,CAACsB,gBAAgB,CAAC,cAAc,EAAEzC,cAAc,CAAC;IACzD,OAAO,MAAM;MACXmB,QAAQ,CAACuB,mBAAmB,CAAC,QAAQ,EAAE7C,QAAQ,CAAC;MAChDsB,QAAQ,CAACuB,mBAAmB,CAAC,WAAW,EAAE5C,WAAW,CAAC;MACtDqB,QAAQ,CAACuB,mBAAmB,CAAC,SAAS,EAAE3C,SAAS,CAAC;MAClDoB,QAAQ,CAACuB,mBAAmB,CAAC,cAAc,EAAE1C,cAAc,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,CAACuB,UAAU,EAAEJ,QAAQ,CAAC,CAAC;EAC1B7B,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,IAAInC,WAAW,EAAE;MACf,MAAM+C,GAAG,GAAGzB,GAAG,CAAC,CAAC,CAACL,QAAQ;MAC1BM,GAAG,CAAC;QACFN;MACF,CAAC,CAAC;MACF,OAAO,MAAMM,GAAG,CAAC;QACfN,QAAQ,EAAE8B;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC/C,WAAW,EAAEiB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAa7B,KAAK,CAAC4D,aAAa,CAAC5D,KAAK,CAAC6D,QAAQ,EAAE,IAAI,EAAE,aAAa7D,KAAK,CAAC4D,aAAa,CAAC,WAAW,EAAE;IAC1GlC,GAAG,EAAEA,GAAG;IACRf,MAAM,EAAEkB,QAAQ;IAChBf,OAAO,EAAEA,OAAO;IAChBC,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA,IAAI;IACVC,eAAe,EAAEA,eAAe;IAChCC,YAAY,EAAEA,YAAY;IAC1BC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA,IAAI;IACVC,KAAK,EAAEA,KAAK;IACZC,KAAK,EAAEA,KAAK;IACZC,KAAK,EAAEA;EACT,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAAC4D,aAAa,CAAC,OAAO,EAAE9D,QAAQ,CAAC;IACrD4B,GAAG,EAAEc;EACP,CAAC,EAAEf,KAAK,CAAC,EAAEpB,QAAQ,CAAC,CAAC;AACvB,CAAC,CAAC;AAEF,SAASH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}