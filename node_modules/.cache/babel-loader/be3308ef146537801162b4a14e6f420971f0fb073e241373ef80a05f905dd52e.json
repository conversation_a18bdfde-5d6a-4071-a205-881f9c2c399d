{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Mesh } from 'three';\nimport { SAH, acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from 'three-mesh-bvh';\nconst isMesh = child => child.isMesh;\n\n/**\n * @deprecated Use the Bvh component instead\n */\nfunction useBVH(mesh, options) {\n  options = {\n    strategy: SAH,\n    verbose: false,\n    setBoundingBox: true,\n    maxDepth: 40,\n    maxLeafTris: 10,\n    indirect: false,\n    ...options\n  };\n  React.useEffect(() => {\n    if (mesh.current) {\n      mesh.current.raycast = acceleratedRaycast;\n      const geometry = mesh.current.geometry;\n      geometry.computeBoundsTree = computeBoundsTree;\n      geometry.disposeBoundsTree = disposeBoundsTree;\n      geometry.computeBoundsTree(options);\n      return () => {\n        if (geometry.boundsTree) {\n          geometry.disposeBoundsTree();\n        }\n      };\n    }\n  }, [mesh, JSON.stringify(options)]);\n}\nconst Bvh = /* @__PURE__ */React.forwardRef(({\n  enabled = true,\n  firstHitOnly = false,\n  children,\n  strategy = SAH,\n  verbose = false,\n  setBoundingBox = true,\n  maxDepth = 40,\n  maxLeafTris = 10,\n  indirect = false,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const raycaster = useThree(state => state.raycaster);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useEffect(() => {\n    if (enabled) {\n      const options = {\n        strategy,\n        verbose,\n        setBoundingBox,\n        maxDepth,\n        maxLeafTris,\n        indirect\n      };\n      const group = ref.current;\n      // This can only safely work if the component is used once, but there is no alternative.\n      // Hijacking the raycast method to do it for individual meshes is not an option as it would\n      // cost too much memory ...\n      raycaster.firstHitOnly = firstHitOnly;\n      group.traverse(child => {\n        // Only include meshes that do not yet have a boundsTree and whose raycast is standard issue\n        if (isMesh(child) && !child.geometry.boundsTree && child.raycast === Mesh.prototype.raycast) {\n          child.raycast = acceleratedRaycast;\n          child.geometry.computeBoundsTree = computeBoundsTree;\n          child.geometry.disposeBoundsTree = disposeBoundsTree;\n          child.geometry.computeBoundsTree(options);\n        }\n      });\n      return () => {\n        delete raycaster.firstHitOnly;\n        group.traverse(child => {\n          if (isMesh(child) && child.geometry.boundsTree) {\n            child.geometry.disposeBoundsTree();\n            child.raycast = Mesh.prototype.raycast;\n          }\n        });\n      };\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), children);\n});\nexport { Bvh, useBVH };", "map": {"version": 3, "names": ["_extends", "useThree", "React", "<PERSON><PERSON>", "SAH", "acceleratedRaycast", "computeBoundsTree", "disposeBoundsTree", "<PERSON><PERSON><PERSON>", "child", "useBVH", "mesh", "options", "strategy", "verbose", "setBoundingBox", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "indirect", "useEffect", "current", "raycast", "geometry", "boundsTree", "JSON", "stringify", "Bvh", "forwardRef", "enabled", "firstHitOnly", "children", "props", "fref", "ref", "useRef", "raycaster", "state", "useImperativeHandle", "group", "traverse", "prototype", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Bvh.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Mesh } from 'three';\nimport { SAH, acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from 'three-mesh-bvh';\n\nconst isMesh = child => child.isMesh;\n\n/**\n * @deprecated Use the Bvh component instead\n */\nfunction useBVH(mesh, options) {\n  options = {\n    strategy: SAH,\n    verbose: false,\n    setBoundingBox: true,\n    maxDepth: 40,\n    maxLeafTris: 10,\n    indirect: false,\n    ...options\n  };\n  React.useEffect(() => {\n    if (mesh.current) {\n      mesh.current.raycast = acceleratedRaycast;\n      const geometry = mesh.current.geometry;\n      geometry.computeBoundsTree = computeBoundsTree;\n      geometry.disposeBoundsTree = disposeBoundsTree;\n      geometry.computeBoundsTree(options);\n      return () => {\n        if (geometry.boundsTree) {\n          geometry.disposeBoundsTree();\n        }\n      };\n    }\n  }, [mesh, JSON.stringify(options)]);\n}\nconst Bvh = /* @__PURE__ */React.forwardRef(({\n  enabled = true,\n  firstHitOnly = false,\n  children,\n  strategy = SAH,\n  verbose = false,\n  setBoundingBox = true,\n  maxDepth = 40,\n  maxLeafTris = 10,\n  indirect = false,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const raycaster = useThree(state => state.raycaster);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useEffect(() => {\n    if (enabled) {\n      const options = {\n        strategy,\n        verbose,\n        setBoundingBox,\n        maxDepth,\n        maxLeafTris,\n        indirect\n      };\n      const group = ref.current;\n      // This can only safely work if the component is used once, but there is no alternative.\n      // Hijacking the raycast method to do it for individual meshes is not an option as it would\n      // cost too much memory ...\n      raycaster.firstHitOnly = firstHitOnly;\n      group.traverse(child => {\n        // Only include meshes that do not yet have a boundsTree and whose raycast is standard issue\n        if (isMesh(child) && !child.geometry.boundsTree && child.raycast === Mesh.prototype.raycast) {\n          child.raycast = acceleratedRaycast;\n          child.geometry.computeBoundsTree = computeBoundsTree;\n          child.geometry.disposeBoundsTree = disposeBoundsTree;\n          child.geometry.computeBoundsTree(options);\n        }\n      });\n      return () => {\n        delete raycaster.firstHitOnly;\n        group.traverse(child => {\n          if (isMesh(child) && child.geometry.boundsTree) {\n            child.geometry.disposeBoundsTree();\n            child.raycast = Mesh.prototype.raycast;\n          }\n        });\n      };\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), children);\n});\n\nexport { Bvh, useBVH };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,OAAO;AAC5B,SAASC,GAAG,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,gBAAgB;AAE9F,MAAMC,MAAM,GAAGC,KAAK,IAAIA,KAAK,CAACD,MAAM;;AAEpC;AACA;AACA;AACA,SAASE,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC7BA,OAAO,GAAG;IACRC,QAAQ,EAAET,GAAG;IACbU,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,KAAK;IACf,GAAGN;EACL,CAAC;EACDV,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIR,IAAI,CAACS,OAAO,EAAE;MAChBT,IAAI,CAACS,OAAO,CAACC,OAAO,GAAGhB,kBAAkB;MACzC,MAAMiB,QAAQ,GAAGX,IAAI,CAACS,OAAO,CAACE,QAAQ;MACtCA,QAAQ,CAAChB,iBAAiB,GAAGA,iBAAiB;MAC9CgB,QAAQ,CAACf,iBAAiB,GAAGA,iBAAiB;MAC9Ce,QAAQ,CAAChB,iBAAiB,CAACM,OAAO,CAAC;MACnC,OAAO,MAAM;QACX,IAAIU,QAAQ,CAACC,UAAU,EAAE;UACvBD,QAAQ,CAACf,iBAAiB,CAAC,CAAC;QAC9B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACI,IAAI,EAAEa,IAAI,CAACC,SAAS,CAACb,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,MAAMc,GAAG,GAAG,eAAexB,KAAK,CAACyB,UAAU,CAAC,CAAC;EAC3CC,OAAO,GAAG,IAAI;EACdC,YAAY,GAAG,KAAK;EACpBC,QAAQ;EACRjB,QAAQ,GAAGT,GAAG;EACdU,OAAO,GAAG,KAAK;EACfC,cAAc,GAAG,IAAI;EACrBC,QAAQ,GAAG,EAAE;EACbC,WAAW,GAAG,EAAE;EAChBC,QAAQ,GAAG,KAAK;EAChB,GAAGa;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAG/B,KAAK,CAACgC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,SAAS,GAAGlC,QAAQ,CAACmC,KAAK,IAAIA,KAAK,CAACD,SAAS,CAAC;EACpDjC,KAAK,CAACmC,mBAAmB,CAACL,IAAI,EAAE,MAAMC,GAAG,CAACb,OAAO,EAAE,EAAE,CAAC;EACtDlB,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIS,OAAO,EAAE;MACX,MAAMhB,OAAO,GAAG;QACdC,QAAQ;QACRC,OAAO;QACPC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC;MACF,CAAC;MACD,MAAMoB,KAAK,GAAGL,GAAG,CAACb,OAAO;MACzB;MACA;MACA;MACAe,SAAS,CAACN,YAAY,GAAGA,YAAY;MACrCS,KAAK,CAACC,QAAQ,CAAC9B,KAAK,IAAI;QACtB;QACA,IAAID,MAAM,CAACC,KAAK,CAAC,IAAI,CAACA,KAAK,CAACa,QAAQ,CAACC,UAAU,IAAId,KAAK,CAACY,OAAO,KAAKlB,IAAI,CAACqC,SAAS,CAACnB,OAAO,EAAE;UAC3FZ,KAAK,CAACY,OAAO,GAAGhB,kBAAkB;UAClCI,KAAK,CAACa,QAAQ,CAAChB,iBAAiB,GAAGA,iBAAiB;UACpDG,KAAK,CAACa,QAAQ,CAACf,iBAAiB,GAAGA,iBAAiB;UACpDE,KAAK,CAACa,QAAQ,CAAChB,iBAAiB,CAACM,OAAO,CAAC;QAC3C;MACF,CAAC,CAAC;MACF,OAAO,MAAM;QACX,OAAOuB,SAAS,CAACN,YAAY;QAC7BS,KAAK,CAACC,QAAQ,CAAC9B,KAAK,IAAI;UACtB,IAAID,MAAM,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACa,QAAQ,CAACC,UAAU,EAAE;YAC9Cd,KAAK,CAACa,QAAQ,CAACf,iBAAiB,CAAC,CAAC;YAClCE,KAAK,CAACY,OAAO,GAAGlB,IAAI,CAACqC,SAAS,CAACnB,OAAO;UACxC;QACF,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAanB,KAAK,CAACuC,aAAa,CAAC,OAAO,EAAEzC,QAAQ,CAAC;IACxDiC,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAED,QAAQ,CAAC;AACtB,CAAC,CAAC;AAEF,SAASJ,GAAG,EAAEhB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}