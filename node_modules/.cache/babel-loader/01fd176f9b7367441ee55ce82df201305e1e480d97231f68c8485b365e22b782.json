{"ast": null, "code": "import { Line3, Plane, Vector3, <PERSON><PERSON> } from \"three\";\nimport { ConvexGeometry } from \"../geometries/ConvexGeometry.js\";\nconst _v1 = /* @__PURE__ */new Vector3();\nconst ConvexObjectBreaker = /* @__PURE__ */(() => {\n  class ConvexObjectBreaker2 {\n    constructor(minSizeForBreak = 1.4, smallDelta = 1e-4) {\n      this.minSizeForBreak = minSizeForBreak;\n      this.smallDelta = smallDelta;\n      this.tempLine1 = new Line3();\n      this.tempPlane1 = new Plane();\n      this.tempPlane2 = new Plane();\n      this.tempPlane_Cut = new Plane();\n      this.tempCM1 = new Vector3();\n      this.tempCM2 = new Vector3();\n      this.tempVector3 = new Vector3();\n      this.tempVector3_2 = new Vector3();\n      this.tempVector3_3 = new Vector3();\n      this.tempVector3_P0 = new Vector3();\n      this.tempVector3_P1 = new Vector3();\n      this.tempVector3_P2 = new Vector3();\n      this.tempVector3_N0 = new Vector3();\n      this.tempVector3_N1 = new Vector3();\n      this.tempVector3_AB = new Vector3();\n      this.tempVector3_CB = new Vector3();\n      this.tempResultObjects = {\n        object1: null,\n        object2: null\n      };\n      this.segments = [];\n      const n = 30 * 30;\n      for (let i = 0; i < n; i++) this.segments[i] = false;\n    }\n    prepareBreakableObject(object, mass, velocity, angularVelocity, breakable) {\n      const userData = object.userData;\n      userData.mass = mass;\n      userData.velocity = velocity.clone();\n      userData.angularVelocity = angularVelocity.clone();\n      userData.breakable = breakable;\n    }\n    /*\n     * @param {int} maxRadialIterations Iterations for radial cuts.\n     * @param {int} maxRandomIterations Max random iterations for not-radial cuts\n     *\n     * Returns the array of pieces\n     */\n    subdivideByImpact(object, pointOfImpact, normal, maxRadialIterations, maxRandomIterations) {\n      const debris = [];\n      const tempPlane1 = this.tempPlane1;\n      const tempPlane2 = this.tempPlane2;\n      this.tempVector3.addVectors(pointOfImpact, normal);\n      tempPlane1.setFromCoplanarPoints(pointOfImpact, object.position, this.tempVector3);\n      const maxTotalIterations = maxRandomIterations + maxRadialIterations;\n      const scope = this;\n      function subdivideRadial(subObject, startAngle, endAngle, numIterations) {\n        if (Math.random() < numIterations * 0.05 || numIterations > maxTotalIterations) {\n          debris.push(subObject);\n          return;\n        }\n        let angle = Math.PI;\n        if (numIterations === 0) {\n          tempPlane2.normal.copy(tempPlane1.normal);\n          tempPlane2.constant = tempPlane1.constant;\n        } else {\n          if (numIterations <= maxRadialIterations) {\n            angle = (endAngle - startAngle) * (0.2 + 0.6 * Math.random()) + startAngle;\n            scope.tempVector3_2.copy(object.position).sub(pointOfImpact).applyAxisAngle(normal, angle).add(pointOfImpact);\n            tempPlane2.setFromCoplanarPoints(pointOfImpact, scope.tempVector3, scope.tempVector3_2);\n          } else {\n            angle = (0.5 * (numIterations & 1) + 0.2 * (2 - Math.random())) * Math.PI;\n            scope.tempVector3_2.copy(pointOfImpact).sub(subObject.position).applyAxisAngle(normal, angle).add(subObject.position);\n            scope.tempVector3_3.copy(normal).add(subObject.position);\n            tempPlane2.setFromCoplanarPoints(subObject.position, scope.tempVector3_3, scope.tempVector3_2);\n          }\n        }\n        scope.cutByPlane(subObject, tempPlane2, scope.tempResultObjects);\n        const obj1 = scope.tempResultObjects.object1;\n        const obj2 = scope.tempResultObjects.object2;\n        if (obj1) {\n          subdivideRadial(obj1, startAngle, angle, numIterations + 1);\n        }\n        if (obj2) {\n          subdivideRadial(obj2, angle, endAngle, numIterations + 1);\n        }\n      }\n      subdivideRadial(object, 0, 2 * Math.PI, 0);\n      return debris;\n    }\n    cutByPlane(object, plane, output) {\n      const geometry = object.geometry;\n      const coords = geometry.attributes.position.array;\n      const normals = geometry.attributes.normal.array;\n      const numPoints = coords.length / 3;\n      let numFaces = numPoints / 3;\n      let indices = geometry.getIndex();\n      if (indices) {\n        indices = indices.array;\n        numFaces = indices.length / 3;\n      }\n      function getVertexIndex(faceIdx, vert) {\n        const idx = faceIdx * 3 + vert;\n        return indices ? indices[idx] : idx;\n      }\n      const points1 = [];\n      const points2 = [];\n      const delta = this.smallDelta;\n      const numPointPairs = numPoints * numPoints;\n      for (let i = 0; i < numPointPairs; i++) this.segments[i] = false;\n      const p0 = this.tempVector3_P0;\n      const p1 = this.tempVector3_P1;\n      const n0 = this.tempVector3_N0;\n      const n1 = this.tempVector3_N1;\n      for (let i = 0; i < numFaces - 1; i++) {\n        const a1 = getVertexIndex(i, 0);\n        const b1 = getVertexIndex(i, 1);\n        const c1 = getVertexIndex(i, 2);\n        n0.set(normals[a1], normals[a1] + 1, normals[a1] + 2);\n        for (let j = i + 1; j < numFaces; j++) {\n          const a2 = getVertexIndex(j, 0);\n          const b2 = getVertexIndex(j, 1);\n          const c2 = getVertexIndex(j, 2);\n          n1.set(normals[a2], normals[a2] + 1, normals[a2] + 2);\n          const coplanar = 1 - n0.dot(n1) < delta;\n          if (coplanar) {\n            if (a1 === a2 || a1 === b2 || a1 === c2) {\n              if (b1 === a2 || b1 === b2 || b1 === c2) {\n                this.segments[a1 * numPoints + b1] = true;\n                this.segments[b1 * numPoints + a1] = true;\n              } else {\n                this.segments[c1 * numPoints + a1] = true;\n                this.segments[a1 * numPoints + c1] = true;\n              }\n            } else if (b1 === a2 || b1 === b2 || b1 === c2) {\n              this.segments[c1 * numPoints + b1] = true;\n              this.segments[b1 * numPoints + c1] = true;\n            }\n          }\n        }\n      }\n      const localPlane = this.tempPlane_Cut;\n      object.updateMatrix();\n      ConvexObjectBreaker2.transformPlaneToLocalSpace(plane, object.matrix, localPlane);\n      for (let i = 0; i < numFaces; i++) {\n        const va = getVertexIndex(i, 0);\n        const vb = getVertexIndex(i, 1);\n        const vc = getVertexIndex(i, 2);\n        for (let segment = 0; segment < 3; segment++) {\n          const i0 = segment === 0 ? va : segment === 1 ? vb : vc;\n          const i1 = segment === 0 ? vb : segment === 1 ? vc : va;\n          const segmentState = this.segments[i0 * numPoints + i1];\n          if (segmentState) continue;\n          this.segments[i0 * numPoints + i1] = true;\n          this.segments[i1 * numPoints + i0] = true;\n          p0.set(coords[3 * i0], coords[3 * i0 + 1], coords[3 * i0 + 2]);\n          p1.set(coords[3 * i1], coords[3 * i1 + 1], coords[3 * i1 + 2]);\n          let mark0 = 0;\n          let d = localPlane.distanceToPoint(p0);\n          if (d > delta) {\n            mark0 = 2;\n            points2.push(p0.clone());\n          } else if (d < -delta) {\n            mark0 = 1;\n            points1.push(p0.clone());\n          } else {\n            mark0 = 3;\n            points1.push(p0.clone());\n            points2.push(p0.clone());\n          }\n          let mark1 = 0;\n          d = localPlane.distanceToPoint(p1);\n          if (d > delta) {\n            mark1 = 2;\n            points2.push(p1.clone());\n          } else if (d < -delta) {\n            mark1 = 1;\n            points1.push(p1.clone());\n          } else {\n            mark1 = 3;\n            points1.push(p1.clone());\n            points2.push(p1.clone());\n          }\n          if (mark0 === 1 && mark1 === 2 || mark0 === 2 && mark1 === 1) {\n            this.tempLine1.start.copy(p0);\n            this.tempLine1.end.copy(p1);\n            let intersection = new Vector3();\n            intersection = localPlane.intersectLine(this.tempLine1, intersection);\n            if (intersection === null) {\n              console.error(\"Internal error: segment does not intersect plane.\");\n              output.segmentedObject1 = null;\n              output.segmentedObject2 = null;\n              return 0;\n            }\n            points1.push(intersection);\n            points2.push(intersection.clone());\n          }\n        }\n      }\n      const newMass = object.userData.mass * 0.5;\n      this.tempCM1.set(0, 0, 0);\n      let radius1 = 0;\n      const numPoints1 = points1.length;\n      if (numPoints1 > 0) {\n        for (let i = 0; i < numPoints1; i++) this.tempCM1.add(points1[i]);\n        this.tempCM1.divideScalar(numPoints1);\n        for (let i = 0; i < numPoints1; i++) {\n          const p = points1[i];\n          p.sub(this.tempCM1);\n          radius1 = Math.max(radius1, p.x, p.y, p.z);\n        }\n        this.tempCM1.add(object.position);\n      }\n      this.tempCM2.set(0, 0, 0);\n      let radius2 = 0;\n      const numPoints2 = points2.length;\n      if (numPoints2 > 0) {\n        for (let i = 0; i < numPoints2; i++) this.tempCM2.add(points2[i]);\n        this.tempCM2.divideScalar(numPoints2);\n        for (let i = 0; i < numPoints2; i++) {\n          const p = points2[i];\n          p.sub(this.tempCM2);\n          radius2 = Math.max(radius2, p.x, p.y, p.z);\n        }\n        this.tempCM2.add(object.position);\n      }\n      let object1 = null;\n      let object2 = null;\n      let numObjects = 0;\n      if (numPoints1 > 4) {\n        object1 = new Mesh(new ConvexGeometry(points1), object.material);\n        object1.position.copy(this.tempCM1);\n        object1.quaternion.copy(object.quaternion);\n        this.prepareBreakableObject(object1, newMass, object.userData.velocity, object.userData.angularVelocity, 2 * radius1 > this.minSizeForBreak);\n        numObjects++;\n      }\n      if (numPoints2 > 4) {\n        object2 = new Mesh(new ConvexGeometry(points2), object.material);\n        object2.position.copy(this.tempCM2);\n        object2.quaternion.copy(object.quaternion);\n        this.prepareBreakableObject(object2, newMass, object.userData.velocity, object.userData.angularVelocity, 2 * radius2 > this.minSizeForBreak);\n        numObjects++;\n      }\n      output.object1 = object1;\n      output.object2 = object2;\n      return numObjects;\n    }\n    static transformFreeVector(v, m) {\n      const x = v.x,\n        y = v.y,\n        z = v.z;\n      const e = m.elements;\n      v.x = e[0] * x + e[4] * y + e[8] * z;\n      v.y = e[1] * x + e[5] * y + e[9] * z;\n      v.z = e[2] * x + e[6] * y + e[10] * z;\n      return v;\n    }\n    static transformFreeVectorInverse(v, m) {\n      const x = v.x,\n        y = v.y,\n        z = v.z;\n      const e = m.elements;\n      v.x = e[0] * x + e[1] * y + e[2] * z;\n      v.y = e[4] * x + e[5] * y + e[6] * z;\n      v.z = e[8] * x + e[9] * y + e[10] * z;\n      return v;\n    }\n    static transformTiedVectorInverse(v, m) {\n      const x = v.x,\n        y = v.y,\n        z = v.z;\n      const e = m.elements;\n      v.x = e[0] * x + e[1] * y + e[2] * z - e[12];\n      v.y = e[4] * x + e[5] * y + e[6] * z - e[13];\n      v.z = e[8] * x + e[9] * y + e[10] * z - e[14];\n      return v;\n    }\n    static transformPlaneToLocalSpace(plane, m, resultPlane) {\n      resultPlane.normal.copy(plane.normal);\n      resultPlane.constant = plane.constant;\n      const referencePoint = ConvexObjectBreaker2.transformTiedVectorInverse(plane.coplanarPoint(_v1), m);\n      ConvexObjectBreaker2.transformFreeVectorInverse(resultPlane.normal, m);\n      resultPlane.constant = -referencePoint.dot(resultPlane.normal);\n    }\n  }\n  return ConvexObjectBreaker2;\n})();\nexport { ConvexObjectBreaker };", "map": {"version": 3, "names": ["_v1", "Vector3", "ConvexObjectBreaker", "ConvexObjectBreaker2", "constructor", "minSizeForBreak", "smallD<PERSON><PERSON>", "tempLine1", "Line3", "tempPlane1", "Plane", "tempPlane2", "tempPlane_Cut", "tempCM1", "tempCM2", "tempVector3", "tempVector3_2", "tempVector3_3", "tempVector3_P0", "tempVector3_P1", "tempVector3_P2", "tempVector3_N0", "tempVector3_N1", "tempVector3_AB", "tempVector3_CB", "tempResultObjects", "object1", "object2", "segments", "n", "i", "prepareBreakableObject", "object", "mass", "velocity", "angularVelocity", "breakable", "userData", "clone", "subdivideByImpact", "pointOfImpact", "normal", "maxRadialIterations", "maxRandomIterations", "debris", "addVectors", "setFromCoplanarPoints", "position", "maxTotalIterations", "scope", "subdivideRadial", "subObject", "startAngle", "endAngle", "numIterations", "Math", "random", "push", "angle", "PI", "copy", "constant", "sub", "applyAxisAngle", "add", "cutByPlane", "obj1", "obj2", "plane", "output", "geometry", "coords", "attributes", "array", "normals", "numPoints", "length", "numFaces", "indices", "getIndex", "getVertexIndex", "faceIdx", "vert", "idx", "points1", "points2", "delta", "numPointPairs", "p0", "p1", "n0", "n1", "a1", "b1", "c1", "set", "j", "a2", "b2", "c2", "coplanar", "dot", "localPlane", "updateMatrix", "transformPlaneToLocalSpace", "matrix", "va", "vb", "vc", "segment", "i0", "i1", "segmentState", "mark0", "d", "distanceToPoint", "mark1", "start", "end", "intersection", "intersectLine", "console", "error", "segmentedObject1", "segmentedObject2", "newMass", "radius1", "numPoints1", "divideScalar", "p", "max", "x", "y", "z", "radius2", "numPoints2", "numObjects", "<PERSON><PERSON>", "ConvexGeometry", "material", "quaternion", "transformFreeVector", "v", "m", "e", "elements", "transformFreeVectorInverse", "transformTiedVectorInverse", "resultPlane", "referencePoint", "coplanarPoint"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/misc/ConvexObjectBreaker.js"], "sourcesContent": ["import { Line3, <PERSON>sh, Plane, Vector3 } from 'three'\nimport { ConvexGeometry } from '../geometries/ConvexGeometry'\n\n/**\n * @fileoverview This class can be used to subdivide a convex Geometry object into pieces.\n *\n * Usage:\n *\n * Use the function prepareBreakableObject to prepare a Mesh object to be broken.\n *\n * Then, call the various functions to subdivide the object (subdivideByImpact, cutByPlane)\n *\n * Sub-objects that are product of subdivision don't need prepareBreakableObject to be called on them.\n *\n * Requisites for the object:\n *\n *  - Mesh object must have a buffer geometry and a material\n *\n *  - Vertex normals must be planar (not smoothed)\n *\n *  - The geometry must be convex (this is not checked in the library). You can create convex\n *  geometries with ConvexGeometry. The BoxGeometry, SphereGeometry and other convex primitives\n *  can also be used.\n *\n * Note: This lib adds member variables to object's userData member (see prepareBreakableObject function)\n * Use with caution and read the code when using with other libs.\n *\n * @param {double} minSizeForBreak Min size a debris can have to break.\n * @param {double} smallDelta Max distance to consider that a point belongs to a plane.\n *\n */\n\nconst _v1 = /* @__PURE__ */ new Vector3()\n\nconst ConvexObjectBreaker = /* @__PURE__ */ (() => {\n  class ConvexObjectBreaker {\n    constructor(minSizeForBreak = 1.4, smallDelta = 0.0001) {\n      this.minSizeForBreak = minSizeForBreak\n      this.smallDelta = smallDelta\n\n      this.tempLine1 = new Line3()\n      this.tempPlane1 = new Plane()\n      this.tempPlane2 = new Plane()\n      this.tempPlane_Cut = new Plane()\n      this.tempCM1 = new Vector3()\n      this.tempCM2 = new Vector3()\n      this.tempVector3 = new Vector3()\n      this.tempVector3_2 = new Vector3()\n      this.tempVector3_3 = new Vector3()\n      this.tempVector3_P0 = new Vector3()\n      this.tempVector3_P1 = new Vector3()\n      this.tempVector3_P2 = new Vector3()\n      this.tempVector3_N0 = new Vector3()\n      this.tempVector3_N1 = new Vector3()\n      this.tempVector3_AB = new Vector3()\n      this.tempVector3_CB = new Vector3()\n      this.tempResultObjects = { object1: null, object2: null }\n\n      this.segments = []\n      const n = 30 * 30\n      for (let i = 0; i < n; i++) this.segments[i] = false\n    }\n\n    prepareBreakableObject(object, mass, velocity, angularVelocity, breakable) {\n      // object is a Object3d (normally a Mesh), must have a buffer geometry, and it must be convex.\n      // Its material property is propagated to its children (sub-pieces)\n      // mass must be > 0\n\n      const userData = object.userData\n      userData.mass = mass\n      userData.velocity = velocity.clone()\n      userData.angularVelocity = angularVelocity.clone()\n      userData.breakable = breakable\n    }\n\n    /*\n     * @param {int} maxRadialIterations Iterations for radial cuts.\n     * @param {int} maxRandomIterations Max random iterations for not-radial cuts\n     *\n     * Returns the array of pieces\n     */\n    subdivideByImpact(object, pointOfImpact, normal, maxRadialIterations, maxRandomIterations) {\n      const debris = []\n\n      const tempPlane1 = this.tempPlane1\n      const tempPlane2 = this.tempPlane2\n\n      this.tempVector3.addVectors(pointOfImpact, normal)\n      tempPlane1.setFromCoplanarPoints(pointOfImpact, object.position, this.tempVector3)\n\n      const maxTotalIterations = maxRandomIterations + maxRadialIterations\n\n      const scope = this\n\n      function subdivideRadial(subObject, startAngle, endAngle, numIterations) {\n        if (Math.random() < numIterations * 0.05 || numIterations > maxTotalIterations) {\n          debris.push(subObject)\n\n          return\n        }\n\n        let angle = Math.PI\n\n        if (numIterations === 0) {\n          tempPlane2.normal.copy(tempPlane1.normal)\n          tempPlane2.constant = tempPlane1.constant\n        } else {\n          if (numIterations <= maxRadialIterations) {\n            angle = (endAngle - startAngle) * (0.2 + 0.6 * Math.random()) + startAngle\n\n            // Rotate tempPlane2 at impact point around normal axis and the angle\n            scope.tempVector3_2\n              .copy(object.position)\n              .sub(pointOfImpact)\n              .applyAxisAngle(normal, angle)\n              .add(pointOfImpact)\n            tempPlane2.setFromCoplanarPoints(pointOfImpact, scope.tempVector3, scope.tempVector3_2)\n          } else {\n            angle = (0.5 * (numIterations & 1) + 0.2 * (2 - Math.random())) * Math.PI\n\n            // Rotate tempPlane2 at object position around normal axis and the angle\n            scope.tempVector3_2\n              .copy(pointOfImpact)\n              .sub(subObject.position)\n              .applyAxisAngle(normal, angle)\n              .add(subObject.position)\n            scope.tempVector3_3.copy(normal).add(subObject.position)\n            tempPlane2.setFromCoplanarPoints(subObject.position, scope.tempVector3_3, scope.tempVector3_2)\n          }\n        }\n\n        // Perform the cut\n        scope.cutByPlane(subObject, tempPlane2, scope.tempResultObjects)\n\n        const obj1 = scope.tempResultObjects.object1\n        const obj2 = scope.tempResultObjects.object2\n\n        if (obj1) {\n          subdivideRadial(obj1, startAngle, angle, numIterations + 1)\n        }\n\n        if (obj2) {\n          subdivideRadial(obj2, angle, endAngle, numIterations + 1)\n        }\n      }\n\n      subdivideRadial(object, 0, 2 * Math.PI, 0)\n\n      return debris\n    }\n\n    cutByPlane(object, plane, output) {\n      // Returns breakable objects in output.object1 and output.object2 members, the resulting 2 pieces of the cut.\n      // object2 can be null if the plane doesn't cut the object.\n      // object1 can be null only in case of internal error\n      // Returned value is number of pieces, 0 for error.\n\n      const geometry = object.geometry\n      const coords = geometry.attributes.position.array\n      const normals = geometry.attributes.normal.array\n\n      const numPoints = coords.length / 3\n      let numFaces = numPoints / 3\n\n      let indices = geometry.getIndex()\n\n      if (indices) {\n        indices = indices.array\n        numFaces = indices.length / 3\n      }\n\n      function getVertexIndex(faceIdx, vert) {\n        // vert = 0, 1 or 2.\n\n        const idx = faceIdx * 3 + vert\n\n        return indices ? indices[idx] : idx\n      }\n\n      const points1 = []\n      const points2 = []\n\n      const delta = this.smallDelta\n\n      // Reset segments mark\n      const numPointPairs = numPoints * numPoints\n      for (let i = 0; i < numPointPairs; i++) this.segments[i] = false\n\n      const p0 = this.tempVector3_P0\n      const p1 = this.tempVector3_P1\n      const n0 = this.tempVector3_N0\n      const n1 = this.tempVector3_N1\n\n      // Iterate through the faces to mark edges shared by coplanar faces\n      for (let i = 0; i < numFaces - 1; i++) {\n        const a1 = getVertexIndex(i, 0)\n        const b1 = getVertexIndex(i, 1)\n        const c1 = getVertexIndex(i, 2)\n\n        // Assuming all 3 vertices have the same normal\n        n0.set(normals[a1], normals[a1] + 1, normals[a1] + 2)\n\n        for (let j = i + 1; j < numFaces; j++) {\n          const a2 = getVertexIndex(j, 0)\n          const b2 = getVertexIndex(j, 1)\n          const c2 = getVertexIndex(j, 2)\n\n          // Assuming all 3 vertices have the same normal\n          n1.set(normals[a2], normals[a2] + 1, normals[a2] + 2)\n\n          const coplanar = 1 - n0.dot(n1) < delta\n\n          if (coplanar) {\n            if (a1 === a2 || a1 === b2 || a1 === c2) {\n              if (b1 === a2 || b1 === b2 || b1 === c2) {\n                this.segments[a1 * numPoints + b1] = true\n                this.segments[b1 * numPoints + a1] = true\n              } else {\n                this.segments[c1 * numPoints + a1] = true\n                this.segments[a1 * numPoints + c1] = true\n              }\n            } else if (b1 === a2 || b1 === b2 || b1 === c2) {\n              this.segments[c1 * numPoints + b1] = true\n              this.segments[b1 * numPoints + c1] = true\n            }\n          }\n        }\n      }\n\n      // Transform the plane to object local space\n      const localPlane = this.tempPlane_Cut\n      object.updateMatrix()\n      ConvexObjectBreaker.transformPlaneToLocalSpace(plane, object.matrix, localPlane)\n\n      // Iterate through the faces adding points to both pieces\n      for (let i = 0; i < numFaces; i++) {\n        const va = getVertexIndex(i, 0)\n        const vb = getVertexIndex(i, 1)\n        const vc = getVertexIndex(i, 2)\n\n        for (let segment = 0; segment < 3; segment++) {\n          const i0 = segment === 0 ? va : segment === 1 ? vb : vc\n          const i1 = segment === 0 ? vb : segment === 1 ? vc : va\n\n          const segmentState = this.segments[i0 * numPoints + i1]\n\n          if (segmentState) continue // The segment already has been processed in another face\n\n          // Mark segment as processed (also inverted segment)\n          this.segments[i0 * numPoints + i1] = true\n          this.segments[i1 * numPoints + i0] = true\n\n          p0.set(coords[3 * i0], coords[3 * i0 + 1], coords[3 * i0 + 2])\n          p1.set(coords[3 * i1], coords[3 * i1 + 1], coords[3 * i1 + 2])\n\n          // mark: 1 for negative side, 2 for positive side, 3 for coplanar point\n          let mark0 = 0\n\n          let d = localPlane.distanceToPoint(p0)\n\n          if (d > delta) {\n            mark0 = 2\n            points2.push(p0.clone())\n          } else if (d < -delta) {\n            mark0 = 1\n            points1.push(p0.clone())\n          } else {\n            mark0 = 3\n            points1.push(p0.clone())\n            points2.push(p0.clone())\n          }\n\n          // mark: 1 for negative side, 2 for positive side, 3 for coplanar point\n          let mark1 = 0\n\n          d = localPlane.distanceToPoint(p1)\n\n          if (d > delta) {\n            mark1 = 2\n            points2.push(p1.clone())\n          } else if (d < -delta) {\n            mark1 = 1\n            points1.push(p1.clone())\n          } else {\n            mark1 = 3\n            points1.push(p1.clone())\n            points2.push(p1.clone())\n          }\n\n          if ((mark0 === 1 && mark1 === 2) || (mark0 === 2 && mark1 === 1)) {\n            // Intersection of segment with the plane\n\n            this.tempLine1.start.copy(p0)\n            this.tempLine1.end.copy(p1)\n\n            let intersection = new Vector3()\n            intersection = localPlane.intersectLine(this.tempLine1, intersection)\n\n            if (intersection === null) {\n              // Shouldn't happen\n              console.error('Internal error: segment does not intersect plane.')\n              output.segmentedObject1 = null\n              output.segmentedObject2 = null\n              return 0\n            }\n\n            points1.push(intersection)\n            points2.push(intersection.clone())\n          }\n        }\n      }\n\n      // Calculate debris mass (very fast and imprecise):\n      const newMass = object.userData.mass * 0.5\n\n      // Calculate debris Center of Mass (again fast and imprecise)\n      this.tempCM1.set(0, 0, 0)\n      let radius1 = 0\n      const numPoints1 = points1.length\n\n      if (numPoints1 > 0) {\n        for (let i = 0; i < numPoints1; i++) this.tempCM1.add(points1[i])\n\n        this.tempCM1.divideScalar(numPoints1)\n        for (let i = 0; i < numPoints1; i++) {\n          const p = points1[i]\n          p.sub(this.tempCM1)\n          radius1 = Math.max(radius1, p.x, p.y, p.z)\n        }\n\n        this.tempCM1.add(object.position)\n      }\n\n      this.tempCM2.set(0, 0, 0)\n      let radius2 = 0\n      const numPoints2 = points2.length\n      if (numPoints2 > 0) {\n        for (let i = 0; i < numPoints2; i++) this.tempCM2.add(points2[i])\n\n        this.tempCM2.divideScalar(numPoints2)\n        for (let i = 0; i < numPoints2; i++) {\n          const p = points2[i]\n          p.sub(this.tempCM2)\n          radius2 = Math.max(radius2, p.x, p.y, p.z)\n        }\n\n        this.tempCM2.add(object.position)\n      }\n\n      let object1 = null\n      let object2 = null\n\n      let numObjects = 0\n\n      if (numPoints1 > 4) {\n        object1 = new Mesh(new ConvexGeometry(points1), object.material)\n        object1.position.copy(this.tempCM1)\n        object1.quaternion.copy(object.quaternion)\n\n        this.prepareBreakableObject(\n          object1,\n          newMass,\n          object.userData.velocity,\n          object.userData.angularVelocity,\n          2 * radius1 > this.minSizeForBreak,\n        )\n\n        numObjects++\n      }\n\n      if (numPoints2 > 4) {\n        object2 = new Mesh(new ConvexGeometry(points2), object.material)\n        object2.position.copy(this.tempCM2)\n        object2.quaternion.copy(object.quaternion)\n\n        this.prepareBreakableObject(\n          object2,\n          newMass,\n          object.userData.velocity,\n          object.userData.angularVelocity,\n          2 * radius2 > this.minSizeForBreak,\n        )\n\n        numObjects++\n      }\n\n      output.object1 = object1\n      output.object2 = object2\n\n      return numObjects\n    }\n\n    static transformFreeVector(v, m) {\n      // input:\n      // vector interpreted as a free vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[4] * y + e[8] * z\n      v.y = e[1] * x + e[5] * y + e[9] * z\n      v.z = e[2] * x + e[6] * y + e[10] * z\n\n      return v\n    }\n\n    static transformFreeVectorInverse(v, m) {\n      // input:\n      // vector interpreted as a free vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[1] * y + e[2] * z\n      v.y = e[4] * x + e[5] * y + e[6] * z\n      v.z = e[8] * x + e[9] * y + e[10] * z\n\n      return v\n    }\n\n    static transformTiedVectorInverse(v, m) {\n      // input:\n      // vector interpreted as a tied (ordinary) vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[1] * y + e[2] * z - e[12]\n      v.y = e[4] * x + e[5] * y + e[6] * z - e[13]\n      v.z = e[8] * x + e[9] * y + e[10] * z - e[14]\n\n      return v\n    }\n\n    static transformPlaneToLocalSpace(plane, m, resultPlane) {\n      resultPlane.normal.copy(plane.normal)\n      resultPlane.constant = plane.constant\n\n      const referencePoint = ConvexObjectBreaker.transformTiedVectorInverse(plane.coplanarPoint(_v1), m)\n\n      ConvexObjectBreaker.transformFreeVectorInverse(resultPlane.normal, m)\n\n      // recalculate constant (like in setFromNormalAndCoplanarPoint)\n      resultPlane.constant = -referencePoint.dot(resultPlane.normal)\n    }\n  }\n\n  return ConvexObjectBreaker\n})()\n\nexport { ConvexObjectBreaker }\n"], "mappings": ";;AAgCA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AAEpC,MAACC,mBAAA,GAAuC,sBAAM;EACjD,MAAMC,oBAAA,CAAoB;IACxBC,YAAYC,eAAA,GAAkB,KAAKC,UAAA,GAAa,MAAQ;MACtD,KAAKD,eAAA,GAAkBA,eAAA;MACvB,KAAKC,UAAA,GAAaA,UAAA;MAElB,KAAKC,SAAA,GAAY,IAAIC,KAAA,CAAO;MAC5B,KAAKC,UAAA,GAAa,IAAIC,KAAA,CAAO;MAC7B,KAAKC,UAAA,GAAa,IAAID,KAAA,CAAO;MAC7B,KAAKE,aAAA,GAAgB,IAAIF,KAAA,CAAO;MAChC,KAAKG,OAAA,GAAU,IAAIZ,OAAA,CAAS;MAC5B,KAAKa,OAAA,GAAU,IAAIb,OAAA,CAAS;MAC5B,KAAKc,WAAA,GAAc,IAAId,OAAA,CAAS;MAChC,KAAKe,aAAA,GAAgB,IAAIf,OAAA,CAAS;MAClC,KAAKgB,aAAA,GAAgB,IAAIhB,OAAA,CAAS;MAClC,KAAKiB,cAAA,GAAiB,IAAIjB,OAAA,CAAS;MACnC,KAAKkB,cAAA,GAAiB,IAAIlB,OAAA,CAAS;MACnC,KAAKmB,cAAA,GAAiB,IAAInB,OAAA,CAAS;MACnC,KAAKoB,cAAA,GAAiB,IAAIpB,OAAA,CAAS;MACnC,KAAKqB,cAAA,GAAiB,IAAIrB,OAAA,CAAS;MACnC,KAAKsB,cAAA,GAAiB,IAAItB,OAAA,CAAS;MACnC,KAAKuB,cAAA,GAAiB,IAAIvB,OAAA,CAAS;MACnC,KAAKwB,iBAAA,GAAoB;QAAEC,OAAA,EAAS;QAAMC,OAAA,EAAS;MAAM;MAEzD,KAAKC,QAAA,GAAW,EAAE;MAClB,MAAMC,CAAA,GAAI,KAAK;MACf,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,EAAGC,CAAA,IAAK,KAAKF,QAAA,CAASE,CAAC,IAAI;IAChD;IAEDC,uBAAuBC,MAAA,EAAQC,IAAA,EAAMC,QAAA,EAAUC,eAAA,EAAiBC,SAAA,EAAW;MAKzE,MAAMC,QAAA,GAAWL,MAAA,CAAOK,QAAA;MACxBA,QAAA,CAASJ,IAAA,GAAOA,IAAA;MAChBI,QAAA,CAASH,QAAA,GAAWA,QAAA,CAASI,KAAA,CAAO;MACpCD,QAAA,CAASF,eAAA,GAAkBA,eAAA,CAAgBG,KAAA,CAAO;MAClDD,QAAA,CAASD,SAAA,GAAYA,SAAA;IACtB;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQDG,kBAAkBP,MAAA,EAAQQ,aAAA,EAAeC,MAAA,EAAQC,mBAAA,EAAqBC,mBAAA,EAAqB;MACzF,MAAMC,MAAA,GAAS,EAAE;MAEjB,MAAMnC,UAAA,GAAa,KAAKA,UAAA;MACxB,MAAME,UAAA,GAAa,KAAKA,UAAA;MAExB,KAAKI,WAAA,CAAY8B,UAAA,CAAWL,aAAA,EAAeC,MAAM;MACjDhC,UAAA,CAAWqC,qBAAA,CAAsBN,aAAA,EAAeR,MAAA,CAAOe,QAAA,EAAU,KAAKhC,WAAW;MAEjF,MAAMiC,kBAAA,GAAqBL,mBAAA,GAAsBD,mBAAA;MAEjD,MAAMO,KAAA,GAAQ;MAEd,SAASC,gBAAgBC,SAAA,EAAWC,UAAA,EAAYC,QAAA,EAAUC,aAAA,EAAe;QACvE,IAAIC,IAAA,CAAKC,MAAA,CAAQ,IAAGF,aAAA,GAAgB,QAAQA,aAAA,GAAgBN,kBAAA,EAAoB;UAC9EJ,MAAA,CAAOa,IAAA,CAAKN,SAAS;UAErB;QACD;QAED,IAAIO,KAAA,GAAQH,IAAA,CAAKI,EAAA;QAEjB,IAAIL,aAAA,KAAkB,GAAG;UACvB3C,UAAA,CAAW8B,MAAA,CAAOmB,IAAA,CAAKnD,UAAA,CAAWgC,MAAM;UACxC9B,UAAA,CAAWkD,QAAA,GAAWpD,UAAA,CAAWoD,QAAA;QAC3C,OAAe;UACL,IAAIP,aAAA,IAAiBZ,mBAAA,EAAqB;YACxCgB,KAAA,IAASL,QAAA,GAAWD,UAAA,KAAe,MAAM,MAAMG,IAAA,CAAKC,MAAA,CAAQ,KAAIJ,UAAA;YAGhEH,KAAA,CAAMjC,aAAA,CACH4C,IAAA,CAAK5B,MAAA,CAAOe,QAAQ,EACpBe,GAAA,CAAItB,aAAa,EACjBuB,cAAA,CAAetB,MAAA,EAAQiB,KAAK,EAC5BM,GAAA,CAAIxB,aAAa;YACpB7B,UAAA,CAAWmC,qBAAA,CAAsBN,aAAA,EAAeS,KAAA,CAAMlC,WAAA,EAAakC,KAAA,CAAMjC,aAAa;UAClG,OAAiB;YACL0C,KAAA,IAAS,OAAOJ,aAAA,GAAgB,KAAK,OAAO,IAAIC,IAAA,CAAKC,MAAA,OAAaD,IAAA,CAAKI,EAAA;YAGvEV,KAAA,CAAMjC,aAAA,CACH4C,IAAA,CAAKpB,aAAa,EAClBsB,GAAA,CAAIX,SAAA,CAAUJ,QAAQ,EACtBgB,cAAA,CAAetB,MAAA,EAAQiB,KAAK,EAC5BM,GAAA,CAAIb,SAAA,CAAUJ,QAAQ;YACzBE,KAAA,CAAMhC,aAAA,CAAc2C,IAAA,CAAKnB,MAAM,EAAEuB,GAAA,CAAIb,SAAA,CAAUJ,QAAQ;YACvDpC,UAAA,CAAWmC,qBAAA,CAAsBK,SAAA,CAAUJ,QAAA,EAAUE,KAAA,CAAMhC,aAAA,EAAegC,KAAA,CAAMjC,aAAa;UAC9F;QACF;QAGDiC,KAAA,CAAMgB,UAAA,CAAWd,SAAA,EAAWxC,UAAA,EAAYsC,KAAA,CAAMxB,iBAAiB;QAE/D,MAAMyC,IAAA,GAAOjB,KAAA,CAAMxB,iBAAA,CAAkBC,OAAA;QACrC,MAAMyC,IAAA,GAAOlB,KAAA,CAAMxB,iBAAA,CAAkBE,OAAA;QAErC,IAAIuC,IAAA,EAAM;UACRhB,eAAA,CAAgBgB,IAAA,EAAMd,UAAA,EAAYM,KAAA,EAAOJ,aAAA,GAAgB,CAAC;QAC3D;QAED,IAAIa,IAAA,EAAM;UACRjB,eAAA,CAAgBiB,IAAA,EAAMT,KAAA,EAAOL,QAAA,EAAUC,aAAA,GAAgB,CAAC;QACzD;MACF;MAEDJ,eAAA,CAAgBlB,MAAA,EAAQ,GAAG,IAAIuB,IAAA,CAAKI,EAAA,EAAI,CAAC;MAEzC,OAAOf,MAAA;IACR;IAEDqB,WAAWjC,MAAA,EAAQoC,KAAA,EAAOC,MAAA,EAAQ;MAMhC,MAAMC,QAAA,GAAWtC,MAAA,CAAOsC,QAAA;MACxB,MAAMC,MAAA,GAASD,QAAA,CAASE,UAAA,CAAWzB,QAAA,CAAS0B,KAAA;MAC5C,MAAMC,OAAA,GAAUJ,QAAA,CAASE,UAAA,CAAW/B,MAAA,CAAOgC,KAAA;MAE3C,MAAME,SAAA,GAAYJ,MAAA,CAAOK,MAAA,GAAS;MAClC,IAAIC,QAAA,GAAWF,SAAA,GAAY;MAE3B,IAAIG,OAAA,GAAUR,QAAA,CAASS,QAAA,CAAU;MAEjC,IAAID,OAAA,EAAS;QACXA,OAAA,GAAUA,OAAA,CAAQL,KAAA;QAClBI,QAAA,GAAWC,OAAA,CAAQF,MAAA,GAAS;MAC7B;MAED,SAASI,eAAeC,OAAA,EAASC,IAAA,EAAM;QAGrC,MAAMC,GAAA,GAAMF,OAAA,GAAU,IAAIC,IAAA;QAE1B,OAAOJ,OAAA,GAAUA,OAAA,CAAQK,GAAG,IAAIA,GAAA;MACjC;MAED,MAAMC,OAAA,GAAU,EAAE;MAClB,MAAMC,OAAA,GAAU,EAAE;MAElB,MAAMC,KAAA,GAAQ,KAAKhF,UAAA;MAGnB,MAAMiF,aAAA,GAAgBZ,SAAA,GAAYA,SAAA;MAClC,SAAS7C,CAAA,GAAI,GAAGA,CAAA,GAAIyD,aAAA,EAAezD,CAAA,IAAK,KAAKF,QAAA,CAASE,CAAC,IAAI;MAE3D,MAAM0D,EAAA,GAAK,KAAKtE,cAAA;MAChB,MAAMuE,EAAA,GAAK,KAAKtE,cAAA;MAChB,MAAMuE,EAAA,GAAK,KAAKrE,cAAA;MAChB,MAAMsE,EAAA,GAAK,KAAKrE,cAAA;MAGhB,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAI+C,QAAA,GAAW,GAAG/C,CAAA,IAAK;QACrC,MAAM8D,EAAA,GAAKZ,cAAA,CAAelD,CAAA,EAAG,CAAC;QAC9B,MAAM+D,EAAA,GAAKb,cAAA,CAAelD,CAAA,EAAG,CAAC;QAC9B,MAAMgE,EAAA,GAAKd,cAAA,CAAelD,CAAA,EAAG,CAAC;QAG9B4D,EAAA,CAAGK,GAAA,CAAIrB,OAAA,CAAQkB,EAAE,GAAGlB,OAAA,CAAQkB,EAAE,IAAI,GAAGlB,OAAA,CAAQkB,EAAE,IAAI,CAAC;QAEpD,SAASI,CAAA,GAAIlE,CAAA,GAAI,GAAGkE,CAAA,GAAInB,QAAA,EAAUmB,CAAA,IAAK;UACrC,MAAMC,EAAA,GAAKjB,cAAA,CAAegB,CAAA,EAAG,CAAC;UAC9B,MAAME,EAAA,GAAKlB,cAAA,CAAegB,CAAA,EAAG,CAAC;UAC9B,MAAMG,EAAA,GAAKnB,cAAA,CAAegB,CAAA,EAAG,CAAC;UAG9BL,EAAA,CAAGI,GAAA,CAAIrB,OAAA,CAAQuB,EAAE,GAAGvB,OAAA,CAAQuB,EAAE,IAAI,GAAGvB,OAAA,CAAQuB,EAAE,IAAI,CAAC;UAEpD,MAAMG,QAAA,GAAW,IAAIV,EAAA,CAAGW,GAAA,CAAIV,EAAE,IAAIL,KAAA;UAElC,IAAIc,QAAA,EAAU;YACZ,IAAIR,EAAA,KAAOK,EAAA,IAAML,EAAA,KAAOM,EAAA,IAAMN,EAAA,KAAOO,EAAA,EAAI;cACvC,IAAIN,EAAA,KAAOI,EAAA,IAAMJ,EAAA,KAAOK,EAAA,IAAML,EAAA,KAAOM,EAAA,EAAI;gBACvC,KAAKvE,QAAA,CAASgE,EAAA,GAAKjB,SAAA,GAAYkB,EAAE,IAAI;gBACrC,KAAKjE,QAAA,CAASiE,EAAA,GAAKlB,SAAA,GAAYiB,EAAE,IAAI;cACrD,OAAqB;gBACL,KAAKhE,QAAA,CAASkE,EAAA,GAAKnB,SAAA,GAAYiB,EAAE,IAAI;gBACrC,KAAKhE,QAAA,CAASgE,EAAA,GAAKjB,SAAA,GAAYmB,EAAE,IAAI;cACtC;YACf,WAAuBD,EAAA,KAAOI,EAAA,IAAMJ,EAAA,KAAOK,EAAA,IAAML,EAAA,KAAOM,EAAA,EAAI;cAC9C,KAAKvE,QAAA,CAASkE,EAAA,GAAKnB,SAAA,GAAYkB,EAAE,IAAI;cACrC,KAAKjE,QAAA,CAASiE,EAAA,GAAKlB,SAAA,GAAYmB,EAAE,IAAI;YACtC;UACF;QACF;MACF;MAGD,MAAMQ,UAAA,GAAa,KAAK1F,aAAA;MACxBoB,MAAA,CAAOuE,YAAA,CAAc;MACrBpG,oBAAA,CAAoBqG,0BAAA,CAA2BpC,KAAA,EAAOpC,MAAA,CAAOyE,MAAA,EAAQH,UAAU;MAG/E,SAASxE,CAAA,GAAI,GAAGA,CAAA,GAAI+C,QAAA,EAAU/C,CAAA,IAAK;QACjC,MAAM4E,EAAA,GAAK1B,cAAA,CAAelD,CAAA,EAAG,CAAC;QAC9B,MAAM6E,EAAA,GAAK3B,cAAA,CAAelD,CAAA,EAAG,CAAC;QAC9B,MAAM8E,EAAA,GAAK5B,cAAA,CAAelD,CAAA,EAAG,CAAC;QAE9B,SAAS+E,OAAA,GAAU,GAAGA,OAAA,GAAU,GAAGA,OAAA,IAAW;UAC5C,MAAMC,EAAA,GAAKD,OAAA,KAAY,IAAIH,EAAA,GAAKG,OAAA,KAAY,IAAIF,EAAA,GAAKC,EAAA;UACrD,MAAMG,EAAA,GAAKF,OAAA,KAAY,IAAIF,EAAA,GAAKE,OAAA,KAAY,IAAID,EAAA,GAAKF,EAAA;UAErD,MAAMM,YAAA,GAAe,KAAKpF,QAAA,CAASkF,EAAA,GAAKnC,SAAA,GAAYoC,EAAE;UAEtD,IAAIC,YAAA,EAAc;UAGlB,KAAKpF,QAAA,CAASkF,EAAA,GAAKnC,SAAA,GAAYoC,EAAE,IAAI;UACrC,KAAKnF,QAAA,CAASmF,EAAA,GAAKpC,SAAA,GAAYmC,EAAE,IAAI;UAErCtB,EAAA,CAAGO,GAAA,CAAIxB,MAAA,CAAO,IAAIuC,EAAE,GAAGvC,MAAA,CAAO,IAAIuC,EAAA,GAAK,CAAC,GAAGvC,MAAA,CAAO,IAAIuC,EAAA,GAAK,CAAC,CAAC;UAC7DrB,EAAA,CAAGM,GAAA,CAAIxB,MAAA,CAAO,IAAIwC,EAAE,GAAGxC,MAAA,CAAO,IAAIwC,EAAA,GAAK,CAAC,GAAGxC,MAAA,CAAO,IAAIwC,EAAA,GAAK,CAAC,CAAC;UAG7D,IAAIE,KAAA,GAAQ;UAEZ,IAAIC,CAAA,GAAIZ,UAAA,CAAWa,eAAA,CAAgB3B,EAAE;UAErC,IAAI0B,CAAA,GAAI5B,KAAA,EAAO;YACb2B,KAAA,GAAQ;YACR5B,OAAA,CAAQ5B,IAAA,CAAK+B,EAAA,CAAGlD,KAAA,EAAO;UACnC,WAAqB4E,CAAA,GAAI,CAAC5B,KAAA,EAAO;YACrB2B,KAAA,GAAQ;YACR7B,OAAA,CAAQ3B,IAAA,CAAK+B,EAAA,CAAGlD,KAAA,EAAO;UACnC,OAAiB;YACL2E,KAAA,GAAQ;YACR7B,OAAA,CAAQ3B,IAAA,CAAK+B,EAAA,CAAGlD,KAAA,EAAO;YACvB+C,OAAA,CAAQ5B,IAAA,CAAK+B,EAAA,CAAGlD,KAAA,EAAO;UACxB;UAGD,IAAI8E,KAAA,GAAQ;UAEZF,CAAA,GAAIZ,UAAA,CAAWa,eAAA,CAAgB1B,EAAE;UAEjC,IAAIyB,CAAA,GAAI5B,KAAA,EAAO;YACb8B,KAAA,GAAQ;YACR/B,OAAA,CAAQ5B,IAAA,CAAKgC,EAAA,CAAGnD,KAAA,EAAO;UACnC,WAAqB4E,CAAA,GAAI,CAAC5B,KAAA,EAAO;YACrB8B,KAAA,GAAQ;YACRhC,OAAA,CAAQ3B,IAAA,CAAKgC,EAAA,CAAGnD,KAAA,EAAO;UACnC,OAAiB;YACL8E,KAAA,GAAQ;YACRhC,OAAA,CAAQ3B,IAAA,CAAKgC,EAAA,CAAGnD,KAAA,EAAO;YACvB+C,OAAA,CAAQ5B,IAAA,CAAKgC,EAAA,CAAGnD,KAAA,EAAO;UACxB;UAED,IAAK2E,KAAA,KAAU,KAAKG,KAAA,KAAU,KAAOH,KAAA,KAAU,KAAKG,KAAA,KAAU,GAAI;YAGhE,KAAK7G,SAAA,CAAU8G,KAAA,CAAMzD,IAAA,CAAK4B,EAAE;YAC5B,KAAKjF,SAAA,CAAU+G,GAAA,CAAI1D,IAAA,CAAK6B,EAAE;YAE1B,IAAI8B,YAAA,GAAe,IAAItH,OAAA,CAAS;YAChCsH,YAAA,GAAejB,UAAA,CAAWkB,aAAA,CAAc,KAAKjH,SAAA,EAAWgH,YAAY;YAEpE,IAAIA,YAAA,KAAiB,MAAM;cAEzBE,OAAA,CAAQC,KAAA,CAAM,mDAAmD;cACjErD,MAAA,CAAOsD,gBAAA,GAAmB;cAC1BtD,MAAA,CAAOuD,gBAAA,GAAmB;cAC1B,OAAO;YACR;YAEDxC,OAAA,CAAQ3B,IAAA,CAAK8D,YAAY;YACzBlC,OAAA,CAAQ5B,IAAA,CAAK8D,YAAA,CAAajF,KAAA,EAAO;UAClC;QACF;MACF;MAGD,MAAMuF,OAAA,GAAU7F,MAAA,CAAOK,QAAA,CAASJ,IAAA,GAAO;MAGvC,KAAKpB,OAAA,CAAQkF,GAAA,CAAI,GAAG,GAAG,CAAC;MACxB,IAAI+B,OAAA,GAAU;MACd,MAAMC,UAAA,GAAa3C,OAAA,CAAQR,MAAA;MAE3B,IAAImD,UAAA,GAAa,GAAG;QAClB,SAASjG,CAAA,GAAI,GAAGA,CAAA,GAAIiG,UAAA,EAAYjG,CAAA,IAAK,KAAKjB,OAAA,CAAQmD,GAAA,CAAIoB,OAAA,CAAQtD,CAAC,CAAC;QAEhE,KAAKjB,OAAA,CAAQmH,YAAA,CAAaD,UAAU;QACpC,SAASjG,CAAA,GAAI,GAAGA,CAAA,GAAIiG,UAAA,EAAYjG,CAAA,IAAK;UACnC,MAAMmG,CAAA,GAAI7C,OAAA,CAAQtD,CAAC;UACnBmG,CAAA,CAAEnE,GAAA,CAAI,KAAKjD,OAAO;UAClBiH,OAAA,GAAUvE,IAAA,CAAK2E,GAAA,CAAIJ,OAAA,EAASG,CAAA,CAAEE,CAAA,EAAGF,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAC;QAC1C;QAED,KAAKxH,OAAA,CAAQmD,GAAA,CAAIhC,MAAA,CAAOe,QAAQ;MACjC;MAED,KAAKjC,OAAA,CAAQiF,GAAA,CAAI,GAAG,GAAG,CAAC;MACxB,IAAIuC,OAAA,GAAU;MACd,MAAMC,UAAA,GAAalD,OAAA,CAAQT,MAAA;MAC3B,IAAI2D,UAAA,GAAa,GAAG;QAClB,SAASzG,CAAA,GAAI,GAAGA,CAAA,GAAIyG,UAAA,EAAYzG,CAAA,IAAK,KAAKhB,OAAA,CAAQkD,GAAA,CAAIqB,OAAA,CAAQvD,CAAC,CAAC;QAEhE,KAAKhB,OAAA,CAAQkH,YAAA,CAAaO,UAAU;QACpC,SAASzG,CAAA,GAAI,GAAGA,CAAA,GAAIyG,UAAA,EAAYzG,CAAA,IAAK;UACnC,MAAMmG,CAAA,GAAI5C,OAAA,CAAQvD,CAAC;UACnBmG,CAAA,CAAEnE,GAAA,CAAI,KAAKhD,OAAO;UAClBwH,OAAA,GAAU/E,IAAA,CAAK2E,GAAA,CAAII,OAAA,EAASL,CAAA,CAAEE,CAAA,EAAGF,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAC;QAC1C;QAED,KAAKvH,OAAA,CAAQkD,GAAA,CAAIhC,MAAA,CAAOe,QAAQ;MACjC;MAED,IAAIrB,OAAA,GAAU;MACd,IAAIC,OAAA,GAAU;MAEd,IAAI6G,UAAA,GAAa;MAEjB,IAAIT,UAAA,GAAa,GAAG;QAClBrG,OAAA,GAAU,IAAI+G,IAAA,CAAK,IAAIC,cAAA,CAAetD,OAAO,GAAGpD,MAAA,CAAO2G,QAAQ;QAC/DjH,OAAA,CAAQqB,QAAA,CAASa,IAAA,CAAK,KAAK/C,OAAO;QAClCa,OAAA,CAAQkH,UAAA,CAAWhF,IAAA,CAAK5B,MAAA,CAAO4G,UAAU;QAEzC,KAAK7G,sBAAA,CACHL,OAAA,EACAmG,OAAA,EACA7F,MAAA,CAAOK,QAAA,CAASH,QAAA,EAChBF,MAAA,CAAOK,QAAA,CAASF,eAAA,EAChB,IAAI2F,OAAA,GAAU,KAAKzH,eACpB;QAEDmI,UAAA;MACD;MAED,IAAID,UAAA,GAAa,GAAG;QAClB5G,OAAA,GAAU,IAAI8G,IAAA,CAAK,IAAIC,cAAA,CAAerD,OAAO,GAAGrD,MAAA,CAAO2G,QAAQ;QAC/DhH,OAAA,CAAQoB,QAAA,CAASa,IAAA,CAAK,KAAK9C,OAAO;QAClCa,OAAA,CAAQiH,UAAA,CAAWhF,IAAA,CAAK5B,MAAA,CAAO4G,UAAU;QAEzC,KAAK7G,sBAAA,CACHJ,OAAA,EACAkG,OAAA,EACA7F,MAAA,CAAOK,QAAA,CAASH,QAAA,EAChBF,MAAA,CAAOK,QAAA,CAASF,eAAA,EAChB,IAAImG,OAAA,GAAU,KAAKjI,eACpB;QAEDmI,UAAA;MACD;MAEDnE,MAAA,CAAO3C,OAAA,GAAUA,OAAA;MACjB2C,MAAA,CAAO1C,OAAA,GAAUA,OAAA;MAEjB,OAAO6G,UAAA;IACR;IAED,OAAOK,oBAAoBC,CAAA,EAAGC,CAAA,EAAG;MAK/B,MAAMZ,CAAA,GAAIW,CAAA,CAAEX,CAAA;QACVC,CAAA,GAAIU,CAAA,CAAEV,CAAA;QACNC,CAAA,GAAIS,CAAA,CAAET,CAAA;MACR,MAAMW,CAAA,GAAID,CAAA,CAAEE,QAAA;MAEZH,CAAA,CAAEX,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA;MACnCS,CAAA,CAAEV,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA;MACnCS,CAAA,CAAET,CAAA,GAAIW,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,EAAE,IAAIX,CAAA;MAEpC,OAAOS,CAAA;IACR;IAED,OAAOI,2BAA2BJ,CAAA,EAAGC,CAAA,EAAG;MAKtC,MAAMZ,CAAA,GAAIW,CAAA,CAAEX,CAAA;QACVC,CAAA,GAAIU,CAAA,CAAEV,CAAA;QACNC,CAAA,GAAIS,CAAA,CAAET,CAAA;MACR,MAAMW,CAAA,GAAID,CAAA,CAAEE,QAAA;MAEZH,CAAA,CAAEX,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA;MACnCS,CAAA,CAAEV,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA;MACnCS,CAAA,CAAET,CAAA,GAAIW,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,EAAE,IAAIX,CAAA;MAEpC,OAAOS,CAAA;IACR;IAED,OAAOK,2BAA2BL,CAAA,EAAGC,CAAA,EAAG;MAKtC,MAAMZ,CAAA,GAAIW,CAAA,CAAEX,CAAA;QACVC,CAAA,GAAIU,CAAA,CAAEV,CAAA;QACNC,CAAA,GAAIS,CAAA,CAAET,CAAA;MACR,MAAMW,CAAA,GAAID,CAAA,CAAEE,QAAA;MAEZH,CAAA,CAAEX,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA,GAAIW,CAAA,CAAE,EAAE;MAC3CF,CAAA,CAAEV,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,CAAC,IAAIX,CAAA,GAAIW,CAAA,CAAE,EAAE;MAC3CF,CAAA,CAAET,CAAA,GAAIW,CAAA,CAAE,CAAC,IAAIb,CAAA,GAAIa,CAAA,CAAE,CAAC,IAAIZ,CAAA,GAAIY,CAAA,CAAE,EAAE,IAAIX,CAAA,GAAIW,CAAA,CAAE,EAAE;MAE5C,OAAOF,CAAA;IACR;IAED,OAAOtC,2BAA2BpC,KAAA,EAAO2E,CAAA,EAAGK,WAAA,EAAa;MACvDA,WAAA,CAAY3G,MAAA,CAAOmB,IAAA,CAAKQ,KAAA,CAAM3B,MAAM;MACpC2G,WAAA,CAAYvF,QAAA,GAAWO,KAAA,CAAMP,QAAA;MAE7B,MAAMwF,cAAA,GAAiBlJ,oBAAA,CAAoBgJ,0BAAA,CAA2B/E,KAAA,CAAMkF,aAAA,CAActJ,GAAG,GAAG+I,CAAC;MAEjG5I,oBAAA,CAAoB+I,0BAAA,CAA2BE,WAAA,CAAY3G,MAAA,EAAQsG,CAAC;MAGpEK,WAAA,CAAYvF,QAAA,GAAW,CAACwF,cAAA,CAAehD,GAAA,CAAI+C,WAAA,CAAY3G,MAAM;IAC9D;EACF;EAED,OAAOtC,oBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}