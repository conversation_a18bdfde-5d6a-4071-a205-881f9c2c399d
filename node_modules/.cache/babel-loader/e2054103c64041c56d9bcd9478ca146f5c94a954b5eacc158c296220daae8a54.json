{"ast": null, "code": "/**\n *\n */\nfunction zero() {\n  return [0, 0, 0];\n}\nfunction one() {\n  return [1, 1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n, a[2] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n, a[2] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n, a[2] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cross(a, b) {\n  var x = a[1] * b[2] - a[2] * b[1];\n  var y = a[2] * b[0] - a[0] * b[2];\n  var z = a[0] * b[1] - a[1] * b[0];\n  return [x, y, z];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1] + a[2] * a[2];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]) + (a[2] - b[2]) * (a[2] - b[2]));\n}\nvar vector3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  cross: cross,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, cross as f, length as g, distance as h, lengthSqr as l, one as o, sub as s, vector3 as v, zero as z };", "map": {"version": 3, "names": ["zero", "one", "add", "a", "b", "addValue", "n", "sub", "subValue", "scale", "dot", "cross", "x", "y", "z", "lengthSqr", "length", "Math", "sqrt", "distance", "vector3", "Object", "freeze", "__proto__", "c", "d", "e", "f", "g", "h", "l", "o", "s", "v"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/maath/dist/vector3-0a088b7f.esm.js"], "sourcesContent": ["/**\n *\n */\nfunction zero() {\n  return [0, 0, 0];\n}\nfunction one() {\n  return [1, 1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n, a[2] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n, a[2] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n, a[2] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cross(a, b) {\n  var x = a[1] * b[2] - a[2] * b[1];\n  var y = a[2] * b[0] - a[0] * b[2];\n  var z = a[0] * b[1] - a[1] * b[0];\n  return [x, y, z];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1] + a[2] * a[2];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]) + (a[2] - b[2]) * (a[2] - b[2]));\n}\n\nvar vector3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  cross: cross,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, cross as f, length as g, distance as h, lengthSqr as l, one as o, sub as s, vector3 as v, zero as z };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,IAAIA,CAAA,EAAG;EACd,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB;AACA,SAASC,GAAGA,CAAA,EAAG;EACb,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB;AACA,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD;AACA,SAASC,QAAQA,CAACF,CAAC,EAAEG,CAAC,EAAE;EACtB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AACvC;AACA,SAASC,GAAGA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD;AACA,SAASI,QAAQA,CAACL,CAAC,EAAEG,CAAC,EAAE;EACtB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AACvC;AACA,SAASG,KAAKA,CAACN,CAAC,EAAEG,CAAC,EAAE;EACnB,OAAO,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC;AACvC;AACA,SAASI,GAAGA,CAACP,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;AAChD;AACA,SAASO,KAAKA,CAACR,CAAC,EAAEC,CAAC,EAAE;EACnB,IAAIQ,CAAC,GAAGT,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIS,CAAC,GAAGV,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIU,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACjC,OAAO,CAACQ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAClB;AACA;AACA;AACA;AACA;;AAEA,SAASC,SAASA,CAACZ,CAAC,EAAE;EACpB,OAAOA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;;AAEA,SAASa,MAAMA,CAACb,CAAC,EAAE;EACjB,OAAOc,IAAI,CAACC,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA,SAASgB,QAAQA,CAAChB,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOa,IAAI,CAACC,IAAI,CAAC,CAACf,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjH;AAEA,IAAIgB,OAAO,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EACvCC,SAAS,EAAE,IAAI;EACfvB,IAAI,EAAEA,IAAI;EACVC,GAAG,EAAEA,GAAG;EACRC,GAAG,EAAEA,GAAG;EACRG,QAAQ,EAAEA,QAAQ;EAClBE,GAAG,EAAEA,GAAG;EACRC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZC,GAAG,EAAEA,GAAG;EACRC,KAAK,EAAEA,KAAK;EACZI,SAAS,EAAEA,SAAS;EACpBC,MAAM,EAAEA,MAAM;EACdG,QAAQ,EAAEA;AACZ,CAAC,CAAC;AAEF,SAASjB,GAAG,IAAIC,CAAC,EAAEE,QAAQ,IAAID,CAAC,EAAEI,QAAQ,IAAIgB,CAAC,EAAEf,KAAK,IAAIgB,CAAC,EAAEf,GAAG,IAAIgB,CAAC,EAAEf,KAAK,IAAIgB,CAAC,EAAEX,MAAM,IAAIY,CAAC,EAAET,QAAQ,IAAIU,CAAC,EAAEd,SAAS,IAAIe,CAAC,EAAE7B,GAAG,IAAI8B,CAAC,EAAExB,GAAG,IAAIyB,CAAC,EAAEZ,OAAO,IAAIa,CAAC,EAAEjC,IAAI,IAAIc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}