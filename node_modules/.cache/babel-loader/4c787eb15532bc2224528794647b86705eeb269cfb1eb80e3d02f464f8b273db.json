{"ast": null, "code": "import { FontLoader } from 'three-stdlib';\nimport { suspend, preload, clear } from 'suspend-react';\nlet fontLoader = null;\nasync function loadFontData(font) {\n  return typeof font === 'string' ? await (await fetch(font)).json() : font;\n}\nfunction parseFontData(fontData) {\n  if (!fontLoader) {\n    fontLoader = new FontLoader();\n  }\n  return fontLoader.parse(fontData);\n}\nasync function loader(font) {\n  const data = await loadFontData(font);\n  return parseFontData(data);\n}\nfunction useFont(font) {\n  return suspend(loader, [font]);\n}\nuseFont.preload = font => preload(loader, [font]);\nuseFont.clear = font => clear([font]);\nexport { useFont };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "suspend", "preload", "clear", "fontLoader", "loadFontData", "font", "fetch", "json", "parseFontData", "fontData", "parse", "loader", "data", "useFont"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/useFont.js"], "sourcesContent": ["import { FontLoader } from 'three-stdlib';\nimport { suspend, preload, clear } from 'suspend-react';\n\nlet fontLoader = null;\nasync function loadFontData(font) {\n  return typeof font === 'string' ? await (await fetch(font)).json() : font;\n}\nfunction parseFontData(fontData) {\n  if (!fontLoader) {\n    fontLoader = new FontLoader();\n  }\n  return fontLoader.parse(fontData);\n}\nasync function loader(font) {\n  const data = await loadFontData(font);\n  return parseFontData(data);\n}\nfunction useFont(font) {\n  return suspend(loader, [font]);\n}\nuseFont.preload = font => preload(loader, [font]);\nuseFont.clear = font => clear([font]);\n\nexport { useFont };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AAEvD,IAAIC,UAAU,GAAG,IAAI;AACrB,eAAeC,YAAYA,CAACC,IAAI,EAAE;EAChC,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAG,MAAM,CAAC,MAAMC,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,CAAC,CAAC,GAAGF,IAAI;AAC3E;AACA,SAASG,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAI,CAACN,UAAU,EAAE;IACfA,UAAU,GAAG,IAAIJ,UAAU,CAAC,CAAC;EAC/B;EACA,OAAOI,UAAU,CAACO,KAAK,CAACD,QAAQ,CAAC;AACnC;AACA,eAAeE,MAAMA,CAACN,IAAI,EAAE;EAC1B,MAAMO,IAAI,GAAG,MAAMR,YAAY,CAACC,IAAI,CAAC;EACrC,OAAOG,aAAa,CAACI,IAAI,CAAC;AAC5B;AACA,SAASC,OAAOA,CAACR,IAAI,EAAE;EACrB,OAAOL,OAAO,CAACW,MAAM,EAAE,CAACN,IAAI,CAAC,CAAC;AAChC;AACAQ,OAAO,CAACZ,OAAO,GAAGI,IAAI,IAAIJ,OAAO,CAACU,MAAM,EAAE,CAACN,IAAI,CAAC,CAAC;AACjDQ,OAAO,CAACX,KAAK,GAAGG,IAAI,IAAIH,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;AAErC,SAASQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}