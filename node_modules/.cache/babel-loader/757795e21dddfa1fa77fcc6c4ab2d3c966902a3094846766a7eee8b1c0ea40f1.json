{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useThree, extend, applyProps } from '@react-three/fiber';\nimport { toCreasedNormals } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\nconst OutlinesMaterial = /* @__PURE__ */shaderMaterial({\n  screenspace: false,\n  color: /* @__PURE__ */new THREE.Color('black'),\n  opacity: 1,\n  thickness: 0.05,\n  size: /* @__PURE__ */new THREE.Vector2()\n}, `#include <common>\n   #include <morphtarget_pars_vertex>\n   #include <skinning_pars_vertex>\n   #include <clipping_planes_pars_vertex>\n   uniform float thickness;\n   uniform bool screenspace;\n   uniform vec2 size;\n   void main() {\n     #if defined (USE_SKINNING)\n\t     #include <beginnormal_vertex>\n       #include <morphnormal_vertex>\n       #include <skinbase_vertex>\n       #include <skinnormal_vertex>\n       #include <defaultnormal_vertex>\n     #endif\n     #include <begin_vertex>\n\t   #include <morphtarget_vertex>\n\t   #include <skinning_vertex>\n     #include <project_vertex>\n     #include <clipping_planes_vertex>\n     vec4 tNormal = vec4(normal, 0.0);\n     vec4 tPosition = vec4(transformed, 1.0);\n     #ifdef USE_INSTANCING\n       tNormal = instanceMatrix * tNormal;\n       tPosition = instanceMatrix * tPosition;\n     #endif\n     if (screenspace) {\n       vec3 newPosition = tPosition.xyz + tNormal.xyz * thickness;\n       gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0); \n     } else {\n       vec4 clipPosition = projectionMatrix * modelViewMatrix * tPosition;\n       vec4 clipNormal = projectionMatrix * modelViewMatrix * tNormal;\n       vec2 offset = normalize(clipNormal.xy) * thickness / size * clipPosition.w * 2.0;\n       clipPosition.xy += offset;\n       gl_Position = clipPosition;\n     }\n   }`, `uniform vec3 color;\n   uniform float opacity;\n   #include <clipping_planes_pars_fragment>\n   void main(){\n     #include <clipping_planes_fragment>\n     gl_FragColor = vec4(color, opacity);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nfunction Outlines({\n  color = 'black',\n  opacity = 1,\n  transparent = false,\n  screenspace = false,\n  toneMapped = true,\n  polygonOffset = false,\n  polygonOffsetFactor = 0,\n  renderOrder = 0,\n  thickness = 0.05,\n  angle = Math.PI,\n  clippingPlanes,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  const [material] = React.useState(() => new OutlinesMaterial({\n    side: THREE.BackSide\n  }));\n  const {\n    gl\n  } = useThree();\n  const contextSize = gl.getDrawingBufferSize(new THREE.Vector2());\n  React.useMemo(() => extend({\n    OutlinesMaterial\n  }), []);\n  const oldAngle = React.useRef(0);\n  const oldGeometry = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const group = ref.current;\n    if (!group) return;\n    const parent = group.parent;\n    if (parent && parent.geometry) {\n      if (oldAngle.current !== angle || oldGeometry.current !== parent.geometry) {\n        var _group$children;\n        oldAngle.current = angle;\n        oldGeometry.current = parent.geometry;\n\n        // Remove old mesh\n        let mesh = (_group$children = group.children) == null ? void 0 : _group$children[0];\n        if (mesh) {\n          if (angle) mesh.geometry.dispose();\n          group.remove(mesh);\n        }\n        if (parent.skeleton) {\n          mesh = new THREE.SkinnedMesh();\n          mesh.material = material;\n          mesh.bind(parent.skeleton, parent.bindMatrix);\n          group.add(mesh);\n        } else if (parent.isInstancedMesh) {\n          mesh = new THREE.InstancedMesh(parent.geometry, material, parent.count);\n          mesh.instanceMatrix = parent.instanceMatrix;\n          group.add(mesh);\n        } else {\n          mesh = new THREE.Mesh();\n          mesh.material = material;\n          group.add(mesh);\n        }\n        mesh.geometry = angle ? toCreasedNormals(parent.geometry, angle) : parent.geometry;\n        mesh.morphTargetInfluences = parent.morphTargetInfluences;\n        mesh.morphTargetDictionary = parent.morphTargetDictionary;\n      }\n    }\n  });\n  React.useLayoutEffect(() => {\n    const group = ref.current;\n    if (!group) return;\n    const mesh = group.children[0];\n    if (mesh) {\n      mesh.renderOrder = renderOrder;\n      const parent = group.parent;\n      applyProps(mesh, {\n        morphTargetInfluences: parent.morphTargetInfluences,\n        morphTargetDictionary: parent.morphTargetDictionary\n      });\n      applyProps(mesh.material, {\n        transparent,\n        thickness,\n        color,\n        opacity,\n        size: contextSize,\n        screenspace,\n        toneMapped,\n        polygonOffset,\n        polygonOffsetFactor,\n        clippingPlanes,\n        clipping: clippingPlanes && clippingPlanes.length > 0\n      });\n    }\n  });\n  React.useEffect(() => {\n    return () => {\n      // Dispose everything on unmount\n      const group = ref.current;\n      if (!group) return;\n      const mesh = group.children[0];\n      if (mesh) {\n        if (angle) mesh.geometry.dispose();\n        group.remove(mesh);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n}\nexport { Outlines };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "shaderMaterial", "useThree", "extend", "applyProps", "toCreasedNormals", "version", "OutlinesMaterial", "screenspace", "color", "Color", "opacity", "thickness", "size", "Vector2", "Outlines", "transparent", "toneMapped", "polygonOffset", "polygonOffsetFactor", "renderOrder", "angle", "Math", "PI", "clippingPlanes", "props", "ref", "useRef", "material", "useState", "side", "BackSide", "gl", "contextSize", "getDrawingBufferSize", "useMemo", "oldAngle", "oldGeometry", "useLayoutEffect", "group", "current", "parent", "geometry", "_group$children", "mesh", "children", "dispose", "remove", "skeleton", "<PERSON><PERSON><PERSON><PERSON>", "bind", "bindMatrix", "add", "isInstancedMesh", "In<PERSON>d<PERSON>esh", "count", "instanceMatrix", "<PERSON><PERSON>", "morphTargetInfluences", "morphTargetDictionary", "clipping", "length", "useEffect", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Outlines.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useThree, extend, applyProps } from '@react-three/fiber';\nimport { toCreasedNormals } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\n\nconst OutlinesMaterial = /* @__PURE__ */shaderMaterial({\n  screenspace: false,\n  color: /* @__PURE__ */new THREE.Color('black'),\n  opacity: 1,\n  thickness: 0.05,\n  size: /* @__PURE__ */new THREE.Vector2()\n}, `#include <common>\n   #include <morphtarget_pars_vertex>\n   #include <skinning_pars_vertex>\n   #include <clipping_planes_pars_vertex>\n   uniform float thickness;\n   uniform bool screenspace;\n   uniform vec2 size;\n   void main() {\n     #if defined (USE_SKINNING)\n\t     #include <beginnormal_vertex>\n       #include <morphnormal_vertex>\n       #include <skinbase_vertex>\n       #include <skinnormal_vertex>\n       #include <defaultnormal_vertex>\n     #endif\n     #include <begin_vertex>\n\t   #include <morphtarget_vertex>\n\t   #include <skinning_vertex>\n     #include <project_vertex>\n     #include <clipping_planes_vertex>\n     vec4 tNormal = vec4(normal, 0.0);\n     vec4 tPosition = vec4(transformed, 1.0);\n     #ifdef USE_INSTANCING\n       tNormal = instanceMatrix * tNormal;\n       tPosition = instanceMatrix * tPosition;\n     #endif\n     if (screenspace) {\n       vec3 newPosition = tPosition.xyz + tNormal.xyz * thickness;\n       gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0); \n     } else {\n       vec4 clipPosition = projectionMatrix * modelViewMatrix * tPosition;\n       vec4 clipNormal = projectionMatrix * modelViewMatrix * tNormal;\n       vec2 offset = normalize(clipNormal.xy) * thickness / size * clipPosition.w * 2.0;\n       clipPosition.xy += offset;\n       gl_Position = clipPosition;\n     }\n   }`, `uniform vec3 color;\n   uniform float opacity;\n   #include <clipping_planes_pars_fragment>\n   void main(){\n     #include <clipping_planes_fragment>\n     gl_FragColor = vec4(color, opacity);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nfunction Outlines({\n  color = 'black',\n  opacity = 1,\n  transparent = false,\n  screenspace = false,\n  toneMapped = true,\n  polygonOffset = false,\n  polygonOffsetFactor = 0,\n  renderOrder = 0,\n  thickness = 0.05,\n  angle = Math.PI,\n  clippingPlanes,\n  ...props\n}) {\n  const ref = React.useRef(null);\n  const [material] = React.useState(() => new OutlinesMaterial({\n    side: THREE.BackSide\n  }));\n  const {\n    gl\n  } = useThree();\n  const contextSize = gl.getDrawingBufferSize(new THREE.Vector2());\n  React.useMemo(() => extend({\n    OutlinesMaterial\n  }), []);\n  const oldAngle = React.useRef(0);\n  const oldGeometry = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const group = ref.current;\n    if (!group) return;\n    const parent = group.parent;\n    if (parent && parent.geometry) {\n      if (oldAngle.current !== angle || oldGeometry.current !== parent.geometry) {\n        var _group$children;\n        oldAngle.current = angle;\n        oldGeometry.current = parent.geometry;\n\n        // Remove old mesh\n        let mesh = (_group$children = group.children) == null ? void 0 : _group$children[0];\n        if (mesh) {\n          if (angle) mesh.geometry.dispose();\n          group.remove(mesh);\n        }\n        if (parent.skeleton) {\n          mesh = new THREE.SkinnedMesh();\n          mesh.material = material;\n          mesh.bind(parent.skeleton, parent.bindMatrix);\n          group.add(mesh);\n        } else if (parent.isInstancedMesh) {\n          mesh = new THREE.InstancedMesh(parent.geometry, material, parent.count);\n          mesh.instanceMatrix = parent.instanceMatrix;\n          group.add(mesh);\n        } else {\n          mesh = new THREE.Mesh();\n          mesh.material = material;\n          group.add(mesh);\n        }\n        mesh.geometry = angle ? toCreasedNormals(parent.geometry, angle) : parent.geometry;\n        mesh.morphTargetInfluences = parent.morphTargetInfluences;\n        mesh.morphTargetDictionary = parent.morphTargetDictionary;\n      }\n    }\n  });\n  React.useLayoutEffect(() => {\n    const group = ref.current;\n    if (!group) return;\n    const mesh = group.children[0];\n    if (mesh) {\n      mesh.renderOrder = renderOrder;\n      const parent = group.parent;\n      applyProps(mesh, {\n        morphTargetInfluences: parent.morphTargetInfluences,\n        morphTargetDictionary: parent.morphTargetDictionary\n      });\n      applyProps(mesh.material, {\n        transparent,\n        thickness,\n        color,\n        opacity,\n        size: contextSize,\n        screenspace,\n        toneMapped,\n        polygonOffset,\n        polygonOffsetFactor,\n        clippingPlanes,\n        clipping: clippingPlanes && clippingPlanes.length > 0\n      });\n    }\n  });\n  React.useEffect(() => {\n    return () => {\n      // Dispose everything on unmount\n      const group = ref.current;\n      if (!group) return;\n      const mesh = group.children[0];\n      if (mesh) {\n        if (angle) mesh.geometry.dispose();\n        group.remove(mesh);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n}\n\nexport { Outlines };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,QAAQ,oBAAoB;AACjE,SAASC,gBAAgB,QAAQ,cAAc;AAC/C,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,gBAAgB,GAAG,eAAeN,cAAc,CAAC;EACrDO,WAAW,EAAE,KAAK;EAClBC,KAAK,EAAE,eAAe,IAAIV,KAAK,CAACW,KAAK,CAAC,OAAO,CAAC;EAC9CC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,eAAe,IAAId,KAAK,CAACe,OAAO,CAAC;AACzC,CAAC,EAAE;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBR,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC9E,KAAK,CAAC;AACN,SAASS,QAAQA,CAAC;EAChBN,KAAK,GAAG,OAAO;EACfE,OAAO,GAAG,CAAC;EACXK,WAAW,GAAG,KAAK;EACnBR,WAAW,GAAG,KAAK;EACnBS,UAAU,GAAG,IAAI;EACjBC,aAAa,GAAG,KAAK;EACrBC,mBAAmB,GAAG,CAAC;EACvBC,WAAW,GAAG,CAAC;EACfR,SAAS,GAAG,IAAI;EAChBS,KAAK,GAAGC,IAAI,CAACC,EAAE;EACfC,cAAc;EACd,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,GAAG,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACC,QAAQ,CAAC,GAAG5B,KAAK,CAAC6B,QAAQ,CAAC,MAAM,IAAItB,gBAAgB,CAAC;IAC3DuB,IAAI,EAAE/B,KAAK,CAACgC;EACd,CAAC,CAAC,CAAC;EACH,MAAM;IACJC;EACF,CAAC,GAAG9B,QAAQ,CAAC,CAAC;EACd,MAAM+B,WAAW,GAAGD,EAAE,CAACE,oBAAoB,CAAC,IAAInC,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;EAChEd,KAAK,CAACmC,OAAO,CAAC,MAAMhC,MAAM,CAAC;IACzBI;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM6B,QAAQ,GAAGpC,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC;EAChC,MAAMU,WAAW,GAAGrC,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EACtC3B,KAAK,CAACsC,eAAe,CAAC,MAAM;IAC1B,MAAMC,KAAK,GAAGb,GAAG,CAACc,OAAO;IACzB,IAAI,CAACD,KAAK,EAAE;IACZ,MAAME,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC3B,IAAIA,MAAM,IAAIA,MAAM,CAACC,QAAQ,EAAE;MAC7B,IAAIN,QAAQ,CAACI,OAAO,KAAKnB,KAAK,IAAIgB,WAAW,CAACG,OAAO,KAAKC,MAAM,CAACC,QAAQ,EAAE;QACzE,IAAIC,eAAe;QACnBP,QAAQ,CAACI,OAAO,GAAGnB,KAAK;QACxBgB,WAAW,CAACG,OAAO,GAAGC,MAAM,CAACC,QAAQ;;QAErC;QACA,IAAIE,IAAI,GAAG,CAACD,eAAe,GAAGJ,KAAK,CAACM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,eAAe,CAAC,CAAC,CAAC;QACnF,IAAIC,IAAI,EAAE;UACR,IAAIvB,KAAK,EAAEuB,IAAI,CAACF,QAAQ,CAACI,OAAO,CAAC,CAAC;UAClCP,KAAK,CAACQ,MAAM,CAACH,IAAI,CAAC;QACpB;QACA,IAAIH,MAAM,CAACO,QAAQ,EAAE;UACnBJ,IAAI,GAAG,IAAI7C,KAAK,CAACkD,WAAW,CAAC,CAAC;UAC9BL,IAAI,CAAChB,QAAQ,GAAGA,QAAQ;UACxBgB,IAAI,CAACM,IAAI,CAACT,MAAM,CAACO,QAAQ,EAAEP,MAAM,CAACU,UAAU,CAAC;UAC7CZ,KAAK,CAACa,GAAG,CAACR,IAAI,CAAC;QACjB,CAAC,MAAM,IAAIH,MAAM,CAACY,eAAe,EAAE;UACjCT,IAAI,GAAG,IAAI7C,KAAK,CAACuD,aAAa,CAACb,MAAM,CAACC,QAAQ,EAAEd,QAAQ,EAAEa,MAAM,CAACc,KAAK,CAAC;UACvEX,IAAI,CAACY,cAAc,GAAGf,MAAM,CAACe,cAAc;UAC3CjB,KAAK,CAACa,GAAG,CAACR,IAAI,CAAC;QACjB,CAAC,MAAM;UACLA,IAAI,GAAG,IAAI7C,KAAK,CAAC0D,IAAI,CAAC,CAAC;UACvBb,IAAI,CAAChB,QAAQ,GAAGA,QAAQ;UACxBW,KAAK,CAACa,GAAG,CAACR,IAAI,CAAC;QACjB;QACAA,IAAI,CAACF,QAAQ,GAAGrB,KAAK,GAAGhB,gBAAgB,CAACoC,MAAM,CAACC,QAAQ,EAAErB,KAAK,CAAC,GAAGoB,MAAM,CAACC,QAAQ;QAClFE,IAAI,CAACc,qBAAqB,GAAGjB,MAAM,CAACiB,qBAAqB;QACzDd,IAAI,CAACe,qBAAqB,GAAGlB,MAAM,CAACkB,qBAAqB;MAC3D;IACF;EACF,CAAC,CAAC;EACF3D,KAAK,CAACsC,eAAe,CAAC,MAAM;IAC1B,MAAMC,KAAK,GAAGb,GAAG,CAACc,OAAO;IACzB,IAAI,CAACD,KAAK,EAAE;IACZ,MAAMK,IAAI,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRA,IAAI,CAACxB,WAAW,GAAGA,WAAW;MAC9B,MAAMqB,MAAM,GAAGF,KAAK,CAACE,MAAM;MAC3BrC,UAAU,CAACwC,IAAI,EAAE;QACfc,qBAAqB,EAAEjB,MAAM,CAACiB,qBAAqB;QACnDC,qBAAqB,EAAElB,MAAM,CAACkB;MAChC,CAAC,CAAC;MACFvD,UAAU,CAACwC,IAAI,CAAChB,QAAQ,EAAE;QACxBZ,WAAW;QACXJ,SAAS;QACTH,KAAK;QACLE,OAAO;QACPE,IAAI,EAAEoB,WAAW;QACjBzB,WAAW;QACXS,UAAU;QACVC,aAAa;QACbC,mBAAmB;QACnBK,cAAc;QACdoC,QAAQ,EAAEpC,cAAc,IAAIA,cAAc,CAACqC,MAAM,GAAG;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF7D,KAAK,CAAC8D,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX;MACA,MAAMvB,KAAK,GAAGb,GAAG,CAACc,OAAO;MACzB,IAAI,CAACD,KAAK,EAAE;MACZ,MAAMK,IAAI,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACR,IAAIvB,KAAK,EAAEuB,IAAI,CAACF,QAAQ,CAACI,OAAO,CAAC,CAAC;QAClCP,KAAK,CAACQ,MAAM,CAACH,IAAI,CAAC;MACpB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa5C,KAAK,CAAC+D,aAAa,CAAC,OAAO,EAAEjE,QAAQ,CAAC;IACxD4B,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ;AAEA,SAASV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}