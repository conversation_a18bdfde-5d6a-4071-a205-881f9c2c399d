{"ast": null, "code": "/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition(indirectBuffer, index, triangleBounds, offset, count, split) {\n  let left = offset;\n  let right = offset + count - 1;\n  const pos = split.pos;\n  const axisOffset = split.axis * 2;\n\n  // hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n  while (true) {\n    while (left <= right && triangleBounds[left * 6 + axisOffset] < pos) {\n      left++;\n    }\n\n    // if a triangle center lies on the partition plane it is considered to be on the right side\n    while (left <= right && triangleBounds[right * 6 + axisOffset] >= pos) {\n      right--;\n    }\n    if (left < right) {\n      // we need to swap all of the information associated with the triangles at index\n      // left and right; that's the verts in the geometry index, the bounds,\n      // and perhaps the SAH planes\n\n      for (let i = 0; i < 3; i++) {\n        let t0 = index[left * 3 + i];\n        index[left * 3 + i] = index[right * 3 + i];\n        index[right * 3 + i] = t0;\n      }\n\n      // swap bounds\n      for (let i = 0; i < 6; i++) {\n        let tb = triangleBounds[left * 6 + i];\n        triangleBounds[left * 6 + i] = triangleBounds[right * 6 + i];\n        triangleBounds[right * 6 + i] = tb;\n      }\n      left++;\n      right--;\n    } else {\n      return left;\n    }\n  }\n}\nexport { partition };", "map": {"version": 3, "names": ["partition", "<PERSON><PERSON><PERSON><PERSON>", "index", "triangleBounds", "offset", "count", "split", "left", "right", "pos", "axisOffset", "axis", "i", "t0", "tb"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/three-mesh-bvh/src/core/build/sortUtils.generated.js"], "sourcesContent": ["/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( indirectBuffer, index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t}\n\n\n\t\t\t// swap bounds\n\t\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\t\tlet tb = triangleBounds[ left * 6 + i ];\n\t\t\t\ttriangleBounds[ left * 6 + i ] = triangleBounds[ right * 6 + i ];\n\t\t\t\ttriangleBounds[ right * 6 + i ] = tb;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nexport { partition };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAAEC,cAAc,EAAEC,KAAK,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAG;EAEjF,IAAIC,IAAI,GAAGH,MAAM;EACjB,IAAII,KAAK,GAAGJ,MAAM,GAAGC,KAAK,GAAG,CAAC;EAC9B,MAAMI,GAAG,GAAGH,KAAK,CAACG,GAAG;EACrB,MAAMC,UAAU,GAAGJ,KAAK,CAACK,IAAI,GAAG,CAAC;;EAEjC;EACA,OAAQ,IAAI,EAAG;IAEd,OAAQJ,IAAI,IAAIC,KAAK,IAAIL,cAAc,CAAEI,IAAI,GAAG,CAAC,GAAGG,UAAU,CAAE,GAAGD,GAAG,EAAG;MAExEF,IAAI,EAAG;IAER;;IAEA;IACA,OAAQA,IAAI,IAAIC,KAAK,IAAIL,cAAc,CAAEK,KAAK,GAAG,CAAC,GAAGE,UAAU,CAAE,IAAID,GAAG,EAAG;MAE1ED,KAAK,EAAG;IAET;IAEA,IAAKD,IAAI,GAAGC,KAAK,EAAG;MAEnB;MACA;MACA;;MAEA,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,IAAIC,EAAE,GAAGX,KAAK,CAAEK,IAAI,GAAG,CAAC,GAAGK,CAAC,CAAE;QAC9BV,KAAK,CAAEK,IAAI,GAAG,CAAC,GAAGK,CAAC,CAAE,GAAGV,KAAK,CAAEM,KAAK,GAAG,CAAC,GAAGI,CAAC,CAAE;QAC9CV,KAAK,CAAEM,KAAK,GAAG,CAAC,GAAGI,CAAC,CAAE,GAAGC,EAAE;MAE5B;;MAGA;MACA,KAAM,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,IAAIE,EAAE,GAAGX,cAAc,CAAEI,IAAI,GAAG,CAAC,GAAGK,CAAC,CAAE;QACvCT,cAAc,CAAEI,IAAI,GAAG,CAAC,GAAGK,CAAC,CAAE,GAAGT,cAAc,CAAEK,KAAK,GAAG,CAAC,GAAGI,CAAC,CAAE;QAChET,cAAc,CAAEK,KAAK,GAAG,CAAC,GAAGI,CAAC,CAAE,GAAGE,EAAE;MAErC;MAEAP,IAAI,EAAG;MACPC,KAAK,EAAG;IAET,CAAC,MAAM;MAEN,OAAOD,IAAI;IAEZ;EAED;AAED;AAEA,SAASP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}