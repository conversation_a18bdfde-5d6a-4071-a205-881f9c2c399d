{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useFrame } from '@react-three/fiber';\nconst boundingBox = /* @__PURE__ */new THREE.Box3();\nconst boundingBoxSize = /* @__PURE__ */new THREE.Vector3();\nconst BBAnchor = ({\n  anchor,\n  ...props\n}) => {\n  const ref = React.useRef(null);\n  const parentRef = React.useRef(null);\n\n  // Reattach group created by this component to the parent's parent,\n  // so it becomes a sibling of its initial parent.\n  // We do that so the children have no impact on a bounding box of a parent.\n  React.useEffect(() => {\n    var _ref$current;\n    if ((_ref$current = ref.current) != null && (_ref$current = _ref$current.parent) != null && _ref$current.parent) {\n      parentRef.current = ref.current.parent;\n      ref.current.parent.parent.add(ref.current);\n    }\n  }, []);\n  useFrame(() => {\n    if (parentRef.current) {\n      boundingBox.setFromObject(parentRef.current);\n      boundingBox.getSize(boundingBoxSize);\n      ref.current.position.set(parentRef.current.position.x + boundingBoxSize.x * (Array.isArray(anchor) ? anchor[0] : anchor.x) / 2, parentRef.current.position.y + boundingBoxSize.y * (Array.isArray(anchor) ? anchor[1] : anchor.y) / 2, parentRef.current.position.z + boundingBoxSize.z * (Array.isArray(anchor) ? anchor[2] : anchor.z) / 2);\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n};\nexport { BBAnchor };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "useFrame", "boundingBox", "Box3", "boundingBoxSize", "Vector3", "BBAnchor", "anchor", "props", "ref", "useRef", "parentRef", "useEffect", "_ref$current", "current", "parent", "add", "setFromObject", "getSize", "position", "set", "x", "Array", "isArray", "y", "z", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/BBAnchor.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nconst boundingBox = /* @__PURE__ */new THREE.Box3();\nconst boundingBoxSize = /* @__PURE__ */new THREE.Vector3();\nconst BBAnchor = ({\n  anchor,\n  ...props\n}) => {\n  const ref = React.useRef(null);\n  const parentRef = React.useRef(null);\n\n  // Reattach group created by this component to the parent's parent,\n  // so it becomes a sibling of its initial parent.\n  // We do that so the children have no impact on a bounding box of a parent.\n  React.useEffect(() => {\n    var _ref$current;\n    if ((_ref$current = ref.current) != null && (_ref$current = _ref$current.parent) != null && _ref$current.parent) {\n      parentRef.current = ref.current.parent;\n      ref.current.parent.parent.add(ref.current);\n    }\n  }, []);\n  useFrame(() => {\n    if (parentRef.current) {\n      boundingBox.setFromObject(parentRef.current);\n      boundingBox.getSize(boundingBoxSize);\n      ref.current.position.set(parentRef.current.position.x + boundingBoxSize.x * (Array.isArray(anchor) ? anchor[0] : anchor.x) / 2, parentRef.current.position.y + boundingBoxSize.y * (Array.isArray(anchor) ? anchor[1] : anchor.y) / 2, parentRef.current.position.z + boundingBoxSize.z * (Array.isArray(anchor) ? anchor[2] : anchor.z) / 2);\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n};\n\nexport { BBAnchor };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,WAAW,GAAG,eAAe,IAAIF,KAAK,CAACG,IAAI,CAAC,CAAC;AACnD,MAAMC,eAAe,GAAG,eAAe,IAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC;AAC1D,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,MAAM;EACN,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA;EACA;EACAX,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAIC,YAAY;IAChB,IAAI,CAACA,YAAY,GAAGJ,GAAG,CAACK,OAAO,KAAK,IAAI,IAAI,CAACD,YAAY,GAAGA,YAAY,CAACE,MAAM,KAAK,IAAI,IAAIF,YAAY,CAACE,MAAM,EAAE;MAC/GJ,SAAS,CAACG,OAAO,GAAGL,GAAG,CAACK,OAAO,CAACC,MAAM;MACtCN,GAAG,CAACK,OAAO,CAACC,MAAM,CAACA,MAAM,CAACC,GAAG,CAACP,GAAG,CAACK,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE,EAAE,CAAC;EACNb,QAAQ,CAAC,MAAM;IACb,IAAIU,SAAS,CAACG,OAAO,EAAE;MACrBZ,WAAW,CAACe,aAAa,CAACN,SAAS,CAACG,OAAO,CAAC;MAC5CZ,WAAW,CAACgB,OAAO,CAACd,eAAe,CAAC;MACpCK,GAAG,CAACK,OAAO,CAACK,QAAQ,CAACC,GAAG,CAACT,SAAS,CAACG,OAAO,CAACK,QAAQ,CAACE,CAAC,GAAGjB,eAAe,CAACiB,CAAC,IAAIC,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACc,CAAC,CAAC,GAAG,CAAC,EAAEV,SAAS,CAACG,OAAO,CAACK,QAAQ,CAACK,CAAC,GAAGpB,eAAe,CAACoB,CAAC,IAAIF,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACiB,CAAC,CAAC,GAAG,CAAC,EAAEb,SAAS,CAACG,OAAO,CAACK,QAAQ,CAACM,CAAC,GAAGrB,eAAe,CAACqB,CAAC,IAAIH,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACkB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/U;EACF,CAAC,CAAC;EACF,OAAO,aAAa1B,KAAK,CAAC2B,aAAa,CAAC,OAAO,EAAE5B,QAAQ,CAAC;IACxDW,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}