{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Euler, Quaternion, MathUtils } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nclass DeviceOrientationControls extends EventDispatcher {\n  // radians\n  constructor(object) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"changeEvent\", {\n      type: \"change\"\n    });\n    __publicField(this, \"EPS\", 1e-6);\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"deviceOrientation\", {\n      alpha: 0,\n      beta: 0,\n      gamma: 0\n    });\n    __publicField(this, \"screenOrientation\", 0);\n    __publicField(this, \"alphaOffset\", 0);\n    __publicField(this, \"onDeviceOrientationChangeEvent\", event => {\n      this.deviceOrientation = event;\n    });\n    __publicField(this, \"onScreenOrientationChangeEvent\", () => {\n      this.screenOrientation = window.orientation || 0;\n    });\n    // The angles alpha, beta and gamma form a set of intrinsic Tait-Bryan angles of type Z-X'-Y''\n    __publicField(this, \"zee\", new Vector3(0, 0, 1));\n    __publicField(this, \"euler\", new Euler());\n    __publicField(this, \"q0\", new Quaternion());\n    __publicField(this, \"q1\", new Quaternion(-Math.sqrt(0.5), 0, 0, Math.sqrt(0.5)));\n    // - PI/2 around the x-axis\n    __publicField(this, \"setObjectQuaternion\", (quaternion, alpha, beta, gamma, orient) => {\n      this.euler.set(beta, alpha, -gamma, \"YXZ\");\n      quaternion.setFromEuler(this.euler);\n      quaternion.multiply(this.q1);\n      quaternion.multiply(this.q0.setFromAxisAngle(this.zee, -orient));\n    });\n    __publicField(this, \"connect\", () => {\n      this.onScreenOrientationChangeEvent();\n      if (window.DeviceOrientationEvent !== void 0 &&\n      // @ts-ignore\n      typeof window.DeviceOrientationEvent.requestPermission === \"function\") {\n        window.DeviceOrientationEvent.requestPermission().then(response => {\n          if (response == \"granted\") {\n            window.addEventListener(\"orientationchange\", this.onScreenOrientationChangeEvent);\n            window.addEventListener(\"deviceorientation\", this.onDeviceOrientationChangeEvent);\n          }\n        }).catch(error => {\n          console.error(\"THREE.DeviceOrientationControls: Unable to use DeviceOrientation API:\", error);\n        });\n      } else {\n        window.addEventListener(\"orientationchange\", this.onScreenOrientationChangeEvent);\n        window.addEventListener(\"deviceorientation\", this.onDeviceOrientationChangeEvent);\n      }\n      this.enabled = true;\n    });\n    __publicField(this, \"disconnect\", () => {\n      window.removeEventListener(\"orientationchange\", this.onScreenOrientationChangeEvent);\n      window.removeEventListener(\"deviceorientation\", this.onDeviceOrientationChangeEvent);\n      this.enabled = false;\n    });\n    __publicField(this, \"lastQuaternion\", new Quaternion());\n    __publicField(this, \"update\", () => {\n      if (this.enabled === false) return;\n      const device = this.deviceOrientation;\n      if (device) {\n        const alpha = device.alpha ? MathUtils.degToRad(device.alpha) + this.alphaOffset : 0;\n        const beta = device.beta ? MathUtils.degToRad(device.beta) : 0;\n        const gamma = device.gamma ? MathUtils.degToRad(device.gamma) : 0;\n        const orient = this.screenOrientation ? MathUtils.degToRad(this.screenOrientation) : 0;\n        this.setObjectQuaternion(this.object.quaternion, alpha, beta, gamma, orient);\n        if (8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n          this.lastQuaternion.copy(this.object.quaternion);\n          this.dispatchEvent(this.changeEvent);\n        }\n      }\n    });\n    __publicField(this, \"dispose\", () => this.disconnect());\n    this.object = object;\n    this.object.rotation.reorder(\"YXZ\");\n    this.connect();\n  }\n}\nexport { DeviceOrientationControls };", "map": {"version": 3, "names": ["DeviceOrientationControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "__publicField", "type", "alpha", "beta", "gamma", "event", "deviceOrientation", "screenOrientation", "window", "orientation", "Vector3", "<PERSON>uler", "Quaternion", "Math", "sqrt", "quaternion", "orient", "euler", "set", "setFromEuler", "multiply", "q1", "q0", "setFromAxisAngle", "zee", "onScreenOrientationChangeEvent", "DeviceOrientationEvent", "requestPermission", "then", "response", "addEventListener", "onDeviceOrientationChangeEvent", "catch", "error", "console", "enabled", "removeEventListener", "device", "MathUtils", "degToRad", "alphaOffset", "setObjectQuaternion", "lastQuaternion", "dot", "EPS", "copy", "dispatchEvent", "changeEvent", "disconnect", "rotation", "reorder", "connect"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/controls/DeviceOrientationControls.ts"], "sourcesContent": ["import { <PERSON>, <PERSON>uler, <PERSON><PERSON><PERSON><PERSON>, Quaternion, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\n/**\n * W3C Device Orientation control (http://w3c.github.io/deviceorientation/spec-source-orientation.html)\n */\n\nclass DeviceOrientationControls extends EventDispatcher<StandardControlsEventMap> {\n  public object: Camera\n\n  private changeEvent = { type: 'change' }\n  private EPS = 0.000001\n\n  public enabled = true\n  public deviceOrientation: Partial<DeviceOrientationEvent> = { alpha: 0, beta: 0, gamma: 0 }\n  public screenOrientation: string | number = 0\n  public alphaOffset = 0 // radians\n\n  constructor(object: Camera) {\n    super()\n\n    this.object = object\n    this.object.rotation.reorder('YXZ')\n\n    this.connect()\n  }\n\n  private onDeviceOrientationChangeEvent = (event: DeviceOrientationEvent): void => {\n    this.deviceOrientation = event\n  }\n\n  private onScreenOrientationChangeEvent = (): void => {\n    this.screenOrientation = window.orientation || 0\n  }\n\n  // The angles alpha, beta and gamma form a set of intrinsic Tait-Bryan angles of type Z-X'-Y''\n\n  private zee = new Vector3(0, 0, 1)\n  private euler = new Euler()\n  private q0 = new Quaternion()\n  private q1 = new Quaternion(-Math.sqrt(0.5), 0, 0, Math.sqrt(0.5)) // - PI/2 around the x-axis\n  private setObjectQuaternion = (\n    quaternion: Quaternion,\n    alpha: number,\n    beta: number,\n    gamma: number,\n    orient: number,\n  ): void => {\n    this.euler.set(beta, alpha, -gamma, 'YXZ') // 'ZXY' for the device, but 'YXZ' for us\n    quaternion.setFromEuler(this.euler) // orient the device\n    quaternion.multiply(this.q1) // camera looks out the back of the device, not the top\n    quaternion.multiply(this.q0.setFromAxisAngle(this.zee, -orient)) // adjust for screen orientation\n  }\n\n  public connect = (): void => {\n    this.onScreenOrientationChangeEvent() // run once on load\n\n    // iOS 13+\n\n    if (\n      window.DeviceOrientationEvent !== undefined &&\n      // @ts-ignore\n      typeof window.DeviceOrientationEvent.requestPermission === 'function'\n    ) {\n      // @ts-ignore\n      window.DeviceOrientationEvent.requestPermission()\n        .then((response: any) => {\n          if (response == 'granted') {\n            window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n            window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n          }\n        })\n        .catch((error: any) => {\n          console.error('THREE.DeviceOrientationControls: Unable to use DeviceOrientation API:', error)\n        })\n    } else {\n      window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n      window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n    }\n\n    this.enabled = true\n  }\n\n  public disconnect = (): void => {\n    window.removeEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n    window.removeEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n\n    this.enabled = false\n  }\n\n  private lastQuaternion = new Quaternion()\n  public update = (): void => {\n    if (this.enabled === false) return\n\n    const device = this.deviceOrientation\n\n    if (device) {\n      const alpha = device.alpha ? MathUtils.degToRad(device.alpha) + this.alphaOffset : 0 // Z\n      const beta = device.beta ? MathUtils.degToRad(device.beta) : 0 // X'\n      const gamma = device.gamma ? MathUtils.degToRad(device.gamma) : 0 // Y''\n      const orient = this.screenOrientation ? MathUtils.degToRad(this.screenOrientation as number) : 0 // O\n\n      this.setObjectQuaternion(this.object.quaternion, alpha, beta, gamma, orient)\n\n      if (8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n        this.lastQuaternion.copy(this.object.quaternion)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n      }\n    }\n  }\n\n  public dispose = (): void => this.disconnect()\n}\n\nexport { DeviceOrientationControls }\n"], "mappings": ";;;;;;;;;;;;;AAQA,MAAMA,yBAAA,SAAkCC,eAAA,CAA0C;EAAA;EAWhFC,YAAYC,MAAA,EAAgB;IACpB;IAXDC,aAAA;IAECA,aAAA,sBAAc;MAAEC,IAAA,EAAM;IAAA;IACtBD,aAAA,cAAM;IAEPA,aAAA,kBAAU;IACVA,aAAA,4BAAqD;MAAEE,KAAA,EAAO;MAAGC,IAAA,EAAM;MAAGC,KAAA,EAAO;IAAA;IACjFJ,aAAA,4BAAqC;IACrCA,aAAA,sBAAc;IAWbA,aAAA,yCAAkCK,KAAA,IAAwC;MAChF,KAAKC,iBAAA,GAAoBD,KAAA;IAAA;IAGnBL,aAAA,yCAAiC,MAAY;MAC9C,KAAAO,iBAAA,GAAoBC,MAAA,CAAOC,WAAA,IAAe;IAAA;IAKzC;IAAAT,aAAA,cAAM,IAAIU,OAAA,CAAQ,GAAG,GAAG,CAAC;IACzBV,aAAA,gBAAQ,IAAIW,KAAA;IACZX,aAAA,aAAK,IAAIY,UAAA;IACTZ,aAAA,aAAK,IAAIY,UAAA,CAAW,CAACC,IAAA,CAAKC,IAAA,CAAK,GAAG,GAAG,GAAG,GAAGD,IAAA,CAAKC,IAAA,CAAK,GAAG,CAAC;IACzD;IAAAd,aAAA,8BAAsB,CAC5Be,UAAA,EACAb,KAAA,EACAC,IAAA,EACAC,KAAA,EACAY,MAAA,KACS;MACT,KAAKC,KAAA,CAAMC,GAAA,CAAIf,IAAA,EAAMD,KAAA,EAAO,CAACE,KAAA,EAAO,KAAK;MAC9BW,UAAA,CAAAI,YAAA,CAAa,KAAKF,KAAK;MACvBF,UAAA,CAAAK,QAAA,CAAS,KAAKC,EAAE;MAChBN,UAAA,CAAAK,QAAA,CAAS,KAAKE,EAAA,CAAGC,gBAAA,CAAiB,KAAKC,GAAA,EAAK,CAACR,MAAM,CAAC;IAAA;IAG1DhB,aAAA,kBAAU,MAAY;MAC3B,KAAKyB,8BAAA,CAA+B;MAIpC,IACEjB,MAAA,CAAOkB,sBAAA,KAA2B;MAAA;MAElC,OAAOlB,MAAA,CAAOkB,sBAAA,CAAuBC,iBAAA,KAAsB,YAC3D;QAEAnB,MAAA,CAAOkB,sBAAA,CAAuBC,iBAAA,CAC3B,EAAAC,IAAA,CAAMC,QAAA,IAAkB;UACvB,IAAIA,QAAA,IAAY,WAAW;YAClBrB,MAAA,CAAAsB,gBAAA,CAAiB,qBAAqB,KAAKL,8BAA8B;YACzEjB,MAAA,CAAAsB,gBAAA,CAAiB,qBAAqB,KAAKC,8BAA8B;UAClF;QAAA,CACD,EACAC,KAAA,CAAOC,KAAA,IAAe;UACbC,OAAA,CAAAD,KAAA,CAAM,yEAAyEA,KAAK;QAAA,CAC7F;MAAA,OACE;QACEzB,MAAA,CAAAsB,gBAAA,CAAiB,qBAAqB,KAAKL,8BAA8B;QACzEjB,MAAA,CAAAsB,gBAAA,CAAiB,qBAAqB,KAAKC,8BAA8B;MAClF;MAEA,KAAKI,OAAA,GAAU;IAAA;IAGVnC,aAAA,qBAAa,MAAY;MACvBQ,MAAA,CAAA4B,mBAAA,CAAoB,qBAAqB,KAAKX,8BAA8B;MAC5EjB,MAAA,CAAA4B,mBAAA,CAAoB,qBAAqB,KAAKL,8BAA8B;MAEnF,KAAKI,OAAA,GAAU;IAAA;IAGTnC,aAAA,yBAAiB,IAAIY,UAAA;IACtBZ,aAAA,iBAAS,MAAY;MAC1B,IAAI,KAAKmC,OAAA,KAAY,OAAO;MAE5B,MAAME,MAAA,GAAS,KAAK/B,iBAAA;MAEpB,IAAI+B,MAAA,EAAQ;QACJ,MAAAnC,KAAA,GAAQmC,MAAA,CAAOnC,KAAA,GAAQoC,SAAA,CAAUC,QAAA,CAASF,MAAA,CAAOnC,KAAK,IAAI,KAAKsC,WAAA,GAAc;QACnF,MAAMrC,IAAA,GAAOkC,MAAA,CAAOlC,IAAA,GAAOmC,SAAA,CAAUC,QAAA,CAASF,MAAA,CAAOlC,IAAI,IAAI;QAC7D,MAAMC,KAAA,GAAQiC,MAAA,CAAOjC,KAAA,GAAQkC,SAAA,CAAUC,QAAA,CAASF,MAAA,CAAOjC,KAAK,IAAI;QAChE,MAAMY,MAAA,GAAS,KAAKT,iBAAA,GAAoB+B,SAAA,CAAUC,QAAA,CAAS,KAAKhC,iBAA2B,IAAI;QAE/F,KAAKkC,mBAAA,CAAoB,KAAK1C,MAAA,CAAOgB,UAAA,EAAYb,KAAA,EAAOC,IAAA,EAAMC,KAAA,EAAOY,MAAM;QAEvE,SAAK,IAAI,KAAK0B,cAAA,CAAeC,GAAA,CAAI,KAAK5C,MAAA,CAAOgB,UAAU,KAAK,KAAK6B,GAAA,EAAK;UACxE,KAAKF,cAAA,CAAeG,IAAA,CAAK,KAAK9C,MAAA,CAAOgB,UAAU;UAE1C,KAAA+B,aAAA,CAAc,KAAKC,WAAW;QACrC;MACF;IAAA;IAGK/C,aAAA,kBAAU,MAAY,KAAKgD,UAAA;IA3FhC,KAAKjD,MAAA,GAASA,MAAA;IACT,KAAAA,MAAA,CAAOkD,QAAA,CAASC,OAAA,CAAQ,KAAK;IAElC,KAAKC,OAAA,CAAQ;EACf;AAwFF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}