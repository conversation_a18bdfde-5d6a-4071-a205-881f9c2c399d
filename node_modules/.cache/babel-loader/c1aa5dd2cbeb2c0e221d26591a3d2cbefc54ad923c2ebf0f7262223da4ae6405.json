{"ast": null, "code": "import { DataTextureLoader, HalfFloatType, FloatType, RGBAFormat, RedFormat, LinearFilter, DataUtils } from \"three\";\nimport { unzlibSync } from \"fflate\";\nimport { version } from \"../_polyfill/constants.js\";\nconst hasColorSpace = version >= 152;\nclass EXRLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n    this.type = HalfFloatType;\n  }\n  parse(buffer) {\n    const USHORT_RANGE = 1 << 16;\n    const BITMAP_SIZE = USHORT_RANGE >> 3;\n    const HUF_ENCBITS = 16;\n    const HUF_DECBITS = 14;\n    const HUF_ENCSIZE = (1 << HUF_ENCBITS) + 1;\n    const HUF_DECSIZE = 1 << HUF_DECBITS;\n    const HUF_DECMASK = HUF_DECSIZE - 1;\n    const NBITS = 16;\n    const A_OFFSET = 1 << NBITS - 1;\n    const MOD_MASK = (1 << NBITS) - 1;\n    const SHORT_ZEROCODE_RUN = 59;\n    const LONG_ZEROCODE_RUN = 63;\n    const SHORTEST_LONG_RUN = 2 + LONG_ZEROCODE_RUN - SHORT_ZEROCODE_RUN;\n    const ULONG_SIZE = 8;\n    const FLOAT32_SIZE = 4;\n    const INT32_SIZE = 4;\n    const INT16_SIZE = 2;\n    const INT8_SIZE = 1;\n    const STATIC_HUFFMAN = 0;\n    const DEFLATE = 1;\n    const UNKNOWN = 0;\n    const LOSSY_DCT = 1;\n    const RLE = 2;\n    const logBase = Math.pow(2.7182818, 2.2);\n    function reverseLutFromBitmap(bitmap, lut) {\n      var k = 0;\n      for (var i = 0; i < USHORT_RANGE; ++i) {\n        if (i == 0 || bitmap[i >> 3] & 1 << (i & 7)) {\n          lut[k++] = i;\n        }\n      }\n      var n = k - 1;\n      while (k < USHORT_RANGE) lut[k++] = 0;\n      return n;\n    }\n    function hufClearDecTable(hdec) {\n      for (var i = 0; i < HUF_DECSIZE; i++) {\n        hdec[i] = {};\n        hdec[i].len = 0;\n        hdec[i].lit = 0;\n        hdec[i].p = null;\n      }\n    }\n    const getBitsReturn = {\n      l: 0,\n      c: 0,\n      lc: 0\n    };\n    function getBits(nBits, c, lc, uInt8Array2, inOffset) {\n      while (lc < nBits) {\n        c = c << 8 | parseUint8Array(uInt8Array2, inOffset);\n        lc += 8;\n      }\n      lc -= nBits;\n      getBitsReturn.l = c >> lc & (1 << nBits) - 1;\n      getBitsReturn.c = c;\n      getBitsReturn.lc = lc;\n    }\n    const hufTableBuffer = new Array(59);\n    function hufCanonicalCodeTable(hcode) {\n      for (var i = 0; i <= 58; ++i) hufTableBuffer[i] = 0;\n      for (var i = 0; i < HUF_ENCSIZE; ++i) hufTableBuffer[hcode[i]] += 1;\n      var c = 0;\n      for (var i = 58; i > 0; --i) {\n        var nc = c + hufTableBuffer[i] >> 1;\n        hufTableBuffer[i] = c;\n        c = nc;\n      }\n      for (var i = 0; i < HUF_ENCSIZE; ++i) {\n        var l = hcode[i];\n        if (l > 0) hcode[i] = l | hufTableBuffer[l]++ << 6;\n      }\n    }\n    function hufUnpackEncTable(uInt8Array2, inDataView, inOffset, ni, im, iM, hcode) {\n      var p = inOffset;\n      var c = 0;\n      var lc = 0;\n      for (; im <= iM; im++) {\n        if (p.value - inOffset.value > ni) return false;\n        getBits(6, c, lc, uInt8Array2, p);\n        var l = getBitsReturn.l;\n        c = getBitsReturn.c;\n        lc = getBitsReturn.lc;\n        hcode[im] = l;\n        if (l == LONG_ZEROCODE_RUN) {\n          if (p.value - inOffset.value > ni) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          getBits(8, c, lc, uInt8Array2, p);\n          var zerun = getBitsReturn.l + SHORTEST_LONG_RUN;\n          c = getBitsReturn.c;\n          lc = getBitsReturn.lc;\n          if (im + zerun > iM + 1) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          while (zerun--) hcode[im++] = 0;\n          im--;\n        } else if (l >= SHORT_ZEROCODE_RUN) {\n          var zerun = l - SHORT_ZEROCODE_RUN + 2;\n          if (im + zerun > iM + 1) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          while (zerun--) hcode[im++] = 0;\n          im--;\n        }\n      }\n      hufCanonicalCodeTable(hcode);\n    }\n    function hufLength(code) {\n      return code & 63;\n    }\n    function hufCode(code) {\n      return code >> 6;\n    }\n    function hufBuildDecTable(hcode, im, iM, hdecod) {\n      for (; im <= iM; im++) {\n        var c = hufCode(hcode[im]);\n        var l = hufLength(hcode[im]);\n        if (c >> l) {\n          throw \"Invalid table entry\";\n        }\n        if (l > HUF_DECBITS) {\n          var pl = hdecod[c >> l - HUF_DECBITS];\n          if (pl.len) {\n            throw \"Invalid table entry\";\n          }\n          pl.lit++;\n          if (pl.p) {\n            var p = pl.p;\n            pl.p = new Array(pl.lit);\n            for (var i = 0; i < pl.lit - 1; ++i) {\n              pl.p[i] = p[i];\n            }\n          } else {\n            pl.p = new Array(1);\n          }\n          pl.p[pl.lit - 1] = im;\n        } else if (l) {\n          var plOffset = 0;\n          for (var i = 1 << HUF_DECBITS - l; i > 0; i--) {\n            var pl = hdecod[(c << HUF_DECBITS - l) + plOffset];\n            if (pl.len || pl.p) {\n              throw \"Invalid table entry\";\n            }\n            pl.len = l;\n            pl.lit = im;\n            plOffset++;\n          }\n        }\n      }\n      return true;\n    }\n    const getCharReturn = {\n      c: 0,\n      lc: 0\n    };\n    function getChar(c, lc, uInt8Array2, inOffset) {\n      c = c << 8 | parseUint8Array(uInt8Array2, inOffset);\n      lc += 8;\n      getCharReturn.c = c;\n      getCharReturn.lc = lc;\n    }\n    const getCodeReturn = {\n      c: 0,\n      lc: 0\n    };\n    function getCode(po, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outBufferOffset, outBufferEndOffset) {\n      if (po == rlc) {\n        if (lc < 8) {\n          getChar(c, lc, uInt8Array2, inOffset);\n          c = getCharReturn.c;\n          lc = getCharReturn.lc;\n        }\n        lc -= 8;\n        var cs = c >> lc;\n        var cs = new Uint8Array([cs])[0];\n        if (outBufferOffset.value + cs > outBufferEndOffset) {\n          return false;\n        }\n        var s = outBuffer[outBufferOffset.value - 1];\n        while (cs-- > 0) {\n          outBuffer[outBufferOffset.value++] = s;\n        }\n      } else if (outBufferOffset.value < outBufferEndOffset) {\n        outBuffer[outBufferOffset.value++] = po;\n      } else {\n        return false;\n      }\n      getCodeReturn.c = c;\n      getCodeReturn.lc = lc;\n    }\n    function UInt16(value) {\n      return value & 65535;\n    }\n    function Int16(value) {\n      var ref = UInt16(value);\n      return ref > 32767 ? ref - 65536 : ref;\n    }\n    const wdec14Return = {\n      a: 0,\n      b: 0\n    };\n    function wdec14(l, h) {\n      var ls = Int16(l);\n      var hs = Int16(h);\n      var hi = hs;\n      var ai = ls + (hi & 1) + (hi >> 1);\n      var as = ai;\n      var bs = ai - hi;\n      wdec14Return.a = as;\n      wdec14Return.b = bs;\n    }\n    function wdec16(l, h) {\n      var m = UInt16(l);\n      var d = UInt16(h);\n      var bb = m - (d >> 1) & MOD_MASK;\n      var aa = d + bb - A_OFFSET & MOD_MASK;\n      wdec14Return.a = aa;\n      wdec14Return.b = bb;\n    }\n    function wav2Decode(buffer2, j, nx, ox, ny, oy, mx) {\n      var w14 = mx < 1 << 14;\n      var n = nx > ny ? ny : nx;\n      var p = 1;\n      var p2;\n      while (p <= n) p <<= 1;\n      p >>= 1;\n      p2 = p;\n      p >>= 1;\n      while (p >= 1) {\n        var py = 0;\n        var ey = py + oy * (ny - p2);\n        var oy1 = oy * p;\n        var oy2 = oy * p2;\n        var ox1 = ox * p;\n        var ox2 = ox * p2;\n        var i00, i01, i10, i11;\n        for (; py <= ey; py += oy2) {\n          var px = py;\n          var ex = py + ox * (nx - p2);\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1;\n            var p10 = px + oy1;\n            var p11 = p10 + ox1;\n            if (w14) {\n              wdec14(buffer2[px + j], buffer2[p10 + j]);\n              i00 = wdec14Return.a;\n              i10 = wdec14Return.b;\n              wdec14(buffer2[p01 + j], buffer2[p11 + j]);\n              i01 = wdec14Return.a;\n              i11 = wdec14Return.b;\n              wdec14(i00, i01);\n              buffer2[px + j] = wdec14Return.a;\n              buffer2[p01 + j] = wdec14Return.b;\n              wdec14(i10, i11);\n              buffer2[p10 + j] = wdec14Return.a;\n              buffer2[p11 + j] = wdec14Return.b;\n            } else {\n              wdec16(buffer2[px + j], buffer2[p10 + j]);\n              i00 = wdec14Return.a;\n              i10 = wdec14Return.b;\n              wdec16(buffer2[p01 + j], buffer2[p11 + j]);\n              i01 = wdec14Return.a;\n              i11 = wdec14Return.b;\n              wdec16(i00, i01);\n              buffer2[px + j] = wdec14Return.a;\n              buffer2[p01 + j] = wdec14Return.b;\n              wdec16(i10, i11);\n              buffer2[p10 + j] = wdec14Return.a;\n              buffer2[p11 + j] = wdec14Return.b;\n            }\n          }\n          if (nx & p) {\n            var p10 = px + oy1;\n            if (w14) wdec14(buffer2[px + j], buffer2[p10 + j]);else wdec16(buffer2[px + j], buffer2[p10 + j]);\n            i00 = wdec14Return.a;\n            buffer2[p10 + j] = wdec14Return.b;\n            buffer2[px + j] = i00;\n          }\n        }\n        if (ny & p) {\n          var px = py;\n          var ex = py + ox * (nx - p2);\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1;\n            if (w14) wdec14(buffer2[px + j], buffer2[p01 + j]);else wdec16(buffer2[px + j], buffer2[p01 + j]);\n            i00 = wdec14Return.a;\n            buffer2[p01 + j] = wdec14Return.b;\n            buffer2[px + j] = i00;\n          }\n        }\n        p2 = p;\n        p >>= 1;\n      }\n      return py;\n    }\n    function hufDecode(encodingTable, decodingTable, uInt8Array2, inDataView, inOffset, ni, rlc, no, outBuffer, outOffset) {\n      var c = 0;\n      var lc = 0;\n      var outBufferEndOffset = no;\n      var inOffsetEnd = Math.trunc(inOffset.value + (ni + 7) / 8);\n      while (inOffset.value < inOffsetEnd) {\n        getChar(c, lc, uInt8Array2, inOffset);\n        c = getCharReturn.c;\n        lc = getCharReturn.lc;\n        while (lc >= HUF_DECBITS) {\n          var index = c >> lc - HUF_DECBITS & HUF_DECMASK;\n          var pl = decodingTable[index];\n          if (pl.len) {\n            lc -= pl.len;\n            getCode(pl.lit, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset);\n            c = getCodeReturn.c;\n            lc = getCodeReturn.lc;\n          } else {\n            if (!pl.p) {\n              throw \"hufDecode issues\";\n            }\n            var j;\n            for (j = 0; j < pl.lit; j++) {\n              var l = hufLength(encodingTable[pl.p[j]]);\n              while (lc < l && inOffset.value < inOffsetEnd) {\n                getChar(c, lc, uInt8Array2, inOffset);\n                c = getCharReturn.c;\n                lc = getCharReturn.lc;\n              }\n              if (lc >= l) {\n                if (hufCode(encodingTable[pl.p[j]]) == (c >> lc - l & (1 << l) - 1)) {\n                  lc -= l;\n                  getCode(pl.p[j], rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset);\n                  c = getCodeReturn.c;\n                  lc = getCodeReturn.lc;\n                  break;\n                }\n              }\n            }\n            if (j == pl.lit) {\n              throw \"hufDecode issues\";\n            }\n          }\n        }\n      }\n      var i = 8 - ni & 7;\n      c >>= i;\n      lc -= i;\n      while (lc > 0) {\n        var pl = decodingTable[c << HUF_DECBITS - lc & HUF_DECMASK];\n        if (pl.len) {\n          lc -= pl.len;\n          getCode(pl.lit, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset);\n          c = getCodeReturn.c;\n          lc = getCodeReturn.lc;\n        } else {\n          throw \"hufDecode issues\";\n        }\n      }\n      return true;\n    }\n    function hufUncompress(uInt8Array2, inDataView, inOffset, nCompressed, outBuffer, nRaw) {\n      var outOffset = {\n        value: 0\n      };\n      var initialInOffset = inOffset.value;\n      var im = parseUint32(inDataView, inOffset);\n      var iM = parseUint32(inDataView, inOffset);\n      inOffset.value += 4;\n      var nBits = parseUint32(inDataView, inOffset);\n      inOffset.value += 4;\n      if (im < 0 || im >= HUF_ENCSIZE || iM < 0 || iM >= HUF_ENCSIZE) {\n        throw \"Something wrong with HUF_ENCSIZE\";\n      }\n      var freq = new Array(HUF_ENCSIZE);\n      var hdec = new Array(HUF_DECSIZE);\n      hufClearDecTable(hdec);\n      var ni = nCompressed - (inOffset.value - initialInOffset);\n      hufUnpackEncTable(uInt8Array2, inDataView, inOffset, ni, im, iM, freq);\n      if (nBits > 8 * (nCompressed - (inOffset.value - initialInOffset))) {\n        throw \"Something wrong with hufUncompress\";\n      }\n      hufBuildDecTable(freq, im, iM, hdec);\n      hufDecode(freq, hdec, uInt8Array2, inDataView, inOffset, nBits, iM, nRaw, outBuffer, outOffset);\n    }\n    function applyLut(lut, data, nData) {\n      for (var i = 0; i < nData; ++i) {\n        data[i] = lut[data[i]];\n      }\n    }\n    function predictor(source) {\n      for (var t = 1; t < source.length; t++) {\n        var d = source[t - 1] + source[t] - 128;\n        source[t] = d;\n      }\n    }\n    function interleaveScalar(source, out) {\n      var t1 = 0;\n      var t2 = Math.floor((source.length + 1) / 2);\n      var s = 0;\n      var stop = source.length - 1;\n      while (true) {\n        if (s > stop) break;\n        out[s++] = source[t1++];\n        if (s > stop) break;\n        out[s++] = source[t2++];\n      }\n    }\n    function decodeRunLength(source) {\n      var size = source.byteLength;\n      var out = new Array();\n      var p = 0;\n      var reader = new DataView(source);\n      while (size > 0) {\n        var l = reader.getInt8(p++);\n        if (l < 0) {\n          var count = -l;\n          size -= count + 1;\n          for (var i = 0; i < count; i++) {\n            out.push(reader.getUint8(p++));\n          }\n        } else {\n          var count = l;\n          size -= 2;\n          var value = reader.getUint8(p++);\n          for (var i = 0; i < count + 1; i++) {\n            out.push(value);\n          }\n        }\n      }\n      return out;\n    }\n    function lossyDctDecode(cscSet, rowPtrs, channelData, acBuffer, dcBuffer, outBuffer) {\n      var dataView = new DataView(outBuffer.buffer);\n      var width = channelData[cscSet.idx[0]].width;\n      var height = channelData[cscSet.idx[0]].height;\n      var numComp = 3;\n      var numFullBlocksX = Math.floor(width / 8);\n      var numBlocksX = Math.ceil(width / 8);\n      var numBlocksY = Math.ceil(height / 8);\n      var leftoverX = width - (numBlocksX - 1) * 8;\n      var leftoverY = height - (numBlocksY - 1) * 8;\n      var currAcComp = {\n        value: 0\n      };\n      var currDcComp = new Array(numComp);\n      var dctData = new Array(numComp);\n      var halfZigBlock = new Array(numComp);\n      var rowBlock = new Array(numComp);\n      var rowOffsets = new Array(numComp);\n      for (let comp2 = 0; comp2 < numComp; ++comp2) {\n        rowOffsets[comp2] = rowPtrs[cscSet.idx[comp2]];\n        currDcComp[comp2] = comp2 < 1 ? 0 : currDcComp[comp2 - 1] + numBlocksX * numBlocksY;\n        dctData[comp2] = new Float32Array(64);\n        halfZigBlock[comp2] = new Uint16Array(64);\n        rowBlock[comp2] = new Uint16Array(numBlocksX * 64);\n      }\n      for (let blocky = 0; blocky < numBlocksY; ++blocky) {\n        var maxY = 8;\n        if (blocky == numBlocksY - 1) maxY = leftoverY;\n        var maxX = 8;\n        for (let blockx = 0; blockx < numBlocksX; ++blockx) {\n          if (blockx == numBlocksX - 1) maxX = leftoverX;\n          for (let comp2 = 0; comp2 < numComp; ++comp2) {\n            halfZigBlock[comp2].fill(0);\n            halfZigBlock[comp2][0] = dcBuffer[currDcComp[comp2]++];\n            unRleAC(currAcComp, acBuffer, halfZigBlock[comp2]);\n            unZigZag(halfZigBlock[comp2], dctData[comp2]);\n            dctInverse(dctData[comp2]);\n          }\n          {\n            csc709Inverse(dctData);\n          }\n          for (let comp2 = 0; comp2 < numComp; ++comp2) {\n            convertToHalf(dctData[comp2], rowBlock[comp2], blockx * 64);\n          }\n        }\n        let offset2 = 0;\n        for (let comp2 = 0; comp2 < numComp; ++comp2) {\n          const type2 = channelData[cscSet.idx[comp2]].type;\n          for (let y2 = 8 * blocky; y2 < 8 * blocky + maxY; ++y2) {\n            offset2 = rowOffsets[comp2][y2];\n            for (let blockx = 0; blockx < numFullBlocksX; ++blockx) {\n              const src = blockx * 64 + (y2 & 7) * 8;\n              dataView.setUint16(offset2 + 0 * INT16_SIZE * type2, rowBlock[comp2][src + 0], true);\n              dataView.setUint16(offset2 + 1 * INT16_SIZE * type2, rowBlock[comp2][src + 1], true);\n              dataView.setUint16(offset2 + 2 * INT16_SIZE * type2, rowBlock[comp2][src + 2], true);\n              dataView.setUint16(offset2 + 3 * INT16_SIZE * type2, rowBlock[comp2][src + 3], true);\n              dataView.setUint16(offset2 + 4 * INT16_SIZE * type2, rowBlock[comp2][src + 4], true);\n              dataView.setUint16(offset2 + 5 * INT16_SIZE * type2, rowBlock[comp2][src + 5], true);\n              dataView.setUint16(offset2 + 6 * INT16_SIZE * type2, rowBlock[comp2][src + 6], true);\n              dataView.setUint16(offset2 + 7 * INT16_SIZE * type2, rowBlock[comp2][src + 7], true);\n              offset2 += 8 * INT16_SIZE * type2;\n            }\n          }\n          if (numFullBlocksX != numBlocksX) {\n            for (let y2 = 8 * blocky; y2 < 8 * blocky + maxY; ++y2) {\n              const offset3 = rowOffsets[comp2][y2] + 8 * numFullBlocksX * INT16_SIZE * type2;\n              const src = numFullBlocksX * 64 + (y2 & 7) * 8;\n              for (let x2 = 0; x2 < maxX; ++x2) {\n                dataView.setUint16(offset3 + x2 * INT16_SIZE * type2, rowBlock[comp2][src + x2], true);\n              }\n            }\n          }\n        }\n      }\n      var halfRow = new Uint16Array(width);\n      var dataView = new DataView(outBuffer.buffer);\n      for (var comp = 0; comp < numComp; ++comp) {\n        channelData[cscSet.idx[comp]].decoded = true;\n        var type = channelData[cscSet.idx[comp]].type;\n        if (channelData[comp].type != 2) continue;\n        for (var y = 0; y < height; ++y) {\n          const offset2 = rowOffsets[comp][y];\n          for (var x = 0; x < width; ++x) {\n            halfRow[x] = dataView.getUint16(offset2 + x * INT16_SIZE * type, true);\n          }\n          for (var x = 0; x < width; ++x) {\n            dataView.setFloat32(offset2 + x * INT16_SIZE * type, decodeFloat16(halfRow[x]), true);\n          }\n        }\n      }\n    }\n    function unRleAC(currAcComp, acBuffer, halfZigBlock) {\n      var acValue;\n      var dctComp = 1;\n      while (dctComp < 64) {\n        acValue = acBuffer[currAcComp.value];\n        if (acValue == 65280) {\n          dctComp = 64;\n        } else if (acValue >> 8 == 255) {\n          dctComp += acValue & 255;\n        } else {\n          halfZigBlock[dctComp] = acValue;\n          dctComp++;\n        }\n        currAcComp.value++;\n      }\n    }\n    function unZigZag(src, dst) {\n      dst[0] = decodeFloat16(src[0]);\n      dst[1] = decodeFloat16(src[1]);\n      dst[2] = decodeFloat16(src[5]);\n      dst[3] = decodeFloat16(src[6]);\n      dst[4] = decodeFloat16(src[14]);\n      dst[5] = decodeFloat16(src[15]);\n      dst[6] = decodeFloat16(src[27]);\n      dst[7] = decodeFloat16(src[28]);\n      dst[8] = decodeFloat16(src[2]);\n      dst[9] = decodeFloat16(src[4]);\n      dst[10] = decodeFloat16(src[7]);\n      dst[11] = decodeFloat16(src[13]);\n      dst[12] = decodeFloat16(src[16]);\n      dst[13] = decodeFloat16(src[26]);\n      dst[14] = decodeFloat16(src[29]);\n      dst[15] = decodeFloat16(src[42]);\n      dst[16] = decodeFloat16(src[3]);\n      dst[17] = decodeFloat16(src[8]);\n      dst[18] = decodeFloat16(src[12]);\n      dst[19] = decodeFloat16(src[17]);\n      dst[20] = decodeFloat16(src[25]);\n      dst[21] = decodeFloat16(src[30]);\n      dst[22] = decodeFloat16(src[41]);\n      dst[23] = decodeFloat16(src[43]);\n      dst[24] = decodeFloat16(src[9]);\n      dst[25] = decodeFloat16(src[11]);\n      dst[26] = decodeFloat16(src[18]);\n      dst[27] = decodeFloat16(src[24]);\n      dst[28] = decodeFloat16(src[31]);\n      dst[29] = decodeFloat16(src[40]);\n      dst[30] = decodeFloat16(src[44]);\n      dst[31] = decodeFloat16(src[53]);\n      dst[32] = decodeFloat16(src[10]);\n      dst[33] = decodeFloat16(src[19]);\n      dst[34] = decodeFloat16(src[23]);\n      dst[35] = decodeFloat16(src[32]);\n      dst[36] = decodeFloat16(src[39]);\n      dst[37] = decodeFloat16(src[45]);\n      dst[38] = decodeFloat16(src[52]);\n      dst[39] = decodeFloat16(src[54]);\n      dst[40] = decodeFloat16(src[20]);\n      dst[41] = decodeFloat16(src[22]);\n      dst[42] = decodeFloat16(src[33]);\n      dst[43] = decodeFloat16(src[38]);\n      dst[44] = decodeFloat16(src[46]);\n      dst[45] = decodeFloat16(src[51]);\n      dst[46] = decodeFloat16(src[55]);\n      dst[47] = decodeFloat16(src[60]);\n      dst[48] = decodeFloat16(src[21]);\n      dst[49] = decodeFloat16(src[34]);\n      dst[50] = decodeFloat16(src[37]);\n      dst[51] = decodeFloat16(src[47]);\n      dst[52] = decodeFloat16(src[50]);\n      dst[53] = decodeFloat16(src[56]);\n      dst[54] = decodeFloat16(src[59]);\n      dst[55] = decodeFloat16(src[61]);\n      dst[56] = decodeFloat16(src[35]);\n      dst[57] = decodeFloat16(src[36]);\n      dst[58] = decodeFloat16(src[48]);\n      dst[59] = decodeFloat16(src[49]);\n      dst[60] = decodeFloat16(src[57]);\n      dst[61] = decodeFloat16(src[58]);\n      dst[62] = decodeFloat16(src[62]);\n      dst[63] = decodeFloat16(src[63]);\n    }\n    function dctInverse(data) {\n      const a = 0.5 * Math.cos(3.14159 / 4);\n      const b = 0.5 * Math.cos(3.14159 / 16);\n      const c = 0.5 * Math.cos(3.14159 / 8);\n      const d = 0.5 * Math.cos(3 * 3.14159 / 16);\n      const e = 0.5 * Math.cos(5 * 3.14159 / 16);\n      const f = 0.5 * Math.cos(3 * 3.14159 / 8);\n      const g = 0.5 * Math.cos(7 * 3.14159 / 16);\n      var alpha = new Array(4);\n      var beta = new Array(4);\n      var theta = new Array(4);\n      var gamma = new Array(4);\n      for (var row = 0; row < 8; ++row) {\n        var rowPtr = row * 8;\n        alpha[0] = c * data[rowPtr + 2];\n        alpha[1] = f * data[rowPtr + 2];\n        alpha[2] = c * data[rowPtr + 6];\n        alpha[3] = f * data[rowPtr + 6];\n        beta[0] = b * data[rowPtr + 1] + d * data[rowPtr + 3] + e * data[rowPtr + 5] + g * data[rowPtr + 7];\n        beta[1] = d * data[rowPtr + 1] - g * data[rowPtr + 3] - b * data[rowPtr + 5] - e * data[rowPtr + 7];\n        beta[2] = e * data[rowPtr + 1] - b * data[rowPtr + 3] + g * data[rowPtr + 5] + d * data[rowPtr + 7];\n        beta[3] = g * data[rowPtr + 1] - e * data[rowPtr + 3] + d * data[rowPtr + 5] - b * data[rowPtr + 7];\n        theta[0] = a * (data[rowPtr + 0] + data[rowPtr + 4]);\n        theta[3] = a * (data[rowPtr + 0] - data[rowPtr + 4]);\n        theta[1] = alpha[0] + alpha[3];\n        theta[2] = alpha[1] - alpha[2];\n        gamma[0] = theta[0] + theta[1];\n        gamma[1] = theta[3] + theta[2];\n        gamma[2] = theta[3] - theta[2];\n        gamma[3] = theta[0] - theta[1];\n        data[rowPtr + 0] = gamma[0] + beta[0];\n        data[rowPtr + 1] = gamma[1] + beta[1];\n        data[rowPtr + 2] = gamma[2] + beta[2];\n        data[rowPtr + 3] = gamma[3] + beta[3];\n        data[rowPtr + 4] = gamma[3] - beta[3];\n        data[rowPtr + 5] = gamma[2] - beta[2];\n        data[rowPtr + 6] = gamma[1] - beta[1];\n        data[rowPtr + 7] = gamma[0] - beta[0];\n      }\n      for (var column = 0; column < 8; ++column) {\n        alpha[0] = c * data[16 + column];\n        alpha[1] = f * data[16 + column];\n        alpha[2] = c * data[48 + column];\n        alpha[3] = f * data[48 + column];\n        beta[0] = b * data[8 + column] + d * data[24 + column] + e * data[40 + column] + g * data[56 + column];\n        beta[1] = d * data[8 + column] - g * data[24 + column] - b * data[40 + column] - e * data[56 + column];\n        beta[2] = e * data[8 + column] - b * data[24 + column] + g * data[40 + column] + d * data[56 + column];\n        beta[3] = g * data[8 + column] - e * data[24 + column] + d * data[40 + column] - b * data[56 + column];\n        theta[0] = a * (data[column] + data[32 + column]);\n        theta[3] = a * (data[column] - data[32 + column]);\n        theta[1] = alpha[0] + alpha[3];\n        theta[2] = alpha[1] - alpha[2];\n        gamma[0] = theta[0] + theta[1];\n        gamma[1] = theta[3] + theta[2];\n        gamma[2] = theta[3] - theta[2];\n        gamma[3] = theta[0] - theta[1];\n        data[0 + column] = gamma[0] + beta[0];\n        data[8 + column] = gamma[1] + beta[1];\n        data[16 + column] = gamma[2] + beta[2];\n        data[24 + column] = gamma[3] + beta[3];\n        data[32 + column] = gamma[3] - beta[3];\n        data[40 + column] = gamma[2] - beta[2];\n        data[48 + column] = gamma[1] - beta[1];\n        data[56 + column] = gamma[0] - beta[0];\n      }\n    }\n    function csc709Inverse(data) {\n      for (var i = 0; i < 64; ++i) {\n        var y = data[0][i];\n        var cb = data[1][i];\n        var cr = data[2][i];\n        data[0][i] = y + 1.5747 * cr;\n        data[1][i] = y - 0.1873 * cb - 0.4682 * cr;\n        data[2][i] = y + 1.8556 * cb;\n      }\n    }\n    function convertToHalf(src, dst, idx) {\n      for (var i = 0; i < 64; ++i) {\n        dst[idx + i] = DataUtils.toHalfFloat(toLinear(src[i]));\n      }\n    }\n    function toLinear(float) {\n      if (float <= 1) {\n        return Math.sign(float) * Math.pow(Math.abs(float), 2.2);\n      } else {\n        return Math.sign(float) * Math.pow(logBase, Math.abs(float) - 1);\n      }\n    }\n    function uncompressRAW(info) {\n      return new DataView(info.array.buffer, info.offset.value, info.size);\n    }\n    function uncompressRLE(info) {\n      var compressed = info.viewer.buffer.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = new Uint8Array(decodeRunLength(compressed));\n      var tmpBuffer = new Uint8Array(rawBuffer.length);\n      predictor(rawBuffer);\n      interleaveScalar(rawBuffer, tmpBuffer);\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressZIP(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = unzlibSync(compressed);\n      var tmpBuffer = new Uint8Array(rawBuffer.length);\n      predictor(rawBuffer);\n      interleaveScalar(rawBuffer, tmpBuffer);\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressPIZ(info) {\n      var inDataView = info.viewer;\n      var inOffset = {\n        value: info.offset.value\n      };\n      var outBuffer = new Uint16Array(info.width * info.scanlineBlockSize * (info.channels * info.type));\n      var bitmap = new Uint8Array(BITMAP_SIZE);\n      var outBufferEnd = 0;\n      var pizChannelData = new Array(info.channels);\n      for (var i = 0; i < info.channels; i++) {\n        pizChannelData[i] = {};\n        pizChannelData[i][\"start\"] = outBufferEnd;\n        pizChannelData[i][\"end\"] = pizChannelData[i][\"start\"];\n        pizChannelData[i][\"nx\"] = info.width;\n        pizChannelData[i][\"ny\"] = info.lines;\n        pizChannelData[i][\"size\"] = info.type;\n        outBufferEnd += pizChannelData[i].nx * pizChannelData[i].ny * pizChannelData[i].size;\n      }\n      var minNonZero = parseUint16(inDataView, inOffset);\n      var maxNonZero = parseUint16(inDataView, inOffset);\n      if (maxNonZero >= BITMAP_SIZE) {\n        throw \"Something is wrong with PIZ_COMPRESSION BITMAP_SIZE\";\n      }\n      if (minNonZero <= maxNonZero) {\n        for (var i = 0; i < maxNonZero - minNonZero + 1; i++) {\n          bitmap[i + minNonZero] = parseUint8(inDataView, inOffset);\n        }\n      }\n      var lut = new Uint16Array(USHORT_RANGE);\n      var maxValue = reverseLutFromBitmap(bitmap, lut);\n      var length = parseUint32(inDataView, inOffset);\n      hufUncompress(info.array, inDataView, inOffset, length, outBuffer, outBufferEnd);\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = pizChannelData[i];\n        for (var j = 0; j < pizChannelData[i].size; ++j) {\n          wav2Decode(outBuffer, cd.start + j, cd.nx, cd.size, cd.ny, cd.nx * cd.size, maxValue);\n        }\n      }\n      applyLut(lut, outBuffer, outBufferEnd);\n      var tmpOffset2 = 0;\n      var tmpBuffer = new Uint8Array(outBuffer.buffer.byteLength);\n      for (var y = 0; y < info.lines; y++) {\n        for (var c = 0; c < info.channels; c++) {\n          var cd = pizChannelData[c];\n          var n = cd.nx * cd.size;\n          var cp = new Uint8Array(outBuffer.buffer, cd.end * INT16_SIZE, n * INT16_SIZE);\n          tmpBuffer.set(cp, tmpOffset2);\n          tmpOffset2 += n * INT16_SIZE;\n          cd.end += n;\n        }\n      }\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressPXR(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = unzlibSync(compressed);\n      const sz = info.lines * info.channels * info.width;\n      const tmpBuffer = info.type == 1 ? new Uint16Array(sz) : new Uint32Array(sz);\n      let tmpBufferEnd = 0;\n      let writePtr = 0;\n      const ptr = new Array(4);\n      for (let y = 0; y < info.lines; y++) {\n        for (let c = 0; c < info.channels; c++) {\n          let pixel = 0;\n          switch (info.type) {\n            case 1:\n              ptr[0] = tmpBufferEnd;\n              ptr[1] = ptr[0] + info.width;\n              tmpBufferEnd = ptr[1] + info.width;\n              for (let j = 0; j < info.width; ++j) {\n                const diff = rawBuffer[ptr[0]++] << 8 | rawBuffer[ptr[1]++];\n                pixel += diff;\n                tmpBuffer[writePtr] = pixel;\n                writePtr++;\n              }\n              break;\n            case 2:\n              ptr[0] = tmpBufferEnd;\n              ptr[1] = ptr[0] + info.width;\n              ptr[2] = ptr[1] + info.width;\n              tmpBufferEnd = ptr[2] + info.width;\n              for (let j = 0; j < info.width; ++j) {\n                const diff = rawBuffer[ptr[0]++] << 24 | rawBuffer[ptr[1]++] << 16 | rawBuffer[ptr[2]++] << 8;\n                pixel += diff;\n                tmpBuffer[writePtr] = pixel;\n                writePtr++;\n              }\n              break;\n          }\n        }\n      }\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressDWA(info) {\n      var inDataView = info.viewer;\n      var inOffset = {\n        value: info.offset.value\n      };\n      var outBuffer = new Uint8Array(info.width * info.lines * (info.channels * info.type * INT16_SIZE));\n      var dwaHeader = {\n        version: parseInt64(inDataView, inOffset),\n        unknownUncompressedSize: parseInt64(inDataView, inOffset),\n        unknownCompressedSize: parseInt64(inDataView, inOffset),\n        acCompressedSize: parseInt64(inDataView, inOffset),\n        dcCompressedSize: parseInt64(inDataView, inOffset),\n        rleCompressedSize: parseInt64(inDataView, inOffset),\n        rleUncompressedSize: parseInt64(inDataView, inOffset),\n        rleRawSize: parseInt64(inDataView, inOffset),\n        totalAcUncompressedCount: parseInt64(inDataView, inOffset),\n        totalDcUncompressedCount: parseInt64(inDataView, inOffset),\n        acCompression: parseInt64(inDataView, inOffset)\n      };\n      if (dwaHeader.version < 2) {\n        throw \"EXRLoader.parse: \" + EXRHeader.compression + \" version \" + dwaHeader.version + \" is unsupported\";\n      }\n      var channelRules = new Array();\n      var ruleSize = parseUint16(inDataView, inOffset) - INT16_SIZE;\n      while (ruleSize > 0) {\n        var name = parseNullTerminatedString(inDataView.buffer, inOffset);\n        var value = parseUint8(inDataView, inOffset);\n        var compression = value >> 2 & 3;\n        var csc = (value >> 4) - 1;\n        var index = new Int8Array([csc])[0];\n        var type = parseUint8(inDataView, inOffset);\n        channelRules.push({\n          name,\n          index,\n          type,\n          compression\n        });\n        ruleSize -= name.length + 3;\n      }\n      var channels = EXRHeader.channels;\n      var channelData = new Array(info.channels);\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = channelData[i] = {};\n        var channel = channels[i];\n        cd.name = channel.name;\n        cd.compression = UNKNOWN;\n        cd.decoded = false;\n        cd.type = channel.pixelType;\n        cd.pLinear = channel.pLinear;\n        cd.width = info.width;\n        cd.height = info.lines;\n      }\n      var cscSet = {\n        idx: new Array(3)\n      };\n      for (var offset2 = 0; offset2 < info.channels; ++offset2) {\n        var cd = channelData[offset2];\n        for (var i = 0; i < channelRules.length; ++i) {\n          var rule = channelRules[i];\n          if (cd.name == rule.name) {\n            cd.compression = rule.compression;\n            if (rule.index >= 0) {\n              cscSet.idx[rule.index] = offset2;\n            }\n            cd.offset = offset2;\n          }\n        }\n      }\n      if (dwaHeader.acCompressedSize > 0) {\n        switch (dwaHeader.acCompression) {\n          case STATIC_HUFFMAN:\n            var acBuffer = new Uint16Array(dwaHeader.totalAcUncompressedCount);\n            hufUncompress(info.array, inDataView, inOffset, dwaHeader.acCompressedSize, acBuffer, dwaHeader.totalAcUncompressedCount);\n            break;\n          case DEFLATE:\n            var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.totalAcUncompressedCount);\n            var data = unzlibSync(compressed);\n            var acBuffer = new Uint16Array(data.buffer);\n            inOffset.value += dwaHeader.totalAcUncompressedCount;\n            break;\n        }\n      }\n      if (dwaHeader.dcCompressedSize > 0) {\n        var zlibInfo = {\n          array: info.array,\n          offset: inOffset,\n          size: dwaHeader.dcCompressedSize\n        };\n        var dcBuffer = new Uint16Array(uncompressZIP(zlibInfo).buffer);\n        inOffset.value += dwaHeader.dcCompressedSize;\n      }\n      if (dwaHeader.rleRawSize > 0) {\n        var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.rleCompressedSize);\n        var data = unzlibSync(compressed);\n        var rleBuffer = decodeRunLength(data.buffer);\n        inOffset.value += dwaHeader.rleCompressedSize;\n      }\n      var outBufferEnd = 0;\n      var rowOffsets = new Array(channelData.length);\n      for (var i = 0; i < rowOffsets.length; ++i) {\n        rowOffsets[i] = new Array();\n      }\n      for (var y = 0; y < info.lines; ++y) {\n        for (var chan = 0; chan < channelData.length; ++chan) {\n          rowOffsets[chan].push(outBufferEnd);\n          outBufferEnd += channelData[chan].width * info.type * INT16_SIZE;\n        }\n      }\n      lossyDctDecode(cscSet, rowOffsets, channelData, acBuffer, dcBuffer, outBuffer);\n      for (var i = 0; i < channelData.length; ++i) {\n        var cd = channelData[i];\n        if (cd.decoded) continue;\n        switch (cd.compression) {\n          case RLE:\n            var row = 0;\n            var rleOffset = 0;\n            for (var y = 0; y < info.lines; ++y) {\n              var rowOffsetBytes = rowOffsets[i][row];\n              for (var x = 0; x < cd.width; ++x) {\n                for (var byte = 0; byte < INT16_SIZE * cd.type; ++byte) {\n                  outBuffer[rowOffsetBytes++] = rleBuffer[rleOffset + byte * cd.width * cd.height];\n                }\n                rleOffset++;\n              }\n              row++;\n            }\n            break;\n          case LOSSY_DCT:\n          default:\n            throw \"EXRLoader.parse: unsupported channel compression\";\n        }\n      }\n      return new DataView(outBuffer.buffer);\n    }\n    function parseNullTerminatedString(buffer2, offset2) {\n      var uintBuffer = new Uint8Array(buffer2);\n      var endOffset = 0;\n      while (uintBuffer[offset2.value + endOffset] != 0) {\n        endOffset += 1;\n      }\n      var stringValue = new TextDecoder().decode(uintBuffer.slice(offset2.value, offset2.value + endOffset));\n      offset2.value = offset2.value + endOffset + 1;\n      return stringValue;\n    }\n    function parseFixedLengthString(buffer2, offset2, size) {\n      var stringValue = new TextDecoder().decode(new Uint8Array(buffer2).slice(offset2.value, offset2.value + size));\n      offset2.value = offset2.value + size;\n      return stringValue;\n    }\n    function parseRational(dataView, offset2) {\n      var x = parseInt32(dataView, offset2);\n      var y = parseUint32(dataView, offset2);\n      return [x, y];\n    }\n    function parseTimecode(dataView, offset2) {\n      var x = parseUint32(dataView, offset2);\n      var y = parseUint32(dataView, offset2);\n      return [x, y];\n    }\n    function parseInt32(dataView, offset2) {\n      var Int32 = dataView.getInt32(offset2.value, true);\n      offset2.value = offset2.value + INT32_SIZE;\n      return Int32;\n    }\n    function parseUint32(dataView, offset2) {\n      var Uint32 = dataView.getUint32(offset2.value, true);\n      offset2.value = offset2.value + INT32_SIZE;\n      return Uint32;\n    }\n    function parseUint8Array(uInt8Array2, offset2) {\n      var Uint8 = uInt8Array2[offset2.value];\n      offset2.value = offset2.value + INT8_SIZE;\n      return Uint8;\n    }\n    function parseUint8(dataView, offset2) {\n      var Uint8 = dataView.getUint8(offset2.value);\n      offset2.value = offset2.value + INT8_SIZE;\n      return Uint8;\n    }\n    const parseInt64 = function (dataView, offset2) {\n      let int;\n      if (\"getBigInt64\" in DataView.prototype) {\n        int = Number(dataView.getBigInt64(offset2.value, true));\n      } else {\n        int = dataView.getUint32(offset2.value + 4, true) + Number(dataView.getUint32(offset2.value, true) << 32);\n      }\n      offset2.value += ULONG_SIZE;\n      return int;\n    };\n    function parseFloat32(dataView, offset2) {\n      var float = dataView.getFloat32(offset2.value, true);\n      offset2.value += FLOAT32_SIZE;\n      return float;\n    }\n    function decodeFloat32(dataView, offset2) {\n      return DataUtils.toHalfFloat(parseFloat32(dataView, offset2));\n    }\n    function decodeFloat16(binary) {\n      var exponent = (binary & 31744) >> 10,\n        fraction = binary & 1023;\n      return (binary >> 15 ? -1 : 1) * (exponent ? exponent === 31 ? fraction ? NaN : Infinity : Math.pow(2, exponent - 15) * (1 + fraction / 1024) : 6103515625e-14 * (fraction / 1024));\n    }\n    function parseUint16(dataView, offset2) {\n      var Uint16 = dataView.getUint16(offset2.value, true);\n      offset2.value += INT16_SIZE;\n      return Uint16;\n    }\n    function parseFloat16(buffer2, offset2) {\n      return decodeFloat16(parseUint16(buffer2, offset2));\n    }\n    function parseChlist(dataView, buffer2, offset2, size) {\n      var startOffset = offset2.value;\n      var channels = [];\n      while (offset2.value < startOffset + size - 1) {\n        var name = parseNullTerminatedString(buffer2, offset2);\n        var pixelType = parseInt32(dataView, offset2);\n        var pLinear = parseUint8(dataView, offset2);\n        offset2.value += 3;\n        var xSampling = parseInt32(dataView, offset2);\n        var ySampling = parseInt32(dataView, offset2);\n        channels.push({\n          name,\n          pixelType,\n          pLinear,\n          xSampling,\n          ySampling\n        });\n      }\n      offset2.value += 1;\n      return channels;\n    }\n    function parseChromaticities(dataView, offset2) {\n      var redX = parseFloat32(dataView, offset2);\n      var redY = parseFloat32(dataView, offset2);\n      var greenX = parseFloat32(dataView, offset2);\n      var greenY = parseFloat32(dataView, offset2);\n      var blueX = parseFloat32(dataView, offset2);\n      var blueY = parseFloat32(dataView, offset2);\n      var whiteX = parseFloat32(dataView, offset2);\n      var whiteY = parseFloat32(dataView, offset2);\n      return {\n        redX,\n        redY,\n        greenX,\n        greenY,\n        blueX,\n        blueY,\n        whiteX,\n        whiteY\n      };\n    }\n    function parseCompression(dataView, offset2) {\n      var compressionCodes = [\"NO_COMPRESSION\", \"RLE_COMPRESSION\", \"ZIPS_COMPRESSION\", \"ZIP_COMPRESSION\", \"PIZ_COMPRESSION\", \"PXR24_COMPRESSION\", \"B44_COMPRESSION\", \"B44A_COMPRESSION\", \"DWAA_COMPRESSION\", \"DWAB_COMPRESSION\"];\n      var compression = parseUint8(dataView, offset2);\n      return compressionCodes[compression];\n    }\n    function parseBox2i(dataView, offset2) {\n      var xMin = parseUint32(dataView, offset2);\n      var yMin = parseUint32(dataView, offset2);\n      var xMax = parseUint32(dataView, offset2);\n      var yMax = parseUint32(dataView, offset2);\n      return {\n        xMin,\n        yMin,\n        xMax,\n        yMax\n      };\n    }\n    function parseLineOrder(dataView, offset2) {\n      var lineOrders = [\"INCREASING_Y\"];\n      var lineOrder = parseUint8(dataView, offset2);\n      return lineOrders[lineOrder];\n    }\n    function parseV2f(dataView, offset2) {\n      var x = parseFloat32(dataView, offset2);\n      var y = parseFloat32(dataView, offset2);\n      return [x, y];\n    }\n    function parseV3f(dataView, offset2) {\n      var x = parseFloat32(dataView, offset2);\n      var y = parseFloat32(dataView, offset2);\n      var z = parseFloat32(dataView, offset2);\n      return [x, y, z];\n    }\n    function parseValue(dataView, buffer2, offset2, type, size) {\n      if (type === \"string\" || type === \"stringvector\" || type === \"iccProfile\") {\n        return parseFixedLengthString(buffer2, offset2, size);\n      } else if (type === \"chlist\") {\n        return parseChlist(dataView, buffer2, offset2, size);\n      } else if (type === \"chromaticities\") {\n        return parseChromaticities(dataView, offset2);\n      } else if (type === \"compression\") {\n        return parseCompression(dataView, offset2);\n      } else if (type === \"box2i\") {\n        return parseBox2i(dataView, offset2);\n      } else if (type === \"lineOrder\") {\n        return parseLineOrder(dataView, offset2);\n      } else if (type === \"float\") {\n        return parseFloat32(dataView, offset2);\n      } else if (type === \"v2f\") {\n        return parseV2f(dataView, offset2);\n      } else if (type === \"v3f\") {\n        return parseV3f(dataView, offset2);\n      } else if (type === \"int\") {\n        return parseInt32(dataView, offset2);\n      } else if (type === \"rational\") {\n        return parseRational(dataView, offset2);\n      } else if (type === \"timecode\") {\n        return parseTimecode(dataView, offset2);\n      } else if (type === \"preview\") {\n        offset2.value += size;\n        return \"skipped\";\n      } else {\n        offset2.value += size;\n        return void 0;\n      }\n    }\n    function parseHeader(dataView, buffer2, offset2) {\n      const EXRHeader2 = {};\n      if (dataView.getUint32(0, true) != 20000630) {\n        throw \"THREE.EXRLoader: provided file doesn't appear to be in OpenEXR format.\";\n      }\n      EXRHeader2.version = dataView.getUint8(4);\n      const spec = dataView.getUint8(5);\n      EXRHeader2.spec = {\n        singleTile: !!(spec & 2),\n        longName: !!(spec & 4),\n        deepFormat: !!(spec & 8),\n        multiPart: !!(spec & 16)\n      };\n      offset2.value = 8;\n      var keepReading = true;\n      while (keepReading) {\n        var attributeName = parseNullTerminatedString(buffer2, offset2);\n        if (attributeName == 0) {\n          keepReading = false;\n        } else {\n          var attributeType = parseNullTerminatedString(buffer2, offset2);\n          var attributeSize = parseUint32(dataView, offset2);\n          var attributeValue = parseValue(dataView, buffer2, offset2, attributeType, attributeSize);\n          if (attributeValue === void 0) {\n            console.warn(`EXRLoader.parse: skipped unknown header attribute type '${attributeType}'.`);\n          } else {\n            EXRHeader2[attributeName] = attributeValue;\n          }\n        }\n      }\n      if ((spec & ~4) != 0) {\n        console.error(\"EXRHeader:\", EXRHeader2);\n        throw \"THREE.EXRLoader: provided file is currently unsupported.\";\n      }\n      return EXRHeader2;\n    }\n    function setupDecoder(EXRHeader2, dataView, uInt8Array2, offset2, outputType) {\n      const EXRDecoder2 = {\n        size: 0,\n        viewer: dataView,\n        array: uInt8Array2,\n        offset: offset2,\n        width: EXRHeader2.dataWindow.xMax - EXRHeader2.dataWindow.xMin + 1,\n        height: EXRHeader2.dataWindow.yMax - EXRHeader2.dataWindow.yMin + 1,\n        channels: EXRHeader2.channels.length,\n        bytesPerLine: null,\n        lines: null,\n        inputSize: null,\n        type: EXRHeader2.channels[0].pixelType,\n        uncompress: null,\n        getter: null,\n        format: null,\n        [hasColorSpace ? \"colorSpace\" : \"encoding\"]: null\n      };\n      switch (EXRHeader2.compression) {\n        case \"NO_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressRAW;\n          break;\n        case \"RLE_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressRLE;\n          break;\n        case \"ZIPS_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressZIP;\n          break;\n        case \"ZIP_COMPRESSION\":\n          EXRDecoder2.lines = 16;\n          EXRDecoder2.uncompress = uncompressZIP;\n          break;\n        case \"PIZ_COMPRESSION\":\n          EXRDecoder2.lines = 32;\n          EXRDecoder2.uncompress = uncompressPIZ;\n          break;\n        case \"PXR24_COMPRESSION\":\n          EXRDecoder2.lines = 16;\n          EXRDecoder2.uncompress = uncompressPXR;\n          break;\n        case \"DWAA_COMPRESSION\":\n          EXRDecoder2.lines = 32;\n          EXRDecoder2.uncompress = uncompressDWA;\n          break;\n        case \"DWAB_COMPRESSION\":\n          EXRDecoder2.lines = 256;\n          EXRDecoder2.uncompress = uncompressDWA;\n          break;\n        default:\n          throw \"EXRLoader.parse: \" + EXRHeader2.compression + \" is unsupported\";\n      }\n      EXRDecoder2.scanlineBlockSize = EXRDecoder2.lines;\n      if (EXRDecoder2.type == 1) {\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder2.getter = parseFloat16;\n            EXRDecoder2.inputSize = INT16_SIZE;\n            break;\n          case HalfFloatType:\n            EXRDecoder2.getter = parseUint16;\n            EXRDecoder2.inputSize = INT16_SIZE;\n            break;\n        }\n      } else if (EXRDecoder2.type == 2) {\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder2.getter = parseFloat32;\n            EXRDecoder2.inputSize = FLOAT32_SIZE;\n            break;\n          case HalfFloatType:\n            EXRDecoder2.getter = decodeFloat32;\n            EXRDecoder2.inputSize = FLOAT32_SIZE;\n        }\n      } else {\n        throw \"EXRLoader.parse: unsupported pixelType \" + EXRDecoder2.type + \" for \" + EXRHeader2.compression + \".\";\n      }\n      EXRDecoder2.blockCount = (EXRHeader2.dataWindow.yMax + 1) / EXRDecoder2.scanlineBlockSize;\n      for (var i = 0; i < EXRDecoder2.blockCount; i++) parseInt64(dataView, offset2);\n      EXRDecoder2.outputChannels = EXRDecoder2.channels == 3 ? 4 : EXRDecoder2.channels;\n      const size = EXRDecoder2.width * EXRDecoder2.height * EXRDecoder2.outputChannels;\n      switch (outputType) {\n        case FloatType:\n          EXRDecoder2.byteArray = new Float32Array(size);\n          if (EXRDecoder2.channels < EXRDecoder2.outputChannels) EXRDecoder2.byteArray.fill(1, 0, size);\n          break;\n        case HalfFloatType:\n          EXRDecoder2.byteArray = new Uint16Array(size);\n          if (EXRDecoder2.channels < EXRDecoder2.outputChannels) EXRDecoder2.byteArray.fill(15360, 0, size);\n          break;\n        default:\n          console.error(\"THREE.EXRLoader: unsupported type: \", outputType);\n          break;\n      }\n      EXRDecoder2.bytesPerLine = EXRDecoder2.width * EXRDecoder2.inputSize * EXRDecoder2.channels;\n      if (EXRDecoder2.outputChannels == 4) EXRDecoder2.format = RGBAFormat;else EXRDecoder2.format = RedFormat;\n      if (hasColorSpace) EXRDecoder2.colorSpace = \"srgb-linear\";else EXRDecoder2.encoding = 3e3;\n      return EXRDecoder2;\n    }\n    const bufferDataView = new DataView(buffer);\n    const uInt8Array = new Uint8Array(buffer);\n    const offset = {\n      value: 0\n    };\n    const EXRHeader = parseHeader(bufferDataView, buffer, offset);\n    const EXRDecoder = setupDecoder(EXRHeader, bufferDataView, uInt8Array, offset, this.type);\n    const tmpOffset = {\n      value: 0\n    };\n    const channelOffsets = {\n      R: 0,\n      G: 1,\n      B: 2,\n      A: 3,\n      Y: 0\n    };\n    for (let scanlineBlockIdx = 0; scanlineBlockIdx < EXRDecoder.height / EXRDecoder.scanlineBlockSize; scanlineBlockIdx++) {\n      const line = parseUint32(bufferDataView, offset);\n      EXRDecoder.size = parseUint32(bufferDataView, offset);\n      EXRDecoder.lines = line + EXRDecoder.scanlineBlockSize > EXRDecoder.height ? EXRDecoder.height - line : EXRDecoder.scanlineBlockSize;\n      const isCompressed = EXRDecoder.size < EXRDecoder.lines * EXRDecoder.bytesPerLine;\n      const viewer = isCompressed ? EXRDecoder.uncompress(EXRDecoder) : uncompressRAW(EXRDecoder);\n      offset.value += EXRDecoder.size;\n      for (let line_y = 0; line_y < EXRDecoder.scanlineBlockSize; line_y++) {\n        const true_y = line_y + scanlineBlockIdx * EXRDecoder.scanlineBlockSize;\n        if (true_y >= EXRDecoder.height) break;\n        for (let channelID = 0; channelID < EXRDecoder.channels; channelID++) {\n          const cOff = channelOffsets[EXRHeader.channels[channelID].name];\n          for (let x = 0; x < EXRDecoder.width; x++) {\n            tmpOffset.value = (line_y * (EXRDecoder.channels * EXRDecoder.width) + channelID * EXRDecoder.width + x) * EXRDecoder.inputSize;\n            const outIndex = (EXRDecoder.height - 1 - true_y) * (EXRDecoder.width * EXRDecoder.outputChannels) + x * EXRDecoder.outputChannels + cOff;\n            EXRDecoder.byteArray[outIndex] = EXRDecoder.getter(viewer, tmpOffset);\n          }\n        }\n      }\n    }\n    return {\n      header: EXRHeader,\n      width: EXRDecoder.width,\n      height: EXRDecoder.height,\n      data: EXRDecoder.byteArray,\n      format: EXRDecoder.format,\n      [hasColorSpace ? \"colorSpace\" : \"encoding\"]: EXRDecoder[hasColorSpace ? \"colorSpace\" : \"encoding\"],\n      type: this.type\n    };\n  }\n  setDataType(value) {\n    this.type = value;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      if (hasColorSpace) texture.colorSpace = texData.colorSpace;else texture.encoding = texData.encoding;\n      texture.minFilter = LinearFilter;\n      texture.magFilter = LinearFilter;\n      texture.generateMipmaps = false;\n      texture.flipY = false;\n      if (onLoad) onLoad(texture, texData);\n    }\n    return super.load(url, onLoadCallback, onProgress, onError);\n  }\n}\nexport { EXRLoader };", "map": {"version": 3, "names": ["hasColorSpace", "version", "EXRLoader", "DataTextureLoader", "constructor", "manager", "type", "HalfFloatType", "parse", "buffer", "USHORT_RANGE", "BITMAP_SIZE", "HUF_ENCBITS", "HUF_DECBITS", "HUF_ENCSIZE", "HUF_DECSIZE", "HUF_DECMASK", "NBITS", "A_OFFSET", "MOD_MASK", "SHORT_ZEROCODE_RUN", "LONG_ZEROCODE_RUN", "SHORTEST_LONG_RUN", "ULONG_SIZE", "FLOAT32_SIZE", "INT32_SIZE", "INT16_SIZE", "INT8_SIZE", "STATIC_HUFFMAN", "DEFLATE", "UNKNOWN", "LOSSY_DCT", "RLE", "logBase", "Math", "pow", "reverseLutFromBitmap", "bitmap", "lut", "k", "i", "n", "hufClearDecTable", "hdec", "len", "lit", "p", "getBitsReturn", "l", "c", "lc", "getBits", "nBits", "uInt8Array2", "inOffset", "parseUint8Array", "hufTable<PERSON>uffer", "Array", "hufCanonicalCodeTable", "hcode", "nc", "hufUnpackEncTable", "inDataView", "ni", "im", "iM", "value", "zerun", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code", "hufCode", "hufBuildDecTable", "hdecod", "pl", "plOffset", "getCharReturn", "getChar", "getCodeReturn", "getCode", "po", "rlc", "outBuffer", "outBufferOffset", "outBufferEndOffset", "cs", "Uint8Array", "s", "UInt16", "Int16", "ref", "wdec14Return", "a", "b", "wdec14", "h", "ls", "hs", "hi", "ai", "as", "bs", "wdec16", "m", "d", "bb", "aa", "wav2Decode", "buffer2", "j", "nx", "ox", "ny", "oy", "mx", "w14", "p2", "py", "ey", "oy1", "oy2", "ox1", "ox2", "i00", "i01", "i10", "i11", "px", "ex", "p01", "p10", "p11", "hufDecode", "encodingTable", "decodingTable", "no", "outOffset", "inOffsetEnd", "trunc", "index", "huf<PERSON>ncompress", "nCompressed", "nRaw", "initialInOffset", "parseUint32", "freq", "applyLut", "data", "nData", "predictor", "source", "t", "length", "interleaveScalar", "out", "t1", "t2", "floor", "stop", "decodeRun<PERSON>ength", "size", "byteLength", "reader", "DataView", "getInt8", "count", "push", "getUint8", "lossyDctDecode", "cscSet", "rowPtrs", "channelData", "a<PERSON><PERSON><PERSON><PERSON>", "d<PERSON><PERSON><PERSON><PERSON>", "dataView", "width", "idx", "height", "numComp", "numFullBlocksX", "numBlocksX", "ceil", "numBlocksY", "leftoverX", "leftoverY", "currAc<PERSON>omp", "currDc<PERSON>omp", "dctData", "halfZigBlock", "rowBlock", "rowOffsets", "comp2", "Float32Array", "Uint16Array", "blocky", "maxY", "maxX", "blockx", "fill", "unRleAC", "unZigZag", "dctInverse", "csc709Inverse", "convertToHalf", "offset2", "type2", "y2", "src", "setUint16", "offset3", "x2", "halfRow", "comp", "decoded", "y", "x", "getUint16", "setFloat32", "decodeFloat16", "acValue", "dctComp", "dst", "cos", "e", "f", "g", "alpha", "beta", "theta", "gamma", "row", "rowPtr", "column", "cb", "cr", "DataUtils", "toHalfFloat", "toLinear", "float", "sign", "abs", "uncompressRAW", "info", "array", "offset", "uncompressRLE", "compressed", "viewer", "slice", "<PERSON><PERSON><PERSON><PERSON>", "tmp<PERSON><PERSON><PERSON>", "uncompressZIP", "unzlibSync", "uncompressPIZ", "scanlineBlockSize", "channels", "outBufferEnd", "pizChannelData", "lines", "minNonZero", "parseUint16", "maxNonZero", "parseUint8", "maxValue", "cd", "start", "tmpOffset2", "cp", "end", "set", "uncompressPXR", "sz", "Uint32Array", "tmpBufferEnd", "writePtr", "ptr", "pixel", "diff", "uncompressDWA", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt64", "unknownUncompressedSize", "unknownCompressedSize", "acCompressedSize", "dcCompressedSize", "rleCompressedSize", "rleUncompressedSize", "rleRawSize", "totalAcUncompressedCount", "totalDcUncompressedCount", "acCompression", "EXRHeader", "compression", "channelRules", "ruleSize", "name", "parseNullTerminatedString", "csc", "Int8Array", "channel", "pixelType", "pLinear", "rule", "zlibInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chan", "rleOffset", "rowOffsetBytes", "byte", "uint<PERSON><PERSON><PERSON>", "endOffset", "stringValue", "TextDecoder", "decode", "parseFixedLengthString", "parseRational", "parseInt32", "parseTimecode", "Int32", "getInt32", "Uint32", "getUint32", "Uint8", "int", "prototype", "Number", "getBigInt64", "parseFloat32", "getFloat32", "decodeFloat32", "binary", "exponent", "fraction", "NaN", "Infinity", "Uint16", "parseFloat16", "parse<PERSON><PERSON><PERSON>", "startOffset", "xSampling", "ySampling", "parseChromaticities", "redX", "redY", "greenX", "greenY", "blueX", "blueY", "whiteX", "whiteY", "parseCompression", "compressionCodes", "parseBox2i", "xMin", "yMin", "xMax", "yMax", "parseLineOrder", "lineOrders", "lineOrder", "parseV2f", "parseV3f", "z", "parseValue", "parse<PERSON><PERSON><PERSON>", "EXRHeader2", "spec", "singleTile", "<PERSON><PERSON><PERSON>", "deepFormat", "multiPart", "keepReading", "attributeName", "attributeType", "attributeSize", "attributeValue", "console", "warn", "error", "setupDecoder", "outputType", "EXRDecoder2", "dataWindow", "bytesPerLine", "inputSize", "uncompress", "getter", "format", "FloatType", "blockCount", "outputChannels", "byteArray", "RGBAFormat", "RedFormat", "colorSpace", "encoding", "bufferDataView", "uInt8Array", "EXRDecoder", "tmpOffset", "channelOffsets", "R", "G", "B", "A", "Y", "scanlineBlockIdx", "line", "isCompressed", "line_y", "true_y", "channelID", "cOff", "outIndex", "header", "setDataType", "load", "url", "onLoad", "onProgress", "onError", "onLoadCallback", "texture", "texData", "minFilter", "LinearFilter", "magFilter", "generateMipmaps", "flipY"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/EXRLoader.js"], "sourcesContent": ["import {\n  Texture,\n  DataTextureLoader,\n  DataUtils,\n  FloatType,\n  HalfFloatType,\n  LinearFilter,\n  RedFormat,\n  RGBAFormat,\n} from 'three'\nimport { unzlibSync } from 'fflate'\nimport { version } from '../_polyfill/constants'\n\n/**\n * OpenEXR loader currently supports uncompressed, ZIP(S), RLE, PIZ and DWA/B compression.\n * Supports reading as UnsignedByte, HalfFloat and Float type data texture.\n *\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation, so I have preserved their copyright notices.\n */\n\n// /*\n// Copyright (c) 2014 - 2017, Syoyo Fujita\n// All rights reserved.\n\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n\n// // End of OpenEXR license -------------------------------------------------\n\n// https://github.com/mrdoob/three.js/pull/25771\nconst hasColorSpace = version >= 152\n\nclass EXRLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n  }\n\n  parse(buffer) {\n    const USHORT_RANGE = 1 << 16\n    const BITMAP_SIZE = USHORT_RANGE >> 3\n\n    const HUF_ENCBITS = 16 // literal (value) bit length\n    const HUF_DECBITS = 14 // decoding bit size (>= 8)\n\n    const HUF_ENCSIZE = (1 << HUF_ENCBITS) + 1 // encoding table size\n    const HUF_DECSIZE = 1 << HUF_DECBITS // decoding table size\n    const HUF_DECMASK = HUF_DECSIZE - 1\n\n    const NBITS = 16\n    const A_OFFSET = 1 << (NBITS - 1)\n    const MOD_MASK = (1 << NBITS) - 1\n\n    const SHORT_ZEROCODE_RUN = 59\n    const LONG_ZEROCODE_RUN = 63\n    const SHORTEST_LONG_RUN = 2 + LONG_ZEROCODE_RUN - SHORT_ZEROCODE_RUN\n\n    const ULONG_SIZE = 8\n    const FLOAT32_SIZE = 4\n    const INT32_SIZE = 4\n    const INT16_SIZE = 2\n    const INT8_SIZE = 1\n\n    const STATIC_HUFFMAN = 0\n    const DEFLATE = 1\n\n    const UNKNOWN = 0\n    const LOSSY_DCT = 1\n    const RLE = 2\n\n    const logBase = Math.pow(2.7182818, 2.2)\n\n    function reverseLutFromBitmap(bitmap, lut) {\n      var k = 0\n\n      for (var i = 0; i < USHORT_RANGE; ++i) {\n        if (i == 0 || bitmap[i >> 3] & (1 << (i & 7))) {\n          lut[k++] = i\n        }\n      }\n\n      var n = k - 1\n\n      while (k < USHORT_RANGE) lut[k++] = 0\n\n      return n\n    }\n\n    function hufClearDecTable(hdec) {\n      for (var i = 0; i < HUF_DECSIZE; i++) {\n        hdec[i] = {}\n        hdec[i].len = 0\n        hdec[i].lit = 0\n        hdec[i].p = null\n      }\n    }\n\n    const getBitsReturn = { l: 0, c: 0, lc: 0 }\n\n    function getBits(nBits, c, lc, uInt8Array, inOffset) {\n      while (lc < nBits) {\n        c = (c << 8) | parseUint8Array(uInt8Array, inOffset)\n        lc += 8\n      }\n\n      lc -= nBits\n\n      getBitsReturn.l = (c >> lc) & ((1 << nBits) - 1)\n      getBitsReturn.c = c\n      getBitsReturn.lc = lc\n    }\n\n    const hufTableBuffer = new Array(59)\n\n    function hufCanonicalCodeTable(hcode) {\n      for (var i = 0; i <= 58; ++i) hufTableBuffer[i] = 0\n      for (var i = 0; i < HUF_ENCSIZE; ++i) hufTableBuffer[hcode[i]] += 1\n\n      var c = 0\n\n      for (var i = 58; i > 0; --i) {\n        var nc = (c + hufTableBuffer[i]) >> 1\n        hufTableBuffer[i] = c\n        c = nc\n      }\n\n      for (var i = 0; i < HUF_ENCSIZE; ++i) {\n        var l = hcode[i]\n        if (l > 0) hcode[i] = l | (hufTableBuffer[l]++ << 6)\n      }\n    }\n\n    function hufUnpackEncTable(uInt8Array, inDataView, inOffset, ni, im, iM, hcode) {\n      var p = inOffset\n      var c = 0\n      var lc = 0\n\n      for (; im <= iM; im++) {\n        if (p.value - inOffset.value > ni) return false\n\n        getBits(6, c, lc, uInt8Array, p)\n\n        var l = getBitsReturn.l\n        c = getBitsReturn.c\n        lc = getBitsReturn.lc\n\n        hcode[im] = l\n\n        if (l == LONG_ZEROCODE_RUN) {\n          if (p.value - inOffset.value > ni) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          getBits(8, c, lc, uInt8Array, p)\n\n          var zerun = getBitsReturn.l + SHORTEST_LONG_RUN\n          c = getBitsReturn.c\n          lc = getBitsReturn.lc\n\n          if (im + zerun > iM + 1) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          while (zerun--) hcode[im++] = 0\n\n          im--\n        } else if (l >= SHORT_ZEROCODE_RUN) {\n          var zerun = l - SHORT_ZEROCODE_RUN + 2\n\n          if (im + zerun > iM + 1) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          while (zerun--) hcode[im++] = 0\n\n          im--\n        }\n      }\n\n      hufCanonicalCodeTable(hcode)\n    }\n\n    function hufLength(code) {\n      return code & 63\n    }\n\n    function hufCode(code) {\n      return code >> 6\n    }\n\n    function hufBuildDecTable(hcode, im, iM, hdecod) {\n      for (; im <= iM; im++) {\n        var c = hufCode(hcode[im])\n        var l = hufLength(hcode[im])\n\n        if (c >> l) {\n          throw 'Invalid table entry'\n        }\n\n        if (l > HUF_DECBITS) {\n          var pl = hdecod[c >> (l - HUF_DECBITS)]\n\n          if (pl.len) {\n            throw 'Invalid table entry'\n          }\n\n          pl.lit++\n\n          if (pl.p) {\n            var p = pl.p\n            pl.p = new Array(pl.lit)\n\n            for (var i = 0; i < pl.lit - 1; ++i) {\n              pl.p[i] = p[i]\n            }\n          } else {\n            pl.p = new Array(1)\n          }\n\n          pl.p[pl.lit - 1] = im\n        } else if (l) {\n          var plOffset = 0\n\n          for (var i = 1 << (HUF_DECBITS - l); i > 0; i--) {\n            var pl = hdecod[(c << (HUF_DECBITS - l)) + plOffset]\n\n            if (pl.len || pl.p) {\n              throw 'Invalid table entry'\n            }\n\n            pl.len = l\n            pl.lit = im\n\n            plOffset++\n          }\n        }\n      }\n\n      return true\n    }\n\n    const getCharReturn = { c: 0, lc: 0 }\n\n    function getChar(c, lc, uInt8Array, inOffset) {\n      c = (c << 8) | parseUint8Array(uInt8Array, inOffset)\n      lc += 8\n\n      getCharReturn.c = c\n      getCharReturn.lc = lc\n    }\n\n    const getCodeReturn = { c: 0, lc: 0 }\n\n    function getCode(po, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outBufferOffset, outBufferEndOffset) {\n      if (po == rlc) {\n        if (lc < 8) {\n          getChar(c, lc, uInt8Array, inOffset)\n          c = getCharReturn.c\n          lc = getCharReturn.lc\n        }\n\n        lc -= 8\n\n        var cs = c >> lc\n        var cs = new Uint8Array([cs])[0]\n\n        if (outBufferOffset.value + cs > outBufferEndOffset) {\n          return false\n        }\n\n        var s = outBuffer[outBufferOffset.value - 1]\n\n        while (cs-- > 0) {\n          outBuffer[outBufferOffset.value++] = s\n        }\n      } else if (outBufferOffset.value < outBufferEndOffset) {\n        outBuffer[outBufferOffset.value++] = po\n      } else {\n        return false\n      }\n\n      getCodeReturn.c = c\n      getCodeReturn.lc = lc\n    }\n\n    function UInt16(value) {\n      return value & 0xffff\n    }\n\n    function Int16(value) {\n      var ref = UInt16(value)\n      return ref > 0x7fff ? ref - 0x10000 : ref\n    }\n\n    const wdec14Return = { a: 0, b: 0 }\n\n    function wdec14(l, h) {\n      var ls = Int16(l)\n      var hs = Int16(h)\n\n      var hi = hs\n      var ai = ls + (hi & 1) + (hi >> 1)\n\n      var as = ai\n      var bs = ai - hi\n\n      wdec14Return.a = as\n      wdec14Return.b = bs\n    }\n\n    function wdec16(l, h) {\n      var m = UInt16(l)\n      var d = UInt16(h)\n\n      var bb = (m - (d >> 1)) & MOD_MASK\n      var aa = (d + bb - A_OFFSET) & MOD_MASK\n\n      wdec14Return.a = aa\n      wdec14Return.b = bb\n    }\n\n    function wav2Decode(buffer, j, nx, ox, ny, oy, mx) {\n      var w14 = mx < 1 << 14\n      var n = nx > ny ? ny : nx\n      var p = 1\n      var p2\n\n      while (p <= n) p <<= 1\n\n      p >>= 1\n      p2 = p\n      p >>= 1\n\n      while (p >= 1) {\n        var py = 0\n        var ey = py + oy * (ny - p2)\n        var oy1 = oy * p\n        var oy2 = oy * p2\n        var ox1 = ox * p\n        var ox2 = ox * p2\n        var i00, i01, i10, i11\n\n        for (; py <= ey; py += oy2) {\n          var px = py\n          var ex = py + ox * (nx - p2)\n\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1\n            var p10 = px + oy1\n            var p11 = p10 + ox1\n\n            if (w14) {\n              wdec14(buffer[px + j], buffer[p10 + j])\n\n              i00 = wdec14Return.a\n              i10 = wdec14Return.b\n\n              wdec14(buffer[p01 + j], buffer[p11 + j])\n\n              i01 = wdec14Return.a\n              i11 = wdec14Return.b\n\n              wdec14(i00, i01)\n\n              buffer[px + j] = wdec14Return.a\n              buffer[p01 + j] = wdec14Return.b\n\n              wdec14(i10, i11)\n\n              buffer[p10 + j] = wdec14Return.a\n              buffer[p11 + j] = wdec14Return.b\n            } else {\n              wdec16(buffer[px + j], buffer[p10 + j])\n\n              i00 = wdec14Return.a\n              i10 = wdec14Return.b\n\n              wdec16(buffer[p01 + j], buffer[p11 + j])\n\n              i01 = wdec14Return.a\n              i11 = wdec14Return.b\n\n              wdec16(i00, i01)\n\n              buffer[px + j] = wdec14Return.a\n              buffer[p01 + j] = wdec14Return.b\n\n              wdec16(i10, i11)\n\n              buffer[p10 + j] = wdec14Return.a\n              buffer[p11 + j] = wdec14Return.b\n            }\n          }\n\n          if (nx & p) {\n            var p10 = px + oy1\n\n            if (w14) wdec14(buffer[px + j], buffer[p10 + j])\n            else wdec16(buffer[px + j], buffer[p10 + j])\n\n            i00 = wdec14Return.a\n            buffer[p10 + j] = wdec14Return.b\n\n            buffer[px + j] = i00\n          }\n        }\n\n        if (ny & p) {\n          var px = py\n          var ex = py + ox * (nx - p2)\n\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1\n\n            if (w14) wdec14(buffer[px + j], buffer[p01 + j])\n            else wdec16(buffer[px + j], buffer[p01 + j])\n\n            i00 = wdec14Return.a\n            buffer[p01 + j] = wdec14Return.b\n\n            buffer[px + j] = i00\n          }\n        }\n\n        p2 = p\n        p >>= 1\n      }\n\n      return py\n    }\n\n    function hufDecode(\n      encodingTable,\n      decodingTable,\n      uInt8Array,\n      inDataView,\n      inOffset,\n      ni,\n      rlc,\n      no,\n      outBuffer,\n      outOffset,\n    ) {\n      var c = 0\n      var lc = 0\n      var outBufferEndOffset = no\n      var inOffsetEnd = Math.trunc(inOffset.value + (ni + 7) / 8)\n\n      while (inOffset.value < inOffsetEnd) {\n        getChar(c, lc, uInt8Array, inOffset)\n\n        c = getCharReturn.c\n        lc = getCharReturn.lc\n\n        while (lc >= HUF_DECBITS) {\n          var index = (c >> (lc - HUF_DECBITS)) & HUF_DECMASK\n          var pl = decodingTable[index]\n\n          if (pl.len) {\n            lc -= pl.len\n\n            getCode(pl.lit, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset)\n\n            c = getCodeReturn.c\n            lc = getCodeReturn.lc\n          } else {\n            if (!pl.p) {\n              throw 'hufDecode issues'\n            }\n\n            var j\n\n            for (j = 0; j < pl.lit; j++) {\n              var l = hufLength(encodingTable[pl.p[j]])\n\n              while (lc < l && inOffset.value < inOffsetEnd) {\n                getChar(c, lc, uInt8Array, inOffset)\n\n                c = getCharReturn.c\n                lc = getCharReturn.lc\n              }\n\n              if (lc >= l) {\n                if (hufCode(encodingTable[pl.p[j]]) == ((c >> (lc - l)) & ((1 << l) - 1))) {\n                  lc -= l\n\n                  getCode(\n                    pl.p[j],\n                    rlc,\n                    c,\n                    lc,\n                    uInt8Array,\n                    inDataView,\n                    inOffset,\n                    outBuffer,\n                    outOffset,\n                    outBufferEndOffset,\n                  )\n\n                  c = getCodeReturn.c\n                  lc = getCodeReturn.lc\n\n                  break\n                }\n              }\n            }\n\n            if (j == pl.lit) {\n              throw 'hufDecode issues'\n            }\n          }\n        }\n      }\n\n      var i = (8 - ni) & 7\n\n      c >>= i\n      lc -= i\n\n      while (lc > 0) {\n        var pl = decodingTable[(c << (HUF_DECBITS - lc)) & HUF_DECMASK]\n\n        if (pl.len) {\n          lc -= pl.len\n\n          getCode(pl.lit, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset)\n\n          c = getCodeReturn.c\n          lc = getCodeReturn.lc\n        } else {\n          throw 'hufDecode issues'\n        }\n      }\n\n      return true\n    }\n\n    function hufUncompress(uInt8Array, inDataView, inOffset, nCompressed, outBuffer, nRaw) {\n      var outOffset = { value: 0 }\n      var initialInOffset = inOffset.value\n\n      var im = parseUint32(inDataView, inOffset)\n      var iM = parseUint32(inDataView, inOffset)\n\n      inOffset.value += 4\n\n      var nBits = parseUint32(inDataView, inOffset)\n\n      inOffset.value += 4\n\n      if (im < 0 || im >= HUF_ENCSIZE || iM < 0 || iM >= HUF_ENCSIZE) {\n        throw 'Something wrong with HUF_ENCSIZE'\n      }\n\n      var freq = new Array(HUF_ENCSIZE)\n      var hdec = new Array(HUF_DECSIZE)\n\n      hufClearDecTable(hdec)\n\n      var ni = nCompressed - (inOffset.value - initialInOffset)\n\n      hufUnpackEncTable(uInt8Array, inDataView, inOffset, ni, im, iM, freq)\n\n      if (nBits > 8 * (nCompressed - (inOffset.value - initialInOffset))) {\n        throw 'Something wrong with hufUncompress'\n      }\n\n      hufBuildDecTable(freq, im, iM, hdec)\n\n      hufDecode(freq, hdec, uInt8Array, inDataView, inOffset, nBits, iM, nRaw, outBuffer, outOffset)\n    }\n\n    function applyLut(lut, data, nData) {\n      for (var i = 0; i < nData; ++i) {\n        data[i] = lut[data[i]]\n      }\n    }\n\n    function predictor(source) {\n      for (var t = 1; t < source.length; t++) {\n        var d = source[t - 1] + source[t] - 128\n        source[t] = d\n      }\n    }\n\n    function interleaveScalar(source, out) {\n      var t1 = 0\n      var t2 = Math.floor((source.length + 1) / 2)\n      var s = 0\n      var stop = source.length - 1\n\n      while (true) {\n        if (s > stop) break\n        out[s++] = source[t1++]\n\n        if (s > stop) break\n        out[s++] = source[t2++]\n      }\n    }\n\n    function decodeRunLength(source) {\n      var size = source.byteLength\n      var out = new Array()\n      var p = 0\n\n      var reader = new DataView(source)\n\n      while (size > 0) {\n        var l = reader.getInt8(p++)\n\n        if (l < 0) {\n          var count = -l\n          size -= count + 1\n\n          for (var i = 0; i < count; i++) {\n            out.push(reader.getUint8(p++))\n          }\n        } else {\n          var count = l\n          size -= 2\n\n          var value = reader.getUint8(p++)\n\n          for (var i = 0; i < count + 1; i++) {\n            out.push(value)\n          }\n        }\n      }\n\n      return out\n    }\n\n    function lossyDctDecode(cscSet, rowPtrs, channelData, acBuffer, dcBuffer, outBuffer) {\n      var dataView = new DataView(outBuffer.buffer)\n\n      var width = channelData[cscSet.idx[0]].width\n      var height = channelData[cscSet.idx[0]].height\n\n      var numComp = 3\n\n      var numFullBlocksX = Math.floor(width / 8.0)\n      var numBlocksX = Math.ceil(width / 8.0)\n      var numBlocksY = Math.ceil(height / 8.0)\n      var leftoverX = width - (numBlocksX - 1) * 8\n      var leftoverY = height - (numBlocksY - 1) * 8\n\n      var currAcComp = { value: 0 }\n      var currDcComp = new Array(numComp)\n      var dctData = new Array(numComp)\n      var halfZigBlock = new Array(numComp)\n      var rowBlock = new Array(numComp)\n      var rowOffsets = new Array(numComp)\n\n      for (let comp = 0; comp < numComp; ++comp) {\n        rowOffsets[comp] = rowPtrs[cscSet.idx[comp]]\n        currDcComp[comp] = comp < 1 ? 0 : currDcComp[comp - 1] + numBlocksX * numBlocksY\n        dctData[comp] = new Float32Array(64)\n        halfZigBlock[comp] = new Uint16Array(64)\n        rowBlock[comp] = new Uint16Array(numBlocksX * 64)\n      }\n\n      for (let blocky = 0; blocky < numBlocksY; ++blocky) {\n        var maxY = 8\n\n        if (blocky == numBlocksY - 1) maxY = leftoverY\n\n        var maxX = 8\n\n        for (let blockx = 0; blockx < numBlocksX; ++blockx) {\n          if (blockx == numBlocksX - 1) maxX = leftoverX\n\n          for (let comp = 0; comp < numComp; ++comp) {\n            halfZigBlock[comp].fill(0)\n\n            // set block DC component\n            halfZigBlock[comp][0] = dcBuffer[currDcComp[comp]++]\n            // set block AC components\n            unRleAC(currAcComp, acBuffer, halfZigBlock[comp])\n\n            // UnZigZag block to float\n            unZigZag(halfZigBlock[comp], dctData[comp])\n            // decode float dct\n            dctInverse(dctData[comp])\n          }\n\n          if (numComp == 3) {\n            csc709Inverse(dctData)\n          }\n\n          for (let comp = 0; comp < numComp; ++comp) {\n            convertToHalf(dctData[comp], rowBlock[comp], blockx * 64)\n          }\n        } // blockx\n\n        let offset = 0\n\n        for (let comp = 0; comp < numComp; ++comp) {\n          const type = channelData[cscSet.idx[comp]].type\n\n          for (let y = 8 * blocky; y < 8 * blocky + maxY; ++y) {\n            offset = rowOffsets[comp][y]\n\n            for (let blockx = 0; blockx < numFullBlocksX; ++blockx) {\n              const src = blockx * 64 + (y & 0x7) * 8\n\n              dataView.setUint16(offset + 0 * INT16_SIZE * type, rowBlock[comp][src + 0], true)\n              dataView.setUint16(offset + 1 * INT16_SIZE * type, rowBlock[comp][src + 1], true)\n              dataView.setUint16(offset + 2 * INT16_SIZE * type, rowBlock[comp][src + 2], true)\n              dataView.setUint16(offset + 3 * INT16_SIZE * type, rowBlock[comp][src + 3], true)\n\n              dataView.setUint16(offset + 4 * INT16_SIZE * type, rowBlock[comp][src + 4], true)\n              dataView.setUint16(offset + 5 * INT16_SIZE * type, rowBlock[comp][src + 5], true)\n              dataView.setUint16(offset + 6 * INT16_SIZE * type, rowBlock[comp][src + 6], true)\n              dataView.setUint16(offset + 7 * INT16_SIZE * type, rowBlock[comp][src + 7], true)\n\n              offset += 8 * INT16_SIZE * type\n            }\n          }\n\n          // handle partial X blocks\n          if (numFullBlocksX != numBlocksX) {\n            for (let y = 8 * blocky; y < 8 * blocky + maxY; ++y) {\n              const offset = rowOffsets[comp][y] + 8 * numFullBlocksX * INT16_SIZE * type\n              const src = numFullBlocksX * 64 + (y & 0x7) * 8\n\n              for (let x = 0; x < maxX; ++x) {\n                dataView.setUint16(offset + x * INT16_SIZE * type, rowBlock[comp][src + x], true)\n              }\n            }\n          }\n        } // comp\n      } // blocky\n\n      var halfRow = new Uint16Array(width)\n      var dataView = new DataView(outBuffer.buffer)\n\n      // convert channels back to float, if needed\n      for (var comp = 0; comp < numComp; ++comp) {\n        channelData[cscSet.idx[comp]].decoded = true\n        var type = channelData[cscSet.idx[comp]].type\n\n        if (channelData[comp].type != 2) continue\n\n        for (var y = 0; y < height; ++y) {\n          const offset = rowOffsets[comp][y]\n\n          for (var x = 0; x < width; ++x) {\n            halfRow[x] = dataView.getUint16(offset + x * INT16_SIZE * type, true)\n          }\n\n          for (var x = 0; x < width; ++x) {\n            dataView.setFloat32(offset + x * INT16_SIZE * type, decodeFloat16(halfRow[x]), true)\n          }\n        }\n      }\n    }\n\n    function unRleAC(currAcComp, acBuffer, halfZigBlock) {\n      var acValue\n      var dctComp = 1\n\n      while (dctComp < 64) {\n        acValue = acBuffer[currAcComp.value]\n\n        if (acValue == 0xff00) {\n          dctComp = 64\n        } else if (acValue >> 8 == 0xff) {\n          dctComp += acValue & 0xff\n        } else {\n          halfZigBlock[dctComp] = acValue\n          dctComp++\n        }\n\n        currAcComp.value++\n      }\n    }\n\n    function unZigZag(src, dst) {\n      dst[0] = decodeFloat16(src[0])\n      dst[1] = decodeFloat16(src[1])\n      dst[2] = decodeFloat16(src[5])\n      dst[3] = decodeFloat16(src[6])\n      dst[4] = decodeFloat16(src[14])\n      dst[5] = decodeFloat16(src[15])\n      dst[6] = decodeFloat16(src[27])\n      dst[7] = decodeFloat16(src[28])\n      dst[8] = decodeFloat16(src[2])\n      dst[9] = decodeFloat16(src[4])\n\n      dst[10] = decodeFloat16(src[7])\n      dst[11] = decodeFloat16(src[13])\n      dst[12] = decodeFloat16(src[16])\n      dst[13] = decodeFloat16(src[26])\n      dst[14] = decodeFloat16(src[29])\n      dst[15] = decodeFloat16(src[42])\n      dst[16] = decodeFloat16(src[3])\n      dst[17] = decodeFloat16(src[8])\n      dst[18] = decodeFloat16(src[12])\n      dst[19] = decodeFloat16(src[17])\n\n      dst[20] = decodeFloat16(src[25])\n      dst[21] = decodeFloat16(src[30])\n      dst[22] = decodeFloat16(src[41])\n      dst[23] = decodeFloat16(src[43])\n      dst[24] = decodeFloat16(src[9])\n      dst[25] = decodeFloat16(src[11])\n      dst[26] = decodeFloat16(src[18])\n      dst[27] = decodeFloat16(src[24])\n      dst[28] = decodeFloat16(src[31])\n      dst[29] = decodeFloat16(src[40])\n\n      dst[30] = decodeFloat16(src[44])\n      dst[31] = decodeFloat16(src[53])\n      dst[32] = decodeFloat16(src[10])\n      dst[33] = decodeFloat16(src[19])\n      dst[34] = decodeFloat16(src[23])\n      dst[35] = decodeFloat16(src[32])\n      dst[36] = decodeFloat16(src[39])\n      dst[37] = decodeFloat16(src[45])\n      dst[38] = decodeFloat16(src[52])\n      dst[39] = decodeFloat16(src[54])\n\n      dst[40] = decodeFloat16(src[20])\n      dst[41] = decodeFloat16(src[22])\n      dst[42] = decodeFloat16(src[33])\n      dst[43] = decodeFloat16(src[38])\n      dst[44] = decodeFloat16(src[46])\n      dst[45] = decodeFloat16(src[51])\n      dst[46] = decodeFloat16(src[55])\n      dst[47] = decodeFloat16(src[60])\n      dst[48] = decodeFloat16(src[21])\n      dst[49] = decodeFloat16(src[34])\n\n      dst[50] = decodeFloat16(src[37])\n      dst[51] = decodeFloat16(src[47])\n      dst[52] = decodeFloat16(src[50])\n      dst[53] = decodeFloat16(src[56])\n      dst[54] = decodeFloat16(src[59])\n      dst[55] = decodeFloat16(src[61])\n      dst[56] = decodeFloat16(src[35])\n      dst[57] = decodeFloat16(src[36])\n      dst[58] = decodeFloat16(src[48])\n      dst[59] = decodeFloat16(src[49])\n\n      dst[60] = decodeFloat16(src[57])\n      dst[61] = decodeFloat16(src[58])\n      dst[62] = decodeFloat16(src[62])\n      dst[63] = decodeFloat16(src[63])\n    }\n\n    function dctInverse(data) {\n      const a = 0.5 * Math.cos(3.14159 / 4.0)\n      const b = 0.5 * Math.cos(3.14159 / 16.0)\n      const c = 0.5 * Math.cos(3.14159 / 8.0)\n      const d = 0.5 * Math.cos((3.0 * 3.14159) / 16.0)\n      const e = 0.5 * Math.cos((5.0 * 3.14159) / 16.0)\n      const f = 0.5 * Math.cos((3.0 * 3.14159) / 8.0)\n      const g = 0.5 * Math.cos((7.0 * 3.14159) / 16.0)\n\n      var alpha = new Array(4)\n      var beta = new Array(4)\n      var theta = new Array(4)\n      var gamma = new Array(4)\n\n      for (var row = 0; row < 8; ++row) {\n        var rowPtr = row * 8\n\n        alpha[0] = c * data[rowPtr + 2]\n        alpha[1] = f * data[rowPtr + 2]\n        alpha[2] = c * data[rowPtr + 6]\n        alpha[3] = f * data[rowPtr + 6]\n\n        beta[0] = b * data[rowPtr + 1] + d * data[rowPtr + 3] + e * data[rowPtr + 5] + g * data[rowPtr + 7]\n        beta[1] = d * data[rowPtr + 1] - g * data[rowPtr + 3] - b * data[rowPtr + 5] - e * data[rowPtr + 7]\n        beta[2] = e * data[rowPtr + 1] - b * data[rowPtr + 3] + g * data[rowPtr + 5] + d * data[rowPtr + 7]\n        beta[3] = g * data[rowPtr + 1] - e * data[rowPtr + 3] + d * data[rowPtr + 5] - b * data[rowPtr + 7]\n\n        theta[0] = a * (data[rowPtr + 0] + data[rowPtr + 4])\n        theta[3] = a * (data[rowPtr + 0] - data[rowPtr + 4])\n        theta[1] = alpha[0] + alpha[3]\n        theta[2] = alpha[1] - alpha[2]\n\n        gamma[0] = theta[0] + theta[1]\n        gamma[1] = theta[3] + theta[2]\n        gamma[2] = theta[3] - theta[2]\n        gamma[3] = theta[0] - theta[1]\n\n        data[rowPtr + 0] = gamma[0] + beta[0]\n        data[rowPtr + 1] = gamma[1] + beta[1]\n        data[rowPtr + 2] = gamma[2] + beta[2]\n        data[rowPtr + 3] = gamma[3] + beta[3]\n\n        data[rowPtr + 4] = gamma[3] - beta[3]\n        data[rowPtr + 5] = gamma[2] - beta[2]\n        data[rowPtr + 6] = gamma[1] - beta[1]\n        data[rowPtr + 7] = gamma[0] - beta[0]\n      }\n\n      for (var column = 0; column < 8; ++column) {\n        alpha[0] = c * data[16 + column]\n        alpha[1] = f * data[16 + column]\n        alpha[2] = c * data[48 + column]\n        alpha[3] = f * data[48 + column]\n\n        beta[0] = b * data[8 + column] + d * data[24 + column] + e * data[40 + column] + g * data[56 + column]\n        beta[1] = d * data[8 + column] - g * data[24 + column] - b * data[40 + column] - e * data[56 + column]\n        beta[2] = e * data[8 + column] - b * data[24 + column] + g * data[40 + column] + d * data[56 + column]\n        beta[3] = g * data[8 + column] - e * data[24 + column] + d * data[40 + column] - b * data[56 + column]\n\n        theta[0] = a * (data[column] + data[32 + column])\n        theta[3] = a * (data[column] - data[32 + column])\n\n        theta[1] = alpha[0] + alpha[3]\n        theta[2] = alpha[1] - alpha[2]\n\n        gamma[0] = theta[0] + theta[1]\n        gamma[1] = theta[3] + theta[2]\n        gamma[2] = theta[3] - theta[2]\n        gamma[3] = theta[0] - theta[1]\n\n        data[0 + column] = gamma[0] + beta[0]\n        data[8 + column] = gamma[1] + beta[1]\n        data[16 + column] = gamma[2] + beta[2]\n        data[24 + column] = gamma[3] + beta[3]\n\n        data[32 + column] = gamma[3] - beta[3]\n        data[40 + column] = gamma[2] - beta[2]\n        data[48 + column] = gamma[1] - beta[1]\n        data[56 + column] = gamma[0] - beta[0]\n      }\n    }\n\n    function csc709Inverse(data) {\n      for (var i = 0; i < 64; ++i) {\n        var y = data[0][i]\n        var cb = data[1][i]\n        var cr = data[2][i]\n\n        data[0][i] = y + 1.5747 * cr\n        data[1][i] = y - 0.1873 * cb - 0.4682 * cr\n        data[2][i] = y + 1.8556 * cb\n      }\n    }\n\n    function convertToHalf(src, dst, idx) {\n      for (var i = 0; i < 64; ++i) {\n        dst[idx + i] = DataUtils.toHalfFloat(toLinear(src[i]))\n      }\n    }\n\n    function toLinear(float) {\n      if (float <= 1) {\n        return Math.sign(float) * Math.pow(Math.abs(float), 2.2)\n      } else {\n        return Math.sign(float) * Math.pow(logBase, Math.abs(float) - 1.0)\n      }\n    }\n\n    function uncompressRAW(info) {\n      return new DataView(info.array.buffer, info.offset.value, info.size)\n    }\n\n    function uncompressRLE(info) {\n      var compressed = info.viewer.buffer.slice(info.offset.value, info.offset.value + info.size)\n\n      var rawBuffer = new Uint8Array(decodeRunLength(compressed))\n      var tmpBuffer = new Uint8Array(rawBuffer.length)\n\n      predictor(rawBuffer) // revert predictor\n\n      interleaveScalar(rawBuffer, tmpBuffer) // interleave pixels\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressZIP(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size)\n      var rawBuffer = unzlibSync(compressed)\n      var tmpBuffer = new Uint8Array(rawBuffer.length)\n\n      predictor(rawBuffer) // revert predictor\n\n      interleaveScalar(rawBuffer, tmpBuffer) // interleave pixels\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressPIZ(info) {\n      var inDataView = info.viewer\n      var inOffset = { value: info.offset.value }\n\n      var outBuffer = new Uint16Array(info.width * info.scanlineBlockSize * (info.channels * info.type))\n      var bitmap = new Uint8Array(BITMAP_SIZE)\n\n      // Setup channel info\n      var outBufferEnd = 0\n      var pizChannelData = new Array(info.channels)\n      for (var i = 0; i < info.channels; i++) {\n        pizChannelData[i] = {}\n        pizChannelData[i]['start'] = outBufferEnd\n        pizChannelData[i]['end'] = pizChannelData[i]['start']\n        pizChannelData[i]['nx'] = info.width\n        pizChannelData[i]['ny'] = info.lines\n        pizChannelData[i]['size'] = info.type\n\n        outBufferEnd += pizChannelData[i].nx * pizChannelData[i].ny * pizChannelData[i].size\n      }\n\n      // Read range compression data\n\n      var minNonZero = parseUint16(inDataView, inOffset)\n      var maxNonZero = parseUint16(inDataView, inOffset)\n\n      if (maxNonZero >= BITMAP_SIZE) {\n        throw 'Something is wrong with PIZ_COMPRESSION BITMAP_SIZE'\n      }\n\n      if (minNonZero <= maxNonZero) {\n        for (var i = 0; i < maxNonZero - minNonZero + 1; i++) {\n          bitmap[i + minNonZero] = parseUint8(inDataView, inOffset)\n        }\n      }\n\n      // Reverse LUT\n      var lut = new Uint16Array(USHORT_RANGE)\n      var maxValue = reverseLutFromBitmap(bitmap, lut)\n\n      var length = parseUint32(inDataView, inOffset)\n\n      // Huffman decoding\n      hufUncompress(info.array, inDataView, inOffset, length, outBuffer, outBufferEnd)\n\n      // Wavelet decoding\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = pizChannelData[i]\n\n        for (var j = 0; j < pizChannelData[i].size; ++j) {\n          wav2Decode(outBuffer, cd.start + j, cd.nx, cd.size, cd.ny, cd.nx * cd.size, maxValue)\n        }\n      }\n\n      // Expand the pixel data to their original range\n      applyLut(lut, outBuffer, outBufferEnd)\n\n      // Rearrange the pixel data into the format expected by the caller.\n      var tmpOffset = 0\n      var tmpBuffer = new Uint8Array(outBuffer.buffer.byteLength)\n      for (var y = 0; y < info.lines; y++) {\n        for (var c = 0; c < info.channels; c++) {\n          var cd = pizChannelData[c]\n\n          var n = cd.nx * cd.size\n          var cp = new Uint8Array(outBuffer.buffer, cd.end * INT16_SIZE, n * INT16_SIZE)\n\n          tmpBuffer.set(cp, tmpOffset)\n          tmpOffset += n * INT16_SIZE\n          cd.end += n\n        }\n      }\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressPXR(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size)\n      var rawBuffer = unzlibSync(compressed)\n\n      const sz = info.lines * info.channels * info.width\n      const tmpBuffer = info.type == 1 ? new Uint16Array(sz) : new Uint32Array(sz)\n\n      let tmpBufferEnd = 0\n      let writePtr = 0\n      const ptr = new Array(4)\n\n      for (let y = 0; y < info.lines; y++) {\n        for (let c = 0; c < info.channels; c++) {\n          let pixel = 0\n\n          switch (info.type) {\n            case 1:\n              ptr[0] = tmpBufferEnd\n              ptr[1] = ptr[0] + info.width\n              tmpBufferEnd = ptr[1] + info.width\n\n              for (let j = 0; j < info.width; ++j) {\n                const diff = (rawBuffer[ptr[0]++] << 8) | rawBuffer[ptr[1]++]\n\n                pixel += diff\n\n                tmpBuffer[writePtr] = pixel\n                writePtr++\n              }\n\n              break\n\n            case 2:\n              ptr[0] = tmpBufferEnd\n              ptr[1] = ptr[0] + info.width\n              ptr[2] = ptr[1] + info.width\n              tmpBufferEnd = ptr[2] + info.width\n\n              for (let j = 0; j < info.width; ++j) {\n                const diff = (rawBuffer[ptr[0]++] << 24) | (rawBuffer[ptr[1]++] << 16) | (rawBuffer[ptr[2]++] << 8)\n\n                pixel += diff\n\n                tmpBuffer[writePtr] = pixel\n                writePtr++\n              }\n\n              break\n          }\n        }\n      }\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressDWA(info) {\n      var inDataView = info.viewer\n      var inOffset = { value: info.offset.value }\n      var outBuffer = new Uint8Array(info.width * info.lines * (info.channels * info.type * INT16_SIZE))\n\n      // Read compression header information\n      var dwaHeader = {\n        version: parseInt64(inDataView, inOffset),\n        unknownUncompressedSize: parseInt64(inDataView, inOffset),\n        unknownCompressedSize: parseInt64(inDataView, inOffset),\n        acCompressedSize: parseInt64(inDataView, inOffset),\n        dcCompressedSize: parseInt64(inDataView, inOffset),\n        rleCompressedSize: parseInt64(inDataView, inOffset),\n        rleUncompressedSize: parseInt64(inDataView, inOffset),\n        rleRawSize: parseInt64(inDataView, inOffset),\n        totalAcUncompressedCount: parseInt64(inDataView, inOffset),\n        totalDcUncompressedCount: parseInt64(inDataView, inOffset),\n        acCompression: parseInt64(inDataView, inOffset),\n      }\n\n      if (dwaHeader.version < 2) {\n        throw 'EXRLoader.parse: ' + EXRHeader.compression + ' version ' + dwaHeader.version + ' is unsupported'\n      }\n\n      // Read channel ruleset information\n      var channelRules = new Array()\n      var ruleSize = parseUint16(inDataView, inOffset) - INT16_SIZE\n\n      while (ruleSize > 0) {\n        var name = parseNullTerminatedString(inDataView.buffer, inOffset)\n        var value = parseUint8(inDataView, inOffset)\n        var compression = (value >> 2) & 3\n        var csc = (value >> 4) - 1\n        var index = new Int8Array([csc])[0]\n        var type = parseUint8(inDataView, inOffset)\n\n        channelRules.push({\n          name: name,\n          index: index,\n          type: type,\n          compression: compression,\n        })\n\n        ruleSize -= name.length + 3\n      }\n\n      // Classify channels\n      var channels = EXRHeader.channels\n      var channelData = new Array(info.channels)\n\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = (channelData[i] = {})\n        var channel = channels[i]\n\n        cd.name = channel.name\n        cd.compression = UNKNOWN\n        cd.decoded = false\n        cd.type = channel.pixelType\n        cd.pLinear = channel.pLinear\n        cd.width = info.width\n        cd.height = info.lines\n      }\n\n      var cscSet = {\n        idx: new Array(3),\n      }\n\n      for (var offset = 0; offset < info.channels; ++offset) {\n        var cd = channelData[offset]\n\n        for (var i = 0; i < channelRules.length; ++i) {\n          var rule = channelRules[i]\n\n          if (cd.name == rule.name) {\n            cd.compression = rule.compression\n\n            if (rule.index >= 0) {\n              cscSet.idx[rule.index] = offset\n            }\n\n            cd.offset = offset\n          }\n        }\n      }\n\n      // Read DCT - AC component data\n      if (dwaHeader.acCompressedSize > 0) {\n        switch (dwaHeader.acCompression) {\n          case STATIC_HUFFMAN:\n            var acBuffer = new Uint16Array(dwaHeader.totalAcUncompressedCount)\n            hufUncompress(\n              info.array,\n              inDataView,\n              inOffset,\n              dwaHeader.acCompressedSize,\n              acBuffer,\n              dwaHeader.totalAcUncompressedCount,\n            )\n            break\n\n          case DEFLATE:\n            var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.totalAcUncompressedCount)\n            var data = unzlibSync(compressed)\n            var acBuffer = new Uint16Array(data.buffer)\n            inOffset.value += dwaHeader.totalAcUncompressedCount\n            break\n        }\n      }\n\n      // Read DCT - DC component data\n      if (dwaHeader.dcCompressedSize > 0) {\n        var zlibInfo = {\n          array: info.array,\n          offset: inOffset,\n          size: dwaHeader.dcCompressedSize,\n        }\n        var dcBuffer = new Uint16Array(uncompressZIP(zlibInfo).buffer)\n        inOffset.value += dwaHeader.dcCompressedSize\n      }\n\n      // Read RLE compressed data\n      if (dwaHeader.rleRawSize > 0) {\n        var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.rleCompressedSize)\n        var data = unzlibSync(compressed)\n        var rleBuffer = decodeRunLength(data.buffer)\n\n        inOffset.value += dwaHeader.rleCompressedSize\n      }\n\n      // Prepare outbuffer data offset\n      var outBufferEnd = 0\n      var rowOffsets = new Array(channelData.length)\n      for (var i = 0; i < rowOffsets.length; ++i) {\n        rowOffsets[i] = new Array()\n      }\n\n      for (var y = 0; y < info.lines; ++y) {\n        for (var chan = 0; chan < channelData.length; ++chan) {\n          rowOffsets[chan].push(outBufferEnd)\n          outBufferEnd += channelData[chan].width * info.type * INT16_SIZE\n        }\n      }\n\n      // Lossy DCT decode RGB channels\n      lossyDctDecode(cscSet, rowOffsets, channelData, acBuffer, dcBuffer, outBuffer)\n\n      // Decode other channels\n      for (var i = 0; i < channelData.length; ++i) {\n        var cd = channelData[i]\n\n        if (cd.decoded) continue\n\n        switch (cd.compression) {\n          case RLE:\n            var row = 0\n            var rleOffset = 0\n\n            for (var y = 0; y < info.lines; ++y) {\n              var rowOffsetBytes = rowOffsets[i][row]\n\n              for (var x = 0; x < cd.width; ++x) {\n                for (var byte = 0; byte < INT16_SIZE * cd.type; ++byte) {\n                  outBuffer[rowOffsetBytes++] = rleBuffer[rleOffset + byte * cd.width * cd.height]\n                }\n\n                rleOffset++\n              }\n\n              row++\n            }\n\n            break\n\n          case LOSSY_DCT: // skip\n\n          default:\n            throw 'EXRLoader.parse: unsupported channel compression'\n        }\n      }\n\n      return new DataView(outBuffer.buffer)\n    }\n\n    function parseNullTerminatedString(buffer, offset) {\n      var uintBuffer = new Uint8Array(buffer)\n      var endOffset = 0\n\n      while (uintBuffer[offset.value + endOffset] != 0) {\n        endOffset += 1\n      }\n\n      var stringValue = new TextDecoder().decode(uintBuffer.slice(offset.value, offset.value + endOffset))\n\n      offset.value = offset.value + endOffset + 1\n\n      return stringValue\n    }\n\n    function parseFixedLengthString(buffer, offset, size) {\n      var stringValue = new TextDecoder().decode(new Uint8Array(buffer).slice(offset.value, offset.value + size))\n\n      offset.value = offset.value + size\n\n      return stringValue\n    }\n\n    function parseRational(dataView, offset) {\n      var x = parseInt32(dataView, offset)\n      var y = parseUint32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseTimecode(dataView, offset) {\n      var x = parseUint32(dataView, offset)\n      var y = parseUint32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseInt32(dataView, offset) {\n      var Int32 = dataView.getInt32(offset.value, true)\n\n      offset.value = offset.value + INT32_SIZE\n\n      return Int32\n    }\n\n    function parseUint32(dataView, offset) {\n      var Uint32 = dataView.getUint32(offset.value, true)\n\n      offset.value = offset.value + INT32_SIZE\n\n      return Uint32\n    }\n\n    function parseUint8Array(uInt8Array, offset) {\n      var Uint8 = uInt8Array[offset.value]\n\n      offset.value = offset.value + INT8_SIZE\n\n      return Uint8\n    }\n\n    function parseUint8(dataView, offset) {\n      var Uint8 = dataView.getUint8(offset.value)\n\n      offset.value = offset.value + INT8_SIZE\n\n      return Uint8\n    }\n\n    const parseInt64 = function (dataView, offset) {\n      let int\n\n      if ('getBigInt64' in DataView.prototype) {\n        int = Number(dataView.getBigInt64(offset.value, true))\n      } else {\n        int = dataView.getUint32(offset.value + 4, true) + Number(dataView.getUint32(offset.value, true) << 32)\n      }\n\n      offset.value += ULONG_SIZE\n\n      return int\n    }\n\n    function parseFloat32(dataView, offset) {\n      var float = dataView.getFloat32(offset.value, true)\n\n      offset.value += FLOAT32_SIZE\n\n      return float\n    }\n\n    function decodeFloat32(dataView, offset) {\n      return DataUtils.toHalfFloat(parseFloat32(dataView, offset))\n    }\n\n    // https://stackoverflow.com/questions/5678432/decompressing-half-precision-floats-in-javascript\n    function decodeFloat16(binary) {\n      var exponent = (binary & 0x7c00) >> 10,\n        fraction = binary & 0x03ff\n\n      return (\n        (binary >> 15 ? -1 : 1) *\n        (exponent\n          ? exponent === 0x1f\n            ? fraction\n              ? NaN\n              : Infinity\n            : Math.pow(2, exponent - 15) * (1 + fraction / 0x400)\n          : 6.103515625e-5 * (fraction / 0x400))\n      )\n    }\n\n    function parseUint16(dataView, offset) {\n      var Uint16 = dataView.getUint16(offset.value, true)\n\n      offset.value += INT16_SIZE\n\n      return Uint16\n    }\n\n    function parseFloat16(buffer, offset) {\n      return decodeFloat16(parseUint16(buffer, offset))\n    }\n\n    function parseChlist(dataView, buffer, offset, size) {\n      var startOffset = offset.value\n      var channels = []\n\n      while (offset.value < startOffset + size - 1) {\n        var name = parseNullTerminatedString(buffer, offset)\n        var pixelType = parseInt32(dataView, offset)\n        var pLinear = parseUint8(dataView, offset)\n        offset.value += 3 // reserved, three chars\n        var xSampling = parseInt32(dataView, offset)\n        var ySampling = parseInt32(dataView, offset)\n\n        channels.push({\n          name: name,\n          pixelType: pixelType,\n          pLinear: pLinear,\n          xSampling: xSampling,\n          ySampling: ySampling,\n        })\n      }\n\n      offset.value += 1\n\n      return channels\n    }\n\n    function parseChromaticities(dataView, offset) {\n      var redX = parseFloat32(dataView, offset)\n      var redY = parseFloat32(dataView, offset)\n      var greenX = parseFloat32(dataView, offset)\n      var greenY = parseFloat32(dataView, offset)\n      var blueX = parseFloat32(dataView, offset)\n      var blueY = parseFloat32(dataView, offset)\n      var whiteX = parseFloat32(dataView, offset)\n      var whiteY = parseFloat32(dataView, offset)\n\n      return {\n        redX: redX,\n        redY: redY,\n        greenX: greenX,\n        greenY: greenY,\n        blueX: blueX,\n        blueY: blueY,\n        whiteX: whiteX,\n        whiteY: whiteY,\n      }\n    }\n\n    function parseCompression(dataView, offset) {\n      var compressionCodes = [\n        'NO_COMPRESSION',\n        'RLE_COMPRESSION',\n        'ZIPS_COMPRESSION',\n        'ZIP_COMPRESSION',\n        'PIZ_COMPRESSION',\n        'PXR24_COMPRESSION',\n        'B44_COMPRESSION',\n        'B44A_COMPRESSION',\n        'DWAA_COMPRESSION',\n        'DWAB_COMPRESSION',\n      ]\n\n      var compression = parseUint8(dataView, offset)\n\n      return compressionCodes[compression]\n    }\n\n    function parseBox2i(dataView, offset) {\n      var xMin = parseUint32(dataView, offset)\n      var yMin = parseUint32(dataView, offset)\n      var xMax = parseUint32(dataView, offset)\n      var yMax = parseUint32(dataView, offset)\n\n      return { xMin: xMin, yMin: yMin, xMax: xMax, yMax: yMax }\n    }\n\n    function parseLineOrder(dataView, offset) {\n      var lineOrders = ['INCREASING_Y']\n\n      var lineOrder = parseUint8(dataView, offset)\n\n      return lineOrders[lineOrder]\n    }\n\n    function parseV2f(dataView, offset) {\n      var x = parseFloat32(dataView, offset)\n      var y = parseFloat32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseV3f(dataView, offset) {\n      var x = parseFloat32(dataView, offset)\n      var y = parseFloat32(dataView, offset)\n      var z = parseFloat32(dataView, offset)\n\n      return [x, y, z]\n    }\n\n    function parseValue(dataView, buffer, offset, type, size) {\n      if (type === 'string' || type === 'stringvector' || type === 'iccProfile') {\n        return parseFixedLengthString(buffer, offset, size)\n      } else if (type === 'chlist') {\n        return parseChlist(dataView, buffer, offset, size)\n      } else if (type === 'chromaticities') {\n        return parseChromaticities(dataView, offset)\n      } else if (type === 'compression') {\n        return parseCompression(dataView, offset)\n      } else if (type === 'box2i') {\n        return parseBox2i(dataView, offset)\n      } else if (type === 'lineOrder') {\n        return parseLineOrder(dataView, offset)\n      } else if (type === 'float') {\n        return parseFloat32(dataView, offset)\n      } else if (type === 'v2f') {\n        return parseV2f(dataView, offset)\n      } else if (type === 'v3f') {\n        return parseV3f(dataView, offset)\n      } else if (type === 'int') {\n        return parseInt32(dataView, offset)\n      } else if (type === 'rational') {\n        return parseRational(dataView, offset)\n      } else if (type === 'timecode') {\n        return parseTimecode(dataView, offset)\n      } else if (type === 'preview') {\n        offset.value += size\n        return 'skipped'\n      } else {\n        offset.value += size\n        return undefined\n      }\n    }\n\n    function parseHeader(dataView, buffer, offset) {\n      const EXRHeader = {}\n\n      if (dataView.getUint32(0, true) != 20000630) {\n        // magic\n        throw \"THREE.EXRLoader: provided file doesn't appear to be in OpenEXR format.\"\n      }\n\n      EXRHeader.version = dataView.getUint8(4)\n\n      const spec = dataView.getUint8(5) // fullMask\n\n      EXRHeader.spec = {\n        singleTile: !!(spec & 2),\n        longName: !!(spec & 4),\n        deepFormat: !!(spec & 8),\n        multiPart: !!(spec & 16),\n      }\n\n      // start of header\n\n      offset.value = 8 // start at 8 - after pre-amble\n\n      var keepReading = true\n\n      while (keepReading) {\n        var attributeName = parseNullTerminatedString(buffer, offset)\n\n        if (attributeName == 0) {\n          keepReading = false\n        } else {\n          var attributeType = parseNullTerminatedString(buffer, offset)\n          var attributeSize = parseUint32(dataView, offset)\n          var attributeValue = parseValue(dataView, buffer, offset, attributeType, attributeSize)\n\n          if (attributeValue === undefined) {\n            console.warn(`EXRLoader.parse: skipped unknown header attribute type \\'${attributeType}\\'.`)\n          } else {\n            EXRHeader[attributeName] = attributeValue\n          }\n        }\n      }\n\n      if ((spec & ~0x04) != 0) {\n        // unsupported tiled, deep-image, multi-part\n        console.error('EXRHeader:', EXRHeader)\n        throw 'THREE.EXRLoader: provided file is currently unsupported.'\n      }\n\n      return EXRHeader\n    }\n\n    function setupDecoder(EXRHeader, dataView, uInt8Array, offset, outputType) {\n      const EXRDecoder = {\n        size: 0,\n        viewer: dataView,\n        array: uInt8Array,\n        offset: offset,\n        width: EXRHeader.dataWindow.xMax - EXRHeader.dataWindow.xMin + 1,\n        height: EXRHeader.dataWindow.yMax - EXRHeader.dataWindow.yMin + 1,\n        channels: EXRHeader.channels.length,\n        bytesPerLine: null,\n        lines: null,\n        inputSize: null,\n        type: EXRHeader.channels[0].pixelType,\n        uncompress: null,\n        getter: null,\n        format: null,\n        [hasColorSpace ? 'colorSpace' : 'encoding']: null,\n      }\n\n      switch (EXRHeader.compression) {\n        case 'NO_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressRAW\n          break\n\n        case 'RLE_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressRLE\n          break\n\n        case 'ZIPS_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressZIP\n          break\n\n        case 'ZIP_COMPRESSION':\n          EXRDecoder.lines = 16\n          EXRDecoder.uncompress = uncompressZIP\n          break\n\n        case 'PIZ_COMPRESSION':\n          EXRDecoder.lines = 32\n          EXRDecoder.uncompress = uncompressPIZ\n          break\n\n        case 'PXR24_COMPRESSION':\n          EXRDecoder.lines = 16\n          EXRDecoder.uncompress = uncompressPXR\n          break\n\n        case 'DWAA_COMPRESSION':\n          EXRDecoder.lines = 32\n          EXRDecoder.uncompress = uncompressDWA\n          break\n\n        case 'DWAB_COMPRESSION':\n          EXRDecoder.lines = 256\n          EXRDecoder.uncompress = uncompressDWA\n          break\n\n        default:\n          throw 'EXRLoader.parse: ' + EXRHeader.compression + ' is unsupported'\n      }\n\n      EXRDecoder.scanlineBlockSize = EXRDecoder.lines\n\n      if (EXRDecoder.type == 1) {\n        // half\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder.getter = parseFloat16\n            EXRDecoder.inputSize = INT16_SIZE\n            break\n\n          case HalfFloatType:\n            EXRDecoder.getter = parseUint16\n            EXRDecoder.inputSize = INT16_SIZE\n            break\n        }\n      } else if (EXRDecoder.type == 2) {\n        // float\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder.getter = parseFloat32\n            EXRDecoder.inputSize = FLOAT32_SIZE\n            break\n\n          case HalfFloatType:\n            EXRDecoder.getter = decodeFloat32\n            EXRDecoder.inputSize = FLOAT32_SIZE\n        }\n      } else {\n        throw 'EXRLoader.parse: unsupported pixelType ' + EXRDecoder.type + ' for ' + EXRHeader.compression + '.'\n      }\n\n      EXRDecoder.blockCount = (EXRHeader.dataWindow.yMax + 1) / EXRDecoder.scanlineBlockSize\n\n      for (var i = 0; i < EXRDecoder.blockCount; i++) parseInt64(dataView, offset) // scanlineOffset\n\n      // we should be passed the scanline offset table, ready to start reading pixel data.\n\n      // RGB images will be converted to RGBA format, preventing software emulation in select devices.\n      EXRDecoder.outputChannels = EXRDecoder.channels == 3 ? 4 : EXRDecoder.channels\n      const size = EXRDecoder.width * EXRDecoder.height * EXRDecoder.outputChannels\n\n      switch (outputType) {\n        case FloatType:\n          EXRDecoder.byteArray = new Float32Array(size)\n\n          // Fill initially with 1s for the alpha value if the texture is not RGBA, RGB values will be overwritten\n          if (EXRDecoder.channels < EXRDecoder.outputChannels) EXRDecoder.byteArray.fill(1, 0, size)\n\n          break\n\n        case HalfFloatType:\n          EXRDecoder.byteArray = new Uint16Array(size)\n\n          if (EXRDecoder.channels < EXRDecoder.outputChannels) EXRDecoder.byteArray.fill(0x3c00, 0, size) // Uint16Array holds half float data, 0x3C00 is 1\n\n          break\n\n        default:\n          console.error('THREE.EXRLoader: unsupported type: ', outputType)\n          break\n      }\n\n      EXRDecoder.bytesPerLine = EXRDecoder.width * EXRDecoder.inputSize * EXRDecoder.channels\n\n      if (EXRDecoder.outputChannels == 4) EXRDecoder.format = RGBAFormat\n      else EXRDecoder.format = RedFormat\n\n      if (hasColorSpace) EXRDecoder.colorSpace = 'srgb-linear'\n      else EXRDecoder.encoding = 3000 // LinearEncoding\n\n      return EXRDecoder\n    }\n\n    // start parsing file [START]\n\n    const bufferDataView = new DataView(buffer)\n    const uInt8Array = new Uint8Array(buffer)\n    const offset = { value: 0 }\n\n    // get header information and validate format.\n    const EXRHeader = parseHeader(bufferDataView, buffer, offset)\n\n    // get input compression information and prepare decoding.\n    const EXRDecoder = setupDecoder(EXRHeader, bufferDataView, uInt8Array, offset, this.type)\n\n    const tmpOffset = { value: 0 }\n    const channelOffsets = { R: 0, G: 1, B: 2, A: 3, Y: 0 }\n\n    for (\n      let scanlineBlockIdx = 0;\n      scanlineBlockIdx < EXRDecoder.height / EXRDecoder.scanlineBlockSize;\n      scanlineBlockIdx++\n    ) {\n      const line = parseUint32(bufferDataView, offset) // line_no\n      EXRDecoder.size = parseUint32(bufferDataView, offset) // data_len\n      EXRDecoder.lines =\n        line + EXRDecoder.scanlineBlockSize > EXRDecoder.height\n          ? EXRDecoder.height - line\n          : EXRDecoder.scanlineBlockSize\n\n      const isCompressed = EXRDecoder.size < EXRDecoder.lines * EXRDecoder.bytesPerLine\n      const viewer = isCompressed ? EXRDecoder.uncompress(EXRDecoder) : uncompressRAW(EXRDecoder)\n\n      offset.value += EXRDecoder.size\n\n      for (let line_y = 0; line_y < EXRDecoder.scanlineBlockSize; line_y++) {\n        const true_y = line_y + scanlineBlockIdx * EXRDecoder.scanlineBlockSize\n        if (true_y >= EXRDecoder.height) break\n\n        for (let channelID = 0; channelID < EXRDecoder.channels; channelID++) {\n          const cOff = channelOffsets[EXRHeader.channels[channelID].name]\n\n          for (let x = 0; x < EXRDecoder.width; x++) {\n            tmpOffset.value =\n              (line_y * (EXRDecoder.channels * EXRDecoder.width) + channelID * EXRDecoder.width + x) *\n              EXRDecoder.inputSize\n            const outIndex =\n              (EXRDecoder.height - 1 - true_y) * (EXRDecoder.width * EXRDecoder.outputChannels) +\n              x * EXRDecoder.outputChannels +\n              cOff\n            EXRDecoder.byteArray[outIndex] = EXRDecoder.getter(viewer, tmpOffset)\n          }\n        }\n      }\n    }\n\n    return {\n      header: EXRHeader,\n      width: EXRDecoder.width,\n      height: EXRDecoder.height,\n      data: EXRDecoder.byteArray,\n      format: EXRDecoder.format,\n      [hasColorSpace ? 'colorSpace' : 'encoding']: EXRDecoder[hasColorSpace ? 'colorSpace' : 'encoding'],\n      type: this.type,\n    }\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      if (hasColorSpace) texture.colorSpace = texData.colorSpace\n      else texture.encoding = texData.encoding\n      texture.minFilter = LinearFilter\n      texture.magFilter = LinearFilter\n      texture.generateMipmaps = false\n      texture.flipY = false\n\n      if (onLoad) onLoad(texture, texData)\n    }\n\n    return super.load(url, onLoadCallback, onProgress, onError)\n  }\n}\n\nexport { EXRLoader }\n"], "mappings": ";;;AAuFA,MAAMA,aAAA,GAAgBC,OAAA,IAAW;AAEjC,MAAMC,SAAA,SAAkBC,iBAAA,CAAkB;EACxCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,IAAA,GAAOC,aAAA;EACb;EAEDC,MAAMC,MAAA,EAAQ;IACZ,MAAMC,YAAA,GAAe,KAAK;IAC1B,MAAMC,WAAA,GAAcD,YAAA,IAAgB;IAEpC,MAAME,WAAA,GAAc;IACpB,MAAMC,WAAA,GAAc;IAEpB,MAAMC,WAAA,IAAe,KAAKF,WAAA,IAAe;IACzC,MAAMG,WAAA,GAAc,KAAKF,WAAA;IACzB,MAAMG,WAAA,GAAcD,WAAA,GAAc;IAElC,MAAME,KAAA,GAAQ;IACd,MAAMC,QAAA,GAAW,KAAMD,KAAA,GAAQ;IAC/B,MAAME,QAAA,IAAY,KAAKF,KAAA,IAAS;IAEhC,MAAMG,kBAAA,GAAqB;IAC3B,MAAMC,iBAAA,GAAoB;IAC1B,MAAMC,iBAAA,GAAoB,IAAID,iBAAA,GAAoBD,kBAAA;IAElD,MAAMG,UAAA,GAAa;IACnB,MAAMC,YAAA,GAAe;IACrB,MAAMC,UAAA,GAAa;IACnB,MAAMC,UAAA,GAAa;IACnB,MAAMC,SAAA,GAAY;IAElB,MAAMC,cAAA,GAAiB;IACvB,MAAMC,OAAA,GAAU;IAEhB,MAAMC,OAAA,GAAU;IAChB,MAAMC,SAAA,GAAY;IAClB,MAAMC,GAAA,GAAM;IAEZ,MAAMC,OAAA,GAAUC,IAAA,CAAKC,GAAA,CAAI,WAAW,GAAG;IAEvC,SAASC,qBAAqBC,MAAA,EAAQC,GAAA,EAAK;MACzC,IAAIC,CAAA,GAAI;MAER,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI9B,YAAA,EAAc,EAAE8B,CAAA,EAAG;QACrC,IAAIA,CAAA,IAAK,KAAKH,MAAA,CAAOG,CAAA,IAAK,CAAC,IAAK,MAAMA,CAAA,GAAI,IAAK;UAC7CF,GAAA,CAAIC,CAAA,EAAG,IAAIC,CAAA;QACZ;MACF;MAED,IAAIC,CAAA,GAAIF,CAAA,GAAI;MAEZ,OAAOA,CAAA,GAAI7B,YAAA,EAAc4B,GAAA,CAAIC,CAAA,EAAG,IAAI;MAEpC,OAAOE,CAAA;IACR;IAED,SAASC,iBAAiBC,IAAA,EAAM;MAC9B,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAIzB,WAAA,EAAayB,CAAA,IAAK;QACpCG,IAAA,CAAKH,CAAC,IAAI,CAAE;QACZG,IAAA,CAAKH,CAAC,EAAEI,GAAA,GAAM;QACdD,IAAA,CAAKH,CAAC,EAAEK,GAAA,GAAM;QACdF,IAAA,CAAKH,CAAC,EAAEM,CAAA,GAAI;MACb;IACF;IAED,MAAMC,aAAA,GAAgB;MAAEC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGC,EAAA,EAAI;IAAG;IAE3C,SAASC,QAAQC,KAAA,EAAOH,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYC,QAAA,EAAU;MACnD,OAAOJ,EAAA,GAAKE,KAAA,EAAO;QACjBH,CAAA,GAAKA,CAAA,IAAK,IAAKM,eAAA,CAAgBF,WAAA,EAAYC,QAAQ;QACnDJ,EAAA,IAAM;MACP;MAEDA,EAAA,IAAME,KAAA;MAENL,aAAA,CAAcC,CAAA,GAAKC,CAAA,IAAKC,EAAA,IAAQ,KAAKE,KAAA,IAAS;MAC9CL,aAAA,CAAcE,CAAA,GAAIA,CAAA;MAClBF,aAAA,CAAcG,EAAA,GAAKA,EAAA;IACpB;IAED,MAAMM,cAAA,GAAiB,IAAIC,KAAA,CAAM,EAAE;IAEnC,SAASC,sBAAsBC,KAAA,EAAO;MACpC,SAASnB,CAAA,GAAI,GAAGA,CAAA,IAAK,IAAI,EAAEA,CAAA,EAAGgB,cAAA,CAAehB,CAAC,IAAI;MAClD,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI1B,WAAA,EAAa,EAAE0B,CAAA,EAAGgB,cAAA,CAAeG,KAAA,CAAMnB,CAAC,CAAC,KAAK;MAElE,IAAIS,CAAA,GAAI;MAER,SAAST,CAAA,GAAI,IAAIA,CAAA,GAAI,GAAG,EAAEA,CAAA,EAAG;QAC3B,IAAIoB,EAAA,GAAMX,CAAA,GAAIO,cAAA,CAAehB,CAAC,KAAM;QACpCgB,cAAA,CAAehB,CAAC,IAAIS,CAAA;QACpBA,CAAA,GAAIW,EAAA;MACL;MAED,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAI1B,WAAA,EAAa,EAAE0B,CAAA,EAAG;QACpC,IAAIQ,CAAA,GAAIW,KAAA,CAAMnB,CAAC;QACf,IAAIQ,CAAA,GAAI,GAAGW,KAAA,CAAMnB,CAAC,IAAIQ,CAAA,GAAKQ,cAAA,CAAeR,CAAC,OAAO;MACnD;IACF;IAED,SAASa,kBAAkBR,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAUS,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIN,KAAA,EAAO;MAC9E,IAAIb,CAAA,GAAIQ,QAAA;MACR,IAAIL,CAAA,GAAI;MACR,IAAIC,EAAA,GAAK;MAET,OAAOc,EAAA,IAAMC,EAAA,EAAID,EAAA,IAAM;QACrB,IAAIlB,CAAA,CAAEoB,KAAA,GAAQZ,QAAA,CAASY,KAAA,GAAQH,EAAA,EAAI,OAAO;QAE1CZ,OAAA,CAAQ,GAAGF,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYP,CAAC;QAE/B,IAAIE,CAAA,GAAID,aAAA,CAAcC,CAAA;QACtBC,CAAA,GAAIF,aAAA,CAAcE,CAAA;QAClBC,EAAA,GAAKH,aAAA,CAAcG,EAAA;QAEnBS,KAAA,CAAMK,EAAE,IAAIhB,CAAA;QAEZ,IAAIA,CAAA,IAAK3B,iBAAA,EAAmB;UAC1B,IAAIyB,CAAA,CAAEoB,KAAA,GAAQZ,QAAA,CAASY,KAAA,GAAQH,EAAA,EAAI;YACjC,MAAM;UACP;UAEDZ,OAAA,CAAQ,GAAGF,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYP,CAAC;UAE/B,IAAIqB,KAAA,GAAQpB,aAAA,CAAcC,CAAA,GAAI1B,iBAAA;UAC9B2B,CAAA,GAAIF,aAAA,CAAcE,CAAA;UAClBC,EAAA,GAAKH,aAAA,CAAcG,EAAA;UAEnB,IAAIc,EAAA,GAAKG,KAAA,GAAQF,EAAA,GAAK,GAAG;YACvB,MAAM;UACP;UAED,OAAOE,KAAA,IAASR,KAAA,CAAMK,EAAA,EAAI,IAAI;UAE9BA,EAAA;QACV,WAAmBhB,CAAA,IAAK5B,kBAAA,EAAoB;UAClC,IAAI+C,KAAA,GAAQnB,CAAA,GAAI5B,kBAAA,GAAqB;UAErC,IAAI4C,EAAA,GAAKG,KAAA,GAAQF,EAAA,GAAK,GAAG;YACvB,MAAM;UACP;UAED,OAAOE,KAAA,IAASR,KAAA,CAAMK,EAAA,EAAI,IAAI;UAE9BA,EAAA;QACD;MACF;MAEDN,qBAAA,CAAsBC,KAAK;IAC5B;IAED,SAASS,UAAUC,IAAA,EAAM;MACvB,OAAOA,IAAA,GAAO;IACf;IAED,SAASC,QAAQD,IAAA,EAAM;MACrB,OAAOA,IAAA,IAAQ;IAChB;IAED,SAASE,iBAAiBZ,KAAA,EAAOK,EAAA,EAAIC,EAAA,EAAIO,MAAA,EAAQ;MAC/C,OAAOR,EAAA,IAAMC,EAAA,EAAID,EAAA,IAAM;QACrB,IAAIf,CAAA,GAAIqB,OAAA,CAAQX,KAAA,CAAMK,EAAE,CAAC;QACzB,IAAIhB,CAAA,GAAIoB,SAAA,CAAUT,KAAA,CAAMK,EAAE,CAAC;QAE3B,IAAIf,CAAA,IAAKD,CAAA,EAAG;UACV,MAAM;QACP;QAED,IAAIA,CAAA,GAAInC,WAAA,EAAa;UACnB,IAAI4D,EAAA,GAAKD,MAAA,CAAOvB,CAAA,IAAMD,CAAA,GAAInC,WAAY;UAEtC,IAAI4D,EAAA,CAAG7B,GAAA,EAAK;YACV,MAAM;UACP;UAED6B,EAAA,CAAG5B,GAAA;UAEH,IAAI4B,EAAA,CAAG3B,CAAA,EAAG;YACR,IAAIA,CAAA,GAAI2B,EAAA,CAAG3B,CAAA;YACX2B,EAAA,CAAG3B,CAAA,GAAI,IAAIW,KAAA,CAAMgB,EAAA,CAAG5B,GAAG;YAEvB,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIiC,EAAA,CAAG5B,GAAA,GAAM,GAAG,EAAEL,CAAA,EAAG;cACnCiC,EAAA,CAAG3B,CAAA,CAAEN,CAAC,IAAIM,CAAA,CAAEN,CAAC;YACd;UACb,OAAiB;YACLiC,EAAA,CAAG3B,CAAA,GAAI,IAAIW,KAAA,CAAM,CAAC;UACnB;UAEDgB,EAAA,CAAG3B,CAAA,CAAE2B,EAAA,CAAG5B,GAAA,GAAM,CAAC,IAAImB,EAAA;QACpB,WAAUhB,CAAA,EAAG;UACZ,IAAI0B,QAAA,GAAW;UAEf,SAASlC,CAAA,GAAI,KAAM3B,WAAA,GAAcmC,CAAA,EAAIR,CAAA,GAAI,GAAGA,CAAA,IAAK;YAC/C,IAAIiC,EAAA,GAAKD,MAAA,EAAQvB,CAAA,IAAMpC,WAAA,GAAcmC,CAAA,IAAM0B,QAAQ;YAEnD,IAAID,EAAA,CAAG7B,GAAA,IAAO6B,EAAA,CAAG3B,CAAA,EAAG;cAClB,MAAM;YACP;YAED2B,EAAA,CAAG7B,GAAA,GAAMI,CAAA;YACTyB,EAAA,CAAG5B,GAAA,GAAMmB,EAAA;YAETU,QAAA;UACD;QACF;MACF;MAED,OAAO;IACR;IAED,MAAMC,aAAA,GAAgB;MAAE1B,CAAA,EAAG;MAAGC,EAAA,EAAI;IAAG;IAErC,SAAS0B,QAAQ3B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYC,QAAA,EAAU;MAC5CL,CAAA,GAAKA,CAAA,IAAK,IAAKM,eAAA,CAAgBF,WAAA,EAAYC,QAAQ;MACnDJ,EAAA,IAAM;MAENyB,aAAA,CAAc1B,CAAA,GAAIA,CAAA;MAClB0B,aAAA,CAAczB,EAAA,GAAKA,EAAA;IACpB;IAED,MAAM2B,aAAA,GAAgB;MAAE5B,CAAA,EAAG;MAAGC,EAAA,EAAI;IAAG;IAErC,SAAS4B,QAAQC,EAAA,EAAIC,GAAA,EAAK/B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAU2B,SAAA,EAAWC,eAAA,EAAiBC,kBAAA,EAAoB;MACjH,IAAIJ,EAAA,IAAMC,GAAA,EAAK;QACb,IAAI9B,EAAA,GAAK,GAAG;UACV0B,OAAA,CAAQ3B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYC,QAAQ;UACnCL,CAAA,GAAI0B,aAAA,CAAc1B,CAAA;UAClBC,EAAA,GAAKyB,aAAA,CAAczB,EAAA;QACpB;QAEDA,EAAA,IAAM;QAEN,IAAIkC,EAAA,GAAKnC,CAAA,IAAKC,EAAA;QACd,IAAIkC,EAAA,GAAK,IAAIC,UAAA,CAAW,CAACD,EAAE,CAAC,EAAE,CAAC;QAE/B,IAAIF,eAAA,CAAgBhB,KAAA,GAAQkB,EAAA,GAAKD,kBAAA,EAAoB;UACnD,OAAO;QACR;QAED,IAAIG,CAAA,GAAIL,SAAA,CAAUC,eAAA,CAAgBhB,KAAA,GAAQ,CAAC;QAE3C,OAAOkB,EAAA,KAAO,GAAG;UACfH,SAAA,CAAUC,eAAA,CAAgBhB,KAAA,EAAO,IAAIoB,CAAA;QACtC;MACT,WAAiBJ,eAAA,CAAgBhB,KAAA,GAAQiB,kBAAA,EAAoB;QACrDF,SAAA,CAAUC,eAAA,CAAgBhB,KAAA,EAAO,IAAIa,EAAA;MAC7C,OAAa;QACL,OAAO;MACR;MAEDF,aAAA,CAAc5B,CAAA,GAAIA,CAAA;MAClB4B,aAAA,CAAc3B,EAAA,GAAKA,EAAA;IACpB;IAED,SAASqC,OAAOrB,KAAA,EAAO;MACrB,OAAOA,KAAA,GAAQ;IAChB;IAED,SAASsB,MAAMtB,KAAA,EAAO;MACpB,IAAIuB,GAAA,GAAMF,MAAA,CAAOrB,KAAK;MACtB,OAAOuB,GAAA,GAAM,QAASA,GAAA,GAAM,QAAUA,GAAA;IACvC;IAED,MAAMC,YAAA,GAAe;MAAEC,CAAA,EAAG;MAAGC,CAAA,EAAG;IAAG;IAEnC,SAASC,OAAO7C,CAAA,EAAG8C,CAAA,EAAG;MACpB,IAAIC,EAAA,GAAKP,KAAA,CAAMxC,CAAC;MAChB,IAAIgD,EAAA,GAAKR,KAAA,CAAMM,CAAC;MAEhB,IAAIG,EAAA,GAAKD,EAAA;MACT,IAAIE,EAAA,GAAKH,EAAA,IAAME,EAAA,GAAK,MAAMA,EAAA,IAAM;MAEhC,IAAIE,EAAA,GAAKD,EAAA;MACT,IAAIE,EAAA,GAAKF,EAAA,GAAKD,EAAA;MAEdP,YAAA,CAAaC,CAAA,GAAIQ,EAAA;MACjBT,YAAA,CAAaE,CAAA,GAAIQ,EAAA;IAClB;IAED,SAASC,OAAOrD,CAAA,EAAG8C,CAAA,EAAG;MACpB,IAAIQ,CAAA,GAAIf,MAAA,CAAOvC,CAAC;MAChB,IAAIuD,CAAA,GAAIhB,MAAA,CAAOO,CAAC;MAEhB,IAAIU,EAAA,GAAMF,CAAA,IAAKC,CAAA,IAAK,KAAMpF,QAAA;MAC1B,IAAIsF,EAAA,GAAMF,CAAA,GAAIC,EAAA,GAAKtF,QAAA,GAAYC,QAAA;MAE/BuE,YAAA,CAAaC,CAAA,GAAIc,EAAA;MACjBf,YAAA,CAAaE,CAAA,GAAIY,EAAA;IAClB;IAED,SAASE,WAAWC,OAAA,EAAQC,CAAA,EAAGC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI;MACjD,IAAIC,GAAA,GAAMD,EAAA,GAAK,KAAK;MACpB,IAAIxE,CAAA,GAAIoE,EAAA,GAAKE,EAAA,GAAKA,EAAA,GAAKF,EAAA;MACvB,IAAI/D,CAAA,GAAI;MACR,IAAIqE,EAAA;MAEJ,OAAOrE,CAAA,IAAKL,CAAA,EAAGK,CAAA,KAAM;MAErBA,CAAA,KAAM;MACNqE,EAAA,GAAKrE,CAAA;MACLA,CAAA,KAAM;MAEN,OAAOA,CAAA,IAAK,GAAG;QACb,IAAIsE,EAAA,GAAK;QACT,IAAIC,EAAA,GAAKD,EAAA,GAAKJ,EAAA,IAAMD,EAAA,GAAKI,EAAA;QACzB,IAAIG,GAAA,GAAMN,EAAA,GAAKlE,CAAA;QACf,IAAIyE,GAAA,GAAMP,EAAA,GAAKG,EAAA;QACf,IAAIK,GAAA,GAAMV,EAAA,GAAKhE,CAAA;QACf,IAAI2E,GAAA,GAAMX,EAAA,GAAKK,EAAA;QACf,IAAIO,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAA;QAEnB,OAAOT,EAAA,IAAMC,EAAA,EAAID,EAAA,IAAMG,GAAA,EAAK;UAC1B,IAAIO,EAAA,GAAKV,EAAA;UACT,IAAIW,EAAA,GAAKX,EAAA,GAAKN,EAAA,IAAMD,EAAA,GAAKM,EAAA;UAEzB,OAAOW,EAAA,IAAMC,EAAA,EAAID,EAAA,IAAML,GAAA,EAAK;YAC1B,IAAIO,GAAA,GAAMF,EAAA,GAAKN,GAAA;YACf,IAAIS,GAAA,GAAMH,EAAA,GAAKR,GAAA;YACf,IAAIY,GAAA,GAAMD,GAAA,GAAMT,GAAA;YAEhB,IAAIN,GAAA,EAAK;cACPrB,MAAA,CAAOc,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,CAAC;cAEtCc,GAAA,GAAMhC,YAAA,CAAaC,CAAA;cACnBiC,GAAA,GAAMlC,YAAA,CAAaE,CAAA;cAEnBC,MAAA,CAAOc,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,GAAGD,OAAA,CAAOuB,GAAA,GAAMtB,CAAC,CAAC;cAEvCe,GAAA,GAAMjC,YAAA,CAAaC,CAAA;cACnBkC,GAAA,GAAMnC,YAAA,CAAaE,CAAA;cAEnBC,MAAA,CAAO6B,GAAA,EAAKC,GAAG;cAEfhB,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,IAAIlB,YAAA,CAAaC,CAAA;cAC9BgB,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;cAE/BC,MAAA,CAAO+B,GAAA,EAAKC,GAAG;cAEflB,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,IAAIlB,YAAA,CAAaC,CAAA;cAC/BgB,OAAA,CAAOuB,GAAA,GAAMtB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;YAC7C,OAAmB;cACLS,MAAA,CAAOM,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,CAAC;cAEtCc,GAAA,GAAMhC,YAAA,CAAaC,CAAA;cACnBiC,GAAA,GAAMlC,YAAA,CAAaE,CAAA;cAEnBS,MAAA,CAAOM,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,GAAGD,OAAA,CAAOuB,GAAA,GAAMtB,CAAC,CAAC;cAEvCe,GAAA,GAAMjC,YAAA,CAAaC,CAAA;cACnBkC,GAAA,GAAMnC,YAAA,CAAaE,CAAA;cAEnBS,MAAA,CAAOqB,GAAA,EAAKC,GAAG;cAEfhB,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,IAAIlB,YAAA,CAAaC,CAAA;cAC9BgB,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;cAE/BS,MAAA,CAAOuB,GAAA,EAAKC,GAAG;cAEflB,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,IAAIlB,YAAA,CAAaC,CAAA;cAC/BgB,OAAA,CAAOuB,GAAA,GAAMtB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;YAChC;UACF;UAED,IAAIiB,EAAA,GAAK/D,CAAA,EAAG;YACV,IAAImF,GAAA,GAAMH,EAAA,GAAKR,GAAA;YAEf,IAAIJ,GAAA,EAAKrB,MAAA,CAAOc,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,CAAC,OAC1CP,MAAA,CAAOM,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,CAAC;YAE3Cc,GAAA,GAAMhC,YAAA,CAAaC,CAAA;YACnBgB,OAAA,CAAOsB,GAAA,GAAMrB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;YAE/Be,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,IAAIc,GAAA;UAClB;QACF;QAED,IAAIX,EAAA,GAAKjE,CAAA,EAAG;UACV,IAAIgF,EAAA,GAAKV,EAAA;UACT,IAAIW,EAAA,GAAKX,EAAA,GAAKN,EAAA,IAAMD,EAAA,GAAKM,EAAA;UAEzB,OAAOW,EAAA,IAAMC,EAAA,EAAID,EAAA,IAAML,GAAA,EAAK;YAC1B,IAAIO,GAAA,GAAMF,EAAA,GAAKN,GAAA;YAEf,IAAIN,GAAA,EAAKrB,MAAA,CAAOc,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,CAAC,OAC1CP,MAAA,CAAOM,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,GAAGD,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,CAAC;YAE3Cc,GAAA,GAAMhC,YAAA,CAAaC,CAAA;YACnBgB,OAAA,CAAOqB,GAAA,GAAMpB,CAAC,IAAIlB,YAAA,CAAaE,CAAA;YAE/Be,OAAA,CAAOmB,EAAA,GAAKlB,CAAC,IAAIc,GAAA;UAClB;QACF;QAEDP,EAAA,GAAKrE,CAAA;QACLA,CAAA,KAAM;MACP;MAED,OAAOsE,EAAA;IACR;IAED,SAASe,UACPC,aAAA,EACAC,aAAA,EACAhF,WAAA,EACAS,UAAA,EACAR,QAAA,EACAS,EAAA,EACAiB,GAAA,EACAsD,EAAA,EACArD,SAAA,EACAsD,SAAA,EACA;MACA,IAAItF,CAAA,GAAI;MACR,IAAIC,EAAA,GAAK;MACT,IAAIiC,kBAAA,GAAqBmD,EAAA;MACzB,IAAIE,WAAA,GAActG,IAAA,CAAKuG,KAAA,CAAMnF,QAAA,CAASY,KAAA,IAASH,EAAA,GAAK,KAAK,CAAC;MAE1D,OAAOT,QAAA,CAASY,KAAA,GAAQsE,WAAA,EAAa;QACnC5D,OAAA,CAAQ3B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYC,QAAQ;QAEnCL,CAAA,GAAI0B,aAAA,CAAc1B,CAAA;QAClBC,EAAA,GAAKyB,aAAA,CAAczB,EAAA;QAEnB,OAAOA,EAAA,IAAMrC,WAAA,EAAa;UACxB,IAAI6H,KAAA,GAASzF,CAAA,IAAMC,EAAA,GAAKrC,WAAA,GAAgBG,WAAA;UACxC,IAAIyD,EAAA,GAAK4D,aAAA,CAAcK,KAAK;UAE5B,IAAIjE,EAAA,CAAG7B,GAAA,EAAK;YACVM,EAAA,IAAMuB,EAAA,CAAG7B,GAAA;YAETkC,OAAA,CAAQL,EAAA,CAAG5B,GAAA,EAAKmC,GAAA,EAAK/B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAU2B,SAAA,EAAWsD,SAAA,EAAWpD,kBAAkB;YAEtGlC,CAAA,GAAI4B,aAAA,CAAc5B,CAAA;YAClBC,EAAA,GAAK2B,aAAA,CAAc3B,EAAA;UAC/B,OAAiB;YACL,IAAI,CAACuB,EAAA,CAAG3B,CAAA,EAAG;cACT,MAAM;YACP;YAED,IAAI8D,CAAA;YAEJ,KAAKA,CAAA,GAAI,GAAGA,CAAA,GAAInC,EAAA,CAAG5B,GAAA,EAAK+D,CAAA,IAAK;cAC3B,IAAI5D,CAAA,GAAIoB,SAAA,CAAUgE,aAAA,CAAc3D,EAAA,CAAG3B,CAAA,CAAE8D,CAAC,CAAC,CAAC;cAExC,OAAO1D,EAAA,GAAKF,CAAA,IAAKM,QAAA,CAASY,KAAA,GAAQsE,WAAA,EAAa;gBAC7C5D,OAAA,CAAQ3B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYC,QAAQ;gBAEnCL,CAAA,GAAI0B,aAAA,CAAc1B,CAAA;gBAClBC,EAAA,GAAKyB,aAAA,CAAczB,EAAA;cACpB;cAED,IAAIA,EAAA,IAAMF,CAAA,EAAG;gBACX,IAAIsB,OAAA,CAAQ8D,aAAA,CAAc3D,EAAA,CAAG3B,CAAA,CAAE8D,CAAC,CAAC,CAAC,MAAO3D,CAAA,IAAMC,EAAA,GAAKF,CAAA,IAAQ,KAAKA,CAAA,IAAK,IAAK;kBACzEE,EAAA,IAAMF,CAAA;kBAEN8B,OAAA,CACEL,EAAA,CAAG3B,CAAA,CAAE8D,CAAC,GACN5B,GAAA,EACA/B,CAAA,EACAC,EAAA,EACAG,WAAA,EACAS,UAAA,EACAR,QAAA,EACA2B,SAAA,EACAsD,SAAA,EACApD,kBACD;kBAEDlC,CAAA,GAAI4B,aAAA,CAAc5B,CAAA;kBAClBC,EAAA,GAAK2B,aAAA,CAAc3B,EAAA;kBAEnB;gBACD;cACF;YACF;YAED,IAAI0D,CAAA,IAAKnC,EAAA,CAAG5B,GAAA,EAAK;cACf,MAAM;YACP;UACF;QACF;MACF;MAED,IAAIL,CAAA,GAAK,IAAIuB,EAAA,GAAM;MAEnBd,CAAA,KAAMT,CAAA;MACNU,EAAA,IAAMV,CAAA;MAEN,OAAOU,EAAA,GAAK,GAAG;QACb,IAAIuB,EAAA,GAAK4D,aAAA,CAAepF,CAAA,IAAMpC,WAAA,GAAcqC,EAAA,GAAOlC,WAAW;QAE9D,IAAIyD,EAAA,CAAG7B,GAAA,EAAK;UACVM,EAAA,IAAMuB,EAAA,CAAG7B,GAAA;UAETkC,OAAA,CAAQL,EAAA,CAAG5B,GAAA,EAAKmC,GAAA,EAAK/B,CAAA,EAAGC,EAAA,EAAIG,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAU2B,SAAA,EAAWsD,SAAA,EAAWpD,kBAAkB;UAEtGlC,CAAA,GAAI4B,aAAA,CAAc5B,CAAA;UAClBC,EAAA,GAAK2B,aAAA,CAAc3B,EAAA;QAC7B,OAAe;UACL,MAAM;QACP;MACF;MAED,OAAO;IACR;IAED,SAASyF,cAActF,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAUsF,WAAA,EAAa3D,SAAA,EAAW4D,IAAA,EAAM;MACrF,IAAIN,SAAA,GAAY;QAAErE,KAAA,EAAO;MAAG;MAC5B,IAAI4E,eAAA,GAAkBxF,QAAA,CAASY,KAAA;MAE/B,IAAIF,EAAA,GAAK+E,WAAA,CAAYjF,UAAA,EAAYR,QAAQ;MACzC,IAAIW,EAAA,GAAK8E,WAAA,CAAYjF,UAAA,EAAYR,QAAQ;MAEzCA,QAAA,CAASY,KAAA,IAAS;MAElB,IAAId,KAAA,GAAQ2F,WAAA,CAAYjF,UAAA,EAAYR,QAAQ;MAE5CA,QAAA,CAASY,KAAA,IAAS;MAElB,IAAIF,EAAA,GAAK,KAAKA,EAAA,IAAMlD,WAAA,IAAemD,EAAA,GAAK,KAAKA,EAAA,IAAMnD,WAAA,EAAa;QAC9D,MAAM;MACP;MAED,IAAIkI,IAAA,GAAO,IAAIvF,KAAA,CAAM3C,WAAW;MAChC,IAAI6B,IAAA,GAAO,IAAIc,KAAA,CAAM1C,WAAW;MAEhC2B,gBAAA,CAAiBC,IAAI;MAErB,IAAIoB,EAAA,GAAK6E,WAAA,IAAetF,QAAA,CAASY,KAAA,GAAQ4E,eAAA;MAEzCjF,iBAAA,CAAkBR,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAUS,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI+E,IAAI;MAEpE,IAAI5F,KAAA,GAAQ,KAAKwF,WAAA,IAAetF,QAAA,CAASY,KAAA,GAAQ4E,eAAA,IAAmB;QAClE,MAAM;MACP;MAEDvE,gBAAA,CAAiByE,IAAA,EAAMhF,EAAA,EAAIC,EAAA,EAAItB,IAAI;MAEnCwF,SAAA,CAAUa,IAAA,EAAMrG,IAAA,EAAMU,WAAA,EAAYS,UAAA,EAAYR,QAAA,EAAUF,KAAA,EAAOa,EAAA,EAAI4E,IAAA,EAAM5D,SAAA,EAAWsD,SAAS;IAC9F;IAED,SAASU,SAAS3G,GAAA,EAAK4G,IAAA,EAAMC,KAAA,EAAO;MAClC,SAAS3G,CAAA,GAAI,GAAGA,CAAA,GAAI2G,KAAA,EAAO,EAAE3G,CAAA,EAAG;QAC9B0G,IAAA,CAAK1G,CAAC,IAAIF,GAAA,CAAI4G,IAAA,CAAK1G,CAAC,CAAC;MACtB;IACF;IAED,SAAS4G,UAAUC,MAAA,EAAQ;MACzB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,MAAA,CAAOE,MAAA,EAAQD,CAAA,IAAK;QACtC,IAAI/C,CAAA,GAAI8C,MAAA,CAAOC,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOC,CAAC,IAAI;QACpCD,MAAA,CAAOC,CAAC,IAAI/C,CAAA;MACb;IACF;IAED,SAASiD,iBAAiBH,MAAA,EAAQI,GAAA,EAAK;MACrC,IAAIC,EAAA,GAAK;MACT,IAAIC,EAAA,GAAKzH,IAAA,CAAK0H,KAAA,EAAOP,MAAA,CAAOE,MAAA,GAAS,KAAK,CAAC;MAC3C,IAAIjE,CAAA,GAAI;MACR,IAAIuE,IAAA,GAAOR,MAAA,CAAOE,MAAA,GAAS;MAE3B,OAAO,MAAM;QACX,IAAIjE,CAAA,GAAIuE,IAAA,EAAM;QACdJ,GAAA,CAAInE,CAAA,EAAG,IAAI+D,MAAA,CAAOK,EAAA,EAAI;QAEtB,IAAIpE,CAAA,GAAIuE,IAAA,EAAM;QACdJ,GAAA,CAAInE,CAAA,EAAG,IAAI+D,MAAA,CAAOM,EAAA,EAAI;MACvB;IACF;IAED,SAASG,gBAAgBT,MAAA,EAAQ;MAC/B,IAAIU,IAAA,GAAOV,MAAA,CAAOW,UAAA;MAClB,IAAIP,GAAA,GAAM,IAAIhG,KAAA,CAAO;MACrB,IAAIX,CAAA,GAAI;MAER,IAAImH,MAAA,GAAS,IAAIC,QAAA,CAASb,MAAM;MAEhC,OAAOU,IAAA,GAAO,GAAG;QACf,IAAI/G,CAAA,GAAIiH,MAAA,CAAOE,OAAA,CAAQrH,CAAA,EAAG;QAE1B,IAAIE,CAAA,GAAI,GAAG;UACT,IAAIoH,KAAA,GAAQ,CAACpH,CAAA;UACb+G,IAAA,IAAQK,KAAA,GAAQ;UAEhB,SAAS5H,CAAA,GAAI,GAAGA,CAAA,GAAI4H,KAAA,EAAO5H,CAAA,IAAK;YAC9BiH,GAAA,CAAIY,IAAA,CAAKJ,MAAA,CAAOK,QAAA,CAASxH,CAAA,EAAG,CAAC;UAC9B;QACX,OAAe;UACL,IAAIsH,KAAA,GAAQpH,CAAA;UACZ+G,IAAA,IAAQ;UAER,IAAI7F,KAAA,GAAQ+F,MAAA,CAAOK,QAAA,CAASxH,CAAA,EAAG;UAE/B,SAASN,CAAA,GAAI,GAAGA,CAAA,GAAI4H,KAAA,GAAQ,GAAG5H,CAAA,IAAK;YAClCiH,GAAA,CAAIY,IAAA,CAAKnG,KAAK;UACf;QACF;MACF;MAED,OAAOuF,GAAA;IACR;IAED,SAASc,eAAeC,MAAA,EAAQC,OAAA,EAASC,WAAA,EAAaC,QAAA,EAAUC,QAAA,EAAU3F,SAAA,EAAW;MACnF,IAAI4F,QAAA,GAAW,IAAIX,QAAA,CAASjF,SAAA,CAAUxE,MAAM;MAE5C,IAAIqK,KAAA,GAAQJ,WAAA,CAAYF,MAAA,CAAOO,GAAA,CAAI,CAAC,CAAC,EAAED,KAAA;MACvC,IAAIE,MAAA,GAASN,WAAA,CAAYF,MAAA,CAAOO,GAAA,CAAI,CAAC,CAAC,EAAEC,MAAA;MAExC,IAAIC,OAAA,GAAU;MAEd,IAAIC,cAAA,GAAiBhJ,IAAA,CAAK0H,KAAA,CAAMkB,KAAA,GAAQ,CAAG;MAC3C,IAAIK,UAAA,GAAajJ,IAAA,CAAKkJ,IAAA,CAAKN,KAAA,GAAQ,CAAG;MACtC,IAAIO,UAAA,GAAanJ,IAAA,CAAKkJ,IAAA,CAAKJ,MAAA,GAAS,CAAG;MACvC,IAAIM,SAAA,GAAYR,KAAA,IAASK,UAAA,GAAa,KAAK;MAC3C,IAAII,SAAA,GAAYP,MAAA,IAAUK,UAAA,GAAa,KAAK;MAE5C,IAAIG,UAAA,GAAa;QAAEtH,KAAA,EAAO;MAAG;MAC7B,IAAIuH,UAAA,GAAa,IAAIhI,KAAA,CAAMwH,OAAO;MAClC,IAAIS,OAAA,GAAU,IAAIjI,KAAA,CAAMwH,OAAO;MAC/B,IAAIU,YAAA,GAAe,IAAIlI,KAAA,CAAMwH,OAAO;MACpC,IAAIW,QAAA,GAAW,IAAInI,KAAA,CAAMwH,OAAO;MAChC,IAAIY,UAAA,GAAa,IAAIpI,KAAA,CAAMwH,OAAO;MAElC,SAASa,KAAA,GAAO,GAAGA,KAAA,GAAOb,OAAA,EAAS,EAAEa,KAAA,EAAM;QACzCD,UAAA,CAAWC,KAAI,IAAIrB,OAAA,CAAQD,MAAA,CAAOO,GAAA,CAAIe,KAAI,CAAC;QAC3CL,UAAA,CAAWK,KAAI,IAAIA,KAAA,GAAO,IAAI,IAAIL,UAAA,CAAWK,KAAA,GAAO,CAAC,IAAIX,UAAA,GAAaE,UAAA;QACtEK,OAAA,CAAQI,KAAI,IAAI,IAAIC,YAAA,CAAa,EAAE;QACnCJ,YAAA,CAAaG,KAAI,IAAI,IAAIE,WAAA,CAAY,EAAE;QACvCJ,QAAA,CAASE,KAAI,IAAI,IAAIE,WAAA,CAAYb,UAAA,GAAa,EAAE;MACjD;MAED,SAASc,MAAA,GAAS,GAAGA,MAAA,GAASZ,UAAA,EAAY,EAAEY,MAAA,EAAQ;QAClD,IAAIC,IAAA,GAAO;QAEX,IAAID,MAAA,IAAUZ,UAAA,GAAa,GAAGa,IAAA,GAAOX,SAAA;QAErC,IAAIY,IAAA,GAAO;QAEX,SAASC,MAAA,GAAS,GAAGA,MAAA,GAASjB,UAAA,EAAY,EAAEiB,MAAA,EAAQ;UAClD,IAAIA,MAAA,IAAUjB,UAAA,GAAa,GAAGgB,IAAA,GAAOb,SAAA;UAErC,SAASQ,KAAA,GAAO,GAAGA,KAAA,GAAOb,OAAA,EAAS,EAAEa,KAAA,EAAM;YACzCH,YAAA,CAAaG,KAAI,EAAEO,IAAA,CAAK,CAAC;YAGzBV,YAAA,CAAaG,KAAI,EAAE,CAAC,IAAIlB,QAAA,CAASa,UAAA,CAAWK,KAAI,GAAG;YAEnDQ,OAAA,CAAQd,UAAA,EAAYb,QAAA,EAAUgB,YAAA,CAAaG,KAAI,CAAC;YAGhDS,QAAA,CAASZ,YAAA,CAAaG,KAAI,GAAGJ,OAAA,CAAQI,KAAI,CAAC;YAE1CU,UAAA,CAAWd,OAAA,CAAQI,KAAI,CAAC;UACzB;UAEiB;YAChBW,aAAA,CAAcf,OAAO;UACtB;UAED,SAASI,KAAA,GAAO,GAAGA,KAAA,GAAOb,OAAA,EAAS,EAAEa,KAAA,EAAM;YACzCY,aAAA,CAAchB,OAAA,CAAQI,KAAI,GAAGF,QAAA,CAASE,KAAI,GAAGM,MAAA,GAAS,EAAE;UACzD;QACF;QAED,IAAIO,OAAA,GAAS;QAEb,SAASb,KAAA,GAAO,GAAGA,KAAA,GAAOb,OAAA,EAAS,EAAEa,KAAA,EAAM;UACzC,MAAMc,KAAA,GAAOlC,WAAA,CAAYF,MAAA,CAAOO,GAAA,CAAIe,KAAI,CAAC,EAAExL,IAAA;UAE3C,SAASuM,EAAA,GAAI,IAAIZ,MAAA,EAAQY,EAAA,GAAI,IAAIZ,MAAA,GAASC,IAAA,EAAM,EAAEW,EAAA,EAAG;YACnDF,OAAA,GAASd,UAAA,CAAWC,KAAI,EAAEe,EAAC;YAE3B,SAAST,MAAA,GAAS,GAAGA,MAAA,GAASlB,cAAA,EAAgB,EAAEkB,MAAA,EAAQ;cACtD,MAAMU,GAAA,GAAMV,MAAA,GAAS,MAAMS,EAAA,GAAI,KAAO;cAEtChC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAEhFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAChFjC,QAAA,CAASkC,SAAA,CAAUJ,OAAA,GAAS,IAAIjL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAM,CAAC,GAAG,IAAI;cAEhFH,OAAA,IAAU,IAAIjL,UAAA,GAAakL,KAAA;YAC5B;UACF;UAGD,IAAI1B,cAAA,IAAkBC,UAAA,EAAY;YAChC,SAAS0B,EAAA,GAAI,IAAIZ,MAAA,EAAQY,EAAA,GAAI,IAAIZ,MAAA,GAASC,IAAA,EAAM,EAAEW,EAAA,EAAG;cACnD,MAAMG,OAAA,GAASnB,UAAA,CAAWC,KAAI,EAAEe,EAAC,IAAI,IAAI3B,cAAA,GAAiBxJ,UAAA,GAAakL,KAAA;cACvE,MAAME,GAAA,GAAM5B,cAAA,GAAiB,MAAM2B,EAAA,GAAI,KAAO;cAE9C,SAASI,EAAA,GAAI,GAAGA,EAAA,GAAId,IAAA,EAAM,EAAEc,EAAA,EAAG;gBAC7BpC,QAAA,CAASkC,SAAA,CAAUC,OAAA,GAASC,EAAA,GAAIvL,UAAA,GAAakL,KAAA,EAAMhB,QAAA,CAASE,KAAI,EAAEgB,GAAA,GAAMG,EAAC,GAAG,IAAI;cACjF;YACF;UACF;QACF;MACF;MAED,IAAIC,OAAA,GAAU,IAAIlB,WAAA,CAAYlB,KAAK;MACnC,IAAID,QAAA,GAAW,IAAIX,QAAA,CAASjF,SAAA,CAAUxE,MAAM;MAG5C,SAAS0M,IAAA,GAAO,GAAGA,IAAA,GAAOlC,OAAA,EAAS,EAAEkC,IAAA,EAAM;QACzCzC,WAAA,CAAYF,MAAA,CAAOO,GAAA,CAAIoC,IAAI,CAAC,EAAEC,OAAA,GAAU;QACxC,IAAI9M,IAAA,GAAOoK,WAAA,CAAYF,MAAA,CAAOO,GAAA,CAAIoC,IAAI,CAAC,EAAE7M,IAAA;QAEzC,IAAIoK,WAAA,CAAYyC,IAAI,EAAE7M,IAAA,IAAQ,GAAG;QAEjC,SAAS+M,CAAA,GAAI,GAAGA,CAAA,GAAIrC,MAAA,EAAQ,EAAEqC,CAAA,EAAG;UAC/B,MAAMV,OAAA,GAASd,UAAA,CAAWsB,IAAI,EAAEE,CAAC;UAEjC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIxC,KAAA,EAAO,EAAEwC,CAAA,EAAG;YAC9BJ,OAAA,CAAQI,CAAC,IAAIzC,QAAA,CAAS0C,SAAA,CAAUZ,OAAA,GAASW,CAAA,GAAI5L,UAAA,GAAapB,IAAA,EAAM,IAAI;UACrE;UAED,SAASgN,CAAA,GAAI,GAAGA,CAAA,GAAIxC,KAAA,EAAO,EAAEwC,CAAA,EAAG;YAC9BzC,QAAA,CAAS2C,UAAA,CAAWb,OAAA,GAASW,CAAA,GAAI5L,UAAA,GAAapB,IAAA,EAAMmN,aAAA,CAAcP,OAAA,CAAQI,CAAC,CAAC,GAAG,IAAI;UACpF;QACF;MACF;IACF;IAED,SAAShB,QAAQd,UAAA,EAAYb,QAAA,EAAUgB,YAAA,EAAc;MACnD,IAAI+B,OAAA;MACJ,IAAIC,OAAA,GAAU;MAEd,OAAOA,OAAA,GAAU,IAAI;QACnBD,OAAA,GAAU/C,QAAA,CAASa,UAAA,CAAWtH,KAAK;QAEnC,IAAIwJ,OAAA,IAAW,OAAQ;UACrBC,OAAA,GAAU;QACpB,WAAmBD,OAAA,IAAW,KAAK,KAAM;UAC/BC,OAAA,IAAWD,OAAA,GAAU;QAC/B,OAAe;UACL/B,YAAA,CAAagC,OAAO,IAAID,OAAA;UACxBC,OAAA;QACD;QAEDnC,UAAA,CAAWtH,KAAA;MACZ;IACF;IAED,SAASqI,SAASO,GAAA,EAAKc,GAAA,EAAK;MAC1BA,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC7Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC7Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC7Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC7Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC9Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC9Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC9Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC9Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC7Bc,GAAA,CAAI,CAAC,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAE7Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC9Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC9Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC9Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAE/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,CAAC,CAAC;MAC9Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAE/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAE/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAE/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAE/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;MAC/Bc,GAAA,CAAI,EAAE,IAAIH,aAAA,CAAcX,GAAA,CAAI,EAAE,CAAC;IAChC;IAED,SAASN,WAAWtD,IAAA,EAAM;MACxB,MAAMvD,CAAA,GAAI,MAAMzD,IAAA,CAAK2L,GAAA,CAAI,UAAU,CAAG;MACtC,MAAMjI,CAAA,GAAI,MAAM1D,IAAA,CAAK2L,GAAA,CAAI,UAAU,EAAI;MACvC,MAAM5K,CAAA,GAAI,MAAMf,IAAA,CAAK2L,GAAA,CAAI,UAAU,CAAG;MACtC,MAAMtH,CAAA,GAAI,MAAMrE,IAAA,CAAK2L,GAAA,CAAK,IAAM,UAAW,EAAI;MAC/C,MAAMC,CAAA,GAAI,MAAM5L,IAAA,CAAK2L,GAAA,CAAK,IAAM,UAAW,EAAI;MAC/C,MAAME,CAAA,GAAI,MAAM7L,IAAA,CAAK2L,GAAA,CAAK,IAAM,UAAW,CAAG;MAC9C,MAAMG,CAAA,GAAI,MAAM9L,IAAA,CAAK2L,GAAA,CAAK,IAAM,UAAW,EAAI;MAE/C,IAAII,KAAA,GAAQ,IAAIxK,KAAA,CAAM,CAAC;MACvB,IAAIyK,IAAA,GAAO,IAAIzK,KAAA,CAAM,CAAC;MACtB,IAAI0K,KAAA,GAAQ,IAAI1K,KAAA,CAAM,CAAC;MACvB,IAAI2K,KAAA,GAAQ,IAAI3K,KAAA,CAAM,CAAC;MAEvB,SAAS4K,GAAA,GAAM,GAAGA,GAAA,GAAM,GAAG,EAAEA,GAAA,EAAK;QAChC,IAAIC,MAAA,GAASD,GAAA,GAAM;QAEnBJ,KAAA,CAAM,CAAC,IAAIhL,CAAA,GAAIiG,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAC9BL,KAAA,CAAM,CAAC,IAAIF,CAAA,GAAI7E,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAC9BL,KAAA,CAAM,CAAC,IAAIhL,CAAA,GAAIiG,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAC9BL,KAAA,CAAM,CAAC,IAAIF,CAAA,GAAI7E,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAE9BJ,IAAA,CAAK,CAAC,IAAItI,CAAA,GAAIsD,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI/H,CAAA,GAAI2C,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIR,CAAA,GAAI5E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIN,CAAA,GAAI9E,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAClGJ,IAAA,CAAK,CAAC,IAAI3H,CAAA,GAAI2C,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIN,CAAA,GAAI9E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI1I,CAAA,GAAIsD,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIR,CAAA,GAAI5E,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAClGJ,IAAA,CAAK,CAAC,IAAIJ,CAAA,GAAI5E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI1I,CAAA,GAAIsD,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIN,CAAA,GAAI9E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI/H,CAAA,GAAI2C,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAClGJ,IAAA,CAAK,CAAC,IAAIF,CAAA,GAAI9E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIR,CAAA,GAAI5E,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI/H,CAAA,GAAI2C,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAI1I,CAAA,GAAIsD,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAElGH,KAAA,CAAM,CAAC,IAAIxI,CAAA,IAAKuD,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIpF,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAClDH,KAAA,CAAM,CAAC,IAAIxI,CAAA,IAAKuD,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIpF,IAAA,CAAKoF,MAAA,GAAS,CAAC;QAClDH,KAAA,CAAM,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BE,KAAA,CAAM,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAE7BG,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAE7BjF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QAEpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAKoF,MAAA,GAAS,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;MACrC;MAED,SAASK,MAAA,GAAS,GAAGA,MAAA,GAAS,GAAG,EAAEA,MAAA,EAAQ;QACzCN,KAAA,CAAM,CAAC,IAAIhL,CAAA,GAAIiG,IAAA,CAAK,KAAKqF,MAAM;QAC/BN,KAAA,CAAM,CAAC,IAAIF,CAAA,GAAI7E,IAAA,CAAK,KAAKqF,MAAM;QAC/BN,KAAA,CAAM,CAAC,IAAIhL,CAAA,GAAIiG,IAAA,CAAK,KAAKqF,MAAM;QAC/BN,KAAA,CAAM,CAAC,IAAIF,CAAA,GAAI7E,IAAA,CAAK,KAAKqF,MAAM;QAE/BL,IAAA,CAAK,CAAC,IAAItI,CAAA,GAAIsD,IAAA,CAAK,IAAIqF,MAAM,IAAIhI,CAAA,GAAI2C,IAAA,CAAK,KAAKqF,MAAM,IAAIT,CAAA,GAAI5E,IAAA,CAAK,KAAKqF,MAAM,IAAIP,CAAA,GAAI9E,IAAA,CAAK,KAAKqF,MAAM;QACrGL,IAAA,CAAK,CAAC,IAAI3H,CAAA,GAAI2C,IAAA,CAAK,IAAIqF,MAAM,IAAIP,CAAA,GAAI9E,IAAA,CAAK,KAAKqF,MAAM,IAAI3I,CAAA,GAAIsD,IAAA,CAAK,KAAKqF,MAAM,IAAIT,CAAA,GAAI5E,IAAA,CAAK,KAAKqF,MAAM;QACrGL,IAAA,CAAK,CAAC,IAAIJ,CAAA,GAAI5E,IAAA,CAAK,IAAIqF,MAAM,IAAI3I,CAAA,GAAIsD,IAAA,CAAK,KAAKqF,MAAM,IAAIP,CAAA,GAAI9E,IAAA,CAAK,KAAKqF,MAAM,IAAIhI,CAAA,GAAI2C,IAAA,CAAK,KAAKqF,MAAM;QACrGL,IAAA,CAAK,CAAC,IAAIF,CAAA,GAAI9E,IAAA,CAAK,IAAIqF,MAAM,IAAIT,CAAA,GAAI5E,IAAA,CAAK,KAAKqF,MAAM,IAAIhI,CAAA,GAAI2C,IAAA,CAAK,KAAKqF,MAAM,IAAI3I,CAAA,GAAIsD,IAAA,CAAK,KAAKqF,MAAM;QAErGJ,KAAA,CAAM,CAAC,IAAIxI,CAAA,IAAKuD,IAAA,CAAKqF,MAAM,IAAIrF,IAAA,CAAK,KAAKqF,MAAM;QAC/CJ,KAAA,CAAM,CAAC,IAAIxI,CAAA,IAAKuD,IAAA,CAAKqF,MAAM,IAAIrF,IAAA,CAAK,KAAKqF,MAAM;QAE/CJ,KAAA,CAAM,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BE,KAAA,CAAM,CAAC,IAAIF,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAE7BG,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAC7BC,KAAA,CAAM,CAAC,IAAID,KAAA,CAAM,CAAC,IAAIA,KAAA,CAAM,CAAC;QAE7BjF,IAAA,CAAK,IAAIqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAK,IAAIqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACpChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACrChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QAErChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACrChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACrChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;QACrChF,IAAA,CAAK,KAAKqF,MAAM,IAAIH,KAAA,CAAM,CAAC,IAAIF,IAAA,CAAK,CAAC;MACtC;IACF;IAED,SAASzB,cAAcvD,IAAA,EAAM;MAC3B,SAAS1G,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAI,EAAEA,CAAA,EAAG;QAC3B,IAAI6K,CAAA,GAAInE,IAAA,CAAK,CAAC,EAAE1G,CAAC;QACjB,IAAIgM,EAAA,GAAKtF,IAAA,CAAK,CAAC,EAAE1G,CAAC;QAClB,IAAIiM,EAAA,GAAKvF,IAAA,CAAK,CAAC,EAAE1G,CAAC;QAElB0G,IAAA,CAAK,CAAC,EAAE1G,CAAC,IAAI6K,CAAA,GAAI,SAASoB,EAAA;QAC1BvF,IAAA,CAAK,CAAC,EAAE1G,CAAC,IAAI6K,CAAA,GAAI,SAASmB,EAAA,GAAK,SAASC,EAAA;QACxCvF,IAAA,CAAK,CAAC,EAAE1G,CAAC,IAAI6K,CAAA,GAAI,SAASmB,EAAA;MAC3B;IACF;IAED,SAAS9B,cAAcI,GAAA,EAAKc,GAAA,EAAK7C,GAAA,EAAK;MACpC,SAASvI,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAI,EAAEA,CAAA,EAAG;QAC3BoL,GAAA,CAAI7C,GAAA,GAAMvI,CAAC,IAAIkM,SAAA,CAAUC,WAAA,CAAYC,QAAA,CAAS9B,GAAA,CAAItK,CAAC,CAAC,CAAC;MACtD;IACF;IAED,SAASoM,SAASC,KAAA,EAAO;MACvB,IAAIA,KAAA,IAAS,GAAG;QACd,OAAO3M,IAAA,CAAK4M,IAAA,CAAKD,KAAK,IAAI3M,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAK6M,GAAA,CAAIF,KAAK,GAAG,GAAG;MAC/D,OAAa;QACL,OAAO3M,IAAA,CAAK4M,IAAA,CAAKD,KAAK,IAAI3M,IAAA,CAAKC,GAAA,CAAIF,OAAA,EAASC,IAAA,CAAK6M,GAAA,CAAIF,KAAK,IAAI,CAAG;MAClE;IACF;IAED,SAASG,cAAcC,IAAA,EAAM;MAC3B,OAAO,IAAI/E,QAAA,CAAS+E,IAAA,CAAKC,KAAA,CAAMzO,MAAA,EAAQwO,IAAA,CAAKE,MAAA,CAAOjL,KAAA,EAAO+K,IAAA,CAAKlF,IAAI;IACpE;IAED,SAASqF,cAAcH,IAAA,EAAM;MAC3B,IAAII,UAAA,GAAaJ,IAAA,CAAKK,MAAA,CAAO7O,MAAA,CAAO8O,KAAA,CAAMN,IAAA,CAAKE,MAAA,CAAOjL,KAAA,EAAO+K,IAAA,CAAKE,MAAA,CAAOjL,KAAA,GAAQ+K,IAAA,CAAKlF,IAAI;MAE1F,IAAIyF,SAAA,GAAY,IAAInK,UAAA,CAAWyE,eAAA,CAAgBuF,UAAU,CAAC;MAC1D,IAAII,SAAA,GAAY,IAAIpK,UAAA,CAAWmK,SAAA,CAAUjG,MAAM;MAE/CH,SAAA,CAAUoG,SAAS;MAEnBhG,gBAAA,CAAiBgG,SAAA,EAAWC,SAAS;MAErC,OAAO,IAAIvF,QAAA,CAASuF,SAAA,CAAUhP,MAAM;IACrC;IAED,SAASiP,cAAcT,IAAA,EAAM;MAC3B,IAAII,UAAA,GAAaJ,IAAA,CAAKC,KAAA,CAAMK,KAAA,CAAMN,IAAA,CAAKE,MAAA,CAAOjL,KAAA,EAAO+K,IAAA,CAAKE,MAAA,CAAOjL,KAAA,GAAQ+K,IAAA,CAAKlF,IAAI;MAClF,IAAIyF,SAAA,GAAYG,UAAA,CAAWN,UAAU;MACrC,IAAII,SAAA,GAAY,IAAIpK,UAAA,CAAWmK,SAAA,CAAUjG,MAAM;MAE/CH,SAAA,CAAUoG,SAAS;MAEnBhG,gBAAA,CAAiBgG,SAAA,EAAWC,SAAS;MAErC,OAAO,IAAIvF,QAAA,CAASuF,SAAA,CAAUhP,MAAM;IACrC;IAED,SAASmP,cAAcX,IAAA,EAAM;MAC3B,IAAInL,UAAA,GAAamL,IAAA,CAAKK,MAAA;MACtB,IAAIhM,QAAA,GAAW;QAAEY,KAAA,EAAO+K,IAAA,CAAKE,MAAA,CAAOjL;MAAO;MAE3C,IAAIe,SAAA,GAAY,IAAI+G,WAAA,CAAYiD,IAAA,CAAKnE,KAAA,GAAQmE,IAAA,CAAKY,iBAAA,IAAqBZ,IAAA,CAAKa,QAAA,GAAWb,IAAA,CAAK3O,IAAA,CAAK;MACjG,IAAI+B,MAAA,GAAS,IAAIgD,UAAA,CAAW1E,WAAW;MAGvC,IAAIoP,YAAA,GAAe;MACnB,IAAIC,cAAA,GAAiB,IAAIvM,KAAA,CAAMwL,IAAA,CAAKa,QAAQ;MAC5C,SAAStN,CAAA,GAAI,GAAGA,CAAA,GAAIyM,IAAA,CAAKa,QAAA,EAAUtN,CAAA,IAAK;QACtCwN,cAAA,CAAexN,CAAC,IAAI,CAAE;QACtBwN,cAAA,CAAexN,CAAC,EAAE,OAAO,IAAIuN,YAAA;QAC7BC,cAAA,CAAexN,CAAC,EAAE,KAAK,IAAIwN,cAAA,CAAexN,CAAC,EAAE,OAAO;QACpDwN,cAAA,CAAexN,CAAC,EAAE,IAAI,IAAIyM,IAAA,CAAKnE,KAAA;QAC/BkF,cAAA,CAAexN,CAAC,EAAE,IAAI,IAAIyM,IAAA,CAAKgB,KAAA;QAC/BD,cAAA,CAAexN,CAAC,EAAE,MAAM,IAAIyM,IAAA,CAAK3O,IAAA;QAEjCyP,YAAA,IAAgBC,cAAA,CAAexN,CAAC,EAAEqE,EAAA,GAAKmJ,cAAA,CAAexN,CAAC,EAAEuE,EAAA,GAAKiJ,cAAA,CAAexN,CAAC,EAAEuH,IAAA;MACjF;MAID,IAAImG,UAAA,GAAaC,WAAA,CAAYrM,UAAA,EAAYR,QAAQ;MACjD,IAAI8M,UAAA,GAAaD,WAAA,CAAYrM,UAAA,EAAYR,QAAQ;MAEjD,IAAI8M,UAAA,IAAczP,WAAA,EAAa;QAC7B,MAAM;MACP;MAED,IAAIuP,UAAA,IAAcE,UAAA,EAAY;QAC5B,SAAS5N,CAAA,GAAI,GAAGA,CAAA,GAAI4N,UAAA,GAAaF,UAAA,GAAa,GAAG1N,CAAA,IAAK;UACpDH,MAAA,CAAOG,CAAA,GAAI0N,UAAU,IAAIG,UAAA,CAAWvM,UAAA,EAAYR,QAAQ;QACzD;MACF;MAGD,IAAIhB,GAAA,GAAM,IAAI0J,WAAA,CAAYtL,YAAY;MACtC,IAAI4P,QAAA,GAAWlO,oBAAA,CAAqBC,MAAA,EAAQC,GAAG;MAE/C,IAAIiH,MAAA,GAASR,WAAA,CAAYjF,UAAA,EAAYR,QAAQ;MAG7CqF,aAAA,CAAcsG,IAAA,CAAKC,KAAA,EAAOpL,UAAA,EAAYR,QAAA,EAAUiG,MAAA,EAAQtE,SAAA,EAAW8K,YAAY;MAG/E,SAASvN,CAAA,GAAI,GAAGA,CAAA,GAAIyM,IAAA,CAAKa,QAAA,EAAU,EAAEtN,CAAA,EAAG;QACtC,IAAI+N,EAAA,GAAKP,cAAA,CAAexN,CAAC;QAEzB,SAASoE,CAAA,GAAI,GAAGA,CAAA,GAAIoJ,cAAA,CAAexN,CAAC,EAAEuH,IAAA,EAAM,EAAEnD,CAAA,EAAG;UAC/CF,UAAA,CAAWzB,SAAA,EAAWsL,EAAA,CAAGC,KAAA,GAAQ5J,CAAA,EAAG2J,EAAA,CAAG1J,EAAA,EAAI0J,EAAA,CAAGxG,IAAA,EAAMwG,EAAA,CAAGxJ,EAAA,EAAIwJ,EAAA,CAAG1J,EAAA,GAAK0J,EAAA,CAAGxG,IAAA,EAAMuG,QAAQ;QACrF;MACF;MAGDrH,QAAA,CAAS3G,GAAA,EAAK2C,SAAA,EAAW8K,YAAY;MAGrC,IAAIU,UAAA,GAAY;MAChB,IAAIhB,SAAA,GAAY,IAAIpK,UAAA,CAAWJ,SAAA,CAAUxE,MAAA,CAAOuJ,UAAU;MAC1D,SAASqD,CAAA,GAAI,GAAGA,CAAA,GAAI4B,IAAA,CAAKgB,KAAA,EAAO5C,CAAA,IAAK;QACnC,SAASpK,CAAA,GAAI,GAAGA,CAAA,GAAIgM,IAAA,CAAKa,QAAA,EAAU7M,CAAA,IAAK;UACtC,IAAIsN,EAAA,GAAKP,cAAA,CAAe/M,CAAC;UAEzB,IAAIR,CAAA,GAAI8N,EAAA,CAAG1J,EAAA,GAAK0J,EAAA,CAAGxG,IAAA;UACnB,IAAI2G,EAAA,GAAK,IAAIrL,UAAA,CAAWJ,SAAA,CAAUxE,MAAA,EAAQ8P,EAAA,CAAGI,GAAA,GAAMjP,UAAA,EAAYe,CAAA,GAAIf,UAAU;UAE7E+N,SAAA,CAAUmB,GAAA,CAAIF,EAAA,EAAID,UAAS;UAC3BA,UAAA,IAAahO,CAAA,GAAIf,UAAA;UACjB6O,EAAA,CAAGI,GAAA,IAAOlO,CAAA;QACX;MACF;MAED,OAAO,IAAIyH,QAAA,CAASuF,SAAA,CAAUhP,MAAM;IACrC;IAED,SAASoQ,cAAc5B,IAAA,EAAM;MAC3B,IAAII,UAAA,GAAaJ,IAAA,CAAKC,KAAA,CAAMK,KAAA,CAAMN,IAAA,CAAKE,MAAA,CAAOjL,KAAA,EAAO+K,IAAA,CAAKE,MAAA,CAAOjL,KAAA,GAAQ+K,IAAA,CAAKlF,IAAI;MAClF,IAAIyF,SAAA,GAAYG,UAAA,CAAWN,UAAU;MAErC,MAAMyB,EAAA,GAAK7B,IAAA,CAAKgB,KAAA,GAAQhB,IAAA,CAAKa,QAAA,GAAWb,IAAA,CAAKnE,KAAA;MAC7C,MAAM2E,SAAA,GAAYR,IAAA,CAAK3O,IAAA,IAAQ,IAAI,IAAI0L,WAAA,CAAY8E,EAAE,IAAI,IAAIC,WAAA,CAAYD,EAAE;MAE3E,IAAIE,YAAA,GAAe;MACnB,IAAIC,QAAA,GAAW;MACf,MAAMC,GAAA,GAAM,IAAIzN,KAAA,CAAM,CAAC;MAEvB,SAAS4J,CAAA,GAAI,GAAGA,CAAA,GAAI4B,IAAA,CAAKgB,KAAA,EAAO5C,CAAA,IAAK;QACnC,SAASpK,CAAA,GAAI,GAAGA,CAAA,GAAIgM,IAAA,CAAKa,QAAA,EAAU7M,CAAA,IAAK;UACtC,IAAIkO,KAAA,GAAQ;UAEZ,QAAQlC,IAAA,CAAK3O,IAAA;YACX,KAAK;cACH4Q,GAAA,CAAI,CAAC,IAAIF,YAAA;cACTE,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIjC,IAAA,CAAKnE,KAAA;cACvBkG,YAAA,GAAeE,GAAA,CAAI,CAAC,IAAIjC,IAAA,CAAKnE,KAAA;cAE7B,SAASlE,CAAA,GAAI,GAAGA,CAAA,GAAIqI,IAAA,CAAKnE,KAAA,EAAO,EAAElE,CAAA,EAAG;gBACnC,MAAMwK,IAAA,GAAQ5B,SAAA,CAAU0B,GAAA,CAAI,CAAC,GAAG,KAAK,IAAK1B,SAAA,CAAU0B,GAAA,CAAI,CAAC,GAAG;gBAE5DC,KAAA,IAASC,IAAA;gBAET3B,SAAA,CAAUwB,QAAQ,IAAIE,KAAA;gBACtBF,QAAA;cACD;cAED;YAEF,KAAK;cACHC,GAAA,CAAI,CAAC,IAAIF,YAAA;cACTE,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIjC,IAAA,CAAKnE,KAAA;cACvBoG,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIjC,IAAA,CAAKnE,KAAA;cACvBkG,YAAA,GAAeE,GAAA,CAAI,CAAC,IAAIjC,IAAA,CAAKnE,KAAA;cAE7B,SAASlE,CAAA,GAAI,GAAGA,CAAA,GAAIqI,IAAA,CAAKnE,KAAA,EAAO,EAAElE,CAAA,EAAG;gBACnC,MAAMwK,IAAA,GAAQ5B,SAAA,CAAU0B,GAAA,CAAI,CAAC,GAAG,KAAK,KAAO1B,SAAA,CAAU0B,GAAA,CAAI,CAAC,GAAG,KAAK,KAAO1B,SAAA,CAAU0B,GAAA,CAAI,CAAC,GAAG,KAAK;gBAEjGC,KAAA,IAASC,IAAA;gBAET3B,SAAA,CAAUwB,QAAQ,IAAIE,KAAA;gBACtBF,QAAA;cACD;cAED;UACH;QACF;MACF;MAED,OAAO,IAAI/G,QAAA,CAASuF,SAAA,CAAUhP,MAAM;IACrC;IAED,SAAS4Q,cAAcpC,IAAA,EAAM;MAC3B,IAAInL,UAAA,GAAamL,IAAA,CAAKK,MAAA;MACtB,IAAIhM,QAAA,GAAW;QAAEY,KAAA,EAAO+K,IAAA,CAAKE,MAAA,CAAOjL;MAAO;MAC3C,IAAIe,SAAA,GAAY,IAAII,UAAA,CAAW4J,IAAA,CAAKnE,KAAA,GAAQmE,IAAA,CAAKgB,KAAA,IAAShB,IAAA,CAAKa,QAAA,GAAWb,IAAA,CAAK3O,IAAA,GAAOoB,UAAA,CAAW;MAGjG,IAAI4P,SAAA,GAAY;QACdrR,OAAA,EAASsR,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACxCkO,uBAAA,EAAyBD,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACxDmO,qBAAA,EAAuBF,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACtDoO,gBAAA,EAAkBH,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACjDqO,gBAAA,EAAkBJ,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACjDsO,iBAAA,EAAmBL,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QAClDuO,mBAAA,EAAqBN,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACpDwO,UAAA,EAAYP,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QAC3CyO,wBAAA,EAA0BR,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACzD0O,wBAAA,EAA0BT,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;QACzD2O,aAAA,EAAeV,UAAA,CAAWzN,UAAA,EAAYR,QAAQ;MAC/C;MAED,IAAIgO,SAAA,CAAUrR,OAAA,GAAU,GAAG;QACzB,MAAM,sBAAsBiS,SAAA,CAAUC,WAAA,GAAc,cAAcb,SAAA,CAAUrR,OAAA,GAAU;MACvF;MAGD,IAAImS,YAAA,GAAe,IAAI3O,KAAA,CAAO;MAC9B,IAAI4O,QAAA,GAAWlC,WAAA,CAAYrM,UAAA,EAAYR,QAAQ,IAAI5B,UAAA;MAEnD,OAAO2Q,QAAA,GAAW,GAAG;QACnB,IAAIC,IAAA,GAAOC,yBAAA,CAA0BzO,UAAA,CAAWrD,MAAA,EAAQ6C,QAAQ;QAChE,IAAIY,KAAA,GAAQmM,UAAA,CAAWvM,UAAA,EAAYR,QAAQ;QAC3C,IAAI6O,WAAA,GAAejO,KAAA,IAAS,IAAK;QACjC,IAAIsO,GAAA,IAAOtO,KAAA,IAAS,KAAK;QACzB,IAAIwE,KAAA,GAAQ,IAAI+J,SAAA,CAAU,CAACD,GAAG,CAAC,EAAE,CAAC;QAClC,IAAIlS,IAAA,GAAO+P,UAAA,CAAWvM,UAAA,EAAYR,QAAQ;QAE1C8O,YAAA,CAAa/H,IAAA,CAAK;UAChBiI,IAAA;UACA5J,KAAA;UACApI,IAAA;UACA6R;QACV,CAAS;QAEDE,QAAA,IAAYC,IAAA,CAAK/I,MAAA,GAAS;MAC3B;MAGD,IAAIuG,QAAA,GAAWoC,SAAA,CAAUpC,QAAA;MACzB,IAAIpF,WAAA,GAAc,IAAIjH,KAAA,CAAMwL,IAAA,CAAKa,QAAQ;MAEzC,SAAStN,CAAA,GAAI,GAAGA,CAAA,GAAIyM,IAAA,CAAKa,QAAA,EAAU,EAAEtN,CAAA,EAAG;QACtC,IAAI+N,EAAA,GAAM7F,WAAA,CAAYlI,CAAC,IAAI;QAC3B,IAAIkQ,OAAA,GAAU5C,QAAA,CAAStN,CAAC;QAExB+N,EAAA,CAAG+B,IAAA,GAAOI,OAAA,CAAQJ,IAAA;QAClB/B,EAAA,CAAG4B,WAAA,GAAcrQ,OAAA;QACjByO,EAAA,CAAGnD,OAAA,GAAU;QACbmD,EAAA,CAAGjQ,IAAA,GAAOoS,OAAA,CAAQC,SAAA;QAClBpC,EAAA,CAAGqC,OAAA,GAAUF,OAAA,CAAQE,OAAA;QACrBrC,EAAA,CAAGzF,KAAA,GAAQmE,IAAA,CAAKnE,KAAA;QAChByF,EAAA,CAAGvF,MAAA,GAASiE,IAAA,CAAKgB,KAAA;MAClB;MAED,IAAIzF,MAAA,GAAS;QACXO,GAAA,EAAK,IAAItH,KAAA,CAAM,CAAC;MACjB;MAED,SAASkJ,OAAA,GAAS,GAAGA,OAAA,GAASsC,IAAA,CAAKa,QAAA,EAAU,EAAEnD,OAAA,EAAQ;QACrD,IAAI4D,EAAA,GAAK7F,WAAA,CAAYiC,OAAM;QAE3B,SAASnK,CAAA,GAAI,GAAGA,CAAA,GAAI4P,YAAA,CAAa7I,MAAA,EAAQ,EAAE/G,CAAA,EAAG;UAC5C,IAAIqQ,IAAA,GAAOT,YAAA,CAAa5P,CAAC;UAEzB,IAAI+N,EAAA,CAAG+B,IAAA,IAAQO,IAAA,CAAKP,IAAA,EAAM;YACxB/B,EAAA,CAAG4B,WAAA,GAAcU,IAAA,CAAKV,WAAA;YAEtB,IAAIU,IAAA,CAAKnK,KAAA,IAAS,GAAG;cACnB8B,MAAA,CAAOO,GAAA,CAAI8H,IAAA,CAAKnK,KAAK,IAAIiE,OAAA;YAC1B;YAED4D,EAAA,CAAGpB,MAAA,GAASxC,OAAA;UACb;QACF;MACF;MAGD,IAAI2E,SAAA,CAAUI,gBAAA,GAAmB,GAAG;QAClC,QAAQJ,SAAA,CAAUW,aAAA;UAChB,KAAKrQ,cAAA;YACH,IAAI+I,QAAA,GAAW,IAAIqB,WAAA,CAAYsF,SAAA,CAAUS,wBAAwB;YACjEpJ,aAAA,CACEsG,IAAA,CAAKC,KAAA,EACLpL,UAAA,EACAR,QAAA,EACAgO,SAAA,CAAUI,gBAAA,EACV/G,QAAA,EACA2G,SAAA,CAAUS,wBACX;YACD;UAEF,KAAKlQ,OAAA;YACH,IAAIwN,UAAA,GAAaJ,IAAA,CAAKC,KAAA,CAAMK,KAAA,CAAMjM,QAAA,CAASY,KAAA,EAAOZ,QAAA,CAASY,KAAA,GAAQoN,SAAA,CAAUS,wBAAwB;YACrG,IAAI7I,IAAA,GAAOyG,UAAA,CAAWN,UAAU;YAChC,IAAI1E,QAAA,GAAW,IAAIqB,WAAA,CAAY9C,IAAA,CAAKzI,MAAM;YAC1C6C,QAAA,CAASY,KAAA,IAASoN,SAAA,CAAUS,wBAAA;YAC5B;QACH;MACF;MAGD,IAAIT,SAAA,CAAUK,gBAAA,GAAmB,GAAG;QAClC,IAAImB,QAAA,GAAW;UACb5D,KAAA,EAAOD,IAAA,CAAKC,KAAA;UACZC,MAAA,EAAQ7L,QAAA;UACRyG,IAAA,EAAMuH,SAAA,CAAUK;QACjB;QACD,IAAI/G,QAAA,GAAW,IAAIoB,WAAA,CAAY0D,aAAA,CAAcoD,QAAQ,EAAErS,MAAM;QAC7D6C,QAAA,CAASY,KAAA,IAASoN,SAAA,CAAUK,gBAAA;MAC7B;MAGD,IAAIL,SAAA,CAAUQ,UAAA,GAAa,GAAG;QAC5B,IAAIzC,UAAA,GAAaJ,IAAA,CAAKC,KAAA,CAAMK,KAAA,CAAMjM,QAAA,CAASY,KAAA,EAAOZ,QAAA,CAASY,KAAA,GAAQoN,SAAA,CAAUM,iBAAiB;QAC9F,IAAI1I,IAAA,GAAOyG,UAAA,CAAWN,UAAU;QAChC,IAAI0D,SAAA,GAAYjJ,eAAA,CAAgBZ,IAAA,CAAKzI,MAAM;QAE3C6C,QAAA,CAASY,KAAA,IAASoN,SAAA,CAAUM,iBAAA;MAC7B;MAGD,IAAI7B,YAAA,GAAe;MACnB,IAAIlE,UAAA,GAAa,IAAIpI,KAAA,CAAMiH,WAAA,CAAYnB,MAAM;MAC7C,SAAS/G,CAAA,GAAI,GAAGA,CAAA,GAAIqJ,UAAA,CAAWtC,MAAA,EAAQ,EAAE/G,CAAA,EAAG;QAC1CqJ,UAAA,CAAWrJ,CAAC,IAAI,IAAIiB,KAAA,CAAO;MAC5B;MAED,SAAS4J,CAAA,GAAI,GAAGA,CAAA,GAAI4B,IAAA,CAAKgB,KAAA,EAAO,EAAE5C,CAAA,EAAG;QACnC,SAAS2F,IAAA,GAAO,GAAGA,IAAA,GAAOtI,WAAA,CAAYnB,MAAA,EAAQ,EAAEyJ,IAAA,EAAM;UACpDnH,UAAA,CAAWmH,IAAI,EAAE3I,IAAA,CAAK0F,YAAY;UAClCA,YAAA,IAAgBrF,WAAA,CAAYsI,IAAI,EAAElI,KAAA,GAAQmE,IAAA,CAAK3O,IAAA,GAAOoB,UAAA;QACvD;MACF;MAGD6I,cAAA,CAAeC,MAAA,EAAQqB,UAAA,EAAYnB,WAAA,EAAaC,QAAA,EAAUC,QAAA,EAAU3F,SAAS;MAG7E,SAASzC,CAAA,GAAI,GAAGA,CAAA,GAAIkI,WAAA,CAAYnB,MAAA,EAAQ,EAAE/G,CAAA,EAAG;QAC3C,IAAI+N,EAAA,GAAK7F,WAAA,CAAYlI,CAAC;QAEtB,IAAI+N,EAAA,CAAGnD,OAAA,EAAS;QAEhB,QAAQmD,EAAA,CAAG4B,WAAA;UACT,KAAKnQ,GAAA;YACH,IAAIqM,GAAA,GAAM;YACV,IAAI4E,SAAA,GAAY;YAEhB,SAAS5F,CAAA,GAAI,GAAGA,CAAA,GAAI4B,IAAA,CAAKgB,KAAA,EAAO,EAAE5C,CAAA,EAAG;cACnC,IAAI6F,cAAA,GAAiBrH,UAAA,CAAWrJ,CAAC,EAAE6L,GAAG;cAEtC,SAASf,CAAA,GAAI,GAAGA,CAAA,GAAIiD,EAAA,CAAGzF,KAAA,EAAO,EAAEwC,CAAA,EAAG;gBACjC,SAAS6F,IAAA,GAAO,GAAGA,IAAA,GAAOzR,UAAA,GAAa6O,EAAA,CAAGjQ,IAAA,EAAM,EAAE6S,IAAA,EAAM;kBACtDlO,SAAA,CAAUiO,cAAA,EAAgB,IAAIH,SAAA,CAAUE,SAAA,GAAYE,IAAA,GAAO5C,EAAA,CAAGzF,KAAA,GAAQyF,EAAA,CAAGvF,MAAM;gBAChF;gBAEDiI,SAAA;cACD;cAED5E,GAAA;YACD;YAED;UAEF,KAAKtM,SAAA;UAEL;YACE,MAAM;QACT;MACF;MAED,OAAO,IAAImI,QAAA,CAASjF,SAAA,CAAUxE,MAAM;IACrC;IAED,SAAS8R,0BAA0B5L,OAAA,EAAQgG,OAAA,EAAQ;MACjD,IAAIyG,UAAA,GAAa,IAAI/N,UAAA,CAAWsB,OAAM;MACtC,IAAI0M,SAAA,GAAY;MAEhB,OAAOD,UAAA,CAAWzG,OAAA,CAAOzI,KAAA,GAAQmP,SAAS,KAAK,GAAG;QAChDA,SAAA,IAAa;MACd;MAED,IAAIC,WAAA,GAAc,IAAIC,WAAA,CAAa,EAACC,MAAA,CAAOJ,UAAA,CAAW7D,KAAA,CAAM5C,OAAA,CAAOzI,KAAA,EAAOyI,OAAA,CAAOzI,KAAA,GAAQmP,SAAS,CAAC;MAEnG1G,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQmP,SAAA,GAAY;MAE1C,OAAOC,WAAA;IACR;IAED,SAASG,uBAAuB9M,OAAA,EAAQgG,OAAA,EAAQ5C,IAAA,EAAM;MACpD,IAAIuJ,WAAA,GAAc,IAAIC,WAAA,CAAa,EAACC,MAAA,CAAO,IAAInO,UAAA,CAAWsB,OAAM,EAAE4I,KAAA,CAAM5C,OAAA,CAAOzI,KAAA,EAAOyI,OAAA,CAAOzI,KAAA,GAAQ6F,IAAI,CAAC;MAE1G4C,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQ6F,IAAA;MAE9B,OAAOuJ,WAAA;IACR;IAED,SAASI,cAAc7I,QAAA,EAAU8B,OAAA,EAAQ;MACvC,IAAIW,CAAA,GAAIqG,UAAA,CAAW9I,QAAA,EAAU8B,OAAM;MACnC,IAAIU,CAAA,GAAItE,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MAEpC,OAAO,CAACW,CAAA,EAAGD,CAAC;IACb;IAED,SAASuG,cAAc/I,QAAA,EAAU8B,OAAA,EAAQ;MACvC,IAAIW,CAAA,GAAIvE,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MACpC,IAAIU,CAAA,GAAItE,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MAEpC,OAAO,CAACW,CAAA,EAAGD,CAAC;IACb;IAED,SAASsG,WAAW9I,QAAA,EAAU8B,OAAA,EAAQ;MACpC,IAAIkH,KAAA,GAAQhJ,QAAA,CAASiJ,QAAA,CAASnH,OAAA,CAAOzI,KAAA,EAAO,IAAI;MAEhDyI,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQzC,UAAA;MAE9B,OAAOoS,KAAA;IACR;IAED,SAAS9K,YAAY8B,QAAA,EAAU8B,OAAA,EAAQ;MACrC,IAAIoH,MAAA,GAASlJ,QAAA,CAASmJ,SAAA,CAAUrH,OAAA,CAAOzI,KAAA,EAAO,IAAI;MAElDyI,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQzC,UAAA;MAE9B,OAAOsS,MAAA;IACR;IAED,SAASxQ,gBAAgBF,WAAA,EAAYsJ,OAAA,EAAQ;MAC3C,IAAIsH,KAAA,GAAQ5Q,WAAA,CAAWsJ,OAAA,CAAOzI,KAAK;MAEnCyI,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQvC,SAAA;MAE9B,OAAOsS,KAAA;IACR;IAED,SAAS5D,WAAWxF,QAAA,EAAU8B,OAAA,EAAQ;MACpC,IAAIsH,KAAA,GAAQpJ,QAAA,CAASP,QAAA,CAASqC,OAAA,CAAOzI,KAAK;MAE1CyI,OAAA,CAAOzI,KAAA,GAAQyI,OAAA,CAAOzI,KAAA,GAAQvC,SAAA;MAE9B,OAAOsS,KAAA;IACR;IAED,MAAM1C,UAAA,GAAa,SAAAA,CAAU1G,QAAA,EAAU8B,OAAA,EAAQ;MAC7C,IAAIuH,GAAA;MAEJ,IAAI,iBAAiBhK,QAAA,CAASiK,SAAA,EAAW;QACvCD,GAAA,GAAME,MAAA,CAAOvJ,QAAA,CAASwJ,WAAA,CAAY1H,OAAA,CAAOzI,KAAA,EAAO,IAAI,CAAC;MAC7D,OAAa;QACLgQ,GAAA,GAAMrJ,QAAA,CAASmJ,SAAA,CAAUrH,OAAA,CAAOzI,KAAA,GAAQ,GAAG,IAAI,IAAIkQ,MAAA,CAAOvJ,QAAA,CAASmJ,SAAA,CAAUrH,OAAA,CAAOzI,KAAA,EAAO,IAAI,KAAK,EAAE;MACvG;MAEDyI,OAAA,CAAOzI,KAAA,IAAS3C,UAAA;MAEhB,OAAO2S,GAAA;IACR;IAED,SAASI,aAAazJ,QAAA,EAAU8B,OAAA,EAAQ;MACtC,IAAIkC,KAAA,GAAQhE,QAAA,CAAS0J,UAAA,CAAW5H,OAAA,CAAOzI,KAAA,EAAO,IAAI;MAElDyI,OAAA,CAAOzI,KAAA,IAAS1C,YAAA;MAEhB,OAAOqN,KAAA;IACR;IAED,SAAS2F,cAAc3J,QAAA,EAAU8B,OAAA,EAAQ;MACvC,OAAO+B,SAAA,CAAUC,WAAA,CAAY2F,YAAA,CAAazJ,QAAA,EAAU8B,OAAM,CAAC;IAC5D;IAGD,SAASc,cAAcgH,MAAA,EAAQ;MAC7B,IAAIC,QAAA,IAAYD,MAAA,GAAS,UAAW;QAClCE,QAAA,GAAWF,MAAA,GAAS;MAEtB,QACGA,MAAA,IAAU,KAAK,KAAK,MACpBC,QAAA,GACGA,QAAA,KAAa,KACXC,QAAA,GACEC,GAAA,GACAC,QAAA,GACF3S,IAAA,CAAKC,GAAA,CAAI,GAAGuS,QAAA,GAAW,EAAE,KAAK,IAAIC,QAAA,GAAW,QAC/C,kBAAkBA,QAAA,GAAW;IAEpC;IAED,SAASxE,YAAYtF,QAAA,EAAU8B,OAAA,EAAQ;MACrC,IAAImI,MAAA,GAASjK,QAAA,CAAS0C,SAAA,CAAUZ,OAAA,CAAOzI,KAAA,EAAO,IAAI;MAElDyI,OAAA,CAAOzI,KAAA,IAASxC,UAAA;MAEhB,OAAOoT,MAAA;IACR;IAED,SAASC,aAAapO,OAAA,EAAQgG,OAAA,EAAQ;MACpC,OAAOc,aAAA,CAAc0C,WAAA,CAAYxJ,OAAA,EAAQgG,OAAM,CAAC;IACjD;IAED,SAASqI,YAAYnK,QAAA,EAAUlE,OAAA,EAAQgG,OAAA,EAAQ5C,IAAA,EAAM;MACnD,IAAIkL,WAAA,GAActI,OAAA,CAAOzI,KAAA;MACzB,IAAI4L,QAAA,GAAW,EAAE;MAEjB,OAAOnD,OAAA,CAAOzI,KAAA,GAAQ+Q,WAAA,GAAclL,IAAA,GAAO,GAAG;QAC5C,IAAIuI,IAAA,GAAOC,yBAAA,CAA0B5L,OAAA,EAAQgG,OAAM;QACnD,IAAIgG,SAAA,GAAYgB,UAAA,CAAW9I,QAAA,EAAU8B,OAAM;QAC3C,IAAIiG,OAAA,GAAUvC,UAAA,CAAWxF,QAAA,EAAU8B,OAAM;QACzCA,OAAA,CAAOzI,KAAA,IAAS;QAChB,IAAIgR,SAAA,GAAYvB,UAAA,CAAW9I,QAAA,EAAU8B,OAAM;QAC3C,IAAIwI,SAAA,GAAYxB,UAAA,CAAW9I,QAAA,EAAU8B,OAAM;QAE3CmD,QAAA,CAASzF,IAAA,CAAK;UACZiI,IAAA;UACAK,SAAA;UACAC,OAAA;UACAsC,SAAA;UACAC;QACV,CAAS;MACF;MAEDxI,OAAA,CAAOzI,KAAA,IAAS;MAEhB,OAAO4L,QAAA;IACR;IAED,SAASsF,oBAAoBvK,QAAA,EAAU8B,OAAA,EAAQ;MAC7C,IAAI0I,IAAA,GAAOf,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACxC,IAAI2I,IAAA,GAAOhB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACxC,IAAI4I,MAAA,GAASjB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAC1C,IAAI6I,MAAA,GAASlB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAC1C,IAAI8I,KAAA,GAAQnB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACzC,IAAI+I,KAAA,GAAQpB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACzC,IAAIgJ,MAAA,GAASrB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAC1C,IAAIiJ,MAAA,GAAStB,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAE1C,OAAO;QACL0I,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,KAAA;QACAC,KAAA;QACAC,MAAA;QACAC;MACD;IACF;IAED,SAASC,iBAAiBhL,QAAA,EAAU8B,OAAA,EAAQ;MAC1C,IAAImJ,gBAAA,GAAmB,CACrB,kBACA,mBACA,oBACA,mBACA,mBACA,qBACA,mBACA,oBACA,oBACA,mBACD;MAED,IAAI3D,WAAA,GAAc9B,UAAA,CAAWxF,QAAA,EAAU8B,OAAM;MAE7C,OAAOmJ,gBAAA,CAAiB3D,WAAW;IACpC;IAED,SAAS4D,WAAWlL,QAAA,EAAU8B,OAAA,EAAQ;MACpC,IAAIqJ,IAAA,GAAOjN,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MACvC,IAAIsJ,IAAA,GAAOlN,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MACvC,IAAIuJ,IAAA,GAAOnN,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MACvC,IAAIwJ,IAAA,GAAOpN,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;MAEvC,OAAO;QAAEqJ,IAAA;QAAYC,IAAA;QAAYC,IAAA;QAAYC;MAAY;IAC1D;IAED,SAASC,eAAevL,QAAA,EAAU8B,OAAA,EAAQ;MACxC,IAAI0J,UAAA,GAAa,CAAC,cAAc;MAEhC,IAAIC,SAAA,GAAYjG,UAAA,CAAWxF,QAAA,EAAU8B,OAAM;MAE3C,OAAO0J,UAAA,CAAWC,SAAS;IAC5B;IAED,SAASC,SAAS1L,QAAA,EAAU8B,OAAA,EAAQ;MAClC,IAAIW,CAAA,GAAIgH,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACrC,IAAIU,CAAA,GAAIiH,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAErC,OAAO,CAACW,CAAA,EAAGD,CAAC;IACb;IAED,SAASmJ,SAAS3L,QAAA,EAAU8B,OAAA,EAAQ;MAClC,IAAIW,CAAA,GAAIgH,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACrC,IAAIU,CAAA,GAAIiH,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MACrC,IAAI8J,CAAA,GAAInC,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAErC,OAAO,CAACW,CAAA,EAAGD,CAAA,EAAGoJ,CAAC;IAChB;IAED,SAASC,WAAW7L,QAAA,EAAUlE,OAAA,EAAQgG,OAAA,EAAQrM,IAAA,EAAMyJ,IAAA,EAAM;MACxD,IAAIzJ,IAAA,KAAS,YAAYA,IAAA,KAAS,kBAAkBA,IAAA,KAAS,cAAc;QACzE,OAAOmT,sBAAA,CAAuB9M,OAAA,EAAQgG,OAAA,EAAQ5C,IAAI;MAC1D,WAAiBzJ,IAAA,KAAS,UAAU;QAC5B,OAAO0U,WAAA,CAAYnK,QAAA,EAAUlE,OAAA,EAAQgG,OAAA,EAAQ5C,IAAI;MACzD,WAAiBzJ,IAAA,KAAS,kBAAkB;QACpC,OAAO8U,mBAAA,CAAoBvK,QAAA,EAAU8B,OAAM;MACnD,WAAiBrM,IAAA,KAAS,eAAe;QACjC,OAAOuV,gBAAA,CAAiBhL,QAAA,EAAU8B,OAAM;MAChD,WAAiBrM,IAAA,KAAS,SAAS;QAC3B,OAAOyV,UAAA,CAAWlL,QAAA,EAAU8B,OAAM;MAC1C,WAAiBrM,IAAA,KAAS,aAAa;QAC/B,OAAO8V,cAAA,CAAevL,QAAA,EAAU8B,OAAM;MAC9C,WAAiBrM,IAAA,KAAS,SAAS;QAC3B,OAAOgU,YAAA,CAAazJ,QAAA,EAAU8B,OAAM;MAC5C,WAAiBrM,IAAA,KAAS,OAAO;QACzB,OAAOiW,QAAA,CAAS1L,QAAA,EAAU8B,OAAM;MACxC,WAAiBrM,IAAA,KAAS,OAAO;QACzB,OAAOkW,QAAA,CAAS3L,QAAA,EAAU8B,OAAM;MACxC,WAAiBrM,IAAA,KAAS,OAAO;QACzB,OAAOqT,UAAA,CAAW9I,QAAA,EAAU8B,OAAM;MAC1C,WAAiBrM,IAAA,KAAS,YAAY;QAC9B,OAAOoT,aAAA,CAAc7I,QAAA,EAAU8B,OAAM;MAC7C,WAAiBrM,IAAA,KAAS,YAAY;QAC9B,OAAOsT,aAAA,CAAc/I,QAAA,EAAU8B,OAAM;MAC7C,WAAiBrM,IAAA,KAAS,WAAW;QAC7BqM,OAAA,CAAOzI,KAAA,IAAS6F,IAAA;QAChB,OAAO;MACf,OAAa;QACL4C,OAAA,CAAOzI,KAAA,IAAS6F,IAAA;QAChB,OAAO;MACR;IACF;IAED,SAAS4M,YAAY9L,QAAA,EAAUlE,OAAA,EAAQgG,OAAA,EAAQ;MAC7C,MAAMiK,UAAA,GAAY,CAAE;MAEpB,IAAI/L,QAAA,CAASmJ,SAAA,CAAU,GAAG,IAAI,KAAK,UAAU;QAE3C,MAAM;MACP;MAED4C,UAAA,CAAU3W,OAAA,GAAU4K,QAAA,CAASP,QAAA,CAAS,CAAC;MAEvC,MAAMuM,IAAA,GAAOhM,QAAA,CAASP,QAAA,CAAS,CAAC;MAEhCsM,UAAA,CAAUC,IAAA,GAAO;QACfC,UAAA,EAAY,CAAC,EAAED,IAAA,GAAO;QACtBE,QAAA,EAAU,CAAC,EAAEF,IAAA,GAAO;QACpBG,UAAA,EAAY,CAAC,EAAEH,IAAA,GAAO;QACtBI,SAAA,EAAW,CAAC,EAAEJ,IAAA,GAAO;MACtB;MAIDlK,OAAA,CAAOzI,KAAA,GAAQ;MAEf,IAAIgT,WAAA,GAAc;MAElB,OAAOA,WAAA,EAAa;QAClB,IAAIC,aAAA,GAAgB5E,yBAAA,CAA0B5L,OAAA,EAAQgG,OAAM;QAE5D,IAAIwK,aAAA,IAAiB,GAAG;UACtBD,WAAA,GAAc;QACxB,OAAe;UACL,IAAIE,aAAA,GAAgB7E,yBAAA,CAA0B5L,OAAA,EAAQgG,OAAM;UAC5D,IAAI0K,aAAA,GAAgBtO,WAAA,CAAY8B,QAAA,EAAU8B,OAAM;UAChD,IAAI2K,cAAA,GAAiBZ,UAAA,CAAW7L,QAAA,EAAUlE,OAAA,EAAQgG,OAAA,EAAQyK,aAAA,EAAeC,aAAa;UAEtF,IAAIC,cAAA,KAAmB,QAAW;YAChCC,OAAA,CAAQC,IAAA,CAAK,2DAA4DJ,aAAA,IAAkB;UACvG,OAAiB;YACLR,UAAA,CAAUO,aAAa,IAAIG,cAAA;UAC5B;QACF;MACF;MAED,KAAKT,IAAA,GAAO,CAAC,MAAS,GAAG;QAEvBU,OAAA,CAAQE,KAAA,CAAM,cAAcb,UAAS;QACrC,MAAM;MACP;MAED,OAAOA,UAAA;IACR;IAED,SAASc,aAAad,UAAA,EAAW/L,QAAA,EAAUxH,WAAA,EAAYsJ,OAAA,EAAQgL,UAAA,EAAY;MACzE,MAAMC,WAAA,GAAa;QACjB7N,IAAA,EAAM;QACNuF,MAAA,EAAQzE,QAAA;QACRqE,KAAA,EAAO7L,WAAA;QACP8L,MAAA,EAAQxC,OAAA;QACR7B,KAAA,EAAO8L,UAAA,CAAUiB,UAAA,CAAW3B,IAAA,GAAOU,UAAA,CAAUiB,UAAA,CAAW7B,IAAA,GAAO;QAC/DhL,MAAA,EAAQ4L,UAAA,CAAUiB,UAAA,CAAW1B,IAAA,GAAOS,UAAA,CAAUiB,UAAA,CAAW5B,IAAA,GAAO;QAChEnG,QAAA,EAAU8G,UAAA,CAAU9G,QAAA,CAASvG,MAAA;QAC7BuO,YAAA,EAAc;QACd7H,KAAA,EAAO;QACP8H,SAAA,EAAW;QACXzX,IAAA,EAAMsW,UAAA,CAAU9G,QAAA,CAAS,CAAC,EAAE6C,SAAA;QAC5BqF,UAAA,EAAY;QACZC,MAAA,EAAQ;QACRC,MAAA,EAAQ;QACR,CAAClY,aAAA,GAAgB,eAAe,UAAU,GAAG;MAC9C;MAED,QAAQ4W,UAAA,CAAUzE,WAAA;QAChB,KAAK;UACHyF,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAahJ,aAAA;UACxB;QAEF,KAAK;UACH4I,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAa5I,aAAA;UACxB;QAEF,KAAK;UACHwI,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAatI,aAAA;UACxB;QAEF,KAAK;UACHkI,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAatI,aAAA;UACxB;QAEF,KAAK;UACHkI,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAapI,aAAA;UACxB;QAEF,KAAK;UACHgI,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAanH,aAAA;UACxB;QAEF,KAAK;UACH+G,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAa3G,aAAA;UACxB;QAEF,KAAK;UACHuG,WAAA,CAAW3H,KAAA,GAAQ;UACnB2H,WAAA,CAAWI,UAAA,GAAa3G,aAAA;UACxB;QAEF;UACE,MAAM,sBAAsBuF,UAAA,CAAUzE,WAAA,GAAc;MACvD;MAEDyF,WAAA,CAAW/H,iBAAA,GAAoB+H,WAAA,CAAW3H,KAAA;MAE1C,IAAI2H,WAAA,CAAWtX,IAAA,IAAQ,GAAG;QAExB,QAAQqX,UAAA;UACN,KAAKQ,SAAA;YACHP,WAAA,CAAWK,MAAA,GAASlD,YAAA;YACpB6C,WAAA,CAAWG,SAAA,GAAYrW,UAAA;YACvB;UAEF,KAAKnB,aAAA;YACHqX,WAAA,CAAWK,MAAA,GAAS9H,WAAA;YACpByH,WAAA,CAAWG,SAAA,GAAYrW,UAAA;YACvB;QACH;MACT,WAAiBkW,WAAA,CAAWtX,IAAA,IAAQ,GAAG;QAE/B,QAAQqX,UAAA;UACN,KAAKQ,SAAA;YACHP,WAAA,CAAWK,MAAA,GAAS3D,YAAA;YACpBsD,WAAA,CAAWG,SAAA,GAAYvW,YAAA;YACvB;UAEF,KAAKjB,aAAA;YACHqX,WAAA,CAAWK,MAAA,GAASzD,aAAA;YACpBoD,WAAA,CAAWG,SAAA,GAAYvW,YAAA;QAC1B;MACT,OAAa;QACL,MAAM,4CAA4CoW,WAAA,CAAWtX,IAAA,GAAO,UAAUsW,UAAA,CAAUzE,WAAA,GAAc;MACvG;MAEDyF,WAAA,CAAWQ,UAAA,IAAcxB,UAAA,CAAUiB,UAAA,CAAW1B,IAAA,GAAO,KAAKyB,WAAA,CAAW/H,iBAAA;MAErE,SAASrN,CAAA,GAAI,GAAGA,CAAA,GAAIoV,WAAA,CAAWQ,UAAA,EAAY5V,CAAA,IAAK+O,UAAA,CAAW1G,QAAA,EAAU8B,OAAM;MAK3EiL,WAAA,CAAWS,cAAA,GAAiBT,WAAA,CAAW9H,QAAA,IAAY,IAAI,IAAI8H,WAAA,CAAW9H,QAAA;MACtE,MAAM/F,IAAA,GAAO6N,WAAA,CAAW9M,KAAA,GAAQ8M,WAAA,CAAW5M,MAAA,GAAS4M,WAAA,CAAWS,cAAA;MAE/D,QAAQV,UAAA;QACN,KAAKQ,SAAA;UACHP,WAAA,CAAWU,SAAA,GAAY,IAAIvM,YAAA,CAAahC,IAAI;UAG5C,IAAI6N,WAAA,CAAW9H,QAAA,GAAW8H,WAAA,CAAWS,cAAA,EAAgBT,WAAA,CAAWU,SAAA,CAAUjM,IAAA,CAAK,GAAG,GAAGtC,IAAI;UAEzF;QAEF,KAAKxJ,aAAA;UACHqX,WAAA,CAAWU,SAAA,GAAY,IAAItM,WAAA,CAAYjC,IAAI;UAE3C,IAAI6N,WAAA,CAAW9H,QAAA,GAAW8H,WAAA,CAAWS,cAAA,EAAgBT,WAAA,CAAWU,SAAA,CAAUjM,IAAA,CAAK,OAAQ,GAAGtC,IAAI;UAE9F;QAEF;UACEwN,OAAA,CAAQE,KAAA,CAAM,uCAAuCE,UAAU;UAC/D;MACH;MAEDC,WAAA,CAAWE,YAAA,GAAeF,WAAA,CAAW9M,KAAA,GAAQ8M,WAAA,CAAWG,SAAA,GAAYH,WAAA,CAAW9H,QAAA;MAE/E,IAAI8H,WAAA,CAAWS,cAAA,IAAkB,GAAGT,WAAA,CAAWM,MAAA,GAASK,UAAA,MACnDX,WAAA,CAAWM,MAAA,GAASM,SAAA;MAEzB,IAAIxY,aAAA,EAAe4X,WAAA,CAAWa,UAAA,GAAa,mBACtCb,WAAA,CAAWc,QAAA,GAAW;MAE3B,OAAOd,WAAA;IACR;IAID,MAAMe,cAAA,GAAiB,IAAIzO,QAAA,CAASzJ,MAAM;IAC1C,MAAMmY,UAAA,GAAa,IAAIvT,UAAA,CAAW5E,MAAM;IACxC,MAAM0O,MAAA,GAAS;MAAEjL,KAAA,EAAO;IAAG;IAG3B,MAAMgO,SAAA,GAAYyE,WAAA,CAAYgC,cAAA,EAAgBlY,MAAA,EAAQ0O,MAAM;IAG5D,MAAM0J,UAAA,GAAanB,YAAA,CAAaxF,SAAA,EAAWyG,cAAA,EAAgBC,UAAA,EAAYzJ,MAAA,EAAQ,KAAK7O,IAAI;IAExF,MAAMwY,SAAA,GAAY;MAAE5U,KAAA,EAAO;IAAG;IAC9B,MAAM6U,cAAA,GAAiB;MAAEC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGC,CAAA,EAAG;MAAGC,CAAA,EAAG;IAAG;IAEvD,SACMC,gBAAA,GAAmB,GACvBA,gBAAA,GAAmBR,UAAA,CAAW7N,MAAA,GAAS6N,UAAA,CAAWhJ,iBAAA,EAClDwJ,gBAAA,IACA;MACA,MAAMC,IAAA,GAAOvQ,WAAA,CAAY4P,cAAA,EAAgBxJ,MAAM;MAC/C0J,UAAA,CAAW9O,IAAA,GAAOhB,WAAA,CAAY4P,cAAA,EAAgBxJ,MAAM;MACpD0J,UAAA,CAAW5I,KAAA,GACTqJ,IAAA,GAAOT,UAAA,CAAWhJ,iBAAA,GAAoBgJ,UAAA,CAAW7N,MAAA,GAC7C6N,UAAA,CAAW7N,MAAA,GAASsO,IAAA,GACpBT,UAAA,CAAWhJ,iBAAA;MAEjB,MAAM0J,YAAA,GAAeV,UAAA,CAAW9O,IAAA,GAAO8O,UAAA,CAAW5I,KAAA,GAAQ4I,UAAA,CAAWf,YAAA;MACrE,MAAMxI,MAAA,GAASiK,YAAA,GAAeV,UAAA,CAAWb,UAAA,CAAWa,UAAU,IAAI7J,aAAA,CAAc6J,UAAU;MAE1F1J,MAAA,CAAOjL,KAAA,IAAS2U,UAAA,CAAW9O,IAAA;MAE3B,SAASyP,MAAA,GAAS,GAAGA,MAAA,GAASX,UAAA,CAAWhJ,iBAAA,EAAmB2J,MAAA,IAAU;QACpE,MAAMC,MAAA,GAASD,MAAA,GAASH,gBAAA,GAAmBR,UAAA,CAAWhJ,iBAAA;QACtD,IAAI4J,MAAA,IAAUZ,UAAA,CAAW7N,MAAA,EAAQ;QAEjC,SAAS0O,SAAA,GAAY,GAAGA,SAAA,GAAYb,UAAA,CAAW/I,QAAA,EAAU4J,SAAA,IAAa;UACpE,MAAMC,IAAA,GAAOZ,cAAA,CAAe7G,SAAA,CAAUpC,QAAA,CAAS4J,SAAS,EAAEpH,IAAI;UAE9D,SAAShF,CAAA,GAAI,GAAGA,CAAA,GAAIuL,UAAA,CAAW/N,KAAA,EAAOwC,CAAA,IAAK;YACzCwL,SAAA,CAAU5U,KAAA,IACPsV,MAAA,IAAUX,UAAA,CAAW/I,QAAA,GAAW+I,UAAA,CAAW/N,KAAA,IAAS4O,SAAA,GAAYb,UAAA,CAAW/N,KAAA,GAAQwC,CAAA,IACpFuL,UAAA,CAAWd,SAAA;YACb,MAAM6B,QAAA,IACHf,UAAA,CAAW7N,MAAA,GAAS,IAAIyO,MAAA,KAAWZ,UAAA,CAAW/N,KAAA,GAAQ+N,UAAA,CAAWR,cAAA,IAClE/K,CAAA,GAAIuL,UAAA,CAAWR,cAAA,GACfsB,IAAA;YACFd,UAAA,CAAWP,SAAA,CAAUsB,QAAQ,IAAIf,UAAA,CAAWZ,MAAA,CAAO3I,MAAA,EAAQwJ,SAAS;UACrE;QACF;MACF;IACF;IAED,OAAO;MACLe,MAAA,EAAQ3H,SAAA;MACRpH,KAAA,EAAO+N,UAAA,CAAW/N,KAAA;MAClBE,MAAA,EAAQ6N,UAAA,CAAW7N,MAAA;MACnB9B,IAAA,EAAM2P,UAAA,CAAWP,SAAA;MACjBJ,MAAA,EAAQW,UAAA,CAAWX,MAAA;MACnB,CAAClY,aAAA,GAAgB,eAAe,UAAU,GAAG6Y,UAAA,CAAW7Y,aAAA,GAAgB,eAAe,UAAU;MACjGM,IAAA,EAAM,KAAKA;IACZ;EACF;EAEDwZ,YAAY5V,KAAA,EAAO;IACjB,KAAK5D,IAAA,GAAO4D,KAAA;IACZ,OAAO;EACR;EAED6V,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,SAASC,eAAeC,OAAA,EAASC,OAAA,EAAS;MACxC,IAAIta,aAAA,EAAeqa,OAAA,CAAQ5B,UAAA,GAAa6B,OAAA,CAAQ7B,UAAA,MAC3C4B,OAAA,CAAQ3B,QAAA,GAAW4B,OAAA,CAAQ5B,QAAA;MAChC2B,OAAA,CAAQE,SAAA,GAAYC,YAAA;MACpBH,OAAA,CAAQI,SAAA,GAAYD,YAAA;MACpBH,OAAA,CAAQK,eAAA,GAAkB;MAC1BL,OAAA,CAAQM,KAAA,GAAQ;MAEhB,IAAIV,MAAA,EAAQA,MAAA,CAAOI,OAAA,EAASC,OAAO;IACpC;IAED,OAAO,MAAMP,IAAA,CAAKC,GAAA,EAAKI,cAAA,EAAgBF,UAAA,EAAYC,OAAO;EAC3D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}