{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getDisplayMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia)\n */\nconst ScreenVideoTexture = /* @__PURE__ */forwardRef(({\n  options = {\n    video: true\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getDisplayMedia(options), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\nexport { ScreenVideoTexture };", "map": {"version": 3, "names": ["_extends", "React", "forwardRef", "useEffect", "suspend", "clear", "VideoTexture", "ScreenVideoTexture", "options", "video", "props", "fref", "mediaStream", "navigator", "mediaDevices", "getDisplayMedia", "getTracks", "for<PERSON>ach", "track", "stop", "createElement", "ref", "src"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/ScreenVideoTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getDisplayMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia)\n */\nconst ScreenVideoTexture = /* @__PURE__ */forwardRef(({\n  options = {\n    video: true\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getDisplayMedia(options), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\n\nexport { ScreenVideoTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7C,SAASC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,yBAAyB;;AAEtD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,eAAeL,UAAU,CAAC,CAAC;EACpDM,OAAO,GAAG;IACRC,KAAK,EAAE;EACT,CAAC;EACD,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,WAAW,GAAGR,OAAO,CAAC,MAAMS,SAAS,CAACC,YAAY,CAACC,eAAe,CAACP,OAAO,CAAC,EAAE,EAAE,CAAC;EACtFL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXS,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACI,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7Ed,KAAK,CAAC,EAAE,CAAC;IACX,CAAC;EACH,CAAC,EAAE,CAACO,WAAW,CAAC,CAAC;EACjB,OAAO,aAAaX,KAAK,CAACmB,aAAa,CAACd,YAAY,EAAEN,QAAQ,CAAC;IAC7DqB,GAAG,EAAEV;EACP,CAAC,EAAED,KAAK,EAAE;IACRY,GAAG,EAAEV;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}