{"ast": null, "code": "import { Object3<PERSON>, <PERSON>ector3, <PERSON><PERSON><PERSON><PERSON>, AnimationMixer } from \"three\";\nimport { CCDIKSolver } from \"./CCDIKSolver.js\";\nimport { MMDPhysics } from \"./MMDPhysics.js\";\nclass MMDAnimationHelper {\n  /**\n   * @param {Object} params - (optional)\n   * @param {boolean} params.sync - Whether animation durations of added objects are synched. Default is true.\n   * @param {Number} params.afterglow - Default is 0.0.\n   * @param {boolean} params.resetPhysicsOnLoop - Default is true.\n   */\n  constructor(params = {}) {\n    this.meshes = [];\n    this.camera = null;\n    this.cameraTarget = new Object3D();\n    this.cameraTarget.name = \"target\";\n    this.audio = null;\n    this.audioManager = null;\n    this.objects = /* @__PURE__ */new WeakMap();\n    this.configuration = {\n      sync: params.sync !== void 0 ? params.sync : true,\n      afterglow: params.afterglow !== void 0 ? params.afterglow : 0,\n      resetPhysicsOnLoop: params.resetPhysicsOnLoop !== void 0 ? params.resetPhysicsOnLoop : true,\n      pmxAnimation: params.pmxAnimation !== void 0 ? params.pmxAnimation : false\n    };\n    this.enabled = {\n      animation: true,\n      ik: true,\n      grant: true,\n      physics: true,\n      cameraAnimation: true\n    };\n    this.onBeforePhysics = function () {};\n    this.sharedPhysics = false;\n    this.masterPhysics = null;\n  }\n  /**\n   * Adds an Three.js Object to helper and setups animation.\n   * The anmation durations of added objects are synched\n   * if this.configuration.sync is true.\n   *\n   * @param {THREE.SkinnedMesh|THREE.Camera|THREE.Audio} object\n   * @param {Object} params - (optional)\n   * @param {THREE.AnimationClip|Array<THREE.AnimationClip>} params.animation - Only for THREE.SkinnedMesh and THREE.Camera. Default is undefined.\n   * @param {boolean} params.physics - Only for THREE.SkinnedMesh. Default is true.\n   * @param {Integer} params.warmup - Only for THREE.SkinnedMesh and physics is true. Default is 60.\n   * @param {Number} params.unitStep - Only for THREE.SkinnedMesh and physics is true. Default is 1 / 65.\n   * @param {Integer} params.maxStepNum - Only for THREE.SkinnedMesh and physics is true. Default is 3.\n   * @param {Vector3} params.gravity - Only for THREE.SkinnedMesh and physics is true. Default ( 0, - 9.8 * 10, 0 ).\n   * @param {Number} params.delayTime - Only for THREE.Audio. Default is 0.0.\n   * @return {MMDAnimationHelper}\n   */\n  add(object, params = {}) {\n    if (object.isSkinnedMesh) {\n      this._addMesh(object, params);\n    } else if (object.isCamera) {\n      this._setupCamera(object, params);\n    } else if (object.type === \"Audio\") {\n      this._setupAudio(object, params);\n    } else {\n      throw new Error(\"THREE.MMDAnimationHelper.add: accepts only THREE.SkinnedMesh or THREE.Camera or THREE.Audio instance.\");\n    }\n    if (this.configuration.sync) this._syncDuration();\n    return this;\n  }\n  /**\n   * Removes an Three.js Object from helper.\n   *\n   * @param {THREE.SkinnedMesh|THREE.Camera|THREE.Audio} object\n   * @return {MMDAnimationHelper}\n   */\n  remove(object) {\n    if (object.isSkinnedMesh) {\n      this._removeMesh(object);\n    } else if (object.isCamera) {\n      this._clearCamera(object);\n    } else if (object.type === \"Audio\") {\n      this._clearAudio(object);\n    } else {\n      throw new Error(\"THREE.MMDAnimationHelper.remove: accepts only THREE.SkinnedMesh or THREE.Camera or THREE.Audio instance.\");\n    }\n    if (this.configuration.sync) this._syncDuration();\n    return this;\n  }\n  /**\n   * Updates the animation.\n   *\n   * @param {Number} delta\n   * @return {MMDAnimationHelper}\n   */\n  update(delta) {\n    if (this.audioManager !== null) this.audioManager.control(delta);\n    for (let i = 0; i < this.meshes.length; i++) {\n      this._animateMesh(this.meshes[i], delta);\n    }\n    if (this.sharedPhysics) this._updateSharedPhysics(delta);\n    if (this.camera !== null) this._animateCamera(this.camera, delta);\n    return this;\n  }\n  /**\n   * Changes the pose of SkinnedMesh as VPD specifies.\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Object} vpd - VPD content parsed MMDParser\n   * @param {Object} params - (optional)\n   * @param {boolean} params.resetPose - Default is true.\n   * @param {boolean} params.ik - Default is true.\n   * @param {boolean} params.grant - Default is true.\n   * @return {MMDAnimationHelper}\n   */\n  pose(mesh, vpd, params = {}) {\n    if (params.resetPose !== false) mesh.pose();\n    const bones = mesh.skeleton.bones;\n    const boneParams = vpd.bones;\n    const boneNameDictionary = {};\n    for (let i = 0, il = bones.length; i < il; i++) {\n      boneNameDictionary[bones[i].name] = i;\n    }\n    const vector = new Vector3();\n    const quaternion = new Quaternion();\n    for (let i = 0, il = boneParams.length; i < il; i++) {\n      const boneParam = boneParams[i];\n      const boneIndex = boneNameDictionary[boneParam.name];\n      if (boneIndex === void 0) continue;\n      const bone = bones[boneIndex];\n      bone.position.add(vector.fromArray(boneParam.translation));\n      bone.quaternion.multiply(quaternion.fromArray(boneParam.quaternion));\n    }\n    mesh.updateMatrixWorld(true);\n    if (this.configuration.pmxAnimation && mesh.geometry.userData.MMD && mesh.geometry.userData.MMD.format === \"pmx\") {\n      const sortedBonesData = this._sortBoneDataArray(mesh.geometry.userData.MMD.bones.slice());\n      const ikSolver = params.ik !== false ? this._createCCDIKSolver(mesh) : null;\n      const grantSolver = params.grant !== false ? this.createGrantSolver(mesh) : null;\n      this._animatePMXMesh(mesh, sortedBonesData, ikSolver, grantSolver);\n    } else {\n      if (params.ik !== false) {\n        this._createCCDIKSolver(mesh).update();\n      }\n      if (params.grant !== false) {\n        this.createGrantSolver(mesh).update();\n      }\n    }\n    return this;\n  }\n  /**\n   * Enabes/Disables an animation feature.\n   *\n   * @param {string} key\n   * @param {boolean} enabled\n   * @return {MMDAnimationHelper}\n   */\n  enable(key, enabled) {\n    if (this.enabled[key] === void 0) {\n      throw new Error(\"THREE.MMDAnimationHelper.enable: unknown key \" + key);\n    }\n    this.enabled[key] = enabled;\n    if (key === \"physics\") {\n      for (let i = 0, il = this.meshes.length; i < il; i++) {\n        this._optimizeIK(this.meshes[i], enabled);\n      }\n    }\n    return this;\n  }\n  /**\n   * Creates an GrantSolver instance.\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @return {GrantSolver}\n   */\n  createGrantSolver(mesh) {\n    return new GrantSolver(mesh, mesh.geometry.userData.MMD.grants);\n  }\n  // private methods\n  _addMesh(mesh, params) {\n    if (this.meshes.indexOf(mesh) >= 0) {\n      throw new Error(\"THREE.MMDAnimationHelper._addMesh: SkinnedMesh '\" + mesh.name + \"' has already been added.\");\n    }\n    this.meshes.push(mesh);\n    this.objects.set(mesh, {\n      looped: false\n    });\n    this._setupMeshAnimation(mesh, params.animation);\n    if (params.physics !== false) {\n      this._setupMeshPhysics(mesh, params);\n    }\n    return this;\n  }\n  _setupCamera(camera, params) {\n    if (this.camera === camera) {\n      throw new Error(\"THREE.MMDAnimationHelper._setupCamera: Camera '\" + camera.name + \"' has already been set.\");\n    }\n    if (this.camera) this.clearCamera(this.camera);\n    this.camera = camera;\n    camera.add(this.cameraTarget);\n    this.objects.set(camera, {});\n    if (params.animation !== void 0) {\n      this._setupCameraAnimation(camera, params.animation);\n    }\n    return this;\n  }\n  _setupAudio(audio, params) {\n    if (this.audio === audio) {\n      throw new Error(\"THREE.MMDAnimationHelper._setupAudio: Audio '\" + audio.name + \"' has already been set.\");\n    }\n    if (this.audio) this.clearAudio(this.audio);\n    this.audio = audio;\n    this.audioManager = new AudioManager(audio, params);\n    this.objects.set(this.audioManager, {\n      duration: this.audioManager.duration\n    });\n    return this;\n  }\n  _removeMesh(mesh) {\n    let found = false;\n    let writeIndex = 0;\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      if (this.meshes[i] === mesh) {\n        this.objects.delete(mesh);\n        found = true;\n        continue;\n      }\n      this.meshes[writeIndex++] = this.meshes[i];\n    }\n    if (!found) {\n      throw new Error(\"THREE.MMDAnimationHelper._removeMesh: SkinnedMesh '\" + mesh.name + \"' has not been added yet.\");\n    }\n    this.meshes.length = writeIndex;\n    return this;\n  }\n  _clearCamera(camera) {\n    if (camera !== this.camera) {\n      throw new Error(\"THREE.MMDAnimationHelper._clearCamera: Camera '\" + camera.name + \"' has not been set yet.\");\n    }\n    this.camera.remove(this.cameraTarget);\n    this.objects.delete(this.camera);\n    this.camera = null;\n    return this;\n  }\n  _clearAudio(audio) {\n    if (audio !== this.audio) {\n      throw new Error(\"THREE.MMDAnimationHelper._clearAudio: Audio '\" + audio.name + \"' has not been set yet.\");\n    }\n    this.objects.delete(this.audioManager);\n    this.audio = null;\n    this.audioManager = null;\n    return this;\n  }\n  _setupMeshAnimation(mesh, animation) {\n    const objects = this.objects.get(mesh);\n    if (animation !== void 0) {\n      const animations = Array.isArray(animation) ? animation : [animation];\n      objects.mixer = new AnimationMixer(mesh);\n      for (let i = 0, il = animations.length; i < il; i++) {\n        objects.mixer.clipAction(animations[i]).play();\n      }\n      objects.mixer.addEventListener(\"loop\", function (event) {\n        const tracks = event.action._clip.tracks;\n        if (tracks.length > 0 && tracks[0].name.slice(0, 6) !== \".bones\") return;\n        objects.looped = true;\n      });\n    }\n    objects.ikSolver = this._createCCDIKSolver(mesh);\n    objects.grantSolver = this.createGrantSolver(mesh);\n    return this;\n  }\n  _setupCameraAnimation(camera, animation) {\n    const animations = Array.isArray(animation) ? animation : [animation];\n    const objects = this.objects.get(camera);\n    objects.mixer = new AnimationMixer(camera);\n    for (let i = 0, il = animations.length; i < il; i++) {\n      objects.mixer.clipAction(animations[i]).play();\n    }\n  }\n  _setupMeshPhysics(mesh, params) {\n    const objects = this.objects.get(mesh);\n    if (params.world === void 0 && this.sharedPhysics) {\n      const masterPhysics = this._getMasterPhysics();\n      if (masterPhysics !== null) world = masterPhysics.world;\n    }\n    objects.physics = this._createMMDPhysics(mesh, params);\n    if (objects.mixer && params.animationWarmup !== false) {\n      this._animateMesh(mesh, 0);\n      objects.physics.reset();\n    }\n    objects.physics.warmup(params.warmup !== void 0 ? params.warmup : 60);\n    this._optimizeIK(mesh, true);\n  }\n  _animateMesh(mesh, delta) {\n    const objects = this.objects.get(mesh);\n    const mixer = objects.mixer;\n    const ikSolver = objects.ikSolver;\n    const grantSolver = objects.grantSolver;\n    const physics = objects.physics;\n    const looped = objects.looped;\n    if (mixer && this.enabled.animation) {\n      this._restoreBones(mesh);\n      mixer.update(delta);\n      this._saveBones(mesh);\n      if (this.configuration.pmxAnimation && mesh.geometry.userData.MMD && mesh.geometry.userData.MMD.format === \"pmx\") {\n        if (!objects.sortedBonesData) objects.sortedBonesData = this._sortBoneDataArray(mesh.geometry.userData.MMD.bones.slice());\n        this._animatePMXMesh(mesh, objects.sortedBonesData, ikSolver && this.enabled.ik ? ikSolver : null, grantSolver && this.enabled.grant ? grantSolver : null);\n      } else {\n        if (ikSolver && this.enabled.ik) {\n          mesh.updateMatrixWorld(true);\n          ikSolver.update();\n        }\n        if (grantSolver && this.enabled.grant) {\n          grantSolver.update();\n        }\n      }\n    }\n    if (looped === true && this.enabled.physics) {\n      if (physics && this.configuration.resetPhysicsOnLoop) physics.reset();\n      objects.looped = false;\n    }\n    if (physics && this.enabled.physics && !this.sharedPhysics) {\n      this.onBeforePhysics(mesh);\n      physics.update(delta);\n    }\n  }\n  // Sort bones in order by 1. transformationClass and 2. bone index.\n  // In PMX animation system, bone transformations should be processed\n  // in this order.\n  _sortBoneDataArray(boneDataArray) {\n    return boneDataArray.sort(function (a, b) {\n      if (a.transformationClass !== b.transformationClass) {\n        return a.transformationClass - b.transformationClass;\n      } else {\n        return a.index - b.index;\n      }\n    });\n  }\n  // PMX Animation system is a bit too complex and doesn't great match to\n  // Three.js Animation system. This method attempts to simulate it as much as\n  // possible but doesn't perfectly simulate.\n  // This method is more costly than the regular one so\n  // you are recommended to set constructor parameter \"pmxAnimation: true\"\n  // only if your PMX model animation doesn't work well.\n  // If you need better method you would be required to write your own.\n  _animatePMXMesh(mesh, sortedBonesData, ikSolver, grantSolver) {\n    _quaternionIndex = 0;\n    _grantResultMap.clear();\n    for (let i = 0, il = sortedBonesData.length; i < il; i++) {\n      updateOne(mesh, sortedBonesData[i].index, ikSolver, grantSolver);\n    }\n    mesh.updateMatrixWorld(true);\n    return this;\n  }\n  _animateCamera(camera, delta) {\n    const mixer = this.objects.get(camera).mixer;\n    if (mixer && this.enabled.cameraAnimation) {\n      mixer.update(delta);\n      camera.updateProjectionMatrix();\n      camera.up.set(0, 1, 0);\n      camera.up.applyQuaternion(camera.quaternion);\n      camera.lookAt(this.cameraTarget.position);\n    }\n  }\n  _optimizeIK(mesh, physicsEnabled) {\n    const iks = mesh.geometry.userData.MMD.iks;\n    const bones = mesh.geometry.userData.MMD.bones;\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i];\n      const links = ik.links;\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        const link = links[j];\n        if (physicsEnabled === true) {\n          link.enabled = bones[link.index].rigidBodyType > 0 ? false : true;\n        } else {\n          link.enabled = true;\n        }\n      }\n    }\n  }\n  _createCCDIKSolver(mesh) {\n    if (CCDIKSolver === void 0) {\n      throw new Error(\"THREE.MMDAnimationHelper: Import CCDIKSolver.\");\n    }\n    return new CCDIKSolver(mesh, mesh.geometry.userData.MMD.iks);\n  }\n  _createMMDPhysics(mesh, params) {\n    if (MMDPhysics === void 0) {\n      throw new Error(\"THREE.MMDPhysics: Import MMDPhysics.\");\n    }\n    return new MMDPhysics(mesh, mesh.geometry.userData.MMD.rigidBodies, mesh.geometry.userData.MMD.constraints, params);\n  }\n  /*\n   * Detects the longest duration and then sets it to them to sync.\n   * TODO: Not to access private properties ( ._actions and ._clip )\n   */\n  _syncDuration() {\n    let max = 0;\n    const objects = this.objects;\n    const meshes = this.meshes;\n    const camera = this.camera;\n    const audioManager = this.audioManager;\n    for (let i = 0, il = meshes.length; i < il; i++) {\n      const mixer = this.objects.get(meshes[i]).mixer;\n      if (mixer === void 0) continue;\n      for (let j = 0; j < mixer._actions.length; j++) {\n        const clip = mixer._actions[j]._clip;\n        if (!objects.has(clip)) {\n          objects.set(clip, {\n            duration: clip.duration\n          });\n        }\n        max = Math.max(max, objects.get(clip).duration);\n      }\n    }\n    if (camera !== null) {\n      const mixer = this.objects.get(camera).mixer;\n      if (mixer !== void 0) {\n        for (let i = 0, il = mixer._actions.length; i < il; i++) {\n          const clip = mixer._actions[i]._clip;\n          if (!objects.has(clip)) {\n            objects.set(clip, {\n              duration: clip.duration\n            });\n          }\n          max = Math.max(max, objects.get(clip).duration);\n        }\n      }\n    }\n    if (audioManager !== null) {\n      max = Math.max(max, objects.get(audioManager).duration);\n    }\n    max += this.configuration.afterglow;\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const mixer = this.objects.get(this.meshes[i]).mixer;\n      if (mixer === void 0) continue;\n      for (let j = 0, jl = mixer._actions.length; j < jl; j++) {\n        mixer._actions[j]._clip.duration = max;\n      }\n    }\n    if (camera !== null) {\n      const mixer = this.objects.get(camera).mixer;\n      if (mixer !== void 0) {\n        for (let i = 0, il = mixer._actions.length; i < il; i++) {\n          mixer._actions[i]._clip.duration = max;\n        }\n      }\n    }\n    if (audioManager !== null) {\n      audioManager.duration = max;\n    }\n  }\n  // workaround\n  _updatePropertyMixersBuffer(mesh) {\n    const mixer = this.objects.get(mesh).mixer;\n    const propertyMixers = mixer._bindings;\n    const accuIndex = mixer._accuIndex;\n    for (let i = 0, il = propertyMixers.length; i < il; i++) {\n      const propertyMixer = propertyMixers[i];\n      const buffer = propertyMixer.buffer;\n      const stride = propertyMixer.valueSize;\n      const offset = (accuIndex + 1) * stride;\n      propertyMixer.binding.getValue(buffer, offset);\n    }\n  }\n  /*\n   * Avoiding these two issues by restore/save bones before/after mixer animation.\n   *\n   * 1. PropertyMixer used by AnimationMixer holds cache value in .buffer.\n   *    Calculating IK, Grant, and Physics after mixer animation can break\n   *    the cache coherency.\n   *\n   * 2. Applying Grant two or more times without reset the posing breaks model.\n   */\n  _saveBones(mesh) {\n    const objects = this.objects.get(mesh);\n    const bones = mesh.skeleton.bones;\n    let backupBones = objects.backupBones;\n    if (backupBones === void 0) {\n      backupBones = new Float32Array(bones.length * 7);\n      objects.backupBones = backupBones;\n    }\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i];\n      bone.position.toArray(backupBones, i * 7);\n      bone.quaternion.toArray(backupBones, i * 7 + 3);\n    }\n  }\n  _restoreBones(mesh) {\n    const objects = this.objects.get(mesh);\n    const backupBones = objects.backupBones;\n    if (backupBones === void 0) return;\n    const bones = mesh.skeleton.bones;\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i];\n      bone.position.fromArray(backupBones, i * 7);\n      bone.quaternion.fromArray(backupBones, i * 7 + 3);\n    }\n  }\n  // experimental\n  _getMasterPhysics() {\n    if (this.masterPhysics !== null) return this.masterPhysics;\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const physics = this.meshes[i].physics;\n      if (physics !== void 0 && physics !== null) {\n        this.masterPhysics = physics;\n        return this.masterPhysics;\n      }\n    }\n    return null;\n  }\n  _updateSharedPhysics(delta) {\n    if (this.meshes.length === 0 || !this.enabled.physics || !this.sharedPhysics) return;\n    const physics = this._getMasterPhysics();\n    if (physics === null) return;\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const p = this.meshes[i].physics;\n      if (p !== null && p !== void 0) {\n        p.updateRigidBodies();\n      }\n    }\n    physics.stepSimulation(delta);\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const p = this.meshes[i].physics;\n      if (p !== null && p !== void 0) {\n        p.updateBones();\n      }\n    }\n  }\n}\nconst _quaternions = [];\nlet _quaternionIndex = 0;\nfunction getQuaternion() {\n  if (_quaternionIndex >= _quaternions.length) {\n    _quaternions.push(new Quaternion());\n  }\n  return _quaternions[_quaternionIndex++];\n}\nconst _grantResultMap = /* @__PURE__ */new Map();\nfunction updateOne(mesh, boneIndex, ikSolver, grantSolver) {\n  const bones = mesh.skeleton.bones;\n  const bonesData = mesh.geometry.userData.MMD.bones;\n  const boneData = bonesData[boneIndex];\n  const bone = bones[boneIndex];\n  if (_grantResultMap.has(boneIndex)) return;\n  const quaternion = getQuaternion();\n  _grantResultMap.set(boneIndex, quaternion.copy(bone.quaternion));\n  if (grantSolver && boneData.grant && !boneData.grant.isLocal && boneData.grant.affectRotation) {\n    const parentIndex = boneData.grant.parentIndex;\n    const ratio = boneData.grant.ratio;\n    if (!_grantResultMap.has(parentIndex)) {\n      updateOne(mesh, parentIndex, ikSolver, grantSolver);\n    }\n    grantSolver.addGrantRotation(bone, _grantResultMap.get(parentIndex), ratio);\n  }\n  if (ikSolver && boneData.ik) {\n    mesh.updateMatrixWorld(true);\n    ikSolver.updateOne(boneData.ik);\n    const links = boneData.ik.links;\n    for (let i = 0, il = links.length; i < il; i++) {\n      const link = links[i];\n      if (link.enabled === false) continue;\n      const linkIndex = link.index;\n      if (_grantResultMap.has(linkIndex)) {\n        _grantResultMap.set(linkIndex, _grantResultMap.get(linkIndex).copy(bones[linkIndex].quaternion));\n      }\n    }\n  }\n  quaternion.copy(bone.quaternion);\n}\nclass AudioManager {\n  /**\n   * @param {THREE.Audio} audio\n   * @param {Object} params - (optional)\n   * @param {Nuumber} params.delayTime\n   */\n  constructor(audio, params = {}) {\n    this.audio = audio;\n    this.elapsedTime = 0;\n    this.currentTime = 0;\n    this.delayTime = params.delayTime !== void 0 ? params.delayTime : 0;\n    this.audioDuration = this.audio.buffer.duration;\n    this.duration = this.audioDuration + this.delayTime;\n  }\n  /**\n   * @param {Number} delta\n   * @return {AudioManager}\n   */\n  control(delta) {\n    this.elapsed += delta;\n    this.currentTime += delta;\n    if (this._shouldStopAudio()) this.audio.stop();\n    if (this._shouldStartAudio()) this.audio.play();\n    return this;\n  }\n  // private methods\n  _shouldStartAudio() {\n    if (this.audio.isPlaying) return false;\n    while (this.currentTime >= this.duration) {\n      this.currentTime -= this.duration;\n    }\n    if (this.currentTime < this.delayTime) return false;\n    if (this.currentTime - this.delayTime > this.audioDuration) return false;\n    return true;\n  }\n  _shouldStopAudio() {\n    return this.audio.isPlaying && this.currentTime >= this.duration;\n  }\n}\nconst _q = /* @__PURE__ */new Quaternion();\nclass GrantSolver {\n  constructor(mesh, grants = []) {\n    this.mesh = mesh;\n    this.grants = grants;\n  }\n  /**\n   * Solve all the grant bones\n   * @return {GrantSolver}\n   */\n  update() {\n    const grants = this.grants;\n    for (let i = 0, il = grants.length; i < il; i++) {\n      this.updateOne(grants[i]);\n    }\n    return this;\n  }\n  /**\n   * Solve a grant bone\n   * @param {Object} grant - grant parameter\n   * @return {GrantSolver}\n   */\n  updateOne(grant) {\n    const bones = this.mesh.skeleton.bones;\n    const bone = bones[grant.index];\n    const parentBone = bones[grant.parentIndex];\n    if (grant.isLocal) {\n      if (grant.affectPosition) ;\n      if (grant.affectRotation) ;\n    } else {\n      if (grant.affectPosition) ;\n      if (grant.affectRotation) {\n        this.addGrantRotation(bone, parentBone.quaternion, grant.ratio);\n      }\n    }\n    return this;\n  }\n  addGrantRotation(bone, q, ratio) {\n    _q.set(0, 0, 0, 1);\n    _q.slerp(q, ratio);\n    bone.quaternion.multiply(_q);\n    return this;\n  }\n}\nexport { MMDAnimationHelper };", "map": {"version": 3, "names": ["MMDAnimationHelper", "constructor", "params", "meshes", "camera", "cameraTarget", "Object3D", "name", "audio", "audioManager", "objects", "WeakMap", "configuration", "sync", "afterglow", "resetPhysicsOnLoop", "pmxAnimation", "enabled", "animation", "ik", "grant", "physics", "cameraAnimation", "onBeforePhysics", "sharedPhysics", "masterPhysics", "add", "object", "isSkinnedMesh", "_add<PERSON>esh", "isCamera", "_setupCamera", "type", "_setupAudio", "Error", "_syncDuration", "remove", "_remove<PERSON><PERSON>", "_clearCamera", "_clearAudio", "update", "delta", "control", "i", "length", "_animate<PERSON><PERSON>", "_updateSharedPhysics", "_animateCamera", "pose", "mesh", "vpd", "resetPose", "bones", "skeleton", "boneParams", "boneNameDictionary", "il", "vector", "Vector3", "quaternion", "Quaternion", "boneParam", "boneIndex", "bone", "position", "fromArray", "translation", "multiply", "updateMatrixWorld", "geometry", "userData", "MMD", "format", "sortedBonesData", "_sortBoneDataArray", "slice", "ikSolver", "_createCCDIKSolver", "grantSolver", "createGrantSolver", "_animatePM<PERSON><PERSON>esh", "enable", "key", "_optimizeIK", "GrantSolver", "grants", "indexOf", "push", "set", "looped", "_setupMeshAnimation", "_setupMeshPhysics", "clearCamera", "_setupCameraAnimation", "clearAudio", "AudioManager", "duration", "found", "writeIndex", "delete", "get", "animations", "Array", "isArray", "mixer", "AnimationMixer", "clipAction", "play", "addEventListener", "event", "tracks", "action", "_clip", "world", "_getMasterPhysics", "_createMMDPhysics", "animationWarmup", "reset", "warmup", "_restoreBones", "_saveBones", "boneDataArray", "sort", "a", "b", "transformationClass", "index", "_quaternionIndex", "_grantResultMap", "clear", "updateOne", "updateProjectionMatrix", "up", "applyQuaternion", "lookAt", "physicsEnabled", "iks", "links", "j", "jl", "link", "rigidBodyType", "CCDIKSolver", "MMDPhysics", "rigidBodies", "constraints", "max", "_actions", "clip", "has", "Math", "_updatePropertyMixersBuffer", "propertyMixers", "_bindings", "accuIndex", "_accuIndex", "propertyMixer", "buffer", "stride", "valueSize", "offset", "binding", "getValue", "<PERSON><PERSON><PERSON>", "Float32Array", "toArray", "p", "updateRigidBodies", "stepSimulation", "updateBones", "_quaternions", "getQuaternion", "Map", "bonesData", "boneData", "copy", "isLocal", "affectRotation", "parentIndex", "ratio", "addGrantRotation", "linkIndex", "elapsedTime", "currentTime", "delayTime", "audioDuration", "elapsed", "_shouldStopAudio", "stop", "_shouldStartAudio", "isPlaying", "_q", "parentBone", "affectPosition", "q", "slerp"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/animation/MMDAnimationHelper.js"], "sourcesContent": ["import { AnimationMixer, Object3D, <PERSON>uatern<PERSON>, Vector3 } from 'three'\nimport { CCDIKSolver } from '../animation/CCDIKSolver'\nimport { MMDPhysics } from '../animation/MMDPhysics'\n\n/**\n * MMDAnimationHelper handles animation of MMD assets loaded by MMDLoader\n * with MMD special features as IK, Grant, and Physics.\n *\n * Dependencies\n *  - ammo.js https://github.com/kripken/ammo.js\n *  - MMDPhysics\n *  - CCDIKSolver\n *\n * TODO\n *  - more precise grant skinning support.\n */\nclass MMDAnimationHelper {\n  /**\n   * @param {Object} params - (optional)\n   * @param {boolean} params.sync - Whether animation durations of added objects are synched. Default is true.\n   * @param {Number} params.afterglow - Default is 0.0.\n   * @param {boolean} params.resetPhysicsOnLoop - Default is true.\n   */\n  constructor(params = {}) {\n    this.meshes = []\n\n    this.camera = null\n    this.cameraTarget = new Object3D()\n    this.cameraTarget.name = 'target'\n\n    this.audio = null\n    this.audioManager = null\n\n    this.objects = new WeakMap()\n\n    this.configuration = {\n      sync: params.sync !== undefined ? params.sync : true,\n      afterglow: params.afterglow !== undefined ? params.afterglow : 0.0,\n      resetPhysicsOnLoop: params.resetPhysicsOnLoop !== undefined ? params.resetPhysicsOnLoop : true,\n      pmxAnimation: params.pmxAnimation !== undefined ? params.pmxAnimation : false,\n    }\n\n    this.enabled = {\n      animation: true,\n      ik: true,\n      grant: true,\n      physics: true,\n      cameraAnimation: true,\n    }\n\n    this.onBeforePhysics = function (/* mesh */) {}\n\n    // experimental\n    this.sharedPhysics = false\n    this.masterPhysics = null\n  }\n\n  /**\n   * Adds an Three.js Object to helper and setups animation.\n   * The anmation durations of added objects are synched\n   * if this.configuration.sync is true.\n   *\n   * @param {THREE.SkinnedMesh|THREE.Camera|THREE.Audio} object\n   * @param {Object} params - (optional)\n   * @param {THREE.AnimationClip|Array<THREE.AnimationClip>} params.animation - Only for THREE.SkinnedMesh and THREE.Camera. Default is undefined.\n   * @param {boolean} params.physics - Only for THREE.SkinnedMesh. Default is true.\n   * @param {Integer} params.warmup - Only for THREE.SkinnedMesh and physics is true. Default is 60.\n   * @param {Number} params.unitStep - Only for THREE.SkinnedMesh and physics is true. Default is 1 / 65.\n   * @param {Integer} params.maxStepNum - Only for THREE.SkinnedMesh and physics is true. Default is 3.\n   * @param {Vector3} params.gravity - Only for THREE.SkinnedMesh and physics is true. Default ( 0, - 9.8 * 10, 0 ).\n   * @param {Number} params.delayTime - Only for THREE.Audio. Default is 0.0.\n   * @return {MMDAnimationHelper}\n   */\n  add(object, params = {}) {\n    if (object.isSkinnedMesh) {\n      this._addMesh(object, params)\n    } else if (object.isCamera) {\n      this._setupCamera(object, params)\n    } else if (object.type === 'Audio') {\n      this._setupAudio(object, params)\n    } else {\n      throw new Error(\n        'THREE.MMDAnimationHelper.add: ' +\n          'accepts only ' +\n          'THREE.SkinnedMesh or ' +\n          'THREE.Camera or ' +\n          'THREE.Audio instance.',\n      )\n    }\n\n    if (this.configuration.sync) this._syncDuration()\n\n    return this\n  }\n\n  /**\n   * Removes an Three.js Object from helper.\n   *\n   * @param {THREE.SkinnedMesh|THREE.Camera|THREE.Audio} object\n   * @return {MMDAnimationHelper}\n   */\n  remove(object) {\n    if (object.isSkinnedMesh) {\n      this._removeMesh(object)\n    } else if (object.isCamera) {\n      this._clearCamera(object)\n    } else if (object.type === 'Audio') {\n      this._clearAudio(object)\n    } else {\n      throw new Error(\n        'THREE.MMDAnimationHelper.remove: ' +\n          'accepts only ' +\n          'THREE.SkinnedMesh or ' +\n          'THREE.Camera or ' +\n          'THREE.Audio instance.',\n      )\n    }\n\n    if (this.configuration.sync) this._syncDuration()\n\n    return this\n  }\n\n  /**\n   * Updates the animation.\n   *\n   * @param {Number} delta\n   * @return {MMDAnimationHelper}\n   */\n  update(delta) {\n    if (this.audioManager !== null) this.audioManager.control(delta)\n\n    for (let i = 0; i < this.meshes.length; i++) {\n      this._animateMesh(this.meshes[i], delta)\n    }\n\n    if (this.sharedPhysics) this._updateSharedPhysics(delta)\n\n    if (this.camera !== null) this._animateCamera(this.camera, delta)\n\n    return this\n  }\n\n  /**\n   * Changes the pose of SkinnedMesh as VPD specifies.\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Object} vpd - VPD content parsed MMDParser\n   * @param {Object} params - (optional)\n   * @param {boolean} params.resetPose - Default is true.\n   * @param {boolean} params.ik - Default is true.\n   * @param {boolean} params.grant - Default is true.\n   * @return {MMDAnimationHelper}\n   */\n  pose(mesh, vpd, params = {}) {\n    if (params.resetPose !== false) mesh.pose()\n\n    const bones = mesh.skeleton.bones\n    const boneParams = vpd.bones\n\n    const boneNameDictionary = {}\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      boneNameDictionary[bones[i].name] = i\n    }\n\n    const vector = new Vector3()\n    const quaternion = new Quaternion()\n\n    for (let i = 0, il = boneParams.length; i < il; i++) {\n      const boneParam = boneParams[i]\n      const boneIndex = boneNameDictionary[boneParam.name]\n\n      if (boneIndex === undefined) continue\n\n      const bone = bones[boneIndex]\n      bone.position.add(vector.fromArray(boneParam.translation))\n      bone.quaternion.multiply(quaternion.fromArray(boneParam.quaternion))\n    }\n\n    mesh.updateMatrixWorld(true)\n\n    // PMX animation system special path\n    if (this.configuration.pmxAnimation && mesh.geometry.userData.MMD && mesh.geometry.userData.MMD.format === 'pmx') {\n      const sortedBonesData = this._sortBoneDataArray(mesh.geometry.userData.MMD.bones.slice())\n      const ikSolver = params.ik !== false ? this._createCCDIKSolver(mesh) : null\n      const grantSolver = params.grant !== false ? this.createGrantSolver(mesh) : null\n      this._animatePMXMesh(mesh, sortedBonesData, ikSolver, grantSolver)\n    } else {\n      if (params.ik !== false) {\n        this._createCCDIKSolver(mesh).update()\n      }\n\n      if (params.grant !== false) {\n        this.createGrantSolver(mesh).update()\n      }\n    }\n\n    return this\n  }\n\n  /**\n   * Enabes/Disables an animation feature.\n   *\n   * @param {string} key\n   * @param {boolean} enabled\n   * @return {MMDAnimationHelper}\n   */\n  enable(key, enabled) {\n    if (this.enabled[key] === undefined) {\n      throw new Error('THREE.MMDAnimationHelper.enable: ' + 'unknown key ' + key)\n    }\n\n    this.enabled[key] = enabled\n\n    if (key === 'physics') {\n      for (let i = 0, il = this.meshes.length; i < il; i++) {\n        this._optimizeIK(this.meshes[i], enabled)\n      }\n    }\n\n    return this\n  }\n\n  /**\n   * Creates an GrantSolver instance.\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @return {GrantSolver}\n   */\n  createGrantSolver(mesh) {\n    return new GrantSolver(mesh, mesh.geometry.userData.MMD.grants)\n  }\n\n  // private methods\n\n  _addMesh(mesh, params) {\n    if (this.meshes.indexOf(mesh) >= 0) {\n      throw new Error('THREE.MMDAnimationHelper._addMesh: ' + \"SkinnedMesh '\" + mesh.name + \"' has already been added.\")\n    }\n\n    this.meshes.push(mesh)\n    this.objects.set(mesh, { looped: false })\n\n    this._setupMeshAnimation(mesh, params.animation)\n\n    if (params.physics !== false) {\n      this._setupMeshPhysics(mesh, params)\n    }\n\n    return this\n  }\n\n  _setupCamera(camera, params) {\n    if (this.camera === camera) {\n      throw new Error('THREE.MMDAnimationHelper._setupCamera: ' + \"Camera '\" + camera.name + \"' has already been set.\")\n    }\n\n    if (this.camera) this.clearCamera(this.camera)\n\n    this.camera = camera\n\n    camera.add(this.cameraTarget)\n\n    this.objects.set(camera, {})\n\n    if (params.animation !== undefined) {\n      this._setupCameraAnimation(camera, params.animation)\n    }\n\n    return this\n  }\n\n  _setupAudio(audio, params) {\n    if (this.audio === audio) {\n      throw new Error('THREE.MMDAnimationHelper._setupAudio: ' + \"Audio '\" + audio.name + \"' has already been set.\")\n    }\n\n    if (this.audio) this.clearAudio(this.audio)\n\n    this.audio = audio\n    this.audioManager = new AudioManager(audio, params)\n\n    this.objects.set(this.audioManager, {\n      duration: this.audioManager.duration,\n    })\n\n    return this\n  }\n\n  _removeMesh(mesh) {\n    let found = false\n    let writeIndex = 0\n\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      if (this.meshes[i] === mesh) {\n        this.objects.delete(mesh)\n        found = true\n\n        continue\n      }\n\n      this.meshes[writeIndex++] = this.meshes[i]\n    }\n\n    if (!found) {\n      throw new Error(\n        'THREE.MMDAnimationHelper._removeMesh: ' + \"SkinnedMesh '\" + mesh.name + \"' has not been added yet.\",\n      )\n    }\n\n    this.meshes.length = writeIndex\n\n    return this\n  }\n\n  _clearCamera(camera) {\n    if (camera !== this.camera) {\n      throw new Error('THREE.MMDAnimationHelper._clearCamera: ' + \"Camera '\" + camera.name + \"' has not been set yet.\")\n    }\n\n    this.camera.remove(this.cameraTarget)\n\n    this.objects.delete(this.camera)\n    this.camera = null\n\n    return this\n  }\n\n  _clearAudio(audio) {\n    if (audio !== this.audio) {\n      throw new Error('THREE.MMDAnimationHelper._clearAudio: ' + \"Audio '\" + audio.name + \"' has not been set yet.\")\n    }\n\n    this.objects.delete(this.audioManager)\n\n    this.audio = null\n    this.audioManager = null\n\n    return this\n  }\n\n  _setupMeshAnimation(mesh, animation) {\n    const objects = this.objects.get(mesh)\n\n    if (animation !== undefined) {\n      const animations = Array.isArray(animation) ? animation : [animation]\n\n      objects.mixer = new AnimationMixer(mesh)\n\n      for (let i = 0, il = animations.length; i < il; i++) {\n        objects.mixer.clipAction(animations[i]).play()\n      }\n\n      // TODO: find a workaround not to access ._clip looking like a private property\n      objects.mixer.addEventListener('loop', function (event) {\n        const tracks = event.action._clip.tracks\n\n        if (tracks.length > 0 && tracks[0].name.slice(0, 6) !== '.bones') return\n\n        objects.looped = true\n      })\n    }\n\n    objects.ikSolver = this._createCCDIKSolver(mesh)\n    objects.grantSolver = this.createGrantSolver(mesh)\n\n    return this\n  }\n\n  _setupCameraAnimation(camera, animation) {\n    const animations = Array.isArray(animation) ? animation : [animation]\n\n    const objects = this.objects.get(camera)\n\n    objects.mixer = new AnimationMixer(camera)\n\n    for (let i = 0, il = animations.length; i < il; i++) {\n      objects.mixer.clipAction(animations[i]).play()\n    }\n  }\n\n  _setupMeshPhysics(mesh, params) {\n    const objects = this.objects.get(mesh)\n\n    // shared physics is experimental\n\n    if (params.world === undefined && this.sharedPhysics) {\n      const masterPhysics = this._getMasterPhysics()\n\n      if (masterPhysics !== null) world = masterPhysics.world // eslint-disable-line no-undef\n    }\n\n    objects.physics = this._createMMDPhysics(mesh, params)\n\n    if (objects.mixer && params.animationWarmup !== false) {\n      this._animateMesh(mesh, 0)\n      objects.physics.reset()\n    }\n\n    objects.physics.warmup(params.warmup !== undefined ? params.warmup : 60)\n\n    this._optimizeIK(mesh, true)\n  }\n\n  _animateMesh(mesh, delta) {\n    const objects = this.objects.get(mesh)\n\n    const mixer = objects.mixer\n    const ikSolver = objects.ikSolver\n    const grantSolver = objects.grantSolver\n    const physics = objects.physics\n    const looped = objects.looped\n\n    if (mixer && this.enabled.animation) {\n      // alternate solution to save/restore bones but less performant?\n      //mesh.pose();\n      //this._updatePropertyMixersBuffer( mesh );\n\n      this._restoreBones(mesh)\n\n      mixer.update(delta)\n\n      this._saveBones(mesh)\n\n      // PMX animation system special path\n      if (\n        this.configuration.pmxAnimation &&\n        mesh.geometry.userData.MMD &&\n        mesh.geometry.userData.MMD.format === 'pmx'\n      ) {\n        if (!objects.sortedBonesData)\n          objects.sortedBonesData = this._sortBoneDataArray(mesh.geometry.userData.MMD.bones.slice())\n\n        this._animatePMXMesh(\n          mesh,\n          objects.sortedBonesData,\n          ikSolver && this.enabled.ik ? ikSolver : null,\n          grantSolver && this.enabled.grant ? grantSolver : null,\n        )\n      } else {\n        if (ikSolver && this.enabled.ik) {\n          mesh.updateMatrixWorld(true)\n          ikSolver.update()\n        }\n\n        if (grantSolver && this.enabled.grant) {\n          grantSolver.update()\n        }\n      }\n    }\n\n    if (looped === true && this.enabled.physics) {\n      if (physics && this.configuration.resetPhysicsOnLoop) physics.reset()\n\n      objects.looped = false\n    }\n\n    if (physics && this.enabled.physics && !this.sharedPhysics) {\n      this.onBeforePhysics(mesh)\n      physics.update(delta)\n    }\n  }\n\n  // Sort bones in order by 1. transformationClass and 2. bone index.\n  // In PMX animation system, bone transformations should be processed\n  // in this order.\n  _sortBoneDataArray(boneDataArray) {\n    return boneDataArray.sort(function (a, b) {\n      if (a.transformationClass !== b.transformationClass) {\n        return a.transformationClass - b.transformationClass\n      } else {\n        return a.index - b.index\n      }\n    })\n  }\n\n  // PMX Animation system is a bit too complex and doesn't great match to\n  // Three.js Animation system. This method attempts to simulate it as much as\n  // possible but doesn't perfectly simulate.\n  // This method is more costly than the regular one so\n  // you are recommended to set constructor parameter \"pmxAnimation: true\"\n  // only if your PMX model animation doesn't work well.\n  // If you need better method you would be required to write your own.\n  _animatePMXMesh(mesh, sortedBonesData, ikSolver, grantSolver) {\n    _quaternionIndex = 0\n    _grantResultMap.clear()\n\n    for (let i = 0, il = sortedBonesData.length; i < il; i++) {\n      updateOne(mesh, sortedBonesData[i].index, ikSolver, grantSolver)\n    }\n\n    mesh.updateMatrixWorld(true)\n    return this\n  }\n\n  _animateCamera(camera, delta) {\n    const mixer = this.objects.get(camera).mixer\n\n    if (mixer && this.enabled.cameraAnimation) {\n      mixer.update(delta)\n\n      camera.updateProjectionMatrix()\n\n      camera.up.set(0, 1, 0)\n      camera.up.applyQuaternion(camera.quaternion)\n      camera.lookAt(this.cameraTarget.position)\n    }\n  }\n\n  _optimizeIK(mesh, physicsEnabled) {\n    const iks = mesh.geometry.userData.MMD.iks\n    const bones = mesh.geometry.userData.MMD.bones\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i]\n      const links = ik.links\n\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        const link = links[j]\n\n        if (physicsEnabled === true) {\n          // disable IK of the bone the corresponding rigidBody type of which is 1 or 2\n          // because its rotation will be overriden by physics\n          link.enabled = bones[link.index].rigidBodyType > 0 ? false : true\n        } else {\n          link.enabled = true\n        }\n      }\n    }\n  }\n\n  _createCCDIKSolver(mesh) {\n    if (CCDIKSolver === undefined) {\n      throw new Error('THREE.MMDAnimationHelper: Import CCDIKSolver.')\n    }\n\n    return new CCDIKSolver(mesh, mesh.geometry.userData.MMD.iks)\n  }\n\n  _createMMDPhysics(mesh, params) {\n    if (MMDPhysics === undefined) {\n      throw new Error('THREE.MMDPhysics: Import MMDPhysics.')\n    }\n\n    return new MMDPhysics(mesh, mesh.geometry.userData.MMD.rigidBodies, mesh.geometry.userData.MMD.constraints, params)\n  }\n\n  /*\n   * Detects the longest duration and then sets it to them to sync.\n   * TODO: Not to access private properties ( ._actions and ._clip )\n   */\n  _syncDuration() {\n    let max = 0.0\n\n    const objects = this.objects\n    const meshes = this.meshes\n    const camera = this.camera\n    const audioManager = this.audioManager\n\n    // get the longest duration\n\n    for (let i = 0, il = meshes.length; i < il; i++) {\n      const mixer = this.objects.get(meshes[i]).mixer\n\n      if (mixer === undefined) continue\n\n      for (let j = 0; j < mixer._actions.length; j++) {\n        const clip = mixer._actions[j]._clip\n\n        if (!objects.has(clip)) {\n          objects.set(clip, {\n            duration: clip.duration,\n          })\n        }\n\n        max = Math.max(max, objects.get(clip).duration)\n      }\n    }\n\n    if (camera !== null) {\n      const mixer = this.objects.get(camera).mixer\n\n      if (mixer !== undefined) {\n        for (let i = 0, il = mixer._actions.length; i < il; i++) {\n          const clip = mixer._actions[i]._clip\n\n          if (!objects.has(clip)) {\n            objects.set(clip, {\n              duration: clip.duration,\n            })\n          }\n\n          max = Math.max(max, objects.get(clip).duration)\n        }\n      }\n    }\n\n    if (audioManager !== null) {\n      max = Math.max(max, objects.get(audioManager).duration)\n    }\n\n    max += this.configuration.afterglow\n\n    // update the duration\n\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const mixer = this.objects.get(this.meshes[i]).mixer\n\n      if (mixer === undefined) continue\n\n      for (let j = 0, jl = mixer._actions.length; j < jl; j++) {\n        mixer._actions[j]._clip.duration = max\n      }\n    }\n\n    if (camera !== null) {\n      const mixer = this.objects.get(camera).mixer\n\n      if (mixer !== undefined) {\n        for (let i = 0, il = mixer._actions.length; i < il; i++) {\n          mixer._actions[i]._clip.duration = max\n        }\n      }\n    }\n\n    if (audioManager !== null) {\n      audioManager.duration = max\n    }\n  }\n\n  // workaround\n\n  _updatePropertyMixersBuffer(mesh) {\n    const mixer = this.objects.get(mesh).mixer\n\n    const propertyMixers = mixer._bindings\n    const accuIndex = mixer._accuIndex\n\n    for (let i = 0, il = propertyMixers.length; i < il; i++) {\n      const propertyMixer = propertyMixers[i]\n      const buffer = propertyMixer.buffer\n      const stride = propertyMixer.valueSize\n      const offset = (accuIndex + 1) * stride\n\n      propertyMixer.binding.getValue(buffer, offset)\n    }\n  }\n\n  /*\n   * Avoiding these two issues by restore/save bones before/after mixer animation.\n   *\n   * 1. PropertyMixer used by AnimationMixer holds cache value in .buffer.\n   *    Calculating IK, Grant, and Physics after mixer animation can break\n   *    the cache coherency.\n   *\n   * 2. Applying Grant two or more times without reset the posing breaks model.\n   */\n  _saveBones(mesh) {\n    const objects = this.objects.get(mesh)\n\n    const bones = mesh.skeleton.bones\n\n    let backupBones = objects.backupBones\n\n    if (backupBones === undefined) {\n      backupBones = new Float32Array(bones.length * 7)\n      objects.backupBones = backupBones\n    }\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i]\n      bone.position.toArray(backupBones, i * 7)\n      bone.quaternion.toArray(backupBones, i * 7 + 3)\n    }\n  }\n\n  _restoreBones(mesh) {\n    const objects = this.objects.get(mesh)\n\n    const backupBones = objects.backupBones\n\n    if (backupBones === undefined) return\n\n    const bones = mesh.skeleton.bones\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i]\n      bone.position.fromArray(backupBones, i * 7)\n      bone.quaternion.fromArray(backupBones, i * 7 + 3)\n    }\n  }\n\n  // experimental\n\n  _getMasterPhysics() {\n    if (this.masterPhysics !== null) return this.masterPhysics\n\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const physics = this.meshes[i].physics\n\n      if (physics !== undefined && physics !== null) {\n        this.masterPhysics = physics\n        return this.masterPhysics\n      }\n    }\n\n    return null\n  }\n\n  _updateSharedPhysics(delta) {\n    if (this.meshes.length === 0 || !this.enabled.physics || !this.sharedPhysics) return\n\n    const physics = this._getMasterPhysics()\n\n    if (physics === null) return\n\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const p = this.meshes[i].physics\n\n      if (p !== null && p !== undefined) {\n        p.updateRigidBodies()\n      }\n    }\n\n    physics.stepSimulation(delta)\n\n    for (let i = 0, il = this.meshes.length; i < il; i++) {\n      const p = this.meshes[i].physics\n\n      if (p !== null && p !== undefined) {\n        p.updateBones()\n      }\n    }\n  }\n}\n\n// Keep working quaternions for less GC\nconst _quaternions = []\nlet _quaternionIndex = 0\n\nfunction getQuaternion() {\n  if (_quaternionIndex >= _quaternions.length) {\n    _quaternions.push(new Quaternion())\n  }\n\n  return _quaternions[_quaternionIndex++]\n}\n\n// Save rotation whose grant and IK are already applied\n// used by grant children\nconst _grantResultMap = new Map()\n\nfunction updateOne(mesh, boneIndex, ikSolver, grantSolver) {\n  const bones = mesh.skeleton.bones\n  const bonesData = mesh.geometry.userData.MMD.bones\n  const boneData = bonesData[boneIndex]\n  const bone = bones[boneIndex]\n\n  // Return if already updated by being referred as a grant parent.\n  if (_grantResultMap.has(boneIndex)) return\n\n  const quaternion = getQuaternion()\n\n  // Initialize grant result here to prevent infinite loop.\n  // If it's referred before updating with actual result later\n  // result without applyting IK or grant is gotten\n  // but better than composing of infinite loop.\n  _grantResultMap.set(boneIndex, quaternion.copy(bone.quaternion))\n\n  // @TODO: Support global grant and grant position\n  if (grantSolver && boneData.grant && !boneData.grant.isLocal && boneData.grant.affectRotation) {\n    const parentIndex = boneData.grant.parentIndex\n    const ratio = boneData.grant.ratio\n\n    if (!_grantResultMap.has(parentIndex)) {\n      updateOne(mesh, parentIndex, ikSolver, grantSolver)\n    }\n\n    grantSolver.addGrantRotation(bone, _grantResultMap.get(parentIndex), ratio)\n  }\n\n  if (ikSolver && boneData.ik) {\n    // @TODO: Updating world matrices every time solving an IK bone is\n    // costly. Optimize if possible.\n    mesh.updateMatrixWorld(true)\n    ikSolver.updateOne(boneData.ik)\n\n    // No confident, but it seems the grant results with ik links should be updated?\n    const links = boneData.ik.links\n\n    for (let i = 0, il = links.length; i < il; i++) {\n      const link = links[i]\n\n      if (link.enabled === false) continue\n\n      const linkIndex = link.index\n\n      if (_grantResultMap.has(linkIndex)) {\n        _grantResultMap.set(linkIndex, _grantResultMap.get(linkIndex).copy(bones[linkIndex].quaternion))\n      }\n    }\n  }\n\n  // Update with the actual result here\n  quaternion.copy(bone.quaternion)\n}\n\n//\n\nclass AudioManager {\n  /**\n   * @param {THREE.Audio} audio\n   * @param {Object} params - (optional)\n   * @param {Nuumber} params.delayTime\n   */\n  constructor(audio, params = {}) {\n    this.audio = audio\n\n    this.elapsedTime = 0.0\n    this.currentTime = 0.0\n    this.delayTime = params.delayTime !== undefined ? params.delayTime : 0.0\n\n    this.audioDuration = this.audio.buffer.duration\n    this.duration = this.audioDuration + this.delayTime\n  }\n\n  /**\n   * @param {Number} delta\n   * @return {AudioManager}\n   */\n  control(delta) {\n    this.elapsed += delta\n    this.currentTime += delta\n\n    if (this._shouldStopAudio()) this.audio.stop()\n    if (this._shouldStartAudio()) this.audio.play()\n\n    return this\n  }\n\n  // private methods\n\n  _shouldStartAudio() {\n    if (this.audio.isPlaying) return false\n\n    while (this.currentTime >= this.duration) {\n      this.currentTime -= this.duration\n    }\n\n    if (this.currentTime < this.delayTime) return false\n\n    // 'duration' can be bigger than 'audioDuration + delayTime' because of sync configuration\n    if (this.currentTime - this.delayTime > this.audioDuration) return false\n\n    return true\n  }\n\n  _shouldStopAudio() {\n    return this.audio.isPlaying && this.currentTime >= this.duration\n  }\n}\n\nconst _q = /* @__PURE__ */ new Quaternion()\n\n/**\n * Solver for Grant (Fuyo in Japanese. I just google translated because\n * Fuyo may be MMD specific term and may not be common word in 3D CG terms.)\n * Grant propagates a bone's transform to other bones transforms even if\n * they are not children.\n * @param {THREE.SkinnedMesh} mesh\n * @param {Array<Object>} grants\n */\nclass GrantSolver {\n  constructor(mesh, grants = []) {\n    this.mesh = mesh\n    this.grants = grants\n  }\n\n  /**\n   * Solve all the grant bones\n   * @return {GrantSolver}\n   */\n  update() {\n    const grants = this.grants\n\n    for (let i = 0, il = grants.length; i < il; i++) {\n      this.updateOne(grants[i])\n    }\n\n    return this\n  }\n\n  /**\n   * Solve a grant bone\n   * @param {Object} grant - grant parameter\n   * @return {GrantSolver}\n   */\n  updateOne(grant) {\n    const bones = this.mesh.skeleton.bones\n    const bone = bones[grant.index]\n    const parentBone = bones[grant.parentIndex]\n\n    if (grant.isLocal) {\n      // TODO: implement\n      if (grant.affectPosition) {\n      }\n\n      // TODO: implement\n      if (grant.affectRotation) {\n      }\n    } else {\n      // TODO: implement\n      if (grant.affectPosition) {\n      }\n\n      if (grant.affectRotation) {\n        this.addGrantRotation(bone, parentBone.quaternion, grant.ratio)\n      }\n    }\n\n    return this\n  }\n\n  addGrantRotation(bone, q, ratio) {\n    _q.set(0, 0, 0, 1)\n    _q.slerp(q, ratio)\n    bone.quaternion.multiply(_q)\n\n    return this\n  }\n}\n\nexport { MMDAnimationHelper }\n"], "mappings": ";;;AAgBA,MAAMA,kBAAA,CAAmB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOvBC,YAAYC,MAAA,GAAS,IAAI;IACvB,KAAKC,MAAA,GAAS,EAAE;IAEhB,KAAKC,MAAA,GAAS;IACd,KAAKC,YAAA,GAAe,IAAIC,QAAA,CAAU;IAClC,KAAKD,YAAA,CAAaE,IAAA,GAAO;IAEzB,KAAKC,KAAA,GAAQ;IACb,KAAKC,YAAA,GAAe;IAEpB,KAAKC,OAAA,GAAU,mBAAIC,OAAA,CAAS;IAE5B,KAAKC,aAAA,GAAgB;MACnBC,IAAA,EAAMX,MAAA,CAAOW,IAAA,KAAS,SAAYX,MAAA,CAAOW,IAAA,GAAO;MAChDC,SAAA,EAAWZ,MAAA,CAAOY,SAAA,KAAc,SAAYZ,MAAA,CAAOY,SAAA,GAAY;MAC/DC,kBAAA,EAAoBb,MAAA,CAAOa,kBAAA,KAAuB,SAAYb,MAAA,CAAOa,kBAAA,GAAqB;MAC1FC,YAAA,EAAcd,MAAA,CAAOc,YAAA,KAAiB,SAAYd,MAAA,CAAOc,YAAA,GAAe;IACzE;IAED,KAAKC,OAAA,GAAU;MACbC,SAAA,EAAW;MACXC,EAAA,EAAI;MACJC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,eAAA,EAAiB;IAClB;IAED,KAAKC,eAAA,GAAkB,YAAsB,CAAE;IAG/C,KAAKC,aAAA,GAAgB;IACrB,KAAKC,aAAA,GAAgB;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBDC,IAAIC,MAAA,EAAQzB,MAAA,GAAS,IAAI;IACvB,IAAIyB,MAAA,CAAOC,aAAA,EAAe;MACxB,KAAKC,QAAA,CAASF,MAAA,EAAQzB,MAAM;IAClC,WAAeyB,MAAA,CAAOG,QAAA,EAAU;MAC1B,KAAKC,YAAA,CAAaJ,MAAA,EAAQzB,MAAM;IACtC,WAAeyB,MAAA,CAAOK,IAAA,KAAS,SAAS;MAClC,KAAKC,WAAA,CAAYN,MAAA,EAAQzB,MAAM;IACrC,OAAW;MACL,MAAM,IAAIgC,KAAA,CACR,uGAKD;IACF;IAED,IAAI,KAAKtB,aAAA,CAAcC,IAAA,EAAM,KAAKsB,aAAA,CAAe;IAEjD,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDC,OAAOT,MAAA,EAAQ;IACb,IAAIA,MAAA,CAAOC,aAAA,EAAe;MACxB,KAAKS,WAAA,CAAYV,MAAM;IAC7B,WAAeA,MAAA,CAAOG,QAAA,EAAU;MAC1B,KAAKQ,YAAA,CAAaX,MAAM;IAC9B,WAAeA,MAAA,CAAOK,IAAA,KAAS,SAAS;MAClC,KAAKO,WAAA,CAAYZ,MAAM;IAC7B,OAAW;MACL,MAAM,IAAIO,KAAA,CACR,0GAKD;IACF;IAED,IAAI,KAAKtB,aAAA,CAAcC,IAAA,EAAM,KAAKsB,aAAA,CAAe;IAEjD,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDK,OAAOC,KAAA,EAAO;IACZ,IAAI,KAAKhC,YAAA,KAAiB,MAAM,KAAKA,YAAA,CAAaiC,OAAA,CAAQD,KAAK;IAE/D,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKxC,MAAA,CAAOyC,MAAA,EAAQD,CAAA,IAAK;MAC3C,KAAKE,YAAA,CAAa,KAAK1C,MAAA,CAAOwC,CAAC,GAAGF,KAAK;IACxC;IAED,IAAI,KAAKjB,aAAA,EAAe,KAAKsB,oBAAA,CAAqBL,KAAK;IAEvD,IAAI,KAAKrC,MAAA,KAAW,MAAM,KAAK2C,cAAA,CAAe,KAAK3C,MAAA,EAAQqC,KAAK;IAEhE,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaDO,KAAKC,IAAA,EAAMC,GAAA,EAAKhD,MAAA,GAAS,IAAI;IAC3B,IAAIA,MAAA,CAAOiD,SAAA,KAAc,OAAOF,IAAA,CAAKD,IAAA,CAAM;IAE3C,MAAMI,KAAA,GAAQH,IAAA,CAAKI,QAAA,CAASD,KAAA;IAC5B,MAAME,UAAA,GAAaJ,GAAA,CAAIE,KAAA;IAEvB,MAAMG,kBAAA,GAAqB,CAAE;IAE7B,SAASZ,CAAA,GAAI,GAAGa,EAAA,GAAKJ,KAAA,CAAMR,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC9CY,kBAAA,CAAmBH,KAAA,CAAMT,CAAC,EAAEpC,IAAI,IAAIoC,CAAA;IACrC;IAED,MAAMc,MAAA,GAAS,IAAIC,OAAA,CAAS;IAC5B,MAAMC,UAAA,GAAa,IAAIC,UAAA,CAAY;IAEnC,SAASjB,CAAA,GAAI,GAAGa,EAAA,GAAKF,UAAA,CAAWV,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACnD,MAAMkB,SAAA,GAAYP,UAAA,CAAWX,CAAC;MAC9B,MAAMmB,SAAA,GAAYP,kBAAA,CAAmBM,SAAA,CAAUtD,IAAI;MAEnD,IAAIuD,SAAA,KAAc,QAAW;MAE7B,MAAMC,IAAA,GAAOX,KAAA,CAAMU,SAAS;MAC5BC,IAAA,CAAKC,QAAA,CAAStC,GAAA,CAAI+B,MAAA,CAAOQ,SAAA,CAAUJ,SAAA,CAAUK,WAAW,CAAC;MACzDH,IAAA,CAAKJ,UAAA,CAAWQ,QAAA,CAASR,UAAA,CAAWM,SAAA,CAAUJ,SAAA,CAAUF,UAAU,CAAC;IACpE;IAEDV,IAAA,CAAKmB,iBAAA,CAAkB,IAAI;IAG3B,IAAI,KAAKxD,aAAA,CAAcI,YAAA,IAAgBiC,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,IAAOtB,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIC,MAAA,KAAW,OAAO;MAChH,MAAMC,eAAA,GAAkB,KAAKC,kBAAA,CAAmBzB,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAInB,KAAA,CAAMuB,KAAA,EAAO;MACxF,MAAMC,QAAA,GAAW1E,MAAA,CAAOiB,EAAA,KAAO,QAAQ,KAAK0D,kBAAA,CAAmB5B,IAAI,IAAI;MACvE,MAAM6B,WAAA,GAAc5E,MAAA,CAAOkB,KAAA,KAAU,QAAQ,KAAK2D,iBAAA,CAAkB9B,IAAI,IAAI;MAC5E,KAAK+B,eAAA,CAAgB/B,IAAA,EAAMwB,eAAA,EAAiBG,QAAA,EAAUE,WAAW;IACvE,OAAW;MACL,IAAI5E,MAAA,CAAOiB,EAAA,KAAO,OAAO;QACvB,KAAK0D,kBAAA,CAAmB5B,IAAI,EAAET,MAAA,CAAQ;MACvC;MAED,IAAItC,MAAA,CAAOkB,KAAA,KAAU,OAAO;QAC1B,KAAK2D,iBAAA,CAAkB9B,IAAI,EAAET,MAAA,CAAQ;MACtC;IACF;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDyC,OAAOC,GAAA,EAAKjE,OAAA,EAAS;IACnB,IAAI,KAAKA,OAAA,CAAQiE,GAAG,MAAM,QAAW;MACnC,MAAM,IAAIhD,KAAA,CAAM,kDAAuDgD,GAAG;IAC3E;IAED,KAAKjE,OAAA,CAAQiE,GAAG,IAAIjE,OAAA;IAEpB,IAAIiE,GAAA,KAAQ,WAAW;MACrB,SAASvC,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;QACpD,KAAKwC,WAAA,CAAY,KAAKhF,MAAA,CAAOwC,CAAC,GAAG1B,OAAO;MACzC;IACF;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQD8D,kBAAkB9B,IAAA,EAAM;IACtB,OAAO,IAAImC,WAAA,CAAYnC,IAAA,EAAMA,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIc,MAAM;EAC/D;EAAA;EAIDxD,SAASoB,IAAA,EAAM/C,MAAA,EAAQ;IACrB,IAAI,KAAKC,MAAA,CAAOmF,OAAA,CAAQrC,IAAI,KAAK,GAAG;MAClC,MAAM,IAAIf,KAAA,CAAM,qDAA0De,IAAA,CAAK1C,IAAA,GAAO,2BAA2B;IAClH;IAED,KAAKJ,MAAA,CAAOoF,IAAA,CAAKtC,IAAI;IACrB,KAAKvC,OAAA,CAAQ8E,GAAA,CAAIvC,IAAA,EAAM;MAAEwC,MAAA,EAAQ;IAAA,CAAO;IAExC,KAAKC,mBAAA,CAAoBzC,IAAA,EAAM/C,MAAA,CAAOgB,SAAS;IAE/C,IAAIhB,MAAA,CAAOmB,OAAA,KAAY,OAAO;MAC5B,KAAKsE,iBAAA,CAAkB1C,IAAA,EAAM/C,MAAM;IACpC;IAED,OAAO;EACR;EAED6B,aAAa3B,MAAA,EAAQF,MAAA,EAAQ;IAC3B,IAAI,KAAKE,MAAA,KAAWA,MAAA,EAAQ;MAC1B,MAAM,IAAI8B,KAAA,CAAM,oDAAyD9B,MAAA,CAAOG,IAAA,GAAO,yBAAyB;IACjH;IAED,IAAI,KAAKH,MAAA,EAAQ,KAAKwF,WAAA,CAAY,KAAKxF,MAAM;IAE7C,KAAKA,MAAA,GAASA,MAAA;IAEdA,MAAA,CAAOsB,GAAA,CAAI,KAAKrB,YAAY;IAE5B,KAAKK,OAAA,CAAQ8E,GAAA,CAAIpF,MAAA,EAAQ,EAAE;IAE3B,IAAIF,MAAA,CAAOgB,SAAA,KAAc,QAAW;MAClC,KAAK2E,qBAAA,CAAsBzF,MAAA,EAAQF,MAAA,CAAOgB,SAAS;IACpD;IAED,OAAO;EACR;EAEDe,YAAYzB,KAAA,EAAON,MAAA,EAAQ;IACzB,IAAI,KAAKM,KAAA,KAAUA,KAAA,EAAO;MACxB,MAAM,IAAI0B,KAAA,CAAM,kDAAuD1B,KAAA,CAAMD,IAAA,GAAO,yBAAyB;IAC9G;IAED,IAAI,KAAKC,KAAA,EAAO,KAAKsF,UAAA,CAAW,KAAKtF,KAAK;IAE1C,KAAKA,KAAA,GAAQA,KAAA;IACb,KAAKC,YAAA,GAAe,IAAIsF,YAAA,CAAavF,KAAA,EAAON,MAAM;IAElD,KAAKQ,OAAA,CAAQ8E,GAAA,CAAI,KAAK/E,YAAA,EAAc;MAClCuF,QAAA,EAAU,KAAKvF,YAAA,CAAauF;IAClC,CAAK;IAED,OAAO;EACR;EAED3D,YAAYY,IAAA,EAAM;IAChB,IAAIgD,KAAA,GAAQ;IACZ,IAAIC,UAAA,GAAa;IAEjB,SAASvD,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACpD,IAAI,KAAKxC,MAAA,CAAOwC,CAAC,MAAMM,IAAA,EAAM;QAC3B,KAAKvC,OAAA,CAAQyF,MAAA,CAAOlD,IAAI;QACxBgD,KAAA,GAAQ;QAER;MACD;MAED,KAAK9F,MAAA,CAAO+F,UAAA,EAAY,IAAI,KAAK/F,MAAA,CAAOwC,CAAC;IAC1C;IAED,IAAI,CAACsD,KAAA,EAAO;MACV,MAAM,IAAI/D,KAAA,CACR,wDAA6De,IAAA,CAAK1C,IAAA,GAAO,2BAC1E;IACF;IAED,KAAKJ,MAAA,CAAOyC,MAAA,GAASsD,UAAA;IAErB,OAAO;EACR;EAED5D,aAAalC,MAAA,EAAQ;IACnB,IAAIA,MAAA,KAAW,KAAKA,MAAA,EAAQ;MAC1B,MAAM,IAAI8B,KAAA,CAAM,oDAAyD9B,MAAA,CAAOG,IAAA,GAAO,yBAAyB;IACjH;IAED,KAAKH,MAAA,CAAOgC,MAAA,CAAO,KAAK/B,YAAY;IAEpC,KAAKK,OAAA,CAAQyF,MAAA,CAAO,KAAK/F,MAAM;IAC/B,KAAKA,MAAA,GAAS;IAEd,OAAO;EACR;EAEDmC,YAAY/B,KAAA,EAAO;IACjB,IAAIA,KAAA,KAAU,KAAKA,KAAA,EAAO;MACxB,MAAM,IAAI0B,KAAA,CAAM,kDAAuD1B,KAAA,CAAMD,IAAA,GAAO,yBAAyB;IAC9G;IAED,KAAKG,OAAA,CAAQyF,MAAA,CAAO,KAAK1F,YAAY;IAErC,KAAKD,KAAA,GAAQ;IACb,KAAKC,YAAA,GAAe;IAEpB,OAAO;EACR;EAEDiF,oBAAoBzC,IAAA,EAAM/B,SAAA,EAAW;IACnC,MAAMR,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAInD,IAAI;IAErC,IAAI/B,SAAA,KAAc,QAAW;MAC3B,MAAMmF,UAAA,GAAaC,KAAA,CAAMC,OAAA,CAAQrF,SAAS,IAAIA,SAAA,GAAY,CAACA,SAAS;MAEpER,OAAA,CAAQ8F,KAAA,GAAQ,IAAIC,cAAA,CAAexD,IAAI;MAEvC,SAASN,CAAA,GAAI,GAAGa,EAAA,GAAK6C,UAAA,CAAWzD,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;QACnDjC,OAAA,CAAQ8F,KAAA,CAAME,UAAA,CAAWL,UAAA,CAAW1D,CAAC,CAAC,EAAEgE,IAAA,CAAM;MAC/C;MAGDjG,OAAA,CAAQ8F,KAAA,CAAMI,gBAAA,CAAiB,QAAQ,UAAUC,KAAA,EAAO;QACtD,MAAMC,MAAA,GAASD,KAAA,CAAME,MAAA,CAAOC,KAAA,CAAMF,MAAA;QAElC,IAAIA,MAAA,CAAOlE,MAAA,GAAS,KAAKkE,MAAA,CAAO,CAAC,EAAEvG,IAAA,CAAKoE,KAAA,CAAM,GAAG,CAAC,MAAM,UAAU;QAElEjE,OAAA,CAAQ+E,MAAA,GAAS;MACzB,CAAO;IACF;IAED/E,OAAA,CAAQkE,QAAA,GAAW,KAAKC,kBAAA,CAAmB5B,IAAI;IAC/CvC,OAAA,CAAQoE,WAAA,GAAc,KAAKC,iBAAA,CAAkB9B,IAAI;IAEjD,OAAO;EACR;EAED4C,sBAAsBzF,MAAA,EAAQc,SAAA,EAAW;IACvC,MAAMmF,UAAA,GAAaC,KAAA,CAAMC,OAAA,CAAQrF,SAAS,IAAIA,SAAA,GAAY,CAACA,SAAS;IAEpE,MAAMR,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAIhG,MAAM;IAEvCM,OAAA,CAAQ8F,KAAA,GAAQ,IAAIC,cAAA,CAAerG,MAAM;IAEzC,SAASuC,CAAA,GAAI,GAAGa,EAAA,GAAK6C,UAAA,CAAWzD,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACnDjC,OAAA,CAAQ8F,KAAA,CAAME,UAAA,CAAWL,UAAA,CAAW1D,CAAC,CAAC,EAAEgE,IAAA,CAAM;IAC/C;EACF;EAEDhB,kBAAkB1C,IAAA,EAAM/C,MAAA,EAAQ;IAC9B,MAAMQ,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAInD,IAAI;IAIrC,IAAI/C,MAAA,CAAO+G,KAAA,KAAU,UAAa,KAAKzF,aAAA,EAAe;MACpD,MAAMC,aAAA,GAAgB,KAAKyF,iBAAA,CAAmB;MAE9C,IAAIzF,aAAA,KAAkB,MAAMwF,KAAA,GAAQxF,aAAA,CAAcwF,KAAA;IACnD;IAEDvG,OAAA,CAAQW,OAAA,GAAU,KAAK8F,iBAAA,CAAkBlE,IAAA,EAAM/C,MAAM;IAErD,IAAIQ,OAAA,CAAQ8F,KAAA,IAAStG,MAAA,CAAOkH,eAAA,KAAoB,OAAO;MACrD,KAAKvE,YAAA,CAAaI,IAAA,EAAM,CAAC;MACzBvC,OAAA,CAAQW,OAAA,CAAQgG,KAAA,CAAO;IACxB;IAED3G,OAAA,CAAQW,OAAA,CAAQiG,MAAA,CAAOpH,MAAA,CAAOoH,MAAA,KAAW,SAAYpH,MAAA,CAAOoH,MAAA,GAAS,EAAE;IAEvE,KAAKnC,WAAA,CAAYlC,IAAA,EAAM,IAAI;EAC5B;EAEDJ,aAAaI,IAAA,EAAMR,KAAA,EAAO;IACxB,MAAM/B,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAInD,IAAI;IAErC,MAAMuD,KAAA,GAAQ9F,OAAA,CAAQ8F,KAAA;IACtB,MAAM5B,QAAA,GAAWlE,OAAA,CAAQkE,QAAA;IACzB,MAAME,WAAA,GAAcpE,OAAA,CAAQoE,WAAA;IAC5B,MAAMzD,OAAA,GAAUX,OAAA,CAAQW,OAAA;IACxB,MAAMoE,MAAA,GAAS/E,OAAA,CAAQ+E,MAAA;IAEvB,IAAIe,KAAA,IAAS,KAAKvF,OAAA,CAAQC,SAAA,EAAW;MAKnC,KAAKqG,aAAA,CAActE,IAAI;MAEvBuD,KAAA,CAAMhE,MAAA,CAAOC,KAAK;MAElB,KAAK+E,UAAA,CAAWvE,IAAI;MAGpB,IACE,KAAKrC,aAAA,CAAcI,YAAA,IACnBiC,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,IACvBtB,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIC,MAAA,KAAW,OACtC;QACA,IAAI,CAAC9D,OAAA,CAAQ+D,eAAA,EACX/D,OAAA,CAAQ+D,eAAA,GAAkB,KAAKC,kBAAA,CAAmBzB,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAInB,KAAA,CAAMuB,KAAA,EAAO;QAE5F,KAAKK,eAAA,CACH/B,IAAA,EACAvC,OAAA,CAAQ+D,eAAA,EACRG,QAAA,IAAY,KAAK3D,OAAA,CAAQE,EAAA,GAAKyD,QAAA,GAAW,MACzCE,WAAA,IAAe,KAAK7D,OAAA,CAAQG,KAAA,GAAQ0D,WAAA,GAAc,IACnD;MACT,OAAa;QACL,IAAIF,QAAA,IAAY,KAAK3D,OAAA,CAAQE,EAAA,EAAI;UAC/B8B,IAAA,CAAKmB,iBAAA,CAAkB,IAAI;UAC3BQ,QAAA,CAASpC,MAAA,CAAQ;QAClB;QAED,IAAIsC,WAAA,IAAe,KAAK7D,OAAA,CAAQG,KAAA,EAAO;UACrC0D,WAAA,CAAYtC,MAAA,CAAQ;QACrB;MACF;IACF;IAED,IAAIiD,MAAA,KAAW,QAAQ,KAAKxE,OAAA,CAAQI,OAAA,EAAS;MAC3C,IAAIA,OAAA,IAAW,KAAKT,aAAA,CAAcG,kBAAA,EAAoBM,OAAA,CAAQgG,KAAA,CAAO;MAErE3G,OAAA,CAAQ+E,MAAA,GAAS;IAClB;IAED,IAAIpE,OAAA,IAAW,KAAKJ,OAAA,CAAQI,OAAA,IAAW,CAAC,KAAKG,aAAA,EAAe;MAC1D,KAAKD,eAAA,CAAgB0B,IAAI;MACzB5B,OAAA,CAAQmB,MAAA,CAAOC,KAAK;IACrB;EACF;EAAA;EAAA;EAAA;EAKDiC,mBAAmB+C,aAAA,EAAe;IAChC,OAAOA,aAAA,CAAcC,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MACxC,IAAID,CAAA,CAAEE,mBAAA,KAAwBD,CAAA,CAAEC,mBAAA,EAAqB;QACnD,OAAOF,CAAA,CAAEE,mBAAA,GAAsBD,CAAA,CAAEC,mBAAA;MACzC,OAAa;QACL,OAAOF,CAAA,CAAEG,KAAA,GAAQF,CAAA,CAAEE,KAAA;MACpB;IACP,CAAK;EACF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EASD9C,gBAAgB/B,IAAA,EAAMwB,eAAA,EAAiBG,QAAA,EAAUE,WAAA,EAAa;IAC5DiD,gBAAA,GAAmB;IACnBC,eAAA,CAAgBC,KAAA,CAAO;IAEvB,SAAStF,CAAA,GAAI,GAAGa,EAAA,GAAKiB,eAAA,CAAgB7B,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACxDuF,SAAA,CAAUjF,IAAA,EAAMwB,eAAA,CAAgB9B,CAAC,EAAEmF,KAAA,EAAOlD,QAAA,EAAUE,WAAW;IAChE;IAED7B,IAAA,CAAKmB,iBAAA,CAAkB,IAAI;IAC3B,OAAO;EACR;EAEDrB,eAAe3C,MAAA,EAAQqC,KAAA,EAAO;IAC5B,MAAM+D,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAIhG,MAAM,EAAEoG,KAAA;IAEvC,IAAIA,KAAA,IAAS,KAAKvF,OAAA,CAAQK,eAAA,EAAiB;MACzCkF,KAAA,CAAMhE,MAAA,CAAOC,KAAK;MAElBrC,MAAA,CAAO+H,sBAAA,CAAwB;MAE/B/H,MAAA,CAAOgI,EAAA,CAAG5C,GAAA,CAAI,GAAG,GAAG,CAAC;MACrBpF,MAAA,CAAOgI,EAAA,CAAGC,eAAA,CAAgBjI,MAAA,CAAOuD,UAAU;MAC3CvD,MAAA,CAAOkI,MAAA,CAAO,KAAKjI,YAAA,CAAa2D,QAAQ;IACzC;EACF;EAEDmB,YAAYlC,IAAA,EAAMsF,cAAA,EAAgB;IAChC,MAAMC,GAAA,GAAMvF,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIiE,GAAA;IACvC,MAAMpF,KAAA,GAAQH,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAInB,KAAA;IAEzC,SAAST,CAAA,GAAI,GAAGa,EAAA,GAAKgF,GAAA,CAAI5F,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC5C,MAAMxB,EAAA,GAAKqH,GAAA,CAAI7F,CAAC;MAChB,MAAM8F,KAAA,GAAQtH,EAAA,CAAGsH,KAAA;MAEjB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKF,KAAA,CAAM7F,MAAA,EAAQ8F,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9C,MAAME,IAAA,GAAOH,KAAA,CAAMC,CAAC;QAEpB,IAAIH,cAAA,KAAmB,MAAM;UAG3BK,IAAA,CAAK3H,OAAA,GAAUmC,KAAA,CAAMwF,IAAA,CAAKd,KAAK,EAAEe,aAAA,GAAgB,IAAI,QAAQ;QACvE,OAAe;UACLD,IAAA,CAAK3H,OAAA,GAAU;QAChB;MACF;IACF;EACF;EAED4D,mBAAmB5B,IAAA,EAAM;IACvB,IAAI6F,WAAA,KAAgB,QAAW;MAC7B,MAAM,IAAI5G,KAAA,CAAM,+CAA+C;IAChE;IAED,OAAO,IAAI4G,WAAA,CAAY7F,IAAA,EAAMA,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIiE,GAAG;EAC5D;EAEDrB,kBAAkBlE,IAAA,EAAM/C,MAAA,EAAQ;IAC9B,IAAI6I,UAAA,KAAe,QAAW;MAC5B,MAAM,IAAI7G,KAAA,CAAM,sCAAsC;IACvD;IAED,OAAO,IAAI6G,UAAA,CAAW9F,IAAA,EAAMA,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAIyE,WAAA,EAAa/F,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAI0E,WAAA,EAAa/I,MAAM;EACnH;EAAA;AAAA;AAAA;AAAA;EAMDiC,cAAA,EAAgB;IACd,IAAI+G,GAAA,GAAM;IAEV,MAAMxI,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMP,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMC,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMK,YAAA,GAAe,KAAKA,YAAA;IAI1B,SAASkC,CAAA,GAAI,GAAGa,EAAA,GAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC/C,MAAM6D,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAIjG,MAAA,CAAOwC,CAAC,CAAC,EAAE6D,KAAA;MAE1C,IAAIA,KAAA,KAAU,QAAW;MAEzB,SAASkC,CAAA,GAAI,GAAGA,CAAA,GAAIlC,KAAA,CAAM2C,QAAA,CAASvG,MAAA,EAAQ8F,CAAA,IAAK;QAC9C,MAAMU,IAAA,GAAO5C,KAAA,CAAM2C,QAAA,CAAST,CAAC,EAAE1B,KAAA;QAE/B,IAAI,CAACtG,OAAA,CAAQ2I,GAAA,CAAID,IAAI,GAAG;UACtB1I,OAAA,CAAQ8E,GAAA,CAAI4D,IAAA,EAAM;YAChBpD,QAAA,EAAUoD,IAAA,CAAKpD;UAC3B,CAAW;QACF;QAEDkD,GAAA,GAAMI,IAAA,CAAKJ,GAAA,CAAIA,GAAA,EAAKxI,OAAA,CAAQ0F,GAAA,CAAIgD,IAAI,EAAEpD,QAAQ;MAC/C;IACF;IAED,IAAI5F,MAAA,KAAW,MAAM;MACnB,MAAMoG,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAIhG,MAAM,EAAEoG,KAAA;MAEvC,IAAIA,KAAA,KAAU,QAAW;QACvB,SAAS7D,CAAA,GAAI,GAAGa,EAAA,GAAKgD,KAAA,CAAM2C,QAAA,CAASvG,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;UACvD,MAAMyG,IAAA,GAAO5C,KAAA,CAAM2C,QAAA,CAASxG,CAAC,EAAEqE,KAAA;UAE/B,IAAI,CAACtG,OAAA,CAAQ2I,GAAA,CAAID,IAAI,GAAG;YACtB1I,OAAA,CAAQ8E,GAAA,CAAI4D,IAAA,EAAM;cAChBpD,QAAA,EAAUoD,IAAA,CAAKpD;YAC7B,CAAa;UACF;UAEDkD,GAAA,GAAMI,IAAA,CAAKJ,GAAA,CAAIA,GAAA,EAAKxI,OAAA,CAAQ0F,GAAA,CAAIgD,IAAI,EAAEpD,QAAQ;QAC/C;MACF;IACF;IAED,IAAIvF,YAAA,KAAiB,MAAM;MACzByI,GAAA,GAAMI,IAAA,CAAKJ,GAAA,CAAIA,GAAA,EAAKxI,OAAA,CAAQ0F,GAAA,CAAI3F,YAAY,EAAEuF,QAAQ;IACvD;IAEDkD,GAAA,IAAO,KAAKtI,aAAA,CAAcE,SAAA;IAI1B,SAAS6B,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACpD,MAAM6D,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAI,KAAKjG,MAAA,CAAOwC,CAAC,CAAC,EAAE6D,KAAA;MAE/C,IAAIA,KAAA,KAAU,QAAW;MAEzB,SAASkC,CAAA,GAAI,GAAGC,EAAA,GAAKnC,KAAA,CAAM2C,QAAA,CAASvG,MAAA,EAAQ8F,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACvDlC,KAAA,CAAM2C,QAAA,CAAST,CAAC,EAAE1B,KAAA,CAAMhB,QAAA,GAAWkD,GAAA;MACpC;IACF;IAED,IAAI9I,MAAA,KAAW,MAAM;MACnB,MAAMoG,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAIhG,MAAM,EAAEoG,KAAA;MAEvC,IAAIA,KAAA,KAAU,QAAW;QACvB,SAAS7D,CAAA,GAAI,GAAGa,EAAA,GAAKgD,KAAA,CAAM2C,QAAA,CAASvG,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;UACvD6D,KAAA,CAAM2C,QAAA,CAASxG,CAAC,EAAEqE,KAAA,CAAMhB,QAAA,GAAWkD,GAAA;QACpC;MACF;IACF;IAED,IAAIzI,YAAA,KAAiB,MAAM;MACzBA,YAAA,CAAauF,QAAA,GAAWkD,GAAA;IACzB;EACF;EAAA;EAIDK,4BAA4BtG,IAAA,EAAM;IAChC,MAAMuD,KAAA,GAAQ,KAAK9F,OAAA,CAAQ0F,GAAA,CAAInD,IAAI,EAAEuD,KAAA;IAErC,MAAMgD,cAAA,GAAiBhD,KAAA,CAAMiD,SAAA;IAC7B,MAAMC,SAAA,GAAYlD,KAAA,CAAMmD,UAAA;IAExB,SAAShH,CAAA,GAAI,GAAGa,EAAA,GAAKgG,cAAA,CAAe5G,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACvD,MAAMiH,aAAA,GAAgBJ,cAAA,CAAe7G,CAAC;MACtC,MAAMkH,MAAA,GAASD,aAAA,CAAcC,MAAA;MAC7B,MAAMC,MAAA,GAASF,aAAA,CAAcG,SAAA;MAC7B,MAAMC,MAAA,IAAUN,SAAA,GAAY,KAAKI,MAAA;MAEjCF,aAAA,CAAcK,OAAA,CAAQC,QAAA,CAASL,MAAA,EAAQG,MAAM;IAC9C;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWDxC,WAAWvE,IAAA,EAAM;IACf,MAAMvC,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAInD,IAAI;IAErC,MAAMG,KAAA,GAAQH,IAAA,CAAKI,QAAA,CAASD,KAAA;IAE5B,IAAI+G,WAAA,GAAczJ,OAAA,CAAQyJ,WAAA;IAE1B,IAAIA,WAAA,KAAgB,QAAW;MAC7BA,WAAA,GAAc,IAAIC,YAAA,CAAahH,KAAA,CAAMR,MAAA,GAAS,CAAC;MAC/ClC,OAAA,CAAQyJ,WAAA,GAAcA,WAAA;IACvB;IAED,SAASxH,CAAA,GAAI,GAAGa,EAAA,GAAKJ,KAAA,CAAMR,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC9C,MAAMoB,IAAA,GAAOX,KAAA,CAAMT,CAAC;MACpBoB,IAAA,CAAKC,QAAA,CAASqG,OAAA,CAAQF,WAAA,EAAaxH,CAAA,GAAI,CAAC;MACxCoB,IAAA,CAAKJ,UAAA,CAAW0G,OAAA,CAAQF,WAAA,EAAaxH,CAAA,GAAI,IAAI,CAAC;IAC/C;EACF;EAED4E,cAActE,IAAA,EAAM;IAClB,MAAMvC,OAAA,GAAU,KAAKA,OAAA,CAAQ0F,GAAA,CAAInD,IAAI;IAErC,MAAMkH,WAAA,GAAczJ,OAAA,CAAQyJ,WAAA;IAE5B,IAAIA,WAAA,KAAgB,QAAW;IAE/B,MAAM/G,KAAA,GAAQH,IAAA,CAAKI,QAAA,CAASD,KAAA;IAE5B,SAAST,CAAA,GAAI,GAAGa,EAAA,GAAKJ,KAAA,CAAMR,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC9C,MAAMoB,IAAA,GAAOX,KAAA,CAAMT,CAAC;MACpBoB,IAAA,CAAKC,QAAA,CAASC,SAAA,CAAUkG,WAAA,EAAaxH,CAAA,GAAI,CAAC;MAC1CoB,IAAA,CAAKJ,UAAA,CAAWM,SAAA,CAAUkG,WAAA,EAAaxH,CAAA,GAAI,IAAI,CAAC;IACjD;EACF;EAAA;EAIDuE,kBAAA,EAAoB;IAClB,IAAI,KAAKzF,aAAA,KAAkB,MAAM,OAAO,KAAKA,aAAA;IAE7C,SAASkB,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACpD,MAAMtB,OAAA,GAAU,KAAKlB,MAAA,CAAOwC,CAAC,EAAEtB,OAAA;MAE/B,IAAIA,OAAA,KAAY,UAAaA,OAAA,KAAY,MAAM;QAC7C,KAAKI,aAAA,GAAgBJ,OAAA;QACrB,OAAO,KAAKI,aAAA;MACb;IACF;IAED,OAAO;EACR;EAEDqB,qBAAqBL,KAAA,EAAO;IAC1B,IAAI,KAAKtC,MAAA,CAAOyC,MAAA,KAAW,KAAK,CAAC,KAAK3B,OAAA,CAAQI,OAAA,IAAW,CAAC,KAAKG,aAAA,EAAe;IAE9E,MAAMH,OAAA,GAAU,KAAK6F,iBAAA,CAAmB;IAExC,IAAI7F,OAAA,KAAY,MAAM;IAEtB,SAASsB,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACpD,MAAM2H,CAAA,GAAI,KAAKnK,MAAA,CAAOwC,CAAC,EAAEtB,OAAA;MAEzB,IAAIiJ,CAAA,KAAM,QAAQA,CAAA,KAAM,QAAW;QACjCA,CAAA,CAAEC,iBAAA,CAAmB;MACtB;IACF;IAEDlJ,OAAA,CAAQmJ,cAAA,CAAe/H,KAAK;IAE5B,SAASE,CAAA,GAAI,GAAGa,EAAA,GAAK,KAAKrD,MAAA,CAAOyC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MACpD,MAAM2H,CAAA,GAAI,KAAKnK,MAAA,CAAOwC,CAAC,EAAEtB,OAAA;MAEzB,IAAIiJ,CAAA,KAAM,QAAQA,CAAA,KAAM,QAAW;QACjCA,CAAA,CAAEG,WAAA,CAAa;MAChB;IACF;EACF;AACH;AAGA,MAAMC,YAAA,GAAe,EAAE;AACvB,IAAI3C,gBAAA,GAAmB;AAEvB,SAAS4C,cAAA,EAAgB;EACvB,IAAI5C,gBAAA,IAAoB2C,YAAA,CAAa9H,MAAA,EAAQ;IAC3C8H,YAAA,CAAanF,IAAA,CAAK,IAAI3B,UAAA,EAAY;EACnC;EAED,OAAO8G,YAAA,CAAa3C,gBAAA,EAAkB;AACxC;AAIA,MAAMC,eAAA,GAAkB,mBAAI4C,GAAA,CAAK;AAEjC,SAAS1C,UAAUjF,IAAA,EAAMa,SAAA,EAAWc,QAAA,EAAUE,WAAA,EAAa;EACzD,MAAM1B,KAAA,GAAQH,IAAA,CAAKI,QAAA,CAASD,KAAA;EAC5B,MAAMyH,SAAA,GAAY5H,IAAA,CAAKoB,QAAA,CAASC,QAAA,CAASC,GAAA,CAAInB,KAAA;EAC7C,MAAM0H,QAAA,GAAWD,SAAA,CAAU/G,SAAS;EACpC,MAAMC,IAAA,GAAOX,KAAA,CAAMU,SAAS;EAG5B,IAAIkE,eAAA,CAAgBqB,GAAA,CAAIvF,SAAS,GAAG;EAEpC,MAAMH,UAAA,GAAagH,aAAA,CAAe;EAMlC3C,eAAA,CAAgBxC,GAAA,CAAI1B,SAAA,EAAWH,UAAA,CAAWoH,IAAA,CAAKhH,IAAA,CAAKJ,UAAU,CAAC;EAG/D,IAAImB,WAAA,IAAegG,QAAA,CAAS1J,KAAA,IAAS,CAAC0J,QAAA,CAAS1J,KAAA,CAAM4J,OAAA,IAAWF,QAAA,CAAS1J,KAAA,CAAM6J,cAAA,EAAgB;IAC7F,MAAMC,WAAA,GAAcJ,QAAA,CAAS1J,KAAA,CAAM8J,WAAA;IACnC,MAAMC,KAAA,GAAQL,QAAA,CAAS1J,KAAA,CAAM+J,KAAA;IAE7B,IAAI,CAACnD,eAAA,CAAgBqB,GAAA,CAAI6B,WAAW,GAAG;MACrChD,SAAA,CAAUjF,IAAA,EAAMiI,WAAA,EAAatG,QAAA,EAAUE,WAAW;IACnD;IAEDA,WAAA,CAAYsG,gBAAA,CAAiBrH,IAAA,EAAMiE,eAAA,CAAgB5B,GAAA,CAAI8E,WAAW,GAAGC,KAAK;EAC3E;EAED,IAAIvG,QAAA,IAAYkG,QAAA,CAAS3J,EAAA,EAAI;IAG3B8B,IAAA,CAAKmB,iBAAA,CAAkB,IAAI;IAC3BQ,QAAA,CAASsD,SAAA,CAAU4C,QAAA,CAAS3J,EAAE;IAG9B,MAAMsH,KAAA,GAAQqC,QAAA,CAAS3J,EAAA,CAAGsH,KAAA;IAE1B,SAAS9F,CAAA,GAAI,GAAGa,EAAA,GAAKiF,KAAA,CAAM7F,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC9C,MAAMiG,IAAA,GAAOH,KAAA,CAAM9F,CAAC;MAEpB,IAAIiG,IAAA,CAAK3H,OAAA,KAAY,OAAO;MAE5B,MAAMoK,SAAA,GAAYzC,IAAA,CAAKd,KAAA;MAEvB,IAAIE,eAAA,CAAgBqB,GAAA,CAAIgC,SAAS,GAAG;QAClCrD,eAAA,CAAgBxC,GAAA,CAAI6F,SAAA,EAAWrD,eAAA,CAAgB5B,GAAA,CAAIiF,SAAS,EAAEN,IAAA,CAAK3H,KAAA,CAAMiI,SAAS,EAAE1H,UAAU,CAAC;MAChG;IACF;EACF;EAGDA,UAAA,CAAWoH,IAAA,CAAKhH,IAAA,CAAKJ,UAAU;AACjC;AAIA,MAAMoC,YAAA,CAAa;EAAA;AAAA;AAAA;AAAA;AAAA;EAMjB9F,YAAYO,KAAA,EAAON,MAAA,GAAS,IAAI;IAC9B,KAAKM,KAAA,GAAQA,KAAA;IAEb,KAAK8K,WAAA,GAAc;IACnB,KAAKC,WAAA,GAAc;IACnB,KAAKC,SAAA,GAAYtL,MAAA,CAAOsL,SAAA,KAAc,SAAYtL,MAAA,CAAOsL,SAAA,GAAY;IAErE,KAAKC,aAAA,GAAgB,KAAKjL,KAAA,CAAMqJ,MAAA,CAAO7D,QAAA;IACvC,KAAKA,QAAA,GAAW,KAAKyF,aAAA,GAAgB,KAAKD,SAAA;EAC3C;EAAA;AAAA;AAAA;AAAA;EAMD9I,QAAQD,KAAA,EAAO;IACb,KAAKiJ,OAAA,IAAWjJ,KAAA;IAChB,KAAK8I,WAAA,IAAe9I,KAAA;IAEpB,IAAI,KAAKkJ,gBAAA,CAAgB,GAAI,KAAKnL,KAAA,CAAMoL,IAAA,CAAM;IAC9C,IAAI,KAAKC,iBAAA,CAAiB,GAAI,KAAKrL,KAAA,CAAMmG,IAAA,CAAM;IAE/C,OAAO;EACR;EAAA;EAIDkF,kBAAA,EAAoB;IAClB,IAAI,KAAKrL,KAAA,CAAMsL,SAAA,EAAW,OAAO;IAEjC,OAAO,KAAKP,WAAA,IAAe,KAAKvF,QAAA,EAAU;MACxC,KAAKuF,WAAA,IAAe,KAAKvF,QAAA;IAC1B;IAED,IAAI,KAAKuF,WAAA,GAAc,KAAKC,SAAA,EAAW,OAAO;IAG9C,IAAI,KAAKD,WAAA,GAAc,KAAKC,SAAA,GAAY,KAAKC,aAAA,EAAe,OAAO;IAEnE,OAAO;EACR;EAEDE,iBAAA,EAAmB;IACjB,OAAO,KAAKnL,KAAA,CAAMsL,SAAA,IAAa,KAAKP,WAAA,IAAe,KAAKvF,QAAA;EACzD;AACH;AAEA,MAAM+F,EAAA,GAAqB,mBAAInI,UAAA,CAAY;AAU3C,MAAMwB,WAAA,CAAY;EAChBnF,YAAYgD,IAAA,EAAMoC,MAAA,GAAS,IAAI;IAC7B,KAAKpC,IAAA,GAAOA,IAAA;IACZ,KAAKoC,MAAA,GAASA,MAAA;EACf;EAAA;AAAA;AAAA;AAAA;EAMD7C,OAAA,EAAS;IACP,MAAM6C,MAAA,GAAS,KAAKA,MAAA;IAEpB,SAAS1C,CAAA,GAAI,GAAGa,EAAA,GAAK6B,MAAA,CAAOzC,MAAA,EAAQD,CAAA,GAAIa,EAAA,EAAIb,CAAA,IAAK;MAC/C,KAAKuF,SAAA,CAAU7C,MAAA,CAAO1C,CAAC,CAAC;IACzB;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODuF,UAAU9G,KAAA,EAAO;IACf,MAAMgC,KAAA,GAAQ,KAAKH,IAAA,CAAKI,QAAA,CAASD,KAAA;IACjC,MAAMW,IAAA,GAAOX,KAAA,CAAMhC,KAAA,CAAM0G,KAAK;IAC9B,MAAMkE,UAAA,GAAa5I,KAAA,CAAMhC,KAAA,CAAM8J,WAAW;IAE1C,IAAI9J,KAAA,CAAM4J,OAAA,EAAS;MAEjB,IAAI5J,KAAA,CAAM6K,cAAA,EAAgB;MAI1B,IAAI7K,KAAA,CAAM6J,cAAA,EAAgB;IAEhC,OAAW;MAEL,IAAI7J,KAAA,CAAM6K,cAAA,EAAgB;MAG1B,IAAI7K,KAAA,CAAM6J,cAAA,EAAgB;QACxB,KAAKG,gBAAA,CAAiBrH,IAAA,EAAMiI,UAAA,CAAWrI,UAAA,EAAYvC,KAAA,CAAM+J,KAAK;MAC/D;IACF;IAED,OAAO;EACR;EAEDC,iBAAiBrH,IAAA,EAAMmI,CAAA,EAAGf,KAAA,EAAO;IAC/BY,EAAA,CAAGvG,GAAA,CAAI,GAAG,GAAG,GAAG,CAAC;IACjBuG,EAAA,CAAGI,KAAA,CAAMD,CAAA,EAAGf,KAAK;IACjBpH,IAAA,CAAKJ,UAAA,CAAWQ,QAAA,CAAS4H,EAAE;IAE3B,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}