{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, Vector4, Vector2, Color } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { LineSegments2, Line2, LineMaterial, LineSegmentsGeometry, LineGeometry } from 'three-stdlib';\nconst Line = /* @__PURE__ */React.forwardRef(function Line({\n  points,\n  color = 0xffffff,\n  vertexColors,\n  linewidth,\n  lineWidth,\n  segments,\n  dashed,\n  ...rest\n}, ref) {\n  var _vertexColors$, _ref;\n  const size = useThree(state => state.size);\n  const line2 = React.useMemo(() => segments ? new LineSegments2() : new Line2(), [segments]);\n  const [lineMaterial] = React.useState(() => new LineMaterial());\n  const itemSize = (vertexColors == null || (_vertexColors$ = vertexColors[0]) == null ? void 0 : _vertexColors$.length) === 4 ? 4 : 3;\n  const lineGeom = React.useMemo(() => {\n    const geom = segments ? new LineSegmentsGeometry() : new LineGeometry();\n    const pValues = points.map(p => {\n      const isArray = Array.isArray(p);\n      return p instanceof Vector3 || p instanceof Vector4 ? [p.x, p.y, p.z] : p instanceof Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n    });\n    geom.setPositions(pValues.flat());\n    if (vertexColors) {\n      // using vertexColors requires the color value to be white see #1813\n      color = 0xffffff;\n      const cValues = vertexColors.map(c => c instanceof Color ? c.toArray() : c);\n      geom.setColors(cValues.flat(), itemSize);\n    }\n    return geom;\n  }, [points, segments, vertexColors, itemSize]);\n  React.useLayoutEffect(() => {\n    line2.computeLineDistances();\n  }, [points, line2]);\n  React.useLayoutEffect(() => {\n    if (dashed) {\n      lineMaterial.defines.USE_DASH = '';\n    } else {\n      // Setting lineMaterial.defines.USE_DASH to undefined is apparently not sufficient.\n      delete lineMaterial.defines.USE_DASH;\n    }\n    lineMaterial.needsUpdate = true;\n  }, [dashed, lineMaterial]);\n  React.useEffect(() => {\n    return () => {\n      lineGeom.dispose();\n      lineMaterial.dispose();\n    };\n  }, [lineGeom]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: line2,\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"primitive\", {\n    object: lineGeom,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: lineMaterial,\n    attach: \"material\",\n    color: color,\n    vertexColors: Boolean(vertexColors),\n    resolution: [size.width, size.height],\n    linewidth: (_ref = linewidth !== null && linewidth !== void 0 ? linewidth : lineWidth) !== null && _ref !== void 0 ? _ref : 1,\n    dashed: dashed,\n    transparent: itemSize === 4\n  }, rest)));\n});\nexport { Line };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "Vector4", "Vector2", "Color", "useThree", "LineSegments2", "Line2", "LineMaterial", "LineSegmentsGeometry", "LineGeometry", "Line", "forwardRef", "points", "color", "vertexColors", "linewidth", "lineWidth", "segments", "dashed", "rest", "ref", "_vertexColors$", "_ref", "size", "state", "line2", "useMemo", "lineMaterial", "useState", "itemSize", "length", "lineGeom", "geom", "p<PERSON><PERSON><PERSON>", "map", "p", "isArray", "Array", "x", "y", "z", "setPositions", "flat", "c<PERSON><PERSON><PERSON>", "c", "toArray", "setColors", "useLayoutEffect", "computeLineDistances", "defines", "USE_DASH", "needsUpdate", "useEffect", "dispose", "createElement", "object", "attach", "Boolean", "resolution", "width", "height", "transparent"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Line.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, Vector4, Vector2, Color } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { LineSegments2, Line2, LineMaterial, LineSegmentsGeometry, LineGeometry } from 'three-stdlib';\n\nconst Line = /* @__PURE__ */React.forwardRef(function Line({\n  points,\n  color = 0xffffff,\n  vertexColors,\n  linewidth,\n  lineWidth,\n  segments,\n  dashed,\n  ...rest\n}, ref) {\n  var _vertexColors$, _ref;\n  const size = useThree(state => state.size);\n  const line2 = React.useMemo(() => segments ? new LineSegments2() : new Line2(), [segments]);\n  const [lineMaterial] = React.useState(() => new LineMaterial());\n  const itemSize = (vertexColors == null || (_vertexColors$ = vertexColors[0]) == null ? void 0 : _vertexColors$.length) === 4 ? 4 : 3;\n  const lineGeom = React.useMemo(() => {\n    const geom = segments ? new LineSegmentsGeometry() : new LineGeometry();\n    const pValues = points.map(p => {\n      const isArray = Array.isArray(p);\n      return p instanceof Vector3 || p instanceof Vector4 ? [p.x, p.y, p.z] : p instanceof Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n    });\n    geom.setPositions(pValues.flat());\n    if (vertexColors) {\n      // using vertexColors requires the color value to be white see #1813\n      color = 0xffffff;\n      const cValues = vertexColors.map(c => c instanceof Color ? c.toArray() : c);\n      geom.setColors(cValues.flat(), itemSize);\n    }\n    return geom;\n  }, [points, segments, vertexColors, itemSize]);\n  React.useLayoutEffect(() => {\n    line2.computeLineDistances();\n  }, [points, line2]);\n  React.useLayoutEffect(() => {\n    if (dashed) {\n      lineMaterial.defines.USE_DASH = '';\n    } else {\n      // Setting lineMaterial.defines.USE_DASH to undefined is apparently not sufficient.\n      delete lineMaterial.defines.USE_DASH;\n    }\n    lineMaterial.needsUpdate = true;\n  }, [dashed, lineMaterial]);\n  React.useEffect(() => {\n    return () => {\n      lineGeom.dispose();\n      lineMaterial.dispose();\n    };\n  }, [lineGeom]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: line2,\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"primitive\", {\n    object: lineGeom,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: lineMaterial,\n    attach: \"material\",\n    color: color,\n    vertexColors: Boolean(vertexColors),\n    resolution: [size.width, size.height],\n    linewidth: (_ref = linewidth !== null && linewidth !== void 0 ? linewidth : lineWidth) !== null && _ref !== void 0 ? _ref : 1,\n    dashed: dashed,\n    transparent: itemSize === 4\n  }, rest)));\n});\n\nexport { Line };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,OAAO;AACxD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,aAAa,EAAEC,KAAK,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,YAAY,QAAQ,cAAc;AAErG,MAAMC,IAAI,GAAG,eAAeX,KAAK,CAACY,UAAU,CAAC,SAASD,IAAIA,CAAC;EACzDE,MAAM;EACNC,KAAK,GAAG,QAAQ;EAChBC,YAAY;EACZC,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACN,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,IAAIC,cAAc,EAAEC,IAAI;EACxB,MAAMC,IAAI,GAAGnB,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,KAAK,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,MAAMT,QAAQ,GAAG,IAAIZ,aAAa,CAAC,CAAC,GAAG,IAAIC,KAAK,CAAC,CAAC,EAAE,CAACW,QAAQ,CAAC,CAAC;EAC3F,MAAM,CAACU,YAAY,CAAC,GAAG5B,KAAK,CAAC6B,QAAQ,CAAC,MAAM,IAAIrB,YAAY,CAAC,CAAC,CAAC;EAC/D,MAAMsB,QAAQ,GAAG,CAACf,YAAY,IAAI,IAAI,IAAI,CAACO,cAAc,GAAGP,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,cAAc,CAACS,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;EACpI,MAAMC,QAAQ,GAAGhC,KAAK,CAAC2B,OAAO,CAAC,MAAM;IACnC,MAAMM,IAAI,GAAGf,QAAQ,GAAG,IAAIT,oBAAoB,CAAC,CAAC,GAAG,IAAIC,YAAY,CAAC,CAAC;IACvE,MAAMwB,OAAO,GAAGrB,MAAM,CAACsB,GAAG,CAACC,CAAC,IAAI;MAC9B,MAAMC,OAAO,GAAGC,KAAK,CAACD,OAAO,CAACD,CAAC,CAAC;MAChC,OAAOA,CAAC,YAAYnC,OAAO,IAAImC,CAAC,YAAYlC,OAAO,GAAG,CAACkC,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,CAAC,GAAGL,CAAC,YAAYjC,OAAO,GAAG,CAACiC,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAE,CAAC,CAAC,GAAGH,OAAO,IAAID,CAAC,CAACL,MAAM,KAAK,CAAC,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,OAAO,IAAID,CAAC,CAACL,MAAM,KAAK,CAAC,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC;IACjN,CAAC,CAAC;IACFH,IAAI,CAACS,YAAY,CAACR,OAAO,CAACS,IAAI,CAAC,CAAC,CAAC;IACjC,IAAI5B,YAAY,EAAE;MAChB;MACAD,KAAK,GAAG,QAAQ;MAChB,MAAM8B,OAAO,GAAG7B,YAAY,CAACoB,GAAG,CAACU,CAAC,IAAIA,CAAC,YAAYzC,KAAK,GAAGyC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGD,CAAC,CAAC;MAC3EZ,IAAI,CAACc,SAAS,CAACH,OAAO,CAACD,IAAI,CAAC,CAAC,EAAEb,QAAQ,CAAC;IAC1C;IACA,OAAOG,IAAI;EACb,CAAC,EAAE,CAACpB,MAAM,EAAEK,QAAQ,EAAEH,YAAY,EAAEe,QAAQ,CAAC,CAAC;EAC9C9B,KAAK,CAACgD,eAAe,CAAC,MAAM;IAC1BtB,KAAK,CAACuB,oBAAoB,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACpC,MAAM,EAAEa,KAAK,CAAC,CAAC;EACnB1B,KAAK,CAACgD,eAAe,CAAC,MAAM;IAC1B,IAAI7B,MAAM,EAAE;MACVS,YAAY,CAACsB,OAAO,CAACC,QAAQ,GAAG,EAAE;IACpC,CAAC,MAAM;MACL;MACA,OAAOvB,YAAY,CAACsB,OAAO,CAACC,QAAQ;IACtC;IACAvB,YAAY,CAACwB,WAAW,GAAG,IAAI;EACjC,CAAC,EAAE,CAACjC,MAAM,EAAES,YAAY,CAAC,CAAC;EAC1B5B,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXrB,QAAQ,CAACsB,OAAO,CAAC,CAAC;MAClB1B,YAAY,CAAC0B,OAAO,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,CAAC,CAAC;EACd,OAAO,aAAahC,KAAK,CAACuD,aAAa,CAAC,WAAW,EAAExD,QAAQ,CAAC;IAC5DyD,MAAM,EAAE9B,KAAK;IACbL,GAAG,EAAEA;EACP,CAAC,EAAED,IAAI,CAAC,EAAE,aAAapB,KAAK,CAACuD,aAAa,CAAC,WAAW,EAAE;IACtDC,MAAM,EAAExB,QAAQ;IAChByB,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAazD,KAAK,CAACuD,aAAa,CAAC,WAAW,EAAExD,QAAQ,CAAC;IACzDyD,MAAM,EAAE5B,YAAY;IACpB6B,MAAM,EAAE,UAAU;IAClB3C,KAAK,EAAEA,KAAK;IACZC,YAAY,EAAE2C,OAAO,CAAC3C,YAAY,CAAC;IACnC4C,UAAU,EAAE,CAACnC,IAAI,CAACoC,KAAK,EAAEpC,IAAI,CAACqC,MAAM,CAAC;IACrC7C,SAAS,EAAE,CAACO,IAAI,GAAGP,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGC,SAAS,MAAM,IAAI,IAAIM,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC;IAC7HJ,MAAM,EAAEA,MAAM;IACd2C,WAAW,EAAEhC,QAAQ,KAAK;EAC5B,CAAC,EAAEV,IAAI,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAAST,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}