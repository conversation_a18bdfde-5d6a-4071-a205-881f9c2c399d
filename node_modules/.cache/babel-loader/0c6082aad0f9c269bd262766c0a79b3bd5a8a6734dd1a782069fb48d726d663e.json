{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { forwardRef, useRef, useState, useCallback, useMemo, useImperativeHandle, useEffect, Suspense, useContext, createContext } from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { easing } from 'maath';\nimport { VideoTexture } from '../core/VideoTexture.js';\nimport { WebcamVideoTexture } from './WebcamVideoTexture.js';\nimport { Facemesh } from './Facemesh.js';\nimport { useFaceLandmarker } from './FaceLandmarker.js';\nfunction mean(v1, v2) {\n  return v1.clone().add(v2).multiplyScalar(0.5);\n}\nfunction localToLocal(objSrc, v, objDst) {\n  // see: https://discourse.threejs.org/t/object3d-localtolocal/51564\n  const v_world = objSrc.localToWorld(v);\n  return objDst.worldToLocal(v_world);\n}\n\n//\n//\n//\n\nconst FaceControlsContext = /* @__PURE__ */createContext({});\n\n/**\n * The camera follows your face.\n *\n * Pre-requisite: wrap into a `FaceLandmarker` provider:\n *\n * ```jsx\n * <FaceLandmarker>...</FaceLandmarker>\n * ```\n */\n\nconst FaceControls = /* @__PURE__ */forwardRef(({\n  camera,\n  videoTexture = {\n    start: true\n  },\n  manualDetect = false,\n  faceLandmarkerResult,\n  manualUpdate = false,\n  makeDefault,\n  smoothTime = 0.25,\n  offset = true,\n  offsetScalar = 80,\n  eyes = false,\n  eyesAsOrigin = true,\n  depth = 0.15,\n  debug = false,\n  facemesh\n}, fref) => {\n  var _result$facialTransfo, _result$faceBlendshap;\n  const scene = useThree(state => state.scene);\n  const defaultCamera = useThree(state => state.camera);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const explCamera = camera || defaultCamera;\n  const facemeshApiRef = useRef(null);\n\n  //\n  // computeTarget()\n  //\n  // Compute `target` position and rotation for the camera (according to <Facemesh>)\n  //\n  //  1. 👀 either following the 2 eyes\n  //  2. 👤 or just the head mesh\n  //\n\n  const [target] = useState(() => new THREE.Object3D());\n  const [irisRightDirPos] = useState(() => new THREE.Vector3());\n  const [irisLeftDirPos] = useState(() => new THREE.Vector3());\n  const [irisRightLookAt] = useState(() => new THREE.Vector3());\n  const [irisLeftLookAt] = useState(() => new THREE.Vector3());\n  const computeTarget = useCallback(() => {\n    // same parent as the camera\n    target.parent = explCamera.parent;\n    const facemeshApi = facemeshApiRef.current;\n    if (facemeshApi) {\n      const {\n        outerRef,\n        eyeRightRef,\n        eyeLeftRef\n      } = facemeshApi;\n      if (eyeRightRef.current && eyeLeftRef.current) {\n        // 1. 👀\n\n        const {\n          irisDirRef: irisRightDirRef\n        } = eyeRightRef.current;\n        const {\n          irisDirRef: irisLeftDirRef\n        } = eyeLeftRef.current;\n        if (irisRightDirRef.current && irisLeftDirRef.current && outerRef.current) {\n          //\n          // position: mean of irisRightDirPos,irisLeftDirPos\n          //\n          irisRightDirPos.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          irisLeftDirPos.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          target.position.copy(localToLocal(outerRef.current, mean(irisRightDirPos, irisLeftDirPos), explCamera.parent || scene));\n\n          //\n          // lookAt: mean of irisRightLookAt,irisLeftLookAt\n          //\n          irisRightLookAt.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          irisLeftLookAt.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          target.lookAt(outerRef.current.localToWorld(mean(irisRightLookAt, irisLeftLookAt)));\n        }\n      } else {\n        // 2. 👤\n\n        if (outerRef.current) {\n          target.position.copy(localToLocal(outerRef.current, new THREE.Vector3(0, 0, 0), explCamera.parent || scene));\n          target.lookAt(outerRef.current.localToWorld(new THREE.Vector3(0, 0, 1)));\n        }\n      }\n    }\n    return target;\n  }, [explCamera, irisLeftDirPos, irisLeftLookAt, irisRightDirPos, irisRightLookAt, scene, target]);\n\n  //\n  // update()\n  //\n  // Updating the camera `current` position and rotation, following `target`\n  //\n\n  const [current] = useState(() => new THREE.Object3D());\n  const update = useCallback(function (delta, target) {\n    if (explCamera) {\n      var _target;\n      (_target = target) !== null && _target !== void 0 ? _target : target = computeTarget();\n      if (smoothTime > 0) {\n        // damping current\n        const eps = 1e-9;\n        easing.damp3(current.position, target.position, smoothTime, delta, undefined, undefined, eps);\n        easing.dampE(current.rotation, target.rotation, smoothTime, delta, undefined, undefined, eps);\n      } else {\n        // instant\n        current.position.copy(target.position);\n        current.rotation.copy(target.rotation);\n      }\n      explCamera.position.copy(current.position);\n      explCamera.rotation.copy(current.rotation);\n    }\n  }, [explCamera, computeTarget, smoothTime, current.position, current.rotation]);\n  useFrame((_, delta) => {\n    if (manualUpdate) return;\n    update(delta);\n  });\n\n  //\n  // onVideoFrame (only used if !manualDetect)\n  //\n\n  const videoTextureRef = useRef(null);\n  const [_faceLandmarkerResult, setFaceLandmarkerResult] = useState();\n  const faceLandmarker = useFaceLandmarker();\n  const onVideoFrame = useCallback((now, metadata) => {\n    const texture = videoTextureRef.current;\n    if (!texture) return;\n    const videoFrame = texture.source.data;\n    const result = faceLandmarker == null ? void 0 : faceLandmarker.detectForVideo(videoFrame, now);\n    setFaceLandmarkerResult(result);\n  }, [faceLandmarker]);\n\n  //\n  // Ref API\n  //\n\n  const api = useMemo(() => Object.assign(Object.create(THREE.EventDispatcher.prototype), {\n    computeTarget,\n    update,\n    facemeshApiRef\n  }), [computeTarget, update]);\n  useImperativeHandle(fref, () => api, [api]);\n\n  //\n  // makeDefault (`controls` global state)\n  //\n\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: api\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, api, get, set]);\n\n  //\n  //\n  //\n\n  const result = faceLandmarkerResult !== null && faceLandmarkerResult !== void 0 ? faceLandmarkerResult : _faceLandmarkerResult;\n  const points = result == null ? void 0 : result.faceLandmarks[0];\n  const facialTransformationMatrix = result == null || (_result$facialTransfo = result.facialTransformationMatrixes) == null ? void 0 : _result$facialTransfo[0];\n  const faceBlendshapes = result == null || (_result$faceBlendshap = result.faceBlendshapes) == null ? void 0 : _result$faceBlendshap[0];\n  const videoTextureProps = {\n    onVideoFrame,\n    ...videoTexture\n  };\n  return /*#__PURE__*/React.createElement(FaceControlsContext.Provider, {\n    value: api\n  }, !manualDetect && /*#__PURE__*/React.createElement(Suspense, {\n    fallback: null\n  }, 'src' in videoTextureProps ? /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps)) : /*#__PURE__*/React.createElement(WebcamVideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps))), /*#__PURE__*/React.createElement(Facemesh, _extends({\n    ref: facemeshApiRef,\n    children: /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n      side: THREE.DoubleSide\n    })\n  }, facemesh, {\n    points: points,\n    depth: depth,\n    facialTransformationMatrix: facialTransformationMatrix,\n    faceBlendshapes: faceBlendshapes,\n    eyes: eyes,\n    eyesAsOrigin: eyesAsOrigin,\n    offset: offset,\n    offsetScalar: offsetScalar,\n    debug: debug,\n    \"rotation-z\": Math.PI,\n    visible: debug\n  })));\n});\nconst useFaceControls = () => useContext(FaceControlsContext);\nexport { FaceControls, useFaceControls };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "forwardRef", "useRef", "useState", "useCallback", "useMemo", "useImperativeHandle", "useEffect", "Suspense", "useContext", "createContext", "useThree", "useFrame", "easing", "VideoTexture", "WebcamVideoTexture", "<PERSON><PERSON><PERSON>", "useFaceLandmarker", "mean", "v1", "v2", "clone", "add", "multiplyScalar", "localToLocal", "objSrc", "v", "objDst", "v_world", "localToWorld", "worldToLocal", "FaceControlsContext", "FaceControls", "camera", "videoTexture", "start", "manualDetect", "faceLandmarkerResult", "manualUpdate", "makeDefault", "smoothTime", "offset", "offsetScalar", "eyes", "eyes<PERSON><PERSON><PERSON><PERSON>", "depth", "debug", "facemesh", "fref", "_result$facialTransfo", "_result$faceBlendshap", "scene", "state", "defaultCamera", "set", "get", "explCamera", "facemeshApiRef", "target", "Object3D", "irisRightDirPos", "Vector3", "irisLeftDirPos", "irisRightLookAt", "irisLeftLookAt", "computeTarget", "parent", "facemeshApi", "current", "outerRef", "eyeRightRef", "eyeLeftRef", "irisDirRef", "irisRightDirRef", "irisLeftDirRef", "copy", "position", "lookAt", "update", "delta", "_target", "eps", "damp3", "undefined", "dampE", "rotation", "_", "videoTextureRef", "_faceLandmarkerResult", "setFaceLandmarkerResult", "faceLandmarker", "onVideoFrame", "now", "metadata", "texture", "videoFrame", "source", "data", "result", "detectForVideo", "api", "Object", "assign", "create", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prototype", "old", "controls", "points", "faceLandmarks", "facialTransformationMatrix", "facialTransformationMatrixes", "faceBlendshapes", "videoTextureProps", "createElement", "Provider", "value", "fallback", "ref", "children", "side", "DoubleSide", "Math", "PI", "visible", "useFaceControls"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/FaceControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { forwardRef, useRef, useState, useCallback, useMemo, useImperativeHandle, useEffect, Suspense, useContext, createContext } from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { easing } from 'maath';\nimport { VideoTexture } from '../core/VideoTexture.js';\nimport { WebcamVideoTexture } from './WebcamVideoTexture.js';\nimport { Facemesh } from './Facemesh.js';\nimport { useFaceLandmarker } from './FaceLandmarker.js';\n\nfunction mean(v1, v2) {\n  return v1.clone().add(v2).multiplyScalar(0.5);\n}\nfunction localToLocal(objSrc, v, objDst) {\n  // see: https://discourse.threejs.org/t/object3d-localtolocal/51564\n  const v_world = objSrc.localToWorld(v);\n  return objDst.worldToLocal(v_world);\n}\n\n//\n//\n//\n\nconst FaceControlsContext = /* @__PURE__ */createContext({});\n\n/**\n * The camera follows your face.\n *\n * Pre-requisite: wrap into a `FaceLandmarker` provider:\n *\n * ```jsx\n * <FaceLandmarker>...</FaceLandmarker>\n * ```\n */\n\nconst FaceControls = /* @__PURE__ */forwardRef(({\n  camera,\n  videoTexture = {\n    start: true\n  },\n  manualDetect = false,\n  faceLandmarkerResult,\n  manualUpdate = false,\n  makeDefault,\n  smoothTime = 0.25,\n  offset = true,\n  offsetScalar = 80,\n  eyes = false,\n  eyesAsOrigin = true,\n  depth = 0.15,\n  debug = false,\n  facemesh\n}, fref) => {\n  var _result$facialTransfo, _result$faceBlendshap;\n  const scene = useThree(state => state.scene);\n  const defaultCamera = useThree(state => state.camera);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const explCamera = camera || defaultCamera;\n  const facemeshApiRef = useRef(null);\n\n  //\n  // computeTarget()\n  //\n  // Compute `target` position and rotation for the camera (according to <Facemesh>)\n  //\n  //  1. 👀 either following the 2 eyes\n  //  2. 👤 or just the head mesh\n  //\n\n  const [target] = useState(() => new THREE.Object3D());\n  const [irisRightDirPos] = useState(() => new THREE.Vector3());\n  const [irisLeftDirPos] = useState(() => new THREE.Vector3());\n  const [irisRightLookAt] = useState(() => new THREE.Vector3());\n  const [irisLeftLookAt] = useState(() => new THREE.Vector3());\n  const computeTarget = useCallback(() => {\n    // same parent as the camera\n    target.parent = explCamera.parent;\n    const facemeshApi = facemeshApiRef.current;\n    if (facemeshApi) {\n      const {\n        outerRef,\n        eyeRightRef,\n        eyeLeftRef\n      } = facemeshApi;\n      if (eyeRightRef.current && eyeLeftRef.current) {\n        // 1. 👀\n\n        const {\n          irisDirRef: irisRightDirRef\n        } = eyeRightRef.current;\n        const {\n          irisDirRef: irisLeftDirRef\n        } = eyeLeftRef.current;\n        if (irisRightDirRef.current && irisLeftDirRef.current && outerRef.current) {\n          //\n          // position: mean of irisRightDirPos,irisLeftDirPos\n          //\n          irisRightDirPos.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          irisLeftDirPos.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          target.position.copy(localToLocal(outerRef.current, mean(irisRightDirPos, irisLeftDirPos), explCamera.parent || scene));\n\n          //\n          // lookAt: mean of irisRightLookAt,irisLeftLookAt\n          //\n          irisRightLookAt.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          irisLeftLookAt.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          target.lookAt(outerRef.current.localToWorld(mean(irisRightLookAt, irisLeftLookAt)));\n        }\n      } else {\n        // 2. 👤\n\n        if (outerRef.current) {\n          target.position.copy(localToLocal(outerRef.current, new THREE.Vector3(0, 0, 0), explCamera.parent || scene));\n          target.lookAt(outerRef.current.localToWorld(new THREE.Vector3(0, 0, 1)));\n        }\n      }\n    }\n    return target;\n  }, [explCamera, irisLeftDirPos, irisLeftLookAt, irisRightDirPos, irisRightLookAt, scene, target]);\n\n  //\n  // update()\n  //\n  // Updating the camera `current` position and rotation, following `target`\n  //\n\n  const [current] = useState(() => new THREE.Object3D());\n  const update = useCallback(function (delta, target) {\n    if (explCamera) {\n      var _target;\n      (_target = target) !== null && _target !== void 0 ? _target : target = computeTarget();\n      if (smoothTime > 0) {\n        // damping current\n        const eps = 1e-9;\n        easing.damp3(current.position, target.position, smoothTime, delta, undefined, undefined, eps);\n        easing.dampE(current.rotation, target.rotation, smoothTime, delta, undefined, undefined, eps);\n      } else {\n        // instant\n        current.position.copy(target.position);\n        current.rotation.copy(target.rotation);\n      }\n      explCamera.position.copy(current.position);\n      explCamera.rotation.copy(current.rotation);\n    }\n  }, [explCamera, computeTarget, smoothTime, current.position, current.rotation]);\n  useFrame((_, delta) => {\n    if (manualUpdate) return;\n    update(delta);\n  });\n\n  //\n  // onVideoFrame (only used if !manualDetect)\n  //\n\n  const videoTextureRef = useRef(null);\n  const [_faceLandmarkerResult, setFaceLandmarkerResult] = useState();\n  const faceLandmarker = useFaceLandmarker();\n  const onVideoFrame = useCallback((now, metadata) => {\n    const texture = videoTextureRef.current;\n    if (!texture) return;\n    const videoFrame = texture.source.data;\n    const result = faceLandmarker == null ? void 0 : faceLandmarker.detectForVideo(videoFrame, now);\n    setFaceLandmarkerResult(result);\n  }, [faceLandmarker]);\n\n  //\n  // Ref API\n  //\n\n  const api = useMemo(() => Object.assign(Object.create(THREE.EventDispatcher.prototype), {\n    computeTarget,\n    update,\n    facemeshApiRef\n  }), [computeTarget, update]);\n  useImperativeHandle(fref, () => api, [api]);\n\n  //\n  // makeDefault (`controls` global state)\n  //\n\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: api\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, api, get, set]);\n\n  //\n  //\n  //\n\n  const result = faceLandmarkerResult !== null && faceLandmarkerResult !== void 0 ? faceLandmarkerResult : _faceLandmarkerResult;\n  const points = result == null ? void 0 : result.faceLandmarks[0];\n  const facialTransformationMatrix = result == null || (_result$facialTransfo = result.facialTransformationMatrixes) == null ? void 0 : _result$facialTransfo[0];\n  const faceBlendshapes = result == null || (_result$faceBlendshap = result.faceBlendshapes) == null ? void 0 : _result$faceBlendshap[0];\n  const videoTextureProps = {\n    onVideoFrame,\n    ...videoTexture\n  };\n  return /*#__PURE__*/React.createElement(FaceControlsContext.Provider, {\n    value: api\n  }, !manualDetect && /*#__PURE__*/React.createElement(Suspense, {\n    fallback: null\n  }, 'src' in videoTextureProps ? /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps)) : /*#__PURE__*/React.createElement(WebcamVideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps))), /*#__PURE__*/React.createElement(Facemesh, _extends({\n    ref: facemeshApiRef,\n    children: /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n      side: THREE.DoubleSide\n    })\n  }, facemesh, {\n    points: points,\n    depth: depth,\n    facialTransformationMatrix: facialTransformationMatrix,\n    faceBlendshapes: faceBlendshapes,\n    eyes: eyes,\n    eyesAsOrigin: eyesAsOrigin,\n    offset: offset,\n    offsetScalar: offsetScalar,\n    debug: debug,\n    \"rotation-z\": Math.PI,\n    visible: debug\n  })));\n});\nconst useFaceControls = () => useContext(FaceControlsContext);\n\nexport { FaceControls, useFaceControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AAC/I,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,iBAAiB,QAAQ,qBAAqB;AAEvD,SAASC,IAAIA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACpB,OAAOD,EAAE,CAACE,KAAK,CAAC,CAAC,CAACC,GAAG,CAACF,EAAE,CAAC,CAACG,cAAc,CAAC,GAAG,CAAC;AAC/C;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAE;EACvC;EACA,MAAMC,OAAO,GAAGH,MAAM,CAACI,YAAY,CAACH,CAAC,CAAC;EACtC,OAAOC,MAAM,CAACG,YAAY,CAACF,OAAO,CAAC;AACrC;;AAEA;AACA;AACA;;AAEA,MAAMG,mBAAmB,GAAG,eAAerB,aAAa,CAAC,CAAC,CAAC,CAAC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMsB,YAAY,GAAG,eAAe/B,UAAU,CAAC,CAAC;EAC9CgC,MAAM;EACNC,YAAY,GAAG;IACbC,KAAK,EAAE;EACT,CAAC;EACDC,YAAY,GAAG,KAAK;EACpBC,oBAAoB;EACpBC,YAAY,GAAG,KAAK;EACpBC,WAAW;EACXC,UAAU,GAAG,IAAI;EACjBC,MAAM,GAAG,IAAI;EACbC,YAAY,GAAG,EAAE;EACjBC,IAAI,GAAG,KAAK;EACZC,YAAY,GAAG,IAAI;EACnBC,KAAK,GAAG,IAAI;EACZC,KAAK,GAAG,KAAK;EACbC;AACF,CAAC,EAAEC,IAAI,KAAK;EACV,IAAIC,qBAAqB,EAAEC,qBAAqB;EAChD,MAAMC,KAAK,GAAGxC,QAAQ,CAACyC,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAC5C,MAAME,aAAa,GAAG1C,QAAQ,CAACyC,KAAK,IAAIA,KAAK,CAACnB,MAAM,CAAC;EACrD,MAAMqB,GAAG,GAAG3C,QAAQ,CAACyC,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAG5C,QAAQ,CAACyC,KAAK,IAAIA,KAAK,CAACG,GAAG,CAAC;EACxC,MAAMC,UAAU,GAAGvB,MAAM,IAAIoB,aAAa;EAC1C,MAAMI,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM,CAACwD,MAAM,CAAC,GAAGvD,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC4D,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC8D,OAAO,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC8D,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACE,eAAe,CAAC,GAAG5D,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC8D,OAAO,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACG,cAAc,CAAC,GAAG7D,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC8D,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAMI,aAAa,GAAG7D,WAAW,CAAC,MAAM;IACtC;IACAsD,MAAM,CAACQ,MAAM,GAAGV,UAAU,CAACU,MAAM;IACjC,MAAMC,WAAW,GAAGV,cAAc,CAACW,OAAO;IAC1C,IAAID,WAAW,EAAE;MACf,MAAM;QACJE,QAAQ;QACRC,WAAW;QACXC;MACF,CAAC,GAAGJ,WAAW;MACf,IAAIG,WAAW,CAACF,OAAO,IAAIG,UAAU,CAACH,OAAO,EAAE;QAC7C;;QAEA,MAAM;UACJI,UAAU,EAAEC;QACd,CAAC,GAAGH,WAAW,CAACF,OAAO;QACvB,MAAM;UACJI,UAAU,EAAEE;QACd,CAAC,GAAGH,UAAU,CAACH,OAAO;QACtB,IAAIK,eAAe,CAACL,OAAO,IAAIM,cAAc,CAACN,OAAO,IAAIC,QAAQ,CAACD,OAAO,EAAE;UACzE;UACA;UACA;UACAR,eAAe,CAACe,IAAI,CAACnD,YAAY,CAACiD,eAAe,CAACL,OAAO,EAAE,IAAIrE,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEQ,QAAQ,CAACD,OAAO,CAAC,CAAC;UACzGN,cAAc,CAACa,IAAI,CAACnD,YAAY,CAACkD,cAAc,CAACN,OAAO,EAAE,IAAIrE,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEQ,QAAQ,CAACD,OAAO,CAAC,CAAC;UACvGV,MAAM,CAACkB,QAAQ,CAACD,IAAI,CAACnD,YAAY,CAAC6C,QAAQ,CAACD,OAAO,EAAElD,IAAI,CAAC0C,eAAe,EAAEE,cAAc,CAAC,EAAEN,UAAU,CAACU,MAAM,IAAIf,KAAK,CAAC,CAAC;;UAEvH;UACA;UACA;UACAY,eAAe,CAACY,IAAI,CAACnD,YAAY,CAACiD,eAAe,CAACL,OAAO,EAAE,IAAIrE,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEQ,QAAQ,CAACD,OAAO,CAAC,CAAC;UACzGJ,cAAc,CAACW,IAAI,CAACnD,YAAY,CAACkD,cAAc,CAACN,OAAO,EAAE,IAAIrE,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEQ,QAAQ,CAACD,OAAO,CAAC,CAAC;UACvGV,MAAM,CAACmB,MAAM,CAACR,QAAQ,CAACD,OAAO,CAACvC,YAAY,CAACX,IAAI,CAAC6C,eAAe,EAAEC,cAAc,CAAC,CAAC,CAAC;QACrF;MACF,CAAC,MAAM;QACL;;QAEA,IAAIK,QAAQ,CAACD,OAAO,EAAE;UACpBV,MAAM,CAACkB,QAAQ,CAACD,IAAI,CAACnD,YAAY,CAAC6C,QAAQ,CAACD,OAAO,EAAE,IAAIrE,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEL,UAAU,CAACU,MAAM,IAAIf,KAAK,CAAC,CAAC;UAC5GO,MAAM,CAACmB,MAAM,CAACR,QAAQ,CAACD,OAAO,CAACvC,YAAY,CAAC,IAAI9B,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E;MACF;IACF;IACA,OAAOH,MAAM;EACf,CAAC,EAAE,CAACF,UAAU,EAAEM,cAAc,EAAEE,cAAc,EAAEJ,eAAe,EAAEG,eAAe,EAAEZ,KAAK,EAAEO,MAAM,CAAC,CAAC;;EAEjG;EACA;EACA;EACA;EACA;;EAEA,MAAM,CAACU,OAAO,CAAC,GAAGjE,QAAQ,CAAC,MAAM,IAAIJ,KAAK,CAAC4D,QAAQ,CAAC,CAAC,CAAC;EACtD,MAAMmB,MAAM,GAAG1E,WAAW,CAAC,UAAU2E,KAAK,EAAErB,MAAM,EAAE;IAClD,IAAIF,UAAU,EAAE;MACd,IAAIwB,OAAO;MACX,CAACA,OAAO,GAAGtB,MAAM,MAAM,IAAI,IAAIsB,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGtB,MAAM,GAAGO,aAAa,CAAC,CAAC;MACtF,IAAIzB,UAAU,GAAG,CAAC,EAAE;QAClB;QACA,MAAMyC,GAAG,GAAG,IAAI;QAChBpE,MAAM,CAACqE,KAAK,CAACd,OAAO,CAACQ,QAAQ,EAAElB,MAAM,CAACkB,QAAQ,EAAEpC,UAAU,EAAEuC,KAAK,EAAEI,SAAS,EAAEA,SAAS,EAAEF,GAAG,CAAC;QAC7FpE,MAAM,CAACuE,KAAK,CAAChB,OAAO,CAACiB,QAAQ,EAAE3B,MAAM,CAAC2B,QAAQ,EAAE7C,UAAU,EAAEuC,KAAK,EAAEI,SAAS,EAAEA,SAAS,EAAEF,GAAG,CAAC;MAC/F,CAAC,MAAM;QACL;QACAb,OAAO,CAACQ,QAAQ,CAACD,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC;QACtCR,OAAO,CAACiB,QAAQ,CAACV,IAAI,CAACjB,MAAM,CAAC2B,QAAQ,CAAC;MACxC;MACA7B,UAAU,CAACoB,QAAQ,CAACD,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAAC;MAC1CpB,UAAU,CAAC6B,QAAQ,CAACV,IAAI,CAACP,OAAO,CAACiB,QAAQ,CAAC;IAC5C;EACF,CAAC,EAAE,CAAC7B,UAAU,EAAES,aAAa,EAAEzB,UAAU,EAAE4B,OAAO,CAACQ,QAAQ,EAAER,OAAO,CAACiB,QAAQ,CAAC,CAAC;EAC/EzE,QAAQ,CAAC,CAAC0E,CAAC,EAAEP,KAAK,KAAK;IACrB,IAAIzC,YAAY,EAAE;IAClBwC,MAAM,CAACC,KAAK,CAAC;EACf,CAAC,CAAC;;EAEF;EACA;EACA;;EAEA,MAAMQ,eAAe,GAAGrF,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACsF,qBAAqB,EAAEC,uBAAuB,CAAC,GAAGtF,QAAQ,CAAC,CAAC;EACnE,MAAMuF,cAAc,GAAGzE,iBAAiB,CAAC,CAAC;EAC1C,MAAM0E,YAAY,GAAGvF,WAAW,CAAC,CAACwF,GAAG,EAAEC,QAAQ,KAAK;IAClD,MAAMC,OAAO,GAAGP,eAAe,CAACnB,OAAO;IACvC,IAAI,CAAC0B,OAAO,EAAE;IACd,MAAMC,UAAU,GAAGD,OAAO,CAACE,MAAM,CAACC,IAAI;IACtC,MAAMC,MAAM,GAAGR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACS,cAAc,CAACJ,UAAU,EAAEH,GAAG,CAAC;IAC/FH,uBAAuB,CAACS,MAAM,CAAC;EACjC,CAAC,EAAE,CAACR,cAAc,CAAC,CAAC;;EAEpB;EACA;EACA;;EAEA,MAAMU,GAAG,GAAG/F,OAAO,CAAC,MAAMgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAACxG,KAAK,CAACyG,eAAe,CAACC,SAAS,CAAC,EAAE;IACtFxC,aAAa;IACba,MAAM;IACNrB;EACF,CAAC,CAAC,EAAE,CAACQ,aAAa,EAAEa,MAAM,CAAC,CAAC;EAC5BxE,mBAAmB,CAAC0C,IAAI,EAAE,MAAMoD,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;;EAE3C;EACA;EACA;;EAEA7F,SAAS,CAAC,MAAM;IACd,IAAIgC,WAAW,EAAE;MACf,MAAMmE,GAAG,GAAGnD,GAAG,CAAC,CAAC,CAACoD,QAAQ;MAC1BrD,GAAG,CAAC;QACFqD,QAAQ,EAAEP;MACZ,CAAC,CAAC;MACF,OAAO,MAAM9C,GAAG,CAAC;QACfqD,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnE,WAAW,EAAE6D,GAAG,EAAE7C,GAAG,EAAED,GAAG,CAAC,CAAC;;EAEhC;EACA;EACA;;EAEA,MAAM4C,MAAM,GAAG7D,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGmD,qBAAqB;EAC9H,MAAMoB,MAAM,GAAGV,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACW,aAAa,CAAC,CAAC,CAAC;EAChE,MAAMC,0BAA0B,GAAGZ,MAAM,IAAI,IAAI,IAAI,CAACjD,qBAAqB,GAAGiD,MAAM,CAACa,4BAA4B,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9D,qBAAqB,CAAC,CAAC,CAAC;EAC9J,MAAM+D,eAAe,GAAGd,MAAM,IAAI,IAAI,IAAI,CAAChD,qBAAqB,GAAGgD,MAAM,CAACc,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9D,qBAAqB,CAAC,CAAC,CAAC;EACtI,MAAM+D,iBAAiB,GAAG;IACxBtB,YAAY;IACZ,GAAGzD;EACL,CAAC;EACD,OAAO,aAAalC,KAAK,CAACkH,aAAa,CAACnF,mBAAmB,CAACoF,QAAQ,EAAE;IACpEC,KAAK,EAAEhB;EACT,CAAC,EAAE,CAAChE,YAAY,IAAI,aAAapC,KAAK,CAACkH,aAAa,CAAC1G,QAAQ,EAAE;IAC7D6G,QAAQ,EAAE;EACZ,CAAC,EAAE,KAAK,IAAIJ,iBAAiB,GAAG,aAAajH,KAAK,CAACkH,aAAa,CAACpG,YAAY,EAAEhB,QAAQ,CAAC;IACtFwH,GAAG,EAAE/B;EACP,CAAC,EAAE0B,iBAAiB,CAAC,CAAC,GAAG,aAAajH,KAAK,CAACkH,aAAa,CAACnG,kBAAkB,EAAEjB,QAAQ,CAAC;IACrFwH,GAAG,EAAE/B;EACP,CAAC,EAAE0B,iBAAiB,CAAC,CAAC,CAAC,EAAE,aAAajH,KAAK,CAACkH,aAAa,CAAClG,QAAQ,EAAElB,QAAQ,CAAC;IAC3EwH,GAAG,EAAE7D,cAAc;IACnB8D,QAAQ,EAAE,aAAavH,KAAK,CAACkH,aAAa,CAAC,oBAAoB,EAAE;MAC/DM,IAAI,EAAEzH,KAAK,CAAC0H;IACd,CAAC;EACH,CAAC,EAAE1E,QAAQ,EAAE;IACX6D,MAAM,EAAEA,MAAM;IACd/D,KAAK,EAAEA,KAAK;IACZiE,0BAA0B,EAAEA,0BAA0B;IACtDE,eAAe,EAAEA,eAAe;IAChCrE,IAAI,EAAEA,IAAI;IACVC,YAAY,EAAEA,YAAY;IAC1BH,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA,YAAY;IAC1BI,KAAK,EAAEA,KAAK;IACZ,YAAY,EAAE4E,IAAI,CAACC,EAAE;IACrBC,OAAO,EAAE9E;EACX,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,MAAM+E,eAAe,GAAGA,CAAA,KAAMpH,UAAU,CAACsB,mBAAmB,CAAC;AAE7D,SAASC,YAAY,EAAE6F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}