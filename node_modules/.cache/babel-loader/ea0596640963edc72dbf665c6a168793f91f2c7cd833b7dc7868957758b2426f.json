{"ast": null, "code": "import { useThree, addAfterEffect } from '@react-three/fiber';\nimport * as React from 'react';\nimport Stats from 'stats-gl';\nconst StatsGl = /* @__PURE__ */React.forwardRef(function StatsGl({\n  className,\n  parent,\n  id,\n  clearStatsGlStyle,\n  ...props\n}, fref) {\n  const gl = useThree(state => state.gl);\n  const stats = React.useMemo(() => {\n    const stats = new Stats({\n      ...props\n    });\n    stats.init(gl);\n    return stats;\n  }, [gl]);\n  React.useImperativeHandle(fref, () => stats.domElement, [stats]);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      node == null || node.appendChild(stats.domElement);\n      stats.domElement.querySelectorAll('canvas').forEach(canvas => {\n        canvas.style.removeProperty('position');\n      });\n      if (id) stats.domElement.id = id;\n      if (clearStatsGlStyle) stats.domElement.removeAttribute('style');\n      stats.domElement.removeAttribute('style');\n      const classNames = (className !== null && className !== void 0 ? className : '').split(' ').filter(cls => cls);\n      if (classNames.length) stats.domElement.classList.add(...classNames);\n      const end = addAfterEffect(() => stats.update());\n      return () => {\n        if (classNames.length) stats.domElement.classList.remove(...classNames);\n        node == null || node.removeChild(stats.domElement);\n        end();\n      };\n    }\n  }, [parent, stats, className, id, clearStatsGlStyle]);\n  return null;\n});\nexport { StatsGl };", "map": {"version": 3, "names": ["useThree", "addAfterEffect", "React", "Stats", "StatsGl", "forwardRef", "className", "parent", "id", "clearStatsGlStyle", "props", "fref", "gl", "state", "stats", "useMemo", "init", "useImperativeHandle", "dom<PERSON>lement", "useEffect", "node", "current", "document", "body", "append<PERSON><PERSON><PERSON>", "querySelectorAll", "for<PERSON>ach", "canvas", "style", "removeProperty", "removeAttribute", "classNames", "split", "filter", "cls", "length", "classList", "add", "end", "update", "remove", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/StatsGl.js"], "sourcesContent": ["import { useThree, addAfterEffect } from '@react-three/fiber';\nimport * as React from 'react';\nimport Stats from 'stats-gl';\n\nconst StatsGl = /* @__PURE__ */React.forwardRef(function StatsGl({\n  className,\n  parent,\n  id,\n  clearStatsGlStyle,\n  ...props\n}, fref) {\n  const gl = useThree(state => state.gl);\n  const stats = React.useMemo(() => {\n    const stats = new Stats({\n      ...props\n    });\n    stats.init(gl);\n    return stats;\n  }, [gl]);\n  React.useImperativeHandle(fref, () => stats.domElement, [stats]);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      node == null || node.appendChild(stats.domElement);\n      stats.domElement.querySelectorAll('canvas').forEach(canvas => {\n        canvas.style.removeProperty('position');\n      });\n      if (id) stats.domElement.id = id;\n      if (clearStatsGlStyle) stats.domElement.removeAttribute('style');\n      stats.domElement.removeAttribute('style');\n      const classNames = (className !== null && className !== void 0 ? className : '').split(' ').filter(cls => cls);\n      if (classNames.length) stats.domElement.classList.add(...classNames);\n      const end = addAfterEffect(() => stats.update());\n      return () => {\n        if (classNames.length) stats.domElement.classList.remove(...classNames);\n        node == null || node.removeChild(stats.domElement);\n        end();\n      };\n    }\n  }, [parent, stats, className, id, clearStatsGlStyle]);\n  return null;\n});\n\nexport { StatsGl };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,QAAQ,oBAAoB;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,UAAU;AAE5B,MAAMC,OAAO,GAAG,eAAeF,KAAK,CAACG,UAAU,CAAC,SAASD,OAAOA,CAAC;EAC/DE,SAAS;EACTC,MAAM;EACNC,EAAE;EACFC,iBAAiB;EACjB,GAAGC;AACL,CAAC,EAAEC,IAAI,EAAE;EACP,MAAMC,EAAE,GAAGZ,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,KAAK,GAAGZ,KAAK,CAACa,OAAO,CAAC,MAAM;IAChC,MAAMD,KAAK,GAAG,IAAIX,KAAK,CAAC;MACtB,GAAGO;IACL,CAAC,CAAC;IACFI,KAAK,CAACE,IAAI,CAACJ,EAAE,CAAC;IACd,OAAOE,KAAK;EACd,CAAC,EAAE,CAACF,EAAE,CAAC,CAAC;EACRV,KAAK,CAACe,mBAAmB,CAACN,IAAI,EAAE,MAAMG,KAAK,CAACI,UAAU,EAAE,CAACJ,KAAK,CAAC,CAAC;EAChEZ,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIL,KAAK,EAAE;MACT,MAAMM,IAAI,GAAGb,MAAM,IAAIA,MAAM,CAACc,OAAO,IAAIC,QAAQ,CAACC,IAAI;MACtDH,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACI,WAAW,CAACV,KAAK,CAACI,UAAU,CAAC;MAClDJ,KAAK,CAACI,UAAU,CAACO,gBAAgB,CAAC,QAAQ,CAAC,CAACC,OAAO,CAACC,MAAM,IAAI;QAC5DA,MAAM,CAACC,KAAK,CAACC,cAAc,CAAC,UAAU,CAAC;MACzC,CAAC,CAAC;MACF,IAAIrB,EAAE,EAAEM,KAAK,CAACI,UAAU,CAACV,EAAE,GAAGA,EAAE;MAChC,IAAIC,iBAAiB,EAAEK,KAAK,CAACI,UAAU,CAACY,eAAe,CAAC,OAAO,CAAC;MAChEhB,KAAK,CAACI,UAAU,CAACY,eAAe,CAAC,OAAO,CAAC;MACzC,MAAMC,UAAU,GAAG,CAACzB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE,EAAE0B,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC;MAC9G,IAAIH,UAAU,CAACI,MAAM,EAAErB,KAAK,CAACI,UAAU,CAACkB,SAAS,CAACC,GAAG,CAAC,GAAGN,UAAU,CAAC;MACpE,MAAMO,GAAG,GAAGrC,cAAc,CAAC,MAAMa,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC;MAChD,OAAO,MAAM;QACX,IAAIR,UAAU,CAACI,MAAM,EAAErB,KAAK,CAACI,UAAU,CAACkB,SAAS,CAACI,MAAM,CAAC,GAAGT,UAAU,CAAC;QACvEX,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACqB,WAAW,CAAC3B,KAAK,CAACI,UAAU,CAAC;QAClDoB,GAAG,CAAC,CAAC;MACP,CAAC;IACH;EACF,CAAC,EAAE,CAAC/B,MAAM,EAAEO,KAAK,EAAER,SAAS,EAAEE,EAAE,EAAEC,iBAAiB,CAAC,CAAC;EACrD,OAAO,IAAI;AACb,CAAC,CAAC;AAEF,SAASL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}