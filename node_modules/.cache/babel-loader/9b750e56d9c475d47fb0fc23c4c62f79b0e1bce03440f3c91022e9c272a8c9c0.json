{"ast": null, "code": "export { r as rubberbandIfOutOfBounds } from '../../dist/maths-0ab39ae9.esm.js';", "map": {"version": 3, "names": ["r", "rubberbandIfOutOfBounds"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@use-gesture/core/utils/dist/use-gesture-core-utils.esm.js"], "sourcesContent": ["export { r as rubberbandIfOutOfBounds } from '../../dist/maths-0ab39ae9.esm.js';\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}