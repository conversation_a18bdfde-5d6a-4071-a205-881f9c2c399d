{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { Line } from './Line.js';\nconst Edges = /* @__PURE__ */React.forwardRef(({\n  threshold = 15,\n  geometry: explicitGeometry,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  const tmpPoints = React.useMemo(() => [0, 0, 0, 1, 0, 0], []);\n  const memoizedGeometry = React.useRef(null);\n  const memoizedThreshold = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.parent;\n    const geometry = explicitGeometry !== null && explicitGeometry !== void 0 ? explicitGeometry : parent == null ? void 0 : parent.geometry;\n    if (!geometry) return;\n    const cached = memoizedGeometry.current === geometry && memoizedThreshold.current === threshold;\n    if (cached) return;\n    memoizedGeometry.current = geometry;\n    memoizedThreshold.current = threshold;\n    const points = new THREE.EdgesGeometry(geometry, threshold).attributes.position.array;\n    ref.current.geometry.setPositions(points);\n    ref.current.geometry.attributes.instanceStart.needsUpdate = true;\n    ref.current.geometry.attributes.instanceEnd.needsUpdate = true;\n    ref.current.computeLineDistances();\n  });\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    segments: true,\n    points: tmpPoints,\n    ref: ref,\n    raycast: () => null\n  }, props));\n});\nexport { Edges };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "Line", "<PERSON>s", "forwardRef", "threshold", "geometry", "explicitGeometry", "props", "fref", "ref", "useRef", "useImperativeHandle", "current", "tmpPoints", "useMemo", "memoizedGeometry", "memoizedThr<PERSON>old", "useLayoutEffect", "parent", "cached", "points", "EdgesGeometry", "attributes", "position", "array", "setPositions", "instanceStart", "needsUpdate", "instanceEnd", "computeLineDistances", "createElement", "segments", "raycast"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Edges.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { Line } from './Line.js';\n\nconst Edges = /* @__PURE__ */React.forwardRef(({\n  threshold = 15,\n  geometry: explicitGeometry,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  const tmpPoints = React.useMemo(() => [0, 0, 0, 1, 0, 0], []);\n  const memoizedGeometry = React.useRef(null);\n  const memoizedThreshold = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.parent;\n    const geometry = explicitGeometry !== null && explicitGeometry !== void 0 ? explicitGeometry : parent == null ? void 0 : parent.geometry;\n    if (!geometry) return;\n    const cached = memoizedGeometry.current === geometry && memoizedThreshold.current === threshold;\n    if (cached) return;\n    memoizedGeometry.current = geometry;\n    memoizedThreshold.current = threshold;\n    const points = new THREE.EdgesGeometry(geometry, threshold).attributes.position.array;\n    ref.current.geometry.setPositions(points);\n    ref.current.geometry.attributes.instanceStart.needsUpdate = true;\n    ref.current.geometry.attributes.instanceEnd.needsUpdate = true;\n    ref.current.computeLineDistances();\n  });\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    segments: true,\n    points: tmpPoints,\n    ref: ref,\n    raycast: () => null\n  }, props));\n});\n\nexport { Edges };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,WAAW;AAEhC,MAAMC,KAAK,GAAG,eAAeH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC7CC,SAAS,GAAG,EAAE;EACdC,QAAQ,EAAEC,gBAAgB;EAC1B,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC9BX,KAAK,CAACY,mBAAmB,CAACH,IAAI,EAAE,MAAMC,GAAG,CAACG,OAAO,EAAE,EAAE,CAAC;EACtD,MAAMC,SAAS,GAAGd,KAAK,CAACe,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D,MAAMC,gBAAgB,GAAGhB,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMM,iBAAiB,GAAGjB,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC5CX,KAAK,CAACkB,eAAe,CAAC,MAAM;IAC1B,MAAMC,MAAM,GAAGT,GAAG,CAACG,OAAO,CAACM,MAAM;IACjC,MAAMb,QAAQ,GAAGC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGY,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACb,QAAQ;IACxI,IAAI,CAACA,QAAQ,EAAE;IACf,MAAMc,MAAM,GAAGJ,gBAAgB,CAACH,OAAO,KAAKP,QAAQ,IAAIW,iBAAiB,CAACJ,OAAO,KAAKR,SAAS;IAC/F,IAAIe,MAAM,EAAE;IACZJ,gBAAgB,CAACH,OAAO,GAAGP,QAAQ;IACnCW,iBAAiB,CAACJ,OAAO,GAAGR,SAAS;IACrC,MAAMgB,MAAM,GAAG,IAAIpB,KAAK,CAACqB,aAAa,CAAChB,QAAQ,EAAED,SAAS,CAAC,CAACkB,UAAU,CAACC,QAAQ,CAACC,KAAK;IACrFf,GAAG,CAACG,OAAO,CAACP,QAAQ,CAACoB,YAAY,CAACL,MAAM,CAAC;IACzCX,GAAG,CAACG,OAAO,CAACP,QAAQ,CAACiB,UAAU,CAACI,aAAa,CAACC,WAAW,GAAG,IAAI;IAChElB,GAAG,CAACG,OAAO,CAACP,QAAQ,CAACiB,UAAU,CAACM,WAAW,CAACD,WAAW,GAAG,IAAI;IAC9DlB,GAAG,CAACG,OAAO,CAACiB,oBAAoB,CAAC,CAAC;EACpC,CAAC,CAAC;EACF,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAAC7B,IAAI,EAAEH,QAAQ,CAAC;IACrDiC,QAAQ,EAAE,IAAI;IACdX,MAAM,EAAEP,SAAS;IACjBJ,GAAG,EAAEA,GAAG;IACRuB,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEzB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}