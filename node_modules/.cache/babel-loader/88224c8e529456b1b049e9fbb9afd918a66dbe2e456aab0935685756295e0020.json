{"ast": null, "code": "import { Group, Raycaster, Matrix4, Vector2 } from \"three\";\nconst _pointer = /* @__PURE__ */new Vector2();\nconst _event = {\n  type: \"\",\n  data: _pointer\n};\nclass InteractiveGroup extends Group {\n  constructor(renderer, camera) {\n    super();\n    const scope = this;\n    const raycaster = new Raycaster();\n    const tempMatrix = new Matrix4();\n    const element = renderer.domElement;\n    function onPointerEvent(event) {\n      event.stopPropagation();\n      _pointer.x = event.clientX / element.clientWidth * 2 - 1;\n      _pointer.y = -(event.clientY / element.clientHeight) * 2 + 1;\n      raycaster.setFromCamera(_pointer, camera);\n      const intersects = raycaster.intersectObjects(scope.children, false);\n      if (intersects.length > 0) {\n        const intersection = intersects[0];\n        const object = intersection.object;\n        const uv = intersection.uv;\n        _event.type = event.type;\n        _event.data.set(uv.x, 1 - uv.y);\n        object.dispatchEvent(_event);\n      }\n    }\n    element.addEventListener(\"pointerdown\", onPointerEvent);\n    element.addEventListener(\"pointerup\", onPointerEvent);\n    element.addEventListener(\"pointermove\", onPointerEvent);\n    element.addEventListener(\"mousedown\", onPointerEvent);\n    element.addEventListener(\"mouseup\", onPointerEvent);\n    element.addEventListener(\"mousemove\", onPointerEvent);\n    element.addEventListener(\"click\", onPointerEvent);\n    const events = {\n      move: \"mousemove\",\n      select: \"click\",\n      selectstart: \"mousedown\",\n      selectend: \"mouseup\"\n    };\n    function onXRControllerEvent(event) {\n      const controller = event.target;\n      tempMatrix.identity().extractRotation(controller.matrixWorld);\n      raycaster.ray.origin.setFromMatrixPosition(controller.matrixWorld);\n      raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix);\n      const intersections = raycaster.intersectObjects(scope.children, false);\n      if (intersections.length > 0) {\n        const intersection = intersections[0];\n        const object = intersection.object;\n        const uv = intersection.uv;\n        _event.type = events[event.type];\n        _event.data.set(uv.x, 1 - uv.y);\n        object.dispatchEvent(_event);\n      }\n    }\n    const controller1 = renderer.xr.getController(0);\n    controller1.addEventListener(\"move\", onXRControllerEvent);\n    controller1.addEventListener(\"select\", onXRControllerEvent);\n    controller1.addEventListener(\"selectstart\", onXRControllerEvent);\n    controller1.addEventListener(\"selectend\", onXRControllerEvent);\n    const controller2 = renderer.xr.getController(1);\n    controller2.addEventListener(\"move\", onXRControllerEvent);\n    controller2.addEventListener(\"select\", onXRControllerEvent);\n    controller2.addEventListener(\"selectstart\", onXRControllerEvent);\n    controller2.addEventListener(\"selectend\", onXRControllerEvent);\n  }\n}\nexport { InteractiveGroup };", "map": {"version": 3, "names": ["_pointer", "Vector2", "_event", "type", "data", "InteractiveGroup", "Group", "constructor", "renderer", "camera", "scope", "raycaster", "Raycaster", "tempMatrix", "Matrix4", "element", "dom<PERSON>lement", "onPointerEvent", "event", "stopPropagation", "x", "clientX", "clientWidth", "y", "clientY", "clientHeight", "setFromCamera", "intersects", "intersectObjects", "children", "length", "intersection", "object", "uv", "set", "dispatchEvent", "addEventListener", "events", "move", "select", "selectstart", "selectend", "onXRControllerEvent", "controller", "target", "identity", "extractRotation", "matrixWorld", "ray", "origin", "setFromMatrixPosition", "direction", "applyMatrix4", "intersections", "controller1", "xr", "getController", "controller2"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/interactive/InteractiveGroup.js"], "sourcesContent": ["import { Group, Matrix4, Ray<PERSON>, Vector2 } from 'three'\n\nconst _pointer = /* @__PURE__ */ new Vector2()\nconst _event = { type: '', data: _pointer }\n\nclass InteractiveGroup extends Group {\n  constructor(renderer, camera) {\n    super()\n\n    const scope = this\n\n    const raycaster = new Raycaster()\n    const tempMatrix = new Matrix4()\n\n    // Pointer Events\n\n    const element = renderer.domElement\n\n    function onPointerEvent(event) {\n      event.stopPropagation()\n\n      _pointer.x = (event.clientX / element.clientWidth) * 2 - 1\n      _pointer.y = -(event.clientY / element.clientHeight) * 2 + 1\n\n      raycaster.setFromCamera(_pointer, camera)\n\n      const intersects = raycaster.intersectObjects(scope.children, false)\n\n      if (intersects.length > 0) {\n        const intersection = intersects[0]\n\n        const object = intersection.object\n        const uv = intersection.uv\n\n        _event.type = event.type\n        _event.data.set(uv.x, 1 - uv.y)\n\n        object.dispatchEvent(_event)\n      }\n    }\n\n    element.addEventListener('pointerdown', onPointerEvent)\n    element.addEventListener('pointerup', onPointerEvent)\n    element.addEventListener('pointermove', onPointerEvent)\n    element.addEventListener('mousedown', onPointerEvent)\n    element.addEventListener('mouseup', onPointerEvent)\n    element.addEventListener('mousemove', onPointerEvent)\n    element.addEventListener('click', onPointerEvent)\n\n    // WebXR Controller Events\n    // TODO: Dispatch pointerevents too\n\n    const events = {\n      move: 'mousemove',\n      select: 'click',\n      selectstart: 'mousedown',\n      selectend: 'mouseup',\n    }\n\n    function onXRControllerEvent(event) {\n      const controller = event.target\n\n      tempMatrix.identity().extractRotation(controller.matrixWorld)\n\n      raycaster.ray.origin.setFromMatrixPosition(controller.matrixWorld)\n      raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix)\n\n      const intersections = raycaster.intersectObjects(scope.children, false)\n\n      if (intersections.length > 0) {\n        const intersection = intersections[0]\n\n        const object = intersection.object\n        const uv = intersection.uv\n\n        _event.type = events[event.type]\n        _event.data.set(uv.x, 1 - uv.y)\n\n        object.dispatchEvent(_event)\n      }\n    }\n\n    const controller1 = renderer.xr.getController(0)\n    controller1.addEventListener('move', onXRControllerEvent)\n    controller1.addEventListener('select', onXRControllerEvent)\n    controller1.addEventListener('selectstart', onXRControllerEvent)\n    controller1.addEventListener('selectend', onXRControllerEvent)\n\n    const controller2 = renderer.xr.getController(1)\n    controller2.addEventListener('move', onXRControllerEvent)\n    controller2.addEventListener('select', onXRControllerEvent)\n    controller2.addEventListener('selectstart', onXRControllerEvent)\n    controller2.addEventListener('selectend', onXRControllerEvent)\n  }\n}\n\nexport { InteractiveGroup }\n"], "mappings": ";AAEA,MAAMA,QAAA,GAA2B,mBAAIC,OAAA,CAAS;AAC9C,MAAMC,MAAA,GAAS;EAAEC,IAAA,EAAM;EAAIC,IAAA,EAAMJ;AAAU;AAE3C,MAAMK,gBAAA,SAAyBC,KAAA,CAAM;EACnCC,YAAYC,QAAA,EAAUC,MAAA,EAAQ;IAC5B,MAAO;IAEP,MAAMC,KAAA,GAAQ;IAEd,MAAMC,SAAA,GAAY,IAAIC,SAAA,CAAW;IACjC,MAAMC,UAAA,GAAa,IAAIC,OAAA,CAAS;IAIhC,MAAMC,OAAA,GAAUP,QAAA,CAASQ,UAAA;IAEzB,SAASC,eAAeC,KAAA,EAAO;MAC7BA,KAAA,CAAMC,eAAA,CAAiB;MAEvBnB,QAAA,CAASoB,CAAA,GAAKF,KAAA,CAAMG,OAAA,GAAUN,OAAA,CAAQO,WAAA,GAAe,IAAI;MACzDtB,QAAA,CAASuB,CAAA,GAAI,EAAEL,KAAA,CAAMM,OAAA,GAAUT,OAAA,CAAQU,YAAA,IAAgB,IAAI;MAE3Dd,SAAA,CAAUe,aAAA,CAAc1B,QAAA,EAAUS,MAAM;MAExC,MAAMkB,UAAA,GAAahB,SAAA,CAAUiB,gBAAA,CAAiBlB,KAAA,CAAMmB,QAAA,EAAU,KAAK;MAEnE,IAAIF,UAAA,CAAWG,MAAA,GAAS,GAAG;QACzB,MAAMC,YAAA,GAAeJ,UAAA,CAAW,CAAC;QAEjC,MAAMK,MAAA,GAASD,YAAA,CAAaC,MAAA;QAC5B,MAAMC,EAAA,GAAKF,YAAA,CAAaE,EAAA;QAExB/B,MAAA,CAAOC,IAAA,GAAOe,KAAA,CAAMf,IAAA;QACpBD,MAAA,CAAOE,IAAA,CAAK8B,GAAA,CAAID,EAAA,CAAGb,CAAA,EAAG,IAAIa,EAAA,CAAGV,CAAC;QAE9BS,MAAA,CAAOG,aAAA,CAAcjC,MAAM;MAC5B;IACF;IAEDa,OAAA,CAAQqB,gBAAA,CAAiB,eAAenB,cAAc;IACtDF,OAAA,CAAQqB,gBAAA,CAAiB,aAAanB,cAAc;IACpDF,OAAA,CAAQqB,gBAAA,CAAiB,eAAenB,cAAc;IACtDF,OAAA,CAAQqB,gBAAA,CAAiB,aAAanB,cAAc;IACpDF,OAAA,CAAQqB,gBAAA,CAAiB,WAAWnB,cAAc;IAClDF,OAAA,CAAQqB,gBAAA,CAAiB,aAAanB,cAAc;IACpDF,OAAA,CAAQqB,gBAAA,CAAiB,SAASnB,cAAc;IAKhD,MAAMoB,MAAA,GAAS;MACbC,IAAA,EAAM;MACNC,MAAA,EAAQ;MACRC,WAAA,EAAa;MACbC,SAAA,EAAW;IACZ;IAED,SAASC,oBAAoBxB,KAAA,EAAO;MAClC,MAAMyB,UAAA,GAAazB,KAAA,CAAM0B,MAAA;MAEzB/B,UAAA,CAAWgC,QAAA,CAAU,EAACC,eAAA,CAAgBH,UAAA,CAAWI,WAAW;MAE5DpC,SAAA,CAAUqC,GAAA,CAAIC,MAAA,CAAOC,qBAAA,CAAsBP,UAAA,CAAWI,WAAW;MACjEpC,SAAA,CAAUqC,GAAA,CAAIG,SAAA,CAAUjB,GAAA,CAAI,GAAG,GAAG,EAAE,EAAEkB,YAAA,CAAavC,UAAU;MAE7D,MAAMwC,aAAA,GAAgB1C,SAAA,CAAUiB,gBAAA,CAAiBlB,KAAA,CAAMmB,QAAA,EAAU,KAAK;MAEtE,IAAIwB,aAAA,CAAcvB,MAAA,GAAS,GAAG;QAC5B,MAAMC,YAAA,GAAesB,aAAA,CAAc,CAAC;QAEpC,MAAMrB,MAAA,GAASD,YAAA,CAAaC,MAAA;QAC5B,MAAMC,EAAA,GAAKF,YAAA,CAAaE,EAAA;QAExB/B,MAAA,CAAOC,IAAA,GAAOkC,MAAA,CAAOnB,KAAA,CAAMf,IAAI;QAC/BD,MAAA,CAAOE,IAAA,CAAK8B,GAAA,CAAID,EAAA,CAAGb,CAAA,EAAG,IAAIa,EAAA,CAAGV,CAAC;QAE9BS,MAAA,CAAOG,aAAA,CAAcjC,MAAM;MAC5B;IACF;IAED,MAAMoD,WAAA,GAAc9C,QAAA,CAAS+C,EAAA,CAAGC,aAAA,CAAc,CAAC;IAC/CF,WAAA,CAAYlB,gBAAA,CAAiB,QAAQM,mBAAmB;IACxDY,WAAA,CAAYlB,gBAAA,CAAiB,UAAUM,mBAAmB;IAC1DY,WAAA,CAAYlB,gBAAA,CAAiB,eAAeM,mBAAmB;IAC/DY,WAAA,CAAYlB,gBAAA,CAAiB,aAAaM,mBAAmB;IAE7D,MAAMe,WAAA,GAAcjD,QAAA,CAAS+C,EAAA,CAAGC,aAAA,CAAc,CAAC;IAC/CC,WAAA,CAAYrB,gBAAA,CAAiB,QAAQM,mBAAmB;IACxDe,WAAA,CAAYrB,gBAAA,CAAiB,UAAUM,mBAAmB;IAC1De,WAAA,CAAYrB,gBAAA,CAAiB,eAAeM,mBAAmB;IAC/De,WAAA,CAAYrB,gBAAA,CAAiB,aAAaM,mBAAmB;EAC9D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}