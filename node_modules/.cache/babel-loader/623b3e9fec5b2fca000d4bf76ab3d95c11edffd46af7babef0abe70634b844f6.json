{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { GLTFLoader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader) => useLoader(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = path => useLoader.clear(GLTFLoader, path);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n//\n\nconst Gltf = /* @__PURE__ */React.forwardRef(({\n  src,\n  useDraco,\n  useMeshOpt,\n  extendLoader,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\nexport { Gltf, useGLTF };", "map": {"version": 3, "names": ["_extends", "React", "GLTFLoader", "DRACOLoader", "MeshoptDecoder", "useLoader", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decoder<PERSON><PERSON>", "extensions", "useDraco", "useMeshopt", "<PERSON><PERSON><PERSON><PERSON>", "loader", "setDecoderPath", "setDRACOLoader", "setMeshoptDecoder", "useGLTF", "path", "preload", "clear", "Gltf", "forwardRef", "src", "useMeshOpt", "props", "ref", "scene", "createElement", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Gltf.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { GLTFLoader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\n\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader) => useLoader(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = path => useLoader.clear(GLTFLoader, path);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n//\n\nconst Gltf = /* @__PURE__ */React.forwardRef(({\n  src,\n  useDraco,\n  useMeshOpt,\n  extendLoader,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\n\nexport { Gltf, useGLTF };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,WAAW,EAAEC,cAAc,QAAQ,cAAc;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,KAAK,QAAQ,YAAY;AAElC,IAAIC,WAAW,GAAG,IAAI;AACtB,IAAIC,WAAW,GAAG,yDAAyD;AAC3E,SAASC,UAAUA,CAACC,QAAQ,GAAG,IAAI,EAAEC,UAAU,GAAG,IAAI,EAAEC,YAAY,EAAE;EACpE,OAAOC,MAAM,IAAI;IACf,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACC,MAAM,CAAC;IACtB;IACA,IAAIH,QAAQ,EAAE;MACZ,IAAI,CAACH,WAAW,EAAE;QAChBA,WAAW,GAAG,IAAIJ,WAAW,CAAC,CAAC;MACjC;MACAI,WAAW,CAACO,cAAc,CAAC,OAAOJ,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGF,WAAW,CAAC;MACjFK,MAAM,CAACE,cAAc,CAACR,WAAW,CAAC;IACpC;IACA,IAAII,UAAU,EAAE;MACdE,MAAM,CAACG,iBAAiB,CAAC,OAAOZ,cAAc,KAAK,UAAU,GAAGA,cAAc,CAAC,CAAC,GAAGA,cAAc,CAAC;IACpG;EACF,CAAC;AACH;AACA,MAAMa,OAAO,GAAGA,CAACC,IAAI,EAAER,QAAQ,EAAEC,UAAU,EAAEC,YAAY,KAAKP,SAAS,CAACH,UAAU,EAAEgB,IAAI,EAAET,UAAU,CAACC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,CAAC,CAAC;AACzIK,OAAO,CAACE,OAAO,GAAG,CAACD,IAAI,EAAER,QAAQ,EAAEC,UAAU,EAAEC,YAAY,KAAKP,SAAS,CAACc,OAAO,CAACjB,UAAU,EAAEgB,IAAI,EAAET,UAAU,CAACC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,CAAC,CAAC;AACnJK,OAAO,CAACG,KAAK,GAAGF,IAAI,IAAIb,SAAS,CAACe,KAAK,CAAClB,UAAU,EAAEgB,IAAI,CAAC;AACzDD,OAAO,CAACH,cAAc,GAAGI,IAAI,IAAI;EAC/BV,WAAW,GAAGU,IAAI;AACpB,CAAC;;AAED;;AAEA,MAAMG,IAAI,GAAG,eAAepB,KAAK,CAACqB,UAAU,CAAC,CAAC;EAC5CC,GAAG;EACHb,QAAQ;EACRc,UAAU;EACVZ,YAAY;EACZ,GAAGa;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGV,OAAO,CAACM,GAAG,EAAEb,QAAQ,EAAEc,UAAU,EAAEZ,YAAY,CAAC;EACpD,OAAO,aAAaX,KAAK,CAAC2B,aAAa,CAACtB,KAAK,EAAEN,QAAQ,CAAC;IACtD0B,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRI,MAAM,EAAEF;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASN,IAAI,EAAEJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}