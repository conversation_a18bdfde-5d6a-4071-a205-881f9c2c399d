{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\nimport { Instances, Instance } from './Instances.js';\nimport { Billboard } from './Billboard.js';\nimport { useSpriteLoader, getFirstFrame } from './useSpriteLoader.js';\n\n// Frame-related types\n\nconst context = /*#__PURE__*/React.createContext(null);\nfunction useSpriteAnimator() {\n  return React.useContext(context);\n}\n\n// Type guard for SpriteData\nfunction isSpriteData(data) {\n  return data !== null && 'meta' in data && 'frames' in data;\n}\nconst geometry = /* @__PURE__ */new THREE.PlaneGeometry(1, 1);\nconst SpriteAnimator = /* @__PURE__ */React.forwardRef(({\n  startFrame = 0,\n  endFrame,\n  fps = 30,\n  frameName = '',\n  textureDataURL,\n  textureImageURL,\n  loop = false,\n  numberOfFrames = 1,\n  autoPlay = true,\n  animationNames,\n  onStart,\n  onEnd,\n  onLoopEnd,\n  onFrame,\n  play,\n  pause = false,\n  flipX = false,\n  alphaTest = 0.0,\n  children,\n  asSprite = false,\n  offset,\n  playBackwards = false,\n  resetOnEnd = false,\n  maxItems = 1,\n  instanceItems = [[0, 0, 0]],\n  spriteDataset,\n  canvasRenderingContext2DSettings,\n  roundFramePosition = false,\n  meshProps = {},\n  ...props\n}, fref) => {\n  const ref = React.useRef(new THREE.Group());\n  const spriteData = React.useRef(null);\n  const matRef = React.useRef(null);\n  const spriteRef = React.useRef(null);\n  const timerOffset = React.useRef(window.performance.now());\n  const currentFrame = React.useRef(startFrame);\n  const currentFrameName = React.useRef(frameName);\n  const fpsInterval = fps > 0 ? 1000 / fps : 0;\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const totalFrames = React.useRef(0);\n  const [aspect, setAspect] = React.useState(new THREE.Vector3(1, 1, 1));\n  const flipOffset = flipX ? -1 : 1;\n  const pauseRef = React.useRef(pause);\n  const pos = React.useRef(offset);\n  const softEnd = React.useRef(false);\n  const {\n    spriteObj,\n    loadJsonAndTexture\n  } = useSpriteLoader(null, null, animationNames, numberOfFrames, undefined, canvasRenderingContext2DSettings);\n  const frameNameRef = React.useRef(frameName);\n\n  // lite version for pre-loaded assets\n  const parseSpriteDataLite = React.useCallback((textureData, data) => {\n    if (data === null) {\n      if (numberOfFrames) {\n        //get size from texture\n\n        totalFrames.current = numberOfFrames;\n        if (playBackwards) {\n          currentFrame.current = numberOfFrames - 1;\n        }\n        spriteData.current = data;\n      }\n    } else {\n      var _spriteData$current$f, _spriteData$current;\n      spriteData.current = data;\n      if (spriteData.current && Array.isArray(spriteData.current.frames)) {\n        totalFrames.current = spriteData.current.frames.length;\n      } else if (spriteData.current && typeof spriteData.current === 'object' && frameNameRef.current) {\n        totalFrames.current = spriteData.current.frames[frameNameRef.current].length;\n      } else {\n        totalFrames.current = 0;\n      }\n      if (playBackwards) {\n        currentFrame.current = totalFrames.current - 1;\n      }\n      const {\n        w,\n        h\n      } = getFirstFrame((_spriteData$current$f = (_spriteData$current = spriteData.current) == null ? void 0 : _spriteData$current.frames) !== null && _spriteData$current$f !== void 0 ? _spriteData$current$f : [], frameNameRef.current).sourceSize;\n      const aspect = calculateAspectRatio(w, h);\n      setAspect(aspect);\n      if (matRef.current) {\n        matRef.current.map = textureData;\n      }\n    }\n    setSpriteTexture(textureData);\n  }, [numberOfFrames, playBackwards]);\n\n  // modify the sprite material after json is parsed and state updated\n  const modifySpritePosition = React.useCallback(() => {\n    if (!spriteData.current) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = Array.isArray(frames) ? frames[0].sourceSize : frameName ? frames[frameName] ? frames[frameName][0].sourceSize : {\n      w: 0,\n      h: 0\n    } : {\n      w: 0,\n      h: 0\n    };\n    if (matRef.current && matRef.current.map) {\n      matRef.current.map.wrapS = matRef.current.map.wrapT = THREE.RepeatWrapping;\n      matRef.current.map.center.set(0, 0);\n      matRef.current.map.repeat.set(1 * flipOffset / (metaInfo.w / frameW), 1 / (metaInfo.h / frameH));\n    }\n    //const framesH = (metaInfo.w - 1) / frameW\n    const framesV = (metaInfo.h - 1) / frameH;\n    const frameOffsetY = 1 / framesV;\n    if (matRef.current && matRef.current.map) {\n      matRef.current.map.offset.x = 0.0; //-matRef.current.map.repeat.x\n      matRef.current.map.offset.y = 1 - frameOffsetY;\n    }\n    if (onStart) {\n      onStart({\n        currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n        currentFrame: currentFrame.current\n      });\n    }\n  }, [flipOffset, frameName, onStart]);\n  const state = React.useMemo(() => ({\n    current: pos.current,\n    offset: pos.current,\n    imageUrl: textureImageURL,\n    hasEnded: false,\n    ref: fref\n  }), [textureImageURL, fref]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    pos.current = offset;\n  }, [offset]);\n  const calculateAspectRatio = (width, height) => {\n    var _spriteRef$current;\n    const ret = new THREE.Vector3();\n    const aspectRatio = height / width;\n    ret.set(1, aspectRatio, 1);\n    (_spriteRef$current = spriteRef.current) == null || _spriteRef$current.scale.copy(ret);\n    return ret;\n  };\n\n  // initial loads\n  React.useEffect(() => {\n    if (spriteDataset) {\n      var _spriteDataset$sprite;\n      parseSpriteDataLite(spriteDataset == null || (_spriteDataset$sprite = spriteDataset.spriteTexture) == null ? void 0 : _spriteDataset$sprite.clone(), spriteDataset.spriteData);\n    } else {\n      if (textureImageURL && textureDataURL) {\n        loadJsonAndTexture(textureImageURL, textureDataURL);\n      }\n    }\n  }, [loadJsonAndTexture, spriteDataset, textureDataURL, textureImageURL, parseSpriteDataLite]);\n  React.useEffect(() => {\n    if (spriteObj) {\n      var _spriteObj$spriteText;\n      parseSpriteDataLite(spriteObj == null || (_spriteObj$spriteText = spriteObj.spriteTexture) == null ? void 0 : _spriteObj$spriteText.clone(), spriteObj == null ? void 0 : spriteObj.spriteData);\n    }\n  }, [spriteObj, parseSpriteDataLite]);\n\n  // support backwards play\n  React.useEffect(() => {\n    state.hasEnded = false;\n    if (spriteData.current && playBackwards === true) {\n      var _ref;\n      currentFrame.current = ((_ref = spriteData.current.frames.length) !== null && _ref !== void 0 ? _ref : 0) - 1;\n    } else {\n      currentFrame.current = 0;\n    }\n  }, [playBackwards, state]);\n  React.useLayoutEffect(() => {\n    modifySpritePosition();\n  }, [spriteTexture, flipX, modifySpritePosition]);\n  React.useEffect(() => {\n    if (autoPlay) {\n      pauseRef.current = false;\n    }\n  }, [autoPlay]);\n  React.useLayoutEffect(() => {\n    if (currentFrameName.current !== frameName && frameName) {\n      currentFrame.current = 0;\n      currentFrameName.current = frameName;\n      state.hasEnded = false;\n      if (fpsInterval <= 0) {\n        currentFrame.current = endFrame || startFrame || 0;\n      }\n      // modifySpritePosition()\n      if (spriteData.current) {\n        const {\n          w,\n          h\n        } = getFirstFrame(spriteData.current.frames, frameName).sourceSize;\n        const _aspect = calculateAspectRatio(w, h);\n        setAspect(_aspect);\n      }\n    }\n  }, [frameName, fpsInterval, state, endFrame, startFrame]);\n\n  // run the animation on each frame\n  const runAnimation = () => {\n    if (!isSpriteData(spriteData.current)) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = getFirstFrame(frames, frameName).sourceSize;\n    const spriteFrames = Array.isArray(frames) ? frames : frameName ? frames[frameName] : [];\n    const _endFrame = endFrame || spriteFrames.length - 1;\n    var _offset = offset === undefined ? state.current : offset;\n    if (fpsInterval <= 0) {\n      currentFrame.current = endFrame || startFrame || 0;\n      calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames);\n      return;\n    }\n    const now = window.performance.now();\n    const diff = now - timerOffset.current;\n    if (diff <= fpsInterval) return;\n\n    // conditionals to support backwards play\n    var endCondition = playBackwards ? currentFrame.current < 0 : currentFrame.current > _endFrame;\n    var onStartCondition = playBackwards ? currentFrame.current === _endFrame : currentFrame.current === 0;\n    var manualProgressEndCondition = playBackwards ? currentFrame.current < 0 : currentFrame.current >= _endFrame;\n    if (endCondition) {\n      currentFrame.current = loop ? startFrame !== null && startFrame !== void 0 ? startFrame : 0 : 0;\n      if (playBackwards) {\n        currentFrame.current = _endFrame;\n      }\n      if (loop) {\n        onLoopEnd == null || onLoopEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n      } else {\n        onEnd == null || onEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n        state.hasEnded = !resetOnEnd;\n        if (resetOnEnd) {\n          pauseRef.current = true;\n          //calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames)\n        }\n      }\n      if (!loop) return;\n    } else if (onStartCondition) {\n      onStart == null || onStart({\n        currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n        currentFrame: currentFrame.current\n      });\n    }\n\n    // for manual update\n    if (_offset !== undefined && manualProgressEndCondition) {\n      if (softEnd.current === false) {\n        onEnd == null || onEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n        softEnd.current = true;\n      }\n    } else {\n      // same for start?\n      softEnd.current = false;\n    }\n\n    // clock to limit fps\n    if (diff <= fpsInterval) return;\n    timerOffset.current = now - diff % fpsInterval;\n    calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames);\n  };\n  const calculateFinalPosition = (frameW, frameH, metaInfo, spriteFrames) => {\n    // get the manual update offset to find the next frame\n    var _offset = offset === undefined ? state.current : offset;\n    const targetFrame = currentFrame.current;\n    let finalValX = 0;\n    let finalValY = 0;\n    calculateAspectRatio(frameW, frameH);\n    const framesH = roundFramePosition ? Math.round((metaInfo.w - 1) / frameW) : (metaInfo.w - 1) / frameW;\n    const framesV = roundFramePosition ? Math.round((metaInfo.h - 1) / frameH) : (metaInfo.h - 1) / frameH;\n    if (!spriteFrames[targetFrame]) {\n      return;\n    }\n    const {\n      frame: {\n        x: frameX,\n        y: frameY\n      },\n      sourceSize: {\n        w: originalSizeX,\n        h: originalSizeY\n      }\n    } = spriteFrames[targetFrame];\n    const frameOffsetX = 1 / framesH;\n    const frameOffsetY = 1 / framesV;\n    if (matRef.current && matRef.current.map) {\n      finalValX = flipOffset > 0 ? frameOffsetX * (frameX / originalSizeX) : frameOffsetX * (frameX / originalSizeX) - matRef.current.map.repeat.x;\n      finalValY = Math.abs(1 - frameOffsetY) - frameOffsetY * (frameY / originalSizeY);\n      matRef.current.map.offset.x = finalValX;\n      matRef.current.map.offset.y = finalValY;\n    }\n\n    // if manual update is active\n    if (_offset !== undefined && _offset !== null) {\n      // Calculate the frame index, based on offset given from the provider\n      let frameIndex = Math.floor(_offset * spriteFrames.length);\n\n      // Ensure the frame index is within the valid range\n      frameIndex = Math.max(0, Math.min(frameIndex, spriteFrames.length - 1));\n      if (isNaN(frameIndex)) {\n        frameIndex = 0; //fallback\n      }\n      currentFrame.current = frameIndex;\n    } else {\n      // auto update\n      if (playBackwards) {\n        currentFrame.current -= 1;\n      } else {\n        currentFrame.current += 1;\n      }\n    }\n  };\n\n  // *** Warning! It runs on every frame! ***\n  useFrame((_state, _delta) => {\n    var _spriteData$current2, _matRef$current;\n    if (!((_spriteData$current2 = spriteData.current) != null && _spriteData$current2.frames) || !((_matRef$current = matRef.current) != null && _matRef$current.map)) {\n      return;\n    }\n    if (pauseRef.current) {\n      return;\n    }\n    if (!state.hasEnded && (autoPlay || play)) {\n      runAnimation();\n      onFrame == null || onFrame({\n        currentFrameName: currentFrameName.current,\n        currentFrame: currentFrame.current\n      });\n    }\n  });\n  function multiplyScale(initialScale = new THREE.Vector3(1, 1, 1), newScale = 1) {\n    if (typeof newScale === 'number') return initialScale.multiplyScalar(newScale);\n    if (Array.isArray(newScale)) return initialScale.multiply(new THREE.Vector3(...newScale));\n    if (newScale instanceof THREE.Vector3) return initialScale.multiply(newScale);\n  }\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: ref,\n    scale: multiplyScale(aspect, props.scale)\n  }), /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, asSprite && /*#__PURE__*/React.createElement(Billboard, null, /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: spriteRef,\n    scale: 1.0,\n    geometry: geometry\n  }, meshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    premultipliedAlpha: false,\n    toneMapped: false,\n    side: THREE.DoubleSide,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }))), !asSprite && /*#__PURE__*/React.createElement(Instances, _extends({\n    geometry: geometry,\n    limit: maxItems !== null && maxItems !== void 0 ? maxItems : 1\n  }, meshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    premultipliedAlpha: false,\n    toneMapped: false,\n    side: THREE.DoubleSide,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }), (instanceItems !== null && instanceItems !== void 0 ? instanceItems : [0]).map((item, index) => /*#__PURE__*/React.createElement(Instance, _extends({\n    key: index,\n    ref: (instanceItems == null ? void 0 : instanceItems.length) === 1 ? spriteRef : null,\n    position: item,\n    scale: 1.0\n  }, meshProps)))), children));\n});\nexport { SpriteAnimator, useSpriteAnimator };", "map": {"version": 3, "names": ["_extends", "React", "useFrame", "THREE", "Instances", "Instance", "Billboard", "useSpriteLoader", "getFirstFrame", "context", "createContext", "useSpriteAnimator", "useContext", "isSpriteData", "data", "geometry", "PlaneGeometry", "SpriteAnimator", "forwardRef", "startFrame", "endFrame", "fps", "frameName", "textureDataURL", "textureImageURL", "loop", "numberOfFrames", "autoPlay", "animationNames", "onStart", "onEnd", "onLoopEnd", "onFrame", "play", "pause", "flipX", "alphaTest", "children", "asSprite", "offset", "playBackwards", "resetOnEnd", "maxItems", "instanceItems", "spriteDataset", "canvasRenderingContext2DSettings", "roundFramePosition", "meshProps", "props", "fref", "ref", "useRef", "Group", "spriteData", "mat<PERSON><PERSON>", "spriteRef", "timerOffset", "window", "performance", "now", "currentFrame", "currentFrameName", "fpsInterval", "spriteTexture", "setSpriteTexture", "useState", "Texture", "totalFrames", "aspect", "setAspect", "Vector3", "flipOffset", "pauseRef", "pos", "softEnd", "spriteObj", "loadJsonAndTexture", "undefined", "frameNameRef", "parseSpriteDataLite", "useCallback", "textureData", "current", "_spriteData$current$f", "_spriteData$current", "Array", "isArray", "frames", "length", "w", "h", "sourceSize", "calculateAspectRatio", "map", "modifySpritePosition", "meta", "size", "metaInfo", "frameW", "frameH", "wrapS", "wrapT", "RepeatWrapping", "center", "set", "repeat", "framesV", "frameOffsetY", "x", "y", "state", "useMemo", "imageUrl", "hasEnded", "useImperativeHandle", "useLayoutEffect", "width", "height", "_spriteRef$current", "ret", "aspectRatio", "scale", "copy", "useEffect", "_spriteDataset$sprite", "clone", "_spriteObj$spriteText", "_ref", "_aspect", "runAnimation", "spriteFrames", "_endFrame", "_offset", "calculateFinalPosition", "diff", "endCondition", "onStartCondition", "manualProgressEndCondition", "targetFrame", "finalValX", "finalValY", "framesH", "Math", "round", "frame", "frameX", "frameY", "originalSizeX", "originalSizeY", "frameOffsetX", "abs", "frameIndex", "floor", "max", "min", "isNaN", "_state", "_delta", "_spriteData$current2", "_matRef$current", "multiplyScale", "initialScale", "newScale", "multiplyScalar", "multiply", "createElement", "Provider", "value", "premultipliedAlpha", "toneMapped", "side", "DoubleSide", "transparent", "limit", "item", "index", "key", "position"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/SpriteAnimator.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\nimport { Instances, Instance } from './Instances.js';\nimport { Billboard } from './Billboard.js';\nimport { useSpriteLoader, getFirstFrame } from './useSpriteLoader.js';\n\n// Frame-related types\n\nconst context = /*#__PURE__*/React.createContext(null);\nfunction useSpriteAnimator() {\n  return React.useContext(context);\n}\n\n// Type guard for SpriteData\nfunction isSpriteData(data) {\n  return data !== null && 'meta' in data && 'frames' in data;\n}\nconst geometry = /* @__PURE__ */new THREE.PlaneGeometry(1, 1);\nconst SpriteAnimator = /* @__PURE__ */React.forwardRef(({\n  startFrame = 0,\n  endFrame,\n  fps = 30,\n  frameName = '',\n  textureDataURL,\n  textureImageURL,\n  loop = false,\n  numberOfFrames = 1,\n  autoPlay = true,\n  animationNames,\n  onStart,\n  onEnd,\n  onLoopEnd,\n  onFrame,\n  play,\n  pause = false,\n  flipX = false,\n  alphaTest = 0.0,\n  children,\n  asSprite = false,\n  offset,\n  playBackwards = false,\n  resetOnEnd = false,\n  maxItems = 1,\n  instanceItems = [[0, 0, 0]],\n  spriteDataset,\n  canvasRenderingContext2DSettings,\n  roundFramePosition = false,\n  meshProps = {},\n  ...props\n}, fref) => {\n  const ref = React.useRef(new THREE.Group());\n  const spriteData = React.useRef(null);\n  const matRef = React.useRef(null);\n  const spriteRef = React.useRef(null);\n  const timerOffset = React.useRef(window.performance.now());\n  const currentFrame = React.useRef(startFrame);\n  const currentFrameName = React.useRef(frameName);\n  const fpsInterval = fps > 0 ? 1000 / fps : 0;\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const totalFrames = React.useRef(0);\n  const [aspect, setAspect] = React.useState(new THREE.Vector3(1, 1, 1));\n  const flipOffset = flipX ? -1 : 1;\n  const pauseRef = React.useRef(pause);\n  const pos = React.useRef(offset);\n  const softEnd = React.useRef(false);\n  const {\n    spriteObj,\n    loadJsonAndTexture\n  } = useSpriteLoader(null, null, animationNames, numberOfFrames, undefined, canvasRenderingContext2DSettings);\n  const frameNameRef = React.useRef(frameName);\n\n  // lite version for pre-loaded assets\n  const parseSpriteDataLite = React.useCallback((textureData, data) => {\n    if (data === null) {\n      if (numberOfFrames) {\n        //get size from texture\n\n        totalFrames.current = numberOfFrames;\n        if (playBackwards) {\n          currentFrame.current = numberOfFrames - 1;\n        }\n        spriteData.current = data;\n      }\n    } else {\n      var _spriteData$current$f, _spriteData$current;\n      spriteData.current = data;\n      if (spriteData.current && Array.isArray(spriteData.current.frames)) {\n        totalFrames.current = spriteData.current.frames.length;\n      } else if (spriteData.current && typeof spriteData.current === 'object' && frameNameRef.current) {\n        totalFrames.current = spriteData.current.frames[frameNameRef.current].length;\n      } else {\n        totalFrames.current = 0;\n      }\n      if (playBackwards) {\n        currentFrame.current = totalFrames.current - 1;\n      }\n      const {\n        w,\n        h\n      } = getFirstFrame((_spriteData$current$f = (_spriteData$current = spriteData.current) == null ? void 0 : _spriteData$current.frames) !== null && _spriteData$current$f !== void 0 ? _spriteData$current$f : [], frameNameRef.current).sourceSize;\n      const aspect = calculateAspectRatio(w, h);\n      setAspect(aspect);\n      if (matRef.current) {\n        matRef.current.map = textureData;\n      }\n    }\n    setSpriteTexture(textureData);\n  }, [numberOfFrames, playBackwards]);\n\n  // modify the sprite material after json is parsed and state updated\n  const modifySpritePosition = React.useCallback(() => {\n    if (!spriteData.current) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = Array.isArray(frames) ? frames[0].sourceSize : frameName ? frames[frameName] ? frames[frameName][0].sourceSize : {\n      w: 0,\n      h: 0\n    } : {\n      w: 0,\n      h: 0\n    };\n    if (matRef.current && matRef.current.map) {\n      matRef.current.map.wrapS = matRef.current.map.wrapT = THREE.RepeatWrapping;\n      matRef.current.map.center.set(0, 0);\n      matRef.current.map.repeat.set(1 * flipOffset / (metaInfo.w / frameW), 1 / (metaInfo.h / frameH));\n    }\n    //const framesH = (metaInfo.w - 1) / frameW\n    const framesV = (metaInfo.h - 1) / frameH;\n    const frameOffsetY = 1 / framesV;\n    if (matRef.current && matRef.current.map) {\n      matRef.current.map.offset.x = 0.0; //-matRef.current.map.repeat.x\n      matRef.current.map.offset.y = 1 - frameOffsetY;\n    }\n    if (onStart) {\n      onStart({\n        currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n        currentFrame: currentFrame.current\n      });\n    }\n  }, [flipOffset, frameName, onStart]);\n  const state = React.useMemo(() => ({\n    current: pos.current,\n    offset: pos.current,\n    imageUrl: textureImageURL,\n    hasEnded: false,\n    ref: fref\n  }), [textureImageURL, fref]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    pos.current = offset;\n  }, [offset]);\n  const calculateAspectRatio = (width, height) => {\n    var _spriteRef$current;\n    const ret = new THREE.Vector3();\n    const aspectRatio = height / width;\n    ret.set(1, aspectRatio, 1);\n    (_spriteRef$current = spriteRef.current) == null || _spriteRef$current.scale.copy(ret);\n    return ret;\n  };\n\n  // initial loads\n  React.useEffect(() => {\n    if (spriteDataset) {\n      var _spriteDataset$sprite;\n      parseSpriteDataLite(spriteDataset == null || (_spriteDataset$sprite = spriteDataset.spriteTexture) == null ? void 0 : _spriteDataset$sprite.clone(), spriteDataset.spriteData);\n    } else {\n      if (textureImageURL && textureDataURL) {\n        loadJsonAndTexture(textureImageURL, textureDataURL);\n      }\n    }\n  }, [loadJsonAndTexture, spriteDataset, textureDataURL, textureImageURL, parseSpriteDataLite]);\n  React.useEffect(() => {\n    if (spriteObj) {\n      var _spriteObj$spriteText;\n      parseSpriteDataLite(spriteObj == null || (_spriteObj$spriteText = spriteObj.spriteTexture) == null ? void 0 : _spriteObj$spriteText.clone(), spriteObj == null ? void 0 : spriteObj.spriteData);\n    }\n  }, [spriteObj, parseSpriteDataLite]);\n\n  // support backwards play\n  React.useEffect(() => {\n    state.hasEnded = false;\n    if (spriteData.current && playBackwards === true) {\n      var _ref;\n      currentFrame.current = ((_ref = spriteData.current.frames.length) !== null && _ref !== void 0 ? _ref : 0) - 1;\n    } else {\n      currentFrame.current = 0;\n    }\n  }, [playBackwards, state]);\n  React.useLayoutEffect(() => {\n    modifySpritePosition();\n  }, [spriteTexture, flipX, modifySpritePosition]);\n  React.useEffect(() => {\n    if (autoPlay) {\n      pauseRef.current = false;\n    }\n  }, [autoPlay]);\n  React.useLayoutEffect(() => {\n    if (currentFrameName.current !== frameName && frameName) {\n      currentFrame.current = 0;\n      currentFrameName.current = frameName;\n      state.hasEnded = false;\n      if (fpsInterval <= 0) {\n        currentFrame.current = endFrame || startFrame || 0;\n      }\n      // modifySpritePosition()\n      if (spriteData.current) {\n        const {\n          w,\n          h\n        } = getFirstFrame(spriteData.current.frames, frameName).sourceSize;\n        const _aspect = calculateAspectRatio(w, h);\n        setAspect(_aspect);\n      }\n    }\n  }, [frameName, fpsInterval, state, endFrame, startFrame]);\n\n  // run the animation on each frame\n  const runAnimation = () => {\n    if (!isSpriteData(spriteData.current)) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = getFirstFrame(frames, frameName).sourceSize;\n    const spriteFrames = Array.isArray(frames) ? frames : frameName ? frames[frameName] : [];\n    const _endFrame = endFrame || spriteFrames.length - 1;\n    var _offset = offset === undefined ? state.current : offset;\n    if (fpsInterval <= 0) {\n      currentFrame.current = endFrame || startFrame || 0;\n      calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames);\n      return;\n    }\n    const now = window.performance.now();\n    const diff = now - timerOffset.current;\n    if (diff <= fpsInterval) return;\n\n    // conditionals to support backwards play\n    var endCondition = playBackwards ? currentFrame.current < 0 : currentFrame.current > _endFrame;\n    var onStartCondition = playBackwards ? currentFrame.current === _endFrame : currentFrame.current === 0;\n    var manualProgressEndCondition = playBackwards ? currentFrame.current < 0 : currentFrame.current >= _endFrame;\n    if (endCondition) {\n      currentFrame.current = loop ? startFrame !== null && startFrame !== void 0 ? startFrame : 0 : 0;\n      if (playBackwards) {\n        currentFrame.current = _endFrame;\n      }\n      if (loop) {\n        onLoopEnd == null || onLoopEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n      } else {\n        onEnd == null || onEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n        state.hasEnded = !resetOnEnd;\n        if (resetOnEnd) {\n          pauseRef.current = true;\n          //calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames)\n        }\n      }\n      if (!loop) return;\n    } else if (onStartCondition) {\n      onStart == null || onStart({\n        currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n        currentFrame: currentFrame.current\n      });\n    }\n\n    // for manual update\n    if (_offset !== undefined && manualProgressEndCondition) {\n      if (softEnd.current === false) {\n        onEnd == null || onEnd({\n          currentFrameName: frameName !== null && frameName !== void 0 ? frameName : '',\n          currentFrame: currentFrame.current\n        });\n        softEnd.current = true;\n      }\n    } else {\n      // same for start?\n      softEnd.current = false;\n    }\n\n    // clock to limit fps\n    if (diff <= fpsInterval) return;\n    timerOffset.current = now - diff % fpsInterval;\n    calculateFinalPosition(frameW, frameH, metaInfo, spriteFrames);\n  };\n  const calculateFinalPosition = (frameW, frameH, metaInfo, spriteFrames) => {\n    // get the manual update offset to find the next frame\n    var _offset = offset === undefined ? state.current : offset;\n    const targetFrame = currentFrame.current;\n    let finalValX = 0;\n    let finalValY = 0;\n    calculateAspectRatio(frameW, frameH);\n    const framesH = roundFramePosition ? Math.round((metaInfo.w - 1) / frameW) : (metaInfo.w - 1) / frameW;\n    const framesV = roundFramePosition ? Math.round((metaInfo.h - 1) / frameH) : (metaInfo.h - 1) / frameH;\n    if (!spriteFrames[targetFrame]) {\n      return;\n    }\n    const {\n      frame: {\n        x: frameX,\n        y: frameY\n      },\n      sourceSize: {\n        w: originalSizeX,\n        h: originalSizeY\n      }\n    } = spriteFrames[targetFrame];\n    const frameOffsetX = 1 / framesH;\n    const frameOffsetY = 1 / framesV;\n    if (matRef.current && matRef.current.map) {\n      finalValX = flipOffset > 0 ? frameOffsetX * (frameX / originalSizeX) : frameOffsetX * (frameX / originalSizeX) - matRef.current.map.repeat.x;\n      finalValY = Math.abs(1 - frameOffsetY) - frameOffsetY * (frameY / originalSizeY);\n      matRef.current.map.offset.x = finalValX;\n      matRef.current.map.offset.y = finalValY;\n    }\n\n    // if manual update is active\n    if (_offset !== undefined && _offset !== null) {\n      // Calculate the frame index, based on offset given from the provider\n      let frameIndex = Math.floor(_offset * spriteFrames.length);\n\n      // Ensure the frame index is within the valid range\n      frameIndex = Math.max(0, Math.min(frameIndex, spriteFrames.length - 1));\n      if (isNaN(frameIndex)) {\n        frameIndex = 0; //fallback\n      }\n      currentFrame.current = frameIndex;\n    } else {\n      // auto update\n      if (playBackwards) {\n        currentFrame.current -= 1;\n      } else {\n        currentFrame.current += 1;\n      }\n    }\n  };\n\n  // *** Warning! It runs on every frame! ***\n  useFrame((_state, _delta) => {\n    var _spriteData$current2, _matRef$current;\n    if (!((_spriteData$current2 = spriteData.current) != null && _spriteData$current2.frames) || !((_matRef$current = matRef.current) != null && _matRef$current.map)) {\n      return;\n    }\n    if (pauseRef.current) {\n      return;\n    }\n    if (!state.hasEnded && (autoPlay || play)) {\n      runAnimation();\n      onFrame == null || onFrame({\n        currentFrameName: currentFrameName.current,\n        currentFrame: currentFrame.current\n      });\n    }\n  });\n  function multiplyScale(initialScale = new THREE.Vector3(1, 1, 1), newScale = 1) {\n    if (typeof newScale === 'number') return initialScale.multiplyScalar(newScale);\n    if (Array.isArray(newScale)) return initialScale.multiply(new THREE.Vector3(...newScale));\n    if (newScale instanceof THREE.Vector3) return initialScale.multiply(newScale);\n  }\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: ref,\n    scale: multiplyScale(aspect, props.scale)\n  }), /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, asSprite && /*#__PURE__*/React.createElement(Billboard, null, /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: spriteRef,\n    scale: 1.0,\n    geometry: geometry\n  }, meshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    premultipliedAlpha: false,\n    toneMapped: false,\n    side: THREE.DoubleSide,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }))), !asSprite && /*#__PURE__*/React.createElement(Instances, _extends({\n    geometry: geometry,\n    limit: maxItems !== null && maxItems !== void 0 ? maxItems : 1\n  }, meshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    premultipliedAlpha: false,\n    toneMapped: false,\n    side: THREE.DoubleSide,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }), (instanceItems !== null && instanceItems !== void 0 ? instanceItems : [0]).map((item, index) => /*#__PURE__*/React.createElement(Instance, _extends({\n    key: index,\n    ref: (instanceItems == null ? void 0 : instanceItems.length) === 1 ? spriteRef : null,\n    position: item,\n    scale: 1.0\n  }, meshProps)))), children));\n});\n\nexport { SpriteAnimator, useSpriteAnimator };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,eAAe,EAAEC,aAAa,QAAQ,sBAAsB;;AAErE;;AAEA,MAAMC,OAAO,GAAG,aAAaR,KAAK,CAACS,aAAa,CAAC,IAAI,CAAC;AACtD,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,OAAOV,KAAK,CAACW,UAAU,CAACH,OAAO,CAAC;AAClC;;AAEA;AACA,SAASI,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,KAAK,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,QAAQ,IAAIA,IAAI;AAC5D;AACA,MAAMC,QAAQ,GAAG,eAAe,IAAIZ,KAAK,CAACa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7D,MAAMC,cAAc,GAAG,eAAehB,KAAK,CAACiB,UAAU,CAAC,CAAC;EACtDC,UAAU,GAAG,CAAC;EACdC,QAAQ;EACRC,GAAG,GAAG,EAAE;EACRC,SAAS,GAAG,EAAE;EACdC,cAAc;EACdC,eAAe;EACfC,IAAI,GAAG,KAAK;EACZC,cAAc,GAAG,CAAC;EAClBC,QAAQ,GAAG,IAAI;EACfC,cAAc;EACdC,OAAO;EACPC,KAAK;EACLC,SAAS;EACTC,OAAO;EACPC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,KAAK,GAAG,KAAK;EACbC,SAAS,GAAG,GAAG;EACfC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChBC,MAAM;EACNC,aAAa,GAAG,KAAK;EACrBC,UAAU,GAAG,KAAK;EAClBC,QAAQ,GAAG,CAAC;EACZC,aAAa,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3BC,aAAa;EACbC,gCAAgC;EAChCC,kBAAkB,GAAG,KAAK;EAC1BC,SAAS,GAAG,CAAC,CAAC;EACd,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGjD,KAAK,CAACkD,MAAM,CAAC,IAAIhD,KAAK,CAACiD,KAAK,CAAC,CAAC,CAAC;EAC3C,MAAMC,UAAU,GAAGpD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMG,MAAM,GAAGrD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMI,SAAS,GAAGtD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMK,WAAW,GAAGvD,KAAK,CAACkD,MAAM,CAACM,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;EAC1D,MAAMC,YAAY,GAAG3D,KAAK,CAACkD,MAAM,CAAChC,UAAU,CAAC;EAC7C,MAAM0C,gBAAgB,GAAG5D,KAAK,CAACkD,MAAM,CAAC7B,SAAS,CAAC;EAChD,MAAMwC,WAAW,GAAGzC,GAAG,GAAG,CAAC,GAAG,IAAI,GAAGA,GAAG,GAAG,CAAC;EAC5C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,KAAK,CAACgE,QAAQ,CAAC,IAAI9D,KAAK,CAAC+D,OAAO,CAAC,CAAC,CAAC;EAC7E,MAAMC,WAAW,GAAGlE,KAAK,CAACkD,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGpE,KAAK,CAACgE,QAAQ,CAAC,IAAI9D,KAAK,CAACmE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACtE,MAAMC,UAAU,GAAGpC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;EACjC,MAAMqC,QAAQ,GAAGvE,KAAK,CAACkD,MAAM,CAACjB,KAAK,CAAC;EACpC,MAAMuC,GAAG,GAAGxE,KAAK,CAACkD,MAAM,CAACZ,MAAM,CAAC;EAChC,MAAMmC,OAAO,GAAGzE,KAAK,CAACkD,MAAM,CAAC,KAAK,CAAC;EACnC,MAAM;IACJwB,SAAS;IACTC;EACF,CAAC,GAAGrE,eAAe,CAAC,IAAI,EAAE,IAAI,EAAEqB,cAAc,EAAEF,cAAc,EAAEmD,SAAS,EAAEhC,gCAAgC,CAAC;EAC5G,MAAMiC,YAAY,GAAG7E,KAAK,CAACkD,MAAM,CAAC7B,SAAS,CAAC;;EAE5C;EACA,MAAMyD,mBAAmB,GAAG9E,KAAK,CAAC+E,WAAW,CAAC,CAACC,WAAW,EAAEnE,IAAI,KAAK;IACnE,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIY,cAAc,EAAE;QAClB;;QAEAyC,WAAW,CAACe,OAAO,GAAGxD,cAAc;QACpC,IAAIc,aAAa,EAAE;UACjBoB,YAAY,CAACsB,OAAO,GAAGxD,cAAc,GAAG,CAAC;QAC3C;QACA2B,UAAU,CAAC6B,OAAO,GAAGpE,IAAI;MAC3B;IACF,CAAC,MAAM;MACL,IAAIqE,qBAAqB,EAAEC,mBAAmB;MAC9C/B,UAAU,CAAC6B,OAAO,GAAGpE,IAAI;MACzB,IAAIuC,UAAU,CAAC6B,OAAO,IAAIG,KAAK,CAACC,OAAO,CAACjC,UAAU,CAAC6B,OAAO,CAACK,MAAM,CAAC,EAAE;QAClEpB,WAAW,CAACe,OAAO,GAAG7B,UAAU,CAAC6B,OAAO,CAACK,MAAM,CAACC,MAAM;MACxD,CAAC,MAAM,IAAInC,UAAU,CAAC6B,OAAO,IAAI,OAAO7B,UAAU,CAAC6B,OAAO,KAAK,QAAQ,IAAIJ,YAAY,CAACI,OAAO,EAAE;QAC/Ff,WAAW,CAACe,OAAO,GAAG7B,UAAU,CAAC6B,OAAO,CAACK,MAAM,CAACT,YAAY,CAACI,OAAO,CAAC,CAACM,MAAM;MAC9E,CAAC,MAAM;QACLrB,WAAW,CAACe,OAAO,GAAG,CAAC;MACzB;MACA,IAAI1C,aAAa,EAAE;QACjBoB,YAAY,CAACsB,OAAO,GAAGf,WAAW,CAACe,OAAO,GAAG,CAAC;MAChD;MACA,MAAM;QACJO,CAAC;QACDC;MACF,CAAC,GAAGlF,aAAa,CAAC,CAAC2E,qBAAqB,GAAG,CAACC,mBAAmB,GAAG/B,UAAU,CAAC6B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,mBAAmB,CAACG,MAAM,MAAM,IAAI,IAAIJ,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE,EAAEL,YAAY,CAACI,OAAO,CAAC,CAACS,UAAU;MAChP,MAAMvB,MAAM,GAAGwB,oBAAoB,CAACH,CAAC,EAAEC,CAAC,CAAC;MACzCrB,SAAS,CAACD,MAAM,CAAC;MACjB,IAAId,MAAM,CAAC4B,OAAO,EAAE;QAClB5B,MAAM,CAAC4B,OAAO,CAACW,GAAG,GAAGZ,WAAW;MAClC;IACF;IACAjB,gBAAgB,CAACiB,WAAW,CAAC;EAC/B,CAAC,EAAE,CAACvD,cAAc,EAAEc,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMsD,oBAAoB,GAAG7F,KAAK,CAAC+E,WAAW,CAAC,MAAM;IACnD,IAAI,CAAC3B,UAAU,CAAC6B,OAAO,EAAE;IACzB,MAAM;MACJa,IAAI,EAAE;QACJC,IAAI,EAAEC;MACR,CAAC;MACDV;IACF,CAAC,GAAGlC,UAAU,CAAC6B,OAAO;IACtB,MAAM;MACJO,CAAC,EAAES,MAAM;MACTR,CAAC,EAAES;IACL,CAAC,GAAGd,KAAK,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACI,UAAU,GAAGrE,SAAS,GAAGiE,MAAM,CAACjE,SAAS,CAAC,GAAGiE,MAAM,CAACjE,SAAS,CAAC,CAAC,CAAC,CAAC,CAACqE,UAAU,GAAG;MACnHF,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,GAAG;MACFD,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD,IAAIpC,MAAM,CAAC4B,OAAO,IAAI5B,MAAM,CAAC4B,OAAO,CAACW,GAAG,EAAE;MACxCvC,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACO,KAAK,GAAG9C,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACQ,KAAK,GAAGlG,KAAK,CAACmG,cAAc;MAC1EhD,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACU,MAAM,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACnClD,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACY,MAAM,CAACD,GAAG,CAAC,CAAC,GAAGjC,UAAU,IAAI0B,QAAQ,CAACR,CAAC,GAAGS,MAAM,CAAC,EAAE,CAAC,IAAID,QAAQ,CAACP,CAAC,GAAGS,MAAM,CAAC,CAAC;IAClG;IACA;IACA,MAAMO,OAAO,GAAG,CAACT,QAAQ,CAACP,CAAC,GAAG,CAAC,IAAIS,MAAM;IACzC,MAAMQ,YAAY,GAAG,CAAC,GAAGD,OAAO;IAChC,IAAIpD,MAAM,CAAC4B,OAAO,IAAI5B,MAAM,CAAC4B,OAAO,CAACW,GAAG,EAAE;MACxCvC,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACtD,MAAM,CAACqE,CAAC,GAAG,GAAG,CAAC,CAAC;MACnCtD,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACtD,MAAM,CAACsE,CAAC,GAAG,CAAC,GAAGF,YAAY;IAChD;IACA,IAAI9E,OAAO,EAAE;MACXA,OAAO,CAAC;QACNgC,gBAAgB,EAAEvC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;QAC7EsC,YAAY,EAAEA,YAAY,CAACsB;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACX,UAAU,EAAEjD,SAAS,EAAEO,OAAO,CAAC,CAAC;EACpC,MAAMiF,KAAK,GAAG7G,KAAK,CAAC8G,OAAO,CAAC,OAAO;IACjC7B,OAAO,EAAET,GAAG,CAACS,OAAO;IACpB3C,MAAM,EAAEkC,GAAG,CAACS,OAAO;IACnB8B,QAAQ,EAAExF,eAAe;IACzByF,QAAQ,EAAE,KAAK;IACf/D,GAAG,EAAED;EACP,CAAC,CAAC,EAAE,CAACzB,eAAe,EAAEyB,IAAI,CAAC,CAAC;EAC5BhD,KAAK,CAACiH,mBAAmB,CAACjE,IAAI,EAAE,MAAMC,GAAG,CAACgC,OAAO,EAAE,EAAE,CAAC;EACtDjF,KAAK,CAACkH,eAAe,CAAC,MAAM;IAC1B1C,GAAG,CAACS,OAAO,GAAG3C,MAAM;EACtB,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMqD,oBAAoB,GAAGA,CAACwB,KAAK,EAAEC,MAAM,KAAK;IAC9C,IAAIC,kBAAkB;IACtB,MAAMC,GAAG,GAAG,IAAIpH,KAAK,CAACmE,OAAO,CAAC,CAAC;IAC/B,MAAMkD,WAAW,GAAGH,MAAM,GAAGD,KAAK;IAClCG,GAAG,CAACf,GAAG,CAAC,CAAC,EAAEgB,WAAW,EAAE,CAAC,CAAC;IAC1B,CAACF,kBAAkB,GAAG/D,SAAS,CAAC2B,OAAO,KAAK,IAAI,IAAIoC,kBAAkB,CAACG,KAAK,CAACC,IAAI,CAACH,GAAG,CAAC;IACtF,OAAOA,GAAG;EACZ,CAAC;;EAED;EACAtH,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpB,IAAI/E,aAAa,EAAE;MACjB,IAAIgF,qBAAqB;MACzB7C,mBAAmB,CAACnC,aAAa,IAAI,IAAI,IAAI,CAACgF,qBAAqB,GAAGhF,aAAa,CAACmB,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6D,qBAAqB,CAACC,KAAK,CAAC,CAAC,EAAEjF,aAAa,CAACS,UAAU,CAAC;IAChL,CAAC,MAAM;MACL,IAAI7B,eAAe,IAAID,cAAc,EAAE;QACrCqD,kBAAkB,CAACpD,eAAe,EAAED,cAAc,CAAC;MACrD;IACF;EACF,CAAC,EAAE,CAACqD,kBAAkB,EAAEhC,aAAa,EAAErB,cAAc,EAAEC,eAAe,EAAEuD,mBAAmB,CAAC,CAAC;EAC7F9E,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpB,IAAIhD,SAAS,EAAE;MACb,IAAImD,qBAAqB;MACzB/C,mBAAmB,CAACJ,SAAS,IAAI,IAAI,IAAI,CAACmD,qBAAqB,GAAGnD,SAAS,CAACZ,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+D,qBAAqB,CAACD,KAAK,CAAC,CAAC,EAAElD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,UAAU,CAAC;IACjM;EACF,CAAC,EAAE,CAACsB,SAAS,EAAEI,mBAAmB,CAAC,CAAC;;EAEpC;EACA9E,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpBb,KAAK,CAACG,QAAQ,GAAG,KAAK;IACtB,IAAI5D,UAAU,CAAC6B,OAAO,IAAI1C,aAAa,KAAK,IAAI,EAAE;MAChD,IAAIuF,IAAI;MACRnE,YAAY,CAACsB,OAAO,GAAG,CAAC,CAAC6C,IAAI,GAAG1E,UAAU,CAAC6B,OAAO,CAACK,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIuC,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,IAAI,CAAC;IAC/G,CAAC,MAAM;MACLnE,YAAY,CAACsB,OAAO,GAAG,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC1C,aAAa,EAAEsE,KAAK,CAAC,CAAC;EAC1B7G,KAAK,CAACkH,eAAe,CAAC,MAAM;IAC1BrB,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC/B,aAAa,EAAE5B,KAAK,EAAE2D,oBAAoB,CAAC,CAAC;EAChD7F,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpB,IAAIhG,QAAQ,EAAE;MACZ6C,QAAQ,CAACU,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC,EAAE,CAACvD,QAAQ,CAAC,CAAC;EACd1B,KAAK,CAACkH,eAAe,CAAC,MAAM;IAC1B,IAAItD,gBAAgB,CAACqB,OAAO,KAAK5D,SAAS,IAAIA,SAAS,EAAE;MACvDsC,YAAY,CAACsB,OAAO,GAAG,CAAC;MACxBrB,gBAAgB,CAACqB,OAAO,GAAG5D,SAAS;MACpCwF,KAAK,CAACG,QAAQ,GAAG,KAAK;MACtB,IAAInD,WAAW,IAAI,CAAC,EAAE;QACpBF,YAAY,CAACsB,OAAO,GAAG9D,QAAQ,IAAID,UAAU,IAAI,CAAC;MACpD;MACA;MACA,IAAIkC,UAAU,CAAC6B,OAAO,EAAE;QACtB,MAAM;UACJO,CAAC;UACDC;QACF,CAAC,GAAGlF,aAAa,CAAC6C,UAAU,CAAC6B,OAAO,CAACK,MAAM,EAAEjE,SAAS,CAAC,CAACqE,UAAU;QAClE,MAAMqC,OAAO,GAAGpC,oBAAoB,CAACH,CAAC,EAAEC,CAAC,CAAC;QAC1CrB,SAAS,CAAC2D,OAAO,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAAC1G,SAAS,EAAEwC,WAAW,EAAEgD,KAAK,EAAE1F,QAAQ,EAAED,UAAU,CAAC,CAAC;;EAEzD;EACA,MAAM8G,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACpH,YAAY,CAACwC,UAAU,CAAC6B,OAAO,CAAC,EAAE;IACvC,MAAM;MACJa,IAAI,EAAE;QACJC,IAAI,EAAEC;MACR,CAAC;MACDV;IACF,CAAC,GAAGlC,UAAU,CAAC6B,OAAO;IACtB,MAAM;MACJO,CAAC,EAAES,MAAM;MACTR,CAAC,EAAES;IACL,CAAC,GAAG3F,aAAa,CAAC+E,MAAM,EAAEjE,SAAS,CAAC,CAACqE,UAAU;IAC/C,MAAMuC,YAAY,GAAG7C,KAAK,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGA,MAAM,GAAGjE,SAAS,GAAGiE,MAAM,CAACjE,SAAS,CAAC,GAAG,EAAE;IACxF,MAAM6G,SAAS,GAAG/G,QAAQ,IAAI8G,YAAY,CAAC1C,MAAM,GAAG,CAAC;IACrD,IAAI4C,OAAO,GAAG7F,MAAM,KAAKsC,SAAS,GAAGiC,KAAK,CAAC5B,OAAO,GAAG3C,MAAM;IAC3D,IAAIuB,WAAW,IAAI,CAAC,EAAE;MACpBF,YAAY,CAACsB,OAAO,GAAG9D,QAAQ,IAAID,UAAU,IAAI,CAAC;MAClDkH,sBAAsB,CAACnC,MAAM,EAAEC,MAAM,EAAEF,QAAQ,EAAEiC,YAAY,CAAC;MAC9D;IACF;IACA,MAAMvE,GAAG,GAAGF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;IACpC,MAAM2E,IAAI,GAAG3E,GAAG,GAAGH,WAAW,CAAC0B,OAAO;IACtC,IAAIoD,IAAI,IAAIxE,WAAW,EAAE;;IAEzB;IACA,IAAIyE,YAAY,GAAG/F,aAAa,GAAGoB,YAAY,CAACsB,OAAO,GAAG,CAAC,GAAGtB,YAAY,CAACsB,OAAO,GAAGiD,SAAS;IAC9F,IAAIK,gBAAgB,GAAGhG,aAAa,GAAGoB,YAAY,CAACsB,OAAO,KAAKiD,SAAS,GAAGvE,YAAY,CAACsB,OAAO,KAAK,CAAC;IACtG,IAAIuD,0BAA0B,GAAGjG,aAAa,GAAGoB,YAAY,CAACsB,OAAO,GAAG,CAAC,GAAGtB,YAAY,CAACsB,OAAO,IAAIiD,SAAS;IAC7G,IAAII,YAAY,EAAE;MAChB3E,YAAY,CAACsB,OAAO,GAAGzD,IAAI,GAAGN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAG,CAAC;MAC/F,IAAIqB,aAAa,EAAE;QACjBoB,YAAY,CAACsB,OAAO,GAAGiD,SAAS;MAClC;MACA,IAAI1G,IAAI,EAAE;QACRM,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC;UAC7B8B,gBAAgB,EAAEvC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;UAC7EsC,YAAY,EAAEA,YAAY,CAACsB;QAC7B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpD,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC;UACrB+B,gBAAgB,EAAEvC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;UAC7EsC,YAAY,EAAEA,YAAY,CAACsB;QAC7B,CAAC,CAAC;QACF4B,KAAK,CAACG,QAAQ,GAAG,CAACxE,UAAU;QAC5B,IAAIA,UAAU,EAAE;UACd+B,QAAQ,CAACU,OAAO,GAAG,IAAI;UACvB;QACF;MACF;MACA,IAAI,CAACzD,IAAI,EAAE;IACb,CAAC,MAAM,IAAI+G,gBAAgB,EAAE;MAC3B3G,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC;QACzBgC,gBAAgB,EAAEvC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;QAC7EsC,YAAY,EAAEA,YAAY,CAACsB;MAC7B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIkD,OAAO,KAAKvD,SAAS,IAAI4D,0BAA0B,EAAE;MACvD,IAAI/D,OAAO,CAACQ,OAAO,KAAK,KAAK,EAAE;QAC7BpD,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC;UACrB+B,gBAAgB,EAAEvC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;UAC7EsC,YAAY,EAAEA,YAAY,CAACsB;QAC7B,CAAC,CAAC;QACFR,OAAO,CAACQ,OAAO,GAAG,IAAI;MACxB;IACF,CAAC,MAAM;MACL;MACAR,OAAO,CAACQ,OAAO,GAAG,KAAK;IACzB;;IAEA;IACA,IAAIoD,IAAI,IAAIxE,WAAW,EAAE;IACzBN,WAAW,CAAC0B,OAAO,GAAGvB,GAAG,GAAG2E,IAAI,GAAGxE,WAAW;IAC9CuE,sBAAsB,CAACnC,MAAM,EAAEC,MAAM,EAAEF,QAAQ,EAAEiC,YAAY,CAAC;EAChE,CAAC;EACD,MAAMG,sBAAsB,GAAGA,CAACnC,MAAM,EAAEC,MAAM,EAAEF,QAAQ,EAAEiC,YAAY,KAAK;IACzE;IACA,IAAIE,OAAO,GAAG7F,MAAM,KAAKsC,SAAS,GAAGiC,KAAK,CAAC5B,OAAO,GAAG3C,MAAM;IAC3D,MAAMmG,WAAW,GAAG9E,YAAY,CAACsB,OAAO;IACxC,IAAIyD,SAAS,GAAG,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjBhD,oBAAoB,CAACM,MAAM,EAAEC,MAAM,CAAC;IACpC,MAAM0C,OAAO,GAAG/F,kBAAkB,GAAGgG,IAAI,CAACC,KAAK,CAAC,CAAC9C,QAAQ,CAACR,CAAC,GAAG,CAAC,IAAIS,MAAM,CAAC,GAAG,CAACD,QAAQ,CAACR,CAAC,GAAG,CAAC,IAAIS,MAAM;IACtG,MAAMQ,OAAO,GAAG5D,kBAAkB,GAAGgG,IAAI,CAACC,KAAK,CAAC,CAAC9C,QAAQ,CAACP,CAAC,GAAG,CAAC,IAAIS,MAAM,CAAC,GAAG,CAACF,QAAQ,CAACP,CAAC,GAAG,CAAC,IAAIS,MAAM;IACtG,IAAI,CAAC+B,YAAY,CAACQ,WAAW,CAAC,EAAE;MAC9B;IACF;IACA,MAAM;MACJM,KAAK,EAAE;QACLpC,CAAC,EAAEqC,MAAM;QACTpC,CAAC,EAAEqC;MACL,CAAC;MACDvD,UAAU,EAAE;QACVF,CAAC,EAAE0D,aAAa;QAChBzD,CAAC,EAAE0D;MACL;IACF,CAAC,GAAGlB,YAAY,CAACQ,WAAW,CAAC;IAC7B,MAAMW,YAAY,GAAG,CAAC,GAAGR,OAAO;IAChC,MAAMlC,YAAY,GAAG,CAAC,GAAGD,OAAO;IAChC,IAAIpD,MAAM,CAAC4B,OAAO,IAAI5B,MAAM,CAAC4B,OAAO,CAACW,GAAG,EAAE;MACxC8C,SAAS,GAAGpE,UAAU,GAAG,CAAC,GAAG8E,YAAY,IAAIJ,MAAM,GAAGE,aAAa,CAAC,GAAGE,YAAY,IAAIJ,MAAM,GAAGE,aAAa,CAAC,GAAG7F,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACY,MAAM,CAACG,CAAC;MAC5IgC,SAAS,GAAGE,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAG3C,YAAY,CAAC,GAAGA,YAAY,IAAIuC,MAAM,GAAGE,aAAa,CAAC;MAChF9F,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACtD,MAAM,CAACqE,CAAC,GAAG+B,SAAS;MACvCrF,MAAM,CAAC4B,OAAO,CAACW,GAAG,CAACtD,MAAM,CAACsE,CAAC,GAAG+B,SAAS;IACzC;;IAEA;IACA,IAAIR,OAAO,KAAKvD,SAAS,IAAIuD,OAAO,KAAK,IAAI,EAAE;MAC7C;MACA,IAAImB,UAAU,GAAGT,IAAI,CAACU,KAAK,CAACpB,OAAO,GAAGF,YAAY,CAAC1C,MAAM,CAAC;;MAE1D;MACA+D,UAAU,GAAGT,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEX,IAAI,CAACY,GAAG,CAACH,UAAU,EAAErB,YAAY,CAAC1C,MAAM,GAAG,CAAC,CAAC,CAAC;MACvE,IAAImE,KAAK,CAACJ,UAAU,CAAC,EAAE;QACrBA,UAAU,GAAG,CAAC,CAAC,CAAC;MAClB;MACA3F,YAAY,CAACsB,OAAO,GAAGqE,UAAU;IACnC,CAAC,MAAM;MACL;MACA,IAAI/G,aAAa,EAAE;QACjBoB,YAAY,CAACsB,OAAO,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLtB,YAAY,CAACsB,OAAO,IAAI,CAAC;MAC3B;IACF;EACF,CAAC;;EAED;EACAhF,QAAQ,CAAC,CAAC0J,MAAM,EAAEC,MAAM,KAAK;IAC3B,IAAIC,oBAAoB,EAAEC,eAAe;IACzC,IAAI,EAAE,CAACD,oBAAoB,GAAGzG,UAAU,CAAC6B,OAAO,KAAK,IAAI,IAAI4E,oBAAoB,CAACvE,MAAM,CAAC,IAAI,EAAE,CAACwE,eAAe,GAAGzG,MAAM,CAAC4B,OAAO,KAAK,IAAI,IAAI6E,eAAe,CAAClE,GAAG,CAAC,EAAE;MACjK;IACF;IACA,IAAIrB,QAAQ,CAACU,OAAO,EAAE;MACpB;IACF;IACA,IAAI,CAAC4B,KAAK,CAACG,QAAQ,KAAKtF,QAAQ,IAAIM,IAAI,CAAC,EAAE;MACzCgG,YAAY,CAAC,CAAC;MACdjG,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC;QACzB6B,gBAAgB,EAAEA,gBAAgB,CAACqB,OAAO;QAC1CtB,YAAY,EAAEA,YAAY,CAACsB;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,SAAS8E,aAAaA,CAACC,YAAY,GAAG,IAAI9J,KAAK,CAACmE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE4F,QAAQ,GAAG,CAAC,EAAE;IAC9E,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE,OAAOD,YAAY,CAACE,cAAc,CAACD,QAAQ,CAAC;IAC9E,IAAI7E,KAAK,CAACC,OAAO,CAAC4E,QAAQ,CAAC,EAAE,OAAOD,YAAY,CAACG,QAAQ,CAAC,IAAIjK,KAAK,CAACmE,OAAO,CAAC,GAAG4F,QAAQ,CAAC,CAAC;IACzF,IAAIA,QAAQ,YAAY/J,KAAK,CAACmE,OAAO,EAAE,OAAO2F,YAAY,CAACG,QAAQ,CAACF,QAAQ,CAAC;EAC/E;EACA,OAAO,aAAajK,KAAK,CAACoK,aAAa,CAAC,OAAO,EAAErK,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;IACnEE,GAAG,EAAEA,GAAG;IACRuE,KAAK,EAAEuC,aAAa,CAAC5F,MAAM,EAAEpB,KAAK,CAACyE,KAAK;EAC1C,CAAC,CAAC,EAAE,aAAaxH,KAAK,CAACoK,aAAa,CAAC5J,OAAO,CAAC6J,QAAQ,EAAE;IACrDC,KAAK,EAAEzD;EACT,CAAC,EAAExE,QAAQ,IAAI,aAAarC,KAAK,CAACoK,aAAa,CAAC/J,SAAS,EAAE,IAAI,EAAE,aAAaL,KAAK,CAACoK,aAAa,CAAC,MAAM,EAAErK,QAAQ,CAAC;IACjHkD,GAAG,EAAEK,SAAS;IACdkE,KAAK,EAAE,GAAG;IACV1G,QAAQ,EAAEA;EACZ,CAAC,EAAEgC,SAAS,CAAC,EAAE,aAAa9C,KAAK,CAACoK,aAAa,CAAC,mBAAmB,EAAE;IACnEG,kBAAkB,EAAE,KAAK;IACzBC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAEvK,KAAK,CAACwK,UAAU;IACtBzH,GAAG,EAAEI,MAAM;IACXuC,GAAG,EAAE9B,aAAa;IAClB6G,WAAW,EAAE,IAAI;IACjBxI,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;EACtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAACE,QAAQ,IAAI,aAAarC,KAAK,CAACoK,aAAa,CAACjK,SAAS,EAAEJ,QAAQ,CAAC;IACtEe,QAAQ,EAAEA,QAAQ;IAClB8J,KAAK,EAAEnI,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG;EAC/D,CAAC,EAAEK,SAAS,CAAC,EAAE,aAAa9C,KAAK,CAACoK,aAAa,CAAC,mBAAmB,EAAE;IACnEG,kBAAkB,EAAE,KAAK;IACzBC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAEvK,KAAK,CAACwK,UAAU;IACtBzH,GAAG,EAAEI,MAAM;IACXuC,GAAG,EAAE9B,aAAa;IAClB6G,WAAW,EAAE,IAAI;IACjBxI,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;EACtE,CAAC,CAAC,EAAE,CAACO,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC,CAAC,EAAEkD,GAAG,CAAC,CAACiF,IAAI,EAAEC,KAAK,KAAK,aAAa9K,KAAK,CAACoK,aAAa,CAAChK,QAAQ,EAAEL,QAAQ,CAAC;IACtJgL,GAAG,EAAED,KAAK;IACV7H,GAAG,EAAE,CAACP,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC6C,MAAM,MAAM,CAAC,GAAGjC,SAAS,GAAG,IAAI;IACrF0H,QAAQ,EAAEH,IAAI;IACdrD,KAAK,EAAE;EACT,CAAC,EAAE1E,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,SAASpB,cAAc,EAAEN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}