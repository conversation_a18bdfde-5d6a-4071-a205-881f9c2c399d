{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, PerspectiveCamera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, LinearFilter, HalfFloatType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial as MeshReflectorMaterial$1 } from '../materials/MeshReflectorMaterial.js';\nconst MeshReflectorMaterial = /* @__PURE__ */React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 1,\n  resolution = 256,\n  blur = [0, 0],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  reflectorOffset = 0,\n  ...props\n}, ref) => {\n  extend({\n    MeshReflectorMaterialImpl: MeshReflectorMaterial$1\n  });\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const blurX = blur[0];\n  const blurY = blur[1];\n  const materialRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => materialRef.current, []);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    var _materialRef$current;\n    const parent = materialRef.current.parent || ((_materialRef$current = materialRef.current) == null || (_materialRef$current = _materialRef$current.__r3f.parent) == null ? void 0 : _materialRef$current.object);\n    if (!parent) return;\n    reflectorWorldPosition.setFromMatrixPosition(parent.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(parent.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    reflectorWorldPosition.addScaledVector(normal, reflectorOffset);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition);\n    // Avoid rendering when reflector is facing away\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n    // Update the texture matrix\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(parent.matrixWorld);\n    // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n    // Calculate the scaled plane vector\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q));\n    // Replacing the third row of the projection matrix\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, [camera, reflectorOffset]);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      type: HalfFloatType\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blurX,\n      height: blurY,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blurX, blurY, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    var _materialRef$current2;\n    const parent = materialRef.current.parent || ((_materialRef$current2 = materialRef.current) == null || (_materialRef$current2 = _materialRef$current2.__r3f.parent) == null ? void 0 : _materialRef$current2.object);\n    if (!parent) return;\n    parent.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    parent.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"meshReflectorMaterialImpl\", _extends({\n    attach: \"material\"\n    // Defines can't be updated dynamically, so we need to recreate the material\n    ,\n\n    key: 'key' + reflectorProps['defines-USE_BLUR'] + reflectorProps['defines-USE_DEPTH'] + reflectorProps['defines-USE_DISTORTION'],\n    ref: materialRef\n  }, reflectorProps, props));\n});\nexport { MeshReflectorMaterial };", "map": {"version": 3, "names": ["_extends", "React", "Plane", "Vector3", "Matrix4", "Vector4", "PerspectiveCamera", "WebGLRenderTarget", "DepthTexture", "DepthFormat", "UnsignedShortType", "LinearFilter", "HalfFloatType", "extend", "useThree", "useFrame", "BlurPass", "MeshReflectorMaterial", "MeshReflectorMaterial$1", "forwardRef", "mixBlur", "mixStrength", "resolution", "blur", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "mirror", "distortion", "mixContrast", "distortionMap", "reflectorOffset", "props", "ref", "MeshReflectorMaterialImpl", "gl", "camera", "scene", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "blurX", "blurY", "materialRef", "useRef", "useImperativeHandle", "current", "reflectorPlane", "useState", "normal", "reflectorWorldPosition", "cameraWorldPosition", "rotationMatrix", "lookAtPosition", "clipPlane", "view", "target", "q", "textureMatrix", "virtualCamera", "beforeRender", "useCallback", "_materialRef$current", "parent", "__r3f", "object", "setFromMatrixPosition", "matrixWorld", "extractRotation", "set", "applyMatrix4", "addScaledVector", "subVectors", "dot", "reflect", "negate", "add", "position", "copy", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "multiply", "matrixWorldInverse", "setFromNormalAndCoplanarPoint", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "fbo1", "fbo2", "blurpass", "reflectorProps", "useMemo", "parameters", "minFilter", "magFilter", "type", "depthBuffer", "depthTexture", "format", "width", "height", "tDiffuse", "texture", "tD<PERSON>h", "tDiffuseBlur", "undefined", "_materialRef$current2", "visible", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "createElement", "attach", "key"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/MeshReflectorMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, PerspectiveCamera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, LinearFilter, HalfFloatType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial as MeshReflectorMaterial$1 } from '../materials/MeshReflectorMaterial.js';\n\nconst MeshReflectorMaterial = /* @__PURE__ */React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 1,\n  resolution = 256,\n  blur = [0, 0],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  reflectorOffset = 0,\n  ...props\n}, ref) => {\n  extend({\n    MeshReflectorMaterialImpl: MeshReflectorMaterial$1\n  });\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const blurX = blur[0];\n  const blurY = blur[1];\n  const materialRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => materialRef.current, []);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    var _materialRef$current;\n    const parent = materialRef.current.parent || ((_materialRef$current = materialRef.current) == null || (_materialRef$current = _materialRef$current.__r3f.parent) == null ? void 0 : _materialRef$current.object);\n    if (!parent) return;\n    reflectorWorldPosition.setFromMatrixPosition(parent.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(parent.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    reflectorWorldPosition.addScaledVector(normal, reflectorOffset);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition);\n    // Avoid rendering when reflector is facing away\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n    // Update the texture matrix\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(parent.matrixWorld);\n    // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n    // Calculate the scaled plane vector\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q));\n    // Replacing the third row of the projection matrix\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, [camera, reflectorOffset]);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      type: HalfFloatType\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blurX,\n      height: blurY,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blurX, blurY, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    var _materialRef$current2;\n    const parent = materialRef.current.parent || ((_materialRef$current2 = materialRef.current) == null || (_materialRef$current2 = _materialRef$current2.__r3f.parent) == null ? void 0 : _materialRef$current2.object);\n    if (!parent) return;\n    parent.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    parent.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"meshReflectorMaterialImpl\", _extends({\n    attach: \"material\"\n    // Defines can't be updated dynamically, so we need to recreate the material\n    ,\n    key: 'key' + reflectorProps['defines-USE_BLUR'] + reflectorProps['defines-USE_DEPTH'] + reflectorProps['defines-USE_DISTORTION'],\n    ref: materialRef\n  }, reflectorProps, props));\n});\n\nexport { MeshReflectorMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,QAAQ,OAAO;AACzK,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,IAAIC,uBAAuB,QAAQ,uCAAuC;AAExG,MAAMD,qBAAqB,GAAG,eAAehB,KAAK,CAACkB,UAAU,CAAC,CAAC;EAC7DC,OAAO,GAAG,CAAC;EACXC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACbC,iBAAiB,GAAG,GAAG;EACvBC,iBAAiB,GAAG,CAAC;EACrBC,UAAU,GAAG,CAAC;EACdC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,CAAC;EACVC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,CAAC;EACfC,aAAa;EACbC,eAAe,GAAG,CAAC;EACnB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTrB,MAAM,CAAC;IACLsB,yBAAyB,EAAEjB;EAC7B,CAAC,CAAC;EACF,MAAMkB,EAAE,GAAGtB,QAAQ,CAAC,CAAC;IACnBsB;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,MAAM,GAAGvB,QAAQ,CAAC,CAAC;IACvBuB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;IACtBwB;EACF,CAAC,KAAKA,KAAK,CAAC;EACZf,IAAI,GAAGgB,KAAK,CAACC,OAAO,CAACjB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;EAChD,MAAMkB,OAAO,GAAGlB,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACrC,MAAMmB,KAAK,GAAGnB,IAAI,CAAC,CAAC,CAAC;EACrB,MAAMoB,KAAK,GAAGpB,IAAI,CAAC,CAAC,CAAC;EACrB,MAAMqB,WAAW,GAAG3C,KAAK,CAAC4C,MAAM,CAAC,IAAI,CAAC;EACtC5C,KAAK,CAAC6C,mBAAmB,CAACZ,GAAG,EAAE,MAAMU,WAAW,CAACG,OAAO,EAAE,EAAE,CAAC;EAC7D,MAAM,CAACC,cAAc,CAAC,GAAG/C,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI/C,KAAK,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACgD,MAAM,CAAC,GAAGjD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgD,sBAAsB,CAAC,GAAGlD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EACpE,MAAM,CAACiD,mBAAmB,CAAC,GAAGnD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EACjE,MAAM,CAACkD,cAAc,CAAC,GAAGpD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI7C,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkD,cAAc,CAAC,GAAGrD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACoD,SAAS,CAAC,GAAGtD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI5C,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmD,IAAI,CAAC,GAAGvD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EAClD,MAAM,CAACsD,MAAM,CAAC,GAAGxD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAACuD,CAAC,CAAC,GAAGzD,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI5C,OAAO,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsD,aAAa,CAAC,GAAG1D,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI7C,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwD,aAAa,CAAC,GAAG3D,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI3C,iBAAiB,CAAC,CAAC,CAAC;EACrE,MAAMuD,YAAY,GAAG5D,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAC3C,IAAIC,oBAAoB;IACxB,MAAMC,MAAM,GAAGpB,WAAW,CAACG,OAAO,CAACiB,MAAM,KAAK,CAACD,oBAAoB,GAAGnB,WAAW,CAACG,OAAO,KAAK,IAAI,IAAI,CAACgB,oBAAoB,GAAGA,oBAAoB,CAACE,KAAK,CAACD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,oBAAoB,CAACG,MAAM,CAAC;IAChN,IAAI,CAACF,MAAM,EAAE;IACbb,sBAAsB,CAACgB,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAChEhB,mBAAmB,CAACe,qBAAqB,CAAC9B,MAAM,CAAC+B,WAAW,CAAC;IAC7Df,cAAc,CAACgB,eAAe,CAACL,MAAM,CAACI,WAAW,CAAC;IAClDlB,MAAM,CAACoB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBpB,MAAM,CAACqB,YAAY,CAAClB,cAAc,CAAC;IACnCF,sBAAsB,CAACqB,eAAe,CAACtB,MAAM,EAAElB,eAAe,CAAC;IAC/DwB,IAAI,CAACiB,UAAU,CAACtB,sBAAsB,EAAEC,mBAAmB,CAAC;IAC5D;IACA,IAAII,IAAI,CAACkB,GAAG,CAACxB,MAAM,CAAC,GAAG,CAAC,EAAE;IAC1BM,IAAI,CAACmB,OAAO,CAACzB,MAAM,CAAC,CAAC0B,MAAM,CAAC,CAAC;IAC7BpB,IAAI,CAACqB,GAAG,CAAC1B,sBAAsB,CAAC;IAChCE,cAAc,CAACgB,eAAe,CAAChC,MAAM,CAAC+B,WAAW,CAAC;IAClDd,cAAc,CAACgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5BhB,cAAc,CAACiB,YAAY,CAAClB,cAAc,CAAC;IAC3CC,cAAc,CAACuB,GAAG,CAACzB,mBAAmB,CAAC;IACvCK,MAAM,CAACgB,UAAU,CAACtB,sBAAsB,EAAEG,cAAc,CAAC;IACzDG,MAAM,CAACkB,OAAO,CAACzB,MAAM,CAAC,CAAC0B,MAAM,CAAC,CAAC;IAC/BnB,MAAM,CAACoB,GAAG,CAAC1B,sBAAsB,CAAC;IAClCS,aAAa,CAACkB,QAAQ,CAACC,IAAI,CAACvB,IAAI,CAAC;IACjCI,aAAa,CAACoB,EAAE,CAACV,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BV,aAAa,CAACoB,EAAE,CAACT,YAAY,CAAClB,cAAc,CAAC;IAC7CO,aAAa,CAACoB,EAAE,CAACL,OAAO,CAACzB,MAAM,CAAC;IAChCU,aAAa,CAACqB,MAAM,CAACxB,MAAM,CAAC;IAC5BG,aAAa,CAACsB,GAAG,GAAG7C,MAAM,CAAC6C,GAAG,CAAC,CAAC;IAChCtB,aAAa,CAACuB,iBAAiB,CAAC,CAAC;IACjCvB,aAAa,CAACwB,gBAAgB,CAACL,IAAI,CAAC1C,MAAM,CAAC+C,gBAAgB,CAAC;IAC5D;IACAzB,aAAa,CAACW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjGX,aAAa,CAAC0B,QAAQ,CAACzB,aAAa,CAACwB,gBAAgB,CAAC;IACtDzB,aAAa,CAAC0B,QAAQ,CAACzB,aAAa,CAAC0B,kBAAkB,CAAC;IACxD3B,aAAa,CAAC0B,QAAQ,CAACrB,MAAM,CAACI,WAAW,CAAC;IAC1C;IACA;IACApB,cAAc,CAACuC,6BAA6B,CAACrC,MAAM,EAAEC,sBAAsB,CAAC;IAC5EH,cAAc,CAACuB,YAAY,CAACX,aAAa,CAAC0B,kBAAkB,CAAC;IAC7D/B,SAAS,CAACe,GAAG,CAACtB,cAAc,CAACE,MAAM,CAACsC,CAAC,EAAExC,cAAc,CAACE,MAAM,CAACuC,CAAC,EAAEzC,cAAc,CAACE,MAAM,CAACwC,CAAC,EAAE1C,cAAc,CAAC2C,QAAQ,CAAC;IACjH,MAAMP,gBAAgB,GAAGxB,aAAa,CAACwB,gBAAgB;IACvD1B,CAAC,CAAC8B,CAAC,GAAG,CAACI,IAAI,CAACC,IAAI,CAACtC,SAAS,CAACiC,CAAC,CAAC,GAAGJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FpC,CAAC,CAAC+B,CAAC,GAAG,CAACG,IAAI,CAACC,IAAI,CAACtC,SAAS,CAACkC,CAAC,CAAC,GAAGL,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FpC,CAAC,CAACgC,CAAC,GAAG,CAAC,GAAG;IACVhC,CAAC,CAACqC,CAAC,GAAG,CAAC,GAAG,GAAGX,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC;IAC3E;IACAvC,SAAS,CAACyC,cAAc,CAAC,GAAG,GAAGzC,SAAS,CAACmB,GAAG,CAAChB,CAAC,CAAC,CAAC;IAChD;IACA0B,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGvC,SAAS,CAACiC,CAAC;IAC1CJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGvC,SAAS,CAACkC,CAAC;IAC1CL,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGvC,SAAS,CAACmC,CAAC,GAAG,GAAG;IACjDN,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGvC,SAAS,CAACwC,CAAC;EAC7C,CAAC,EAAE,CAAC1D,MAAM,EAAEL,eAAe,CAAC,CAAC;EAC7B,MAAM,CAACiE,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC,GAAGnG,KAAK,CAACoG,OAAO,CAAC,MAAM;IACjE,MAAMC,UAAU,GAAG;MACjBC,SAAS,EAAE5F,YAAY;MACvB6F,SAAS,EAAE7F,YAAY;MACvB8F,IAAI,EAAE7F;IACR,CAAC;IACD,MAAMqF,IAAI,GAAG,IAAI1F,iBAAiB,CAACe,UAAU,EAAEA,UAAU,EAAEgF,UAAU,CAAC;IACtEL,IAAI,CAACS,WAAW,GAAG,IAAI;IACvBT,IAAI,CAACU,YAAY,GAAG,IAAInG,YAAY,CAACc,UAAU,EAAEA,UAAU,CAAC;IAC5D2E,IAAI,CAACU,YAAY,CAACC,MAAM,GAAGnG,WAAW;IACtCwF,IAAI,CAACU,YAAY,CAACF,IAAI,GAAG/F,iBAAiB;IAC1C,MAAMwF,IAAI,GAAG,IAAI3F,iBAAiB,CAACe,UAAU,EAAEA,UAAU,EAAEgF,UAAU,CAAC;IACtE,MAAMH,QAAQ,GAAG,IAAInF,QAAQ,CAAC;MAC5BoB,EAAE;MACFd,UAAU;MACVuF,KAAK,EAAEnE,KAAK;MACZoE,MAAM,EAAEnE,KAAK;MACbnB,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC;IACF,CAAC,CAAC;IACF,MAAMyE,cAAc,GAAG;MACrBxE,MAAM;MACN+B,aAAa;MACbvC,OAAO;MACP2F,QAAQ,EAAEd,IAAI,CAACe,OAAO;MACtBC,MAAM,EAAEhB,IAAI,CAACU,YAAY;MACzBO,YAAY,EAAEhB,IAAI,CAACc,OAAO;MAC1BvE,OAAO;MACPpB,WAAW;MACXG,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC,oBAAoB;MACpBE,UAAU;MACVE,aAAa;MACbD,WAAW;MACX,kBAAkB,EAAEW,OAAO,GAAG,EAAE,GAAG0E,SAAS;MAC5C,mBAAmB,EAAEzF,UAAU,GAAG,CAAC,GAAG,EAAE,GAAGyF,SAAS;MACpD,wBAAwB,EAAEpF,aAAa,GAAG,EAAE,GAAGoF;IACjD,CAAC;IACD,OAAO,CAAClB,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC;EAC/C,CAAC,EAAE,CAAChE,EAAE,EAAEM,KAAK,EAAEC,KAAK,EAAEgB,aAAa,EAAErC,UAAU,EAAEM,MAAM,EAAEa,OAAO,EAAErB,OAAO,EAAEC,WAAW,EAAEG,iBAAiB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,oBAAoB,EAAEE,UAAU,EAAEE,aAAa,EAAED,WAAW,CAAC,CAAC;EACxMf,QAAQ,CAAC,MAAM;IACb,IAAIqG,qBAAqB;IACzB,MAAMpD,MAAM,GAAGpB,WAAW,CAACG,OAAO,CAACiB,MAAM,KAAK,CAACoD,qBAAqB,GAAGxE,WAAW,CAACG,OAAO,KAAK,IAAI,IAAI,CAACqE,qBAAqB,GAAGA,qBAAqB,CAACnD,KAAK,CAACD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,qBAAqB,CAAClD,MAAM,CAAC;IACpN,IAAI,CAACF,MAAM,EAAE;IACbA,MAAM,CAACqD,OAAO,GAAG,KAAK;IACtB,MAAMC,gBAAgB,GAAGlF,EAAE,CAACmF,EAAE,CAACC,OAAO;IACtC,MAAMC,uBAAuB,GAAGrF,EAAE,CAACsF,SAAS,CAACC,UAAU;IACvD9D,YAAY,CAAC,CAAC;IACdzB,EAAE,CAACmF,EAAE,CAACC,OAAO,GAAG,KAAK;IACrBpF,EAAE,CAACsF,SAAS,CAACC,UAAU,GAAG,KAAK;IAC/BvF,EAAE,CAACwF,eAAe,CAAC3B,IAAI,CAAC;IACxB7D,EAAE,CAACyF,KAAK,CAACC,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC5F,EAAE,CAAC6F,SAAS,EAAE7F,EAAE,CAAC8F,KAAK,CAAC,CAAC;IAC7B9F,EAAE,CAAC+F,MAAM,CAAC7F,KAAK,EAAEsB,aAAa,CAAC;IAC/B,IAAInB,OAAO,EAAE0D,QAAQ,CAACgC,MAAM,CAAC/F,EAAE,EAAE6D,IAAI,EAAEC,IAAI,CAAC;IAC5C9D,EAAE,CAACmF,EAAE,CAACC,OAAO,GAAGF,gBAAgB;IAChClF,EAAE,CAACsF,SAAS,CAACC,UAAU,GAAGF,uBAAuB;IACjDzD,MAAM,CAACqD,OAAO,GAAG,IAAI;IACrBjF,EAAE,CAACwF,eAAe,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO,aAAa3H,KAAK,CAACmI,aAAa,CAAC,2BAA2B,EAAEpI,QAAQ,CAAC;IAC5EqI,MAAM,EAAE;IACR;IAAA;;IAEAC,GAAG,EAAE,KAAK,GAAGlC,cAAc,CAAC,kBAAkB,CAAC,GAAGA,cAAc,CAAC,mBAAmB,CAAC,GAAGA,cAAc,CAAC,wBAAwB,CAAC;IAChIlE,GAAG,EAAEU;EACP,CAAC,EAAEwD,cAAc,EAAEnE,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,SAAShB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}