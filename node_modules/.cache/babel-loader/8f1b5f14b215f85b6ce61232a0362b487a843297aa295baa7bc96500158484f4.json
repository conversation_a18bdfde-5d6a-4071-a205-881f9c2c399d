{"ast": null, "code": "import { Mesh, MathUtils } from \"three\";\nclass MorphBlendMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material);\n    this.animationsMap = {};\n    this.animationsList = [];\n    const numFrames = Object.keys(this.morphTargetDictionary).length;\n    const name = \"__default\";\n    const startFrame = 0;\n    const endFrame = numFrames - 1;\n    const fps = numFrames / 1;\n    this.createAnimation(name, startFrame, endFrame, fps);\n    this.setAnimationWeight(name, 1);\n  }\n  createAnimation(name, start, end, fps) {\n    const animation = {\n      start,\n      end,\n      length: end - start + 1,\n      fps,\n      duration: (end - start) / fps,\n      lastFrame: 0,\n      currentFrame: 0,\n      active: false,\n      time: 0,\n      direction: 1,\n      weight: 1,\n      directionBackwards: false,\n      mirroredLoop: false\n    };\n    this.animationsMap[name] = animation;\n    this.animationsList.push(animation);\n  }\n  autoCreateAnimations(fps) {\n    const pattern = /([a-z]+)_?(\\d+)/i;\n    let firstAnimation;\n    const frameRanges = {};\n    let i = 0;\n    for (const key in this.morphTargetDictionary) {\n      const chunks = key.match(pattern);\n      if (chunks && chunks.length > 1) {\n        const name = chunks[1];\n        if (!frameRanges[name]) frameRanges[name] = {\n          start: Infinity,\n          end: -Infinity\n        };\n        const range = frameRanges[name];\n        if (i < range.start) range.start = i;\n        if (i > range.end) range.end = i;\n        if (!firstAnimation) firstAnimation = name;\n      }\n      i++;\n    }\n    for (const name in frameRanges) {\n      const range = frameRanges[name];\n      this.createAnimation(name, range.start, range.end, fps);\n    }\n    this.firstAnimation = firstAnimation;\n  }\n  setAnimationDirectionForward(name) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.direction = 1;\n      animation.directionBackwards = false;\n    }\n  }\n  setAnimationDirectionBackward(name) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.direction = -1;\n      animation.directionBackwards = true;\n    }\n  }\n  setAnimationFPS(name, fps) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.fps = fps;\n      animation.duration = (animation.end - animation.start) / animation.fps;\n    }\n  }\n  setAnimationDuration(name, duration) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.duration = duration;\n      animation.fps = (animation.end - animation.start) / animation.duration;\n    }\n  }\n  setAnimationWeight(name, weight) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.weight = weight;\n    }\n  }\n  setAnimationTime(name, time) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.time = time;\n    }\n  }\n  getAnimationTime(name) {\n    let time = 0;\n    const animation = this.animationsMap[name];\n    if (animation) {\n      time = animation.time;\n    }\n    return time;\n  }\n  getAnimationDuration(name) {\n    let duration = -1;\n    const animation = this.animationsMap[name];\n    if (animation) {\n      duration = animation.duration;\n    }\n    return duration;\n  }\n  playAnimation(name) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.time = 0;\n      animation.active = true;\n    } else {\n      console.warn(\"THREE.MorphBlendMesh: animation[\" + name + \"] undefined in .playAnimation()\");\n    }\n  }\n  stopAnimation(name) {\n    const animation = this.animationsMap[name];\n    if (animation) {\n      animation.active = false;\n    }\n  }\n  update(delta) {\n    for (let i = 0, il = this.animationsList.length; i < il; i++) {\n      const animation = this.animationsList[i];\n      if (!animation.active) continue;\n      const frameTime = animation.duration / animation.length;\n      animation.time += animation.direction * delta;\n      if (animation.mirroredLoop) {\n        if (animation.time > animation.duration || animation.time < 0) {\n          animation.direction *= -1;\n          if (animation.time > animation.duration) {\n            animation.time = animation.duration;\n            animation.directionBackwards = true;\n          }\n          if (animation.time < 0) {\n            animation.time = 0;\n            animation.directionBackwards = false;\n          }\n        }\n      } else {\n        animation.time = animation.time % animation.duration;\n        if (animation.time < 0) animation.time += animation.duration;\n      }\n      const keyframe = animation.start + MathUtils.clamp(Math.floor(animation.time / frameTime), 0, animation.length - 1);\n      const weight = animation.weight;\n      if (keyframe !== animation.currentFrame) {\n        this.morphTargetInfluences[animation.lastFrame] = 0;\n        this.morphTargetInfluences[animation.currentFrame] = 1 * weight;\n        this.morphTargetInfluences[keyframe] = 0;\n        animation.lastFrame = animation.currentFrame;\n        animation.currentFrame = keyframe;\n      }\n      let mix = animation.time % frameTime / frameTime;\n      if (animation.directionBackwards) mix = 1 - mix;\n      if (animation.currentFrame !== animation.lastFrame) {\n        this.morphTargetInfluences[animation.currentFrame] = mix * weight;\n        this.morphTargetInfluences[animation.lastFrame] = (1 - mix) * weight;\n      } else {\n        this.morphTargetInfluences[animation.currentFrame] = weight;\n      }\n    }\n  }\n}\nexport { MorphBlendMesh };", "map": {"version": 3, "names": ["MorphBlendMesh", "<PERSON><PERSON>", "constructor", "geometry", "material", "animationsMap", "animationsList", "numFrames", "Object", "keys", "morphTargetDictionary", "length", "name", "startFrame", "endFrame", "fps", "createAnimation", "setAnimationWeight", "start", "end", "animation", "duration", "<PERSON><PERSON><PERSON><PERSON>", "currentFrame", "active", "time", "direction", "weight", "directionBackwards", "mirroredLoop", "push", "autoCreateAnimations", "pattern", "firstAnimation", "frameRanges", "i", "key", "chunks", "match", "Infinity", "range", "setAnimationDirectionForward", "setAnimationDirectionBackward", "setAnimationFPS", "setAnimationDuration", "setAnimationTime", "getAnimationTime", "getAnimationDuration", "playAnimation", "console", "warn", "stopAnimation", "update", "delta", "il", "frameTime", "keyframe", "MathUtils", "clamp", "Math", "floor", "morphTargetInfluences", "mix"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/misc/MorphBlendMesh.js"], "sourcesContent": ["import { MathUtil<PERSON>, Mesh } from 'three'\n\nclass MorphBlendMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material)\n\n    this.animationsMap = {}\n    this.animationsList = []\n\n    // prepare default animation\n    // (all frames played together in 1 second)\n\n    const numFrames = Object.keys(this.morphTargetDictionary).length\n\n    const name = '__default'\n\n    const startFrame = 0\n    const endFrame = numFrames - 1\n\n    const fps = numFrames / 1\n\n    this.createAnimation(name, startFrame, endFrame, fps)\n    this.setAnimationWeight(name, 1)\n  }\n\n  createAnimation(name, start, end, fps) {\n    const animation = {\n      start: start,\n      end: end,\n\n      length: end - start + 1,\n\n      fps: fps,\n      duration: (end - start) / fps,\n\n      lastFrame: 0,\n      currentFrame: 0,\n\n      active: false,\n\n      time: 0,\n      direction: 1,\n      weight: 1,\n\n      directionBackwards: false,\n      mirroredLoop: false,\n    }\n\n    this.animationsMap[name] = animation\n    this.animationsList.push(animation)\n  }\n\n  autoCreateAnimations(fps) {\n    const pattern = /([a-z]+)_?(\\d+)/i\n\n    let firstAnimation\n\n    const frameRanges = {}\n\n    let i = 0\n\n    for (const key in this.morphTargetDictionary) {\n      const chunks = key.match(pattern)\n\n      if (chunks && chunks.length > 1) {\n        const name = chunks[1]\n\n        if (!frameRanges[name]) frameRanges[name] = { start: Infinity, end: -Infinity }\n\n        const range = frameRanges[name]\n\n        if (i < range.start) range.start = i\n        if (i > range.end) range.end = i\n\n        if (!firstAnimation) firstAnimation = name\n      }\n\n      i++\n    }\n\n    for (const name in frameRanges) {\n      const range = frameRanges[name]\n      this.createAnimation(name, range.start, range.end, fps)\n    }\n\n    this.firstAnimation = firstAnimation\n  }\n\n  setAnimationDirectionForward(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.direction = 1\n      animation.directionBackwards = false\n    }\n  }\n\n  setAnimationDirectionBackward(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.direction = -1\n      animation.directionBackwards = true\n    }\n  }\n\n  setAnimationFPS(name, fps) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.fps = fps\n      animation.duration = (animation.end - animation.start) / animation.fps\n    }\n  }\n\n  setAnimationDuration(name, duration) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.duration = duration\n      animation.fps = (animation.end - animation.start) / animation.duration\n    }\n  }\n\n  setAnimationWeight(name, weight) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.weight = weight\n    }\n  }\n\n  setAnimationTime(name, time) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.time = time\n    }\n  }\n\n  getAnimationTime(name) {\n    let time = 0\n\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      time = animation.time\n    }\n\n    return time\n  }\n\n  getAnimationDuration(name) {\n    let duration = -1\n\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      duration = animation.duration\n    }\n\n    return duration\n  }\n\n  playAnimation(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.time = 0\n      animation.active = true\n    } else {\n      console.warn('THREE.MorphBlendMesh: animation[' + name + '] undefined in .playAnimation()')\n    }\n  }\n\n  stopAnimation(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.active = false\n    }\n  }\n\n  update(delta) {\n    for (let i = 0, il = this.animationsList.length; i < il; i++) {\n      const animation = this.animationsList[i]\n\n      if (!animation.active) continue\n\n      const frameTime = animation.duration / animation.length\n\n      animation.time += animation.direction * delta\n\n      if (animation.mirroredLoop) {\n        if (animation.time > animation.duration || animation.time < 0) {\n          animation.direction *= -1\n\n          if (animation.time > animation.duration) {\n            animation.time = animation.duration\n            animation.directionBackwards = true\n          }\n\n          if (animation.time < 0) {\n            animation.time = 0\n            animation.directionBackwards = false\n          }\n        }\n      } else {\n        animation.time = animation.time % animation.duration\n\n        if (animation.time < 0) animation.time += animation.duration\n      }\n\n      const keyframe =\n        animation.start + MathUtils.clamp(Math.floor(animation.time / frameTime), 0, animation.length - 1)\n      const weight = animation.weight\n\n      if (keyframe !== animation.currentFrame) {\n        this.morphTargetInfluences[animation.lastFrame] = 0\n        this.morphTargetInfluences[animation.currentFrame] = 1 * weight\n\n        this.morphTargetInfluences[keyframe] = 0\n\n        animation.lastFrame = animation.currentFrame\n        animation.currentFrame = keyframe\n      }\n\n      let mix = (animation.time % frameTime) / frameTime\n\n      if (animation.directionBackwards) mix = 1 - mix\n\n      if (animation.currentFrame !== animation.lastFrame) {\n        this.morphTargetInfluences[animation.currentFrame] = mix * weight\n        this.morphTargetInfluences[animation.lastFrame] = (1 - mix) * weight\n      } else {\n        this.morphTargetInfluences[animation.currentFrame] = weight\n      }\n    }\n  }\n}\n\nexport { MorphBlendMesh }\n"], "mappings": ";AAEA,MAAMA,cAAA,SAAuBC,IAAA,CAAK;EAChCC,YAAYC,QAAA,EAAUC,QAAA,EAAU;IAC9B,MAAMD,QAAA,EAAUC,QAAQ;IAExB,KAAKC,aAAA,GAAgB,CAAE;IACvB,KAAKC,cAAA,GAAiB,EAAE;IAKxB,MAAMC,SAAA,GAAYC,MAAA,CAAOC,IAAA,CAAK,KAAKC,qBAAqB,EAAEC,MAAA;IAE1D,MAAMC,IAAA,GAAO;IAEb,MAAMC,UAAA,GAAa;IACnB,MAAMC,QAAA,GAAWP,SAAA,GAAY;IAE7B,MAAMQ,GAAA,GAAMR,SAAA,GAAY;IAExB,KAAKS,eAAA,CAAgBJ,IAAA,EAAMC,UAAA,EAAYC,QAAA,EAAUC,GAAG;IACpD,KAAKE,kBAAA,CAAmBL,IAAA,EAAM,CAAC;EAChC;EAEDI,gBAAgBJ,IAAA,EAAMM,KAAA,EAAOC,GAAA,EAAKJ,GAAA,EAAK;IACrC,MAAMK,SAAA,GAAY;MAChBF,KAAA;MACAC,GAAA;MAEAR,MAAA,EAAQQ,GAAA,GAAMD,KAAA,GAAQ;MAEtBH,GAAA;MACAM,QAAA,GAAWF,GAAA,GAAMD,KAAA,IAASH,GAAA;MAE1BO,SAAA,EAAW;MACXC,YAAA,EAAc;MAEdC,MAAA,EAAQ;MAERC,IAAA,EAAM;MACNC,SAAA,EAAW;MACXC,MAAA,EAAQ;MAERC,kBAAA,EAAoB;MACpBC,YAAA,EAAc;IACf;IAED,KAAKxB,aAAA,CAAcO,IAAI,IAAIQ,SAAA;IAC3B,KAAKd,cAAA,CAAewB,IAAA,CAAKV,SAAS;EACnC;EAEDW,qBAAqBhB,GAAA,EAAK;IACxB,MAAMiB,OAAA,GAAU;IAEhB,IAAIC,cAAA;IAEJ,MAAMC,WAAA,GAAc,CAAE;IAEtB,IAAIC,CAAA,GAAI;IAER,WAAWC,GAAA,IAAO,KAAK1B,qBAAA,EAAuB;MAC5C,MAAM2B,MAAA,GAASD,GAAA,CAAIE,KAAA,CAAMN,OAAO;MAEhC,IAAIK,MAAA,IAAUA,MAAA,CAAO1B,MAAA,GAAS,GAAG;QAC/B,MAAMC,IAAA,GAAOyB,MAAA,CAAO,CAAC;QAErB,IAAI,CAACH,WAAA,CAAYtB,IAAI,GAAGsB,WAAA,CAAYtB,IAAI,IAAI;UAAEM,KAAA,EAAOqB,QAAA;UAAUpB,GAAA,EAAK,CAAAoB;QAAW;QAE/E,MAAMC,KAAA,GAAQN,WAAA,CAAYtB,IAAI;QAE9B,IAAIuB,CAAA,GAAIK,KAAA,CAAMtB,KAAA,EAAOsB,KAAA,CAAMtB,KAAA,GAAQiB,CAAA;QACnC,IAAIA,CAAA,GAAIK,KAAA,CAAMrB,GAAA,EAAKqB,KAAA,CAAMrB,GAAA,GAAMgB,CAAA;QAE/B,IAAI,CAACF,cAAA,EAAgBA,cAAA,GAAiBrB,IAAA;MACvC;MAEDuB,CAAA;IACD;IAED,WAAWvB,IAAA,IAAQsB,WAAA,EAAa;MAC9B,MAAMM,KAAA,GAAQN,WAAA,CAAYtB,IAAI;MAC9B,KAAKI,eAAA,CAAgBJ,IAAA,EAAM4B,KAAA,CAAMtB,KAAA,EAAOsB,KAAA,CAAMrB,GAAA,EAAKJ,GAAG;IACvD;IAED,KAAKkB,cAAA,GAAiBA,cAAA;EACvB;EAEDQ,6BAA6B7B,IAAA,EAAM;IACjC,MAAMQ,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUM,SAAA,GAAY;MACtBN,SAAA,CAAUQ,kBAAA,GAAqB;IAChC;EACF;EAEDc,8BAA8B9B,IAAA,EAAM;IAClC,MAAMQ,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUM,SAAA,GAAY;MACtBN,SAAA,CAAUQ,kBAAA,GAAqB;IAChC;EACF;EAEDe,gBAAgB/B,IAAA,EAAMG,GAAA,EAAK;IACzB,MAAMK,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUL,GAAA,GAAMA,GAAA;MAChBK,SAAA,CAAUC,QAAA,IAAYD,SAAA,CAAUD,GAAA,GAAMC,SAAA,CAAUF,KAAA,IAASE,SAAA,CAAUL,GAAA;IACpE;EACF;EAED6B,qBAAqBhC,IAAA,EAAMS,QAAA,EAAU;IACnC,MAAMD,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUC,QAAA,GAAWA,QAAA;MACrBD,SAAA,CAAUL,GAAA,IAAOK,SAAA,CAAUD,GAAA,GAAMC,SAAA,CAAUF,KAAA,IAASE,SAAA,CAAUC,QAAA;IAC/D;EACF;EAEDJ,mBAAmBL,IAAA,EAAMe,MAAA,EAAQ;IAC/B,MAAMP,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUO,MAAA,GAASA,MAAA;IACpB;EACF;EAEDkB,iBAAiBjC,IAAA,EAAMa,IAAA,EAAM;IAC3B,MAAML,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUK,IAAA,GAAOA,IAAA;IAClB;EACF;EAEDqB,iBAAiBlC,IAAA,EAAM;IACrB,IAAIa,IAAA,GAAO;IAEX,MAAML,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbK,IAAA,GAAOL,SAAA,CAAUK,IAAA;IAClB;IAED,OAAOA,IAAA;EACR;EAEDsB,qBAAqBnC,IAAA,EAAM;IACzB,IAAIS,QAAA,GAAW;IAEf,MAAMD,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbC,QAAA,GAAWD,SAAA,CAAUC,QAAA;IACtB;IAED,OAAOA,QAAA;EACR;EAED2B,cAAcpC,IAAA,EAAM;IAClB,MAAMQ,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUK,IAAA,GAAO;MACjBL,SAAA,CAAUI,MAAA,GAAS;IACzB,OAAW;MACLyB,OAAA,CAAQC,IAAA,CAAK,qCAAqCtC,IAAA,GAAO,iCAAiC;IAC3F;EACF;EAEDuC,cAAcvC,IAAA,EAAM;IAClB,MAAMQ,SAAA,GAAY,KAAKf,aAAA,CAAcO,IAAI;IAEzC,IAAIQ,SAAA,EAAW;MACbA,SAAA,CAAUI,MAAA,GAAS;IACpB;EACF;EAED4B,OAAOC,KAAA,EAAO;IACZ,SAASlB,CAAA,GAAI,GAAGmB,EAAA,GAAK,KAAKhD,cAAA,CAAeK,MAAA,EAAQwB,CAAA,GAAImB,EAAA,EAAInB,CAAA,IAAK;MAC5D,MAAMf,SAAA,GAAY,KAAKd,cAAA,CAAe6B,CAAC;MAEvC,IAAI,CAACf,SAAA,CAAUI,MAAA,EAAQ;MAEvB,MAAM+B,SAAA,GAAYnC,SAAA,CAAUC,QAAA,GAAWD,SAAA,CAAUT,MAAA;MAEjDS,SAAA,CAAUK,IAAA,IAAQL,SAAA,CAAUM,SAAA,GAAY2B,KAAA;MAExC,IAAIjC,SAAA,CAAUS,YAAA,EAAc;QAC1B,IAAIT,SAAA,CAAUK,IAAA,GAAOL,SAAA,CAAUC,QAAA,IAAYD,SAAA,CAAUK,IAAA,GAAO,GAAG;UAC7DL,SAAA,CAAUM,SAAA,IAAa;UAEvB,IAAIN,SAAA,CAAUK,IAAA,GAAOL,SAAA,CAAUC,QAAA,EAAU;YACvCD,SAAA,CAAUK,IAAA,GAAOL,SAAA,CAAUC,QAAA;YAC3BD,SAAA,CAAUQ,kBAAA,GAAqB;UAChC;UAED,IAAIR,SAAA,CAAUK,IAAA,GAAO,GAAG;YACtBL,SAAA,CAAUK,IAAA,GAAO;YACjBL,SAAA,CAAUQ,kBAAA,GAAqB;UAChC;QACF;MACT,OAAa;QACLR,SAAA,CAAUK,IAAA,GAAOL,SAAA,CAAUK,IAAA,GAAOL,SAAA,CAAUC,QAAA;QAE5C,IAAID,SAAA,CAAUK,IAAA,GAAO,GAAGL,SAAA,CAAUK,IAAA,IAAQL,SAAA,CAAUC,QAAA;MACrD;MAED,MAAMmC,QAAA,GACJpC,SAAA,CAAUF,KAAA,GAAQuC,SAAA,CAAUC,KAAA,CAAMC,IAAA,CAAKC,KAAA,CAAMxC,SAAA,CAAUK,IAAA,GAAO8B,SAAS,GAAG,GAAGnC,SAAA,CAAUT,MAAA,GAAS,CAAC;MACnG,MAAMgB,MAAA,GAASP,SAAA,CAAUO,MAAA;MAEzB,IAAI6B,QAAA,KAAapC,SAAA,CAAUG,YAAA,EAAc;QACvC,KAAKsC,qBAAA,CAAsBzC,SAAA,CAAUE,SAAS,IAAI;QAClD,KAAKuC,qBAAA,CAAsBzC,SAAA,CAAUG,YAAY,IAAI,IAAII,MAAA;QAEzD,KAAKkC,qBAAA,CAAsBL,QAAQ,IAAI;QAEvCpC,SAAA,CAAUE,SAAA,GAAYF,SAAA,CAAUG,YAAA;QAChCH,SAAA,CAAUG,YAAA,GAAeiC,QAAA;MAC1B;MAED,IAAIM,GAAA,GAAO1C,SAAA,CAAUK,IAAA,GAAO8B,SAAA,GAAaA,SAAA;MAEzC,IAAInC,SAAA,CAAUQ,kBAAA,EAAoBkC,GAAA,GAAM,IAAIA,GAAA;MAE5C,IAAI1C,SAAA,CAAUG,YAAA,KAAiBH,SAAA,CAAUE,SAAA,EAAW;QAClD,KAAKuC,qBAAA,CAAsBzC,SAAA,CAAUG,YAAY,IAAIuC,GAAA,GAAMnC,MAAA;QAC3D,KAAKkC,qBAAA,CAAsBzC,SAAA,CAAUE,SAAS,KAAK,IAAIwC,GAAA,IAAOnC,MAAA;MACtE,OAAa;QACL,KAAKkC,qBAAA,CAAsBzC,SAAA,CAAUG,YAAY,IAAII,MAAA;MACtD;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}