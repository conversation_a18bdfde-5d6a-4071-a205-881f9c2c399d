{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nconst _inverseMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _ray = /* @__PURE__ */new THREE.Ray();\nconst _sphere = /* @__PURE__ */new THREE.Sphere();\nconst _position = /* @__PURE__ */new THREE.Vector3();\nclass PositionPoint extends THREE.Group {\n  constructor() {\n    super();\n    this.size = 0;\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  }\n\n  // This will allow the virtual instance have bounds\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n  raycast(raycaster, intersects) {\n    var _raycaster$params$Poi, _raycaster$params$Poi2;\n    const parent = this.instance.current;\n    if (!parent || !parent.geometry) return;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey);\n    // If the instance wasn't found or exceeds the parents draw range, bail out\n    if (instanceId === -1 || instanceId > parent.geometry.drawRange.count) return;\n    const threshold = (_raycaster$params$Poi = (_raycaster$params$Poi2 = raycaster.params.Points) == null ? void 0 : _raycaster$params$Poi2.threshold) !== null && _raycaster$params$Poi !== void 0 ? _raycaster$params$Poi : 1;\n    _sphere.set(this.getWorldPosition(_position), threshold);\n    if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n    _inverseMatrix.copy(parent.matrixWorld).invert();\n    _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n    const localThreshold = threshold / ((this.scale.x + this.scale.y + this.scale.z) / 3);\n    const localThresholdSq = localThreshold * localThreshold;\n    const rayPointDistanceSq = _ray.distanceSqToPoint(this.position);\n    if (rayPointDistanceSq < localThresholdSq) {\n      const intersectPoint = new THREE.Vector3();\n      _ray.closestPointToPoint(this.position, intersectPoint);\n      intersectPoint.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(intersectPoint);\n      if (distance < raycaster.near || distance > raycaster.far) return;\n      intersects.push({\n        distance: distance,\n        distanceToRay: Math.sqrt(rayPointDistanceSq),\n        point: intersectPoint,\n        index: instanceId,\n        face: null,\n        object: this\n      });\n    }\n  }\n}\nlet i, positionRef;\nconst context = /* @__PURE__ */React.createContext(null);\nconst parentMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst position = /* @__PURE__ */new THREE.Vector3();\n\n/**\n * Instance implementation, relies on react + context to update the attributes based on the children of this component\n */\nconst PointsInstances = /* @__PURE__ */React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  ...props\n}, ref) => {\n  const parentRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => parentRef.current, []);\n  const [refs, setRefs] = React.useState([]);\n  const [[positions, colors, sizes]] = React.useState(() => [new Float32Array(limit * 3), Float32Array.from({\n    length: limit * 3\n  }, () => 1), Float32Array.from({\n    length: limit\n  }, () => 1)]);\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.geometry.attributes.position.needsUpdate = true;\n  });\n  useFrame(() => {\n    parentRef.current.updateMatrix();\n    parentRef.current.updateMatrixWorld();\n    parentMatrix.copy(parentRef.current.matrixWorld).invert();\n    parentRef.current.geometry.drawRange.count = Math.min(limit, range !== undefined ? range : limit, refs.length);\n    for (i = 0; i < refs.length; i++) {\n      positionRef = refs[i].current;\n      positionRef.getWorldPosition(position).applyMatrix4(parentMatrix);\n      position.toArray(positions, i * 3);\n      parentRef.current.geometry.attributes.position.needsUpdate = true;\n      positionRef.matrixWorldNeedsUpdate = true;\n      positionRef.color.toArray(colors, i * 3);\n      parentRef.current.geometry.attributes.color.needsUpdate = true;\n      sizes.set([positionRef.size], i);\n      parentRef.current.geometry.attributes.size.needsUpdate = true;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setRefs(refs => [...refs, ref]);\n      return () => setRefs(refs => refs.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    userData: {\n      instances: refs\n    },\n    matrixAutoUpdate: false,\n    ref: parentRef,\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1],\n    usage: THREE.DynamicDrawUsage\n  })), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nconst Point = /* @__PURE__ */React.forwardRef(({\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionPoint\n  }), []);\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionPoint\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: group\n  }, props), children);\n});\n\n/**\n * Buffer implementation, relies on complete buffers of the correct number, leaves it to the user to update them\n */\n\nconst PointsBuffer = /* @__PURE__ */React.forwardRef(({\n  children,\n  positions,\n  colors,\n  sizes,\n  stride = 3,\n  ...props\n}, forwardedRef) => {\n  const pointsRef = React.useRef(null);\n  React.useImperativeHandle(forwardedRef, () => pointsRef.current, []);\n  useFrame(() => {\n    const attr = pointsRef.current.geometry.attributes;\n    attr.position.needsUpdate = true;\n    if (colors) attr.color.needsUpdate = true;\n    if (sizes) attr.size.needsUpdate = true;\n  });\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    ref: pointsRef\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, stride],\n    usage: THREE.DynamicDrawUsage\n  }), colors && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, stride],\n    count: colors.length / stride,\n    usage: THREE.DynamicDrawUsage\n  }), sizes && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1],\n    count: sizes.length / stride,\n    usage: THREE.DynamicDrawUsage\n  })), children);\n});\nconst Points = /* @__PURE__ */React.forwardRef((props, forwardedRef) => {\n  if (props.positions instanceof Float32Array) {\n    return /*#__PURE__*/React.createElement(PointsBuffer, _extends({}, props, {\n      ref: forwardedRef\n    }));\n  } else return /*#__PURE__*/React.createElement(PointsInstances, _extends({}, props, {\n    ref: forwardedRef\n  }));\n});\nexport { Point, Points, PointsBuffer, PositionPoint };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useFrame", "_inverseMatrix", "Matrix4", "_ray", "<PERSON>", "_sphere", "Sphere", "_position", "Vector3", "PositionPoint", "Group", "constructor", "size", "color", "Color", "instance", "current", "undefined", "<PERSON><PERSON><PERSON>", "geometry", "_this$instance$curren", "raycast", "raycaster", "intersects", "_raycaster$params$Poi", "_raycaster$params$Poi2", "parent", "instanceId", "userData", "instances", "indexOf", "drawRange", "count", "threshold", "params", "Points", "set", "getWorldPosition", "ray", "intersectsSphere", "copy", "matrixWorld", "invert", "applyMatrix4", "localThreshold", "scale", "x", "y", "z", "localThresholdSq", "rayPointDistanceSq", "distanceSqToPoint", "position", "intersectPoint", "closestPointToPoint", "distance", "origin", "distanceTo", "near", "far", "push", "distanceToRay", "Math", "sqrt", "point", "index", "face", "object", "i", "positionRef", "context", "createContext", "parentMatrix", "PointsInstances", "forwardRef", "children", "range", "limit", "props", "ref", "parentRef", "useRef", "useImperativeHandle", "refs", "setRefs", "useState", "positions", "colors", "sizes", "Float32Array", "from", "length", "useEffect", "attributes", "needsUpdate", "updateMatrix", "updateMatrixWorld", "min", "toArray", "matrixWorldNeedsUpdate", "api", "useMemo", "getParent", "subscribe", "filter", "item", "createElement", "matrixAutoUpdate", "attach", "args", "usage", "DynamicDrawUsage", "Provider", "value", "Point", "group", "useContext", "useLayoutEffect", "PointsBuffer", "stride", "forwardedRef", "pointsRef", "attr"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Points.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\n\nconst _inverseMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst _ray = /* @__PURE__ */new THREE.Ray();\nconst _sphere = /* @__PURE__ */new THREE.Sphere();\nconst _position = /* @__PURE__ */new THREE.Vector3();\nclass PositionPoint extends THREE.Group {\n  constructor() {\n    super();\n    this.size = 0;\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  }\n\n  // This will allow the virtual instance have bounds\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n  raycast(raycaster, intersects) {\n    var _raycaster$params$Poi, _raycaster$params$Poi2;\n    const parent = this.instance.current;\n    if (!parent || !parent.geometry) return;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey);\n    // If the instance wasn't found or exceeds the parents draw range, bail out\n    if (instanceId === -1 || instanceId > parent.geometry.drawRange.count) return;\n    const threshold = (_raycaster$params$Poi = (_raycaster$params$Poi2 = raycaster.params.Points) == null ? void 0 : _raycaster$params$Poi2.threshold) !== null && _raycaster$params$Poi !== void 0 ? _raycaster$params$Poi : 1;\n    _sphere.set(this.getWorldPosition(_position), threshold);\n    if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n    _inverseMatrix.copy(parent.matrixWorld).invert();\n    _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n    const localThreshold = threshold / ((this.scale.x + this.scale.y + this.scale.z) / 3);\n    const localThresholdSq = localThreshold * localThreshold;\n    const rayPointDistanceSq = _ray.distanceSqToPoint(this.position);\n    if (rayPointDistanceSq < localThresholdSq) {\n      const intersectPoint = new THREE.Vector3();\n      _ray.closestPointToPoint(this.position, intersectPoint);\n      intersectPoint.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(intersectPoint);\n      if (distance < raycaster.near || distance > raycaster.far) return;\n      intersects.push({\n        distance: distance,\n        distanceToRay: Math.sqrt(rayPointDistanceSq),\n        point: intersectPoint,\n        index: instanceId,\n        face: null,\n        object: this\n      });\n    }\n  }\n}\nlet i, positionRef;\nconst context = /* @__PURE__ */React.createContext(null);\nconst parentMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst position = /* @__PURE__ */new THREE.Vector3();\n\n/**\n * Instance implementation, relies on react + context to update the attributes based on the children of this component\n */\nconst PointsInstances = /* @__PURE__ */React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  ...props\n}, ref) => {\n  const parentRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => parentRef.current, []);\n  const [refs, setRefs] = React.useState([]);\n  const [[positions, colors, sizes]] = React.useState(() => [new Float32Array(limit * 3), Float32Array.from({\n    length: limit * 3\n  }, () => 1), Float32Array.from({\n    length: limit\n  }, () => 1)]);\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.geometry.attributes.position.needsUpdate = true;\n  });\n  useFrame(() => {\n    parentRef.current.updateMatrix();\n    parentRef.current.updateMatrixWorld();\n    parentMatrix.copy(parentRef.current.matrixWorld).invert();\n    parentRef.current.geometry.drawRange.count = Math.min(limit, range !== undefined ? range : limit, refs.length);\n    for (i = 0; i < refs.length; i++) {\n      positionRef = refs[i].current;\n      positionRef.getWorldPosition(position).applyMatrix4(parentMatrix);\n      position.toArray(positions, i * 3);\n      parentRef.current.geometry.attributes.position.needsUpdate = true;\n      positionRef.matrixWorldNeedsUpdate = true;\n      positionRef.color.toArray(colors, i * 3);\n      parentRef.current.geometry.attributes.color.needsUpdate = true;\n      sizes.set([positionRef.size], i);\n      parentRef.current.geometry.attributes.size.needsUpdate = true;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setRefs(refs => [...refs, ref]);\n      return () => setRefs(refs => refs.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    userData: {\n      instances: refs\n    },\n    matrixAutoUpdate: false,\n    ref: parentRef,\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3],\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1],\n    usage: THREE.DynamicDrawUsage\n  })), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nconst Point = /* @__PURE__ */React.forwardRef(({\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionPoint\n  }), []);\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionPoint\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: group\n  }, props), children);\n});\n\n/**\n * Buffer implementation, relies on complete buffers of the correct number, leaves it to the user to update them\n */\n\nconst PointsBuffer = /* @__PURE__ */React.forwardRef(({\n  children,\n  positions,\n  colors,\n  sizes,\n  stride = 3,\n  ...props\n}, forwardedRef) => {\n  const pointsRef = React.useRef(null);\n  React.useImperativeHandle(forwardedRef, () => pointsRef.current, []);\n  useFrame(() => {\n    const attr = pointsRef.current.geometry.attributes;\n    attr.position.needsUpdate = true;\n    if (colors) attr.color.needsUpdate = true;\n    if (sizes) attr.size.needsUpdate = true;\n  });\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    ref: pointsRef\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, stride],\n    usage: THREE.DynamicDrawUsage\n  }), colors && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, stride],\n    count: colors.length / stride,\n    usage: THREE.DynamicDrawUsage\n  }), sizes && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1],\n    count: sizes.length / stride,\n    usage: THREE.DynamicDrawUsage\n  })), children);\n});\nconst Points = /* @__PURE__ */React.forwardRef((props, forwardedRef) => {\n  if (props.positions instanceof Float32Array) {\n    return /*#__PURE__*/React.createElement(PointsBuffer, _extends({}, props, {\n      ref: forwardedRef\n    }));\n  } else return /*#__PURE__*/React.createElement(PointsInstances, _extends({}, props, {\n    ref: forwardedRef\n  }));\n});\n\nexport { Point, Points, PointsBuffer, PositionPoint };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AAErD,MAAMC,cAAc,GAAG,eAAe,IAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC;AACzD,MAAMC,IAAI,GAAG,eAAe,IAAIN,KAAK,CAACO,GAAG,CAAC,CAAC;AAC3C,MAAMC,OAAO,GAAG,eAAe,IAAIR,KAAK,CAACS,MAAM,CAAC,CAAC;AACjD,MAAMC,SAAS,GAAG,eAAe,IAAIV,KAAK,CAACW,OAAO,CAAC,CAAC;AACpD,MAAMC,aAAa,SAASZ,KAAK,CAACa,KAAK,CAAC;EACtCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,KAAK,GAAG,IAAIhB,KAAK,CAACiB,KAAK,CAAC,OAAO,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG;MACdC,OAAO,EAAEC;IACX,CAAC;IACD,IAAI,CAACC,WAAW,GAAG;MACjBF,OAAO,EAAEC;IACX,CAAC;EACH;;EAEA;EACA,IAAIE,QAAQA,CAAA,EAAG;IACb,IAAIC,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAG,IAAI,CAACL,QAAQ,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,qBAAqB,CAACD,QAAQ;EAC1G;EACAE,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;IAC7B,IAAIC,qBAAqB,EAAEC,sBAAsB;IACjD,MAAMC,MAAM,GAAG,IAAI,CAACX,QAAQ,CAACC,OAAO;IACpC,IAAI,CAACU,MAAM,IAAI,CAACA,MAAM,CAACP,QAAQ,EAAE;IACjC,MAAMQ,UAAU,GAAGD,MAAM,CAACE,QAAQ,CAACC,SAAS,CAACC,OAAO,CAAC,IAAI,CAACZ,WAAW,CAAC;IACtE;IACA,IAAIS,UAAU,KAAK,CAAC,CAAC,IAAIA,UAAU,GAAGD,MAAM,CAACP,QAAQ,CAACY,SAAS,CAACC,KAAK,EAAE;IACvE,MAAMC,SAAS,GAAG,CAACT,qBAAqB,GAAG,CAACC,sBAAsB,GAAGH,SAAS,CAACY,MAAM,CAACC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,sBAAsB,CAACQ,SAAS,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;IAC3NnB,OAAO,CAAC+B,GAAG,CAAC,IAAI,CAACC,gBAAgB,CAAC9B,SAAS,CAAC,EAAE0B,SAAS,CAAC;IACxD,IAAIX,SAAS,CAACgB,GAAG,CAACC,gBAAgB,CAAClC,OAAO,CAAC,KAAK,KAAK,EAAE;IACvDJ,cAAc,CAACuC,IAAI,CAACd,MAAM,CAACe,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IAChDvC,IAAI,CAACqC,IAAI,CAAClB,SAAS,CAACgB,GAAG,CAAC,CAACK,YAAY,CAAC1C,cAAc,CAAC;IACrD,MAAM2C,cAAc,GAAGX,SAAS,IAAI,CAAC,IAAI,CAACY,KAAK,CAACC,CAAC,GAAG,IAAI,CAACD,KAAK,CAACE,CAAC,GAAG,IAAI,CAACF,KAAK,CAACG,CAAC,IAAI,CAAC,CAAC;IACrF,MAAMC,gBAAgB,GAAGL,cAAc,GAAGA,cAAc;IACxD,MAAMM,kBAAkB,GAAG/C,IAAI,CAACgD,iBAAiB,CAAC,IAAI,CAACC,QAAQ,CAAC;IAChE,IAAIF,kBAAkB,GAAGD,gBAAgB,EAAE;MACzC,MAAMI,cAAc,GAAG,IAAIxD,KAAK,CAACW,OAAO,CAAC,CAAC;MAC1CL,IAAI,CAACmD,mBAAmB,CAAC,IAAI,CAACF,QAAQ,EAAEC,cAAc,CAAC;MACvDA,cAAc,CAACV,YAAY,CAAC,IAAI,CAACF,WAAW,CAAC;MAC7C,MAAMc,QAAQ,GAAGjC,SAAS,CAACgB,GAAG,CAACkB,MAAM,CAACC,UAAU,CAACJ,cAAc,CAAC;MAChE,IAAIE,QAAQ,GAAGjC,SAAS,CAACoC,IAAI,IAAIH,QAAQ,GAAGjC,SAAS,CAACqC,GAAG,EAAE;MAC3DpC,UAAU,CAACqC,IAAI,CAAC;QACdL,QAAQ,EAAEA,QAAQ;QAClBM,aAAa,EAAEC,IAAI,CAACC,IAAI,CAACb,kBAAkB,CAAC;QAC5Cc,KAAK,EAAEX,cAAc;QACrBY,KAAK,EAAEtC,UAAU;QACjBuC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF;AACF;AACA,IAAIC,CAAC,EAAEC,WAAW;AAClB,MAAMC,OAAO,GAAG,eAAexE,KAAK,CAACyE,aAAa,CAAC,IAAI,CAAC;AACxD,MAAMC,YAAY,GAAG,eAAe,IAAI3E,KAAK,CAACK,OAAO,CAAC,CAAC;AACvD,MAAMkD,QAAQ,GAAG,eAAe,IAAIvD,KAAK,CAACW,OAAO,CAAC,CAAC;;AAEnD;AACA;AACA;AACA,MAAMiE,eAAe,GAAG,eAAe3E,KAAK,CAAC4E,UAAU,CAAC,CAAC;EACvDC,QAAQ;EACRC,KAAK;EACLC,KAAK,GAAG,IAAI;EACZ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,SAAS,GAAGlF,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;EACpCnF,KAAK,CAACoF,mBAAmB,CAACH,GAAG,EAAE,MAAMC,SAAS,CAAChE,OAAO,EAAE,EAAE,CAAC;EAC3D,MAAM,CAACmE,IAAI,EAAEC,OAAO,CAAC,GAAGtF,KAAK,CAACuF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC,CAACC,SAAS,EAAEC,MAAM,EAAEC,KAAK,CAAC,CAAC,GAAG1F,KAAK,CAACuF,QAAQ,CAAC,MAAM,CAAC,IAAII,YAAY,CAACZ,KAAK,GAAG,CAAC,CAAC,EAAEY,YAAY,CAACC,IAAI,CAAC;IACxGC,MAAM,EAAEd,KAAK,GAAG;EAClB,CAAC,EAAE,MAAM,CAAC,CAAC,EAAEY,YAAY,CAACC,IAAI,CAAC;IAC7BC,MAAM,EAAEd;EACV,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EACb/E,KAAK,CAAC8F,SAAS,CAAC,MAAM;IACpB;IACAZ,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAAC0E,UAAU,CAACzC,QAAQ,CAAC0C,WAAW,GAAG,IAAI;EACnE,CAAC,CAAC;EACF9F,QAAQ,CAAC,MAAM;IACbgF,SAAS,CAAChE,OAAO,CAAC+E,YAAY,CAAC,CAAC;IAChCf,SAAS,CAAChE,OAAO,CAACgF,iBAAiB,CAAC,CAAC;IACrCxB,YAAY,CAAChC,IAAI,CAACwC,SAAS,CAAChE,OAAO,CAACyB,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IACzDsC,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACY,SAAS,CAACC,KAAK,GAAG8B,IAAI,CAACmC,GAAG,CAACpB,KAAK,EAAED,KAAK,KAAK3D,SAAS,GAAG2D,KAAK,GAAGC,KAAK,EAAEM,IAAI,CAACQ,MAAM,CAAC;IAC9G,KAAKvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,IAAI,CAACQ,MAAM,EAAEvB,CAAC,EAAE,EAAE;MAChCC,WAAW,GAAGc,IAAI,CAACf,CAAC,CAAC,CAACpD,OAAO;MAC7BqD,WAAW,CAAChC,gBAAgB,CAACe,QAAQ,CAAC,CAACT,YAAY,CAAC6B,YAAY,CAAC;MACjEpB,QAAQ,CAAC8C,OAAO,CAACZ,SAAS,EAAElB,CAAC,GAAG,CAAC,CAAC;MAClCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAAC0E,UAAU,CAACzC,QAAQ,CAAC0C,WAAW,GAAG,IAAI;MACjEzB,WAAW,CAAC8B,sBAAsB,GAAG,IAAI;MACzC9B,WAAW,CAACxD,KAAK,CAACqF,OAAO,CAACX,MAAM,EAAEnB,CAAC,GAAG,CAAC,CAAC;MACxCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAAC0E,UAAU,CAAChF,KAAK,CAACiF,WAAW,GAAG,IAAI;MAC9DN,KAAK,CAACpD,GAAG,CAAC,CAACiC,WAAW,CAACzD,IAAI,CAAC,EAAEwD,CAAC,CAAC;MAChCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAAC0E,UAAU,CAACjF,IAAI,CAACkF,WAAW,GAAG,IAAI;IAC/D;EACF,CAAC,CAAC;EACF,MAAMM,GAAG,GAAGtG,KAAK,CAACuG,OAAO,CAAC,OAAO;IAC/BC,SAAS,EAAEA,CAAA,KAAMtB,SAAS;IAC1BuB,SAAS,EAAExB,GAAG,IAAI;MAChBK,OAAO,CAACD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,GAAG,CAAC,CAAC;MAC/B,OAAO,MAAMK,OAAO,CAACD,IAAI,IAAIA,IAAI,CAACqB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzF,OAAO,KAAK+D,GAAG,CAAC/D,OAAO,CAAC,CAAC;IACjF;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAalB,KAAK,CAAC4G,aAAa,CAAC,QAAQ,EAAE9G,QAAQ,CAAC;IACzDgC,QAAQ,EAAE;MACRC,SAAS,EAAEsD;IACb,CAAC;IACDwB,gBAAgB,EAAE,KAAK;IACvB5B,GAAG,EAAEC,SAAS;IACd3D,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEyD,KAAK,CAAC,EAAE,aAAahF,KAAK,CAAC4G,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa5G,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtHE,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,CAACvB,SAAS,EAAE,CAAC,CAAC;IACpBwB,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAE,aAAajH,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtDE,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACtB,MAAM,EAAE,CAAC,CAAC;IACjBuB,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAE,aAAajH,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtDE,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,CAACrB,KAAK,EAAE,CAAC,CAAC;IAChBsB,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,CAAC,EAAE,aAAajH,KAAK,CAAC4G,aAAa,CAACpC,OAAO,CAAC0C,QAAQ,EAAE;IACtDC,KAAK,EAAEb;EACT,CAAC,EAAEzB,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAMuC,KAAK,GAAG,eAAepH,KAAK,CAAC4E,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACR,GAAGG;AACL,CAAC,EAAEC,GAAG,KAAK;EACTjF,KAAK,CAACuG,OAAO,CAAC,MAAMtG,MAAM,CAAC;IACzBU;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM0G,KAAK,GAAGrH,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;EAChCnF,KAAK,CAACoF,mBAAmB,CAACH,GAAG,EAAE,MAAMoC,KAAK,CAACnG,OAAO,EAAE,EAAE,CAAC;EACvD,MAAM;IACJuF,SAAS;IACTD;EACF,CAAC,GAAGxG,KAAK,CAACsH,UAAU,CAAC9C,OAAO,CAAC;EAC7BxE,KAAK,CAACuH,eAAe,CAAC,MAAMd,SAAS,CAACY,KAAK,CAAC,EAAE,EAAE,CAAC;EACjD,OAAO,aAAarH,KAAK,CAAC4G,aAAa,CAAC,eAAe,EAAE9G,QAAQ,CAAC;IAChEmB,QAAQ,EAAEuF,SAAS,CAAC,CAAC;IACrBpF,WAAW,EAAEiG,KAAK;IAClBpC,GAAG,EAAEoC;EACP,CAAC,EAAErC,KAAK,CAAC,EAAEH,QAAQ,CAAC;AACtB,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,MAAM2C,YAAY,GAAG,eAAexH,KAAK,CAAC4E,UAAU,CAAC,CAAC;EACpDC,QAAQ;EACRW,SAAS;EACTC,MAAM;EACNC,KAAK;EACL+B,MAAM,GAAG,CAAC;EACV,GAAGzC;AACL,CAAC,EAAE0C,YAAY,KAAK;EAClB,MAAMC,SAAS,GAAG3H,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;EACpCnF,KAAK,CAACoF,mBAAmB,CAACsC,YAAY,EAAE,MAAMC,SAAS,CAACzG,OAAO,EAAE,EAAE,CAAC;EACpEhB,QAAQ,CAAC,MAAM;IACb,MAAM0H,IAAI,GAAGD,SAAS,CAACzG,OAAO,CAACG,QAAQ,CAAC0E,UAAU;IAClD6B,IAAI,CAACtE,QAAQ,CAAC0C,WAAW,GAAG,IAAI;IAChC,IAAIP,MAAM,EAAEmC,IAAI,CAAC7G,KAAK,CAACiF,WAAW,GAAG,IAAI;IACzC,IAAIN,KAAK,EAAEkC,IAAI,CAAC9G,IAAI,CAACkF,WAAW,GAAG,IAAI;EACzC,CAAC,CAAC;EACF,OAAO,aAAahG,KAAK,CAAC4G,aAAa,CAAC,QAAQ,EAAE9G,QAAQ,CAAC;IACzDmF,GAAG,EAAE0C;EACP,CAAC,EAAE3C,KAAK,CAAC,EAAE,aAAahF,KAAK,CAAC4G,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa5G,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtHE,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,CAACvB,SAAS,EAAEiC,MAAM,CAAC;IACzBT,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAExB,MAAM,IAAI,aAAazF,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IAChEE,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACtB,MAAM,EAAEgC,MAAM,CAAC;IACtBvF,KAAK,EAAEuD,MAAM,CAACI,MAAM,GAAG4B,MAAM;IAC7BT,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,EAAEvB,KAAK,IAAI,aAAa1F,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IAC/DE,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,CAACrB,KAAK,EAAE,CAAC,CAAC;IAChBxD,KAAK,EAAEwD,KAAK,CAACG,MAAM,GAAG4B,MAAM;IAC5BT,KAAK,EAAEjH,KAAK,CAACkH;EACf,CAAC,CAAC,CAAC,EAAEpC,QAAQ,CAAC;AAChB,CAAC,CAAC;AACF,MAAMxC,MAAM,GAAG,eAAerC,KAAK,CAAC4E,UAAU,CAAC,CAACI,KAAK,EAAE0C,YAAY,KAAK;EACtE,IAAI1C,KAAK,CAACQ,SAAS,YAAYG,YAAY,EAAE;IAC3C,OAAO,aAAa3F,KAAK,CAAC4G,aAAa,CAACY,YAAY,EAAE1H,QAAQ,CAAC,CAAC,CAAC,EAAEkF,KAAK,EAAE;MACxEC,GAAG,EAAEyC;IACP,CAAC,CAAC,CAAC;EACL,CAAC,MAAM,OAAO,aAAa1H,KAAK,CAAC4G,aAAa,CAACjC,eAAe,EAAE7E,QAAQ,CAAC,CAAC,CAAC,EAAEkF,KAAK,EAAE;IAClFC,GAAG,EAAEyC;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASN,KAAK,EAAE/E,MAAM,EAAEmF,YAAY,EAAE7G,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}