{"ast": null, "code": "import * as THREE from 'three';\nimport { TextureLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport * as React from 'react';\nimport { useState } from 'react';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\n// utils\nconst getFirstFrame = (frames, frameName) => {\n  if (Array.isArray(frames)) {\n    return frames[0];\n  } else {\n    const k = frameName !== null && frameName !== void 0 ? frameName : Object.keys(frames)[0];\n    return frames[k][0];\n  }\n};\nconst checkIfFrameIsEmpty = frameData => {\n  for (let i = 3; i < frameData.length; i += 4) {\n    if (frameData[i] !== 0) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction useSpriteLoader(/** The URL of the sprite sheet. */\ninput, /** The JSON data of the sprite sheet. */\njson, /** The names of the animations in the sprite sheet. */\nanimationNames, /** The number of frames in the sprite sheet. */\nnumberOfFrames, /** A callback that is called when the sprite sheet is loaded. */\nonLoad, /** The settings to use when creating the 2D context. */\ncanvasRenderingContext2DSettings) {\n  const viewportRef = React.useRef(useThree(state => state.viewport));\n  const spriteDataRef = React.useRef(null);\n  const totalFrames = React.useRef(0);\n  const aspectFactor = 0.1;\n  const inputRef = React.useRef(input);\n  const jsonRef = React.useRef(json);\n  const animationFramesRef = React.useRef(animationNames);\n  const [spriteData, setSpriteData] = useState(null);\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const textureLoader = React.useMemo(() => new THREE.TextureLoader(), []);\n  const [spriteObj, setSpriteObj] = useState(null);\n  const calculateAspectRatio = React.useCallback((width, height, factor) => {\n    const adaptedHeight = height * (viewportRef.current.aspect > width / height ? viewportRef.current.width / width : viewportRef.current.height / height);\n    const adaptedWidth = width * (viewportRef.current.aspect > width / height ? viewportRef.current.width / width : viewportRef.current.height / height);\n    const scaleX = adaptedWidth * factor;\n    const scaleY = adaptedHeight * factor;\n    const currentMaxScale = 1;\n    // Calculate the maximum scale based on the aspect ratio and max scale limit\n    let finalMaxScaleW = Math.min(currentMaxScale, scaleX);\n    let finalMaxScaleH = Math.min(currentMaxScale, scaleY);\n\n    // Ensure that scaleX and scaleY do not exceed the max scale while maintaining aspect ratio\n    if (scaleX > currentMaxScale) {\n      finalMaxScaleW = currentMaxScale;\n      finalMaxScaleH = scaleY / scaleX * currentMaxScale;\n    }\n    return new THREE.Vector3(finalMaxScaleW, finalMaxScaleH, 1);\n  }, []);\n  const getRowsAndColumns = React.useCallback((texture, totalFrames) => {\n    if (texture.image) {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d', canvasRenderingContext2DSettings);\n      if (!ctx) {\n        throw new Error('Failed to get 2d context');\n      }\n      canvas.width = texture.image.width;\n      canvas.height = texture.image.height;\n      ctx.drawImage(texture.image, 0, 0);\n      const width = texture.image.width;\n      const height = texture.image.height;\n\n      // Calculate rows and columns based on the number of frames and image dimensions\n      const cols = Math.round(Math.sqrt(totalFrames * (width / height)));\n      const rows = Math.round(totalFrames / cols);\n      const frameWidth = width / cols;\n      const frameHeight = height / rows;\n      const emptyFrames = [];\n      for (let row = 0; row < rows; row++) {\n        for (let col = 0; col < cols; col++) {\n          const frameIndex = row * cols + col;\n          if (frameIndex >= totalFrames) {\n            emptyFrames.push({\n              row,\n              col\n            });\n            continue;\n          }\n          const frameData = ctx.getImageData(col * frameWidth, row * frameHeight, frameWidth, frameHeight).data;\n          const isEmpty = checkIfFrameIsEmpty(frameData);\n          if (isEmpty) {\n            emptyFrames.push({\n              row,\n              col\n            });\n          }\n        }\n      }\n      return {\n        rows,\n        columns: cols,\n        frameWidth,\n        frameHeight,\n        emptyFrames\n      };\n    } else {\n      return {\n        rows: 0,\n        columns: 0,\n        frameWidth: 0,\n        frameHeight: 0,\n        emptyFrames: []\n      };\n    }\n  }, [canvasRenderingContext2DSettings]);\n\n  // calculate scale ratio for the frames\n  const calculateScaleRatio = React.useCallback(frames => {\n    // Helper function to calculate scale ratio for an array of frames\n    const processFrameArray = frameArray => {\n      // Find the largest frame\n      let largestFrame = null;\n      for (const frame of frameArray) {\n        const {\n          w,\n          h\n        } = frame.frame;\n        const area = w * h;\n        if (!largestFrame || area > largestFrame.area) {\n          largestFrame = {\n            w,\n            h,\n            area\n          };\n        }\n      }\n\n      // Set scaleRatio property on each frame\n      const frameArr = frameArray.map(frame => {\n        const {\n          w,\n          h\n        } = frame.frame;\n        const area = w * h;\n        const scaleRatio = largestFrame ? area === largestFrame.area ? 1 : Math.sqrt(area / largestFrame.area) : 1;\n        return {\n          ...frame,\n          scaleRatio\n        };\n      });\n      return frameArr;\n    };\n\n    // Handle both array and record cases\n    if (Array.isArray(frames)) {\n      return processFrameArray(frames);\n    } else {\n      const result = {};\n      for (const key in frames) {\n        result[key] = processFrameArray(frames[key]);\n      }\n      return result;\n    }\n  }, []);\n\n  // for frame based JSON Hash sprite data\n  const parseFrames = React.useCallback(() => {\n    const sprites = {};\n    const data = spriteDataRef.current;\n    const delimiters = animationFramesRef.current;\n    if (data) {\n      if (delimiters && Array.isArray(data['frames'])) {\n        for (let i = 0; i < delimiters.length; i++) {\n          // we convert each named animation group into an array\n          sprites[delimiters[i]] = [];\n          for (const value of data['frames']) {\n            const frameData = value['frame'];\n            const sourceWidth = value['sourceSize']['w'];\n            const sourceHeight = value['sourceSize']['h'];\n            if (typeof value['filename'] === 'string' && value['filename'].toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n              sprites[delimiters[i]].push({\n                ...value,\n                frame: frameData,\n                sourceSize: {\n                  w: sourceWidth,\n                  h: sourceHeight\n                }\n              });\n            }\n          }\n        }\n        for (const frame in sprites) {\n          const scaleRatioData = calculateScaleRatio(sprites[frame]);\n          if (Array.isArray(scaleRatioData)) {\n            sprites[frame] = scaleRatioData;\n          }\n        }\n        return sprites;\n      } else if (delimiters && typeof data['frames'] === 'object') {\n        for (let i = 0; i < delimiters.length; i++) {\n          // we convert each named animation group into an array\n          sprites[delimiters[i]] = [];\n          for (const innerKey in data['frames']) {\n            const value = data['frames'][innerKey];\n            const frameData = value['frame'];\n            const sourceWidth = value['sourceSize']['w'];\n            const sourceHeight = value['sourceSize']['h'];\n            if (typeof innerKey === 'string' && innerKey.toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n              sprites[delimiters[i]].push({\n                ...value,\n                frame: frameData,\n                sourceSize: {\n                  w: sourceWidth,\n                  h: sourceHeight\n                }\n              });\n            }\n          }\n        }\n        for (const frame in sprites) {\n          const scaleRatioData = calculateScaleRatio(sprites[frame]);\n          if (Array.isArray(scaleRatioData)) {\n            sprites[frame] = scaleRatioData;\n          }\n        }\n        return sprites;\n      } else {\n        let spritesArr = [];\n        if (data != null && data.frames) {\n          if (Array.isArray(data.frames)) {\n            spritesArr = data.frames.map(frame => ({\n              ...frame,\n              x: frame.frame.x,\n              y: frame.frame.y,\n              w: frame.frame.w,\n              h: frame.frame.h\n            }));\n          } else {\n            spritesArr = Object.values(data.frames).flat().map(frame => ({\n              ...frame,\n              x: frame.frame.x,\n              y: frame.frame.y,\n              w: frame.frame.w,\n              h: frame.frame.h\n            }));\n          }\n        }\n        return calculateScaleRatio(spritesArr);\n      }\n    }\n    return [];\n  }, [calculateScaleRatio, spriteDataRef]);\n  const parseSpriteData = React.useCallback((json, _spriteTexture) => {\n    let aspect = new THREE.Vector3(1, 1, 1);\n    // sprite only case\n    if (json === null) {\n      if (_spriteTexture && numberOfFrames) {\n        //get size from texture\n        const width = _spriteTexture.image.width;\n        const height = _spriteTexture.image.height;\n        totalFrames.current = numberOfFrames;\n        const {\n          rows,\n          columns,\n          frameWidth,\n          frameHeight,\n          emptyFrames\n        } = getRowsAndColumns(_spriteTexture, numberOfFrames);\n        const nonJsonFrames = {\n          frames: [],\n          meta: {\n            version: '1.0',\n            size: {\n              w: width,\n              h: height\n            },\n            rows,\n            columns,\n            frameWidth,\n            frameHeight,\n            scale: '1'\n          }\n        };\n        for (let row = 0; row < rows; row++) {\n          for (let col = 0; col < columns; col++) {\n            const isExcluded = (emptyFrames !== null && emptyFrames !== void 0 ? emptyFrames : []).some(coord => coord.row === row && coord.col === col);\n            if (isExcluded) {\n              continue;\n            }\n            if (Array.isArray(nonJsonFrames.frames)) {\n              nonJsonFrames.frames.push({\n                frame: {\n                  x: col * frameWidth,\n                  y: row * frameHeight,\n                  w: frameWidth,\n                  h: frameHeight\n                },\n                scaleRatio: 1,\n                rotated: false,\n                trimmed: false,\n                spriteSourceSize: {\n                  x: 0,\n                  y: 0,\n                  w: frameWidth,\n                  h: frameHeight\n                },\n                sourceSize: {\n                  w: frameWidth,\n                  h: frameHeight\n                }\n              });\n            }\n          }\n        }\n        aspect = calculateAspectRatio(frameWidth, frameHeight, aspectFactor);\n        spriteDataRef.current = nonJsonFrames;\n      }\n\n      //scale ratio for standalone sprite\n      if (spriteDataRef.current && spriteDataRef.current.frames) {\n        spriteDataRef.current.frames = calculateScaleRatio(spriteDataRef.current.frames);\n      }\n    } else if (_spriteTexture) {\n      spriteDataRef.current = json;\n      spriteDataRef.current.frames = parseFrames();\n      totalFrames.current = Array.isArray(json.frames) ? json.frames.length : Object.keys(json.frames).length;\n      const {\n        w,\n        h\n      } = getFirstFrame(json.frames).sourceSize;\n      aspect = calculateAspectRatio(w, h, aspectFactor);\n    }\n    setSpriteData(spriteDataRef.current);\n    if ('encoding' in _spriteTexture) {\n      _spriteTexture.encoding = 3001; // sRGBEncoding\n    } else if ('colorSpace' in _spriteTexture) {\n      //@ts-ignore\n      _spriteTexture.colorSpace = THREE.SRGBColorSpace;\n    }\n    setSpriteTexture(_spriteTexture);\n    setSpriteObj({\n      spriteTexture: _spriteTexture,\n      spriteData: spriteDataRef.current,\n      aspect: aspect\n    });\n  }, [getRowsAndColumns, numberOfFrames, parseFrames, calculateAspectRatio, calculateScaleRatio]);\n\n  /**\r\n   *\r\n   */\n  const loadJsonAndTextureAndExecuteCallback = React.useCallback((jsonUrl, textureUrl, callback) => {\n    const jsonPromise = fetch(jsonUrl).then(response => response.json());\n    const texturePromise = new Promise(resolve => {\n      textureLoader.load(textureUrl, resolve);\n    });\n    Promise.all([jsonPromise, texturePromise]).then(response => {\n      callback(response[0], response[1]);\n    });\n  }, [textureLoader]);\n  const loadStandaloneSprite = React.useCallback(textureUrl => {\n    if (!textureUrl && !inputRef.current) {\n      throw new Error('Either textureUrl or input must be provided');\n    }\n    const validUrl = textureUrl !== null && textureUrl !== void 0 ? textureUrl : inputRef.current;\n    if (!validUrl) {\n      throw new Error('A valid texture URL must be provided');\n    }\n    textureLoader.load(validUrl, texture => parseSpriteData(null, texture));\n  }, [textureLoader, parseSpriteData]);\n  const loadJsonAndTexture = React.useCallback((textureUrl, jsonUrl) => {\n    if (jsonUrl && textureUrl) {\n      loadJsonAndTextureAndExecuteCallback(jsonUrl, textureUrl, parseSpriteData);\n    } else {\n      loadStandaloneSprite(textureUrl);\n    }\n  }, [loadJsonAndTextureAndExecuteCallback, loadStandaloneSprite, parseSpriteData]);\n  React.useLayoutEffect(() => {\n    if (jsonRef.current && inputRef.current) {\n      loadJsonAndTextureAndExecuteCallback(jsonRef.current, inputRef.current, parseSpriteData);\n    } else if (inputRef.current) {\n      // only load the texture, this is an image sprite only\n      loadStandaloneSprite();\n    }\n    const _inputRef = inputRef.current;\n    return () => {\n      if (_inputRef) {\n        useLoader.clear(TextureLoader, _inputRef);\n      }\n    };\n  }, [loadJsonAndTextureAndExecuteCallback, loadStandaloneSprite, parseSpriteData]);\n  React.useLayoutEffect(() => {\n    onLoad == null || onLoad(spriteTexture, spriteData !== null && spriteData !== void 0 ? spriteData : null);\n  }, [spriteTexture, spriteData, onLoad]);\n  return {\n    spriteObj,\n    loadJsonAndTexture\n  };\n}\nuseSpriteLoader.preload = url => useLoader.preload(TextureLoader, url);\nuseSpriteLoader.clear = input => useLoader.clear(TextureLoader, input);\nexport { checkIfFrameIsEmpty, getFirstFrame, useSpriteLoader };", "map": {"version": 3, "names": ["THREE", "TextureLoader", "useThree", "useLoader", "React", "useState", "getFirstFrame", "frames", "frameName", "Array", "isArray", "k", "Object", "keys", "checkIfFrameIsEmpty", "frameData", "i", "length", "useSpriteLoader", "input", "json", "animationNames", "numberOfFrames", "onLoad", "canvasRenderingContext2DSettings", "viewportRef", "useRef", "state", "viewport", "spriteDataRef", "totalFrames", "aspectFactor", "inputRef", "jsonRef", "animationFramesRef", "spriteData", "setSpriteData", "spriteTexture", "setSpriteTexture", "Texture", "textureLoader", "useMemo", "spriteObj", "setSpriteObj", "calculateAspectRatio", "useCallback", "width", "height", "factor", "adaptedHeight", "current", "aspect", "adapted<PERSON><PERSON>th", "scaleX", "scaleY", "currentMaxScale", "finalMaxScaleW", "Math", "min", "finalMaxScaleH", "Vector3", "getRowsAndColumns", "texture", "image", "canvas", "document", "createElement", "ctx", "getContext", "Error", "drawImage", "cols", "round", "sqrt", "rows", "frameWidth", "frameHeight", "emptyFrames", "row", "col", "frameIndex", "push", "getImageData", "data", "isEmpty", "columns", "calculateScaleRatio", "processFrameArray", "frameArray", "largestFrame", "frame", "w", "h", "area", "frameArr", "map", "scaleRatio", "result", "key", "parseFrames", "sprites", "delimiters", "value", "sourceWidth", "sourceHeight", "toLowerCase", "indexOf", "sourceSize", "scaleRatioData", "innerKey", "spritesArr", "x", "y", "values", "flat", "parseSpriteData", "_spriteTexture", "nonJsonFrames", "meta", "version", "size", "scale", "isExcluded", "some", "coord", "rotated", "trimmed", "spriteSourceSize", "encoding", "colorSpace", "SRGBColorSpace", "loadJsonAndTextureAndExecuteCallback", "jsonUrl", "textureUrl", "callback", "jsonPromise", "fetch", "then", "response", "texturePromise", "Promise", "resolve", "load", "all", "loadStandaloneSprite", "validUrl", "loadJsonAndTexture", "useLayoutEffect", "_inputRef", "clear", "preload", "url"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/useSpriteLoader.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { TextureLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport * as React from 'react';\nimport { useState } from 'react';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\n// utils\nconst getFirstFrame = (frames, frameName) => {\n  if (Array.isArray(frames)) {\n    return frames[0];\n  } else {\n    const k = frameName !== null && frameName !== void 0 ? frameName : Object.keys(frames)[0];\n    return frames[k][0];\n  }\n};\nconst checkIfFrameIsEmpty = frameData => {\n  for (let i = 3; i < frameData.length; i += 4) {\n    if (frameData[i] !== 0) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction useSpriteLoader(/** The URL of the sprite sheet. */\ninput, /** The JSON data of the sprite sheet. */\njson, /** The names of the animations in the sprite sheet. */\nanimationNames, /** The number of frames in the sprite sheet. */\nnumberOfFrames, /** A callback that is called when the sprite sheet is loaded. */\nonLoad, /** The settings to use when creating the 2D context. */\ncanvasRenderingContext2DSettings) {\n  const viewportRef = React.useRef(useThree(state => state.viewport));\n  const spriteDataRef = React.useRef(null);\n  const totalFrames = React.useRef(0);\n  const aspectFactor = 0.1;\n  const inputRef = React.useRef(input);\n  const jsonRef = React.useRef(json);\n  const animationFramesRef = React.useRef(animationNames);\n  const [spriteData, setSpriteData] = useState(null);\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const textureLoader = React.useMemo(() => new THREE.TextureLoader(), []);\n  const [spriteObj, setSpriteObj] = useState(null);\n  const calculateAspectRatio = React.useCallback((width, height, factor) => {\n    const adaptedHeight = height * (viewportRef.current.aspect > width / height ? viewportRef.current.width / width : viewportRef.current.height / height);\n    const adaptedWidth = width * (viewportRef.current.aspect > width / height ? viewportRef.current.width / width : viewportRef.current.height / height);\n    const scaleX = adaptedWidth * factor;\n    const scaleY = adaptedHeight * factor;\n    const currentMaxScale = 1;\n    // Calculate the maximum scale based on the aspect ratio and max scale limit\n    let finalMaxScaleW = Math.min(currentMaxScale, scaleX);\n    let finalMaxScaleH = Math.min(currentMaxScale, scaleY);\n\n    // Ensure that scaleX and scaleY do not exceed the max scale while maintaining aspect ratio\n    if (scaleX > currentMaxScale) {\n      finalMaxScaleW = currentMaxScale;\n      finalMaxScaleH = scaleY / scaleX * currentMaxScale;\n    }\n    return new THREE.Vector3(finalMaxScaleW, finalMaxScaleH, 1);\n  }, []);\n  const getRowsAndColumns = React.useCallback((texture, totalFrames) => {\n    if (texture.image) {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d', canvasRenderingContext2DSettings);\n      if (!ctx) {\n        throw new Error('Failed to get 2d context');\n      }\n      canvas.width = texture.image.width;\n      canvas.height = texture.image.height;\n      ctx.drawImage(texture.image, 0, 0);\n      const width = texture.image.width;\n      const height = texture.image.height;\n\n      // Calculate rows and columns based on the number of frames and image dimensions\n      const cols = Math.round(Math.sqrt(totalFrames * (width / height)));\n      const rows = Math.round(totalFrames / cols);\n      const frameWidth = width / cols;\n      const frameHeight = height / rows;\n      const emptyFrames = [];\n      for (let row = 0; row < rows; row++) {\n        for (let col = 0; col < cols; col++) {\n          const frameIndex = row * cols + col;\n          if (frameIndex >= totalFrames) {\n            emptyFrames.push({\n              row,\n              col\n            });\n            continue;\n          }\n          const frameData = ctx.getImageData(col * frameWidth, row * frameHeight, frameWidth, frameHeight).data;\n          const isEmpty = checkIfFrameIsEmpty(frameData);\n          if (isEmpty) {\n            emptyFrames.push({\n              row,\n              col\n            });\n          }\n        }\n      }\n      return {\n        rows,\n        columns: cols,\n        frameWidth,\n        frameHeight,\n        emptyFrames\n      };\n    } else {\n      return {\n        rows: 0,\n        columns: 0,\n        frameWidth: 0,\n        frameHeight: 0,\n        emptyFrames: []\n      };\n    }\n  }, [canvasRenderingContext2DSettings]);\n\n  // calculate scale ratio for the frames\n  const calculateScaleRatio = React.useCallback(frames => {\n    // Helper function to calculate scale ratio for an array of frames\n    const processFrameArray = frameArray => {\n      // Find the largest frame\n      let largestFrame = null;\n      for (const frame of frameArray) {\n        const {\n          w,\n          h\n        } = frame.frame;\n        const area = w * h;\n        if (!largestFrame || area > largestFrame.area) {\n          largestFrame = {\n            w,\n            h,\n            area\n          };\n        }\n      }\n\n      // Set scaleRatio property on each frame\n      const frameArr = frameArray.map(frame => {\n        const {\n          w,\n          h\n        } = frame.frame;\n        const area = w * h;\n        const scaleRatio = largestFrame ? area === largestFrame.area ? 1 : Math.sqrt(area / largestFrame.area) : 1;\n        return {\n          ...frame,\n          scaleRatio\n        };\n      });\n      return frameArr;\n    };\n\n    // Handle both array and record cases\n    if (Array.isArray(frames)) {\n      return processFrameArray(frames);\n    } else {\n      const result = {};\n      for (const key in frames) {\n        result[key] = processFrameArray(frames[key]);\n      }\n      return result;\n    }\n  }, []);\n\n  // for frame based JSON Hash sprite data\n  const parseFrames = React.useCallback(() => {\n    const sprites = {};\n    const data = spriteDataRef.current;\n    const delimiters = animationFramesRef.current;\n    if (data) {\n      if (delimiters && Array.isArray(data['frames'])) {\n        for (let i = 0; i < delimiters.length; i++) {\n          // we convert each named animation group into an array\n          sprites[delimiters[i]] = [];\n          for (const value of data['frames']) {\n            const frameData = value['frame'];\n            const sourceWidth = value['sourceSize']['w'];\n            const sourceHeight = value['sourceSize']['h'];\n            if (typeof value['filename'] === 'string' && value['filename'].toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n              sprites[delimiters[i]].push({\n                ...value,\n                frame: frameData,\n                sourceSize: {\n                  w: sourceWidth,\n                  h: sourceHeight\n                }\n              });\n            }\n          }\n        }\n        for (const frame in sprites) {\n          const scaleRatioData = calculateScaleRatio(sprites[frame]);\n          if (Array.isArray(scaleRatioData)) {\n            sprites[frame] = scaleRatioData;\n          }\n        }\n        return sprites;\n      } else if (delimiters && typeof data['frames'] === 'object') {\n        for (let i = 0; i < delimiters.length; i++) {\n          // we convert each named animation group into an array\n          sprites[delimiters[i]] = [];\n          for (const innerKey in data['frames']) {\n            const value = data['frames'][innerKey];\n            const frameData = value['frame'];\n            const sourceWidth = value['sourceSize']['w'];\n            const sourceHeight = value['sourceSize']['h'];\n            if (typeof innerKey === 'string' && innerKey.toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n              sprites[delimiters[i]].push({\n                ...value,\n                frame: frameData,\n                sourceSize: {\n                  w: sourceWidth,\n                  h: sourceHeight\n                }\n              });\n            }\n          }\n        }\n        for (const frame in sprites) {\n          const scaleRatioData = calculateScaleRatio(sprites[frame]);\n          if (Array.isArray(scaleRatioData)) {\n            sprites[frame] = scaleRatioData;\n          }\n        }\n        return sprites;\n      } else {\n        let spritesArr = [];\n        if (data != null && data.frames) {\n          if (Array.isArray(data.frames)) {\n            spritesArr = data.frames.map(frame => ({\n              ...frame,\n              x: frame.frame.x,\n              y: frame.frame.y,\n              w: frame.frame.w,\n              h: frame.frame.h\n            }));\n          } else {\n            spritesArr = Object.values(data.frames).flat().map(frame => ({\n              ...frame,\n              x: frame.frame.x,\n              y: frame.frame.y,\n              w: frame.frame.w,\n              h: frame.frame.h\n            }));\n          }\n        }\n        return calculateScaleRatio(spritesArr);\n      }\n    }\n    return [];\n  }, [calculateScaleRatio, spriteDataRef]);\n  const parseSpriteData = React.useCallback((json, _spriteTexture) => {\n    let aspect = new THREE.Vector3(1, 1, 1);\n    // sprite only case\n    if (json === null) {\n      if (_spriteTexture && numberOfFrames) {\n        //get size from texture\n        const width = _spriteTexture.image.width;\n        const height = _spriteTexture.image.height;\n        totalFrames.current = numberOfFrames;\n        const {\n          rows,\n          columns,\n          frameWidth,\n          frameHeight,\n          emptyFrames\n        } = getRowsAndColumns(_spriteTexture, numberOfFrames);\n        const nonJsonFrames = {\n          frames: [],\n          meta: {\n            version: '1.0',\n            size: {\n              w: width,\n              h: height\n            },\n            rows,\n            columns,\n            frameWidth,\n            frameHeight,\n            scale: '1'\n          }\n        };\n        for (let row = 0; row < rows; row++) {\n          for (let col = 0; col < columns; col++) {\n            const isExcluded = (emptyFrames !== null && emptyFrames !== void 0 ? emptyFrames : []).some(coord => coord.row === row && coord.col === col);\n            if (isExcluded) {\n              continue;\n            }\n            if (Array.isArray(nonJsonFrames.frames)) {\n              nonJsonFrames.frames.push({\n                frame: {\n                  x: col * frameWidth,\n                  y: row * frameHeight,\n                  w: frameWidth,\n                  h: frameHeight\n                },\n                scaleRatio: 1,\n                rotated: false,\n                trimmed: false,\n                spriteSourceSize: {\n                  x: 0,\n                  y: 0,\n                  w: frameWidth,\n                  h: frameHeight\n                },\n                sourceSize: {\n                  w: frameWidth,\n                  h: frameHeight\n                }\n              });\n            }\n          }\n        }\n        aspect = calculateAspectRatio(frameWidth, frameHeight, aspectFactor);\n        spriteDataRef.current = nonJsonFrames;\n      }\n\n      //scale ratio for standalone sprite\n      if (spriteDataRef.current && spriteDataRef.current.frames) {\n        spriteDataRef.current.frames = calculateScaleRatio(spriteDataRef.current.frames);\n      }\n    } else if (_spriteTexture) {\n      spriteDataRef.current = json;\n      spriteDataRef.current.frames = parseFrames();\n      totalFrames.current = Array.isArray(json.frames) ? json.frames.length : Object.keys(json.frames).length;\n      const {\n        w,\n        h\n      } = getFirstFrame(json.frames).sourceSize;\n      aspect = calculateAspectRatio(w, h, aspectFactor);\n    }\n    setSpriteData(spriteDataRef.current);\n    if ('encoding' in _spriteTexture) {\n      _spriteTexture.encoding = 3001; // sRGBEncoding\n    } else if ('colorSpace' in _spriteTexture) {\n      //@ts-ignore\n      _spriteTexture.colorSpace = THREE.SRGBColorSpace;\n    }\n    setSpriteTexture(_spriteTexture);\n    setSpriteObj({\n      spriteTexture: _spriteTexture,\n      spriteData: spriteDataRef.current,\n      aspect: aspect\n    });\n  }, [getRowsAndColumns, numberOfFrames, parseFrames, calculateAspectRatio, calculateScaleRatio]);\n\n  /**\r\n   *\r\n   */\n  const loadJsonAndTextureAndExecuteCallback = React.useCallback((jsonUrl, textureUrl, callback) => {\n    const jsonPromise = fetch(jsonUrl).then(response => response.json());\n    const texturePromise = new Promise(resolve => {\n      textureLoader.load(textureUrl, resolve);\n    });\n    Promise.all([jsonPromise, texturePromise]).then(response => {\n      callback(response[0], response[1]);\n    });\n  }, [textureLoader]);\n  const loadStandaloneSprite = React.useCallback(textureUrl => {\n    if (!textureUrl && !inputRef.current) {\n      throw new Error('Either textureUrl or input must be provided');\n    }\n    const validUrl = textureUrl !== null && textureUrl !== void 0 ? textureUrl : inputRef.current;\n    if (!validUrl) {\n      throw new Error('A valid texture URL must be provided');\n    }\n    textureLoader.load(validUrl, texture => parseSpriteData(null, texture));\n  }, [textureLoader, parseSpriteData]);\n  const loadJsonAndTexture = React.useCallback((textureUrl, jsonUrl) => {\n    if (jsonUrl && textureUrl) {\n      loadJsonAndTextureAndExecuteCallback(jsonUrl, textureUrl, parseSpriteData);\n    } else {\n      loadStandaloneSprite(textureUrl);\n    }\n  }, [loadJsonAndTextureAndExecuteCallback, loadStandaloneSprite, parseSpriteData]);\n  React.useLayoutEffect(() => {\n    if (jsonRef.current && inputRef.current) {\n      loadJsonAndTextureAndExecuteCallback(jsonRef.current, inputRef.current, parseSpriteData);\n    } else if (inputRef.current) {\n      // only load the texture, this is an image sprite only\n      loadStandaloneSprite();\n    }\n    const _inputRef = inputRef.current;\n    return () => {\n      if (_inputRef) {\n        useLoader.clear(TextureLoader, _inputRef);\n      }\n    };\n  }, [loadJsonAndTextureAndExecuteCallback, loadStandaloneSprite, parseSpriteData]);\n  React.useLayoutEffect(() => {\n    onLoad == null || onLoad(spriteTexture, spriteData !== null && spriteData !== void 0 ? spriteData : null);\n  }, [spriteTexture, spriteData, onLoad]);\n  return {\n    spriteObj,\n    loadJsonAndTexture\n  };\n}\nuseSpriteLoader.preload = url => useLoader.preload(TextureLoader, url);\nuseSpriteLoader.clear = input => useLoader.clear(TextureLoader, input);\n\nexport { checkIfFrameIsEmpty, getFirstFrame, useSpriteLoader };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,OAAO;AACrC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;;AAEhC;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC3C,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CAAC,CAAC,CAAC;EAClB,CAAC,MAAM;IACL,MAAMI,CAAC,GAAGH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGI,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC;IACzF,OAAOA,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;AACF,CAAC;AACD,MAAMG,mBAAmB,GAAGC,SAAS,IAAI;EACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC5C,IAAID,SAAS,CAACC,CAAC,CAAC,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASE,eAAeA,CAAC;AACzBC,KAAK,EAAE;AACPC,IAAI,EAAE;AACNC,cAAc,EAAE;AAChBC,cAAc,EAAE;AAChBC,MAAM,EAAE;AACRC,gCAAgC,EAAE;EAChC,MAAMC,WAAW,GAAGrB,KAAK,CAACsB,MAAM,CAACxB,QAAQ,CAACyB,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAAC;EACnE,MAAMC,aAAa,GAAGzB,KAAK,CAACsB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMI,WAAW,GAAG1B,KAAK,CAACsB,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMK,YAAY,GAAG,GAAG;EACxB,MAAMC,QAAQ,GAAG5B,KAAK,CAACsB,MAAM,CAACP,KAAK,CAAC;EACpC,MAAMc,OAAO,GAAG7B,KAAK,CAACsB,MAAM,CAACN,IAAI,CAAC;EAClC,MAAMc,kBAAkB,GAAG9B,KAAK,CAACsB,MAAM,CAACL,cAAc,CAAC;EACvD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,KAAK,CAACC,QAAQ,CAAC,IAAIL,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;EAC7E,MAAMC,aAAa,GAAGpC,KAAK,CAACqC,OAAO,CAAC,MAAM,IAAIzC,KAAK,CAACC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC;EACxE,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMuC,oBAAoB,GAAGxC,KAAK,CAACyC,WAAW,CAAC,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,KAAK;IACxE,MAAMC,aAAa,GAAGF,MAAM,IAAItB,WAAW,CAACyB,OAAO,CAACC,MAAM,GAAGL,KAAK,GAAGC,MAAM,GAAGtB,WAAW,CAACyB,OAAO,CAACJ,KAAK,GAAGA,KAAK,GAAGrB,WAAW,CAACyB,OAAO,CAACH,MAAM,GAAGA,MAAM,CAAC;IACtJ,MAAMK,YAAY,GAAGN,KAAK,IAAIrB,WAAW,CAACyB,OAAO,CAACC,MAAM,GAAGL,KAAK,GAAGC,MAAM,GAAGtB,WAAW,CAACyB,OAAO,CAACJ,KAAK,GAAGA,KAAK,GAAGrB,WAAW,CAACyB,OAAO,CAACH,MAAM,GAAGA,MAAM,CAAC;IACpJ,MAAMM,MAAM,GAAGD,YAAY,GAAGJ,MAAM;IACpC,MAAMM,MAAM,GAAGL,aAAa,GAAGD,MAAM;IACrC,MAAMO,eAAe,GAAG,CAAC;IACzB;IACA,IAAIC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACH,eAAe,EAAEF,MAAM,CAAC;IACtD,IAAIM,cAAc,GAAGF,IAAI,CAACC,GAAG,CAACH,eAAe,EAAED,MAAM,CAAC;;IAEtD;IACA,IAAID,MAAM,GAAGE,eAAe,EAAE;MAC5BC,cAAc,GAAGD,eAAe;MAChCI,cAAc,GAAGL,MAAM,GAAGD,MAAM,GAAGE,eAAe;IACpD;IACA,OAAO,IAAIvD,KAAK,CAAC4D,OAAO,CAACJ,cAAc,EAAEG,cAAc,EAAE,CAAC,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,iBAAiB,GAAGzD,KAAK,CAACyC,WAAW,CAAC,CAACiB,OAAO,EAAEhC,WAAW,KAAK;IACpE,IAAIgC,OAAO,CAACC,KAAK,EAAE;MACjB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,EAAE5C,gCAAgC,CAAC;MACrE,IAAI,CAAC2C,GAAG,EAAE;QACR,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACAL,MAAM,CAAClB,KAAK,GAAGgB,OAAO,CAACC,KAAK,CAACjB,KAAK;MAClCkB,MAAM,CAACjB,MAAM,GAAGe,OAAO,CAACC,KAAK,CAAChB,MAAM;MACpCoB,GAAG,CAACG,SAAS,CAACR,OAAO,CAACC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAClC,MAAMjB,KAAK,GAAGgB,OAAO,CAACC,KAAK,CAACjB,KAAK;MACjC,MAAMC,MAAM,GAAGe,OAAO,CAACC,KAAK,CAAChB,MAAM;;MAEnC;MACA,MAAMwB,IAAI,GAAGd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACgB,IAAI,CAAC3C,WAAW,IAAIgB,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC;MAClE,MAAM2B,IAAI,GAAGjB,IAAI,CAACe,KAAK,CAAC1C,WAAW,GAAGyC,IAAI,CAAC;MAC3C,MAAMI,UAAU,GAAG7B,KAAK,GAAGyB,IAAI;MAC/B,MAAMK,WAAW,GAAG7B,MAAM,GAAG2B,IAAI;MACjC,MAAMG,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,IAAI,EAAEI,GAAG,EAAE,EAAE;QACnC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,IAAI,EAAEQ,GAAG,EAAE,EAAE;UACnC,MAAMC,UAAU,GAAGF,GAAG,GAAGP,IAAI,GAAGQ,GAAG;UACnC,IAAIC,UAAU,IAAIlD,WAAW,EAAE;YAC7B+C,WAAW,CAACI,IAAI,CAAC;cACfH,GAAG;cACHC;YACF,CAAC,CAAC;YACF;UACF;UACA,MAAMhE,SAAS,GAAGoD,GAAG,CAACe,YAAY,CAACH,GAAG,GAAGJ,UAAU,EAAEG,GAAG,GAAGF,WAAW,EAAED,UAAU,EAAEC,WAAW,CAAC,CAACO,IAAI;UACrG,MAAMC,OAAO,GAAGtE,mBAAmB,CAACC,SAAS,CAAC;UAC9C,IAAIqE,OAAO,EAAE;YACXP,WAAW,CAACI,IAAI,CAAC;cACfH,GAAG;cACHC;YACF,CAAC,CAAC;UACJ;QACF;MACF;MACA,OAAO;QACLL,IAAI;QACJW,OAAO,EAAEd,IAAI;QACbI,UAAU;QACVC,WAAW;QACXC;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLH,IAAI,EAAE,CAAC;QACPW,OAAO,EAAE,CAAC;QACVV,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC,EAAE,CAACrD,gCAAgC,CAAC,CAAC;;EAEtC;EACA,MAAM8D,mBAAmB,GAAGlF,KAAK,CAACyC,WAAW,CAACtC,MAAM,IAAI;IACtD;IACA,MAAMgF,iBAAiB,GAAGC,UAAU,IAAI;MACtC;MACA,IAAIC,YAAY,GAAG,IAAI;MACvB,KAAK,MAAMC,KAAK,IAAIF,UAAU,EAAE;QAC9B,MAAM;UACJG,CAAC;UACDC;QACF,CAAC,GAAGF,KAAK,CAACA,KAAK;QACf,MAAMG,IAAI,GAAGF,CAAC,GAAGC,CAAC;QAClB,IAAI,CAACH,YAAY,IAAII,IAAI,GAAGJ,YAAY,CAACI,IAAI,EAAE;UAC7CJ,YAAY,GAAG;YACbE,CAAC;YACDC,CAAC;YACDC;UACF,CAAC;QACH;MACF;;MAEA;MACA,MAAMC,QAAQ,GAAGN,UAAU,CAACO,GAAG,CAACL,KAAK,IAAI;QACvC,MAAM;UACJC,CAAC;UACDC;QACF,CAAC,GAAGF,KAAK,CAACA,KAAK;QACf,MAAMG,IAAI,GAAGF,CAAC,GAAGC,CAAC;QAClB,MAAMI,UAAU,GAAGP,YAAY,GAAGI,IAAI,KAAKJ,YAAY,CAACI,IAAI,GAAG,CAAC,GAAGpC,IAAI,CAACgB,IAAI,CAACoB,IAAI,GAAGJ,YAAY,CAACI,IAAI,CAAC,GAAG,CAAC;QAC1G,OAAO;UACL,GAAGH,KAAK;UACRM;QACF,CAAC;MACH,CAAC,CAAC;MACF,OAAOF,QAAQ;IACjB,CAAC;;IAED;IACA,IAAIrF,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;MACzB,OAAOgF,iBAAiB,CAAChF,MAAM,CAAC;IAClC,CAAC,MAAM;MACL,MAAM0F,MAAM,GAAG,CAAC,CAAC;MACjB,KAAK,MAAMC,GAAG,IAAI3F,MAAM,EAAE;QACxB0F,MAAM,CAACC,GAAG,CAAC,GAAGX,iBAAiB,CAAChF,MAAM,CAAC2F,GAAG,CAAC,CAAC;MAC9C;MACA,OAAOD,MAAM;IACf;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,WAAW,GAAG/F,KAAK,CAACyC,WAAW,CAAC,MAAM;IAC1C,MAAMuD,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMjB,IAAI,GAAGtD,aAAa,CAACqB,OAAO;IAClC,MAAMmD,UAAU,GAAGnE,kBAAkB,CAACgB,OAAO;IAC7C,IAAIiC,IAAI,EAAE;MACR,IAAIkB,UAAU,IAAI5F,KAAK,CAACC,OAAO,CAACyE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;QAC/C,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqF,UAAU,CAACpF,MAAM,EAAED,CAAC,EAAE,EAAE;UAC1C;UACAoF,OAAO,CAACC,UAAU,CAACrF,CAAC,CAAC,CAAC,GAAG,EAAE;UAC3B,KAAK,MAAMsF,KAAK,IAAInB,IAAI,CAAC,QAAQ,CAAC,EAAE;YAClC,MAAMpE,SAAS,GAAGuF,KAAK,CAAC,OAAO,CAAC;YAChC,MAAMC,WAAW,GAAGD,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;YAC5C,MAAME,YAAY,GAAGF,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;YAC7C,IAAI,OAAOA,KAAK,CAAC,UAAU,CAAC,KAAK,QAAQ,IAAIA,KAAK,CAAC,UAAU,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAACL,UAAU,CAACrF,CAAC,CAAC,CAACyF,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;cACxHL,OAAO,CAACC,UAAU,CAACrF,CAAC,CAAC,CAAC,CAACiE,IAAI,CAAC;gBAC1B,GAAGqB,KAAK;gBACRZ,KAAK,EAAE3E,SAAS;gBAChB4F,UAAU,EAAE;kBACVhB,CAAC,EAAEY,WAAW;kBACdX,CAAC,EAAEY;gBACL;cACF,CAAC,CAAC;YACJ;UACF;QACF;QACA,KAAK,MAAMd,KAAK,IAAIU,OAAO,EAAE;UAC3B,MAAMQ,cAAc,GAAGtB,mBAAmB,CAACc,OAAO,CAACV,KAAK,CAAC,CAAC;UAC1D,IAAIjF,KAAK,CAACC,OAAO,CAACkG,cAAc,CAAC,EAAE;YACjCR,OAAO,CAACV,KAAK,CAAC,GAAGkB,cAAc;UACjC;QACF;QACA,OAAOR,OAAO;MAChB,CAAC,MAAM,IAAIC,UAAU,IAAI,OAAOlB,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAC3D,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqF,UAAU,CAACpF,MAAM,EAAED,CAAC,EAAE,EAAE;UAC1C;UACAoF,OAAO,CAACC,UAAU,CAACrF,CAAC,CAAC,CAAC,GAAG,EAAE;UAC3B,KAAK,MAAM6F,QAAQ,IAAI1B,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrC,MAAMmB,KAAK,GAAGnB,IAAI,CAAC,QAAQ,CAAC,CAAC0B,QAAQ,CAAC;YACtC,MAAM9F,SAAS,GAAGuF,KAAK,CAAC,OAAO,CAAC;YAChC,MAAMC,WAAW,GAAGD,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;YAC5C,MAAME,YAAY,GAAGF,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;YAC7C,IAAI,OAAOO,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACJ,WAAW,CAAC,CAAC,CAACC,OAAO,CAACL,UAAU,CAACrF,CAAC,CAAC,CAACyF,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;cACtGL,OAAO,CAACC,UAAU,CAACrF,CAAC,CAAC,CAAC,CAACiE,IAAI,CAAC;gBAC1B,GAAGqB,KAAK;gBACRZ,KAAK,EAAE3E,SAAS;gBAChB4F,UAAU,EAAE;kBACVhB,CAAC,EAAEY,WAAW;kBACdX,CAAC,EAAEY;gBACL;cACF,CAAC,CAAC;YACJ;UACF;QACF;QACA,KAAK,MAAMd,KAAK,IAAIU,OAAO,EAAE;UAC3B,MAAMQ,cAAc,GAAGtB,mBAAmB,CAACc,OAAO,CAACV,KAAK,CAAC,CAAC;UAC1D,IAAIjF,KAAK,CAACC,OAAO,CAACkG,cAAc,CAAC,EAAE;YACjCR,OAAO,CAACV,KAAK,CAAC,GAAGkB,cAAc;UACjC;QACF;QACA,OAAOR,OAAO;MAChB,CAAC,MAAM;QACL,IAAIU,UAAU,GAAG,EAAE;QACnB,IAAI3B,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC5E,MAAM,EAAE;UAC/B,IAAIE,KAAK,CAACC,OAAO,CAACyE,IAAI,CAAC5E,MAAM,CAAC,EAAE;YAC9BuG,UAAU,GAAG3B,IAAI,CAAC5E,MAAM,CAACwF,GAAG,CAACL,KAAK,KAAK;cACrC,GAAGA,KAAK;cACRqB,CAAC,EAAErB,KAAK,CAACA,KAAK,CAACqB,CAAC;cAChBC,CAAC,EAAEtB,KAAK,CAACA,KAAK,CAACsB,CAAC;cAChBrB,CAAC,EAAED,KAAK,CAACA,KAAK,CAACC,CAAC;cAChBC,CAAC,EAAEF,KAAK,CAACA,KAAK,CAACE;YACjB,CAAC,CAAC,CAAC;UACL,CAAC,MAAM;YACLkB,UAAU,GAAGlG,MAAM,CAACqG,MAAM,CAAC9B,IAAI,CAAC5E,MAAM,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAACnB,GAAG,CAACL,KAAK,KAAK;cAC3D,GAAGA,KAAK;cACRqB,CAAC,EAAErB,KAAK,CAACA,KAAK,CAACqB,CAAC;cAChBC,CAAC,EAAEtB,KAAK,CAACA,KAAK,CAACsB,CAAC;cAChBrB,CAAC,EAAED,KAAK,CAACA,KAAK,CAACC,CAAC;cAChBC,CAAC,EAAEF,KAAK,CAACA,KAAK,CAACE;YACjB,CAAC,CAAC,CAAC;UACL;QACF;QACA,OAAON,mBAAmB,CAACwB,UAAU,CAAC;MACxC;IACF;IACA,OAAO,EAAE;EACX,CAAC,EAAE,CAACxB,mBAAmB,EAAEzD,aAAa,CAAC,CAAC;EACxC,MAAMsF,eAAe,GAAG/G,KAAK,CAACyC,WAAW,CAAC,CAACzB,IAAI,EAAEgG,cAAc,KAAK;IAClE,IAAIjE,MAAM,GAAG,IAAInD,KAAK,CAAC4D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACA,IAAIxC,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIgG,cAAc,IAAI9F,cAAc,EAAE;QACpC;QACA,MAAMwB,KAAK,GAAGsE,cAAc,CAACrD,KAAK,CAACjB,KAAK;QACxC,MAAMC,MAAM,GAAGqE,cAAc,CAACrD,KAAK,CAAChB,MAAM;QAC1CjB,WAAW,CAACoB,OAAO,GAAG5B,cAAc;QACpC,MAAM;UACJoD,IAAI;UACJW,OAAO;UACPV,UAAU;UACVC,WAAW;UACXC;QACF,CAAC,GAAGhB,iBAAiB,CAACuD,cAAc,EAAE9F,cAAc,CAAC;QACrD,MAAM+F,aAAa,GAAG;UACpB9G,MAAM,EAAE,EAAE;UACV+G,IAAI,EAAE;YACJC,OAAO,EAAE,KAAK;YACdC,IAAI,EAAE;cACJ7B,CAAC,EAAE7C,KAAK;cACR8C,CAAC,EAAE7C;YACL,CAAC;YACD2B,IAAI;YACJW,OAAO;YACPV,UAAU;YACVC,WAAW;YACX6C,KAAK,EAAE;UACT;QACF,CAAC;QACD,KAAK,IAAI3C,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,IAAI,EAAEI,GAAG,EAAE,EAAE;UACnC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGM,OAAO,EAAEN,GAAG,EAAE,EAAE;YACtC,MAAM2C,UAAU,GAAG,CAAC7C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,EAAE,EAAE8C,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC9C,GAAG,KAAKA,GAAG,IAAI8C,KAAK,CAAC7C,GAAG,KAAKA,GAAG,CAAC;YAC5I,IAAI2C,UAAU,EAAE;cACd;YACF;YACA,IAAIjH,KAAK,CAACC,OAAO,CAAC2G,aAAa,CAAC9G,MAAM,CAAC,EAAE;cACvC8G,aAAa,CAAC9G,MAAM,CAAC0E,IAAI,CAAC;gBACxBS,KAAK,EAAE;kBACLqB,CAAC,EAAEhC,GAAG,GAAGJ,UAAU;kBACnBqC,CAAC,EAAElC,GAAG,GAAGF,WAAW;kBACpBe,CAAC,EAAEhB,UAAU;kBACbiB,CAAC,EAAEhB;gBACL,CAAC;gBACDoB,UAAU,EAAE,CAAC;gBACb6B,OAAO,EAAE,KAAK;gBACdC,OAAO,EAAE,KAAK;gBACdC,gBAAgB,EAAE;kBAChBhB,CAAC,EAAE,CAAC;kBACJC,CAAC,EAAE,CAAC;kBACJrB,CAAC,EAAEhB,UAAU;kBACbiB,CAAC,EAAEhB;gBACL,CAAC;gBACD+B,UAAU,EAAE;kBACVhB,CAAC,EAAEhB,UAAU;kBACbiB,CAAC,EAAEhB;gBACL;cACF,CAAC,CAAC;YACJ;UACF;QACF;QACAzB,MAAM,GAAGP,oBAAoB,CAAC+B,UAAU,EAAEC,WAAW,EAAE7C,YAAY,CAAC;QACpEF,aAAa,CAACqB,OAAO,GAAGmE,aAAa;MACvC;;MAEA;MACA,IAAIxF,aAAa,CAACqB,OAAO,IAAIrB,aAAa,CAACqB,OAAO,CAAC3C,MAAM,EAAE;QACzDsB,aAAa,CAACqB,OAAO,CAAC3C,MAAM,GAAG+E,mBAAmB,CAACzD,aAAa,CAACqB,OAAO,CAAC3C,MAAM,CAAC;MAClF;IACF,CAAC,MAAM,IAAI6G,cAAc,EAAE;MACzBvF,aAAa,CAACqB,OAAO,GAAG9B,IAAI;MAC5BS,aAAa,CAACqB,OAAO,CAAC3C,MAAM,GAAG4F,WAAW,CAAC,CAAC;MAC5CrE,WAAW,CAACoB,OAAO,GAAGzC,KAAK,CAACC,OAAO,CAACU,IAAI,CAACb,MAAM,CAAC,GAAGa,IAAI,CAACb,MAAM,CAACU,MAAM,GAAGL,MAAM,CAACC,IAAI,CAACO,IAAI,CAACb,MAAM,CAAC,CAACU,MAAM;MACvG,MAAM;QACJ0E,CAAC;QACDC;MACF,CAAC,GAAGtF,aAAa,CAACc,IAAI,CAACb,MAAM,CAAC,CAACoG,UAAU;MACzCxD,MAAM,GAAGP,oBAAoB,CAAC+C,CAAC,EAAEC,CAAC,EAAE7D,YAAY,CAAC;IACnD;IACAK,aAAa,CAACP,aAAa,CAACqB,OAAO,CAAC;IACpC,IAAI,UAAU,IAAIkE,cAAc,EAAE;MAChCA,cAAc,CAACY,QAAQ,GAAG,IAAI,CAAC,CAAC;IAClC,CAAC,MAAM,IAAI,YAAY,IAAIZ,cAAc,EAAE;MACzC;MACAA,cAAc,CAACa,UAAU,GAAGjI,KAAK,CAACkI,cAAc;IAClD;IACA5F,gBAAgB,CAAC8E,cAAc,CAAC;IAChCzE,YAAY,CAAC;MACXN,aAAa,EAAE+E,cAAc;MAC7BjF,UAAU,EAAEN,aAAa,CAACqB,OAAO;MACjCC,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACU,iBAAiB,EAAEvC,cAAc,EAAE6E,WAAW,EAAEvD,oBAAoB,EAAE0C,mBAAmB,CAAC,CAAC;;EAE/F;AACF;AACA;EACE,MAAM6C,oCAAoC,GAAG/H,KAAK,CAACyC,WAAW,CAAC,CAACuF,OAAO,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IAChG,MAAMC,WAAW,GAAGC,KAAK,CAACJ,OAAO,CAAC,CAACK,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACtH,IAAI,CAAC,CAAC,CAAC;IACpE,MAAMuH,cAAc,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC5CrG,aAAa,CAACsG,IAAI,CAACT,UAAU,EAAEQ,OAAO,CAAC;IACzC,CAAC,CAAC;IACFD,OAAO,CAACG,GAAG,CAAC,CAACR,WAAW,EAAEI,cAAc,CAAC,CAAC,CAACF,IAAI,CAACC,QAAQ,IAAI;MAC1DJ,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClG,aAAa,CAAC,CAAC;EACnB,MAAMwG,oBAAoB,GAAG5I,KAAK,CAACyC,WAAW,CAACwF,UAAU,IAAI;IAC3D,IAAI,CAACA,UAAU,IAAI,CAACrG,QAAQ,CAACkB,OAAO,EAAE;MACpC,MAAM,IAAImB,KAAK,CAAC,6CAA6C,CAAC;IAChE;IACA,MAAM4E,QAAQ,GAAGZ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGrG,QAAQ,CAACkB,OAAO;IAC7F,IAAI,CAAC+F,QAAQ,EAAE;MACb,MAAM,IAAI5E,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACA7B,aAAa,CAACsG,IAAI,CAACG,QAAQ,EAAEnF,OAAO,IAAIqD,eAAe,CAAC,IAAI,EAAErD,OAAO,CAAC,CAAC;EACzE,CAAC,EAAE,CAACtB,aAAa,EAAE2E,eAAe,CAAC,CAAC;EACpC,MAAM+B,kBAAkB,GAAG9I,KAAK,CAACyC,WAAW,CAAC,CAACwF,UAAU,EAAED,OAAO,KAAK;IACpE,IAAIA,OAAO,IAAIC,UAAU,EAAE;MACzBF,oCAAoC,CAACC,OAAO,EAAEC,UAAU,EAAElB,eAAe,CAAC;IAC5E,CAAC,MAAM;MACL6B,oBAAoB,CAACX,UAAU,CAAC;IAClC;EACF,CAAC,EAAE,CAACF,oCAAoC,EAAEa,oBAAoB,EAAE7B,eAAe,CAAC,CAAC;EACjF/G,KAAK,CAAC+I,eAAe,CAAC,MAAM;IAC1B,IAAIlH,OAAO,CAACiB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,EAAE;MACvCiF,oCAAoC,CAAClG,OAAO,CAACiB,OAAO,EAAElB,QAAQ,CAACkB,OAAO,EAAEiE,eAAe,CAAC;IAC1F,CAAC,MAAM,IAAInF,QAAQ,CAACkB,OAAO,EAAE;MAC3B;MACA8F,oBAAoB,CAAC,CAAC;IACxB;IACA,MAAMI,SAAS,GAAGpH,QAAQ,CAACkB,OAAO;IAClC,OAAO,MAAM;MACX,IAAIkG,SAAS,EAAE;QACbjJ,SAAS,CAACkJ,KAAK,CAACpJ,aAAa,EAAEmJ,SAAS,CAAC;MAC3C;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,oCAAoC,EAAEa,oBAAoB,EAAE7B,eAAe,CAAC,CAAC;EACjF/G,KAAK,CAAC+I,eAAe,CAAC,MAAM;IAC1B5H,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACc,aAAa,EAAEF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,IAAI,CAAC;EAC3G,CAAC,EAAE,CAACE,aAAa,EAAEF,UAAU,EAAEZ,MAAM,CAAC,CAAC;EACvC,OAAO;IACLmB,SAAS;IACTwG;EACF,CAAC;AACH;AACAhI,eAAe,CAACoI,OAAO,GAAGC,GAAG,IAAIpJ,SAAS,CAACmJ,OAAO,CAACrJ,aAAa,EAAEsJ,GAAG,CAAC;AACtErI,eAAe,CAACmI,KAAK,GAAGlI,KAAK,IAAIhB,SAAS,CAACkJ,KAAK,CAACpJ,aAAa,EAAEkB,KAAK,CAAC;AAEtE,SAASL,mBAAmB,EAAER,aAAa,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}