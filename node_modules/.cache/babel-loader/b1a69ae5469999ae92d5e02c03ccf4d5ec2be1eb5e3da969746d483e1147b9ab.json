{"ast": null, "code": "import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectTris } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast(bvh, root, side, ray, intersects, near, far) {\n  BufferStack.setBuffer(bvh._roots[root]);\n  _raycast(0, bvh, side, ray, intersects, near, far);\n  BufferStack.clearBuffer();\n}\nfunction _raycast(nodeIndex32, bvh, side, ray, intersects, near, far) {\n  const {\n    float32Array,\n    uint16Array,\n    uint32Array\n  } = BufferStack;\n  const nodeIndex16 = nodeIndex32 * 2;\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n    intersectTris(bvh, side, ray, offset, count, intersects, near, far);\n  } else {\n    const leftIndex = LEFT_NODE(nodeIndex32);\n    if (intersectRay(leftIndex, float32Array, ray, near, far)) {\n      _raycast(leftIndex, bvh, side, ray, intersects, near, far);\n    }\n    const rightIndex = RIGHT_NODE(nodeIndex32, uint32Array);\n    if (intersectRay(rightIndex, float32Array, ray, near, far)) {\n      _raycast(rightIndex, bvh, side, ray, intersects, near, far);\n    }\n  }\n}\nexport { raycast };", "map": {"version": 3, "names": ["intersectRay", "IS_LEAF", "OFFSET", "COUNT", "LEFT_NODE", "RIGHT_NODE", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>", "intersectTris", "raycast", "bvh", "root", "side", "ray", "intersects", "near", "far", "<PERSON><PERSON><PERSON><PERSON>", "_roots", "_raycast", "<PERSON><PERSON><PERSON><PERSON>", "nodeIndex32", "float32Array", "uint16Array", "uint32Array", "nodeIndex16", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "leftIndex", "rightIndex"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/three-mesh-bvh/src/core/cast/raycast.generated.js"], "sourcesContent": ["import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectTris } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast( bvh, root, side, ray, intersects, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\t_raycast( 0, bvh, side, ray, intersects, near, far );\n\tBufferStack.clearBuffer();\n\n}\n\nfunction _raycast( nodeIndex32, bvh, side, ray, intersects, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tconst nodeIndex16 = nodeIndex32 * 2;\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\n\t\tintersectTris( bvh, side, ray, offset, count, intersects, near, far );\n\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( leftIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( rightIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycast };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,QAAQ,6BAA6B;AAC3F,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,aAAa,QAAQ,sCAAsC;AACpE,OAAO,+CAA+C;;AAEtD;AACA;AACA;;AAEA,SAASC,OAAOA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAE/DT,WAAW,CAACU,SAAS,CAAEP,GAAG,CAACQ,MAAM,CAAEP,IAAI,CAAG,CAAC;EAC3CQ,QAAQ,CAAE,CAAC,EAAET,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAI,CAAC;EACpDT,WAAW,CAACa,WAAW,CAAC,CAAC;AAE1B;AAEA,SAASD,QAAQA,CAAEE,WAAW,EAAEX,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEvE,MAAM;IAAEM,YAAY;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGjB,WAAW;EAC9D,MAAMkB,WAAW,GAAGJ,WAAW,GAAG,CAAC;EACnC,MAAMK,MAAM,GAAGxB,OAAO,CAAEuB,WAAW,EAAEF,WAAY,CAAC;EAClD,IAAKG,MAAM,EAAG;IAEb,MAAMC,MAAM,GAAGxB,MAAM,CAAEkB,WAAW,EAAEG,WAAY,CAAC;IACjD,MAAMI,KAAK,GAAGxB,KAAK,CAAEqB,WAAW,EAAEF,WAAY,CAAC;IAG/Cf,aAAa,CAAEE,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEc,MAAM,EAAEC,KAAK,EAAEd,UAAU,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAGtE,CAAC,MAAM;IAEN,MAAMa,SAAS,GAAGxB,SAAS,CAAEgB,WAAY,CAAC;IAC1C,IAAKpB,YAAY,CAAE4B,SAAS,EAAEP,YAAY,EAAET,GAAG,EAAEE,IAAI,EAAEC,GAAI,CAAC,EAAG;MAE9DG,QAAQ,CAAEU,SAAS,EAAEnB,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAI,CAAC;IAE7D;IAEA,MAAMc,UAAU,GAAGxB,UAAU,CAAEe,WAAW,EAAEG,WAAY,CAAC;IACzD,IAAKvB,YAAY,CAAE6B,UAAU,EAAER,YAAY,EAAET,GAAG,EAAEE,IAAI,EAAEC,GAAI,CAAC,EAAG;MAE/DG,QAAQ,CAAEW,UAAU,EAAEpB,GAAG,EAAEE,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAI,CAAC;IAE9D;EAED;AAED;AAEA,SAASP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}