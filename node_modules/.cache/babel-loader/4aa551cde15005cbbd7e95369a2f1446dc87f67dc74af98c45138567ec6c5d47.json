{"ast": null, "code": "async function AmmoPhysics() {\n  if (\"Ammo\" in window === false) {\n    console.error(\"AmmoPhysics: Couldn't find Ammo.js\");\n    return;\n  }\n  const AmmoLib = await Ammo();\n  const frameRate = 60;\n  const collisionConfiguration = new AmmoLib.btDefaultCollisionConfiguration();\n  const dispatcher = new AmmoLib.btCollisionDispatcher(collisionConfiguration);\n  const broadphase = new AmmoLib.btDbvtBroadphase();\n  const solver = new AmmoLib.btSequentialImpulseConstraintSolver();\n  const world = new AmmoLib.btDiscreteDynamicsWorld(dispatcher, broadphase, solver, collisionConfiguration);\n  world.setGravity(new AmmoLib.btVector3(0, -9.8, 0));\n  const worldTransform = new AmmoLib.btTransform();\n  function getShape(geometry) {\n    const parameters = geometry.parameters;\n    if (geometry.type === \"BoxGeometry\") {\n      const sx = parameters.width !== void 0 ? parameters.width / 2 : 0.5;\n      const sy = parameters.height !== void 0 ? parameters.height / 2 : 0.5;\n      const sz = parameters.depth !== void 0 ? parameters.depth / 2 : 0.5;\n      const shape = new AmmoLib.btBoxShape(new AmmoLib.btVector3(sx, sy, sz));\n      shape.setMargin(0.05);\n      return shape;\n    } else if (geometry.type === \"SphereGeometry\" || geometry.type === \"IcosahedronGeometry\") {\n      const radius = parameters.radius !== void 0 ? parameters.radius : 1;\n      const shape = new AmmoLib.btSphereShape(radius);\n      shape.setMargin(0.05);\n      return shape;\n    }\n    return null;\n  }\n  const meshes = [];\n  const meshMap = /* @__PURE__ */new WeakMap();\n  function addMesh(mesh, mass = 0) {\n    const shape = getShape(mesh.geometry);\n    if (shape !== null) {\n      if (mesh.isInstancedMesh) {\n        handleInstancedMesh(mesh, mass, shape);\n      } else if (mesh.isMesh) {\n        handleMesh(mesh, mass, shape);\n      }\n    }\n  }\n  function handleMesh(mesh, mass, shape) {\n    const position = mesh.position;\n    const quaternion = mesh.quaternion;\n    const transform = new AmmoLib.btTransform();\n    transform.setIdentity();\n    transform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z));\n    transform.setRotation(new AmmoLib.btQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w));\n    const motionState = new AmmoLib.btDefaultMotionState(transform);\n    const localInertia = new AmmoLib.btVector3(0, 0, 0);\n    shape.calculateLocalInertia(mass, localInertia);\n    const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia);\n    const body = new AmmoLib.btRigidBody(rbInfo);\n    world.addRigidBody(body);\n    if (mass > 0) {\n      meshes.push(mesh);\n      meshMap.set(mesh, body);\n    }\n  }\n  function handleInstancedMesh(mesh, mass, shape) {\n    const array = mesh.instanceMatrix.array;\n    const bodies = [];\n    for (let i = 0; i < mesh.count; i++) {\n      const index = i * 16;\n      const transform = new AmmoLib.btTransform();\n      transform.setFromOpenGLMatrix(array.slice(index, index + 16));\n      const motionState = new AmmoLib.btDefaultMotionState(transform);\n      const localInertia = new AmmoLib.btVector3(0, 0, 0);\n      shape.calculateLocalInertia(mass, localInertia);\n      const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia);\n      const body = new AmmoLib.btRigidBody(rbInfo);\n      world.addRigidBody(body);\n      bodies.push(body);\n    }\n    if (mass > 0) {\n      mesh.instanceMatrix.setUsage(35048);\n      meshes.push(mesh);\n      meshMap.set(mesh, bodies);\n    }\n  }\n  function setMeshPosition(mesh, position, index = 0) {\n    if (mesh.isInstancedMesh) {\n      const bodies = meshMap.get(mesh);\n      const body = bodies[index];\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0));\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0));\n      worldTransform.setIdentity();\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z));\n      body.setWorldTransform(worldTransform);\n    } else if (mesh.isMesh) {\n      const body = meshMap.get(mesh);\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0));\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0));\n      worldTransform.setIdentity();\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z));\n      body.setWorldTransform(worldTransform);\n    }\n  }\n  let lastTime = 0;\n  function step() {\n    const time = performance.now();\n    if (lastTime > 0) {\n      const delta = (time - lastTime) / 1e3;\n      world.stepSimulation(delta, 10);\n    }\n    lastTime = time;\n    for (let i = 0, l = meshes.length; i < l; i++) {\n      const mesh = meshes[i];\n      if (mesh.isInstancedMesh) {\n        const array = mesh.instanceMatrix.array;\n        const bodies = meshMap.get(mesh);\n        for (let j = 0; j < bodies.length; j++) {\n          const body = bodies[j];\n          const motionState = body.getMotionState();\n          motionState.getWorldTransform(worldTransform);\n          const position = worldTransform.getOrigin();\n          const quaternion = worldTransform.getRotation();\n          compose(position, quaternion, array, j * 16);\n        }\n        mesh.instanceMatrix.needsUpdate = true;\n      } else if (mesh.isMesh) {\n        const body = meshMap.get(mesh);\n        const motionState = body.getMotionState();\n        motionState.getWorldTransform(worldTransform);\n        const position = worldTransform.getOrigin();\n        const quaternion = worldTransform.getRotation();\n        mesh.position.set(position.x(), position.y(), position.z());\n        mesh.quaternion.set(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w());\n      }\n    }\n  }\n  setInterval(step, 1e3 / frameRate);\n  return {\n    addMesh,\n    setMeshPosition\n    // addCompoundMesh\n  };\n}\nfunction compose(position, quaternion, array, index) {\n  const x = quaternion.x(),\n    y = quaternion.y(),\n    z = quaternion.z(),\n    w = quaternion.w();\n  const x2 = x + x,\n    y2 = y + y,\n    z2 = z + z;\n  const xx = x * x2,\n    xy = x * y2,\n    xz = x * z2;\n  const yy = y * y2,\n    yz = y * z2,\n    zz = z * z2;\n  const wx = w * x2,\n    wy = w * y2,\n    wz = w * z2;\n  array[index + 0] = 1 - (yy + zz);\n  array[index + 1] = xy + wz;\n  array[index + 2] = xz - wy;\n  array[index + 3] = 0;\n  array[index + 4] = xy - wz;\n  array[index + 5] = 1 - (xx + zz);\n  array[index + 6] = yz + wx;\n  array[index + 7] = 0;\n  array[index + 8] = xz + wy;\n  array[index + 9] = yz - wx;\n  array[index + 10] = 1 - (xx + yy);\n  array[index + 11] = 0;\n  array[index + 12] = position.x();\n  array[index + 13] = position.y();\n  array[index + 14] = position.z();\n  array[index + 15] = 1;\n}\nexport { AmmoPhysics };", "map": {"version": 3, "names": ["AmmoPhysics", "window", "console", "error", "AmmoLib", "Ammo", "frameRate", "collisionConfiguration", "btDefaultCollisionConfiguration", "dispatcher", "btCollisionDispatcher", "broadphase", "btDbvtBroadphase", "solver", "btSequentialImpulseConstraintSolver", "world", "btDiscreteDynamicsWorld", "setGravity", "btVector3", "worldTransform", "btTransform", "getShape", "geometry", "parameters", "type", "sx", "width", "sy", "height", "sz", "depth", "shape", "btBoxShape", "<PERSON><PERSON><PERSON><PERSON>", "radius", "btSphereShape", "meshes", "meshMap", "WeakMap", "<PERSON><PERSON><PERSON>", "mesh", "mass", "isInstancedMesh", "handleInstancedMesh", "<PERSON><PERSON><PERSON>", "handleMesh", "position", "quaternion", "transform", "setIdentity", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "z", "setRotation", "btQuaternion", "w", "motionState", "btDefaultMotionState", "localInertia", "calculateLocalInertia", "rbInfo", "btRigidBodyConstructionInfo", "body", "btRigidBody", "addRigidBody", "push", "set", "array", "instanceMatrix", "bodies", "i", "count", "index", "setFromOpenGLMatrix", "slice", "setUsage", "setMeshPosition", "get", "setAngularVelocity", "setLinearVelocity", "setWorldTransform", "lastTime", "step", "time", "performance", "now", "delta", "stepSimulation", "l", "length", "j", "getMotionState", "getWorldTransform", "<PERSON><PERSON><PERSON><PERSON>", "getRotation", "compose", "needsUpdate", "setInterval", "x2", "y2", "z2", "xx", "xy", "xz", "yy", "yz", "zz", "wx", "wy", "wz"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/physics/AmmoPhysics.js"], "sourcesContent": ["async function AmmoPhysics() {\n  if ('Ammo' in window === false) {\n    console.error(\"AmmoPhysics: Couldn't find Ammo.js\")\n    return\n  }\n\n  const AmmoLib = await Ammo()\n\n  const frameRate = 60\n\n  const collisionConfiguration = new AmmoLib.btDefaultCollisionConfiguration()\n  const dispatcher = new AmmoLib.btCollisionDispatcher(collisionConfiguration)\n  const broadphase = new AmmoLib.btDbvtBroadphase()\n  const solver = new AmmoLib.btSequentialImpulseConstraintSolver()\n  const world = new AmmoLib.btDiscreteDynamicsWorld(dispatcher, broadphase, solver, collisionConfiguration)\n  world.setGravity(new AmmoLib.btVector3(0, -9.8, 0))\n\n  const worldTransform = new AmmoLib.btTransform()\n\n  //\n\n  function getShape(geometry) {\n    const parameters = geometry.parameters\n\n    // TODO change type to is*\n\n    if (geometry.type === 'BoxGeometry') {\n      const sx = parameters.width !== undefined ? parameters.width / 2 : 0.5\n      const sy = parameters.height !== undefined ? parameters.height / 2 : 0.5\n      const sz = parameters.depth !== undefined ? parameters.depth / 2 : 0.5\n\n      const shape = new AmmoLib.btBoxShape(new AmmoLib.btVector3(sx, sy, sz))\n      shape.setMargin(0.05)\n\n      return shape\n    } else if (geometry.type === 'SphereGeometry' || geometry.type === 'IcosahedronGeometry') {\n      const radius = parameters.radius !== undefined ? parameters.radius : 1\n\n      const shape = new AmmoLib.btSphereShape(radius)\n      shape.setMargin(0.05)\n\n      return shape\n    }\n\n    return null\n  }\n\n  const meshes = []\n  const meshMap = new WeakMap()\n\n  function addMesh(mesh, mass = 0) {\n    const shape = getShape(mesh.geometry)\n\n    if (shape !== null) {\n      if (mesh.isInstancedMesh) {\n        handleInstancedMesh(mesh, mass, shape)\n      } else if (mesh.isMesh) {\n        handleMesh(mesh, mass, shape)\n      }\n    }\n  }\n\n  function handleMesh(mesh, mass, shape) {\n    const position = mesh.position\n    const quaternion = mesh.quaternion\n\n    const transform = new AmmoLib.btTransform()\n    transform.setIdentity()\n    transform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n    transform.setRotation(new AmmoLib.btQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w))\n\n    const motionState = new AmmoLib.btDefaultMotionState(transform)\n\n    const localInertia = new AmmoLib.btVector3(0, 0, 0)\n    shape.calculateLocalInertia(mass, localInertia)\n\n    const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia)\n\n    const body = new AmmoLib.btRigidBody(rbInfo)\n    // body.setFriction( 4 );\n    world.addRigidBody(body)\n\n    if (mass > 0) {\n      meshes.push(mesh)\n      meshMap.set(mesh, body)\n    }\n  }\n\n  function handleInstancedMesh(mesh, mass, shape) {\n    const array = mesh.instanceMatrix.array\n\n    const bodies = []\n\n    for (let i = 0; i < mesh.count; i++) {\n      const index = i * 16\n\n      const transform = new AmmoLib.btTransform()\n      transform.setFromOpenGLMatrix(array.slice(index, index + 16))\n\n      const motionState = new AmmoLib.btDefaultMotionState(transform)\n\n      const localInertia = new AmmoLib.btVector3(0, 0, 0)\n      shape.calculateLocalInertia(mass, localInertia)\n\n      const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia)\n\n      const body = new AmmoLib.btRigidBody(rbInfo)\n      world.addRigidBody(body)\n\n      bodies.push(body)\n    }\n\n    if (mass > 0) {\n      mesh.instanceMatrix.setUsage(35048) // THREE.DynamicDrawUsage = 35048\n      meshes.push(mesh)\n\n      meshMap.set(mesh, bodies)\n    }\n  }\n\n  //\n\n  function setMeshPosition(mesh, position, index = 0) {\n    if (mesh.isInstancedMesh) {\n      const bodies = meshMap.get(mesh)\n      const body = bodies[index]\n\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0))\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0))\n\n      worldTransform.setIdentity()\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n      body.setWorldTransform(worldTransform)\n    } else if (mesh.isMesh) {\n      const body = meshMap.get(mesh)\n\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0))\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0))\n\n      worldTransform.setIdentity()\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n      body.setWorldTransform(worldTransform)\n    }\n  }\n\n  //\n\n  let lastTime = 0\n\n  function step() {\n    const time = performance.now()\n\n    if (lastTime > 0) {\n      const delta = (time - lastTime) / 1000\n\n      // console.time( 'world.step' );\n      world.stepSimulation(delta, 10)\n      // console.timeEnd( 'world.step' );\n    }\n\n    lastTime = time\n\n    //\n\n    for (let i = 0, l = meshes.length; i < l; i++) {\n      const mesh = meshes[i]\n\n      if (mesh.isInstancedMesh) {\n        const array = mesh.instanceMatrix.array\n        const bodies = meshMap.get(mesh)\n\n        for (let j = 0; j < bodies.length; j++) {\n          const body = bodies[j]\n\n          const motionState = body.getMotionState()\n          motionState.getWorldTransform(worldTransform)\n\n          const position = worldTransform.getOrigin()\n          const quaternion = worldTransform.getRotation()\n\n          compose(position, quaternion, array, j * 16)\n        }\n\n        mesh.instanceMatrix.needsUpdate = true\n      } else if (mesh.isMesh) {\n        const body = meshMap.get(mesh)\n\n        const motionState = body.getMotionState()\n        motionState.getWorldTransform(worldTransform)\n\n        const position = worldTransform.getOrigin()\n        const quaternion = worldTransform.getRotation()\n        mesh.position.set(position.x(), position.y(), position.z())\n        mesh.quaternion.set(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w())\n      }\n    }\n  }\n\n  // animate\n\n  setInterval(step, 1000 / frameRate)\n\n  return {\n    addMesh: addMesh,\n    setMeshPosition: setMeshPosition,\n    // addCompoundMesh\n  }\n}\n\nfunction compose(position, quaternion, array, index) {\n  const x = quaternion.x(),\n    y = quaternion.y(),\n    z = quaternion.z(),\n    w = quaternion.w()\n  const x2 = x + x,\n    y2 = y + y,\n    z2 = z + z\n  const xx = x * x2,\n    xy = x * y2,\n    xz = x * z2\n  const yy = y * y2,\n    yz = y * z2,\n    zz = z * z2\n  const wx = w * x2,\n    wy = w * y2,\n    wz = w * z2\n\n  array[index + 0] = 1 - (yy + zz)\n  array[index + 1] = xy + wz\n  array[index + 2] = xz - wy\n  array[index + 3] = 0\n\n  array[index + 4] = xy - wz\n  array[index + 5] = 1 - (xx + zz)\n  array[index + 6] = yz + wx\n  array[index + 7] = 0\n\n  array[index + 8] = xz + wy\n  array[index + 9] = yz - wx\n  array[index + 10] = 1 - (xx + yy)\n  array[index + 11] = 0\n\n  array[index + 12] = position.x()\n  array[index + 13] = position.y()\n  array[index + 14] = position.z()\n  array[index + 15] = 1\n}\n\nexport { AmmoPhysics }\n"], "mappings": "AAAA,eAAeA,YAAA,EAAc;EAC3B,IAAI,UAAUC,MAAA,KAAW,OAAO;IAC9BC,OAAA,CAAQC,KAAA,CAAM,oCAAoC;IAClD;EACD;EAED,MAAMC,OAAA,GAAU,MAAMC,IAAA,CAAM;EAE5B,MAAMC,SAAA,GAAY;EAElB,MAAMC,sBAAA,GAAyB,IAAIH,OAAA,CAAQI,+BAAA,CAAiC;EAC5E,MAAMC,UAAA,GAAa,IAAIL,OAAA,CAAQM,qBAAA,CAAsBH,sBAAsB;EAC3E,MAAMI,UAAA,GAAa,IAAIP,OAAA,CAAQQ,gBAAA,CAAkB;EACjD,MAAMC,MAAA,GAAS,IAAIT,OAAA,CAAQU,mCAAA,CAAqC;EAChE,MAAMC,KAAA,GAAQ,IAAIX,OAAA,CAAQY,uBAAA,CAAwBP,UAAA,EAAYE,UAAA,EAAYE,MAAA,EAAQN,sBAAsB;EACxGQ,KAAA,CAAME,UAAA,CAAW,IAAIb,OAAA,CAAQc,SAAA,CAAU,GAAG,MAAM,CAAC,CAAC;EAElD,MAAMC,cAAA,GAAiB,IAAIf,OAAA,CAAQgB,WAAA,CAAa;EAIhD,SAASC,SAASC,QAAA,EAAU;IAC1B,MAAMC,UAAA,GAAaD,QAAA,CAASC,UAAA;IAI5B,IAAID,QAAA,CAASE,IAAA,KAAS,eAAe;MACnC,MAAMC,EAAA,GAAKF,UAAA,CAAWG,KAAA,KAAU,SAAYH,UAAA,CAAWG,KAAA,GAAQ,IAAI;MACnE,MAAMC,EAAA,GAAKJ,UAAA,CAAWK,MAAA,KAAW,SAAYL,UAAA,CAAWK,MAAA,GAAS,IAAI;MACrE,MAAMC,EAAA,GAAKN,UAAA,CAAWO,KAAA,KAAU,SAAYP,UAAA,CAAWO,KAAA,GAAQ,IAAI;MAEnE,MAAMC,KAAA,GAAQ,IAAI3B,OAAA,CAAQ4B,UAAA,CAAW,IAAI5B,OAAA,CAAQc,SAAA,CAAUO,EAAA,EAAIE,EAAA,EAAIE,EAAE,CAAC;MACtEE,KAAA,CAAME,SAAA,CAAU,IAAI;MAEpB,OAAOF,KAAA;IACb,WAAeT,QAAA,CAASE,IAAA,KAAS,oBAAoBF,QAAA,CAASE,IAAA,KAAS,uBAAuB;MACxF,MAAMU,MAAA,GAASX,UAAA,CAAWW,MAAA,KAAW,SAAYX,UAAA,CAAWW,MAAA,GAAS;MAErE,MAAMH,KAAA,GAAQ,IAAI3B,OAAA,CAAQ+B,aAAA,CAAcD,MAAM;MAC9CH,KAAA,CAAME,SAAA,CAAU,IAAI;MAEpB,OAAOF,KAAA;IACR;IAED,OAAO;EACR;EAED,MAAMK,MAAA,GAAS,EAAE;EACjB,MAAMC,OAAA,GAAU,mBAAIC,OAAA,CAAS;EAE7B,SAASC,QAAQC,IAAA,EAAMC,IAAA,GAAO,GAAG;IAC/B,MAAMV,KAAA,GAAQV,QAAA,CAASmB,IAAA,CAAKlB,QAAQ;IAEpC,IAAIS,KAAA,KAAU,MAAM;MAClB,IAAIS,IAAA,CAAKE,eAAA,EAAiB;QACxBC,mBAAA,CAAoBH,IAAA,EAAMC,IAAA,EAAMV,KAAK;MAC7C,WAAiBS,IAAA,CAAKI,MAAA,EAAQ;QACtBC,UAAA,CAAWL,IAAA,EAAMC,IAAA,EAAMV,KAAK;MAC7B;IACF;EACF;EAED,SAASc,WAAWL,IAAA,EAAMC,IAAA,EAAMV,KAAA,EAAO;IACrC,MAAMe,QAAA,GAAWN,IAAA,CAAKM,QAAA;IACtB,MAAMC,UAAA,GAAaP,IAAA,CAAKO,UAAA;IAExB,MAAMC,SAAA,GAAY,IAAI5C,OAAA,CAAQgB,WAAA,CAAa;IAC3C4B,SAAA,CAAUC,WAAA,CAAa;IACvBD,SAAA,CAAUE,SAAA,CAAU,IAAI9C,OAAA,CAAQc,SAAA,CAAU4B,QAAA,CAASK,CAAA,EAAGL,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAC,CAAC;IAC7EL,SAAA,CAAUM,WAAA,CAAY,IAAIlD,OAAA,CAAQmD,YAAA,CAAaR,UAAA,CAAWI,CAAA,EAAGJ,UAAA,CAAWK,CAAA,EAAGL,UAAA,CAAWM,CAAA,EAAGN,UAAA,CAAWS,CAAC,CAAC;IAEtG,MAAMC,WAAA,GAAc,IAAIrD,OAAA,CAAQsD,oBAAA,CAAqBV,SAAS;IAE9D,MAAMW,YAAA,GAAe,IAAIvD,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC;IAClDa,KAAA,CAAM6B,qBAAA,CAAsBnB,IAAA,EAAMkB,YAAY;IAE9C,MAAME,MAAA,GAAS,IAAIzD,OAAA,CAAQ0D,2BAAA,CAA4BrB,IAAA,EAAMgB,WAAA,EAAa1B,KAAA,EAAO4B,YAAY;IAE7F,MAAMI,IAAA,GAAO,IAAI3D,OAAA,CAAQ4D,WAAA,CAAYH,MAAM;IAE3C9C,KAAA,CAAMkD,YAAA,CAAaF,IAAI;IAEvB,IAAItB,IAAA,GAAO,GAAG;MACZL,MAAA,CAAO8B,IAAA,CAAK1B,IAAI;MAChBH,OAAA,CAAQ8B,GAAA,CAAI3B,IAAA,EAAMuB,IAAI;IACvB;EACF;EAED,SAASpB,oBAAoBH,IAAA,EAAMC,IAAA,EAAMV,KAAA,EAAO;IAC9C,MAAMqC,KAAA,GAAQ5B,IAAA,CAAK6B,cAAA,CAAeD,KAAA;IAElC,MAAME,MAAA,GAAS,EAAE;IAEjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI/B,IAAA,CAAKgC,KAAA,EAAOD,CAAA,IAAK;MACnC,MAAME,KAAA,GAAQF,CAAA,GAAI;MAElB,MAAMvB,SAAA,GAAY,IAAI5C,OAAA,CAAQgB,WAAA,CAAa;MAC3C4B,SAAA,CAAU0B,mBAAA,CAAoBN,KAAA,CAAMO,KAAA,CAAMF,KAAA,EAAOA,KAAA,GAAQ,EAAE,CAAC;MAE5D,MAAMhB,WAAA,GAAc,IAAIrD,OAAA,CAAQsD,oBAAA,CAAqBV,SAAS;MAE9D,MAAMW,YAAA,GAAe,IAAIvD,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC;MAClDa,KAAA,CAAM6B,qBAAA,CAAsBnB,IAAA,EAAMkB,YAAY;MAE9C,MAAME,MAAA,GAAS,IAAIzD,OAAA,CAAQ0D,2BAAA,CAA4BrB,IAAA,EAAMgB,WAAA,EAAa1B,KAAA,EAAO4B,YAAY;MAE7F,MAAMI,IAAA,GAAO,IAAI3D,OAAA,CAAQ4D,WAAA,CAAYH,MAAM;MAC3C9C,KAAA,CAAMkD,YAAA,CAAaF,IAAI;MAEvBO,MAAA,CAAOJ,IAAA,CAAKH,IAAI;IACjB;IAED,IAAItB,IAAA,GAAO,GAAG;MACZD,IAAA,CAAK6B,cAAA,CAAeO,QAAA,CAAS,KAAK;MAClCxC,MAAA,CAAO8B,IAAA,CAAK1B,IAAI;MAEhBH,OAAA,CAAQ8B,GAAA,CAAI3B,IAAA,EAAM8B,MAAM;IACzB;EACF;EAID,SAASO,gBAAgBrC,IAAA,EAAMM,QAAA,EAAU2B,KAAA,GAAQ,GAAG;IAClD,IAAIjC,IAAA,CAAKE,eAAA,EAAiB;MACxB,MAAM4B,MAAA,GAASjC,OAAA,CAAQyC,GAAA,CAAItC,IAAI;MAC/B,MAAMuB,IAAA,GAAOO,MAAA,CAAOG,KAAK;MAEzBV,IAAA,CAAKgB,kBAAA,CAAmB,IAAI3E,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC,CAAC;MACtD6C,IAAA,CAAKiB,iBAAA,CAAkB,IAAI5E,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC,CAAC;MAErDC,cAAA,CAAe8B,WAAA,CAAa;MAC5B9B,cAAA,CAAe+B,SAAA,CAAU,IAAI9C,OAAA,CAAQc,SAAA,CAAU4B,QAAA,CAASK,CAAA,EAAGL,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAC,CAAC;MAClFU,IAAA,CAAKkB,iBAAA,CAAkB9D,cAAc;IAC3C,WAAeqB,IAAA,CAAKI,MAAA,EAAQ;MACtB,MAAMmB,IAAA,GAAO1B,OAAA,CAAQyC,GAAA,CAAItC,IAAI;MAE7BuB,IAAA,CAAKgB,kBAAA,CAAmB,IAAI3E,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC,CAAC;MACtD6C,IAAA,CAAKiB,iBAAA,CAAkB,IAAI5E,OAAA,CAAQc,SAAA,CAAU,GAAG,GAAG,CAAC,CAAC;MAErDC,cAAA,CAAe8B,WAAA,CAAa;MAC5B9B,cAAA,CAAe+B,SAAA,CAAU,IAAI9C,OAAA,CAAQc,SAAA,CAAU4B,QAAA,CAASK,CAAA,EAAGL,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAC,CAAC;MAClFU,IAAA,CAAKkB,iBAAA,CAAkB9D,cAAc;IACtC;EACF;EAID,IAAI+D,QAAA,GAAW;EAEf,SAASC,KAAA,EAAO;IACd,MAAMC,IAAA,GAAOC,WAAA,CAAYC,GAAA,CAAK;IAE9B,IAAIJ,QAAA,GAAW,GAAG;MAChB,MAAMK,KAAA,IAASH,IAAA,GAAOF,QAAA,IAAY;MAGlCnE,KAAA,CAAMyE,cAAA,CAAeD,KAAA,EAAO,EAAE;IAE/B;IAEDL,QAAA,GAAWE,IAAA;IAIX,SAASb,CAAA,GAAI,GAAGkB,CAAA,GAAIrD,MAAA,CAAOsD,MAAA,EAAQnB,CAAA,GAAIkB,CAAA,EAAGlB,CAAA,IAAK;MAC7C,MAAM/B,IAAA,GAAOJ,MAAA,CAAOmC,CAAC;MAErB,IAAI/B,IAAA,CAAKE,eAAA,EAAiB;QACxB,MAAM0B,KAAA,GAAQ5B,IAAA,CAAK6B,cAAA,CAAeD,KAAA;QAClC,MAAME,MAAA,GAASjC,OAAA,CAAQyC,GAAA,CAAItC,IAAI;QAE/B,SAASmD,CAAA,GAAI,GAAGA,CAAA,GAAIrB,MAAA,CAAOoB,MAAA,EAAQC,CAAA,IAAK;UACtC,MAAM5B,IAAA,GAAOO,MAAA,CAAOqB,CAAC;UAErB,MAAMlC,WAAA,GAAcM,IAAA,CAAK6B,cAAA,CAAgB;UACzCnC,WAAA,CAAYoC,iBAAA,CAAkB1E,cAAc;UAE5C,MAAM2B,QAAA,GAAW3B,cAAA,CAAe2E,SAAA,CAAW;UAC3C,MAAM/C,UAAA,GAAa5B,cAAA,CAAe4E,WAAA,CAAa;UAE/CC,OAAA,CAAQlD,QAAA,EAAUC,UAAA,EAAYqB,KAAA,EAAOuB,CAAA,GAAI,EAAE;QAC5C;QAEDnD,IAAA,CAAK6B,cAAA,CAAe4B,WAAA,GAAc;MAC1C,WAAiBzD,IAAA,CAAKI,MAAA,EAAQ;QACtB,MAAMmB,IAAA,GAAO1B,OAAA,CAAQyC,GAAA,CAAItC,IAAI;QAE7B,MAAMiB,WAAA,GAAcM,IAAA,CAAK6B,cAAA,CAAgB;QACzCnC,WAAA,CAAYoC,iBAAA,CAAkB1E,cAAc;QAE5C,MAAM2B,QAAA,GAAW3B,cAAA,CAAe2E,SAAA,CAAW;QAC3C,MAAM/C,UAAA,GAAa5B,cAAA,CAAe4E,WAAA,CAAa;QAC/CvD,IAAA,CAAKM,QAAA,CAASqB,GAAA,CAAIrB,QAAA,CAASK,CAAA,CAAC,GAAIL,QAAA,CAASM,CAAA,CAAG,GAAEN,QAAA,CAASO,CAAA,EAAG;QAC1Db,IAAA,CAAKO,UAAA,CAAWoB,GAAA,CAAIpB,UAAA,CAAWI,CAAA,CAAC,GAAIJ,UAAA,CAAWK,CAAA,CAAC,GAAIL,UAAA,CAAWM,CAAA,CAAG,GAAEN,UAAA,CAAWS,CAAA,CAAC,CAAE;MACnF;IACF;EACF;EAID0C,WAAA,CAAYf,IAAA,EAAM,MAAO7E,SAAS;EAElC,OAAO;IACLiC,OAAA;IACAsC;IAAA;EAED;AACH;AAEA,SAASmB,QAAQlD,QAAA,EAAUC,UAAA,EAAYqB,KAAA,EAAOK,KAAA,EAAO;EACnD,MAAMtB,CAAA,GAAIJ,UAAA,CAAWI,CAAA,CAAG;IACtBC,CAAA,GAAIL,UAAA,CAAWK,CAAA,CAAG;IAClBC,CAAA,GAAIN,UAAA,CAAWM,CAAA,CAAG;IAClBG,CAAA,GAAIT,UAAA,CAAWS,CAAA,CAAG;EACpB,MAAM2C,EAAA,GAAKhD,CAAA,GAAIA,CAAA;IACbiD,EAAA,GAAKhD,CAAA,GAAIA,CAAA;IACTiD,EAAA,GAAKhD,CAAA,GAAIA,CAAA;EACX,MAAMiD,EAAA,GAAKnD,CAAA,GAAIgD,EAAA;IACbI,EAAA,GAAKpD,CAAA,GAAIiD,EAAA;IACTI,EAAA,GAAKrD,CAAA,GAAIkD,EAAA;EACX,MAAMI,EAAA,GAAKrD,CAAA,GAAIgD,EAAA;IACbM,EAAA,GAAKtD,CAAA,GAAIiD,EAAA;IACTM,EAAA,GAAKtD,CAAA,GAAIgD,EAAA;EACX,MAAMO,EAAA,GAAKpD,CAAA,GAAI2C,EAAA;IACbU,EAAA,GAAKrD,CAAA,GAAI4C,EAAA;IACTU,EAAA,GAAKtD,CAAA,GAAI6C,EAAA;EAEXjC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI,KAAKgC,EAAA,GAAKE,EAAA;EAC7BvC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI8B,EAAA,GAAKO,EAAA;EACxB1C,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI+B,EAAA,GAAKK,EAAA;EACxBzC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI;EAEnBL,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI8B,EAAA,GAAKO,EAAA;EACxB1C,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI,KAAK6B,EAAA,GAAKK,EAAA;EAC7BvC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAIiC,EAAA,GAAKE,EAAA;EACxBxC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI;EAEnBL,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAI+B,EAAA,GAAKK,EAAA;EACxBzC,KAAA,CAAMK,KAAA,GAAQ,CAAC,IAAIiC,EAAA,GAAKE,EAAA;EACxBxC,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI,KAAK6B,EAAA,GAAKG,EAAA;EAC9BrC,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI;EAEpBL,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI3B,QAAA,CAASK,CAAA,CAAG;EAChCiB,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI3B,QAAA,CAASM,CAAA,CAAG;EAChCgB,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI3B,QAAA,CAASO,CAAA,CAAG;EAChCe,KAAA,CAAMK,KAAA,GAAQ,EAAE,IAAI;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}