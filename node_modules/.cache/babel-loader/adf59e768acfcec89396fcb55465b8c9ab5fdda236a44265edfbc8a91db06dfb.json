{"ast": null, "code": "import { linkedin, telegram, whatsApp, email } from \"../assets/image\";\nconst MENU_LINKS = [{\n  title: \"Home\",\n  link: \"./#home\"\n}, {\n  title: \"About\",\n  link: \"./#about\"\n}, {\n  title: \"Education\",\n  link: \"./#education\"\n}, {\n  title: \"Experience\",\n  link: \"./#experience\"\n}, {\n  title: \"Skills\",\n  link: \"./#skills\"\n}, {\n  title: \"Projects\",\n  link: \"./#projects\"\n}, {\n  title: \"Contact\",\n  link: \"./#contact\"\n}];\nconst SOCIAL_ITEMS = [{\n  icon: whatsApp,\n  link: \"\"\n}, {\n  icon: telegram,\n  link: \"\"\n}, {\n  icon: linkedin,\n  link: \"\"\n}, {\n  icon: email,\n  link: \"\"\n}];\nexport { MENU_LINKS, SOCIAL_ITEMS };", "map": {"version": 3, "names": ["linkedin", "telegram", "whatsApp", "email", "MENU_LINKS", "title", "link", "SOCIAL_ITEMS", "icon"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/Links.ts"], "sourcesContent": ["import { linkedin, telegram, whatsApp, email } from \"../assets/image\";\nimport { MENU_LINK, SOCIAL_DATA } from \"../types\";\n\nconst MENU_LINKS: MENU_LINK[] = [\n  { title: \"Home\", link: \"./#home\" },\n  { title: \"About\", link: \"./#about\" },\n  { title: \"Education\", link: \"./#education\" },\n  { title: \"Experience\", link: \"./#experience\" },\n  { title: \"Skills\", link: \"./#skills\" },\n  { title: \"Projects\", link: \"./#projects\" },\n  { title: \"Contact\", link: \"./#contact\" },\n];\n\nconst SOCIAL_ITEMS: SOCIAL_DATA[] = [\n  { icon: whatsApp, link: \"\" },\n  { icon: telegram, link: \"\" },\n  { icon: linkedin, link: \"\" },\n  { icon: email, link: \"\" },\n];\n\nexport { MENU_LINKS, SOCIAL_ITEMS };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,iBAAiB;AAGrE,MAAMC,UAAuB,GAAG,CAC9B;EAAEC,KAAK,EAAE,MAAM;EAAEC,IAAI,EAAE;AAAU,CAAC,EAClC;EAAED,KAAK,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAW,CAAC,EACpC;EAAED,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAe,CAAC,EAC5C;EAAED,KAAK,EAAE,YAAY;EAAEC,IAAI,EAAE;AAAgB,CAAC,EAC9C;EAAED,KAAK,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAY,CAAC,EACtC;EAAED,KAAK,EAAE,UAAU;EAAEC,IAAI,EAAE;AAAc,CAAC,EAC1C;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAa,CAAC,CACzC;AAED,MAAMC,YAA2B,GAAG,CAClC;EAAEC,IAAI,EAAEN,QAAQ;EAAEI,IAAI,EAAE;AAAG,CAAC,EAC5B;EAAEE,IAAI,EAAEP,QAAQ;EAAEK,IAAI,EAAE;AAAG,CAAC,EAC5B;EAAEE,IAAI,EAAER,QAAQ;EAAEM,IAAI,EAAE;AAAG,CAAC,EAC5B;EAAEE,IAAI,EAAEL,KAAK;EAAEG,IAAI,EAAE;AAAG,CAAC,CAC1B;AAED,SAASF,UAAU,EAAEG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}