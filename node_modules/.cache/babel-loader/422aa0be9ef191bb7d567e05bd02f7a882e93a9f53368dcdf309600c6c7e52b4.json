{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\nexport { PerspectiveCamera };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "useFBO", "isFunction", "node", "PerspectiveCamera", "forwardRef", "envMap", "resolution", "frames", "Infinity", "makeDefault", "children", "props", "ref", "set", "camera", "size", "cameraRef", "useRef", "useImperativeHandle", "current", "groupRef", "fbo", "useLayoutEffect", "manual", "aspect", "width", "height", "updateProjectionMatrix", "count", "oldEnvMap", "functional", "state", "visible", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scene", "background", "render", "oldCam", "createElement", "Fragment", "texture"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/PerspectiveCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\n\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { PerspectiveCamera };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,MAAM,QAAQ,UAAU;AAEjC,MAAMC,UAAU,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,UAAU;AACrD,MAAMC,iBAAiB,GAAG,eAAeN,KAAK,CAACO,UAAU,CAAC,CAAC;EACzDC,MAAM;EACNC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAGC,QAAQ;EACjBC,WAAW;EACXC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,GAAG,GAAGf,QAAQ,CAAC,CAAC;IACpBe;EACF,CAAC,KAAKA,GAAG,CAAC;EACV,MAAMC,MAAM,GAAGhB,QAAQ,CAAC,CAAC;IACvBgB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,IAAI,GAAGjB,QAAQ,CAAC,CAAC;IACrBiB;EACF,CAAC,KAAKA,IAAI,CAAC;EACX,MAAMC,SAAS,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACpCpB,KAAK,CAACqB,mBAAmB,CAACN,GAAG,EAAE,MAAMI,SAAS,CAACG,OAAO,EAAE,EAAE,CAAC;EAC3D,MAAMC,QAAQ,GAAGvB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMI,GAAG,GAAGrB,MAAM,CAACM,UAAU,CAAC;EAC9BT,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACX,KAAK,CAACY,MAAM,EAAE;MACjBP,SAAS,CAACG,OAAO,CAACK,MAAM,GAAGT,IAAI,CAACU,KAAK,GAAGV,IAAI,CAACW,MAAM;IACrD;EACF,CAAC,EAAE,CAACX,IAAI,EAAEJ,KAAK,CAAC,CAAC;EACjBd,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1BN,SAAS,CAACG,OAAO,CAACQ,sBAAsB,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAG7B,UAAU,CAACS,QAAQ,CAAC;EACvCX,QAAQ,CAACgC,KAAK,IAAI;IAChB,IAAID,UAAU,KAAKvB,MAAM,KAAKC,QAAQ,IAAIoB,KAAK,GAAGrB,MAAM,CAAC,EAAE;MACzDa,QAAQ,CAACD,OAAO,CAACa,OAAO,GAAG,KAAK;MAChCD,KAAK,CAACE,EAAE,CAACC,eAAe,CAACb,GAAG,CAAC;MAC7BQ,SAAS,GAAGE,KAAK,CAACI,KAAK,CAACC,UAAU;MAClC,IAAI/B,MAAM,EAAE0B,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG/B,MAAM;MAC3C0B,KAAK,CAACE,EAAE,CAACI,MAAM,CAACN,KAAK,CAACI,KAAK,EAAEnB,SAAS,CAACG,OAAO,CAAC;MAC/CY,KAAK,CAACI,KAAK,CAACC,UAAU,GAAGP,SAAS;MAClCE,KAAK,CAACE,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9Bd,QAAQ,CAACD,OAAO,CAACa,OAAO,GAAG,IAAI;MAC/BJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF/B,KAAK,CAACyB,eAAe,CAAC,MAAM;IAC1B,IAAIb,WAAW,EAAE;MACf,MAAM6B,MAAM,GAAGxB,MAAM;MACrBD,GAAG,CAAC,OAAO;QACTC,MAAM,EAAEE,SAAS,CAACG;MACpB,CAAC,CAAC,CAAC;MACH,OAAO,MAAMN,GAAG,CAAC,OAAO;QACtBC,MAAM,EAAEwB;MACV,CAAC,CAAC,CAAC;IACL;IACA;IACA;EACF,CAAC,EAAE,CAACtB,SAAS,EAAEP,WAAW,EAAEI,GAAG,CAAC,CAAC;EACjC,OAAO,aAAahB,KAAK,CAAC0C,aAAa,CAAC1C,KAAK,CAAC2C,QAAQ,EAAE,IAAI,EAAE,aAAa3C,KAAK,CAAC0C,aAAa,CAAC,mBAAmB,EAAE3C,QAAQ,CAAC;IAC3HgB,GAAG,EAAEI;EACP,CAAC,EAAEL,KAAK,CAAC,EAAE,CAACmB,UAAU,IAAIpB,QAAQ,CAAC,EAAE,aAAab,KAAK,CAAC0C,aAAa,CAAC,OAAO,EAAE;IAC7E3B,GAAG,EAAEQ;EACP,CAAC,EAAEU,UAAU,IAAIpB,QAAQ,CAACW,GAAG,CAACoB,OAAO,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,SAAStC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}