{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { applyProps } from '@react-three/fiber';\n\n// credits for the box-projecting shader code go to codercat (https://codercat.tk)\n// and @0beqz https://gist.github.com/0beqz/8d51b4ae16d68021a09fb504af708fca\n\nconst worldposReplace = /* glsl */`\n#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n  vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n  #ifdef BOX_PROJECTED_ENV_MAP\n    vWorldPosition = worldPosition.xyz;\n  #endif\n#endif\n`;\nconst boxProjectDefinitions = /*glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  uniform vec3 envMapSize;\n  uniform vec3 envMapPosition;\n  varying vec3 vWorldPosition;\n    \n  vec3 parallaxCorrectNormal( vec3 v, vec3 cubeSize, vec3 cubePos ) {\n    vec3 nDir = normalize( v );\n    vec3 rbmax = ( .5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbmin = ( -.5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbminmax;\n    rbminmax.x = ( nDir.x > 0. ) ? rbmax.x : rbmin.x;\n    rbminmax.y = ( nDir.y > 0. ) ? rbmax.y : rbmin.y;\n    rbminmax.z = ( nDir.z > 0. ) ? rbmax.z : rbmin.z;\n    float correction = min( min( rbminmax.x, rbminmax.y ), rbminmax.z );\n    vec3 boxIntersection = vWorldPosition + nDir * correction;    \n    return boxIntersection - cubePos;\n  }\n#endif\n`;\n\n// will be inserted after \"vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\"\nconst getIBLIrradiance_patch = /* glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  worldNormal = parallaxCorrectNormal( worldNormal, envMapSize, envMapPosition );\n#endif\n`;\n\n// will be inserted after \"reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\"\nconst getIBLRadiance_patch = /* glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  reflectVec = parallaxCorrectNormal( reflectVec, envMapSize, envMapPosition );\n#endif\n`;\n\n// FIXME Replace with `THREE.WebGLProgramParametersWithUniforms` type when able to target @types/three@0.160.0\n\nfunction boxProjectedEnvMap(shader, envMapPosition, envMapSize) {\n  // defines\n  shader.defines.BOX_PROJECTED_ENV_MAP = true;\n  // uniforms\n  shader.uniforms.envMapPosition = {\n    value: envMapPosition\n  };\n  shader.uniforms.envMapSize = {\n    value: envMapSize\n  };\n  // vertex shader\n  shader.vertexShader = `\n  varying vec3 vWorldPosition;\n  ${shader.vertexShader.replace('#include <worldpos_vertex>', worldposReplace)}`;\n  // fragment shader\n  shader.fragmentShader = `\n    ${boxProjectDefinitions}\n    ${shader.fragmentShader.replace('#include <envmap_physical_pars_fragment>', THREE.ShaderChunk.envmap_physical_pars_fragment).replace('vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );', `vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n         ${getIBLIrradiance_patch}\n         `).replace('reflectVec = inverseTransformDirection( reflectVec, viewMatrix );', `reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n         ${getIBLRadiance_patch}\n        `)}`;\n}\nfunction useBoxProjectedEnv(position = new THREE.Vector3(), size = new THREE.Vector3()) {\n  const [config] = React.useState(() => ({\n    position: new THREE.Vector3(),\n    size: new THREE.Vector3()\n  }));\n  applyProps(config, {\n    position,\n    size\n  });\n  const ref = React.useRef(null);\n  const spread = React.useMemo(() => ({\n    ref,\n    onBeforeCompile: shader => boxProjectedEnvMap(shader, config.position, config.size),\n    customProgramCacheKey: () => JSON.stringify(config.position.toArray()) + JSON.stringify(config.size.toArray())\n  }), [...config.position.toArray(), ...config.size.toArray()]);\n  React.useLayoutEffect(() => void (ref.current.needsUpdate = true), [config]);\n  return spread;\n}\nexport { useBoxProjectedEnv };", "map": {"version": 3, "names": ["THREE", "React", "applyProps", "worldposReplace", "boxProjectDefinitions", "getIBLIrradiance_patch", "getIBLRadiance_patch", "boxProjectedEnvMap", "shader", "envMapPosition", "envMapSize", "defines", "BOX_PROJECTED_ENV_MAP", "uniforms", "value", "vertexShader", "replace", "fragmentShader", "ShaderChunk", "envmap_physical_pars_fragment", "useBoxProjectedEnv", "position", "Vector3", "size", "config", "useState", "ref", "useRef", "spread", "useMemo", "onBeforeCompile", "customProgramCacheKey", "JSON", "stringify", "toArray", "useLayoutEffect", "current", "needsUpdate"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/useBoxProjectedEnv.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { applyProps } from '@react-three/fiber';\n\n// credits for the box-projecting shader code go to codercat (https://codercat.tk)\n// and @0beqz https://gist.github.com/0beqz/8d51b4ae16d68021a09fb504af708fca\n\nconst worldposReplace = /* glsl */`\n#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n  vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n  #ifdef BOX_PROJECTED_ENV_MAP\n    vWorldPosition = worldPosition.xyz;\n  #endif\n#endif\n`;\nconst boxProjectDefinitions = /*glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  uniform vec3 envMapSize;\n  uniform vec3 envMapPosition;\n  varying vec3 vWorldPosition;\n    \n  vec3 parallaxCorrectNormal( vec3 v, vec3 cubeSize, vec3 cubePos ) {\n    vec3 nDir = normalize( v );\n    vec3 rbmax = ( .5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbmin = ( -.5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbminmax;\n    rbminmax.x = ( nDir.x > 0. ) ? rbmax.x : rbmin.x;\n    rbminmax.y = ( nDir.y > 0. ) ? rbmax.y : rbmin.y;\n    rbminmax.z = ( nDir.z > 0. ) ? rbmax.z : rbmin.z;\n    float correction = min( min( rbminmax.x, rbminmax.y ), rbminmax.z );\n    vec3 boxIntersection = vWorldPosition + nDir * correction;    \n    return boxIntersection - cubePos;\n  }\n#endif\n`;\n\n// will be inserted after \"vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\"\nconst getIBLIrradiance_patch = /* glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  worldNormal = parallaxCorrectNormal( worldNormal, envMapSize, envMapPosition );\n#endif\n`;\n\n// will be inserted after \"reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\"\nconst getIBLRadiance_patch = /* glsl */`\n#ifdef BOX_PROJECTED_ENV_MAP\n  reflectVec = parallaxCorrectNormal( reflectVec, envMapSize, envMapPosition );\n#endif\n`;\n\n// FIXME Replace with `THREE.WebGLProgramParametersWithUniforms` type when able to target @types/three@0.160.0\n\nfunction boxProjectedEnvMap(shader, envMapPosition, envMapSize) {\n  // defines\n  shader.defines.BOX_PROJECTED_ENV_MAP = true;\n  // uniforms\n  shader.uniforms.envMapPosition = {\n    value: envMapPosition\n  };\n  shader.uniforms.envMapSize = {\n    value: envMapSize\n  };\n  // vertex shader\n  shader.vertexShader = `\n  varying vec3 vWorldPosition;\n  ${shader.vertexShader.replace('#include <worldpos_vertex>', worldposReplace)}`;\n  // fragment shader\n  shader.fragmentShader = `\n    ${boxProjectDefinitions}\n    ${shader.fragmentShader.replace('#include <envmap_physical_pars_fragment>', THREE.ShaderChunk.envmap_physical_pars_fragment).replace('vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );', `vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n         ${getIBLIrradiance_patch}\n         `).replace('reflectVec = inverseTransformDirection( reflectVec, viewMatrix );', `reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n         ${getIBLRadiance_patch}\n        `)}`;\n}\nfunction useBoxProjectedEnv(position = new THREE.Vector3(), size = new THREE.Vector3()) {\n  const [config] = React.useState(() => ({\n    position: new THREE.Vector3(),\n    size: new THREE.Vector3()\n  }));\n  applyProps(config, {\n    position,\n    size\n  });\n  const ref = React.useRef(null);\n  const spread = React.useMemo(() => ({\n    ref,\n    onBeforeCompile: shader => boxProjectedEnvMap(shader, config.position, config.size),\n    customProgramCacheKey: () => JSON.stringify(config.position.toArray()) + JSON.stringify(config.size.toArray())\n  }), [...config.position.toArray(), ...config.size.toArray()]);\n  React.useLayoutEffect(() => void (ref.current.needsUpdate = true), [config]);\n  return spread;\n}\n\nexport { useBoxProjectedEnv };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,oBAAoB;;AAE/C;AACA;;AAEA,MAAMC,eAAe,GAAG,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,qBAAqB,GAAG,SAAS;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,sBAAsB,GAAG,UAAU;AACzC;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA,CAAC;;AAED;;AAEA,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,cAAc,EAAEC,UAAU,EAAE;EAC9D;EACAF,MAAM,CAACG,OAAO,CAACC,qBAAqB,GAAG,IAAI;EAC3C;EACAJ,MAAM,CAACK,QAAQ,CAACJ,cAAc,GAAG;IAC/BK,KAAK,EAAEL;EACT,CAAC;EACDD,MAAM,CAACK,QAAQ,CAACH,UAAU,GAAG;IAC3BI,KAAK,EAAEJ;EACT,CAAC;EACD;EACAF,MAAM,CAACO,YAAY,GAAG;AACxB;AACA,IAAIP,MAAM,CAACO,YAAY,CAACC,OAAO,CAAC,4BAA4B,EAAEb,eAAe,CAAC,EAAE;EAC9E;EACAK,MAAM,CAACS,cAAc,GAAG;AAC1B,MAAMb,qBAAqB;AAC3B,MAAMI,MAAM,CAACS,cAAc,CAACD,OAAO,CAAC,0CAA0C,EAAEhB,KAAK,CAACkB,WAAW,CAACC,6BAA6B,CAAC,CAACH,OAAO,CAAC,qEAAqE,EAAE;AAChN,WAAWX,sBAAsB;AACjC,UAAU,CAAC,CAACW,OAAO,CAAC,mEAAmE,EAAE;AACzF,WAAWV,oBAAoB;AAC/B,SAAS,CAAC,EAAE;AACZ;AACA,SAASc,kBAAkBA,CAACC,QAAQ,GAAG,IAAIrB,KAAK,CAACsB,OAAO,CAAC,CAAC,EAAEC,IAAI,GAAG,IAAIvB,KAAK,CAACsB,OAAO,CAAC,CAAC,EAAE;EACtF,MAAM,CAACE,MAAM,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,OAAO;IACrCJ,QAAQ,EAAE,IAAIrB,KAAK,CAACsB,OAAO,CAAC,CAAC;IAC7BC,IAAI,EAAE,IAAIvB,KAAK,CAACsB,OAAO,CAAC;EAC1B,CAAC,CAAC,CAAC;EACHpB,UAAU,CAACsB,MAAM,EAAE;IACjBH,QAAQ;IACRE;EACF,CAAC,CAAC;EACF,MAAMG,GAAG,GAAGzB,KAAK,CAAC0B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,MAAM,GAAG3B,KAAK,CAAC4B,OAAO,CAAC,OAAO;IAClCH,GAAG;IACHI,eAAe,EAAEtB,MAAM,IAAID,kBAAkB,CAACC,MAAM,EAAEgB,MAAM,CAACH,QAAQ,EAAEG,MAAM,CAACD,IAAI,CAAC;IACnFQ,qBAAqB,EAAEA,CAAA,KAAMC,IAAI,CAACC,SAAS,CAACT,MAAM,CAACH,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAAGF,IAAI,CAACC,SAAS,CAACT,MAAM,CAACD,IAAI,CAACW,OAAO,CAAC,CAAC;EAC/G,CAAC,CAAC,EAAE,CAAC,GAAGV,MAAM,CAACH,QAAQ,CAACa,OAAO,CAAC,CAAC,EAAE,GAAGV,MAAM,CAACD,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7DjC,KAAK,CAACkC,eAAe,CAAC,MAAM,MAAMT,GAAG,CAACU,OAAO,CAACC,WAAW,GAAG,IAAI,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EAC5E,OAAOI,MAAM;AACf;AAEA,SAASR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}