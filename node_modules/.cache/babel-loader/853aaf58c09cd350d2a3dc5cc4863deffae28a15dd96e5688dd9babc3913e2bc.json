{"ast": null, "code": "export { shallow } from 'zustand/vanilla/shallow';\nexport { useShallow } from 'zustand/react/shallow';", "map": {"version": 3, "names": ["shallow", "useShallow"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/zustand/esm/shallow.mjs"], "sourcesContent": ["export { shallow } from 'zustand/vanilla/shallow';\nexport { useShallow } from 'zustand/react/shallow';\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB;AACjD,SAASC,UAAU,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}