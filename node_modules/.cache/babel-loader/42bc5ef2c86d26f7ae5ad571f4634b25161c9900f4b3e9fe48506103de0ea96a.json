{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { version } from '../helpers/constants.js';\nconst opaque_fragment = version >= 154 ? 'opaque_fragment' : 'output_fragment';\nclass PointMaterialImpl extends THREE.PointsMaterial {\n  constructor(props) {\n    super(props);\n    this.onBeforeCompile = (shader, renderer) => {\n      const {\n        isWebGL2\n      } = renderer.capabilities;\n      shader.fragmentShader = shader.fragmentShader.replace(`#include <${opaque_fragment}>`, `\n        ${!isWebGL2 ? `#extension GL_OES_standard_derivatives : enable\\n#include <${opaque_fragment}>` : `#include <${opaque_fragment}>`}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n      `);\n    };\n  }\n}\nconst PointMaterial = /* @__PURE__ */React.forwardRef((props, ref) => {\n  const [material] = React.useState(() => new PointMaterialImpl(null));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({}, props, {\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }));\n});\nexport { PointMaterial, PointMaterialImpl };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "version", "opaque_fragment", "PointMaterialImpl", "PointsMaterial", "constructor", "props", "onBeforeCompile", "shader", "renderer", "isWebGL2", "capabilities", "fragmentShader", "replace", "PointMaterial", "forwardRef", "ref", "material", "useState", "createElement", "object", "attach"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/PointMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { version } from '../helpers/constants.js';\n\nconst opaque_fragment = version >= 154 ? 'opaque_fragment' : 'output_fragment';\nclass PointMaterialImpl extends THREE.PointsMaterial {\n  constructor(props) {\n    super(props);\n    this.onBeforeCompile = (shader, renderer) => {\n      const {\n        isWebGL2\n      } = renderer.capabilities;\n      shader.fragmentShader = shader.fragmentShader.replace(`#include <${opaque_fragment}>`, `\n        ${!isWebGL2 ? `#extension GL_OES_standard_derivatives : enable\\n#include <${opaque_fragment}>` : `#include <${opaque_fragment}>`}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n      `);\n    };\n  }\n}\nconst PointMaterial = /* @__PURE__ */React.forwardRef((props, ref) => {\n  const [material] = React.useState(() => new PointMaterialImpl(null));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({}, props, {\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }));\n});\n\nexport { PointMaterial, PointMaterialImpl };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,eAAe,GAAGD,OAAO,IAAI,GAAG,GAAG,iBAAiB,GAAG,iBAAiB;AAC9E,MAAME,iBAAiB,SAASJ,KAAK,CAACK,cAAc,CAAC;EACnDC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,eAAe,GAAG,CAACC,MAAM,EAAEC,QAAQ,KAAK;MAC3C,MAAM;QACJC;MACF,CAAC,GAAGD,QAAQ,CAACE,YAAY;MACzBH,MAAM,CAACI,cAAc,GAAGJ,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,aAAaX,eAAe,GAAG,EAAE;AAC7F,UAAU,CAACQ,QAAQ,GAAG,8DAA8DR,eAAe,GAAG,GAAG,aAAaA,eAAe,GAAG;AACxI;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC/E,OAAO,CAAC;IACJ,CAAC;EACH;AACF;AACA,MAAMa,aAAa,GAAG,eAAed,KAAK,CAACe,UAAU,CAAC,CAACT,KAAK,EAAEU,GAAG,KAAK;EACpE,MAAM,CAACC,QAAQ,CAAC,GAAGjB,KAAK,CAACkB,QAAQ,CAAC,MAAM,IAAIf,iBAAiB,CAAC,IAAI,CAAC,CAAC;EACpE,OAAO,aAAaH,KAAK,CAACmB,aAAa,CAAC,WAAW,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IACvEc,MAAM,EAAEH,QAAQ;IAChBD,GAAG,EAAEA,GAAG;IACRK,MAAM,EAAE;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASP,aAAa,EAAEX,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}