{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { PointerLockControls as PointerLockControls$1 } from 'three-stdlib';\nconst PointerLockControls = /* @__PURE__ */React.forwardRef(({\n  domElement,\n  selector,\n  onChange,\n  onLock,\n  onUnlock,\n  enabled = true,\n  makeDefault,\n  ...props\n}, ref) => {\n  const {\n    camera,\n    ...rest\n  } = props;\n  const setEvents = useThree(state => state.setEvents);\n  const gl = useThree(state => state.gl);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new PointerLockControls$1(explCamera), [explCamera]);\n  React.useEffect(() => {\n    if (enabled) {\n      controls.connect(explDomElement);\n      // Force events to be centered while PLC is active\n      const oldComputeOffsets = get().events.compute;\n      setEvents({\n        compute(event, state) {\n          const offsetX = state.size.width / 2;\n          const offsetY = state.size.height / 2;\n          state.pointer.set(offsetX / state.size.width * 2 - 1, -(offsetY / state.size.height) * 2 + 1);\n          state.raycaster.setFromCamera(state.pointer, state.camera);\n        }\n      });\n      return () => {\n        controls.disconnect();\n        setEvents({\n          compute: oldComputeOffsets\n        });\n      };\n    }\n  }, [enabled, controls]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onLock) controls.addEventListener('lock', onLock);\n    if (onUnlock) controls.addEventListener('unlock', onUnlock);\n\n    // Enforce previous interaction\n    const handler = () => controls.lock();\n    const elements = selector ? Array.from(document.querySelectorAll(selector)) : [document];\n    elements.forEach(element => element && element.addEventListener('click', handler));\n    return () => {\n      controls.removeEventListener('change', callback);\n      if (onLock) controls.removeEventListener('lock', onLock);\n      if (onUnlock) controls.removeEventListener('unlock', onUnlock);\n      elements.forEach(element => element ? element.removeEventListener('click', handler) : undefined);\n    };\n  }, [onChange, onLock, onUnlock, selector, controls, invalidate]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, rest));\n});\nexport { PointerLockControls };", "map": {"version": 3, "names": ["_extends", "useThree", "React", "PointerLockControls", "PointerLockControls$1", "forwardRef", "dom<PERSON>lement", "selector", "onChange", "onLock", "onUnlock", "enabled", "makeDefault", "props", "ref", "camera", "rest", "setEvents", "state", "gl", "defaultCamera", "invalidate", "events", "get", "set", "explCamera", "explDomElement", "connected", "controls", "useMemo", "useEffect", "connect", "oldComputeOffsets", "compute", "event", "offsetX", "size", "width", "offsetY", "height", "pointer", "raycaster", "setFromCamera", "disconnect", "callback", "e", "addEventListener", "handler", "lock", "elements", "Array", "from", "document", "querySelectorAll", "for<PERSON>ach", "element", "removeEventListener", "undefined", "old", "createElement", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/PointerLockControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { PointerLockControls as PointerLockControls$1 } from 'three-stdlib';\n\nconst PointerLockControls = /* @__PURE__ */React.forwardRef(({\n  domElement,\n  selector,\n  onChange,\n  onLock,\n  onUnlock,\n  enabled = true,\n  makeDefault,\n  ...props\n}, ref) => {\n  const {\n    camera,\n    ...rest\n  } = props;\n  const setEvents = useThree(state => state.setEvents);\n  const gl = useThree(state => state.gl);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new PointerLockControls$1(explCamera), [explCamera]);\n  React.useEffect(() => {\n    if (enabled) {\n      controls.connect(explDomElement);\n      // Force events to be centered while PLC is active\n      const oldComputeOffsets = get().events.compute;\n      setEvents({\n        compute(event, state) {\n          const offsetX = state.size.width / 2;\n          const offsetY = state.size.height / 2;\n          state.pointer.set(offsetX / state.size.width * 2 - 1, -(offsetY / state.size.height) * 2 + 1);\n          state.raycaster.setFromCamera(state.pointer, state.camera);\n        }\n      });\n      return () => {\n        controls.disconnect();\n        setEvents({\n          compute: oldComputeOffsets\n        });\n      };\n    }\n  }, [enabled, controls]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onLock) controls.addEventListener('lock', onLock);\n    if (onUnlock) controls.addEventListener('unlock', onUnlock);\n\n    // Enforce previous interaction\n    const handler = () => controls.lock();\n    const elements = selector ? Array.from(document.querySelectorAll(selector)) : [document];\n    elements.forEach(element => element && element.addEventListener('click', handler));\n    return () => {\n      controls.removeEventListener('change', callback);\n      if (onLock) controls.removeEventListener('lock', onLock);\n      if (onUnlock) controls.removeEventListener('unlock', onUnlock);\n      elements.forEach(element => element ? element.removeEventListener('click', handler) : undefined);\n    };\n  }, [onChange, onLock, onUnlock, selector, controls, invalidate]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, rest));\n});\n\nexport { PointerLockControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,qBAAqB,QAAQ,cAAc;AAE3E,MAAMD,mBAAmB,GAAG,eAAeD,KAAK,CAACG,UAAU,CAAC,CAAC;EAC3DC,UAAU;EACVC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,QAAQ;EACRC,OAAO,GAAG,IAAI;EACdC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC,MAAM;IACN,GAAGC;EACL,CAAC,GAAGH,KAAK;EACT,MAAMI,SAAS,GAAGhB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACD,SAAS,CAAC;EACpD,MAAME,EAAE,GAAGlB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,aAAa,GAAGnB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACH,MAAM,CAAC;EACrD,MAAMM,UAAU,GAAGpB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACG,UAAU,CAAC;EACtD,MAAMC,MAAM,GAAGrB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACI,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGtB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGvB,QAAQ,CAACiB,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,UAAU,GAAGV,MAAM,IAAIK,aAAa;EAC1C,MAAMM,cAAc,GAAGpB,UAAU,IAAIgB,MAAM,CAACK,SAAS,IAAIR,EAAE,CAACb,UAAU;EACtE,MAAMsB,QAAQ,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,MAAM,IAAIzB,qBAAqB,CAACqB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACzFvB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAInB,OAAO,EAAE;MACXiB,QAAQ,CAACG,OAAO,CAACL,cAAc,CAAC;MAChC;MACA,MAAMM,iBAAiB,GAAGT,GAAG,CAAC,CAAC,CAACD,MAAM,CAACW,OAAO;MAC9ChB,SAAS,CAAC;QACRgB,OAAOA,CAACC,KAAK,EAAEhB,KAAK,EAAE;UACpB,MAAMiB,OAAO,GAAGjB,KAAK,CAACkB,IAAI,CAACC,KAAK,GAAG,CAAC;UACpC,MAAMC,OAAO,GAAGpB,KAAK,CAACkB,IAAI,CAACG,MAAM,GAAG,CAAC;UACrCrB,KAAK,CAACsB,OAAO,CAAChB,GAAG,CAACW,OAAO,GAAGjB,KAAK,CAACkB,IAAI,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEC,OAAO,GAAGpB,KAAK,CAACkB,IAAI,CAACG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC7FrB,KAAK,CAACuB,SAAS,CAACC,aAAa,CAACxB,KAAK,CAACsB,OAAO,EAAEtB,KAAK,CAACH,MAAM,CAAC;QAC5D;MACF,CAAC,CAAC;MACF,OAAO,MAAM;QACXa,QAAQ,CAACe,UAAU,CAAC,CAAC;QACrB1B,SAAS,CAAC;UACRgB,OAAO,EAAED;QACX,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,EAAE,CAACrB,OAAO,EAAEiB,QAAQ,CAAC,CAAC;EACvB1B,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,MAAMc,QAAQ,GAAGC,CAAC,IAAI;MACpBxB,UAAU,CAAC,CAAC;MACZ,IAAIb,QAAQ,EAAEA,QAAQ,CAACqC,CAAC,CAAC;IAC3B,CAAC;IACDjB,QAAQ,CAACkB,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC7C,IAAInC,MAAM,EAAEmB,QAAQ,CAACkB,gBAAgB,CAAC,MAAM,EAAErC,MAAM,CAAC;IACrD,IAAIC,QAAQ,EAAEkB,QAAQ,CAACkB,gBAAgB,CAAC,QAAQ,EAAEpC,QAAQ,CAAC;;IAE3D;IACA,MAAMqC,OAAO,GAAGA,CAAA,KAAMnB,QAAQ,CAACoB,IAAI,CAAC,CAAC;IACrC,MAAMC,QAAQ,GAAG1C,QAAQ,GAAG2C,KAAK,CAACC,IAAI,CAACC,QAAQ,CAACC,gBAAgB,CAAC9C,QAAQ,CAAC,CAAC,GAAG,CAAC6C,QAAQ,CAAC;IACxFH,QAAQ,CAACK,OAAO,CAACC,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACT,gBAAgB,CAAC,OAAO,EAAEC,OAAO,CAAC,CAAC;IAClF,OAAO,MAAM;MACXnB,QAAQ,CAAC4B,mBAAmB,CAAC,QAAQ,EAAEZ,QAAQ,CAAC;MAChD,IAAInC,MAAM,EAAEmB,QAAQ,CAAC4B,mBAAmB,CAAC,MAAM,EAAE/C,MAAM,CAAC;MACxD,IAAIC,QAAQ,EAAEkB,QAAQ,CAAC4B,mBAAmB,CAAC,QAAQ,EAAE9C,QAAQ,CAAC;MAC9DuC,QAAQ,CAACK,OAAO,CAACC,OAAO,IAAIA,OAAO,GAAGA,OAAO,CAACC,mBAAmB,CAAC,OAAO,EAAET,OAAO,CAAC,GAAGU,SAAS,CAAC;IAClG,CAAC;EACH,CAAC,EAAE,CAACjD,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEH,QAAQ,EAAEqB,QAAQ,EAAEP,UAAU,CAAC,CAAC;EAChEnB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIlB,WAAW,EAAE;MACf,MAAM8C,GAAG,GAAGnC,GAAG,CAAC,CAAC,CAACK,QAAQ;MAC1B;MACAJ,GAAG,CAAC;QACFI;MACF,CAAC,CAAC;MACF,OAAO,MAAMJ,GAAG,CAAC;QACfI,QAAQ,EAAE8B;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9C,WAAW,EAAEgB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAa1B,KAAK,CAACyD,aAAa,CAAC,WAAW,EAAE3D,QAAQ,CAAC;IAC5Dc,GAAG,EAAEA,GAAG;IACR8C,MAAM,EAAEhC;EACV,CAAC,EAAEZ,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASb,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}