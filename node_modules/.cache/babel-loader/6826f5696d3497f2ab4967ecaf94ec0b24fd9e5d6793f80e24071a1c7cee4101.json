{"ast": null, "code": "import * as React from 'react';\nimport { Object3D, AnimationMixer } from 'three';\nimport { useFrame } from '@react-three/fiber';\nfunction useAnimations(clips, root) {\n  const ref = React.useRef(null);\n  const [actualRef] = React.useState(() => root ? root instanceof Object3D ? {\n    current: root\n  } : root : ref);\n  const [mixer] = React.useState(() => new AnimationMixer(undefined));\n  React.useLayoutEffect(() => {\n    if (root) actualRef.current = root instanceof Object3D ? root : root.current;\n    mixer._root = actualRef.current;\n  });\n  const lazyActions = React.useRef({});\n  const api = React.useMemo(() => {\n    const actions = {};\n    clips.forEach(clip => Object.defineProperty(actions, clip.name, {\n      enumerable: true,\n      get() {\n        if (actualRef.current) {\n          return lazyActions.current[clip.name] || (lazyActions.current[clip.name] = mixer.clipAction(clip, actualRef.current));\n        }\n      },\n      configurable: true\n    }));\n    return {\n      ref: actualRef,\n      clips,\n      actions,\n      names: clips.map(c => c.name),\n      mixer\n    };\n  }, [clips]);\n  useFrame((state, delta) => mixer.update(delta));\n  React.useEffect(() => {\n    const currentRoot = actualRef.current;\n    return () => {\n      // Clean up only when clips change, wipe out lazy actions and uncache clips\n      lazyActions.current = {};\n      mixer.stopAllAction();\n      Object.values(api.actions).forEach(action => {\n        if (currentRoot) {\n          mixer.uncacheAction(action, currentRoot);\n        }\n      });\n    };\n  }, [clips]);\n  return api;\n}\nexport { useAnimations };", "map": {"version": 3, "names": ["React", "Object3D", "AnimationMixer", "useFrame", "useAnimations", "clips", "root", "ref", "useRef", "actualRef", "useState", "current", "mixer", "undefined", "useLayoutEffect", "_root", "lazyActions", "api", "useMemo", "actions", "for<PERSON>ach", "clip", "Object", "defineProperty", "name", "enumerable", "get", "clipAction", "configurable", "names", "map", "c", "state", "delta", "update", "useEffect", "currentRoot", "stopAllAction", "values", "action", "uncacheAction"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/useAnimations.js"], "sourcesContent": ["import * as React from 'react';\nimport { Object3D, AnimationMixer } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nfunction useAnimations(clips, root) {\n  const ref = React.useRef(null);\n  const [actualRef] = React.useState(() => root ? root instanceof Object3D ? {\n    current: root\n  } : root : ref);\n  const [mixer] = React.useState(() => new AnimationMixer(undefined));\n  React.useLayoutEffect(() => {\n    if (root) actualRef.current = root instanceof Object3D ? root : root.current;\n    mixer._root = actualRef.current;\n  });\n  const lazyActions = React.useRef({});\n  const api = React.useMemo(() => {\n    const actions = {};\n    clips.forEach(clip => Object.defineProperty(actions, clip.name, {\n      enumerable: true,\n      get() {\n        if (actualRef.current) {\n          return lazyActions.current[clip.name] || (lazyActions.current[clip.name] = mixer.clipAction(clip, actualRef.current));\n        }\n      },\n      configurable: true\n    }));\n    return {\n      ref: actualRef,\n      clips,\n      actions,\n      names: clips.map(c => c.name),\n      mixer\n    };\n  }, [clips]);\n  useFrame((state, delta) => mixer.update(delta));\n  React.useEffect(() => {\n    const currentRoot = actualRef.current;\n    return () => {\n      // Clean up only when clips change, wipe out lazy actions and uncache clips\n      lazyActions.current = {};\n      mixer.stopAllAction();\n      Object.values(api.actions).forEach(action => {\n        if (currentRoot) {\n          mixer.uncacheAction(action, currentRoot);\n        }\n      });\n    };\n  }, [clips]);\n  return api;\n}\n\nexport { useAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAChD,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAClC,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACC,SAAS,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,MAAMJ,IAAI,GAAGA,IAAI,YAAYL,QAAQ,GAAG;IACzEU,OAAO,EAAEL;EACX,CAAC,GAAGA,IAAI,GAAGC,GAAG,CAAC;EACf,MAAM,CAACK,KAAK,CAAC,GAAGZ,KAAK,CAACU,QAAQ,CAAC,MAAM,IAAIR,cAAc,CAACW,SAAS,CAAC,CAAC;EACnEb,KAAK,CAACc,eAAe,CAAC,MAAM;IAC1B,IAAIR,IAAI,EAAEG,SAAS,CAACE,OAAO,GAAGL,IAAI,YAAYL,QAAQ,GAAGK,IAAI,GAAGA,IAAI,CAACK,OAAO;IAC5EC,KAAK,CAACG,KAAK,GAAGN,SAAS,CAACE,OAAO;EACjC,CAAC,CAAC;EACF,MAAMK,WAAW,GAAGhB,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMS,GAAG,GAAGjB,KAAK,CAACkB,OAAO,CAAC,MAAM;IAC9B,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClBd,KAAK,CAACe,OAAO,CAACC,IAAI,IAAIC,MAAM,CAACC,cAAc,CAACJ,OAAO,EAAEE,IAAI,CAACG,IAAI,EAAE;MAC9DC,UAAU,EAAE,IAAI;MAChBC,GAAGA,CAAA,EAAG;QACJ,IAAIjB,SAAS,CAACE,OAAO,EAAE;UACrB,OAAOK,WAAW,CAACL,OAAO,CAACU,IAAI,CAACG,IAAI,CAAC,KAAKR,WAAW,CAACL,OAAO,CAACU,IAAI,CAACG,IAAI,CAAC,GAAGZ,KAAK,CAACe,UAAU,CAACN,IAAI,EAAEZ,SAAS,CAACE,OAAO,CAAC,CAAC;QACvH;MACF,CAAC;MACDiB,YAAY,EAAE;IAChB,CAAC,CAAC,CAAC;IACH,OAAO;MACLrB,GAAG,EAAEE,SAAS;MACdJ,KAAK;MACLc,OAAO;MACPU,KAAK,EAAExB,KAAK,CAACyB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACP,IAAI,CAAC;MAC7BZ;IACF,CAAC;EACH,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EACXF,QAAQ,CAAC,CAAC6B,KAAK,EAAEC,KAAK,KAAKrB,KAAK,CAACsB,MAAM,CAACD,KAAK,CAAC,CAAC;EAC/CjC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAG3B,SAAS,CAACE,OAAO;IACrC,OAAO,MAAM;MACX;MACAK,WAAW,CAACL,OAAO,GAAG,CAAC,CAAC;MACxBC,KAAK,CAACyB,aAAa,CAAC,CAAC;MACrBf,MAAM,CAACgB,MAAM,CAACrB,GAAG,CAACE,OAAO,CAAC,CAACC,OAAO,CAACmB,MAAM,IAAI;QAC3C,IAAIH,WAAW,EAAE;UACfxB,KAAK,CAAC4B,aAAa,CAACD,MAAM,EAAEH,WAAW,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC/B,KAAK,CAAC,CAAC;EACX,OAAOY,GAAG;AACZ;AAEA,SAASb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}