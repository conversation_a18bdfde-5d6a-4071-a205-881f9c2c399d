{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { WebGLRenderTarget, NearestFilter, MeshDepthMaterial, RGBADepthPacking, NoBlending, UniformsUtils, ShaderMaterial, Color } from \"three\";\nimport { BokehShader } from \"../shaders/BokehShader.js\";\nclass BokehPass extends Pass {\n  constructor(scene, camera, params) {\n    super();\n    __publicField(this, \"scene\");\n    __publicField(this, \"camera\");\n    __publicField(this, \"renderTargetDepth\");\n    __publicField(this, \"materialDepth\");\n    __publicField(this, \"materialBokeh\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"_oldClearColor\");\n    __publicField(this, \"uniforms\");\n    this.scene = scene;\n    this.camera = camera;\n    const focus = params.focus !== void 0 ? params.focus : 1;\n    const aspect = params.aspect !== void 0 ? params.aspect : camera.aspect;\n    const aperture = params.aperture !== void 0 ? params.aperture : 0.025;\n    const maxblur = params.maxblur !== void 0 ? params.maxblur : 1;\n    const width = params.width || window.innerWidth || 1;\n    const height = params.height || window.innerHeight || 1;\n    this.renderTargetDepth = new WebGLRenderTarget(width, height, {\n      minFilter: NearestFilter,\n      magFilter: NearestFilter\n    });\n    this.renderTargetDepth.texture.name = \"BokehPass.depth\";\n    this.materialDepth = new MeshDepthMaterial();\n    this.materialDepth.depthPacking = RGBADepthPacking;\n    this.materialDepth.blending = NoBlending;\n    if (BokehShader === void 0) {\n      console.error(\"BokehPass relies on BokehShader\");\n    }\n    const bokehShader = BokehShader;\n    const bokehUniforms = UniformsUtils.clone(bokehShader.uniforms);\n    bokehUniforms[\"tDepth\"].value = this.renderTargetDepth.texture;\n    bokehUniforms[\"focus\"].value = focus;\n    bokehUniforms[\"aspect\"].value = aspect;\n    bokehUniforms[\"aperture\"].value = aperture;\n    bokehUniforms[\"maxblur\"].value = maxblur;\n    bokehUniforms[\"nearClip\"].value = camera.near;\n    bokehUniforms[\"farClip\"].value = camera.far;\n    this.materialBokeh = new ShaderMaterial({\n      defines: Object.assign({}, bokehShader.defines),\n      uniforms: bokehUniforms,\n      vertexShader: bokehShader.vertexShader,\n      fragmentShader: bokehShader.fragmentShader\n    });\n    this.uniforms = bokehUniforms;\n    this.needsSwap = false;\n    this.fsQuad = new FullScreenQuad(this.materialBokeh);\n    this._oldClearColor = new Color();\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.scene.overrideMaterial = this.materialDepth;\n    renderer.getClearColor(this._oldClearColor);\n    const oldClearAlpha = renderer.getClearAlpha();\n    const oldAutoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    renderer.setClearColor(16777215);\n    renderer.setClearAlpha(1);\n    renderer.setRenderTarget(this.renderTargetDepth);\n    renderer.clear();\n    renderer.render(this.scene, this.camera);\n    this.uniforms[\"tColor\"].value = readBuffer.texture;\n    this.uniforms[\"nearClip\"].value = this.camera.near;\n    this.uniforms[\"farClip\"].value = this.camera.far;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n    this.scene.overrideMaterial = null;\n    renderer.setClearColor(this._oldClearColor);\n    renderer.setClearAlpha(oldClearAlpha);\n    renderer.autoClear = oldAutoClear;\n  }\n}\nexport { BokehPass };", "map": {"version": 3, "names": ["BokehPass", "Pass", "constructor", "scene", "camera", "params", "__publicField", "focus", "aspect", "aperture", "maxblur", "width", "window", "innerWidth", "height", "innerHeight", "renderTargetDepth", "WebGLRenderTarget", "minFilter", "NearestFilter", "magFilter", "texture", "name", "material<PERSON><PERSON>h", "MeshDepthMaterial", "depthPacking", "RGBADepthPacking", "blending", "NoBlending", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "error", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "bokehUniforms", "UniformsUtils", "clone", "uniforms", "value", "near", "far", "materialBokeh", "ShaderMaterial", "defines", "Object", "assign", "vertexShader", "fragmentShader", "needsSwap", "fsQuad", "FullScreenQuad", "_oldClearColor", "Color", "render", "renderer", "writeBuffer", "readBuffer", "overrideMaterial", "getClearColor", "oldClearAlpha", "getClearAlpha", "oldAutoClear", "autoClear", "setClearColor", "setClearAlpha", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "renderToScreen"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/BokehPass.ts"], "sourcesContent": ["/**\n * Depth-of-field post-process with bokeh shader\n */\n\nimport { Pass, FullScreenQuad } from './Pass'\nimport {\n  Color,\n  MeshDepthMaterial,\n  NearestFilter,\n  NoBlending,\n  PerspectiveCamera,\n  RGBADepthPacking,\n  Scene,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderer,\n  WebGLRenderTarget,\n} from 'three'\nimport { BokehShader } from '../shaders/BokehShader'\n\ntype BokehPassParams = {\n  focus?: number\n  aspect?: number\n  aperture?: number\n  maxblur?: number\n  width?: number\n  height?: number\n}\n\nclass BokehPass extends Pass {\n  public scene: Scene\n  public camera: PerspectiveCamera\n  public renderTargetDepth: WebGLRenderTarget\n  public materialDepth: MeshDepthMaterial\n  public materialBokeh: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  private _oldClearColor: Color\n\n  public uniforms\n\n  constructor(scene: Scene, camera: PerspectiveCamera, params: BokehPassParams) {\n    super()\n    this.scene = scene\n    this.camera = camera\n    const focus = params.focus !== undefined ? params.focus : 1.0\n    const aspect = params.aspect !== undefined ? params.aspect : camera.aspect\n    const aperture = params.aperture !== undefined ? params.aperture : 0.025\n    const maxblur = params.maxblur !== undefined ? params.maxblur : 1.0 // render targets\n\n    const width = params.width || window.innerWidth || 1\n    const height = params.height || window.innerHeight || 1\n    this.renderTargetDepth = new WebGLRenderTarget(width, height, {\n      minFilter: NearestFilter,\n      magFilter: NearestFilter,\n    })\n    this.renderTargetDepth.texture.name = 'BokehPass.depth' // depth material\n\n    this.materialDepth = new MeshDepthMaterial()\n    this.materialDepth.depthPacking = RGBADepthPacking\n    this.materialDepth.blending = NoBlending // bokeh material\n\n    if (BokehShader === undefined) {\n      console.error('BokehPass relies on BokehShader')\n    }\n\n    const bokehShader = BokehShader\n    const bokehUniforms = UniformsUtils.clone(bokehShader.uniforms)\n    bokehUniforms['tDepth'].value = this.renderTargetDepth.texture\n    bokehUniforms['focus'].value = focus\n    bokehUniforms['aspect'].value = aspect\n    bokehUniforms['aperture'].value = aperture\n    bokehUniforms['maxblur'].value = maxblur\n    bokehUniforms['nearClip'].value = camera.near\n    bokehUniforms['farClip'].value = camera.far\n    this.materialBokeh = new ShaderMaterial({\n      defines: Object.assign({}, bokehShader.defines),\n      uniforms: bokehUniforms,\n      vertexShader: bokehShader.vertexShader,\n      fragmentShader: bokehShader.fragmentShader,\n    })\n    this.uniforms = bokehUniforms\n    this.needsSwap = false\n    this.fsQuad = new FullScreenQuad(this.materialBokeh)\n    this._oldClearColor = new Color()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    // Render depth into texture\n    this.scene.overrideMaterial = this.materialDepth\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n    renderer.setClearColor(0xffffff)\n    renderer.setClearAlpha(1.0)\n    renderer.setRenderTarget(this.renderTargetDepth)\n    renderer.clear()\n    renderer.render(this.scene, this.camera) // Render bokeh composite\n\n    this.uniforms['tColor'].value = readBuffer.texture\n    this.uniforms['nearClip'].value = this.camera.near\n    this.uniforms['farClip'].value = this.camera.far\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n\n    this.scene.overrideMaterial = null\n    renderer.setClearColor(this._oldClearColor)\n    renderer.setClearAlpha(oldClearAlpha)\n    renderer.autoClear = oldAutoClear\n  }\n}\n\nexport { BokehPass }\n"], "mappings": ";;;;;;;;;;;;;;AA6BA,MAAMA,SAAA,SAAkBC,IAAA,CAAK;EAY3BC,YAAYC,KAAA,EAAcC,MAAA,EAA2BC,MAAA,EAAyB;IACtE;IAZDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAECA,aAAA;IAEDA,aAAA;IAIL,KAAKH,KAAA,GAAQA,KAAA;IACb,KAAKC,MAAA,GAASA,MAAA;IACd,MAAMG,KAAA,GAAQF,MAAA,CAAOE,KAAA,KAAU,SAAYF,MAAA,CAAOE,KAAA,GAAQ;IAC1D,MAAMC,MAAA,GAASH,MAAA,CAAOG,MAAA,KAAW,SAAYH,MAAA,CAAOG,MAAA,GAASJ,MAAA,CAAOI,MAAA;IACpE,MAAMC,QAAA,GAAWJ,MAAA,CAAOI,QAAA,KAAa,SAAYJ,MAAA,CAAOI,QAAA,GAAW;IACnE,MAAMC,OAAA,GAAUL,MAAA,CAAOK,OAAA,KAAY,SAAYL,MAAA,CAAOK,OAAA,GAAU;IAEhE,MAAMC,KAAA,GAAQN,MAAA,CAAOM,KAAA,IAASC,MAAA,CAAOC,UAAA,IAAc;IACnD,MAAMC,MAAA,GAAST,MAAA,CAAOS,MAAA,IAAUF,MAAA,CAAOG,WAAA,IAAe;IACtD,KAAKC,iBAAA,GAAoB,IAAIC,iBAAA,CAAkBN,KAAA,EAAOG,MAAA,EAAQ;MAC5DI,SAAA,EAAWC,aAAA;MACXC,SAAA,EAAWD;IAAA,CACZ;IACI,KAAAH,iBAAA,CAAkBK,OAAA,CAAQC,IAAA,GAAO;IAEjC,KAAAC,aAAA,GAAgB,IAAIC,iBAAA;IACzB,KAAKD,aAAA,CAAcE,YAAA,GAAeC,gBAAA;IAClC,KAAKH,aAAA,CAAcI,QAAA,GAAWC,UAAA;IAE9B,IAAIC,WAAA,KAAgB,QAAW;MAC7BC,OAAA,CAAQC,KAAA,CAAM,iCAAiC;IACjD;IAEA,MAAMC,WAAA,GAAcH,WAAA;IACpB,MAAMI,aAAA,GAAgBC,aAAA,CAAcC,KAAA,CAAMH,WAAA,CAAYI,QAAQ;IAC9DH,aAAA,CAAc,QAAQ,EAAEI,KAAA,GAAQ,KAAKrB,iBAAA,CAAkBK,OAAA;IACzCY,aAAA,QAAO,EAAEI,KAAA,GAAQ9B,KAAA;IACjB0B,aAAA,SAAQ,EAAEI,KAAA,GAAQ7B,MAAA;IAClByB,aAAA,WAAU,EAAEI,KAAA,GAAQ5B,QAAA;IACpBwB,aAAA,UAAS,EAAEI,KAAA,GAAQ3B,OAAA;IACnBuB,aAAA,WAAU,EAAEI,KAAA,GAAQjC,MAAA,CAAOkC,IAAA;IAC3BL,aAAA,UAAS,EAAEI,KAAA,GAAQjC,MAAA,CAAOmC,GAAA;IACnC,KAAAC,aAAA,GAAgB,IAAIC,cAAA,CAAe;MACtCC,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIZ,WAAA,CAAYU,OAAO;MAC9CN,QAAA,EAAUH,aAAA;MACVY,YAAA,EAAcb,WAAA,CAAYa,YAAA;MAC1BC,cAAA,EAAgBd,WAAA,CAAYc;IAAA,CAC7B;IACD,KAAKV,QAAA,GAAWH,aAAA;IAChB,KAAKc,SAAA,GAAY;IACjB,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKT,aAAa;IAC9C,KAAAU,cAAA,GAAiB,IAAIC,KAAA;EAC5B;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EAEM;IAED,KAAApD,KAAA,CAAMqD,gBAAA,GAAmB,KAAKjC,aAAA;IAC1B8B,QAAA,CAAAI,aAAA,CAAc,KAAKP,cAAc;IACpC,MAAAQ,aAAA,GAAgBL,QAAA,CAASM,aAAA;IAC/B,MAAMC,YAAA,GAAeP,QAAA,CAASQ,SAAA;IAC9BR,QAAA,CAASQ,SAAA,GAAY;IACrBR,QAAA,CAASS,aAAA,CAAc,QAAQ;IAC/BT,QAAA,CAASU,aAAA,CAAc,CAAG;IACjBV,QAAA,CAAAW,eAAA,CAAgB,KAAKhD,iBAAiB;IAC/CqC,QAAA,CAASY,KAAA,CAAM;IACfZ,QAAA,CAASD,MAAA,CAAO,KAAKjD,KAAA,EAAO,KAAKC,MAAM;IAEvC,KAAKgC,QAAA,CAAS,QAAQ,EAAEC,KAAA,GAAQkB,UAAA,CAAWlC,OAAA;IAC3C,KAAKe,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKjC,MAAA,CAAOkC,IAAA;IAC9C,KAAKF,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQ,KAAKjC,MAAA,CAAOmC,GAAA;IAE7C,IAAI,KAAK2B,cAAA,EAAgB;MACvBb,QAAA,CAASW,eAAA,CAAgB,IAAI;MACxB,KAAAhB,MAAA,CAAOI,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASW,eAAA,CAAgBV,WAAW;MACpCD,QAAA,CAASY,KAAA,CAAM;MACV,KAAAjB,MAAA,CAAOI,MAAA,CAAOC,QAAQ;IAC7B;IAEA,KAAKlD,KAAA,CAAMqD,gBAAA,GAAmB;IACrBH,QAAA,CAAAS,aAAA,CAAc,KAAKZ,cAAc;IAC1CG,QAAA,CAASU,aAAA,CAAcL,aAAa;IACpCL,QAAA,CAASQ,SAAA,GAAYD,YAAA;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}