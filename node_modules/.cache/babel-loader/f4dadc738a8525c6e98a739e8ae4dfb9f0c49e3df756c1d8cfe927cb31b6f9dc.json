{"ast": null, "code": "import * as React from 'react';\nimport { CubeTextureLoader } from 'three';\nimport { useLoader } from '@react-three/fiber';\nfunction useCubeTexture(files, {\n  path\n}) {\n  const [cubeTexture] = useLoader(CubeTextureLoader, [files], loader => loader.setPath(path));\n  return cubeTexture;\n}\nuseCubeTexture.preload = (files, {\n  path\n}) => useLoader.preload(CubeTextureLoader, [files], loader => loader.setPath(path));\nfunction CubeTexture({\n  children,\n  files,\n  ...options\n}) {\n  const texture = useCubeTexture(files, {\n    ...options\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n}\nexport { CubeTexture, useCubeTexture };", "map": {"version": 3, "names": ["React", "CubeTextureLoader", "useLoader", "useCubeTexture", "files", "path", "cubeTexture", "loader", "set<PERSON>ath", "preload", "CubeTexture", "children", "options", "texture", "createElement", "Fragment"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/CubeTexture.js"], "sourcesContent": ["import * as React from 'react';\nimport { CubeTextureLoader } from 'three';\nimport { useLoader } from '@react-three/fiber';\n\nfunction useCubeTexture(files, {\n  path\n}) {\n  const [cubeTexture] = useLoader(CubeTextureLoader, [files], loader => loader.setPath(path));\n  return cubeTexture;\n}\nuseCubeTexture.preload = (files, {\n  path\n}) => useLoader.preload(CubeTextureLoader, [files], loader => loader.setPath(path));\nfunction CubeTexture({\n  children,\n  files,\n  ...options\n}) {\n  const texture = useCubeTexture(files, {\n    ...options\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(texture));\n}\n\nexport { CubeTexture, useCubeTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,OAAO;AACzC,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7BC;AACF,CAAC,EAAE;EACD,MAAM,CAACC,WAAW,CAAC,GAAGJ,SAAS,CAACD,iBAAiB,EAAE,CAACG,KAAK,CAAC,EAAEG,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC;EAC3F,OAAOC,WAAW;AACpB;AACAH,cAAc,CAACM,OAAO,GAAG,CAACL,KAAK,EAAE;EAC/BC;AACF,CAAC,KAAKH,SAAS,CAACO,OAAO,CAACR,iBAAiB,EAAE,CAACG,KAAK,CAAC,EAAEG,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC;AACnF,SAASK,WAAWA,CAAC;EACnBC,QAAQ;EACRP,KAAK;EACL,GAAGQ;AACL,CAAC,EAAE;EACD,MAAMC,OAAO,GAAGV,cAAc,CAACC,KAAK,EAAE;IACpC,GAAGQ;EACL,CAAC,CAAC;EACF,OAAO,aAAaZ,KAAK,CAACc,aAAa,CAACd,KAAK,CAACe,QAAQ,EAAE,IAAI,EAAEJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,OAAO,CAAC,CAAC;AAC9G;AAEA,SAASH,WAAW,EAAEP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}