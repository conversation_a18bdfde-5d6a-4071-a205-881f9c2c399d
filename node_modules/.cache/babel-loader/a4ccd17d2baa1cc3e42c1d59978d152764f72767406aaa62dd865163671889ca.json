{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nconst ScreenSpace = /* @__PURE__ */React.forwardRef(({\n  children,\n  depth = -1,\n  ...rest\n}, ref) => {\n  const localRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => localRef.current, []);\n  useFrame(({\n    camera\n  }) => {\n    localRef.current.quaternion.copy(camera.quaternion);\n    localRef.current.position.copy(camera.position);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: localRef\n  }, rest), /*#__PURE__*/React.createElement(\"group\", {\n    \"position-z\": -depth\n  }, children));\n});\nexport { ScreenSpace };", "map": {"version": 3, "names": ["_extends", "React", "useFrame", "ScreenSpace", "forwardRef", "children", "depth", "rest", "ref", "localRef", "useRef", "useImperativeHandle", "current", "camera", "quaternion", "copy", "position", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/ScreenSpace.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\n\nconst ScreenSpace = /* @__PURE__ */React.forwardRef(({\n  children,\n  depth = -1,\n  ...rest\n}, ref) => {\n  const localRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => localRef.current, []);\n  useFrame(({\n    camera\n  }) => {\n    localRef.current.quaternion.copy(camera.quaternion);\n    localRef.current.position.copy(camera.position);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: localRef\n  }, rest), /*#__PURE__*/React.createElement(\"group\", {\n    \"position-z\": -depth\n  }, children));\n});\n\nexport { ScreenSpace };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,WAAW,GAAG,eAAeF,KAAK,CAACG,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,QAAQ,GAAGR,KAAK,CAACS,MAAM,CAAC,IAAI,CAAC;EACnCT,KAAK,CAACU,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAO,EAAE,EAAE,CAAC;EAC1DV,QAAQ,CAAC,CAAC;IACRW;EACF,CAAC,KAAK;IACJJ,QAAQ,CAACG,OAAO,CAACE,UAAU,CAACC,IAAI,CAACF,MAAM,CAACC,UAAU,CAAC;IACnDL,QAAQ,CAACG,OAAO,CAACI,QAAQ,CAACD,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC;EACjD,CAAC,CAAC;EACF,OAAO,aAAaf,KAAK,CAACgB,aAAa,CAAC,OAAO,EAAEjB,QAAQ,CAAC;IACxDQ,GAAG,EAAEC;EACP,CAAC,EAAEF,IAAI,CAAC,EAAE,aAAaN,KAAK,CAACgB,aAAa,CAAC,OAAO,EAAE;IAClD,YAAY,EAAE,CAACX;EACjB,CAAC,EAAED,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}