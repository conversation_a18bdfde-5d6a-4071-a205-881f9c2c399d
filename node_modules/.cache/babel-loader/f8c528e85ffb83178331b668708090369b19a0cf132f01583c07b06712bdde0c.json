{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { applyProps } from '@react-three/fiber';\nimport { DecalGeometry } from 'three-stdlib';\nfunction isArray(vec) {\n  return Array.isArray(vec);\n}\nfunction vecToArray(vec = [0, 0, 0]) {\n  if (isArray(vec)) {\n    return vec;\n  } else if (vec instanceof THREE.Vector3 || vec instanceof THREE.Euler) {\n    return [vec.x, vec.y, vec.z];\n  } else {\n    return [vec, vec, vec];\n  }\n}\nconst Decal = /* @__PURE__ */React.forwardRef(function Decal({\n  debug,\n  depthTest = false,\n  polygonOffsetFactor = -10,\n  map,\n  mesh,\n  children,\n  position,\n  rotation,\n  scale,\n  ...props\n}, forwardRef) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current);\n  const helper = React.useRef(null);\n  const state = React.useRef({\n    position: new THREE.Vector3(),\n    rotation: new THREE.Euler(),\n    scale: new THREE.Vector3(1, 1, 1)\n  });\n  React.useLayoutEffect(() => {\n    const parent = (mesh == null ? void 0 : mesh.current) || ref.current.parent;\n    const target = ref.current;\n    if (!(parent instanceof THREE.Mesh)) {\n      throw new Error('Decal must have a Mesh as parent or specify its \"mesh\" prop');\n    }\n    if (parent) {\n      applyProps(state.current, {\n        position,\n        scale\n      });\n\n      // Zero out the parents matrix world for this operation\n      const matrixWorld = parent.matrixWorld.clone();\n      parent.matrixWorld.identity();\n      if (!rotation || typeof rotation === 'number') {\n        const o = new THREE.Object3D();\n        o.position.copy(state.current.position);\n\n        // Thanks https://x.com/N8Programs !\n        const vertices = parent.geometry.attributes.position.array;\n        if (parent.geometry.attributes.normal === undefined) parent.geometry.computeVertexNormals();\n        const normal = parent.geometry.attributes.normal.array;\n        let distance = Infinity;\n        let closestNormal = new THREE.Vector3();\n        const ox = o.position.x;\n        const oy = o.position.y;\n        const oz = o.position.z;\n        const vLength = vertices.length;\n        let chosenIdx = -1;\n        for (let i = 0; i < vLength; i += 3) {\n          const x = vertices[i];\n          const y = vertices[i + 1];\n          const z = vertices[i + 2];\n          const xDiff = x - ox;\n          const yDiff = y - oy;\n          const zDiff = z - oz;\n          const distSquared = xDiff * xDiff + yDiff * yDiff + zDiff * zDiff;\n          if (distSquared < distance) {\n            distance = distSquared;\n            chosenIdx = i;\n          }\n        }\n        closestNormal.fromArray(normal, chosenIdx);\n\n        // Get vector tangent to normal\n        o.lookAt(o.position.clone().add(closestNormal));\n        o.rotateZ(Math.PI);\n        o.rotateY(Math.PI);\n        if (typeof rotation === 'number') o.rotateZ(rotation);\n        applyProps(state.current, {\n          rotation: o.rotation\n        });\n      } else {\n        applyProps(state.current, {\n          rotation\n        });\n      }\n      if (helper.current) {\n        applyProps(helper.current, state.current);\n      }\n      target.geometry = new DecalGeometry(parent, state.current.position, state.current.rotation, state.current.scale);\n      // Reset parents matix-world\n      parent.matrixWorld = matrixWorld;\n      return () => {\n        target.geometry.dispose();\n      };\n    }\n  }, [mesh, ...vecToArray(position), ...vecToArray(scale), ...vecToArray(rotation)]);\n  React.useLayoutEffect(() => {\n    if (helper.current) {\n      // Prevent the helpers from blocking rays\n      helper.current.traverse(child => child.raycast = () => null);\n    }\n  }, [debug]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    \"material-transparent\": true,\n    \"material-polygonOffset\": true,\n    \"material-polygonOffsetFactor\": polygonOffsetFactor,\n    \"material-depthTest\": depthTest,\n    \"material-map\": map\n  }, props), children, debug && /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: helper\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", null), /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }), /*#__PURE__*/React.createElement(\"axesHelper\", null)));\n});\nexport { Decal };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "applyProps", "DecalGeometry", "isArray", "vec", "Array", "vecToArray", "Vector3", "<PERSON>uler", "x", "y", "z", "Decal", "forwardRef", "debug", "depthTest", "polygonOffsetFactor", "map", "mesh", "children", "position", "rotation", "scale", "props", "ref", "useRef", "useImperativeHandle", "current", "helper", "state", "useLayoutEffect", "parent", "target", "<PERSON><PERSON>", "Error", "matrixWorld", "clone", "identity", "o", "Object3D", "copy", "vertices", "geometry", "attributes", "array", "normal", "undefined", "computeVertexNormals", "distance", "Infinity", "closestNormal", "ox", "oy", "oz", "vLength", "length", "chosenIdx", "i", "xDiff", "yDiff", "zDiff", "distSquared", "fromArray", "lookAt", "add", "rotateZ", "Math", "PI", "rotateY", "dispose", "traverse", "child", "raycast", "createElement", "wireframe"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Decal.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { applyProps } from '@react-three/fiber';\nimport { DecalGeometry } from 'three-stdlib';\n\nfunction isArray(vec) {\n  return Array.isArray(vec);\n}\nfunction vecToArray(vec = [0, 0, 0]) {\n  if (isArray(vec)) {\n    return vec;\n  } else if (vec instanceof THREE.Vector3 || vec instanceof THREE.Euler) {\n    return [vec.x, vec.y, vec.z];\n  } else {\n    return [vec, vec, vec];\n  }\n}\nconst Decal = /* @__PURE__ */React.forwardRef(function Decal({\n  debug,\n  depthTest = false,\n  polygonOffsetFactor = -10,\n  map,\n  mesh,\n  children,\n  position,\n  rotation,\n  scale,\n  ...props\n}, forwardRef) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current);\n  const helper = React.useRef(null);\n  const state = React.useRef({\n    position: new THREE.Vector3(),\n    rotation: new THREE.Euler(),\n    scale: new THREE.Vector3(1, 1, 1)\n  });\n  React.useLayoutEffect(() => {\n    const parent = (mesh == null ? void 0 : mesh.current) || ref.current.parent;\n    const target = ref.current;\n    if (!(parent instanceof THREE.Mesh)) {\n      throw new Error('Decal must have a Mesh as parent or specify its \"mesh\" prop');\n    }\n    if (parent) {\n      applyProps(state.current, {\n        position,\n        scale\n      });\n\n      // Zero out the parents matrix world for this operation\n      const matrixWorld = parent.matrixWorld.clone();\n      parent.matrixWorld.identity();\n      if (!rotation || typeof rotation === 'number') {\n        const o = new THREE.Object3D();\n        o.position.copy(state.current.position);\n\n        // Thanks https://x.com/N8Programs !\n        const vertices = parent.geometry.attributes.position.array;\n        if (parent.geometry.attributes.normal === undefined) parent.geometry.computeVertexNormals();\n        const normal = parent.geometry.attributes.normal.array;\n        let distance = Infinity;\n        let closestNormal = new THREE.Vector3();\n        const ox = o.position.x;\n        const oy = o.position.y;\n        const oz = o.position.z;\n        const vLength = vertices.length;\n        let chosenIdx = -1;\n        for (let i = 0; i < vLength; i += 3) {\n          const x = vertices[i];\n          const y = vertices[i + 1];\n          const z = vertices[i + 2];\n          const xDiff = x - ox;\n          const yDiff = y - oy;\n          const zDiff = z - oz;\n          const distSquared = xDiff * xDiff + yDiff * yDiff + zDiff * zDiff;\n          if (distSquared < distance) {\n            distance = distSquared;\n            chosenIdx = i;\n          }\n        }\n        closestNormal.fromArray(normal, chosenIdx);\n\n        // Get vector tangent to normal\n        o.lookAt(o.position.clone().add(closestNormal));\n        o.rotateZ(Math.PI);\n        o.rotateY(Math.PI);\n        if (typeof rotation === 'number') o.rotateZ(rotation);\n        applyProps(state.current, {\n          rotation: o.rotation\n        });\n      } else {\n        applyProps(state.current, {\n          rotation\n        });\n      }\n      if (helper.current) {\n        applyProps(helper.current, state.current);\n      }\n      target.geometry = new DecalGeometry(parent, state.current.position, state.current.rotation, state.current.scale);\n      // Reset parents matix-world\n      parent.matrixWorld = matrixWorld;\n      return () => {\n        target.geometry.dispose();\n      };\n    }\n  }, [mesh, ...vecToArray(position), ...vecToArray(scale), ...vecToArray(rotation)]);\n  React.useLayoutEffect(() => {\n    if (helper.current) {\n      // Prevent the helpers from blocking rays\n      helper.current.traverse(child => child.raycast = () => null);\n    }\n  }, [debug]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    \"material-transparent\": true,\n    \"material-polygonOffset\": true,\n    \"material-polygonOffsetFactor\": polygonOffsetFactor,\n    \"material-depthTest\": depthTest,\n    \"material-map\": map\n  }, props), children, debug && /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: helper\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", null), /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }), /*#__PURE__*/React.createElement(\"axesHelper\", null)));\n});\n\nexport { Decal };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B;AACA,SAASE,UAAUA,CAACF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;EACnC,IAAID,OAAO,CAACC,GAAG,CAAC,EAAE;IAChB,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,YAAYJ,KAAK,CAACO,OAAO,IAAIH,GAAG,YAAYJ,KAAK,CAACQ,KAAK,EAAE;IACrE,OAAO,CAACJ,GAAG,CAACK,CAAC,EAAEL,GAAG,CAACM,CAAC,EAAEN,GAAG,CAACO,CAAC,CAAC;EAC9B,CAAC,MAAM;IACL,OAAO,CAACP,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EACxB;AACF;AACA,MAAMQ,KAAK,GAAG,eAAeb,KAAK,CAACc,UAAU,CAAC,SAASD,KAAKA,CAAC;EAC3DE,KAAK;EACLC,SAAS,GAAG,KAAK;EACjBC,mBAAmB,GAAG,CAAC,EAAE;EACzBC,GAAG;EACHC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEV,UAAU,EAAE;EACb,MAAMW,GAAG,GAAGzB,KAAK,CAAC0B,MAAM,CAAC,IAAI,CAAC;EAC9B1B,KAAK,CAAC2B,mBAAmB,CAACb,UAAU,EAAE,MAAMW,GAAG,CAACG,OAAO,CAAC;EACxD,MAAMC,MAAM,GAAG7B,KAAK,CAAC0B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMI,KAAK,GAAG9B,KAAK,CAAC0B,MAAM,CAAC;IACzBL,QAAQ,EAAE,IAAIpB,KAAK,CAACO,OAAO,CAAC,CAAC;IAC7Bc,QAAQ,EAAE,IAAIrB,KAAK,CAACQ,KAAK,CAAC,CAAC;IAC3Bc,KAAK,EAAE,IAAItB,KAAK,CAACO,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAClC,CAAC,CAAC;EACFR,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1B,MAAMC,MAAM,GAAG,CAACb,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,OAAO,KAAKH,GAAG,CAACG,OAAO,CAACI,MAAM;IAC3E,MAAMC,MAAM,GAAGR,GAAG,CAACG,OAAO;IAC1B,IAAI,EAAEI,MAAM,YAAY/B,KAAK,CAACiC,IAAI,CAAC,EAAE;MACnC,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;IAChF;IACA,IAAIH,MAAM,EAAE;MACV9B,UAAU,CAAC4B,KAAK,CAACF,OAAO,EAAE;QACxBP,QAAQ;QACRE;MACF,CAAC,CAAC;;MAEF;MACA,MAAMa,WAAW,GAAGJ,MAAM,CAACI,WAAW,CAACC,KAAK,CAAC,CAAC;MAC9CL,MAAM,CAACI,WAAW,CAACE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAChB,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC7C,MAAMiB,CAAC,GAAG,IAAItC,KAAK,CAACuC,QAAQ,CAAC,CAAC;QAC9BD,CAAC,CAAClB,QAAQ,CAACoB,IAAI,CAACX,KAAK,CAACF,OAAO,CAACP,QAAQ,CAAC;;QAEvC;QACA,MAAMqB,QAAQ,GAAGV,MAAM,CAACW,QAAQ,CAACC,UAAU,CAACvB,QAAQ,CAACwB,KAAK;QAC1D,IAAIb,MAAM,CAACW,QAAQ,CAACC,UAAU,CAACE,MAAM,KAAKC,SAAS,EAAEf,MAAM,CAACW,QAAQ,CAACK,oBAAoB,CAAC,CAAC;QAC3F,MAAMF,MAAM,GAAGd,MAAM,CAACW,QAAQ,CAACC,UAAU,CAACE,MAAM,CAACD,KAAK;QACtD,IAAII,QAAQ,GAAGC,QAAQ;QACvB,IAAIC,aAAa,GAAG,IAAIlD,KAAK,CAACO,OAAO,CAAC,CAAC;QACvC,MAAM4C,EAAE,GAAGb,CAAC,CAAClB,QAAQ,CAACX,CAAC;QACvB,MAAM2C,EAAE,GAAGd,CAAC,CAAClB,QAAQ,CAACV,CAAC;QACvB,MAAM2C,EAAE,GAAGf,CAAC,CAAClB,QAAQ,CAACT,CAAC;QACvB,MAAM2C,OAAO,GAAGb,QAAQ,CAACc,MAAM;QAC/B,IAAIC,SAAS,GAAG,CAAC,CAAC;QAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,IAAI,CAAC,EAAE;UACnC,MAAMhD,CAAC,GAAGgC,QAAQ,CAACgB,CAAC,CAAC;UACrB,MAAM/C,CAAC,GAAG+B,QAAQ,CAACgB,CAAC,GAAG,CAAC,CAAC;UACzB,MAAM9C,CAAC,GAAG8B,QAAQ,CAACgB,CAAC,GAAG,CAAC,CAAC;UACzB,MAAMC,KAAK,GAAGjD,CAAC,GAAG0C,EAAE;UACpB,MAAMQ,KAAK,GAAGjD,CAAC,GAAG0C,EAAE;UACpB,MAAMQ,KAAK,GAAGjD,CAAC,GAAG0C,EAAE;UACpB,MAAMQ,WAAW,GAAGH,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK;UACjE,IAAIC,WAAW,GAAGb,QAAQ,EAAE;YAC1BA,QAAQ,GAAGa,WAAW;YACtBL,SAAS,GAAGC,CAAC;UACf;QACF;QACAP,aAAa,CAACY,SAAS,CAACjB,MAAM,EAAEW,SAAS,CAAC;;QAE1C;QACAlB,CAAC,CAACyB,MAAM,CAACzB,CAAC,CAAClB,QAAQ,CAACgB,KAAK,CAAC,CAAC,CAAC4B,GAAG,CAACd,aAAa,CAAC,CAAC;QAC/CZ,CAAC,CAAC2B,OAAO,CAACC,IAAI,CAACC,EAAE,CAAC;QAClB7B,CAAC,CAAC8B,OAAO,CAACF,IAAI,CAACC,EAAE,CAAC;QAClB,IAAI,OAAO9C,QAAQ,KAAK,QAAQ,EAAEiB,CAAC,CAAC2B,OAAO,CAAC5C,QAAQ,CAAC;QACrDpB,UAAU,CAAC4B,KAAK,CAACF,OAAO,EAAE;UACxBN,QAAQ,EAAEiB,CAAC,CAACjB;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpB,UAAU,CAAC4B,KAAK,CAACF,OAAO,EAAE;UACxBN;QACF,CAAC,CAAC;MACJ;MACA,IAAIO,MAAM,CAACD,OAAO,EAAE;QAClB1B,UAAU,CAAC2B,MAAM,CAACD,OAAO,EAAEE,KAAK,CAACF,OAAO,CAAC;MAC3C;MACAK,MAAM,CAACU,QAAQ,GAAG,IAAIxC,aAAa,CAAC6B,MAAM,EAAEF,KAAK,CAACF,OAAO,CAACP,QAAQ,EAAES,KAAK,CAACF,OAAO,CAACN,QAAQ,EAAEQ,KAAK,CAACF,OAAO,CAACL,KAAK,CAAC;MAChH;MACAS,MAAM,CAACI,WAAW,GAAGA,WAAW;MAChC,OAAO,MAAM;QACXH,MAAM,CAACU,QAAQ,CAAC2B,OAAO,CAAC,CAAC;MAC3B,CAAC;IACH;EACF,CAAC,EAAE,CAACnD,IAAI,EAAE,GAAGZ,UAAU,CAACc,QAAQ,CAAC,EAAE,GAAGd,UAAU,CAACgB,KAAK,CAAC,EAAE,GAAGhB,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC;EAClFtB,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1B,IAAIF,MAAM,CAACD,OAAO,EAAE;MAClB;MACAC,MAAM,CAACD,OAAO,CAAC2C,QAAQ,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,IAAI,CAAC;IAC9D;EACF,CAAC,EAAE,CAAC1D,KAAK,CAAC,CAAC;EACX,OAAO,aAAaf,KAAK,CAAC0E,aAAa,CAAC,MAAM,EAAE3E,QAAQ,CAAC;IACvD0B,GAAG,EAAEA,GAAG;IACR,sBAAsB,EAAE,IAAI;IAC5B,wBAAwB,EAAE,IAAI;IAC9B,8BAA8B,EAAER,mBAAmB;IACnD,oBAAoB,EAAED,SAAS;IAC/B,cAAc,EAAEE;EAClB,CAAC,EAAEM,KAAK,CAAC,EAAEJ,QAAQ,EAAEL,KAAK,IAAI,aAAaf,KAAK,CAAC0E,aAAa,CAAC,MAAM,EAAE;IACrEjD,GAAG,EAAEI;EACP,CAAC,EAAE,aAAa7B,KAAK,CAAC0E,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,aAAa1E,KAAK,CAAC0E,aAAa,CAAC,oBAAoB,EAAE;IAC/GC,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAa3E,KAAK,CAAC0E,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF,SAAS7D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}