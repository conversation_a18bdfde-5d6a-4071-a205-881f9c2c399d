{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { UniformsUtils, ShaderMaterial } from \"three\";\nimport { HalftoneShader } from \"../shaders/HalftoneShader.js\";\nclass HalftonePass extends Pass {\n  constructor(width, height, params) {\n    super();\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"uniforms\");\n    if (HalftoneShader === void 0) {\n      console.error(\"THREE.HalftonePass requires HalftoneShader\");\n    }\n    this.uniforms = UniformsUtils.clone(HalftoneShader.uniforms);\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      fragmentShader: HalftoneShader.fragmentShader,\n      vertexShader: HalftoneShader.vertexShader\n    });\n    this.uniforms.width.value = width;\n    this.uniforms.height.value = height;\n    for (const key in params) {\n      if (params.hasOwnProperty(key) && this.uniforms.hasOwnProperty(key)) {\n        this.uniforms[key].value = params[key];\n      }\n    }\n    this.fsQuad = new FullScreenQuad(this.material);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.material.uniforms[\"tDiffuse\"].value = readBuffer.texture;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n  setSize(width, height) {\n    this.uniforms.width.value = width;\n    this.uniforms.height.value = height;\n  }\n}\nexport { HalftonePass };", "map": {"version": 3, "names": ["HalftonePass", "Pass", "constructor", "width", "height", "params", "__publicField", "HalftoneShader", "console", "error", "uniforms", "UniformsUtils", "clone", "material", "ShaderMaterial", "fragmentShader", "vertexShader", "value", "key", "hasOwnProperty", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "texture", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "setSize"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/postprocessing/HalftonePass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { ShaderMaterial, UniformsUtils, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { HalftoneShader } from '../shaders/HalftoneShader'\n\ntype HalftonePassParams = {\n  shape?: number\n  radius?: number\n  rotateR?: number\n  rotateB?: number\n  rotateG?: number\n  scatter?: number\n  blending?: number\n  blendingMode?: number\n  greyscale?: number\n  disable?: number\n}\n\n/**\n * RGB Halftone pass for three.js effects composer. Requires HalftoneShader.\n */\n\nclass HalftonePass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: any\n\n  constructor(width: number, height: number, params: HalftonePassParams) {\n    super()\n\n    if (HalftoneShader === undefined) {\n      console.error('THREE.HalftonePass requires HalftoneShader')\n    }\n\n    this.uniforms = UniformsUtils.clone(HalftoneShader.uniforms)\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      fragmentShader: HalftoneShader.fragmentShader,\n      vertexShader: HalftoneShader.vertexShader,\n    })\n\n    // set params\n    this.uniforms.width.value = width\n    this.uniforms.height.value = height\n\n    for (const key in params) {\n      if (params.hasOwnProperty(key) && this.uniforms.hasOwnProperty(key)) {\n        this.uniforms[key].value = params[key as keyof HalftonePassParams]\n      }\n    }\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.material.uniforms['tDiffuse'].value = readBuffer.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public setSize(width: number, height: number): void {\n    this.uniforms.width.value = width\n    this.uniforms.height.value = height\n  }\n}\n\nexport { HalftonePass }\n"], "mappings": ";;;;;;;;;;;;;;AAqBA,MAAMA,YAAA,SAAqBC,IAAA,CAAK;EAM9BC,YAAYC,KAAA,EAAeC,MAAA,EAAgBC,MAAA,EAA4B;IAC/D;IANDC,aAAA;IACAA,aAAA;IAEAA,aAAA;IAKL,IAAIC,cAAA,KAAmB,QAAW;MAChCC,OAAA,CAAQC,KAAA,CAAM,4CAA4C;IAC5D;IAEA,KAAKC,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAML,cAAA,CAAeG,QAAQ;IACtD,KAAAG,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCJ,QAAA,EAAU,KAAKA,QAAA;MACfK,cAAA,EAAgBR,cAAA,CAAeQ,cAAA;MAC/BC,YAAA,EAAcT,cAAA,CAAeS;IAAA,CAC9B;IAGI,KAAAN,QAAA,CAASP,KAAA,CAAMc,KAAA,GAAQd,KAAA;IACvB,KAAAO,QAAA,CAASN,MAAA,CAAOa,KAAA,GAAQb,MAAA;IAE7B,WAAWc,GAAA,IAAOb,MAAA,EAAQ;MACpB,IAAAA,MAAA,CAAOc,cAAA,CAAeD,GAAG,KAAK,KAAKR,QAAA,CAASS,cAAA,CAAeD,GAAG,GAAG;QACnE,KAAKR,QAAA,CAASQ,GAAG,EAAED,KAAA,GAAQZ,MAAA,CAAOa,GAA+B;MACnE;IACF;IAEA,KAAKE,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKR,QAAQ;EAChD;EAEOS,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EAEM;IACN,KAAKZ,QAAA,CAASH,QAAA,CAAS,UAAU,EAAEO,KAAA,GAAQQ,UAAA,CAAWC,OAAA;IAEtD,IAAI,KAAKC,cAAA,EAAgB;MACvBJ,QAAA,CAASK,eAAA,CAAgB,IAAI;MACxB,KAAAR,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASK,eAAA,CAAgBJ,WAAW;MACpC,IAAI,KAAKK,KAAA,EAAON,QAAA,CAASM,KAAA,CAAM;MAC1B,KAAAT,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC7B;EACF;EAEOO,QAAQ3B,KAAA,EAAeC,MAAA,EAAsB;IAC7C,KAAAM,QAAA,CAASP,KAAA,CAAMc,KAAA,GAAQd,KAAA;IACvB,KAAAO,QAAA,CAASN,MAAA,CAAOa,KAAA,GAAQb,MAAA;EAC/B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}