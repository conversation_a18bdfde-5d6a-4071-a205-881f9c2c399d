{"ast": null, "code": "import * as React from 'react';\nconst context = /* @__PURE__ */React.createContext(null);\nexport { context };", "map": {"version": 3, "names": ["React", "context", "createContext"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/web/pivotControls/context.js"], "sourcesContent": ["import * as React from 'react';\n\nconst context = /* @__PURE__ */React.createContext(null);\n\nexport { context };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,OAAO,GAAG,eAAeD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAExD,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}