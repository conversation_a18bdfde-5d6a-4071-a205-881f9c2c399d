{"ast": null, "code": "const RGBShiftShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    amount: {\n      value: 5e-3\n    },\n    angle: {\n      value: 0\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform float amount;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 offset = amount * vec2( cos(angle), sin(angle));\n    \tvec4 cr = texture2D(tDiffuse, vUv + offset);\n    \tvec4 cga = texture2D(tDiffuse, vUv);\n    \tvec4 cb = texture2D(tDiffuse, vUv - offset);\n    \tgl_FragColor = vec4(cr.r, cga.g, cb.b, cga.a);\n\n    }\n  `)\n};\nexport { RGBShiftShader };", "map": {"version": 3, "names": ["RGBShiftShader", "uniforms", "tDiffuse", "value", "amount", "angle", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/shaders/RGBShiftShader.ts"], "sourcesContent": ["/**\n * RGB Shift Shader\n * Shifts red and blue channels from center in opposite directions\n * Ported from http://kriss.cx/tom/2009/05/rgb-shift/\n * by <PERSON> / http://kriss.cx/tom/\n *\n * amount: shift distance (1 is width of input)\n * angle: shift angle in radians\n */\n\nexport const RGBShiftShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    amount: { value: 0.005 },\n    angle: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float amount;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 offset = amount * vec2( cos(angle), sin(angle));\n    \tvec4 cr = texture2D(tDiffuse, vUv + offset);\n    \tvec4 cga = texture2D(tDiffuse, vUv);\n    \tvec4 cb = texture2D(tDiffuse, vUv - offset);\n    \tgl_FragColor = vec4(cr.r, cga.g, cb.b, cga.a);\n\n    }\n  `,\n}\n"], "mappings": "AAUO,MAAMA,cAAA,GAAiB;EAC5BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,MAAA,EAAQ;MAAED,KAAA,EAAO;IAAM;IACvBE,KAAA,EAAO;MAAEF,KAAA,EAAO;IAAI;EACtB;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}