{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { Box3, MathUtils, Matrix4, Quaternion, Raycaster, Sphere, Spherical, Vector2, Vector3, Vector4 } from 'three';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport CameraControlsImpl from 'camera-controls';\nexport { default as CameraControlsImpl } from 'camera-controls';\nconst CameraControls = /* @__PURE__ */forwardRef((props, ref) => {\n  const {\n    impl: SubclassImpl,\n    camera,\n    domElement,\n    makeDefault,\n    onControlStart,\n    onControl,\n    onControlEnd,\n    onTransitionStart,\n    onUpdate,\n    onWake,\n    onRest,\n    onSleep,\n    onStart,\n    onEnd,\n    onChange,\n    regress,\n    ...restProps\n  } = props;\n  const Impl = SubclassImpl !== null && SubclassImpl !== void 0 ? SubclassImpl : CameraControlsImpl;\n\n  // useMemo is used here instead of useEffect, otherwise the useMemo below runs first and throws\n  useMemo(() => {\n    // to allow for tree shaking, we only import the subset of THREE that is used by camera-controls\n    // see https://github.com/yomotsu/camera-controls#important\n    const subsetOfTHREE = {\n      Box3,\n      MathUtils: {\n        clamp: MathUtils.clamp\n      },\n      Matrix4,\n      Quaternion,\n      Raycaster,\n      Sphere,\n      Spherical,\n      Vector2,\n      Vector3,\n      Vector4\n    };\n    Impl.install({\n      THREE: subsetOfTHREE\n    });\n    extend({\n      CameraControlsImpl: Impl\n    });\n  }, [Impl]);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new Impl(explCamera), [Impl, explCamera]);\n  useFrame((state, delta) => {\n    controls.update(delta);\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.disconnect();\n  }, [explDomElement, controls]);\n  useEffect(() => {\n    function invalidateAndRegress() {\n      invalidate();\n      if (regress) performance.regress();\n    }\n    const handleControlStart = e => {\n      invalidateAndRegress();\n      onControlStart == null || onControlStart(e);\n      onStart == null || onStart(e); // backwards compatibility\n    };\n    const handleControl = e => {\n      invalidateAndRegress();\n      onControl == null || onControl(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleControlEnd = e => {\n      onControlEnd == null || onControlEnd(e);\n      onEnd == null || onEnd(e); // backwards compatibility\n    };\n    const handleTransitionStart = e => {\n      invalidateAndRegress();\n      onTransitionStart == null || onTransitionStart(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleUpdate = e => {\n      invalidateAndRegress();\n      onUpdate == null || onUpdate(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleWake = e => {\n      invalidateAndRegress();\n      onWake == null || onWake(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleRest = e => {\n      onRest == null || onRest(e);\n    };\n    const handleSleep = e => {\n      onSleep == null || onSleep(e);\n    };\n    controls.addEventListener('controlstart', handleControlStart);\n    controls.addEventListener('control', handleControl);\n    controls.addEventListener('controlend', handleControlEnd);\n    controls.addEventListener('transitionstart', handleTransitionStart);\n    controls.addEventListener('update', handleUpdate);\n    controls.addEventListener('wake', handleWake);\n    controls.addEventListener('rest', handleRest);\n    controls.addEventListener('sleep', handleSleep);\n    return () => {\n      controls.removeEventListener('controlstart', handleControlStart);\n      controls.removeEventListener('control', handleControl);\n      controls.removeEventListener('controlend', handleControlEnd);\n      controls.removeEventListener('transitionstart', handleTransitionStart);\n      controls.removeEventListener('update', handleUpdate);\n      controls.removeEventListener('wake', handleWake);\n      controls.removeEventListener('rest', handleRest);\n      controls.removeEventListener('sleep', handleSleep);\n    };\n  }, [controls, invalidate, setEvents, regress, performance, onControlStart, onControl, onControlEnd, onTransitionStart, onUpdate, onWake, onRest, onSleep, onChange, onStart, onEnd]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\nexport { CameraControls };", "map": {"version": 3, "names": ["_extends", "Box3", "MathUtils", "Matrix4", "Quaternion", "Raycaster", "Sphere", "Spherical", "Vector2", "Vector3", "Vector4", "React", "forwardRef", "useMemo", "useEffect", "extend", "useThree", "useFrame", "CameraControlsImpl", "default", "CameraControls", "props", "ref", "impl", "SubclassImpl", "camera", "dom<PERSON>lement", "makeDefault", "onControlStart", "onControl", "onControlEnd", "onTransitionStart", "onUpdate", "onWake", "onRest", "onSleep", "onStart", "onEnd", "onChange", "regress", "restProps", "Impl", "subsetOfTHREE", "clamp", "install", "THREE", "defaultCamera", "state", "gl", "invalidate", "events", "setEvents", "set", "get", "performance", "explCamera", "explDomElement", "connected", "controls", "delta", "update", "connect", "disconnect", "invalidateAndRegress", "handleControlStart", "e", "handleControl", "handleControlEnd", "handleTransitionStart", "handleUpdate", "handleWake", "handleRest", "handleSleep", "addEventListener", "removeEventListener", "old", "createElement", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/CameraControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { Box3, MathUtils, Matrix4, Quaternion, Raycaster, Sphere, Spherical, Vector2, Vector3, Vector4 } from 'three';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport CameraControlsImpl from 'camera-controls';\nexport { default as CameraControlsImpl } from 'camera-controls';\n\nconst CameraControls = /* @__PURE__ */forwardRef((props, ref) => {\n  const {\n    impl: SubclassImpl,\n    camera,\n    domElement,\n    makeDefault,\n    onControlStart,\n    onControl,\n    onControlEnd,\n    onTransitionStart,\n    onUpdate,\n    onWake,\n    onRest,\n    onSleep,\n    onStart,\n    onEnd,\n    onChange,\n    regress,\n    ...restProps\n  } = props;\n  const Impl = SubclassImpl !== null && SubclassImpl !== void 0 ? SubclassImpl : CameraControlsImpl;\n\n  // useMemo is used here instead of useEffect, otherwise the useMemo below runs first and throws\n  useMemo(() => {\n    // to allow for tree shaking, we only import the subset of THREE that is used by camera-controls\n    // see https://github.com/yomotsu/camera-controls#important\n    const subsetOfTHREE = {\n      Box3,\n      MathUtils: {\n        clamp: MathUtils.clamp\n      },\n      Matrix4,\n      Quaternion,\n      Raycaster,\n      Sphere,\n      Spherical,\n      Vector2,\n      Vector3,\n      Vector4\n    };\n    Impl.install({\n      THREE: subsetOfTHREE\n    });\n    extend({\n      CameraControlsImpl: Impl\n    });\n  }, [Impl]);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new Impl(explCamera), [Impl, explCamera]);\n  useFrame((state, delta) => {\n    controls.update(delta);\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.disconnect();\n  }, [explDomElement, controls]);\n  useEffect(() => {\n    function invalidateAndRegress() {\n      invalidate();\n      if (regress) performance.regress();\n    }\n    const handleControlStart = e => {\n      invalidateAndRegress();\n      onControlStart == null || onControlStart(e);\n      onStart == null || onStart(e); // backwards compatibility\n    };\n    const handleControl = e => {\n      invalidateAndRegress();\n      onControl == null || onControl(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleControlEnd = e => {\n      onControlEnd == null || onControlEnd(e);\n      onEnd == null || onEnd(e); // backwards compatibility\n    };\n    const handleTransitionStart = e => {\n      invalidateAndRegress();\n      onTransitionStart == null || onTransitionStart(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleUpdate = e => {\n      invalidateAndRegress();\n      onUpdate == null || onUpdate(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleWake = e => {\n      invalidateAndRegress();\n      onWake == null || onWake(e);\n      onChange == null || onChange(e); // backwards compatibility\n    };\n    const handleRest = e => {\n      onRest == null || onRest(e);\n    };\n    const handleSleep = e => {\n      onSleep == null || onSleep(e);\n    };\n    controls.addEventListener('controlstart', handleControlStart);\n    controls.addEventListener('control', handleControl);\n    controls.addEventListener('controlend', handleControlEnd);\n    controls.addEventListener('transitionstart', handleTransitionStart);\n    controls.addEventListener('update', handleUpdate);\n    controls.addEventListener('wake', handleWake);\n    controls.addEventListener('rest', handleRest);\n    controls.addEventListener('sleep', handleSleep);\n    return () => {\n      controls.removeEventListener('controlstart', handleControlStart);\n      controls.removeEventListener('control', handleControl);\n      controls.removeEventListener('controlend', handleControlEnd);\n      controls.removeEventListener('transitionstart', handleTransitionStart);\n      controls.removeEventListener('update', handleUpdate);\n      controls.removeEventListener('wake', handleWake);\n      controls.removeEventListener('rest', handleRest);\n      controls.removeEventListener('sleep', handleSleep);\n    };\n  }, [controls, invalidate, setEvents, regress, performance, onControlStart, onControl, onControlEnd, onTransitionStart, onUpdate, onWake, onRest, onSleep, onChange, onStart, onEnd]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\n\nexport { CameraControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAQ,OAAO;AACrH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,OAAOC,kBAAkB,MAAM,iBAAiB;AAChD,SAASC,OAAO,IAAID,kBAAkB,QAAQ,iBAAiB;AAE/D,MAAME,cAAc,GAAG,eAAeR,UAAU,CAAC,CAACS,KAAK,EAAEC,GAAG,KAAK;EAC/D,MAAM;IACJC,IAAI,EAAEC,YAAY;IAClBC,MAAM;IACNC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,YAAY;IACZC,iBAAiB;IACjBC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACP,GAAGC;EACL,CAAC,GAAGnB,KAAK;EACT,MAAMoB,IAAI,GAAGjB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGN,kBAAkB;;EAEjG;EACAL,OAAO,CAAC,MAAM;IACZ;IACA;IACA,MAAM6B,aAAa,GAAG;MACpBzC,IAAI;MACJC,SAAS,EAAE;QACTyC,KAAK,EAAEzC,SAAS,CAACyC;MACnB,CAAC;MACDxC,OAAO;MACPC,UAAU;MACVC,SAAS;MACTC,MAAM;MACNC,SAAS;MACTC,OAAO;MACPC,OAAO;MACPC;IACF,CAAC;IACD+B,IAAI,CAACG,OAAO,CAAC;MACXC,KAAK,EAAEH;IACT,CAAC,CAAC;IACF3B,MAAM,CAAC;MACLG,kBAAkB,EAAEuB;IACtB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,MAAMK,aAAa,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACtB,MAAM,CAAC;EACrD,MAAMuB,EAAE,GAAGhC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,UAAU,GAAGjC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACE,UAAU,CAAC;EACtD,MAAMC,MAAM,GAAGlC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,SAAS,GAAGnC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACI,SAAS,CAAC;EACpD,MAAMC,GAAG,GAAGpC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGrC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,WAAW,GAAGtC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACO,WAAW,CAAC;EACxD,MAAMC,UAAU,GAAG9B,MAAM,IAAIqB,aAAa;EAC1C,MAAMU,cAAc,GAAG9B,UAAU,IAAIwB,MAAM,CAACO,SAAS,IAAIT,EAAE,CAACtB,UAAU;EACtE,MAAMgC,QAAQ,GAAG7C,OAAO,CAAC,MAAM,IAAI4B,IAAI,CAACc,UAAU,CAAC,EAAE,CAACd,IAAI,EAAEc,UAAU,CAAC,CAAC;EACxEtC,QAAQ,CAAC,CAAC8B,KAAK,EAAEY,KAAK,KAAK;IACzBD,QAAQ,CAACE,MAAM,CAACD,KAAK,CAAC;EACxB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN7C,SAAS,CAAC,MAAM;IACd4C,QAAQ,CAACG,OAAO,CAACL,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACI,UAAU,CAAC,CAAC;EACzC,CAAC,EAAE,CAACN,cAAc,EAAEE,QAAQ,CAAC,CAAC;EAC9B5C,SAAS,CAAC,MAAM;IACd,SAASiD,oBAAoBA,CAAA,EAAG;MAC9Bd,UAAU,CAAC,CAAC;MACZ,IAAIV,OAAO,EAAEe,WAAW,CAACf,OAAO,CAAC,CAAC;IACpC;IACA,MAAMyB,kBAAkB,GAAGC,CAAC,IAAI;MAC9BF,oBAAoB,CAAC,CAAC;MACtBnC,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACqC,CAAC,CAAC;MAC3C7B,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC6B,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,MAAMC,aAAa,GAAGD,CAAC,IAAI;MACzBF,oBAAoB,CAAC,CAAC;MACtBlC,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACoC,CAAC,CAAC;MACjC3B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2B,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,MAAME,gBAAgB,GAAGF,CAAC,IAAI;MAC5BnC,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACmC,CAAC,CAAC;MACvC5B,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC4B,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,MAAMG,qBAAqB,GAAGH,CAAC,IAAI;MACjCF,oBAAoB,CAAC,CAAC;MACtBhC,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,CAACkC,CAAC,CAAC;MACjD3B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2B,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,MAAMI,YAAY,GAAGJ,CAAC,IAAI;MACxBF,oBAAoB,CAAC,CAAC;MACtB/B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACiC,CAAC,CAAC;MAC/B3B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2B,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,MAAMK,UAAU,GAAGL,CAAC,IAAI;MACtBF,oBAAoB,CAAC,CAAC;MACtB9B,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACgC,CAAC,CAAC;MAC3B3B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2B,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,MAAMM,UAAU,GAAGN,CAAC,IAAI;MACtB/B,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC+B,CAAC,CAAC;IAC7B,CAAC;IACD,MAAMO,WAAW,GAAGP,CAAC,IAAI;MACvB9B,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC8B,CAAC,CAAC;IAC/B,CAAC;IACDP,QAAQ,CAACe,gBAAgB,CAAC,cAAc,EAAET,kBAAkB,CAAC;IAC7DN,QAAQ,CAACe,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACnDR,QAAQ,CAACe,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,CAAC;IACzDT,QAAQ,CAACe,gBAAgB,CAAC,iBAAiB,EAAEL,qBAAqB,CAAC;IACnEV,QAAQ,CAACe,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACjDX,QAAQ,CAACe,gBAAgB,CAAC,MAAM,EAAEH,UAAU,CAAC;IAC7CZ,QAAQ,CAACe,gBAAgB,CAAC,MAAM,EAAEF,UAAU,CAAC;IAC7Cb,QAAQ,CAACe,gBAAgB,CAAC,OAAO,EAAED,WAAW,CAAC;IAC/C,OAAO,MAAM;MACXd,QAAQ,CAACgB,mBAAmB,CAAC,cAAc,EAAEV,kBAAkB,CAAC;MAChEN,QAAQ,CAACgB,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;MACtDR,QAAQ,CAACgB,mBAAmB,CAAC,YAAY,EAAEP,gBAAgB,CAAC;MAC5DT,QAAQ,CAACgB,mBAAmB,CAAC,iBAAiB,EAAEN,qBAAqB,CAAC;MACtEV,QAAQ,CAACgB,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MACpDX,QAAQ,CAACgB,mBAAmB,CAAC,MAAM,EAAEJ,UAAU,CAAC;MAChDZ,QAAQ,CAACgB,mBAAmB,CAAC,MAAM,EAAEH,UAAU,CAAC;MAChDb,QAAQ,CAACgB,mBAAmB,CAAC,OAAO,EAAEF,WAAW,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACd,QAAQ,EAAET,UAAU,EAAEE,SAAS,EAAEZ,OAAO,EAAEe,WAAW,EAAE1B,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEG,QAAQ,EAAEF,OAAO,EAAEC,KAAK,CAAC,CAAC;EACpLvB,SAAS,CAAC,MAAM;IACd,IAAIa,WAAW,EAAE;MACf,MAAMgD,GAAG,GAAGtB,GAAG,CAAC,CAAC,CAACK,QAAQ;MAC1BN,GAAG,CAAC;QACFM,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF,OAAO,MAAMN,GAAG,CAAC;QACfM,QAAQ,EAAEiB;MACZ,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE,CAAChD,WAAW,EAAE+B,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAa/C,KAAK,CAACiE,aAAa,CAAC,WAAW,EAAE5E,QAAQ,CAAC;IAC5DsB,GAAG,EAAEA,GAAG;IACRuD,MAAM,EAAEnB;EACV,CAAC,EAAElB,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASpB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}