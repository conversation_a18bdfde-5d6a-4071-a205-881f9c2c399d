{"ast": null, "code": "import { Object3D, SphereGeometry, MeshBasicMaterial, Color, LineBasicMaterial, Quaternion, Vector3, Matrix4, Mesh, Line, BufferGeometry, BufferAttribute } from \"three\";\nconst _q = /* @__PURE__ */new Quaternion();\nconst _targetPos = /* @__PURE__ */new Vector3();\nconst _targetVec = /* @__PURE__ */new Vector3();\nconst _effectorPos = /* @__PURE__ */new Vector3();\nconst _effectorVec = /* @__PURE__ */new Vector3();\nconst _linkPos = /* @__PURE__ */new Vector3();\nconst _invLinkQ = /* @__PURE__ */new Quaternion();\nconst _linkScale = /* @__PURE__ */new Vector3();\nconst _axis = /* @__PURE__ */new Vector3();\nconst _vector = /* @__PURE__ */new Vector3();\nconst _matrix = /* @__PURE__ */new Matrix4();\nclass CCDIKSolver {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} iks\n   */\n  constructor(mesh, iks = []) {\n    this.mesh = mesh;\n    this.iks = iks;\n    this._valid();\n  }\n  /**\n   * Update all IK bones.\n   *\n   * @return {CCDIKSolver}\n   */\n  update() {\n    const iks = this.iks;\n    for (let i = 0, il = iks.length; i < il; i++) {\n      this.updateOne(iks[i]);\n    }\n    return this;\n  }\n  /**\n   * Update one IK bone\n   *\n   * @param {Object} ik parameter\n   * @return {CCDIKSolver}\n   */\n  updateOne(ik) {\n    const bones = this.mesh.skeleton.bones;\n    const math = Math;\n    const effector = bones[ik.effector];\n    const target = bones[ik.target];\n    _targetPos.setFromMatrixPosition(target.matrixWorld);\n    const links = ik.links;\n    const iteration = ik.iteration !== void 0 ? ik.iteration : 1;\n    for (let i = 0; i < iteration; i++) {\n      let rotated = false;\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        const link = bones[links[j].index];\n        if (links[j].enabled === false) break;\n        const limitation = links[j].limitation;\n        const rotationMin = links[j].rotationMin;\n        const rotationMax = links[j].rotationMax;\n        link.matrixWorld.decompose(_linkPos, _invLinkQ, _linkScale);\n        _invLinkQ.invert();\n        _effectorPos.setFromMatrixPosition(effector.matrixWorld);\n        _effectorVec.subVectors(_effectorPos, _linkPos);\n        _effectorVec.applyQuaternion(_invLinkQ);\n        _effectorVec.normalize();\n        _targetVec.subVectors(_targetPos, _linkPos);\n        _targetVec.applyQuaternion(_invLinkQ);\n        _targetVec.normalize();\n        let angle = _targetVec.dot(_effectorVec);\n        if (angle > 1) {\n          angle = 1;\n        } else if (angle < -1) {\n          angle = -1;\n        }\n        angle = math.acos(angle);\n        if (angle < 1e-5) continue;\n        if (ik.minAngle !== void 0 && angle < ik.minAngle) {\n          angle = ik.minAngle;\n        }\n        if (ik.maxAngle !== void 0 && angle > ik.maxAngle) {\n          angle = ik.maxAngle;\n        }\n        _axis.crossVectors(_effectorVec, _targetVec);\n        _axis.normalize();\n        _q.setFromAxisAngle(_axis, angle);\n        link.quaternion.multiply(_q);\n        if (limitation !== void 0) {\n          let c = link.quaternion.w;\n          if (c > 1) c = 1;\n          const c2 = math.sqrt(1 - c * c);\n          link.quaternion.set(limitation.x * c2, limitation.y * c2, limitation.z * c2, c);\n        }\n        if (rotationMin !== void 0) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).max(rotationMin));\n        }\n        if (rotationMax !== void 0) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).min(rotationMax));\n        }\n        link.updateMatrixWorld(true);\n        rotated = true;\n      }\n      if (!rotated) break;\n    }\n    return this;\n  }\n  /**\n   * Creates Helper\n   *\n   * @return {CCDIKHelper}\n   */\n  createHelper() {\n    return new CCDIKHelper(this.mesh, this.iks);\n  }\n  // private methods\n  _valid() {\n    const iks = this.iks;\n    const bones = this.mesh.skeleton.bones;\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i];\n      const effector = bones[ik.effector];\n      const links = ik.links;\n      let link0, link1;\n      link0 = effector;\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        link1 = bones[links[j].index];\n        if (link0.parent !== link1) {\n          console.warn(\"THREE.CCDIKSolver: bone \" + link0.name + \" is not the child of bone \" + link1.name);\n        }\n        link0 = link1;\n      }\n    }\n  }\n}\nfunction getPosition(bone, matrixWorldInv) {\n  return _vector.setFromMatrixPosition(bone.matrixWorld).applyMatrix4(matrixWorldInv);\n}\nfunction setPositionOfBoneToAttributeArray(array, index, bone, matrixWorldInv) {\n  const v = getPosition(bone, matrixWorldInv);\n  array[index * 3 + 0] = v.x;\n  array[index * 3 + 1] = v.y;\n  array[index * 3 + 2] = v.z;\n}\nclass CCDIKHelper extends Object3D {\n  constructor(mesh, iks = [], sphereSize = 0.25) {\n    super();\n    this.root = mesh;\n    this.iks = iks;\n    this.matrix.copy(mesh.matrixWorld);\n    this.matrixAutoUpdate = false;\n    this.sphereGeometry = new SphereGeometry(sphereSize, 16, 8);\n    this.targetSphereMaterial = new MeshBasicMaterial({\n      color: new Color(16746632),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n    this.effectorSphereMaterial = new MeshBasicMaterial({\n      color: new Color(8978312),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n    this.linkSphereMaterial = new MeshBasicMaterial({\n      color: new Color(8947967),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n    this.lineMaterial = new LineBasicMaterial({\n      color: new Color(16711680),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n    this._init();\n  }\n  /**\n   * Updates IK bones visualization.\n   */\n  updateMatrixWorld(force) {\n    const mesh = this.root;\n    if (this.visible) {\n      let offset = 0;\n      const iks = this.iks;\n      const bones = mesh.skeleton.bones;\n      _matrix.copy(mesh.matrixWorld).invert();\n      for (let i = 0, il = iks.length; i < il; i++) {\n        const ik = iks[i];\n        const targetBone = bones[ik.target];\n        const effectorBone = bones[ik.effector];\n        const targetMesh = this.children[offset++];\n        const effectorMesh = this.children[offset++];\n        targetMesh.position.copy(getPosition(targetBone, _matrix));\n        effectorMesh.position.copy(getPosition(effectorBone, _matrix));\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j];\n          const linkBone = bones[link.index];\n          const linkMesh = this.children[offset++];\n          linkMesh.position.copy(getPosition(linkBone, _matrix));\n        }\n        const line = this.children[offset++];\n        const array = line.geometry.attributes.position.array;\n        setPositionOfBoneToAttributeArray(array, 0, targetBone, _matrix);\n        setPositionOfBoneToAttributeArray(array, 1, effectorBone, _matrix);\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j];\n          const linkBone = bones[link.index];\n          setPositionOfBoneToAttributeArray(array, j + 2, linkBone, _matrix);\n        }\n        line.geometry.attributes.position.needsUpdate = true;\n      }\n    }\n    this.matrix.copy(mesh.matrixWorld);\n    super.updateMatrixWorld(force);\n  }\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    this.sphereGeometry.dispose();\n    this.targetSphereMaterial.dispose();\n    this.effectorSphereMaterial.dispose();\n    this.linkSphereMaterial.dispose();\n    this.lineMaterial.dispose();\n    const children = this.children;\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child.isLine) child.geometry.dispose();\n    }\n  }\n  // private method\n  _init() {\n    const scope = this;\n    const iks = this.iks;\n    function createLineGeometry(ik) {\n      const geometry = new BufferGeometry();\n      const vertices = new Float32Array((2 + ik.links.length) * 3);\n      geometry.setAttribute(\"position\", new BufferAttribute(vertices, 3));\n      return geometry;\n    }\n    function createTargetMesh() {\n      return new Mesh(scope.sphereGeometry, scope.targetSphereMaterial);\n    }\n    function createEffectorMesh() {\n      return new Mesh(scope.sphereGeometry, scope.effectorSphereMaterial);\n    }\n    function createLinkMesh() {\n      return new Mesh(scope.sphereGeometry, scope.linkSphereMaterial);\n    }\n    function createLine(ik) {\n      return new Line(createLineGeometry(ik), scope.lineMaterial);\n    }\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i];\n      this.add(createTargetMesh());\n      this.add(createEffectorMesh());\n      for (let j = 0, jl = ik.links.length; j < jl; j++) {\n        this.add(createLinkMesh());\n      }\n      this.add(createLine(ik));\n    }\n  }\n}\nexport { CCDIKHelper, CCDIKSolver };", "map": {"version": 3, "names": ["_q", "Quaternion", "_targetPos", "Vector3", "_targetVec", "_effectorPos", "_effectorVec", "_linkPos", "_invLinkQ", "_linkScale", "_axis", "_vector", "_matrix", "Matrix4", "CCDIKSolver", "constructor", "mesh", "iks", "_valid", "update", "i", "il", "length", "updateOne", "ik", "bones", "skeleton", "math", "Math", "effector", "target", "setFromMatrixPosition", "matrixWorld", "links", "iteration", "rotated", "j", "jl", "link", "index", "enabled", "limitation", "rotationMin", "rotationMax", "decompose", "invert", "subVectors", "applyQuaternion", "normalize", "angle", "dot", "acos", "minAngle", "maxAngle", "crossVectors", "setFromAxisAngle", "quaternion", "multiply", "c", "w", "c2", "sqrt", "set", "x", "y", "z", "rotation", "setFromVector3", "setFromEuler", "max", "min", "updateMatrixWorld", "createHelper", "CCDIKHelper", "link0", "link1", "parent", "console", "warn", "name", "getPosition", "bone", "matrixWorldInv", "applyMatrix4", "setPositionOfBoneToAttributeArray", "array", "v", "Object3D", "sphereSize", "root", "matrix", "copy", "matrixAutoUpdate", "sphereGeometry", "SphereGeometry", "targetSphereMaterial", "MeshBasicMaterial", "color", "Color", "depthTest", "depthWrite", "transparent", "effectorSphereMaterial", "linkSphereMaterial", "lineMaterial", "LineBasicMaterial", "_init", "force", "visible", "offset", "targetBone", "effectorBone", "<PERSON><PERSON><PERSON>", "children", "<PERSON>or<PERSON><PERSON>", "position", "linkBone", "linkMesh", "line", "geometry", "attributes", "needsUpdate", "dispose", "child", "isLine", "scope", "createLineGeometry", "BufferGeometry", "vertices", "Float32Array", "setAttribute", "BufferAttribute", "create<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "createEffector<PERSON>esh", "createLinkMesh", "createLine", "Line", "add"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/animation/CCDIKSolver.js"], "sourcesContent": ["import {\n  Buffer<PERSON>ttribute,\n  BufferGeometry,\n  Color,\n  Line,\n  LineBasicMaterial,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Quaternion,\n  SphereGeometry,\n  Vector3,\n} from 'three'\n\nconst _q = /* @__PURE__ */ new Quaternion()\nconst _targetPos = /* @__PURE__ */ new Vector3()\nconst _targetVec = /* @__PURE__ */ new Vector3()\nconst _effectorPos = /* @__PURE__ */ new Vector3()\nconst _effectorVec = /* @__PURE__ */ new Vector3()\nconst _linkPos = /* @__PURE__ */ new Vector3()\nconst _invLinkQ = /* @__PURE__ */ new Quaternion()\nconst _linkScale = /* @__PURE__ */ new Vector3()\nconst _axis = /* @__PURE__ */ new Vector3()\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _matrix = /* @__PURE__ */ new Matrix4()\n\n/**\n * CCD Algorithm\n *  - https://sites.google.com/site/auraliusproject/ccd-algorithm\n *\n * // ik parameter example\n * //\n * // target, effector, index in links are bone index in skeleton.bones.\n * // the bones relation should be\n * // <-- parent                                  child -->\n * // links[ n ], links[ n - 1 ], ..., links[ 0 ], effector\n * iks = [ {\n *\ttarget: 1,\n *\teffector: 2,\n *\tlinks: [ { index: 5, limitation: new Vector3( 1, 0, 0 ) }, { index: 4, enabled: false }, { index : 3 } ],\n *\titeration: 10,\n *\tminAngle: 0.0,\n *\tmaxAngle: 1.0,\n * } ];\n */\n\nclass CCDIKSolver {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} iks\n   */\n  constructor(mesh, iks = []) {\n    this.mesh = mesh\n    this.iks = iks\n\n    this._valid()\n  }\n\n  /**\n   * Update all IK bones.\n   *\n   * @return {CCDIKSolver}\n   */\n  update() {\n    const iks = this.iks\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      this.updateOne(iks[i])\n    }\n\n    return this\n  }\n\n  /**\n   * Update one IK bone\n   *\n   * @param {Object} ik parameter\n   * @return {CCDIKSolver}\n   */\n  updateOne(ik) {\n    const bones = this.mesh.skeleton.bones\n\n    // for reference overhead reduction in loop\n    const math = Math\n\n    const effector = bones[ik.effector]\n    const target = bones[ik.target]\n\n    // don't use getWorldPosition() here for the performance\n    // because it calls updateMatrixWorld( true ) inside.\n    _targetPos.setFromMatrixPosition(target.matrixWorld)\n\n    const links = ik.links\n    const iteration = ik.iteration !== undefined ? ik.iteration : 1\n\n    for (let i = 0; i < iteration; i++) {\n      let rotated = false\n\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        const link = bones[links[j].index]\n\n        // skip this link and following links.\n        // this skip is used for MMD performance optimization.\n        if (links[j].enabled === false) break\n\n        const limitation = links[j].limitation\n        const rotationMin = links[j].rotationMin\n        const rotationMax = links[j].rotationMax\n\n        // don't use getWorldPosition/Quaternion() here for the performance\n        // because they call updateMatrixWorld( true ) inside.\n        link.matrixWorld.decompose(_linkPos, _invLinkQ, _linkScale)\n        _invLinkQ.invert()\n        _effectorPos.setFromMatrixPosition(effector.matrixWorld)\n\n        // work in link world\n        _effectorVec.subVectors(_effectorPos, _linkPos)\n        _effectorVec.applyQuaternion(_invLinkQ)\n        _effectorVec.normalize()\n\n        _targetVec.subVectors(_targetPos, _linkPos)\n        _targetVec.applyQuaternion(_invLinkQ)\n        _targetVec.normalize()\n\n        let angle = _targetVec.dot(_effectorVec)\n\n        if (angle > 1.0) {\n          angle = 1.0\n        } else if (angle < -1.0) {\n          angle = -1.0\n        }\n\n        angle = math.acos(angle)\n\n        // skip if changing angle is too small to prevent vibration of bone\n        if (angle < 1e-5) continue\n\n        if (ik.minAngle !== undefined && angle < ik.minAngle) {\n          angle = ik.minAngle\n        }\n\n        if (ik.maxAngle !== undefined && angle > ik.maxAngle) {\n          angle = ik.maxAngle\n        }\n\n        _axis.crossVectors(_effectorVec, _targetVec)\n        _axis.normalize()\n\n        _q.setFromAxisAngle(_axis, angle)\n        link.quaternion.multiply(_q)\n\n        // TODO: re-consider the limitation specification\n        if (limitation !== undefined) {\n          let c = link.quaternion.w\n\n          if (c > 1.0) c = 1.0\n\n          const c2 = math.sqrt(1 - c * c)\n          link.quaternion.set(limitation.x * c2, limitation.y * c2, limitation.z * c2, c)\n        }\n\n        if (rotationMin !== undefined) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).max(rotationMin))\n        }\n\n        if (rotationMax !== undefined) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).min(rotationMax))\n        }\n\n        link.updateMatrixWorld(true)\n\n        rotated = true\n      }\n\n      if (!rotated) break\n    }\n\n    return this\n  }\n\n  /**\n   * Creates Helper\n   *\n   * @return {CCDIKHelper}\n   */\n  createHelper() {\n    return new CCDIKHelper(this.mesh, this.iks)\n  }\n\n  // private methods\n\n  _valid() {\n    const iks = this.iks\n    const bones = this.mesh.skeleton.bones\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i]\n      const effector = bones[ik.effector]\n      const links = ik.links\n      let link0, link1\n\n      link0 = effector\n\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        link1 = bones[links[j].index]\n\n        if (link0.parent !== link1) {\n          console.warn('THREE.CCDIKSolver: bone ' + link0.name + ' is not the child of bone ' + link1.name)\n        }\n\n        link0 = link1\n      }\n    }\n  }\n}\n\nfunction getPosition(bone, matrixWorldInv) {\n  return _vector.setFromMatrixPosition(bone.matrixWorld).applyMatrix4(matrixWorldInv)\n}\n\nfunction setPositionOfBoneToAttributeArray(array, index, bone, matrixWorldInv) {\n  const v = getPosition(bone, matrixWorldInv)\n\n  array[index * 3 + 0] = v.x\n  array[index * 3 + 1] = v.y\n  array[index * 3 + 2] = v.z\n}\n\n/**\n * Visualize IK bones\n *\n * @param {SkinnedMesh} mesh\n * @param {Array<Object>} iks\n */\nclass CCDIKHelper extends Object3D {\n  constructor(mesh, iks = [], sphereSize = 0.25) {\n    super()\n\n    this.root = mesh\n    this.iks = iks\n\n    this.matrix.copy(mesh.matrixWorld)\n    this.matrixAutoUpdate = false\n\n    this.sphereGeometry = new SphereGeometry(sphereSize, 16, 8)\n\n    this.targetSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0xff8888),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.effectorSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0x88ff88),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.linkSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0x8888ff),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.lineMaterial = new LineBasicMaterial({\n      color: new Color(0xff0000),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this._init()\n  }\n\n  /**\n   * Updates IK bones visualization.\n   */\n  updateMatrixWorld(force) {\n    const mesh = this.root\n\n    if (this.visible) {\n      let offset = 0\n\n      const iks = this.iks\n      const bones = mesh.skeleton.bones\n\n      _matrix.copy(mesh.matrixWorld).invert()\n\n      for (let i = 0, il = iks.length; i < il; i++) {\n        const ik = iks[i]\n\n        const targetBone = bones[ik.target]\n        const effectorBone = bones[ik.effector]\n\n        const targetMesh = this.children[offset++]\n        const effectorMesh = this.children[offset++]\n\n        targetMesh.position.copy(getPosition(targetBone, _matrix))\n        effectorMesh.position.copy(getPosition(effectorBone, _matrix))\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j]\n          const linkBone = bones[link.index]\n\n          const linkMesh = this.children[offset++]\n\n          linkMesh.position.copy(getPosition(linkBone, _matrix))\n        }\n\n        const line = this.children[offset++]\n        const array = line.geometry.attributes.position.array\n\n        setPositionOfBoneToAttributeArray(array, 0, targetBone, _matrix)\n        setPositionOfBoneToAttributeArray(array, 1, effectorBone, _matrix)\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j]\n          const linkBone = bones[link.index]\n          setPositionOfBoneToAttributeArray(array, j + 2, linkBone, _matrix)\n        }\n\n        line.geometry.attributes.position.needsUpdate = true\n      }\n    }\n\n    this.matrix.copy(mesh.matrixWorld)\n\n    super.updateMatrixWorld(force)\n  }\n\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    this.sphereGeometry.dispose()\n\n    this.targetSphereMaterial.dispose()\n    this.effectorSphereMaterial.dispose()\n    this.linkSphereMaterial.dispose()\n    this.lineMaterial.dispose()\n\n    const children = this.children\n\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i]\n\n      if (child.isLine) child.geometry.dispose()\n    }\n  }\n\n  // private method\n\n  _init() {\n    const scope = this\n    const iks = this.iks\n\n    function createLineGeometry(ik) {\n      const geometry = new BufferGeometry()\n      const vertices = new Float32Array((2 + ik.links.length) * 3)\n      geometry.setAttribute('position', new BufferAttribute(vertices, 3))\n\n      return geometry\n    }\n\n    function createTargetMesh() {\n      return new Mesh(scope.sphereGeometry, scope.targetSphereMaterial)\n    }\n\n    function createEffectorMesh() {\n      return new Mesh(scope.sphereGeometry, scope.effectorSphereMaterial)\n    }\n\n    function createLinkMesh() {\n      return new Mesh(scope.sphereGeometry, scope.linkSphereMaterial)\n    }\n\n    function createLine(ik) {\n      return new Line(createLineGeometry(ik), scope.lineMaterial)\n    }\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i]\n\n      this.add(createTargetMesh())\n      this.add(createEffectorMesh())\n\n      for (let j = 0, jl = ik.links.length; j < jl; j++) {\n        this.add(createLinkMesh())\n      }\n\n      this.add(createLine(ik))\n    }\n  }\n}\n\nexport { CCDIKSolver, CCDIKHelper }\n"], "mappings": ";AAeA,MAAMA,EAAA,GAAqB,mBAAIC,UAAA,CAAY;AAC3C,MAAMC,UAAA,GAA6B,mBAAIC,OAAA,CAAS;AAChD,MAAMC,UAAA,GAA6B,mBAAID,OAAA,CAAS;AAChD,MAAME,YAAA,GAA+B,mBAAIF,OAAA,CAAS;AAClD,MAAMG,YAAA,GAA+B,mBAAIH,OAAA,CAAS;AAClD,MAAMI,QAAA,GAA2B,mBAAIJ,OAAA,CAAS;AAC9C,MAAMK,SAAA,GAA4B,mBAAIP,UAAA,CAAY;AAClD,MAAMQ,UAAA,GAA6B,mBAAIN,OAAA,CAAS;AAChD,MAAMO,KAAA,GAAwB,mBAAIP,OAAA,CAAS;AAC3C,MAAMQ,OAAA,GAA0B,mBAAIR,OAAA,CAAS;AAC7C,MAAMS,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAsB7C,MAAMC,WAAA,CAAY;EAAA;AAAA;AAAA;AAAA;EAKhBC,YAAYC,IAAA,EAAMC,GAAA,GAAM,IAAI;IAC1B,KAAKD,IAAA,GAAOA,IAAA;IACZ,KAAKC,GAAA,GAAMA,GAAA;IAEX,KAAKC,MAAA,CAAQ;EACd;EAAA;AAAA;AAAA;AAAA;AAAA;EAODC,OAAA,EAAS;IACP,MAAMF,GAAA,GAAM,KAAKA,GAAA;IAEjB,SAASG,CAAA,GAAI,GAAGC,EAAA,GAAKJ,GAAA,CAAIK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5C,KAAKG,SAAA,CAAUN,GAAA,CAAIG,CAAC,CAAC;IACtB;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDG,UAAUC,EAAA,EAAI;IACZ,MAAMC,KAAA,GAAQ,KAAKT,IAAA,CAAKU,QAAA,CAASD,KAAA;IAGjC,MAAME,IAAA,GAAOC,IAAA;IAEb,MAAMC,QAAA,GAAWJ,KAAA,CAAMD,EAAA,CAAGK,QAAQ;IAClC,MAAMC,MAAA,GAASL,KAAA,CAAMD,EAAA,CAAGM,MAAM;IAI9B5B,UAAA,CAAW6B,qBAAA,CAAsBD,MAAA,CAAOE,WAAW;IAEnD,MAAMC,KAAA,GAAQT,EAAA,CAAGS,KAAA;IACjB,MAAMC,SAAA,GAAYV,EAAA,CAAGU,SAAA,KAAc,SAAYV,EAAA,CAAGU,SAAA,GAAY;IAE9D,SAASd,CAAA,GAAI,GAAGA,CAAA,GAAIc,SAAA,EAAWd,CAAA,IAAK;MAClC,IAAIe,OAAA,GAAU;MAEd,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKJ,KAAA,CAAMX,MAAA,EAAQc,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9C,MAAME,IAAA,GAAOb,KAAA,CAAMQ,KAAA,CAAMG,CAAC,EAAEG,KAAK;QAIjC,IAAIN,KAAA,CAAMG,CAAC,EAAEI,OAAA,KAAY,OAAO;QAEhC,MAAMC,UAAA,GAAaR,KAAA,CAAMG,CAAC,EAAEK,UAAA;QAC5B,MAAMC,WAAA,GAAcT,KAAA,CAAMG,CAAC,EAAEM,WAAA;QAC7B,MAAMC,WAAA,GAAcV,KAAA,CAAMG,CAAC,EAAEO,WAAA;QAI7BL,IAAA,CAAKN,WAAA,CAAYY,SAAA,CAAUrC,QAAA,EAAUC,SAAA,EAAWC,UAAU;QAC1DD,SAAA,CAAUqC,MAAA,CAAQ;QAClBxC,YAAA,CAAa0B,qBAAA,CAAsBF,QAAA,CAASG,WAAW;QAGvD1B,YAAA,CAAawC,UAAA,CAAWzC,YAAA,EAAcE,QAAQ;QAC9CD,YAAA,CAAayC,eAAA,CAAgBvC,SAAS;QACtCF,YAAA,CAAa0C,SAAA,CAAW;QAExB5C,UAAA,CAAW0C,UAAA,CAAW5C,UAAA,EAAYK,QAAQ;QAC1CH,UAAA,CAAW2C,eAAA,CAAgBvC,SAAS;QACpCJ,UAAA,CAAW4C,SAAA,CAAW;QAEtB,IAAIC,KAAA,GAAQ7C,UAAA,CAAW8C,GAAA,CAAI5C,YAAY;QAEvC,IAAI2C,KAAA,GAAQ,GAAK;UACfA,KAAA,GAAQ;QAClB,WAAmBA,KAAA,GAAQ,IAAM;UACvBA,KAAA,GAAQ;QACT;QAEDA,KAAA,GAAQtB,IAAA,CAAKwB,IAAA,CAAKF,KAAK;QAGvB,IAAIA,KAAA,GAAQ,MAAM;QAElB,IAAIzB,EAAA,CAAG4B,QAAA,KAAa,UAAaH,KAAA,GAAQzB,EAAA,CAAG4B,QAAA,EAAU;UACpDH,KAAA,GAAQzB,EAAA,CAAG4B,QAAA;QACZ;QAED,IAAI5B,EAAA,CAAG6B,QAAA,KAAa,UAAaJ,KAAA,GAAQzB,EAAA,CAAG6B,QAAA,EAAU;UACpDJ,KAAA,GAAQzB,EAAA,CAAG6B,QAAA;QACZ;QAED3C,KAAA,CAAM4C,YAAA,CAAahD,YAAA,EAAcF,UAAU;QAC3CM,KAAA,CAAMsC,SAAA,CAAW;QAEjBhD,EAAA,CAAGuD,gBAAA,CAAiB7C,KAAA,EAAOuC,KAAK;QAChCX,IAAA,CAAKkB,UAAA,CAAWC,QAAA,CAASzD,EAAE;QAG3B,IAAIyC,UAAA,KAAe,QAAW;UAC5B,IAAIiB,CAAA,GAAIpB,IAAA,CAAKkB,UAAA,CAAWG,CAAA;UAExB,IAAID,CAAA,GAAI,GAAKA,CAAA,GAAI;UAEjB,MAAME,EAAA,GAAKjC,IAAA,CAAKkC,IAAA,CAAK,IAAIH,CAAA,GAAIA,CAAC;UAC9BpB,IAAA,CAAKkB,UAAA,CAAWM,GAAA,CAAIrB,UAAA,CAAWsB,CAAA,GAAIH,EAAA,EAAInB,UAAA,CAAWuB,CAAA,GAAIJ,EAAA,EAAInB,UAAA,CAAWwB,CAAA,GAAIL,EAAA,EAAIF,CAAC;QAC/E;QAED,IAAIhB,WAAA,KAAgB,QAAW;UAC7BJ,IAAA,CAAK4B,QAAA,CAASC,cAAA,CAAexD,OAAA,CAAQyD,YAAA,CAAa9B,IAAA,CAAK4B,QAAQ,EAAEG,GAAA,CAAI3B,WAAW,CAAC;QAClF;QAED,IAAIC,WAAA,KAAgB,QAAW;UAC7BL,IAAA,CAAK4B,QAAA,CAASC,cAAA,CAAexD,OAAA,CAAQyD,YAAA,CAAa9B,IAAA,CAAK4B,QAAQ,EAAEI,GAAA,CAAI3B,WAAW,CAAC;QAClF;QAEDL,IAAA,CAAKiC,iBAAA,CAAkB,IAAI;QAE3BpC,OAAA,GAAU;MACX;MAED,IAAI,CAACA,OAAA,EAAS;IACf;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODqC,aAAA,EAAe;IACb,OAAO,IAAIC,WAAA,CAAY,KAAKzD,IAAA,EAAM,KAAKC,GAAG;EAC3C;EAAA;EAIDC,OAAA,EAAS;IACP,MAAMD,GAAA,GAAM,KAAKA,GAAA;IACjB,MAAMQ,KAAA,GAAQ,KAAKT,IAAA,CAAKU,QAAA,CAASD,KAAA;IAEjC,SAASL,CAAA,GAAI,GAAGC,EAAA,GAAKJ,GAAA,CAAIK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5C,MAAMI,EAAA,GAAKP,GAAA,CAAIG,CAAC;MAChB,MAAMS,QAAA,GAAWJ,KAAA,CAAMD,EAAA,CAAGK,QAAQ;MAClC,MAAMI,KAAA,GAAQT,EAAA,CAAGS,KAAA;MACjB,IAAIyC,KAAA,EAAOC,KAAA;MAEXD,KAAA,GAAQ7C,QAAA;MAER,SAASO,CAAA,GAAI,GAAGC,EAAA,GAAKJ,KAAA,CAAMX,MAAA,EAAQc,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9CuC,KAAA,GAAQlD,KAAA,CAAMQ,KAAA,CAAMG,CAAC,EAAEG,KAAK;QAE5B,IAAImC,KAAA,CAAME,MAAA,KAAWD,KAAA,EAAO;UAC1BE,OAAA,CAAQC,IAAA,CAAK,6BAA6BJ,KAAA,CAAMK,IAAA,GAAO,+BAA+BJ,KAAA,CAAMI,IAAI;QACjG;QAEDL,KAAA,GAAQC,KAAA;MACT;IACF;EACF;AACH;AAEA,SAASK,YAAYC,IAAA,EAAMC,cAAA,EAAgB;EACzC,OAAOvE,OAAA,CAAQoB,qBAAA,CAAsBkD,IAAA,CAAKjD,WAAW,EAAEmD,YAAA,CAAaD,cAAc;AACpF;AAEA,SAASE,kCAAkCC,KAAA,EAAO9C,KAAA,EAAO0C,IAAA,EAAMC,cAAA,EAAgB;EAC7E,MAAMI,CAAA,GAAIN,WAAA,CAAYC,IAAA,EAAMC,cAAc;EAE1CG,KAAA,CAAM9C,KAAA,GAAQ,IAAI,CAAC,IAAI+C,CAAA,CAAEvB,CAAA;EACzBsB,KAAA,CAAM9C,KAAA,GAAQ,IAAI,CAAC,IAAI+C,CAAA,CAAEtB,CAAA;EACzBqB,KAAA,CAAM9C,KAAA,GAAQ,IAAI,CAAC,IAAI+C,CAAA,CAAErB,CAAA;AAC3B;AAQA,MAAMQ,WAAA,SAAoBc,QAAA,CAAS;EACjCxE,YAAYC,IAAA,EAAMC,GAAA,GAAM,IAAIuE,UAAA,GAAa,MAAM;IAC7C,MAAO;IAEP,KAAKC,IAAA,GAAOzE,IAAA;IACZ,KAAKC,GAAA,GAAMA,GAAA;IAEX,KAAKyE,MAAA,CAAOC,IAAA,CAAK3E,IAAA,CAAKgB,WAAW;IACjC,KAAK4D,gBAAA,GAAmB;IAExB,KAAKC,cAAA,GAAiB,IAAIC,cAAA,CAAeN,UAAA,EAAY,IAAI,CAAC;IAE1D,KAAKO,oBAAA,GAAuB,IAAIC,iBAAA,CAAkB;MAChDC,KAAA,EAAO,IAAIC,KAAA,CAAM,QAAQ;MACzBC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IACnB,CAAK;IAED,KAAKC,sBAAA,GAAyB,IAAIN,iBAAA,CAAkB;MAClDC,KAAA,EAAO,IAAIC,KAAA,CAAM,OAAQ;MACzBC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IACnB,CAAK;IAED,KAAKE,kBAAA,GAAqB,IAAIP,iBAAA,CAAkB;MAC9CC,KAAA,EAAO,IAAIC,KAAA,CAAM,OAAQ;MACzBC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IACnB,CAAK;IAED,KAAKG,YAAA,GAAe,IAAIC,iBAAA,CAAkB;MACxCR,KAAA,EAAO,IAAIC,KAAA,CAAM,QAAQ;MACzBC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IACnB,CAAK;IAED,KAAKK,KAAA,CAAO;EACb;EAAA;AAAA;AAAA;EAKDnC,kBAAkBoC,KAAA,EAAO;IACvB,MAAM3F,IAAA,GAAO,KAAKyE,IAAA;IAElB,IAAI,KAAKmB,OAAA,EAAS;MAChB,IAAIC,MAAA,GAAS;MAEb,MAAM5F,GAAA,GAAM,KAAKA,GAAA;MACjB,MAAMQ,KAAA,GAAQT,IAAA,CAAKU,QAAA,CAASD,KAAA;MAE5Bb,OAAA,CAAQ+E,IAAA,CAAK3E,IAAA,CAAKgB,WAAW,EAAEa,MAAA,CAAQ;MAEvC,SAASzB,CAAA,GAAI,GAAGC,EAAA,GAAKJ,GAAA,CAAIK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC5C,MAAMI,EAAA,GAAKP,GAAA,CAAIG,CAAC;QAEhB,MAAM0F,UAAA,GAAarF,KAAA,CAAMD,EAAA,CAAGM,MAAM;QAClC,MAAMiF,YAAA,GAAetF,KAAA,CAAMD,EAAA,CAAGK,QAAQ;QAEtC,MAAMmF,UAAA,GAAa,KAAKC,QAAA,CAASJ,MAAA,EAAQ;QACzC,MAAMK,YAAA,GAAe,KAAKD,QAAA,CAASJ,MAAA,EAAQ;QAE3CG,UAAA,CAAWG,QAAA,CAASxB,IAAA,CAAKX,WAAA,CAAY8B,UAAA,EAAYlG,OAAO,CAAC;QACzDsG,YAAA,CAAaC,QAAA,CAASxB,IAAA,CAAKX,WAAA,CAAY+B,YAAA,EAAcnG,OAAO,CAAC;QAE7D,SAASwB,CAAA,GAAI,GAAGC,EAAA,GAAKb,EAAA,CAAGS,KAAA,CAAMX,MAAA,EAAQc,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACjD,MAAME,IAAA,GAAOd,EAAA,CAAGS,KAAA,CAAMG,CAAC;UACvB,MAAMgF,QAAA,GAAW3F,KAAA,CAAMa,IAAA,CAAKC,KAAK;UAEjC,MAAM8E,QAAA,GAAW,KAAKJ,QAAA,CAASJ,MAAA,EAAQ;UAEvCQ,QAAA,CAASF,QAAA,CAASxB,IAAA,CAAKX,WAAA,CAAYoC,QAAA,EAAUxG,OAAO,CAAC;QACtD;QAED,MAAM0G,IAAA,GAAO,KAAKL,QAAA,CAASJ,MAAA,EAAQ;QACnC,MAAMxB,KAAA,GAAQiC,IAAA,CAAKC,QAAA,CAASC,UAAA,CAAWL,QAAA,CAAS9B,KAAA;QAEhDD,iCAAA,CAAkCC,KAAA,EAAO,GAAGyB,UAAA,EAAYlG,OAAO;QAC/DwE,iCAAA,CAAkCC,KAAA,EAAO,GAAG0B,YAAA,EAAcnG,OAAO;QAEjE,SAASwB,CAAA,GAAI,GAAGC,EAAA,GAAKb,EAAA,CAAGS,KAAA,CAAMX,MAAA,EAAQc,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACjD,MAAME,IAAA,GAAOd,EAAA,CAAGS,KAAA,CAAMG,CAAC;UACvB,MAAMgF,QAAA,GAAW3F,KAAA,CAAMa,IAAA,CAAKC,KAAK;UACjC6C,iCAAA,CAAkCC,KAAA,EAAOjD,CAAA,GAAI,GAAGgF,QAAA,EAAUxG,OAAO;QAClE;QAED0G,IAAA,CAAKC,QAAA,CAASC,UAAA,CAAWL,QAAA,CAASM,WAAA,GAAc;MACjD;IACF;IAED,KAAK/B,MAAA,CAAOC,IAAA,CAAK3E,IAAA,CAAKgB,WAAW;IAEjC,MAAMuC,iBAAA,CAAkBoC,KAAK;EAC9B;EAAA;AAAA;AAAA;EAKDe,QAAA,EAAU;IACR,KAAK7B,cAAA,CAAe6B,OAAA,CAAS;IAE7B,KAAK3B,oBAAA,CAAqB2B,OAAA,CAAS;IACnC,KAAKpB,sBAAA,CAAuBoB,OAAA,CAAS;IACrC,KAAKnB,kBAAA,CAAmBmB,OAAA,CAAS;IACjC,KAAKlB,YAAA,CAAakB,OAAA,CAAS;IAE3B,MAAMT,QAAA,GAAW,KAAKA,QAAA;IAEtB,SAAS7F,CAAA,GAAI,GAAGA,CAAA,GAAI6F,QAAA,CAAS3F,MAAA,EAAQF,CAAA,IAAK;MACxC,MAAMuG,KAAA,GAAQV,QAAA,CAAS7F,CAAC;MAExB,IAAIuG,KAAA,CAAMC,MAAA,EAAQD,KAAA,CAAMJ,QAAA,CAASG,OAAA,CAAS;IAC3C;EACF;EAAA;EAIDhB,MAAA,EAAQ;IACN,MAAMmB,KAAA,GAAQ;IACd,MAAM5G,GAAA,GAAM,KAAKA,GAAA;IAEjB,SAAS6G,mBAAmBtG,EAAA,EAAI;MAC9B,MAAM+F,QAAA,GAAW,IAAIQ,cAAA,CAAgB;MACrC,MAAMC,QAAA,GAAW,IAAIC,YAAA,EAAc,IAAIzG,EAAA,CAAGS,KAAA,CAAMX,MAAA,IAAU,CAAC;MAC3DiG,QAAA,CAASW,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBH,QAAA,EAAU,CAAC,CAAC;MAElE,OAAOT,QAAA;IACR;IAED,SAASa,iBAAA,EAAmB;MAC1B,OAAO,IAAIC,IAAA,CAAKR,KAAA,CAAMhC,cAAA,EAAgBgC,KAAA,CAAM9B,oBAAoB;IACjE;IAED,SAASuC,mBAAA,EAAqB;MAC5B,OAAO,IAAID,IAAA,CAAKR,KAAA,CAAMhC,cAAA,EAAgBgC,KAAA,CAAMvB,sBAAsB;IACnE;IAED,SAASiC,eAAA,EAAiB;MACxB,OAAO,IAAIF,IAAA,CAAKR,KAAA,CAAMhC,cAAA,EAAgBgC,KAAA,CAAMtB,kBAAkB;IAC/D;IAED,SAASiC,WAAWhH,EAAA,EAAI;MACtB,OAAO,IAAIiH,IAAA,CAAKX,kBAAA,CAAmBtG,EAAE,GAAGqG,KAAA,CAAMrB,YAAY;IAC3D;IAED,SAASpF,CAAA,GAAI,GAAGC,EAAA,GAAKJ,GAAA,CAAIK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5C,MAAMI,EAAA,GAAKP,GAAA,CAAIG,CAAC;MAEhB,KAAKsH,GAAA,CAAIN,gBAAA,EAAkB;MAC3B,KAAKM,GAAA,CAAIJ,kBAAA,EAAoB;MAE7B,SAASlG,CAAA,GAAI,GAAGC,EAAA,GAAKb,EAAA,CAAGS,KAAA,CAAMX,MAAA,EAAQc,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACjD,KAAKsG,GAAA,CAAIH,cAAA,EAAgB;MAC1B;MAED,KAAKG,GAAA,CAAIF,UAAA,CAAWhH,EAAE,CAAC;IACxB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}