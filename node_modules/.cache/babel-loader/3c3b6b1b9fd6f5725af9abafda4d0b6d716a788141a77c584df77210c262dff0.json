{"ast": null, "code": "const isIterable = obj => Symbol.iterator in obj;\nconst hasIterableEntries = value =>\n// HACK: avoid checking entries type\n\"entries\" in value;\nconst compareEntries = (valueA, valueB) => {\n  const mapA = valueA instanceof Map ? valueA : new Map(valueA.entries());\n  const mapB = valueB instanceof Map ? valueB : new Map(valueB.entries());\n  if (mapA.size !== mapB.size) {\n    return false;\n  }\n  for (const [key, value] of mapA) {\n    if (!Object.is(value, mapB.get(key))) {\n      return false;\n    }\n  }\n  return true;\n};\nconst compareIterables = (valueA, valueB) => {\n  const iteratorA = valueA[Symbol.iterator]();\n  const iteratorB = valueB[Symbol.iterator]();\n  let nextA = iteratorA.next();\n  let nextB = iteratorB.next();\n  while (!nextA.done && !nextB.done) {\n    if (!Object.is(nextA.value, nextB.value)) {\n      return false;\n    }\n    nextA = iteratorA.next();\n    nextB = iteratorB.next();\n  }\n  return !!nextA.done && !!nextB.done;\n};\nfunction shallow(valueA, valueB) {\n  if (Object.is(valueA, valueB)) {\n    return true;\n  }\n  if (typeof valueA !== \"object\" || valueA === null || typeof valueB !== \"object\" || valueB === null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(valueA) !== Object.getPrototypeOf(valueB)) {\n    return false;\n  }\n  if (isIterable(valueA) && isIterable(valueB)) {\n    if (hasIterableEntries(valueA) && hasIterableEntries(valueB)) {\n      return compareEntries(valueA, valueB);\n    }\n    return compareIterables(valueA, valueB);\n  }\n  return compareEntries({\n    entries: () => Object.entries(valueA)\n  }, {\n    entries: () => Object.entries(valueB)\n  });\n}\nexport { shallow };", "map": {"version": 3, "names": ["isIterable", "obj", "Symbol", "iterator", "hasIterableEntries", "value", "compareEntries", "valueA", "valueB", "mapA", "Map", "entries", "mapB", "size", "key", "Object", "is", "get", "compareIterables", "iteratorA", "iteratorB", "nextA", "next", "nextB", "done", "shallow", "getPrototypeOf"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/zustand/esm/vanilla/shallow.mjs"], "sourcesContent": ["const isIterable = (obj) => Symbol.iterator in obj;\nconst hasIterableEntries = (value) => (\n  // HACK: avoid checking entries type\n  \"entries\" in value\n);\nconst compareEntries = (valueA, valueB) => {\n  const mapA = valueA instanceof Map ? valueA : new Map(valueA.entries());\n  const mapB = valueB instanceof Map ? valueB : new Map(valueB.entries());\n  if (mapA.size !== mapB.size) {\n    return false;\n  }\n  for (const [key, value] of mapA) {\n    if (!Object.is(value, mapB.get(key))) {\n      return false;\n    }\n  }\n  return true;\n};\nconst compareIterables = (valueA, valueB) => {\n  const iteratorA = valueA[Symbol.iterator]();\n  const iteratorB = valueB[Symbol.iterator]();\n  let nextA = iteratorA.next();\n  let nextB = iteratorB.next();\n  while (!nextA.done && !nextB.done) {\n    if (!Object.is(nextA.value, nextB.value)) {\n      return false;\n    }\n    nextA = iteratorA.next();\n    nextB = iteratorB.next();\n  }\n  return !!nextA.done && !!nextB.done;\n};\nfunction shallow(valueA, valueB) {\n  if (Object.is(valueA, valueB)) {\n    return true;\n  }\n  if (typeof valueA !== \"object\" || valueA === null || typeof valueB !== \"object\" || valueB === null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(valueA) !== Object.getPrototypeOf(valueB)) {\n    return false;\n  }\n  if (isIterable(valueA) && isIterable(valueB)) {\n    if (hasIterableEntries(valueA) && hasIterableEntries(valueB)) {\n      return compareEntries(valueA, valueB);\n    }\n    return compareIterables(valueA, valueB);\n  }\n  return compareEntries(\n    { entries: () => Object.entries(valueA) },\n    { entries: () => Object.entries(valueB) }\n  );\n}\n\nexport { shallow };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAIC,GAAG,IAAKC,MAAM,CAACC,QAAQ,IAAIF,GAAG;AAClD,MAAMG,kBAAkB,GAAIC,KAAK;AAC/B;AACA,SAAS,IAAIA,KACd;AACD,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACzC,MAAMC,IAAI,GAAGF,MAAM,YAAYG,GAAG,GAAGH,MAAM,<PERSON>G,<PERSON>AAIG,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC;EACvE,MAAMC,IAAI,GAAGJ,MAAM,YAAYE,GAAG,GAAGF,MAAM,GAAG,IAAIE,GAAG,CAACF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC;EACvE,IAAIF,IAAI,CAACI,IAAI,KAAKD,IAAI,CAACC,IAAI,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,KAAK,MAAM,CAACC,GAAG,EAAET,KAAK,CAAC,IAAII,IAAI,EAAE;IAC/B,IAAI,CAACM,MAAM,CAACC,EAAE,CAACX,KAAK,EAAEO,IAAI,CAACK,GAAG,CAACH,GAAG,CAAC,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMI,gBAAgB,GAAGA,CAACX,MAAM,EAAEC,MAAM,KAAK;EAC3C,MAAMW,SAAS,GAAGZ,MAAM,CAACL,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAMiB,SAAS,GAAGZ,MAAM,CAACN,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC3C,IAAIkB,KAAK,GAAGF,SAAS,CAACG,IAAI,CAAC,CAAC;EAC5B,IAAIC,KAAK,GAAGH,SAAS,CAACE,IAAI,CAAC,CAAC;EAC5B,OAAO,CAACD,KAAK,CAACG,IAAI,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE;IACjC,IAAI,CAACT,MAAM,CAACC,EAAE,CAACK,KAAK,CAAChB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAAC,EAAE;MACxC,OAAO,KAAK;IACd;IACAgB,KAAK,GAAGF,SAAS,CAACG,IAAI,CAAC,CAAC;IACxBC,KAAK,GAAGH,SAAS,CAACE,IAAI,CAAC,CAAC;EAC1B;EACA,OAAO,CAAC,CAACD,KAAK,CAACG,IAAI,IAAI,CAAC,CAACD,KAAK,CAACC,IAAI;AACrC,CAAC;AACD,SAASC,OAAOA,CAAClB,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIO,MAAM,CAACC,EAAE,CAACT,MAAM,EAAEC,MAAM,CAAC,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IAClG,OAAO,KAAK;EACd;EACA,IAAIO,MAAM,CAACW,cAAc,CAACnB,MAAM,CAAC,KAAKQ,MAAM,CAACW,cAAc,CAAClB,MAAM,CAAC,EAAE;IACnE,OAAO,KAAK;EACd;EACA,IAAIR,UAAU,CAACO,MAAM,CAAC,IAAIP,UAAU,CAACQ,MAAM,CAAC,EAAE;IAC5C,IAAIJ,kBAAkB,CAACG,MAAM,CAAC,IAAIH,kBAAkB,CAACI,MAAM,CAAC,EAAE;MAC5D,OAAOF,cAAc,CAACC,MAAM,EAAEC,MAAM,CAAC;IACvC;IACA,OAAOU,gBAAgB,CAACX,MAAM,EAAEC,MAAM,CAAC;EACzC;EACA,OAAOF,cAAc,CACnB;IAAEK,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACJ,OAAO,CAACJ,MAAM;EAAE,CAAC,EACzC;IAAEI,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACJ,OAAO,CAACH,MAAM;EAAE,CAC1C,CAAC;AACH;AAEA,SAASiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}