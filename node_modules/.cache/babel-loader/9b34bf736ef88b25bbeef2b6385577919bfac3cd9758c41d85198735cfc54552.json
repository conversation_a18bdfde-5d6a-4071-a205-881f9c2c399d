{"ast": null, "code": "import { Matrix4, Ray, Sphere, Vector3 } from 'three';\nconst _inverseMatrix = /* @__PURE__ */new Matrix4();\nconst _ray = /* @__PURE__ */new Ray();\nconst _sphere = /* @__PURE__ */new Sphere();\nconst _vA = /* @__PURE__ */new Vector3();\nfunction meshBounds(raycaster, intersects) {\n  const geometry = this.geometry;\n  const material = this.material;\n  const matrixWorld = this.matrixWorld;\n  if (material === undefined) return;\n  // Checking boundingSphere distance to ray\n  if (geometry.boundingSphere === null) geometry.computeBoundingSphere();\n  _sphere.copy(geometry.boundingSphere);\n  _sphere.applyMatrix4(matrixWorld);\n  if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n  _inverseMatrix.copy(matrixWorld).invert();\n  _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n  // Check boundingBox before continuing\n  if (geometry.boundingBox !== null && _ray.intersectBox(geometry.boundingBox, _vA) === null) return;\n  intersects.push({\n    distance: _vA.distanceTo(raycaster.ray.origin),\n    point: _vA.clone(),\n    object: this\n  });\n}\nexport { meshBounds };", "map": {"version": 3, "names": ["Matrix4", "<PERSON>", "Sphere", "Vector3", "_inverseMatrix", "_ray", "_sphere", "_vA", "meshBounds", "raycaster", "intersects", "geometry", "material", "matrixWorld", "undefined", "boundingSphere", "computeBoundingSphere", "copy", "applyMatrix4", "ray", "intersectsSphere", "invert", "boundingBox", "intersectBox", "push", "distance", "distanceTo", "origin", "point", "clone", "object"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/meshBounds.js"], "sourcesContent": ["import { Matrix4, Ray, Sphere, Vector3 } from 'three';\n\nconst _inverseMatrix = /* @__PURE__ */new Matrix4();\nconst _ray = /* @__PURE__ */new Ray();\nconst _sphere = /* @__PURE__ */new Sphere();\nconst _vA = /* @__PURE__ */new Vector3();\nfunction meshBounds(raycaster, intersects) {\n  const geometry = this.geometry;\n  const material = this.material;\n  const matrixWorld = this.matrixWorld;\n  if (material === undefined) return;\n  // Checking boundingSphere distance to ray\n  if (geometry.boundingSphere === null) geometry.computeBoundingSphere();\n  _sphere.copy(geometry.boundingSphere);\n  _sphere.applyMatrix4(matrixWorld);\n  if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n  _inverseMatrix.copy(matrixWorld).invert();\n  _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n  // Check boundingBox before continuing\n  if (geometry.boundingBox !== null && _ray.intersectBox(geometry.boundingBox, _vA) === null) return;\n  intersects.push({\n    distance: _vA.distanceTo(raycaster.ray.origin),\n    point: _vA.clone(),\n    object: this\n  });\n}\n\nexport { meshBounds };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAErD,MAAMC,cAAc,GAAG,eAAe,IAAIJ,OAAO,CAAC,CAAC;AACnD,MAAMK,IAAI,GAAG,eAAe,IAAIJ,GAAG,CAAC,CAAC;AACrC,MAAMK,OAAO,GAAG,eAAe,IAAIJ,MAAM,CAAC,CAAC;AAC3C,MAAMK,GAAG,GAAG,eAAe,IAAIJ,OAAO,CAAC,CAAC;AACxC,SAASK,UAAUA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACzC,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9B,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9B,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;EACpC,IAAID,QAAQ,KAAKE,SAAS,EAAE;EAC5B;EACA,IAAIH,QAAQ,CAACI,cAAc,KAAK,IAAI,EAAEJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;EACtEV,OAAO,CAACW,IAAI,CAACN,QAAQ,CAACI,cAAc,CAAC;EACrCT,OAAO,CAACY,YAAY,CAACL,WAAW,CAAC;EACjC,IAAIJ,SAAS,CAACU,GAAG,CAACC,gBAAgB,CAACd,OAAO,CAAC,KAAK,KAAK,EAAE;EACvDF,cAAc,CAACa,IAAI,CAACJ,WAAW,CAAC,CAACQ,MAAM,CAAC,CAAC;EACzChB,IAAI,CAACY,IAAI,CAACR,SAAS,CAACU,GAAG,CAAC,CAACD,YAAY,CAACd,cAAc,CAAC;EACrD;EACA,IAAIO,QAAQ,CAACW,WAAW,KAAK,IAAI,IAAIjB,IAAI,CAACkB,YAAY,CAACZ,QAAQ,CAACW,WAAW,EAAEf,GAAG,CAAC,KAAK,IAAI,EAAE;EAC5FG,UAAU,CAACc,IAAI,CAAC;IACdC,QAAQ,EAAElB,GAAG,CAACmB,UAAU,CAACjB,SAAS,CAACU,GAAG,CAACQ,MAAM,CAAC;IAC9CC,KAAK,EAAErB,GAAG,CAACsB,KAAK,CAAC,CAAC;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,SAAStB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}