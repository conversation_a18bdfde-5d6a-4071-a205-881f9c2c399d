{"ast": null, "code": "import { Loader, FileLoader, TextureLoader, Points, LineSegments, Mesh, PointsMaterial, LineBasicMaterial, BufferAttribute, MeshPhongMaterial, BackSide, DoubleSide, FrontSide, Vector2, Color, AddOperation, EquirectangularRefractionMapping, EquirectangularReflectionMapping, ClampToEdgeWrapping, MirroredRepeatWrapping, RepeatWrapping, MeshPhysicalMaterial, MeshStandardMaterial, BufferGeometry, Float32BufferAttribute } from \"three\";\nimport { IFFParser } from \"./lwo/IFFParser.js\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nlet _lwoTree;\nclass LWOLoader extends Loader {\n  constructor(manager, parameters = {}) {\n    super(manager);\n    this.resourcePath = parameters.resourcePath !== void 0 ? parameters.resourcePath : \"\";\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = scope.path === \"\" ? extractParentUrl(url, \"Objects\") : scope.path;\n    const modelName = url.split(path).pop().split(\".\")[0];\n    const loader = new FileLoader(this.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer, path, modelName));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(iffBuffer, path, modelName) {\n    _lwoTree = new IFFParser().parse(iffBuffer);\n    const textureLoader = new TextureLoader(this.manager).setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    return new LWOTreeParser(textureLoader).parse(modelName);\n  }\n}\nclass LWOTreeParser {\n  constructor(textureLoader) {\n    this.textureLoader = textureLoader;\n  }\n  parse(modelName) {\n    this.materials = new MaterialParser(this.textureLoader).parse();\n    this.defaultLayerName = modelName;\n    this.meshes = this.parseLayers();\n    return {\n      materials: this.materials,\n      meshes: this.meshes\n    };\n  }\n  parseLayers() {\n    const meshes = [];\n    const finalMeshes = [];\n    const geometryParser = new GeometryParser();\n    const scope = this;\n    _lwoTree.layers.forEach(function (layer) {\n      const geometry = geometryParser.parse(layer.geometry, layer);\n      const mesh = scope.parseMesh(geometry, layer);\n      meshes[layer.number] = mesh;\n      if (layer.parent === -1) finalMeshes.push(mesh);else meshes[layer.parent].add(mesh);\n    });\n    this.applyPivots(finalMeshes);\n    return finalMeshes;\n  }\n  parseMesh(geometry, layer) {\n    let mesh;\n    const materials = this.getMaterials(geometry.userData.matNames, layer.geometry.type);\n    if (UV1 === \"uv2\") this.duplicateUVs(geometry, materials);\n    if (layer.geometry.type === \"points\") mesh = new Points(geometry, materials);else if (layer.geometry.type === \"lines\") mesh = new LineSegments(geometry, materials);else mesh = new Mesh(geometry, materials);\n    if (layer.name) mesh.name = layer.name;else mesh.name = this.defaultLayerName + \"_layer_\" + layer.number;\n    mesh.userData.pivot = layer.pivot;\n    return mesh;\n  }\n  // TODO: may need to be reversed in z to convert LWO to three.js coordinates\n  applyPivots(meshes) {\n    meshes.forEach(function (mesh) {\n      mesh.traverse(function (child) {\n        const pivot = child.userData.pivot;\n        child.position.x += pivot[0];\n        child.position.y += pivot[1];\n        child.position.z += pivot[2];\n        if (child.parent) {\n          const parentPivot = child.parent.userData.pivot;\n          child.position.x -= parentPivot[0];\n          child.position.y -= parentPivot[1];\n          child.position.z -= parentPivot[2];\n        }\n      });\n    });\n  }\n  getMaterials(namesArray, type) {\n    const materials = [];\n    const scope = this;\n    namesArray.forEach(function (name, i) {\n      materials[i] = scope.getMaterialByName(name);\n    });\n    if (type === \"points\" || type === \"lines\") {\n      materials.forEach(function (mat, i) {\n        const spec = {\n          color: mat.color\n        };\n        if (type === \"points\") {\n          spec.size = 0.1;\n          spec.map = mat.map;\n          spec.morphTargets = mat.morphTargets;\n          materials[i] = new PointsMaterial(spec);\n        } else if (type === \"lines\") {\n          materials[i] = new LineBasicMaterial(spec);\n        }\n      });\n    }\n    const filtered = materials.filter(Boolean);\n    if (filtered.length === 1) return filtered[0];\n    return materials;\n  }\n  getMaterialByName(name) {\n    return this.materials.filter(function (m) {\n      return m.name === name;\n    })[0];\n  }\n  // If the material has an aoMap, duplicate UVs\n  duplicateUVs(geometry, materials) {\n    let duplicateUVs = false;\n    if (!Array.isArray(materials)) {\n      if (materials.aoMap) duplicateUVs = true;\n    } else {\n      materials.forEach(function (material) {\n        if (material.aoMap) duplicateUVs = true;\n      });\n    }\n    if (!duplicateUVs) return;\n    geometry.setAttribute(\"uv2\", new BufferAttribute(geometry.attributes.uv.array, 2));\n  }\n}\nclass MaterialParser {\n  constructor(textureLoader) {\n    this.textureLoader = textureLoader;\n  }\n  parse() {\n    const materials = [];\n    this.textures = {};\n    for (const name in _lwoTree.materials) {\n      if (_lwoTree.format === \"LWO3\") {\n        materials.push(this.parseMaterial(_lwoTree.materials[name], name, _lwoTree.textures));\n      } else if (_lwoTree.format === \"LWO2\") {\n        materials.push(this.parseMaterialLwo2(_lwoTree.materials[name], name, _lwoTree.textures));\n      }\n    }\n    return materials;\n  }\n  parseMaterial(materialData, name, textures) {\n    let params = {\n      name,\n      side: this.getSide(materialData.attributes),\n      flatShading: this.getSmooth(materialData.attributes)\n    };\n    const connections = this.parseConnections(materialData.connections, materialData.nodes);\n    const maps = this.parseTextureNodes(connections.maps);\n    this.parseAttributeImageMaps(connections.attributes, textures, maps, materialData.maps);\n    const attributes = this.parseAttributes(connections.attributes, maps);\n    this.parseEnvMap(connections, maps, attributes);\n    params = Object.assign(maps, params);\n    params = Object.assign(params, attributes);\n    const materialType = this.getMaterialType(connections.attributes);\n    return new materialType(params);\n  }\n  parseMaterialLwo2(materialData, name) {\n    let params = {\n      name,\n      side: this.getSide(materialData.attributes),\n      flatShading: this.getSmooth(materialData.attributes)\n    };\n    const attributes = this.parseAttributes(materialData.attributes, {});\n    params = Object.assign(params, attributes);\n    return new MeshPhongMaterial(params);\n  }\n  // Note: converting from left to right handed coords by switching x -> -x in vertices, and\n  // then switching mat FrontSide -> BackSide\n  // NB: this means that FrontSide and BackSide have been switched!\n  getSide(attributes) {\n    if (!attributes.side) return BackSide;\n    switch (attributes.side) {\n      case 0:\n      case 1:\n        return BackSide;\n      case 2:\n        return FrontSide;\n      case 3:\n        return DoubleSide;\n    }\n  }\n  getSmooth(attributes) {\n    if (!attributes.smooth) return true;\n    return !attributes.smooth;\n  }\n  parseConnections(connections, nodes) {\n    const materialConnections = {\n      maps: {}\n    };\n    const inputName = connections.inputName;\n    const inputNodeName = connections.inputNodeName;\n    const nodeName = connections.nodeName;\n    const scope = this;\n    inputName.forEach(function (name, index) {\n      if (name === \"Material\") {\n        const matNode = scope.getNodeByRefName(inputNodeName[index], nodes);\n        materialConnections.attributes = matNode.attributes;\n        materialConnections.envMap = matNode.fileName;\n        materialConnections.name = inputNodeName[index];\n      }\n    });\n    nodeName.forEach(function (name, index) {\n      if (name === materialConnections.name) {\n        materialConnections.maps[inputName[index]] = scope.getNodeByRefName(inputNodeName[index], nodes);\n      }\n    });\n    return materialConnections;\n  }\n  getNodeByRefName(refName, nodes) {\n    for (const name in nodes) {\n      if (nodes[name].refName === refName) return nodes[name];\n    }\n  }\n  parseTextureNodes(textureNodes) {\n    const maps = {};\n    for (const name in textureNodes) {\n      const node = textureNodes[name];\n      const path = node.fileName;\n      if (!path) return;\n      const texture = this.loadTexture(path);\n      if (node.widthWrappingMode !== void 0) texture.wrapS = this.getWrappingType(node.widthWrappingMode);\n      if (node.heightWrappingMode !== void 0) texture.wrapT = this.getWrappingType(node.heightWrappingMode);\n      switch (name) {\n        case \"Color\":\n          maps.map = texture;\n          break;\n        case \"Roughness\":\n          maps.roughnessMap = texture;\n          maps.roughness = 0.5;\n          break;\n        case \"Specular\":\n          maps.specularMap = texture;\n          maps.specular = 16777215;\n          break;\n        case \"Luminous\":\n          maps.emissiveMap = texture;\n          maps.emissive = 8421504;\n          break;\n        case \"Luminous Color\":\n          maps.emissive = 8421504;\n          break;\n        case \"Metallic\":\n          maps.metalnessMap = texture;\n          maps.metalness = 0.5;\n          break;\n        case \"Transparency\":\n        case \"Alpha\":\n          maps.alphaMap = texture;\n          maps.transparent = true;\n          break;\n        case \"Normal\":\n          maps.normalMap = texture;\n          if (node.amplitude !== void 0) maps.normalScale = new Vector2(node.amplitude, node.amplitude);\n          break;\n        case \"Bump\":\n          maps.bumpMap = texture;\n          break;\n      }\n    }\n    if (maps.roughnessMap && maps.specularMap) delete maps.specularMap;\n    return maps;\n  }\n  // maps can also be defined on individual material attributes, parse those here\n  // This occurs on Standard (Phong) surfaces\n  parseAttributeImageMaps(attributes, textures, maps) {\n    for (const name in attributes) {\n      const attribute = attributes[name];\n      if (attribute.maps) {\n        const mapData = attribute.maps[0];\n        const path = this.getTexturePathByIndex(mapData.imageIndex, textures);\n        if (!path) return;\n        const texture = this.loadTexture(path);\n        if (mapData.wrap !== void 0) texture.wrapS = this.getWrappingType(mapData.wrap.w);\n        if (mapData.wrap !== void 0) texture.wrapT = this.getWrappingType(mapData.wrap.h);\n        switch (name) {\n          case \"Color\":\n            maps.map = texture;\n            break;\n          case \"Diffuse\":\n            maps.aoMap = texture;\n            break;\n          case \"Roughness\":\n            maps.roughnessMap = texture;\n            maps.roughness = 1;\n            break;\n          case \"Specular\":\n            maps.specularMap = texture;\n            maps.specular = 16777215;\n            break;\n          case \"Luminosity\":\n            maps.emissiveMap = texture;\n            maps.emissive = 8421504;\n            break;\n          case \"Metallic\":\n            maps.metalnessMap = texture;\n            maps.metalness = 1;\n            break;\n          case \"Transparency\":\n          case \"Alpha\":\n            maps.alphaMap = texture;\n            maps.transparent = true;\n            break;\n          case \"Normal\":\n            maps.normalMap = texture;\n            break;\n          case \"Bump\":\n            maps.bumpMap = texture;\n            break;\n        }\n      }\n    }\n  }\n  parseAttributes(attributes, maps) {\n    const params = {};\n    if (attributes.Color && !maps.map) {\n      params.color = new Color().fromArray(attributes.Color.value);\n    } else {\n      params.color = new Color();\n    }\n    if (attributes.Transparency && attributes.Transparency.value !== 0) {\n      params.opacity = 1 - attributes.Transparency.value;\n      params.transparent = true;\n    }\n    if (attributes[\"Bump Height\"]) params.bumpScale = attributes[\"Bump Height\"].value * 0.1;\n    if (attributes[\"Refraction Index\"]) params.refractionRatio = 1 / attributes[\"Refraction Index\"].value;\n    this.parsePhysicalAttributes(params, attributes, maps);\n    this.parseStandardAttributes(params, attributes, maps);\n    this.parsePhongAttributes(params, attributes, maps);\n    return params;\n  }\n  parsePhysicalAttributes(params, attributes) {\n    if (attributes.Clearcoat && attributes.Clearcoat.value > 0) {\n      params.clearcoat = attributes.Clearcoat.value;\n      if (attributes[\"Clearcoat Gloss\"]) {\n        params.clearcoatRoughness = 0.5 * (1 - attributes[\"Clearcoat Gloss\"].value);\n      }\n    }\n  }\n  parseStandardAttributes(params, attributes, maps) {\n    if (attributes.Luminous) {\n      params.emissiveIntensity = attributes.Luminous.value;\n      if (attributes[\"Luminous Color\"] && !maps.emissive) {\n        params.emissive = new Color().fromArray(attributes[\"Luminous Color\"].value);\n      } else {\n        params.emissive = new Color(8421504);\n      }\n    }\n    if (attributes.Roughness && !maps.roughnessMap) params.roughness = attributes.Roughness.value;\n    if (attributes.Metallic && !maps.metalnessMap) params.metalness = attributes.Metallic.value;\n  }\n  parsePhongAttributes(params, attributes, maps) {\n    if (attributes.Diffuse) params.color.multiplyScalar(attributes.Diffuse.value);\n    if (attributes.Reflection) {\n      params.reflectivity = attributes.Reflection.value;\n      params.combine = AddOperation;\n    }\n    if (attributes.Luminosity) {\n      params.emissiveIntensity = attributes.Luminosity.value;\n      if (!maps.emissiveMap && !maps.map) {\n        params.emissive = params.color;\n      } else {\n        params.emissive = new Color(8421504);\n      }\n    }\n    if (!attributes.Roughness && attributes.Specular && !maps.specularMap) {\n      if (attributes[\"Color Highlight\"]) {\n        params.specular = new Color().setScalar(attributes.Specular.value).lerp(params.color.clone().multiplyScalar(attributes.Specular.value), attributes[\"Color Highlight\"].value);\n      } else {\n        params.specular = new Color().setScalar(attributes.Specular.value);\n      }\n    }\n    if (params.specular && attributes.Glossiness) {\n      params.shininess = 7 + Math.pow(2, attributes.Glossiness.value * 12 + 2);\n    }\n  }\n  parseEnvMap(connections, maps, attributes) {\n    if (connections.envMap) {\n      const envMap = this.loadTexture(connections.envMap);\n      if (attributes.transparent && attributes.opacity < 0.999) {\n        envMap.mapping = EquirectangularRefractionMapping;\n        if (attributes.reflectivity !== void 0) {\n          delete attributes.reflectivity;\n          delete attributes.combine;\n        }\n        if (attributes.metalness !== void 0) {\n          delete attributes.metalness;\n        }\n      } else {\n        envMap.mapping = EquirectangularReflectionMapping;\n      }\n      maps.envMap = envMap;\n    }\n  }\n  // get texture defined at top level by its index\n  getTexturePathByIndex(index) {\n    let fileName = \"\";\n    if (!_lwoTree.textures) return fileName;\n    _lwoTree.textures.forEach(function (texture) {\n      if (texture.index === index) fileName = texture.fileName;\n    });\n    return fileName;\n  }\n  loadTexture(path) {\n    if (!path) return null;\n    const texture = this.textureLoader.load(path, void 0, void 0, function () {\n      console.warn(\"LWOLoader: non-standard resource hierarchy. Use `resourcePath` parameter to specify root content directory.\");\n    });\n    return texture;\n  }\n  // 0 = Reset, 1 = Repeat, 2 = Mirror, 3 = Edge\n  getWrappingType(num) {\n    switch (num) {\n      case 0:\n        console.warn('LWOLoader: \"Reset\" texture wrapping type is not supported in three.js');\n        return ClampToEdgeWrapping;\n      case 1:\n        return RepeatWrapping;\n      case 2:\n        return MirroredRepeatWrapping;\n      case 3:\n        return ClampToEdgeWrapping;\n    }\n  }\n  getMaterialType(nodeData) {\n    if (nodeData.Clearcoat && nodeData.Clearcoat.value > 0) return MeshPhysicalMaterial;\n    if (nodeData.Roughness) return MeshStandardMaterial;\n    return MeshPhongMaterial;\n  }\n}\nclass GeometryParser {\n  parse(geoData, layer) {\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(geoData.points, 3));\n    const indices = this.splitIndices(geoData.vertexIndices, geoData.polygonDimensions);\n    geometry.setIndex(indices);\n    this.parseGroups(geometry, geoData);\n    geometry.computeVertexNormals();\n    this.parseUVs(geometry, layer, indices);\n    this.parseMorphTargets(geometry, layer, indices);\n    geometry.translate(-layer.pivot[0], -layer.pivot[1], -layer.pivot[2]);\n    return geometry;\n  }\n  // split quads into tris\n  splitIndices(indices, polygonDimensions) {\n    const remappedIndices = [];\n    let i = 0;\n    polygonDimensions.forEach(function (dim) {\n      if (dim < 4) {\n        for (let k = 0; k < dim; k++) remappedIndices.push(indices[i + k]);\n      } else if (dim === 4) {\n        remappedIndices.push(indices[i], indices[i + 1], indices[i + 2], indices[i], indices[i + 2], indices[i + 3]);\n      } else if (dim > 4) {\n        for (let k = 1; k < dim - 1; k++) {\n          remappedIndices.push(indices[i], indices[i + k], indices[i + k + 1]);\n        }\n        console.warn(\"LWOLoader: polygons with greater than 4 sides are not supported\");\n      }\n      i += dim;\n    });\n    return remappedIndices;\n  }\n  // NOTE: currently ignoring poly indices and assuming that they are intelligently ordered\n  parseGroups(geometry, geoData) {\n    const tags = _lwoTree.tags;\n    const matNames = [];\n    let elemSize = 3;\n    if (geoData.type === \"lines\") elemSize = 2;\n    if (geoData.type === \"points\") elemSize = 1;\n    const remappedIndices = this.splitMaterialIndices(geoData.polygonDimensions, geoData.materialIndices);\n    let indexNum = 0;\n    const indexPairs = {};\n    let prevMaterialIndex;\n    let materialIndex;\n    let prevStart = 0;\n    let currentCount = 0;\n    for (let i = 0; i < remappedIndices.length; i += 2) {\n      materialIndex = remappedIndices[i + 1];\n      if (i === 0) matNames[indexNum] = tags[materialIndex];\n      if (prevMaterialIndex === void 0) prevMaterialIndex = materialIndex;\n      if (materialIndex !== prevMaterialIndex) {\n        let currentIndex;\n        if (indexPairs[tags[prevMaterialIndex]]) {\n          currentIndex = indexPairs[tags[prevMaterialIndex]];\n        } else {\n          currentIndex = indexNum;\n          indexPairs[tags[prevMaterialIndex]] = indexNum;\n          matNames[indexNum] = tags[prevMaterialIndex];\n          indexNum++;\n        }\n        geometry.addGroup(prevStart, currentCount, currentIndex);\n        prevStart += currentCount;\n        prevMaterialIndex = materialIndex;\n        currentCount = 0;\n      }\n      currentCount += elemSize;\n    }\n    if (geometry.groups.length > 0) {\n      let currentIndex;\n      if (indexPairs[tags[materialIndex]]) {\n        currentIndex = indexPairs[tags[materialIndex]];\n      } else {\n        currentIndex = indexNum;\n        indexPairs[tags[materialIndex]] = indexNum;\n        matNames[indexNum] = tags[materialIndex];\n      }\n      geometry.addGroup(prevStart, currentCount, currentIndex);\n    }\n    geometry.userData.matNames = matNames;\n  }\n  splitMaterialIndices(polygonDimensions, indices) {\n    const remappedIndices = [];\n    polygonDimensions.forEach(function (dim, i) {\n      if (dim <= 3) {\n        remappedIndices.push(indices[i * 2], indices[i * 2 + 1]);\n      } else if (dim === 4) {\n        remappedIndices.push(indices[i * 2], indices[i * 2 + 1], indices[i * 2], indices[i * 2 + 1]);\n      } else {\n        for (let k = 0; k < dim - 2; k++) {\n          remappedIndices.push(indices[i * 2], indices[i * 2 + 1]);\n        }\n      }\n    });\n    return remappedIndices;\n  }\n  // UV maps:\n  // 1: are defined via index into an array of points, not into a geometry\n  // - the geometry is also defined by an index into this array, but the indexes may not match\n  // 2: there can be any number of UV maps for a single geometry. Here these are combined,\n  // \twith preference given to the first map encountered\n  // 3: UV maps can be partial - that is, defined for only a part of the geometry\n  // 4: UV maps can be VMAP or VMAD (discontinuous, to allow for seams). In practice, most\n  // UV maps are defined as partially VMAP and partially VMAD\n  // VMADs are currently not supported\n  parseUVs(geometry, layer) {\n    const remappedUVs = Array.from(Array(geometry.attributes.position.count * 2), function () {\n      return 0;\n    });\n    for (const name in layer.uvs) {\n      const uvs = layer.uvs[name].uvs;\n      const uvIndices = layer.uvs[name].uvIndices;\n      uvIndices.forEach(function (i, j) {\n        remappedUVs[i * 2] = uvs[j * 2];\n        remappedUVs[i * 2 + 1] = uvs[j * 2 + 1];\n      });\n    }\n    geometry.setAttribute(\"uv\", new Float32BufferAttribute(remappedUVs, 2));\n  }\n  parseMorphTargets(geometry, layer) {\n    let num = 0;\n    for (const name in layer.morphTargets) {\n      const remappedPoints = geometry.attributes.position.array.slice();\n      if (!geometry.morphAttributes.position) geometry.morphAttributes.position = [];\n      const morphPoints = layer.morphTargets[name].points;\n      const morphIndices = layer.morphTargets[name].indices;\n      const type = layer.morphTargets[name].type;\n      morphIndices.forEach(function (i, j) {\n        if (type === \"relative\") {\n          remappedPoints[i * 3] += morphPoints[j * 3];\n          remappedPoints[i * 3 + 1] += morphPoints[j * 3 + 1];\n          remappedPoints[i * 3 + 2] += morphPoints[j * 3 + 2];\n        } else {\n          remappedPoints[i * 3] = morphPoints[j * 3];\n          remappedPoints[i * 3 + 1] = morphPoints[j * 3 + 1];\n          remappedPoints[i * 3 + 2] = morphPoints[j * 3 + 2];\n        }\n      });\n      geometry.morphAttributes.position[num] = new Float32BufferAttribute(remappedPoints, 3);\n      geometry.morphAttributes.position[num].name = name;\n      num++;\n    }\n    geometry.morphTargetsRelative = false;\n  }\n}\nfunction extractParentUrl(url, dir) {\n  const index = url.indexOf(dir);\n  if (index === -1) return \"./\";\n  return url.substr(0, index);\n}\nexport { LWOLoader };", "map": {"version": 3, "names": ["_lwoTree", "LWOLoader", "Loader", "constructor", "manager", "parameters", "resourcePath", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "extractParentUrl", "modelName", "split", "pop", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "buffer", "parse", "e", "console", "error", "itemError", "iffBuffer", "IFFParser", "textureLoader", "TextureLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "LWOTreeParser", "materials", "Material<PERSON><PERSON>er", "defaultLayerName", "meshes", "parseLayers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Geo<PERSON><PERSON><PERSON><PERSON>", "layers", "for<PERSON>ach", "layer", "geometry", "mesh", "<PERSON><PERSON><PERSON><PERSON>", "number", "parent", "push", "add", "applyPivots", "getMaterials", "userData", "<PERSON><PERSON><PERSON><PERSON>", "type", "UV1", "duplicateUVs", "Points", "LineSegments", "<PERSON><PERSON>", "name", "pivot", "traverse", "child", "position", "x", "y", "z", "parentPivot", "namesArray", "i", "getMaterialByName", "mat", "spec", "color", "size", "map", "morphTargets", "PointsMaterial", "LineBasicMaterial", "filtered", "filter", "Boolean", "length", "m", "Array", "isArray", "aoMap", "material", "setAttribute", "BufferAttribute", "attributes", "uv", "array", "textures", "format", "parseMaterial", "parseMaterialLwo2", "materialData", "params", "side", "getSide", "flatShading", "getSmooth", "connections", "parseConnections", "nodes", "maps", "parseTextureNodes", "parseAttributeImageMaps", "parseAttributes", "parseEnvMap", "Object", "assign", "materialType", "getMaterialType", "MeshPhongMaterial", "BackSide", "FrontSide", "DoubleSide", "smooth", "materialConnections", "inputName", "inputNodeName", "nodeName", "index", "matNode", "getNodeByRefName", "envMap", "fileName", "refName", "textureNodes", "node", "texture", "loadTexture", "widthWrappingMode", "wrapS", "getWrappingType", "heightWrappingMode", "wrapT", "roughnessMap", "roughness", "specularMap", "specular", "emissiveMap", "emissive", "metalnessMap", "metalness", "alphaMap", "transparent", "normalMap", "amplitude", "normalScale", "Vector2", "bumpMap", "attribute", "mapData", "getTexturePathByIndex", "imageIndex", "wrap", "w", "h", "Color", "fromArray", "value", "Transparency", "opacity", "bumpScale", "refractionRatio", "parsePhysicalAttributes", "parseStandardAttributes", "parsePhongAttributes", "Clearcoat", "clearcoat", "clearcoatRoughness", "Luminous", "emissiveIntensity", "Roughness", "Metallic", "Diffuse", "multiplyScalar", "Reflection", "reflectivity", "combine", "AddOperation", "Luminosity", "Specular", "setScalar", "lerp", "clone", "Glossiness", "shininess", "Math", "pow", "mapping", "EquirectangularRefractionMapping", "EquirectangularReflectionMapping", "warn", "num", "ClampToEdgeWrapping", "RepeatWrapping", "MirroredRepeatWrapping", "nodeData", "MeshPhysicalMaterial", "MeshStandardMaterial", "geoData", "BufferGeometry", "Float32BufferAttribute", "points", "indices", "splitIndices", "vertexIndices", "polygonDimensions", "setIndex", "parseGroups", "computeVertexNormals", "parseUVs", "parseMorphTargets", "translate", "remappedIndices", "dim", "k", "tags", "elemSize", "splitMaterialIndices", "materialIndices", "indexNum", "indexPairs", "prevMaterialIndex", "materialIndex", "prevStart", "currentCount", "currentIndex", "addGroup", "groups", "remappedUVs", "from", "count", "uvs", "uvIndices", "j", "remappedPoints", "slice", "morphAttributes", "morphPoints", "morphIndices", "morphTargetsRelative", "dir", "indexOf", "substr"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/loaders/LWOLoader.js"], "sourcesContent": ["/**\n * @version 1.1.1\n *\n * @desc Load files in LWO3 and LWO2 format on Three.js\n *\n * LWO3 format specification:\n * \thttp://static.lightwave3d.com/sdk/2018/html/filefmts/lwo3.html\n *\n * LWO2 format specification:\n * \thttp://static.lightwave3d.com/sdk/2018/html/filefmts/lwo2.html\n *\n **/\n\nimport {\n  AddOperation,\n  BackSide,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DoubleSide,\n  EquirectangularReflectionMapping,\n  EquirectangularRefractionMapping,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  Mesh,\n  MeshPhongMaterial,\n  MeshPhysicalMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  Points,\n  PointsMaterial,\n  RepeatWrapping,\n  TextureLoader,\n  Vector2,\n} from 'three'\n\nimport { IFFParser } from './lwo/IFFParser.js'\nimport { UV1 } from '../_polyfill/uv1.ts'\n\nlet _lwoTree\n\nclass LWOLoader extends Loader {\n  constructor(manager, parameters = {}) {\n    super(manager)\n\n    this.resourcePath = parameters.resourcePath !== undefined ? parameters.resourcePath : ''\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? extractParentUrl(url, 'Objects') : scope.path\n\n    // give the mesh a default name based on the filename\n    const modelName = url.split(path).pop().split('.')[0]\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n\n    loader.load(\n      url,\n      function (buffer) {\n        // console.time( 'Total parsing: ' );\n\n        try {\n          onLoad(scope.parse(buffer, path, modelName))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n\n        // console.timeEnd( 'Total parsing: ' );\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(iffBuffer, path, modelName) {\n    _lwoTree = new IFFParser().parse(iffBuffer)\n\n    // console.log( 'lwoTree', lwoTree );\n\n    const textureLoader = new TextureLoader(this.manager)\n      .setPath(this.resourcePath || path)\n      .setCrossOrigin(this.crossOrigin)\n\n    return new LWOTreeParser(textureLoader).parse(modelName)\n  }\n}\n\n// Parse the lwoTree object\nclass LWOTreeParser {\n  constructor(textureLoader) {\n    this.textureLoader = textureLoader\n  }\n\n  parse(modelName) {\n    this.materials = new MaterialParser(this.textureLoader).parse()\n    this.defaultLayerName = modelName\n\n    this.meshes = this.parseLayers()\n\n    return {\n      materials: this.materials,\n      meshes: this.meshes,\n    }\n  }\n\n  parseLayers() {\n    // array of all meshes for building hierarchy\n    const meshes = []\n\n    // final array containing meshes with scene graph hierarchy set up\n    const finalMeshes = []\n\n    const geometryParser = new GeometryParser()\n\n    const scope = this\n    _lwoTree.layers.forEach(function (layer) {\n      const geometry = geometryParser.parse(layer.geometry, layer)\n\n      const mesh = scope.parseMesh(geometry, layer)\n\n      meshes[layer.number] = mesh\n\n      if (layer.parent === -1) finalMeshes.push(mesh)\n      else meshes[layer.parent].add(mesh)\n    })\n\n    this.applyPivots(finalMeshes)\n\n    return finalMeshes\n  }\n\n  parseMesh(geometry, layer) {\n    let mesh\n\n    const materials = this.getMaterials(geometry.userData.matNames, layer.geometry.type)\n\n    if (UV1 === 'uv2') this.duplicateUVs(geometry, materials)\n\n    if (layer.geometry.type === 'points') mesh = new Points(geometry, materials)\n    else if (layer.geometry.type === 'lines') mesh = new LineSegments(geometry, materials)\n    else mesh = new Mesh(geometry, materials)\n\n    if (layer.name) mesh.name = layer.name\n    else mesh.name = this.defaultLayerName + '_layer_' + layer.number\n\n    mesh.userData.pivot = layer.pivot\n\n    return mesh\n  }\n\n  // TODO: may need to be reversed in z to convert LWO to three.js coordinates\n  applyPivots(meshes) {\n    meshes.forEach(function (mesh) {\n      mesh.traverse(function (child) {\n        const pivot = child.userData.pivot\n\n        child.position.x += pivot[0]\n        child.position.y += pivot[1]\n        child.position.z += pivot[2]\n\n        if (child.parent) {\n          const parentPivot = child.parent.userData.pivot\n\n          child.position.x -= parentPivot[0]\n          child.position.y -= parentPivot[1]\n          child.position.z -= parentPivot[2]\n        }\n      })\n    })\n  }\n\n  getMaterials(namesArray, type) {\n    const materials = []\n\n    const scope = this\n\n    namesArray.forEach(function (name, i) {\n      materials[i] = scope.getMaterialByName(name)\n    })\n\n    // convert materials to line or point mats if required\n    if (type === 'points' || type === 'lines') {\n      materials.forEach(function (mat, i) {\n        const spec = {\n          color: mat.color,\n        }\n\n        if (type === 'points') {\n          spec.size = 0.1\n          spec.map = mat.map\n          spec.morphTargets = mat.morphTargets\n          materials[i] = new PointsMaterial(spec)\n        } else if (type === 'lines') {\n          materials[i] = new LineBasicMaterial(spec)\n        }\n      })\n    }\n\n    // if there is only one material, return that directly instead of array\n    const filtered = materials.filter(Boolean)\n    if (filtered.length === 1) return filtered[0]\n\n    return materials\n  }\n\n  getMaterialByName(name) {\n    return this.materials.filter(function (m) {\n      return m.name === name\n    })[0]\n  }\n\n  // If the material has an aoMap, duplicate UVs\n  duplicateUVs(geometry, materials) {\n    let duplicateUVs = false\n\n    if (!Array.isArray(materials)) {\n      if (materials.aoMap) duplicateUVs = true\n    } else {\n      materials.forEach(function (material) {\n        if (material.aoMap) duplicateUVs = true\n      })\n    }\n\n    if (!duplicateUVs) return\n\n    geometry.setAttribute('uv2', new BufferAttribute(geometry.attributes.uv.array, 2))\n  }\n}\n\nclass MaterialParser {\n  constructor(textureLoader) {\n    this.textureLoader = textureLoader\n  }\n\n  parse() {\n    const materials = []\n    this.textures = {}\n\n    for (const name in _lwoTree.materials) {\n      if (_lwoTree.format === 'LWO3') {\n        materials.push(this.parseMaterial(_lwoTree.materials[name], name, _lwoTree.textures))\n      } else if (_lwoTree.format === 'LWO2') {\n        materials.push(this.parseMaterialLwo2(_lwoTree.materials[name], name, _lwoTree.textures))\n      }\n    }\n\n    return materials\n  }\n\n  parseMaterial(materialData, name, textures) {\n    let params = {\n      name: name,\n      side: this.getSide(materialData.attributes),\n      flatShading: this.getSmooth(materialData.attributes),\n    }\n\n    const connections = this.parseConnections(materialData.connections, materialData.nodes)\n\n    const maps = this.parseTextureNodes(connections.maps)\n\n    this.parseAttributeImageMaps(connections.attributes, textures, maps, materialData.maps)\n\n    const attributes = this.parseAttributes(connections.attributes, maps)\n\n    this.parseEnvMap(connections, maps, attributes)\n\n    params = Object.assign(maps, params)\n    params = Object.assign(params, attributes)\n\n    const materialType = this.getMaterialType(connections.attributes)\n\n    return new materialType(params)\n  }\n\n  parseMaterialLwo2(materialData, name /*, textures*/) {\n    let params = {\n      name: name,\n      side: this.getSide(materialData.attributes),\n      flatShading: this.getSmooth(materialData.attributes),\n    }\n\n    const attributes = this.parseAttributes(materialData.attributes, {})\n    params = Object.assign(params, attributes)\n    return new MeshPhongMaterial(params)\n  }\n\n  // Note: converting from left to right handed coords by switching x -> -x in vertices, and\n  // then switching mat FrontSide -> BackSide\n  // NB: this means that FrontSide and BackSide have been switched!\n  getSide(attributes) {\n    if (!attributes.side) return BackSide\n\n    switch (attributes.side) {\n      case 0:\n      case 1:\n        return BackSide\n      case 2:\n        return FrontSide\n      case 3:\n        return DoubleSide\n    }\n  }\n\n  getSmooth(attributes) {\n    if (!attributes.smooth) return true\n    return !attributes.smooth\n  }\n\n  parseConnections(connections, nodes) {\n    const materialConnections = {\n      maps: {},\n    }\n\n    const inputName = connections.inputName\n    const inputNodeName = connections.inputNodeName\n    const nodeName = connections.nodeName\n\n    const scope = this\n    inputName.forEach(function (name, index) {\n      if (name === 'Material') {\n        const matNode = scope.getNodeByRefName(inputNodeName[index], nodes)\n        materialConnections.attributes = matNode.attributes\n        materialConnections.envMap = matNode.fileName\n        materialConnections.name = inputNodeName[index]\n      }\n    })\n\n    nodeName.forEach(function (name, index) {\n      if (name === materialConnections.name) {\n        materialConnections.maps[inputName[index]] = scope.getNodeByRefName(inputNodeName[index], nodes)\n      }\n    })\n\n    return materialConnections\n  }\n\n  getNodeByRefName(refName, nodes) {\n    for (const name in nodes) {\n      if (nodes[name].refName === refName) return nodes[name]\n    }\n  }\n\n  parseTextureNodes(textureNodes) {\n    const maps = {}\n\n    for (const name in textureNodes) {\n      const node = textureNodes[name]\n      const path = node.fileName\n\n      if (!path) return\n\n      const texture = this.loadTexture(path)\n\n      if (node.widthWrappingMode !== undefined) texture.wrapS = this.getWrappingType(node.widthWrappingMode)\n      if (node.heightWrappingMode !== undefined) texture.wrapT = this.getWrappingType(node.heightWrappingMode)\n\n      switch (name) {\n        case 'Color':\n          maps.map = texture\n          break\n        case 'Roughness':\n          maps.roughnessMap = texture\n          maps.roughness = 0.5\n          break\n        case 'Specular':\n          maps.specularMap = texture\n          maps.specular = 0xffffff\n          break\n        case 'Luminous':\n          maps.emissiveMap = texture\n          maps.emissive = 0x808080\n          break\n        case 'Luminous Color':\n          maps.emissive = 0x808080\n          break\n        case 'Metallic':\n          maps.metalnessMap = texture\n          maps.metalness = 0.5\n          break\n        case 'Transparency':\n        case 'Alpha':\n          maps.alphaMap = texture\n          maps.transparent = true\n          break\n        case 'Normal':\n          maps.normalMap = texture\n          if (node.amplitude !== undefined) maps.normalScale = new Vector2(node.amplitude, node.amplitude)\n          break\n        case 'Bump':\n          maps.bumpMap = texture\n          break\n      }\n    }\n\n    // LWO BSDF materials can have both spec and rough, but this is not valid in three\n    if (maps.roughnessMap && maps.specularMap) delete maps.specularMap\n\n    return maps\n  }\n\n  // maps can also be defined on individual material attributes, parse those here\n  // This occurs on Standard (Phong) surfaces\n  parseAttributeImageMaps(attributes, textures, maps) {\n    for (const name in attributes) {\n      const attribute = attributes[name]\n\n      if (attribute.maps) {\n        const mapData = attribute.maps[0]\n\n        const path = this.getTexturePathByIndex(mapData.imageIndex, textures)\n        if (!path) return\n\n        const texture = this.loadTexture(path)\n\n        if (mapData.wrap !== undefined) texture.wrapS = this.getWrappingType(mapData.wrap.w)\n        if (mapData.wrap !== undefined) texture.wrapT = this.getWrappingType(mapData.wrap.h)\n\n        switch (name) {\n          case 'Color':\n            maps.map = texture\n            break\n          case 'Diffuse':\n            maps.aoMap = texture\n            break\n          case 'Roughness':\n            maps.roughnessMap = texture\n            maps.roughness = 1\n            break\n          case 'Specular':\n            maps.specularMap = texture\n            maps.specular = 0xffffff\n            break\n          case 'Luminosity':\n            maps.emissiveMap = texture\n            maps.emissive = 0x808080\n            break\n          case 'Metallic':\n            maps.metalnessMap = texture\n            maps.metalness = 1\n            break\n          case 'Transparency':\n          case 'Alpha':\n            maps.alphaMap = texture\n            maps.transparent = true\n            break\n          case 'Normal':\n            maps.normalMap = texture\n            break\n          case 'Bump':\n            maps.bumpMap = texture\n            break\n        }\n      }\n    }\n  }\n\n  parseAttributes(attributes, maps) {\n    const params = {}\n\n    // don't use color data if color map is present\n    if (attributes.Color && !maps.map) {\n      params.color = new Color().fromArray(attributes.Color.value)\n    } else {\n      params.color = new Color()\n    }\n\n    if (attributes.Transparency && attributes.Transparency.value !== 0) {\n      params.opacity = 1 - attributes.Transparency.value\n      params.transparent = true\n    }\n\n    if (attributes['Bump Height']) params.bumpScale = attributes['Bump Height'].value * 0.1\n\n    if (attributes['Refraction Index']) params.refractionRatio = 1 / attributes['Refraction Index'].value\n\n    this.parsePhysicalAttributes(params, attributes, maps)\n    this.parseStandardAttributes(params, attributes, maps)\n    this.parsePhongAttributes(params, attributes, maps)\n\n    return params\n  }\n\n  parsePhysicalAttributes(params, attributes /*, maps*/) {\n    if (attributes.Clearcoat && attributes.Clearcoat.value > 0) {\n      params.clearcoat = attributes.Clearcoat.value\n\n      if (attributes['Clearcoat Gloss']) {\n        params.clearcoatRoughness = 0.5 * (1 - attributes['Clearcoat Gloss'].value)\n      }\n    }\n  }\n\n  parseStandardAttributes(params, attributes, maps) {\n    if (attributes.Luminous) {\n      params.emissiveIntensity = attributes.Luminous.value\n\n      if (attributes['Luminous Color'] && !maps.emissive) {\n        params.emissive = new Color().fromArray(attributes['Luminous Color'].value)\n      } else {\n        params.emissive = new Color(0x808080)\n      }\n    }\n\n    if (attributes.Roughness && !maps.roughnessMap) params.roughness = attributes.Roughness.value\n    if (attributes.Metallic && !maps.metalnessMap) params.metalness = attributes.Metallic.value\n  }\n\n  parsePhongAttributes(params, attributes, maps) {\n    if (attributes.Diffuse) params.color.multiplyScalar(attributes.Diffuse.value)\n\n    if (attributes.Reflection) {\n      params.reflectivity = attributes.Reflection.value\n      params.combine = AddOperation\n    }\n\n    if (attributes.Luminosity) {\n      params.emissiveIntensity = attributes.Luminosity.value\n\n      if (!maps.emissiveMap && !maps.map) {\n        params.emissive = params.color\n      } else {\n        params.emissive = new Color(0x808080)\n      }\n    }\n\n    // parse specular if there is no roughness - we will interpret the material as 'Phong' in this case\n    if (!attributes.Roughness && attributes.Specular && !maps.specularMap) {\n      if (attributes['Color Highlight']) {\n        params.specular = new Color()\n          .setScalar(attributes.Specular.value)\n          .lerp(params.color.clone().multiplyScalar(attributes.Specular.value), attributes['Color Highlight'].value)\n      } else {\n        params.specular = new Color().setScalar(attributes.Specular.value)\n      }\n    }\n\n    if (params.specular && attributes.Glossiness) {\n      params.shininess = 7 + Math.pow(2, attributes.Glossiness.value * 12 + 2)\n    }\n  }\n\n  parseEnvMap(connections, maps, attributes) {\n    if (connections.envMap) {\n      const envMap = this.loadTexture(connections.envMap)\n\n      if (attributes.transparent && attributes.opacity < 0.999) {\n        envMap.mapping = EquirectangularRefractionMapping\n\n        // Reflectivity and refraction mapping don't work well together in Phong materials\n        if (attributes.reflectivity !== undefined) {\n          delete attributes.reflectivity\n          delete attributes.combine\n        }\n\n        if (attributes.metalness !== undefined) {\n          delete attributes.metalness\n        }\n      } else {\n        envMap.mapping = EquirectangularReflectionMapping\n      }\n\n      maps.envMap = envMap\n    }\n  }\n\n  // get texture defined at top level by its index\n  getTexturePathByIndex(index) {\n    let fileName = ''\n\n    if (!_lwoTree.textures) return fileName\n\n    _lwoTree.textures.forEach(function (texture) {\n      if (texture.index === index) fileName = texture.fileName\n    })\n\n    return fileName\n  }\n\n  loadTexture(path) {\n    if (!path) return null\n\n    const texture = this.textureLoader.load(path, undefined, undefined, function () {\n      console.warn(\n        'LWOLoader: non-standard resource hierarchy. Use `resourcePath` parameter to specify root content directory.',\n      )\n    })\n\n    return texture\n  }\n\n  // 0 = Reset, 1 = Repeat, 2 = Mirror, 3 = Edge\n  getWrappingType(num) {\n    switch (num) {\n      case 0:\n        console.warn('LWOLoader: \"Reset\" texture wrapping type is not supported in three.js')\n        return ClampToEdgeWrapping\n      case 1:\n        return RepeatWrapping\n      case 2:\n        return MirroredRepeatWrapping\n      case 3:\n        return ClampToEdgeWrapping\n    }\n  }\n\n  getMaterialType(nodeData) {\n    if (nodeData.Clearcoat && nodeData.Clearcoat.value > 0) return MeshPhysicalMaterial\n    if (nodeData.Roughness) return MeshStandardMaterial\n    return MeshPhongMaterial\n  }\n}\n\nclass GeometryParser {\n  parse(geoData, layer) {\n    const geometry = new BufferGeometry()\n\n    geometry.setAttribute('position', new Float32BufferAttribute(geoData.points, 3))\n\n    const indices = this.splitIndices(geoData.vertexIndices, geoData.polygonDimensions)\n    geometry.setIndex(indices)\n\n    this.parseGroups(geometry, geoData)\n\n    geometry.computeVertexNormals()\n\n    this.parseUVs(geometry, layer, indices)\n    this.parseMorphTargets(geometry, layer, indices)\n\n    // TODO: z may need to be reversed to account for coordinate system change\n    geometry.translate(-layer.pivot[0], -layer.pivot[1], -layer.pivot[2])\n\n    // let userData = geometry.userData;\n    // geometry = geometry.toNonIndexed()\n    // geometry.userData = userData;\n\n    return geometry\n  }\n\n  // split quads into tris\n  splitIndices(indices, polygonDimensions) {\n    const remappedIndices = []\n\n    let i = 0\n    polygonDimensions.forEach(function (dim) {\n      if (dim < 4) {\n        for (let k = 0; k < dim; k++) remappedIndices.push(indices[i + k])\n      } else if (dim === 4) {\n        remappedIndices.push(\n          indices[i],\n          indices[i + 1],\n          indices[i + 2],\n\n          indices[i],\n          indices[i + 2],\n          indices[i + 3],\n        )\n      } else if (dim > 4) {\n        for (let k = 1; k < dim - 1; k++) {\n          remappedIndices.push(indices[i], indices[i + k], indices[i + k + 1])\n        }\n\n        console.warn('LWOLoader: polygons with greater than 4 sides are not supported')\n      }\n\n      i += dim\n    })\n\n    return remappedIndices\n  }\n\n  // NOTE: currently ignoring poly indices and assuming that they are intelligently ordered\n  parseGroups(geometry, geoData) {\n    const tags = _lwoTree.tags\n    const matNames = []\n\n    let elemSize = 3\n    if (geoData.type === 'lines') elemSize = 2\n    if (geoData.type === 'points') elemSize = 1\n\n    const remappedIndices = this.splitMaterialIndices(geoData.polygonDimensions, geoData.materialIndices)\n\n    let indexNum = 0 // create new indices in numerical order\n    const indexPairs = {} // original indices mapped to numerical indices\n\n    let prevMaterialIndex\n    let materialIndex\n\n    let prevStart = 0\n    let currentCount = 0\n\n    for (let i = 0; i < remappedIndices.length; i += 2) {\n      materialIndex = remappedIndices[i + 1]\n\n      if (i === 0) matNames[indexNum] = tags[materialIndex]\n\n      if (prevMaterialIndex === undefined) prevMaterialIndex = materialIndex\n\n      if (materialIndex !== prevMaterialIndex) {\n        let currentIndex\n        if (indexPairs[tags[prevMaterialIndex]]) {\n          currentIndex = indexPairs[tags[prevMaterialIndex]]\n        } else {\n          currentIndex = indexNum\n          indexPairs[tags[prevMaterialIndex]] = indexNum\n          matNames[indexNum] = tags[prevMaterialIndex]\n          indexNum++\n        }\n\n        geometry.addGroup(prevStart, currentCount, currentIndex)\n\n        prevStart += currentCount\n\n        prevMaterialIndex = materialIndex\n        currentCount = 0\n      }\n\n      currentCount += elemSize\n    }\n\n    // the loop above doesn't add the last group, do that here.\n    if (geometry.groups.length > 0) {\n      let currentIndex\n      if (indexPairs[tags[materialIndex]]) {\n        currentIndex = indexPairs[tags[materialIndex]]\n      } else {\n        currentIndex = indexNum\n        indexPairs[tags[materialIndex]] = indexNum\n        matNames[indexNum] = tags[materialIndex]\n      }\n\n      geometry.addGroup(prevStart, currentCount, currentIndex)\n    }\n\n    // Mat names from TAGS chunk, used to build up an array of materials for this geometry\n    geometry.userData.matNames = matNames\n  }\n\n  splitMaterialIndices(polygonDimensions, indices) {\n    const remappedIndices = []\n\n    polygonDimensions.forEach(function (dim, i) {\n      if (dim <= 3) {\n        remappedIndices.push(indices[i * 2], indices[i * 2 + 1])\n      } else if (dim === 4) {\n        remappedIndices.push(indices[i * 2], indices[i * 2 + 1], indices[i * 2], indices[i * 2 + 1])\n      } else {\n        // ignore > 4 for now\n        for (let k = 0; k < dim - 2; k++) {\n          remappedIndices.push(indices[i * 2], indices[i * 2 + 1])\n        }\n      }\n    })\n\n    return remappedIndices\n  }\n\n  // UV maps:\n  // 1: are defined via index into an array of points, not into a geometry\n  // - the geometry is also defined by an index into this array, but the indexes may not match\n  // 2: there can be any number of UV maps for a single geometry. Here these are combined,\n  // \twith preference given to the first map encountered\n  // 3: UV maps can be partial - that is, defined for only a part of the geometry\n  // 4: UV maps can be VMAP or VMAD (discontinuous, to allow for seams). In practice, most\n  // UV maps are defined as partially VMAP and partially VMAD\n  // VMADs are currently not supported\n  parseUVs(geometry, layer) {\n    // start by creating a UV map set to zero for the whole geometry\n    const remappedUVs = Array.from(Array(geometry.attributes.position.count * 2), function () {\n      return 0\n    })\n\n    for (const name in layer.uvs) {\n      const uvs = layer.uvs[name].uvs\n      const uvIndices = layer.uvs[name].uvIndices\n\n      uvIndices.forEach(function (i, j) {\n        remappedUVs[i * 2] = uvs[j * 2]\n        remappedUVs[i * 2 + 1] = uvs[j * 2 + 1]\n      })\n    }\n\n    geometry.setAttribute('uv', new Float32BufferAttribute(remappedUVs, 2))\n  }\n\n  parseMorphTargets(geometry, layer) {\n    let num = 0\n    for (const name in layer.morphTargets) {\n      const remappedPoints = geometry.attributes.position.array.slice()\n\n      if (!geometry.morphAttributes.position) geometry.morphAttributes.position = []\n\n      const morphPoints = layer.morphTargets[name].points\n      const morphIndices = layer.morphTargets[name].indices\n      const type = layer.morphTargets[name].type\n\n      morphIndices.forEach(function (i, j) {\n        if (type === 'relative') {\n          remappedPoints[i * 3] += morphPoints[j * 3]\n          remappedPoints[i * 3 + 1] += morphPoints[j * 3 + 1]\n          remappedPoints[i * 3 + 2] += morphPoints[j * 3 + 2]\n        } else {\n          remappedPoints[i * 3] = morphPoints[j * 3]\n          remappedPoints[i * 3 + 1] = morphPoints[j * 3 + 1]\n          remappedPoints[i * 3 + 2] = morphPoints[j * 3 + 2]\n        }\n      })\n\n      geometry.morphAttributes.position[num] = new Float32BufferAttribute(remappedPoints, 3)\n      geometry.morphAttributes.position[num].name = name\n\n      num++\n    }\n\n    geometry.morphTargetsRelative = false\n  }\n}\n\n// ************** UTILITY FUNCTIONS **************\n\nfunction extractParentUrl(url, dir) {\n  const index = url.indexOf(dir)\n\n  if (index === -1) return './'\n\n  return url.substr(0, index)\n}\n\nexport { LWOLoader }\n"], "mappings": ";;;AA4CA,IAAIA,QAAA;AAEJ,MAAMC,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAASC,UAAA,GAAa,IAAI;IACpC,MAAMD,OAAO;IAEb,KAAKE,YAAA,GAAeD,UAAA,CAAWC,YAAA,KAAiB,SAAYD,UAAA,CAAWC,YAAA,GAAe;EACvF;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA,KAAS,KAAKC,gBAAA,CAAiBN,GAAA,EAAK,SAAS,IAAII,KAAA,CAAMC,IAAA;IAG1E,MAAME,SAAA,GAAYP,GAAA,CAAIQ,KAAA,CAAMH,IAAI,EAAEI,GAAA,CAAG,EAAGD,KAAA,CAAM,GAAG,EAAE,CAAC;IAEpD,MAAME,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKf,OAAO;IAC1Cc,MAAA,CAAOE,OAAA,CAAQR,KAAA,CAAMC,IAAI;IACzBK,MAAA,CAAOG,eAAA,CAAgB,aAAa;IAEpCH,MAAA,CAAOX,IAAA,CACLC,GAAA,EACA,UAAUc,MAAA,EAAQ;MAGhB,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,MAAA,EAAQT,IAAA,EAAME,SAAS,CAAC;MAC5C,SAAQS,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMR,OAAA,CAAQuB,SAAA,CAAUnB,GAAG;MAC5B;IAGF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,SAAA,EAAWf,IAAA,EAAME,SAAA,EAAW;IAChCf,QAAA,GAAW,IAAI6B,SAAA,GAAYN,KAAA,CAAMK,SAAS;IAI1C,MAAME,aAAA,GAAgB,IAAIC,aAAA,CAAc,KAAK3B,OAAO,EACjDgB,OAAA,CAAQ,KAAKd,YAAA,IAAgBO,IAAI,EACjCmB,cAAA,CAAe,KAAKC,WAAW;IAElC,OAAO,IAAIC,aAAA,CAAcJ,aAAa,EAAEP,KAAA,CAAMR,SAAS;EACxD;AACH;AAGA,MAAMmB,aAAA,CAAc;EAClB/B,YAAY2B,aAAA,EAAe;IACzB,KAAKA,aAAA,GAAgBA,aAAA;EACtB;EAEDP,MAAMR,SAAA,EAAW;IACf,KAAKoB,SAAA,GAAY,IAAIC,cAAA,CAAe,KAAKN,aAAa,EAAEP,KAAA,CAAO;IAC/D,KAAKc,gBAAA,GAAmBtB,SAAA;IAExB,KAAKuB,MAAA,GAAS,KAAKC,WAAA,CAAa;IAEhC,OAAO;MACLJ,SAAA,EAAW,KAAKA,SAAA;MAChBG,MAAA,EAAQ,KAAKA;IACd;EACF;EAEDC,YAAA,EAAc;IAEZ,MAAMD,MAAA,GAAS,EAAE;IAGjB,MAAME,WAAA,GAAc,EAAE;IAEtB,MAAMC,cAAA,GAAiB,IAAIC,cAAA,CAAgB;IAE3C,MAAM9B,KAAA,GAAQ;IACdZ,QAAA,CAAS2C,MAAA,CAAOC,OAAA,CAAQ,UAAUC,KAAA,EAAO;MACvC,MAAMC,QAAA,GAAWL,cAAA,CAAelB,KAAA,CAAMsB,KAAA,CAAMC,QAAA,EAAUD,KAAK;MAE3D,MAAME,IAAA,GAAOnC,KAAA,CAAMoC,SAAA,CAAUF,QAAA,EAAUD,KAAK;MAE5CP,MAAA,CAAOO,KAAA,CAAMI,MAAM,IAAIF,IAAA;MAEvB,IAAIF,KAAA,CAAMK,MAAA,KAAW,IAAIV,WAAA,CAAYW,IAAA,CAAKJ,IAAI,OACzCT,MAAA,CAAOO,KAAA,CAAMK,MAAM,EAAEE,GAAA,CAAIL,IAAI;IACxC,CAAK;IAED,KAAKM,WAAA,CAAYb,WAAW;IAE5B,OAAOA,WAAA;EACR;EAEDQ,UAAUF,QAAA,EAAUD,KAAA,EAAO;IACzB,IAAIE,IAAA;IAEJ,MAAMZ,SAAA,GAAY,KAAKmB,YAAA,CAAaR,QAAA,CAASS,QAAA,CAASC,QAAA,EAAUX,KAAA,CAAMC,QAAA,CAASW,IAAI;IAEnF,IAAIC,GAAA,KAAQ,OAAO,KAAKC,YAAA,CAAab,QAAA,EAAUX,SAAS;IAExD,IAAIU,KAAA,CAAMC,QAAA,CAASW,IAAA,KAAS,UAAUV,IAAA,GAAO,IAAIa,MAAA,CAAOd,QAAA,EAAUX,SAAS,WAClEU,KAAA,CAAMC,QAAA,CAASW,IAAA,KAAS,SAASV,IAAA,GAAO,IAAIc,YAAA,CAAaf,QAAA,EAAUX,SAAS,OAChFY,IAAA,GAAO,IAAIe,IAAA,CAAKhB,QAAA,EAAUX,SAAS;IAExC,IAAIU,KAAA,CAAMkB,IAAA,EAAMhB,IAAA,CAAKgB,IAAA,GAAOlB,KAAA,CAAMkB,IAAA,MAC7BhB,IAAA,CAAKgB,IAAA,GAAO,KAAK1B,gBAAA,GAAmB,YAAYQ,KAAA,CAAMI,MAAA;IAE3DF,IAAA,CAAKQ,QAAA,CAASS,KAAA,GAAQnB,KAAA,CAAMmB,KAAA;IAE5B,OAAOjB,IAAA;EACR;EAAA;EAGDM,YAAYf,MAAA,EAAQ;IAClBA,MAAA,CAAOM,OAAA,CAAQ,UAAUG,IAAA,EAAM;MAC7BA,IAAA,CAAKkB,QAAA,CAAS,UAAUC,KAAA,EAAO;QAC7B,MAAMF,KAAA,GAAQE,KAAA,CAAMX,QAAA,CAASS,KAAA;QAE7BE,KAAA,CAAMC,QAAA,CAASC,CAAA,IAAKJ,KAAA,CAAM,CAAC;QAC3BE,KAAA,CAAMC,QAAA,CAASE,CAAA,IAAKL,KAAA,CAAM,CAAC;QAC3BE,KAAA,CAAMC,QAAA,CAASG,CAAA,IAAKN,KAAA,CAAM,CAAC;QAE3B,IAAIE,KAAA,CAAMhB,MAAA,EAAQ;UAChB,MAAMqB,WAAA,GAAcL,KAAA,CAAMhB,MAAA,CAAOK,QAAA,CAASS,KAAA;UAE1CE,KAAA,CAAMC,QAAA,CAASC,CAAA,IAAKG,WAAA,CAAY,CAAC;UACjCL,KAAA,CAAMC,QAAA,CAASE,CAAA,IAAKE,WAAA,CAAY,CAAC;UACjCL,KAAA,CAAMC,QAAA,CAASG,CAAA,IAAKC,WAAA,CAAY,CAAC;QAClC;MACT,CAAO;IACP,CAAK;EACF;EAEDjB,aAAakB,UAAA,EAAYf,IAAA,EAAM;IAC7B,MAAMtB,SAAA,GAAY,EAAE;IAEpB,MAAMvB,KAAA,GAAQ;IAEd4D,UAAA,CAAW5B,OAAA,CAAQ,UAAUmB,IAAA,EAAMU,CAAA,EAAG;MACpCtC,SAAA,CAAUsC,CAAC,IAAI7D,KAAA,CAAM8D,iBAAA,CAAkBX,IAAI;IACjD,CAAK;IAGD,IAAIN,IAAA,KAAS,YAAYA,IAAA,KAAS,SAAS;MACzCtB,SAAA,CAAUS,OAAA,CAAQ,UAAU+B,GAAA,EAAKF,CAAA,EAAG;QAClC,MAAMG,IAAA,GAAO;UACXC,KAAA,EAAOF,GAAA,CAAIE;QACZ;QAED,IAAIpB,IAAA,KAAS,UAAU;UACrBmB,IAAA,CAAKE,IAAA,GAAO;UACZF,IAAA,CAAKG,GAAA,GAAMJ,GAAA,CAAII,GAAA;UACfH,IAAA,CAAKI,YAAA,GAAeL,GAAA,CAAIK,YAAA;UACxB7C,SAAA,CAAUsC,CAAC,IAAI,IAAIQ,cAAA,CAAeL,IAAI;QAChD,WAAmBnB,IAAA,KAAS,SAAS;UAC3BtB,SAAA,CAAUsC,CAAC,IAAI,IAAIS,iBAAA,CAAkBN,IAAI;QAC1C;MACT,CAAO;IACF;IAGD,MAAMO,QAAA,GAAWhD,SAAA,CAAUiD,MAAA,CAAOC,OAAO;IACzC,IAAIF,QAAA,CAASG,MAAA,KAAW,GAAG,OAAOH,QAAA,CAAS,CAAC;IAE5C,OAAOhD,SAAA;EACR;EAEDuC,kBAAkBX,IAAA,EAAM;IACtB,OAAO,KAAK5B,SAAA,CAAUiD,MAAA,CAAO,UAAUG,CAAA,EAAG;MACxC,OAAOA,CAAA,CAAExB,IAAA,KAASA,IAAA;IACnB,GAAE,CAAC;EACL;EAAA;EAGDJ,aAAab,QAAA,EAAUX,SAAA,EAAW;IAChC,IAAIwB,YAAA,GAAe;IAEnB,IAAI,CAAC6B,KAAA,CAAMC,OAAA,CAAQtD,SAAS,GAAG;MAC7B,IAAIA,SAAA,CAAUuD,KAAA,EAAO/B,YAAA,GAAe;IAC1C,OAAW;MACLxB,SAAA,CAAUS,OAAA,CAAQ,UAAU+C,QAAA,EAAU;QACpC,IAAIA,QAAA,CAASD,KAAA,EAAO/B,YAAA,GAAe;MAC3C,CAAO;IACF;IAED,IAAI,CAACA,YAAA,EAAc;IAEnBb,QAAA,CAAS8C,YAAA,CAAa,OAAO,IAAIC,eAAA,CAAgB/C,QAAA,CAASgD,UAAA,CAAWC,EAAA,CAAGC,KAAA,EAAO,CAAC,CAAC;EAClF;AACH;AAEA,MAAM5D,cAAA,CAAe;EACnBjC,YAAY2B,aAAA,EAAe;IACzB,KAAKA,aAAA,GAAgBA,aAAA;EACtB;EAEDP,MAAA,EAAQ;IACN,MAAMY,SAAA,GAAY,EAAE;IACpB,KAAK8D,QAAA,GAAW,CAAE;IAElB,WAAWlC,IAAA,IAAQ/D,QAAA,CAASmC,SAAA,EAAW;MACrC,IAAInC,QAAA,CAASkG,MAAA,KAAW,QAAQ;QAC9B/D,SAAA,CAAUgB,IAAA,CAAK,KAAKgD,aAAA,CAAcnG,QAAA,CAASmC,SAAA,CAAU4B,IAAI,GAAGA,IAAA,EAAM/D,QAAA,CAASiG,QAAQ,CAAC;MAC5F,WAAiBjG,QAAA,CAASkG,MAAA,KAAW,QAAQ;QACrC/D,SAAA,CAAUgB,IAAA,CAAK,KAAKiD,iBAAA,CAAkBpG,QAAA,CAASmC,SAAA,CAAU4B,IAAI,GAAGA,IAAA,EAAM/D,QAAA,CAASiG,QAAQ,CAAC;MACzF;IACF;IAED,OAAO9D,SAAA;EACR;EAEDgE,cAAcE,YAAA,EAActC,IAAA,EAAMkC,QAAA,EAAU;IAC1C,IAAIK,MAAA,GAAS;MACXvC,IAAA;MACAwC,IAAA,EAAM,KAAKC,OAAA,CAAQH,YAAA,CAAaP,UAAU;MAC1CW,WAAA,EAAa,KAAKC,SAAA,CAAUL,YAAA,CAAaP,UAAU;IACpD;IAED,MAAMa,WAAA,GAAc,KAAKC,gBAAA,CAAiBP,YAAA,CAAaM,WAAA,EAAaN,YAAA,CAAaQ,KAAK;IAEtF,MAAMC,IAAA,GAAO,KAAKC,iBAAA,CAAkBJ,WAAA,CAAYG,IAAI;IAEpD,KAAKE,uBAAA,CAAwBL,WAAA,CAAYb,UAAA,EAAYG,QAAA,EAAUa,IAAA,EAAMT,YAAA,CAAaS,IAAI;IAEtF,MAAMhB,UAAA,GAAa,KAAKmB,eAAA,CAAgBN,WAAA,CAAYb,UAAA,EAAYgB,IAAI;IAEpE,KAAKI,WAAA,CAAYP,WAAA,EAAaG,IAAA,EAAMhB,UAAU;IAE9CQ,MAAA,GAASa,MAAA,CAAOC,MAAA,CAAON,IAAA,EAAMR,MAAM;IACnCA,MAAA,GAASa,MAAA,CAAOC,MAAA,CAAOd,MAAA,EAAQR,UAAU;IAEzC,MAAMuB,YAAA,GAAe,KAAKC,eAAA,CAAgBX,WAAA,CAAYb,UAAU;IAEhE,OAAO,IAAIuB,YAAA,CAAaf,MAAM;EAC/B;EAEDF,kBAAkBC,YAAA,EAActC,IAAA,EAAqB;IACnD,IAAIuC,MAAA,GAAS;MACXvC,IAAA;MACAwC,IAAA,EAAM,KAAKC,OAAA,CAAQH,YAAA,CAAaP,UAAU;MAC1CW,WAAA,EAAa,KAAKC,SAAA,CAAUL,YAAA,CAAaP,UAAU;IACpD;IAED,MAAMA,UAAA,GAAa,KAAKmB,eAAA,CAAgBZ,YAAA,CAAaP,UAAA,EAAY,EAAE;IACnEQ,MAAA,GAASa,MAAA,CAAOC,MAAA,CAAOd,MAAA,EAAQR,UAAU;IACzC,OAAO,IAAIyB,iBAAA,CAAkBjB,MAAM;EACpC;EAAA;EAAA;EAAA;EAKDE,QAAQV,UAAA,EAAY;IAClB,IAAI,CAACA,UAAA,CAAWS,IAAA,EAAM,OAAOiB,QAAA;IAE7B,QAAQ1B,UAAA,CAAWS,IAAA;MACjB,KAAK;MACL,KAAK;QACH,OAAOiB,QAAA;MACT,KAAK;QACH,OAAOC,SAAA;MACT,KAAK;QACH,OAAOC,UAAA;IACV;EACF;EAEDhB,UAAUZ,UAAA,EAAY;IACpB,IAAI,CAACA,UAAA,CAAW6B,MAAA,EAAQ,OAAO;IAC/B,OAAO,CAAC7B,UAAA,CAAW6B,MAAA;EACpB;EAEDf,iBAAiBD,WAAA,EAAaE,KAAA,EAAO;IACnC,MAAMe,mBAAA,GAAsB;MAC1Bd,IAAA,EAAM,CAAE;IACT;IAED,MAAMe,SAAA,GAAYlB,WAAA,CAAYkB,SAAA;IAC9B,MAAMC,aAAA,GAAgBnB,WAAA,CAAYmB,aAAA;IAClC,MAAMC,QAAA,GAAWpB,WAAA,CAAYoB,QAAA;IAE7B,MAAMnH,KAAA,GAAQ;IACdiH,SAAA,CAAUjF,OAAA,CAAQ,UAAUmB,IAAA,EAAMiE,KAAA,EAAO;MACvC,IAAIjE,IAAA,KAAS,YAAY;QACvB,MAAMkE,OAAA,GAAUrH,KAAA,CAAMsH,gBAAA,CAAiBJ,aAAA,CAAcE,KAAK,GAAGnB,KAAK;QAClEe,mBAAA,CAAoB9B,UAAA,GAAamC,OAAA,CAAQnC,UAAA;QACzC8B,mBAAA,CAAoBO,MAAA,GAASF,OAAA,CAAQG,QAAA;QACrCR,mBAAA,CAAoB7D,IAAA,GAAO+D,aAAA,CAAcE,KAAK;MAC/C;IACP,CAAK;IAEDD,QAAA,CAASnF,OAAA,CAAQ,UAAUmB,IAAA,EAAMiE,KAAA,EAAO;MACtC,IAAIjE,IAAA,KAAS6D,mBAAA,CAAoB7D,IAAA,EAAM;QACrC6D,mBAAA,CAAoBd,IAAA,CAAKe,SAAA,CAAUG,KAAK,CAAC,IAAIpH,KAAA,CAAMsH,gBAAA,CAAiBJ,aAAA,CAAcE,KAAK,GAAGnB,KAAK;MAChG;IACP,CAAK;IAED,OAAOe,mBAAA;EACR;EAEDM,iBAAiBG,OAAA,EAASxB,KAAA,EAAO;IAC/B,WAAW9C,IAAA,IAAQ8C,KAAA,EAAO;MACxB,IAAIA,KAAA,CAAM9C,IAAI,EAAEsE,OAAA,KAAYA,OAAA,EAAS,OAAOxB,KAAA,CAAM9C,IAAI;IACvD;EACF;EAEDgD,kBAAkBuB,YAAA,EAAc;IAC9B,MAAMxB,IAAA,GAAO,CAAE;IAEf,WAAW/C,IAAA,IAAQuE,YAAA,EAAc;MAC/B,MAAMC,IAAA,GAAOD,YAAA,CAAavE,IAAI;MAC9B,MAAMlD,IAAA,GAAO0H,IAAA,CAAKH,QAAA;MAElB,IAAI,CAACvH,IAAA,EAAM;MAEX,MAAM2H,OAAA,GAAU,KAAKC,WAAA,CAAY5H,IAAI;MAErC,IAAI0H,IAAA,CAAKG,iBAAA,KAAsB,QAAWF,OAAA,CAAQG,KAAA,GAAQ,KAAKC,eAAA,CAAgBL,IAAA,CAAKG,iBAAiB;MACrG,IAAIH,IAAA,CAAKM,kBAAA,KAAuB,QAAWL,OAAA,CAAQM,KAAA,GAAQ,KAAKF,eAAA,CAAgBL,IAAA,CAAKM,kBAAkB;MAEvG,QAAQ9E,IAAA;QACN,KAAK;UACH+C,IAAA,CAAK/B,GAAA,GAAMyD,OAAA;UACX;QACF,KAAK;UACH1B,IAAA,CAAKiC,YAAA,GAAeP,OAAA;UACpB1B,IAAA,CAAKkC,SAAA,GAAY;UACjB;QACF,KAAK;UACHlC,IAAA,CAAKmC,WAAA,GAAcT,OAAA;UACnB1B,IAAA,CAAKoC,QAAA,GAAW;UAChB;QACF,KAAK;UACHpC,IAAA,CAAKqC,WAAA,GAAcX,OAAA;UACnB1B,IAAA,CAAKsC,QAAA,GAAW;UAChB;QACF,KAAK;UACHtC,IAAA,CAAKsC,QAAA,GAAW;UAChB;QACF,KAAK;UACHtC,IAAA,CAAKuC,YAAA,GAAeb,OAAA;UACpB1B,IAAA,CAAKwC,SAAA,GAAY;UACjB;QACF,KAAK;QACL,KAAK;UACHxC,IAAA,CAAKyC,QAAA,GAAWf,OAAA;UAChB1B,IAAA,CAAK0C,WAAA,GAAc;UACnB;QACF,KAAK;UACH1C,IAAA,CAAK2C,SAAA,GAAYjB,OAAA;UACjB,IAAID,IAAA,CAAKmB,SAAA,KAAc,QAAW5C,IAAA,CAAK6C,WAAA,GAAc,IAAIC,OAAA,CAAQrB,IAAA,CAAKmB,SAAA,EAAWnB,IAAA,CAAKmB,SAAS;UAC/F;QACF,KAAK;UACH5C,IAAA,CAAK+C,OAAA,GAAUrB,OAAA;UACf;MACH;IACF;IAGD,IAAI1B,IAAA,CAAKiC,YAAA,IAAgBjC,IAAA,CAAKmC,WAAA,EAAa,OAAOnC,IAAA,CAAKmC,WAAA;IAEvD,OAAOnC,IAAA;EACR;EAAA;EAAA;EAIDE,wBAAwBlB,UAAA,EAAYG,QAAA,EAAUa,IAAA,EAAM;IAClD,WAAW/C,IAAA,IAAQ+B,UAAA,EAAY;MAC7B,MAAMgE,SAAA,GAAYhE,UAAA,CAAW/B,IAAI;MAEjC,IAAI+F,SAAA,CAAUhD,IAAA,EAAM;QAClB,MAAMiD,OAAA,GAAUD,SAAA,CAAUhD,IAAA,CAAK,CAAC;QAEhC,MAAMjG,IAAA,GAAO,KAAKmJ,qBAAA,CAAsBD,OAAA,CAAQE,UAAA,EAAYhE,QAAQ;QACpE,IAAI,CAACpF,IAAA,EAAM;QAEX,MAAM2H,OAAA,GAAU,KAAKC,WAAA,CAAY5H,IAAI;QAErC,IAAIkJ,OAAA,CAAQG,IAAA,KAAS,QAAW1B,OAAA,CAAQG,KAAA,GAAQ,KAAKC,eAAA,CAAgBmB,OAAA,CAAQG,IAAA,CAAKC,CAAC;QACnF,IAAIJ,OAAA,CAAQG,IAAA,KAAS,QAAW1B,OAAA,CAAQM,KAAA,GAAQ,KAAKF,eAAA,CAAgBmB,OAAA,CAAQG,IAAA,CAAKE,CAAC;QAEnF,QAAQrG,IAAA;UACN,KAAK;YACH+C,IAAA,CAAK/B,GAAA,GAAMyD,OAAA;YACX;UACF,KAAK;YACH1B,IAAA,CAAKpB,KAAA,GAAQ8C,OAAA;YACb;UACF,KAAK;YACH1B,IAAA,CAAKiC,YAAA,GAAeP,OAAA;YACpB1B,IAAA,CAAKkC,SAAA,GAAY;YACjB;UACF,KAAK;YACHlC,IAAA,CAAKmC,WAAA,GAAcT,OAAA;YACnB1B,IAAA,CAAKoC,QAAA,GAAW;YAChB;UACF,KAAK;YACHpC,IAAA,CAAKqC,WAAA,GAAcX,OAAA;YACnB1B,IAAA,CAAKsC,QAAA,GAAW;YAChB;UACF,KAAK;YACHtC,IAAA,CAAKuC,YAAA,GAAeb,OAAA;YACpB1B,IAAA,CAAKwC,SAAA,GAAY;YACjB;UACF,KAAK;UACL,KAAK;YACHxC,IAAA,CAAKyC,QAAA,GAAWf,OAAA;YAChB1B,IAAA,CAAK0C,WAAA,GAAc;YACnB;UACF,KAAK;YACH1C,IAAA,CAAK2C,SAAA,GAAYjB,OAAA;YACjB;UACF,KAAK;YACH1B,IAAA,CAAK+C,OAAA,GAAUrB,OAAA;YACf;QACH;MACF;IACF;EACF;EAEDvB,gBAAgBnB,UAAA,EAAYgB,IAAA,EAAM;IAChC,MAAMR,MAAA,GAAS,CAAE;IAGjB,IAAIR,UAAA,CAAWuE,KAAA,IAAS,CAACvD,IAAA,CAAK/B,GAAA,EAAK;MACjCuB,MAAA,CAAOzB,KAAA,GAAQ,IAAIwF,KAAA,CAAK,EAAGC,SAAA,CAAUxE,UAAA,CAAWuE,KAAA,CAAME,KAAK;IACjE,OAAW;MACLjE,MAAA,CAAOzB,KAAA,GAAQ,IAAIwF,KAAA,CAAO;IAC3B;IAED,IAAIvE,UAAA,CAAW0E,YAAA,IAAgB1E,UAAA,CAAW0E,YAAA,CAAaD,KAAA,KAAU,GAAG;MAClEjE,MAAA,CAAOmE,OAAA,GAAU,IAAI3E,UAAA,CAAW0E,YAAA,CAAaD,KAAA;MAC7CjE,MAAA,CAAOkD,WAAA,GAAc;IACtB;IAED,IAAI1D,UAAA,CAAW,aAAa,GAAGQ,MAAA,CAAOoE,SAAA,GAAY5E,UAAA,CAAW,aAAa,EAAEyE,KAAA,GAAQ;IAEpF,IAAIzE,UAAA,CAAW,kBAAkB,GAAGQ,MAAA,CAAOqE,eAAA,GAAkB,IAAI7E,UAAA,CAAW,kBAAkB,EAAEyE,KAAA;IAEhG,KAAKK,uBAAA,CAAwBtE,MAAA,EAAQR,UAAA,EAAYgB,IAAI;IACrD,KAAK+D,uBAAA,CAAwBvE,MAAA,EAAQR,UAAA,EAAYgB,IAAI;IACrD,KAAKgE,oBAAA,CAAqBxE,MAAA,EAAQR,UAAA,EAAYgB,IAAI;IAElD,OAAOR,MAAA;EACR;EAEDsE,wBAAwBtE,MAAA,EAAQR,UAAA,EAAuB;IACrD,IAAIA,UAAA,CAAWiF,SAAA,IAAajF,UAAA,CAAWiF,SAAA,CAAUR,KAAA,GAAQ,GAAG;MAC1DjE,MAAA,CAAO0E,SAAA,GAAYlF,UAAA,CAAWiF,SAAA,CAAUR,KAAA;MAExC,IAAIzE,UAAA,CAAW,iBAAiB,GAAG;QACjCQ,MAAA,CAAO2E,kBAAA,GAAqB,OAAO,IAAInF,UAAA,CAAW,iBAAiB,EAAEyE,KAAA;MACtE;IACF;EACF;EAEDM,wBAAwBvE,MAAA,EAAQR,UAAA,EAAYgB,IAAA,EAAM;IAChD,IAAIhB,UAAA,CAAWoF,QAAA,EAAU;MACvB5E,MAAA,CAAO6E,iBAAA,GAAoBrF,UAAA,CAAWoF,QAAA,CAASX,KAAA;MAE/C,IAAIzE,UAAA,CAAW,gBAAgB,KAAK,CAACgB,IAAA,CAAKsC,QAAA,EAAU;QAClD9C,MAAA,CAAO8C,QAAA,GAAW,IAAIiB,KAAA,CAAO,EAACC,SAAA,CAAUxE,UAAA,CAAW,gBAAgB,EAAEyE,KAAK;MAClF,OAAa;QACLjE,MAAA,CAAO8C,QAAA,GAAW,IAAIiB,KAAA,CAAM,OAAQ;MACrC;IACF;IAED,IAAIvE,UAAA,CAAWsF,SAAA,IAAa,CAACtE,IAAA,CAAKiC,YAAA,EAAczC,MAAA,CAAO0C,SAAA,GAAYlD,UAAA,CAAWsF,SAAA,CAAUb,KAAA;IACxF,IAAIzE,UAAA,CAAWuF,QAAA,IAAY,CAACvE,IAAA,CAAKuC,YAAA,EAAc/C,MAAA,CAAOgD,SAAA,GAAYxD,UAAA,CAAWuF,QAAA,CAASd,KAAA;EACvF;EAEDO,qBAAqBxE,MAAA,EAAQR,UAAA,EAAYgB,IAAA,EAAM;IAC7C,IAAIhB,UAAA,CAAWwF,OAAA,EAAShF,MAAA,CAAOzB,KAAA,CAAM0G,cAAA,CAAezF,UAAA,CAAWwF,OAAA,CAAQf,KAAK;IAE5E,IAAIzE,UAAA,CAAW0F,UAAA,EAAY;MACzBlF,MAAA,CAAOmF,YAAA,GAAe3F,UAAA,CAAW0F,UAAA,CAAWjB,KAAA;MAC5CjE,MAAA,CAAOoF,OAAA,GAAUC,YAAA;IAClB;IAED,IAAI7F,UAAA,CAAW8F,UAAA,EAAY;MACzBtF,MAAA,CAAO6E,iBAAA,GAAoBrF,UAAA,CAAW8F,UAAA,CAAWrB,KAAA;MAEjD,IAAI,CAACzD,IAAA,CAAKqC,WAAA,IAAe,CAACrC,IAAA,CAAK/B,GAAA,EAAK;QAClCuB,MAAA,CAAO8C,QAAA,GAAW9C,MAAA,CAAOzB,KAAA;MACjC,OAAa;QACLyB,MAAA,CAAO8C,QAAA,GAAW,IAAIiB,KAAA,CAAM,OAAQ;MACrC;IACF;IAGD,IAAI,CAACvE,UAAA,CAAWsF,SAAA,IAAatF,UAAA,CAAW+F,QAAA,IAAY,CAAC/E,IAAA,CAAKmC,WAAA,EAAa;MACrE,IAAInD,UAAA,CAAW,iBAAiB,GAAG;QACjCQ,MAAA,CAAO4C,QAAA,GAAW,IAAImB,KAAA,CAAO,EAC1ByB,SAAA,CAAUhG,UAAA,CAAW+F,QAAA,CAAStB,KAAK,EACnCwB,IAAA,CAAKzF,MAAA,CAAOzB,KAAA,CAAMmH,KAAA,CAAK,EAAGT,cAAA,CAAezF,UAAA,CAAW+F,QAAA,CAAStB,KAAK,GAAGzE,UAAA,CAAW,iBAAiB,EAAEyE,KAAK;MACnH,OAAa;QACLjE,MAAA,CAAO4C,QAAA,GAAW,IAAImB,KAAA,CAAK,EAAGyB,SAAA,CAAUhG,UAAA,CAAW+F,QAAA,CAAStB,KAAK;MAClE;IACF;IAED,IAAIjE,MAAA,CAAO4C,QAAA,IAAYpD,UAAA,CAAWmG,UAAA,EAAY;MAC5C3F,MAAA,CAAO4F,SAAA,GAAY,IAAIC,IAAA,CAAKC,GAAA,CAAI,GAAGtG,UAAA,CAAWmG,UAAA,CAAW1B,KAAA,GAAQ,KAAK,CAAC;IACxE;EACF;EAEDrD,YAAYP,WAAA,EAAaG,IAAA,EAAMhB,UAAA,EAAY;IACzC,IAAIa,WAAA,CAAYwB,MAAA,EAAQ;MACtB,MAAMA,MAAA,GAAS,KAAKM,WAAA,CAAY9B,WAAA,CAAYwB,MAAM;MAElD,IAAIrC,UAAA,CAAW0D,WAAA,IAAe1D,UAAA,CAAW2E,OAAA,GAAU,OAAO;QACxDtC,MAAA,CAAOkE,OAAA,GAAUC,gCAAA;QAGjB,IAAIxG,UAAA,CAAW2F,YAAA,KAAiB,QAAW;UACzC,OAAO3F,UAAA,CAAW2F,YAAA;UAClB,OAAO3F,UAAA,CAAW4F,OAAA;QACnB;QAED,IAAI5F,UAAA,CAAWwD,SAAA,KAAc,QAAW;UACtC,OAAOxD,UAAA,CAAWwD,SAAA;QACnB;MACT,OAAa;QACLnB,MAAA,CAAOkE,OAAA,GAAUE,gCAAA;MAClB;MAEDzF,IAAA,CAAKqB,MAAA,GAASA,MAAA;IACf;EACF;EAAA;EAGD6B,sBAAsBhC,KAAA,EAAO;IAC3B,IAAII,QAAA,GAAW;IAEf,IAAI,CAACpI,QAAA,CAASiG,QAAA,EAAU,OAAOmC,QAAA;IAE/BpI,QAAA,CAASiG,QAAA,CAASrD,OAAA,CAAQ,UAAU4F,OAAA,EAAS;MAC3C,IAAIA,OAAA,CAAQR,KAAA,KAAUA,KAAA,EAAOI,QAAA,GAAWI,OAAA,CAAQJ,QAAA;IACtD,CAAK;IAED,OAAOA,QAAA;EACR;EAEDK,YAAY5H,IAAA,EAAM;IAChB,IAAI,CAACA,IAAA,EAAM,OAAO;IAElB,MAAM2H,OAAA,GAAU,KAAK1G,aAAA,CAAcvB,IAAA,CAAKM,IAAA,EAAM,QAAW,QAAW,YAAY;MAC9EY,OAAA,CAAQ+K,IAAA,CACN,6GACD;IACP,CAAK;IAED,OAAOhE,OAAA;EACR;EAAA;EAGDI,gBAAgB6D,GAAA,EAAK;IACnB,QAAQA,GAAA;MACN,KAAK;QACHhL,OAAA,CAAQ+K,IAAA,CAAK,uEAAuE;QACpF,OAAOE,mBAAA;MACT,KAAK;QACH,OAAOC,cAAA;MACT,KAAK;QACH,OAAOC,sBAAA;MACT,KAAK;QACH,OAAOF,mBAAA;IACV;EACF;EAEDpF,gBAAgBuF,QAAA,EAAU;IACxB,IAAIA,QAAA,CAAS9B,SAAA,IAAa8B,QAAA,CAAS9B,SAAA,CAAUR,KAAA,GAAQ,GAAG,OAAOuC,oBAAA;IAC/D,IAAID,QAAA,CAASzB,SAAA,EAAW,OAAO2B,oBAAA;IAC/B,OAAOxF,iBAAA;EACR;AACH;AAEA,MAAM7E,cAAA,CAAe;EACnBnB,MAAMyL,OAAA,EAASnK,KAAA,EAAO;IACpB,MAAMC,QAAA,GAAW,IAAImK,cAAA,CAAgB;IAErCnK,QAAA,CAAS8C,YAAA,CAAa,YAAY,IAAIsH,sBAAA,CAAuBF,OAAA,CAAQG,MAAA,EAAQ,CAAC,CAAC;IAE/E,MAAMC,OAAA,GAAU,KAAKC,YAAA,CAAaL,OAAA,CAAQM,aAAA,EAAeN,OAAA,CAAQO,iBAAiB;IAClFzK,QAAA,CAAS0K,QAAA,CAASJ,OAAO;IAEzB,KAAKK,WAAA,CAAY3K,QAAA,EAAUkK,OAAO;IAElClK,QAAA,CAAS4K,oBAAA,CAAsB;IAE/B,KAAKC,QAAA,CAAS7K,QAAA,EAAUD,KAAA,EAAOuK,OAAO;IACtC,KAAKQ,iBAAA,CAAkB9K,QAAA,EAAUD,KAAA,EAAOuK,OAAO;IAG/CtK,QAAA,CAAS+K,SAAA,CAAU,CAAChL,KAAA,CAAMmB,KAAA,CAAM,CAAC,GAAG,CAACnB,KAAA,CAAMmB,KAAA,CAAM,CAAC,GAAG,CAACnB,KAAA,CAAMmB,KAAA,CAAM,CAAC,CAAC;IAMpE,OAAOlB,QAAA;EACR;EAAA;EAGDuK,aAAaD,OAAA,EAASG,iBAAA,EAAmB;IACvC,MAAMO,eAAA,GAAkB,EAAE;IAE1B,IAAIrJ,CAAA,GAAI;IACR8I,iBAAA,CAAkB3K,OAAA,CAAQ,UAAUmL,GAAA,EAAK;MACvC,IAAIA,GAAA,GAAM,GAAG;QACX,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,GAAA,EAAKC,CAAA,IAAKF,eAAA,CAAgB3K,IAAA,CAAKiK,OAAA,CAAQ3I,CAAA,GAAIuJ,CAAC,CAAC;MACzE,WAAiBD,GAAA,KAAQ,GAAG;QACpBD,eAAA,CAAgB3K,IAAA,CACdiK,OAAA,CAAQ3I,CAAC,GACT2I,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GACb2I,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GAEb2I,OAAA,CAAQ3I,CAAC,GACT2I,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GACb2I,OAAA,CAAQ3I,CAAA,GAAI,CAAC,CACd;MACT,WAAiBsJ,GAAA,GAAM,GAAG;QAClB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,GAAA,GAAM,GAAGC,CAAA,IAAK;UAChCF,eAAA,CAAgB3K,IAAA,CAAKiK,OAAA,CAAQ3I,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAIuJ,CAAC,GAAGZ,OAAA,CAAQ3I,CAAA,GAAIuJ,CAAA,GAAI,CAAC,CAAC;QACpE;QAEDvM,OAAA,CAAQ+K,IAAA,CAAK,iEAAiE;MAC/E;MAED/H,CAAA,IAAKsJ,GAAA;IACX,CAAK;IAED,OAAOD,eAAA;EACR;EAAA;EAGDL,YAAY3K,QAAA,EAAUkK,OAAA,EAAS;IAC7B,MAAMiB,IAAA,GAAOjO,QAAA,CAASiO,IAAA;IACtB,MAAMzK,QAAA,GAAW,EAAE;IAEnB,IAAI0K,QAAA,GAAW;IACf,IAAIlB,OAAA,CAAQvJ,IAAA,KAAS,SAASyK,QAAA,GAAW;IACzC,IAAIlB,OAAA,CAAQvJ,IAAA,KAAS,UAAUyK,QAAA,GAAW;IAE1C,MAAMJ,eAAA,GAAkB,KAAKK,oBAAA,CAAqBnB,OAAA,CAAQO,iBAAA,EAAmBP,OAAA,CAAQoB,eAAe;IAEpG,IAAIC,QAAA,GAAW;IACf,MAAMC,UAAA,GAAa,CAAE;IAErB,IAAIC,iBAAA;IACJ,IAAIC,aAAA;IAEJ,IAAIC,SAAA,GAAY;IAChB,IAAIC,YAAA,GAAe;IAEnB,SAASjK,CAAA,GAAI,GAAGA,CAAA,GAAIqJ,eAAA,CAAgBxI,MAAA,EAAQb,CAAA,IAAK,GAAG;MAClD+J,aAAA,GAAgBV,eAAA,CAAgBrJ,CAAA,GAAI,CAAC;MAErC,IAAIA,CAAA,KAAM,GAAGjB,QAAA,CAAS6K,QAAQ,IAAIJ,IAAA,CAAKO,aAAa;MAEpD,IAAID,iBAAA,KAAsB,QAAWA,iBAAA,GAAoBC,aAAA;MAEzD,IAAIA,aAAA,KAAkBD,iBAAA,EAAmB;QACvC,IAAII,YAAA;QACJ,IAAIL,UAAA,CAAWL,IAAA,CAAKM,iBAAiB,CAAC,GAAG;UACvCI,YAAA,GAAeL,UAAA,CAAWL,IAAA,CAAKM,iBAAiB,CAAC;QAC3D,OAAe;UACLI,YAAA,GAAeN,QAAA;UACfC,UAAA,CAAWL,IAAA,CAAKM,iBAAiB,CAAC,IAAIF,QAAA;UACtC7K,QAAA,CAAS6K,QAAQ,IAAIJ,IAAA,CAAKM,iBAAiB;UAC3CF,QAAA;QACD;QAEDvL,QAAA,CAAS8L,QAAA,CAASH,SAAA,EAAWC,YAAA,EAAcC,YAAY;QAEvDF,SAAA,IAAaC,YAAA;QAEbH,iBAAA,GAAoBC,aAAA;QACpBE,YAAA,GAAe;MAChB;MAEDA,YAAA,IAAgBR,QAAA;IACjB;IAGD,IAAIpL,QAAA,CAAS+L,MAAA,CAAOvJ,MAAA,GAAS,GAAG;MAC9B,IAAIqJ,YAAA;MACJ,IAAIL,UAAA,CAAWL,IAAA,CAAKO,aAAa,CAAC,GAAG;QACnCG,YAAA,GAAeL,UAAA,CAAWL,IAAA,CAAKO,aAAa,CAAC;MACrD,OAAa;QACLG,YAAA,GAAeN,QAAA;QACfC,UAAA,CAAWL,IAAA,CAAKO,aAAa,CAAC,IAAIH,QAAA;QAClC7K,QAAA,CAAS6K,QAAQ,IAAIJ,IAAA,CAAKO,aAAa;MACxC;MAED1L,QAAA,CAAS8L,QAAA,CAASH,SAAA,EAAWC,YAAA,EAAcC,YAAY;IACxD;IAGD7L,QAAA,CAASS,QAAA,CAASC,QAAA,GAAWA,QAAA;EAC9B;EAED2K,qBAAqBZ,iBAAA,EAAmBH,OAAA,EAAS;IAC/C,MAAMU,eAAA,GAAkB,EAAE;IAE1BP,iBAAA,CAAkB3K,OAAA,CAAQ,UAAUmL,GAAA,EAAKtJ,CAAA,EAAG;MAC1C,IAAIsJ,GAAA,IAAO,GAAG;QACZD,eAAA,CAAgB3K,IAAA,CAAKiK,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAI,IAAI,CAAC,CAAC;MAC/D,WAAiBsJ,GAAA,KAAQ,GAAG;QACpBD,eAAA,CAAgB3K,IAAA,CAAKiK,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAI,IAAI,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAI,IAAI,CAAC,CAAC;MACnG,OAAa;QAEL,SAASuJ,CAAA,GAAI,GAAGA,CAAA,GAAID,GAAA,GAAM,GAAGC,CAAA,IAAK;UAChCF,eAAA,CAAgB3K,IAAA,CAAKiK,OAAA,CAAQ3I,CAAA,GAAI,CAAC,GAAG2I,OAAA,CAAQ3I,CAAA,GAAI,IAAI,CAAC,CAAC;QACxD;MACF;IACP,CAAK;IAED,OAAOqJ,eAAA;EACR;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAWDH,SAAS7K,QAAA,EAAUD,KAAA,EAAO;IAExB,MAAMiM,WAAA,GAActJ,KAAA,CAAMuJ,IAAA,CAAKvJ,KAAA,CAAM1C,QAAA,CAASgD,UAAA,CAAW3B,QAAA,CAAS6K,KAAA,GAAQ,CAAC,GAAG,YAAY;MACxF,OAAO;IACb,CAAK;IAED,WAAWjL,IAAA,IAAQlB,KAAA,CAAMoM,GAAA,EAAK;MAC5B,MAAMA,GAAA,GAAMpM,KAAA,CAAMoM,GAAA,CAAIlL,IAAI,EAAEkL,GAAA;MAC5B,MAAMC,SAAA,GAAYrM,KAAA,CAAMoM,GAAA,CAAIlL,IAAI,EAAEmL,SAAA;MAElCA,SAAA,CAAUtM,OAAA,CAAQ,UAAU6B,CAAA,EAAG0K,CAAA,EAAG;QAChCL,WAAA,CAAYrK,CAAA,GAAI,CAAC,IAAIwK,GAAA,CAAIE,CAAA,GAAI,CAAC;QAC9BL,WAAA,CAAYrK,CAAA,GAAI,IAAI,CAAC,IAAIwK,GAAA,CAAIE,CAAA,GAAI,IAAI,CAAC;MAC9C,CAAO;IACF;IAEDrM,QAAA,CAAS8C,YAAA,CAAa,MAAM,IAAIsH,sBAAA,CAAuB4B,WAAA,EAAa,CAAC,CAAC;EACvE;EAEDlB,kBAAkB9K,QAAA,EAAUD,KAAA,EAAO;IACjC,IAAI4J,GAAA,GAAM;IACV,WAAW1I,IAAA,IAAQlB,KAAA,CAAMmC,YAAA,EAAc;MACrC,MAAMoK,cAAA,GAAiBtM,QAAA,CAASgD,UAAA,CAAW3B,QAAA,CAAS6B,KAAA,CAAMqJ,KAAA,CAAO;MAEjE,IAAI,CAACvM,QAAA,CAASwM,eAAA,CAAgBnL,QAAA,EAAUrB,QAAA,CAASwM,eAAA,CAAgBnL,QAAA,GAAW,EAAE;MAE9E,MAAMoL,WAAA,GAAc1M,KAAA,CAAMmC,YAAA,CAAajB,IAAI,EAAEoJ,MAAA;MAC7C,MAAMqC,YAAA,GAAe3M,KAAA,CAAMmC,YAAA,CAAajB,IAAI,EAAEqJ,OAAA;MAC9C,MAAM3J,IAAA,GAAOZ,KAAA,CAAMmC,YAAA,CAAajB,IAAI,EAAEN,IAAA;MAEtC+L,YAAA,CAAa5M,OAAA,CAAQ,UAAU6B,CAAA,EAAG0K,CAAA,EAAG;QACnC,IAAI1L,IAAA,KAAS,YAAY;UACvB2L,cAAA,CAAe3K,CAAA,GAAI,CAAC,KAAK8K,WAAA,CAAYJ,CAAA,GAAI,CAAC;UAC1CC,cAAA,CAAe3K,CAAA,GAAI,IAAI,CAAC,KAAK8K,WAAA,CAAYJ,CAAA,GAAI,IAAI,CAAC;UAClDC,cAAA,CAAe3K,CAAA,GAAI,IAAI,CAAC,KAAK8K,WAAA,CAAYJ,CAAA,GAAI,IAAI,CAAC;QAC5D,OAAe;UACLC,cAAA,CAAe3K,CAAA,GAAI,CAAC,IAAI8K,WAAA,CAAYJ,CAAA,GAAI,CAAC;UACzCC,cAAA,CAAe3K,CAAA,GAAI,IAAI,CAAC,IAAI8K,WAAA,CAAYJ,CAAA,GAAI,IAAI,CAAC;UACjDC,cAAA,CAAe3K,CAAA,GAAI,IAAI,CAAC,IAAI8K,WAAA,CAAYJ,CAAA,GAAI,IAAI,CAAC;QAClD;MACT,CAAO;MAEDrM,QAAA,CAASwM,eAAA,CAAgBnL,QAAA,CAASsI,GAAG,IAAI,IAAIS,sBAAA,CAAuBkC,cAAA,EAAgB,CAAC;MACrFtM,QAAA,CAASwM,eAAA,CAAgBnL,QAAA,CAASsI,GAAG,EAAE1I,IAAA,GAAOA,IAAA;MAE9C0I,GAAA;IACD;IAED3J,QAAA,CAAS2M,oBAAA,GAAuB;EACjC;AACH;AAIA,SAAS3O,iBAAiBN,GAAA,EAAKkP,GAAA,EAAK;EAClC,MAAM1H,KAAA,GAAQxH,GAAA,CAAImP,OAAA,CAAQD,GAAG;EAE7B,IAAI1H,KAAA,KAAU,IAAI,OAAO;EAEzB,OAAOxH,GAAA,CAAIoP,MAAA,CAAO,GAAG5H,KAAK;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}