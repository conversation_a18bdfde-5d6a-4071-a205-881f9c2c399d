{"ast": null, "code": "function init() {\n  const _p2 = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180];\n  for (let i = 0; i < 256; i++) {\n    _p2[256 + i] = _p2[i];\n  }\n  return _p2;\n}\nconst _p = /* @__PURE__ */init();\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\nfunction lerp(t, a, b) {\n  return a + t * (b - a);\n}\nfunction grad(hash, x, y, z) {\n  const h = hash & 15;\n  const u = h < 8 ? x : y,\n    v = h < 4 ? y : h == 12 || h == 14 ? x : z;\n  return ((h & 1) == 0 ? u : -u) + ((h & 2) == 0 ? v : -v);\n}\nclass ImprovedNoise {\n  noise(x, y, z) {\n    const floorX = Math.floor(x),\n      floorY = Math.floor(y),\n      floorZ = Math.floor(z);\n    const X = floorX & 255,\n      Y = floorY & 255,\n      Z = floorZ & 255;\n    x -= floorX;\n    y -= floorY;\n    z -= floorZ;\n    const xMinus1 = x - 1,\n      yMinus1 = y - 1,\n      zMinus1 = z - 1;\n    const u = fade(x),\n      v = fade(y),\n      w = fade(z);\n    const A = _p[X] + Y,\n      AA = _p[A] + Z,\n      AB = _p[A + 1] + Z,\n      B = _p[X + 1] + Y,\n      BA = _p[B] + Z,\n      BB = _p[B + 1] + Z;\n    return lerp(w, lerp(v, lerp(u, grad(_p[AA], x, y, z), grad(_p[BA], xMinus1, y, z)), lerp(u, grad(_p[AB], x, yMinus1, z), grad(_p[BB], xMinus1, yMinus1, z))), lerp(v, lerp(u, grad(_p[AA + 1], x, y, zMinus1), grad(_p[BA + 1], xMinus1, y, zMinus1)), lerp(u, grad(_p[AB + 1], x, yMinus1, zMinus1), grad(_p[BB + 1], xMinus1, yMinus1, zMinus1))));\n  }\n}\nexport { ImprovedNoise };", "map": {"version": 3, "names": ["init", "_p2", "i", "_p", "fade", "t", "lerp", "a", "b", "grad", "hash", "x", "y", "z", "h", "u", "v", "Improved<PERSON><PERSON>", "noise", "floorX", "Math", "floor", "floorY", "floorZ", "X", "Y", "Z", "xMinus1", "yMinus1", "zMinus1", "w", "A", "AA", "AB", "B", "BA", "BB"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/math/ImprovedNoise.js"], "sourcesContent": ["// https://cs.nyu.edu/~perlin/noise/\n\nfunction init() {\n  // prettier-ignore\n  const _p = [ 151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10,\n    23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87,\n    174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211,\n    133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208,\n    89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5,\n    202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119,\n    248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232,\n    178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249,\n    14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205,\n    93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180 ];\n\n  for (let i = 0; i < 256; i++) {\n    _p[256 + i] = _p[i]\n  }\n\n  return _p\n}\n\nconst _p = /* @__PURE__ */ init()\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10)\n}\n\nfunction lerp(t, a, b) {\n  return a + t * (b - a)\n}\n\nfunction grad(hash, x, y, z) {\n  const h = hash & 15\n  const u = h < 8 ? x : y,\n    v = h < 4 ? y : h == 12 || h == 14 ? x : z\n  return ((h & 1) == 0 ? u : -u) + ((h & 2) == 0 ? v : -v)\n}\n\nclass ImprovedNoise {\n  noise(x, y, z) {\n    const floorX = Math.floor(x),\n      floorY = Math.floor(y),\n      floorZ = Math.floor(z)\n\n    const X = floorX & 255,\n      Y = floorY & 255,\n      Z = floorZ & 255\n\n    x -= floorX\n    y -= floorY\n    z -= floorZ\n\n    const xMinus1 = x - 1,\n      yMinus1 = y - 1,\n      zMinus1 = z - 1\n\n    const u = fade(x),\n      v = fade(y),\n      w = fade(z)\n\n    const A = _p[X] + Y,\n      AA = _p[A] + Z,\n      AB = _p[A + 1] + Z,\n      B = _p[X + 1] + Y,\n      BA = _p[B] + Z,\n      BB = _p[B + 1] + Z\n\n    return lerp(\n      w,\n      lerp(\n        v,\n        lerp(u, grad(_p[AA], x, y, z), grad(_p[BA], xMinus1, y, z)),\n        lerp(u, grad(_p[AB], x, yMinus1, z), grad(_p[BB], xMinus1, yMinus1, z)),\n      ),\n      lerp(\n        v,\n        lerp(u, grad(_p[AA + 1], x, y, zMinus1), grad(_p[BA + 1], xMinus1, y, zMinus1)),\n        lerp(u, grad(_p[AB + 1], x, yMinus1, zMinus1), grad(_p[BB + 1], xMinus1, yMinus1, zMinus1)),\n      ),\n    )\n  }\n}\n\nexport { ImprovedNoise }\n"], "mappings": "AAEA,SAASA,KAAA,EAAO;EAEd,MAAMC,GAAA,GAAK,CAAE,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IACjI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IACvH,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KACpH,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KACpH,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,GACtH,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KACnH,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KACtH,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KACnH,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KACrH,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK;EAE1E,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKA,CAAA,IAAK;IAC5BD,GAAA,CAAG,MAAMC,CAAC,IAAID,GAAA,CAAGC,CAAC;EACnB;EAED,OAAOD,GAAA;AACT;AAEA,MAAME,EAAA,GAAqB,eAAAH,IAAA,CAAM;AAEjC,SAASI,KAAKC,CAAA,EAAG;EACf,OAAOA,CAAA,GAAIA,CAAA,GAAIA,CAAA,IAAKA,CAAA,IAAKA,CAAA,GAAI,IAAI,MAAM;AACzC;AAEA,SAASC,KAAKD,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAG;EACrB,OAAOD,CAAA,GAAIF,CAAA,IAAKG,CAAA,GAAID,CAAA;AACtB;AAEA,SAASE,KAAKC,IAAA,EAAMC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;EAC3B,MAAMC,CAAA,GAAIJ,IAAA,GAAO;EACjB,MAAMK,CAAA,GAAID,CAAA,GAAI,IAAIH,CAAA,GAAIC,CAAA;IACpBI,CAAA,GAAIF,CAAA,GAAI,IAAIF,CAAA,GAAIE,CAAA,IAAK,MAAMA,CAAA,IAAK,KAAKH,CAAA,GAAIE,CAAA;EAC3C,SAASC,CAAA,GAAI,MAAM,IAAIC,CAAA,GAAI,CAACA,CAAA,MAAOD,CAAA,GAAI,MAAM,IAAIE,CAAA,GAAI,CAACA,CAAA;AACxD;AAEA,MAAMC,aAAA,CAAc;EAClBC,MAAMP,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IACb,MAAMM,MAAA,GAASC,IAAA,CAAKC,KAAA,CAAMV,CAAC;MACzBW,MAAA,GAASF,IAAA,CAAKC,KAAA,CAAMT,CAAC;MACrBW,MAAA,GAASH,IAAA,CAAKC,KAAA,CAAMR,CAAC;IAEvB,MAAMW,CAAA,GAAIL,MAAA,GAAS;MACjBM,CAAA,GAAIH,MAAA,GAAS;MACbI,CAAA,GAAIH,MAAA,GAAS;IAEfZ,CAAA,IAAKQ,MAAA;IACLP,CAAA,IAAKU,MAAA;IACLT,CAAA,IAAKU,MAAA;IAEL,MAAMI,OAAA,GAAUhB,CAAA,GAAI;MAClBiB,OAAA,GAAUhB,CAAA,GAAI;MACdiB,OAAA,GAAUhB,CAAA,GAAI;IAEhB,MAAME,CAAA,GAAIX,IAAA,CAAKO,CAAC;MACdK,CAAA,GAAIZ,IAAA,CAAKQ,CAAC;MACVkB,CAAA,GAAI1B,IAAA,CAAKS,CAAC;IAEZ,MAAMkB,CAAA,GAAI5B,EAAA,CAAGqB,CAAC,IAAIC,CAAA;MAChBO,EAAA,GAAK7B,EAAA,CAAG4B,CAAC,IAAIL,CAAA;MACbO,EAAA,GAAK9B,EAAA,CAAG4B,CAAA,GAAI,CAAC,IAAIL,CAAA;MACjBQ,CAAA,GAAI/B,EAAA,CAAGqB,CAAA,GAAI,CAAC,IAAIC,CAAA;MAChBU,EAAA,GAAKhC,EAAA,CAAG+B,CAAC,IAAIR,CAAA;MACbU,EAAA,GAAKjC,EAAA,CAAG+B,CAAA,GAAI,CAAC,IAAIR,CAAA;IAEnB,OAAOpB,IAAA,CACLwB,CAAA,EACAxB,IAAA,CACEU,CAAA,EACAV,IAAA,CAAKS,CAAA,EAAGN,IAAA,CAAKN,EAAA,CAAG6B,EAAE,GAAGrB,CAAA,EAAGC,CAAA,EAAGC,CAAC,GAAGJ,IAAA,CAAKN,EAAA,CAAGgC,EAAE,GAAGR,OAAA,EAASf,CAAA,EAAGC,CAAC,CAAC,GAC1DP,IAAA,CAAKS,CAAA,EAAGN,IAAA,CAAKN,EAAA,CAAG8B,EAAE,GAAGtB,CAAA,EAAGiB,OAAA,EAASf,CAAC,GAAGJ,IAAA,CAAKN,EAAA,CAAGiC,EAAE,GAAGT,OAAA,EAASC,OAAA,EAASf,CAAC,CAAC,CACvE,GACDP,IAAA,CACEU,CAAA,EACAV,IAAA,CAAKS,CAAA,EAAGN,IAAA,CAAKN,EAAA,CAAG6B,EAAA,GAAK,CAAC,GAAGrB,CAAA,EAAGC,CAAA,EAAGiB,OAAO,GAAGpB,IAAA,CAAKN,EAAA,CAAGgC,EAAA,GAAK,CAAC,GAAGR,OAAA,EAASf,CAAA,EAAGiB,OAAO,CAAC,GAC9EvB,IAAA,CAAKS,CAAA,EAAGN,IAAA,CAAKN,EAAA,CAAG8B,EAAA,GAAK,CAAC,GAAGtB,CAAA,EAAGiB,OAAA,EAASC,OAAO,GAAGpB,IAAA,CAAKN,EAAA,CAAGiC,EAAA,GAAK,CAAC,GAAGT,OAAA,EAASC,OAAA,EAASC,OAAO,CAAC,CAC3F,CACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}