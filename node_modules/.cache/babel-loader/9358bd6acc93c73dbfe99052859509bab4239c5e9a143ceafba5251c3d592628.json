{"ast": null, "code": "import { Vector3, Vector2, <PERSON>, DoubleSide, BackSide, REVISION } from 'three';\nconst IS_GT_REVISION_169 = parseInt(REVISION) >= 169;\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst _vA = /* @__PURE__ */new Vector3();\nconst _vB = /* @__PURE__ */new Vector3();\nconst _vC = /* @__PURE__ */new Vector3();\nconst _uvA = /* @__PURE__ */new Vector2();\nconst _uvB = /* @__PURE__ */new Vector2();\nconst _uvC = /* @__PURE__ */new Vector2();\nconst _normalA = /* @__PURE__ */new Vector3();\nconst _normalB = /* @__PURE__ */new Vector3();\nconst _normalC = /* @__PURE__ */new Vector3();\nconst _intersectionPoint = /* @__PURE__ */new Vector3();\nfunction checkIntersection(ray, pA, pB, pC, point, side, near, far) {\n  let intersect;\n  if (side === BackSide) {\n    intersect = ray.intersectTriangle(pC, pB, pA, true, point);\n  } else {\n    intersect = ray.intersectTriangle(pA, pB, pC, side !== DoubleSide, point);\n  }\n  if (intersect === null) return null;\n  const distance = ray.origin.distanceTo(point);\n  if (distance < near || distance > far) return null;\n  return {\n    distance: distance,\n    point: point.clone()\n  };\n}\nfunction checkBufferGeometryIntersection(ray, position, normal, uv, uv1, a, b, c, side, near, far) {\n  _vA.fromBufferAttribute(position, a);\n  _vB.fromBufferAttribute(position, b);\n  _vC.fromBufferAttribute(position, c);\n  const intersection = checkIntersection(ray, _vA, _vB, _vC, _intersectionPoint, side, near, far);\n  if (intersection) {\n    const barycoord = new Vector3();\n    Triangle.getBarycoord(_intersectionPoint, _vA, _vB, _vC, barycoord);\n    if (uv) {\n      _uvA.fromBufferAttribute(uv, a);\n      _uvB.fromBufferAttribute(uv, b);\n      _uvC.fromBufferAttribute(uv, c);\n      intersection.uv = Triangle.getInterpolation(_intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2());\n    }\n    if (uv1) {\n      _uvA.fromBufferAttribute(uv1, a);\n      _uvB.fromBufferAttribute(uv1, b);\n      _uvC.fromBufferAttribute(uv1, c);\n      intersection.uv1 = Triangle.getInterpolation(_intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2());\n    }\n    if (normal) {\n      _normalA.fromBufferAttribute(normal, a);\n      _normalB.fromBufferAttribute(normal, b);\n      _normalC.fromBufferAttribute(normal, c);\n      intersection.normal = Triangle.getInterpolation(_intersectionPoint, _vA, _vB, _vC, _normalA, _normalB, _normalC, new Vector3());\n      if (intersection.normal.dot(ray.direction) > 0) {\n        intersection.normal.multiplyScalar(-1);\n      }\n    }\n    const face = {\n      a: a,\n      b: b,\n      c: c,\n      normal: new Vector3(),\n      materialIndex: 0\n    };\n    Triangle.getNormal(_vA, _vB, _vC, face.normal);\n    intersection.face = face;\n    intersection.faceIndex = a;\n    if (IS_GT_REVISION_169) {\n      intersection.barycoord = barycoord;\n    }\n  }\n  return intersection;\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri(geo, side, ray, tri, intersections, near, far) {\n  const triOffset = tri * 3;\n  let a = triOffset + 0;\n  let b = triOffset + 1;\n  let c = triOffset + 2;\n  const index = geo.index;\n  if (geo.index) {\n    a = index.getX(a);\n    b = index.getX(b);\n    c = index.getX(c);\n  }\n  const {\n    position,\n    normal,\n    uv,\n    uv1\n  } = geo.attributes;\n  const intersection = checkBufferGeometryIntersection(ray, position, normal, uv, uv1, a, b, c, side, near, far);\n  if (intersection) {\n    intersection.faceIndex = tri;\n    if (intersections) intersections.push(intersection);\n    return intersection;\n  }\n  return null;\n}\nexport { intersectTri };", "map": {"version": 3, "names": ["Vector3", "Vector2", "Triangle", "DoubleSide", "BackSide", "REVISION", "IS_GT_REVISION_169", "parseInt", "_vA", "_vB", "_vC", "_uvA", "_uvB", "_uvC", "_normalA", "_normalB", "_normalC", "_intersectionPoint", "checkIntersection", "ray", "pA", "pB", "pC", "point", "side", "near", "far", "intersect", "intersectTriangle", "distance", "origin", "distanceTo", "clone", "checkBufferGeometryIntersection", "position", "normal", "uv", "uv1", "a", "b", "c", "fromBufferAttribute", "intersection", "barycoord", "getBarycoord", "getInterpolation", "dot", "direction", "multiplyScalar", "face", "materialIndex", "getNormal", "faceIndex", "intersectTri", "geo", "tri", "intersections", "triOffset", "index", "getX", "attributes", "push"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/three-mesh-bvh/src/utils/ThreeRayIntersectUtilities.js"], "sourcesContent": ["import { Vector3, Vector2, <PERSON>, DoubleSide, BackSide, REVISION } from 'three';\n\nconst IS_GT_REVISION_169 = parseInt( REVISION ) >= 169;\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst _vA = /* @__PURE__ */ new Vector3();\nconst _vB = /* @__PURE__ */ new Vector3();\nconst _vC = /* @__PURE__ */ new Vector3();\n\nconst _uvA = /* @__PURE__ */ new Vector2();\nconst _uvB = /* @__PURE__ */ new Vector2();\nconst _uvC = /* @__PURE__ */ new Vector2();\n\nconst _normalA = /* @__PURE__ */ new Vector3();\nconst _normalB = /* @__PURE__ */ new Vector3();\nconst _normalC = /* @__PURE__ */ new Vector3();\n\nconst _intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side, near, far ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\tif ( distance < near || distance > far ) return null;\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far ) {\n\n\t_vA.fromBufferAttribute( position, a );\n\t_vB.fromBufferAttribute( position, b );\n\t_vC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, _vA, _vB, _vC, _intersectionPoint, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tconst barycoord = new Vector3();\n\t\tTriangle.getBarycoord( _intersectionPoint, _vA, _vB, _vC, barycoord );\n\n\t\tif ( uv ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv, a );\n\t\t\t_uvB.fromBufferAttribute( uv, b );\n\t\t\t_uvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( uv1 ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv1, a );\n\t\t\t_uvB.fromBufferAttribute( uv1, b );\n\t\t\t_uvC.fromBufferAttribute( uv1, c );\n\n\t\t\tintersection.uv1 = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( normal ) {\n\n\t\t\t_normalA.fromBufferAttribute( normal, a );\n\t\t\t_normalB.fromBufferAttribute( normal, b );\n\t\t\t_normalC.fromBufferAttribute( normal, c );\n\n\t\t\tintersection.normal = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _normalA, _normalB, _normalC, new Vector3() );\n\t\t\tif ( intersection.normal.dot( ray.direction ) > 0 ) {\n\n\t\t\t\tintersection.normal.multiplyScalar( - 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( _vA, _vB, _vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t\tif ( IS_GT_REVISION_169 ) {\n\n\t\t\tintersection.barycoord = barycoord;\n\n\t\t}\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections, near, far ) {\n\n\tconst triOffset = tri * 3;\n\tlet a = triOffset + 0;\n\tlet b = triOffset + 1;\n\tlet c = triOffset + 2;\n\n\tconst index = geo.index;\n\tif ( geo.index ) {\n\n\t\ta = index.getX( a );\n\t\tb = index.getX( b );\n\t\tc = index.getX( c );\n\n\t}\n\n\tconst { position, normal, uv, uv1 } = geo.attributes;\n\tconst intersection = checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,OAAO;AAElF,MAAMC,kBAAkB,GAAGC,QAAQ,CAAEF,QAAS,CAAC,IAAI,GAAG;;AAEtD;AACA;AACA,MAAMG,GAAG,GAAG,eAAgB,IAAIR,OAAO,CAAC,CAAC;AACzC,MAAMS,GAAG,GAAG,eAAgB,IAAIT,OAAO,CAAC,CAAC;AACzC,MAAMU,GAAG,GAAG,eAAgB,IAAIV,OAAO,CAAC,CAAC;AAEzC,MAAMW,IAAI,GAAG,eAAgB,IAAIV,OAAO,CAAC,CAAC;AAC1C,MAAMW,IAAI,GAAG,eAAgB,IAAIX,OAAO,CAAC,CAAC;AAC1C,MAAMY,IAAI,GAAG,eAAgB,IAAIZ,OAAO,CAAC,CAAC;AAE1C,MAAMa,QAAQ,GAAG,eAAgB,IAAId,OAAO,CAAC,CAAC;AAC9C,MAAMe,QAAQ,GAAG,eAAgB,IAAIf,OAAO,CAAC,CAAC;AAC9C,MAAMgB,QAAQ,GAAG,eAAgB,IAAIhB,OAAO,CAAC,CAAC;AAE9C,MAAMiB,kBAAkB,GAAG,eAAgB,IAAIjB,OAAO,CAAC,CAAC;AACxD,SAASkB,iBAAiBA,CAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAErE,IAAIC,SAAS;EACb,IAAKH,IAAI,KAAKpB,QAAQ,EAAG;IAExBuB,SAAS,GAAGR,GAAG,CAACS,iBAAiB,CAAEN,EAAE,EAAED,EAAE,EAAED,EAAE,EAAE,IAAI,EAAEG,KAAM,CAAC;EAE7D,CAAC,MAAM;IAENI,SAAS,GAAGR,GAAG,CAACS,iBAAiB,CAAER,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,IAAI,KAAKrB,UAAU,EAAEoB,KAAM,CAAC;EAE5E;EAEA,IAAKI,SAAS,KAAK,IAAI,EAAG,OAAO,IAAI;EAErC,MAAME,QAAQ,GAAGV,GAAG,CAACW,MAAM,CAACC,UAAU,CAAER,KAAM,CAAC;EAE/C,IAAKM,QAAQ,GAAGJ,IAAI,IAAII,QAAQ,GAAGH,GAAG,EAAG,OAAO,IAAI;EAEpD,OAAO;IAENG,QAAQ,EAAEA,QAAQ;IAClBN,KAAK,EAAEA,KAAK,CAACS,KAAK,CAAC;EAEpB,CAAC;AAEF;AAEA,SAASC,+BAA+BA,CAAEd,GAAG,EAAEe,QAAQ,EAAEC,MAAM,EAAEC,EAAE,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEhB,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEpGlB,GAAG,CAACiC,mBAAmB,CAAEP,QAAQ,EAAEI,CAAE,CAAC;EACtC7B,GAAG,CAACgC,mBAAmB,CAAEP,QAAQ,EAAEK,CAAE,CAAC;EACtC7B,GAAG,CAAC+B,mBAAmB,CAAEP,QAAQ,EAAEM,CAAE,CAAC;EAEtC,MAAME,YAAY,GAAGxB,iBAAiB,CAAEC,GAAG,EAAEX,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEO,kBAAkB,EAAEO,IAAI,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAEjG,IAAKgB,YAAY,EAAG;IAEnB,MAAMC,SAAS,GAAG,IAAI3C,OAAO,CAAC,CAAC;IAC/BE,QAAQ,CAAC0C,YAAY,CAAE3B,kBAAkB,EAAET,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEiC,SAAU,CAAC;IAErE,IAAKP,EAAE,EAAG;MAETzB,IAAI,CAAC8B,mBAAmB,CAAEL,EAAE,EAAEE,CAAE,CAAC;MACjC1B,IAAI,CAAC6B,mBAAmB,CAAEL,EAAE,EAAEG,CAAE,CAAC;MACjC1B,IAAI,CAAC4B,mBAAmB,CAAEL,EAAE,EAAEI,CAAE,CAAC;MAEjCE,YAAY,CAACN,EAAE,GAAGlC,QAAQ,CAAC2C,gBAAgB,CAAE5B,kBAAkB,EAAET,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE,IAAIZ,OAAO,CAAC,CAAE,CAAC;IAElH;IAEA,IAAKoC,GAAG,EAAG;MAEV1B,IAAI,CAAC8B,mBAAmB,CAAEJ,GAAG,EAAEC,CAAE,CAAC;MAClC1B,IAAI,CAAC6B,mBAAmB,CAAEJ,GAAG,EAAEE,CAAE,CAAC;MAClC1B,IAAI,CAAC4B,mBAAmB,CAAEJ,GAAG,EAAEG,CAAE,CAAC;MAElCE,YAAY,CAACL,GAAG,GAAGnC,QAAQ,CAAC2C,gBAAgB,CAAE5B,kBAAkB,EAAET,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE,IAAIZ,OAAO,CAAC,CAAE,CAAC;IAEnH;IAEA,IAAKkC,MAAM,EAAG;MAEbrB,QAAQ,CAAC2B,mBAAmB,CAAEN,MAAM,EAAEG,CAAE,CAAC;MACzCvB,QAAQ,CAAC0B,mBAAmB,CAAEN,MAAM,EAAEI,CAAE,CAAC;MACzCvB,QAAQ,CAACyB,mBAAmB,CAAEN,MAAM,EAAEK,CAAE,CAAC;MAEzCE,YAAY,CAACP,MAAM,GAAGjC,QAAQ,CAAC2C,gBAAgB,CAAE5B,kBAAkB,EAAET,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEI,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,IAAIhB,OAAO,CAAC,CAAE,CAAC;MACjI,IAAK0C,YAAY,CAACP,MAAM,CAACW,GAAG,CAAE3B,GAAG,CAAC4B,SAAU,CAAC,GAAG,CAAC,EAAG;QAEnDL,YAAY,CAACP,MAAM,CAACa,cAAc,CAAE,CAAE,CAAE,CAAC;MAE1C;IAED;IAEA,MAAMC,IAAI,GAAG;MACZX,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJL,MAAM,EAAE,IAAInC,OAAO,CAAC,CAAC;MACrBkD,aAAa,EAAE;IAChB,CAAC;IAEDhD,QAAQ,CAACiD,SAAS,CAAE3C,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEuC,IAAI,CAACd,MAAO,CAAC;IAEhDO,YAAY,CAACO,IAAI,GAAGA,IAAI;IACxBP,YAAY,CAACU,SAAS,GAAGd,CAAC;IAE1B,IAAKhC,kBAAkB,EAAG;MAEzBoC,YAAY,CAACC,SAAS,GAAGA,SAAS;IAEnC;EAED;EAEA,OAAOD,YAAY;AAEpB;;AAEA;AACA,SAASW,YAAYA,CAAEC,GAAG,EAAE9B,IAAI,EAAEL,GAAG,EAAEoC,GAAG,EAAEC,aAAa,EAAE/B,IAAI,EAAEC,GAAG,EAAG;EAEtE,MAAM+B,SAAS,GAAGF,GAAG,GAAG,CAAC;EACzB,IAAIjB,CAAC,GAAGmB,SAAS,GAAG,CAAC;EACrB,IAAIlB,CAAC,GAAGkB,SAAS,GAAG,CAAC;EACrB,IAAIjB,CAAC,GAAGiB,SAAS,GAAG,CAAC;EAErB,MAAMC,KAAK,GAAGJ,GAAG,CAACI,KAAK;EACvB,IAAKJ,GAAG,CAACI,KAAK,EAAG;IAEhBpB,CAAC,GAAGoB,KAAK,CAACC,IAAI,CAAErB,CAAE,CAAC;IACnBC,CAAC,GAAGmB,KAAK,CAACC,IAAI,CAAEpB,CAAE,CAAC;IACnBC,CAAC,GAAGkB,KAAK,CAACC,IAAI,CAAEnB,CAAE,CAAC;EAEpB;EAEA,MAAM;IAAEN,QAAQ;IAAEC,MAAM;IAAEC,EAAE;IAAEC;EAAI,CAAC,GAAGiB,GAAG,CAACM,UAAU;EACpD,MAAMlB,YAAY,GAAGT,+BAA+B,CAAEd,GAAG,EAAEe,QAAQ,EAAEC,MAAM,EAAEC,EAAE,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEhB,IAAI,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAEhH,IAAKgB,YAAY,EAAG;IAEnBA,YAAY,CAACU,SAAS,GAAGG,GAAG;IAC5B,IAAKC,aAAa,EAAGA,aAAa,CAACK,IAAI,CAAEnB,YAAa,CAAC;IACvD,OAAOA,YAAY;EAEpB;EAEA,OAAO,IAAI;AAEZ;AAEA,SAASW,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}