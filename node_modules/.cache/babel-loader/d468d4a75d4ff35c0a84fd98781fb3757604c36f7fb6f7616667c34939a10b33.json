{"ast": null, "code": "import { Curve, Vector4, Vector3 } from \"three\";\nimport { calcBSplinePoint, calcNURBSDerivatives } from \"./NURBSUtils.js\";\nclass NURBSCurve extends Curve {\n  constructor(degree, knots, controlPoints, startKnot, endKnot) {\n    super();\n    this.degree = degree;\n    this.knots = knots;\n    this.controlPoints = [];\n    this.startKnot = startKnot || 0;\n    this.endKnot = endKnot || this.knots.length - 1;\n    for (let i = 0; i < controlPoints.length; ++i) {\n      const point = controlPoints[i];\n      this.controlPoints[i] = new Vector4(point.x, point.y, point.z, point.w);\n    }\n  }\n  getPoint(t, optionalTarget) {\n    const point = optionalTarget || new Vector3();\n    const u = this.knots[this.startKnot] + t * (this.knots[this.endKnot] - this.knots[this.startKnot]);\n    const hpoint = calcBSplinePoint(this.degree, this.knots, this.controlPoints, u);\n    if (hpoint.w != 1) {\n      hpoint.divideScalar(hpoint.w);\n    }\n    return point.set(hpoint.x, hpoint.y, hpoint.z);\n  }\n  getTangent(t, optionalTarget) {\n    const tangent = optionalTarget || new Vector3();\n    const u = this.knots[0] + t * (this.knots[this.knots.length - 1] - this.knots[0]);\n    const ders = calcNURBSDerivatives(this.degree, this.knots, this.controlPoints, u, 1);\n    tangent.copy(ders[1]).normalize();\n    return tangent;\n  }\n}\nexport { NURBSCurve };", "map": {"version": 3, "names": ["NURBSCurve", "Curve", "constructor", "degree", "knots", "controlPoints", "startKnot", "endKnot", "length", "i", "point", "Vector4", "x", "y", "z", "w", "getPoint", "t", "optionalTarget", "Vector3", "u", "hpoint", "calcBSplinePoint", "divideScalar", "set", "getTangent", "tangent", "ders", "calcNURBSDerivatives", "copy", "normalize"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/curves/NURBSCurve.js"], "sourcesContent": ["import { Curve, Vector3, Vector4 } from 'three'\nimport * as NURBSUtils from '../curves/NURBSUtils'\n\n/**\n * NURBS curve object\n *\n * Derives from Curve, overriding getPoint and getTangent.\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n *\n **/\n\nclass NURBSCurve extends Curve {\n  constructor(\n    degree,\n    knots /* array of reals */,\n    controlPoints /* array of Vector(2|3|4) */,\n    startKnot /* index in knots */,\n    endKnot /* index in knots */,\n  ) {\n    super()\n\n    this.degree = degree\n    this.knots = knots\n    this.controlPoints = []\n    // Used by periodic NURBS to remove hidden spans\n    this.startKnot = startKnot || 0\n    this.endKnot = endKnot || this.knots.length - 1\n    for (let i = 0; i < controlPoints.length; ++i) {\n      // ensure Vector4 for control points\n      const point = controlPoints[i]\n      this.controlPoints[i] = new Vector4(point.x, point.y, point.z, point.w)\n    }\n  }\n\n  getPoint(t, optionalTarget) {\n    const point = optionalTarget || new Vector3()\n\n    const u = this.knots[this.startKnot] + t * (this.knots[this.endKnot] - this.knots[this.startKnot]) // linear mapping t->u\n\n    // following results in (wx, wy, wz, w) homogeneous point\n    const hpoint = NURBSUtils.calcBSplinePoint(this.degree, this.knots, this.controlPoints, u)\n\n    if (hpoint.w != 1.0) {\n      // project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n      hpoint.divideScalar(hpoint.w)\n    }\n\n    return point.set(hpoint.x, hpoint.y, hpoint.z)\n  }\n\n  getTangent(t, optionalTarget) {\n    const tangent = optionalTarget || new Vector3()\n\n    const u = this.knots[0] + t * (this.knots[this.knots.length - 1] - this.knots[0])\n    const ders = NURBSUtils.calcNURBSDerivatives(this.degree, this.knots, this.controlPoints, u, 1)\n    tangent.copy(ders[1]).normalize()\n\n    return tangent\n  }\n}\n\nexport { NURBSCurve }\n"], "mappings": ";;AAYA,MAAMA,UAAA,SAAmBC,KAAA,CAAM;EAC7BC,YACEC,MAAA,EACAC,KAAA,EACAC,aAAA,EACAC,SAAA,EACAC,OAAA,EACA;IACA,MAAO;IAEP,KAAKJ,MAAA,GAASA,MAAA;IACd,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKC,aAAA,GAAgB,EAAE;IAEvB,KAAKC,SAAA,GAAYA,SAAA,IAAa;IAC9B,KAAKC,OAAA,GAAUA,OAAA,IAAW,KAAKH,KAAA,CAAMI,MAAA,GAAS;IAC9C,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,aAAA,CAAcG,MAAA,EAAQ,EAAEC,CAAA,EAAG;MAE7C,MAAMC,KAAA,GAAQL,aAAA,CAAcI,CAAC;MAC7B,KAAKJ,aAAA,CAAcI,CAAC,IAAI,IAAIE,OAAA,CAAQD,KAAA,CAAME,CAAA,EAAGF,KAAA,CAAMG,CAAA,EAAGH,KAAA,CAAMI,CAAA,EAAGJ,KAAA,CAAMK,CAAC;IACvE;EACF;EAEDC,SAASC,CAAA,EAAGC,cAAA,EAAgB;IAC1B,MAAMR,KAAA,GAAQQ,cAAA,IAAkB,IAAIC,OAAA,CAAS;IAE7C,MAAMC,CAAA,GAAI,KAAKhB,KAAA,CAAM,KAAKE,SAAS,IAAIW,CAAA,IAAK,KAAKb,KAAA,CAAM,KAAKG,OAAO,IAAI,KAAKH,KAAA,CAAM,KAAKE,SAAS;IAGhG,MAAMe,MAAA,GAASC,gBAAA,CAA4B,KAAKnB,MAAA,EAAQ,KAAKC,KAAA,EAAO,KAAKC,aAAA,EAAee,CAAC;IAEzF,IAAIC,MAAA,CAAON,CAAA,IAAK,GAAK;MAEnBM,MAAA,CAAOE,YAAA,CAAaF,MAAA,CAAON,CAAC;IAC7B;IAED,OAAOL,KAAA,CAAMc,GAAA,CAAIH,MAAA,CAAOT,CAAA,EAAGS,MAAA,CAAOR,CAAA,EAAGQ,MAAA,CAAOP,CAAC;EAC9C;EAEDW,WAAWR,CAAA,EAAGC,cAAA,EAAgB;IAC5B,MAAMQ,OAAA,GAAUR,cAAA,IAAkB,IAAIC,OAAA,CAAS;IAE/C,MAAMC,CAAA,GAAI,KAAKhB,KAAA,CAAM,CAAC,IAAIa,CAAA,IAAK,KAAKb,KAAA,CAAM,KAAKA,KAAA,CAAMI,MAAA,GAAS,CAAC,IAAI,KAAKJ,KAAA,CAAM,CAAC;IAC/E,MAAMuB,IAAA,GAAOC,oBAAA,CAAgC,KAAKzB,MAAA,EAAQ,KAAKC,KAAA,EAAO,KAAKC,aAAA,EAAee,CAAA,EAAG,CAAC;IAC9FM,OAAA,CAAQG,IAAA,CAAKF,IAAA,CAAK,CAAC,CAAC,EAAEG,SAAA,CAAW;IAEjC,OAAOJ,OAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}