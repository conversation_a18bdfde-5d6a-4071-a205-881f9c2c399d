{"ast": null, "code": "import { Reflector } from \"./Reflector.js\";\nclass ReflectorRTT extends Reflector {\n  constructor(geometry, options) {\n    super(geometry, options);\n    this.geometry.setDrawRange(0, 0);\n  }\n}\nexport { ReflectorRTT };", "map": {"version": 3, "names": ["ReflectorRTT", "Reflector", "constructor", "geometry", "options", "setDrawRange"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/objects/ReflectorRTT.ts"], "sourcesContent": ["import { BufferGeometry } from 'three'\nimport { Reflector, ReflectorOptions } from '../objects/Reflector'\n\nclass ReflectorRTT extends Reflector {\n  constructor(geometry?: BufferGeometry, options?: ReflectorOptions) {\n    super(geometry, options)\n    this.geometry.setDrawRange(0, 0)\n  }\n}\n\nexport { ReflectorRTT }\n"], "mappings": ";AAGA,MAAMA,YAAA,SAAqBC,SAAA,CAAU;EACnCC,YAAYC,QAAA,EAA2BC,OAAA,EAA4B;IACjE,MAAMD,QAAA,EAAUC,OAAO;IAClB,KAAAD,QAAA,CAASE,YAAA,CAAa,GAAG,CAAC;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}