{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { FBXLoader } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\nfunction useFBX(path) {\n  return useLoader(FBXLoader, path);\n}\nuseFBX.preload = path => useLoader.preload(FBXLoader, path);\nuseFBX.clear = input => useLoader.clear(FBXLoader, input);\nfunction Fbx({\n  path,\n  ...props\n}) {\n  const fbx = useFBX(path);\n  const object = fbx.children[0];\n  return /*#__PURE__*/React.createElement(Clone, _extends({}, props, {\n    object: object\n  }));\n}\nexport { Fbx, useFBX };", "map": {"version": 3, "names": ["_extends", "React", "FBXLoader", "useLoader", "<PERSON><PERSON>", "useFBX", "path", "preload", "clear", "input", "Fbx", "props", "fbx", "object", "children", "createElement"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/@react-three/drei/core/Fbx.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { FBXLoader } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\n\nfunction useFBX(path) {\n  return useLoader(FBXLoader, path);\n}\nuseFBX.preload = path => useLoader.preload(FBXLoader, path);\nuseFBX.clear = input => useLoader.clear(FBXLoader, input);\nfunction Fbx({\n  path,\n  ...props\n}) {\n  const fbx = useFBX(path);\n  const object = fbx.children[0];\n  return /*#__PURE__*/React.createElement(Clone, _extends({}, props, {\n    object: object\n  }));\n}\n\nexport { Fbx, useFBX };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,KAAK,QAAQ,YAAY;AAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,OAAOH,SAAS,CAACD,SAAS,EAAEI,IAAI,CAAC;AACnC;AACAD,MAAM,CAACE,OAAO,GAAGD,IAAI,IAAIH,SAAS,CAACI,OAAO,CAACL,SAAS,EAAEI,IAAI,CAAC;AAC3DD,MAAM,CAACG,KAAK,GAAGC,KAAK,IAAIN,SAAS,CAACK,KAAK,CAACN,SAAS,EAAEO,KAAK,CAAC;AACzD,SAASC,GAAGA,CAAC;EACXJ,IAAI;EACJ,GAAGK;AACL,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGP,MAAM,CAACC,IAAI,CAAC;EACxB,MAAMO,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC;EAC9B,OAAO,aAAab,KAAK,CAACc,aAAa,CAACX,KAAK,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACjEE,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL;AAEA,SAASH,GAAG,EAAEL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}