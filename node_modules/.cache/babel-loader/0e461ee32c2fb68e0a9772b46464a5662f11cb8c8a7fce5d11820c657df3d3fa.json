{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, TOUCH, Spherical, Vector2, Quaternion, MOUSE, PerspectiveCamera, OrthographicCamera } from \"three\";\nimport { EventDispatcher } from \"../EventDispatcher.js\";\nconst STATE = {\n  NONE: -1,\n  ROTATE: 0,\n  DOLLY: 1,\n  PAN: 2,\n  TOUCH_ROTATE: 3,\n  TOUCH_PAN: 4,\n  TOUCH_DOLLY_PAN: 5,\n  TOUCH_DOLLY_ROTATE: 6\n};\nclass CameraControls extends EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    /** Set to false to disable this control */\n    __publicField(this, \"enabled\", true);\n    /** \"target\" sets the location of focus, where the object orbits around */\n    __publicField(this, \"target\", new Vector3());\n    /** Set to true to enable trackball behavior */\n    __publicField(this, \"trackball\", false);\n    /** How far you can dolly in ( PerspectiveCamera only ) */\n    __publicField(this, \"minDistance\", 0);\n    /** How far you can dolly out ( PerspectiveCamera only ) */\n    __publicField(this, \"maxDistance\", Infinity);\n    // How far you can zoom in and out ( OrthographicCamera only )\n    __publicField(this, \"minZoom\", 0);\n    __publicField(this, \"maxZoom\", Infinity);\n    // How far you can orbit vertically, upper and lower limits.\n    // Range is 0 to Math.PI radians.\n    __publicField(this, \"minPolarAngle\", 0);\n    __publicField(this, \"maxPolarAngle\", Math.PI);\n    // How far you can orbit horizontally, upper and lower limits.\n    // If set, must be a sub-interval of the interval [ - Math.PI, Math.PI ].\n    __publicField(this, \"minAzimuthAngle\", -Infinity);\n    // radians\n    __publicField(this, \"maxAzimuthAngle\", Infinity);\n    // radians\n    // Set to true to enable damping (inertia)\n    // If damping is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"enableDamping\", false);\n    __publicField(this, \"dampingFactor\", 0.05);\n    /**\n     * This option enables dollying in and out; property named as \"zoom\" for backwards compatibility\n     * Set to false to disable zooming\n     */\n    __publicField(this, \"enableZoom\", true);\n    __publicField(this, \"zoomSpeed\", 1);\n    /** Set to false to disable rotating */\n    __publicField(this, \"enableRotate\", true);\n    __publicField(this, \"rotateSpeed\", 1);\n    /** Set to false to disable panning */\n    __publicField(this, \"enablePan\", true);\n    __publicField(this, \"panSpeed\", 1);\n    /** if true, pan in screen-space */\n    __publicField(this, \"screenSpacePanning\", false);\n    /** pixels moved per arrow key push */\n    __publicField(this, \"keyPanSpeed\", 7);\n    /**\n     * Set to true to automatically rotate around the target\n     * If auto-rotate is enabled, you must call controls.update() in your animation loop\n     * auto-rotate is not supported for trackball behavior\n     */\n    __publicField(this, \"autoRotate\", false);\n    __publicField(this, \"autoRotateSpeed\", 2);\n    // 30 seconds per round when fps is 60\n    /** Set to false to disable use of the keys */\n    __publicField(this, \"enableKeys\", true);\n    /** The four arrow keys */\n    __publicField(this, \"keys\", {\n      LEFT: \"ArrowLeft\",\n      UP: \"ArrowUp\",\n      RIGHT: \"ArrowRight\",\n      BOTTOM: \"ArrowDown\"\n    });\n    __publicField(this, \"mouseButtons\");\n    /** Touch fingers */\n    __publicField(this, \"touches\", {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN\n    });\n    // for reset\n    __publicField(this, \"target0\");\n    __publicField(this, \"position0\");\n    __publicField(this, \"quaternion0\");\n    __publicField(this, \"zoom0\");\n    // current position in spherical coordinates\n    __publicField(this, \"spherical\", new Spherical());\n    __publicField(this, \"sphericalDelta\", new Spherical());\n    __publicField(this, \"changeEvent\", {\n      type: \"change\"\n    });\n    __publicField(this, \"startEvent\", {\n      type: \"start\"\n    });\n    __publicField(this, \"endEvent\", {\n      type: \"end\"\n    });\n    __publicField(this, \"state\", STATE.NONE);\n    __publicField(this, \"EPS\", 1e-6);\n    __publicField(this, \"scale\", 1);\n    __publicField(this, \"panOffset\", new Vector3());\n    __publicField(this, \"zoomChanged\", false);\n    __publicField(this, \"rotateStart\", new Vector2());\n    __publicField(this, \"rotateEnd\", new Vector2());\n    __publicField(this, \"rotateDelta\", new Vector2());\n    __publicField(this, \"panStart\", new Vector2());\n    __publicField(this, \"panEnd\", new Vector2());\n    __publicField(this, \"panDelta\", new Vector2());\n    __publicField(this, \"dollyStart\", new Vector2());\n    __publicField(this, \"dollyEnd\", new Vector2());\n    __publicField(this, \"dollyDelta\", new Vector2());\n    __publicField(this, \"offset\", new Vector3());\n    __publicField(this, \"lastPosition\", new Vector3());\n    __publicField(this, \"lastQuaternion\", new Quaternion());\n    __publicField(this, \"q\", new Quaternion());\n    __publicField(this, \"v\", new Vector3());\n    __publicField(this, \"vec\", new Vector3());\n    __publicField(this, \"quat\");\n    __publicField(this, \"quatInverse\");\n    __publicField(this, \"getPolarAngle\", () => this.spherical.phi);\n    __publicField(this, \"getAzimuthalAngle\", () => this.spherical.theta);\n    __publicField(this, \"saveState\", () => {\n      this.target0.copy(this.target);\n      this.position0.copy(this.object.position);\n      this.quaternion0.copy(this.object.quaternion);\n      this.zoom0 = this.object.zoom;\n    });\n    __publicField(this, \"reset\", () => {\n      this.target.copy(this.target0);\n      this.object.position.copy(this.position0);\n      this.object.quaternion.copy(this.quaternion0);\n      this.object.zoom = this.zoom0;\n      this.object.updateProjectionMatrix();\n      this.dispatchEvent(this.changeEvent);\n      this.update();\n      this.state = STATE.NONE;\n    });\n    __publicField(this, \"dispose\", () => {\n      this.domElement.removeEventListener(\"contextmenu\", this.onContextMenu, false);\n      this.domElement.removeEventListener(\"mousedown\", this.onMouseDown, false);\n      this.domElement.removeEventListener(\"wheel\", this.onMouseWheel, false);\n      this.domElement.removeEventListener(\"touchstart\", this.onTouchStart, false);\n      this.domElement.removeEventListener(\"touchend\", this.onTouchEnd, false);\n      this.domElement.removeEventListener(\"touchmove\", this.onTouchMove, false);\n      document.removeEventListener(\"mousemove\", this.onMouseMove, false);\n      document.removeEventListener(\"mouseup\", this.onMouseUp, false);\n      this.domElement.removeEventListener(\"keydown\", this.onKeyDown, false);\n    });\n    __publicField(this, \"update\", () => {\n      const position = this.object.position;\n      this.offset.copy(position).sub(this.target);\n      if (this.trackball) {\n        if (this.sphericalDelta.theta) {\n          this.vec.set(0, 1, 0).applyQuaternion(this.object.quaternion);\n          const factor = this.enableDamping ? this.dampingFactor : 1;\n          this.q.setFromAxisAngle(this.vec, this.sphericalDelta.theta * factor);\n          this.object.quaternion.premultiply(this.q);\n          this.offset.applyQuaternion(this.q);\n        }\n        if (this.sphericalDelta.phi) {\n          this.vec.set(1, 0, 0).applyQuaternion(this.object.quaternion);\n          const factor = this.enableDamping ? this.dampingFactor : 1;\n          this.q.setFromAxisAngle(this.vec, this.sphericalDelta.phi * factor);\n          this.object.quaternion.premultiply(this.q);\n          this.offset.applyQuaternion(this.q);\n        }\n        this.offset.multiplyScalar(this.scale);\n        this.offset.clampLength(this.minDistance, this.maxDistance);\n      } else {\n        this.offset.applyQuaternion(this.quat);\n        if (this.autoRotate && this.state === STATE.NONE) {\n          this.rotateLeft(this.getAutoRotationAngle());\n        }\n        this.spherical.setFromVector3(this.offset);\n        if (this.enableDamping) {\n          this.spherical.theta += this.sphericalDelta.theta * this.dampingFactor;\n          this.spherical.phi += this.sphericalDelta.phi * this.dampingFactor;\n        } else {\n          this.spherical.theta += this.sphericalDelta.theta;\n          this.spherical.phi += this.sphericalDelta.phi;\n        }\n        this.spherical.theta = Math.max(this.minAzimuthAngle, Math.min(this.maxAzimuthAngle, this.spherical.theta));\n        this.spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, this.spherical.phi));\n        this.spherical.makeSafe();\n        this.spherical.radius *= this.scale;\n        this.spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, this.spherical.radius));\n        this.offset.setFromSpherical(this.spherical);\n        this.offset.applyQuaternion(this.quatInverse);\n      }\n      if (this.enableDamping === true) {\n        this.target.addScaledVector(this.panOffset, this.dampingFactor);\n      } else {\n        this.target.add(this.panOffset);\n      }\n      position.copy(this.target).add(this.offset);\n      if (this.trackball === false) {\n        this.object.lookAt(this.target);\n      }\n      if (this.enableDamping === true) {\n        this.sphericalDelta.theta *= 1 - this.dampingFactor;\n        this.sphericalDelta.phi *= 1 - this.dampingFactor;\n        this.panOffset.multiplyScalar(1 - this.dampingFactor);\n      } else {\n        this.sphericalDelta.set(0, 0, 0);\n        this.panOffset.set(0, 0, 0);\n      }\n      this.scale = 1;\n      if (this.zoomChanged || this.lastPosition.distanceToSquared(this.object.position) > this.EPS || 8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n        this.dispatchEvent(this.changeEvent);\n        this.lastPosition.copy(this.object.position);\n        this.lastQuaternion.copy(this.object.quaternion);\n        this.zoomChanged = false;\n        return true;\n      }\n      return false;\n    });\n    __publicField(this, \"getAutoRotationAngle\", () => 2 * Math.PI / 60 / 60 * this.autoRotateSpeed);\n    __publicField(this, \"getZoomScale\", () => Math.pow(0.95, this.zoomSpeed));\n    __publicField(this, \"rotateLeft\", angle => {\n      this.sphericalDelta.theta -= angle;\n    });\n    __publicField(this, \"rotateUp\", angle => {\n      this.sphericalDelta.phi -= angle;\n    });\n    __publicField(this, \"panLeft\", (distance, objectMatrix) => {\n      this.v.setFromMatrixColumn(objectMatrix, 0);\n      this.v.multiplyScalar(-distance);\n      this.panOffset.add(this.v);\n    });\n    __publicField(this, \"panUp\", (distance, objectMatrix) => {\n      if (this.screenSpacePanning === true) {\n        this.v.setFromMatrixColumn(objectMatrix, 1);\n      } else {\n        this.v.setFromMatrixColumn(objectMatrix, 0);\n        this.v.crossVectors(this.object.up, this.v);\n      }\n      this.v.multiplyScalar(distance);\n      this.panOffset.add(this.v);\n    });\n    // deltaX and deltaY are in pixels; right and down are positive\n    __publicField(this, \"pan\", (deltaX, deltaY) => {\n      const element = this.domElement;\n      if (this.object instanceof PerspectiveCamera) {\n        const position = this.object.position;\n        this.offset.copy(position).sub(this.target);\n        let targetDistance = this.offset.length();\n        targetDistance *= Math.tan(this.object.fov / 2 * Math.PI / 180);\n        this.panLeft(2 * deltaX * targetDistance / element.clientHeight, this.object.matrix);\n        this.panUp(2 * deltaY * targetDistance / element.clientHeight, this.object.matrix);\n      } else if (this.object.isOrthographicCamera) {\n        this.panLeft(deltaX * (this.object.right - this.object.left) / this.object.zoom / element.clientWidth, this.object.matrix);\n        this.panUp(deltaY * (this.object.top - this.object.bottom) / this.object.zoom / element.clientHeight, this.object.matrix);\n      } else {\n        console.warn(\"WARNING: CameraControls.js encountered an unknown camera type - pan disabled.\");\n        this.enablePan = false;\n      }\n    });\n    __publicField(this, \"dollyIn\", dollyScale => {\n      if (this.object instanceof PerspectiveCamera) {\n        this.scale /= dollyScale;\n      } else if (this.object instanceof OrthographicCamera) {\n        this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom * dollyScale));\n        this.object.updateProjectionMatrix();\n        this.zoomChanged = true;\n      } else {\n        console.warn(\"WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.\");\n        this.enableZoom = false;\n      }\n    });\n    __publicField(this, \"dollyOut\", dollyScale => {\n      if (this.object instanceof PerspectiveCamera) {\n        this.scale *= dollyScale;\n      } else if (this.object instanceof OrthographicCamera) {\n        this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / dollyScale));\n        this.object.updateProjectionMatrix();\n        this.zoomChanged = true;\n      } else {\n        console.warn(\"WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.\");\n        this.enableZoom = false;\n      }\n    });\n    // event callbacks - update the object state\n    __publicField(this, \"handleMouseDownRotate\", event => {\n      this.rotateStart.set(event.clientX, event.clientY);\n    });\n    // TODO: confirm if worthwhile to return the Vector2 instead of void\n    __publicField(this, \"handleMouseDownDolly\", event => {\n      this.dollyStart.set(event.clientX, event.clientY);\n    });\n    __publicField(this, \"handleMouseDownPan\", event => {\n      this.panStart.set(event.clientX, event.clientY);\n    });\n    __publicField(this, \"handleMouseMoveRotate\", event => {\n      this.rotateEnd.set(event.clientX, event.clientY);\n      this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed);\n      const element = this.domElement;\n      this.rotateLeft(2 * Math.PI * this.rotateDelta.x / element.clientHeight);\n      this.rotateUp(2 * Math.PI * this.rotateDelta.y / element.clientHeight);\n      this.rotateStart.copy(this.rotateEnd);\n      this.update();\n    });\n    __publicField(this, \"handleMouseMoveDolly\", event => {\n      this.dollyEnd.set(event.clientX, event.clientY);\n      this.dollyDelta.subVectors(this.dollyEnd, this.dollyStart);\n      if (this.dollyDelta.y > 0) {\n        this.dollyIn(this.getZoomScale());\n      } else if (this.dollyDelta.y < 0) {\n        this.dollyOut(this.getZoomScale());\n      }\n      this.dollyStart.copy(this.dollyEnd);\n      this.update();\n    });\n    __publicField(this, \"handleMouseMovePan\", event => {\n      this.panEnd.set(event.clientX, event.clientY);\n      this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed);\n      this.pan(this.panDelta.x, this.panDelta.y);\n      this.panStart.copy(this.panEnd);\n      this.update();\n    });\n    __publicField(this, \"handleMouseWheel\", event => {\n      if (event.deltaY < 0) {\n        this.dollyOut(this.getZoomScale());\n      } else if (event.deltaY > 0) {\n        this.dollyIn(this.getZoomScale());\n      }\n      this.update();\n    });\n    __publicField(this, \"handleKeyDown\", event => {\n      let needsUpdate = false;\n      switch (event.code) {\n        case this.keys.UP:\n          this.pan(0, this.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case this.keys.BOTTOM:\n          this.pan(0, -this.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case this.keys.LEFT:\n          this.pan(this.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n        case this.keys.RIGHT:\n          this.pan(-this.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n      }\n      if (needsUpdate) {\n        event.preventDefault();\n        this.update();\n      }\n    });\n    __publicField(this, \"handleTouchStartRotate\", event => {\n      if (event.touches.length == 1) {\n        this.rotateStart.set(event.touches[0].pageX, event.touches[0].pageY);\n      } else {\n        const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX);\n        const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY);\n        this.rotateStart.set(x, y);\n      }\n    });\n    __publicField(this, \"handleTouchStartPan\", event => {\n      if (event.touches.length == 1) {\n        this.panStart.set(event.touches[0].pageX, event.touches[0].pageY);\n      } else {\n        const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX);\n        const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY);\n        this.panStart.set(x, y);\n      }\n    });\n    __publicField(this, \"handleTouchStartDolly\", event => {\n      const dx = event.touches[0].pageX - event.touches[1].pageX;\n      const dy = event.touches[0].pageY - event.touches[1].pageY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      this.dollyStart.set(0, distance);\n    });\n    __publicField(this, \"handleTouchStartDollyPan\", event => {\n      if (this.enableZoom) this.handleTouchStartDolly(event);\n      if (this.enablePan) this.handleTouchStartPan(event);\n    });\n    __publicField(this, \"handleTouchStartDollyRotate\", event => {\n      if (this.enableZoom) this.handleTouchStartDolly(event);\n      if (this.enableRotate) this.handleTouchStartRotate(event);\n    });\n    __publicField(this, \"handleTouchMoveRotate\", event => {\n      if (event.touches.length == 1) {\n        this.rotateEnd.set(event.touches[0].pageX, event.touches[0].pageY);\n      } else {\n        const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX);\n        const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY);\n        this.rotateEnd.set(x, y);\n      }\n      this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed);\n      const element = this.domElement;\n      this.rotateLeft(2 * Math.PI * this.rotateDelta.x / element.clientHeight);\n      this.rotateUp(2 * Math.PI * this.rotateDelta.y / element.clientHeight);\n      this.rotateStart.copy(this.rotateEnd);\n    });\n    __publicField(this, \"handleTouchMovePan\", event => {\n      if (event.touches.length == 1) {\n        this.panEnd.set(event.touches[0].pageX, event.touches[0].pageY);\n      } else {\n        const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX);\n        const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY);\n        this.panEnd.set(x, y);\n      }\n      this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed);\n      this.pan(this.panDelta.x, this.panDelta.y);\n      this.panStart.copy(this.panEnd);\n    });\n    __publicField(this, \"handleTouchMoveDolly\", event => {\n      const dx = event.touches[0].pageX - event.touches[1].pageX;\n      const dy = event.touches[0].pageY - event.touches[1].pageY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      this.dollyEnd.set(0, distance);\n      this.dollyDelta.set(0, Math.pow(this.dollyEnd.y / this.dollyStart.y, this.zoomSpeed));\n      this.dollyIn(this.dollyDelta.y);\n      this.dollyStart.copy(this.dollyEnd);\n    });\n    __publicField(this, \"handleTouchMoveDollyPan\", event => {\n      if (this.enableZoom) this.handleTouchMoveDolly(event);\n      if (this.enablePan) this.handleTouchMovePan(event);\n    });\n    __publicField(this, \"handleTouchMoveDollyRotate\", event => {\n      if (this.enableZoom) this.handleTouchMoveDolly(event);\n      if (this.enableRotate) this.handleTouchMoveRotate(event);\n    });\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n    __publicField(this, \"onMouseDown\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      this.domElement.focus ? this.domElement.focus() : window.focus();\n      let mouseAction;\n      switch (event.button) {\n        case 0:\n          mouseAction = this.mouseButtons.LEFT;\n          break;\n        case 1:\n          mouseAction = this.mouseButtons.MIDDLE;\n          break;\n        case 2:\n          mouseAction = this.mouseButtons.RIGHT;\n          break;\n        default:\n          mouseAction = -1;\n      }\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (this.enableZoom === false) return;\n          this.handleMouseDownDolly(event);\n          this.state = STATE.DOLLY;\n          break;\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (this.enablePan === false) return;\n            this.handleMouseDownPan(event);\n            this.state = STATE.PAN;\n          } else {\n            if (this.enableRotate === false) return;\n            this.handleMouseDownRotate(event);\n            this.state = STATE.ROTATE;\n          }\n          break;\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (this.enableRotate === false) return;\n            this.handleMouseDownRotate(event);\n            this.state = STATE.ROTATE;\n          } else {\n            if (this.enablePan === false) return;\n            this.handleMouseDownPan(event);\n            this.state = STATE.PAN;\n          }\n          break;\n        default:\n          this.state = STATE.NONE;\n      }\n      if (this.state !== STATE.NONE) {\n        document.addEventListener(\"mousemove\", this.onMouseMove, false);\n        document.addEventListener(\"mouseup\", this.onMouseUp, false);\n        this.dispatchEvent(this.startEvent);\n      }\n    });\n    __publicField(this, \"onMouseMove\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      switch (this.state) {\n        case STATE.ROTATE:\n          if (this.enableRotate === false) return;\n          this.handleMouseMoveRotate(event);\n          break;\n        case STATE.DOLLY:\n          if (this.enableZoom === false) return;\n          this.handleMouseMoveDolly(event);\n          break;\n        case STATE.PAN:\n          if (this.enablePan === false) return;\n          this.handleMouseMovePan(event);\n          break;\n      }\n    });\n    __publicField(this, \"onMouseUp\", () => {\n      if (this.enabled === false) return;\n      document.removeEventListener(\"mousemove\", this.onMouseMove, false);\n      document.removeEventListener(\"mouseup\", this.onMouseUp, false);\n      this.dispatchEvent(this.endEvent);\n      this.state = STATE.NONE;\n    });\n    __publicField(this, \"onMouseWheel\", event => {\n      if (this.enabled === false || this.enableZoom === false || this.state !== STATE.NONE && this.state !== STATE.ROTATE) {\n        return;\n      }\n      event.preventDefault();\n      this.dispatchEvent(this.startEvent);\n      this.handleMouseWheel(event);\n      this.dispatchEvent(this.endEvent);\n    });\n    __publicField(this, \"onKeyDown\", event => {\n      if (this.enabled === false || this.enableKeys === false || this.enablePan === false) return;\n      this.handleKeyDown(event);\n    });\n    __publicField(this, \"onTouchStart\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      switch (event.touches.length) {\n        case 1:\n          switch (this.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (this.enableRotate === false) return;\n              this.handleTouchStartRotate(event);\n              this.state = STATE.TOUCH_ROTATE;\n              break;\n            case TOUCH.PAN:\n              if (this.enablePan === false) return;\n              this.handleTouchStartPan(event);\n              this.state = STATE.TOUCH_PAN;\n              break;\n            default:\n              this.state = STATE.NONE;\n          }\n          break;\n        case 2:\n          switch (this.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (this.enableZoom === false && this.enablePan === false) return;\n              this.handleTouchStartDollyPan(event);\n              this.state = STATE.TOUCH_DOLLY_PAN;\n              break;\n            case TOUCH.DOLLY_ROTATE:\n              if (this.enableZoom === false && this.enableRotate === false) return;\n              this.handleTouchStartDollyRotate(event);\n              this.state = STATE.TOUCH_DOLLY_ROTATE;\n              break;\n            default:\n              this.state = STATE.NONE;\n          }\n          break;\n        default:\n          this.state = STATE.NONE;\n      }\n      if (this.state !== STATE.NONE) {\n        this.dispatchEvent(this.startEvent);\n      }\n    });\n    __publicField(this, \"onTouchMove\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      switch (this.state) {\n        case STATE.TOUCH_ROTATE:\n          if (this.enableRotate === false) return;\n          this.handleTouchMoveRotate(event);\n          this.update();\n          break;\n        case STATE.TOUCH_PAN:\n          if (this.enablePan === false) return;\n          this.handleTouchMovePan(event);\n          this.update();\n          break;\n        case STATE.TOUCH_DOLLY_PAN:\n          if (this.enableZoom === false && this.enablePan === false) return;\n          this.handleTouchMoveDollyPan(event);\n          this.update();\n          break;\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (this.enableZoom === false && this.enableRotate === false) return;\n          this.handleTouchMoveDollyRotate(event);\n          this.update();\n          break;\n        default:\n          this.state = STATE.NONE;\n      }\n    });\n    __publicField(this, \"onTouchEnd\", () => {\n      if (this.enabled === false) return;\n      this.dispatchEvent(this.endEvent);\n      this.state = STATE.NONE;\n    });\n    __publicField(this, \"onContextMenu\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n    });\n    if (domElement === void 0) {\n      console.warn('THREE.CameraControls: The second parameter \"domElement\" is now mandatory.');\n    }\n    if (domElement instanceof Document) {\n      console.error('THREE.CameraControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.');\n    }\n    this.object = object;\n    this.domElement = domElement;\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN\n    };\n    this.target0 = this.target.clone();\n    this.position0 = this.object.position.clone();\n    this.quaternion0 = this.object.quaternion.clone();\n    this.zoom0 = this.object.zoom;\n    this.quat = new Quaternion().setFromUnitVectors(this.object.up, new Vector3(0, 1, 0));\n    this.quatInverse = this.quat.clone().invert();\n    this.lastPosition = new Vector3();\n    this.lastQuaternion = new Quaternion();\n    this.domElement.addEventListener(\"contextmenu\", this.onContextMenu, false);\n    this.domElement.addEventListener(\"mousedown\", this.onMouseDown, false);\n    this.domElement.addEventListener(\"wheel\", this.onMouseWheel, false);\n    this.domElement.addEventListener(\"touchstart\", this.onTouchStart, false);\n    this.domElement.addEventListener(\"touchend\", this.onTouchEnd, false);\n    this.domElement.addEventListener(\"touchmove\", this.onTouchMove, false);\n    this.domElement.addEventListener(\"keydown\", this.onKeyDown, false);\n    if (this.domElement.tabIndex === -1) {\n      this.domElement.tabIndex = 0;\n    }\n    this.object.lookAt(this.target);\n    this.update();\n    this.saveState();\n  }\n  handleMouseUp() {}\n  handleTouchEnd() {}\n}\nclass OrbitControlsExp extends CameraControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    __publicField(this, \"mouseButtons\");\n    __publicField(this, \"touches\");\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN\n    };\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN\n    };\n  }\n}\nclass MapControlsExp extends CameraControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    __publicField(this, \"mouseButtons\");\n    __publicField(this, \"touches\");\n    this.mouseButtons = {\n      LEFT: MOUSE.PAN,\n      RIGHT: MOUSE.ROTATE\n    };\n    this.touches = {\n      ONE: TOUCH.PAN,\n      TWO: TOUCH.DOLLY_ROTATE\n    };\n  }\n}\nclass TrackballControlsExp extends CameraControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    __publicField(this, \"trackball\");\n    __publicField(this, \"screenSpacePanning\");\n    __publicField(this, \"autoRotate\");\n    __publicField(this, \"mouseButtons\");\n    __publicField(this, \"touches\");\n    this.trackball = true;\n    this.screenSpacePanning = true;\n    this.autoRotate = false;\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN\n    };\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN\n    };\n  }\n}\nexport { CameraControls, MapControlsExp, OrbitControlsExp, STATE, TrackballControlsExp };", "map": {"version": 3, "names": ["STATE", "NONE", "ROTATE", "DOLLY", "PAN", "TOUCH_ROTATE", "TOUCH_PAN", "TOUCH_DOLLY_PAN", "TOUCH_DOLLY_ROTATE", "CameraControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "dom<PERSON>lement", "__publicField", "Vector3", "Infinity", "Math", "PI", "LEFT", "UP", "RIGHT", "BOTTOM", "ONE", "TOUCH", "TWO", "DOLLY_PAN", "Spherical", "type", "Vector2", "Quaternion", "spherical", "phi", "theta", "target0", "copy", "target", "position0", "position", "quaternion0", "quaternion", "zoom0", "zoom", "updateProjectionMatrix", "dispatchEvent", "changeEvent", "update", "state", "removeEventListener", "onContextMenu", "onMouseDown", "onMouseWheel", "onTouchStart", "onTouchEnd", "onTouchMove", "document", "onMouseMove", "onMouseUp", "onKeyDown", "offset", "sub", "trackball", "sphericalDel<PERSON>", "vec", "set", "applyQuaternion", "factor", "enableDamping", "dampingFactor", "q", "setFromAxisAngle", "premultiply", "multiplyScalar", "scale", "clampLength", "minDistance", "maxDistance", "quat", "autoRotate", "rotateLeft", "getAutoRotationAngle", "setFromVector3", "max", "minAzimuthAngle", "min", "maxAzimuthAngle", "minPolarAngle", "maxPolarAngle", "makeSafe", "radius", "setFromSpherical", "quatInverse", "addScaledVector", "panOffset", "add", "lookAt", "zoomChanged", "lastPosition", "distanceToSquared", "EPS", "lastQuaternion", "dot", "autoRotateSpeed", "pow", "zoomSpeed", "angle", "distance", "objectMatrix", "v", "setFromMatrixColumn", "screenSpacePanning", "crossVectors", "up", "deltaX", "deltaY", "element", "PerspectiveCamera", "targetDistance", "length", "tan", "fov", "panLeft", "clientHeight", "matrix", "panUp", "isOrthographicCamera", "right", "left", "clientWidth", "top", "bottom", "console", "warn", "enablePan", "dollyScale", "OrthographicCamera", "minZoom", "max<PERSON><PERSON>", "enableZoom", "event", "rotateStart", "clientX", "clientY", "dolly<PERSON><PERSON><PERSON>", "panStart", "rotateEnd", "<PERSON><PERSON><PERSON><PERSON>", "subVectors", "rotateSpeed", "x", "rotateUp", "y", "dollyEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dollyIn", "getZoomScale", "dollyOut", "panEnd", "panDelta", "panSpeed", "pan", "needsUpdate", "code", "keys", "keyPanSpeed", "preventDefault", "touches", "pageX", "pageY", "dx", "dy", "sqrt", "handleTouchStartDolly", "handleTouchStartPan", "enableRotate", "handleTouchStartRotate", "handleTouchMoveDolly", "handleTouchMovePan", "handleTouchMoveRotate", "enabled", "focus", "window", "mouseAction", "button", "mouseButtons", "MIDDLE", "MOUSE", "handleMouseDownDolly", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "handleMouseDownPan", "handleMouseDownRotate", "addEventListener", "startEvent", "handleMouseMoveRotate", "handleMouseMoveDolly", "handleMouseMovePan", "endEvent", "handleMouseWheel", "<PERSON><PERSON><PERSON><PERSON>", "handleKeyDown", "handleTouchStartDollyPan", "DOLLY_ROTATE", "handleTouchStartDollyRotate", "handleTouchMoveDollyPan", "handleTouchMoveDollyRotate", "Document", "error", "clone", "setFromUnitVectors", "invert", "tabIndex", "saveState", "handleMouseUp", "handleTouchEnd", "OrbitControlsExp", "MapControlsExp", "TrackballControlsExp"], "sources": ["/Users/<USER>/Documents/Workspace/pro/trust-framer/node_modules/src/controls/experimental/CameraControls.ts"], "sourcesContent": ["import {\n  MOUSE,\n  Matrix4,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { EventDispatcher } from '../EventDispatcher'\n\nexport type CHANGE_EVENT = {\n  type: 'change' | 'start' | 'end'\n}\n\nexport const STATE = {\n  NONE: -1,\n  ROTATE: 0,\n  DOLLY: 1,\n  PAN: 2,\n  TOUCH_ROTATE: 3,\n  TOUCH_PAN: 4,\n  TOUCH_DOLLY_PAN: 5,\n  TOUCH_DOLLY_ROTATE: 6,\n}\n\nclass CameraControls extends EventDispatcher<Record<string, {}>> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement\n\n  /** Set to false to disable this control */\n  enabled = true\n\n  /** \"target\" sets the location of focus, where the object orbits around */\n  target = new Vector3()\n\n  /** Set to true to enable trackball behavior */\n  trackball = false\n\n  /** How far you can dolly in ( PerspectiveCamera only ) */\n  minDistance = 0\n  /** How far you can dolly out ( PerspectiveCamera only ) */\n  maxDistance = Infinity\n\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0\n  maxPolarAngle = Math.PI\n\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, must be a sub-interval of the interval [ - Math.PI, Math.PI ].\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n\n  /**\n   * This option enables dollying in and out; property named as \"zoom\" for backwards compatibility\n   * Set to false to disable zooming\n   */\n  enableZoom = true\n  zoomSpeed = 1.0\n\n  /** Set to false to disable rotating */\n  enableRotate = true\n  rotateSpeed = 1.0\n\n  /** Set to false to disable panning */\n  enablePan = true\n  panSpeed = 1.0\n  /** if true, pan in screen-space */\n  screenSpacePanning = false\n  /** pixels moved per arrow key push */\n  keyPanSpeed = 7.0\n\n  /**\n   * Set to true to automatically rotate around the target\n   * If auto-rotate is enabled, you must call controls.update() in your animation loop\n   * auto-rotate is not supported for trackball behavior\n   */\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per round when fps is 60\n\n  /** Set to false to disable use of the keys */\n  enableKeys = true\n\n  /** The four arrow keys */\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n\n  mouseButtons: {\n    LEFT: MOUSE\n    MIDDLE?: MOUSE\n    RIGHT: MOUSE\n  }\n\n  /** Touch fingers */\n  touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n\n  // for reset\n  target0: Vector3\n  position0: Vector3\n  quaternion0: Quaternion\n  zoom0: number\n\n  // current position in spherical coordinates\n  spherical = new Spherical()\n  sphericalDelta = new Spherical()\n\n  private changeEvent = { type: 'change' }\n  private startEvent = { type: 'start' }\n  private endEvent = { type: 'end' }\n  private state = STATE.NONE\n\n  private EPS = 0.000001\n\n  private scale = 1\n  private panOffset = new Vector3()\n  private zoomChanged = false\n\n  private rotateStart = new Vector2()\n  private rotateEnd = new Vector2()\n  private rotateDelta = new Vector2()\n\n  private panStart = new Vector2()\n  private panEnd = new Vector2()\n  private panDelta = new Vector2()\n\n  private dollyStart = new Vector2()\n  private dollyEnd = new Vector2()\n  private dollyDelta = new Vector2()\n\n  private offset = new Vector3()\n\n  private lastPosition = new Vector3()\n  private lastQuaternion = new Quaternion()\n\n  private q = new Quaternion()\n  private v = new Vector3()\n  private vec = new Vector3()\n\n  private quat: Quaternion\n  private quatInverse: Quaternion\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super()\n\n    if (domElement === undefined) {\n      console.warn('THREE.CameraControls: The second parameter \"domElement\" is now mandatory.')\n    }\n    if (domElement instanceof Document) {\n      console.error(\n        'THREE.CameraControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n\n    this.object = object\n    this.domElement = domElement\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN,\n    }\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.quaternion0 = this.object.quaternion.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // internals\n    //\n\n    // so camera.up is the orbit axis\n    this.quat = new Quaternion().setFromUnitVectors(this.object.up, new Vector3(0, 1, 0))\n    this.quatInverse = this.quat.clone().invert()\n\n    this.lastPosition = new Vector3()\n    this.lastQuaternion = new Quaternion()\n\n    this.domElement.addEventListener('contextmenu', this.onContextMenu, false)\n\n    this.domElement.addEventListener('mousedown', this.onMouseDown, false)\n    this.domElement.addEventListener('wheel', this.onMouseWheel, false)\n\n    this.domElement.addEventListener('touchstart', this.onTouchStart, false)\n    this.domElement.addEventListener('touchend', this.onTouchEnd, false)\n    this.domElement.addEventListener('touchmove', this.onTouchMove, false)\n\n    this.domElement.addEventListener('keydown', this.onKeyDown, false)\n\n    // make sure element can receive keys.\n\n    if (this.domElement.tabIndex === -1) {\n      this.domElement.tabIndex = 0\n    }\n\n    // force an update at start\n\n    this.object.lookAt(this.target)\n    this.update()\n    this.saveState()\n  }\n\n  getPolarAngle = (): number => this.spherical.phi\n\n  getAzimuthalAngle = (): number => this.spherical.theta\n\n  saveState = (): void => {\n    this.target0.copy(this.target)\n    this.position0.copy(this.object.position)\n    this.quaternion0.copy(this.object.quaternion)\n    this.zoom0 = this.object.zoom\n  }\n\n  reset = (): void => {\n    this.target.copy(this.target0)\n    this.object.position.copy(this.position0)\n    this.object.quaternion.copy(this.quaternion0)\n    this.object.zoom = this.zoom0\n\n    this.object.updateProjectionMatrix()\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n\n    this.update()\n\n    this.state = STATE.NONE\n  }\n\n  dispose = (): void => {\n    this.domElement.removeEventListener('contextmenu', this.onContextMenu, false)\n    this.domElement.removeEventListener('mousedown', this.onMouseDown, false)\n    this.domElement.removeEventListener('wheel', this.onMouseWheel, false)\n\n    this.domElement.removeEventListener('touchstart', this.onTouchStart, false)\n    this.domElement.removeEventListener('touchend', this.onTouchEnd, false)\n    this.domElement.removeEventListener('touchmove', this.onTouchMove, false)\n\n    document.removeEventListener('mousemove', this.onMouseMove, false)\n    document.removeEventListener('mouseup', this.onMouseUp, false)\n\n    this.domElement.removeEventListener('keydown', this.onKeyDown, false)\n\n    //this.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n  }\n\n  private update = (): boolean => {\n    const position = this.object.position\n\n    this.offset.copy(position).sub(this.target)\n\n    if (this.trackball) {\n      // rotate around screen-space y-axis\n\n      if (this.sphericalDelta.theta) {\n        this.vec.set(0, 1, 0).applyQuaternion(this.object.quaternion)\n\n        const factor = this.enableDamping ? this.dampingFactor : 1\n\n        this.q.setFromAxisAngle(this.vec, this.sphericalDelta.theta * factor)\n\n        this.object.quaternion.premultiply(this.q)\n        this.offset.applyQuaternion(this.q)\n      }\n\n      // rotate around screen-space x-axis\n\n      if (this.sphericalDelta.phi) {\n        this.vec.set(1, 0, 0).applyQuaternion(this.object.quaternion)\n\n        const factor = this.enableDamping ? this.dampingFactor : 1\n\n        this.q.setFromAxisAngle(this.vec, this.sphericalDelta.phi * factor)\n\n        this.object.quaternion.premultiply(this.q)\n        this.offset.applyQuaternion(this.q)\n      }\n\n      this.offset.multiplyScalar(this.scale)\n      this.offset.clampLength(this.minDistance, this.maxDistance)\n    } else {\n      // rotate offset to \"y-axis-is-up\" space\n      this.offset.applyQuaternion(this.quat)\n\n      if (this.autoRotate && this.state === STATE.NONE) {\n        this.rotateLeft(this.getAutoRotationAngle())\n      }\n\n      this.spherical.setFromVector3(this.offset)\n\n      if (this.enableDamping) {\n        this.spherical.theta += this.sphericalDelta.theta * this.dampingFactor\n        this.spherical.phi += this.sphericalDelta.phi * this.dampingFactor\n      } else {\n        this.spherical.theta += this.sphericalDelta.theta\n        this.spherical.phi += this.sphericalDelta.phi\n      }\n\n      // restrict theta to be between desired limits\n      this.spherical.theta = Math.max(this.minAzimuthAngle, Math.min(this.maxAzimuthAngle, this.spherical.theta))\n\n      // restrict phi to be between desired limits\n      this.spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, this.spherical.phi))\n\n      this.spherical.makeSafe()\n\n      this.spherical.radius *= this.scale\n\n      // restrict radius to be between desired limits\n      this.spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, this.spherical.radius))\n\n      this.offset.setFromSpherical(this.spherical)\n\n      // rotate offset back to \"camera-up-vector-is-up\" space\n      this.offset.applyQuaternion(this.quatInverse)\n    }\n\n    // move target to panned location\n\n    if (this.enableDamping === true) {\n      this.target.addScaledVector(this.panOffset, this.dampingFactor)\n    } else {\n      this.target.add(this.panOffset)\n    }\n\n    position.copy(this.target).add(this.offset)\n\n    if (this.trackball === false) {\n      this.object.lookAt(this.target)\n    }\n\n    if (this.enableDamping === true) {\n      this.sphericalDelta.theta *= 1 - this.dampingFactor\n      this.sphericalDelta.phi *= 1 - this.dampingFactor\n\n      this.panOffset.multiplyScalar(1 - this.dampingFactor)\n    } else {\n      this.sphericalDelta.set(0, 0, 0)\n\n      this.panOffset.set(0, 0, 0)\n    }\n\n    this.scale = 1\n\n    // update condition is:\n    // min(camera displacement, camera rotation in radians)^2 > EPS\n    // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n    if (\n      this.zoomChanged ||\n      this.lastPosition.distanceToSquared(this.object.position) > this.EPS ||\n      8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS\n    ) {\n      // @ts-ignore\n      this.dispatchEvent(this.changeEvent)\n\n      this.lastPosition.copy(this.object.position)\n      this.lastQuaternion.copy(this.object.quaternion)\n      this.zoomChanged = false\n\n      return true\n    }\n\n    return false\n  }\n\n  private getAutoRotationAngle = (): number => ((2 * Math.PI) / 60 / 60) * this.autoRotateSpeed\n\n  private getZoomScale = (): number => Math.pow(0.95, this.zoomSpeed)\n\n  private rotateLeft = (angle: number): void => {\n    this.sphericalDelta.theta -= angle\n  }\n\n  private rotateUp = (angle: number): void => {\n    this.sphericalDelta.phi -= angle\n  }\n\n  private panLeft = (distance: number, objectMatrix: Matrix4): void => {\n    this.v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n    this.v.multiplyScalar(-distance)\n\n    this.panOffset.add(this.v)\n  }\n\n  private panUp = (distance: number, objectMatrix: Matrix4): void => {\n    if (this.screenSpacePanning === true) {\n      this.v.setFromMatrixColumn(objectMatrix, 1)\n    } else {\n      this.v.setFromMatrixColumn(objectMatrix, 0)\n      this.v.crossVectors(this.object.up, this.v)\n    }\n\n    this.v.multiplyScalar(distance)\n\n    this.panOffset.add(this.v)\n  }\n\n  // deltaX and deltaY are in pixels; right and down are positive\n  private pan = (deltaX: number, deltaY: number): void => {\n    const element = this.domElement\n\n    if (this.object instanceof PerspectiveCamera) {\n      // perspective\n      const position = this.object.position\n      this.offset.copy(position).sub(this.target)\n      let targetDistance = this.offset.length()\n\n      // half of the fov is center to top of screen\n      targetDistance *= Math.tan(((this.object.fov / 2) * Math.PI) / 180.0)\n\n      // we use only clientHeight here so aspect ratio does not distort speed\n      this.panLeft((2 * deltaX * targetDistance) / element.clientHeight, this.object.matrix)\n      this.panUp((2 * deltaY * targetDistance) / element.clientHeight, this.object.matrix)\n    } else if (this.object.isOrthographicCamera) {\n      // orthographic\n      this.panLeft(\n        (deltaX * (this.object.right - this.object.left)) / this.object.zoom / element.clientWidth,\n        this.object.matrix,\n      )\n      this.panUp(\n        (deltaY * (this.object.top - this.object.bottom)) / this.object.zoom / element.clientHeight,\n        this.object.matrix,\n      )\n    } else {\n      // camera neither orthographic nor perspective\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - pan disabled.')\n      this.enablePan = false\n    }\n  }\n\n  private dollyIn = (dollyScale: number): void => {\n    // TODO: replace w/.isPerspectiveCamera ?\n    if (this.object instanceof PerspectiveCamera) {\n      this.scale /= dollyScale\n      // TODO: replace w/.isOrthographicCamera ?\n    } else if (this.object instanceof OrthographicCamera) {\n      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom * dollyScale))\n      this.object.updateProjectionMatrix()\n      this.zoomChanged = true\n    } else {\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.')\n      this.enableZoom = false\n    }\n  }\n\n  private dollyOut = (dollyScale: number): void => {\n    // TODO: replace w/.isPerspectiveCamera ?\n    if (this.object instanceof PerspectiveCamera) {\n      this.scale *= dollyScale\n      // TODO: replace w/.isOrthographicCamera ?\n    } else if (this.object instanceof OrthographicCamera) {\n      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / dollyScale))\n      this.object.updateProjectionMatrix()\n      this.zoomChanged = true\n    } else {\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.')\n      this.enableZoom = false\n    }\n  }\n\n  // event callbacks - update the object state\n\n  private handleMouseDownRotate = (event: MouseEvent): void => {\n    this.rotateStart.set(event.clientX, event.clientY)\n  }\n\n  // TODO: confirm if worthwhile to return the Vector2 instead of void\n  private handleMouseDownDolly = (event: MouseEvent): void => {\n    this.dollyStart.set(event.clientX, event.clientY)\n  }\n\n  private handleMouseDownPan = (event: MouseEvent): void => {\n    this.panStart.set(event.clientX, event.clientY)\n  }\n\n  private handleMouseMoveRotate = (event: MouseEvent): void => {\n    this.rotateEnd.set(event.clientX, event.clientY)\n\n    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed)\n\n    const element = this.domElement\n\n    this.rotateLeft((2 * Math.PI * this.rotateDelta.x) / element.clientHeight) // yes, height\n\n    this.rotateUp((2 * Math.PI * this.rotateDelta.y) / element.clientHeight)\n\n    this.rotateStart.copy(this.rotateEnd)\n\n    this.update()\n  }\n\n  private handleMouseMoveDolly = (event: MouseEvent): void => {\n    this.dollyEnd.set(event.clientX, event.clientY)\n\n    this.dollyDelta.subVectors(this.dollyEnd, this.dollyStart)\n\n    if (this.dollyDelta.y > 0) {\n      this.dollyIn(this.getZoomScale())\n    } else if (this.dollyDelta.y < 0) {\n      this.dollyOut(this.getZoomScale())\n    }\n\n    this.dollyStart.copy(this.dollyEnd)\n\n    this.update()\n  }\n\n  private handleMouseMovePan = (event: MouseEvent): void => {\n    this.panEnd.set(event.clientX, event.clientY)\n\n    this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed)\n\n    this.pan(this.panDelta.x, this.panDelta.y)\n\n    this.panStart.copy(this.panEnd)\n\n    this.update()\n  }\n\n  private handleMouseUp(/*event*/): void {\n    // no-op\n  }\n\n  private handleMouseWheel = (event: WheelEvent): void => {\n    if (event.deltaY < 0) {\n      this.dollyOut(this.getZoomScale())\n    } else if (event.deltaY > 0) {\n      this.dollyIn(this.getZoomScale())\n    }\n\n    this.update()\n  }\n\n  private handleKeyDown = (event: KeyboardEvent): void => {\n    let needsUpdate = false\n\n    switch (event.code) {\n      case this.keys.UP:\n        this.pan(0, this.keyPanSpeed)\n        needsUpdate = true\n        break\n\n      case this.keys.BOTTOM:\n        this.pan(0, -this.keyPanSpeed)\n        needsUpdate = true\n        break\n\n      case this.keys.LEFT:\n        this.pan(this.keyPanSpeed, 0)\n        needsUpdate = true\n        break\n\n      case this.keys.RIGHT:\n        this.pan(-this.keyPanSpeed, 0)\n        needsUpdate = true\n        break\n    }\n\n    if (needsUpdate) {\n      // prevent the browser from scrolling on cursor keys\n      event.preventDefault()\n\n      this.update()\n    }\n  }\n\n  private handleTouchStartRotate = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.rotateStart.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.rotateStart.set(x, y)\n    }\n  }\n\n  private handleTouchStartPan = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.panStart.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.panStart.set(x, y)\n    }\n  }\n\n  private handleTouchStartDolly = (event: TouchEvent): void => {\n    const dx = event.touches[0].pageX - event.touches[1].pageX\n    const dy = event.touches[0].pageY - event.touches[1].pageY\n\n    const distance = Math.sqrt(dx * dx + dy * dy)\n\n    this.dollyStart.set(0, distance)\n  }\n\n  private handleTouchStartDollyPan = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchStartDolly(event)\n\n    if (this.enablePan) this.handleTouchStartPan(event)\n  }\n\n  private handleTouchStartDollyRotate = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchStartDolly(event)\n\n    if (this.enableRotate) this.handleTouchStartRotate(event)\n  }\n\n  private handleTouchMoveRotate = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.rotateEnd.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.rotateEnd.set(x, y)\n    }\n\n    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed)\n\n    const element = this.domElement\n\n    this.rotateLeft((2 * Math.PI * this.rotateDelta.x) / element.clientHeight) // yes, height\n\n    this.rotateUp((2 * Math.PI * this.rotateDelta.y) / element.clientHeight)\n\n    this.rotateStart.copy(this.rotateEnd)\n  }\n\n  private handleTouchMovePan = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.panEnd.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.panEnd.set(x, y)\n    }\n\n    this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed)\n\n    this.pan(this.panDelta.x, this.panDelta.y)\n\n    this.panStart.copy(this.panEnd)\n  }\n\n  private handleTouchMoveDolly = (event: TouchEvent): void => {\n    const dx = event.touches[0].pageX - event.touches[1].pageX\n    const dy = event.touches[0].pageY - event.touches[1].pageY\n\n    const distance = Math.sqrt(dx * dx + dy * dy)\n\n    this.dollyEnd.set(0, distance)\n\n    this.dollyDelta.set(0, Math.pow(this.dollyEnd.y / this.dollyStart.y, this.zoomSpeed))\n\n    this.dollyIn(this.dollyDelta.y)\n\n    this.dollyStart.copy(this.dollyEnd)\n  }\n\n  private handleTouchMoveDollyPan = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchMoveDolly(event)\n\n    if (this.enablePan) this.handleTouchMovePan(event)\n  }\n\n  private handleTouchMoveDollyRotate = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchMoveDolly(event)\n\n    if (this.enableRotate) this.handleTouchMoveRotate(event)\n  }\n\n  private handleTouchEnd(/*event*/): void {\n    // no-op\n  }\n\n  //\n  // event handlers - FSM: listen for events and reset state\n  //\n\n  private onMouseDown = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    // Prevent the browser from scrolling.\n\n    event.preventDefault()\n\n    // Manually set the focus since calling preventDefault above\n    // prevents the browser from setting it automatically.\n\n    this.domElement.focus ? this.domElement.focus() : window.focus()\n\n    let mouseAction\n\n    switch (event.button) {\n      case 0:\n        mouseAction = this.mouseButtons.LEFT\n        break\n\n      case 1:\n        mouseAction = this.mouseButtons.MIDDLE\n        break\n\n      case 2:\n        mouseAction = this.mouseButtons.RIGHT\n        break\n\n      default:\n        mouseAction = -1\n    }\n\n    switch (mouseAction) {\n      case MOUSE.DOLLY:\n        if (this.enableZoom === false) return\n\n        this.handleMouseDownDolly(event)\n\n        this.state = STATE.DOLLY\n\n        break\n\n      case MOUSE.ROTATE:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enablePan === false) return\n\n          this.handleMouseDownPan(event)\n\n          this.state = STATE.PAN\n        } else {\n          if (this.enableRotate === false) return\n\n          this.handleMouseDownRotate(event)\n\n          this.state = STATE.ROTATE\n        }\n\n        break\n\n      case MOUSE.PAN:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate === false) return\n\n          this.handleMouseDownRotate(event)\n\n          this.state = STATE.ROTATE\n        } else {\n          if (this.enablePan === false) return\n\n          this.handleMouseDownPan(event)\n\n          this.state = STATE.PAN\n        }\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n\n    if (this.state !== STATE.NONE) {\n      document.addEventListener('mousemove', this.onMouseMove, false)\n      document.addEventListener('mouseup', this.onMouseUp, false)\n\n      // @ts-ignore\n      this.dispatchEvent(this.startEvent)\n    }\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (this.state) {\n      case STATE.ROTATE:\n        if (this.enableRotate === false) return\n\n        this.handleMouseMoveRotate(event)\n\n        break\n\n      case STATE.DOLLY:\n        if (this.enableZoom === false) return\n\n        this.handleMouseMoveDolly(event)\n\n        break\n\n      case STATE.PAN:\n        if (this.enablePan === false) return\n\n        this.handleMouseMovePan(event)\n\n        break\n    }\n  }\n\n  private onMouseUp = (): void => {\n    if (this.enabled === false) return\n\n    // this.handleMouseUp()\n\n    document.removeEventListener('mousemove', this.onMouseMove, false)\n    document.removeEventListener('mouseup', this.onMouseUp, false)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n\n    this.state = STATE.NONE\n  }\n\n  private onMouseWheel = (event: WheelEvent): void => {\n    if (\n      this.enabled === false ||\n      this.enableZoom === false ||\n      (this.state !== STATE.NONE && this.state !== STATE.ROTATE)\n    ) {\n      return\n    }\n\n    event.preventDefault()\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n\n    this.handleMouseWheel(event)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private onKeyDown = (event: KeyboardEvent): void => {\n    if (this.enabled === false || this.enableKeys === false || this.enablePan === false) return\n\n    this.handleKeyDown(event)\n  }\n\n  private onTouchStart = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        switch (this.touches.ONE) {\n          case TOUCH.ROTATE:\n            if (this.enableRotate === false) return\n\n            this.handleTouchStartRotate(event)\n\n            this.state = STATE.TOUCH_ROTATE\n\n            break\n\n          case TOUCH.PAN:\n            if (this.enablePan === false) return\n\n            this.handleTouchStartPan(event)\n\n            this.state = STATE.TOUCH_PAN\n\n            break\n\n          default:\n            this.state = STATE.NONE\n        }\n\n        break\n\n      case 2:\n        switch (this.touches.TWO) {\n          case TOUCH.DOLLY_PAN:\n            if (this.enableZoom === false && this.enablePan === false) return\n\n            this.handleTouchStartDollyPan(event)\n\n            this.state = STATE.TOUCH_DOLLY_PAN\n\n            break\n\n          case TOUCH.DOLLY_ROTATE:\n            if (this.enableZoom === false && this.enableRotate === false) return\n\n            this.handleTouchStartDollyRotate(event)\n\n            this.state = STATE.TOUCH_DOLLY_ROTATE\n\n            break\n\n          default:\n            this.state = STATE.NONE\n        }\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n\n    if (this.state !== STATE.NONE) {\n      // @ts-ignore\n      this.dispatchEvent(this.startEvent)\n    }\n  }\n\n  private onTouchMove = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (this.state) {\n      case STATE.TOUCH_ROTATE:\n        if (this.enableRotate === false) return\n\n        this.handleTouchMoveRotate(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_PAN:\n        if (this.enablePan === false) return\n\n        this.handleTouchMovePan(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_DOLLY_PAN:\n        if (this.enableZoom === false && this.enablePan === false) return\n\n        this.handleTouchMoveDollyPan(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_DOLLY_ROTATE:\n        if (this.enableZoom === false && this.enableRotate === false) return\n\n        this.handleTouchMoveDollyRotate(event)\n\n        this.update()\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n  }\n\n  private onTouchEnd = (): void => {\n    if (this.enabled === false) return\n\n    // this.handleTouchEnd()\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n\n    this.state = STATE.NONE\n  }\n\n  private onContextMenu = (event: Event): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n  }\n}\n\n/**\n * OrbitControls maintains the \"up\" direction, camera.up (+Y by default).\n *\n * @event Orbit - left mouse / touch: one-finger move\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n */\nclass OrbitControlsExp extends CameraControls {\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN,\n    }\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN,\n    }\n  }\n}\n\n/**\n * MapControls maintains the \"up\" direction, camera.up (+Y by default)\n *\n * @event Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - left mouse, or left right + ctrl/meta/shiftKey, or arrow keys / touch: one-finger move\n */\nclass MapControlsExp extends CameraControls {\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.mouseButtons = {\n      LEFT: MOUSE.PAN,\n      RIGHT: MOUSE.ROTATE,\n    }\n    this.touches = {\n      ONE: TOUCH.PAN,\n      TWO: TOUCH.DOLLY_ROTATE,\n    }\n  }\n}\n\n/**\n * TrackballControls allows the camera to rotate over the polls and does not maintain camera.up\n *\n * @event Orbit - left mouse / touch: one-finger move\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n */\nclass TrackballControlsExp extends CameraControls {\n  trackball: boolean\n  screenSpacePanning: boolean\n  autoRotate: boolean\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.trackball = true\n    this.screenSpacePanning = true\n    this.autoRotate = false\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN,\n    }\n\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN,\n    }\n  }\n}\n\nexport { CameraControls, OrbitControlsExp, MapControlsExp, TrackballControlsExp }\n"], "mappings": ";;;;;;;;;;;;;AAiBO,MAAMA,KAAA,GAAQ;EACnBC,IAAA,EAAM;EACNC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,GAAA,EAAK;EACLC,YAAA,EAAc;EACdC,SAAA,EAAW;EACXC,eAAA,EAAiB;EACjBC,kBAAA,EAAoB;AACtB;AAEA,MAAMC,cAAA,SAAuBC,eAAA,CAAoC;EA4H/DC,YAAYC,MAAA,EAAgDC,UAAA,EAAyB;IAC7E;IA5HRC,aAAA;IACAA,aAAA;IAGA;IAAAA,aAAA,kBAAU;IAGV;IAAAA,aAAA,iBAAS,IAAIC,OAAA;IAGb;IAAAD,aAAA,oBAAY;IAGZ;IAAAA,aAAA,sBAAc;IAEd;IAAAA,aAAA,sBAAcE,QAAA;IAGd;IAAAF,aAAA,kBAAU;IACVA,aAAA,kBAAUE,QAAA;IAIV;IAAA;IAAAF,aAAA,wBAAgB;IAChBA,aAAA,wBAAgBG,IAAA,CAAKC,EAAA;IAIrB;IAAA;IAAAJ,aAAA,0BAAkB,CAAAE,QAAA;IAClB;IAAAF,aAAA,0BAAkBE,QAAA;IAIlB;IAAA;IAAA;IAAAF,aAAA,wBAAgB;IAChBA,aAAA,wBAAgB;IAMhB;AAAA;AAAA;AAAA;IAAAA,aAAA,qBAAa;IACbA,aAAA,oBAAY;IAGZ;IAAAA,aAAA,uBAAe;IACfA,aAAA,sBAAc;IAGd;IAAAA,aAAA,oBAAY;IACZA,aAAA,mBAAW;IAEX;IAAAA,aAAA,6BAAqB;IAErB;IAAAA,aAAA,sBAAc;IAOd;AAAA;AAAA;AAAA;AAAA;IAAAA,aAAA,qBAAa;IACbA,aAAA,0BAAkB;IAGlB;IAAA;IAAAA,aAAA,qBAAa;IAGb;IAAAA,aAAA,eAAO;MAAEK,IAAA,EAAM;MAAaC,EAAA,EAAI;MAAWC,KAAA,EAAO;MAAcC,MAAA,EAAQ;IAAA;IAExER,aAAA;IAOA;IAAAA,aAAA,kBAAU;MAAES,GAAA,EAAKC,KAAA,CAAMtB,MAAA;MAAQuB,GAAA,EAAKD,KAAA,CAAME;IAAA;IAG1C;IAAAZ,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAGA;IAAAA,aAAA,oBAAY,IAAIa,SAAA;IAChBb,aAAA,yBAAiB,IAAIa,SAAA;IAEbb,aAAA,sBAAc;MAAEc,IAAA,EAAM;IAAA;IACtBd,aAAA,qBAAa;MAAEc,IAAA,EAAM;IAAA;IACrBd,aAAA,mBAAW;MAAEc,IAAA,EAAM;IAAA;IACnBd,aAAA,gBAAQd,KAAA,CAAMC,IAAA;IAEda,aAAA,cAAM;IAENA,aAAA,gBAAQ;IACRA,aAAA,oBAAY,IAAIC,OAAA;IAChBD,aAAA,sBAAc;IAEdA,aAAA,sBAAc,IAAIe,OAAA;IAClBf,aAAA,oBAAY,IAAIe,OAAA;IAChBf,aAAA,sBAAc,IAAIe,OAAA;IAElBf,aAAA,mBAAW,IAAIe,OAAA;IACff,aAAA,iBAAS,IAAIe,OAAA;IACbf,aAAA,mBAAW,IAAIe,OAAA;IAEff,aAAA,qBAAa,IAAIe,OAAA;IACjBf,aAAA,mBAAW,IAAIe,OAAA;IACff,aAAA,qBAAa,IAAIe,OAAA;IAEjBf,aAAA,iBAAS,IAAIC,OAAA;IAEbD,aAAA,uBAAe,IAAIC,OAAA;IACnBD,aAAA,yBAAiB,IAAIgB,UAAA;IAErBhB,aAAA,YAAI,IAAIgB,UAAA;IACRhB,aAAA,YAAI,IAAIC,OAAA;IACRD,aAAA,cAAM,IAAIC,OAAA;IAEVD,aAAA;IACAA,aAAA;IAgERA,aAAA,wBAAgB,MAAc,KAAKiB,SAAA,CAAUC,GAAA;IAE7ClB,aAAA,4BAAoB,MAAc,KAAKiB,SAAA,CAAUE,KAAA;IAEjDnB,aAAA,oBAAY,MAAY;MACjB,KAAAoB,OAAA,CAAQC,IAAA,CAAK,KAAKC,MAAM;MAC7B,KAAKC,SAAA,CAAUF,IAAA,CAAK,KAAKvB,MAAA,CAAO0B,QAAQ;MACxC,KAAKC,WAAA,CAAYJ,IAAA,CAAK,KAAKvB,MAAA,CAAO4B,UAAU;MACvC,KAAAC,KAAA,GAAQ,KAAK7B,MAAA,CAAO8B,IAAA;IAAA;IAG3B5B,aAAA,gBAAQ,MAAY;MACb,KAAAsB,MAAA,CAAOD,IAAA,CAAK,KAAKD,OAAO;MAC7B,KAAKtB,MAAA,CAAO0B,QAAA,CAASH,IAAA,CAAK,KAAKE,SAAS;MACxC,KAAKzB,MAAA,CAAO4B,UAAA,CAAWL,IAAA,CAAK,KAAKI,WAAW;MACvC,KAAA3B,MAAA,CAAO8B,IAAA,GAAO,KAAKD,KAAA;MAExB,KAAK7B,MAAA,CAAO+B,sBAAA;MAEP,KAAAC,aAAA,CAAc,KAAKC,WAAW;MAEnC,KAAKC,MAAA,CAAO;MAEZ,KAAKC,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;IAAA;IAGrBa,aAAA,kBAAU,MAAY;MACpB,KAAKD,UAAA,CAAWmC,mBAAA,CAAoB,eAAe,KAAKC,aAAA,EAAe,KAAK;MAC5E,KAAKpC,UAAA,CAAWmC,mBAAA,CAAoB,aAAa,KAAKE,WAAA,EAAa,KAAK;MACxE,KAAKrC,UAAA,CAAWmC,mBAAA,CAAoB,SAAS,KAAKG,YAAA,EAAc,KAAK;MAErE,KAAKtC,UAAA,CAAWmC,mBAAA,CAAoB,cAAc,KAAKI,YAAA,EAAc,KAAK;MAC1E,KAAKvC,UAAA,CAAWmC,mBAAA,CAAoB,YAAY,KAAKK,UAAA,EAAY,KAAK;MACtE,KAAKxC,UAAA,CAAWmC,mBAAA,CAAoB,aAAa,KAAKM,WAAA,EAAa,KAAK;MAExEC,QAAA,CAASP,mBAAA,CAAoB,aAAa,KAAKQ,WAAA,EAAa,KAAK;MACjED,QAAA,CAASP,mBAAA,CAAoB,WAAW,KAAKS,SAAA,EAAW,KAAK;MAE7D,KAAK5C,UAAA,CAAWmC,mBAAA,CAAoB,WAAW,KAAKU,SAAA,EAAW,KAAK;IAAA;IAK9D5C,aAAA,iBAAS,MAAe;MACxB,MAAAwB,QAAA,GAAW,KAAK1B,MAAA,CAAO0B,QAAA;MAE7B,KAAKqB,MAAA,CAAOxB,IAAA,CAAKG,QAAQ,EAAEsB,GAAA,CAAI,KAAKxB,MAAM;MAE1C,IAAI,KAAKyB,SAAA,EAAW;QAGd,SAAKC,cAAA,CAAe7B,KAAA,EAAO;UACxB,KAAA8B,GAAA,CAAIC,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEC,eAAA,CAAgB,KAAKrD,MAAA,CAAO4B,UAAU;UAE5D,MAAM0B,MAAA,GAAS,KAAKC,aAAA,GAAgB,KAAKC,aAAA,GAAgB;UAEzD,KAAKC,CAAA,CAAEC,gBAAA,CAAiB,KAAKP,GAAA,EAAK,KAAKD,cAAA,CAAe7B,KAAA,GAAQiC,MAAM;UAEpE,KAAKtD,MAAA,CAAO4B,UAAA,CAAW+B,WAAA,CAAY,KAAKF,CAAC;UACpC,KAAAV,MAAA,CAAOM,eAAA,CAAgB,KAAKI,CAAC;QACpC;QAII,SAAKP,cAAA,CAAe9B,GAAA,EAAK;UACtB,KAAA+B,GAAA,CAAIC,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEC,eAAA,CAAgB,KAAKrD,MAAA,CAAO4B,UAAU;UAE5D,MAAM0B,MAAA,GAAS,KAAKC,aAAA,GAAgB,KAAKC,aAAA,GAAgB;UAEzD,KAAKC,CAAA,CAAEC,gBAAA,CAAiB,KAAKP,GAAA,EAAK,KAAKD,cAAA,CAAe9B,GAAA,GAAMkC,MAAM;UAElE,KAAKtD,MAAA,CAAO4B,UAAA,CAAW+B,WAAA,CAAY,KAAKF,CAAC;UACpC,KAAAV,MAAA,CAAOM,eAAA,CAAgB,KAAKI,CAAC;QACpC;QAEK,KAAAV,MAAA,CAAOa,cAAA,CAAe,KAAKC,KAAK;QACrC,KAAKd,MAAA,CAAOe,WAAA,CAAY,KAAKC,WAAA,EAAa,KAAKC,WAAW;MAAA,OACrD;QAEA,KAAAjB,MAAA,CAAOM,eAAA,CAAgB,KAAKY,IAAI;QAErC,IAAI,KAAKC,UAAA,IAAc,KAAK/B,KAAA,KAAU/C,KAAA,CAAMC,IAAA,EAAM;UAC3C,KAAA8E,UAAA,CAAW,KAAKC,oBAAA,CAAsB;QAC7C;QAEK,KAAAjD,SAAA,CAAUkD,cAAA,CAAe,KAAKtB,MAAM;QAEzC,IAAI,KAAKQ,aAAA,EAAe;UACtB,KAAKpC,SAAA,CAAUE,KAAA,IAAS,KAAK6B,cAAA,CAAe7B,KAAA,GAAQ,KAAKmC,aAAA;UACzD,KAAKrC,SAAA,CAAUC,GAAA,IAAO,KAAK8B,cAAA,CAAe9B,GAAA,GAAM,KAAKoC,aAAA;QAAA,OAChD;UACA,KAAArC,SAAA,CAAUE,KAAA,IAAS,KAAK6B,cAAA,CAAe7B,KAAA;UACvC,KAAAF,SAAA,CAAUC,GAAA,IAAO,KAAK8B,cAAA,CAAe9B,GAAA;QAC5C;QAGA,KAAKD,SAAA,CAAUE,KAAA,GAAQhB,IAAA,CAAKiE,GAAA,CAAI,KAAKC,eAAA,EAAiBlE,IAAA,CAAKmE,GAAA,CAAI,KAAKC,eAAA,EAAiB,KAAKtD,SAAA,CAAUE,KAAK,CAAC;QAG1G,KAAKF,SAAA,CAAUC,GAAA,GAAMf,IAAA,CAAKiE,GAAA,CAAI,KAAKI,aAAA,EAAerE,IAAA,CAAKmE,GAAA,CAAI,KAAKG,aAAA,EAAe,KAAKxD,SAAA,CAAUC,GAAG,CAAC;QAElG,KAAKD,SAAA,CAAUyD,QAAA;QAEV,KAAAzD,SAAA,CAAU0D,MAAA,IAAU,KAAKhB,KAAA;QAG9B,KAAK1C,SAAA,CAAU0D,MAAA,GAASxE,IAAA,CAAKiE,GAAA,CAAI,KAAKP,WAAA,EAAa1D,IAAA,CAAKmE,GAAA,CAAI,KAAKR,WAAA,EAAa,KAAK7C,SAAA,CAAU0D,MAAM,CAAC;QAE/F,KAAA9B,MAAA,CAAO+B,gBAAA,CAAiB,KAAK3D,SAAS;QAGtC,KAAA4B,MAAA,CAAOM,eAAA,CAAgB,KAAK0B,WAAW;MAC9C;MAII,SAAKxB,aAAA,KAAkB,MAAM;QAC/B,KAAK/B,MAAA,CAAOwD,eAAA,CAAgB,KAAKC,SAAA,EAAW,KAAKzB,aAAa;MAAA,OACzD;QACA,KAAAhC,MAAA,CAAO0D,GAAA,CAAI,KAAKD,SAAS;MAChC;MAEAvD,QAAA,CAASH,IAAA,CAAK,KAAKC,MAAM,EAAE0D,GAAA,CAAI,KAAKnC,MAAM;MAEtC,SAAKE,SAAA,KAAc,OAAO;QACvB,KAAAjD,MAAA,CAAOmF,MAAA,CAAO,KAAK3D,MAAM;MAChC;MAEI,SAAK+B,aAAA,KAAkB,MAAM;QAC1B,KAAAL,cAAA,CAAe7B,KAAA,IAAS,IAAI,KAAKmC,aAAA;QACjC,KAAAN,cAAA,CAAe9B,GAAA,IAAO,IAAI,KAAKoC,aAAA;QAEpC,KAAKyB,SAAA,CAAUrB,cAAA,CAAe,IAAI,KAAKJ,aAAa;MAAA,OAC/C;QACL,KAAKN,cAAA,CAAeE,GAAA,CAAI,GAAG,GAAG,CAAC;QAE/B,KAAK6B,SAAA,CAAU7B,GAAA,CAAI,GAAG,GAAG,CAAC;MAC5B;MAEA,KAAKS,KAAA,GAAQ;MAOX,SAAKuB,WAAA,IACL,KAAKC,YAAA,CAAaC,iBAAA,CAAkB,KAAKtF,MAAA,CAAO0B,QAAQ,IAAI,KAAK6D,GAAA,IACjE,KAAK,IAAI,KAAKC,cAAA,CAAeC,GAAA,CAAI,KAAKzF,MAAA,CAAO4B,UAAU,KAAK,KAAK2D,GAAA,EACjE;QAEK,KAAAvD,aAAA,CAAc,KAAKC,WAAW;QAEnC,KAAKoD,YAAA,CAAa9D,IAAA,CAAK,KAAKvB,MAAA,CAAO0B,QAAQ;QAC3C,KAAK8D,cAAA,CAAejE,IAAA,CAAK,KAAKvB,MAAA,CAAO4B,UAAU;QAC/C,KAAKwD,WAAA,GAAc;QAEZ;MACT;MAEO;IAAA;IAGDlF,aAAA,+BAAuB,MAAgB,IAAIG,IAAA,CAAKC,EAAA,GAAM,KAAK,KAAM,KAAKoF,eAAA;IAEtExF,aAAA,uBAAe,MAAcG,IAAA,CAAKsF,GAAA,CAAI,MAAM,KAAKC,SAAS;IAE1D1F,aAAA,qBAAc2F,KAAA,IAAwB;MAC5C,KAAK3C,cAAA,CAAe7B,KAAA,IAASwE,KAAA;IAAA;IAGvB3F,aAAA,mBAAY2F,KAAA,IAAwB;MAC1C,KAAK3C,cAAA,CAAe9B,GAAA,IAAOyE,KAAA;IAAA;IAGrB3F,aAAA,kBAAU,CAAC4F,QAAA,EAAkBC,YAAA,KAAgC;MAC9D,KAAAC,CAAA,CAAEC,mBAAA,CAAoBF,YAAA,EAAc,CAAC;MACrC,KAAAC,CAAA,CAAEpC,cAAA,CAAe,CAACkC,QAAQ;MAE1B,KAAAb,SAAA,CAAUC,GAAA,CAAI,KAAKc,CAAC;IAAA;IAGnB9F,aAAA,gBAAQ,CAAC4F,QAAA,EAAkBC,YAAA,KAAgC;MAC7D,SAAKG,kBAAA,KAAuB,MAAM;QAC/B,KAAAF,CAAA,CAAEC,mBAAA,CAAoBF,YAAA,EAAc,CAAC;MAAA,OACrC;QACA,KAAAC,CAAA,CAAEC,mBAAA,CAAoBF,YAAA,EAAc,CAAC;QAC1C,KAAKC,CAAA,CAAEG,YAAA,CAAa,KAAKnG,MAAA,CAAOoG,EAAA,EAAI,KAAKJ,CAAC;MAC5C;MAEK,KAAAA,CAAA,CAAEpC,cAAA,CAAekC,QAAQ;MAEzB,KAAAb,SAAA,CAAUC,GAAA,CAAI,KAAKc,CAAC;IAAA;IAInB;IAAA9F,aAAA,cAAM,CAACmG,MAAA,EAAgBC,MAAA,KAAyB;MACtD,MAAMC,OAAA,GAAU,KAAKtG,UAAA;MAEjB,SAAKD,MAAA,YAAkBwG,iBAAA,EAAmB;QAEtC,MAAA9E,QAAA,GAAW,KAAK1B,MAAA,CAAO0B,QAAA;QAC7B,KAAKqB,MAAA,CAAOxB,IAAA,CAAKG,QAAQ,EAAEsB,GAAA,CAAI,KAAKxB,MAAM;QACtC,IAAAiF,cAAA,GAAiB,KAAK1D,MAAA,CAAO2D,MAAA,CAAO;QAGtBD,cAAA,IAAApG,IAAA,CAAKsG,GAAA,CAAM,KAAK3G,MAAA,CAAO4G,GAAA,GAAM,IAAKvG,IAAA,CAAKC,EAAA,GAAM,GAAK;QAG/D,KAAAuG,OAAA,CAAS,IAAIR,MAAA,GAASI,cAAA,GAAkBF,OAAA,CAAQO,YAAA,EAAc,KAAK9G,MAAA,CAAO+G,MAAM;QAChF,KAAAC,KAAA,CAAO,IAAIV,MAAA,GAASG,cAAA,GAAkBF,OAAA,CAAQO,YAAA,EAAc,KAAK9G,MAAA,CAAO+G,MAAM;MAAA,WAC1E,KAAK/G,MAAA,CAAOiH,oBAAA,EAAsB;QAEtC,KAAAJ,OAAA,CACFR,MAAA,IAAU,KAAKrG,MAAA,CAAOkH,KAAA,GAAQ,KAAKlH,MAAA,CAAOmH,IAAA,IAAS,KAAKnH,MAAA,CAAO8B,IAAA,GAAOyE,OAAA,CAAQa,WAAA,EAC/E,KAAKpH,MAAA,CAAO+G,MAAA;QAET,KAAAC,KAAA,CACFV,MAAA,IAAU,KAAKtG,MAAA,CAAOqH,GAAA,GAAM,KAAKrH,MAAA,CAAOsH,MAAA,IAAW,KAAKtH,MAAA,CAAO8B,IAAA,GAAOyE,OAAA,CAAQO,YAAA,EAC/E,KAAK9G,MAAA,CAAO+G,MAAA;MACd,OACK;QAELQ,OAAA,CAAQC,IAAA,CAAK,+EAA+E;QAC5F,KAAKC,SAAA,GAAY;MACnB;IAAA;IAGMvH,aAAA,kBAAWwH,UAAA,IAA6B;MAE1C,SAAK1H,MAAA,YAAkBwG,iBAAA,EAAmB;QAC5C,KAAK3C,KAAA,IAAS6D,UAAA;MAAA,WAEL,KAAK1H,MAAA,YAAkB2H,kBAAA,EAAoB;QACpD,KAAK3H,MAAA,CAAO8B,IAAA,GAAOzB,IAAA,CAAKiE,GAAA,CAAI,KAAKsD,OAAA,EAASvH,IAAA,CAAKmE,GAAA,CAAI,KAAKqD,OAAA,EAAS,KAAK7H,MAAA,CAAO8B,IAAA,GAAO4F,UAAU,CAAC;QAC/F,KAAK1H,MAAA,CAAO+B,sBAAA;QACZ,KAAKqD,WAAA,GAAc;MAAA,OACd;QACLmC,OAAA,CAAQC,IAAA,CAAK,sFAAsF;QACnG,KAAKM,UAAA,GAAa;MACpB;IAAA;IAGM5H,aAAA,mBAAYwH,UAAA,IAA6B;MAE3C,SAAK1H,MAAA,YAAkBwG,iBAAA,EAAmB;QAC5C,KAAK3C,KAAA,IAAS6D,UAAA;MAAA,WAEL,KAAK1H,MAAA,YAAkB2H,kBAAA,EAAoB;QACpD,KAAK3H,MAAA,CAAO8B,IAAA,GAAOzB,IAAA,CAAKiE,GAAA,CAAI,KAAKsD,OAAA,EAASvH,IAAA,CAAKmE,GAAA,CAAI,KAAKqD,OAAA,EAAS,KAAK7H,MAAA,CAAO8B,IAAA,GAAO4F,UAAU,CAAC;QAC/F,KAAK1H,MAAA,CAAO+B,sBAAA;QACZ,KAAKqD,WAAA,GAAc;MAAA,OACd;QACLmC,OAAA,CAAQC,IAAA,CAAK,sFAAsF;QACnG,KAAKM,UAAA,GAAa;MACpB;IAAA;IAKM;IAAA5H,aAAA,gCAAyB6H,KAAA,IAA4B;MAC3D,KAAKC,WAAA,CAAY5E,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;IAAA;IAI3C;IAAAhI,aAAA,+BAAwB6H,KAAA,IAA4B;MAC1D,KAAKI,UAAA,CAAW/E,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;IAAA;IAG1ChI,aAAA,6BAAsB6H,KAAA,IAA4B;MACxD,KAAKK,QAAA,CAAShF,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;IAAA;IAGxChI,aAAA,gCAAyB6H,KAAA,IAA4B;MAC3D,KAAKM,SAAA,CAAUjF,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;MAE1C,KAAAI,WAAA,CAAYC,UAAA,CAAW,KAAKF,SAAA,EAAW,KAAKL,WAAW,EAAEpE,cAAA,CAAe,KAAK4E,WAAW;MAE7F,MAAMjC,OAAA,GAAU,KAAKtG,UAAA;MAEhB,KAAAkE,UAAA,CAAY,IAAI9D,IAAA,CAAKC,EAAA,GAAK,KAAKgI,WAAA,CAAYG,CAAA,GAAKlC,OAAA,CAAQO,YAAY;MAEpE,KAAA4B,QAAA,CAAU,IAAIrI,IAAA,CAAKC,EAAA,GAAK,KAAKgI,WAAA,CAAYK,CAAA,GAAKpC,OAAA,CAAQO,YAAY;MAElE,KAAAkB,WAAA,CAAYzG,IAAA,CAAK,KAAK8G,SAAS;MAEpC,KAAKnG,MAAA,CAAO;IAAA;IAGNhC,aAAA,+BAAwB6H,KAAA,IAA4B;MAC1D,KAAKa,QAAA,CAASxF,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;MAE9C,KAAKW,UAAA,CAAWN,UAAA,CAAW,KAAKK,QAAA,EAAU,KAAKT,UAAU;MAErD,SAAKU,UAAA,CAAWF,CAAA,GAAI,GAAG;QACpB,KAAAG,OAAA,CAAQ,KAAKC,YAAA,CAAc;MACvB,gBAAKF,UAAA,CAAWF,CAAA,GAAI,GAAG;QAC3B,KAAAK,QAAA,CAAS,KAAKD,YAAA,CAAc;MACnC;MAEK,KAAAZ,UAAA,CAAW5G,IAAA,CAAK,KAAKqH,QAAQ;MAElC,KAAK1G,MAAA,CAAO;IAAA;IAGNhC,aAAA,6BAAsB6H,KAAA,IAA4B;MACxD,KAAKkB,MAAA,CAAO7F,GAAA,CAAI2E,KAAA,CAAME,OAAA,EAASF,KAAA,CAAMG,OAAO;MAEvC,KAAAgB,QAAA,CAASX,UAAA,CAAW,KAAKU,MAAA,EAAQ,KAAKb,QAAQ,EAAExE,cAAA,CAAe,KAAKuF,QAAQ;MAEjF,KAAKC,GAAA,CAAI,KAAKF,QAAA,CAAST,CAAA,EAAG,KAAKS,QAAA,CAASP,CAAC;MAEpC,KAAAP,QAAA,CAAS7G,IAAA,CAAK,KAAK0H,MAAM;MAE9B,KAAK/G,MAAA,CAAO;IAAA;IAONhC,aAAA,2BAAoB6H,KAAA,IAA4B;MAClD,IAAAA,KAAA,CAAMzB,MAAA,GAAS,GAAG;QACf,KAAA0C,QAAA,CAAS,KAAKD,YAAA,CAAc;MAAA,WACxBhB,KAAA,CAAMzB,MAAA,GAAS,GAAG;QACtB,KAAAwC,OAAA,CAAQ,KAAKC,YAAA,CAAc;MAClC;MAEA,KAAK7G,MAAA,CAAO;IAAA;IAGNhC,aAAA,wBAAiB6H,KAAA,IAA+B;MACtD,IAAIsB,WAAA,GAAc;MAElB,QAAQtB,KAAA,CAAMuB,IAAA;QACZ,KAAK,KAAKC,IAAA,CAAK/I,EAAA;UACR,KAAA4I,GAAA,CAAI,GAAG,KAAKI,WAAW;UACdH,WAAA;UACd;QAEF,KAAK,KAAKE,IAAA,CAAK7I,MAAA;UACb,KAAK0I,GAAA,CAAI,GAAG,CAAC,KAAKI,WAAW;UACfH,WAAA;UACd;QAEF,KAAK,KAAKE,IAAA,CAAKhJ,IAAA;UACR,KAAA6I,GAAA,CAAI,KAAKI,WAAA,EAAa,CAAC;UACdH,WAAA;UACd;QAEF,KAAK,KAAKE,IAAA,CAAK9I,KAAA;UACb,KAAK2I,GAAA,CAAI,CAAC,KAAKI,WAAA,EAAa,CAAC;UACfH,WAAA;UACd;MACJ;MAEA,IAAIA,WAAA,EAAa;QAEftB,KAAA,CAAM0B,cAAA,CAAe;QAErB,KAAKvH,MAAA,CAAO;MACd;IAAA;IAGMhC,aAAA,iCAA0B6H,KAAA,IAA4B;MACxD,IAAAA,KAAA,CAAM2B,OAAA,CAAQhD,MAAA,IAAU,GAAG;QACxB,KAAAsB,WAAA,CAAY5E,GAAA,CAAI2E,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,EAAO5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAK;MAAA,OAC9D;QACC,MAAAnB,CAAA,GAAI,OAAOV,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;QACrD,MAAAhB,CAAA,GAAI,OAAOZ,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;QAEtD,KAAA5B,WAAA,CAAY5E,GAAA,CAAIqF,CAAA,EAAGE,CAAC;MAC3B;IAAA;IAGMzI,aAAA,8BAAuB6H,KAAA,IAA4B;MACrD,IAAAA,KAAA,CAAM2B,OAAA,CAAQhD,MAAA,IAAU,GAAG;QACxB,KAAA0B,QAAA,CAAShF,GAAA,CAAI2E,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,EAAO5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAK;MAAA,OAC3D;QACC,MAAAnB,CAAA,GAAI,OAAOV,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;QACrD,MAAAhB,CAAA,GAAI,OAAOZ,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;QAEtD,KAAAxB,QAAA,CAAShF,GAAA,CAAIqF,CAAA,EAAGE,CAAC;MACxB;IAAA;IAGMzI,aAAA,gCAAyB6H,KAAA,IAA4B;MACrD,MAAA8B,EAAA,GAAK9B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;MAC/C,MAAAG,EAAA,GAAK/B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;MAErD,MAAM9D,QAAA,GAAWzF,IAAA,CAAK0J,IAAA,CAAKF,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;MAEvC,KAAA3B,UAAA,CAAW/E,GAAA,CAAI,GAAG0C,QAAQ;IAAA;IAGzB5F,aAAA,mCAA4B6H,KAAA,IAA4B;MAC9D,IAAI,KAAKD,UAAA,EAAY,KAAKkC,qBAAA,CAAsBjC,KAAK;MAErD,IAAI,KAAKN,SAAA,EAAW,KAAKwC,mBAAA,CAAoBlC,KAAK;IAAA;IAG5C7H,aAAA,sCAA+B6H,KAAA,IAA4B;MACjE,IAAI,KAAKD,UAAA,EAAY,KAAKkC,qBAAA,CAAsBjC,KAAK;MAErD,IAAI,KAAKmC,YAAA,EAAc,KAAKC,sBAAA,CAAuBpC,KAAK;IAAA;IAGlD7H,aAAA,gCAAyB6H,KAAA,IAA4B;MACvD,IAAAA,KAAA,CAAM2B,OAAA,CAAQhD,MAAA,IAAU,GAAG;QACxB,KAAA2B,SAAA,CAAUjF,GAAA,CAAI2E,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,EAAO5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAK;MAAA,OAC5D;QACC,MAAAnB,CAAA,GAAI,OAAOV,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;QACrD,MAAAhB,CAAA,GAAI,OAAOZ,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;QAEtD,KAAAvB,SAAA,CAAUjF,GAAA,CAAIqF,CAAA,EAAGE,CAAC;MACzB;MAEK,KAAAL,WAAA,CAAYC,UAAA,CAAW,KAAKF,SAAA,EAAW,KAAKL,WAAW,EAAEpE,cAAA,CAAe,KAAK4E,WAAW;MAE7F,MAAMjC,OAAA,GAAU,KAAKtG,UAAA;MAEhB,KAAAkE,UAAA,CAAY,IAAI9D,IAAA,CAAKC,EAAA,GAAK,KAAKgI,WAAA,CAAYG,CAAA,GAAKlC,OAAA,CAAQO,YAAY;MAEpE,KAAA4B,QAAA,CAAU,IAAIrI,IAAA,CAAKC,EAAA,GAAK,KAAKgI,WAAA,CAAYK,CAAA,GAAKpC,OAAA,CAAQO,YAAY;MAElE,KAAAkB,WAAA,CAAYzG,IAAA,CAAK,KAAK8G,SAAS;IAAA;IAG9BnI,aAAA,6BAAsB6H,KAAA,IAA4B;MACpD,IAAAA,KAAA,CAAM2B,OAAA,CAAQhD,MAAA,IAAU,GAAG;QACxB,KAAAuC,MAAA,CAAO7F,GAAA,CAAI2E,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,EAAO5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAK;MAAA,OACzD;QACC,MAAAnB,CAAA,GAAI,OAAOV,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;QACrD,MAAAhB,CAAA,GAAI,OAAOZ,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;QAEtD,KAAAX,MAAA,CAAO7F,GAAA,CAAIqF,CAAA,EAAGE,CAAC;MACtB;MAEK,KAAAO,QAAA,CAASX,UAAA,CAAW,KAAKU,MAAA,EAAQ,KAAKb,QAAQ,EAAExE,cAAA,CAAe,KAAKuF,QAAQ;MAEjF,KAAKC,GAAA,CAAI,KAAKF,QAAA,CAAST,CAAA,EAAG,KAAKS,QAAA,CAASP,CAAC;MAEpC,KAAAP,QAAA,CAAS7G,IAAA,CAAK,KAAK0H,MAAM;IAAA;IAGxB/I,aAAA,+BAAwB6H,KAAA,IAA4B;MACpD,MAAA8B,EAAA,GAAK9B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA,GAAQ5B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEC,KAAA;MAC/C,MAAAG,EAAA,GAAK/B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA,GAAQ7B,KAAA,CAAM2B,OAAA,CAAQ,CAAC,EAAEE,KAAA;MAErD,MAAM9D,QAAA,GAAWzF,IAAA,CAAK0J,IAAA,CAAKF,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;MAEvC,KAAAlB,QAAA,CAASxF,GAAA,CAAI,GAAG0C,QAAQ;MAE7B,KAAK+C,UAAA,CAAWzF,GAAA,CAAI,GAAG/C,IAAA,CAAKsF,GAAA,CAAI,KAAKiD,QAAA,CAASD,CAAA,GAAI,KAAKR,UAAA,CAAWQ,CAAA,EAAG,KAAK/C,SAAS,CAAC;MAE/E,KAAAkD,OAAA,CAAQ,KAAKD,UAAA,CAAWF,CAAC;MAEzB,KAAAR,UAAA,CAAW5G,IAAA,CAAK,KAAKqH,QAAQ;IAAA;IAG5B1I,aAAA,kCAA2B6H,KAAA,IAA4B;MAC7D,IAAI,KAAKD,UAAA,EAAY,KAAKsC,oBAAA,CAAqBrC,KAAK;MAEpD,IAAI,KAAKN,SAAA,EAAW,KAAK4C,kBAAA,CAAmBtC,KAAK;IAAA;IAG3C7H,aAAA,qCAA8B6H,KAAA,IAA4B;MAChE,IAAI,KAAKD,UAAA,EAAY,KAAKsC,oBAAA,CAAqBrC,KAAK;MAEpD,IAAI,KAAKmC,YAAA,EAAc,KAAKI,qBAAA,CAAsBvC,KAAK;IAAA;IAWjD;IAAA;IAAA;IAAA7H,aAAA,sBAAe6H,KAAA,IAA4B;MACjD,IAAI,KAAKwC,OAAA,KAAY,OAAO;MAI5BxC,KAAA,CAAM0B,cAAA,CAAe;MAKrB,KAAKxJ,UAAA,CAAWuK,KAAA,GAAQ,KAAKvK,UAAA,CAAWuK,KAAA,CAAM,IAAIC,MAAA,CAAOD,KAAA;MAErD,IAAAE,WAAA;MAEJ,QAAQ3C,KAAA,CAAM4C,MAAA;QACZ,KAAK;UACHD,WAAA,GAAc,KAAKE,YAAA,CAAarK,IAAA;UAChC;QAEF,KAAK;UACHmK,WAAA,GAAc,KAAKE,YAAA,CAAaC,MAAA;UAChC;QAEF,KAAK;UACHH,WAAA,GAAc,KAAKE,YAAA,CAAanK,KAAA;UAChC;QAEF;UACgBiK,WAAA;MAClB;MAEA,QAAQA,WAAA;QACN,KAAKI,KAAA,CAAMvL,KAAA;UACT,IAAI,KAAKuI,UAAA,KAAe,OAAO;UAE/B,KAAKiD,oBAAA,CAAqBhD,KAAK;UAE/B,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMG,KAAA;UAEnB;QAEF,KAAKuL,KAAA,CAAMxL,MAAA;UACT,IAAIyI,KAAA,CAAMiD,OAAA,IAAWjD,KAAA,CAAMkD,OAAA,IAAWlD,KAAA,CAAMmD,QAAA,EAAU;YACpD,IAAI,KAAKzD,SAAA,KAAc,OAAO;YAE9B,KAAK0D,kBAAA,CAAmBpD,KAAK;YAE7B,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMI,GAAA;UAAA,OACd;YACL,IAAI,KAAK0K,YAAA,KAAiB,OAAO;YAEjC,KAAKkB,qBAAA,CAAsBrD,KAAK;YAEhC,KAAK5F,KAAA,GAAQ/C,KAAA,CAAME,MAAA;UACrB;UAEA;QAEF,KAAKwL,KAAA,CAAMtL,GAAA;UACT,IAAIuI,KAAA,CAAMiD,OAAA,IAAWjD,KAAA,CAAMkD,OAAA,IAAWlD,KAAA,CAAMmD,QAAA,EAAU;YACpD,IAAI,KAAKhB,YAAA,KAAiB,OAAO;YAEjC,KAAKkB,qBAAA,CAAsBrD,KAAK;YAEhC,KAAK5F,KAAA,GAAQ/C,KAAA,CAAME,MAAA;UAAA,OACd;YACL,IAAI,KAAKmI,SAAA,KAAc,OAAO;YAE9B,KAAK0D,kBAAA,CAAmBpD,KAAK;YAE7B,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMI,GAAA;UACrB;UAEA;QAEF;UACE,KAAK2C,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;MACvB;MAEI,SAAK8C,KAAA,KAAU/C,KAAA,CAAMC,IAAA,EAAM;QAC7BsD,QAAA,CAAS0I,gBAAA,CAAiB,aAAa,KAAKzI,WAAA,EAAa,KAAK;QAC9DD,QAAA,CAAS0I,gBAAA,CAAiB,WAAW,KAAKxI,SAAA,EAAW,KAAK;QAGrD,KAAAb,aAAA,CAAc,KAAKsJ,UAAU;MACpC;IAAA;IAGMpL,aAAA,sBAAe6H,KAAA,IAA4B;MACjD,IAAI,KAAKwC,OAAA,KAAY,OAAO;MAE5BxC,KAAA,CAAM0B,cAAA,CAAe;MAErB,QAAQ,KAAKtH,KAAA;QACX,KAAK/C,KAAA,CAAME,MAAA;UACT,IAAI,KAAK4K,YAAA,KAAiB,OAAO;UAEjC,KAAKqB,qBAAA,CAAsBxD,KAAK;UAEhC;QAEF,KAAK3I,KAAA,CAAMG,KAAA;UACT,IAAI,KAAKuI,UAAA,KAAe,OAAO;UAE/B,KAAK0D,oBAAA,CAAqBzD,KAAK;UAE/B;QAEF,KAAK3I,KAAA,CAAMI,GAAA;UACT,IAAI,KAAKiI,SAAA,KAAc,OAAO;UAE9B,KAAKgE,kBAAA,CAAmB1D,KAAK;UAE7B;MACJ;IAAA;IAGM7H,aAAA,oBAAY,MAAY;MAC9B,IAAI,KAAKqK,OAAA,KAAY,OAAO;MAI5B5H,QAAA,CAASP,mBAAA,CAAoB,aAAa,KAAKQ,WAAA,EAAa,KAAK;MACjED,QAAA,CAASP,mBAAA,CAAoB,WAAW,KAAKS,SAAA,EAAW,KAAK;MAGxD,KAAAb,aAAA,CAAc,KAAK0J,QAAQ;MAEhC,KAAKvJ,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;IAAA;IAGba,aAAA,uBAAgB6H,KAAA,IAA4B;MAClD,IACE,KAAKwC,OAAA,KAAY,SACjB,KAAKzC,UAAA,KAAe,SACnB,KAAK3F,KAAA,KAAU/C,KAAA,CAAMC,IAAA,IAAQ,KAAK8C,KAAA,KAAU/C,KAAA,CAAME,MAAA,EACnD;QACA;MACF;MAEAyI,KAAA,CAAM0B,cAAA,CAAe;MAGhB,KAAAzH,aAAA,CAAc,KAAKsJ,UAAU;MAElC,KAAKK,gBAAA,CAAiB5D,KAAK;MAGtB,KAAA/F,aAAA,CAAc,KAAK0J,QAAQ;IAAA;IAG1BxL,aAAA,oBAAa6H,KAAA,IAA+B;MAClD,IAAI,KAAKwC,OAAA,KAAY,SAAS,KAAKqB,UAAA,KAAe,SAAS,KAAKnE,SAAA,KAAc,OAAO;MAErF,KAAKoE,aAAA,CAAc9D,KAAK;IAAA;IAGlB7H,aAAA,uBAAgB6H,KAAA,IAA4B;MAClD,IAAI,KAAKwC,OAAA,KAAY,OAAO;MAE5BxC,KAAA,CAAM0B,cAAA,CAAe;MAEb,QAAA1B,KAAA,CAAM2B,OAAA,CAAQhD,MAAA;QACpB,KAAK;UACK,aAAKgD,OAAA,CAAQ/I,GAAA;YACnB,KAAKC,KAAA,CAAMtB,MAAA;cACT,IAAI,KAAK4K,YAAA,KAAiB,OAAO;cAEjC,KAAKC,sBAAA,CAAuBpC,KAAK;cAEjC,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMK,YAAA;cAEnB;YAEF,KAAKmB,KAAA,CAAMpB,GAAA;cACT,IAAI,KAAKiI,SAAA,KAAc,OAAO;cAE9B,KAAKwC,mBAAA,CAAoBlC,KAAK;cAE9B,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMM,SAAA;cAEnB;YAEF;cACE,KAAKyC,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;UACvB;UAEA;QAEF,KAAK;UACK,aAAKqK,OAAA,CAAQ7I,GAAA;YACnB,KAAKD,KAAA,CAAME,SAAA;cACT,IAAI,KAAKgH,UAAA,KAAe,SAAS,KAAKL,SAAA,KAAc,OAAO;cAE3D,KAAKqE,wBAAA,CAAyB/D,KAAK;cAEnC,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMO,eAAA;cAEnB;YAEF,KAAKiB,KAAA,CAAMmL,YAAA;cACT,IAAI,KAAKjE,UAAA,KAAe,SAAS,KAAKoC,YAAA,KAAiB,OAAO;cAE9D,KAAK8B,2BAAA,CAA4BjE,KAAK;cAEtC,KAAK5F,KAAA,GAAQ/C,KAAA,CAAMQ,kBAAA;cAEnB;YAEF;cACE,KAAKuC,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;UACvB;UAEA;QAEF;UACE,KAAK8C,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;MACvB;MAEI,SAAK8C,KAAA,KAAU/C,KAAA,CAAMC,IAAA,EAAM;QAExB,KAAA2C,aAAA,CAAc,KAAKsJ,UAAU;MACpC;IAAA;IAGMpL,aAAA,sBAAe6H,KAAA,IAA4B;MACjD,IAAI,KAAKwC,OAAA,KAAY,OAAO;MAE5BxC,KAAA,CAAM0B,cAAA,CAAe;MAErB,QAAQ,KAAKtH,KAAA;QACX,KAAK/C,KAAA,CAAMK,YAAA;UACT,IAAI,KAAKyK,YAAA,KAAiB,OAAO;UAEjC,KAAKI,qBAAA,CAAsBvC,KAAK;UAEhC,KAAK7F,MAAA,CAAO;UAEZ;QAEF,KAAK9C,KAAA,CAAMM,SAAA;UACT,IAAI,KAAK+H,SAAA,KAAc,OAAO;UAE9B,KAAK4C,kBAAA,CAAmBtC,KAAK;UAE7B,KAAK7F,MAAA,CAAO;UAEZ;QAEF,KAAK9C,KAAA,CAAMO,eAAA;UACT,IAAI,KAAKmI,UAAA,KAAe,SAAS,KAAKL,SAAA,KAAc,OAAO;UAE3D,KAAKwE,uBAAA,CAAwBlE,KAAK;UAElC,KAAK7F,MAAA,CAAO;UAEZ;QAEF,KAAK9C,KAAA,CAAMQ,kBAAA;UACT,IAAI,KAAKkI,UAAA,KAAe,SAAS,KAAKoC,YAAA,KAAiB,OAAO;UAE9D,KAAKgC,0BAAA,CAA2BnE,KAAK;UAErC,KAAK7F,MAAA,CAAO;UAEZ;QAEF;UACE,KAAKC,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;MACvB;IAAA;IAGMa,aAAA,qBAAa,MAAY;MAC/B,IAAI,KAAKqK,OAAA,KAAY,OAAO;MAKvB,KAAAvI,aAAA,CAAc,KAAK0J,QAAQ;MAEhC,KAAKvJ,KAAA,GAAQ/C,KAAA,CAAMC,IAAA;IAAA;IAGba,aAAA,wBAAiB6H,KAAA,IAAuB;MAC9C,IAAI,KAAKwC,OAAA,KAAY,OAAO;MAE5BxC,KAAA,CAAM0B,cAAA,CAAe;IAAA;IAzzBrB,IAAIxJ,UAAA,KAAe,QAAW;MAC5BsH,OAAA,CAAQC,IAAA,CAAK,2EAA2E;IAC1F;IACA,IAAIvH,UAAA,YAAsBkM,QAAA,EAAU;MAC1B5E,OAAA,CAAA6E,KAAA,CACN;IAEJ;IAEA,KAAKpM,MAAA,GAASA,MAAA;IACd,KAAKC,UAAA,GAAaA,UAAA;IAElB,KAAK2K,YAAA,GAAe;MAClBrK,IAAA,EAAMuK,KAAA,CAAMxL,MAAA;MACZuL,MAAA,EAAQC,KAAA,CAAMvL,KAAA;MACdkB,KAAA,EAAOqK,KAAA,CAAMtL;IAAA;IAIV,KAAA8B,OAAA,GAAU,KAAKE,MAAA,CAAO6K,KAAA,CAAM;IACjC,KAAK5K,SAAA,GAAY,KAAKzB,MAAA,CAAO0B,QAAA,CAAS2K,KAAA,CAAM;IAC5C,KAAK1K,WAAA,GAAc,KAAK3B,MAAA,CAAO4B,UAAA,CAAWyK,KAAA,CAAM;IAC3C,KAAAxK,KAAA,GAAQ,KAAK7B,MAAA,CAAO8B,IAAA;IAOzB,KAAKmC,IAAA,GAAO,IAAI/C,UAAA,CAAW,EAAEoL,kBAAA,CAAmB,KAAKtM,MAAA,CAAOoG,EAAA,EAAI,IAAIjG,OAAA,CAAQ,GAAG,GAAG,CAAC,CAAC;IACpF,KAAK4E,WAAA,GAAc,KAAKd,IAAA,CAAKoI,KAAA,GAAQE,MAAA;IAEhC,KAAAlH,YAAA,GAAe,IAAIlF,OAAA;IACnB,KAAAqF,cAAA,GAAiB,IAAItE,UAAA;IAE1B,KAAKjB,UAAA,CAAWoL,gBAAA,CAAiB,eAAe,KAAKhJ,aAAA,EAAe,KAAK;IAEzE,KAAKpC,UAAA,CAAWoL,gBAAA,CAAiB,aAAa,KAAK/I,WAAA,EAAa,KAAK;IACrE,KAAKrC,UAAA,CAAWoL,gBAAA,CAAiB,SAAS,KAAK9I,YAAA,EAAc,KAAK;IAElE,KAAKtC,UAAA,CAAWoL,gBAAA,CAAiB,cAAc,KAAK7I,YAAA,EAAc,KAAK;IACvE,KAAKvC,UAAA,CAAWoL,gBAAA,CAAiB,YAAY,KAAK5I,UAAA,EAAY,KAAK;IACnE,KAAKxC,UAAA,CAAWoL,gBAAA,CAAiB,aAAa,KAAK3I,WAAA,EAAa,KAAK;IAErE,KAAKzC,UAAA,CAAWoL,gBAAA,CAAiB,WAAW,KAAKvI,SAAA,EAAW,KAAK;IAI7D,SAAK7C,UAAA,CAAWuM,QAAA,KAAa,IAAI;MACnC,KAAKvM,UAAA,CAAWuM,QAAA,GAAW;IAC7B;IAIK,KAAAxM,MAAA,CAAOmF,MAAA,CAAO,KAAK3D,MAAM;IAC9B,KAAKU,MAAA,CAAO;IACZ,KAAKuK,SAAA,CAAU;EACjB;EA+TQC,cAAA,EAA+B,CAEvC;EAyJQC,eAAA,EAAgC,CAExC;AAsSF;AASA,MAAMC,gBAAA,SAAyB/M,cAAA,CAAe;EAU5CE,YAAYC,MAAA,EAAgDC,UAAA,EAAyB;IACnF,MAAMD,MAAA,EAAQC,UAAU;IAV1BC,aAAA;IAIAA,aAAA;IAQE,KAAK0K,YAAA,GAAe;MAClBrK,IAAA,EAAMuK,KAAA,CAAMxL,MAAA;MACZmB,KAAA,EAAOqK,KAAA,CAAMtL;IAAA;IAEf,KAAKkK,OAAA,GAAU;MACb/I,GAAA,EAAKC,KAAA,CAAMtB,MAAA;MACXuB,GAAA,EAAKD,KAAA,CAAME;IAAA;EAEf;AACF;AASA,MAAM+L,cAAA,SAAuBhN,cAAA,CAAe;EAU1CE,YAAYC,MAAA,EAAgDC,UAAA,EAAyB;IACnF,MAAMD,MAAA,EAAQC,UAAU;IAV1BC,aAAA;IAIAA,aAAA;IAQE,KAAK0K,YAAA,GAAe;MAClBrK,IAAA,EAAMuK,KAAA,CAAMtL,GAAA;MACZiB,KAAA,EAAOqK,KAAA,CAAMxL;IAAA;IAEf,KAAKoK,OAAA,GAAU;MACb/I,GAAA,EAAKC,KAAA,CAAMpB,GAAA;MACXqB,GAAA,EAAKD,KAAA,CAAMmL;IAAA;EAEf;AACF;AASA,MAAMe,oBAAA,SAA6BjN,cAAA,CAAe;EAahDE,YAAYC,MAAA,EAAgDC,UAAA,EAAyB;IACnF,MAAMD,MAAA,EAAQC,UAAU;IAb1BC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAIAA,aAAA;IAQE,KAAK+C,SAAA,GAAY;IACjB,KAAKiD,kBAAA,GAAqB;IAC1B,KAAKhC,UAAA,GAAa;IAElB,KAAK0G,YAAA,GAAe;MAClBrK,IAAA,EAAMuK,KAAA,CAAMxL,MAAA;MACZmB,KAAA,EAAOqK,KAAA,CAAMtL;IAAA;IAGf,KAAKkK,OAAA,GAAU;MACb/I,GAAA,EAAKC,KAAA,CAAMtB,MAAA;MACXuB,GAAA,EAAKD,KAAA,CAAME;IAAA;EAEf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}