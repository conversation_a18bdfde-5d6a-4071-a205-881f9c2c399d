[{"/Users/<USER>/Documents/Workspace/pro/trust-framer/src/index.tsx": "1", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/App.tsx": "3", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/About.tsx": "4", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/SafeSendPage.tsx": "5", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/Features.tsx": "6", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/HomePage.tsx": "7", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/contexts/ThemeContext.tsx": "8", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Key.tsx": "9", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Reason.tsx": "10", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Mission.tsx": "11", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Introduce.tsx": "12", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Problem.tsx": "13", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Wallet.tsx": "14", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Header.tsx": "15", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/ComingSoon.tsx": "16", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Additional.tsx": "17", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Header.tsx": "18", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/GetStarted.tsx": "19", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Powerful.tsx": "20", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Features.tsx": "21", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Download.tsx": "22", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Header.tsx": "23", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Benefit.tsx": "24", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Slider.tsx": "25", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/FAQs.tsx": "26", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/SafeSend.tsx": "27", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Support.tsx": "28", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Future.tsx": "29", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Wallet.tsx": "30", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Works.tsx": "31", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Layout/index.tsx": "32", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/assets/image.ts": "33", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/Items.ts": "34", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Text.tsx": "35", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Card.tsx": "36", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Button.tsx": "37", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Home/Wallet.tsx": "38", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Home/Support.tsx": "39", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/UserGroup/index.tsx": "40", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Animation/index.tsx": "41", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Contact/index.tsx": "42", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Footer/index.tsx": "43", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/index.tsx": "44", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Background.tsx": "45", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Animation/BackgroundBeams.tsx": "46", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/UserGroup/Avatars.tsx": "47", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/Links.ts": "48", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Contact/index.tsx": "49", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/index.tsx": "50", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/lib/utils.ts": "51", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/index.tsx": "52", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Image.tsx": "53", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/NavMenuList.tsx": "54", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/ThemeToggle.tsx": "55", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/NavMenuList.tsx": "56", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/NavMenuItem.tsx": "57", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/NavMenuItem.tsx": "58", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/TechStack.tsx": "59", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/TechStack.ts": "60", "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx": "61"}, {"size": 554, "mtime": 1751872218740, "results": "62", "hashOfConfig": "63"}, {"size": 425, "mtime": 1751872218748, "results": "64", "hashOfConfig": "63"}, {"size": 727, "mtime": 1751873448905, "results": "65", "hashOfConfig": "63"}, {"size": 544, "mtime": 1751872218745, "results": "66", "hashOfConfig": "63"}, {"size": 457, "mtime": 1751872218747, "results": "67", "hashOfConfig": "63"}, {"size": 667, "mtime": 1751872218746, "results": "68", "hashOfConfig": "63"}, {"size": 833, "mtime": 1751875737546, "results": "69", "hashOfConfig": "63"}, {"size": 2484, "mtime": 1751873420242, "results": "70", "hashOfConfig": "63"}, {"size": 2335, "mtime": 1751872218737, "results": "71", "hashOfConfig": "63"}, {"size": 2916, "mtime": 1751872218737, "results": "72", "hashOfConfig": "63"}, {"size": 1368, "mtime": 1751872218737, "results": "73", "hashOfConfig": "63"}, {"size": 1184, "mtime": 1751872218739, "results": "74", "hashOfConfig": "63"}, {"size": 4611, "mtime": 1751872218739, "results": "75", "hashOfConfig": "63"}, {"size": 5756, "mtime": 1751872218740, "results": "76", "hashOfConfig": "63"}, {"size": 629, "mtime": 1751872218736, "results": "77", "hashOfConfig": "63"}, {"size": 2777, "mtime": 1751872218737, "results": "78", "hashOfConfig": "63"}, {"size": 2413, "mtime": 1751872218737, "results": "79", "hashOfConfig": "63"}, {"size": 626, "mtime": 1751872218737, "results": "80", "hashOfConfig": "63"}, {"size": 958, "mtime": 1751872218737, "results": "81", "hashOfConfig": "63"}, {"size": 2242, "mtime": 1751872218738, "results": "82", "hashOfConfig": "63"}, {"size": 2121, "mtime": 1751872218738, "results": "83", "hashOfConfig": "63"}, {"size": 3727, "mtime": 1751872218738, "results": "84", "hashOfConfig": "63"}, {"size": 2187, "mtime": 1751872218738, "results": "85", "hashOfConfig": "63"}, {"size": 2603, "mtime": 1751872218738, "results": "86", "hashOfConfig": "63"}, {"size": 1302, "mtime": 1751872218739, "results": "87", "hashOfConfig": "63"}, {"size": 1835, "mtime": 1751872218738, "results": "88", "hashOfConfig": "63"}, {"size": 2299, "mtime": 1751872218739, "results": "89", "hashOfConfig": "63"}, {"size": 1722, "mtime": 1751872218739, "results": "90", "hashOfConfig": "63"}, {"size": 1381, "mtime": 1751872218738, "results": "91", "hashOfConfig": "63"}, {"size": 2710, "mtime": 1751872218739, "results": "92", "hashOfConfig": "63"}, {"size": 2878, "mtime": 1751872218739, "results": "93", "hashOfConfig": "63"}, {"size": 474, "mtime": 1751872218733, "results": "94", "hashOfConfig": "63"}, {"size": 5747, "mtime": 1751872218581, "results": "95", "hashOfConfig": "63"}, {"size": 7986, "mtime": 1751872218736, "results": "96", "hashOfConfig": "63"}, {"size": 1786, "mtime": 1751872218736, "results": "97", "hashOfConfig": "63"}, {"size": 1536, "mtime": 1751872218735, "results": "98", "hashOfConfig": "63"}, {"size": 697, "mtime": 1751872218735, "results": "99", "hashOfConfig": "63"}, {"size": 751, "mtime": 1751872218733, "results": "100", "hashOfConfig": "63"}, {"size": 696, "mtime": 1751872218733, "results": "101", "hashOfConfig": "63"}, {"size": 449, "mtime": 1751872218735, "results": "102", "hashOfConfig": "63"}, {"size": 3957, "mtime": 1751872218732, "results": "103", "hashOfConfig": "63"}, {"size": 1484, "mtime": 1751872218733, "results": "104", "hashOfConfig": "63"}, {"size": 8199, "mtime": 1751872218733, "results": "105", "hashOfConfig": "63"}, {"size": 638, "mtime": 1751872218735, "results": "106", "hashOfConfig": "63"}, {"size": 1005, "mtime": 1751872218735, "results": "107", "hashOfConfig": "63"}, {"size": 9841, "mtime": 1751872218732, "results": "108", "hashOfConfig": "63"}, {"size": 566, "mtime": 1751872218735, "results": "109", "hashOfConfig": "63"}, {"size": 527, "mtime": 1751872218736, "results": "110", "hashOfConfig": "63"}, {"size": 414, "mtime": 1751872218732, "results": "111", "hashOfConfig": "63"}, {"size": 881, "mtime": 1751873505004, "results": "112", "hashOfConfig": "63"}, {"size": 164, "mtime": 1751872218740, "results": "113", "hashOfConfig": "63"}, {"size": 1527, "mtime": 1751873538117, "results": "114", "hashOfConfig": "63"}, {"size": 249, "mtime": 1751872218736, "results": "115", "hashOfConfig": "63"}, {"size": 350, "mtime": 1751872218734, "results": "116", "hashOfConfig": "63"}, {"size": 2157, "mtime": 1751873436315, "results": "117", "hashOfConfig": "63"}, {"size": 476, "mtime": 1751872218734, "results": "118", "hashOfConfig": "63"}, {"size": 898, "mtime": 1751872218734, "results": "119", "hashOfConfig": "63"}, {"size": 967, "mtime": 1751872218734, "results": "120", "hashOfConfig": "63"}, {"size": 5098, "mtime": 1751875889541, "results": "121", "hashOfConfig": "63"}, {"size": 3246, "mtime": 1751874109830, "results": "122", "hashOfConfig": "63"}, {"size": 4254, "mtime": 1751876002303, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1erum93", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/App.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/About.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/SafeSendPage.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/Features.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/contexts/ThemeContext.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Key.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Reason.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Mission.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Introduce.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Problem.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/SafeSend/Wallet.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/About/Header.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/ComingSoon.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Additional.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Header.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/GetStarted.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Features/Powerful.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Features.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Download.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Header.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Benefit.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Slider.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/FAQs.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/SafeSend.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Support.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Future.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Wallet.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/Works.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Layout/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/assets/image.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/Items.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Text.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Card.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Button.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Home/Wallet.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Home/Support.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/UserGroup/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Animation/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Contact/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Footer/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Background.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Animation/BackgroundBeams.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/UserGroup/Avatars.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/Links.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/Contact/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/index.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/common/Image.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/NavMenuList.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/ThemeToggle.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/NavMenuList.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Desktop/NavMenuItem.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Nav/Mobile/NavMenuItem.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/containers/Home/TechStack.tsx", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/constants/TechStack.ts", [], [], "/Users/<USER>/Documents/Workspace/pro/trust-framer/src/components/Cards/TechStackCard.tsx", [], []]