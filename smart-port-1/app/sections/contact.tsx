import { Send } from "lucide-react";
import dynamic from "next/dynamic";
import ContactSection from "../components/ContactForm";

// Dynamically import Map with no SSR to prevent hydration errors from browser APIs
const Map = dynamic(() => import("../components/Map"), {
  ssr: false,
});

const Contact = ({ className }: any) => {
  return (
    <div
      className={`${className} flex-1 p-8 overflow-y-auto`}
      style={{ scrollbarWidth: "thin" }}
    >
      <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
        <Send size={20} />
        Get in Touch
      </h2>
      <Map />
      <ContactSection />
    </div>
  );
};

export default Contact;
