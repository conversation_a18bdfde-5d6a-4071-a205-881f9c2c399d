import { WorkItem } from "../types/threeCard";

export const worksArr: WorkItem[] = [
  {
    title: "HomeMall",
    description:
      "HomeMall is a leading e-commerce platform in Myanmar, offering a wide range of products and services to customers across the country.",
    url: "./images/work-reference-screenshots/homemall.png",
    website: "https://homemall.com.mm/",
  },
  {
    title: "Feefty",
    description:
      "Feefty is a technological company offering to financial advisors, investment managers and CFOs the ability to create their own structured products online.We create simple and efficient tools, we bet on transparency on fees policy and focus on great support to give every chance to investors.",
    url: "./images/work-reference-screenshots/feefty.png",
    website: "https://feefty.com",
  },
  {
    title: "Openfabric AI",
    description:
      "Openfabric is a decentralized AI platform where the collaboration between AI innovators, data providers, businesses, and infrastructure providers will facilitate the creation and use of new intelligent algorithms and services.",
    url: "./images/work-reference-screenshots/openfabric-ai.png",
    website: "https://openfabric.ai/",
  },
  {
    title: "Del-Qui landing",
    description:
      "<PERSON> provides total support from business strategy to technology selection.",
    url: "./images/work-reference-screenshots/del-qui.png",
    website: "https://landing-delqui.vercel.app/",
  },
  {
    title: "FlowForge",
    description:
      "FlowForge is a leading platform for building and managing workflows.",
    url: "./images/work-reference-screenshots/trust-framer.png",
    website: "https://trust-framer-nine.vercel.app/",
  },
  {
    title: "Next.js landing studio",
    description:
      "Transform your brand with innovative design and strategic thinking. We partner with ambitious companies to create impactful digital solutions.",
    url: "./images/work-reference-screenshots/next.js-landing-studio.png",
    website: "https://next-landing-mock.vercel.app/",
  },
  {
    title: "Saigon Center Client",
    description:
      "Saigon Center is a leading shopping mall in Vietnam, offering a wide range of products and services to customers across the country.",
    url: "./images/work-reference-screenshots/saigon-center.png",
    website: "https://saigon-center-client.vercel.app/",
  },
  {
    title: "FlowForge",
    description:
      "FlowForge is a leading platform for building and managing workflows.",
    url: "./images/work-reference-screenshots/flow-forge.png",
    website: "https://flow-digital-eta.vercel.app/",
  },
  {
    title: "Vietnam travel",
    description:
      "A land of staggering natural beauty and cultural complexities, of dynamic mega cities and hill-tribe villages, Vietnam is both exotic and compelling",
    url: "./images/work-reference-screenshots/vietnam travel.png",
    website: "https://vietnam-travel-ten.vercel.app/",
  },
  {
    title: "GSAP landing demo",
    description: "A landing page demo using GSAP",
    url: "./images/work-reference-screenshots/gsap-landing-demo.png",
    website: "https://mo-landing-react-gsap.vercel.app/",
  },
];

export const experiences = [
  {
    startDate: "Jan 2024",
    endDate: "Currently",
    status: "",
    job: "Senior Full-stack Developer & System Operator",
    company: "CMC Telecom",
    description:
      "CMC Telecom is a leading telecommunications company in Hanoi, Vietnam, offering a wide range of services to customers across the country.",
    mark: "CMC Telecom",
  },
  {
    startDate: "Oct 2021",
    endDate: "Dec 2023",
    status: "",
    job: "Senior Full-stack Developer & System Engineer",
    company: "FPT Information System",
    description:
      "FPT Information System is a leading software company in Vietnam, offering a wide range of services to customers across the country.",
    mark: "FPT Information System",
  },
  {
    startDate: "Oct 2019",
    endDate: "Oct2021",
    status: "",
    job: "Senior Frontend Developer and Camera Tuning Engineer",
    company: "Blaze Company",
    description:
      "Blaze Blinds is a curtain sewing company headquartered in the Netherland, specializing in exporting curtains to European and American markets. Our company Size’s about 1.000 employees.",
    mark: "Blaze Company",
  },
];

export const comments = [
  {
    name: "Stephan",
    date: "April 30, 2025 at 11:06 PM",
    text: "Amazing portfolio",
  },
  { name: "KevinLeon", date: "December 9, 2024 at 7:35 PM", text: "perfect" },
  {
    name: "James",
    date: "October 2, 2024 at 12:56 AM",
    text: "Impressive work, keep it up!",
  },
];

export const testimonials = [
  {
    quote:
      "Working with Quoc has been a pleasure. He provided excellent clarity on project requirements, scope and the vision of the project. All expectations and requirements were communicated upfront, and he was responsive to any of my requests for clarification. Antero - I enjoyed working with you and hope to have a long business relationship.",
    author: "Nguyen",
    position: "CEO at CMC Telecom",
  },
  {
    quote:
      "Quoc delivered our project ahead of schedule with exceptional quality. His full-stack expertise helped us overcome several technical challenges we'd been struggling with for months.",
    author: "Trung",
    position: "CTO at Blaze Company",
  },
];
