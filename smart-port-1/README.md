<h1 align="center"><img src="https://media.giphy.com/media/hvRJCLFzcasrR4ia7z/giphy.gif" width="30px"> Hello! <img src="https://media.giphy.com/media/hvRJCLFzcasrR4ia7z/giphy.gif" width="30px"></h1>
# 👋 Welcome! I'm Do Quoc Dat
🚀 **Full-Stack Developer**
 
I breathe code and love turning ideas into reality!  
Building seamless digital experiences is my passion, whether it's a dynamic web app, an intuitive mobile experience, or a powerful API.  
 
💡 _"Great code isn't just written; it's designed."_

---

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd smart-port-1
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:

   ```env
   # Email Configuration (Required for contact form)
   EMAIL_SERVER_HOST=smtp.gmail.com
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER=<EMAIL>
   EMAIL_SERVER_PASSWORD=your-app-password
   EMAIL_FROM=<EMAIL>
   ```

   **For Gmail setup:**

   - Enable 2-Step Verification in your Google Account
   - Generate an App Password: Google Account → Security → 2-Step Verification → App passwords
   - Use the App Password (not your regular password) for `EMAIL_SERVER_PASSWORD`

4. **Run the development server**

   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

---

## 🌱 About Me

🔹 7+ years of experience in front-end & full-stack development  
🔹 Passionate about building scalable, interactive web & mobile apps  
🔹 Experienced in **React.js, Next.js, Nuxt.js, Vue.js, Angular, Flutter, OpenAI, Node.js, Django, Python,**

💬 Let's connect and build something great together!

---

## 💼 What I Do

🚀 **Front-End Development** → React, Next.js, Vue.js, Nuxt.js, Angular.js, Flutter.

🔹 𝐅𝐫𝐨𝐧𝐭𝐞𝐧𝐝: React.js, Next.js, Vue.js, Nuxt.js, Gatsby

🔹 𝐌𝐨𝐛𝐢𝐥𝐞: React Native (IOS & Android)

🔹 𝐁𝐚𝐜𝐤𝐞𝐧𝐝: Node.js, Express.js, Python, Django, FastAPI, Flask, Laravel

🔹 𝐀𝐏𝐈 & 𝐑𝐞𝐚𝐥𝐭𝐢𝐦𝐞: GraphQL, Axios, Fetch, WebSocket, Socket.io

🔹 𝐔𝐈 & 𝐒𝐭𝐲𝐥𝐢𝐧𝐠: Material-UI, Ant Design, Chakra UI, Tailwind CSS, Styled Components

🔹 𝐀𝐧𝐢𝐦𝐚𝐭𝐢𝐨𝐧: Framer Motion, GSAP, Lottie, React Spring, Three.js

🔹 𝐃𝐚𝐭𝐚𝐛𝐚𝐬𝐞𝐬 & 𝐂𝐥𝐨𝐮𝐝: Mongo DB, Firebase, Supabase, MySQL, Stripe, Google API, Sentry

🔹 𝐓𝐨𝐨𝐥𝐬 & 𝐂𝐨𝐥𝐥𝐚𝐛𝐨𝐫𝐚𝐭𝐢𝐨𝐧: GitHub, GitLab, Jira, Trello, Bitbucket

🔹 𝐃𝐚𝐭𝐚 𝐕𝐢𝐬𝐮𝐚𝐥𝐢𝐳𝐚𝐭𝐢𝐨𝐧: Google Charts, Chart.js

---

## 📧 Contact Form Setup

The contact form sends emails to `<EMAIL>`. To set up email functionality:

1. **Gmail Option (Recommended)**

   - Use your Gmail account with App Password
   - More secure than using your regular password
   - Follow the setup steps in the Getting Started section

2. **SendGrid Option (Alternative)**
   ```env
   EMAIL_SERVER_HOST=smtp.sendgrid.net
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER=apikey
   EMAIL_SERVER_PASSWORD=your-sendgrid-api-key
   EMAIL_FROM=<EMAIL>
   ```

## 🛠️ Tech Stack

- **Framework:** Next.js 15 with App Router
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **3D Graphics:** Three.js, React Three Fiber
- **Icons:** Lucide React, React Icons
- **Email:** Nodemailer
- **Deployment:** Vercel Ready

## 📝 License

© 2025 Do Quoc Dat. All rights reserved.
